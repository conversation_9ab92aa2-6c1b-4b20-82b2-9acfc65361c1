
a203f5f67f70a9e19b9b2803873778c486e40e68	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.93.1754018536329.js\",\"contentHash\":\"38fd4190e547195962f3a9ac53723a59\"}","integrity":"sha512-ynhr1v1tDSpnFXOWtzennDGidfrH1R+9eNFGYOogWjerVbM9nruH+M7MM/f+LJ1o0CrRwCevvs8EAiQN1C0czQ==","time":1754018576086,"size":250263}