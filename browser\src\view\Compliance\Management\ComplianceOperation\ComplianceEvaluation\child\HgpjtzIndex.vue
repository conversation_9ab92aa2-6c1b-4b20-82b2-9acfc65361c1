    <template>
      <el-container>
        <el-card>
          <div>
            <el-input v-model="tableQuery.fuzzyValue" class="filter_input" placeholder="检索字段（评价对象类型、评价对象、评价年度）"
              clearable @keyup.enter.native="refreshData" @clear="refreshData">
<!--              <el-popover slot="prepend" placement="bottom-start" width="1000" trigger="click">-->
<!--                <el-form ref="queryForm" label-width="100px" size="mini">-->
<!--                  <el-row>-->
<!--                    <el-col :span="12">-->
<!--                      <el-form-item label="评价对象类型">-->
<!--                        <el-select v-model="tableQuery.evaluationObjectType" clearable placeholder="请选择"-->
<!--                          style="width: 100%">-->
<!--                          <el-option v-for="item in utils.compliance_evaluation_type" :key="item.dicName"-->
<!--                            :label="item.dicName" :value="item.dicName" />-->
<!--                        </el-select>-->
<!--                      </el-form-item>-->
<!--                    </el-col>-->
<!--                    <el-col :span="12">-->
<!--                      <el-form-item label="评价对象">-->
<!--                        <el-input v-model="tableQuery.evaluationObject" clearable placeholder="请输入..." />-->
<!--                      </el-form-item>-->
<!--                    </el-col>-->
<!--                  </el-row>-->
<!--                  <el-row>-->
<!--                    <el-col :span="12">-->
<!--                      <el-form-item label="评价年度">-->
<!--                        <el-select v-model="tableQuery.evaluationYear" clearable placeholder="请选择年份"-->
<!--                          style="width: 100%">-->
<!--                          <el-option v-for="year in yearOptions" :key="year" :label="year" :value="year" />-->
<!--                        </el-select>-->
<!--                      </el-form-item>-->
<!--                    </el-col>-->
<!--                  </el-row>-->
<!--                  <el-button-group style="float: right">-->
<!--                    <el-button type="primary" size="mini" icon="el-icon-search" @click="search_">搜索</el-button>-->
<!--                    <el-button type="primary" size="mini" icon="el-icon-refresh-left" @click="empty_">重置</el-button>-->
<!--                  </el-button-group>-->
<!--                </el-form>-->
<!--                <el-button slot="reference" size="small" type="primary">高级检索</el-button>-->
<!--              </el-popover>-->
              <el-button slot="append" icon="el-icon-search" @click="search_" />
            </el-input>
          </div>
        </el-card>
        <el-card>
          <el-table ref="table" v-loading="tableLoading" :data="tableData" size="mini" border
            stripe fit highlight-current-row :show-overflow-tooltip="true" row-key="id"
            style="table-layout: fixed;width: 100%;" @sort-change="tableSort" @row-dblclick="rowDblclick">
            <el-table-column type="index" width="50" label="序号" align="center" />
            <el-table-column v-if="ss.tableColumns.find(item => item.key === 'evaluationObjectType').visible"
              prop="evaluationObjectType" show-overflow-tooltip label="评价对象类型" min-width="150" />
            <el-table-column v-if="ss.tableColumns.find(item => item.key === 'evaluationObject').visible"
              prop="evaluationObject" show-overflow-tooltip label="评价对象" min-width="250" />
            <el-table-column v-if="ss.tableColumns.find(item => item.key === 'evaluatorTime').visible"
              prop="evaluatorTime" show-overflow-tooltip label="评价日期" min-width="100" sortable="custom">
              <template slot-scope="scope">
                <span>{{ scope.row.evaluatorTime | parseTime }}</span>
              </template>
            </el-table-column>
            <el-table-column v-if="ss.tableColumns.find(item => item.key === 'evaluator').visible" show-overflow-tooltip
              prop="evaluator" label="提交人" min-width="80" />
            <el-table-column v-if="ss.tableColumns.find(item => item.key === 'createDeptName').visible"
              show-overflow-tooltip prop="createDeptName" label="提交单位" min-width="250" />
            <el-table-column v-if="ss.tableColumns.find(item => item.key === 'dataState').visible" prop="dataState"
              show-overflow-tooltip label="状态" min-width="100" sortable="custom" />
            <el-table-column label="操作" align="center" width="100" fixed="right">
              <template slot-scope="scope">
                <el-button type="text" @click="selectRow(scope.row)">选择</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
        <el-footer style="height: 15px; display: flex; justify-content: center;">
          <!--分页工具栏-->
          <pagination :total="tableQuery.total" :page.sync="tableQuery.page" :limit.sync="tableQuery.limit"
            @pagination="refreshData" />
        </el-footer>
      </el-container>
    </template>

<script>
// 组件
import pagination from '@/view/components/Pagination/PaginationIndex'
import SimpleBoardIndex from "@/view/components/SimpleBoard/SimpleBoardIndex";
import orgTree from '@/view/components/OrgTree/OrgTree'
// vuex状态值
import { mapGetters } from 'vuex'

// 接口api
import ComplianceEvaluationNotificationSlipApi from '@/api/risk/ComplianceEvaluationNotificationSlip.js'
import taskApi from '@/api/_system/task'

export default {
  name: 'HgpjIndex',
  inject: ["layout"],
  components: { pagination, SimpleBoardIndex, orgTree },
  props: {
    evaluationObjectType: {
      type: String, // 根据实际类型调整（如 Number、Array 等）
      required: true, // 如果必传
      default: ''    // 默认值（非必传时可选）
    }
  },
  data() {
    return {
      tableQuery: {
        page: 1,
        limit: 10,
        total: 0,
        evaluationObjectType: null, // 评价对象类型
        evaluationObject: null, // 评价对象
        evaluationYear: null, // 评价年度
        fuzzyValue: null, // 模糊搜索值
        dataState: "审批完成",//状态编码
      },
      yearOptions: Array.from({ length: 10 }, (_, i) => new Date().getFullYear() - i),
      tableData: [],
      dialogVisible: false,
      orgTreeDialog: false,
      zxcheckedData: [],
      orgVisible: false,
      tableLoading: false,
      ss: {
        data: this.tableData,
        tableColumns: [
          { key: 'code', label: '编码', visible: true },
          { key: 'evaluationObjectType', label: '评价对象类型', visible: true },
          { key: 'evaluationObject', label: '评价对象', visible: true },
          { key: 'evaluationYear', label: '评价年度', visible: true },
          { key: 'evaluationConclusion', label: '评价结论', visible: true },
          { key: 'evaluatorTime', label: '评价时间', visible: true },
          { key: 'evaluator', label: '提交人', visible: true },
          { key: 'createDeptName', label: '提交单位', visible: true },
          { key: 'plannedStartTime', label: '计划开始时间', visible: true },
          { key: 'plannedEndTime', label: '计划结束时间', visible: true },
          { key: 'dataState', label: '状态', visible: true },
        ]
      },
    }
  },
  computed: {
    ...mapGetters([
      'orgContext', 'currentFunctionId'
    ])
  },
  activated() {
    // 长连接页面第二次激活的时候,不会走created方法,会走此方法
    this.refreshData()
  },
  created() {
    this.refreshData()
  },
  mounted() {
    this.$nextTick(function () {
      this.table_height = window.innerHeight - this.$refs.table.$el.offsetTop - 84 - 45

      // 监听窗口大小变化
      const self = this
      window.onresize = function () {
        self.table_height = window.innerHeight - self.$refs.table.$el.offsetTop - 84 - 45
      }
    })
  },
  methods: {
    isEdit(row) {
      return row.dataStateCode === this.utils.dataState_BPM.SAVE.code || row.dataStateCode === this.utils.dataState_BPM.BACK.code
    },
    isDelete(row) {
      return row.dataStateCode === this.utils.dataState_BPM.SAVE.code
    },
    // 刷新数据
    refreshData() {
      // 赋值当前人组织全路径
      this.tableQuery.functionCode = this.currentFunctionId.functionCode
      this.tableQuery.orgId = this.orgContext.currentOrgId
      this.tableQuery.currentPsnFullId = this.orgContext.currentPsnFullId
      this.tableQuery.evaluationObjectType = this.evaluationObjectType;
      console.log("this.tableQuery.evaluationObjectType",this.tableQuery.evaluationObjectType)
      // 主要用于删除,当前页只有一条数据的时候,删除需要回到上一页
      if (this.tableData.length === 0 && this.tableQuery.page > 1) {
        this.tableQuery.page--
      }
      ComplianceEvaluationNotificationSlipApi.query(this.tableQuery).then(response => {
        let rows = response.data.data.records
        this.tableData = rows
        this.ss.data = rows
        this.tableQuery.total = response.data.data.total
        this.tableLoading = false
      }).catch({})
    },

    rowDblclick(row, column, event) {
      this.selectRow(row);
    },
    selectRow(row) {
      this.$emit('rowDblclick', row);
    },
    tableSort(column, prop, order) {
      this.tableQuery.sortName = column.prop
      this.tableQuery.order = column.order === "ascending"
      this.refreshData()
    },
    // 点击搜索按钮事件,回到第一页,重新刷新数据
    search_: function () {
      this.tableQuery.page = 1
      this.refreshData()
    },
    // 点击清空按钮事件,清空查询条件属性,回到第一页,重新刷新数据
    empty_() {
      // 清空搜索条件
      this.tableQuery = {
        page: 1,
        limit: 10,
        total: 0,
        evaluationObjectType: '',
        evaluationObject: '',
        evaluationYear: '',
      };
      this.refreshData();
    },
    // 点击刷新按钮事件
    refresh_() {
      this.tableQuery.sortName = null
      this.tableQuery.order = null
      this.empty_()
    },
    showOrgTreeDialog() {
      this.dialogVisible = true;
    },
    cancel() {
      this.dialogVisible = false
    },
    choiceDeptSure() {
      let selectedUnits = this.zxcheckedData.map(item => item.name).join(', ');
      this.tableQuery.reportingUnit = selectedUnits;
      this.dialogVisible = false;
    },
  }
}
</script>

<style scoped>
/* .el-table__fixed-body-wrapper {
  top: 50px !important;
} */
</style>
