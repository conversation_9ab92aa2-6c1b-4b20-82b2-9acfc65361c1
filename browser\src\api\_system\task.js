import {request} from '@/api/index'

export default {
    /**
     * 查待办
     * @param data
     * @returns {*}
     */
    selectTask(data) {
        return request({
            url: '/sys_task/selectTask',
            method: 'post',
            data
        })
    },
    /**
     * 查已办
     * @param data
     * @returns {*}
     */
    selectHistoryTask(data) {
        return request({
            url: '/sys_task/selectHistoryTask',
            method: 'post',
            data
        })
    },
    /**
     * 查询业务历史所有代办数据id
     * @param data
     * @returns {*}
     */
    selectAllProcessId(data) {
        return request({
            url: '/sys_task/selectAllProcessId',
            method: 'post',
            data
        })
    },

    /**
     * mcp方法查询待办
     * @param data
     * @returns {*}
     */
    unfinished(data) {
        return request({
            url: '/wfl/query/my/task/unfinished',
            method: 'post',
            data
        })
    },
    /**
     * mcp方法查询已办
     * @param data
     * @returns {*}
     */
    finished(data) {
        return request({
            url: '/wfl/query/my/task/finished',
            method: 'post',
            data
        })
    },

    /**
     * mcp方法创建待办
     * @param data
     * @returns {*}
     */
    tasks(data) {
        return request({
            url: '/runtime/tasks',
            method: 'post',
            data
        })
    },

    /**
     * mcp方法 流程流转（待办会变已办）
     * @param data
     *          activityConfig	Array[活动配置]
     *          comment	string	审批意见
     *          dsName	string	数据源名称
     *          dsType	string	数据类型
     *          extParams	object	其他参数
     *          formData	Array[object]	表单数据
     *          handlerName	string	处理器名称
     *          argetKey	string	回退key
     *          taskId	string	流程任务id                                                        必填
     *          variables	object	流程变量
     *          -----------------------------------参考---------------------------------------------------
     *          {
	                "activityConfig": [
                        {
                            "activityId": "Activity_0nz1zgt",
                            "assigneeList": []
                        },
                        {
                            "activityId": "Activity_17hkc98",
                            "assigneeList": []
                        }
	                ],
                "taskId": "195561",
                "variables": {
                    "formData": {
                        "CESHI_LI": {
                            "PANDUAN": "0",
                            "DATE": "2021-05-25 16:00:00",
                            "JINE": 1999,
                            "TEXT": "wch33",
                            "_token": "5943b97fb40a9a70286432530d071fc7",
                            "objectVersionNumber": 2,
                            "sid": 10215
                        }
                    },
                    "carbonCopy": "Y"
                },
                "comment": "",
                "dsName": "CESHI_LI",
                "dsType": "MD",
                "formData": [
                    {
                        "PANDUAN": "0",
                        "DATE": "2021-05-25 16:02:00",
                        "JINE": 100000,
                        "TEXT": "wch3",
                        "_token": "5943b97fb40a9a70286432530d071fc7",
                        "objectVersionNumber": 2,
                        "sid": 10215,
                        "tableName": "CESHI_LI",
                        "__status": "update"
                    }
                ]
    }
     * @returns {*}
     */
    completeTask(data) {
        return request({
            url: '/wfl/runtime/completeTask',
            method: 'post',
            data
        })
    },

    /**
     *
     * 根据businessKey查taskId,processInstanceId
     * @param data
     * @return {*}
     */
    selectTaskId(data) {
        return request({
            url: '/sys_task/selectTaskId',
            method: 'post',
            data
        })
    },

    /**
     * 根据functionCode查询functionID
     * @param data
     * @return {*}
     */
    selectFunctionId(data){
        return request({
            url: '/sys_task/selectFunctionId',
            method: 'post',
            data
        })
    },

    /**
     * 根据taskID判断流程是否是退回到首节点
     */
    queryHistoricalNode(data) {
        return request({
            url: '/sys_task/queryHistoricalNode',
            method: 'post',
            data
        })
    },
    getRead(data) {
        return request({
            url: '/wfl/CarbonCopy/my/Task/CarbonCopy',
            method: 'post',
            data
        })
    },
    getUnRead(data) {
        return request({
            url: '/wfl/CarbonCopy/my/Task/CarbonCopy',
            method: 'post',
            data
        })
    },
    CarbonCopyRead(data) {
        return request({
            url: 'wfl/CarbonCopy/CarbonCopy/Read',
            method: 'post',
            data
        })
    },
}