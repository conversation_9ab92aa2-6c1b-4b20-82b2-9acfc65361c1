{"version": 3, "sources": ["web/kendo.common-material.css"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAKA,MACE,MAAO,IAET,qBACE,QAAS,EAEX,SACE,OAAQ,EACR,QAAS,EACT,OAAQ,EACR,QAAS,EACT,gBAAiB,KACjB,UAAW,KACX,WAAY,KAEd,mBAEA,sBADA,sBAEE,QAAS,GACT,QAAS,MACT,MAAO,KACP,WAAY,OACZ,OAAQ,EACR,SAAU,OAEZ,aAEA,gBADA,gBAEE,QAAS,aAEX,aAEA,gBADA,gBAEE,QAAS,MAeX,gBAIA,8BAhBA,SACA,UAmBA,cAPA,iBAIA,+BAdA,eAEA,mBAHA,UAOA,sBAMA,gBAIA,8BAZA,cAOA,eAIA,6BAVA,0BAEA,WACA,iBAPA,WAEA,WAeE,kBAAmB,OACnB,oBAAqB,EAAE,OAEzB,cACE,gBAAiB,KAEnB,2BACE,MAAO,QAQT,UAJA,oBACA,sBAEA,wBADA,WAHA,iBAME,UAAW,KACX,YAAa,QACb,aAAc,MACd,aAAc,IACd,mBAAoB,KAGtB,SAEA,cADA,gBAFA,UAIE,aAAc,MACd,aAAc,IACd,mBAAoB,KAEtB,SACA,UACE,YAAa,OACb,QAAS,EAGX,2BADA,sBAEE,MAAO,EACP,OAAQ,EAGV,SACE,QAAS,IAGX,UACE,QAAS,aACT,OAAQ,EACR,QAAS,KAAK,KACd,YAAa,QACb,YAAa,OACb,WAAY,OACZ,OAAQ,QACR,gBAAiB,KAGnB,2BAKA,kCAFA,iCAJA,oBAEA,4BAGA,mCAFA,kCAIE,OAAQ,QAEV,WACE,oBAAqB,KAClB,iBAAkB,KACjB,gBAAiB,KACb,YAAa,KACrB,gBAAiB,KAGnB,qBACE,eAAgB,MAChB,YAAa,MAEf,kCACA,iCACE,QAAS,EACT,OAAQ,EAEV,kBACE,QAAS,MAEX,uBACA,mCACA,mCACA,kCACE,MAAO,KAET,oBACA,2BACE,aAAc,KACd,cAAe,KAEjB,mBACE,SAAU,QAGZ,8BACE,cAAe,KAEjB,2BACA,4BACA,6BACE,aAAc,IACd,aAAc,MACd,YAAa,KACb,YAAa,OAEf,oCACA,qCACE,eAAgB,SAElB,yBACE,WAAY,cAEZ,MAAuxR,QACvxR,aAAc,EAEhB,+CACE,MAAO,QAGT,QACE,OAAQ,QACR,QAAS,EACT,gBAAiB,KAEnB,2BACE,OAAQ,QAGV,kBAGA,4BACA,gCAFA,0BADA,0BAIA,yCACE,OAAQ,kBACR,QAAS,EAEX,aACE,kBACA,2BACE,QAAS,aAGb,eACE,aAAc,MAEhB,eACE,WAAY,OAKd,6BAFA,QACA,UAEE,QAAS,aACT,MAAO,KACP,OAAQ,KACR,SAAU,OACV,kBAAmB,UACnB,UAAW,EACX,YAAa,EACb,WAAY,OACZ,yBAA0B,KAE5B,iBACE,iBAAkB,eAMpB,oCAFA,eACA,iBAEE,eAAgB,OAIlB,qCAFA,gBACA,kBAEE,eAAgB,OAElB,QACA,UACE,iBAAkB,YAEpB,qDACE,oBAAqB,EAAE,KAEzB,qDACE,oBAAqB,EAAE,MAEzB,+BACE,oBAAqB,MAAM,EAO7B,gDAFA,sEAHA,oEAEA,gDADA,8CAGA,gDAEE,oBAAqB,MAAM,EAE7B,4DACA,0DACE,oBAAqB,MAAM,KAE7B,+BACE,oBAAqB,MAAM,MAO7B,gDAFA,sEAHA,oEAEA,gDADA,8CAGA,gDAEE,oBAAqB,MAAM,MAE7B,4DACA,0DACE,oBAAqB,MAAM,MAE7B,iDACE,oBAAqB,EAAI,EAE3B,iDACE,oBAAqB,EAAI,MAE3B,mDACE,oBAAqB,EAAE,OAEzB,mDACE,oBAAqB,EAAE,OAEzB,uDACE,oBAAqB,MAAM,MAE7B,uEACE,oBAAqB,MAAM,OAE7B,aACE,oBAAqB,EAAI,EAO3B,8BAFA,oDAHA,kDAEA,8BADA,4BAGA,8BAEE,oBAAqB,EAAI,EAE3B,aACE,oBAAqB,EAAI,MAO3B,8BAFA,oDAHA,kDAEA,8BADA,4BAGA,8BAEE,oBAAqB,EAAI,MAE3B,oBACE,oBAAqB,EAAI,MAO3B,qCAFA,2DAHA,yDAEA,qCADA,mCAGA,qCAEE,oBAAqB,MAAM,MAC3B,oBAAqB,EAAI,MAE3B,aACE,oBAAqB,EAAI,MAO3B,8BAFA,oDAHA,kDAEA,8BADA,4BAGA,8BAEE,oBAAqB,EAAI,MAE3B,aACE,oBAAqB,EAAI,MAO3B,8BAFA,oDAHA,kDAEA,8BADA,4BAGA,8BAEE,oBAAqB,EAAI,MAE3B,oBACE,oBAAqB,EAAI,MAO3B,qCAFA,2DAHA,yDAEA,qCADA,mCAGA,qCAEE,oBAAqB,MAAM,MAC3B,oBAAqB,EAAI,MAE3B,YACE,oBAAqB,EAAI,MAO3B,6BAFA,mDAHA,iDAEA,6BADA,2BAGA,6BAEE,oBAAqB,EAAI,MAE3B,YACE,oBAAqB,EAAI,MAO3B,6BAFA,mDAHA,iDAEA,6BADA,2BAGA,6BAEE,oBAAqB,EAAI,MAE3B,mBACE,oBAAqB,EAAI,MAO3B,oCAFA,0DAHA,wDAEA,oCADA,kCAGA,oCAEE,oBAAqB,MAAM,MAC3B,oBAAqB,EAAI,MAE3B,YACE,oBAAqB,EAAI,MAO3B,6BAFA,mDAHA,iDAEA,6BADA,2BAGA,6BAEE,oBAAqB,EAAI,MAE3B,YACE,oBAAqB,EAAI,OAO3B,6BAFA,mDAHA,iDAEA,6BADA,2BAGA,6BAEE,oBAAqB,EAAI,OAE3B,mBACE,oBAAqB,EAAI,OAO3B,oCAFA,0DAHA,wDAEA,oCADA,kCAGA,oCAEE,oBAAqB,MAAM,OAC3B,oBAAqB,EAAI,OAE3B,cACE,oBAAqB,EAAE,OAOzB,+BAFA,qDAHA,mDAEA,+BADA,6BAGA,+BAEE,oBAAqB,MAAM,OAE7B,cACE,oBAAqB,EAAI,OAO3B,+BAFA,qDAHA,mDAEA,+BADA,6BAGA,+BAEE,oBAAqB,EAAI,OAE3B,cACE,oBAAqB,EAAE,OAOzB,+BAFA,qDAHA,mDAEA,+BADA,6BAGA,+BAEE,oBAAqB,MAAM,OAE7B,cACE,oBAAqB,EAAI,OAO3B,+BAFA,qDAHA,mDAEA,+BADA,6BAGA,+BAEE,oBAAqB,EAAI,OAE3B,iBACE,oBAAqB,EAAI,OAO3B,kCAFA,wDAHA,sDAEA,kCADA,gCAGA,kCAEE,oBAAqB,EAAI,OAE3B,iBACE,oBAAqB,EAAI,OAO3B,kCAFA,wDAHA,sDAEA,kCADA,gCAGA,kCAEE,oBAAqB,EAAI,OAE3B,iBACE,oBAAqB,EAAI,OAO3B,kCAFA,wDAHA,sDAEA,kCADA,gCAGA,kCAEE,oBAAqB,EAAI,OAE3B,iBACE,oBAAqB,EAAI,OAO3B,kCAFA,wDAHA,sDAEA,kCADA,gCAGA,kCAEE,oBAAqB,EAAI,OAE3B,YACA,QACA,iBACE,oBAAqB,EAAI,OAiB3B,6BACA,yBACA,kCARA,mDACA,+CACA,wDAXA,iDACA,6CACA,sDAIA,6BACA,yBACA,kCALA,2BACA,uBACA,gCAOA,6BACA,yBACA,kCAIE,oBAAqB,EAAI,OAE3B,cACA,mBACA,eACA,wBACE,oBAAqB,EAAI,OAsB3B,+BACA,oCACA,gCACA,yCAXA,qDACA,0DACA,sDACA,+DAfA,mDACA,wDACA,oDACA,6DAKA,+BACA,oCACA,gCACA,yCAPA,6BACA,kCACA,8BACA,uCASA,+BACA,oCACA,gCACA,yCAKE,oBAAqB,EAAI,OAE3B,cACA,SACA,kBACE,oBAAqB,EAAI,OAiB3B,+BACA,0BACA,mCARA,qDACA,gDACA,yDAXA,mDACA,8CACA,uDAIA,+BACA,0BACA,mCALA,6BACA,wBACA,iCAOA,+BACA,0BACA,mCAIE,oBAAqB,EAAI,OAE3B,gBACA,qBACA,gBACA,yBACE,oBAAqB,EAAI,OAsB3B,iCACA,sCACA,iCACA,0CAXA,uDACA,4DACA,uDACA,gEAfA,qDACA,0DACA,qDACA,8DAKA,iCACA,sCACA,iCACA,0CAPA,+BACA,oCACA,+BACA,wCASA,iCACA,sCACA,iCACA,0CAKE,oBAAqB,EAAI,OAG3B,QACA,UAFA,YAGE,oBAAqB,MAAM,EAkB7B,yBACA,2BAFA,6BALA,+CACA,iDAFA,mDARA,6CACA,+CAFA,iDAOA,yBACA,2BAFA,6BAFA,uBACA,yBAFA,2BAUA,yBACA,2BAFA,6BAME,oBAAqB,MAAM,EAG7B,UACA,gBAFA,WAGA,YACA,kBACE,oBAAqB,MAAM,MA4B7B,2BACA,iCAFA,4BAGA,6BACA,mCAbA,iDACA,uDAFA,kDAGA,mDACA,yDAlBA,+CACA,qDAFA,gDAGA,iDACA,uDAOA,2BACA,iCAFA,4BAGA,6BACA,mCARA,yBACA,+BAFA,0BAGA,2BACA,iCAYA,2BACA,iCAFA,4BAGA,6BACA,mCAME,oBAAqB,MAAM,MAE7B,UACA,gBACA,YACA,kBACE,oBAAqB,MAAM,MAsB7B,2BACA,iCACA,6BACA,mCAXA,iDACA,uDACA,mDACA,yDAfA,+CACA,qDACA,iDACA,uDAKA,2BACA,iCACA,6BACA,mCAPA,yBACA,+BACA,2BACA,iCASA,2BACA,iCACA,6BACA,mCAKE,oBAAqB,MAAM,MAE7B,YACE,oBAAqB,OAAO,MAO9B,6BAFA,mDAHA,iDAEA,6BADA,2BAGA,6BAEE,oBAAqB,OAAO,MAE9B,yBACE,oBAAqB,OAAO,MAE9B,wCACE,oBAAqB,OAAO,MAE9B,UACA,UACA,UACE,oBAAqB,MAAM,MAiB7B,2BACA,2BACA,2BARA,iDACA,iDACA,iDAXA,+CACA,+CACA,+CAIA,2BACA,2BACA,2BALA,yBACA,yBACA,yBAOA,2BACA,2BACA,2BAIE,oBAAqB,MAAM,MAE7B,iBACA,6CACA,0CACE,oBAAqB,MAAM,MAiB7B,kCACA,8DACA,2DARA,wDACA,oFACA,iFAXA,sDACA,kFACA,+EAIA,kCACA,8DACA,2DALA,gCACA,4DACA,yDAOA,kCACA,8DACA,2DAIE,oBAAqB,MAAM,MAG7B,UACA,UAFA,YAGE,oBAAqB,MAAM,MAkB7B,2BACA,2BAFA,6BALA,iDACA,iDAFA,mDARA,+CACA,+CAFA,iDAOA,2BACA,2BAFA,6BAFA,yBACA,yBAFA,2BAUA,2BACA,2BAFA,6BAME,oBAAqB,MAAM,MAG7B,OADA,UAEE,oBAAqB,MAAM,MAa7B,wBADA,2BAHA,8CADA,iDALA,4CADA,+CAKA,wBADA,2BADA,sBADA,yBAOA,wBADA,2BAIE,oBAAqB,MAAM,MAG7B,UADA,YAEE,oBAAqB,MAAM,MAa7B,2BADA,6BAHA,iDADA,mDALA,+CADA,iDAKA,2BADA,6BADA,yBADA,2BAOA,2BADA,6BAIE,oBAAqB,MAAM,MAG7B,gBADA,kBAEE,oBAAqB,MAAM,MAa7B,iCADA,mCAHA,uDADA,yDALA,qDADA,uDAKA,iCADA,mCADA,+BADA,iCAOA,iCADA,mCAIE,oBAAqB,MAAM,MAE7B,UACE,oBAAqB,MAAM,EAO7B,2BAFA,iDAHA,+CAEA,2BADA,yBAGA,2BAEE,oBAAqB,MAAM,EAE7B,YACE,oBAAqB,MAAM,MAO7B,6BAFA,mDAHA,iDAEA,6BADA,2BAGA,6BAEE,oBAAqB,MAAM,MAE7B,aACE,oBAAqB,MAAM,OAO7B,8BAFA,oDAHA,kDAEA,8BADA,4BAGA,8BAEE,oBAAqB,MAAM,OAE7B,eACE,oBAAqB,OAAO,OAO9B,gCAFA,sDAHA,oDAEA,gCADA,8BAGA,gCAEE,oBAAqB,OAAO,OAE9B,aACE,oBAAqB,MAAM,OAO7B,8BAFA,oDAHA,kDAEA,8BADA,4BAGA,8BAEE,oBAAqB,MAAM,OAE7B,cACE,oBAAqB,MAAM,OAO7B,+BAFA,qDAHA,mDAEA,+BADA,6BAGA,+BAEE,oBAAqB,MAAM,OAE7B,cACE,oBAAqB,MAAM,OAO7B,+BAFA,qDAHA,mDAEA,+BADA,6BAGA,+BAEE,oBAAqB,MAAM,OAE7B,SACE,oBAAqB,OAAO,OAO9B,0BAFA,gDAHA,8CAEA,0BADA,wBAGA,0BAEE,oBAAqB,OAAO,OAE9B,WACE,oBAAqB,OAAO,OAO9B,4BAFA,kDAHA,gDAEA,4BADA,0BAGA,4BAEE,oBAAqB,OAAO,OAE9B,aACE,oBAAqB,MAAM,OAO7B,8BAFA,oDAHA,kDAEA,8BADA,4BAGA,8BAEE,oBAAqB,MAAM,OAE7B,cACE,oBAAqB,MAAM,OAO7B,+BAFA,qDAHA,mDAEA,+BADA,6BAGA,+BAEE,oBAAqB,MAAM,OAE7B,WACE,oBAAqB,MAAM,OAO7B,4BAFA,kDAHA,gDAEA,4BADA,0BAGA,4BAEE,oBAAqB,MAAM,OAE7B,WACE,oBAAqB,MAAM,OAO7B,4BAFA,kDAHA,gDAEA,4BADA,0BAGA,4BAEE,oBAAqB,MAAM,OAE7B,YACE,oBAAqB,MAAM,OAO7B,6BAFA,mDAHA,iDAEA,6BADA,2BAGA,6BAEE,oBAAqB,MAAM,OAE7B,YACE,oBAAqB,MAAM,OAO7B,6BAFA,mDAHA,iDAEA,6BADA,2BAGA,6BAEE,oBAAqB,MAAM,OAE7B,YACE,oBAAqB,OAAO,OAO9B,6BAFA,mDAHA,iDAEA,6BADA,2BAGA,6BAEE,oBAAqB,OAAO,OAE9B,sBACE,oBAAqB,OAAO,OAE9B,YACE,oBAAqB,OAAO,OAE9B,cACA,cACE,oBAAqB,OAAO,MAY9B,+BACA,+BALA,qDACA,qDAPA,mDACA,mDAGA,+BACA,+BAHA,6BACA,6BAKA,+BACA,+BAGE,oBAAqB,OAAO,MAE9B,cACA,iBACE,oBAAqB,OAAO,MAY9B,+BACA,kCALA,qDACA,wDAPA,mDACA,sDAGA,+BACA,kCAHA,6BACA,gCAKA,+BACA,kCAGE,oBAAqB,OAAO,MAE9B,cACA,iBACE,oBAAqB,OAAO,MAY9B,+BACA,kCALA,qDACA,wDAPA,mDACA,sDAGA,+BACA,kCAHA,6BACA,gCAKA,+BACA,kCAGE,oBAAqB,OAAO,MAE9B,aACE,oBAAqB,EAAE,OAEzB,UACA,WACE,oBAAqB,OAAO,OAY9B,2BACA,4BALA,iDACA,kDAPA,+CACA,gDAGA,2BACA,4BAHA,yBACA,0BAKA,2BACA,4BAGE,oBAAqB,OAAO,OAE9B,cACE,oBAAqB,OAAO,OAO9B,+BAFA,qDAHA,mDAEA,+BADA,6BAGA,+BAEE,oBAAqB,OAAO,OAE9B,eACE,oBAAqB,OAAO,OAO9B,gCAFA,sDAHA,oDAEA,gCADA,8BAGA,gCAEE,oBAAqB,OAAO,OAE9B,WACE,oBAAqB,OAAO,OAO9B,4BAFA,kDAHA,gDAEA,4BADA,0BAGA,4BAEE,oBAAqB,OAAO,OAE9B,aACE,oBAAqB,OAAO,OAO9B,8BAFA,oDAHA,kDAEA,8BADA,4BAGA,8BAEE,oBAAqB,OAAO,OAE9B,aACE,oBAAqB,OAAO,OAO9B,8BAFA,oDAHA,kDAEA,8BADA,4BAGA,8BAEE,oBAAqB,OAAO,OAE9B,WACE,oBAAqB,MAAM,MAO7B,4BAFA,kDAHA,gDAEA,4BADA,0BAGA,4BAEE,oBAAqB,MAAM,MAE7B,WACE,oBAAqB,MAAM,MAO7B,4BAFA,kDAHA,gDAEA,4BADA,0BAGA,4BAEE,oBAAqB,MAAM,MAE7B,SACE,oBAAqB,MAAM,MAO7B,0BAFA,gDAHA,8CAEA,0BADA,wBAGA,0BAEE,oBAAqB,MAAM,MAE7B,SACE,oBAAqB,MAAM,MAO7B,0BAFA,gDAHA,8CAEA,0BADA,wBAGA,0BAEE,oBAAqB,MAAM,MAE7B,WACE,oBAAqB,MAAM,MAO7B,4BAFA,kDAHA,gDAEA,4BADA,0BAGA,4BAEE,oBAAqB,MAAM,MAE7B,cACE,oBAAqB,MAAM,OAO7B,+BAFA,qDAHA,mDAEA,+BADA,6BAGA,+BAEE,oBAAqB,MAAM,OAE7B,eACE,oBAAqB,MAAM,OAO7B,gCAFA,sDAHA,oDAEA,gCADA,8BAGA,gCAEE,oBAAqB,MAAM,OAE7B,UACE,oBAAqB,MAAM,OAO7B,2BAFA,iDAHA,+CAEA,2BADA,yBAGA,2BAEE,oBAAqB,MAAM,OAE7B,UACE,oBAAqB,MAAM,OAO7B,2BAFA,iDAHA,+CAEA,2BADA,yBAGA,2BAEE,oBAAqB,MAAM,OAE7B,WACE,oBAAqB,MAAM,OAO7B,4BAFA,kDAHA,gDAEA,4BADA,0BAGA,4BAEE,oBAAqB,MAAM,OAE7B,eACE,oBAAqB,MAAM,OAO7B,gCAFA,sDAHA,oDAEA,gCADA,8BAGA,gCAEE,oBAAqB,MAAM,OAE7B,SACE,oBAAqB,MAAM,OAO7B,0BAFA,gDAHA,8CAEA,0BADA,wBAGA,0BAEE,oBAAqB,MAAM,OAE7B,eACE,oBAAqB,MAAM,OAO7B,gCAFA,sDAHA,oDAEA,gCADA,8BAGA,gCAEE,oBAAqB,MAAM,OAE7B,QACE,oBAAqB,EAAI,EAO3B,yBAFA,+CAHA,6CAEA,yBADA,uBAGA,yBAEE,oBAAqB,EAAI,EAG3B,aADA,gBAEE,oBAAqB,MAAM,OAa7B,8BADA,iCAHA,oDADA,uDALA,kDADA,qDAKA,8BADA,iCADA,4BADA,+BAOA,8BADA,iCAIE,oBAAqB,MAAM,OAG7B,QADA,eAEE,oBAAqB,MAAM,OAa7B,yBADA,gCAHA,+CADA,sDALA,6CADA,oDAKA,yBADA,gCADA,uBADA,8BAOA,yBADA,gCAIE,oBAAqB,MAAM,OAE7B,UACE,oBAAqB,MAAM,MAO7B,2BAFA,iDAHA,+CAEA,2BADA,yBAGA,2BAEE,oBAAqB,MAAM,MAE7B,kBACE,oBAAqB,MAAM,OAO7B,mCAFA,yDAHA,uDAEA,mCADA,iCAGA,mCAEE,oBAAqB,MAAM,OAE7B,gBACE,oBAAqB,OAAO,MAE9B,SACE,OAAQ,EAEV,kCACE,oBAAqB,EAAE,EAEzB,kCACE,oBAAqB,EAAE,MAKzB,6CAFA,+CACA,0EAEE,oBAAqB,EAAI,MAmB3B,8DAFA,gEACA,2FALA,oFAFA,sFACA,iHARA,kFAFA,oFACA,+GAOA,8DAFA,gEACA,2FAFA,4DAFA,8DACA,yFAUA,8DAFA,gEACA,2FAKE,oBAAqB,EAAI,MAG3B,wBACE,MAAO,QACP,aAAc,QACd,iBAAkB,QAEpB,qBACE,MAAO,QACP,aAAc,QACd,iBAAkB,QAEpB,sBACE,MAAO,QACP,aAAc,QACd,iBAAkB,QAEpB,gBACE,QAAS,EAAE,IAGb,WACA,iBACE,iBAAkB,YAClB,kBAAmB,UACnB,oBAAqB,OAAO,OAG9B,iBADA,gBAEA,gBACE,SAAU,SAEZ,gBACE,QAAS,IAEX,oCACE,OAAQ,KACR,SAAU,SACV,IAAK,EACL,OAAQ,EACR,KAAM,EACN,MAAO,EAET,gBACE,YAAa,QACb,WAAY,OAId,iBADA,iBAEE,MAAO,KACP,OAAQ,KAEV,iBACE,IAAK,EACL,KAAM,EACN,QAAS,EAEX,iBACE,OAAQ,kBACR,QAAS,GAEX,iBACE,OAAQ,EACR,MAAO,KACP,OAAQ,KAEV,4BACE,OAAQ,iBACR,QAAS,EACT,SAAU,SAGZ,aACE,SAAU,SACV,QAAS,MACT,aAAc,MACd,aAAc,IACd,UAAW,KACX,QAAS,KAAK,KACd,YAAa,OACb,OAAQ,QAEV,eACE,WAAY,KACZ,aAAc,IACd,eAAgB,OAElB,eACE,SAAU,SACV,MAAO,IACP,SAAU,QAEZ,uBACE,SAAU,SACV,KAAM,KACN,MAAO,IACP,OAAQ,IAEV,4BACE,IAAK,KACL,oBAAqB,KAAK,OAE5B,4BACE,OAAQ,KACR,oBAAqB,KAAK,OAG5B,aACE,SAAU,SACV,SAAU,OAEZ,sBACE,IAAK,EACL,MAAO,EACP,MAAO,KAEP,OAA4iS,KAC5iS,WAAY,OAEd,mBACE,QAAS,KACT,SAAU,SACV,QAAS,OACT,OAAQ,IACR,MAAO,IACP,OAAQ,IAAI,MAAM,QAClB,iBAAkB,QAEpB,0DACE,wBACE,OAAQ,KACR,MAAO,KACP,cAAe,KAGnB,2BACE,WAAY,KAId,gBACE,WAAY,IACZ,SAAU,SAEZ,2BACE,MAAO,EACP,OAAQ,EACR,WAAY,IACZ,cAAe,IAAI,MAAM,YACzB,WAAY,IAAI,MAAM,IACtB,YAAa,IAAI,MAAM,YACvB,aAAc,IAAI,MAAM,YAE1B,2BACE,MAAO,EACP,OAAQ,EACR,WAAY,IACZ,cAAe,IAAI,MAAM,YACzB,WAAY,IAAI,MAAM,YACtB,YAAa,IAAI,MAAM,YACvB,aAAc,IAAI,MAAM,IAE1B,4BACE,MAAO,EACP,OAAQ,EACR,WAAY,IACZ,cAAe,IAAI,MAAM,YACzB,WAAY,IAAI,MAAM,YACtB,YAAa,IAAI,MAAM,IACvB,aAAc,IAAI,MAAM,YAG1B,uBAGA,yBAEA,8BACA,mBACA,kBANA,UACA,YAEA,kBAIE,mBAAoB,YACZ,WAAY,YAItB,gBAGA,SALA,UAMA,uBACA,2BACA,2BAJA,4CAHA,WAQA,mBACA,SAPA,qBAQE,mBAAoB,WACZ,WAAY,WAGtB,2BACE,mBAAoB,WACZ,WAAY,WAEtB,uBACE,QAAS,EAEX,iBACE,gBAAiB,KAGnB,WACA,aACE,wBAAyB,WACzB,gBAAiB,WAGnB,SADA,iBAEE,QAAS,OACT,QAAS,EACT,MAAO,EACP,OAAQ,EACR,mBAAoB,KACpB,SAAU,OAGZ,eADA,uBAEE,SAAU,SAEZ,kBACE,SAAU,SACV,aAAc,MACd,eAAgB,OAChB,YAAa,OACb,OAAQ,QAEV,yBACE,QAAS,GACT,SAAU,SACV,IAAK,EACL,KAAM,EACN,aAAc,IACd,aAAc,MACd,MAAO,IACP,OAAQ,IACR,UAAW,IACX,YAAa,IACb,WAAY,OAEd,kDACE,QAAS,GACT,SAAU,SACV,KAAM,IACN,IAAK,IACL,kBAAmB,qBACnB,cAAe,qBACX,UAAW,qBACf,aAAc,IACd,aAAc,MACd,MAAO,IACP,OAAQ,IACR,UAAW,IACX,WAAY,OACZ,QAAS,IACT,YAAa,KAEf,6CACE,QAAS,QAEX,uCACE,OAAQ,KAEV,eACE,SAAU,SACV,aAAc,MACd,eAAgB,OAChB,YAAa,OACb,OAAQ,QAEV,sBACE,QAAS,GACT,SAAU,SACV,IAAK,EACL,KAAM,EACN,MAAO,KACP,OAAQ,KACR,aAAc,MAEhB,sCACE,QAAS,GACT,MAAO,KACP,OAAQ,KACR,SAAU,SACV,IAAK,IACL,KAAM,IAER,iCACE,OAAQ,KAGV,gBADA,wBAEE,QAAS,aACT,MAAO,KAET,yBACA,sBACE,aAAc,EAEhB,gCAEA,4BADA,6BAEE,QAAS,KAGX,yBACA,sBACE,cAAe,MAEjB,gCACA,6BACE,MAAO,EAET,6CACE,MAAO,IAET,uBACE,oBAAqB,KAGvB,qBACE,SAAU,SACV,SAAU,OACV,KAAM,SACN,MAAO,QAET,oBACE,SAAU,KACV,2BAA4B,MAC5B,iBAAkB,MAAM,MACxB,mBAAoB,yBACpB,qBAAsB;;;;;;;;;;;;;;;;;;;;;;;AAIxB,eACE,kBAAmB,kBAAkB,cACrC,cAAe,kBAAkB,cACjC,UAAW,kBAAkB,cAC7B,KAAM,EAER,gBACE,kBAAmB,iBAAiB,cACpC,cAAe,iBAAiB,cAChC,UAAW,iBAAiB,cAC5B,MAAO,EAET,eACA,gBACE,SAAU,MACV,QAAS,MACT,SAAU,KACV,UAAW,MACX,OAAQ,KACR,IAAK,EAEP,iCACA,kCACE,kBAAmB,cAAc,cACjC,cAAe,cAAc,cAC7B,UAAW,cAAc,cAE3B,iBACA,kBACE,SAAU,KAEZ,cACE,SAAU,OACV,WAAY,EAEd,gCACE,WAAY,MACZ,SAAU;;;;;;;;;;;;;;;;;;;;;;;AAGZ,aACE,OAAQ,EACR,QAAS,EAEX,gCACE,QAAS,IAAI,EAEf,iCACE,oBAAqB,EAEvB,uBACE,SAAU,SACV,MAAO,MAGT,qCADA,cAEE,MAAO,KACP,MAAO,KACP,MAAO,IACP,QAAS,KAAK,EAAE,IAChB,YAAa,GACb,WAAY,MAEd,cACA,qCACE,MAAO,MACP,MAAO,MACP,MAAO,IACP,aAAc,GACd,QAAS,EAAE,EAAE,KAEf,mCACA,gCACE,WAAY,KAEd,iCACE,OAAQ,EAAE,MAKZ,oCAHA,+CACA,4CACA,qDAEE,YAAa,EAEf,uCACE,MAAO,KACP,WAAY,MACZ,aAAc,IAAI,EAAE,EACpB,aAAc,MACd,SAAU,SACV,OAAQ,KACR,QAAS;;;;;;;;;;;;;;;;;;;;;;;AAIX,aACE,QAAS,aACT,SAAU,SACV,QAAS,MACT,aAAc,MACd,aAAc,IACd,YAAa,IAEf,mBACA,mBACE,SAAU,SACV,MAAO,KACP,OAAQ,MACR,oBAAqB,MACrB,oBAAqB,IACrB,WAAY,KACZ,QAAS,KAAK,EACd,UAAW,MACX,YAAa,OACb,WAAY,KAGd,mBACE,SAAU,SACV,OAAQ,KAAK,EAAE,KAAK,KACpB,QAAS,KAAK,IAEhB,gBACE,SAAU,SACV,KAAM,MACN,MAAO,MACP,SAAU,OACV,OAAQ,QACR,cAAe,SAEjB,yBACE,OAAQ,EAAE,IAAI,EAAE,EAChB,eAAgB,OAElB,uBACE,YAAa,EAEf,qBACE,SAAU,SACV,OAAQ,KACR,QAAS,MACT,SAAU,KACV,QAAS,EAEX,2BACE,QAAS,EACT,SAAU,QAEZ,uCACE,OAAQ,KAEV,qCACE,SAAU,SACV,IAAK,EACL,MAAO,KACP,YAAa,KACb,YAAa,OAEf,oCACE,QAAS,aACT,MAAO,KACP,OAAQ,KACR,QAAS,IACT,gBAAiB,KACjB,eAAgB,OAChB,QAAS,GAEX,kCACE,aAAc,MACd,aAAc,IACd,QAAS,IACT,QAAS,EAEX,yBACE,OAAQ,EACR,eAAgB,IAElB,2BACE,SAAU,SACV,QAAS,EACT,iBAAkB,KAClB,UAAW,EACX,YAAa,IACb,OAAQ,iBACR,QAAS,EACT,KAAM,EAER,YACE,IAAK,KACL,KAAM,EACN,MAAO,KACP,OAAQ,IACR,OAAQ,SAEV,YACE,IAAK,EACL,MAAO,KACP,MAAO,IACP,OAAQ,KACR,OAAQ,SAEV,YACE,OAAQ,KACR,KAAM,EACN,MAAO,KACP,OAAQ,IACR,OAAQ,SAEV,YACE,IAAK,EACL,KAAM,KACN,MAAO,IACP,OAAQ,KACR,OAAQ,SAEV,aACE,OAAQ,KACR,MAAO,KACP,MAAO,KACP,OAAQ,KACR,OAAQ,UAEV,aACE,OAAQ,KACR,KAAM,KACN,MAAO,IACP,OAAQ,IACR,OAAQ,UAEV,aACE,IAAK,KACL,MAAO,KACP,MAAO,IACP,OAAQ,IACR,OAAQ,UAEV,aACE,IAAK,KACL,KAAM,KACN,MAAO,IACP,OAAQ,IACR,OAAQ,UAEV,WACE,SAAU,MACV,IAAK,EACL,KAAM,EACN,QAAS,MACT,MAAO,KACP,OAAQ,KACR,iBAAkB,KAClB,OAAQ,kBACR,QAAS,GAEX,qBACE,SAAU,SACV,MAAO,KACP,OAAQ,KACR,iBAAkB,KAClB,OAAQ,iBACR,QAAS,EAEX,kBACE,MAAO,KACP,WAAY,MACZ,aAAc,IAAI,EAAE,EACpB,aAAc,MACd,SAAU,SACV,OAAQ,KACR,QAAS,KACT,OAAQ,EAAE,KAEZ,4BACE,QAAS,aACT,OAAQ,EAAE,EAAE,EAAE,IACd,UAAW;;;;;;;;;;;;;;;;;;;;;;;AAIb,YACE,OAAQ,EACR,QAAS,EACT,KAAM,EACN,SAAU,SAEZ,kBACE,QAAS,KAAM,KAAM,EAEvB,yCACE,YAAa,OACb,SAAU,OAEZ,sBACE,SAAU,SACV,IAAK,KACL,QAAS,EACT,oBAAqB,KAClB,iBAAkB,KACjB,gBAAiB,KACb,YAAa,KAEvB,6BACE,IAAK,KACL,OAAQ,KAEV,iBACE,KAAM,KAER,iBACE,MAAO,KAGT,sCADA,0BAEE,gBAAiB,KACjB,QAAS,aACT,SAAU,SACV,aAAc,MACd,aAAc,IAAI,IAAI,EACtB,OAAQ,EAAE,KAAK,EAAE,EACjB,QAAS,EACT,eAAgB,IAIlB,8CADA,kCADA,gCAGE,cAAe,KACf,eAAgB,IAElB,gCACE,QAAS,EAGX,sCADA,0BAEE,QAAS,aACT,oBAAqB,EACrB,QAAS,KAAK,MAGhB,sCADA,0BAEE,OAAQ,KAAK,IAAI,EAAE,KACnB,eAAgB,IAIlB,+CACA,gDAHA,mCACA,oCAGE,OAAQ,KAAK,IAAI,EAAE,KACnB,eAAgB,OAGlB,6BACE,IAAK,EACL,KAAM,EACN,OAAQ,EACR,MAAO,IACP,SAAU,SACV,WAAY,IACZ,WAAY,IAAI,MAAM,YACtB,aAAc,QACd,mBAAoB,MAAM,IAAM,OAChC,WAAY,MAAM,IAAM,OACxB,mBAAoB,qBACpB,WAAY,qBACZ,kBAAmB,aAAa,GAAG,YAAY,SACvC,UAAW,aAAa,GAAG,YAAY,SAEjD,8BACE,kBAAmB,KACX,UAAW,KAErB,wCACE,MAAO,KACP,kBAAmB,KACX,UAAW,KAGrB,mCADA,uBAEE,SAAU,OACV,aAAc,MACd,aAAc,IACd,OAAQ,EAAE,OAAO,KACjB,QAAS,KAAK,MACd,KAAM,EAER,uBACE,QAAS,KACT,SAAU,KAEZ,yCACE,QAAS,EAEX,4CACE,QAAS,KAAK,MAEhB,gCACE,GACE,KAAM,EAER,IACE,KAAM,IAER,KACE,KAAM,GAGV,wBACE,GACE,KAAM,EAER,IACE,KAAM,IAER,KACE,KAAM,GAIV,+BACA,gCACE,OAAQ,OAAO,KAEjB,2CACA,4CACE,QAAS,MACT,cAAe,KAEjB,2CACA,4CACE,QAAS,MAMX,+DACA,gEAHA,mDAFA,iDAGA,oDAFA,kDAKE,cAAe,KACf,eAAgB,EAGlB,mCACE,MAAO,KACP,QAAS,MAAM,EAAE,KAAK,KAExB,2CACE,aAAc,IAAI,EAAE,IAAI,IACxB,cAAe,IAAI,EAAE,EAAE,IAEzB,mDACE,aAAc,IAAI,EAAE,IAAI,IAI1B,+DADA,mDADA,iDAGE,aAAc,KACd,cAAe,IAGjB,oCACE,MAAO,MACP,QAAS,MAAM,KAAK,KAAK,EAE3B,4CACE,aAAc,IAAI,IAAI,IAAI,EAC1B,cAAe,EAAE,IAAI,IAAI,EAE3B,oDACE,aAAc,IAAI,IAAI,IAAI,EAI5B,gEADA,oDADA,kDAGE,YAAa,KACb,aAAc,IAGhB,qCACE,WAAY,KACZ,QAAS,EAAE,KAAK,KAGlB,0CADA,8BAEE,OAAQ,KAAK,OAAO,EACpB,QAAS,EACT,SAAU,SAEZ,6CACE,aAAc,EAAE,IAAI,IACpB,cAAe,EAAE,EAAE,IAAI,IAEzB,qDACE,cAAe,EACf,eAAgB,EAElB,8BACE,WAAY,MAEd,gDACE,IAAK,KACL,OAAQ;;;;;;;;;;;;;;;;;;;;;;;AAIV,YACE,KAAM,EAGR,iBADA,oBAEE,gBAAiB,KACjB,QAAS,MACT,aAAc,EACd,OAAQ,EACR,KAAM,EACN,cAAe,EAEjB,6BACA,8BACE,MAAO,KACP,WAAY,IACZ,aAAc,IACd,eAAgB,OAGlB,yBADA,4BAEE,QAAS,MACT,SAAU,SACV,oBAAqB,MACrB,oBAAqB,IACrB,QAAS,EAAE,IACX,YAAa,OACb,gBAAiB,KACjB,KAAM,EAGR,qBADA,mBAEE,SAAU,SACV,IAAK,IACL,MAAO,IACP,WAAY,KAGd,uBADA,qBAEE,SAAU,SACV,oBAAqB,MACrB,oBAAqB,IACrB,OAAQ,EACR,QAAS,EACT,KAAM,EAER,yBACE,cAAe,EACf,UAAW,MACX,YAAa,IAEf,kCACE,aAAc,IAEhB,gCACE,cAAe,EAEjB,kBACE,cAAe;;;;;;;;;;;;;;;;;;;;;;;AAIjB,QACE,OAAQ,QAEV,QACA,sBACE,WAAY,KACZ,OAAQ,EACR,QAAS,EACT,KAAM,EAER,cACE,QAAS,GACT,QAAS,MACT,MAAO,IACP,OAAQ,EACR,MAAO,QACP,MAAO,KAET,gBACE,oBAAqB,KACrB,iBAAkB,UAClB,gBAAiB,KACb,YAAa,KAEnB,oBACE,oBAAqB,QAClB,iBAAkB,QACjB,gBAAiB,QACb,YAAa,QAEvB,wBACA,2BACE,QAAS,MACT,MAAO,KACP,aAAc,EAGhB,iBADA,gCAEA,kBACE,OAAQ,KAAK,IAAI,EAAE,KACnB,eAAgB,OAElB,gCACE,OAAQ,KAAK,EAAE,EAEjB,wBACE,QAAS,MACT,QAAS,QACT,YAAa,OACb,oBAAqB,KAClB,iBAAkB,KACjB,gBAAiB,KACb,YAAa,KAEvB,sBACE,QAAS,KACT,aAAc,MACd,aAAc,IACd,SAAU,QACV,YAAa,OAEf,8BACE,QAAS,MACT,aAAc,EAEhB,gBACA,oCACE,SAAU,SACV,MAAO,KACP,aAAc,MACd,aAAc,EAAE,IAAI,EAAE,EACtB,eAAgB,IAChB,KAAM,EACN,mBAAoB,YACZ,WAAY,YAEtB,gDACA,sCACE,QAAS,MAAM,MAAM,MAAM,KAE7B,+CACE,QAAS,KAEX,0CACE,mBAAoB,WACZ,WAAY,WAEtB,0CACE,OAAQ,EAEV,qCACE,aAAc,KAEhB,qCACE,SAAU,SACV,IAAK,IACL,WAAY,KACZ,MAAO,IACP,MAAO,MAET,+BACE,OAAQ,EAEV,+BACA,sBACE,SAAU,SACV,KAAM,EAER,sDACA,oCACA,wCACA,+BACE,IAAK,EACL,KAAM,EAER,6CACE,IAAK,KACL,KAAM,KACN,YAAa,KAEf,+BACA,gCACE,WAAY,KACZ,aAAc,IAEhB,qCACA,sCACE,WAAY,KAEd,yCACE,YAAa,KAEf,uBACE,QAAS,MAAO,EAChB,OAAQ,KACR,MAAO,IACP,UAAW,EACX,YAAa,EACb,aAAc,EAAE,IAAI,EAAE,EAGxB,mCADA,gCAEE,QAAS,EACT,OAAQ,IACR,MAAO,KACP,aAAc,IAAI,EAAE,EAGtB,gBACE,OAAQ,EACR,oBAAqB,KAClB,iBAAkB,KACjB,gBAAiB,KACb,YAAa;;;;;;;;;;;;;;;;;;;;;;;AAIvB,QACA,YACE,SAAU,SACV,KAAM,EAER,cACE,MAAO,KACP,OAAQ,EAER,UAA42sC,KAC52sC,gBAAiB,SACjB,eAAgB,EAChB,YAAa,KACb,aAAc,EACd,QAAS,EAEX,sBACE,SAAU,OAGZ,iBADA,2BAEE,SAAU,OACV,aAAc,MACd,aAAc,EAAE,EAAE,IAAI,IACtB,QAAS,KAAK,KAAK,KAAK,KACxB,YAAa,IACb,YAAa,OACb,cAAe,SACf,WAAY,KAEd,2BACE,eAAgB,OAElB,cAEA,wBADA,mBAEE,QAAS,MACT,MAAO,KAET,mBACE,cAAe,MACf,SAAU,SACV,WAAY,IACZ,YAAa,IAEf,iCACE,cAAe,MAGjB,mCADA,6BAEE,SAAU,SACV,IAAK,EACL,MAAO,EAET,mCACE,MAAO,MACP,MAAO,MAET,yBACE,eAAgB,OAElB,sCACE,eAAgB,OAChB,SAAU,SACV,OAAQ,IAEV,gCACE,iBAAkB,YAGpB,mCACE,QAAS,MACT,WAAY,KACZ,YAAa,KAEb,OAA85sC,MAAO,MAAO,MAAO,EACn7sC,QAAS,KAAK,KAAK,KAAK,EACxB,SAAU,OACV,cAAe,SAEjB,sCACE,aAAc,KAEhB,mCACE,SAAU,OAEZ,kCACE,eAAgB,SAElB,uBACE,OAAQ,QAEV,wBAGA,kCAFA,uCAGA,wCAFA,gCAGE,OAAQ,WAEV,WACE,aAAc,MACd,aAAc,EAAE,EAAE,EAAE,IACpB,QAAS,KAAK,KACd,SAAU,OACV,YAAa,MACb,eAAgB,OAChB,cAAe,SAEjB,2BACA,0BACE,SAAU,QAEZ,oBACE,cAAe,KAEjB,4BACA,2BAEE,WAAk8sC,EACl8sC,cAAe,EAGjB,oBADA,oBAEE,SAAU,SACV,MAAO,KACP,SAAU,OACV,aAAc,MACd,aAAc,EAAE,IAAI,EAAE,EACtB,KAAM,EAGR,kBADA,kBAEE,cAAe,KAEf,oBAA8/sC,MAC9/sC,oBAAqB,IACrB,KAAM,EAGR,4BADA,0BAEE,cAAe,KAEjB,gBACE,SAAU,SACV,MAAO,KACP,SAAU,KACV,WAAY,KACZ,WAAY,OACZ,KAAM,EACN,WAAY,EAGd,wBACE,4BAA6B,OAE/B,iCACE,4BAA6B,QAE/B,yBACE,SAAU,SACV,WAAY,OACZ,OAAQ,IAEV,kBACE,MAAO,KACP,OAAQ,KACR,WAAY,OAEd,2BACE,MAAO,KACP,OAAQ,IACR,YAAa,IACb,eAAgB,OAChB,OAAQ,EAAE,KAEZ,6DACE,IAAK,IACL,KAAM,IACN,YAAa,MACb,WAAY,KACZ,SAAU,SAEZ,aACE,QACE,OAAQ,eAEV,eACE,QAAS,YAGX,gBADA,oBAEE,SAAU,QACV,OAAQ,gBAGZ,qBACE,mBAAoB,UAEtB,2BACE,OAAQ,KACR,WAAY,OACZ,SAAU,SAGZ,sBAEA,6BADA,qBAFA,qBAIE,aAAc,MAGhB,sBACE,YAAa,OAGf,gBADA,uBAEA,cACE,YAAa,OAGf,uBACA,sBAFA,sBAGE,QAAS,aACT,eAAgB,IAChB,SAAU,OAEV,SAA2ltC,SAC3ltC,aAAc,MACd,aAAc,EAAE,IAAI,EAAE,EAGxB,uCACA,0CAFA,0CAGE,QAAS,aACT,eAAgB,IAElB,gBACE,aAAc,MACd,aAAc,IAAI,EAAE,EAKtB,6BAFA,6BACA,6BAFA,uCAIE,kBAAmB,EAErB,mCACE,kBAAmB,IAErB,4BACA,mCACE,aAAc,EAAE,EAAE,IAGpB,sBACE,aAAc,MACd,aAAc,IAAI,EAAE,EAAE,IAExB,mBACE,aAAc,MACd,aAAc,IAAI,EAEpB,iCACE,kBAAmB,IAErB,eACE,aAAc,MACd,aAAc,IAAI,EAAE,EAEtB,kBACE,iBAAkB,EAEpB,kBACE,iBAAkB,IAGpB,cACE,MAAO,KACP,SAAU,OACV,SAAU,SACV,aAAc,MACd,aAAc,IACd,YAAa,IACb,QAAS,OAAQ,EAAE,OAAQ,MAE7B,cACE,aAAc,IAAI,EAAE,EAEtB,yBACA,yBACA,mCACE,QAAS,aACT,eAAgB,IAChB,aAAc,IAEhB,iBACE,OAAQ,EAAE,IAEZ,mCACE,eAAgB,IAElB,iCACE,QAAS,KAGX,eADA,oBAEE,MAAO,KAET,yBACE,MAAO,KACP,OAAQ,QAEV,kCACE,SAAU,SAEZ,cACE,MAAO,MACP,QAAS,EAAE,QAEb,yBACE,gBAAiB,KAGnB,yBACA,mCAFA,sBAGE,UAAW,IAEb,sBACE,MAAO,KACP,OAAQ,EAAE,SACV,OAAQ,IAER,YAAgptC,IAEhptC,cAA0ptC,SAC1ptC,OAAQ,QACR,WAAY,OAEd,uCACE,WAAY,IACZ,OAAQ,QAEV,yBACE,WAAY,OACZ,YAAa,IACb,aAAc,MACd,aAAc,IACd,cAAe,SAEjB,sBACE,aAAc,MACd,aAAc,IAEhB,+BACE,MAAO,MACP,aAAc,KACd,aAAc,EACd,cAAe,EAEjB,mCACE,aAAc,MACd,aAAc,IACd,WAAY,OACZ,cAAe,SAEjB,yBACE,MAAO,QAET,0BACE,MAAO,MAET,iBACE,MAAO,MAET,eACA,eACE,QAAS,EAAE,SAEb,eACE,QAAS,aACT,YAAa,IAEf,oCACE,WAAY,KAEd,yBACA,wBACE,OAAQ,EAAE,KAAK,EAGjB,yBACA,gCACE,MAAO,MACP,OAAQ,MAAO,MAAO,MACtB,QAAS,KAAK,KAAK,KACnB,SAAU,SACV,QAAS,EAGX,+BACE,SAAU,SAEZ,eACE,QAAS,KAEX,eACE,QAAS,MAGX,8BADA,6BAEE,QAAS,MAEX,8BACE,MAAO,KACP,cAAe,IAEjB,oBAEA,0BADA,yBAEE,OAAQ,MAAM,EAAE,EAElB,iCACE,MAAO,IACP,OAAQ,KAAK,EAAE,KAEjB,yBACE,MAAO,IACP,OAAQ,KAAK,GAAG,EAAE,EAEpB,mCACE,aAAc,EAEhB,wCACE,YAAa,IACb,OAAQ,KAEV,mBACE,SAAU,KACV,WAAY,OACZ,YAAa,OACb,WAAY,MAEd,2BACE,YAAa,MAGf,wBACE,OAAQ,KAAK,IAAI,EAAE,IAErB,kBACE,QAAS,aACT,eAAgB,OAChB,YAAa,MACb,QAAS,EAAE,KAEb,sBACE,iBAAkB,IAEpB,8BACA,iCACE,iBAAkB,EAClB,cAAe,KAEjB,6BACE,kBAAmB,EAErB,qBACA,yBACE,MAAO,KAET,mBACE,oBAAqB,MACrB,oBAAqB,IAEvB,mBACE,YAAa,EAEf,qBACE,SAAU,SACV,MAAO,IACP,OAAQ,KACR,kBAAmB,UACnB,oBAAqB,OAAO,OAE9B,sCACE,QAAS,aACT,aAAc,MACd,aAAc,IACd,OAAQ,EAAE,IACV,QAAS,MAAM,MAAM,MAAM,KAC3B,YAAa,MAEf,2BACE,QAAS,aACT,aAAc,EACd,QAAS,EACT,YAAa,OACb,gBAAiB,KAEnB,6BACE,OAAQ,EACR,QAAS,EACT,WAAY,IACZ,YAAa,EAEf,mCACE,OAAQ,EAAE,EAAE,EAAE,KAEhB,qCACE,OAAQ,EAAE,EAAE,EAAE,IAGhB,6BADA,qBAEE,QAAS,aACT,eAAgB,OAGlB,qBACE,QAAS,MACT,QAAS,aACT,MAAO,EACP,MAAO,KAET,SACE,SAAU,SACV,MAAO,EACP,OAAQ,EACR,aAAc,MACd,aAAc,IACd,aAAc,IAAK,YAAY,YAAY,IAC3C,OAAQ,OAAQ,EAAE,EAAE,MACpB,QAAS,EACT,SAAU,OACV,eAAgB,IAGlB,gBADA,mBAEE,OAAQ,EACR,QAAS,OAAQ,KAAM,OAAQ,IAC/B,OAAQ,QAEV,0BACE,QAAS,EAEX,gCACE,QAAS,MAEX,gCACE,aAAc,MACd,aAAc,IACd,iBAAkB,KAEpB,0BACE,eAAgB,OAElB,gBACE,QAAS,aAEX,kBACE,OAAQ,EAAE,MAEZ,wBACA,qCACE,UAAW,KAEb,8BACE,UAAW,KAGb,uCACE,MAAO,KACP,UAAW,EAEb,cACE,SAAU,SAEZ,uBACE,SAAU,QAEZ,qBACE,QAAS,EAAE,KACX,YAAa,OAEf,gCACE,YAAa,OAEf,wBACA,uBACA,+BACA,8BACA,8BACE,MAAO,KAET,6BACA,iCACE,MAAO,KACP,UAAW,MAEb,kCACE,YAAa,KAEf,gCACE,WAAY,MACZ,cAAe,MAGjB,yBACE,SAAU,SACV,MAAO,IACP,iBAAkB,KAEpB,gCACA,yBACE,SAAU,SACV,OAAQ,KACR,OAAQ,WACR,QAAS,EAEX,WACE,SAAU,SACV,QAAS,OAEX,iBACA,gBACE,SAAU,SACV,IAAK,EACL,KAAM,EACN,MAAO,KACP,OAAQ,KAEV,iBACE,OAAQ,kBACR,QAAS,GAEX,sBACE,MAAO,MAGT,sBACA,6BACE,UAAW,KAGb,eACE,UAAW,MAEb,yBACE,aAAc,KAEhB,uBACE,aAAc,EAEhB,mCACE,YAAa,OAEf,yBACE,WAAY,MACZ,SAAU,KAGZ,6BACE,MAAO,KACP,MAAO,eAGT,6BACA,qCACA,4CACE,OAAQ,eACR,SAAU,QAGZ,4DACA,+DAFA,+DAGE,MAAO,eAIT,oCADA,oCAEE,QAAS,YAEX,oBACE,SAAU,SACV,MAAO,KACP,OAAQ,KACR,IAAK,EACL,KAAM,EACN,QAAS,IAEX,qCACE,OAAQ,kBACR,QAAS,GAEX,4CACE,OAAQ,KACR,SAAU,SACV,IAAK,EACL,OAAQ,EACR,KAAM,EACN,MAAO,EAET,kCACE,QAAS,KAGX,eACE,MAAO,eACP,aAAc,eAGhB,kBADA,2BAEE,YAAa,iBAEf,6BACE,QAAS,eAEX,0CAEE,oBACA,sBACA,wBAHA,wBAIE,SAAU,QACV,WAAY,MAOd,qBAJA,iCAKA,uBAJA,mCAKA,yBAJA,qCACA,yBAJA,qCAQE,QAAS,aACT,eAAgB,IAOlB,+BAJA,uBAKA,iCAJA,yBAKA,mCAJA,2BACA,mCAJA,2BAQE,SAAU,SACV,KAAM,MACN,QAAS,mBACT,QAAS,oBACT,QAAS,mBACT,QAAS,YACT,mBAAoB,SACpB,sBAAuB,QACvB,uBAAwB,eACpB,mBAAoB,eAChB,eAAgB,eACxB,SAAU,QACV,OAAQ,KAOV,2CAJA,mCAKA,6CAJA,qCAKA,+CAJA,uCACA,+CAJA,uCAQE,KAAM,KAOR,gDAJA,wCAKA,kDAJA,0CAKA,oDAJA,4CACA,oDAJA,4CAQE,cAAe,kBACX,UAAW,kBACf,kBAAmB,kBAOrB,gDAJA,wCAKA,kDAJA,0CAKA,oDAJA,4CACA,oDAJA,4CAQE,SAAU,SACV,KAAM,IACN,cAAe,sBACX,UAAW,sBACf,kBAAmB,sBAGrB,gDAIA,0DAIA,6CAIA,mDAXA,kDAIA,4DAIA,+CAIA,qDAXA,oDAIA,8DAIA,iDAIA,uDAfA,oDAIA,8DAIA,iDAIA,uDAIE,YAAa,EACb,eAAgB,EAOlB,sCAJA,8BAKA,wCAJA,gCAKA,0CAJA,kCACA,0CAJA,kCAQE,KAAM,KACN,MAAO,MACP,MAAO,MAOT,kDAJA,0CAKA,oDAJA,4CAKA,sDAJA,8CACA,sDAJA,8CAQE,KAAM,KACN,MAAO,KAOT,uDAJA,+CAKA,yDAJA,iDAKA,2DAJA,mDACA,2DAJA,mDAQE,MAAO,MAOT,+CAJA,uCAKA,iDAJA,yCAKA,mDAJA,2CACA,mDAJA,2CAQE,QAAS,MACT,YAAa,EAOf,+DAJA,wDAKA,iEAJA,0DAKA,mEAJA,4DACA,mEAJA,4DAQE,cAAe,iBACX,UAAW,iBACf,kBAAmB,iBAGrB,+CACA,iDACA,mDAHA,mDAIE,QAAS,KAGX,+CACA,iDACA,mDAHA,mDAIE,MAAO,MACP,QAAS,EAAE,OAAO,EAAE,OACpB,cAAe,SAGjB,+BACA,iCACA,mCAHA,mCAIE,YAAa,MAGf,sCACA,wCACA,0CAHA,0CAIE,aAAc,MACd,YAAa,EAOf,+BAJA,yCAKA,iCAJA,2CAKA,mCAJA,6CACA,mCAJA,6CAQE,QAAS,MACT,aAAc,EACd,QAAS,IAAI,IAAI,IAAI,IACrB,WAAY,KAGd,wCACA,0CACA,4CAHA,4CAIE,mBAAoB,WACZ,WAAY,WACpB,QAAS,IAAI,IAAI,EAGnB,wDACA,0DACA,4DAHA,4DAIE,OAAQ,KAAK,KAAK,EAClB,QAAS,EAGX,gEACA,kEACA,oEAHA,oEAIE,cAAe,EAAE,EAAE,SAAS,SAG9B,2CACA,6CACA,+CAHA,+CAIE,QAAS,cAGb,yCAEE,oBACA,sBACA,wBAHA,wBAIE,QAAS,MAGb,yCAEE,qBACA,uBACA,yBAHA,yBAIE,QAAS;;;;;;;;;;;;;;;;;;;;;;;AAIb,sBACE,QAAS,KAAK,KACd,YAAa,MAEf,iCACE,eAAgB,SAChB,aAAc,IAEhB,wBACE,QAAS,KAEX,gCACA,+CACE,OAAQ,QAEV,yBACE,SAAU,SACV,QAAS,MACT,WAAY,OACZ,MAAO,KACP,OAAQ,IACR,WAAY,KACZ,iBAAkB,YAClB,kBAAmB,UAErB,kBACE,QAAS,aACT,aAAc,IAAI,MAClB,OAAQ,IACR,eAAgB,IAChB,OAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;AAKZ,SACE,YAAa,OACb,SAAU,SAEZ,oCACE,QAAS,KAAK,KAAK,KAAK,KAG1B,oBACE,QAAS,KAAK,KAEhB,gBACE,QAAS,aACT,YAAa,OACb,eAAgB,IAElB,qBACE,SAAU,SACV,OAAQ,SACR,MAAO,IACP,aAAc,EAAE,IAChB,kBAAmB,SAErB,4BACE,eAAgB,OAElB,YACE,SAAU,OACV,YAAa,OACb,eAAgB,IAElB,8BACE,eAAgB,OAGlB,8BADA,8BAEE,aAAc,EACd,OAAQ,KAGV,iBACE,aAAc,MACd,aAAc,EAAE,EAAE,IAClB,YAAa,MACb,QAAS,KAEX,iCACE,aAAc,IAAI,EAAE,EAEtB,iBACA,oBACE,MAAO,KACP,aAAc,KAEhB,2BACE,aAAc,KACd,eAAgB,IAElB,gCACE,MAAO,MACP,aAAc,EAEhB,kDACE,QAAS,KAEX,uBACE,QAAS,aACT,aAAc,MACd,aAAc,IAAI,IAAI,IAAI,EAE5B,sCACE,kBAAmB,IAErB,yBACE,QAAS,aACT,QAAS,EAAE,MAEb,mCACA,2CACE,uBAAwB,IACxB,0BAA2B,IAE7B,+BACA,uCACE,wBAAyB,IACzB,2BAA4B,IAE9B,6BACE,YAAa,QACb,YAAa,EACb,eAAgB,EAGlB,oCACE,OAAQ,IAEV,oCACE,QAAS,YAEX,qCACE,WAAY,OACZ,WAAY,OAEd,0BACE,YAAa,IAEf,4BACE,YAAa,IAGf,oCACE,OAAQ,MAEV,eAEE,SAA44+C,SAI94+C,4BAFA,iBACA,kBAEE,OAAQ,MAEV,iCACE,QAAS,MAEX,kBACE,WAAY,IAGd,iBACA,sBAFA,cAGE,SAAU,SACV,IAAK,EACL,KAAM,EAER,gBACE,SAAU,SAEZ,qCACE,WAAY,OAEd,8BACE,WAAY,OAEd,6CACE,oBAAqB,IAIvB,gBACE,OAAQ,KACR,QAAS,aACT,eAAgB,IAChB,WAAY,IAEd,yBACE,OAAQ,KACR,SAAU,SACV,QAAS,EAEX,yBACE,OAAQ,KACR,SAAU,OAKZ,+BAFA,gCACA,sBAFA,uBAIE,QAAS,GACT,SAAU,SACV,IAAK,EACL,MAAO,EACP,OAAQ,EACR,aAAc,MACd,aAAc,IACd,aAAc,YAGhB,gCADA,uBAEE,KAAM,EACN,kBAAmB,QAGrB,+BADA,sBAEE,MAAO,EACP,mBAAoB,QAGtB,UACA,UACE,SAAU,SAEZ,UACE,OAAQ,IAEV,UACE,MAAO,IAET,WACA,WACE,SAAU,SACV,IAAK,KACL,MAAO,EACP,OAAQ,EACR,aAAc,MACd,aAAc,IAEhB,WACE,MAAO,KACP,iBAAkB,YAClB,oBAAqB,YACrB,mBAAoB,YAEtB,WACE,KAAM,KACN,iBAAkB,YAClB,oBAAqB,YACrB,kBAAmB,YAGrB,kBACE,MAAO,KACP,OAAQ,KACR,WAAY,IACZ,aAAc,MACd,aAAc,IACd,kBAAmB,cACf,cAAe,cACX,UAAW,cAErB,yBACE,YAAa,IAIf,uCADA,qCAEE,YAAa,EACb,eAAgB,EAElB,uCACE,WAAY,IAEd,qCACE,WAAY,KACZ,cAAe,KAEjB,uCACE,aAAc,IACd,cAAe,IAGjB,0CADA,6CAEE,eAAgB,SAElB,sBACE,SAAU,SACV,KAAM,MAGR,OACE,SAAU,SACV,OAAQ,EACR,IAAK,MAEP,aACE,SAAU,SACV,QAAS,EAAE,KAAK,IAChB,OAAQ,KAAK,MAAM,EACnB,QAAS,EAGX,yBADA,mBAEE,QAAS,EAEX,kBACE,OAAQ,EAAE,MAAM,EAAE,MAEpB,gBACE,SAAU,SACV,QAAS,EAEX,iBACE,SAAU,SACV,IAAK,EACL,OAAQ,EACR,KAAM,EACN,MAAO,IACP,QAAS,EAEX,YACE,SAAU,SACV,IAAK,EACL,MAAO,KACP,OAAQ,KACR,YAAa,KACb,QAAS,KACT,OAAQ,QAEV,0BACE,iBAAkB,YAEpB,2BACA,uCACE,IAAK,KAGP,gCADA,+BAEE,QAAS,MAEX,mBACE,QAAS,MACT,QAAS,aACT,MAAO,EACP,OAAQ,KAEV,kBACE,QAAS,GACT,QAAS,aACT,eAAgB,OAChB,MAAO,IACP,OAAQ,IACR,cAAe,IACf,YAAa,IAGf,gCADA,wBAEA,sCACE,aAAc,MACd,aAAc,IACd,YAAa,IAEf,cACE,KAAM,EAER,YACE,MAAO,EAET,eACE,aAAc,MACd,aAAc,IACd,WAAY,KACZ,SAAU,OACV,OAAQ,QACR,WAAY,MACZ,YAAa,OAEf,iBACE,QAAS,KAAK,MAAM,KAAK,KACzB,YAAa,OAEf,gBACA,wBACE,SAAU,SACV,IAAK,EACL,MAAO,IACP,YAAa,OAEf,gBACE,QAAS,EAEX,4BACE,SAAU,OACV,MAAO,KACP,OAAQ,IAAI,IAAI,EAAE,IAEpB,sCACE,WAAY,IAEd,oCACE,QAAS,aAEX,eACE,QAAS,KAGX,mCADA,kCAEE,QAAS,aAEX,gCACE,SAAU,SACV,WAAY,OACZ,QAAS,EACT,OAAQ,KAEV,sCACA,qCACE,WAAY,QAEd,sCACE,QAAS,GACT,SAAU,SACV,OAAQ,kBACR,QAAS,GAEX,4BACE,MAAO,EACP,IAAK,EACL,OAAQ,EACR,MAAO,KAET,4BACE,KAAM,EACN,IAAK,EACL,OAAQ,EACR,MAAO,KAET,kCACA,kCACE,KAAM,IACN,IAAK,IACL,WAAY,MACZ,OAAQ,MACR,MAAO,IAET,kCACE,KAAM,KACN,MAAO,IAET,mBACE,SAAU,SACV,OAAQ,EACR,MAAO,EACP,OAAQ,EACR,YAAa,KACb,aAAc,IACd,aAAc,MACd,iBAAkB,YAClB,kBAAmB,YACnB,mBAAoB,YACpB,QAAS,KACT,OAAQ,SAGV,uCADA,sCAEE,QAAS,MAEX,mBACE,QAAS,EAKX,+BADA,qCAEA,8CACA,0DAJA,mCAKE,IAAK,IAGP,qCADA,mCAEE,WAAY,KAEd,+BACA,8CACA,0DACE,WAAY,MAEd,kCACE,OAAQ,kBACR,OAAQ,UAEV,wCACE,OAAQ,IAEV,mCACE,OAAQ,KAEV,qDACA,qDACE,IAAK,EACL,WAAY,EACZ,OAAQ,KAGV,gBACE,QAAS,KACT,WAAY,KACZ,YAAa,OAEf,uBACE,UAAW,KACX,QAAS,MAEX,YACE,OAAQ,KAAK,EAAE,KACf,UAAW,KAEb,mBACE,YAAa,IAGf,kBACE,SAAU,SACV,QAAS,EACT,KAAM,EACN,YAAa,KACb,WAAY,KACZ,SAAU,OACV,cAAe,SACf,YAAa,OAEf,8BACE,OAAQ,EAAI,IAGd,0CACE,MAAO,MAET,+CACE,MAAO,MAET,oCACE,OAAQ,EAAE,KAGZ,4DADA,qDAEE,MAAO,KAET,mDACE,MAAO,KAGT,8BACE,MAAO,KAGT,8BAEA,8CADA,uCAEE,MAAO,eACP,OAAQ,eACR,SAAU,kBAEZ,uCACE,OAAQ,eACR,SAAU,kBAGZ,sDACE,QAAS,YAGX,0CACA,mCACE,QAAS,KAGX,sBACE,QAAS,KACT,MAAO,KACP,aAAc,KAEhB,6BACE,MAAO,MACP,aAAc,EACd,YAAa,KAEf,0CACE,kCACE,SAAU,SACV,MAAO,IACP,IAAK,IACL,QAAS,MAEX,yCACE,MAAO,KACP,KAAM,IAER,0DACE,QAAS,KAEX,oDACE,QAAS,MACT,aAAc,IAEhB,2DACE,WAAY,KACZ,aAAc,IAEhB,4DACE,QAAS,MACT,SAAU,SACV,cAAe,MACf,aAAc,IAEhB,mEACE,aAAc,EAEhB,kEACE,QAAS,MACT,QAAS,GACT,SAAU,SACV,IAAK,IACL,WAAY,MACZ,MAAO,OACP,MAAO,KACP,OAAQ,KAEV,sDACA,qEACE,QAAS,MACT,OAAQ,EACR,cAAe,EAEjB,mDACE,OAAQ,IAAI,MAAM,QAClB,iBAAkB,KAClB,iBAAkB,KAClB,mBAAoB,EAAE,IAAI,IAAI,EAAE,eACxB,WAAY,EAAE,IAAI,IAAI,EAAE,eAElC,0DACE,WAAY,MAGhB,yCACE,sBACE,QAAS,aAEX,0BACA,uBACE,QAAS,KAEX,wBACA,qBACE,OAAQ,EAEV,qBACE,QAAS,KACT,MAAO,EACP,aAAc,EAEhB,2BACE,QAAS,KACT,UAAW,EAEb,qCACE,WAAY,OAEd,2BACE,MAAO;;;;;;;;;;;;;;;;;;;;;;;AAMX,SACE,SAAU,SAEZ,iBACE,QAAS,KACT,oBAAqB,IACrB,oBAAqB,MAEvB,0BACE,QAAS,KAEX,2BACE,aAAc,KACd,YAAa,MACb,UAAW,KACX,WAAY,KACZ,SAAU,SACV,QAAS,KAAK,IAAI,KAAK,KAEzB,iBACE,SAAU,SACV,MAAO,IACP,IAAK,IAGP,oBACE,YAAa,OAEf,gBACE,eAAgB,EAChB,aAAc,KAEhB,4BACE,eAAgB,IAChB,QAAS,EAEX,YACE,eAAgB,IAElB,4BACA,uBACE,aAAc,EAEhB,2CACA,gDACE,kBAAmB,IAErB,uCACE,kBAAmB,EAErB,4BACE,SAAU,OAEZ,eACE,kBAAmB,IACnB,kBAAmB,MAErB,yCACE,OAAQ,KAEV,kCACE,eAAgB,IAElB,gBACA,SACE,YAAa,IAEf,gBACE,iBAAkB,KAEpB,2BACE,oBAAqB,IAEvB,kCACE,iBAAkB,EAEpB,mCACE,WAAY,MAGd,iCACE,OAAQ,KAEV,+BACE,MAAO,IAET,+BACE,MAAO,IAGT,0CADA,yCAEE,MAAO,IAET,uCACA,uCACE,MAAO,KACP,WAAY,KAEd,mBACE,OAAQ,EAAE,EAAE,KAAK,KACjB,eAAgB,UAElB,2BACE,OAAQ,EAAE,IAAI,EAAE,EAElB,4BACE,aAAc,MACd,aAAc,EAEhB,gCACE,SAAU,KACV,QAAS,KACT,aAAc,MACd,aAAc,EAAE,EAAE,EAAE,IACpB,MAAO,KACP,MAAO,IAET,4CACE,aAAc,EACd,aAAc,KAEhB,oCACE,MAAO,MACP,aAAc,EAEhB,gCACE,aAAc,EACd,aAAc,KACd,aAAc,IACd,SAAU,QAEZ,mCACE,YAAa,KACb,cAAe,IACf,QAAS,KAAK,EAAE,EAChB,aAAc,MACd,aAAc,IAEhB,yBACE,eAAgB,IAElB,mCACE,QAAS,KAAK,MAAM,KAAK,KACzB,OAAQ,EAAE,SACV,SAAU,SACV,UAAW,KACX,YAAa,MACb,WAAY,IAGd,kBACE,oBAAqB,EAAE,EAEzB,gBACE,oBAAqB,MAAM,EAE7B,eACE,oBAAqB,MAAM,EAE7B,cACE,oBAAqB,MAAM,EAE7B,kBACE,oBAAqB,MAAM,EAE7B,cACE,oBAAqB,MAAM,EAG7B,8CACE,SAAU,kBAGZ,8CADA,0DAEE,OAAQ,eAEV,6CACE,cAAe,YAEjB,8BACE,MAAO,eACP,OAAQ,eAGV,oDADA,wDAEE,MAAO,eACP,OAAQ;;;;;;;;;;;;;;;;;;;;;;;AAKV,YACE,SAAU,SACV,QAAS,aACT,MAAO,SACP,SAAU,OAGZ,oBADA,eAEE,gBAAiB,KAEnB,2BACE,gBAAiB,UAGnB,sBADA,sBAEE,SAAU,SACV,WAAY,OACZ,KAAM,EAGR,iCADA,iCAEE,SAAU,SACV,IAAK,SACL,YAAa,SACb,OAAQ,SAEV,iCACE,KAAM,GAER,iCACE,MAAO,GAET,uBACE,MAAO,KACP,eAAgB,EAChB,MAAO,KACP,OAAQ,SACR,aAAc,EACd,OAAQ,EACR,aAAc,MACd,QAAS,EAEX,uBACA,0BACE,WAAY,MAEd,8CACE,OAAQ,KAEV,iCACE,QAAS,aACT,MAAO,IACP,OAAQ,SACR,YAAa,SACb,OAAQ,SAAU,UAAW,QAAS,EAExC,8BACE,eAAgB,OAGlB,yCADA,yCAEE,OAAQ,SACR,MAAO,SAET,eACE,oBAAqB,MACrB,oBAAqB,IACrB,QAAS,KAAK,MAAM,KAAK,KACzB,YAAa,IACb,OAAQ,QAEV,eACE,QAAS,SACT,OAAQ,QAEV,2BACE,aAAc,OACd,aAAc,SACd,QAAS,EAEX,+BACE,QAAS,MACT,SAAU,OACV,WAAY,SACZ,YAAa,SACb,QAAS,EAAE,MAAM,EAAE,KAErB,iCACE,QAAS,MAAM,EAAE,KACjB,WAAY,OAEd,sBACE,MAAO,KAET,mCACA,wCACE,QAAS,MACT,OAAQ,KACR,QAAS,KAAK,EAEhB,+BACE,gBAAiB;;;;;;;;;;;;;;;;;;;;;;;AAInB,eAEE,aAAiz4D,EACjz4D,WAAY,IACZ,SAAU,KACV,YAAa,OAEf,oBACE,QAAS,MACT,aAAc,EACd,OAAQ,EACR,QAAS,EAAE,EAAE,EAAE,KAIjB,uBADA,6BADA,qBAGE,OAAQ,EACR,QAAS,EACT,WAAY,IACZ,gBAAiB,KACjB,SAAU,SAKZ,wBAHA,oBACA,qBAGA,kBAFA,sBAGE,QAAS,aACT,eAAgB,IAElB,wBACE,WAAY,KAEd,oBACA,kBACE,eAAgB,OAElB,6BACE,eAAgB,SAElB,0BACA,wBAEA,qBAEA,8BAHA,oBAEA,6BAEE,WAAY,MACZ,YAAa,MACb,OAAQ,QAGV,8BADA,6BAEE,OAAQ,QAGV,qBADA,sBAEE,aAAc,IAEhB,kBACE,OAAQ,IAAI,EAAE,IAAI,QAClB,QAAS,OAAQ,QAAS,OAAQ,QAClC,YAAa,SACb,gBAAiB,KACjB,aAAc,MACd,aAAc,IAEhB,sBACE,OAAQ,QAEV,yBACE,SAAU,SACV,QAAS,MACT,WAAY,OACZ,MAAO,KACP,OAAQ,IACR,WAAY,KACZ,iBAAkB,YAClB,kBAAmB;;;;;;;;;;;;;;;;;;;;;;;AAWrB,2BAJA,mBAEA,gBALA,kBAEA,sBAIA,gBAFA,sBAHA,kBAOE,iBAAkB,KAEpB,gBAKA,eAJA,YACA,cAEA,kBAGA,YADA,kBAEA,aACA,WANA,cAOA,2BACE,SAAU,SACV,QAAS,aACT,MAAO,OACP,SAAU,QACV,aAAc,EACd,eAAgB,OAElB,2BACA,6BAEA,iCAEA,2BADA,iCAEA,0BAJA,6BAKE,MAAO,OAET,gBAKA,eAJA,YACA,cAEA,kBAGA,YADA,kBAEA,aALA,cAMA,2BACE,YAAa,OAEf,eACA,2BACE,MAAO,KAET,kBACE,MAAO,KAET,gBAEA,gBADA,eAEE,SAAU,SACV,OAAQ,QAEV,iBACE,SAAU,SAEZ,iBAEA,gBADA,eAEE,QAAS,MAEX,SAOA,WALA,QAMA,sBAFA,YAHA,UACA,YACA,YAJA,UAQE,QAAS,EACT,4BAA6B,cAE/B,SAGA,YAGA,uBACA,sBAFA,cAJA,UACA,YAEA,YAIE,sBAAuB,KAEzB,iBACE,SAAU,SACV,SAAU,KAGZ,8BADA,0BAEE,sBAAuB,KACvB,4BAA6B,cAC7B,QAAS,IACT,aAAc,IACd,aAAc,MAGhB,oCACA,qCAFA,sCAIA,oCADA,mCAEE,oBAAqB,EACrB,eAAgB,IAElB,uCACE,eAAgB,IAGlB,gBACA,iBAEA,gBADA,eAHA,WAKE,aAAc,IACd,aAAc,MACd,QAAS,EAAE,MAAM,EAAE,EAErB,iCACE,cAAe,EAGjB,gBADA,WAEE,QAAS,EAEX,wBACE,aAAc,MAEhB,yBACE,cAAe,MAEjB,mBACE,IAAK,IACL,OAAQ,KAAK,EAAE,EACf,SAAU,SAEZ,sBACE,KAAM,IAER,uBACE,MAAO,IAGT,qBACE,QAAS,MACT,QAAS,MACT,OAAQ,KACR,SAAU,OAEZ,gBACA,iCACA,+BAGA,gCACA,8BAHA,+BACA,6BAGE,mBAAoB,mBAAmB,KAAK,SAC5C,WAAY,mBAAmB,KAAK,SACpC,WAAY,WAAW,KAAK,SAC5B,WAAY,WAAW,KAAK,SAAU,mBAAmB,KAAK,SAC9D,mBAAoB,2BACpB,WAAY,2BAKd,qBADA,yBADA,wBADA,iBAIE,MAAO,KACP,eAAgB,IAIlB,0BADA,yBADA,wBAGA,sBACE,YAAa,QACb,aAAc,EACd,QAAS,EAEX,qBACA,sBACE,WAAY,IAId,2BADA,0BADA,yBAGE,SAAU,SAEV,IAAmghE,EACnghE,MAAO,EACP,QAAS,aACT,eAAgB,IAChB,gBAAiB,KAEnB,sBAEA,0BADA,yBAEE,aAAc,MACd,aAAc,EAAE,EAAE,EAAE,IACpB,aAAc,QAGhB,gCACA,0CACE,MAAO,EAGT,yBADA,iBAEE,QAAS,MAKX,sBACA,uBACE,SAAU,OACV,OAAQ,EACR,gBAAiB,KACjB,KAAM,QACN,MAAO,QAET,qBACA,sBACE,QAAS,MACT,SAAU,OACV,cAAe,SAGjB,yBAGA,0BADA,yBADA,wBAGA,sBALA,iBAME,OAAQ,KACR,YAAa,KACb,QAAS,OAAQ,EACjB,YAAa,KACb,OAAQ,EACR,OAAQ,EAEV,oCAEA,uBADA,sBAEE,QAAS,MACT,QAAS,aACT,MAAO,EACP,OAAQ,KACR,eAAgB,KAGlB,8CAEA,iCADA,gCAEE,eAAgB,MAGlB,6CAEA,gCADA,+BAEE,QAAS,KAEX,qBAEA,yBADA,wBAEE,QAAS,OAIX,2BADA,0BADA,yBAGE,WAAY,KACZ,YAAa,QACb,eAAgB,OAChB,gBAAiB,WACjB,WAAY,OACZ,MAAO,MACP,OAAQ,KAEV,0BACE,QAAS,EAEX,iCACE,cAAe,EAEjB,oBACA,YACA,qBACE,OAAQ,QAEV,SACE,aAAc,MACd,aAAc,IAGhB,oBADA,iBAEE,OAAQ,QAEV,qBACE,OAAQ,EAEV,QACE,OAAQ,KAGV,iCACA,oBAFA,yBAGA,yBACA,yCACE,QAAS,IAAI,IAAI,IAAI,IACrB,YAAa,MACb,WAAY,MAEd,yBACE,aAAc,IACd,aAAc,MACd,aAAc,YACd,QAAS,EAAE,IAEb,kCACE,IAAK,KAEP,mDACE,QAAS,IACT,QAAS,MACT,iBAAkB,IAClB,iBAAkB,MAClB,SAAU,SACV,IAAK,KACL,KAAM,EACN,MAAO,EAET,yBACA,yCACE,cAAe,KAEjB,8BACE,QAAS,IAGX,iDACA,uDAFA,kDAGE,OAAQ,EACR,WAAY,IAQd,iCACA,oCACA,qCAJA,uCADA,qCAEA,wCAJA,kCADA,gCAEA,mCAOE,QAAS,EAAE,IACX,aAAc,IACd,aAAc,MAEhB,eACE,SAAU,SACV,cAAe,IAEjB,0BACE,cAAe,KACf,MAAO,KAET,uBACE,SAAU,SACV,MAAO,IACP,IAAK,IACL,kBAAmB,iBACf,cAAe,iBACX,UAAW,iBAErB,mCACE,aAAc,EACd,cAAe,EACf,kBAAmB,EACnB,mBAAoB,EAGtB,oBACE,SAAU,SACV,aAAc,EACd,aAAc,MACd,cAAe,IACf,aAAc,QACd,iBAAkB,KAClB,WAAY,OAEd,6BACE,iBAAkB,YAClB,OAAQ,OACR,YAAa,OACb,QAAS,MAAO,EAChB,YAAa,KACb,OAAQ,EACR,OAAQ,IAAI,EAAE,EACd,MAAO,KAET,wCACE,QAAS,KAEX,uBACE,OAAQ,IAAI,EAAE,IAAI,IAClB,QAAS,KAAK,MAAM,KAAK,KACzB,YAAa,QACb,MAAO,KACP,SAAU,SAEZ,2BACA,0BACE,SAAU,SACV,MAAO,IACP,OAAQ,IAEV,iCACE,WAAY,OAEd,8BACE,SAAU,SACV,IAAK,EACL,OAAQ,EACR,MAAO,EACP,QAAS,KAAK,KAGhB,iCACE,cAAe,MAEjB,4BACE,MAAO,MAET,yCACE,OAAQ,EAAE,IAEZ,uBACE,OAAQ,QAEV,UAGA,cACA,kBAHA,WACA,cAGE,QAAS,aACT,eAAgB,OAElB,wBACE,OAAQ,EAEV,sBACE,QAAS,IAAI,IAGf,SACE,QAAS,MAAO,EAElB,SACA,iBACE,QAAS,EACT,4BAA6B,cAE/B,WACE,QAAS,EAEX,gBACA,mBACE,QAAS,IAAI,KAEf,gBACE,OAAQ,QACR,YAAa,KACb,YAAa,MAEf,sBACE,YAAa,IAEf,sBACE,OAAQ,OAEV,mBACE,OAAQ,KAGV,sBACE,iBAAkB,YAEpB,2BACE,OAAQ,EAEV,0BACE,QAAS,MACT,OAAQ,IACR,YAAa,IACb,eAAgB,OAChB,aAAc,EACd,QAAS,EAEX,0BACE,OAAQ,KAEV,oDACE,mBAAoB,KAGtB,8BACE,YAAa,IAEf,iCACE,eAAgB,IAChB,YAAa,EACb,QAAS,aACT,OAAQ,IACR,MAAO,IAET,4BACE,SAAU,SACV,IAAK,KACL,QAAS,aACT,QAAS,IAAI,IAAI,IACjB,UAAW,EACX,YAAa,EACb,aAAc,IACd,YAAa,IACb,cAAe,IACf,kBAAmB,UACnB,eAAgB,OAChB,MAAO,KACP,OAAQ,KACR,yBAA0B,KAE5B,8CACE,QAAS,MACT,OAAQ,IACR,MAAO,KACP,SAAU,SACV,KAAM,IACN,OAAQ,KACR,cAAe,YAEjB,yBACE,OAAQ,QAEV,oBACE,SAAU,SACV,KAAM,EACN,IAAK,EACL,MAAO,KACP,OAAQ,KACR,iBAAkB,KAClB,QAAS,GACT,OAAQ,kBAEV,gBACE,SAAU,SACV,YAAa,EACb,aAAc,EACd,QAAS,aAEX,2BACE,gBAAiB,SACjB,SAAU,SACV,MAAO,KACP,OAAQ,KAEV,wBACE,MAAO,KACP,OAAQ,KACR,SAAU,OACV,yBAA0B,KAE5B,yCACA,+CACE,QAAS,IACT,WAAY,IACZ,mBAAoB,EAAE,IAAI,IAAI,IAAI,eAAoB,MAAM,EAAE,EAAE,EAAE,IAAI,sBAC9D,WAAY,EAAE,IAAI,IAAI,IAAI,eAAoB,MAAM,EAAE,EAAE,EAAE,IAAI,sBACtE,SAAU,SAEZ,8BACE,QAAS,IACT,SAAU,SACV,mBAAoB,EAAE,IAAI,IAAI,IAAI,eAAoB,MAAM,EAAE,EAAE,EAAE,IAAI,qBAC9D,WAAY,EAAE,IAAI,IAAI,IAAI,eAAoB,MAAM,EAAE,EAAE,EAAE,IAAI,qBAExE,mBACE,SAAU,SACV,QAAS,aACT,MAAO,MACP,eAAgB,IAElB,sBACE,iBAAkB,YAClB,iBAAkB,KAEpB,qCACE,iBAAkB,+BAClB,oBAAqB,IAAI,IACzB,WAAY,MAEd,yDACE,YAAa,SAAU,cAAe,iBAAkB,cAAe,UACvE,QAAS,MAAM,KAAK,MAAM,IAC1B,OAAQ,EACR,OAAQ,EACR,MAAO,IAET,oCACE,SAAU,SACV,oBAAqB,KAClB,iBAAkB,KACjB,gBAAiB,KACb,YAAa,KACrB,iBAAkB,WAAW,gBAE/B,kDACE,OAAQ,QACR,SAAU,SACV,QAAS,GACT,KAAM,IACN,IAAK,IACL,MAAO,IACP,OAAQ,IACR,OAAQ,IAAI,MAAM,KAClB,YAAa,KACb,WAAY,KACZ,cAAe,IACf,mBAAoB,EAAE,IAAI,IAAI,KACtB,WAAY,EAAE,IAAI,IAAI,KAC9B,WAAY,IAGd,wDADA,wDAEE,WAAY,IACZ,aAAc,KACd,mBAAoB,EAAE,IAAI,IAAI,KACtB,WAAY,EAAE,IAAI,IAAI,KAEhC,+CACA,iDACE,OAAQ,KAEV,wCACE,OAAQ,KACR,MAAO,IACP,OAAQ,EAAE,GAEZ,wDACE,mBAAoB,EAAE,IAAI,EAAE,KAAM,EAAE,KAAK,EAAE,KACnC,WAAY,EAAE,IAAI,EAAE,KAAM,EAAE,KAAK,EAAE,KAE7C,iCACA,0CACE,QAAS,MAEX,qDACA,8DACE,WAAY,IAEd,+CACA,wDACE,WAAY,IACZ,OAAQ,IAAI,MAAM,KAClB,WAAY,IACZ,OAAQ,IACR,MAAO,IACP,mBAAoB,EAAE,IAAI,IAAI,KACtB,WAAY,EAAE,IAAI,IAAI,KAIhC,qDAFA,qDAGA,8DAFA,8DAGE,WAAY,IACZ,aAAc,KACd,mBAAoB,EAAE,IAAI,IAAI,KACtB,WAAY,EAAE,IAAI,IAAI,KAC9B,aAAc,IACd,QAAS,IAEX,iDACE,WAAY,2KACZ,WAAY,8FACZ,WAAY,0FAEd,0DACE,iBAAkB,+BAClB,wBAAyB,KAAK,KACtB,gBAAiB,KAAK,KAC9B,oBAAqB,KAAK,IAC1B,kBAAmB,UAErB,+BACE,WAAY,KACZ,cAAe,IACf,WAAY,OACZ,UAAW,IAEb,yCACE,MAAO,IAET,mCACE,WAAY,2EAAsF,+EAClG,WAAY,uDAAiE,8DAC7E,WAAY,qDAA+D,0DAC3E,OAAQ,MACR,cAAe,IAEjB,wDACE,WAAY,4jCAEd,0CACE,WAAY,upBAAupB,CAAC,wpBAEtqB,wDACE,WAAY,sBAAqB,OAAO,EAAE,IAE5C,iEACE,WAAY,+BAA8B,OAAO,EAAE,IAErD,0CACE,OAAQ,+GAA6G;;;;;;;;;;;;;;;;;;;;;;;AAIvH,eACE,MAAO,KACP,OAAQ,MACR,aAAc,MACd,aAAc,MACd,aAAc,IACd,gBAAiB,SACjB,eAAgB,IAChB,UAAW,KACX,eAAgB,IAChB,SAAU,SAEZ,iBACE,aAAc,IACd,QAAS,KAAK,KACd,UAAW,WACX,SAAU,KAEZ,4BACE,OAAQ,KACR,aAAc,EACd,cAAe,IACf,mBAAoB,eACZ,WAAY,eAEtB,mCACE,SAAU,OAEZ,iCACE,OAAQ,EACR,QAAS,EAEX,kBACE,OAAQ,EACR,QAAS,KAAK,EACd,gBAAiB,KACjB,YAAa,MACb,OAAQ,QACR,UAAW,WAGb,qBACE,QAAS,aACT,eAAgB,OAGlB,wBACA,yBAFA,4BAGE,QAAS,EAIX,2BAEA,6BADA,4BAFA,+BADA,+BAKE,QAAS,aACT,QAAS,KAAK,EAEhB,mCACA,qBACE,aAAc,IAEhB,4CACE,aAAc,EAEhB,qCACE,SAAU,SAEZ,kBACE,mBAAoB,KACpB,QAAS,EACT,QAAS,MACT,IAAK,EACL,KAAM,EACN,SAAU,SACV,OAAQ,KACR,MAAO,KACP,OAAQ,KAAK,EAAE,EAEjB,+BACE,SAAU,SACV,IAAK,IACL,aAAc,MACd,aAAc,EAAE,IAAI,EAAE,EACtB,OAAQ,EAAE,KAAK,EAAE,KACjB,QAAS,EAAE,EAAE,EAAE,IACf,UAAW,MAEb,2BACE,QAAS,MACT,OAAQ,IACR,UAAW,EACX,YAAa,EAKf,iCAFA,8BADA,8BAEA,+BAEE,eAAgB,OAElB,gBACE,YAAa,OAEf,wBACE,QAAS,aACT,eAAgB,OAChB,OAAQ,IAAI,EACZ,MAAO,KACP,OAAQ,KACR,YAAa,KAEf,6BACE,MAAO,KACP,OAAQ,KACR,eAAgB,OAChB,yBAA0B,KAE5B,UACE,oBAAqB,OAAO,OAE9B,QACE,oBAAqB,OAAO,EAE9B,uBACA,0BACE,oBAAqB,OAAO,EAC5B,oBAAqB,OAAO,EAE9B,uBACA,0BACE,oBAAqB,OAAO,EAE9B,UACE,oBAAqB,OAAO,MAE9B,yBACA,4BACE,oBAAqB,OAAO,MAC5B,oBAAqB,OAAO,MAE9B,yBACA,4BACE,oBAAqB,OAAO,MAE9B,aACE,oBAAqB,OAAO,MAE9B,4BACA,+BACE,oBAAqB,OAAO,MAC5B,oBAAqB,OAAO,MAE9B,4BACA,+BACE,oBAAqB,OAAO,MAE9B,iBACE,oBAAqB,OAAO,MAE9B,gCACA,mCACE,oBAAqB,OAAO,MAC5B,oBAAqB,OAAO,MAE9B,gCACA,mCACE,oBAAqB,OAAO,MAE9B,aACE,oBAAqB,OAAO,MAE9B,4BACA,+BACE,oBAAqB,OAAO,MAC5B,oBAAqB,OAAO,MAE9B,4BACA,+BACE,oBAAqB,OAAO,MAE9B,aACE,oBAAqB,OAAO,OAE9B,4BACA,+BACE,oBAAqB,OAAO,OAC5B,oBAAqB,OAAO,OAE9B,4BACA,+BACE,oBAAqB,OAAO,OAE9B,4BACE,oBAAqB,OAAO,MAE9B,4BACE,oBAAqB,OAAO,OAE9B,eACE,oBAAqB,OAAO,OAE9B,8BACA,iCACE,oBAAqB,OAAO,OAC5B,oBAAqB,OAAO,OAE9B,8BACA,iCACE,oBAAqB,OAAO,OAE9B,iBACE,oBAAqB,OAAO,OAE9B,gCACA,mCACE,oBAAqB,OAAO,OAC5B,oBAAqB,OAAO,OAE9B,gCACA,mCACE,oBAAqB,OAAO,OAE9B,gBACE,oBAAqB,OAAO,OAE9B,+BACA,kCACE,oBAAqB,OAAO,OAC5B,oBAAqB,OAAO,OAE9B,+BACA,kCACE,oBAAqB,OAAO,OAE9B,eACE,oBAAqB,OAAO,OAE9B,8BACA,iCACE,oBAAqB,OAAO,OAC5B,oBAAqB,OAAO,OAE9B,8BACA,iCACE,oBAAqB,OAAO,OAE9B,uBACE,oBAAqB,OAAO,OAE9B,sCACA,yCACE,oBAAqB,OAAO,OAC5B,oBAAqB,OAAO,OAE9B,sCACA,yCACE,oBAAqB,OAAO,OAE9B,qBACE,oBAAqB,OAAO,OAE9B,oCACA,uCACE,oBAAqB,OAAO,OAC5B,oBAAqB,OAAO,OAE9B,oCACA,uCACE,oBAAqB,OAAO,OAE9B,UACA,kBACE,oBAAqB,OAAO,EAE9B,yBACA,iCACA,4BACA,oCACE,oBAAqB,OAAO,EAC5B,oBAAqB,OAAO,EAE9B,yBACA,iCACA,4BACA,oCACE,oBAAqB,OAAO,EAE9B,WACA,iBACE,oBAAqB,OAAO,MAE9B,0BACA,gCACA,6BACA,mCACE,oBAAqB,OAAO,MAC5B,oBAAqB,OAAO,MAE9B,0BACA,gCACA,6BACA,mCACE,oBAAqB,OAAO,MAE9B,cACE,oBAAqB,OAAO,MAE9B,6BACA,gCACE,oBAAqB,OAAO,MAC5B,oBAAqB,OAAO,MAE9B,6BACA,gCACE,oBAAqB,OAAO,MAE9B,UACE,oBAAqB,OAAO,MAE9B,yBACA,4BACE,oBAAqB,OAAO,MAC5B,oBAAqB,OAAO,MAE9B,yBACA,4BACE,oBAAqB,OAAO,MAE9B,eACE,oBAAqB,OAAO,MAE9B,8BACA,iCACE,oBAAqB,OAAO,MAC5B,oBAAqB,OAAO,MAE9B,8BACA,iCACE,oBAAqB,OAAO,MAE9B,cACE,oBAAqB,OAAO,OAE9B,6BACA,gCACE,oBAAqB,OAAO,OAC5B,oBAAqB,OAAO,OAE9B,6BACA,gCACE,oBAAqB,OAAO,OAE9B,aACE,oBAAqB,OAAO,OAE9B,4BACA,+BACE,oBAAqB,OAAO,OAC5B,oBAAqB,OAAO,OAE9B,4BACA,+BACE,oBAAqB,OAAO,OAE9B,eACE,oBAAqB,OAAO,OAE9B,8BACA,iCACE,oBAAqB,OAAO,OAC5B,oBAAqB,OAAO,OAE9B,8BACA,iCACE,oBAAqB,OAAO,OAE9B,mBACE,oBAAqB,OAAO,OAE9B,kCACA,qCACE,oBAAqB,OAAO,OAC5B,oBAAqB,OAAO,OAE9B,kCACA,qCACE,oBAAqB,OAAO,OAE9B,eACE,oBAAqB,OAAO,EAE9B,8BACA,iCACE,oBAAqB,OAAO,EAC5B,oBAAqB,OAAO,EAE9B,8BACA,iCACE,oBAAqB,OAAO,EAE9B,iBACE,oBAAqB,OAAO,MAE9B,gCACA,mCACE,oBAAqB,OAAO,MAC5B,oBAAqB,OAAO,MAE9B,gCACA,mCACE,oBAAqB,OAAO,MAE9B,kBACE,oBAAqB,OAAO,MAE9B,iCACA,oCACE,oBAAqB,OAAO,MAC5B,oBAAqB,OAAO,MAE9B,iCACA,oCACE,oBAAqB,OAAO,MAE9B,eACE,oBAAqB,OAAO,MAE9B,8BACA,iCACE,oBAAqB,OAAO,MAC5B,oBAAqB,OAAO,MAE9B,8BACA,iCACE,oBAAqB,OAAO,MAE9B,eACE,oBAAqB,OAAO,MAE9B,8BACA,iCACE,oBAAqB,OAAO,MAC5B,oBAAqB,OAAO,MAE9B,8BACA,iCACE,oBAAqB,OAAO,MAE9B,aACE,oBAAqB,OAAO,OAE9B,4BACA,+BACE,oBAAqB,OAAO,OAC5B,oBAAqB,OAAO,OAE9B,4BACA,+BACE,oBAAqB,OAAO,OAE9B,gBACE,oBAAqB,OAAO,OAE9B,+BACA,kCACE,oBAAqB,OAAO,OAC5B,oBAAqB,OAAO,OAE9B,+BACA,kCACE,oBAAqB,OAAO,OAE9B,cACE,oBAAqB,OAAO,OAE9B,6BACA,gCACE,oBAAqB,OAAO,OAC5B,oBAAqB,OAAO,OAE9B,6BACA,gCACE,oBAAqB,OAAO,OAE9B,OACE,oBAAqB,OAAO,OAE9B,sBACA,yBACE,oBAAqB,OAAO,OAC5B,oBAAqB,OAAO,OAE9B,sBACA,yBACE,oBAAqB,OAAO,OAE9B,SACE,oBAAqB,OAAO,OAE9B,wBACA,2BACE,oBAAqB,OAAO,OAC5B,oBAAqB,OAAO,OAE9B,wBACA,2BACE,oBAAqB,OAAO,OAG9B,YACE,MAAO,MAET,YACE,MAAO,MAET,eACE,MAAO,MAET,4BACE,MAAO,KACP,OAAQ,IAAI,EAAE,EAEhB,kCACE,QAAS,IAEX,yCACE,QAAS,MACT,OAAQ,EACR,UAAW,EACX,YAAa,EAEf,wBACE,aAAc,MACd,aAAc,IACd,aAAc,KAEhB,sCACA,8BACE,SAAU,SACV,QAAS,EAEX,+BACE,aAAc,MACd,aAAc,IAEhB,oCACE,mBAAoB,IAEtB,2CACE,kBAAmB,IAErB,kCACE,QAAS,KAGX,gCADA,+BAEE,eAAgB,OAElB,kCACE,OAAQ,kBACR,QAAS,GAEX,2BACE,MAAO,KACP,OAAQ,KACR,aAAc,MACd,aAAc,IACd,QAAS,EAEX,qBACE,QAAS,MACT,MAAO,KACP,OAAQ,KACR,OAAQ,EACR,OAAQ,EACR,QAAS,EACT,WAAY,KAEd,kBACE,QAAS,EAEX,2BACE,QAAS,OACT,eAAgB,IAGlB,yBACE,OAAQ,EACR,OAAQ,EACR,QAAS,EAEX,yBACA,oCACE,UAAW,QACX,YAAa,SAAU,cAAe,UAExC,iBACE,QAAS,IACT,MAAO,MAET,+BACE,MAAO,IAET,+BACE,MAAO,IAET,0CACE,MAAO,IAET,mBACE,MAAO,KAET,sBACE,MAAO,KACP,UAAW,MAEb,qCACE,OAAQ,EAAE,IAAI,EAEhB,oCACE,MAAO,IAET,oCACE,MAAO,IAET,+CACE,MAAO,IAGT,uBADA,sBAEE,MAAO,IAET,2BACE,QAAS,aAEX,oCACE,MAAO,MACP,OAAQ,MACR,QAAS,KAAK,KAAK,KAAK,KACxB,aAAc,IACd,aAAc,MACd,SAAU,KAEZ,gCACE,gBAAiB,UAEnB,YACE,MAAO,QACP,QAAS,MAAM,KAAK,KAEtB,sBACE,OAAQ,KAAK,EAEf,WACE,aAAc,IACd,aAAc,MACd,MAAO,KACP,OAAQ,KACR,OAAQ,IACR,eAAgB,IAChB,QAAS,aACT,SAAU,OACV,yBAA0B,KAE5B,2BACE,SAAU,SACV,QAAS,IACT,MAAO,EACP,OAAQ,EAEV,qBACE,SAAU,SACV,iBAAkB,KAClB,QAAS,EAEX,qBACE,WAAY,SACZ,SAAU,SAEZ,kBACE,SAAU,SACV,MAAO,EACP,IAAK,EAEP,iCACE,MAAO,KACP,OAAQ,KACR,QAAS,MACT,WAAY,KACZ,gBAAiB,QACjB,OAAQ,EACR,cAAe,KACf,OAAQ,EACR,QAAS,EAAE,IAEb,kDACE,QAAS,KAEX,+BACE,QAAS;;;;;;;;;;;;;;;;;;;;;;;AAIX,qBACE,QAAS,KAAK,KACd,OAAQ,QACR,SAAU,SACV,YAAa,OAEf,4CACE,cAAe,KAEjB,+BACE,eAAgB,YAChB,aAAc,IAEhB,gCACE,SAAU,SACV,IAAK,IACL,MAAO,IACP,QAAS,KAEX,uDACE,QAAS;;;;;;;;;;;;;;;;;;;;;;;AAIX,eACE,QAAS,aACT,SAAU,SACV,eAAgB,OAElB,eACE,cAAe,IAEjB,0BACE,MAAO,KACP,OAAQ,MAEV,wBACE,MAAO,MACP,OAAQ,KAEV,iCACE,SAAU,SACV,aAAc,MACd,aAAc,IACd,SAAU,OAEZ,4CACA,yEACE,KAAM,KACN,MAAO,KACP,IAAK,KACL,OAAQ,KACR,cAAe,IAAI,EAAE,EAAE,IAEzB,kEACA,mDACE,KAAM,KACN,MAAO,KACP,cAAe,EAAE,IAAI,IAAI,EAE3B,0CACE,KAAM,KACN,OAAQ,KACR,MAAO,KACP,cAAe,EAAE,EAAE,IAAI,IAEzB,gEACE,OAAQ,KACR,IAAK,KACL,cAAe,IAAI,IAAI,EAAE,EAE3B,4CACA,mDACE,cAAe,IAEjB,wBACE,WAAY,KACZ,OAAQ,EACR,QAAS,EACT,SAAU,SACV,KAAM,KACN,IAAK,KACL,MAAO,KACP,OAAQ,KACR,cAAe,IACf,YAAa,OAEf,kCACE,QAAS,aACT,OAAQ,KACR,aAAc,MACd,YAAa,KAEf,0CACE,YAAa,EAEf,yCACE,mBAAoB,EAEtB,kCACA,+DACE,aAAc,IAAI,EAAE,IAAI,IAE1B,wDACA,yCACE,aAAc,IAAI,EAAE,IAAI,IAE1B,mCACA,yCACA,+DACE,uBAAwB,IACxB,0BAA2B,IAC3B,kBAAmB,IAErB,kCACA,0CACE,wBAAyB,IACzB,2BAA4B,IAE9B,wDACA,0CACE,mBAAoB,IAEtB,mDACE,mBAAoB,IAEtB,gCACE,MAAO,KACP,aAAc,MACd,aAAc,IAAI,IAAI,EAAE,IACxB,WAAY,KAEd,wCACE,WAAY,EAEd,yCACE,oBAAqB,EAEvB,iCACE,uBAAwB,IACxB,wBAAyB,IAE3B,gCACE,0BAA2B,IAC3B,2BAA4B,IAC5B,oBAAqB,IAEvB,sDACE,aAAc,EAAE,IAAI,IAAI,IAE1B,uDACE,iBAAkB,IAEpB,wBACE,SAAU,SACV,IAAK,KACL,OAAQ,IAAI,MAAM,YAClB,YAAa,IACb,MAAO,KACP,OAAQ,KAEV,wBACA,+EACE,KAAM,KACN,MAAO,KACP,WAAY,MAEd,wEACA,yDACE,KAAM,KACN,MAAO,KACP,WAAY,KAEd,gDACE,IAAK,KACL,OAAQ,KAEV,sEACE,OAAQ,KACR,IAAK,KAEP,mBACE,QAAS,aACT,QAAS,EAAE,KACX,UAAW,KACX,YAAa,OAEf,iEACE,SAAU,SACV,OAAQ,EACR,KAAM,EAER,2CACE,kBAAmB,eAAe,kBAC9B,cAAe,eAAe,kBAC1B,UAAW,eAAe,kBAClC,yBAA0B,EAAE,EACxB,qBAAsB,EAAE,EACpB,iBAAkB,EAAE,EAE9B,iEACE,kBAAmB,cAAc,kBAC7B,cAAe,cAAc,kBACzB,UAAW,cAAc,kBACjC,yBAA0B,EAAE,KACxB,qBAAsB,EAAE,KACpB,iBAAkB,EAAE,KAE9B,kDACE,qBAAsB,MAClB,iBAAkB,MACd,aAAc,MACtB,QAAS,KAAK;;;;;;;;;;;;;;;;;;;;;;;AAIhB,aACE,SAAU,SACV,aAAc,EACd,iBAAkB,YAClB,oBAAqB,KAClB,iBAAkB,KACjB,gBAAiB,KACb,YAAa,KAEvB,mBACE,MAAO,KACP,OAAQ,MAGV,qBACE,QAAS,aACT,MAAO,MAEP,OAAs+iF,KAEx+iF,eACE,MAAO,KACP,OAAQ,KAGV,4BADA,oBAEE,SAAU,SACV,IAAK,EACL,MAAO,KACP,UAAW,EACX,OAAQ,KACR,OAAQ,EACR,QAAS,EACT,QAAS,EAEX,4BACE,WAAY,IACZ,eAAgB,IAElB,iCACE,OAAQ,kBACR,QAAS,GAEX,iDACE,MAAO,KAET,6BACE,KAAM,EAGR,8CADA,sCAEE,IAAK,KACL,OAAQ,EAEV,6BACE,MAAO,EAET,kBAEA,kBADA,gBAEE,OAAQ,QAGV,oBADA,gBAEE,SAAU,SACV,OAAQ,EACR,QAAS,EAGX,yCADA,qCAEE,IAAK,IACL,KAAM,EACN,OAAQ,IACR,WAAY,KACZ,kBAAmB,SAErB,uDACE,KAAM,KAGR,uCADA,mCAEE,KAAM,IACN,OAAQ,EACR,MAAO,IACP,YAAa,KACb,kBAAmB,SAErB,qDACE,OAAQ,KAEV,cACE,SAAU,SACV,kBAAmB,UACnB,iBAAkB,YAClB,YAAa,QACb,SAAU,OACV,gBAAiB,KACjB,WAAY,OACZ,QAAS,EAEX,mCACE,IAAK,KACL,MAAO,KACP,OAAQ,KAEV,iCACE,KAAM,KACN,MAAO,KACP,OAAQ,KAEV,kCACE,YAAa,KAEf,qCACE,OAAQ,KAEV,mCACE,YAAa,IAEf,qDACE,YAAa,EAEf,qDACE,OAAQ,EACR,YAAa,KAEf,kBACE,SAAU,SACV,OAAQ,EACR,QAAS,EACT,iBAAkB,YAClB,kBAAmB,UACnB,oBAAqB,OAAO,OAE9B,6BACE,MAAO,KACP,OAAQ,KACR,WAAY,OAEd,6BACE,oBAAqB,OAAO,MAE9B,+CACE,oBAAqB,OAAO,OAE9B,mDACE,oBAAqB,OAAO,OAE9B,mCACE,oBAAqB,OAAO,KAE9B,qDACE,oBAAqB,OAAO,MAE9B,yDACE,oBAAqB,OAAO,MAE9B,2BACE,oBAAqB,MAAM,OAE7B,6CACE,oBAAqB,OAAO,OAE9B,iDACE,oBAAqB,OAAO,OAE9B,iCACE,oBAAqB,KAAK,OAE5B,mDACE,oBAAqB,MAAM,OAE7B,uDACE,oBAAqB,MAAM,OAE7B,8BACE,oBAAqB,EAAE,MAEzB,2CACE,oBAAqB,EAAE,KAEzB,gDACE,oBAAqB,EAAE,OAEzB,6DACE,oBAAqB,EAAE,MAEzB,oDACE,oBAAqB,EAAE,OAEzB,iEACE,oBAAqB,EAAE,MAEzB,6BACE,oBAAqB,KAAK,MAE5B,0CACE,oBAAqB,KAAK,KAE5B,+CACE,oBAAqB,KAAK,OAE5B,4DACE,oBAAqB,KAAK,MAE5B,mDACE,oBAAqB,KAAK,OAE5B,gEACE,oBAAqB,KAAK,MAE5B,4BACE,oBAAqB,MAAM,KAE7B,yCACE,oBAAqB,KAAK,KAE5B,8CACE,oBAAqB,OAAO,KAE9B,2DACE,oBAAqB,MAAM,KAE7B,kDACE,oBAAqB,OAAO,KAE9B,+DACE,oBAAqB,MAAM,KAE7B,2BACE,oBAAqB,MAAM,EAE7B,wCACE,oBAAqB,KAAK,EAE5B,6CACE,oBAAqB,OAAO,EAE9B,0DACE,oBAAqB,MAAM,EAE7B,iDACE,oBAAqB,OAAO,EAE9B,8DACE,oBAAqB,MAAM,EAE7B,2BACE,WAAY,MAEd,6CACE,WAAY,KAEd,mBACE,SAAU,SACV,YAAa,OACb,UAAW,MAEb,8BACE,KAAM,EACN,MAAO,KACP,YAAa,EAEf,uCACE,KAAM,KAER,sCACE,KAAM,KACN,MAAO,KAET,8BACE,OAAQ,OAEV,gDACE,IAAK,OAEP,4BACE,KAAM,KACN,QAAS,MACT,WAAY,KAEd,oCACE,IAAK,MAEP,qCACE,OAAQ,MAEV,8CACE,KAAM,KACN,MAAO,KAET,kBACE,IAAK;;;;;;;;;;;;;;;;;;;;;;;AAMP,oBADA,qBAEE,aAAc,MAGhB,oBADA,qBAEE,YAAa,KACb,QAAS,IAEX,qBACE,SAAU,SACV,aAAc,EAAE,EAAE,IAEpB,kCACE,aAAc,EACd,YAAa,EACb,aAAc,EACd,cAAe,EAEjB,oBACE,WAAY,OAEd,oBACE,aAAc,IAAI,EAAE,EAEtB,wBACE,MAAO,MAET,oCACE,MAAO,KAET,wCACE,MAAO,KACP,cAAe,KAEjB,2CACE,MAAO,KACP,MAAO,KAGT,0BADA,2BAEE,QAAS,aACT,aAAc,MACd,aAAc,IAAI,IAAI,IAAI,EAE5B,iDACA,wDACE,aAAc,EAGhB,4DADA,uCAEE,kBAAmB,IAErB,0CACE,aAAc,KACd,aAAc,IAGhB,4BADA,6BAEE,QAAS,aACT,QAAS,EAAE,MAGb,yCADA,yCAEE,aAAc,KACd,cAAe,KAEjB,4CACE,QAAS,EAEX,oCACE,OAAQ,EAAE,MAGZ,4CADA,yDAEE,WAAY,IAEd,qFACE,MAAO,KACP,WAAY,OAEd,wFACE,WAAY,IACZ,OAAQ,EAEV,0CACE,MAAO,MAET,0CACE,MAAO,KAGT,4BADA,mCAEE,OAAQ,KAAK,IAAI,EAAE,EAErB,oBACA,yBACE,SAAU,OAEZ,yBACE,SAAU,SACV,aAAc,MACd,aAAc,EAEhB,qDACE,mBAAoB,IAGtB,qBADA,mBAEE,SAAU,SAEZ,mBACE,SAAU,OACV,aAAc,MACd,aAAc,EAEhB,qBACE,SAAU,KAEZ,oBACA,mBACE,eAAgB,EAChB,MAAO,KACP,OAAQ,EACR,gBAAiB,SAEnB,gCACE,QAAS,EACT,eAAgB,IAGlB,6CACE,MAAO,KAET,mBACE,aAAc,MACd,UAAW,KAEb,sCACE,aAAc,KAEhB,+DACE,OAAQ,KAEV,sBACA,sBACE,OAAQ,MACR,QAAS,OAAO,KAChB,UAAW,KAEb,mCACA,mCACE,mBAAoB,YACZ,WAAY,YAEtB,iCACA,qCACE,MAAO,YACP,SAAU,iBAEZ,iCACE,aAAc,YACd,cAAe,YACf,mBAAoB,YAEtB,+DACE,aAAc,IAEhB,yEACE,OAAQ,KAGV,iCADA,6CAEE,OAAQ,KACR,WAAY,MAGd,oDADA,gEAEE,OAAQ,KAEV,sBACA,aACE,eAAgB,IAGlB,2DACE,eAAgB,IAElB,gEACE,WAAY,OACZ,eAAgB,OAElB,qEACE,UAAW,MAEb,uBACE,SAAU,OACV,cAAe,SAGjB,uBADA,sBAEE,aAAc,MACd,aAAc,EAAE,EAAE,IAAI,IAGxB,mCADA,kCAEE,kBAAmB,EAErB,0DACE,kBAAmB,IAErB,sDACE,kBAAmB,EAGrB,oEADA,8DAEA,oCACE,oBAAqB,EAIvB,4EADA,8EADA,wCAGE,oBAAqB,IAEvB,sBACE,WAAY,MACZ,cAAe,KACf,aAAc,MACd,aAAc,EAAE,IAAI,IAAI,EACxB,aAAc,YACd,YAAa,OAEf,gDACE,oBAAqB,YAGvB,0EADA,4DAEE,oBAAqB,QAEvB,8BACE,oBAAqB,OAEvB,uBACA,sBACE,SAAU,SAEZ,uBACE,MAAO,EACP,OAAQ,EACR,OAAQ,MAAM,IAAI,YAClB,KAAM,EAER,sBACE,KAAM,IACN,MAAO,EACP,OAAQ,IAEV,QACE,SAAU,SAEZ,kBACE,WAAY,OACZ,UAAW,KACX,YAAa,IACb,QAAS,EAEX,oBACE,QAAS,MACT,WAAY,MAEd,SACA,eACE,SAAU,SACV,aAAc,MACd,aAAc,IACd,WAAY,KACZ,SAAU,OAEZ,SACE,OAAQ,QACR,WAAY,MAEd,mBACE,OAAQ,kBACR,QAAS,GACT,OAAQ,iBACR,OAAQ,cAEV,6BACE,YAAa,OAEf,kBACE,QAAS,KAAK,MAAM,KAAK,KAE3B,cACE,QAAS,KACT,eAAgB,EAChB,UAAW,KAEb,iCACE,QAAS,MAEX,iBACA,iBACA,gBACE,SAAU,SACV,IAAK,IACL,MAAO,IACP,YAAa,OAEf,iBACE,QAAS,EAEX,wCACE,IAAK,EACL,MAAO,EAET,6BACE,SAAU,OACV,MAAO,KACP,OAAQ,IAAI,IAAI,EAAE,IAEpB,uCACE,WAAY,IAEd,qCACE,QAAS,aAEX,gBACE,QAAS,KAEX,+BACA,oCACE,QAAS,aAGX,iCADA,8BAEE,SAAU,SACV,IAAK,EACL,KAAM,EACN,MAAO,KACP,WAAY,OAEd,iCACE,IAAK,KACL,OAAQ,EAEV,0BACA,oDACE,SAAU,SACV,WAAY,OACZ,QAAS,EAGX,iCADA,gCAEA,2DACE,WAAY,QAEd,gCACE,QAAS,GACT,SAAU,SACV,OAAQ,kBACR,QAAS,GAEX,oDACE,eAAgB,KACR,OAAQ,KAChB,QAAS,EAEX,qBACE,IAAK,EACL,KAAM,EACN,MAAO,EACP,OAAQ,KAEV,qBACE,OAAQ,EACR,KAAM,EACN,MAAO,EACP,OAAQ,KAEV,qBACE,MAAO,EACP,IAAK,EACL,OAAQ,EACR,MAAO,KAET,qBACE,KAAM,EACN,IAAK,EACL,OAAQ,EACR,MAAO,KAET,2BACA,2BACE,IAAK,IACL,KAAM,IACN,YAAa,KACb,MAAO,IACP,OAAQ,IAEV,2BACE,IAAK,KACL,OAAQ,IAEV,2BACA,2BACE,KAAM,IACN,IAAK,IACL,WAAY,MACZ,OAAQ,MACR,MAAO,IAET,2BACE,KAAM,KACN,MAAO,IAET,yCACA,yCACE,OAAQ,KAEV,yCACA,yCACE,MAAO,KAET,+CACA,+CACE,IAAK,EACL,YAAa,KACb,MAAO,IACP,OAAQ,KAEV,+CACE,OAAQ,EAEV,+CACA,+CACE,KAAM,EACN,WAAY,MACZ,OAAQ,MACR,MAAO,KAET,+CACE,MAAO,EAET,+CACE,cAAe,EAAE,EAAE,IAAI,IAEzB,+CACE,cAAe,IAAI,IAAI,EAAE,EAE3B,+CACE,cAAe,EAAE,IAAI,IAAI,EAE3B,+CACE,cAAe,IAAI,EAAE,EAAE,IAEzB,8DACE,SAAU,SACV,WAAY,OACZ,OAAQ,IACR,YAAa,IAEf,mDACE,SAAU,OACV,QAAS,aACT,MAAO,IACP,OAAQ,IACR,WAAY,EACZ,OAAQ,IAEV,qBACE,aAAc,MACd,aAAc,EAEhB,oCACA,kCACE,QAAS,GACT,SAAU,SACV,MAAO,EACP,OAAQ,EACR,aAAc,MACd,aAAc,IAEhB,+BACE,IAAK,EACL,KAAM,EACN,mBAAoB,YACpB,oBAAqB,YAEvB,8BACE,OAAQ,EACR,MAAO,EACP,iBAAkB,YAClB,kBAAmB,YAErB,kCACE,SAAU,SACV,IAAK,KACL,KAAM,KACN,UAAW,KAEb,qCACE,SAAU,SACV,OAAQ,KACR,MAAO,MACP,UAAW,KAEb,kCACE,MAAO,MAET,kBACE,WAAY,KAEd,4BACE,MAAO,KACP,aAAc,KAEhB,uCACE,MAAO,MACP,aAAc,EAGhB,8CADA,8CAEE,WAAY,KAGd,yBADA,gCAEE,MAAO,IAET,wBACE,MAAO,KAET,wBACE,MAAO,KAET,4BACE,SAAU,SACV,eAAgB,IAElB,8CACE,eAAgB,IAElB,yCACE,SAAU,SACV,MAAO,KAET,qCACE,YAAa,KAEf,kBACE,QAAS,aACT,MAAO,IACP,OAAQ,IACR,eAAgB,OAChB,aAAc,KAEhB,uBACE,MAAO,KACP,OAAQ,EAAE,KAAK,EAAE,EACjB,UAAW,IACX,YAAa,IAEf,wBACE,QAAS,MACT,OAAQ,KAAK,EAAE,EACf,UAAW,MACX,WAAY,OAEd,wBACE,UAAW,KAEb,wBACE,YAAa,OAEf,8CACA,8CACE,MAAO,MAET,qCACE,MAAO,IAET,qCACE,MAAO,IAET,8CACA,0CACE,MAAO,KAET,0CACE,WAAY,IACZ,OAAQ,SAEV,iEACE,aAAc,IAEhB,wDACE,MAAO,MACP,aAAc,GACd,YAAa,GACb,QAAS,EAAE,EAAE,KAEf,YACE,MAAO,KAET,wBACE,MAAO,MAET,mCACE,YAAa,IAEf,4BACE,OAAQ,KAAK,EAAE,KACf,YAAa,IAEf,sCACE,OAAQ,EAEV,mCACE,YAAa,KAGf,oCADA,2CAEE,MAAO,IAGT,uCADA,qCAEA,qCACE,MAAO,IAET,2DACE,MAAO,KAET,iBACE,OAAQ,EACR,QAAS,IAAI,EAAE,IACf,WAAY,OAEd,+CACE,MAAO,KAET,+CACE,OAAQ,KAAK,EAAE,KAEjB,4DACE,QAAS,KAEX,wDACE,QAAS,KAEX,qBACE,MAAO,MACP,QAAS,EAAE,KAGb,0CAEE,gDACA,kDACA,oDAHA,oDAIE,SAAU,SACV,MAAO,IACP,IAAK,IACL,QAAS,MAGX,uDACA,yDACA,2DAHA,2DAIE,MAAO,KACP,KAAM,IAGR,wEACA,0EACA,4EAHA,4EAIE,QAAS,KAGX,8DACA,gEACA,kEAHA,kEAIE,QAAS,KAGX,8DACA,gEACA,kEAHA,kEAIE,QAAS,OAGX,kEACA,oEACA,sEAHA,sEAIE,QAAS,MACT,aAAc,IAGhB,yEACA,2EACA,6EAHA,6EAIE,WAAY,KACZ,aAAc,IAGhB,0EACA,4EACA,8EAHA,8EAIE,QAAS,MACT,SAAU,SACV,cAAe,MACf,aAAc,IAGhB,iFACA,mFACA,qFAHA,qFAIE,aAAc,EAGhB,gFACA,kFACA,oFAHA,oFAIE,QAAS,MACT,QAAS,GACT,SAAU,SACV,IAAK,IACL,WAAY,MACZ,MAAO,OACP,MAAO,QACP,OAAQ,QAGV,oEAIA,mFAHA,sEAIA,qFAHA,wEAIA,uFAPA,wEAIA,uFAIE,QAAS,MACT,OAAQ,EACR,cAAe,EAGjB,iEACA,mEACA,qEAHA,qEAIE,OAAQ,IAAI,MAAM,QAClB,iBAAkB,KAClB,iBAAkB,KAClB,mBAAoB,EAAE,IAAI,IAAI,EAAE,eACxB,WAAY,EAAE,IAAI,IAAI,EAAE,eAGlC,wEACA,0EACA,4EAHA,4EAIE,WAAY,MAGhB,yCAEE,oGACA,sGACA,wGAHA,wGAIE,QAAS,KAGX,8BACA,gCACA,kCAHA,kCAIE,MAAO,IAGT,8BACA,gCACA,kCAHA,kCAIE,MAAO,IACP,SAAU,OAOZ,kCAJA,kCAKA,oCAJA,oCAKA,sCAJA,sCACA,sCAJA,sCAQE,YAAa,QAIjB,wBACE,SAAU,OAEZ,kCACA,6CACA,2CACE,OAAQ,eACR,SAAU,kBAEZ,4CACE,QAAS,YAEX,iDACE,aAAc,YAGhB,gEADA,+DAEE,MAAO;;;;;;;;;;;;;;;;;;;;;;;AAIT,WACE,SAAU,SACV,QAAS,MACT,aAAc,MACd,aAAc,EACd,QAAS,IAAI,IAAI,IAAI,IACrB,kBAAmB,SACnB,UAAW,KAEX,WAAg8zF,OAGl8zF,kBACE,WAAY,MACZ,OAAQ,EAEV,mBACE,OAAQ,KAEV,uCACE,cAAe,KAEjB,eACE,SAAU,OACV,QAAS,aACT,aAAc,IACd,QAAS,IAAI,IAAI,IAAI,IAEvB,eACE,QAAS,KAEX,WACE,SAAU,SACV,MAAO,EACP,OAAQ,EACR,aAAc,MACd,aAAc,IACd,aAAc,YAEhB,aACE,IAAK,MACL,KAAM,IAER,aACE,IAAK,IACL,KAAM,MAER,aACE,KAAM,IACN,OAAQ,MAEV,aACE,IAAK,IACL,MAAO,MAET,+BACA,+BACE,YAAa,KAGf,+BADA,+BAEE,WAAY,KAEd,iCACE,eAAgB,SAChB,aAAc,IAEhB,sBACE,QAAS;;;;;;;;;;;;;;;;;;;;;;;AAIX,WACE,SAAU,SACV,QAAS,MACT,eAAgB,OAChB,YAAa,MAIf,wCACA,0CAHA,6BACA,+BAGE,eAAgB,OAChB,WAAY,KACZ,cAAe,KAEjB,oBACE,YAAa,QACb,OAAQ,QACR,YAAa,IACb,eAAgB,IAElB,2BACE,QAAS,MACT,QAAS,aACT,MAAO,EAET,0BACE,OAAQ,OAEV,+CAEA,kCADA,iCAEE,QAAS,KAEX,gCACE,YAAa,KAEf,qBACE,SAAU,OACV,YAAa,OAEf,yBACE,MAAO,KAET,0BACE,MAAO,MAGT,qBADA,aAEE,QAAS,aACT,eAAgB,OAChB,YAAa,OAEf,wBACE,aAAc,EAAE,EAAE,EAAE,IACpB,aAAc,MACd,MAAO,IACP,YAAa,QAEf,2BACE,gBAAiB,KAEnB,8BACE,QAAS,aAEX,qCACE,OAAQ,EAAE,EAAE,EAAE,KACd,cAAe,EAEjB,qBAEA,2BAIA,wBALA,2BAGA,sBADA,qBAEA,iBAEE,OAAQ,EAAE,KACV,YAAa,OACb,eAAgB,OAElB,2BACE,aAAc,EAGhB,0CADA,qCAEE,OAAQ,EAEV,iDACE,OAAQ,EAAE,EAAE,EAAE,KAEhB,8BACE,aAAc,EAAE,EAAE,EAAE,IACpB,aAAc,MACd,OAAQ,IACR,MAAO,IACP,YAAa,QACb,QAAS,EAAE,KACX,OAAQ,EACR,SAAU,SACV,MAAO,MACP,cAAe,EAEjB,8BACE,MAAO,KACP,OAAQ,EAEV,mCACE,aAAc,EAAE,EAAE,IAClB,aAAc,MACd,OAAQ,IACR,YAAa,EACb,UAAW,EACX,QAAS,EAEX,yCACA,6BACE,WAAY,KACZ,QAAS,MACT,WAAY,IACZ,aAAc,YACd,YAAa,OAEf,mBACE,WAAY,KAEd,sCACE,QAAS,EAEX,yCACE,QAAS,MAEX,wCACE,aAAc,IAAI,EAClB,aAAc,MACd,cAAe,EACf,QAAS,IAAI,EACb,OAAQ,IAAI,EAEd,yCACE,QAAS,KAGX,0DACA,qDAFA,+CAGE,WAAY,EACZ,WAAY,EACZ,YAAa,IAEf,qDACE,QAAS,KAEX,8CACE,cAAe,EACf,cAAe,EACf,eAAgB;;;;;;;;;;;;;;;;;;;;;;;AAIlB,YACE,SAAU,SACV,OAAQ,MAEV,oBACE,aAAc,EACd,SAAU,OAEZ,oBACE,SAAU,OAEZ,0BACE,SAAU,KAEZ,4BACE,SAAU,SACV,IAAK,IACL,KAAM,IACN,OAAQ,KAAK,EAAE,EAAE,KAEnB,kBACA,YACE,SAAU,SACV,aAAc,MACd,UAAW,EACX,QAAS,EACT,oBAAqB,KAClB,iBAAkB,KACjB,gBAAiB,KACb,YAAa,KAEvB,yCACA,mCACE,IAAK,EACL,MAAO,IACP,aAAc,EAAE,IAChB,kBAAmB,SAErB,2BACA,qBACE,KAAM,EACN,OAAQ,IACR,aAAc,IAAI,EAClB,kBAAmB,SAErB,iCACE,OAAQ,SAEV,+BACE,OAAQ,SAEV,6BACE,QAAS,KAEX,8BACA,4BACE,iBAAkB,IAEpB,+BACE,SAAU,SACV,IAAK,IACL,MAAO,IACP,OAAQ,KACR,WAAY,MAGd,iBADA,iBAGA,eADA,eAEE,OAAQ,QAEV,wCACE,WAAY,MAEd,wCACE,WAAY,KAEd,8BACE,MAAO,IAET,4BACE,OAAQ,IAEV,6BACE,SAAU,SACV,KAAM,IACN,MAAO,KACP,OAAQ,IACR,YAAa,MAEf,sCACE,YAAa,MAEf,sCACE,YAAa,KAGf,kDADA,gDAEE,QAAS,aAEX,wCACE,oBAAqB,OAAO,KAE9B,8CACE,oBAAqB,OAAO,KAE9B,wCACA,sCACE,oBAAqB,KAAK,OAE5B,8CACA,4CACE,oBAAqB,MAAM,OAE7B,wCACA,sCACE,oBAAqB,KAAK,OAE5B,8CACA,4CACE,oBAAqB,MAAM,OAE7B,sCACE,oBAAqB,MAAM,OAE7B,4CACE,oBAAqB,MAAM,OAE7B,sCACA,oCACE,oBAAqB,IAAI,OAE3B,4CACA,0CACE,oBAAqB,MAAM,OAE7B,sCACA,oCACE,oBAAqB,IAAI,OAE3B,4CACA,0CACE,oBAAqB,MAAM,OAE7B,qBACE,SAAU;;;;;;;;;;;;;;;;;;;;;;;AAIZ,eACE,SAAU,SAEZ,eACA,iBACE,eAAgB,OAElB,YACA,QACE,SAAU,SAEZ,YACE,aAAc,MACd,aAAc,EACd,QAAS,KACT,iBAAkB,YAEpB,eACE,WAAY,OACZ,YAAa,KAEf,sBACE,WAAY,QAEd,iBACE,SAAU,SACV,SAAU,OACV,UAAW,IAEb,2BACE,UAAW,QAGb,wBACA,wBAFA,gCAGE,OAAQ,KAEV,uBACE,SAAU,SACV,OAAQ,EACR,MAAO,EACP,QAAS,EACT,KAAM,MAAM,oBAEZ,OAAi7sG,iBACj7sG,QAAS,EACT,OAAQ,EACR,QAAS,EACT,OAAQ,QAEV,gBACE,OAAQ,EAAE,EAAE,KACZ,YAAa,KACb,aAAc,MACd,aAAc,IAAI,EAAE,EAEtB,0BACE,QAAS,EAEX,0BACA,+BACE,YAAa,IAEf,kBACE,oBAAqB,OAAO,OAE9B,cACE,oBAAqB,OAAO,OAO9B,+BAFA,qDAHA,mDAEA,+BADA,6BAGA,+BAEE,oBAAqB,OAAO,OAE9B,WACA,WACE,oBAAqB,OAAO,MAY9B,4BACA,4BALA,kDACA,kDAPA,gDACA,gDAGA,4BACA,4BAHA,0BACA,0BAKA,4BACA,4BAGE,oBAAqB,OAAO,MAE9B,aACE,oBAAqB,OAAO,OAO9B,8BAFA,oDAHA,kDAEA,8BADA,4BAGA,8BAEE,oBAAqB,OAAO,OAE9B,QACE,aAAc,MACd,aAAc,EAAE,EAAE,IAClB,QAAS,OAAO,OAAO,OAAO,KAEhC,gBACE,SAAU,SAEZ,gBACE,oBAAqB,OAAO,OAO9B,iCAFA,uDAHA,qDAEA,iCADA,+BAGA,iCAEE,oBAAqB,OAAO,OAE9B,YACE,SAAU,SACV,QAAS,aACT,UAAW,KACX,UAAW,SACX,eAAgB,OAChB,YAAa,IACb,SAAU,OACV,cAAe,SACf,YAAa,OAEf,iBACE,SAAU,SACV,MAAO,KAET,2BACA,4BACE,eAAgB,YAElB,6BACE,YAAa,IAEf,8BACE,YAAa,KAEf,iBACE,YAAa,OAEf,YACE,SAAU,SACV,IAAK,EACL,OAAQ,EACR,KAAM,EAER,mBACE,UAAW,QACX,OAAQ,MAAO,EAAE,EACjB,mBAAoB,YACZ,WAAY,YAGtB,iBADA,mBAEE,cAAe,KAEjB,mBACE,YAAa,KACb,aAAc;;;;;;;;;;;;;;;;;;;;;;;AAIhB,+BACA,gCACE,QAAS,KAEX,4BACE,OAAQ,EACR,QAAS,EAEX,mCACE,WAAY,OAEd,sCACE,QAAS,OACT,OAAQ,EACR,UAAW,IACX,WAAY,OAEd,oDACE,QAAS,KAEX,wBACE,QAAS,MACT,OAAQ,kBACR,QAAS,GACT,SAAU,MAEZ,eACE,SAAU,SACV,MAAO,MACP,MAAO,IACP,QAAS,EAEX,qBACE,SAAU,SACV,IAAK,EACL,KAAM,IACN,YAAa,KACb,WAAY,OAEd,6BACE,aAAc,EACd,cAAe,EAEjB,yBACE,SAAU,SACV,IAAK,IACL,MAAO,IACP,OAAQ,EAEV,eACE,SAAU,SACV,MAAO,KACP,MAAO,IAET,oBACE,SAAU,SACV,IAAK,IACL,KAAM,EACN,QAAS,EACT,aAAc,IACd,YAAa,KAEf,wBACE,MAAO,KACP,UAAW,QACX,YAAa,QACb,OAAQ,EAEV,uBACA,4BACE,WAAY,EACZ,gBAAiB,KACjB,eAAgB,OAChB,SAAU,OAEZ,6BACE,gBAAiB,UAEnB,0CACE,gBAAiB,KACjB,OAAQ,QAEV,sCACE,aAAc,MACd,aAAc,IACd,OAAQ,IAAI,EAAE,EACd,QAAS,MACT,YAAa,KACb,YAAa,OAGf,iEACE,QAAS,KAEX,+BACE,MAAO,KAET,gCACE,MAAO,MAET,4CACE,MAAO,KAET,yBACE,MAAO,KACP,QAAS,MACT,aAAc,EACd,iBAAkB,YAEpB,0CACE,QAAS,KAEX,0CACE,MAAO,KACP,YAAa,EACb,eAAgB,IAElB,iCACE,eAAgB,OAElB,SACE,MAAO,KACP,OAAQ,MACR,aAAc,MACd,aAAc,IACd,iBAAkB,EAClB,OAAQ,EAAE,EAAE,MACZ,QAAS,IACT,SAAU,KACV,YAAa,IAEf,QACE,MAAO,KACP,MAAO,MACP,OAAQ,KACR,SAAU,OACV,aAAc,MACd,aAAc,IACd,OAAQ,IACR,QAAS,EAAE,EAAE,IACb,oBAAqB,EAAE,MACvB,kBAAmB,SACnB,OAAQ,QAEV,0BACA,6BACE,oBAAqB,EAAE,OAEzB,wBACE,MAAO,KACP,QAAS,OACT,MAAO,KACP,OAAQ,KACR,OAAQ,IAAI,KAAK,EAAE,IACnB,oBAAqB,KAClB,iBAAkB,KACjB,gBAAiB,KACb,YAAa,KAEvB,uBACE,MAAO,KACP,OAAQ,KAEV,wBACE,OAAQ,IAAI,EAAE,EAAE,IAElB,yBACE,MAAO,KACP,OAAQ,KACR,oBAAqB,EAAE,OACvB,kBAAmB,UAErB,0BACE,OAAQ,KAAK,EAAE,EAAE,KAGnB,cADA,eAEE,OAAQ,KAAK,EAAE,IACf,YAAa,IAEf,eACE,MAAO,KACP,MAAO,MACP,SAAU,OACV,cAAe,SAEjB,cACE,MAAO,MAIT,oBADA,cADA,eAGE,QAAS,MAEX,gCACE,WAAY,MAEd,sCACE,MAAO,IAET,sCACE,MAAO,IAET,cACE,OAAQ,MAAM,EAAE,EAElB,+BACE,YAAa,IACb,UAAW,KAEb,qBACE,QAAS,MACT,OAAQ,EAAE,EAAE,KACZ,UAAW,IACX,YAAa,IAEf,cACA,gCACE,WAAY;;;;;;;;;;;;;;;;;;;;;;;AAGd,SACA,SACA,aACA,cACE,sBAAuB,KACvB,4BAA6B,cAE/B,SACA,cACE,OAAQ,MAKV,cAHA,YACA,YACA,iBAEE,iBAAkB,YAEpB,SACE,WAAY,KACZ,SAAU,SAEZ,mBACE,KAAM,EACN,SAAU,OAEZ,iBACE,cAAe,IACf,QAAS,IACT,YAAa,OACb,QAAS,MACT,YAAa,OACb,kBAAmB,SACnB,oBAAqB,EAAE,EACvB,iBAAkB,gOAClB,MAAO,KAET,yBACE,MAAO,KAET,uBACE,eAAgB,EAChB,gBAAiB,SAEnB,oBACE,MAAO,KACP,WAAY,OACZ,QAAS,IAEX,oBACE,MAAO,KACP,WAAY,KACZ,QAAS,KAAK,KAIhB,YACE,SAAU,SACV,kBAAmB,cAErB,aACE,SAAU,SACV,aAAc,IACd,aAAc,MACd,aAAc,QACd,cAAe,EACf,OAAQ,KAEV,gBACE,SAAU,SACV,MAAO,KACP,OAAQ,KACR,iBAAkB,KAClB,iBAAkB,sBAClB,OAAQ,iBAEV,UACE,WAAY,QACZ,MAAO,IACP,OAAQ,KACR,OAAQ,SACR,QAAS,EACT,cAAe,IACf,SAAU,SAEZ,cACE,MAAO,KACP,OAAQ,KACR,iBAAkB,YAEpB,cACE,KAAM,KAER,eACE,MAAO,KAET,kBACE,OAAQ,MAAM,EAAE,EAAE,MAClB,QAAS,KAAK,KAAK,EAAE,EAEvB,kCACE,YAAa,MACb,cAAe,KAEjB,mBACE,OAAQ,MAAM,EAAE,EAAE,MAClB,QAAS,KAAK,EAAE,EAAE,KAEpB,mCACE,aAAc,KAEhB,QACE,SAAU,SACV,OAAQ,KACR,iBAAkB,KAClB,OAAQ,kBACR,QAAS,GAEX,UACE,WAAY,QACZ,MAAO,IACP,OAAQ,KACR,SAAU,SAGZ,sBACE,SAAU,SAEZ,4BACE,SAAU,SACV,OAAQ,IACR,cAAe,IACf,WAAY,QAEd,6BACE,WAAY,KACZ,UAAW,MACX,QAAS,EACT,WAAY,OACZ,OAAQ,EACR,mBAAoB,EAAE,IAAI,IAAI,eACtB,WAAY,EAAE,IAAI,IAAI,eAC9B,WAAY,KAGd,aACA,kBACE,QAAS,aACT,eAAgB,IAElB,kBACE,OAAQ,KACR,MAAO,KAIT,WADA,OAEE,OAAQ,MAGV,8BADA,0BAEE,eAAgB,EAChB,oBAAqB,KAClB,iBAAkB,KACjB,gBAAiB,KACb,YAAa,KAKvB,gCAFA,8BACA,4BAFA,0BAIE,SAAU,SACV,MAAO,KACP,OAAQ,KAGV,oBADA,gBAEE,SAAU,SACV,KAAM,EACN,IAAK,EAGP,+BADA,2BAEE,QAAS,KAEX,iBACE,SAAU,SACV,MAAO,KACP,OAAQ,KACR,OAAQ,MAAM,EAAE,EAAE,MAClB,OAAQ,QAEV,qBACE,oBAAqB,EAAI,KAE3B,4BACE,oBAAqB,EAAI,EAE3B,qGACE,iBACE,MAAO,KACP,OAAQ,KACR,OAAQ,MAAM,EAAE,EAAE,MAEpB,qBACE,oBAAqB,EAAI,MAI7B,kBACE,IAAK,EAEP,qBACE,OAAQ,EAEV,mBACE,KAAM,EAER,oBACE,MAAO,EAET,gBACE,SAAU,SAEZ,iDACE,aAAc,EAEhB,kDACE,YAAa,EAGf,aACE,MAAO,KACP,OAAQ,KACR,OAAQ,KACR,cAAe,KACf,SAAU,SACV,QAAS,aACT,eAAgB,OAElB,2BACE,QAAS,KAEX,oBACE,aAAc,YACd,WAAY,IAEd,0BACE,OAAQ,EACR,QAAS,EACT,YAAa,KACb,cAAe,KACf,SAAU,SACV,UAAW,IAEX,YAAq09G,IAEv09G,+BACE,IAAK,IACL,KAAM,IACN,YAAa,KAEf,+BACE,MAAO,IACP,IAAK,IACL,WAAY,KAEd,+BACE,OAAQ,IACR,KAAM,IACN,YAAa,KAEf,+BACE,KAAM,IACN,IAAK,IACL,WAAY,KAGd,sBACE,iBAAkB,qBAClB,UAAW,KACX,QAAS,IAAI,IACb,QAAS,KAGX,gBACE,OAAQ,KACR,eAAgB,OAElB,8BACE,QAAS,KAEX,eACE,cAAe,IACf,QAAS,aAEX,yBACE,SAAU,SACV,KAAM,IAAK,KAAM,KAAK,UAExB,mCACE,cAAe,IAAI,EAAE,EAAE,IAEzB,8CACE,cAAe,EACf,YAAa,KAEf,kCACE,cAAe,EAAE,IAAI,IAAI,EACzB,YAAa,KAEf,+BACE,QAAS,EAEX,8BACE,QAAS,MAEX,iCACE,cAAe,IAAI,IAAI,EAAE,EAE3B,gCACE,cAAe,EAAE,EAAE,IAAI,IACvB,WAAY,KAEd,aACE,OAAQ,EACR,MAAO,MACP,eAAgB,IAGlB,0CACE,cAAe,EAAE,IAAI,IAAI,EAE3B,qDACE,cAAe,EACf,YAAa,EACb,aAAc,KAEhB,yCACE,cAAe,IAAI,EAAE,EAAE,IACvB,YAAa,EACb,aAAc,KAGhB,WACE,OAAQ,MAEV,8BACE,MAAO,KACP,OAAQ,KACR,SAAU,SAEZ,8BACE,MAAO,KACP,OAAQ,KACR,SAAU,SAEZ,oBACE,MAAO,KACP,OAAQ,KAGV,eACE,mBAAoB,YACZ,WAAY,YAGtB,WACE,SAAU,OACV,OAAQ,MAEV,gBACE,mBAAoB,WACZ,WAAY,WACpB,aAAc,MACd,aAAc,IACd,SAAU,SACV,OAAQ,KAAK,EAAE,EAAE,KACjB,SAAU,OAEZ,uBACE,QAAS,KAEX,uCACE,QAAS,KAEX,8BACA,oCACE,QAAS,EACT,iBAAkB,KAEpB,2BACE,SAAU,SACV,OAAQ,KAEV,iBACE,mBAAoB,WACZ,WAAY,WACpB,SAAU,OACV,cAAe,SACf,YAAa,OACb,OAAQ,OACR,QAAS,EAAE,KACX,YAAa,OAEf,iCACE,aAAc,EAAE,EAAE,IAClB,aAAc,MAEhB,gBACE,SAAU,SACV,IAAK,EACL,KAAM,EACN,OAAQ,EACR,MAAO,EAET,iCACE,IAAK,OAEP,0BACE,mBAAoB,WACZ,WAAY,WACpB,cAAe,SACf,SAAU,SACV,IAAK,EACL,OAAQ,EACR,MAAO,OACP,YAAa,OACb,SAAU,OACV,QAAS,KAAK,EACd,YAAa,OAEf,8BACE,SAAU,SACV,IAAK,EACL,MAAO,OACP,yBAA0B,MACtB,qBAAsB,MAClB,iBAAkB,MAC1B,kBAAmB,eACf,cAAe,eACX,UAAW,eAErB,0CACE,KAAM;;;;;;;;;;;;;;;;;;;;;;;AAMR,SACA,iBAFA,SAGE,MAAO,KACP,OAAQ,KACR,iBAAkB,KAClB,oBAAqB,KACrB,oBAAqB,KAClB,iBAAkB,KACjB,gBAAiB,KACb,YAAa,KACrB,yBAA0B,KACtB,qBAAsB,KAClB,iBAAkB,KAE5B,iBACE,SAAU,SACV,MAAO,KACP,OAAQ,KAEV,SACA,SACE,YAAa,WAEf,SACE,WAAY,OAEd,SACE,IAAK,EACL,KAAM,EACN,SAAU,SACV,QAAS,YACT,QAAS,aACT,QAAS,YACT,QAAS,KACT,OAAQ,KACR,MAAO,KACP,mBAAoB,SACpB,sBAAuB,OACvB,uBAAwB,OACpB,mBAAoB,OAChB,eAAgB,OACxB,kBAAmB,QACnB,oBAAqB,QACjB,eAAgB,QACZ,YAAa,QACrB,sBAAuB,QACnB,mBAAoB,QAChB,cAAe,QACvB,eAAgB,IAGlB,eADA,eAEE,SAAU,OAGZ,iBADA,iBAGA,iBADA,iBAGA,iBADA,iBAGA,iBADA,iBAEE,SAAU,SAEZ,eACE,QAAS,gBACT,QAAS,YACT,QAAS,aACT,QAAS,YACT,QAAS,KAEX,YACE,WAAY,IACZ,iBAAkB,EAClB,aAAc,EACV,SAAU,EACN,KAAM,EACd,WAAY,QACZ,QAAS,MACT,MAAO,KACP,SAAU,OACV,SAAU,SAEZ,mBACE,gBAAiB,KACjB,QAAS,QAAQ,IACjB,YAAa,IAEf,gBACE,QAAS,EACT,OAAQ,EAEV,SACE,KAAM,EACN,OAAQ,EACR,SAAU,MACV,MAAO,KACP,OAAQ,KACR,WAAY,eACZ,QAAS,MAGX,wBADA,gCAEE,MAAO,KACP,mBAAoB,KACZ,WAAY,KACpB,OAAQ,EAEV,gCACE,MAAO,KAIT,gDADA,8CAEA,gDACA,mDACE,aAAc,MACd,aAAc,IACd,QAAS,KAAK,KACd,WAAY,OACZ,MAAO,KACP,YAAa,IAGf,wDADA,yDAEE,YAAa,IAEf,kDACA,uDACE,MAAO,KAET,oDACA,yDACE,QAAS,MAEX,6CAEA,kDADA,+CAEA,4DACE,MAAO,MAGT,kDADA,+CAEA,4DACE,MAAO,KAGT,4DACA,kDACE,QAAS,MACT,WAAY,OAEd,kDACE,UAAW,MACX,OAAQ,KAAK,IAEf,8CACE,WAAY,OACZ,YAAa,IACb,cAAe,MAEjB,+CACE,OAAQ,YACR,QAAS,YACT,KAAM,YAGR,6BACE,mBAAoB,KACZ,WAAY,KACpB,YAAa,IAIf,sEADA,oEAEA,sEAHA,qEAIE,SAAU,SACV,MAAO,KACP,WAAY,KACZ,mBAAoB,WACZ,WAAY,WAEtB,8CACE,MAAO,IAGT,0DADA,sDAEE,MAAO,KAET,0CACE,MAAO,KACP,OAAQ,EAEV,eACE,aAAc,EAEhB,sCACE,SAAU,SACV,IAAK,IACL,WAAY,MACZ,KAAM,KACN,MAAO,KACP,OAAQ,KACR,aAAc,MACd,aAAc,IACd,cAAe,KAEjB,6CACE,QAAS,GACT,SAAU,SACV,IAAK,IACL,WAAY,KACZ,KAAM,IACN,MAAO,IACP,OAAQ,IACR,oBAAqB,KAAK,MAE5B,4CACE,QAAS,GACT,SAAU,SACV,IAAK,IACL,WAAY,KACZ,MAAO,IACP,MAAO,IACP,OAAQ,IACR,oBAAqB,KAAK,MAG5B,4BACE,wBAAyB,WACjB,gBAAiB,WAG3B,mCADA,yCAEE,QAAS,EACT,OAAQ,EACR,gBAAiB,KACjB,cAAe,EACf,WAAY,IAEd,4BACE,IAAK,IACL,MAAO,MACP,SAAU,SACV,WAAY,QAEd,kDACE,QAAS,EAEX,oDACE,QAAS,GAKX,8CAHA,wCAEA,iDADA,gDAGE,QAAS,MACT,SAAU,SACV,gBAAiB,KACjB,eAAgB,OAChB,mBAAoB,WACZ,WAAY,WACpB,QAAS,KAAK,EAAE,KAAK,IACrB,UAAW,IAEb,wCACA,6DACE,YAAa,IACb,MAAO,KAET,8CACE,SAAU,SACV,OAAQ,EACR,MAAO,KACP,MAAO,KACP,MAAO,KAET,8CACA,oDACE,QAAS,MACT,WAAY,KACZ,SAAU,OACV,cAAe,SACf,mBAAoB,WACZ,WAAY,WACpB,QAAS,KAAK,EACd,OAAQ,EAGV,8CACA,8CAFA,wCAGE,UAAW,IACX,YAAa,MACb,SAAU,OAEZ,8CACA,8CACE,MAAO,KACP,MAAO,KACP,MAAO,KACP,WAAY,MAKd,wCADA,sCADA,yCADA,oCAIE,QAAS,KAEX,8CACE,QAAS,KAAK,EAEhB,qDACE,QAAS,KAAK,EAEhB,2DACE,YAAa,IAEf,0CACE,YAAa,OAGf,oDADA,gDAEE,mBAAoB,WACZ,WAAY,WACpB,QAAS,KAAK,IACd,OAAQ,EAKV,4EAFA,+DACA,yEAFA,8CAIE,QAAS,MACT,QAAS,KAAK,IACd,UAAW,MACX,SAAU,eACV,OAAQ,EACR,YAAa,IACb,YAAa,IACb,WAAY,IACZ,WAAY,IAAI,MAAM,YAGxB,4EADA,yEAEE,SAAU,SAEZ,oDACE,WAAY,EAEd,mDACE,cAAe,EAGjB,iDADA,gDAEE,YAAa,QACb,gBAAiB,KACjB,OAAQ,MAAO,EAAE,MAAO,KAG1B,wBACA,qBACA,6CACA,0CACE,mBAAoB,KACjB,gBAAiB,KACZ,WAAY,KACpB,iBAAkB,YAIpB,6CACA,0CAFA,kDADA,iDAIE,OAAQ,EACR,UAAW,QACX,MAAO,KACP,OAAQ,KACR,OAAQ,MAAM,IAAI,MAAM,EAK1B,mDACA,gDAJA,+BAEA,wDADA,uDAIE,UAAW,QACX,YAAa,QACb,MAAO,OACP,OAAQ,IAEV,kDACE,OAAQ,KACR,MAAO,KAMT,sFADA,0CAHA,sFACA,gEACA,kDAGE,YAAa,EACb,UAAW,IACX,YAAa,MACb,eAAgB,OAChB,OAAQ,KACR,QAAS,EACT,OAAQ,EACR,OAAQ,EACR,WAAY,IACZ,mBAAoB,KACZ,WAAY,KACpB,cAAe,EAEjB,0CACE,OAAQ,EACR,cAAe,EAEjB,gDACE,OAAQ,QAEV,mDACA,2DACE,OAAQ,EACR,WAAY,IAMd,sFADA,0CAFA,uDACA,yCAFA,yGAKE,MAAO,IACP,QAAS,KAAK,EACd,OAAQ,MAAO,EAMjB,gDADA,0CAHA,+CACA,gEACA,kDAGE,mBAAoB,KACjB,gBAAiB,KACZ,WAAY,KACpB,MAAO,MACP,QAAS,EACT,SAAU,SAEZ,sEACE,QAAS,KACT,cAAe,EAEjB,4EACE,MAAO,KACP,MAAO,KACP,QAAS,IAAI,EACb,OAAQ,EACR,cAAe,IACf,YAAa,KACb,aAAc,IACd,aAAc,MAEhB,oEACE,SAAU,SACV,KAAM,KACN,QAAS,EAEX,oCACE,MAAO,KAET,kEACE,WAAY,IACZ,OAAQ,EACR,mBAAoB,KACZ,WAAY,KACpB,QAAS,KAAK,IAEhB,qDACE,MAAO,KAGT,wDADA,oCAEE,QAAS,MACT,aAAc,MAGhB,2DADA,uCAEE,QAAS,WACT,WAAY,OAEd,qEACE,OAAQ,EACR,aAAc,IACd,mBAAoB,WACZ,WAAY,WAEtB,iFACE,WAAY,MAEd,gFACE,cAAe,MAEjB,mFACE,YAAa,IAEf,wDACE,YAAa,IACb,MAAO,KAIT,iDADA,gDADA,+CAGE,SAAU,OACV,mBAAoB,KACZ,WAAY,KAEtB,wDACA,4DACA,4DACE,SAAU,SACV,IAAK,EACL,MAAO,EACP,YAAa,OAEf,+DACA,mEACE,QAAS,MACT,QAAS,aACT,MAAO,EACP,OAAQ,KACR,eAAgB,OAElB,0DACE,OAAQ,IAGV,kDADA,mCAEE,OAAQ,EAGV,4DADA,6CAEE,OAAQ,EAAE,EAAE,EAAE,MAEhB,0CACA,oDACA,uCACE,MAAO,MACP,OAAQ,MACR,YAAa,MACb,cAAe,IACf,mBAAoB,WACZ,WAAY,WAEtB,0CACA,oDACE,MAAO,KACP,YAAa,MACb,QAAS,EAAE,MACX,UAAW,KAEb,+BACE,YAAa,MAEf,kCACE,wDACE,SAAU,SACV,MAAO,KACP,IAAK,KAGP,oEADA,6DAEE,MAAO,IAET,+BACA,kCACE,QAAS,MAGb,+GAKE,sFADA,0CAFA,uDACA,yCAFA,yGAKE,MAAO,IAET,wDACE,YAAa,IAEf,iEACE,YAAa,IAEf,gCACE,aAAc,KACd,cAAe,KAEjB,kCACE,YAAa,EACb,aAAc,EAEhB,+BACE,SAAU,SAEZ,kCACE,MAAO,KACP,QAAS,MACT,SAAU,OACV,aAAc,MACd,MAAO,KACP,cAAe,SACf,OAAQ,MACR,WAAY,OAEd,qCACE,MAAO,KACP,QAAS,aAEX,8BACE,MAAO,MAIX,uEADA,qEAEA,mFACE,MAAO,KAGT,qBACE,oBAAqB,KAClB,iBAAkB,KACjB,gBAAiB,KACb,YAAa,KACrB,wBAAyB,SACzB,kBAAmB,cAErB,6BACE,SAAU,SACV,eAAgB,EAElB,oBACE,SAAU,SACV,WAAY,OACZ,QAAS,OACT,OAAQ,KACR,MAAO,KACP,iBAAkB,eAClB,QAAS,EACT,mBAAoB,QAAQ,IAAK,OACjC,WAAY,QAAQ,IAAK,OACzB,mBAAoB,sBACpB,WAAY,sBAEd,uBACE,OAAQ,KACR,MAAO,IACP,IAAK,IAEP,yBACE,MAAO,KACP,KAAM,IACN,OAAQ,IAGV,0CACE,0CACE,SAAU,SACV,MAAO,IACP,IAAK,IACL,QAAS,MAEX,iDACE,MAAO,KACP,KAAM,IAER,kEACE,QAAS,KAEX,wDACE,QAAS,KAEX,wDACE,QAAS,OAEX,4DACE,QAAS,MACT,aAAc,IAEhB,mEACE,WAAY,KACZ,aAAc,IAEhB,oEACE,QAAS,MACT,SAAU,SACV,cAAe,MACf,aAAc,IACd,mBAAoB,WACZ,WAAY,WAEtB,2EACE,aAAc,EAEhB,0EACE,QAAS,MACT,QAAS,GACT,SAAU,SACV,IAAK,IACL,WAAY,MACZ,MAAO,OACP,MAAO,QACP,OAAQ,QAEV,8DACA,6EACE,QAAS,MACT,OAAQ,EACR,cAAe,EAEjB,2DACE,OAAQ,IAAI,MAAM,QAClB,iBAAkB,KAClB,iBAAkB,KAClB,mBAAoB,EAAE,IAAI,IAAI,EAAE,eACxB,WAAY,EAAE,IAAI,IAAI,EAAE,eAElC,kEACE,WAAY;;;;;;;;;;;;;;;;;;;;;;;AAMhB,wBADA,qBAEE,mBAAoB,IAAI,MAAM,SAC9B,WAAY,IAAI,MAAM,SAExB,MACE,SAAU,SAEZ,oBACE,QAAS,EAEX,iBACE,QAAS,EAEX,aACA,eACE,WAAY,iBAEd,4BACE,QAAS,EAEX,yBACE,QAAS,EAGX,iCACE,kBAAmB,mBACf,cAAe,mBACX,UAAW,mBAErB,+BACE,kBAAmB,mBACf,cAAe,mBACX,UAAW,mBAGrB,4CADA,8CAEE,kBAAmB,mBACf,cAAe,mBACX,UAAW,mBAErB,iDACE,kBAAmB,mBACf,cAAe,mBACX,UAAW,mBAErB,+CACE,kBAAmB,mBACf,cAAe,mBACX,UAAW,mBAGrB,iCACE,YAAa,QACb,QAAS,EAEX,+BACE,QAAS,EAEX,iDACE,YAAa,QACb,QAAS,EAEX,+CACE,QAAS,EAWX,+CAEA,8CADA,8CAJA,4CAEA,2CADA,2CAKE,mBAAoB,IAAI,MAAM,SAC9B,WAAY,IAAI,MAAM,SAExB,8CACE,YAAa,UACb,kBAAmB,iBACf,cAAe,iBACX,UAAW,iBAGrB,6CADA,6CAEE,YAAa,QACb,QAAS,EAEX,+CACE,kBAAmB,kBACf,cAAe,kBACX,UAAW,kBAGrB,2CADA,2CAEE,QAAS,EAEX,8DACE,YAAa,UACb,kBAAmB,cACf,cAAe,cACX,UAAW,cAErB,4DACE,kBAAmB,iBACf,cAAe,iBACX,UAAW,iBAErB,2DACE,kBAAmB,kBACf,cAAe,kBACX,UAAW,kBAErB,yDACE,kBAAmB,cACf,cAAe,cACX,UAAW,cAGrB,6DADA,6DAEE,YAAa,QACb,QAAS,EAGX,0DADA,0DAEE,QAAS,EAGX,2DADA,2DAEE,QAAS,EAGX,wDADA,wDAEE,QAAS,EAKX,yDACE,kBAAmB,kBACf,cAAe,kBACX,UAAW,kBAErB,0DACE,kBAAmB,iBACf,cAAe,iBACX,UAAW,iBAErB,yEACE,kBAAmB,cACf,cAAe,cACX,UAAW,cAErB,uEACE,kBAAmB,kBACf,cAAe,kBACX,UAAW,kBAErB,sEACE,kBAAmB,iBACf,cAAe,iBACX,UAAW,iBAErB,oEACE,kBAAmB,cACf,cAAe,cACX,UAAW,cAQrB,iCACE,YAAa,UACb,kBAAmB,iBACf,cAAe,iBACX,UAAW,iBAErB,kCACE,kBAAmB,kBACf,cAAe,kBACX,UAAW,kBAErB,iDACE,YAAa,UACb,kBAAmB,cACf,cAAe,cACX,UAAW,cAErB,+CACE,kBAAmB,iBACf,cAAe,iBACX,UAAW,iBAErB,8CACE,kBAAmB,kBACf,cAAe,kBACX,UAAW,kBAErB,4CACE,kBAAmB,cACf,cAAe,cACX,UAAW,cAKrB,4CACE,kBAAmB,kBACf,cAAe,kBACX,UAAW,kBAErB,6CACE,kBAAmB,iBACf,cAAe,iBACX,UAAW,iBAErB,4DACE,kBAAmB,cACf,cAAe,cACX,UAAW,cAErB,0DACE,kBAAmB,kBACf,cAAe,kBACX,UAAW,kBAErB,yDACE,kBAAmB,iBACf,cAAe,iBACX,UAAW,iBAErB,uDACE,kBAAmB,cACf,cAAe,cACX,UAAW,cAQrB,iCACE,YAAa,UACb,kBAAmB,iBACf,cAAe,iBACX,UAAW,iBAErB,kCACE,kBAAmB,kBACf,cAAe,kBACX,UAAW,kBAErB,iDACE,YAAa,UACb,kBAAmB,cACf,cAAe,cACX,UAAW,cAErB,+CACE,kBAAmB,iBACf,cAAe,iBACX,UAAW,iBAErB,8CACE,kBAAmB,kBACf,cAAe,kBACX,UAAW,kBAErB,4CACE,kBAAmB,cACf,cAAe,cACX,UAAW,cAKrB,4CACE,kBAAmB,kBACf,cAAe,kBACX,UAAW,kBAErB,6CACE,kBAAmB,iBACf,cAAe,iBACX,UAAW,iBAErB,4DACE,kBAAmB,cACf,cAAe,cACX,UAAW,cAErB,0DACE,kBAAmB,kBACf,cAAe,kBACX,UAAW,kBAErB,yDACE,kBAAmB,iBACf,cAAe,iBACX,UAAW,iBAErB,uDACE,kBAAmB,cACf,cAAe,cACX,UAAW,cAIrB,mDADA,yCAEE,YAAa,UACb,kBAAmB,iBACf,cAAe,iBACX,UAAW,iBAErB,oDACE,kBAAmB,kBACf,cAAe,kBACX,UAAW,kBAErB,iDACE,kBAAmB,iBACf,cAAe,iBACX,UAAW,iBAErB,mDACE,kBAAmB,kBACf,cAAe,kBACX,UAAW,kBAErB,sDACE,kBAAmB,KACf,cAAe,KACX,UAAW,KAErB,yDACE,YAAa,UACb,kBAAmB,KACf,cAAe,KACX,UAAW,KAErB,uDACA,iEACE,kBAAmB,iBACf,cAAe,iBACX,UAAW,iBAErB,kEACE,kBAAmB,kBACf,cAAe,kBACX,UAAW,kBAErB,+DACE,kBAAmB,iBACf,cAAe,iBACX,UAAW,iBAErB,iEACE,kBAAmB,kBACf,cAAe,kBACX,UAAW;;;;;;;;;;;;;;;;;;;;;;;AAIrB,gBACE,SAAU,SAEZ,uCACE,OAAQ,KAEV,mBACE,WAAY,OAEZ,2BAAky3H,MACly3H,SAAU,SAEZ,mCACE,SAAU,SACV,IAAK,EACL,MAAO,KACP,OAAQ,EACR,QAAS,EACT,gBAAiB,KAEnB,wBACE,MAAO,KACP,OAAQ,EACR,QAAS,EACT,gBAAiB,KAEnB,kCACE,WAAY,MAEd,yBACE,SAAU,SAEZ,mDACE,SAAU,SACV,MAAO,KACP,mBAAoB,WACZ,WAAY,WACpB,SAAU,OACV,YAAa,OAEf,kCACA,yBACA,yCACE,eAAgB,UAChB,UAAW,OAEb,kCACE,SAAU,SACV,IAAK,EACL,MAAO,EACP,QAAS,EAAE,KACX,YAAa,IAEf,iCACE,MAAO,KAGT,wCACA,yCAFA,wBAGE,iBAAkB,MAClB,iBAAkB,IAClB,YAAa,EAEf,yBACA,yCACE,WAAY;;;;;;;;;;;;;;;;;;;;;;;AAGd,WACE,YAAa,cACb,IAAK,2CACL,IAAK,kDAAiD,4BAA6B,2CAA0C,mBAAoB,4CAA2C,eAAgB,yDAAwD,cACpQ,YAAa,IACb,WAAY,OAEd,oBACE,QAAS,QAEX,oBACE,QAAS,QAEX,oBACE,QAAS,QAEX,oBACE,QAAS,QAEX,mBACE,QAAS,QAEX,mBACE,QAAS,QAEX,mBACE,QAAS,QAEX,mBACE,QAAS,QAEX,qBACE,QAAS,QAEX,qBACE,QAAS,QAEX,qBACE,QAAS,QAEX,qBACE,QAAS,QAEX,qBACE,QAAS,QAEX,qBACE,QAAS,QAEX,qBACE,QAAS,QAEX,qBACE,QAAS,QAEX,wBACE,QAAS,QAEX,wBACE,QAAS,QAEX,wBACE,QAAS,QAEX,wBACE,QAAS,QAEX,sBACE,QAAS,QAEX,sBACE,QAAS,QAEX,sBACE,QAAS,QAEX,sBACE,QAAS,QAEX,wBACE,QAAS,QAEX,wBACE,QAAS,QAEX,wBACE,QAAS,QAEX,wBACE,QAAS,QAEX,mBACE,QAAS,QAEX,cACE,QAAS,QAEX,sBACE,QAAS,QAEX,iBACE,QAAS,QAEX,kBACE,QAAS,QAEX,iBACE,QAAS,QAEX,kBACE,QAAS,QAEX,kBACE,QAAS,QAEX,mBACE,QAAS,QAEX,mBACE,QAAS,QAEX,yBACE,QAAS,QAEX,oBACE,QAAS,QAEX,0BACE,QAAS,QAEX,oBACE,QAAS,QAEX,qBACE,QAAS,QAEX,qBACE,QAAS,QAEX,gBACE,QAAS,QAEX,kBACE,QAAS,QAEX,qBACE,QAAS,QAEX,kBACE,QAAS,QAEX,mBACE,QAAS,QAEX,oBACE,QAAS,QAEX,qBACE,QAAS,QAEX,kBACE,QAAS,QAEX,uBACE,QAAS,QAEX,sBACE,QAAS,QAEX,wBACE,QAAS,QAEX,yBACE,QAAS,QAEX,kBACE,QAAS,QAEX,wBACE,QAAS,QAEX,iBACE,QAAS,QAEX,kBACE,QAAS,QAEX,gBACE,QAAS,QAEX,iBACE,QAAS,QAEX,gBACE,QAAS,QAEX,iBACE,QAAS,QAEX,iBACE,QAAS,QAEX,mBACE,QAAS,QAEX,iBACE,QAAS,QAEX,oBACE,QAAS,QAEX,sBACE,QAAS,QAEX,kBACE,QAAS,QAEX,kBACE,QAAS,QAEX,iBACE,QAAS,QAEX,kBACE,QAAS,QAEX,oBACE,QAAS,QAEX,sBACE,QAAS,QAEX,sBACE,QAAS,QAEX,gBACE,QAAS,QAEX,iBACE,QAAS,QAEX,iBACE,QAAS,QAEX,uBACE,QAAS,QAEX,uBACE,QAAS,QAEX,uBACE,QAAS,QAEX,sBACE,QAAS,QAEX,gBACE,QAAS,QAEX,iBACE,QAAS,QAEX,kBACE,QAAS,QAEX,iBACE,QAAS,QAEX,mBACE,QAAS,QAEX,sBACE,QAAS,QAEX,2BACE,QAAS,QAEX,iBACE,QAAS,QAEX,sBACE,QAAS,QAEX,wBACE,QAAS,QAEX,eACE,QAAS,QAEX,sBACE,QAAS,QAEX,wBACE,QAAS,QAEX,uBACE,QAAS,QAEX,gBACE,QAAS,QAEX,6BACE,QAAS,QAEX,6BACE,QAAS,QAEX,yBACE,QAAS,QAEX,2BACE,QAAS,QAEX,0BACE,QAAS,QAEX,yBACE,QAAS,QAEX,0BACE,QAAS,QAEX,sBACE,QAAS,QAEX,yBACE,QAAS,QAEX,yBACE,QAAS,QAEX,mBACE,QAAS,QAEX,oBACE,QAAS,QAEX,qBACE,QAAS,QAEX,qBACE,QAAS,QAEX,qBACE,QAAS,QAEX,kCACE,QAAS,QAEX,gCACE,QAAS,QAEX,qBACE,QAAS,QAEX,sBACE,QAAS,QAEX,mBACE,QAAS,QAEX,sBACE,QAAS,QAEX,6BACE,QAAS,QAEX,wBACE,QAAS,QAEX,iBACE,QAAS,QAEX,sBACE,QAAS,QAEX,mBACE,QAAS,QAEX,gBACE,QAAS,QAEX,yBACE,QAAS,QAEX,4BACE,QAAS,QAEX,6BACE,QAAS,QAEX,0BACE,QAAS,QAEX,0BACE,QAAS,QAEX,0BACE,QAAS,QAEX,uBACE,QAAS,QAEX,wBACE,QAAS,QAEX,0BACE,QAAS,QAEX,wBACE,QAAS,QAEX,wBACE,QAAS,QAEX,2BACE,QAAS,QAEX,sCACE,QAAS,QAEX,oCACE,QAAS,QAEX,4BACE,QAAS,QAEX,uBACE,QAAS,QAEX,yBACE,QAAS,QAEX,0BACE,QAAS,QAEX,wBACE,QAAS,QAEX,uBACE,QAAS,QAEX,+BACE,QAAS,QAEX,6BACE,QAAS,QAEX,sBACE,QAAS,QAEX,mBACE,QAAS,QAEX,oBACE,QAAS,QAEX,uBACE,QAAS,QAEX,uBACE,QAAS,QAEX,yBACE,QAAS,QAEX,0BACE,QAAS,QAEX,wBACE,QAAS,QAEX,4BACE,QAAS,QAEX,mBACE,QAAS,QAEX,wBACE,QAAS;;;;;;;;;;;;;;;;;;;;;;;AAGX,eACE,MAAO,MACP,OAAQ,MACR,SAAU,SACV,aAAc,IACd,aAAc,MACd,OAAQ,QAEV,mCACE,cAAe,IACf,SAAU,SACV,aAAc,IACd,aAAc,MACd,SAAU,OACV,mBAAoB,WACZ,WAAY,WAEtB,yCACE,iBAAkB,EAEpB,0CACE,kBAAmB,EAErB,uDACE,SAAU,SACV,YAAa,IACb,mBAAoB,WACZ,WAAY,WACpB,SAAU,OACV,QAAS,IACT,wBAAyB,YACjB,gBAAiB,YAG3B,oCADA,oCAEE,SAAU,SACV,aAAc,MAEhB,oCACE,IAAK,EACL,aAAc,EAAE,EAAE,EAAE,IAEtB,oCACE,KAAM,EACN,aAAc,IAAI,EAAE,EAEtB,+BACA,wCACE,aAAc,IACd,aAAc,MACd,SAAU,SACV,mBAAoB,WACZ,WAAY,WAEtB,0CACE,SAAU,SACV,mBAAoB,WACZ,WAAY,WAEtB,yCACE,aAAc,EAAE,EAAE,IAClB,aAAc,MACd,SAAU,SACV,UAAW,KACX,YAAa,MAAO,QAAS,WAC7B,QAAS,MACT,MAAO,KAGT,oEADA,oEAEE,QAAS,WAEX,oEACE,SAAU,SACV,eAAgB,IAChB,aAAc,EAAE,IAAI,EAAE,EACtB,aAAc,MAEhB,gFACE,MAAO,MACP,cAAe,EACf,aAAc,YAEhB,iGACE,cAAe,EACf,aAAc,YACd,WAAY,IACZ,mBAAoB,KACZ,WAAY,KAEtB,0GACE,cAAe,EAEjB,2GACE,cAAe,EACf,OAAQ,KAAK,KAAK,KAAK,EACvB,QAAS,IAAI,IAAI,IAAI,EAEvB,oEACE,MAAO,KACP,aAAc,KACd,OAAQ,KACR,eAAgB,OAElB,4EACE,iBAAkB,KAClB,YAAa,cACb,MAAO,KACP,WAAY,OACZ,YAAa,IACb,aAAc,OACd,eAAgB,KAChB,UAAW,MACX,YAAa,EACb,QAAS,EACT,YAAa,EACb,uBAAwB,YACxB,wBAAyB,UACzB,QAAS,QACT,QAAS,MACT,SAAU,SACV,IAAK,IACL,kBAAmB,iBACf,cAAe,iBACX,UAAW,iBACnB,aAAc,EAAE,IAAI,EAAE,EACtB,aAAc,MACd,MAAO,KACP,YAAa,MAEf,sEACE,SAAU,OACV,OAAQ,KACR,YAAa,KACb,QAAS,OAAO,EAChB,YAAa,KACb,OAAQ,EACR,OAAQ,EAEV,4CACE,QAAS,EACT,aAAc,EACd,OAAQ,KACR,YAAa,KACb,MAAO,KACP,mBAAoB,WACZ,WAAY,WACpB,YAAa,IACb,QAAS,IAAI,KAAM,EAErB,0CACE,SAAU,SACV,QAAS,KACT,QAAS,EAAE,IACX,YAAa,KACb,QAAS,KACT,SAAU,OAEZ,mCACE,SAAU,SACV,UAAW,KACX,YAAa,MAAO,QAAS,WAE/B,mCACE,SAAU,SACV,YAAa,MAEf,qDACE,QAAS,EAAE,EAAE,EAEf,mDACE,QAAS,aACT,SAAU,SACV,QAAS,EACT,IAAK,EACL,KAAM,EACN,QAAS,EAAE,EAAE,EAEf,6DACE,QAAS,KACT,YAAa,MAIf,iCAFA,uCACA,oCAEA,yCACE,SAAU,SAEZ,4CACE,SAAU,SAEZ,yCACE,aAAc,IAAI,EAAE,EAEtB,wCACE,SAAU,SACV,IAAK,IACL,kBAAmB,iBACf,cAAe,iBACX,UAAW,iBAErB,wCACE,SAAU,SACV,IAAK,KACL,kBAAmB,kBACf,cAAe,kBACX,UAAW,kBAErB,wBACE,SAAU,SACV,IAAK,EACL,MAAO,EACP,OAAQ,KAAK,EAAE,EAAE,KACjB,aAAc,IACd,aAAc,IAAK,IAAK,YAAY,YAEtC,0CACE,QAAS,IACT,OAAQ,IACR,MAAO,IACP,SAAU,SACV,QAAS,MACT,OAAQ,EACR,MAAO,EACP,cAAe,IACf,cAAe,KACf,aAAc,KACd,aAAc,IACd,aAAc,MACd,QAAS,IACT,OAAQ,UAEV,wEACE,QAAS,KAEX,4BACA,kCACE,SAAU,SACV,mBAAoB,WACZ,WAAY,WAEtB,4BACE,aAAc,IACd,aAAc,MACd,OAAQ,UAEV,oCACE,SAAU,SAEZ,mCACE,SAAU,SAEZ,mCACE,SAAU,SACV,MAAO,EACP,IAAK,IACL,kBAAmB,iBACf,cAAe,iBACX,UAAW,iBACnB,QAAS,GACT,OAAQ,QAEV,yCACE,QAAS,EAEX,qBACE,OAAQ,WAEV,mBACE,OAAQ,WAGV,wBACA,qBACA,2BAHA,oBAIE,OAAQ,KAGV,6CACA,0CACA,gDAHA,yCAIE,OAAQ,WAGV,2CACA,wCACA,8CAHA,uCAIE,OAAQ,WAEV,aACA,oBACA,yBACE,YAAa,cACb,MAAO,KACP,WAAY,OACZ,YAAa,IACb,aAAc,OACd,eAAgB,KAChB,UAAW,MACX,YAAa,EACb,QAAS,EACT,YAAa,EACb,uBAAwB,YACxB,wBAAyB,UACzB,iBAAkB,KAClB,UAAW,KAEb,2CACE,SAAU,QAGZ,yBADA,wBAEE,QAAS,MACT,MAAO,KACP,cAAe,EACf,OAAQ,KACR,WAAY,KACZ,YAAa,IAEf,2CACE,OAAQ,IAAI,MAAM,QAClB,YAAa,KACb,aAAc,KACd,QAAS,KAEX,sBACE,SAAU,SACV,OAAQ,QAEV,sBACE,MAAO,MAGT,yBADA,+BAEE,SAAU,SACV,QAAS,EACT,IAAK,EACL,KAAM,EACN,SAAU,OACV,QAAS,EACT,OAAQ,EACR,OAAQ,EACR,MAAO,IACP,OAAQ,IACR,mBAAoB,WACZ,WAAY,WAEtB,iCACE,SAAU,MAEZ,0BACE,SAAU,SACV,IAAK,EACL,KAAM,EACN,QAAS,MACT,aAAc,EAAE,IAAI,IAAI,EACxB,aAAc,MAEhB,gCACE,QAAS,GACT,QAAS,MACT,MAAO,EACP,OAAQ,EACR,SAAU,OACV,SAAU,SACV,OAAQ,EACR,MAAO,EACP,aAAc,IACd,aAAc,MAEhB,wBACE,MAAO,KACP,OAAQ,KACR,SAAU,OACV,2BAA4B,MAC5B,SAAU,SACV,QAAS,EAEX,+BACE,SAAU,SACV,MAAO,KACP,OAAQ,KACR,mBAAoB,WACZ,WAAY,WACpB,QAAS,EACT,oBAAqB,KAClB,iBAAkB,KACjB,gBAAiB,KACb,YAAa,KAEvB,yBACE,SAAU,SAEZ,6BACA,0BACE,WAAY,OAEd,iDACA,8CACE,aAAc,MACd,aAAc,EAAE,IAAI,IAAI,EAE1B,yBACE,QAAS,QAAQ,MAAM,MAEzB,6CACE,MAAO,KAET,mCACE,aAAc,EACd,OAAQ,KAAK,KAAK,EAClB,QAAS,KAAK,IAAI,EAEpB,8CACE,QAAS,KAEX,sCACE,QAAS,MAAM,EACf,aAAc,IACd,aAAc,MACd,OAAQ,MAEV,sCACE,cAAe,EACf,QAAS,KAAK,KAAK,KAAK,KACxB,OAAQ,QACR,YAAa,MAEf,uCACE,MAAO,KAET,uEACE,WAAY,IAEd,wCACE,QAAS,IAEX,oCACE,MAAO,IACP,OAAQ,EAAE,EAAE,EAAE,MAEhB,oCACE,MAAO,IACP,OAAQ,EAAE,OAAO,EAAE,EAErB,mDACE,aAAc,OACd,eAAgB,SAGlB,yDADA,0DAEE,WAAY,KAGd,oCADA,oCAEE,QAAS,EACT,YAAa,IAGf,gDADA,+CAEE,MAAO,KAET,qCACE,aAAc,IAAI,EAAE,EACpB,aAAc,MACd,QAAS,IACT,OAAQ,EAAE,KAEZ,mDACE,MAAO,IACP,MAAO,KACP,YAAa,KAEf,sDACE,QAAS,OACT,QAAS,EACT,MAAO,EACP,OAAQ,EAEV,yDACE,SAAU,SACV,QAAS,aACT,WAAY,OACZ,MAAO,KACP,OAAQ,KACR,YAAa,KACb,aAAc,IACd,aAAc,MAEhB,gEACE,YAAa,cACb,UAAW,KAEb,2DACE,QAAS,QAEX,4DACE,QAAS,QAEX,0CACE,SAAU,SACV,MAAO,IACP,IAAK,IACL,MAAO,KACP,OAAQ,KACR,aAAc,IACd,aAAc,MAEhB,iDACE,SAAU,SACV,IAAK,EACL,MAAO,EACP,QAAS,GACT,QAAS,aACT,MAAO,EACP,OAAQ,EACR,aAAc,IACd,aAAc,MACd,WAAY,KACZ,aAAc,KAEhB,wCACE,IAAK,IACL,MAAO,KACP,OAAQ,KAEV,4CACE,SAAU,SACV,IAAK,IACL,kBAAmB,iBACf,cAAe,iBACX,UAAW,iBACnB,MAAO,KACP,OAAQ,KACR,aAAc,IAAI,EAClB,aAAc,MAEhB,0CACE,SAAU,SACV,IAAK,EACL,KAAM,IACN,kBAAmB,iBACf,cAAe,iBACX,UAAW,iBACnB,MAAO,KACP,OAAQ,KACR,aAAc,EAAE,IAChB,aAAc,MAEhB,8DACE,OAAQ,KAEV,4DACE,MAAO,KAET,2CACA,gCACE,QAAS,IACT,QAAS,MACT,MAAO,KACP,OAAQ,EAEV,mDACE,YAAa,IACb,WAAY,OACZ,UAAW,OACX,YAAa,OACb,OAAQ,EAAE,QAAS,MACnB,aAAc,MACd,aAAc,IAAI,EAEpB,8BACE,MAAO,MAET,2EACE,mBAAoB,WACZ,WAAY,WACpB,MAAO,IAET,8CACE,eAAgB,OAElB,kCACE,MAAO,IACP,OAAQ,MACR,eAAgB,OAChB,QAAS,aAEX,qCACE,eAAgB,IAElB,6CACE,QAAS,KACT,aAAc,EACd,cAAe,EAEjB,iDACE,QAAS,MACT,WAAY,KACZ,eAAgB,QAChB,QAAS,KAAM,MAAM,KAAM,IAC3B,aAAc,EACd,cAAe,EAEjB,2BACE,MAAO,MAET,mCACE,aAAc,EAEhB,2CACE,aAAc,KAEhB,uDACE,YAAa,MACb,MAAO,KAET,iEACE,OAAQ,MACR,WAAY,OACZ,WAAY,KACZ,aAAc,IACd,aAAc,MAEhB,6EACE,SAAU,QACV,QAAS,IAAI,IAEf,sCACE,iBAAkB,IAClB,iBAAkB,MAClB,QAAS,IAAI,EAEf,8CACE,OAAQ,QACR,YAAa,KAEf,sDACE,OAAQ,EAAE,IAEZ,8CACE,QAAS,EAAE,IAAI,EAAE,KAEnB,yDACA,wDACE,MAAO,KACP,cAAe,IAEjB,6DACE,iBAAkB,KAEpB,4DACE,MAAO,KACP,OAAQ,IAAI,EAEd,6CACE,iBAAkB,EAClB,OAAQ,IACR,QAAS,EACT,SAAU,OAEZ,iBACA,eACE,SAAU,SAEZ,sBACE,MAAO,KACP,OAAQ,KAEV,sBACE,MAAO,IACP,OAAQ,KACR,OAAQ,EAAE,KAEZ,8CACE,OAAQ,KACR,MAAO,KACP,MAAO,KAET,8CACE,OAAQ,IACR,MAAO,KAGT,uCADA,8CAEE,aAAc,YACd,iBAAkB,YAClB,iBAAkB,KAGpB,wBACE,kBAAmB,EACnB,mBAAoB,EACpB,iBAAkB,EAEpB,mCACE,kBAAmB,EACnB,mBAAoB,EACpB,oBAAqB,EACrB,OAAQ,EACR,QAAS,EAEX,mCACE,QAAS,KAEX,iCACE,iBAAkB,EAClB,kBAAmB,EACnB,mBAAoB,EAEtB,sDACE,YAAa,EAEf,iDACE,aAAc,EAGhB,iCACA,uCAFA,iCAGA,2FACE,OAAQ,EACR,aAAc,YACd,iBAAkB,YAClB,iBAAkB,KAEpB,yDACE,MAAO,KAET,uDACE,MAAO,MAET,2DACE,MAAO,MAET,sGACE,iBAAkB,YAClB,mBAAoB,KACZ,WAAY,KAEtB,qGACE,aAAc,YAEhB,oCACE,OAAQ,EAAE,IACV,MAAO,EACP,SAAU,OACV,OAAQ,MACR,eAAgB,OAChB,QAAS,aAGX,8DADA,8DAIA,6DAFA,6DACA,6DAEE,gBAAiB,UAEnB,yCACA,yCACA,yCACA,yCACE,iBAAkB,YAEpB,mCACE,aAAc,IACd,aAAc,MACd,SAAU,SACV,mBAAoB,WACZ,WAAY,WAEtB,4BACE,UAAW,MAEb,oCACE,QAAS,EAAE,KACX,mBAAoB,KACZ,WAAY,KAGtB,0BACE,SAAU,SAEZ,uCACE,SAAU,SACV,OAAQ,KACR,KAAM,KACN,QAAS,EAEX,uDACE,QAAS,EACT,OAAQ,KAAM,KAAM,KAAM,MAE5B,sEACE,QAAS,IACT,YAAa,OAEf,+DACE,OAAQ,EAEV,iCACE,WAAY,KACZ,WAAY,cACZ,aAAc,YAEhB,6BACE,SAAU,SACV,MAAO,KACP,QAAS,GACT,YAAa,IACb,QAAS,YAEX,kCACE,SAAU,SACV,IAAK,IACL,KAAM,IACN,kBAAmB,qBACf,cAAe,qBACX,UAAW,qBAErB,cACE,WAAY,IAEd,qBACE,QAAS,GACT,QAAS,MACT,MAAO,KACP,OAAQ,IACR,YAAa,QACb,cAAe,OACf,aAAc,OAEhB,iCACE,QAAS,MACT,QAAS,GACT,MAAO,KAET,WACA,cACE,iBAAkB,KAIpB,YAFA,OACA,UAEE,SAAU,SACV,QAAS,aACT,MAAO,OACP,SAAU,QACV,aAAc,EACd,eAAgB,OAElB,2BAKA,wCAFA,wCACA,4CAHA,mCACA,sCAIE,OAAQ,EACR,IAAK,IACL,kBAAmB,iBACf,cAAe,iBACX,UAAW,iBAErB,kCACA,+CACE,WAAY,KAMd,wCAFA,wCACA,4CAHA,mCACA,sCAIE,UAAW,KACX,MAAO,OAET,8CACE,IAAK,IAEP,2BACA,8CACE,aAAc,IAQhB,2CANA,yCAWA,wDANA,sDAIA,wDANA,sDAOA,4DANA,0DAGA,mDANA,iDAOA,sDANA,oDAUE,QAAS,aACT,QAAS,EAEX,4CACA,yDACE,QAAS,KAGX,8DADA,4DAEE,QAAS,aACT,QAAS,EAEX,+CACA,4DACE,WAAY,KAId,+BAFA,sBACA,yBAEE,MAAO,OAET,OACA,UACE,YAAa,OAGf,iBACA,oBACE,aAAc,MACd,aAAc,EAAE,EAAE,EAAE,IACpB,aAAc,QAIhB,yBAGA,0BADA,yBADA,wBAGA,sBALA,iBAME,OAAQ,KACR,YAAa,KACb,QAAS,IAAI,EACb,YAAa,KACb,OAAQ,EACR,OAAQ,EAIV,2BADA,0BADA,yBAGE,WAAY,KACZ,YAAa,KACb,eAAgB,OAChB,gBAAiB,WACjB,WAAY,OACZ,OAAQ,KAEV,6BACA,4BACE,SAAU,SACV,MAAO,IACP,OAAQ,IAEV,gBACA,mBACE,QAAS,IAAI,EAEf,gBACE,OAAQ,eACR,YAAa,KACb,YAAa,MAEf,oCAEA,uBADA,sBAEE,QAAS,KAGX,YACE,mBAAoB,WACZ,WAAY,WACpB,MAAO,KACP,OAAQ,KACR,MAAO,KACP,OAAQ,IAAI,MAAM,KAClB,QAAS,IACT,iBAAkB,KAEpB,6BACE,iBAAkB,QAEpB,kBACE,QAAS,EACT,mBAAoB,MAAM,EAAE,IAAI,IAAI,iBAAqB,EAAE,EAAE,IAAI,qBACzD,WAAY,MAAM,EAAE,IAAI,IAAI,iBAAqB,EAAE,EAAE,IAAI,qBAEnE,iDACA,yDACE,QAAS,QAEX,gBACA,eACE,WAAY,OACZ,QAAS,YAEX,YACE,MAAO,KACP,OAAQ,KACR,cAAe,IACf,OAAQ,IAAI,MAAM,KAClB,QAAS,aACT,eAAgB,OAChB,iBAAkB,KAEpB,6BACE,iBAAkB,QAEpB,kBACE,QAAS,EACT,mBAAoB,MAAM,EAAE,IAAI,IAAI,iBAAqB,EAAE,EAAE,IAAI,qBACzD,WAAY,MAAM,EAAE,IAAI,IAAI,iBAAqB,EAAE,EAAE,IAAI,qBAEnE,6CACA,qDACE,cAAe,IACf,QAAS,MACT,SAAU,SACV,IAAK,IACL,KAAM,IACN,QAAS,GACT,MAAO,KACP,OAAQ,KACR,iBAAkB,KAEpB,UACE,MAAO,KACP,OAAQ,KACR,OAAQ,QACR,cAAe,IACf,mBAAoB,sBACZ,WAAY,sBACpB,OAAQ,IAAI,MAAM,KAClB,QAAS,aACT,eAAgB,OAChB,iBAAkB,KAEpB,2BACE,iBAAkB,QAEpB,wBACE,cAAe,IACf,QAAS,MACT,SAAU,SACV,IAAK,IACL,KAAM,IACN,QAAS,GACT,MAAO,KACP,OAAQ,KACR,iBAAkB,KAEpB,kBACE,QAAS,cAIX,gBACE,YAAa,IAGf,wBADA,wBAEE,YAAa,IAGf,iBACA,2BACE,QAAS,IACT,UAAW,OACX,YAAa,IACb,cAAe,IAAI,MAAM,QACzB,MAAO,QACP,iBAAkB,KAClB,iBAAkB,8DAClB,iBAAkB,oDAClB,iBAAkB,6CAEpB,yBACE,QAAS,IACT,aAAc,kBACd,iBAAkB,QAEpB,qBACE,aAAc,IAEhB,kBACE,cAAe,IAAI,MAAM,QAE3B,WACE,QAAS,IACT,WAAY,KAGd,yBADA,cAEE,cAAe,YACf,YAAa,eAEf,gCACE,iBAAkB,kBAClB,MAAO,eAET,sBACE,MAAO,kBAET,2BACA,iCACE,iBAAkB,QAClB,MAAO,KAIT,0BAFA,qDACA,mDAEA,kCACA,yBACA,6BACA,iBACA,mBACE,WAAY,eACZ,mBAAoB,eAGtB,6CACE,UAAW,KACX,YAAa,KAEf,aACE,mBAAoB,EAAE,IAAI,KAAK,yBAC/B,WAAY,EAAE,IAAI,KAAK,yBAEzB,gBACE,OAAQ,KACR,YAAa,KACb,aAAc,IACd,UAAW,KACX,MAAO,QAET,mBACE,OAAQ,KACR,QAAS,IAAI,EACb,iBAAkB,sBAClB,aAAc,eAGhB,6BACE,OAAQ,KACR,aAAc,KAEhB,+BACE,YAAa,OAEf,oCACE,iBAAkB,QAGpB,qBACA,kBACE,OAAQ,EAGV,WACE,iBAAkB,KAClB,mBAAoB,QAAQ,KAAK,OACjC,WAAY,QAAQ,KAAK;;;;;;;;;;;;;;;;;;;;;;;AAK3B,WACE,YAAa,cACb,IAAK,uCAAsC,mBAE7C,WACE,YAAa,cACb,YAAa,IACb,IAAK,4CAA2C,mBAElD,WACE,YAAa,cACb,WAAY,OACZ,IAAK,+CAA8C,mBAErD,WACE,YAAa,cACb,YAAa,IACb,WAAY,OACZ,IAAK,mDAAkD,mBAGzD,WACE,YAAa,eACb,IAAK,wCAAuC,mBAE9C,WACE,YAAa,eACb,YAAa,IACb,IAAK,6CAA4C,mBAEnD,WACE,YAAa,eACb,WAAY,OACZ,IAAK,+CAA8C,mBAErD,WACE,YAAa,eACb,YAAa,IACb,WAAY,OACZ,IAAK,mDAAkD,mBAGzD,WACE,YAAa,cACb,IAAK,2CAA0C,mBAEjD,WACE,YAAa,cACb,YAAa,IACb,IAAK,gDAA+C,mBAEtD,WACE,YAAa,cACb,WAAY,OACZ,IAAK,mDAAkD,mBAEzD,WACE,YAAa,cACb,YAAa,IACb,WAAY,OACZ,IAAK,uDAAsD,mBAG7D,sCADA,uCAEE,QAAS,eAEX,UACA,qBACE,YAAa,QACb,QAAS,IAAI,KAEf,qBACE,iBAAkB,KAClB,aAAc,KACd,mBAAoB,KACZ,WAAY,KAEtB,8BACE,QAAS,EAGX,sCADA,0BAEE,OAAQ,EAGV,yBADA,4BAEE,YAAa,MAEf,qBACE,WAAY,KAEd,8BACE,WAAY,KAEd,sCACE,eAAgB,UAChB,aAAc,EACd,aAAc,MACd,QAAS,EAEX,8CACA,+CACE,QAAS,KAGX,kDADA,gDAEE,cAAe,KAEjB,0CACE,oBAAqB,IAGvB,mDADA,iDAEE,aAAc,KAEhB,2CACE,mBAAoB,IAEtB,4CACE,kBAAmB,IAErB,6CACE,iBAAkB,IAEpB,sCACE,QAAS,QAEX,gBACE,aAAc,IACd,aAAc,MAEhB,qBACE,WAAY,KAGd,iBADA,2BAEE,QAAS,OAAO,KAElB,2BACE,aAAc,QAEhB,mBACE,cAAe,QAEjB,6BACE,aAAc,OACd,cAAe,OACf,YAAa,QACb,OAAQ,EAEV,mCACE,MAAO,QACP,MAAO,QAET,eACE,QAAS,KAAK,KAAK,EAErB,yBACE,OAAQ,EACR,MAAO,IACP,cAAe,EAEjB,0BACE,MAAO,MAET,kCACE,OAAQ,KAAM,MAAO,EACrB,iBAAkB,IAClB,iBAAkB,MAEpB,0BACE,kBAAmB,IACnB,kBAAmB,MAErB,sCACE,QAAS,KAAK,MAAM,OAAO,KAE7B,2BACE,QAAS,KAAK,KAAK,OAGrB,6BADA,qBAEE,eAAgB,SAElB,WACE,QAAS,OAAQ,QAEnB,mCACE,QAAS,EACT,OAAQ,EACR,WAAY,KACZ,YAAa,QAEf,mBACE,YAAa,IAEf,iCACE,oBAAqB,EAEvB,cACA,8BACE,oBAAqB,IACrB,aAAc,MAEhB,sBACE,iBAAkB,EAEpB,wCACA,+CACE,WAAY,EAEd,sBACE,kBAAmB,EAErB,mCACE,YAAa,QACb,OAAQ,QAEV,yBACA,mCACE,aAAc,IAAI,EAAE,EACpB,YAAa,KAEf,cACE,YAAa,QACb,QAAS,OAAO,EAAE,OAAO,MAG3B,yBADA,sBAEE,OAAQ,QACR,YAAa,QAGf,eADA,sBAEE,QAAS,EACT,UAAW,QAEb,iDACE,WAAY,OACZ,YAAa,MACb,UAAW,QAEb,8BACE,OAAQ,QAEV,cACE,QAAS,OAAO,QAAQ,OAAO,QAEjC,+BACE,aAAc,QAEhB,0BACE,MAAO,MAET,2BACA,0BACE,OAAQ,KAEV,iBAEA,gBADA,eAEE,QAAS,EAAE,QAAQ,EAAE,EAIvB,2BADA,0BADA,yBAGE,MAAO,QAET,kBACE,MAAO,OAET,4BACE,MAAO,IAET,iCACE,cAAe,IAEjB,yCACE,OAAQ,EAAE,IAEZ,YACE,MAAO,KAET,sBACE,OAAQ,EAAE,KAEZ,eACE,QAAS,EAEX,+BACE,WAAY,QACZ,YAAa,QACb,QAAS,EACT,WAAY,OAEd,eACE,oBAAqB,EACrB,QAAS,OAAO,MAAM,OAAO,EAE/B,sBACE,iBAAkB,IAClB,iBAAkB,MAClB,OAAQ,EAAE,KAEZ,0BACE,QAAS,OAAQ,EAEnB,6BACE,QAAS,EAGX,iCADA,yBAEE,WAAY,QACZ,YAAa,QAEf,0DACE,cAAe,MAEjB,yBACE,QAAS,IAAI,KAEf,UAEA,sBADA,8BAEE,eAAgB,UAElB,iCACE,MAAO,IACP,OAAQ,QACR,YAAa,QACb,OAAQ,OAAQ,UAAW,OAAQ,EAErC,8BACE,eAAgB,OAGlB,iCADA,iCAEE,SAAU,SACV,IAAK,OACL,YAAa,QACb,OAAQ,QAGV,yCADA,yCAEE,OAAQ,QACR,MAAO,QAET,iCACE,KAAM,OAER,iCACE,MAAO,OAET,mCACA,wCACE,QAAS,QAAQ,EAAE,QAErB,8BACE,QAAS,EACT,OAAQ,EAEV,6BACE,OAAQ,KAEV,uBACE,cAAe,QACf,OAAQ,IAAI,EAAE,IAAI,IAClB,QAAS,EAAE,MAAM,EAAE,OACnB,YAAa,OAEf,4BACE,aAAc,KAEhB,8BACE,YAAa,EAEf,0BACE,eAAgB,SAElB,0BACE,OAAQ,QACR,YAAa,QAEf,uCACE,eAAgB,OAElB,uCACE,eAAgB,IAGlB,gBADA,iBAEA,oCACE,aAAc,EAEhB,wBACE,aAAc,IACd,aAAc,MAEhB,gDACA,sCACE,QAAS,KAAM,QAAQ,KAAM,QAE/B,qCACE,MAAO,SAET,6CACE,QAAS,QAAQ,EAEnB,4DACE,QAAS,EAEX,6CACE,OAAQ,EAEV,eACE,eAAgB,EAElB,2BACE,aAAc,IAAI,EAAE,EAEtB,iCACE,aAAc,KAEhB,WACE,YAAa,OAEf,qBACE,YAAa,OAEf,sCACE,WAAY,KACZ,cAAe,KAEjB,uBACE,WAAY,KAEd,qCACE,YAAa,KACb,eAAgB,KAElB,2BACE,YAAa,QAEf,qCACE,YAAa,QACb,QAAS,EAAE,OAEb,oBACE,OAAQ,KACR,YAAa,KACb,QAAS,OAAQ,EACjB,YAAa,KACb,OAAQ,EACR,OAAQ,EAEV,8BACE,aAAc,EACd,YAAa,QACb,MAAO,OACP,OAAQ,OACR,QAAS,EAEX,yCACA,6BACE,UAAW,MAEb,wBACE,OAAQ,IAAI,EAEd,iCACE,aAAc,KACd,cAAe,KAEjB,+CACA,wCACE,MAAO,KACP,OAAQ,KAGV,sCADA,kCAEE,QAAS,EAEX,0CACE,MAAO,KACP,OAAQ,KACR,YAAa,KACb,OAAQ,EAGV,sDADA,0BAEE,aAAc,EAEhB,oCACE,YAAa,IAEf,sCACE,YAAa,MAEf,gDACE,WAAY,IAEd,qDACE,WAAY,KAEd,0CACE,eAAgB,OAElB,wCACE,IAAK,IACL,MAAO,IAET,8BACE,QAAS,IAAI,KAEf,4CACE,MAAO,KAET,cACE,aAAc,IAEhB,eACE,QAAS,IAAI,KAEf,mBACA,mBACE,QAAS,KAAM,EAAE,OAEnB,qCACE,MAAO,OAET,aACE,aAAc,EAEhB,gBACE,MAAO,QACP,KAAM,QAER,qBACE,QAAS,QAEX,yCACE,QAAS,EAEX,iDACE,YAAa,KAEf,QACE,QAAS,OAAQ,OAAO,OAAQ,KAElC,sBACA,sBACE,OAAQ,QAEV,oCACE,OAAQ,QAEV,oCACE,OAAQ,QAKV,wCAFA,yCACA,+BAFA,gCAIE,aAAc,EAEhB,iBACE,aAAc,EAEhB,yBACE,YAAa,QAEf,wBACE,WAAY,QAEd,0BACE,QAAS,OAAO,MAAM,OAAO,KAE/B,2BACE,MAAO,KACP,OAAQ,KAEV,4BACE,YAAa,KACb,aAAc,IACd,cAAe,EAAE,IAAI,IAAI,IACzB,aAAc,MACd,OAAQ,MAEV,8BACE,WAAY,KAEd,qBACE,QAAS,QAEX,gCACE,IAAK,KAEP,gBACE,aAAc,EAEhB,4BACE,WAAY,IAGd,yCADA,qCAEE,OAAQ,IACR,WAAY,KAGd,uCADA,mCAEE,MAAO,IACP,YAAa,KAIf,+CACA,wDAHA,mCACA,iCAGE,MAAO,IACP,OAAQ,IAEV,+CACE,WAAY,KAEd,8BACE,YAAa,KAEf,iCACE,OAAQ,KACR,QAAS,OAAQ,EAEnB,+BACA,oCACA,gEACA,yEACE,MAAO,KACP,OAAQ,KAEV,+CACE,YAAa,KAGf,gEADA,oDAEE,IAAK,KAEP,kDACE,KAAM,KAKR,qBADA,mBAEE,MAAO,KAGT,yBACE,aAAc,QAGhB,gBADA,mBAEE,QAAS,OAEX,mBACE,aAAc,QAEhB,gBACE,QAAS,IAGX,wBADA,wBAEE,cAAe,KAEjB,8BACE,cAAe,IAGjB,iBAGA,yCAFA,kCACA,wCAEE,eAAgB,UAElB,sCACE,QAAS,KAEX,uBACA,2BACE,aAAc,EAAE,EAAE,IAAI,EAIxB,gBAFA,4BACA,gCAEE,eAAgB,OAChB,YAAa,KAEf,gBACE,QAAS,EAEX,iBACE,eAAgB,OAChB,YAAa,KAEf,2BACE,eAAgB,OAElB,iBAEA,oBADA,qBAEE,YAAa,KACb,eAAgB,OAElB,oBACE,QAAS,EAEX,8BACE,MAAO,MAET,yBACE,QAAS,IACT,QAAS,MACT,MAAO,KAET,iBACA,qBACE,QAAS,EAAE,QAEb,wBACE,YAAa,QAEf,aACE,UAAW,IACX,QAAS,MAAM,IAEjB,4CACE,oBAAqB,EAAE,EAGzB,oEADA,qDAEE,oBAAqB,EAAE,KAGzB,oEADA,qDAEE,oBAAqB,EAAE,MAGzB,UACA,gBAFA,WAGE,oBAAqB,MAAM,MAE7B,yBACE,WAAY,KAEd,gEACE,oBAAqB,OAAO,MAE9B,iCACA,sEACE,oBAAqB,MAAM,OAE7B,iCACA,sEACE,oBAAqB,MAAM,OAE7B,4BACA,iEACE,oBAAqB,OAAO,OAE9B,+BACA,oEACE,oBAAqB,OAAO,OAE9B,gCACA,qEACE,oBAAqB,MAAM,OAE7B,8BACA,mEACE,oBAAqB,MAAM,MAE7B,gCACA,qEACE,oBAAqB,MAAM,OAE7B,gCAEA,8DADA,+CAEE,oBAAqB,MAAM,MAC3B,QAAS,EAEX,gCAEA,8DADA,+CAEE,oBAAqB,MAAM,MAC3B,QAAS,EAEX,qBACE,oBAAqB,EAAE,MAEzB,oBACE,oBAAqB,EAAE,MAEzB,uBACE,oBAAqB,IAEvB,8CACE,oBAAqB,OAAO,KAE9B,8CACE,oBAAqB,KAAK,OAE5B,8CACE,oBAAqB,KAAK,OAE5B,4CACE,oBAAqB,MAAM,OAE7B,4CACE,oBAAqB,IAAI,OAE3B,4CACE,oBAAqB,IAAI,OAE3B,wDACE,oBAAqB,OAAO,KAE9B,wDACE,oBAAqB,MAAM,OAE7B,wDACE,oBAAqB,MAAM,OAE7B,sDACE,oBAAqB,MAAM,OAE7B,sDACE,oBAAqB,MAAM,OAE7B,sDACE,oBAAqB,MAAM,OAE7B,kBACE,oBAAqB,MAAM,MAE7B,4BACA,iEACE,oBAAqB,EAAE,OAEzB,4BACA,iEACE,oBAAqB,EAAE,OAEzB,8BACA,qCACE,QAAS,OACT,OAAQ,QAAS,MAEnB,sDACE,oBAAqB,MAAM,MAG7B,mCADA,mBAEE,oBAAqB,MAAM,MAG7B,qCADA,qBAEE,oBAAqB,MAAM,MAE7B,uBACA,uCACE,oBAAqB,MAAM,MAG7B,0CADA,0BAEE,oBAAqB,MAAM,MAG7B,0CADA,0BAEE,oBAAqB,MAAM,MAG7B,+DADA,mCAEE,oBAAqB,OAAO,MAG9B,sDADA,iCAEE,oBAAqB,MAAM,OAG7B,sDADA,iCAEE,oBAAqB,MAAM,OAE7B,2DACA,kEACE,oBAAqB,MAAM,OAE7B,0BACA,iDACE,oBAAqB,MAAM,MAE7B,0BACA,iDACE,oBAAqB,MAAM,MAE7B,wBACA,+CACE,QAAS,EACT,oBAAqB,MAAM,OAG7B,8DADA,yBAEE,oBAAqB,OAAO,MAE9B,0BACE,oBAAqB,MAAM,OAI7B,mCAFA,mBACA,4CAEE,oBAAqB,MAAM,MAE7B,uBACE,oBAAqB,EAAE,MAEzB,qBACE,oBAAqB,EAAE,EAGzB,kEADA,2BAEE,oBAAqB,MAAM,OAE7B,iBACA,wDACE,oBAAqB,EAAE,OAGzB,sEADA,gDAEE,oBAAqB,OAAO,OAG9B,uEADA,iDAEE,oBAAqB,OAAO,OAG9B,iCADA,uCAEE,oBAAqB,MAAM,OAI7B,4BADA,kCADA,YAGE,oBAAqB,OAAO,MAE9B,kDAEA,8DADA,+CAEA,2DACE,oBAAqB,EAAE,OAEzB,kDAEA,8DADA,+CAEA,2DACE,oBAAqB,EAAE,OAEzB,kDAEA,8DADA,+CAEA,2DACE,oBAAqB,EAAE,OAEzB,sBACE,oBAAqB,EAAE,OAEzB,sBACE,oBAAqB,EAAE,OAEzB,sBACE,oBAAqB,EAAE,OAEzB,iCACE,oBAAqB,EAAE,OAEzB,iCACE,oBAAqB,EAAE,OAEzB,iCACE,oBAAqB,EAAE,OAEzB,+BACA,qCACE,oBAAqB,MAAM,MAI7B,2DADA,iEADA,2CAGE,oBAAqB,OAAO,MAG9B,iEADA,oCAEE,oBAAqB,MAAM,OAG7B,4DADA,+BAEE,oBAAqB,OAAO,MAE9B,0BACE,oBAAqB,OAAO,OAE9B,mDACE,aAAc,IAEhB,qCACE,QAAS,EAEX,kDACE,MAAO,KACP,OAAQ,KACR,aAAc,EACd,YAAa,EACb,IAAK,IACL,KAAM,IACN,kBAAmB,KACf,cAAe,KACX,UAAW,KACnB,QAAS,EAEX,sCACE,IAAK,IACL,KAAM,IAER,kCAEA,iCADA,gCAEE,aAAc,MAEhB,iDACE,aAAc,IAEhB,yDACE,OAAQ,EAAE,IAEZ,uCACA,qEACA,iCACA,sEACA,wDACE,oBAAqB,MAAM,MAE7B,uCACA,qEACA,iCACA,sEACA,wDACE,oBAAqB,MAAM,MAE7B,0CACA,oDACA,uCACE,cAAe,EAEjB,0CACA,uCACA,6CACE,QAAS,OAAO,MAElB,oDACE,QAAS,OAAO,MAAM,OAExB,0CACE,UAAW,QAEb,kEACE,oBAAqB,IACrB,oBAAqB,MACrB,eAAgB,EAElB,0FACE,cAAe", "file": "web/kendo.common-material.min.css", "sourceRoot": "/source/", "sourcesContent": ["/** \r\n * Kendo UI v2016.2.714 (http://www.telerik.com/kendo-ui)                                                                                                                                               \r\n * Copyright 2016 Telerik AD. All rights reserved.                                                                                                                                                      \r\n *                                                                                                                                                                                                      \r\n * Kendo UI commercial licenses may be obtained at                                                                                                                                                      \r\n * http://www.telerik.com/purchase/license-agreement/kendo-ui-complete                                                                                                                                  \r\n * If you do not own a commercial license, this file shall be governed by the trial license terms.                                                                                                      \r\n                                                                                                                                                                                                       \r\n                                                                                                                                                                                                       \r\n                                                                                                                                                                                                       \r\n                                                                                                                                                                                                       \r\n                                                                                                                                                                                                       \r\n                                                                                                                                                                                                       \r\n                                                                                                                                                                                                       \r\n                                                                                                                                                                                                       \r\n                                                                                                                                                                                                       \r\n                                                                                                                                                                                                       \r\n                                                                                                                                                                                                       \r\n                                                                                                                                                                                                       \r\n                                                                                                                                                                                                       \r\n                                                                                                                                                                                                       \r\n                                                                                                                                                                                                       \r\n\r\n*/\r\n/* Kendo base CSS */\r\n.fake {\r\n  color: red;\r\n}\r\n.k-common-test-class {\r\n  opacity: 0;\r\n}\r\n.k-reset {\r\n  margin: 0;\r\n  padding: 0;\r\n  border: 0;\r\n  outline: 0;\r\n  text-decoration: none;\r\n  font-size: 100%;\r\n  list-style: none;\r\n}\r\n.k-floatwrap:after,\r\n.k-slider-items:after,\r\n.k-grid-toolbar:after {\r\n  content: \"\";\r\n  display: block;\r\n  clear: both;\r\n  visibility: hidden;\r\n  height: 0;\r\n  overflow: hidden;\r\n}\r\n.k-floatwrap,\r\n.k-slider-items,\r\n.k-grid-toolbar {\r\n  display: inline-block;\r\n}\r\n.k-floatwrap,\r\n.k-slider-items,\r\n.k-grid-toolbar {\r\n  display: block;\r\n}\r\n/* main gradient */\r\n.k-block,\r\n.k-button,\r\n.k-header,\r\n.k-grid-header,\r\n.k-toolbar,\r\n.k-grouping-header,\r\n.k-tooltip,\r\n.k-pager-wrap,\r\n.k-tabstrip-items .k-item,\r\n.k-link.k-state-hover,\r\n.k-textbox,\r\n.k-textbox:hover,\r\n.k-autocomplete,\r\n.k-dropdown-wrap,\r\n.k-picker-wrap,\r\n.k-numeric-wrap,\r\n.k-autocomplete.k-state-hover,\r\n.k-dropdown-wrap.k-state-hover,\r\n.k-picker-wrap.k-state-hover,\r\n.k-numeric-wrap.k-state-hover,\r\n.k-draghandle {\r\n  background-repeat: repeat;\r\n  background-position: 0 center;\r\n}\r\n.k-link:hover {\r\n  text-decoration: none;\r\n}\r\n.k-state-highlight > .k-link {\r\n  color: inherit;\r\n}\r\n/* widget */\r\n.k-textbox > input,\r\n.k-input[type=\"text\"],\r\n.k-input[type=\"number\"],\r\n.k-textbox,\r\n.k-picker-wrap .k-input,\r\n.k-button {\r\n  font-size: 100%;\r\n  font-family: inherit;\r\n  border-style: solid;\r\n  border-width: 1px;\r\n  -webkit-appearance: none;\r\n}\r\n.k-widget,\r\n.k-block,\r\n.k-inline-block,\r\n.k-draghandle {\r\n  border-style: solid;\r\n  border-width: 1px;\r\n  -webkit-appearance: none;\r\n}\r\n.k-block,\r\n.k-widget {\r\n  line-height: normal;\r\n  outline: 0;\r\n}\r\n.k-widget ::-ms-clear,\r\n.k-list-filter ::-ms-clear {\r\n  width: 0;\r\n  height: 0;\r\n}\r\n/* Block */\r\n.k-block {\r\n  padding: 2px;\r\n}\r\n/* button */\r\n.k-button {\r\n  display: inline-block;\r\n  margin: 0;\r\n  padding: 10px 14px;\r\n  font-family: inherit;\r\n  line-height: 1.72em;\r\n  text-align: center;\r\n  cursor: pointer;\r\n  text-decoration: none;\r\n}\r\n.k-button[disabled],\r\n.k-button.k-state-disabled,\r\n.k-state-disabled .k-button,\r\n.k-state-disabled .k-button:hover,\r\n.k-button.k-state-disabled:hover,\r\n.k-state-disabled .k-button:active,\r\n.k-button.k-state-disabled:active {\r\n  cursor: default;\r\n}\r\na.k-button {\r\n  -webkit-user-select: none;\r\n     -moz-user-select: none;\r\n      -ms-user-select: none;\r\n          user-select: none;\r\n  text-decoration: none;\r\n}\r\n/* Override the important default line-height in Firefox 4+ */\r\n.k-ff input.k-button {\r\n  padding-bottom: 0.37em;\r\n  padding-top: 0.37em;\r\n}\r\nbutton.k-button::-moz-focus-inner,\r\ninput.k-button::-moz-focus-inner {\r\n  padding: 0;\r\n  border: 0;\r\n}\r\na.k-button-expand {\r\n  display: block;\r\n}\r\nbutton.k-button-expand,\r\ninput[type=\"submit\"].k-button-expand,\r\ninput[type=\"button\"].k-button-expand,\r\ninput[type=\"reset\"].k-button-expand {\r\n  width: 100%;\r\n}\r\nbody .k-button-icon,\r\nbody .k-split-button-arrow {\r\n  padding-left: .4em;\r\n  padding-right: .4em;\r\n}\r\n.k-button-icontext {\r\n  overflow: visible;\r\n  /*IE9*/\r\n}\r\n.k-toolbar .k-button-icontext {\r\n  padding-right: .8em;\r\n}\r\n.k-button-icontext .k-icon,\r\n.k-button-icontext .k-image,\r\n.k-button-icontext .k-sprite {\r\n  margin-right: 3px;\r\n  margin-right: .3rem;\r\n  margin-left: -3px;\r\n  margin-left: -0.3rem;\r\n}\r\n.k-button.k-button-icontext .k-icon,\r\n.k-button.k-button-icontext .k-image {\r\n  vertical-align: text-top;\r\n}\r\nhtml body .k-button-bare {\r\n  background: none !important;\r\n  /*spares long selectors*/\r\n  color: inherit;\r\n  border-width: 0;\r\n}\r\nhtml body .k-button-bare.k-upload-button:hover {\r\n  color: inherit;\r\n}\r\n/* link */\r\n.k-link {\r\n  cursor: pointer;\r\n  outline: 0;\r\n  text-decoration: none;\r\n}\r\n.k-grid-header span.k-link {\r\n  cursor: default;\r\n}\r\n/* states */\r\n.k-state-disabled,\r\n.k-state-disabled .k-link,\r\n.k-state-disabled .k-icon,\r\n.k-state-disabled .k-button,\r\n.k-state-disabled .k-draghandle,\r\n.k-state-disabled .k-upload-button input {\r\n  cursor: default !important;\r\n  outline: 0;\r\n}\r\n@media print {\r\n  .k-state-disabled,\r\n  .k-state-disabled .k-input {\r\n    opacity: 1 !important;\r\n  }\r\n}\r\n.k-state-error {\r\n  border-style: ridge;\r\n}\r\n.k-state-empty {\r\n  font-style: italic;\r\n}\r\n/* icons */\r\n.k-icon,\r\n.k-sprite,\r\n.k-button-group .k-tool-icon {\r\n  display: inline-block;\r\n  width: 16px;\r\n  height: 16px;\r\n  overflow: hidden;\r\n  background-repeat: no-repeat;\r\n  font-size: 0;\r\n  line-height: 0;\r\n  text-align: center;\r\n  -ms-high-contrast-adjust: none;\r\n}\r\n.k-icon.k-i-none {\r\n  background-image: none !important;\r\n  /* should never be a background on these */\r\n}\r\n/* In IE7 vertical align: middle can't be overridden */\r\n.k-ie8 .k-icon,\r\n.k-ie8 .k-sprite,\r\n.k-ie8 .k-button-group .k-tool-icon {\r\n  vertical-align: middle;\r\n}\r\n:root * > .k-icon,\r\n:root * > .k-sprite,\r\n:root * > .k-button-group .k-tool-icon {\r\n  vertical-align: middle;\r\n}\r\n.k-icon,\r\n.k-sprite {\r\n  background-color: transparent;\r\n}\r\n.k-numerictextbox .k-select .k-link span.k-i-arrow-n {\r\n  background-position: 0 -3px;\r\n}\r\n.k-numerictextbox .k-select .k-link span.k-i-arrow-s {\r\n  background-position: 0 -35px;\r\n}\r\n.k-state-selected .k-i-arrow-n {\r\n  background-position: -16px 0px;\r\n}\r\n.k-link:not(.k-state-disabled):hover > .k-state-selected .k-i-arrow-n,\r\n.k-state-hover > .k-state-selected .k-i-arrow-n,\r\n.k-state-hover > * > .k-state-selected .k-i-arrow-n,\r\n.k-button:not(.k-state-disabled):hover .k-state-selected .k-i-arrow-n,\r\n.k-textbox:hover .k-state-selected .k-i-arrow-n,\r\n.k-button:active .k-state-selected .k-i-arrow-n {\r\n  background-position: -16px 0px;\r\n}\r\n.k-numerictextbox .k-link.k-state-selected span.k-i-arrow-n,\r\n.k-numerictextbox .k-state-hover .k-link span.k-i-arrow-n {\r\n  background-position: -16px -3px;\r\n}\r\n.k-state-selected .k-i-arrow-s {\r\n  background-position: -16px -32px;\r\n}\r\n.k-link:not(.k-state-disabled):hover > .k-state-selected .k-i-arrow-s,\r\n.k-state-hover > .k-state-selected .k-i-arrow-s,\r\n.k-state-hover > * > .k-state-selected .k-i-arrow-s,\r\n.k-button:not(.k-state-disabled):hover .k-state-selected .k-i-arrow-s,\r\n.k-textbox:hover .k-state-selected .k-i-arrow-s,\r\n.k-button:active .k-state-selected .k-i-arrow-s {\r\n  background-position: -16px -32px;\r\n}\r\n.k-numerictextbox .k-link.k-state-selected span.k-i-arrow-s,\r\n.k-numerictextbox .k-state-hover .k-link span.k-i-arrow-s {\r\n  background-position: -16px -35px;\r\n}\r\n.k-grid-header th > .k-link:hover span.k-i-arrow-n {\r\n  background-position: 0px 0px;\r\n}\r\n.k-grid-header th > .k-link:hover span.k-i-arrow-s {\r\n  background-position: 0px -32px;\r\n}\r\n.k-group-indicator .k-link:hover span.k-si-arrow-n {\r\n  background-position: 0 -129px;\r\n}\r\n.k-group-indicator .k-link:hover span.k-si-arrow-s {\r\n  background-position: 0 -159px;\r\n}\r\n.k-group-indicator .k-button:hover span.k-group-delete {\r\n  background-position: -32px -16px;\r\n}\r\n.k-scheduler .k-scheduler-toolbar .k-nav-current .k-link .k-i-calendar {\r\n  background-position: -32px -176px;\r\n}\r\n.k-i-arrow-n {\r\n  background-position: 0px 0px;\r\n}\r\n.k-link:not(.k-state-disabled):hover > .k-i-arrow-n,\r\n.k-state-hover > .k-i-arrow-n,\r\n.k-state-hover > * > .k-i-arrow-n,\r\n.k-button:not(.k-state-disabled):hover .k-i-arrow-n,\r\n.k-textbox:hover .k-i-arrow-n,\r\n.k-button:active .k-i-arrow-n {\r\n  background-position: 0px 0px;\r\n}\r\n.k-i-arrow-e {\r\n  background-position: 0px -16px;\r\n}\r\n.k-link:not(.k-state-disabled):hover > .k-i-arrow-e,\r\n.k-state-hover > .k-i-arrow-e,\r\n.k-state-hover > * > .k-i-arrow-e,\r\n.k-button:not(.k-state-disabled):hover .k-i-arrow-e,\r\n.k-textbox:hover .k-i-arrow-e,\r\n.k-button:active .k-i-arrow-e {\r\n  background-position: 0px -16px;\r\n}\r\n.k-rtl .k-i-arrow-w {\r\n  background-position: 0px -16px;\r\n}\r\n.k-rtl .k-link:not(.k-state-disabled):hover > .k-i-arrow-w,\r\n.k-rtl .k-state-hover > .k-i-arrow-w,\r\n.k-rtl .k-state-hover > * > .k-i-arrow-w,\r\n.k-rtl .k-button:not(.k-state-disabled):hover .k-i-arrow-w,\r\n.k-rtl .k-textbox:hover .k-i-arrow-w,\r\n.k-rtl .k-button:active .k-i-arrow-w {\r\n  background-position: -16px -16px;\r\n  background-position: 0px -16px;\r\n}\r\n.k-i-arrow-s {\r\n  background-position: 0px -32px;\r\n}\r\n.k-link:not(.k-state-disabled):hover > .k-i-arrow-s,\r\n.k-state-hover > .k-i-arrow-s,\r\n.k-state-hover > * > .k-i-arrow-s,\r\n.k-button:not(.k-state-disabled):hover .k-i-arrow-s,\r\n.k-textbox:hover .k-i-arrow-s,\r\n.k-button:active .k-i-arrow-s {\r\n  background-position: 0px -32px;\r\n}\r\n.k-i-arrow-w {\r\n  background-position: 0px -48px;\r\n}\r\n.k-link:not(.k-state-disabled):hover > .k-i-arrow-w,\r\n.k-state-hover > .k-i-arrow-w,\r\n.k-state-hover > * > .k-i-arrow-w,\r\n.k-button:not(.k-state-disabled):hover .k-i-arrow-w,\r\n.k-textbox:hover .k-i-arrow-w,\r\n.k-button:active .k-i-arrow-w {\r\n  background-position: 0px -48px;\r\n}\r\n.k-rtl .k-i-arrow-e {\r\n  background-position: 0px -48px;\r\n}\r\n.k-rtl .k-link:not(.k-state-disabled):hover > .k-i-arrow-e,\r\n.k-rtl .k-state-hover > .k-i-arrow-e,\r\n.k-rtl .k-state-hover > * > .k-i-arrow-e,\r\n.k-rtl .k-button:not(.k-state-disabled):hover .k-i-arrow-e,\r\n.k-rtl .k-textbox:hover .k-i-arrow-e,\r\n.k-rtl .k-button:active .k-i-arrow-e {\r\n  background-position: -16px -48px;\r\n  background-position: 0px -48px;\r\n}\r\n.k-i-seek-n {\r\n  background-position: 0px -64px;\r\n}\r\n.k-link:not(.k-state-disabled):hover > .k-i-seek-n,\r\n.k-state-hover > .k-i-seek-n,\r\n.k-state-hover > * > .k-i-seek-n,\r\n.k-button:not(.k-state-disabled):hover .k-i-seek-n,\r\n.k-textbox:hover .k-i-seek-n,\r\n.k-button:active .k-i-seek-n {\r\n  background-position: 0px -64px;\r\n}\r\n.k-i-seek-e {\r\n  background-position: 0px -80px;\r\n}\r\n.k-link:not(.k-state-disabled):hover > .k-i-seek-e,\r\n.k-state-hover > .k-i-seek-e,\r\n.k-state-hover > * > .k-i-seek-e,\r\n.k-button:not(.k-state-disabled):hover .k-i-seek-e,\r\n.k-textbox:hover .k-i-seek-e,\r\n.k-button:active .k-i-seek-e {\r\n  background-position: 0px -80px;\r\n}\r\n.k-rtl .k-i-seek-w {\r\n  background-position: 0px -80px;\r\n}\r\n.k-rtl .k-link:not(.k-state-disabled):hover > .k-i-seek-w,\r\n.k-rtl .k-state-hover > .k-i-seek-w,\r\n.k-rtl .k-state-hover > * > .k-i-seek-w,\r\n.k-rtl .k-button:not(.k-state-disabled):hover .k-i-seek-w,\r\n.k-rtl .k-textbox:hover .k-i-seek-w,\r\n.k-rtl .k-button:active .k-i-seek-w {\r\n  background-position: -16px -80px;\r\n  background-position: 0px -80px;\r\n}\r\n.k-i-seek-s {\r\n  background-position: 0px -96px;\r\n}\r\n.k-link:not(.k-state-disabled):hover > .k-i-seek-s,\r\n.k-state-hover > .k-i-seek-s,\r\n.k-state-hover > * > .k-i-seek-s,\r\n.k-button:not(.k-state-disabled):hover .k-i-seek-s,\r\n.k-textbox:hover .k-i-seek-s,\r\n.k-button:active .k-i-seek-s {\r\n  background-position: 0px -96px;\r\n}\r\n.k-i-seek-w {\r\n  background-position: 0px -112px;\r\n}\r\n.k-link:not(.k-state-disabled):hover > .k-i-seek-w,\r\n.k-state-hover > .k-i-seek-w,\r\n.k-state-hover > * > .k-i-seek-w,\r\n.k-button:not(.k-state-disabled):hover .k-i-seek-w,\r\n.k-textbox:hover .k-i-seek-w,\r\n.k-button:active .k-i-seek-w {\r\n  background-position: 0px -112px;\r\n}\r\n.k-rtl .k-i-seek-e {\r\n  background-position: 0px -112px;\r\n}\r\n.k-rtl .k-link:not(.k-state-disabled):hover > .k-i-seek-e,\r\n.k-rtl .k-state-hover > .k-i-seek-e,\r\n.k-rtl .k-state-hover > * > .k-i-seek-e,\r\n.k-rtl .k-button:not(.k-state-disabled):hover .k-i-seek-e,\r\n.k-rtl .k-textbox:hover .k-i-seek-e,\r\n.k-rtl .k-button:active .k-i-seek-e {\r\n  background-position: -16px -112px;\r\n  background-position: 0px -112px;\r\n}\r\n.k-si-arrow-n {\r\n  background-position: 0 -129px;\r\n}\r\n.k-link:not(.k-state-disabled):hover > .k-si-arrow-n,\r\n.k-state-hover > .k-si-arrow-n,\r\n.k-state-hover > * > .k-si-arrow-n,\r\n.k-button:not(.k-state-disabled):hover .k-si-arrow-n,\r\n.k-textbox:hover .k-si-arrow-n,\r\n.k-button:active .k-si-arrow-n {\r\n  background-position: -16px -129px;\r\n}\r\n.k-si-arrow-e {\r\n  background-position: 0px -144px;\r\n}\r\n.k-link:not(.k-state-disabled):hover > .k-si-arrow-e,\r\n.k-state-hover > .k-si-arrow-e,\r\n.k-state-hover > * > .k-si-arrow-e,\r\n.k-button:not(.k-state-disabled):hover .k-si-arrow-e,\r\n.k-textbox:hover .k-si-arrow-e,\r\n.k-button:active .k-si-arrow-e {\r\n  background-position: 0px -144px;\r\n}\r\n.k-si-arrow-s {\r\n  background-position: 0 -159px;\r\n}\r\n.k-link:not(.k-state-disabled):hover > .k-si-arrow-s,\r\n.k-state-hover > .k-si-arrow-s,\r\n.k-state-hover > * > .k-si-arrow-s,\r\n.k-button:not(.k-state-disabled):hover .k-si-arrow-s,\r\n.k-textbox:hover .k-si-arrow-s,\r\n.k-button:active .k-si-arrow-s {\r\n  background-position: -16px -159px;\r\n}\r\n.k-si-arrow-w {\r\n  background-position: 0px -176px;\r\n}\r\n.k-link:not(.k-state-disabled):hover > .k-si-arrow-w,\r\n.k-state-hover > .k-si-arrow-w,\r\n.k-state-hover > * > .k-si-arrow-w,\r\n.k-button:not(.k-state-disabled):hover .k-si-arrow-w,\r\n.k-textbox:hover .k-si-arrow-w,\r\n.k-button:active .k-si-arrow-w {\r\n  background-position: 0px -176px;\r\n}\r\n.k-i-arrowhead-n {\r\n  background-position: 0px -256px;\r\n}\r\n.k-link:not(.k-state-disabled):hover > .k-i-arrowhead-n,\r\n.k-state-hover > .k-i-arrowhead-n,\r\n.k-state-hover > * > .k-i-arrowhead-n,\r\n.k-button:not(.k-state-disabled):hover .k-i-arrowhead-n,\r\n.k-textbox:hover .k-i-arrowhead-n,\r\n.k-button:active .k-i-arrowhead-n {\r\n  background-position: 0px -256px;\r\n}\r\n.k-i-arrowhead-e {\r\n  background-position: 0px -272px;\r\n}\r\n.k-link:not(.k-state-disabled):hover > .k-i-arrowhead-e,\r\n.k-state-hover > .k-i-arrowhead-e,\r\n.k-state-hover > * > .k-i-arrowhead-e,\r\n.k-button:not(.k-state-disabled):hover .k-i-arrowhead-e,\r\n.k-textbox:hover .k-i-arrowhead-e,\r\n.k-button:active .k-i-arrowhead-e {\r\n  background-position: 0px -272px;\r\n}\r\n.k-i-arrowhead-s {\r\n  background-position: 0px -288px;\r\n}\r\n.k-link:not(.k-state-disabled):hover > .k-i-arrowhead-s,\r\n.k-state-hover > .k-i-arrowhead-s,\r\n.k-state-hover > * > .k-i-arrowhead-s,\r\n.k-button:not(.k-state-disabled):hover .k-i-arrowhead-s,\r\n.k-textbox:hover .k-i-arrowhead-s,\r\n.k-button:active .k-i-arrowhead-s {\r\n  background-position: 0px -288px;\r\n}\r\n.k-i-arrowhead-w {\r\n  background-position: 0px -304px;\r\n}\r\n.k-link:not(.k-state-disabled):hover > .k-i-arrowhead-w,\r\n.k-state-hover > .k-i-arrowhead-w,\r\n.k-state-hover > * > .k-i-arrowhead-w,\r\n.k-button:not(.k-state-disabled):hover .k-i-arrowhead-w,\r\n.k-textbox:hover .k-i-arrowhead-w,\r\n.k-button:active .k-i-arrowhead-w {\r\n  background-position: 0px -304px;\r\n}\r\n.k-i-expand,\r\n.k-plus,\r\n.k-plus-disabled {\r\n  background-position: 0px -192px;\r\n}\r\n.k-link:not(.k-state-disabled):hover > .k-i-expand,\r\n.k-link:not(.k-state-disabled):hover > .k-plus,\r\n.k-link:not(.k-state-disabled):hover > .k-plus-disabled,\r\n.k-state-hover > .k-i-expand,\r\n.k-state-hover > .k-plus,\r\n.k-state-hover > .k-plus-disabled,\r\n.k-state-hover > * > .k-i-expand,\r\n.k-state-hover > * > .k-plus,\r\n.k-state-hover > * > .k-plus-disabled,\r\n.k-button:not(.k-state-disabled):hover .k-i-expand,\r\n.k-button:not(.k-state-disabled):hover .k-plus,\r\n.k-button:not(.k-state-disabled):hover .k-plus-disabled,\r\n.k-textbox:hover .k-i-expand,\r\n.k-textbox:hover .k-plus,\r\n.k-textbox:hover .k-plus-disabled,\r\n.k-button:active .k-i-expand,\r\n.k-button:active .k-plus,\r\n.k-button:active .k-plus-disabled {\r\n  background-position: 0px -192px;\r\n}\r\n.k-i-expand-w,\r\n.k-rtl .k-i-expand,\r\n.k-rtl .k-plus,\r\n.k-rtl .k-plus-disabled {\r\n  background-position: 0px -208px;\r\n}\r\n.k-link:not(.k-state-disabled):hover > .k-i-expand-w,\r\n.k-link:not(.k-state-disabled):hover > .k-rtl .k-i-expand,\r\n.k-link:not(.k-state-disabled):hover > .k-rtl .k-plus,\r\n.k-link:not(.k-state-disabled):hover > .k-rtl .k-plus-disabled,\r\n.k-state-hover > .k-i-expand-w,\r\n.k-state-hover > .k-rtl .k-i-expand,\r\n.k-state-hover > .k-rtl .k-plus,\r\n.k-state-hover > .k-rtl .k-plus-disabled,\r\n.k-state-hover > * > .k-i-expand-w,\r\n.k-state-hover > * > .k-rtl .k-i-expand,\r\n.k-state-hover > * > .k-rtl .k-plus,\r\n.k-state-hover > * > .k-rtl .k-plus-disabled,\r\n.k-button:not(.k-state-disabled):hover .k-i-expand-w,\r\n.k-button:not(.k-state-disabled):hover .k-rtl .k-i-expand,\r\n.k-button:not(.k-state-disabled):hover .k-rtl .k-plus,\r\n.k-button:not(.k-state-disabled):hover .k-rtl .k-plus-disabled,\r\n.k-textbox:hover .k-i-expand-w,\r\n.k-textbox:hover .k-rtl .k-i-expand,\r\n.k-textbox:hover .k-rtl .k-plus,\r\n.k-textbox:hover .k-rtl .k-plus-disabled,\r\n.k-button:active .k-i-expand-w,\r\n.k-button:active .k-rtl .k-i-expand,\r\n.k-button:active .k-rtl .k-plus,\r\n.k-button:active .k-rtl .k-plus-disabled {\r\n  background-position: 0px -208px;\r\n}\r\n.k-i-collapse,\r\n.k-minus,\r\n.k-minus-disabled {\r\n  background-position: 0px -224px;\r\n}\r\n.k-link:not(.k-state-disabled):hover > .k-i-collapse,\r\n.k-link:not(.k-state-disabled):hover > .k-minus,\r\n.k-link:not(.k-state-disabled):hover > .k-minus-disabled,\r\n.k-state-hover > .k-i-collapse,\r\n.k-state-hover > .k-minus,\r\n.k-state-hover > .k-minus-disabled,\r\n.k-state-hover > * > .k-i-collapse,\r\n.k-state-hover > * > .k-minus,\r\n.k-state-hover > * > .k-minus-disabled,\r\n.k-button:not(.k-state-disabled):hover .k-i-collapse,\r\n.k-button:not(.k-state-disabled):hover .k-minus,\r\n.k-button:not(.k-state-disabled):hover .k-minus-disabled,\r\n.k-textbox:hover .k-i-collapse,\r\n.k-textbox:hover .k-minus,\r\n.k-textbox:hover .k-minus-disabled,\r\n.k-button:active .k-i-collapse,\r\n.k-button:active .k-minus,\r\n.k-button:active .k-minus-disabled {\r\n  background-position: 0px -224px;\r\n}\r\n.k-i-collapse-w,\r\n.k-rtl .k-i-collapse,\r\n.k-rtl .k-minus,\r\n.k-rtl .k-minus-disabled {\r\n  background-position: 0px -240px;\r\n}\r\n.k-link:not(.k-state-disabled):hover > .k-i-collapse-w,\r\n.k-link:not(.k-state-disabled):hover > .k-rtl .k-i-collapse,\r\n.k-link:not(.k-state-disabled):hover > .k-rtl .k-minus,\r\n.k-link:not(.k-state-disabled):hover > .k-rtl .k-minus-disabled,\r\n.k-state-hover > .k-i-collapse-w,\r\n.k-state-hover > .k-rtl .k-i-collapse,\r\n.k-state-hover > .k-rtl .k-minus,\r\n.k-state-hover > .k-rtl .k-minus-disabled,\r\n.k-state-hover > * > .k-i-collapse-w,\r\n.k-state-hover > * > .k-rtl .k-i-collapse,\r\n.k-state-hover > * > .k-rtl .k-minus,\r\n.k-state-hover > * > .k-rtl .k-minus-disabled,\r\n.k-button:not(.k-state-disabled):hover .k-i-collapse-w,\r\n.k-button:not(.k-state-disabled):hover .k-rtl .k-i-collapse,\r\n.k-button:not(.k-state-disabled):hover .k-rtl .k-minus,\r\n.k-button:not(.k-state-disabled):hover .k-rtl .k-minus-disabled,\r\n.k-textbox:hover .k-i-collapse-w,\r\n.k-textbox:hover .k-rtl .k-i-collapse,\r\n.k-textbox:hover .k-rtl .k-minus,\r\n.k-textbox:hover .k-rtl .k-minus-disabled,\r\n.k-button:active .k-i-collapse-w,\r\n.k-button:active .k-rtl .k-i-collapse,\r\n.k-button:active .k-rtl .k-minus,\r\n.k-button:active .k-rtl .k-minus-disabled {\r\n  background-position: 0px -240px;\r\n}\r\n.k-i-pencil,\r\n.k-edit {\r\n  background-position: -32px 0px;\r\n}\r\n.k-link:not(.k-state-disabled):hover > .k-i-pencil,\r\n.k-link:not(.k-state-disabled):hover > .k-edit,\r\n.k-state-hover > .k-i-pencil,\r\n.k-state-hover > .k-edit,\r\n.k-state-hover > * > .k-i-pencil,\r\n.k-state-hover > * > .k-edit,\r\n.k-button:not(.k-state-disabled):hover .k-i-pencil,\r\n.k-button:not(.k-state-disabled):hover .k-edit,\r\n.k-textbox:hover .k-i-pencil,\r\n.k-textbox:hover .k-edit,\r\n.k-button:active .k-i-pencil,\r\n.k-button:active .k-edit {\r\n  background-position: -32px 0px;\r\n}\r\n.k-i-close,\r\n.k-delete,\r\n.k-group-delete {\r\n  background-position: -32px -16px;\r\n}\r\n.k-link:not(.k-state-disabled):hover > .k-i-close,\r\n.k-link:not(.k-state-disabled):hover > .k-delete,\r\n.k-link:not(.k-state-disabled):hover > .k-group-delete,\r\n.k-state-hover > .k-i-close,\r\n.k-state-hover > .k-delete,\r\n.k-state-hover > .k-group-delete,\r\n.k-state-hover > * > .k-i-close,\r\n.k-state-hover > * > .k-delete,\r\n.k-state-hover > * > .k-group-delete,\r\n.k-button:not(.k-state-disabled):hover .k-i-close,\r\n.k-button:not(.k-state-disabled):hover .k-delete,\r\n.k-button:not(.k-state-disabled):hover .k-group-delete,\r\n.k-textbox:hover .k-i-close,\r\n.k-textbox:hover .k-delete,\r\n.k-textbox:hover .k-group-delete,\r\n.k-button:active .k-i-close,\r\n.k-button:active .k-delete,\r\n.k-button:active .k-group-delete {\r\n  background-position: -32px -16px;\r\n}\r\n.k-si-close {\r\n  background-position: -160px -80px;\r\n}\r\n.k-link:not(.k-state-disabled):hover > .k-si-close,\r\n.k-state-hover > .k-si-close,\r\n.k-state-hover > * > .k-si-close,\r\n.k-button:not(.k-state-disabled):hover .k-si-close,\r\n.k-textbox:hover .k-si-close,\r\n.k-button:active .k-si-close {\r\n  background-position: -160px -80px;\r\n}\r\n.k-multiselect .k-delete {\r\n  background-position: -160px -80px;\r\n}\r\n.k-multiselect .k-state-hover .k-delete {\r\n  background-position: -176px -80px;\r\n}\r\n.k-i-tick,\r\n.k-insert,\r\n.k-update {\r\n  background-position: -32px -32px;\r\n}\r\n.k-link:not(.k-state-disabled):hover > .k-i-tick,\r\n.k-link:not(.k-state-disabled):hover > .k-insert,\r\n.k-link:not(.k-state-disabled):hover > .k-update,\r\n.k-state-hover > .k-i-tick,\r\n.k-state-hover > .k-insert,\r\n.k-state-hover > .k-update,\r\n.k-state-hover > * > .k-i-tick,\r\n.k-state-hover > * > .k-insert,\r\n.k-state-hover > * > .k-update,\r\n.k-button:not(.k-state-disabled):hover .k-i-tick,\r\n.k-button:not(.k-state-disabled):hover .k-insert,\r\n.k-button:not(.k-state-disabled):hover .k-update,\r\n.k-textbox:hover .k-i-tick,\r\n.k-textbox:hover .k-insert,\r\n.k-textbox:hover .k-update,\r\n.k-button:active .k-i-tick,\r\n.k-button:active .k-insert,\r\n.k-button:active .k-update {\r\n  background-position: -32px -32px;\r\n}\r\n.k-check:checked,\r\n.k-mobile-list .k-edit-field [type=checkbox],\r\n.k-mobile-list .k-edit-field [type=radio] {\r\n  background-position: -32px -32px;\r\n}\r\n.k-link:not(.k-state-disabled):hover > .k-check:checked,\r\n.k-link:not(.k-state-disabled):hover > .k-mobile-list .k-edit-field [type=checkbox],\r\n.k-link:not(.k-state-disabled):hover > .k-mobile-list .k-edit-field [type=radio],\r\n.k-state-hover > .k-check:checked,\r\n.k-state-hover > .k-mobile-list .k-edit-field [type=checkbox],\r\n.k-state-hover > .k-mobile-list .k-edit-field [type=radio],\r\n.k-state-hover > * > .k-check:checked,\r\n.k-state-hover > * > .k-mobile-list .k-edit-field [type=checkbox],\r\n.k-state-hover > * > .k-mobile-list .k-edit-field [type=radio],\r\n.k-button:not(.k-state-disabled):hover .k-check:checked,\r\n.k-button:not(.k-state-disabled):hover .k-mobile-list .k-edit-field [type=checkbox],\r\n.k-button:not(.k-state-disabled):hover .k-mobile-list .k-edit-field [type=radio],\r\n.k-textbox:hover .k-check:checked,\r\n.k-textbox:hover .k-mobile-list .k-edit-field [type=checkbox],\r\n.k-textbox:hover .k-mobile-list .k-edit-field [type=radio],\r\n.k-button:active .k-check:checked,\r\n.k-button:active .k-mobile-list .k-edit-field [type=checkbox],\r\n.k-button:active .k-mobile-list .k-edit-field [type=radio] {\r\n  background-position: -32px -32px;\r\n}\r\n.k-i-cancel,\r\n.k-cancel,\r\n.k-denied {\r\n  background-position: -32px -48px;\r\n}\r\n.k-link:not(.k-state-disabled):hover > .k-i-cancel,\r\n.k-link:not(.k-state-disabled):hover > .k-cancel,\r\n.k-link:not(.k-state-disabled):hover > .k-denied,\r\n.k-state-hover > .k-i-cancel,\r\n.k-state-hover > .k-cancel,\r\n.k-state-hover > .k-denied,\r\n.k-state-hover > * > .k-i-cancel,\r\n.k-state-hover > * > .k-cancel,\r\n.k-state-hover > * > .k-denied,\r\n.k-button:not(.k-state-disabled):hover .k-i-cancel,\r\n.k-button:not(.k-state-disabled):hover .k-cancel,\r\n.k-button:not(.k-state-disabled):hover .k-denied,\r\n.k-textbox:hover .k-i-cancel,\r\n.k-textbox:hover .k-cancel,\r\n.k-textbox:hover .k-denied,\r\n.k-button:active .k-i-cancel,\r\n.k-button:active .k-cancel,\r\n.k-button:active .k-denied {\r\n  background-position: -32px -48px;\r\n}\r\n.k-i-plus,\r\n.k-add {\r\n  background-position: -32px -64px;\r\n}\r\n.k-link:not(.k-state-disabled):hover > .k-i-plus,\r\n.k-link:not(.k-state-disabled):hover > .k-add,\r\n.k-state-hover > .k-i-plus,\r\n.k-state-hover > .k-add,\r\n.k-state-hover > * > .k-i-plus,\r\n.k-state-hover > * > .k-add,\r\n.k-button:not(.k-state-disabled):hover .k-i-plus,\r\n.k-button:not(.k-state-disabled):hover .k-add,\r\n.k-textbox:hover .k-i-plus,\r\n.k-textbox:hover .k-add,\r\n.k-button:active .k-i-plus,\r\n.k-button:active .k-add {\r\n  background-position: -32px -64px;\r\n}\r\n.k-i-funnel,\r\n.k-filter {\r\n  background-position: -32px -80px;\r\n}\r\n.k-link:not(.k-state-disabled):hover > .k-i-funnel,\r\n.k-link:not(.k-state-disabled):hover > .k-filter,\r\n.k-state-hover > .k-i-funnel,\r\n.k-state-hover > .k-filter,\r\n.k-state-hover > * > .k-i-funnel,\r\n.k-state-hover > * > .k-filter,\r\n.k-button:not(.k-state-disabled):hover .k-i-funnel,\r\n.k-button:not(.k-state-disabled):hover .k-filter,\r\n.k-textbox:hover .k-i-funnel,\r\n.k-textbox:hover .k-filter,\r\n.k-button:active .k-i-funnel,\r\n.k-button:active .k-filter {\r\n  background-position: -32px -80px;\r\n}\r\n.k-i-funnel-clear,\r\n.k-clear-filter {\r\n  background-position: -32px -96px;\r\n}\r\n.k-link:not(.k-state-disabled):hover > .k-i-funnel-clear,\r\n.k-link:not(.k-state-disabled):hover > .k-clear-filter,\r\n.k-state-hover > .k-i-funnel-clear,\r\n.k-state-hover > .k-clear-filter,\r\n.k-state-hover > * > .k-i-funnel-clear,\r\n.k-state-hover > * > .k-clear-filter,\r\n.k-button:not(.k-state-disabled):hover .k-i-funnel-clear,\r\n.k-button:not(.k-state-disabled):hover .k-clear-filter,\r\n.k-textbox:hover .k-i-funnel-clear,\r\n.k-textbox:hover .k-clear-filter,\r\n.k-button:active .k-i-funnel-clear,\r\n.k-button:active .k-clear-filter {\r\n  background-position: -32px -96px;\r\n}\r\n.k-i-lock {\r\n  background-position: -64px 0px;\r\n}\r\n.k-link:not(.k-state-disabled):hover > .k-i-lock,\r\n.k-state-hover > .k-i-lock,\r\n.k-state-hover > * > .k-i-lock,\r\n.k-button:not(.k-state-disabled):hover .k-i-lock,\r\n.k-textbox:hover .k-i-lock,\r\n.k-button:active .k-i-lock {\r\n  background-position: -64px 0px;\r\n}\r\n.k-i-unlock {\r\n  background-position: -64px -16px;\r\n}\r\n.k-link:not(.k-state-disabled):hover > .k-i-unlock,\r\n.k-state-hover > .k-i-unlock,\r\n.k-state-hover > * > .k-i-unlock,\r\n.k-button:not(.k-state-disabled):hover .k-i-unlock,\r\n.k-textbox:hover .k-i-unlock,\r\n.k-button:active .k-i-unlock {\r\n  background-position: -64px -16px;\r\n}\r\n.k-i-refresh {\r\n  background-position: -32px -112px;\r\n}\r\n.k-link:not(.k-state-disabled):hover > .k-i-refresh,\r\n.k-state-hover > .k-i-refresh,\r\n.k-state-hover > * > .k-i-refresh,\r\n.k-button:not(.k-state-disabled):hover .k-i-refresh,\r\n.k-textbox:hover .k-i-refresh,\r\n.k-button:active .k-i-refresh {\r\n  background-position: -32px -112px;\r\n}\r\n.k-i-exception {\r\n  background-position: -160px -304px;\r\n}\r\n.k-link:not(.k-state-disabled):hover > .k-i-exception,\r\n.k-state-hover > .k-i-exception,\r\n.k-state-hover > * > .k-i-exception,\r\n.k-button:not(.k-state-disabled):hover .k-i-exception,\r\n.k-textbox:hover .k-i-exception,\r\n.k-button:active .k-i-exception {\r\n  background-position: -160px -304px;\r\n}\r\n.k-i-restore {\r\n  background-position: -32px -128px;\r\n}\r\n.k-link:not(.k-state-disabled):hover > .k-i-restore,\r\n.k-state-hover > .k-i-restore,\r\n.k-state-hover > * > .k-i-restore,\r\n.k-button:not(.k-state-disabled):hover .k-i-restore,\r\n.k-textbox:hover .k-i-restore,\r\n.k-button:active .k-i-restore {\r\n  background-position: -32px -128px;\r\n}\r\n.k-i-maximize {\r\n  background-position: -32px -144px;\r\n}\r\n.k-link:not(.k-state-disabled):hover > .k-i-maximize,\r\n.k-state-hover > .k-i-maximize,\r\n.k-state-hover > * > .k-i-maximize,\r\n.k-button:not(.k-state-disabled):hover .k-i-maximize,\r\n.k-textbox:hover .k-i-maximize,\r\n.k-button:active .k-i-maximize {\r\n  background-position: -32px -144px;\r\n}\r\n.k-i-minimize {\r\n  background-position: -64px -288px;\r\n}\r\n.k-link:not(.k-state-disabled):hover > .k-i-minimize,\r\n.k-state-hover > .k-i-minimize,\r\n.k-state-hover > * > .k-i-minimize,\r\n.k-button:not(.k-state-disabled):hover .k-i-minimize,\r\n.k-textbox:hover .k-i-minimize,\r\n.k-button:active .k-i-minimize {\r\n  background-position: -64px -288px;\r\n}\r\n.k-i-pin {\r\n  background-position: -160px -256px;\r\n}\r\n.k-link:not(.k-state-disabled):hover > .k-i-pin,\r\n.k-state-hover > .k-i-pin,\r\n.k-state-hover > * > .k-i-pin,\r\n.k-button:not(.k-state-disabled):hover .k-i-pin,\r\n.k-textbox:hover .k-i-pin,\r\n.k-button:active .k-i-pin {\r\n  background-position: -160px -256px;\r\n}\r\n.k-i-unpin {\r\n  background-position: -160px -272px;\r\n}\r\n.k-link:not(.k-state-disabled):hover > .k-i-unpin,\r\n.k-state-hover > .k-i-unpin,\r\n.k-state-hover > * > .k-i-unpin,\r\n.k-button:not(.k-state-disabled):hover .k-i-unpin,\r\n.k-textbox:hover .k-i-unpin,\r\n.k-button:active .k-i-unpin {\r\n  background-position: -160px -272px;\r\n}\r\n.k-resize-se {\r\n  background-position: -32px -160px;\r\n}\r\n.k-link:not(.k-state-disabled):hover > .k-resize-se,\r\n.k-state-hover > .k-resize-se,\r\n.k-state-hover > * > .k-resize-se,\r\n.k-button:not(.k-state-disabled):hover .k-resize-se,\r\n.k-textbox:hover .k-resize-se,\r\n.k-button:active .k-resize-se {\r\n  background-position: -32px -160px;\r\n}\r\n.k-i-calendar {\r\n  background-position: -32px -176px;\r\n}\r\n.k-link:not(.k-state-disabled):hover > .k-i-calendar,\r\n.k-state-hover > .k-i-calendar,\r\n.k-state-hover > * > .k-i-calendar,\r\n.k-button:not(.k-state-disabled):hover .k-i-calendar,\r\n.k-textbox:hover .k-i-calendar,\r\n.k-button:active .k-i-calendar {\r\n  background-position: -32px -176px;\r\n}\r\n.k-i-clock {\r\n  background-position: -32px -192px;\r\n}\r\n.k-link:not(.k-state-disabled):hover > .k-i-clock,\r\n.k-state-hover > .k-i-clock,\r\n.k-state-hover > * > .k-i-clock,\r\n.k-button:not(.k-state-disabled):hover .k-i-clock,\r\n.k-textbox:hover .k-i-clock,\r\n.k-button:active .k-i-clock {\r\n  background-position: -32px -192px;\r\n}\r\n.k-si-plus {\r\n  background-position: -32px -208px;\r\n}\r\n.k-link:not(.k-state-disabled):hover > .k-si-plus,\r\n.k-state-hover > .k-si-plus,\r\n.k-state-hover > * > .k-si-plus,\r\n.k-button:not(.k-state-disabled):hover .k-si-plus,\r\n.k-textbox:hover .k-si-plus,\r\n.k-button:active .k-si-plus {\r\n  background-position: -32px -208px;\r\n}\r\n.k-si-minus {\r\n  background-position: -32px -224px;\r\n}\r\n.k-link:not(.k-state-disabled):hover > .k-si-minus,\r\n.k-state-hover > .k-si-minus,\r\n.k-state-hover > * > .k-si-minus,\r\n.k-button:not(.k-state-disabled):hover .k-si-minus,\r\n.k-textbox:hover .k-si-minus,\r\n.k-button:active .k-si-minus {\r\n  background-position: -32px -224px;\r\n}\r\n.k-i-search {\r\n  background-position: -32px -240px;\r\n}\r\n.k-link:not(.k-state-disabled):hover > .k-i-search,\r\n.k-state-hover > .k-i-search,\r\n.k-state-hover > * > .k-i-search,\r\n.k-button:not(.k-state-disabled):hover .k-i-search,\r\n.k-textbox:hover .k-i-search,\r\n.k-button:active .k-i-search {\r\n  background-position: -32px -240px;\r\n}\r\n.k-i-custom {\r\n  background-position: -115px -113px;\r\n}\r\n.k-link:not(.k-state-disabled):hover > .k-i-custom,\r\n.k-state-hover > .k-i-custom,\r\n.k-state-hover > * > .k-i-custom,\r\n.k-button:not(.k-state-disabled):hover .k-i-custom,\r\n.k-textbox:hover .k-i-custom,\r\n.k-button:active .k-i-custom {\r\n  background-position: -141px -113px;\r\n}\r\n.k-editor .k-i-custom {\r\n  background-position: -111px -109px;\r\n}\r\n.k-viewHtml {\r\n  background-position: -288px -120px;\r\n}\r\n.k-i-insert-n,\r\n.k-insert-top {\r\n  background-position: -160px -32px;\r\n}\r\n.k-link:not(.k-state-disabled):hover > .k-i-insert-n,\r\n.k-link:not(.k-state-disabled):hover > .k-insert-top,\r\n.k-state-hover > .k-i-insert-n,\r\n.k-state-hover > .k-insert-top,\r\n.k-state-hover > * > .k-i-insert-n,\r\n.k-state-hover > * > .k-insert-top,\r\n.k-button:not(.k-state-disabled):hover .k-i-insert-n,\r\n.k-button:not(.k-state-disabled):hover .k-insert-top,\r\n.k-textbox:hover .k-i-insert-n,\r\n.k-textbox:hover .k-insert-top,\r\n.k-button:active .k-i-insert-n,\r\n.k-button:active .k-insert-top {\r\n  background-position: -160px -32px;\r\n}\r\n.k-i-insert-m,\r\n.k-insert-middle {\r\n  background-position: -160px -48px;\r\n}\r\n.k-link:not(.k-state-disabled):hover > .k-i-insert-m,\r\n.k-link:not(.k-state-disabled):hover > .k-insert-middle,\r\n.k-state-hover > .k-i-insert-m,\r\n.k-state-hover > .k-insert-middle,\r\n.k-state-hover > * > .k-i-insert-m,\r\n.k-state-hover > * > .k-insert-middle,\r\n.k-button:not(.k-state-disabled):hover .k-i-insert-m,\r\n.k-button:not(.k-state-disabled):hover .k-insert-middle,\r\n.k-textbox:hover .k-i-insert-m,\r\n.k-textbox:hover .k-insert-middle,\r\n.k-button:active .k-i-insert-m,\r\n.k-button:active .k-insert-middle {\r\n  background-position: -160px -48px;\r\n}\r\n.k-i-insert-s,\r\n.k-insert-bottom {\r\n  background-position: -160px -64px;\r\n}\r\n.k-link:not(.k-state-disabled):hover > .k-i-insert-s,\r\n.k-link:not(.k-state-disabled):hover > .k-insert-bottom,\r\n.k-state-hover > .k-i-insert-s,\r\n.k-state-hover > .k-insert-bottom,\r\n.k-state-hover > * > .k-i-insert-s,\r\n.k-state-hover > * > .k-insert-bottom,\r\n.k-button:not(.k-state-disabled):hover .k-i-insert-s,\r\n.k-button:not(.k-state-disabled):hover .k-insert-bottom,\r\n.k-textbox:hover .k-i-insert-s,\r\n.k-textbox:hover .k-insert-bottom,\r\n.k-button:active .k-i-insert-s,\r\n.k-button:active .k-insert-bottom {\r\n  background-position: -160px -64px;\r\n}\r\n.k-drop-hint {\r\n  background-position: 0 -326px;\r\n}\r\n.k-i-note,\r\n.k-warning {\r\n  background-position: -160px -240px;\r\n}\r\n.k-link:not(.k-state-disabled):hover > .k-i-note,\r\n.k-link:not(.k-state-disabled):hover > .k-warning,\r\n.k-state-hover > .k-i-note,\r\n.k-state-hover > .k-warning,\r\n.k-state-hover > * > .k-i-note,\r\n.k-state-hover > * > .k-warning,\r\n.k-button:not(.k-state-disabled):hover .k-i-note,\r\n.k-button:not(.k-state-disabled):hover .k-warning,\r\n.k-textbox:hover .k-i-note,\r\n.k-textbox:hover .k-warning,\r\n.k-button:active .k-i-note,\r\n.k-button:active .k-warning {\r\n  background-position: -160px -240px;\r\n}\r\n.k-i-sort-asc {\r\n  background-position: -112px -240px;\r\n}\r\n.k-link:not(.k-state-disabled):hover > .k-i-sort-asc,\r\n.k-state-hover > .k-i-sort-asc,\r\n.k-state-hover > * > .k-i-sort-asc,\r\n.k-button:not(.k-state-disabled):hover .k-i-sort-asc,\r\n.k-textbox:hover .k-i-sort-asc,\r\n.k-button:active .k-i-sort-asc {\r\n  background-position: -112px -240px;\r\n}\r\n.k-i-sort-desc {\r\n  background-position: -112px -256px;\r\n}\r\n.k-link:not(.k-state-disabled):hover > .k-i-sort-desc,\r\n.k-state-hover > .k-i-sort-desc,\r\n.k-state-hover > * > .k-i-sort-desc,\r\n.k-button:not(.k-state-disabled):hover .k-i-sort-desc,\r\n.k-textbox:hover .k-i-sort-desc,\r\n.k-button:active .k-i-sort-desc {\r\n  background-position: -112px -256px;\r\n}\r\n.k-i-group {\r\n  background-position: -112px -272px;\r\n}\r\n.k-link:not(.k-state-disabled):hover > .k-i-group,\r\n.k-state-hover > .k-i-group,\r\n.k-state-hover > * > .k-i-group,\r\n.k-button:not(.k-state-disabled):hover .k-i-group,\r\n.k-textbox:hover .k-i-group,\r\n.k-button:active .k-i-group {\r\n  background-position: -112px -272px;\r\n}\r\n.k-i-ungroup {\r\n  background-position: -112px -288px;\r\n}\r\n.k-link:not(.k-state-disabled):hover > .k-i-ungroup,\r\n.k-state-hover > .k-i-ungroup,\r\n.k-state-hover > * > .k-i-ungroup,\r\n.k-button:not(.k-state-disabled):hover .k-i-ungroup,\r\n.k-textbox:hover .k-i-ungroup,\r\n.k-button:active .k-i-ungroup {\r\n  background-position: -112px -288px;\r\n}\r\n.k-i-columns {\r\n  background-position: -112px -304px;\r\n}\r\n.k-link:not(.k-state-disabled):hover > .k-i-columns,\r\n.k-state-hover > .k-i-columns,\r\n.k-state-hover > * > .k-i-columns,\r\n.k-button:not(.k-state-disabled):hover .k-i-columns,\r\n.k-textbox:hover .k-i-columns,\r\n.k-button:active .k-i-columns {\r\n  background-position: -112px -304px;\r\n}\r\n.k-i-hbars {\r\n  background-position: -64px -32px;\r\n}\r\n.k-link:not(.k-state-disabled):hover > .k-i-hbars,\r\n.k-state-hover > .k-i-hbars,\r\n.k-state-hover > * > .k-i-hbars,\r\n.k-button:not(.k-state-disabled):hover .k-i-hbars,\r\n.k-textbox:hover .k-i-hbars,\r\n.k-button:active .k-i-hbars {\r\n  background-position: -64px -32px;\r\n}\r\n.k-i-vbars {\r\n  background-position: -64px -48px;\r\n}\r\n.k-link:not(.k-state-disabled):hover > .k-i-vbars,\r\n.k-state-hover > .k-i-vbars,\r\n.k-state-hover > * > .k-i-vbars,\r\n.k-button:not(.k-state-disabled):hover .k-i-vbars,\r\n.k-textbox:hover .k-i-vbars,\r\n.k-button:active .k-i-vbars {\r\n  background-position: -64px -48px;\r\n}\r\n.k-i-sum {\r\n  background-position: -64px -64px;\r\n}\r\n.k-link:not(.k-state-disabled):hover > .k-i-sum,\r\n.k-state-hover > .k-i-sum,\r\n.k-state-hover > * > .k-i-sum,\r\n.k-button:not(.k-state-disabled):hover .k-i-sum,\r\n.k-textbox:hover .k-i-sum,\r\n.k-button:active .k-i-sum {\r\n  background-position: -64px -64px;\r\n}\r\n.k-i-pdf {\r\n  background-position: -64px -80px;\r\n}\r\n.k-link:not(.k-state-disabled):hover > .k-i-pdf,\r\n.k-state-hover > .k-i-pdf,\r\n.k-state-hover > * > .k-i-pdf,\r\n.k-button:not(.k-state-disabled):hover .k-i-pdf,\r\n.k-textbox:hover .k-i-pdf,\r\n.k-button:active .k-i-pdf {\r\n  background-position: -64px -80px;\r\n}\r\n.k-i-excel {\r\n  background-position: -64px -96px;\r\n}\r\n.k-link:not(.k-state-disabled):hover > .k-i-excel,\r\n.k-state-hover > .k-i-excel,\r\n.k-state-hover > * > .k-i-excel,\r\n.k-button:not(.k-state-disabled):hover .k-i-excel,\r\n.k-textbox:hover .k-i-excel,\r\n.k-button:active .k-i-excel {\r\n  background-position: -64px -96px;\r\n}\r\n.k-i-rotatecw {\r\n  background-position: -64px -112px;\r\n}\r\n.k-link:not(.k-state-disabled):hover > .k-i-rotatecw,\r\n.k-state-hover > .k-i-rotatecw,\r\n.k-state-hover > * > .k-i-rotatecw,\r\n.k-button:not(.k-state-disabled):hover .k-i-rotatecw,\r\n.k-textbox:hover .k-i-rotatecw,\r\n.k-button:active .k-i-rotatecw {\r\n  background-position: -64px -112px;\r\n}\r\n.k-i-rotateccw {\r\n  background-position: -64px -128px;\r\n}\r\n.k-link:not(.k-state-disabled):hover > .k-i-rotateccw,\r\n.k-state-hover > .k-i-rotateccw,\r\n.k-state-hover > * > .k-i-rotateccw,\r\n.k-button:not(.k-state-disabled):hover .k-i-rotateccw,\r\n.k-textbox:hover .k-i-rotateccw,\r\n.k-button:active .k-i-rotateccw {\r\n  background-position: -64px -128px;\r\n}\r\n.k-i-undo {\r\n  background-position: -64px -160px;\r\n}\r\n.k-link:not(.k-state-disabled):hover > .k-i-undo,\r\n.k-state-hover > .k-i-undo,\r\n.k-state-hover > * > .k-i-undo,\r\n.k-button:not(.k-state-disabled):hover .k-i-undo,\r\n.k-textbox:hover .k-i-undo,\r\n.k-button:active .k-i-undo {\r\n  background-position: -64px -160px;\r\n}\r\n.k-i-redo {\r\n  background-position: -64px -144px;\r\n}\r\n.k-link:not(.k-state-disabled):hover > .k-i-redo,\r\n.k-state-hover > .k-i-redo,\r\n.k-state-hover > * > .k-i-redo,\r\n.k-button:not(.k-state-disabled):hover .k-i-redo,\r\n.k-textbox:hover .k-i-redo,\r\n.k-button:active .k-i-redo {\r\n  background-position: -64px -144px;\r\n}\r\n.k-i-shape {\r\n  background-position: -64px -176px;\r\n}\r\n.k-link:not(.k-state-disabled):hover > .k-i-shape,\r\n.k-state-hover > .k-i-shape,\r\n.k-state-hover > * > .k-i-shape,\r\n.k-button:not(.k-state-disabled):hover .k-i-shape,\r\n.k-textbox:hover .k-i-shape,\r\n.k-button:active .k-i-shape {\r\n  background-position: -64px -176px;\r\n}\r\n.k-i-connector {\r\n  background-position: -64px -192px;\r\n}\r\n.k-link:not(.k-state-disabled):hover > .k-i-connector,\r\n.k-state-hover > .k-i-connector,\r\n.k-state-hover > * > .k-i-connector,\r\n.k-button:not(.k-state-disabled):hover .k-i-connector,\r\n.k-textbox:hover .k-i-connector,\r\n.k-button:active .k-i-connector {\r\n  background-position: -64px -192px;\r\n}\r\n.k-i-kpi {\r\n  background-position: -64px -208px;\r\n}\r\n.k-link:not(.k-state-disabled):hover > .k-i-kpi,\r\n.k-state-hover > .k-i-kpi,\r\n.k-state-hover > * > .k-i-kpi,\r\n.k-button:not(.k-state-disabled):hover .k-i-kpi,\r\n.k-textbox:hover .k-i-kpi,\r\n.k-button:active .k-i-kpi {\r\n  background-position: -64px -208px;\r\n}\r\n.k-i-dimension {\r\n  background-position: -64px -224px;\r\n}\r\n.k-link:not(.k-state-disabled):hover > .k-i-dimension,\r\n.k-state-hover > .k-i-dimension,\r\n.k-state-hover > * > .k-i-dimension,\r\n.k-button:not(.k-state-disabled):hover .k-i-dimension,\r\n.k-textbox:hover .k-i-dimension,\r\n.k-button:active .k-i-dimension {\r\n  background-position: -64px -224px;\r\n}\r\n.k-file {\r\n  background-position: 0px 0px;\r\n}\r\n.k-link:not(.k-state-disabled):hover > .k-file,\r\n.k-state-hover > .k-file,\r\n.k-state-hover > * > .k-file,\r\n.k-button:not(.k-state-disabled):hover .k-file,\r\n.k-textbox:hover .k-file,\r\n.k-button:active .k-file {\r\n  background-position: 0px 0px;\r\n}\r\n.k-i-folder-add,\r\n.k-addfolder {\r\n  background-position: -32px -272px;\r\n}\r\n.k-link:not(.k-state-disabled):hover > .k-i-folder-add,\r\n.k-link:not(.k-state-disabled):hover > .k-addfolder,\r\n.k-state-hover > .k-i-folder-add,\r\n.k-state-hover > .k-addfolder,\r\n.k-state-hover > * > .k-i-folder-add,\r\n.k-state-hover > * > .k-addfolder,\r\n.k-button:not(.k-state-disabled):hover .k-i-folder-add,\r\n.k-button:not(.k-state-disabled):hover .k-addfolder,\r\n.k-textbox:hover .k-i-folder-add,\r\n.k-textbox:hover .k-addfolder,\r\n.k-button:active .k-i-folder-add,\r\n.k-button:active .k-addfolder {\r\n  background-position: -32px -272px;\r\n}\r\n.k-i-folder-up,\r\n.k-goup {\r\n  background-position: -32px -288px;\r\n}\r\n.k-link:not(.k-state-disabled):hover > .k-i-folder-up,\r\n.k-link:not(.k-state-disabled):hover > .k-goup,\r\n.k-state-hover > .k-i-folder-up,\r\n.k-state-hover > .k-goup,\r\n.k-state-hover > * > .k-i-folder-up,\r\n.k-state-hover > * > .k-goup,\r\n.k-button:not(.k-state-disabled):hover .k-i-folder-up,\r\n.k-button:not(.k-state-disabled):hover .k-goup,\r\n.k-textbox:hover .k-i-folder-up,\r\n.k-textbox:hover .k-goup,\r\n.k-button:active .k-i-folder-up,\r\n.k-button:active .k-goup {\r\n  background-position: -32px -288px;\r\n}\r\n.k-i-more {\r\n  background-position: -64px -32px;\r\n}\r\n.k-link:not(.k-state-disabled):hover > .k-i-more,\r\n.k-state-hover > .k-i-more,\r\n.k-state-hover > * > .k-i-more,\r\n.k-button:not(.k-state-disabled):hover .k-i-more,\r\n.k-textbox:hover .k-i-more,\r\n.k-button:active .k-i-more {\r\n  background-position: -64px -32px;\r\n}\r\n.k-i-gantt-toggle {\r\n  background-position: -64px -240px;\r\n}\r\n.k-link:not(.k-state-disabled):hover > .k-i-gantt-toggle,\r\n.k-state-hover > .k-i-gantt-toggle,\r\n.k-state-hover > * > .k-i-gantt-toggle,\r\n.k-button:not(.k-state-disabled):hover .k-i-gantt-toggle,\r\n.k-textbox:hover .k-i-gantt-toggle,\r\n.k-button:active .k-i-gantt-toggle {\r\n  background-position: -64px -240px;\r\n}\r\n.k-file > .k-icon {\r\n  background-position: -115px -91px;\r\n}\r\n.k-image {\r\n  border: 0;\r\n}\r\n.k-breadcrumbs:hover .k-i-arrow-n {\r\n  background-position: 0 0;\r\n}\r\n.k-breadcrumbs:hover .k-i-arrow-e {\r\n  background-position: 0 -16px;\r\n}\r\n/* Dropdown icon in k-scheduler-views */\r\n.k-pager-numbers .k-current-page .k-link:after,\r\n.k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view .k-link:after,\r\n.k-gantt-views > .k-current-view > .k-link:after {\r\n  background-position: 0px -32px;\r\n}\r\n.k-link:not(.k-state-disabled):hover > .k-pager-numbers .k-current-page .k-link:after,\r\n.k-link:not(.k-state-disabled):hover > .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view .k-link:after,\r\n.k-link:not(.k-state-disabled):hover > .k-gantt-views > .k-current-view > .k-link:after,\r\n.k-state-hover > .k-pager-numbers .k-current-page .k-link:after,\r\n.k-state-hover > .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view .k-link:after,\r\n.k-state-hover > .k-gantt-views > .k-current-view > .k-link:after,\r\n.k-state-hover > * > .k-pager-numbers .k-current-page .k-link:after,\r\n.k-state-hover > * > .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view .k-link:after,\r\n.k-state-hover > * > .k-gantt-views > .k-current-view > .k-link:after,\r\n.k-button:not(.k-state-disabled):hover .k-pager-numbers .k-current-page .k-link:after,\r\n.k-button:not(.k-state-disabled):hover .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view .k-link:after,\r\n.k-button:not(.k-state-disabled):hover .k-gantt-views > .k-current-view > .k-link:after,\r\n.k-textbox:hover .k-pager-numbers .k-current-page .k-link:after,\r\n.k-textbox:hover .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view .k-link:after,\r\n.k-textbox:hover .k-gantt-views > .k-current-view > .k-link:after,\r\n.k-button:active .k-pager-numbers .k-current-page .k-link:after,\r\n.k-button:active .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view .k-link:after,\r\n.k-button:active .k-gantt-views > .k-current-view > .k-link:after {\r\n  background-position: 0px -32px;\r\n}\r\n/* Colors */\r\nhtml .k-success-colored {\r\n  color: #507f50;\r\n  border-color: #d0dfd0;\r\n  background-color: #f0fff0;\r\n}\r\nhtml .k-info-colored {\r\n  color: #50607f;\r\n  border-color: #d0d9df;\r\n  background-color: #f0f9ff;\r\n}\r\nhtml .k-error-colored {\r\n  color: #7f5050;\r\n  border-color: #dfd0d0;\r\n  background-color: #fff0f0;\r\n}\r\n.k-inline-block {\r\n  padding: 0 2px;\r\n}\r\n/* loading */\r\n.k-loading,\r\n.k-loading-image {\r\n  background-color: transparent;\r\n  background-repeat: no-repeat;\r\n  background-position: center center;\r\n}\r\n.k-loading-mask,\r\n.k-loading-image,\r\n.k-loading-text {\r\n  position: absolute;\r\n}\r\n.k-loading-mask {\r\n  z-index: 100;\r\n}\r\n.k-loading-mask .k-loading-progress {\r\n  margin: auto;\r\n  position: absolute;\r\n  top: 0;\r\n  bottom: 0;\r\n  left: 0;\r\n  right: 0;\r\n}\r\n.k-loading-text {\r\n  text-indent: -4000px;\r\n  text-align: center;\r\n  /*rtl*/\r\n}\r\n.k-loading-image,\r\n.k-loading-color {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n.k-loading-image {\r\n  top: 0;\r\n  left: 0;\r\n  z-index: 2;\r\n}\r\n.k-loading-color {\r\n  filter: alpha(opacity=30);\r\n  opacity: .3;\r\n}\r\n.k-content-frame {\r\n  border: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n.k-pane > .k-splitter-overlay {\r\n  filter: alpha(opacity=0);\r\n  opacity: 0;\r\n  position: absolute;\r\n}\r\n/* drag n drop */\r\n.k-drag-clue {\r\n  position: absolute;\r\n  z-index: 10003;\r\n  border-style: solid;\r\n  border-width: 1px;\r\n  font-size: .9em;\r\n  padding: .2em .4em;\r\n  white-space: nowrap;\r\n  cursor: default;\r\n}\r\n.k-drag-status {\r\n  margin-top: -3px;\r\n  margin-right: 4px;\r\n  vertical-align: middle;\r\n}\r\n.k-reorder-cue {\r\n  position: absolute;\r\n  width: 1px;\r\n  overflow: visible;\r\n}\r\n.k-reorder-cue .k-icon {\r\n  position: absolute;\r\n  left: -4px;\r\n  width: 8px;\r\n  height: 4px;\r\n}\r\n.k-reorder-cue .k-i-arrow-s {\r\n  top: -4px;\r\n  background-position: -4px -166px;\r\n}\r\n.k-reorder-cue .k-i-arrow-n {\r\n  bottom: -4px;\r\n  background-position: -4px -134px;\r\n}\r\n/* virtual scrollbar */\r\n.k-scrollbar {\r\n  position: absolute;\r\n  overflow: scroll;\r\n}\r\n.k-scrollbar-vertical {\r\n  top: 0;\r\n  right: 0;\r\n  width: 17px;\r\n  /* scrollbar width */\r\n  height: 100%;\r\n  overflow-x: hidden;\r\n}\r\n.k-touch-scrollbar {\r\n  display: none;\r\n  position: absolute;\r\n  z-index: 200000;\r\n  height: 8px;\r\n  width: 8px;\r\n  border: 1px solid #8a8a8a;\r\n  background-color: #858585;\r\n}\r\n@media only screen and (-webkit-min-device-pixel-ratio: 2) {\r\n  body .k-touch-scrollbar {\r\n    height: 12px;\r\n    width: 12px;\r\n    border-radius: 7px;\r\n  }\r\n}\r\n.k-virtual-scrollable-wrap {\r\n  overflow-x: auto;\r\n  /*needed by IE8*/\r\n}\r\n/* current time indicator */\r\n.k-current-time {\r\n  background: #f00;\r\n  position: absolute;\r\n}\r\n.k-current-time-arrow-down {\r\n  width: 0;\r\n  height: 0;\r\n  background: transparent;\r\n  border-bottom: 4px solid  transparent;\r\n  border-top: 4px solid #f00;\r\n  border-left: 4px solid transparent;\r\n  border-right: 4px solid transparent;\r\n}\r\n.k-current-time-arrow-left {\r\n  width: 0;\r\n  height: 0;\r\n  background: transparent;\r\n  border-bottom: 4px solid  transparent;\r\n  border-top: 4px solid transparent;\r\n  border-left: 4px solid transparent;\r\n  border-right: 4px solid #f00;\r\n}\r\n.k-current-time-arrow-right {\r\n  width: 0;\r\n  height: 0;\r\n  background: transparent;\r\n  border-bottom: 4px solid  transparent;\r\n  border-top: 4px solid transparent;\r\n  border-left: 4px solid #f00;\r\n  border-right: 4px solid transparent;\r\n}\r\n/* override box sizing for grid layout framework integration (Bootstrap 3, Foundation 4) */\r\n.k-animation-container,\r\n.k-widget,\r\n.k-widget *,\r\n.k-animation-container *,\r\n.k-widget *:before,\r\n.k-animation-container *:after,\r\n.k-block .k-header,\r\n.k-list-container {\r\n  -webkit-box-sizing: content-box;\r\n          box-sizing: content-box;\r\n}\r\n.k-button,\r\n.k-textbox,\r\n.k-autocomplete,\r\ndiv.k-window-content,\r\n.k-tabstrip > .k-content > .km-scroll-container,\r\n.k-block,\r\n.k-edit-cell .k-widget,\r\n.k-grid-edit-row .k-widget,\r\n.k-grid-edit-row .text-box,\r\n.km-actionsheet > li,\r\n.km-shim {\r\n  -webkit-box-sizing: border-box;\r\n          box-sizing: border-box;\r\n}\r\n/* Fix for Bootstrap 3 */\r\n.input-group .form-control {\r\n  -webkit-box-sizing: border-box;\r\n          box-sizing: border-box;\r\n}\r\n.form-control.k-widget {\r\n  padding: 0;\r\n}\r\na.k-button:hover {\r\n  text-decoration: none;\r\n}\r\n/* override iOS styles in mobile Kendo */\r\n.km-widget,\r\n.km-widget * {\r\n  -webkit-background-clip: border-box;\r\n  background-clip: border-box;\r\n}\r\ninput.k-checkbox,\r\n.k-radio {\r\n  display: inline;\r\n  opacity: 0;\r\n  width: 0;\r\n  margin: 0;\r\n  -webkit-appearance: none;\r\n  overflow: hidden;\r\n}\r\n.k-ff input.k-checkbox,\r\n.k-ff .k-radio {\r\n  position: absolute;\r\n}\r\n.k-checkbox-label {\r\n  position: relative;\r\n  padding-left: 1.5em;\r\n  vertical-align: middle;\r\n  line-height: 0.875em;\r\n  cursor: pointer;\r\n}\r\n.k-checkbox-label:before {\r\n  content: \"\";\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  border-width: 1px;\r\n  border-style: solid;\r\n  width: 1em;\r\n  height: 1em;\r\n  font-size: 1em;\r\n  line-height: 1em;\r\n  text-align: center;\r\n}\r\n.k-checkbox:indeterminate + .k-checkbox-label:after {\r\n  content: \"\";\r\n  position: absolute;\r\n  left: 50%;\r\n  top: 50%;\r\n  -webkit-transform: translate(-50%, -50%);\r\n  -ms-transform: translate(-50%, -50%);\r\n      transform: translate(-50%, -50%);\r\n  border-width: 1px;\r\n  border-style: solid;\r\n  width: 8px;\r\n  height: 8px;\r\n  font-size: 1em;\r\n  text-align: center;\r\n  content: \" \";\r\n  margin-left: -2px;\r\n}\r\n.k-checkbox:checked + .k-checkbox-label:before {\r\n  content: \"\\2713\";\r\n}\r\n.k-checkbox:disabled + .k-checkbox-label {\r\n  cursor: auto;\r\n}\r\n.k-radio-label {\r\n  position: relative;\r\n  padding-left: 1.5em;\r\n  vertical-align: middle;\r\n  line-height: 0.875em;\r\n  cursor: pointer;\r\n}\r\n.k-radio-label:before {\r\n  content: \"\";\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  width: 14px;\r\n  height: 14px;\r\n  border-style: solid;\r\n}\r\n.k-radio:checked + .k-radio-label:after {\r\n  content: \"\";\r\n  width: 10px;\r\n  height: 10px;\r\n  position: absolute;\r\n  top: 3px;\r\n  left: 3px;\r\n}\r\n.k-radio:disabled + .k-radio-label {\r\n  cursor: auto;\r\n}\r\n.k-ie8 input.k-checkbox,\r\n.k-ie8 .k-radio {\r\n  display: inline-block;\r\n  width: auto;\r\n}\r\n.k-ie8 .k-checkbox-label,\r\n.k-ie8 .k-radio-label {\r\n  padding-left: 0;\r\n}\r\n.k-ie8 .k-checkbox-label:before,\r\n.k-ie8 .k-radio-label:before,\r\n.k-ie8 .k-radio-label:after {\r\n  display: none;\r\n}\r\n/* RTL for checkboxes and radio buttons */\r\n.k-rtl .k-checkbox-label,\r\n.k-rtl .k-radio-label {\r\n  padding-right: 1.5em;\r\n}\r\n.k-rtl .k-checkbox-label:before,\r\n.k-rtl .k-radio-label:before {\r\n  right: 0;\r\n}\r\n.k-rtl .k-radio:checked + .k-radio-label:after {\r\n  right: 3px;\r\n}\r\ninput.k-checkbox + label {\r\n  -webkit-user-select: none;\r\n}\r\n/* Off-screen container used during export */\r\n.k-pdf-export-shadow {\r\n  position: absolute;\r\n  overflow: hidden;\r\n  left: -15000px;\r\n  width: 14400px;\r\n}\r\n.km-native-scroller {\r\n  overflow: auto;\r\n  -webkit-overflow-scrolling: touch;\r\n  -ms-touch-action: pan-x pan-y;\r\n  -ms-overflow-style: -ms-autohiding-scrollbar;\r\n  -ms-scroll-snap-type: proximity;\r\n}\r\n/* responsive panel */\r\n.k-rpanel-left {\r\n  -webkit-transform: translateX(-100%) translateZ(0);\r\n  -ms-transform: translateX(-100%) translateZ(0);\r\n  transform: translateX(-100%) translateZ(0);\r\n  left: 0;\r\n}\r\n.k-rpanel-right {\r\n  -webkit-transform: translateX(100%) translateZ(0);\r\n  -ms-transform: translateX(100%) translateZ(0);\r\n  transform: translateX(100%) translateZ(0);\r\n  right: 0;\r\n}\r\n.k-rpanel-left,\r\n.k-rpanel-right {\r\n  position: fixed;\r\n  display: block;\r\n  overflow: auto;\r\n  min-width: 320px;\r\n  height: 100%;\r\n  top: 0;\r\n}\r\n.k-rpanel-left.k-rpanel-expanded,\r\n.k-rpanel-right.k-rpanel-expanded {\r\n  -webkit-transform: translateX(0) translateZ(0);\r\n  -ms-transform: translateX(0) translateZ(0);\r\n  transform: translateX(0) translateZ(0);\r\n}\r\n.k-rpanel-left + *,\r\n.k-rpanel-right + * {\r\n  overflow: auto;\r\n}\r\n.k-rpanel-top {\r\n  position: static;\r\n  max-height: 0;\r\n}\r\n.k-rpanel-top.k-rpanel-expanded {\r\n  max-height: 568px;\r\n  overflow: visible !important;\r\n}\r\n.k-edit-form {\r\n  margin: 0;\r\n  padding: 0;\r\n}\r\n.k-window > div.k-popup-edit-form {\r\n  padding: 1em 0;\r\n}\r\n.k-grid-edit-row .k-edit-form td {\r\n  border-bottom-width: 0;\r\n}\r\n.k-edit-form-container {\r\n  position: relative;\r\n  width: 400px;\r\n}\r\n.k-edit-label,\r\n.k-edit-form-container .editor-label {\r\n  float: left;\r\n  clear: both;\r\n  width: 30%;\r\n  padding: .4em 0 1em;\r\n  margin-left: 2%;\r\n  text-align: right;\r\n}\r\n.k-edit-field,\r\n.k-edit-form-container .editor-field {\r\n  float: right;\r\n  clear: right;\r\n  width: 60%;\r\n  margin-right: 2%;\r\n  padding: 0 0 .6em;\r\n}\r\n.k-edit-field > input[type=\"checkbox\"],\r\n.k-edit-field > input[type=\"radio\"] {\r\n  margin-top: .4em;\r\n}\r\n.k-edit-form-container .k-button {\r\n  margin: 0 .16em;\r\n}\r\n.k-edit-field > input[type=\"checkbox\"]:first-child,\r\n.k-edit-field > input[type=\"radio\"]:first-child,\r\n.k-edit-field > label:first-child > input[type=\"checkbox\"],\r\n.k-edit-field > .k-button:first-child {\r\n  margin-left: 0;\r\n}\r\n.k-edit-form-container .k-edit-buttons {\r\n  clear: both;\r\n  text-align: right;\r\n  border-width: 1px 0 0;\r\n  border-style: solid;\r\n  position: relative;\r\n  bottom: -1em;\r\n  padding: .6em;\r\n}\r\n/* Window */\r\ndiv.k-window {\r\n  display: inline-block;\r\n  position: absolute;\r\n  z-index: 10001;\r\n  border-style: solid;\r\n  border-width: 1px;\r\n  padding-top: 2em;\r\n}\r\n.k-block > .k-header,\r\n.k-window-titlebar {\r\n  position: absolute;\r\n  width: 100%;\r\n  height: 1.1em;\r\n  border-bottom-style: solid;\r\n  border-bottom-width: 1px;\r\n  margin-top: -2em;\r\n  padding: .4em 0;\r\n  font-size: 1.2em;\r\n  white-space: nowrap;\r\n  min-height: 16px;\r\n  /* icon size */\r\n}\r\n.k-block > .k-header {\r\n  position: relative;\r\n  margin: -2px 0 10px -2px;\r\n  padding: .3em 2px;\r\n}\r\n.k-window-title {\r\n  position: absolute;\r\n  left: .44em;\r\n  right: .44em;\r\n  overflow: hidden;\r\n  cursor: default;\r\n  text-overflow: ellipsis;\r\n}\r\n.k-window-title .k-image {\r\n  margin: 0 5px 0 0;\r\n  vertical-align: middle;\r\n}\r\ndiv.k-window-titleless {\r\n  padding-top: 0;\r\n}\r\ndiv.k-window-content {\r\n  position: relative;\r\n  height: 100%;\r\n  padding: .58em;\r\n  overflow: auto;\r\n  outline: 0;\r\n}\r\ndiv.k-window-iframecontent {\r\n  padding: 0;\r\n  overflow: visible;\r\n}\r\n.k-window-content > .km-scroll-container {\r\n  height: 100%;\r\n}\r\n.k-window-titlebar .k-window-actions {\r\n  position: absolute;\r\n  top: 0;\r\n  right: .3em;\r\n  padding-top: .3em;\r\n  white-space: nowrap;\r\n}\r\n.k-window-titlebar .k-window-action {\r\n  display: inline-block;\r\n  width: 16px;\r\n  height: 16px;\r\n  padding: 2px;\r\n  text-decoration: none;\r\n  vertical-align: middle;\r\n  opacity: .7;\r\n}\r\n.k-window-titlebar .k-state-hover {\r\n  border-style: solid;\r\n  border-width: 1px;\r\n  padding: 1px;\r\n  opacity: 1;\r\n}\r\n.k-window-action .k-icon {\r\n  margin: 0;\r\n  vertical-align: top;\r\n}\r\n.k-window > .k-resize-handle {\r\n  position: absolute;\r\n  z-index: 1;\r\n  background-color: #fff;\r\n  font-size: 0;\r\n  line-height: 6px;\r\n  filter: alpha(opacity=0);\r\n  opacity: 0;\r\n  zoom: 1;\r\n}\r\n.k-resize-n {\r\n  top: -3px;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 6px;\r\n  cursor: n-resize;\r\n}\r\n.k-resize-e {\r\n  top: 0;\r\n  right: -3px;\r\n  width: 6px;\r\n  height: 100%;\r\n  cursor: e-resize;\r\n}\r\n.k-resize-s {\r\n  bottom: -3px;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 6px;\r\n  cursor: s-resize;\r\n}\r\n.k-resize-w {\r\n  top: 0;\r\n  left: -3px;\r\n  width: 6px;\r\n  height: 100%;\r\n  cursor: w-resize;\r\n}\r\n.k-resize-se {\r\n  bottom: -3px;\r\n  right: -3px;\r\n  width: 16px;\r\n  height: 16px;\r\n  cursor: se-resize;\r\n}\r\n.k-resize-sw {\r\n  bottom: -3px;\r\n  left: -3px;\r\n  width: 6px;\r\n  height: 6px;\r\n  cursor: sw-resize;\r\n}\r\n.k-resize-ne {\r\n  top: -3px;\r\n  right: -3px;\r\n  width: 6px;\r\n  height: 6px;\r\n  cursor: ne-resize;\r\n}\r\n.k-resize-nw {\r\n  top: -3px;\r\n  left: -3px;\r\n  width: 6px;\r\n  height: 6px;\r\n  cursor: nw-resize;\r\n}\r\n.k-overlay {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  z-index: 10001;\r\n  width: 100%;\r\n  height: 100%;\r\n  background-color: #000;\r\n  filter: alpha(opacity=50);\r\n  opacity: .5;\r\n}\r\n.k-window .k-overlay {\r\n  position: absolute;\r\n  width: 100%;\r\n  height: 100%;\r\n  background-color: #fff;\r\n  filter: alpha(opacity=0);\r\n  opacity: 0;\r\n}\r\n.k-action-buttons {\r\n  clear: both;\r\n  text-align: right;\r\n  border-width: 1px 0 0;\r\n  border-style: solid;\r\n  position: relative;\r\n  bottom: -1em;\r\n  padding: .6em;\r\n  margin: 0 -1em;\r\n}\r\n.k-action-buttons .k-button {\r\n  display: inline-block;\r\n  margin: 0 0 0 6px;\r\n  min-width: 75px;\r\n}\r\n/* TabStrip */\r\n.k-tabstrip {\r\n  margin: 0;\r\n  padding: 0;\r\n  zoom: 1;\r\n  position: relative;\r\n}\r\n.k-tabstrip-items {\r\n  padding: 0.3em 0.3em 0;\r\n}\r\n.k-tabstrip-scrollable .k-tabstrip-items {\r\n  white-space: nowrap;\r\n  overflow: hidden;\r\n}\r\n.k-tabstrip > .k-button {\r\n  position: absolute;\r\n  top: .4em;\r\n  z-index: 2;\r\n  -webkit-user-select: none;\r\n     -moz-user-select: none;\r\n      -ms-user-select: none;\r\n          user-select: none;\r\n}\r\n.k-tabstrip-bottom > .k-button {\r\n  top: auto;\r\n  bottom: .4em;\r\n}\r\n.k-tabstrip-prev {\r\n  left: .4em;\r\n}\r\n.k-tabstrip-next {\r\n  right: .4em;\r\n}\r\n.k-tabstrip-items .k-item,\r\n.k-panelbar .k-tabstrip-items .k-item {\r\n  list-style-type: none;\r\n  display: inline-block;\r\n  position: relative;\r\n  border-style: solid;\r\n  border-width: 1px 1px 0;\r\n  margin: 0 -1px 0 0;\r\n  padding: 0;\r\n  vertical-align: top;\r\n}\r\n.k-tabstrip-items .k-tab-on-top,\r\n.k-tabstrip-items .k-state-active,\r\n.k-panelbar .k-tabstrip-items .k-state-active {\r\n  margin-bottom: -1px;\r\n  padding-bottom: 1px;\r\n}\r\n.k-tabstrip-items .k-tab-on-top {\r\n  z-index: 1;\r\n}\r\n.k-tabstrip-items .k-link,\r\n.k-panelbar .k-tabstrip-items .k-link {\r\n  display: inline-block;\r\n  border-bottom-width: 0;\r\n  padding: .5em .92em;\r\n}\r\n.k-tabstrip-items .k-icon,\r\n.k-panelbar .k-tabstrip-items .k-icon {\r\n  margin: -1px 4px 0 -3px;\r\n  vertical-align: top;\r\n}\r\n.k-tabstrip-items .k-item .k-image,\r\n.k-tabstrip-items .k-item .k-sprite,\r\n.k-panelbar .k-tabstrip-items .k-item .k-image,\r\n.k-panelbar .k-tabstrip-items .k-item .k-sprite {\r\n  margin: -3px 3px 0 -6px;\r\n  vertical-align: middle;\r\n}\r\n/* TabStrip Loading Progress */\r\n.k-tabstrip-items .k-loading {\r\n  top: 0;\r\n  left: 0;\r\n  height: 0;\r\n  width: 20%;\r\n  position: absolute;\r\n  background: transparent;\r\n  border-top: 1px solid transparent;\r\n  border-color: inherit;\r\n  -webkit-transition: width 200ms linear;\r\n          transition: width 200ms linear;\r\n  -webkit-transition: \"width 200ms linear\";\r\n          transition: \"width 200ms linear\";\r\n  -webkit-animation: k-tab-loader 1s ease-in-out infinite;\r\n          animation: k-tab-loader 1s ease-in-out infinite;\r\n}\r\n.k-tabstrip-items .k-progress {\r\n  -webkit-animation: none;\r\n          animation: none;\r\n}\r\n.k-tabstrip-items .k-loading.k-complete {\r\n  width: 100%;\r\n  -webkit-animation: none;\r\n          animation: none;\r\n}\r\n.k-tabstrip > .k-content,\r\n.k-panelbar .k-tabstrip > .k-content {\r\n  position: static;\r\n  border-style: solid;\r\n  border-width: 1px;\r\n  margin: 0 .286em .3em;\r\n  padding: .3em .92em;\r\n  zoom: 1;\r\n}\r\n.k-tabstrip > .k-content {\r\n  display: none;\r\n  overflow: auto;\r\n}\r\n.k-tabstrip > .k-content.km-scroll-wrapper {\r\n  padding: 0;\r\n}\r\n.k-tabstrip > .k-content > .km-scroll-container {\r\n  padding: .3em .92em;\r\n}\r\n@-webkit-keyframes k-tab-loader {\r\n  0% {\r\n    left: 0;\r\n  }\r\n  50% {\r\n    left: 80%;\r\n  }\r\n  100% {\r\n    left: 0;\r\n  }\r\n}\r\n@keyframes k-tab-loader {\r\n  0% {\r\n    left: 0;\r\n  }\r\n  50% {\r\n    left: 80%;\r\n  }\r\n  100% {\r\n    left: 0;\r\n  }\r\n}\r\n/* left and right tabs */\r\n.k-tabstrip-left > div.k-content,\r\n.k-tabstrip-right > div.k-content {\r\n  margin: .286em .3em;\r\n}\r\n.k-tabstrip-left > .k-tabstrip-items .k-item,\r\n.k-tabstrip-right > .k-tabstrip-items .k-item {\r\n  display: block;\r\n  margin-bottom: -1px;\r\n}\r\n.k-tabstrip-left > .k-tabstrip-items .k-link,\r\n.k-tabstrip-right > .k-tabstrip-items .k-link {\r\n  display: block;\r\n}\r\n.k-tabstrip-left > .k-tabstrip-items .k-tab-on-top,\r\n.k-tabstrip-right > .k-tabstrip-items .k-tab-on-top,\r\n.k-tabstrip-left > .k-tabstrip-items .k-state-active,\r\n.k-tabstrip-right > .k-tabstrip-items .k-state-active,\r\n.k-panelbar .k-tabstrip-left > .k-tabstrip-items .k-state-active,\r\n.k-panelbar .k-tabstrip-right > .k-tabstrip-items .k-state-active {\r\n  margin-bottom: -1px;\r\n  padding-bottom: 0;\r\n}\r\n/* left tabs */\r\n.k-tabstrip-left > .k-tabstrip-items {\r\n  float: left;\r\n  padding: .25em 0 .3em .3em;\r\n}\r\n.k-tabstrip-left > .k-tabstrip-items .k-item {\r\n  border-width: 1px 0 1px 1px;\r\n  border-radius: 3px 0 0 3px;\r\n}\r\n.k-tabstrip-left > .k-tabstrip-items .k-state-active {\r\n  border-width: 1px 0 1px 1px;\r\n}\r\n.k-tabstrip-left > .k-tabstrip-items .k-tab-on-top,\r\n.k-tabstrip-left > .k-tabstrip-items .k-state-active,\r\n.k-panelbar .k-tabstrip-left > .k-tabstrip-items .k-state-active {\r\n  margin-right: -2px;\r\n  padding-right: 1px;\r\n}\r\n/* right tabs */\r\n.k-tabstrip-right > .k-tabstrip-items {\r\n  float: right;\r\n  padding: .25em .3em .3em 0;\r\n}\r\n.k-tabstrip-right > .k-tabstrip-items .k-item {\r\n  border-width: 1px 1px 1px 0;\r\n  border-radius: 0 3px 3px 0;\r\n}\r\n.k-tabstrip-right > .k-tabstrip-items .k-state-active {\r\n  border-width: 1px 1px 1px 0;\r\n}\r\n.k-tabstrip-right > .k-tabstrip-items .k-tab-on-top,\r\n.k-tabstrip-right > .k-tabstrip-items .k-state-active,\r\n.k-panelbar .k-tabstrip-right > .k-tabstrip-items .k-state-active {\r\n  margin-left: -1px;\r\n  padding-left: 1px;\r\n}\r\n/* bottom tabs */\r\n.k-tabstrip-bottom > .k-tabstrip-items {\r\n  margin-top: -1px;\r\n  padding: 0 .3em .3em;\r\n}\r\n.k-tabstrip-bottom > .k-content,\r\n.k-panelbar .k-tabstrip-bottom > .k-content {\r\n  margin: .3em .286em 0;\r\n  z-index: 1;\r\n  position: relative;\r\n}\r\n.k-tabstrip-bottom > .k-tabstrip-items .k-item {\r\n  border-width: 0 1px 1px;\r\n  border-radius: 0 0 4px 4px;\r\n}\r\n.k-tabstrip-bottom > .k-tabstrip-items .k-state-active {\r\n  margin-bottom: 0;\r\n  padding-bottom: 0;\r\n}\r\n.k-tabstrip-bottom > .k-content {\r\n  min-height: 100px;\r\n}\r\n.k-tabstrip-bottom > .k-tabstrip-items .k-loading {\r\n  top: auto;\r\n  bottom: 0;\r\n}\r\n/* PanelBar */\r\n.k-panelbar {\r\n  zoom: 1;\r\n}\r\n.k-panelbar > .k-item,\r\n.k-panel > .k-item {\r\n  list-style-type: none;\r\n  display: block;\r\n  border-width: 0;\r\n  margin: 0;\r\n  zoom: 1;\r\n  border-radius: 0;\r\n}\r\n.k-panelbar .k-link > .k-image,\r\n.k-panelbar .k-link > .k-sprite {\r\n  float: left;\r\n  margin-top: 4px;\r\n  margin-right: 5px;\r\n  vertical-align: middle;\r\n}\r\n.k-panelbar > .k-item > .k-link,\r\n.k-panel > .k-item > .k-link {\r\n  display: block;\r\n  position: relative;\r\n  border-bottom-style: solid;\r\n  border-bottom-width: 1px;\r\n  padding: 0 1em;\r\n  line-height: 2.34em;\r\n  text-decoration: none;\r\n  zoom: 1;\r\n}\r\n.k-panelbar-expand,\r\n.k-panelbar-collapse {\r\n  position: absolute;\r\n  top: 50%;\r\n  right: 4px;\r\n  margin-top: -8px;\r\n}\r\n.k-panelbar .k-panel,\r\n.k-panelbar .k-content {\r\n  position: relative;\r\n  border-bottom-style: solid;\r\n  border-bottom-width: 1px;\r\n  margin: 0;\r\n  padding: 0;\r\n  zoom: 1;\r\n}\r\n.k-panel > .k-item > .k-link {\r\n  border-bottom: 0;\r\n  font-size: .95em;\r\n  line-height: 2.2;\r\n}\r\n.k-panel .k-panel > .k-item > .k-link {\r\n  padding-left: 2em;\r\n}\r\n.k-panelbar .k-i-seek-e .k-link {\r\n  border-bottom: 0;\r\n}\r\n.k-panel .k-panel {\r\n  border-bottom: 0;\r\n}\r\n/* Menu */\r\n.k-menu {\r\n  cursor: default;\r\n}\r\n.k-menu,\r\n.k-menu .k-menu-group {\r\n  list-style: none;\r\n  margin: 0;\r\n  padding: 0;\r\n  zoom: 1;\r\n}\r\n.k-menu:after {\r\n  content: '';\r\n  display: block;\r\n  width: 99%;\r\n  height: 0;\r\n  float: inherit;\r\n  clear: both;\r\n}\r\n.k-menu .k-item {\r\n  -webkit-user-select: none;\r\n  -moz-user-select: -moz-none;\r\n  -ms-user-select: none;\r\n      user-select: none;\r\n}\r\n.k-menu .k-item div {\r\n  -webkit-user-select: default;\r\n     -moz-user-select: default;\r\n      -ms-user-select: default;\r\n          user-select: default;\r\n}\r\n.k-menu .k-item .k-item,\r\nul.k-menu-vertical > .k-item {\r\n  display: block;\r\n  float: none;\r\n  border-width: 0;\r\n}\r\n.k-menu .k-item > .k-link > .k-icon,\r\n.k-menu .k-image,\r\n.k-menu .k-sprite {\r\n  margin: -2px 4px 0 -4px;\r\n  vertical-align: middle;\r\n}\r\n.k-menu .k-item > .k-link > .k-icon {\r\n  margin: -2px 0 0;\r\n}\r\n.k-menu .k-item > .k-link {\r\n  display: block;\r\n  padding: 1.071em;\r\n  line-height: 1.34em;\r\n  -webkit-user-select: none;\r\n     -moz-user-select: none;\r\n      -ms-user-select: none;\r\n          user-select: none;\r\n}\r\n.k-menu .k-menu-group {\r\n  display: none;\r\n  border-style: solid;\r\n  border-width: 1px;\r\n  overflow: visible;\r\n  white-space: nowrap;\r\n}\r\n.k-menu .k-menu-group > .k-item {\r\n  display: block;\r\n  border-width: 0;\r\n}\r\n.k-menu .k-item,\r\n.k-widget.k-menu-horizontal > .k-item {\r\n  position: relative;\r\n  float: left;\r\n  border-style: solid;\r\n  border-width: 0 1px 0 0;\r\n  vertical-align: top;\r\n  zoom: 1;\r\n  -webkit-box-sizing: content-box;\r\n          box-sizing: content-box;\r\n}\r\n.k-context-menu.k-menu-vertical > .k-item > .k-link,\r\n.k-menu .k-menu-group .k-item > .k-link {\r\n  padding: .28em 1.8em .38em .9em;\r\n}\r\n.k-context-menu.k-menu-horizontal > .k-separator {\r\n  display: none;\r\n}\r\n.k-context-menu.k-menu-horizontal > .k-item {\r\n  -webkit-box-sizing: border-box;\r\n          box-sizing: border-box;\r\n}\r\n.k-context-menu.k-menu-horizontal > .k-last {\r\n  border: 0;\r\n}\r\n.k-menu .k-item > .k-link > .k-i-arrow-s {\r\n  margin-right: -8px;\r\n}\r\n.k-menu .k-item > .k-link > .k-i-arrow-e {\r\n  position: absolute;\r\n  top: 50%;\r\n  margin-top: -8px;\r\n  right: 2px;\r\n  right: .2rem;\r\n}\r\n.k-menu .k-animation-container {\r\n  border: 0;\r\n}\r\n.k-menu .k-animation-container,\r\n.k-menu .k-menu-group {\r\n  position: absolute;\r\n  left: 0;\r\n}\r\n.k-menu .k-animation-container .k-animation-container,\r\n.k-menu .k-menu-group .k-menu-group,\r\n.k-menu-vertical .k-animation-container,\r\n.k-menu-vertical .k-menu-group {\r\n  top: 0;\r\n  left: 0;\r\n}\r\n.k-menu .k-animation-container .k-menu-group {\r\n  top: auto;\r\n  left: auto;\r\n  margin-left: -1px;\r\n}\r\n.k-menu .k-animation-container,\r\n.k-popup .k-animation-container {\r\n  margin-top: -1px;\r\n  padding-left: 1px;\r\n}\r\n.k-ie .k-menu .k-animation-container,\r\n.k-ie .k-popup .k-animation-container {\r\n  margin-top: -2px;\r\n}\r\n.k-popup .k-animation-container .k-popup {\r\n  margin-left: -1px;\r\n}\r\nul.k-menu .k-separator {\r\n  padding: 0.25em 0;\r\n  height: 100%;\r\n  width: 1px;\r\n  font-size: 0;\r\n  line-height: 0;\r\n  border-width: 0 1px 0 0;\r\n}\r\nul.k-menu-vertical .k-separator,\r\n.k-menu .k-menu-group .k-separator {\r\n  padding: 0;\r\n  height: 1px;\r\n  width: 100%;\r\n  border-width: 1px 0 0;\r\n}\r\n/* Context Menu */\r\n.k-context-menu {\r\n  border: 0;\r\n  -webkit-user-select: none;\r\n     -moz-user-select: none;\r\n      -ms-user-select: none;\r\n          user-select: none;\r\n}\r\n/* Grid */\r\n.k-grid,\r\n.k-listview {\r\n  position: relative;\r\n  zoom: 1;\r\n}\r\n.k-grid table {\r\n  width: 100%;\r\n  margin: 0;\r\n  /* override CSS libraries */\r\n  max-width: none;\r\n  border-collapse: separate;\r\n  border-spacing: 0;\r\n  empty-cells: show;\r\n  border-width: 0;\r\n  outline: none;\r\n}\r\n.k-header.k-drag-clue {\r\n  overflow: hidden;\r\n}\r\n.k-grid-header th.k-header,\r\n.k-filter-row th {\r\n  overflow: hidden;\r\n  border-style: solid;\r\n  border-width: 0 0 1px 1px;\r\n  padding: .5em .6em .4em .6em;\r\n  font-weight: normal;\r\n  white-space: nowrap;\r\n  text-overflow: ellipsis;\r\n  text-align: left;\r\n}\r\n.k-grid-header th.k-header {\r\n  vertical-align: bottom;\r\n}\r\n.k-filtercell,\r\n.k-filtercell > span,\r\n.k-filtercell .k-widget {\r\n  display: block;\r\n  width: auto;\r\n}\r\n.k-filtercell > span {\r\n  padding-right: 4.8em;\r\n  position: relative;\r\n  min-height: 2em;\r\n  line-height: 2em;\r\n}\r\n.k-filtercell > .k-operator-hidden {\r\n  padding-right: 2.3em;\r\n}\r\n.k-filtercell > span > .k-button,\r\n.k-filter-row .k-dropdown-operator {\r\n  position: absolute;\r\n  top: 0;\r\n  right: 0;\r\n}\r\n.k-filter-row .k-dropdown-operator {\r\n  width: 2.1em;\r\n  right: 2.5em;\r\n}\r\n.k-filtercell > span > label {\r\n  vertical-align: middle;\r\n}\r\n.k-filter-row label > input[type=\"radio\"] {\r\n  vertical-align: middle;\r\n  position: relative;\r\n  bottom: 2px;\r\n}\r\n.k-ie10 .k-grid-header a:active {\r\n  background-color: transparent;\r\n  /*remove gray background*/\r\n}\r\n.k-grid-header th.k-header > .k-link {\r\n  display: block;\r\n  min-height: 18px;\r\n  line-height: 18px;\r\n  /* due to sorting icons*/\r\n  margin: -0.5em -0.6em -0.4em 0;\r\n  padding: .5em .6em .4em 0;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n}\r\n.k-grid-header th.k-with-icon .k-link {\r\n  margin-right: 18px;\r\n}\r\n.k-grid-header th.k-header .k-icon {\r\n  position: static;\r\n}\r\n.k-grid-header th > .k-link > .k-icon {\r\n  vertical-align: text-top;\r\n}\r\n.k-grid .k-state-hover {\r\n  cursor: pointer;\r\n}\r\n.k-grid-column-resizing,\r\n.k-grid-column-resizing .k-grid-filter,\r\n.k-grid-column-resizing .k-link,\r\n.k-grid-column-resizing .k-button,\r\n.k-grid-column-resizing .k-grid-toolbar {\r\n  cursor: col-resize;\r\n}\r\n.k-grid td {\r\n  border-style: solid;\r\n  border-width: 0 0 0 1px;\r\n  padding: .4em .6em;\r\n  overflow: hidden;\r\n  line-height: 1.6em;\r\n  vertical-align: middle;\r\n  text-overflow: ellipsis;\r\n}\r\n.k-grid .k-grouping-row td,\r\n.k-grid .k-hierarchy-cell {\r\n  overflow: visible;\r\n}\r\n.k-grid-edit-row td {\r\n  text-overflow: clip;\r\n}\r\n.k-grid-edit-row .k-textbox,\r\n.k-grid-edit-row .text-box {\r\n  /*reset default webkit styles*/\r\n  margin-top: 0;\r\n  margin-bottom: 0;\r\n}\r\n.k-grid-header-wrap,\r\n.k-grid-footer-wrap {\r\n  position: relative;\r\n  width: 100%;\r\n  overflow: hidden;\r\n  border-style: solid;\r\n  border-width: 0 1px 0 0;\r\n  zoom: 1;\r\n}\r\ndiv.k-grid-header,\r\ndiv.k-grid-footer {\r\n  padding-right: 17px;\r\n  /* scrollbar width; may vary; can be calculated */\r\n  border-bottom-style: solid;\r\n  border-bottom-width: 1px;\r\n  zoom: 1;\r\n}\r\n.k-grid-header-wrap > table,\r\n.k-grid-header-locked > table {\r\n  margin-bottom: -1px;\r\n}\r\n.k-grid-content {\r\n  position: relative;\r\n  width: 100%;\r\n  overflow: auto;\r\n  overflow-x: auto;\r\n  overflow-y: scroll;\r\n  zoom: 1;\r\n  min-height: 0%;\r\n  /* IE9 bug workaround - expanding Grid on hover */\r\n}\r\n.k-mobile .k-grid tbody {\r\n  -webkit-backface-visibility: hidden;\r\n}\r\n.k-mobile .k-grid-backface tbody {\r\n  -webkit-backface-visibility: visible;\r\n}\r\n.k-grid-content-expander {\r\n  position: absolute;\r\n  visibility: hidden;\r\n  height: 1px;\r\n}\r\n.k-grid-norecords {\r\n  width: 100%;\r\n  height: 100%;\r\n  text-align: center;\r\n}\r\n.k-grid-norecords-template {\r\n  width: 20em;\r\n  height: 4em;\r\n  line-height: 4em;\r\n  vertical-align: middle;\r\n  margin: 0 auto;\r\n}\r\n.k-grid-content > .k-grid-norecords > .k-grid-norecords-template {\r\n  top: 50%;\r\n  left: 50%;\r\n  margin-left: -10em;\r\n  margin-top: -2em;\r\n  position: absolute;\r\n}\r\n@media print {\r\n  .k-grid {\r\n    height: auto !important;\r\n  }\r\n  .k-grid-header {\r\n    padding: 0 !important;\r\n  }\r\n  .k-grid-header-wrap,\r\n  .k-grid-content {\r\n    overflow: visible;\r\n    height: auto !important;\r\n  }\r\n}\r\n.k-grid .k-scrollbar {\r\n  -ms-overflow-style: scrollbar;\r\n}\r\n.k-virtual-scrollable-wrap {\r\n  height: 100%;\r\n  overflow-y: hidden;\r\n  position: relative;\r\n}\r\n.k-grid-header table,\r\n.k-grid-content table,\r\n.k-grid-footer table,\r\n.k-grid-content-locked > table {\r\n  table-layout: fixed;\r\n}\r\n/* Grid :: locked columns */\r\n.k-grid-lockedcolumns {\r\n  white-space: nowrap;\r\n}\r\n.k-grid-content-locked,\r\n.k-grid-content,\r\n.k-pager-wrap {\r\n  white-space: normal;\r\n}\r\n.k-grid-header-locked,\r\n.k-grid-content-locked,\r\n.k-grid-footer-locked {\r\n  display: inline-block;\r\n  vertical-align: top;\r\n  overflow: hidden;\r\n  /* generally uneeded */\r\n  position: relative;\r\n  border-style: solid;\r\n  border-width: 0 1px 0 0;\r\n}\r\n.k-grid-header-locked + .k-grid-header-wrap,\r\n.k-grid-content-locked + .k-grid-content,\r\n.k-grid-footer-locked + .k-grid-footer-wrap {\r\n  display: inline-block;\r\n  vertical-align: top;\r\n}\r\n.k-grid-toolbar {\r\n  border-style: solid;\r\n  border-width: 1px 0 0;\r\n}\r\n.k-grid-header th.k-header:first-child,\r\n.k-grid tbody td:first-child,\r\n.k-grid tfoot td:first-child,\r\n.k-filter-row > th:first-child {\r\n  border-left-width: 0;\r\n}\r\n.k-grid-header th.k-header.k-first {\r\n  border-left-width: 1px;\r\n}\r\n.k-grid-toolbar:first-child,\r\n.k-grouping-header + .k-grid-toolbar {\r\n  border-width: 0 0 1px;\r\n}\r\n/* Grid :: footer */\r\n.k-footer-template td {\r\n  border-style: solid;\r\n  border-width: 1px 0 0 1px;\r\n}\r\n.k-group-footer td {\r\n  border-style: solid;\r\n  border-width: 1px 0;\r\n}\r\n.k-group-footer .k-group-cell + td {\r\n  border-left-width: 1px;\r\n}\r\n.k-grid-footer {\r\n  border-style: solid;\r\n  border-width: 1px 0 0;\r\n}\r\n.k-grid-footer td {\r\n  border-top-width: 0;\r\n}\r\n.k-grid-footer > td {\r\n  border-top-width: 1px;\r\n}\r\n/* Grid :: paging */\r\n.k-pager-wrap {\r\n  clear: both;\r\n  overflow: hidden;\r\n  position: relative;\r\n  border-style: solid;\r\n  border-width: 1px;\r\n  line-height: 2.0em;\r\n  padding: 0.333em 0 0.333em 0.250em;\r\n}\r\n.k-grid-pager {\r\n  border-width: 1px 0 0;\r\n}\r\n.k-grid .k-pager-numbers,\r\n.k-pager-numbers .k-link,\r\n.k-pager-numbers .k-state-selected {\r\n  display: inline-block;\r\n  vertical-align: top;\r\n  margin-right: 1px;\r\n}\r\n.k-pager-numbers {\r\n  margin: 0 2px;\r\n}\r\n.k-pager-numbers .k-state-selected {\r\n  vertical-align: top;\r\n}\r\n.k-pager-numbers .k-current-page {\r\n  display: none;\r\n}\r\n.k-pager-numbers li,\r\n.k-pager-input {\r\n  float: left;\r\n}\r\n.k-grid .k-pager-numbers {\r\n  float: left;\r\n  cursor: default;\r\n}\r\n.k-widget.k-grid .k-pager-numbers {\r\n  position: relative;\r\n}\r\n.k-pager-info {\r\n  float: right;\r\n  padding: 0 1.333em;\r\n}\r\n.k-pager-numbers .k-link {\r\n  text-decoration: none;\r\n}\r\n.k-pager-wrap > .k-link,\r\n.k-pager-numbers .k-link,\r\n.k-pager-numbers .k-state-selected {\r\n  min-width: 2em;\r\n}\r\n.k-pager-wrap > .k-link {\r\n  float: left;\r\n  margin: 0 0.08333em;\r\n  height: 2em;\r\n  /*IE7*/\r\n  line-height: 2em;\r\n  /*IE7*/\r\n  border-radius: 1.0833em;\r\n  cursor: pointer;\r\n  text-align: center;\r\n}\r\n.k-pager-wrap > a.k-state-disabled:hover {\r\n  background: none;\r\n  cursor: default;\r\n}\r\n.k-pager-numbers .k-link {\r\n  text-align: center;\r\n  line-height: 2em;\r\n  border-style: solid;\r\n  border-width: 1px;\r\n  border-radius: 1.0833em;\r\n}\r\n.k-pager-wrap > .k-link {\r\n  border-style: solid;\r\n  border-width: 1px;\r\n}\r\n.k-pager-wrap .k-pager-refresh {\r\n  float: right;\r\n  margin-right: 0.5em;\r\n  border-width: 0;\r\n  border-radius: 0;\r\n}\r\n.k-pager-numbers .k-state-selected {\r\n  border-style: solid;\r\n  border-width: 1px;\r\n  text-align: center;\r\n  border-radius: 1.0833em;\r\n}\r\n.k-pager-wrap .k-textbox {\r\n  width: 3.333em;\r\n}\r\n.k-pager-wrap .k-dropdown {\r\n  width: 4.500em;\r\n}\r\n.k-pager-refresh {\r\n  float: right;\r\n}\r\n.k-pager-input,\r\n.k-pager-sizes {\r\n  padding: 0 1.4166em;\r\n}\r\n.k-pager-sizes {\r\n  display: inline-block;\r\n  padding-top: 1px;\r\n}\r\n.k-pager-sizes .k-widget.k-dropdown {\r\n  margin-top: -2px;\r\n}\r\n.k-pager-wrap .k-textbox,\r\n.k-pager-wrap .k-widget {\r\n  margin: 0 .4em 0;\r\n}\r\n/* Grid :: filtering */\r\n.k-header > .k-grid-filter,\r\n.k-header > .k-header-column-menu {\r\n  float: right;\r\n  margin: -0.5em -0.6em -0.4em;\r\n  padding: .5em .2em .4em;\r\n  position: relative;\r\n  z-index: 1;\r\n  /*mvc site.css*/\r\n}\r\n.k-grid .k-animation-container {\r\n  position: absolute;\r\n}\r\n.k-filter-menu {\r\n  padding: .5em;\r\n}\r\n.k-list-filter {\r\n  display: block;\r\n}\r\nform.k-filter-menu .k-widget,\r\nform.k-filter-menu .k-textbox {\r\n  display: block;\r\n}\r\nform.k-filter-menu .k-textbox {\r\n  width: 100%;\r\n  margin-bottom: 3px;\r\n}\r\n.k-filter-help-text,\r\n.k-filter-menu .k-widget,\r\n.k-filter-menu .k-textbox {\r\n  margin: .19em 0 0;\r\n}\r\n.k-filter-menu span.k-filter-and {\r\n  width: 6em;\r\n  margin: .5em 0 .5em;\r\n}\r\n.k-filter-menu .k-button {\r\n  width: 48%;\r\n  margin: .5em 4% 0 0;\r\n}\r\n.k-filter-menu .k-button + .k-button {\r\n  margin-right: 0;\r\n}\r\n.k-filter-menu .k-filter-selected-items {\r\n  font-weight: bold;\r\n  margin: .5em;\r\n}\r\n.k-multicheck-wrap {\r\n  overflow: auto;\r\n  overflow-x: hidden;\r\n  white-space: nowrap;\r\n  max-height: 300px;\r\n}\r\n.k-multicheck-wrap .k-item {\r\n  line-height: 2.2em;\r\n}\r\n/* Grid :: grouping */\r\n.k-grouping-row .k-icon {\r\n  margin: -3px 4px 0 2px;\r\n}\r\n.k-grouping-row p {\r\n  display: inline-block;\r\n  vertical-align: middle;\r\n  margin-left: -0.6em;\r\n  padding: 0 .6em;\r\n}\r\n.k-grouping-row + tr td {\r\n  border-top-width: 1px;\r\n}\r\n.k-grouping-row .k-group-cell,\r\n.k-grouping-row + tr .k-group-cell {\r\n  border-top-width: 0;\r\n  text-overflow: none;\r\n}\r\n.k-grid .k-hierarchy-cell + td {\r\n  border-left-width: 0;\r\n}\r\n.k-grid .k-group-col,\r\n.k-grid .k-hierarchy-col {\r\n  width: 27px;\r\n}\r\n.k-grouping-header {\r\n  border-bottom-style: solid;\r\n  border-bottom-width: 1px;\r\n}\r\n.k-grouping-header {\r\n  line-height: 2;\r\n}\r\n.k-grouping-dropclue {\r\n  position: absolute;\r\n  width: 6px;\r\n  height: 25px;\r\n  background-repeat: no-repeat;\r\n  background-position: -165px -148px;\r\n}\r\n.k-grouping-header .k-group-indicator {\r\n  display: inline-block;\r\n  border-style: solid;\r\n  border-width: 1px;\r\n  margin: 0 3px;\r\n  padding: .15em .15em .15em .4em;\r\n  line-height: 1.5em;\r\n}\r\n.k-grouping-header .k-link {\r\n  display: inline-block;\r\n  border-width: 0;\r\n  padding: 0;\r\n  line-height: normal;\r\n  text-decoration: none;\r\n}\r\n.k-grouping-header .k-button {\r\n  border: 0;\r\n  padding: 0;\r\n  background: transparent;\r\n  line-height: 1;\r\n}\r\n.k-grouping-header .k-link .k-icon {\r\n  margin: 0 0 0 -3px;\r\n}\r\n.k-grouping-header .k-button .k-icon {\r\n  margin: 0 0 0 3px;\r\n}\r\n.k-grouping-header a,\r\n.k-grouping-header .k-button {\r\n  display: inline-block;\r\n  vertical-align: middle;\r\n}\r\n/* Grid :: editing */\r\n.k-dirty-cell:before {\r\n  content: \"\\a0\";\r\n  display: inline-block;\r\n  width: 0;\r\n  float: left;\r\n}\r\n.k-dirty {\r\n  position: absolute;\r\n  width: 0;\r\n  height: 0;\r\n  border-style: solid;\r\n  border-width: 3px;\r\n  border-color: #f00 transparent transparent #f00;\r\n  margin: -0.45em 0 0 -0.6em;\r\n  padding: 0;\r\n  overflow: hidden;\r\n  vertical-align: top;\r\n}\r\n.k-grouping-header,\r\n.k-grid-toolbar {\r\n  margin: 0;\r\n  padding: 0.429em 0.2em 0.429em 1em;\r\n  cursor: default;\r\n}\r\n.k-grid .k-edit-container {\r\n  padding: 0;\r\n}\r\n.k-grid .field-validation-error {\r\n  display: block;\r\n}\r\n.k-grid .input-validation-error {\r\n  border-style: ridge;\r\n  border-color: #f00;\r\n  background-color: #ffc0cb;\r\n}\r\n.k-grid-toolbar .k-button {\r\n  vertical-align: middle;\r\n}\r\n.k-grid-actions {\r\n  display: inline-block;\r\n}\r\n.k-grid .k-button {\r\n  margin: 0 .16em;\r\n}\r\n.k-grid tbody .k-button,\r\n.k-ie8 .k-grid tbody button.k-button {\r\n  min-width: 64px;\r\n}\r\n.k-grid tbody button.k-button {\r\n  min-width: 78px;\r\n  /* for all except IE8 */\r\n}\r\nhtml body .k-grid tbody .k-button-icon {\r\n  width: auto;\r\n  min-width: 0;\r\n}\r\n.k-detail-row {\r\n  position: relative;\r\n}\r\n.k-grid .k-detail-cell {\r\n  overflow: visible;\r\n}\r\n.k-grid .k-edit-cell {\r\n  padding: 0 .3em;\r\n  white-space: nowrap;\r\n}\r\n.k-grid .k-edit-cell .k-tooltip {\r\n  white-space: normal;\r\n}\r\n.k-edit-cell > .k-textbox,\r\n.k-edit-cell > .k-widget,\r\n.k-grid-edit-row > td > .k-textbox,\r\n.k-grid-edit-row > td > .k-widget,\r\n.k-grid-edit-row > td > .text-box {\r\n  width: 100%;\r\n}\r\nhtml .k-edit-cell .k-tooltip,\r\nhtml .k-grid-edit-row .k-tooltip {\r\n  width: auto;\r\n  max-width: 300px;\r\n}\r\n.k-edit-cell input[type=\"checkbox\"] {\r\n  margin-left: .6em;\r\n}\r\n.k-grid tbody td > .k-grid-delete {\r\n  margin-top: -0.2em;\r\n  margin-bottom: -0.2em;\r\n}\r\n/* Grid :: resizing */\r\n.k-grid-resize-indicator {\r\n  position: absolute;\r\n  width: 2px;\r\n  background-color: #aaa;\r\n}\r\n.k-grid-header .k-resize-handle,\r\n.k-grid > .k-resize-handle {\r\n  position: absolute;\r\n  height: 25px;\r\n  cursor: col-resize;\r\n  z-index: 2;\r\n}\r\n.k-marquee {\r\n  position: absolute;\r\n  z-index: 100000;\r\n}\r\n.k-marquee-color,\r\n.k-marquee-text {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n.k-marquee-color {\r\n  filter: alpha(opacity=60);\r\n  opacity: .6;\r\n}\r\n.k-ie9 .k-column-menu {\r\n  width: 160px;\r\n  /*fix flicker on item hover*/\r\n}\r\n.k-ie8 .k-grid-filter,\r\n.k-ie8 .k-header-column-menu {\r\n  font-size: 100%;\r\n  /* Fix small menus in IE8 */\r\n}\r\n.k-column-menu {\r\n  min-width: 160px;\r\n}\r\n.k-column-menu .k-sprite {\r\n  margin-right: 10px;\r\n}\r\n.k-column-menu > .k-menu {\r\n  border-width: 0;\r\n}\r\n.k-column-menu .k-calendar .k-link {\r\n  white-space: normal;\r\n}\r\n.k-columns-item .k-group {\r\n  max-height: 200px;\r\n  overflow: auto;\r\n}\r\n/* Remove Grid scrollbar during built-in export */\r\n.k-pdf-export-shadow .k-grid {\r\n  float: left;\r\n  width: auto !important;\r\n}\r\n/* Remove all sizes and scrolling */\r\n.k-pdf-export-shadow .k-grid,\r\n.k-pdf-export-shadow .k-grid-content,\r\n.k-pdf-export-shadow .k-grid-content-locked {\r\n  height: auto !important;\r\n  overflow: visible;\r\n}\r\n.k-pdf-export-shadow .k-grid-header-locked + .k-grid-header-wrap,\r\n.k-pdf-export-shadow .k-grid-content-locked + .k-grid-content,\r\n.k-pdf-export-shadow .k-grid-footer-locked + .k-grid-footer-wrap {\r\n  width: auto !important;\r\n}\r\n/* Remove empty space reserved above the scrollbar */\r\n.k-pdf-export-shadow .k-grid-header,\r\n.k-pdf-export-shadow .k-grid-footer {\r\n  padding: 0 !important;\r\n}\r\n.k-loading-pdf-mask {\r\n  position: absolute;\r\n  width: 100%;\r\n  height: 100%;\r\n  top: 0;\r\n  left: 0;\r\n  z-index: 100;\r\n}\r\n.k-loading-pdf-mask .k-loading-color {\r\n  filter: alpha(opacity=50);\r\n  opacity: 0.5;\r\n}\r\n.k-loading-pdf-mask .k-loading-pdf-progress {\r\n  margin: auto;\r\n  position: absolute;\r\n  top: 0;\r\n  bottom: 0;\r\n  left: 0;\r\n  right: 0;\r\n}\r\n.k-pdf-export .k-loading-pdf-mask {\r\n  display: none;\r\n}\r\n/* responsive styles  */\r\n.k-autofitting {\r\n  width: auto !important;\r\n  table-layout: auto !important;\r\n}\r\n.k-autofitting th.k-header,\r\n.k-autofitting td {\r\n  white-space: nowrap !important;\r\n}\r\n.k-autofitting .k-detail-row {\r\n  display: none !important;\r\n}\r\n@media only screen and (max-width: 1024px) {\r\n  .k-webkit .k-pager-wrap,\r\n  .k-ff .k-pager-wrap,\r\n  .k-ie11 .k-pager-wrap,\r\n  .k-safari .k-pager-wrap {\r\n    overflow: visible;\r\n    min-height: 2.1em;\r\n  }\r\n  .k-webkit .k-pager-wrap .k-pager-nav,\r\n  .k-ff .k-pager-wrap .k-pager-nav,\r\n  .k-ie11 .k-pager-wrap .k-pager-nav,\r\n  .k-safari .k-pager-wrap .k-pager-nav,\r\n  .k-webkit .k-pager-input,\r\n  .k-ff .k-pager-input,\r\n  .k-ie11 .k-pager-input,\r\n  .k-safari .k-pager-input {\r\n    display: inline-block;\r\n    vertical-align: top;\r\n  }\r\n  .k-webkit .k-pager-numbers,\r\n  .k-ff .k-pager-numbers,\r\n  .k-ie11 .k-pager-numbers,\r\n  .k-safari .k-pager-numbers,\r\n  .k-webkit .k-grid .k-pager-numbers,\r\n  .k-ff .k-grid .k-pager-numbers,\r\n  .k-ie11 .k-grid .k-pager-numbers,\r\n  .k-safari .k-grid .k-pager-numbers {\r\n    position: absolute;\r\n    left: 4.8em;\r\n    display: -webkit-inline-box;\r\n    display: -webkit-inline-flex;\r\n    display: -ms-inline-flexbox;\r\n    display: inline-flex;\r\n    -webkit-box-orient: vertical;\r\n    -webkit-box-direction: reverse;\r\n    -webkit-flex-direction: column-reverse;\r\n        -ms-flex-direction: column-reverse;\r\n            flex-direction: column-reverse;\r\n    overflow: visible;\r\n    height: auto;\r\n  }\r\n  .k-webkit .k-pager-numbers:first-child,\r\n  .k-ff .k-pager-numbers:first-child,\r\n  .k-ie11 .k-pager-numbers:first-child,\r\n  .k-safari .k-pager-numbers:first-child,\r\n  .k-webkit .k-grid .k-pager-numbers:first-child,\r\n  .k-ff .k-grid .k-pager-numbers:first-child,\r\n  .k-ie11 .k-grid .k-pager-numbers:first-child,\r\n  .k-safari .k-grid .k-pager-numbers:first-child {\r\n    left: .2em;\r\n  }\r\n  .k-webkit .k-pager-numbers.k-state-expanded,\r\n  .k-ff .k-pager-numbers.k-state-expanded,\r\n  .k-ie11 .k-pager-numbers.k-state-expanded,\r\n  .k-safari .k-pager-numbers.k-state-expanded,\r\n  .k-webkit .k-grid .k-pager-numbers.k-state-expanded,\r\n  .k-ff .k-grid .k-pager-numbers.k-state-expanded,\r\n  .k-ie11 .k-grid .k-pager-numbers.k-state-expanded,\r\n  .k-safari .k-grid .k-pager-numbers.k-state-expanded {\r\n    -ms-transform: translatey(-100%);\r\n        transform: translatey(-100%);\r\n    -webkit-transform: translatey(-100%);\r\n  }\r\n  .k-webkit .km-pane-wrapper .k-pager-numbers,\r\n  .k-ff .km-pane-wrapper .k-pager-numbers,\r\n  .k-ie11 .km-pane-wrapper .k-pager-numbers,\r\n  .k-safari .km-pane-wrapper .k-pager-numbers,\r\n  .k-webkit .km-pane-wrapper .k-grid .k-pager-numbers,\r\n  .k-ff .km-pane-wrapper .k-grid .k-pager-numbers,\r\n  .k-ie11 .km-pane-wrapper .k-grid .k-pager-numbers,\r\n  .k-safari .km-pane-wrapper .k-grid .k-pager-numbers {\r\n    position: relative;\r\n    left: 50%;\r\n    -ms-transform: translate(-50%, -100%);\r\n        transform: translate(-50%, -100%);\r\n    -webkit-transform: translate(-50%, -100%);\r\n  }\r\n  .k-webkit .km-pane-wrapper .k-pager-numbers .k-link,\r\n  .k-ff .km-pane-wrapper .k-pager-numbers .k-link,\r\n  .k-ie11 .km-pane-wrapper .k-pager-numbers .k-link,\r\n  .k-safari .km-pane-wrapper .k-pager-numbers .k-link,\r\n  .k-webkit .km-pane-wrapper .k-pager-numbers .k-state-selected,\r\n  .k-ff .km-pane-wrapper .k-pager-numbers .k-state-selected,\r\n  .k-ie11 .km-pane-wrapper .k-pager-numbers .k-state-selected,\r\n  .k-safari .km-pane-wrapper .k-pager-numbers .k-state-selected,\r\n  .k-webkit .km-pane-wrapper .k-pager-wrap > .k-link,\r\n  .k-ff .km-pane-wrapper .k-pager-wrap > .k-link,\r\n  .k-ie11 .km-pane-wrapper .k-pager-wrap > .k-link,\r\n  .k-safari .km-pane-wrapper .k-pager-wrap > .k-link,\r\n  .k-webkit .km-pane-wrapper .k-pager-wrap > .k-pager-info,\r\n  .k-ff .km-pane-wrapper .k-pager-wrap > .k-pager-info,\r\n  .k-ie11 .km-pane-wrapper .k-pager-wrap > .k-pager-info,\r\n  .k-safari .km-pane-wrapper .k-pager-wrap > .k-pager-info {\r\n    padding-top: 0;\r\n    padding-bottom: 0;\r\n  }\r\n  .k-webkit .k-rtl .k-pager-numbers,\r\n  .k-ff .k-rtl .k-pager-numbers,\r\n  .k-ie11 .k-rtl .k-pager-numbers,\r\n  .k-safari .k-rtl .k-pager-numbers,\r\n  .k-webkit .k-rtl .k-grid .k-pager-numbers,\r\n  .k-ff .k-rtl .k-grid .k-pager-numbers,\r\n  .k-ie11 .k-rtl .k-grid .k-pager-numbers,\r\n  .k-safari .k-rtl .k-grid .k-pager-numbers {\r\n    left: auto;\r\n    right: 4.8em;\r\n    width: 4.5em;\r\n  }\r\n  .k-webkit .k-rtl .k-pager-numbers:first-child,\r\n  .k-ff .k-rtl .k-pager-numbers:first-child,\r\n  .k-ie11 .k-rtl .k-pager-numbers:first-child,\r\n  .k-safari .k-rtl .k-pager-numbers:first-child,\r\n  .k-webkit .k-rtl .k-grid .k-pager-numbers:first-child,\r\n  .k-ff .k-rtl .k-grid .k-pager-numbers:first-child,\r\n  .k-ie11 .k-rtl .k-grid .k-pager-numbers:first-child,\r\n  .k-safari .k-rtl .k-grid .k-pager-numbers:first-child {\r\n    left: auto;\r\n    right: .2em;\r\n  }\r\n  .k-webkit .k-rtl .km-pane-wrapper .k-pager-numbers,\r\n  .k-ff .k-rtl .km-pane-wrapper .k-pager-numbers,\r\n  .k-ie11 .k-rtl .km-pane-wrapper .k-pager-numbers,\r\n  .k-safari .k-rtl .km-pane-wrapper .k-pager-numbers,\r\n  .k-webkit .k-rtl .km-pane-wrapper .k-grid .k-pager-numbers,\r\n  .k-ff .k-rtl .km-pane-wrapper .k-grid .k-pager-numbers,\r\n  .k-ie11 .k-rtl .km-pane-wrapper .k-grid .k-pager-numbers,\r\n  .k-safari .k-rtl .km-pane-wrapper .k-grid .k-pager-numbers {\r\n    right: 5.8em;\r\n  }\r\n  .k-webkit .k-pager-numbers .k-current-page,\r\n  .k-ff .k-pager-numbers .k-current-page,\r\n  .k-ie11 .k-pager-numbers .k-current-page,\r\n  .k-safari .k-pager-numbers .k-current-page,\r\n  .k-webkit .k-grid .k-pager-numbers .k-current-page,\r\n  .k-ff .k-grid .k-pager-numbers .k-current-page,\r\n  .k-ie11 .k-grid .k-pager-numbers .k-current-page,\r\n  .k-safari .k-grid .k-pager-numbers .k-current-page {\r\n    display: block;\r\n    border-left: 0;\r\n  }\r\n  .k-webkit .k-pager-numbers.k-state-expanded .k-current-page,\r\n  .k-ff .k-pager-numbers.k-state-expanded .k-current-page,\r\n  .k-ie11 .k-pager-numbers.k-state-expanded .k-current-page,\r\n  .k-safari .k-pager-numbers.k-state-expanded .k-current-page,\r\n  .k-webkit .k-grid .k-pager-number.k-state-expanded .k-current-page,\r\n  .k-ff .k-grid .k-pager-number.k-state-expanded .k-current-page,\r\n  .k-ie11 .k-grid .k-pager-number.k-state-expanded .k-current-page,\r\n  .k-safari .k-grid .k-pager-number.k-state-expanded .k-current-page {\r\n    -ms-transform: translatey(100%);\r\n        transform: translatey(100%);\r\n    -webkit-transform: translatey(100%);\r\n  }\r\n  .k-webkit .k-pager-numbers li:not(.k-current-page),\r\n  .k-ff .k-pager-numbers li:not(.k-current-page),\r\n  .k-ie11 .k-pager-numbers li:not(.k-current-page),\r\n  .k-safari .k-pager-numbers li:not(.k-current-page) {\r\n    display: none;\r\n  }\r\n  .k-webkit .k-pager-numbers .k-current-page .k-link,\r\n  .k-ff .k-pager-numbers .k-current-page .k-link,\r\n  .k-ie11 .k-pager-numbers .k-current-page .k-link,\r\n  .k-safari .k-pager-numbers .k-current-page .k-link {\r\n    width: 3.2em;\r\n    padding: 0 .429em 0 .714em;\r\n    border-radius: 1.0833em;\r\n  }\r\n  .k-webkit .k-pager-numbers + .k-link,\r\n  .k-ff .k-pager-numbers + .k-link,\r\n  .k-ie11 .k-pager-numbers + .k-link,\r\n  .k-safari .k-pager-numbers + .k-link {\r\n    margin-left: 4.8em;\r\n  }\r\n  .k-webkit .k-rtl .k-pager-numbers + .k-link,\r\n  .k-ff .k-rtl .k-pager-numbers + .k-link,\r\n  .k-ie11 .k-rtl .k-pager-numbers + .k-link,\r\n  .k-safari .k-rtl .k-pager-numbers + .k-link {\r\n    margin-right: 5.1em;\r\n    margin-left: 0;\r\n  }\r\n  .k-webkit .k-pager-numbers .k-state-selected,\r\n  .k-ff .k-pager-numbers .k-state-selected,\r\n  .k-ie11 .k-pager-numbers .k-state-selected,\r\n  .k-safari .k-pager-numbers .k-state-selected,\r\n  .k-webkit .k-pager-numbers .k-link,\r\n  .k-ff .k-pager-numbers .k-link,\r\n  .k-ie11 .k-pager-numbers .k-link,\r\n  .k-safari .k-pager-numbers .k-link {\r\n    display: block;\r\n    margin-right: 0;\r\n    padding: 1px 5px 1px 5px;\r\n    text-align: left;\r\n  }\r\n  .k-webkit .k-pager-numbers.k-state-expanded,\r\n  .k-ff .k-pager-numbers.k-state-expanded,\r\n  .k-ie11 .k-pager-numbers.k-state-expanded,\r\n  .k-safari .k-pager-numbers.k-state-expanded {\r\n    -webkit-box-sizing: border-box;\r\n            box-sizing: border-box;\r\n    padding: 2px 2px 0;\r\n  }\r\n  .k-webkit .k-pager-numbers.k-state-expanded .k-current-page,\r\n  .k-ff .k-pager-numbers.k-state-expanded .k-current-page,\r\n  .k-ie11 .k-pager-numbers.k-state-expanded .k-current-page,\r\n  .k-safari .k-pager-numbers.k-state-expanded .k-current-page {\r\n    margin: -2em -3px 0;\r\n    padding: 0;\r\n  }\r\n  .k-webkit .k-pager-numbers.k-state-expanded .k-current-page .k-link,\r\n  .k-ff .k-pager-numbers.k-state-expanded .k-current-page .k-link,\r\n  .k-ie11 .k-pager-numbers.k-state-expanded .k-current-page .k-link,\r\n  .k-safari .k-pager-numbers.k-state-expanded .k-current-page .k-link {\r\n    border-radius: 0 0 1.0833em 1.0833em;\r\n  }\r\n  .k-webkit .k-pager-numbers.k-state-expanded li,\r\n  .k-ff .k-pager-numbers.k-state-expanded li,\r\n  .k-ie11 .k-pager-numbers.k-state-expanded li,\r\n  .k-safari .k-pager-numbers.k-state-expanded li {\r\n    display: inline-block;\r\n  }\r\n}\r\n@media only screen and (max-width: 640px) {\r\n  .k-webkit .k-pager-info,\r\n  .k-ff .k-pager-info,\r\n  .k-ie11 .k-pager-info,\r\n  .k-safari .k-pager-info {\r\n    display: none;\r\n  }\r\n}\r\n@media only screen and (max-width: 480px) {\r\n  .k-webkit .k-pager-sizes,\r\n  .k-ff .k-pager-sizes,\r\n  .k-ie11 .k-pager-sizes,\r\n  .k-safari .k-pager-sizes {\r\n    display: none;\r\n  }\r\n}\r\n.k-treelist .k-status {\r\n  padding: .4em .6em;\r\n  line-height: 1.6em;\r\n}\r\n.k-treelist .k-status .k-loading {\r\n  vertical-align: baseline;\r\n  margin-right: 5px;\r\n}\r\n.k-treelist tr.k-hidden {\r\n  display: none;\r\n}\r\n.k-treelist.k-treelist-dragging,\r\n.k-treelist.k-treelist-dragging .k-state-hover {\r\n  cursor: default;\r\n}\r\n.k-treelist .k-drop-hint {\r\n  position: absolute;\r\n  z-index: 10000;\r\n  visibility: hidden;\r\n  width: 80px;\r\n  height: 5px;\r\n  margin-top: -3px;\r\n  background-color: transparent;\r\n  background-repeat: no-repeat;\r\n}\r\n.k-drag-separator {\r\n  display: inline-block;\r\n  border-right: 1px solid;\r\n  height: 1em;\r\n  vertical-align: top;\r\n  margin: 0 .5em;\r\n}\r\n/* Gantt Chart start */\r\n/* Gantt Main Layout */\r\n.k-gantt {\r\n  white-space: nowrap;\r\n  position: relative;\r\n}\r\n.k-gantt-layout {\r\n  display: inline-block;\r\n  white-space: normal;\r\n  vertical-align: top;\r\n}\r\n.k-gantt .k-splitbar {\r\n  position: relative;\r\n  cursor: e-resize;\r\n  width: 5px;\r\n  border-width: 0 1px;\r\n  background-repeat: repeat-y;\r\n}\r\n.k-gantt .k-gantt-layout th {\r\n  vertical-align: bottom;\r\n}\r\n.k-gantt td {\r\n  overflow: hidden;\r\n  white-space: nowrap;\r\n  vertical-align: top;\r\n}\r\n.k-gantt .k-grid .k-edit-cell {\r\n  vertical-align: middle;\r\n}\r\n.k-gantt-treelist > .k-treelist,\r\n.k-gantt-timeline > .k-timeline {\r\n  border-width: 0;\r\n  height: 100%;\r\n}\r\n/* Gantt Toolbar, footer */\r\n.k-gantt-toolbar {\r\n  border-style: solid;\r\n  border-width: 0 0 1px;\r\n  line-height: 2.4em;\r\n  padding: .5em;\r\n}\r\n.k-gantt-layout + .k-gantt-toolbar {\r\n  border-width: 1px 0 0;\r\n}\r\n.k-gantt-actions,\r\n.k-gantt-toolbar > ul {\r\n  float: left;\r\n  margin-right: .6em;\r\n}\r\n.k-gantt-actions > .k-button {\r\n  margin-right: .5em;\r\n  vertical-align: top;\r\n}\r\n.k-gantt-toolbar > .k-gantt-views {\r\n  float: right;\r\n  margin-right: 0;\r\n}\r\n.k-gantt-toolbar > .k-gantt-views > li.k-current-view {\r\n  display: none;\r\n}\r\n.k-gantt-toolbar > ul > li {\r\n  display: inline-block;\r\n  border-style: solid;\r\n  border-width: 1px 1px 1px 0;\r\n}\r\n.k-gantt-toolbar > ul > li:first-child + li {\r\n  border-left-width: 1px;\r\n}\r\n.k-gantt-toolbar .k-link {\r\n  display: inline-block;\r\n  padding: 0 1.1em;\r\n}\r\n.k-gantt-toolbar li:first-child + li,\r\n.k-gantt-toolbar li:first-child + li > .k-link {\r\n  border-top-left-radius: 4px;\r\n  border-bottom-left-radius: 4px;\r\n}\r\n.k-gantt-toolbar li:last-child,\r\n.k-gantt-toolbar li:last-child > .k-link {\r\n  border-top-right-radius: 4px;\r\n  border-bottom-right-radius: 4px;\r\n}\r\n.k-gantt-toolbar li.k-button {\r\n  line-height: inherit;\r\n  padding-top: 0;\r\n  padding-bottom: 0;\r\n}\r\n/* Gantt TreeList */\r\n.k-gantt-treelist .k-grid-header tr {\r\n  height: 5em;\r\n}\r\n.k-gantt .k-treelist .k-grid-header {\r\n  padding: 0 !important;\r\n}\r\n.k-gantt .k-treelist .k-grid-content {\r\n  overflow-y: hidden;\r\n  overflow-x: scroll;\r\n}\r\n.k-treelist-group > tr > span {\r\n  font-weight: bold;\r\n}\r\n.k-treelist-group .k-widget {\r\n  font-weight: normal;\r\n}\r\n/* Gantt TimeLine */\r\n.k-gantt-timeline .k-grid-header tr {\r\n  height: 2.5em;\r\n}\r\n.k-gantt-tasks {\r\n  /*needed for RTL*/\r\n  position: relative;\r\n}\r\n.k-gantt-rows tr,\r\n.k-gantt-tasks tr,\r\n.k-gantt .k-grid-content tr {\r\n  height: 2.3em;\r\n}\r\n.k-gantt .k-gantt-tasks td:after {\r\n  content: \"\\a0\";\r\n}\r\n.k-gantt-timeline {\r\n  background: transparent;\r\n}\r\n.k-gantt-rows,\r\n.k-gantt-columns,\r\n.k-gantt-dependencies {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n}\r\n.k-gantt-tables {\r\n  position: relative;\r\n}\r\n.k-gantt .k-timeline .k-grid-content {\r\n  overflow-x: scroll;\r\n}\r\n.k-gantt .k-gantt-timeline th {\r\n  text-align: center;\r\n}\r\n.k-gantt .k-gantt-timeline tr:first-child th {\r\n  border-bottom-width: 1px;\r\n}\r\n/* Gantt TimeLine objects */\r\n/* Summary */\r\n.k-task-summary {\r\n  height: 10px;\r\n  display: inline-block;\r\n  vertical-align: top;\r\n  margin-top: 3px;\r\n}\r\n.k-task-summary-complete {\r\n  height: 10px;\r\n  position: relative;\r\n  z-index: 2;\r\n}\r\n.k-task-summary-progress {\r\n  height: 15px;\r\n  overflow: hidden;\r\n}\r\n.k-task-summary:before,\r\n.k-task-summary-complete:before,\r\n.k-task-summary:after,\r\n.k-task-summary-complete:after {\r\n  content: \"\";\r\n  position: absolute;\r\n  top: 0;\r\n  width: 0;\r\n  height: 0;\r\n  border-style: solid;\r\n  border-width: 8px;\r\n  border-color: transparent;\r\n}\r\n.k-task-summary:before,\r\n.k-task-summary-complete:before {\r\n  left: 0;\r\n  border-left-color: inherit;\r\n}\r\n.k-task-summary:after,\r\n.k-task-summary-complete:after {\r\n  right: 0;\r\n  border-right-color: inherit;\r\n}\r\n/* Lines */\r\n.k-line-h,\r\n.k-line-v {\r\n  position: absolute;\r\n}\r\n.k-line-h {\r\n  height: 2px;\r\n}\r\n.k-line-v {\r\n  width: 2px;\r\n}\r\n.k-arrow-e,\r\n.k-arrow-w {\r\n  position: absolute;\r\n  top: -4px;\r\n  width: 0;\r\n  height: 0;\r\n  border-style: solid;\r\n  border-width: 5px;\r\n}\r\n.k-arrow-e {\r\n  right: -6px;\r\n  border-top-color: transparent;\r\n  border-bottom-color: transparent;\r\n  border-right-color: transparent;\r\n}\r\n.k-arrow-w {\r\n  left: -6px;\r\n  border-top-color: transparent;\r\n  border-bottom-color: transparent;\r\n  border-left-color: transparent;\r\n}\r\n/* Milestone */\r\n.k-task-milestone {\r\n  width: 13px;\r\n  height: 13px;\r\n  margin-top: 3px;\r\n  border-style: solid;\r\n  border-width: 1px;\r\n  -webkit-transform: rotate(45deg);\r\n      -ms-transform: rotate(45deg);\r\n          transform: rotate(45deg);\r\n}\r\n.k-ie8 .k-task-milestone {\r\n  margin-left: 1px;\r\n}\r\n/* Button */\r\n.k-gantt .k-gantt-treelist .k-button,\r\n.k-gantt .k-gantt-tasks .k-button-icon {\r\n  padding-top: 0;\r\n  padding-bottom: 0;\r\n}\r\n.k-gantt .k-gantt-tasks .k-button-icon {\r\n  margin-top: 4px;\r\n}\r\n.k-gantt .k-gantt-treelist .k-button {\r\n  margin-top: -4px;\r\n  margin-bottom: -2px;\r\n}\r\n.k-gantt .k-gantt-tasks .k-button-icon {\r\n  padding-left: 2px;\r\n  padding-right: 2px;\r\n}\r\n.k-gantt .k-gantt-treelist .k-button .k-icon,\r\n.k-gantt .k-gantt-tasks .k-button .k-icon {\r\n  vertical-align: text-top;\r\n}\r\n.k-rel .k-button-icon {\r\n  position: absolute;\r\n  left: 200px;\r\n}\r\n/* Tasks */\r\n.k-rel {\r\n  position: relative;\r\n  height: 0;\r\n  top: -0.3em;\r\n}\r\n.k-task-wrap {\r\n  position: absolute;\r\n  padding: 0 23px 5px;\r\n  margin: -1px -23px 0;\r\n  z-index: 2;\r\n}\r\n.k-task-wrap:hover,\r\n.k-line.k-state-selected {\r\n  z-index: 3;\r\n}\r\n.k-milestone-wrap {\r\n  margin: 0 -13px 0 -27px;\r\n}\r\n.k-task-content {\r\n  position: relative;\r\n  z-index: 2;\r\n}\r\n.k-task-complete {\r\n  position: absolute;\r\n  top: 0;\r\n  bottom: 0;\r\n  left: 0;\r\n  width: 20%;\r\n  z-index: 1;\r\n}\r\n.k-task-dot {\r\n  position: absolute;\r\n  top: 0;\r\n  width: 16px;\r\n  height: 16px;\r\n  line-height: 16px;\r\n  display: none;\r\n  cursor: pointer;\r\n}\r\n.k-task-dot.k-state-hover {\r\n  background-color: transparent;\r\n}\r\n.k-task-single + .k-task-dot,\r\n.k-task-single + .k-task-dot + .k-task-dot {\r\n  top: .2em;\r\n}\r\n.k-task-wrap:hover .k-task-dot,\r\n.k-task-wrap-active .k-task-dot {\r\n  display: block;\r\n}\r\n.k-task-dot:before {\r\n  content: \"\\a0\";\r\n  display: inline-block;\r\n  width: 0;\r\n  height: 16px;\r\n}\r\n.k-task-dot:after {\r\n  content: \"\";\r\n  display: inline-block;\r\n  vertical-align: middle;\r\n  width: 8px;\r\n  height: 8px;\r\n  border-radius: 4px;\r\n  margin-left: 4px;\r\n}\r\n.k-task-dot:hover:after,\r\n.k-task-dot.k-state-hover:after,\r\n.k-task-wrap-active .k-task-dot:after {\r\n  border-style: solid;\r\n  border-width: 1px;\r\n  margin-left: 3px;\r\n}\r\n.k-task-start {\r\n  left: 0;\r\n}\r\n.k-task-end {\r\n  right: 0;\r\n}\r\n.k-task-single {\r\n  border-style: solid;\r\n  border-width: 1px;\r\n  text-align: left;\r\n  overflow: hidden;\r\n  cursor: default;\r\n  min-height: 1.3em;\r\n  white-space: nowrap;\r\n}\r\n.k-task-template {\r\n  padding: .2em 1.4em .2em .6em;\r\n  line-height: normal;\r\n}\r\n.k-task-actions,\r\n.k-task-content > .k-link {\r\n  position: absolute;\r\n  top: 0;\r\n  right: 4px;\r\n  white-space: nowrap;\r\n}\r\n.k-task-actions {\r\n  z-index: 1;\r\n}\r\n.k-task-actions:first-child {\r\n  position: static;\r\n  float: left;\r\n  margin: 4px 2px 0 4px;\r\n}\r\n.k-webkit .k-task-actions:first-child {\r\n  margin-top: 3px;\r\n}\r\n.k-task-actions:first-child > .k-link {\r\n  display: inline-block;\r\n}\r\n.k-task-delete {\r\n  display: none;\r\n}\r\n.k-task-wrap:hover .k-task-delete,\r\n.k-task-wrap-active .k-task-delete {\r\n  display: inline-block;\r\n}\r\n.k-task-single .k-resize-handle {\r\n  position: absolute;\r\n  visibility: hidden;\r\n  z-index: 2;\r\n  height: auto;\r\n}\r\n.k-task-single:hover .k-resize-handle,\r\n.k-task-wrap-active .k-resize-handle {\r\n  visibility: visible;\r\n}\r\n.k-task-single .k-resize-handle:after {\r\n  content: \"\";\r\n  position: absolute;\r\n  filter: alpha(opacity=50);\r\n  opacity: .5;\r\n}\r\n.k-task-content > .k-resize-e {\r\n  right: 0;\r\n  top: 0;\r\n  bottom: 0;\r\n  width: .4em;\r\n}\r\n.k-task-content > .k-resize-w {\r\n  left: 0;\r\n  top: 0;\r\n  bottom: 0;\r\n  width: .4em;\r\n}\r\n.k-task-content > .k-resize-e:after,\r\n.k-task-content > .k-resize-w:after {\r\n  left: 1px;\r\n  top: 50%;\r\n  margin-top: -0.7em;\r\n  height: 1.4em;\r\n  width: 1px;\r\n}\r\n.k-task-content > .k-resize-e:after {\r\n  left: auto;\r\n  right: 1px;\r\n}\r\n.k-task-draghandle {\r\n  position: absolute;\r\n  bottom: 0;\r\n  width: 0;\r\n  height: 0;\r\n  margin-left: 16px;\r\n  border-width: 5px;\r\n  border-style: solid;\r\n  border-top-color: transparent;\r\n  border-left-color: transparent;\r\n  border-right-color: transparent;\r\n  display: none;\r\n  cursor: e-resize;\r\n}\r\n.k-task-wrap:hover .k-task-draghandle,\r\n.k-task-wrap-active .k-task-draghandle {\r\n  display: block;\r\n}\r\n.k-dependency-hint {\r\n  z-index: 4;\r\n}\r\n/* Higher row height styles */\r\n.k-gantt-rowheight .k-task-summary,\r\n.k-gantt-rowheight .k-task-milestone,\r\n.k-gantt-rowheight .k-task-dot,\r\n.k-gantt-rowheight .k-task-single + .k-task-dot,\r\n.k-gantt-rowheight .k-task-single + .k-task-dot + .k-task-dot {\r\n  top: 50%;\r\n}\r\n.k-gantt-rowheight .k-task-summary,\r\n.k-gantt-rowheight .k-task-milestone {\r\n  margin-top: -6px;\r\n}\r\n.k-gantt-rowheight .k-task-dot,\r\n.k-gantt-rowheight .k-task-single + .k-task-dot,\r\n.k-gantt-rowheight .k-task-single + .k-task-dot + .k-task-dot {\r\n  margin-top: -11px;\r\n}\r\n.k-gantt-rowheight .k-task-single {\r\n  height: -webkit-calc(98%);\r\n  height: calc(98%);\r\n}\r\n.k-ie .k-gantt-rowheight .k-task-single {\r\n  height: 99%;\r\n}\r\n.k-gantt-rowheight .k-task-content {\r\n  height: 100%;\r\n}\r\n.k-gantt-rowheight .k-task-content > .k-resize-e:after,\r\n.k-gantt-rowheight .k-task-content > .k-resize-w:after {\r\n  top: 0;\r\n  margin-top: 0;\r\n  height: 100%;\r\n}\r\n/*Task Hover Tooltip*/\r\n.k-task-details {\r\n  padding: .4em;\r\n  text-align: left;\r\n  white-space: nowrap;\r\n}\r\n.k-task-details > strong {\r\n  font-size: 120%;\r\n  display: block;\r\n}\r\n.k-task-pct {\r\n  margin: .5em 0 .1em;\r\n  font-size: 170%;\r\n}\r\n.k-task-details > ul {\r\n  line-height: 1.2;\r\n}\r\n/*Resources*/\r\n.k-resources-wrap {\r\n  position: absolute;\r\n  z-index: 2;\r\n  zoom: 1;\r\n  margin-left: 20px;\r\n  margin-top: -2px;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n}\r\n.k-resources-wrap .k-resource {\r\n  margin: 0px 5px;\r\n}\r\n/* Gantt Edit form */\r\n.k-gantt-edit-form > .k-edit-form-container {\r\n  width: 430px;\r\n}\r\n.k-gantt-edit-form > .k-resources-form-container {\r\n  width: 506px;\r\n}\r\n.k-resources-form-container > .k-grid {\r\n  margin: 0 .9em;\r\n}\r\n.k-gantt-edit-form > .k-edit-form-container .k-textbox,\r\n.k-gantt-edit-form > .k-edit-form-container .k-numerictextbox {\r\n  width: 15em;\r\n}\r\n.k-gantt-edit-form .k-edit-buttons .k-gantt-delete {\r\n  float: left;\r\n}\r\n/* Gantt Chart PDF export */\r\n.k-pdf-export-shadow .k-gantt {\r\n  float: left;\r\n}\r\n/* Remove all sizes and scrolling */\r\n.k-pdf-export-shadow .k-gantt,\r\n.k-pdf-export-shadow .k-gantt-timeline,\r\n.k-pdf-export-shadow .k-gantt .k-grid-content {\r\n  width: auto !important;\r\n  height: auto !important;\r\n  overflow: visible !important;\r\n}\r\n.k-pdf-export-shadow .k-gantt-treelist {\r\n  height: auto !important;\r\n  overflow: visible !important;\r\n}\r\n/* Remove empty space reserved above the scrollbar */\r\n.k-pdf-export-shadow .k-gantt-timeline .k-grid-header {\r\n  padding: 0 !important;\r\n}\r\n/* Hide the splitter */\r\n.k-pdf-export-shadow .k-gantt .k-splitbar,\r\n.k-pdf-export-shadow .k-pdf-export {\r\n  display: none;\r\n}\r\n/* Responsive styles  */\r\nbutton.k-gantt-toggle {\r\n  display: none;\r\n  float: left;\r\n  margin-right: .5em;\r\n}\r\n.k-rtl button.k-gantt-toggle {\r\n  float: right;\r\n  margin-right: 0;\r\n  margin-left: .5em;\r\n}\r\n@media only screen and (max-width: 1024px) {\r\n  .k-gantt-toolbar > ul.k-gantt-views {\r\n    position: absolute;\r\n    right: 6px;\r\n    top: 6px;\r\n    z-index: 10000;\r\n  }\r\n  .k-rtl .k-gantt-toolbar > ul.k-gantt-views {\r\n    right: auto;\r\n    left: 6px;\r\n  }\r\n  .k-gantt-toolbar > ul.k-gantt-views > li:not(.k-current-view) {\r\n    display: none;\r\n  }\r\n  .k-gantt-toolbar > ul.k-gantt-views > li.k-current-view {\r\n    display: block;\r\n    border-width: 1px;\r\n  }\r\n  .k-rtl .k-gantt-toolbar > ul.k-gantt-views > li.k-current-view {\r\n    text-align: left;\r\n    padding-left: 1em;\r\n  }\r\n  .k-gantt-toolbar > ul.k-gantt-views > li.k-current-view > .k-link {\r\n    display: block;\r\n    position: relative;\r\n    padding-right: 2.5em;\r\n    padding-left: 1em;\r\n  }\r\n  .k-rtl .k-gantt-toolbar > ul.k-gantt-views > li.k-current-view > .k-link {\r\n    padding-left: 0;\r\n  }\r\n  .k-gantt-toolbar > ul.k-gantt-views > li.k-current-view > .k-link:after {\r\n    display: block;\r\n    content: \"\";\r\n    position: absolute;\r\n    top: 50%;\r\n    margin-top: -0.6em;\r\n    right: 0.333em;\r\n    width: 16px;\r\n    height: 16px;\r\n  }\r\n  .k-gantt-toolbar > ul.k-gantt-views.k-state-expanded > li,\r\n  .k-gantt-toolbar > ul.k-gantt-views.k-state-expanded > li:first-child + li {\r\n    display: block;\r\n    border: 0;\r\n    border-radius: 0;\r\n  }\r\n  .k-gantt-toolbar > ul.k-gantt-views.k-state-expanded {\r\n    border: 1px solid #c5c5c5;\r\n    background-color: #fff;\r\n    background-image: none;\r\n    -webkit-box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.3);\r\n            box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.3);\r\n  }\r\n  .k-rtl .k-gantt-toolbar > ul.k-gantt-views.k-state-expanded {\r\n    text-align: left;\r\n  }\r\n}\r\n@media only screen and (max-width: 480px) {\r\n  button.k-gantt-toggle {\r\n    display: inline-block;\r\n  }\r\n  .k-gantt-create span + span,\r\n  .k-gantt-pdf span + span {\r\n    display: none;\r\n  }\r\n  .k-gantt-create .k-icon,\r\n  .k-gantt-pdf .k-icon {\r\n    margin: 0;\r\n  }\r\n  .k-gantt .k-splitbar {\r\n    display: none;\r\n    width: 0;\r\n    border-width: 0;\r\n  }\r\n  .k-gantt .k-gantt-treelist {\r\n    display: none;\r\n    max-width: 0;\r\n  }\r\n  .k-gantt .k-treelist .k-grid-content {\r\n    overflow-y: scroll;\r\n  }\r\n  .k-gantt .k-gantt-timeline {\r\n    width: 100%;\r\n  }\r\n}\r\n/* Gantt Chart end */\r\n/* Pivot start */\r\n.k-pivot {\r\n  position: relative;\r\n}\r\n.k-pivot-toolbar {\r\n  padding: .2em;\r\n  border-bottom-width: 1px;\r\n  border-bottom-style: solid;\r\n}\r\n.k-pivot .k-pivot-toolbar {\r\n  padding: .6em;\r\n}\r\n.k-pivot-toolbar .k-button {\r\n  margin-right: .4em;\r\n  line-height: 1.2em;\r\n  font-size: .9em;\r\n  text-align: left;\r\n  position: relative;\r\n  padding: .3em 5em .3em .3em;\r\n}\r\n.k-field-actions {\r\n  position: absolute;\r\n  right: 2px;\r\n  top: 3px;\r\n}\r\n/*IE7 requires the following style to be applied to cells directly*/\r\n.k-pivot .k-grid td {\r\n  white-space: nowrap;\r\n}\r\n.k-pivot-layout {\r\n  border-spacing: 0;\r\n  table-layout: auto;\r\n}\r\n.k-pivot-layout > tbody > tr > td {\r\n  vertical-align: top;\r\n  padding: 0;\r\n}\r\n.k-pivot td {\r\n  vertical-align: top;\r\n}\r\n.k-pivot-rowheaders > .k-grid,\r\n.k-pivot-table > .k-grid {\r\n  border-width: 0;\r\n}\r\n.k-pivot-rowheaders > .k-grid td:first-child,\r\n.k-pivot-table .k-grid-header .k-header.k-first {\r\n  border-left-width: 1px;\r\n}\r\n.k-pivot-rowheaders > .k-grid td.k-first {\r\n  border-left-width: 0;\r\n}\r\n.k-pivot-rowheaders > .k-grid {\r\n  overflow: hidden;\r\n}\r\n.k-pivot-table {\r\n  border-left-width: 1px;\r\n  border-left-style: solid;\r\n}\r\n.k-pivot-table .k-grid-header-wrap > table {\r\n  height: 100%;\r\n}\r\n.k-pivot .k-grid-header .k-header {\r\n  vertical-align: top;\r\n}\r\n.k-header.k-alt,\r\ntd.k-alt {\r\n  font-weight: bold;\r\n}\r\n.k-header.k-alt {\r\n  background-image: none;\r\n}\r\n.k-pivot-layout .k-grid td {\r\n  border-bottom-width: 1px;\r\n}\r\n.k-pivot-layout .k-grid-footer > td {\r\n  border-top-width: 0;\r\n}\r\n.k-pivot-filter-window .k-treeview {\r\n  max-height: 600px;\r\n}\r\n/* selector */\r\n.k-fieldselector .k-edit-buttons {\r\n  bottom: auto;\r\n}\r\n.k-fieldselector .k-edit-label {\r\n  width: 16%;\r\n}\r\n.k-fieldselector .k-edit-field {\r\n  width: 77%;\r\n}\r\n.k-fieldselector .k-edit-field > .k-widget,\r\n.k-fieldselector .k-edit-field > .k-textbox {\r\n  width: 99%;\r\n}\r\n.k-fieldselector .k-edit-buttons > input,\r\n.k-fieldselector .k-edit-buttons > label {\r\n  float: left;\r\n  margin-top: .4em;\r\n}\r\n.k-fieldselector p {\r\n  margin: 0 0 .2em .5em;\r\n  text-transform: uppercase;\r\n}\r\n.k-fieldselector p .k-icon {\r\n  margin: 0 5px 0 0;\r\n}\r\n.k-fieldselector .k-columns {\r\n  border-style: solid;\r\n  border-width: 0;\r\n}\r\n.k-fieldselector .k-columns > div {\r\n  overflow: auto;\r\n  padding: .6em;\r\n  border-style: solid;\r\n  border-width: 0 0 0 1px;\r\n  float: left;\r\n  width: 45%;\r\n}\r\n.k-fieldselector .k-columns > div:first-child {\r\n  border-width: 0;\r\n  margin-right: -1px;\r\n}\r\n.k-fieldselector .k-columns > div + div {\r\n  float: right;\r\n  border-width: 0;\r\n}\r\n.k-fieldselector div.k-treeview {\r\n  border-width: 0;\r\n  margin-right: -1px;\r\n  padding-left: 4px;\r\n  overflow: visible;\r\n}\r\n.k-fieldselector .k-list-container {\r\n  margin-left: .5em;\r\n  margin-bottom: 1em;\r\n  padding: .2em 0 0;\r\n  border-style: solid;\r\n  border-width: 1px;\r\n}\r\n.k-fieldselector .k-list {\r\n  padding-bottom: 2em;\r\n}\r\n.k-fieldselector .k-list li.k-item {\r\n  padding: .3em 3.3em .3em .3em;\r\n  margin: 0 .2em.2em;\r\n  position: relative;\r\n  font-size: .9em;\r\n  line-height: 1.2em;\r\n  min-height: 1em;\r\n}\r\n/* KPI icons */\r\n.k-i-kpi-decrease {\r\n  background-position: 0 0;\r\n}\r\n.k-i-kpi-denied {\r\n  background-position: -16px 0;\r\n}\r\n.k-i-kpi-equal {\r\n  background-position: -32px 0;\r\n}\r\n.k-i-kpi-hold {\r\n  background-position: -48px 0;\r\n}\r\n.k-i-kpi-increase {\r\n  background-position: -64px 0;\r\n}\r\n.k-i-kpi-open {\r\n  background-position: -80px 0;\r\n}\r\n/* Pivot PDF Export styles */\r\n.k-pdf-export-shadow .k-pivot .k-grid-content {\r\n  overflow: visible !important;\r\n}\r\n.k-pdf-export-shadow .k-pivot .k-pivot-rowheaders .k-grid,\r\n.k-pdf-export-shadow .k-pivot .k-grid-content {\r\n  height: 100% !important;\r\n}\r\n.k-pdf-export-shadow .k-pivot .k-grid-header {\r\n  padding-right: 0 !important;\r\n}\r\n.k-pdf-export-shadow .k-pivot {\r\n  width: auto !important;\r\n  height: auto !important;\r\n}\r\n.k-pdf-export-shadow .k-pivot .k-grid-header-wrap > table,\r\n.k-pdf-export-shadow .k-pivot .k-grid-content > table {\r\n  width: 100% !important;\r\n  height: auto !important;\r\n}\r\n/* Pivot end */\r\n/* Calendar */\r\n.k-calendar {\r\n  position: relative;\r\n  display: inline-block;\r\n  width: 16.917em;\r\n  overflow: hidden;\r\n}\r\n.k-calendar td,\r\n.k-calendar .k-link {\r\n  text-decoration: none;\r\n}\r\n.k-calendar .k-action-link {\r\n  text-decoration: underline;\r\n}\r\n.k-calendar .k-header,\r\n.k-calendar .k-footer {\r\n  position: relative;\r\n  text-align: center;\r\n  zoom: 1;\r\n}\r\n.k-widget.k-calendar .k-nav-prev,\r\n.k-widget.k-calendar .k-nav-next {\r\n  position: absolute;\r\n  top: 0.16666em;\r\n  line-height: 1.8333em;\r\n  height: 1.8333em;\r\n}\r\n.k-widget.k-calendar .k-nav-prev {\r\n  left: 1%;\r\n}\r\n.k-widget.k-calendar .k-nav-next {\r\n  right: 1%;\r\n}\r\n.k-calendar .k-content {\r\n  float: left;\r\n  border-spacing: 0;\r\n  width: 100%;\r\n  height: 14.167em;\r\n  border-width: 0;\r\n  margin: 0;\r\n  table-layout: fixed;\r\n  outline: 0;\r\n}\r\n.k-calendar .k-content,\r\n.k-calendar .k-content th {\r\n  text-align: right;\r\n}\r\n.k-calendar .k-animation-container .k-content {\r\n  height: 100%;\r\n}\r\n.k-widget.k-calendar .k-nav-fast {\r\n  display: inline-block;\r\n  width: 75%;\r\n  height: 1.8333em;\r\n  line-height: 1.8333em;\r\n  margin: 0.16666em -0.08333em 0.3333em 0;\r\n}\r\n.k-calendar .k-header .k-icon {\r\n  vertical-align: middle;\r\n}\r\n.k-calendar .k-header .k-link.k-nav-prev,\r\n.k-calendar .k-header .k-link.k-nav-next {\r\n  height: 1.8333em;\r\n  width: 1.8333em;\r\n}\r\n.k-calendar th {\r\n  border-bottom-style: solid;\r\n  border-bottom-width: 1px;\r\n  padding: .4em .45em .4em .1em;\r\n  font-weight: normal;\r\n  cursor: default;\r\n}\r\n.k-calendar td {\r\n  padding: 0.08333em;\r\n  cursor: pointer;\r\n}\r\n.k-calendar .k-state-focus {\r\n  border-style: dotted;\r\n  border-width: 0.08333em;\r\n  padding: 0;\r\n}\r\n.k-calendar .k-content .k-link {\r\n  display: block;\r\n  overflow: hidden;\r\n  min-height: 1.8333em;\r\n  line-height: 1.8333em;\r\n  padding: 0 .45em 0 .1em;\r\n}\r\n.k-calendar .k-meta-view .k-link {\r\n  padding: .25em 0 .3em;\r\n  text-align: center;\r\n}\r\n.k-calendar .k-footer {\r\n  clear: both;\r\n}\r\n.k-calendar .k-footer .k-nav-today,\r\n.k-calendar .k-footer > .k-state-disabled {\r\n  display: block;\r\n  height: 100%;\r\n  padding: .5em 0;\r\n}\r\n.k-calendar .k-nav-today:hover {\r\n  text-decoration: underline;\r\n}\r\n/* TreeView */\r\ndiv.k-treeview {\r\n  /* due to k-widget */\r\n  border-width: 0;\r\n  background: none;\r\n  overflow: auto;\r\n  white-space: nowrap;\r\n}\r\n.k-treeview .k-item {\r\n  display: block;\r\n  border-width: 0;\r\n  margin: 0;\r\n  padding: 0 0 0 16px;\r\n}\r\n.k-treeview > .k-group,\r\n.k-treeview .k-item > .k-group,\r\n.k-treeview .k-content {\r\n  margin: 0;\r\n  padding: 0;\r\n  background: none;\r\n  list-style-type: none;\r\n  position: relative;\r\n}\r\n.k-treeview .k-icon,\r\n.k-treeview .k-image,\r\n.k-treeview .k-sprite,\r\n.k-treeview .k-checkbox,\r\n.k-treeview .k-in {\r\n  display: inline-block;\r\n  vertical-align: top;\r\n}\r\n.k-treeview .k-checkbox {\r\n  margin-top: .2em;\r\n}\r\n.k-treeview .k-icon,\r\n.k-treeview .k-in {\r\n  vertical-align: middle;\r\n}\r\n.k-treeview .k-request-retry {\r\n  vertical-align: baseline;\r\n}\r\n.k-treeview .k-plus,\r\n.k-treeview .k-minus,\r\n.k-treeview .k-plus-disabled,\r\n.k-treeview .k-minus-disabled {\r\n  margin-top: 0.25em;\r\n  margin-left: -16px;\r\n  cursor: pointer;\r\n}\r\n.k-treeview .k-plus-disabled,\r\n.k-treeview .k-minus-disabled {\r\n  cursor: default;\r\n}\r\n.k-treeview .k-sprite,\r\n.k-treeview .k-image {\r\n  margin-right: 3px;\r\n}\r\n.k-treeview .k-in {\r\n  margin: 1px 0 1px 0.3333em;\r\n  padding: 0.429em 0.6667em 0.429em 0.5833em;\r\n  line-height: 1.3333em;\r\n  text-decoration: none;\r\n  border-style: solid;\r\n  border-width: 1px;\r\n}\r\n.k-treeview span.k-in {\r\n  cursor: default;\r\n}\r\n.k-treeview .k-drop-hint {\r\n  position: absolute;\r\n  z-index: 10000;\r\n  visibility: hidden;\r\n  width: 80px;\r\n  height: 5px;\r\n  margin-top: -3px;\r\n  background-color: transparent;\r\n  background-repeat: no-repeat;\r\n}\r\n/* ComboBox & DropDownList */\r\nspan.k-datepicker,\r\nspan.k-timepicker,\r\nspan.k-datetimepicker,\r\nspan.k-colorpicker,\r\nspan.k-numerictextbox,\r\nspan.k-combobox,\r\nspan.k-dropdown,\r\n.k-toolbar .k-split-button {\r\n  background-image: none;\r\n}\r\n.k-autocomplete,\r\n.k-combobox,\r\n.k-datepicker,\r\n.k-timepicker,\r\n.k-datetimepicker,\r\n.k-colorpicker,\r\n.k-numerictextbox,\r\n.k-dropdown,\r\n.k-selectbox,\r\n.k-textbox,\r\n.k-toolbar .k-split-button {\r\n  position: relative;\r\n  display: inline-block;\r\n  width: 12.4em;\r\n  overflow: visible;\r\n  border-width: 0;\r\n  vertical-align: middle;\r\n}\r\n.k-filter-menu .k-combobox,\r\n.k-filter-menu .k-datepicker,\r\n.k-filter-menu .k-timepicker,\r\n.k-filter-menu .k-datetimepicker,\r\n.k-filter-menu .k-numerictextbox,\r\n.k-filter-menu .k-dropdown,\r\n.k-filter-menu .k-textbox {\r\n  width: 13.2em;\r\n}\r\n.k-autocomplete,\r\n.k-combobox,\r\n.k-datepicker,\r\n.k-timepicker,\r\n.k-datetimepicker,\r\n.k-colorpicker,\r\n.k-numerictextbox,\r\n.k-dropdown,\r\n.k-selectbox,\r\n.k-toolbar .k-split-button {\r\n  white-space: nowrap;\r\n}\r\n.k-colorpicker,\r\n.k-toolbar .k-split-button {\r\n  width: auto;\r\n}\r\n.k-datetimepicker {\r\n  width: 15em;\r\n}\r\n.k-autocomplete,\r\n.k-picker-wrap,\r\n.k-numeric-wrap {\r\n  position: relative;\r\n  cursor: default;\r\n}\r\n.k-dropdown-wrap {\r\n  position: relative;\r\n}\r\n.k-dropdown-wrap,\r\n.k-picker-wrap,\r\n.k-numeric-wrap {\r\n  display: block;\r\n}\r\n.k-block,\r\n.k-widget,\r\n.k-grid,\r\n.k-slider,\r\n.k-splitter,\r\n.k-treeview,\r\n.k-panelbar,\r\n.k-content,\r\n.k-header-column-menu {\r\n  outline: 0;\r\n  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);\r\n}\r\n.k-block,\r\n.k-slider,\r\n.k-splitbar,\r\n.k-calendar,\r\n.k-treeview,\r\n.k-pager-wrap,\r\n.k-grid-header .k-link,\r\n.k-header-column-menu {\r\n  -webkit-touch-callout: none;\r\n}\r\n.k-list-scroller {\r\n  position: relative;\r\n  overflow: auto;\r\n}\r\n.k-popup.k-list-container,\r\n.k-popup.k-calendar-container {\r\n  -webkit-touch-callout: none;\r\n  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);\r\n  padding: 2px;\r\n  border-width: 1px;\r\n  border-style: solid;\r\n}\r\n.k-list-container.k-state-border-down,\r\n.k-autocomplete.k-state-border-down,\r\n.k-dropdown-wrap.k-state-border-down,\r\n.k-picker-wrap.k-state-border-down,\r\n.k-numeric-wrap.k-state-border-down {\r\n  border-bottom-width: 0;\r\n  padding-bottom: 1px;\r\n}\r\n.k-list-container .km-scroll-container {\r\n  padding-bottom: 6px;\r\n}\r\n.k-textbox,\r\n.k-autocomplete,\r\n.k-dropdown-wrap,\r\n.k-picker-wrap,\r\n.k-numeric-wrap {\r\n  border-width: 1px;\r\n  border-style: solid;\r\n  padding: 0 1.9em 0 0;\r\n}\r\n.k-numeric-wrap.k-expand-padding {\r\n  padding-right: 0;\r\n}\r\n.k-textbox,\r\n.k-autocomplete {\r\n  padding: 0;\r\n}\r\n.k-textbox.k-space-left {\r\n  padding-left: 1.9em;\r\n}\r\n.k-textbox.k-space-right {\r\n  padding-right: 1.9em;\r\n}\r\n.k-textbox .k-icon {\r\n  top: 50%;\r\n  margin: -8px 0 0;\r\n  position: absolute;\r\n}\r\n.k-space-left .k-icon {\r\n  left: 3px;\r\n}\r\n.k-space-right .k-icon {\r\n  right: 3px;\r\n}\r\n/*prevent missing bottom border at some zoom levels*/\r\nspan.k-textbox:after {\r\n  content: \"\\a0\";\r\n  display: block;\r\n  height: .4px;\r\n  overflow: hidden;\r\n}\r\n.k-autocomplete,\r\n.k-dropdown-wrap.k-state-focused,\r\n.k-dropdown-wrap.k-state-hover,\r\n.k-picker-wrap.k-state-focused,\r\n.k-picker-wrap.k-state-hover,\r\n.k-numeric-wrap.k-state-focused,\r\n.k-numeric-wrap.k-state-hover {\r\n  -webkit-transition: -webkit-box-shadow .15s ease-out;\r\n          transition: box-shadow .15s ease-out;\r\n  -webkit-transition: \"box-shadow .15s ease-out\";\r\n          transition: \"box-shadow .15s ease-out\";\r\n}\r\n.k-textbox > input,\r\n.k-picker-wrap .k-input,\r\n.k-numeric-wrap .k-input,\r\n.k-combobox .k-input {\r\n  width: 100%;\r\n  vertical-align: top;\r\n}\r\n.k-picker-wrap .k-input,\r\n.k-numeric-wrap .k-input,\r\n.k-dropdown-wrap .k-input,\r\n.k-selectbox .k-input {\r\n  font-family: inherit;\r\n  border-width: 0;\r\n  outline: 0;\r\n}\r\n.k-dropdown .k-input,\r\n.k-selectbox .k-input {\r\n  background: transparent;\r\n}\r\n.k-picker-wrap .k-select,\r\n.k-numeric-wrap .k-select,\r\n.k-dropdown-wrap .k-select {\r\n  position: absolute;\r\n  /* icon positioning */\r\n  top: 0;\r\n  right: 0;\r\n  display: inline-block;\r\n  vertical-align: top;\r\n  text-decoration: none;\r\n}\r\n.k-combobox .k-select,\r\n.k-picker-wrap .k-select,\r\n.k-numeric-wrap .k-select {\r\n  border-style: solid;\r\n  border-width: 0 0 0 1px;\r\n  border-color: inherit;\r\n  /* skin-related, inherit does not work in ie7- */\r\n}\r\nspan.k-datetimepicker .k-select,\r\nspan.k-datetimepicker .k-select + .k-select {\r\n  right: 0;\r\n}\r\n.k-textbox > input,\r\n.k-autocomplete .k-input {\r\n  display: block;\r\n}\r\n.k-combobox .k-icon {\r\n  /*margin-top: 1px;*/\r\n}\r\n.k-dropdown .k-select,\r\n.k-selectbox .k-select {\r\n  overflow: hidden;\r\n  border: 0;\r\n  text-decoration: none;\r\n  font: inherit;\r\n  color: inherit;\r\n}\r\n.k-dropdown .k-input,\r\n.k-selectbox .k-input {\r\n  display: block;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n}\r\n.k-textbox > input,\r\n.k-autocomplete .k-input,\r\n.k-picker-wrap .k-input,\r\n.k-numeric-wrap .k-input,\r\n.k-dropdown-wrap .k-input,\r\n.k-selectbox .k-input {\r\n  height: 2.214em;\r\n  line-height: 2.214em;\r\n  padding: 0.177em 0;\r\n  text-indent: 0.8em;\r\n  border: 0;\r\n  margin: 0;\r\n}\r\n.k-combobox .k-dropdown-wrap:before,\r\n.k-picker-wrap:before,\r\n.k-numeric-wrap:before {\r\n  content: \"\\a0\";\r\n  display: inline-block;\r\n  width: 0;\r\n  height: 2.214em;\r\n  padding-bottom: 0.3em;\r\n}\r\n/* fix missing bottom border on browser zoom in Chrome */\r\n.k-webkit .k-combobox .k-dropdown-wrap:before,\r\n.k-webkit .k-picker-wrap:before,\r\n.k-webkit .k-numeric-wrap:before {\r\n  padding-bottom: 0.38em;\r\n}\r\n/* above style breaks NumericTextBox layout due display:block style applied to the input */\r\n.km.root .k-combobox .k-dropdown-wrap:before,\r\n.km.root .k-picker-wrap:before,\r\n.km.root .k-numeric-wrap:before {\r\n  content: none;\r\n}\r\n.k-combobox .k-input,\r\n.k-picker-wrap .k-input,\r\n.k-numeric-wrap .k-input {\r\n  display: inline;\r\n}\r\n.k-picker-wrap .k-select,\r\n.k-numeric-wrap .k-select,\r\n.k-dropdown-wrap .k-select {\r\n  min-height: 2.214em;\r\n  line-height: 2.564em;\r\n  vertical-align: middle;\r\n  -moz-box-sizing: border-box;\r\n  text-align: center;\r\n  width: 1.9em;\r\n  height: 100%;\r\n}\r\n.k-numeric-wrap .k-select {\r\n  padding: 0;\r\n}\r\nbody .k-datetimepicker .k-select {\r\n  border-radius: 0;\r\n}\r\n.k-combobox .k-icon,\r\n.k-dropdown,\r\n.k-selectbox .k-icon {\r\n  cursor: pointer;\r\n}\r\n.k-popup {\r\n  border-style: solid;\r\n  border-width: 1px;\r\n}\r\n.k-popup .k-item,\r\n.k-list-optionlabel {\r\n  cursor: default;\r\n}\r\n.k-popup .k-calendar {\r\n  border: 0;\r\n}\r\n.k-list {\r\n  height: auto;\r\n}\r\n.k-popup .k-list .k-item,\r\n.k-fieldselector .k-list .k-item,\r\n.k-list-optionlabel,\r\n.k-popup > .k-group-header,\r\n.k-popup > .k-virtual-wrap > .k-group-header {\r\n  padding: 1px 5px 1px 5px;\r\n  line-height: 1.8em;\r\n  min-height: 1.8em;\r\n}\r\n.k-popup .k-list .k-item {\r\n  border-width: 1px;\r\n  border-style: solid;\r\n  border-color: transparent;\r\n  padding: 0 4px;\r\n}\r\n.k-popup .k-list .k-item > .k-group {\r\n  top: -1px;\r\n}\r\n.k-group-header + div > .k-list > .k-item.k-first:before {\r\n  content: \" \";\r\n  display: block;\r\n  border-top-width: 1px;\r\n  border-top-style: solid;\r\n  position: absolute;\r\n  top: -1px;\r\n  left: 0;\r\n  right: 0;\r\n}\r\n.k-popup > .k-group-header,\r\n.k-popup > .k-virtual-wrap > .k-group-header {\r\n  padding-right: 22px;\r\n}\r\n.k-overflow-container .k-item {\r\n  padding: 1px;\r\n}\r\n.k-overflow-container > .k-state-disabled .k-button,\r\n.k-overflow-container .k-button.k-state-disabled,\r\n.k-overflow-container .k-button.k-state-disabled:hover {\r\n  border: 0 ;\r\n  background: none;\r\n}\r\n.k-popup .k-list .k-state-hover,\r\n.k-popup .k-list .k-state-focused,\r\n.k-popup .k-list .k-state-selected,\r\n.k-overflow-container .k-state-hover,\r\n.k-overflow-container .k-state-focused,\r\n.k-overflow-container .k-state-selected,\r\n.k-fieldselector .k-list .k-item,\r\n.k-list-optionlabel.k-state-focused,\r\n.k-list-optionlabel.k-state-selected {\r\n  padding: 0 4px;\r\n  border-width: 1px;\r\n  border-style: solid;\r\n}\r\n.k-list-filter {\r\n  position: relative;\r\n  margin-bottom: 2px;\r\n}\r\n.k-list-filter > .k-textbox {\r\n  padding-right: 20px;\r\n  width: 100%;\r\n}\r\n.k-list-filter > .k-icon {\r\n  position: absolute;\r\n  right: 6px;\r\n  top: 50%;\r\n  -webkit-transform: translateY(-50%);\r\n      -ms-transform: translateY(-50%);\r\n          transform: translateY(-50%);\r\n}\r\n.km-root .k-list-filter > .k-textbox {\r\n  padding-left: 0;\r\n  padding-right: 0;\r\n  border-left-width: 0;\r\n  border-right-width: 0;\r\n}\r\n/* MultiSelect */\r\n.k-multiselect-wrap {\r\n  position: relative;\r\n  border-width: 0px;\r\n  border-style: solid;\r\n  border-radius: 4px;\r\n  border-color: #C5C5C5;\r\n  background-color: #FFF;\r\n  min-height: 2.04em;\r\n}\r\n.k-multiselect-wrap .k-input {\r\n  background-color: transparent;\r\n  height: 1.31em;\r\n  line-height: 1.31em;\r\n  padding: 0.18em 0;\r\n  text-indent: 0.8em;\r\n  border: 0;\r\n  margin: 1px 0 0;\r\n  float: left;\r\n}\r\n.k-multiselect-wrap .k-input::-ms-clear {\r\n  display: none;\r\n}\r\n.k-multiselect-wrap li {\r\n  margin: 1px 0 1px 1px;\r\n  padding: .1em 1.6em .1em .4em;\r\n  line-height: 2.064em;\r\n  float: left;\r\n  position: relative;\r\n}\r\n.k-autocomplete .k-loading,\r\n.k-multiselect .k-loading {\r\n  position: absolute;\r\n  right: 3px;\r\n  bottom: 4px;\r\n}\r\n.k-multiselect .k-loading-hidden {\r\n  visibility: hidden;\r\n}\r\n.k-multiselect-wrap .k-select {\r\n  position: absolute;\r\n  top: 0;\r\n  bottom: 0;\r\n  right: 0;\r\n  padding: .1em .2em;\r\n}\r\n/* Date/Time Pickers */\r\n.k-datetimepicker .k-picker-wrap {\r\n  padding-right: 3.8em;\r\n}\r\n.k-datetimepicker .k-select {\r\n  width: 3.8em;\r\n}\r\n.k-datetimepicker .k-picker-wrap .k-icon {\r\n  margin: 0 2px;\r\n}\r\n.k-picker-wrap .k-icon {\r\n  cursor: pointer;\r\n}\r\n.k-button,\r\n.k-textbox,\r\n.k-timepicker,\r\n.k-datepicker,\r\n.k-datetimepicker {\r\n  display: inline-block;\r\n  vertical-align: middle;\r\n}\r\n.k-picker-wrap .k-input {\r\n  margin: 0;\r\n}\r\n.k-time-popup .k-item {\r\n  padding: 1px 3px;\r\n}\r\n/* inputs */\r\n.k-input {\r\n  padding: 0.25em 0;\r\n}\r\n.k-input,\r\n.k-textbox > input {\r\n  outline: 0;\r\n  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);\r\n}\r\n.k-textbox {\r\n  outline: 0;\r\n}\r\ninput.k-textbox,\r\ntextarea.k-textbox {\r\n  padding: 2px .3em;\r\n}\r\ninput.k-textbox {\r\n  height: 2.694em;\r\n  text-indent: 0.8em;\r\n  line-height: 1.6em;\r\n}\r\n.k-ie input.k-textbox {\r\n  text-indent: 0.4em;\r\n}\r\n.k-ff input.k-textbox {\r\n  height: 2.17em;\r\n}\r\ntextarea.k-textbox {\r\n  height: auto;\r\n}\r\n/* NumericTextBox */\r\nspan.k-numerictextbox {\r\n  background-color: transparent;\r\n}\r\n.k-numerictextbox .k-input {\r\n  margin: 0;\r\n}\r\n.k-numerictextbox .k-link {\r\n  display: block;\r\n  height: 1em;\r\n  line-height: 1em;\r\n  vertical-align: middle;\r\n  border-width: 0;\r\n  padding: 0;\r\n}\r\n.k-numerictextbox .k-icon {\r\n  height: 11px;\r\n}\r\n.k-numeric-wrap .k-input::-webkit-inner-spin-button {\r\n  -webkit-appearance: none;\r\n}\r\n/* ColorPicker */\r\n.k-colorpicker .k-picker-wrap {\r\n  line-height: 2em;\r\n}\r\n.k-colorpicker .k-selected-color {\r\n  vertical-align: top;\r\n  line-height: 0;\r\n  display: inline-block;\r\n  height: 2em;\r\n  width: 2em;\r\n}\r\n.k-colorpicker .k-tool-icon {\r\n  position: relative;\r\n  top: -2px;\r\n  display: inline-block;\r\n  padding: 3px 3px 2px;\r\n  font-size: 0;\r\n  line-height: 0;\r\n  margin-right: 3px;\r\n  margin-left: 2px;\r\n  margin-bottom: 3px;\r\n  background-repeat: no-repeat;\r\n  vertical-align: middle;\r\n  width: 16px;\r\n  height: 16px;\r\n  -ms-high-contrast-adjust: none;\r\n}\r\n.k-colorpicker .k-tool-icon .k-selected-color {\r\n  display: block;\r\n  height: 3px;\r\n  width: 16px;\r\n  position: absolute;\r\n  left: 3px;\r\n  bottom: -3px;\r\n  border-radius: 0 !important;\r\n}\r\n.k-colorpicker .k-select {\r\n  cursor: pointer;\r\n}\r\n.k-disabled-overlay {\r\n  position: absolute;\r\n  left: 0;\r\n  top: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  background-color: #fff;\r\n  opacity: 0.5;\r\n  filter: alpha(opacity=50);\r\n}\r\n.k-colorpalette {\r\n  position: relative;\r\n  line-height: 0;\r\n  border-width: 0;\r\n  display: inline-block;\r\n}\r\n.k-colorpalette .k-palette {\r\n  border-collapse: collapse;\r\n  position: relative;\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n.k-colorpalette .k-item {\r\n  width: 14px;\r\n  height: 14px;\r\n  overflow: hidden;\r\n  -ms-high-contrast-adjust: none;\r\n}\r\n.k-colorpalette .k-item.k-state-selected,\r\n.k-colorpalette .k-item.k-state-selected:hover {\r\n  z-index: 100;\r\n  background: transparent;\r\n  -webkit-box-shadow: 0 1px 3px 1px rgba(0, 0, 0, 0.7), inset 0 0 0 1px rgba(255, 255, 255, 0.45);\r\n          box-shadow: 0 1px 3px 1px rgba(0, 0, 0, 0.7), inset 0 0 0 1px rgba(255, 255, 255, 0.45);\r\n  position: relative;\r\n}\r\n.k-colorpalette .k-item:hover {\r\n  z-index: 101;\r\n  position: relative;\r\n  -webkit-box-shadow: 0 1px 3px 1px rgba(0, 0, 0, 0.5), inset 0 0 0 1px rgba(255, 255, 255, 0.3);\r\n          box-shadow: 0 1px 3px 1px rgba(0, 0, 0, 0.5), inset 0 0 0 1px rgba(255, 255, 255, 0.3);\r\n}\r\n.k-flatcolorpicker {\r\n  position: relative;\r\n  display: inline-block;\r\n  width: 250px;\r\n  padding-bottom: 5px;\r\n}\r\ndiv.k-flatcolorpicker {\r\n  background-color: transparent;\r\n  background-image: none;\r\n}\r\n.k-flatcolorpicker .k-selected-color {\r\n  background-image: url(\"textures/transtexture.png\");\r\n  background-position: 50% 50%;\r\n  text-align: right;\r\n}\r\n.k-flatcolorpicker .k-selected-color input.k-color-value {\r\n  font-family: Consolas, \"Ubuntu Mono\", \"Lucida Console\", \"Courier New\", monospace;\r\n  padding: .75em .3em .65em 1em;\r\n  border: 0;\r\n  margin: 0;\r\n  width: 70%;\r\n}\r\n.k-flatcolorpicker .k-hsv-rectangle {\r\n  position: relative;\r\n  -webkit-user-select: none;\r\n     -moz-user-select: none;\r\n      -ms-user-select: none;\r\n          user-select: none;\r\n  -ms-touch-action: pinch-zoom double-tap-zoom;\r\n}\r\n.k-flatcolorpicker .k-hsv-rectangle .k-draghandle {\r\n  cursor: pointer;\r\n  position: absolute;\r\n  z-index: 10;\r\n  left: 50%;\r\n  top: 50%;\r\n  width: 8px;\r\n  height: 8px;\r\n  border: 1px solid #eee;\r\n  margin-left: -5px;\r\n  margin-top: -5px;\r\n  border-radius: 6px;\r\n  -webkit-box-shadow: 0 1px 2px #444;\r\n          box-shadow: 0 1px 2px #444;\r\n  background: transparent;\r\n}\r\n.k-flatcolorpicker .k-hsv-rectangle .k-draghandle:hover,\r\n.k-flatcolorpicker .k-hsv-rectangle .k-draghandle:focus {\r\n  background: transparent;\r\n  border-color: #fff;\r\n  -webkit-box-shadow: 0 1px 5px #000;\r\n          box-shadow: 0 1px 5px #000;\r\n}\r\n.k-flatcolorpicker .k-hsv-rectangle.k-dragging,\r\n.k-flatcolorpicker .k-hsv-rectangle.k-dragging * {\r\n  cursor: none;\r\n}\r\n.k-flatcolorpicker .k-slider-horizontal {\r\n  height: 20px;\r\n  width: 90%;\r\n  margin: 0 5%;\r\n}\r\n.k-flatcolorpicker .k-slider-horizontal .k-slider-track {\r\n  -webkit-box-shadow: 0 1px 0 #fff, 0 -1px 0 #999;\r\n          box-shadow: 0 1px 0 #fff, 0 -1px 0 #999;\r\n}\r\n.k-flatcolorpicker .k-hue-slider,\r\n.k-flatcolorpicker .k-transparency-slider {\r\n  display: block;\r\n}\r\n.k-flatcolorpicker .k-hue-slider .k-slider-selection,\r\n.k-flatcolorpicker .k-transparency-slider .k-slider-selection {\r\n  background: transparent;\r\n}\r\n.k-flatcolorpicker .k-hue-slider .k-draghandle,\r\n.k-flatcolorpicker .k-transparency-slider .k-draghandle {\r\n  background: transparent;\r\n  border: 3px solid #eee;\r\n  margin-top: 1px;\r\n  height: 8px;\r\n  width: 8px;\r\n  -webkit-box-shadow: 0 1px 4px #444;\r\n          box-shadow: 0 1px 4px #444;\r\n}\r\n.k-flatcolorpicker .k-hue-slider .k-draghandle:hover,\r\n.k-flatcolorpicker .k-transparency-slider .k-draghandle:hover,\r\n.k-flatcolorpicker .k-hue-slider .k-draghandle:focus,\r\n.k-flatcolorpicker .k-transparency-slider .k-draghandle:focus {\r\n  background: transparent;\r\n  border-color: #fff;\r\n  -webkit-box-shadow: 0 1px 5px #000;\r\n          box-shadow: 0 1px 5px #000;\r\n  border-width: 2px;\r\n  padding: 1px;\r\n}\r\n.k-flatcolorpicker .k-hue-slider .k-slider-track {\r\n  background: -webkit-gradient(linear, left top, right top, from(#ff0000), color-stop(16%, #ffff00), color-stop(33%, #00ff00), color-stop(50%, #00ffff), color-stop(67%, #0000ff), color-stop(84%, #ff00ff), to(#ff0004));\r\n  background: -webkit-linear-gradient(left, #ff0000 0%, #ffff00 16%, #00ff00 33%, #00ffff 50%, #0000ff 67%, #ff00ff 84%, #ff0004 100%);\r\n  background: linear-gradient(to right, #ff0000 0%, #ffff00 16%, #00ff00 33%, #00ffff 50%, #0000ff 67%, #ff00ff 84%, #ff0004 100%);\r\n}\r\n.k-flatcolorpicker .k-transparency-slider .k-slider-track {\r\n  background-image: url(\"textures/transparency.png\");\r\n  -webkit-background-size: 100% auto;\r\n          background-size: 100% auto;\r\n  background-position: 100% 50%;\r\n  background-repeat: no-repeat;\r\n}\r\n.k-flatcolorpicker .k-controls {\r\n  margin-top: 10px;\r\n  margin-bottom: 5px;\r\n  text-align: center;\r\n  font-size: 90%;\r\n}\r\n.k-flatcolorpicker .k-controls .k-button {\r\n  width: 6em;\r\n}\r\n.k-flatcolorpicker .k-hsv-gradient {\r\n  background: -webkit-gradient(linear, left top, left bottom, from(rgba(0, 0, 0, 0)), to(#000000)), -webkit-gradient(linear, left top, right top, from(#ffffff), to(rgba(255, 255, 255, 0)));\r\n  background: -webkit-linear-gradient(top, rgba(0, 0, 0, 0) 0%, #000000 100%), -webkit-linear-gradient(left, #ffffff 0%, rgba(255, 255, 255, 0) 100%);\r\n  background: linear-gradient(to bottom, rgba(0, 0, 0, 0) 0%, #000000 100%), linear-gradient(to right, #ffffff 0%, rgba(255, 255, 255, 0) 100%);\r\n  height: 180px;\r\n  margin-bottom: 5px;\r\n}\r\n.k-ie9 .k-flatcolorpicker .k-hue-slider .k-slider-track {\r\n  background: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiA/Pgo8c3ZnIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgd2lkdGg9IjEwMCUiIGhlaWdodD0iMTAwJSIgdmlld0JveD0iMCAwIDEgMSIgcHJlc2VydmVBc3BlY3RSYXRpbz0ibm9uZSI+CiAgPGxpbmVhckdyYWRpZW50IGlkPSJncmFkLXVjZ2ctZ2VuZXJhdGVkIiBncmFkaWVudFVuaXRzPSJ1c2VyU3BhY2VPblVzZSIgeDE9IjAlIiB5MT0iMCUiIHgyPSIxMDAlIiB5Mj0iMCUiPgogICAgPHN0b3Agb2Zmc2V0PSIwJSIgc3RvcC1jb2xvcj0iI2ZmMDAwMCIgc3RvcC1vcGFjaXR5PSIxIi8+CiAgICA8c3RvcCBvZmZzZXQ9IjE2JSIgc3RvcC1jb2xvcj0iI2ZmZmYwMCIgc3RvcC1vcGFjaXR5PSIxIi8+CiAgICA8c3RvcCBvZmZzZXQ9IjMzJSIgc3RvcC1jb2xvcj0iIzAwZmYwMCIgc3RvcC1vcGFjaXR5PSIxIi8+CiAgICA8c3RvcCBvZmZzZXQ9IjUwJSIgc3RvcC1jb2xvcj0iIzAwZmZmZiIgc3RvcC1vcGFjaXR5PSIxIi8+CiAgICA8c3RvcCBvZmZzZXQ9IjY3JSIgc3RvcC1jb2xvcj0iIzAwMDBmZiIgc3RvcC1vcGFjaXR5PSIxIi8+CiAgICA8c3RvcCBvZmZzZXQ9Ijg0JSIgc3RvcC1jb2xvcj0iI2ZmMDBmZiIgc3RvcC1vcGFjaXR5PSIxIi8+CiAgICA8c3RvcCBvZmZzZXQ9IjEwMCUiIHN0b3AtY29sb3I9IiNmZjAwMDQiIHN0b3Atb3BhY2l0eT0iMSIvPgogIDwvbGluZWFyR3JhZGllbnQ+CiAgPHJlY3QgeD0iMCIgeT0iMCIgd2lkdGg9IjEiIGhlaWdodD0iMSIgZmlsbD0idXJsKCNncmFkLXVjZ2ctZ2VuZXJhdGVkKSIgLz4KPC9zdmc+);\r\n}\r\n.k-ie9 .k-flatcolorpicker .k-hsv-gradient {\r\n  background: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiA/Pgo8c3ZnIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgd2lkdGg9IjEwMCUiIGhlaWdodD0iMTAwJSIgdmlld0JveD0iMCAwIDEgMSIgcHJlc2VydmVBc3BlY3RSYXRpbz0ibm9uZSI+CiAgPGxpbmVhckdyYWRpZW50IGlkPSJncmFkLXVjZ2ctZ2VuZXJhdGVkIiBncmFkaWVudFVuaXRzPSJ1c2VyU3BhY2VPblVzZSIgeDE9IjAlIiB5MT0iMCUiIHgyPSIwJSIgeTI9IjEwMCUiPgogICAgPHN0b3Agb2Zmc2V0PSIwJSIgc3RvcC1jb2xvcj0iIzAwMDAwMCIgc3RvcC1vcGFjaXR5PSIwIi8+CiAgICA8c3RvcCBvZmZzZXQ9IjEwMCUiIHN0b3AtY29sb3I9IiMwMDAwMDAiIHN0b3Atb3BhY2l0eT0iMSIvPgogIDwvbGluZWFyR3JhZGllbnQ+CiAgPHJlY3QgeD0iMCIgeT0iMCIgd2lkdGg9IjEiIGhlaWdodD0iMSIgZmlsbD0idXJsKCNncmFkLXVjZ2ctZ2VuZXJhdGVkKSIgLz4KPC9zdmc+), url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiA/Pgo8c3ZnIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgd2lkdGg9IjEwMCUiIGhlaWdodD0iMTAwJSIgdmlld0JveD0iMCAwIDEgMSIgcHJlc2VydmVBc3BlY3RSYXRpbz0ibm9uZSI+CiAgPGxpbmVhckdyYWRpZW50IGlkPSJncmFkLXVjZ2ctZ2VuZXJhdGVkIiBncmFkaWVudFVuaXRzPSJ1c2VyU3BhY2VPblVzZSIgeDE9IjAlIiB5MT0iMCUiIHgyPSIxMDAlIiB5Mj0iMCUiPgogICAgPHN0b3Agb2Zmc2V0PSIwJSIgc3RvcC1jb2xvcj0iI2ZmZmZmZiIgc3RvcC1vcGFjaXR5PSIxIi8+CiAgICA8c3RvcCBvZmZzZXQ9IjEwMCUiIHN0b3AtY29sb3I9IiNmZmZmZmYiIHN0b3Atb3BhY2l0eT0iMCIvPgogIDwvbGluZWFyR3JhZGllbnQ+CiAgPHJlY3QgeD0iMCIgeT0iMCIgd2lkdGg9IjEiIGhlaWdodD0iMSIgZmlsbD0idXJsKCNncmFkLXVjZ2ctZ2VuZXJhdGVkKSIgLz4KPC9zdmc+);\r\n}\r\n.k-ie8 .k-flatcolorpicker .k-hue-slider .k-slider-track {\r\n  background: url(\"textures/hue.png\") repeat 0 50%;\r\n}\r\n.k-ie8 .k-flatcolorpicker .k-transparency-slider .k-slider-track {\r\n  background: url(\"textures/transparency.png\") repeat 0 50%;\r\n}\r\n.k-ie8 .k-flatcolorpicker .k-hsv-gradient {\r\n  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffffffff',endColorstr='#00ffffff',GradientType=1) progid:DXImageTransform.Microsoft.gradient(startColorstr='#00000000',endColorstr='#ff000000',GradientType=0);\r\n}\r\n/* Editor */\r\ntable.k-editor {\r\n  width: 100%;\r\n  height: 250px;\r\n  table-layout: fixed;\r\n  border-style: solid;\r\n  border-width: 1px;\r\n  border-collapse: separate;\r\n  border-spacing: 4px;\r\n  font-size: 100%;\r\n  vertical-align: top;\r\n  position: relative;\r\n}\r\n.k-editor-inline {\r\n  border-width: 2px;\r\n  padding: .3em .5em;\r\n  word-wrap: break-word;\r\n  overflow: auto;\r\n}\r\n.k-editortoolbar-dragHandle {\r\n  cursor: move;\r\n  padding-left: 0;\r\n  padding-right: 3px;\r\n  -webkit-box-shadow: none !important;\r\n          box-shadow: none !important;\r\n}\r\n.k-editor-widget > .k-window-content {\r\n  overflow: hidden;\r\n}\r\n.k-editor .k-editor-toolbar-wrap {\r\n  border: 0;\r\n  padding: 0;\r\n}\r\n.k-editor-toolbar {\r\n  margin: 0;\r\n  padding: .1em 0;\r\n  list-style-type: none;\r\n  line-height: 1.3em;\r\n  cursor: default;\r\n  word-wrap: break-word;\r\n  /* allow tools to wrap properly in IE */\r\n}\r\n.k-editor-toolbar li {\r\n  display: inline-block;\r\n  vertical-align: middle;\r\n}\r\n.k-webkit .k-editor-toolbar,\r\n.k-ff .k-editor-toolbar,\r\n.k-ie9 .k-editor-toolbar {\r\n  padding: 0;\r\n}\r\n.k-webkit .k-editor-toolbar li,\r\n.k-safari .k-editor-toolbar li,\r\n.k-ff .k-editor-toolbar li,\r\n.k-ie9 .k-editor-toolbar li,\r\n.k-ie10 .k-editor-toolbar li {\r\n  display: inline-block;\r\n  padding: .1em 0;\r\n}\r\n.k-editor-toolbar .k-editor-widget,\r\n.k-editor-toolbar > li {\r\n  margin-right: 6px;\r\n}\r\n.k-group-start.k-group-end .k-editor-widget {\r\n  margin-right: 0;\r\n}\r\n.k-editor-toolbar .k-editor-dropdown {\r\n  position: relative;\r\n}\r\n.k-select-overlay {\r\n  -webkit-appearance: none;\r\n  opacity: 0;\r\n  z-index: 11000;\r\n  top: 0;\r\n  left: 0;\r\n  position: absolute;\r\n  height: 26px;\r\n  width: 100%;\r\n  margin: -4px 0 0;\r\n}\r\n.k-editor-toolbar .k-separator {\r\n  position: relative;\r\n  top: 1px;\r\n  border-style: solid;\r\n  border-width: 0 1px 0 0;\r\n  margin: 0 .3em 0 .1em;\r\n  padding: 0 0 0 1px;\r\n  font-size: 1.3em;\r\n}\r\n.k-editor-toolbar .k-break {\r\n  display: block;\r\n  height: 1px;\r\n  font-size: 0;\r\n  line-height: 0;\r\n}\r\n.k-editor-toolbar .k-dropdown,\r\n.k-editor-toolbar .k-combobox,\r\n.k-editor-toolbar .k-selectbox,\r\n.k-editor-toolbar .k-colorpicker {\r\n  vertical-align: middle;\r\n}\r\n.k-button-group {\r\n  white-space: nowrap;\r\n}\r\n.k-button-group .k-tool {\r\n  display: inline-block;\r\n  vertical-align: middle;\r\n  margin: 1px 0;\r\n  width: 36px;\r\n  height: 36px;\r\n  line-height: 36px;\r\n}\r\n.k-button-group .k-tool-icon {\r\n  width: 24px;\r\n  height: 24px;\r\n  vertical-align: middle;\r\n  -ms-high-contrast-adjust: none;\r\n}\r\n.k-i-move {\r\n  background-position: -160px -288px;\r\n}\r\n.k-bold {\r\n  background-position: -240px 0;\r\n}\r\n.k-state-hover .k-bold,\r\n.k-state-selected .k-bold {\r\n  background-position: -264px 0;\r\n  background-position: -240px 0;\r\n}\r\n.k-state-hover .k-bold,\r\n.k-state-selected .k-bold {\r\n  background-position: -240px 0;\r\n}\r\n.k-italic {\r\n  background-position: -240px -24px;\r\n}\r\n.k-state-hover .k-italic,\r\n.k-state-selected .k-italic {\r\n  background-position: -264px -24px;\r\n  background-position: -240px -24px;\r\n}\r\n.k-state-hover .k-italic,\r\n.k-state-selected .k-italic {\r\n  background-position: -240px -24px;\r\n}\r\n.k-underline {\r\n  background-position: -240px -48px;\r\n}\r\n.k-state-hover .k-underline,\r\n.k-state-selected .k-underline {\r\n  background-position: -264px -48px;\r\n  background-position: -240px -48px;\r\n}\r\n.k-state-hover .k-underline,\r\n.k-state-selected .k-underline {\r\n  background-position: -240px -48px;\r\n}\r\n.k-strikethrough {\r\n  background-position: -240px -72px;\r\n}\r\n.k-state-hover .k-strikethrough,\r\n.k-state-selected .k-strikethrough {\r\n  background-position: -264px -72px;\r\n  background-position: -240px -72px;\r\n}\r\n.k-state-hover .k-strikethrough,\r\n.k-state-selected .k-strikethrough {\r\n  background-position: -240px -72px;\r\n}\r\n.k-foreColor {\r\n  background-position: -240px -96px;\r\n}\r\n.k-state-hover .k-foreColor,\r\n.k-state-selected .k-foreColor {\r\n  background-position: -264px -96px;\r\n  background-position: -240px -96px;\r\n}\r\n.k-state-hover .k-foreColor,\r\n.k-state-selected .k-foreColor {\r\n  background-position: -240px -96px;\r\n}\r\n.k-backColor {\r\n  background-position: -240px -120px;\r\n}\r\n.k-state-hover .k-backColor,\r\n.k-state-selected .k-backColor {\r\n  background-position: -264px -120px;\r\n  background-position: -240px -120px;\r\n}\r\n.k-state-hover .k-backColor,\r\n.k-state-selected .k-backColor {\r\n  background-position: -240px -120px;\r\n}\r\n.k-colorpicker .k-foreColor {\r\n  background-position: -240px -96px;\r\n}\r\n.k-colorpicker .k-backColor {\r\n  background-position: -240px -120px;\r\n}\r\n.k-justifyLeft {\r\n  background-position: -240px -144px;\r\n}\r\n.k-state-hover .k-justifyLeft,\r\n.k-state-selected .k-justifyLeft {\r\n  background-position: -264px -144px;\r\n  background-position: -240px -144px;\r\n}\r\n.k-state-hover .k-justifyLeft,\r\n.k-state-selected .k-justifyLeft {\r\n  background-position: -240px -144px;\r\n}\r\n.k-justifyCenter {\r\n  background-position: -240px -168px;\r\n}\r\n.k-state-hover .k-justifyCenter,\r\n.k-state-selected .k-justifyCenter {\r\n  background-position: -264px -168px;\r\n  background-position: -240px -168px;\r\n}\r\n.k-state-hover .k-justifyCenter,\r\n.k-state-selected .k-justifyCenter {\r\n  background-position: -240px -168px;\r\n}\r\n.k-justifyRight {\r\n  background-position: -240px -192px;\r\n}\r\n.k-state-hover .k-justifyRight,\r\n.k-state-selected .k-justifyRight {\r\n  background-position: -264px -192px;\r\n  background-position: -240px -192px;\r\n}\r\n.k-state-hover .k-justifyRight,\r\n.k-state-selected .k-justifyRight {\r\n  background-position: -240px -192px;\r\n}\r\n.k-justifyFull {\r\n  background-position: -240px -216px;\r\n}\r\n.k-state-hover .k-justifyFull,\r\n.k-state-selected .k-justifyFull {\r\n  background-position: -264px -216px;\r\n  background-position: -240px -216px;\r\n}\r\n.k-state-hover .k-justifyFull,\r\n.k-state-selected .k-justifyFull {\r\n  background-position: -240px -216px;\r\n}\r\n.k-insertUnorderedList {\r\n  background-position: -240px -264px;\r\n}\r\n.k-state-hover .k-insertUnorderedList,\r\n.k-state-selected .k-insertUnorderedList {\r\n  background-position: -264px -264px;\r\n  background-position: -240px -264px;\r\n}\r\n.k-state-hover .k-insertUnorderedList,\r\n.k-state-selected .k-insertUnorderedList {\r\n  background-position: -240px -264px;\r\n}\r\n.k-insertOrderedList {\r\n  background-position: -240px -288px;\r\n}\r\n.k-state-hover .k-insertOrderedList,\r\n.k-state-selected .k-insertOrderedList {\r\n  background-position: -264px -288px;\r\n  background-position: -240px -288px;\r\n}\r\n.k-state-hover .k-insertOrderedList,\r\n.k-state-selected .k-insertOrderedList {\r\n  background-position: -240px -288px;\r\n}\r\n.k-indent,\r\n.k-rtl .k-outdent {\r\n  background-position: -288px 0;\r\n}\r\n.k-state-hover .k-indent,\r\n.k-state-hover .k-rtl .k-outdent,\r\n.k-state-selected .k-indent,\r\n.k-state-selected .k-rtl .k-outdent {\r\n  background-position: -312px 0;\r\n  background-position: -288px 0;\r\n}\r\n.k-state-hover .k-indent,\r\n.k-state-hover .k-rtl .k-outdent,\r\n.k-state-selected .k-indent,\r\n.k-state-selected .k-rtl .k-outdent {\r\n  background-position: -288px 0;\r\n}\r\n.k-outdent,\r\n.k-rtl .k-indent {\r\n  background-position: -288px -24px;\r\n}\r\n.k-state-hover .k-outdent,\r\n.k-state-hover .k-rtl .k-indent,\r\n.k-state-selected .k-outdent,\r\n.k-state-selected .k-rtl .k-indent {\r\n  background-position: -312px -24px;\r\n  background-position: -288px -24px;\r\n}\r\n.k-state-hover .k-outdent,\r\n.k-state-hover .k-rtl .k-indent,\r\n.k-state-selected .k-outdent,\r\n.k-state-selected .k-rtl .k-indent {\r\n  background-position: -288px -24px;\r\n}\r\n.k-createLink {\r\n  background-position: -288px -48px;\r\n}\r\n.k-state-hover .k-createLink,\r\n.k-state-selected .k-createLink {\r\n  background-position: -312px -48px;\r\n  background-position: -288px -48px;\r\n}\r\n.k-state-hover .k-createLink,\r\n.k-state-selected .k-createLink {\r\n  background-position: -288px -48px;\r\n}\r\n.k-unlink {\r\n  background-position: -288px -72px;\r\n}\r\n.k-state-hover .k-unlink,\r\n.k-state-selected .k-unlink {\r\n  background-position: -312px -72px;\r\n  background-position: -288px -72px;\r\n}\r\n.k-state-hover .k-unlink,\r\n.k-state-selected .k-unlink {\r\n  background-position: -288px -72px;\r\n}\r\n.k-insertImage {\r\n  background-position: -288px -96px;\r\n}\r\n.k-state-hover .k-insertImage,\r\n.k-state-selected .k-insertImage {\r\n  background-position: -312px -96px;\r\n  background-position: -288px -96px;\r\n}\r\n.k-state-hover .k-insertImage,\r\n.k-state-selected .k-insertImage {\r\n  background-position: -288px -96px;\r\n}\r\n.k-insertFile {\r\n  background-position: -288px -216px;\r\n}\r\n.k-state-hover .k-insertFile,\r\n.k-state-selected .k-insertFile {\r\n  background-position: -312px -216px;\r\n  background-position: -288px -216px;\r\n}\r\n.k-state-hover .k-insertFile,\r\n.k-state-selected .k-insertFile {\r\n  background-position: -288px -216px;\r\n}\r\n.k-subscript {\r\n  background-position: -288px -144px;\r\n}\r\n.k-state-hover .k-subscript,\r\n.k-state-selected .k-subscript {\r\n  background-position: -312px -144px;\r\n  background-position: -288px -144px;\r\n}\r\n.k-state-hover .k-subscript,\r\n.k-state-selected .k-subscript {\r\n  background-position: -288px -144px;\r\n}\r\n.k-superscript {\r\n  background-position: -288px -168px;\r\n}\r\n.k-state-hover .k-superscript,\r\n.k-state-selected .k-superscript {\r\n  background-position: -312px -168px;\r\n  background-position: -288px -168px;\r\n}\r\n.k-state-hover .k-superscript,\r\n.k-state-selected .k-superscript {\r\n  background-position: -288px -168px;\r\n}\r\n.k-cleanFormatting {\r\n  background-position: -288px -192px;\r\n}\r\n.k-state-hover .k-cleanFormatting,\r\n.k-state-selected .k-cleanFormatting {\r\n  background-position: -312px -192px;\r\n  background-position: -288px -192px;\r\n}\r\n.k-state-hover .k-cleanFormatting,\r\n.k-state-selected .k-cleanFormatting {\r\n  background-position: -288px -192px;\r\n}\r\n.k-createTable {\r\n  background-position: -192px 0;\r\n}\r\n.k-state-hover .k-createTable,\r\n.k-state-selected .k-createTable {\r\n  background-position: -216px 0;\r\n  background-position: -192px 0;\r\n}\r\n.k-state-hover .k-createTable,\r\n.k-state-selected .k-createTable {\r\n  background-position: -192px 0;\r\n}\r\n.k-addColumnLeft {\r\n  background-position: -192px -24px;\r\n}\r\n.k-state-hover .k-addColumnLeft,\r\n.k-state-selected .k-addColumnLeft {\r\n  background-position: -216px -24px;\r\n  background-position: -192px -24px;\r\n}\r\n.k-state-hover .k-addColumnLeft,\r\n.k-state-selected .k-addColumnLeft {\r\n  background-position: -192px -24px;\r\n}\r\n.k-addColumnRight {\r\n  background-position: -192px -48px;\r\n}\r\n.k-state-hover .k-addColumnRight,\r\n.k-state-selected .k-addColumnRight {\r\n  background-position: -216px -48px;\r\n  background-position: -192px -48px;\r\n}\r\n.k-state-hover .k-addColumnRight,\r\n.k-state-selected .k-addColumnRight {\r\n  background-position: -192px -48px;\r\n}\r\n.k-addRowAbove {\r\n  background-position: -192px -72px;\r\n}\r\n.k-state-hover .k-addRowAbove,\r\n.k-state-selected .k-addRowAbove {\r\n  background-position: -216px -72px;\r\n  background-position: -192px -72px;\r\n}\r\n.k-state-hover .k-addRowAbove,\r\n.k-state-selected .k-addRowAbove {\r\n  background-position: -192px -72px;\r\n}\r\n.k-addRowBelow {\r\n  background-position: -192px -96px;\r\n}\r\n.k-state-hover .k-addRowBelow,\r\n.k-state-selected .k-addRowBelow {\r\n  background-position: -216px -96px;\r\n  background-position: -192px -96px;\r\n}\r\n.k-state-hover .k-addRowBelow,\r\n.k-state-selected .k-addRowBelow {\r\n  background-position: -192px -96px;\r\n}\r\n.k-deleteRow {\r\n  background-position: -192px -120px;\r\n}\r\n.k-state-hover .k-deleteRow,\r\n.k-state-selected .k-deleteRow {\r\n  background-position: -216px -120px;\r\n  background-position: -192px -120px;\r\n}\r\n.k-state-hover .k-deleteRow,\r\n.k-state-selected .k-deleteRow {\r\n  background-position: -192px -120px;\r\n}\r\n.k-deleteColumn {\r\n  background-position: -192px -144px;\r\n}\r\n.k-state-hover .k-deleteColumn,\r\n.k-state-selected .k-deleteColumn {\r\n  background-position: -216px -144px;\r\n  background-position: -192px -144px;\r\n}\r\n.k-state-hover .k-deleteColumn,\r\n.k-state-selected .k-deleteColumn {\r\n  background-position: -192px -144px;\r\n}\r\n.k-mergeCells {\r\n  background-position: -192px -168px;\r\n}\r\n.k-state-hover .k-mergeCells,\r\n.k-state-selected .k-mergeCells {\r\n  background-position: -216px -168px;\r\n  background-position: -192px -168px;\r\n}\r\n.k-state-hover .k-mergeCells,\r\n.k-state-selected .k-mergeCells {\r\n  background-position: -192px -168px;\r\n}\r\n.k-pdf {\r\n  background-position: -288px -240px;\r\n}\r\n.k-state-hover .k-pdf,\r\n.k-state-selected .k-pdf {\r\n  background-position: -312px -240px;\r\n  background-position: -288px -240px;\r\n}\r\n.k-state-hover .k-pdf,\r\n.k-state-selected .k-pdf {\r\n  background-position: -288px -240px;\r\n}\r\n.k-print {\r\n  background-position: -288px -264px;\r\n}\r\n.k-state-hover .k-print,\r\n.k-state-selected .k-print {\r\n  background-position: -312px -264px;\r\n  background-position: -288px -264px;\r\n}\r\n.k-state-hover .k-print,\r\n.k-state-selected .k-print {\r\n  background-position: -288px -264px;\r\n}\r\n/* default tool widths */\r\n.k-fontName {\r\n  width: 110px;\r\n}\r\n.k-fontSize {\r\n  width: 124px;\r\n}\r\n.k-formatBlock {\r\n  width: 147px;\r\n}\r\n.k-editortoolbar-dragHandle {\r\n  float: left;\r\n  margin: 1px 0 0;\r\n}\r\n.k-editor-toolbar .k-button-group {\r\n  padding: 1px;\r\n}\r\n.k-editor .k-editor-toolbar .k-row-break {\r\n  display: block;\r\n  height: 0;\r\n  font-size: 0;\r\n  line-height: 0;\r\n}\r\n.k-button-group .k-tool {\r\n  border-style: solid;\r\n  border-width: 1px;\r\n  margin-right: -1px;\r\n}\r\n.k-button-group .k-tool.k-state-hover,\r\n.k-button-group .k-tool:focus {\r\n  position: relative;\r\n  z-index: 1;\r\n}\r\n.k-rtl .k-button-group .k-tool {\r\n  border-style: solid;\r\n  border-width: 1px;\r\n}\r\n.k-button-group .k-tool.k-group-end {\r\n  border-right-width: 1px;\r\n}\r\n.k-rtl .k-button-group .k-tool.k-group-end {\r\n  border-left-width: 1px;\r\n}\r\n.k-button-group .k-state-disabled {\r\n  display: none;\r\n}\r\n.k-button-group .k-state-hover,\r\n.k-button-group .k-state-active {\r\n  vertical-align: middle;\r\n}\r\n.k-button-group .k-state-disabled {\r\n  filter: alpha(opacity=30);\r\n  opacity: .3;\r\n}\r\n.k-editor .k-editable-area {\r\n  width: 100%;\r\n  height: 100%;\r\n  border-style: solid;\r\n  border-width: 1px;\r\n  outline: 0;\r\n}\r\n.k-editor .k-content {\r\n  display: block;\r\n  width: 100%;\r\n  height: 100%;\r\n  border: 0;\r\n  margin: 0;\r\n  padding: 0;\r\n  background: #fff;\r\n}\r\n.k-editor .k-tool {\r\n  outline: 0;\r\n}\r\n.k-editor iframe.k-content {\r\n  display: inline;\r\n  vertical-align: top;\r\n  /*fixes missing top border caused by the inline display*/\r\n}\r\n.k-editor .k-raw-content {\r\n  border: 0;\r\n  margin: 0;\r\n  padding: 0;\r\n}\r\n.k-editor .k-raw-content,\r\n.k-editor-dialog .k-editor-textarea {\r\n  font-size: inherit;\r\n  font-family: consolas, \"courier new\", monospace;\r\n}\r\n.k-editor-dialog {\r\n  padding: 1em;\r\n  width: 400px;\r\n}\r\n.k-editor-dialog .k-edit-label {\r\n  width: 25%;\r\n}\r\n.k-editor-dialog .k-edit-field {\r\n  width: 66%;\r\n}\r\n.k-editor-dialog .k-edit-field .k-textbox {\r\n  width: 96%;\r\n}\r\n.k-viewhtml-dialog {\r\n  width: auto;\r\n}\r\n.k-filebrowser-dialog {\r\n  width: auto;\r\n  min-width: 350px;\r\n}\r\n.k-filebrowser-dialog .k-filebrowser {\r\n  margin: 0 1em 0;\r\n}\r\n.k-filebrowser-dialog .k-edit-label {\r\n  width: 18%;\r\n}\r\n.k-filebrowser-dialog .k-edit-field {\r\n  width: 75%;\r\n}\r\n.k-filebrowser-dialog .k-edit-field .k-textbox {\r\n  width: 70%;\r\n}\r\n#k-editor-image-width,\r\n#k-editor-image-height {\r\n  width: 5em;\r\n}\r\n.k-editor-dialog .k-button {\r\n  display: inline-block;\r\n}\r\n.k-editor-dialog .k-editor-textarea {\r\n  width: 600px;\r\n  height: 350px;\r\n  padding: .2em .2em .2em .4em;\r\n  border-width: 1px;\r\n  border-style: solid;\r\n  overflow: auto;\r\n}\r\n.k-button-wrapper .k-link:hover {\r\n  text-decoration: underline;\r\n}\r\n.k-ct-popup {\r\n  width: 180.4px;\r\n  padding: .65em .5em .5em;\r\n}\r\n.k-ct-popup .k-status {\r\n  margin: .3em 0;\r\n}\r\n.k-ct-cell {\r\n  border-width: 1px;\r\n  border-style: solid;\r\n  width: 18px;\r\n  height: 18px;\r\n  margin: 1px;\r\n  vertical-align: top;\r\n  display: inline-block;\r\n  overflow: hidden;\r\n  -ms-high-contrast-adjust: none;\r\n}\r\n.k-editor .k-resize-handle {\r\n  position: absolute;\r\n  padding: 5px;\r\n  right: 0;\r\n  bottom: 0;\r\n}\r\n.k-editor .k-overlay {\r\n  position: absolute;\r\n  background-color: #fff;\r\n  opacity: 0;\r\n}\r\n.k-toolbar-resizable {\r\n  min-height: 2.4375em;\r\n  position: relative;\r\n}\r\n.k-overflow-tools {\r\n  position: absolute;\r\n  right: 0;\r\n  top: 0;\r\n}\r\n.k-editor-overflow-popup .k-tool {\r\n  width: auto;\r\n  height: auto;\r\n  display: block;\r\n  text-align: left;\r\n  text-decoration: initial;\r\n  border: 0;\r\n  padding-right: 0.5em;\r\n  margin: 0;\r\n  padding: 0 6px;\r\n}\r\n.k-editor-overflow-popup .k-tool.k-state-disabled {\r\n  display: none;\r\n}\r\n.k-editor-toolbar .k-tool-text {\r\n  display: none;\r\n}\r\n/* Notification */\r\n.k-notification-wrap {\r\n  padding: .6em .5em;\r\n  cursor: default;\r\n  position: relative;\r\n  white-space: nowrap;\r\n}\r\n.k-notification-button .k-notification-wrap {\r\n  padding-right: 20px;\r\n}\r\n.k-notification-wrap > .k-i-note {\r\n  vertical-align: text-bottom;\r\n  margin-right: 4px;\r\n}\r\n.k-notification-wrap > .k-i-close {\r\n  position: absolute;\r\n  top: 7px;\r\n  right: 4px;\r\n  display: none;\r\n}\r\n.k-notification-button .k-notification-wrap > .k-i-close {\r\n  display: block;\r\n}\r\n/* Progressbar */\r\n.k-progressbar {\r\n  display: inline-block;\r\n  position: relative;\r\n  vertical-align: middle;\r\n}\r\n.k-progressbar {\r\n  border-radius: 4px;\r\n}\r\n.k-progressbar-horizontal {\r\n  width: 27em;\r\n  height: 1.9em;\r\n}\r\n.k-progressbar-vertical {\r\n  width: 1.9em;\r\n  height: 27em;\r\n}\r\n.k-progressbar > .k-state-selected {\r\n  position: absolute;\r\n  border-style: solid;\r\n  border-width: 1px;\r\n  overflow: hidden;\r\n}\r\n.k-progressbar-horizontal > .k-state-selected,\r\n.k-rtl .k-progressbar-horizontal.k-progressbar-reverse > .k-state-selected {\r\n  left: -1px;\r\n  right: auto;\r\n  top: -1px;\r\n  height: 100%;\r\n  border-radius: 4px 0 0 4px;\r\n}\r\n.k-progressbar-horizontal.k-progressbar-reverse > .k-state-selected,\r\n.k-rtl .k-progressbar-horizontal > .k-state-selected {\r\n  left: auto;\r\n  right: -1px;\r\n  border-radius: 0 4px 4px 0;\r\n}\r\n.k-progressbar-vertical > .k-state-selected {\r\n  left: -1px;\r\n  bottom: -1px;\r\n  width: 100%;\r\n  border-radius: 0 0 4px 4px;\r\n}\r\n.k-progressbar-vertical.k-progressbar-reverse > .k-state-selected {\r\n  bottom: auto;\r\n  top: -1px;\r\n  border-radius: 4px 4px 0 0;\r\n}\r\n.k-progressbar > .k-state-selected.k-complete,\r\n.k-rtl .k-progressbar > .k-state-selected.k-complete {\r\n  border-radius: 4px;\r\n}\r\n.k-progressbar > .k-reset {\r\n  list-style: none;\r\n  margin: 0;\r\n  padding: 0;\r\n  position: absolute;\r\n  left: -1px;\r\n  top: -1px;\r\n  width: 100%;\r\n  height: 100%;\r\n  border-radius: 4px;\r\n  white-space: nowrap;\r\n}\r\n.k-progressbar-horizontal .k-item {\r\n  display: inline-block;\r\n  height: 100%;\r\n  border-style: solid;\r\n  margin-left: -1px;\r\n}\r\n.k-progressbar-horizontal .k-item.k-first {\r\n  margin-left: 0;\r\n}\r\n.k-progressbar-horizontal .k-item.k-last {\r\n  border-right-width: 0;\r\n}\r\n.k-progressbar-horizontal .k-item,\r\n.k-rtl .k-progressbar-horizontal.k-progressbar-reverse .k-item {\r\n  border-width: 1px 0 1px 1px;\r\n}\r\n.k-progressbar-horizontal.k-progressbar-reverse .k-item,\r\n.k-rtl .k-progressbar-horizontal .k-item {\r\n  border-width: 1px 0 1px 1px;\r\n}\r\n.k-progressbar-horizontal .k-first,\r\n.k-rtl .k-progressbar-horizontal .k-last,\r\n.k-rtl .k-progressbar-horizontal.k-progressbar-reverse .k-last {\r\n  border-top-left-radius: 4px;\r\n  border-bottom-left-radius: 4px;\r\n  border-left-width: 1px;\r\n}\r\n.k-progressbar-horizontal .k-last,\r\n.k-rtl .k-progressbar-horizontal .k-first {\r\n  border-top-right-radius: 4px;\r\n  border-bottom-right-radius: 4px;\r\n}\r\n.k-progressbar-horizontal.k-progressbar-reverse .k-last,\r\n.k-rtl .k-progressbar-horizontal .k-first {\r\n  border-right-width: 1px;\r\n}\r\n.k-progressbar-horizontal .k-last.k-state-selected {\r\n  border-right-width: 1px;\r\n}\r\n.k-progressbar-vertical .k-item {\r\n  width: 100%;\r\n  border-style: solid;\r\n  border-width: 1px 1px 0 1px;\r\n  margin-top: -1px;\r\n}\r\n.k-progressbar-vertical .k-item.k-first {\r\n  margin-top: 0;\r\n}\r\n.k-progressbar-vertical li.k-item.k-last {\r\n  border-bottom-width: 0;\r\n}\r\n.k-progressbar-vertical .k-first {\r\n  border-top-left-radius: 4px;\r\n  border-top-right-radius: 4px;\r\n}\r\n.k-progressbar-vertical .k-last {\r\n  border-bottom-left-radius: 4px;\r\n  border-bottom-right-radius: 4px;\r\n  border-bottom-width: 1px;\r\n}\r\n.k-progressbar-vertical.k-progressbar-reverse .k-item {\r\n  border-width: 0 1px 1px 1px;\r\n}\r\n.k-progressbar-vertical.k-progressbar-reverse .k-first {\r\n  border-top-width: 1px;\r\n}\r\n.k-progress-status-wrap {\r\n  position: absolute;\r\n  top: -1px;\r\n  border: 1px solid transparent;\r\n  line-height: 2em;\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n.k-progress-status-wrap,\r\n.k-rtl .k-progressbar-horizontal.k-progressbar-reverse .k-progress-status-wrap {\r\n  left: -1px;\r\n  right: auto;\r\n  text-align: right;\r\n}\r\n.k-progressbar-horizontal.k-progressbar-reverse .k-progress-status-wrap,\r\n.k-rtl .k-progressbar-horizontal .k-progress-status-wrap {\r\n  left: auto;\r\n  right: -1px;\r\n  text-align: left;\r\n}\r\n.k-progressbar-vertical .k-progress-status-wrap {\r\n  top: auto;\r\n  bottom: -1px;\r\n}\r\n.k-progressbar-vertical.k-progressbar-reverse .k-progress-status-wrap {\r\n  bottom: auto;\r\n  top: -1px;\r\n}\r\n.k-progress-status {\r\n  display: inline-block;\r\n  padding: 0 .5em;\r\n  min-width: 10px;\r\n  white-space: nowrap;\r\n}\r\n.k-progressbar-vertical.k-progressbar-reverse .k-progress-status {\r\n  position: absolute;\r\n  bottom: 0;\r\n  left: 0;\r\n}\r\n.k-progressbar-vertical .k-progress-status {\r\n  -webkit-transform: rotate(-90deg) translateX(-100%);\r\n      -ms-transform: rotate(-90deg) translateX(-100%);\r\n          transform: rotate(-90deg) translateX(-100%);\r\n  -webkit-transform-origin: 0 0;\r\n      -ms-transform-origin: 0 0;\r\n          transform-origin: 0 0;\r\n}\r\n.k-progressbar-vertical.k-progressbar-reverse .k-progress-status {\r\n  -webkit-transform: rotate(90deg) translateX(-100%);\r\n      -ms-transform: rotate(90deg) translateX(-100%);\r\n          transform: rotate(90deg) translateX(-100%);\r\n  -webkit-transform-origin: 0 100%;\r\n      -ms-transform-origin: 0 100%;\r\n          transform-origin: 0 100%;\r\n}\r\n.k-ie8 .k-progressbar-vertical .k-progress-status {\r\n  -webkit-writing-mode: bt-lr;\r\n      -ms-writing-mode: bt-lr;\r\n          writing-mode: bt-lr;\r\n  padding: .5em 0;\r\n}\r\n/* Slider */\r\ndiv.k-slider {\r\n  position: relative;\r\n  border-width: 0;\r\n  background-color: transparent;\r\n  -webkit-user-select: none;\r\n     -moz-user-select: none;\r\n      -ms-user-select: none;\r\n          user-select: none;\r\n}\r\n.k-slider-vertical {\r\n  width: 26px;\r\n  height: 200px;\r\n  /* default height */\r\n}\r\n.k-slider-horizontal {\r\n  display: inline-block;\r\n  width: 200px;\r\n  /* default width */\r\n  height: 26px;\r\n}\r\n.k-slider-wrap {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n.k-slider .k-button,\r\n.k-grid .k-slider .k-button {\r\n  position: absolute;\r\n  top: 0;\r\n  width: 24px;\r\n  min-width: 0;\r\n  height: 24px;\r\n  margin: 0;\r\n  padding: 0;\r\n  outline: 0;\r\n}\r\n.k-slider .k-button .k-icon {\r\n  margin-top: 3px;\r\n  vertical-align: top;\r\n}\r\n.k-state-disabled .k-slider-wrap {\r\n  filter: alpha(opacity=60);\r\n  opacity: .6;\r\n}\r\n.k-state-disabled .k-slider-wrap .k-slider-items {\r\n  color: #333;\r\n}\r\n.k-slider .k-button-decrease {\r\n  left: 0;\r\n}\r\n.k-slider-vertical .k-button-decrease,\r\n.k-grid .k-slider-vertical .k-button-decrease {\r\n  top: auto;\r\n  bottom: 0;\r\n}\r\n.k-slider .k-button-increase {\r\n  right: 0;\r\n}\r\n.k-slider .k-icon,\r\n.k-slider-track,\r\n.k-slider .k-tick {\r\n  cursor: pointer;\r\n}\r\n.k-slider-track,\r\n.k-slider-selection {\r\n  position: absolute;\r\n  margin: 0;\r\n  padding: 0;\r\n}\r\n.k-slider-horizontal .k-slider-track,\r\n.k-slider-horizontal .k-slider-selection {\r\n  top: 50%;\r\n  left: 0;\r\n  height: 8px;\r\n  margin-top: -4px;\r\n  background-repeat: repeat-x;\r\n}\r\n.k-slider-horizontal .k-slider-buttons .k-slider-track {\r\n  left: 34px;\r\n}\r\n.k-slider-vertical .k-slider-track,\r\n.k-slider-vertical .k-slider-selection {\r\n  left: 50%;\r\n  bottom: 0;\r\n  width: 8px;\r\n  margin-left: -4px;\r\n  background-repeat: repeat-y;\r\n}\r\n.k-slider-vertical .k-slider-buttons .k-slider-track {\r\n  bottom: 34px;\r\n}\r\n.k-draghandle {\r\n  position: absolute;\r\n  background-repeat: no-repeat;\r\n  background-color: transparent;\r\n  text-indent: -3333px;\r\n  overflow: hidden;\r\n  text-decoration: none;\r\n  text-align: center;\r\n  outline: 0;\r\n}\r\n.k-slider-horizontal .k-draghandle {\r\n  top: -4px;\r\n  width: 13px;\r\n  height: 14px;\r\n}\r\n.k-slider-vertical .k-draghandle {\r\n  left: -4px;\r\n  width: 14px;\r\n  height: 13px;\r\n}\r\n.k-slider-buttons .k-slider-items {\r\n  margin-left: 34px;\r\n}\r\n.k-slider-horizontal .k-slider-items {\r\n  height: 100%;\r\n}\r\n.k-slider-vertical .k-slider-items {\r\n  padding-top: 1px;\r\n}\r\n.k-slider-vertical .k-slider-buttons .k-slider-items {\r\n  padding-top: 0;\r\n}\r\n.k-slider-vertical .k-slider-buttons .k-slider-items {\r\n  margin: 0;\r\n  padding-top: 35px;\r\n}\r\n.k-slider .k-tick {\r\n  position: relative;\r\n  margin: 0;\r\n  padding: 0;\r\n  background-color: transparent;\r\n  background-repeat: no-repeat;\r\n  background-position: center center;\r\n}\r\n.k-slider-horizontal .k-tick {\r\n  float: left;\r\n  height: 100%;\r\n  text-align: center;\r\n}\r\n.k-slider-horizontal .k-tick {\r\n  background-position: center -92px;\r\n}\r\n.k-slider-horizontal .k-slider-topleft .k-tick {\r\n  background-position: center -122px;\r\n}\r\n.k-slider-horizontal .k-slider-bottomright .k-tick {\r\n  background-position: center -152px;\r\n}\r\n.k-slider-horizontal .k-tick-large {\r\n  background-position: center -2px;\r\n}\r\n.k-slider-horizontal .k-slider-topleft .k-tick-large {\r\n  background-position: center -32px;\r\n}\r\n.k-slider-horizontal .k-slider-bottomright .k-tick-large {\r\n  background-position: center -62px;\r\n}\r\n.k-slider-vertical .k-tick {\r\n  background-position: -92px center;\r\n}\r\n.k-slider-vertical .k-slider-topleft .k-tick {\r\n  background-position: -122px center;\r\n}\r\n.k-slider-vertical .k-slider-bottomright .k-tick {\r\n  background-position: -152px center;\r\n}\r\n.k-slider-vertical .k-tick-large {\r\n  background-position: -2px center;\r\n}\r\n.k-slider-vertical .k-slider-topleft .k-tick-large {\r\n  background-position: -32px center;\r\n}\r\n.k-slider-vertical .k-slider-bottomright .k-tick-large {\r\n  background-position: -62px center;\r\n}\r\n.k-slider-horizontal .k-first {\r\n  background-position: 0 -92px;\r\n}\r\n.k-slider-horizontal .k-tick-large.k-first {\r\n  background-position: 0 -2px;\r\n}\r\n.k-slider-horizontal .k-slider-topleft .k-first {\r\n  background-position: 0 -122px;\r\n}\r\n.k-slider-horizontal .k-slider-topleft .k-tick-large.k-first {\r\n  background-position: 0 -32px;\r\n}\r\n.k-slider-horizontal .k-slider-bottomright .k-first {\r\n  background-position: 0 -152px;\r\n}\r\n.k-slider-horizontal .k-slider-bottomright .k-tick-large.k-first {\r\n  background-position: 0 -62px;\r\n}\r\n.k-slider-horizontal .k-last {\r\n  background-position: 100% -92px;\r\n}\r\n.k-slider-horizontal .k-tick-large.k-last {\r\n  background-position: 100% -2px;\r\n}\r\n.k-slider-horizontal .k-slider-topleft .k-last {\r\n  background-position: 100% -122px;\r\n}\r\n.k-slider-horizontal .k-slider-topleft .k-tick-large.k-last {\r\n  background-position: 100% -32px;\r\n}\r\n.k-slider-horizontal .k-slider-bottomright .k-last {\r\n  background-position: 100% -152px;\r\n}\r\n.k-slider-horizontal .k-slider-bottomright .k-tick-large.k-last {\r\n  background-position: 100% -62px;\r\n}\r\n.k-slider-vertical .k-first {\r\n  background-position: -92px 100%;\r\n}\r\n.k-slider-vertical .k-tick-large.k-first {\r\n  background-position: -2px 100%;\r\n}\r\n.k-slider-vertical .k-slider-topleft .k-first {\r\n  background-position: -122px 100%;\r\n}\r\n.k-slider-vertical .k-slider-topleft .k-tick-large.k-first {\r\n  background-position: -32px 100%;\r\n}\r\n.k-slider-vertical .k-slider-bottomright .k-first {\r\n  background-position: -152px 100%;\r\n}\r\n.k-slider-vertical .k-slider-bottomright .k-tick-large.k-first {\r\n  background-position: -62px 100%;\r\n}\r\n.k-slider-vertical .k-last {\r\n  background-position: -92px 0;\r\n}\r\n.k-slider-vertical .k-tick-large.k-last {\r\n  background-position: -2px 0;\r\n}\r\n.k-slider-vertical .k-slider-topleft .k-last {\r\n  background-position: -122px 0;\r\n}\r\n.k-slider-vertical .k-slider-topleft .k-tick-large.k-last {\r\n  background-position: -32px 0;\r\n}\r\n.k-slider-vertical .k-slider-bottomright .k-last {\r\n  background-position: -152px 0;\r\n}\r\n.k-slider-vertical .k-slider-bottomright .k-tick-large.k-last {\r\n  background-position: -62px 0;\r\n}\r\n.k-slider-vertical .k-tick {\r\n  text-align: right;\r\n}\r\n.k-slider-vertical .k-slider-topleft .k-tick {\r\n  text-align: left;\r\n}\r\n.k-slider .k-label {\r\n  position: absolute;\r\n  white-space: nowrap;\r\n  font-size: .92em;\r\n}\r\n.k-slider-horizontal .k-label {\r\n  left: 0;\r\n  width: 100%;\r\n  line-height: 1;\r\n}\r\n.k-slider-horizontal .k-first .k-label {\r\n  left: -50%;\r\n}\r\n.k-slider-horizontal .k-last .k-label {\r\n  left: auto;\r\n  right: -50%;\r\n}\r\n.k-slider-horizontal .k-label {\r\n  bottom: -1.2em;\r\n}\r\n.k-slider-horizontal .k-slider-topleft .k-label {\r\n  top: -1.2em;\r\n}\r\n.k-slider-vertical .k-label {\r\n  left: 120%;\r\n  display: block;\r\n  text-align: left;\r\n}\r\n.k-slider-vertical .k-last .k-label {\r\n  top: -0.5em;\r\n}\r\n.k-slider-vertical .k-first .k-label {\r\n  bottom: -0.5em;\r\n}\r\n.k-slider-vertical .k-slider-topleft .k-label {\r\n  left: auto;\r\n  right: 120%;\r\n}\r\n.k-slider-tooltip {\r\n  top: -4444px;\r\n  /*prevent window resize in IE8 when appending*/\r\n}\r\n/* Scheduler */\r\n.k-scheduler-toolbar,\r\n.k-scheduler-footer {\r\n  border-style: solid;\r\n}\r\n.k-scheduler-toolbar,\r\n.k-scheduler-footer {\r\n  line-height: 28px;\r\n  padding: 6px;\r\n}\r\n.k-scheduler-toolbar {\r\n  position: relative;\r\n  border-width: 0 0 1px;\r\n}\r\n.k-edit-field.k-scheduler-toolbar {\r\n  border-width: 0;\r\n  padding-top: 0;\r\n  padding-left: 0;\r\n  padding-right: 0;\r\n}\r\n.k-scheduler-header {\r\n  text-align: center;\r\n}\r\n.k-scheduler-footer {\r\n  border-width: 1px 0 0;\r\n}\r\n.k-scheduler-toolbar > ul {\r\n  float: right;\r\n}\r\n.k-scheduler-toolbar > ul:first-child {\r\n  float: left;\r\n}\r\n.k-scheduler-toolbar > .k-scheduler-tools {\r\n  float: left;\r\n  margin-bottom: .5em;\r\n}\r\n.k-scheduler-tools + .k-scheduler-navigation {\r\n  float: left;\r\n  clear: left;\r\n}\r\n.k-scheduler-toolbar > ul > li,\r\n.k-scheduler-footer > ul > li {\r\n  display: inline-block;\r\n  border-style: solid;\r\n  border-width: 1px 1px 1px 0;\r\n}\r\n.k-scheduler .k-scheduler-toolbar .k-nav-current,\r\n.k-scheduler .k-scheduler-toolbar .k-scheduler-tools > li {\r\n  border-width: 0;\r\n}\r\n.k-scheduler-toolbar > ul > li:first-child,\r\n.k-scheduler-toolbar > ul.k-scheduler-views > li:first-child + li {\r\n  border-left-width: 1px;\r\n}\r\n.k-scheduler div.k-scheduler-footer ul li {\r\n  margin-right: .6em;\r\n  border-width: 1px;\r\n}\r\n.k-scheduler-toolbar .k-link,\r\n.k-scheduler-footer .k-link {\r\n  display: inline-block;\r\n  padding: 0 1.1em;\r\n}\r\n.k-scheduler-toolbar .k-nav-prev .k-link,\r\n.k-scheduler-toolbar .k-nav-next .k-link {\r\n  padding-left: .6em;\r\n  padding-right: .6em;\r\n}\r\n.k-scheduler-toolbar .k-nav-current .k-link {\r\n  padding: 0;\r\n}\r\n.k-scheduler-toolbar .k-nav-current {\r\n  margin: 0 1.1em;\r\n}\r\n.k-scheduler div.k-scheduler-toolbar > ul > li.k-nav-current,\r\n.k-scheduler .k-nav-current > .k-state-active {\r\n  background: none;\r\n}\r\n.k-scheduler-phone .k-scheduler-toolbar + .k-scheduler-toolbar .k-scheduler-navigation {\r\n  width: 100%;\r\n  text-align: center;\r\n}\r\n.k-scheduler-phone .k-scheduler-toolbar + .k-scheduler-toolbar .k-scheduler-navigation > li {\r\n  background: none;\r\n  border: 0;\r\n}\r\n.k-scheduler-phone .k-toolbar .k-nav-next {\r\n  float: right;\r\n}\r\n.k-scheduler-phone .k-toolbar .k-nav-prev {\r\n  float: left;\r\n}\r\n.k-scheduler-toolbar .k-i-calendar,\r\n.k-scheduler-footer .k-icon {\r\n  margin: -2px 6px 0 0;\r\n}\r\n.k-scheduler-header,\r\n.k-scheduler-header-wrap {\r\n  overflow: hidden;\r\n}\r\n.k-scheduler-header-wrap {\r\n  position: relative;\r\n  border-style: solid;\r\n  border-width: 0;\r\n}\r\n.k-scheduler .k-scrollbar-v .k-scheduler-header-wrap {\r\n  border-right-width: 1px;\r\n}\r\n.k-scheduler-times,\r\n.k-scheduler-content {\r\n  position: relative;\r\n}\r\n.k-scheduler-times {\r\n  overflow: hidden;\r\n  border-style: solid;\r\n  border-width: 0;\r\n}\r\n.k-scheduler-content {\r\n  overflow: auto;\r\n}\r\n.k-scheduler-layout,\r\n.k-scheduler-table {\r\n  border-spacing: 0;\r\n  width: 100%;\r\n  margin: 0;\r\n  border-collapse: separate;\r\n}\r\n.k-scheduler-layout > tbody > tr > td {\r\n  padding: 0;\r\n  vertical-align: top;\r\n}\r\n/* fix smashed second layout column in iPad */\r\n.k-safari .k-scheduler-layout > tbody > tr > td + td {\r\n  width: 100%;\r\n}\r\n.k-scheduler-table {\r\n  table-layout: fixed;\r\n  max-width: none;\r\n}\r\n.k-scheduler-times .k-scheduler-table {\r\n  table-layout: auto;\r\n}\r\n.k-scheduler-monthview .k-scheduler-content .k-scheduler-table {\r\n  height: 100%;\r\n}\r\n.k-scheduler-table td,\r\n.k-scheduler-table th {\r\n  height: 1.5em;\r\n  padding: .334em .5em;\r\n  font-size: 100%;\r\n}\r\n.k-scheduler .k-scheduler-table td,\r\n.k-scheduler .k-scheduler-table th {\r\n  -webkit-box-sizing: content-box;\r\n          box-sizing: content-box;\r\n}\r\n.k-scheduler-monthview .k-hidden,\r\n.k-scheduler-monthview .k-hidden > div {\r\n  width: 0 !important;\r\n  overflow: hidden !important;\r\n}\r\n.k-scheduler-monthview .k-hidden {\r\n  padding-left: 0 !important;\r\n  padding-right: 0 !important;\r\n  border-right-width: 0 !important;\r\n}\r\n.k-scheduler-monthview > tbody > tr:first-child .k-scheduler-times {\r\n  margin-right: 1px;\r\n}\r\n.k-scheduler-monthview > tbody > tr:first-child .k-scheduler-times .k-hidden {\r\n  height: auto;\r\n}\r\n.k-scheduler-monthview .k-scheduler-table td,\r\n.k-scheduler-monthview .k-hidden {\r\n  height: 80px;\r\n  text-align: right;\r\n}\r\n.k-scheduler-phone .k-scheduler-monthview .k-scheduler-table td,\r\n.k-scheduler-phone .k-scheduler-monthview .k-hidden {\r\n  height: 40px;\r\n}\r\n.k-scheduler-table td,\r\n.k-slot-cell {\r\n  vertical-align: top;\r\n}\r\n/* separate due to old IEs */\r\n.k-scheduler-layout tr + tr .k-scheduler-times th:last-child {\r\n  vertical-align: top;\r\n}\r\n.k-scheduler-phone .k-scheduler-monthview .k-scheduler-table td {\r\n  text-align: center;\r\n  vertical-align: middle;\r\n}\r\n.k-scheduler-phone .k-scheduler-monthview .k-scheduler-table td span {\r\n  font-size: 1.5em;\r\n}\r\n.k-scheduler-header th {\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n}\r\n.k-scheduler-table td,\r\n.k-scheduler-header th {\r\n  border-style: solid;\r\n  border-width: 0 0 1px 1px;\r\n}\r\n.k-scheduler-table td:first-child,\r\n.k-scheduler-header th:first-child {\r\n  border-left-width: 0;\r\n}\r\n.k-scheduler-agendaview .k-scheduler-table td:first-child {\r\n  border-left-width: 1px;\r\n}\r\n.k-scheduler-agendaview .k-scheduler-table td.k-first {\r\n  border-left-width: 0;\r\n}\r\n.k-scheduler-layout tr + tr .k-scheduler-times tr:last-child > th,\r\n.k-scheduler-layout tr + tr .k-scheduler-table > tbody > tr:last-child > td,\r\n.k-scheduler-table > tbody > tr > .k-last {\r\n  border-bottom-width: 0;\r\n}\r\n.k-scrollbar-h tr + tr .k-scheduler-times,\r\n.k-scrollbar-h .k-scheduler-content .k-scheduler-table > tbody > tr:last-child > td,\r\n.k-scheduler-agendaview.k-scrollbar-h .k-scheduler-table > tbody > tr > td.k-last {\r\n  border-bottom-width: 1px;\r\n}\r\n.k-scheduler-times th {\r\n  text-align: right;\r\n  padding-right: .6em;\r\n  border-style: solid;\r\n  border-width: 0 1px 1px 0;\r\n  border-color: transparent;\r\n  white-space: nowrap;\r\n}\r\n.k-scheduler-layout tr + tr .k-scheduler-times th {\r\n  border-bottom-color: transparent;\r\n}\r\n.k-scheduler-layout tr + tr .k-scheduler-times th.k-slot-cell,\r\n.k-scheduler-layout tr + tr .k-scheduler-times th.k-scheduler-times-all-day {\r\n  border-bottom-color: inherit;\r\n}\r\n.k-scheduler .k-middle-row td {\r\n  border-bottom-style: dotted;\r\n}\r\n.k-scheduler-now-arrow,\r\n.k-scheduler-now-line {\r\n  position: absolute;\r\n}\r\n.k-scheduler-now-arrow {\r\n  width: 0;\r\n  height: 0;\r\n  border: solid 5px transparent;\r\n  left: 0;\r\n}\r\n.k-scheduler-now-line {\r\n  left: 5px;\r\n  right: 0;\r\n  height: 1px;\r\n}\r\n.k-task {\r\n  position: relative;\r\n}\r\ndiv.k-more-events {\r\n  text-align: center;\r\n  font-size: 18px;\r\n  line-height: 1.2;\r\n  padding: 0;\r\n}\r\n.k-more-events > span {\r\n  display: block;\r\n  margin-top: -0.6em;\r\n}\r\n.k-event,\r\n.k-more-events {\r\n  position: absolute;\r\n  border-style: solid;\r\n  border-width: 1px;\r\n  text-align: left;\r\n  overflow: hidden;\r\n}\r\n.k-event {\r\n  cursor: default;\r\n  min-height: 1.3em;\r\n}\r\n.k-event-drag-hint {\r\n  filter: alpha(opacity=60);\r\n  opacity: .6;\r\n  cursor: -webkit-grabbing;\r\n  cursor: -moz-grabbing;\r\n}\r\n.k-scheduler-header .k-event {\r\n  white-space: nowrap;\r\n}\r\n.k-event-template {\r\n  padding: .3em 1.4em .3em .6em;\r\n}\r\n.k-event-time {\r\n  display: none;\r\n  padding-bottom: 0;\r\n  font-size: .9em;\r\n}\r\n.k-event-drag-hint .k-event-time {\r\n  display: block;\r\n}\r\n.k-event-actions,\r\n.k-event > .k-link,\r\n.k-task > .k-link {\r\n  position: absolute;\r\n  top: 3px;\r\n  right: 4px;\r\n  white-space: nowrap;\r\n}\r\n.k-event-actions {\r\n  z-index: 1;\r\n}\r\n.k-scheduler-agendaview .k-task > .k-link {\r\n  top: 0;\r\n  right: 0;\r\n}\r\n.k-event-actions:first-child {\r\n  position: static;\r\n  float: left;\r\n  margin: 4px 2px 0 4px;\r\n}\r\n.k-webkit .k-event-actions:first-child {\r\n  margin-top: 3px;\r\n}\r\n.k-event-actions:first-child > .k-link {\r\n  display: inline-block;\r\n}\r\n.k-event-delete {\r\n  display: none;\r\n}\r\n.k-event:hover .k-event-delete,\r\ntr:hover > td > .k-task .k-event-delete {\r\n  display: inline-block;\r\n}\r\n.k-event .k-event-top-actions,\r\n.k-event .k-event-bottom-actions {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  text-align: center;\r\n}\r\n.k-event .k-event-bottom-actions {\r\n  top: auto;\r\n  bottom: 0;\r\n}\r\n.k-event .k-resize-handle,\r\n.k-scheduler-mobile .k-event:hover .k-resize-handle {\r\n  position: absolute;\r\n  visibility: hidden;\r\n  z-index: 2;\r\n}\r\n.k-event:hover .k-resize-handle,\r\n.k-event-active .k-resize-handle,\r\n.k-scheduler-mobile .k-event-active:hover .k-resize-handle {\r\n  visibility: visible;\r\n}\r\n.k-event .k-resize-handle:after {\r\n  content: \"\";\r\n  position: absolute;\r\n  filter: alpha(opacity=50);\r\n  opacity: .5;\r\n}\r\n.k-scheduler-mobile .k-event .k-resize-handle:after {\r\n  -webkit-filter: none;\r\n          filter: none;\r\n  opacity: 1;\r\n}\r\n.k-event > .k-resize-n {\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: .4em;\r\n}\r\n.k-event > .k-resize-s {\r\n  bottom: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: .4em;\r\n}\r\n.k-event > .k-resize-e {\r\n  right: 0;\r\n  top: 0;\r\n  bottom: 0;\r\n  width: .4em;\r\n}\r\n.k-event > .k-resize-w {\r\n  left: 0;\r\n  top: 0;\r\n  bottom: 0;\r\n  width: .4em;\r\n}\r\n.k-event > .k-resize-n:after,\r\n.k-event > .k-resize-s:after {\r\n  top: 1px;\r\n  left: 50%;\r\n  margin-left: -1em;\r\n  width: 2em;\r\n  height: 1px;\r\n}\r\n.k-event > .k-resize-s:after {\r\n  top: auto;\r\n  bottom: 1px;\r\n}\r\n.k-event > .k-resize-e:after,\r\n.k-event > .k-resize-w:after {\r\n  left: 1px;\r\n  top: 50%;\r\n  margin-top: -0.7em;\r\n  height: 1.4em;\r\n  width: 1px;\r\n}\r\n.k-event > .k-resize-e:after {\r\n  left: auto;\r\n  right: 1px;\r\n}\r\n.k-scheduler-mobile .k-event > .k-resize-n,\r\n.k-scheduler-mobile .k-event > .k-resize-s {\r\n  height: .6em;\r\n}\r\n.k-scheduler-mobile .k-event > .k-resize-e,\r\n.k-scheduler-mobile .k-event > .k-resize-w {\r\n  width: .6em;\r\n}\r\n.k-scheduler-mobile .k-event > .k-resize-n:after,\r\n.k-scheduler-mobile .k-event > .k-resize-s:after {\r\n  top: 0;\r\n  margin-left: -3em;\r\n  width: 4em;\r\n  height: .6em;\r\n}\r\n.k-scheduler-mobile .k-event > .k-resize-s:after {\r\n  bottom: 0;\r\n}\r\n.k-scheduler-mobile .k-event > .k-resize-e:after,\r\n.k-scheduler-mobile .k-event > .k-resize-w:after {\r\n  left: 0;\r\n  margin-top: -0.7em;\r\n  height: 1.4em;\r\n  width: .6em;\r\n}\r\n.k-scheduler-mobile .k-event > .k-resize-e:after {\r\n  right: 0;\r\n}\r\n.k-scheduler-mobile .k-event > .k-resize-n:after {\r\n  border-radius: 0 0 4px 4px;\r\n}\r\n.k-scheduler-mobile .k-event > .k-resize-s:after {\r\n  border-radius: 4px 4px 0 0;\r\n}\r\n.k-scheduler-mobile .k-event > .k-resize-w:after {\r\n  border-radius: 0 4px 4px 0;\r\n}\r\n.k-scheduler-mobile .k-event > .k-resize-e:after {\r\n  border-radius: 4px 0 0 4px;\r\n}\r\n.k-scheduler-phone .k-scheduler-monthview .k-events-container {\r\n  position: absolute;\r\n  text-align: center;\r\n  height: 6px;\r\n  line-height: 6px;\r\n}\r\n.k-scheduler-phone .k-scheduler-monthview .k-event {\r\n  position: static;\r\n  display: inline-block;\r\n  width: 4px;\r\n  height: 4px;\r\n  min-height: 0;\r\n  margin: 1px;\r\n}\r\n.k-scheduler-marquee {\r\n  border-style: solid;\r\n  border-width: 0;\r\n}\r\n.k-scheduler-marquee.k-first:before,\r\n.k-scheduler-marquee.k-last:after {\r\n  content: \"\";\r\n  position: absolute;\r\n  width: 0;\r\n  height: 0;\r\n  border-style: solid;\r\n  border-width: 3px;\r\n}\r\ndiv.k-scheduler-marquee:before {\r\n  top: 0;\r\n  left: 0;\r\n  border-right-color: transparent;\r\n  border-bottom-color: transparent;\r\n}\r\ndiv.k-scheduler-marquee:after {\r\n  bottom: 0;\r\n  right: 0;\r\n  border-top-color: transparent;\r\n  border-left-color: transparent;\r\n}\r\n.k-scheduler-marquee .k-label-top {\r\n  position: absolute;\r\n  top: .3em;\r\n  left: .8em;\r\n  font-size: .8em;\r\n}\r\n.k-scheduler-marquee .k-label-bottom {\r\n  position: absolute;\r\n  bottom: .3em;\r\n  right: .81em;\r\n  font-size: .8em;\r\n}\r\n.k-scheduler-quickedit .k-textbox {\r\n  width: 200px;\r\n}\r\n.k-tooltip-bottom {\r\n  text-align: left;\r\n}\r\n.k-tooltip-bottom .k-button {\r\n  float: left;\r\n  margin-right: .3em;\r\n}\r\n.k-tooltip-bottom .k-quickedit-details {\r\n  float: right;\r\n  margin-right: 0;\r\n}\r\n.k-scheduler-agendaview .k-scheduler-table th,\r\n.k-scheduler-agendaview .k-scheduler-table td {\r\n  text-align: left;\r\n}\r\n.k-scheduler-times .k-slot-cell,\r\n.k-scheduler-groupcolumn {\r\n  width: 6em;\r\n}\r\n.k-scheduler-datecolumn {\r\n  width: 12em;\r\n}\r\n.k-scheduler-timecolumn {\r\n  width: 11em;\r\n}\r\n.k-scheduler-timecolumn > div {\r\n  position: relative;\r\n  vertical-align: top;\r\n}\r\n.k-webkit .k-scheduler-timecolumn > div > .k-icon {\r\n  vertical-align: top;\r\n}\r\n.k-scheduler-timecolumn > div > .k-i-arrow-e {\r\n  position: absolute;\r\n  right: -4px;\r\n}\r\n.k-scheduler-timecolumn .k-i-arrow-w {\r\n  margin-left: -4px;\r\n}\r\n.k-scheduler-mark {\r\n  display: inline-block;\r\n  width: 1em;\r\n  height: 1em;\r\n  vertical-align: middle;\r\n  margin-right: .5em;\r\n}\r\n.k-scheduler-agendaday {\r\n  float: left;\r\n  margin: 0 .2em 0 0;\r\n  font-size: 3em;\r\n  font-weight: normal;\r\n}\r\n.k-scheduler-agendaweek {\r\n  display: block;\r\n  margin: .4em 0 0;\r\n  font-size: 1.1em;\r\n  font-style: normal;\r\n}\r\n.k-scheduler-agendadate {\r\n  font-size: .8em;\r\n}\r\n.k-scheduler-timecolumn {\r\n  white-space: nowrap;\r\n}\r\n.k-scheduler-edit-form .k-edit-form-container,\r\n.k-scheduler-timezones .k-edit-form-container {\r\n  width: 520px;\r\n}\r\n.k-scheduler-edit-form .k-edit-label {\r\n  width: 17%;\r\n}\r\n.k-scheduler-edit-form .k-edit-field {\r\n  width: 77%;\r\n}\r\n.k-scheduler-edit-form .k-textbox[name=\"title\"],\r\n.k-scheduler-edit-form textarea.k-textbox {\r\n  width: 100%;\r\n}\r\n.k-scheduler-edit-form textarea.k-textbox {\r\n  min-height: 4em;\r\n  resize: vertical;\r\n}\r\n.k-scheduler-edit-form > .k-edit-box:first-child .k-datetimepicker {\r\n  margin-right: 1em;\r\n}\r\n.km-pane-wrapper .k-scheduler-edit-form .k-edit-buttons {\r\n  clear: right;\r\n  margin-right: 2%;\r\n  margin-left: 2%;\r\n  padding: 0 0 .6em;\r\n}\r\n.k-edit-box {\r\n  float: left;\r\n}\r\n.k-edit-box + .k-edit-box {\r\n  float: right;\r\n}\r\n.k-scheduler-edit-form label + input {\r\n  margin-left: 1em;\r\n}\r\n.k-edit-field > ul.k-reset > li {\r\n  margin: .2em 0 .4em;\r\n  line-height: 2.4;\r\n}\r\n.k-edit-field > ul.k-reset.k-toolbar > li {\r\n  margin: 0;\r\n}\r\n.k-edit-field > ul.k-reset .k-widget {\r\n  margin-left: .8em;\r\n}\r\n.k-edit-field > ul.k-reset .k-numerictextbox,\r\n.k-edit-field span.k-recur-interval {\r\n  width: 5em;\r\n}\r\n.k-edit-field > ul.k-reset .k-dropdown,\r\n.k-edit-field > ul.k-reset .k-datepicker,\r\ndiv[name=\"recurrenceRule\"] > .k-dropdown {\r\n  width: 9em;\r\n}\r\n.k-scheduler-edit-form .k-edit-buttons .k-scheduler-delete {\r\n  float: left;\r\n}\r\n.k-popup-message {\r\n  margin: 0;\r\n  padding: 1em 0 2em;\r\n  text-align: center;\r\n}\r\n.k-scheduler-timezones .k-dropdown:first-child {\r\n  width: 100%;\r\n}\r\n.k-scheduler-timezones .k-dropdown + .k-dropdown {\r\n  margin: .5em 0 .7em;\r\n}\r\n.k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view {\r\n  display: none;\r\n}\r\n.k-scheduler-toolbar li.k-nav-current .k-sm-date-format {\r\n  display: none;\r\n}\r\n.k-scheduler-refresh {\r\n  float: right;\r\n  padding: 0 .5em;\r\n}\r\n/* Responsive styles  */\r\n@media only screen and (max-width: 1024px) {\r\n  .k-webkit .k-scheduler-toolbar > ul.k-scheduler-views,\r\n  .k-ff .k-scheduler-toolbar > ul.k-scheduler-views,\r\n  .k-ie11 .k-scheduler-toolbar > ul.k-scheduler-views,\r\n  .k-safari .k-scheduler-toolbar > ul.k-scheduler-views {\r\n    position: absolute;\r\n    right: 6px;\r\n    top: 6px;\r\n    z-index: 10000;\r\n  }\r\n  .k-webkit .k-rtl .k-scheduler-toolbar > ul.k-scheduler-views,\r\n  .k-ff .k-rtl .k-scheduler-toolbar > ul.k-scheduler-views,\r\n  .k-ie11 .k-rtl .k-scheduler-toolbar > ul.k-scheduler-views,\r\n  .k-safari .k-rtl .k-scheduler-toolbar > ul.k-scheduler-views {\r\n    right: auto;\r\n    left: 6px;\r\n  }\r\n  .k-webkit .k-scheduler-toolbar > ul.k-scheduler-views > li:not(.k-current-view),\r\n  .k-ff .k-scheduler-toolbar > ul.k-scheduler-views > li:not(.k-current-view),\r\n  .k-ie11 .k-scheduler-toolbar > ul.k-scheduler-views > li:not(.k-current-view),\r\n  .k-safari .k-scheduler-toolbar > ul.k-scheduler-views > li:not(.k-current-view) {\r\n    display: none;\r\n  }\r\n  .k-webkit .k-scheduler-toolbar li.k-nav-current .k-lg-date-format,\r\n  .k-ff .k-scheduler-toolbar li.k-nav-current .k-lg-date-format,\r\n  .k-ie11 .k-scheduler-toolbar li.k-nav-current .k-lg-date-format,\r\n  .k-safari .k-scheduler-toolbar li.k-nav-current .k-lg-date-format {\r\n    display: none;\r\n  }\r\n  .k-webkit .k-scheduler-toolbar li.k-nav-current .k-sm-date-format,\r\n  .k-ff .k-scheduler-toolbar li.k-nav-current .k-sm-date-format,\r\n  .k-ie11 .k-scheduler-toolbar li.k-nav-current .k-sm-date-format,\r\n  .k-safari .k-scheduler-toolbar li.k-nav-current .k-sm-date-format {\r\n    display: inline;\r\n  }\r\n  .k-webkit .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view,\r\n  .k-ff .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view,\r\n  .k-ie11 .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view,\r\n  .k-safari .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view {\r\n    display: block;\r\n    border-width: 1px;\r\n  }\r\n  .k-webkit .k-rtl .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view,\r\n  .k-ff .k-rtl .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view,\r\n  .k-ie11 .k-rtl .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view,\r\n  .k-safari .k-rtl .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view {\r\n    text-align: left;\r\n    padding-left: 1em;\r\n  }\r\n  .k-webkit .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view > .k-link,\r\n  .k-ff .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view > .k-link,\r\n  .k-ie11 .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view > .k-link,\r\n  .k-safari .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view > .k-link {\r\n    display: block;\r\n    position: relative;\r\n    padding-right: 2.5em;\r\n    padding-left: 1em;\r\n  }\r\n  .k-webkit .k-rtl .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view > .k-link,\r\n  .k-ff .k-rtl .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view > .k-link,\r\n  .k-ie11 .k-rtl .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view > .k-link,\r\n  .k-safari .k-rtl .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view > .k-link {\r\n    padding-left: 0;\r\n  }\r\n  .k-webkit .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view > .k-link:after,\r\n  .k-ff .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view > .k-link:after,\r\n  .k-ie11 .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view > .k-link:after,\r\n  .k-safari .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view > .k-link:after {\r\n    display: block;\r\n    content: \"\";\r\n    position: absolute;\r\n    top: 50%;\r\n    margin-top: -0.6em;\r\n    right: 0.333em;\r\n    width: 1.333em;\r\n    height: 1.333em;\r\n  }\r\n  .k-webkit .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded > li,\r\n  .k-ff .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded > li,\r\n  .k-ie11 .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded > li,\r\n  .k-safari .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded > li,\r\n  .k-webkit .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded > li:first-child + li,\r\n  .k-ff .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded > li:first-child + li,\r\n  .k-ie11 .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded > li:first-child + li,\r\n  .k-safari .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded > li:first-child + li {\r\n    display: block;\r\n    border: 0;\r\n    border-radius: 0;\r\n  }\r\n  .k-webkit .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded,\r\n  .k-ff .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded,\r\n  .k-ie11 .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded,\r\n  .k-safari .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded {\r\n    border: 1px solid #c5c5c5;\r\n    background-color: #fff;\r\n    background-image: none;\r\n    -webkit-box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.3);\r\n            box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.3);\r\n  }\r\n  .k-webkit .k-rtl .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded,\r\n  .k-ff .k-rtl .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded,\r\n  .k-ie11 .k-rtl .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded,\r\n  .k-safari .k-rtl .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded {\r\n    text-align: left;\r\n  }\r\n}\r\n@media only screen and (max-width: 480px) {\r\n  .k-webkit .k-scheduler:not(.k-scheduler-mobile) .k-scheduler-toolbar li.k-nav-current .k-sm-date-format,\r\n  .k-ff .k-scheduler:not(.k-scheduler-mobile) .k-scheduler-toolbar li.k-nav-current .k-sm-date-format,\r\n  .k-ie11 .k-scheduler:not(.k-scheduler-mobile) .k-scheduler-toolbar li.k-nav-current .k-sm-date-format,\r\n  .k-safari .k-scheduler:not(.k-scheduler-mobile) .k-scheduler-toolbar li.k-nav-current .k-sm-date-format {\r\n    display: none;\r\n  }\r\n  .k-webkit .k-scheduler-timecolumn,\r\n  .k-ff .k-scheduler-timecolumn,\r\n  .k-ie11 .k-scheduler-timecolumn,\r\n  .k-safari .k-scheduler-timecolumn {\r\n    width: 5em;\r\n  }\r\n  .k-webkit .k-scheduler-datecolumn,\r\n  .k-ff .k-scheduler-datecolumn,\r\n  .k-ie11 .k-scheduler-datecolumn,\r\n  .k-safari .k-scheduler-datecolumn {\r\n    width: 6em;\r\n    overflow: hidden;\r\n  }\r\n  .k-webkit .k-scheduler-timecolumn > div,\r\n  .k-ff .k-scheduler-timecolumn > div,\r\n  .k-ie11 .k-scheduler-timecolumn > div,\r\n  .k-safari .k-scheduler-timecolumn > div,\r\n  .k-webkit .k-scheduler-datecolumn > div,\r\n  .k-ff .k-scheduler-datecolumn > div,\r\n  .k-ie11 .k-scheduler-datecolumn > div,\r\n  .k-safari .k-scheduler-datecolumn > div {\r\n    white-space: normal;\r\n  }\r\n}\r\n/* Remove scrollbars during PDF export */\r\n.k-scheduler-pdf-export {\r\n  overflow: hidden;\r\n}\r\n.k-pdf-export-shadow .k-scheduler,\r\n.k-scheduler-pdf-export .k-scheduler-content,\r\n.k-scheduler-pdf-export .k-scheduler-times {\r\n  height: auto !important;\r\n  overflow: visible !important;\r\n}\r\n.k-scheduler-pdf-export .k-scheduler-header {\r\n  padding: 0 !important;\r\n}\r\n.k-scheduler-pdf-export .k-scheduler-header-wrap {\r\n  border-width: 0 !important;\r\n}\r\n.k-scheduler-pdf-export .k-scheduler-header .k-scheduler-table,\r\n.k-scheduler-pdf-export .k-scheduler-content .k-scheduler-table {\r\n  width: 100% !important;\r\n}\r\n/* Tooltip */\r\n.k-tooltip {\r\n  position: absolute;\r\n  z-index: 12000;\r\n  border-style: solid;\r\n  border-width: 0;\r\n  padding: 5px 5px 5px 6px;\r\n  background-repeat: repeat-x;\r\n  min-width: 20px;\r\n  /*slider tooltip only*/\r\n  text-align: center;\r\n  /*slider tooltip only*/\r\n}\r\n.k-tooltip-button {\r\n  text-align: right;\r\n  height: 0;\r\n}\r\n.k-tooltip-content {\r\n  height: 100%;\r\n}\r\n.k-tooltip-closable .k-tooltip-content {\r\n  padding-right: 20px;\r\n}\r\nspan.k-tooltip {\r\n  position: static;\r\n  display: inline-block;\r\n  border-width: 1px;\r\n  padding: 2px 5px 1px 6px;\r\n}\r\n.k-invalid-msg {\r\n  display: none;\r\n}\r\n.k-callout {\r\n  position: absolute;\r\n  width: 0;\r\n  height: 0;\r\n  border-style: solid;\r\n  border-width: 6px;\r\n  border-color: transparent;\r\n}\r\n.k-callout-n {\r\n  top: -12px;\r\n  left: 50%;\r\n}\r\n.k-callout-w {\r\n  top: 50%;\r\n  left: -12px;\r\n}\r\n.k-callout-s {\r\n  left: 50%;\r\n  bottom: -12px;\r\n}\r\n.k-callout-e {\r\n  top: 50%;\r\n  right: -12px;\r\n}\r\n.k-slider-tooltip .k-callout-n,\r\n.k-slider-tooltip .k-callout-s {\r\n  margin-left: -6px;\r\n}\r\n.k-slider-tooltip .k-callout-w,\r\n.k-slider-tooltip .k-callout-e {\r\n  margin-top: -6px;\r\n}\r\n.k-tooltip-validation .k-warning {\r\n  vertical-align: text-top;\r\n  margin-right: 3px;\r\n}\r\n.k-tooltip-validation {\r\n  z-index: 9999;\r\n}\r\n/* Toolbar */\r\n.k-toolbar {\r\n  position: relative;\r\n  display: block;\r\n  vertical-align: middle;\r\n  line-height: 2.9em;\r\n}\r\n.k-toolbar .k-button .k-icon,\r\n.k-toolbar .k-button .k-sprite,\r\n.k-overflow-container .k-button .k-icon,\r\n.k-overflow-container .k-button .k-sprite {\r\n  vertical-align: middle;\r\n  margin-top: -7px;\r\n  margin-bottom: -5px;\r\n}\r\n.k-toolbar .k-input {\r\n  line-height: inherit;\r\n  height: inherit;\r\n  padding-top: 2px;\r\n  padding-bottom: 2px;\r\n}\r\n.k-toolbar .k-input:before {\r\n  content: \"\\a0\";\r\n  display: inline-block;\r\n  width: 0;\r\n}\r\n.k-ie .k-toolbar .k-input {\r\n  height: 1.65em;\r\n}\r\n.k-toolbar .k-combobox .k-dropdown-wrap:before,\r\n.k-toolbar .k-picker-wrap:before,\r\n.k-toolbar .k-numeric-wrap:before {\r\n  display: none;\r\n}\r\n.k-overflow-container .k-sprite {\r\n  margin-left: -4px;\r\n}\r\n.k-toolbar-resizable {\r\n  overflow: hidden;\r\n  white-space: nowrap;\r\n}\r\n.k-toolbar > .k-align-left {\r\n  float: none;\r\n}\r\n.k-toolbar > .k-align-right {\r\n  float: right;\r\n}\r\n.k-toolbar > *,\r\n.k-toolbar .k-button {\r\n  display: inline-block;\r\n  vertical-align: middle;\r\n  line-height: 1.72em;\r\n}\r\n.k-toolbar .k-separator {\r\n  border-width: 0 0 0 1px;\r\n  border-style: solid;\r\n  width: 1px;\r\n  line-height: inherit;\r\n}\r\n.k-toolbar .k-button-group {\r\n  list-style-type: none;\r\n}\r\n.k-toolbar .k-button-group > li {\r\n  display: inline-block;\r\n}\r\n.k-toolbar .k-button-group .k-button {\r\n  margin: 0 0 0 -1px;\r\n  border-radius: 0;\r\n}\r\n.k-toolbar .k-button,\r\n.k-toolbar .k-split-button,\r\n.k-toolbar .k-button-group,\r\n.k-toolbar .k-widget,\r\n.k-toolbar .k-textbox,\r\n.k-toolbar label,\r\n.k-toolbar .k-separator {\r\n  margin: 0 .2em;\r\n  line-height: 1.72em;\r\n  vertical-align: middle;\r\n}\r\n.k-toolbar .k-split-button {\r\n  padding-left: 0;\r\n}\r\n.k-toolbar .k-split-button .k-button,\r\n.k-toolbar .k-button-group .k-group-start {\r\n  margin: 0;\r\n}\r\n.k-toolbar .k-split-button .k-split-button-arrow {\r\n  margin: 0 0 0 -1px;\r\n}\r\n.k-toolbar .k-overflow-anchor {\r\n  border-width: 0 0 0 1px;\r\n  border-style: solid;\r\n  height: 3em;\r\n  width: 3em;\r\n  line-height: inherit;\r\n  padding: 0 .5em;\r\n  margin: 0;\r\n  position: relative;\r\n  float: right;\r\n  border-radius: 0;\r\n}\r\n.k-overflow-container .k-item {\r\n  float: none;\r\n  border: 0;\r\n}\r\n.k-overflow-container .k-separator {\r\n  border-width: 0 0 1px;\r\n  border-style: solid;\r\n  height: 1px;\r\n  line-height: 0;\r\n  font-size: 0;\r\n  padding: 0;\r\n}\r\n.k-overflow-container .k-overflow-button,\r\n.k-split-container .k-button {\r\n  text-align: left;\r\n  display: block;\r\n  background: none;\r\n  border-color: transparent;\r\n  white-space: nowrap;\r\n}\r\n.k-split-container {\r\n  margin-top: -1px;\r\n}\r\n.k-overflow-container .k-button-group {\r\n  padding: 0;\r\n}\r\n.k-overflow-container .k-button-group > li {\r\n  display: block;\r\n}\r\n.k-overflow-container .k-overflow-group {\r\n  border-width: 1px 0;\r\n  border-style: solid;\r\n  border-radius: 0;\r\n  padding: 2px 0;\r\n  margin: 1px 0;\r\n}\r\n.k-overflow-container .k-overflow-hidden {\r\n  display: none;\r\n}\r\n.k-overflow-container .k-toolbar-first-visible,\r\n.k-overflow-container .k-overflow-group + .k-overflow-group,\r\n.k-overflow-container .k-separator + .k-overflow-group {\r\n  border-top: 0;\r\n  margin-top: 0;\r\n  padding-top: 1px;\r\n}\r\n.k-overflow-container .k-overflow-group + .k-separator {\r\n  display: none;\r\n}\r\n.k-overflow-container .k-toolbar-last-visible {\r\n  border-bottom: 0;\r\n  margin-bottom: 0;\r\n  padding-bottom: 1px;\r\n}\r\n/* Splitter */\r\n.k-splitter {\r\n  position: relative;\r\n  height: 300px;\r\n}\r\n.k-pane > .k-splitter {\r\n  border-width: 0;\r\n  overflow: hidden;\r\n}\r\n.k-splitter .k-pane {\r\n  overflow: hidden;\r\n}\r\n.k-splitter .k-scrollable {\r\n  overflow: auto;\r\n}\r\n.k-splitter .k-pane-loading {\r\n  position: absolute;\r\n  top: 50%;\r\n  left: 50%;\r\n  margin: -8px 0 0 -8px;\r\n}\r\n.k-ghost-splitbar,\r\n.k-splitbar {\r\n  position: absolute;\r\n  border-style: solid;\r\n  font-size: 0;\r\n  outline: 0;\r\n  -webkit-user-select: none;\r\n     -moz-user-select: none;\r\n      -ms-user-select: none;\r\n          user-select: none;\r\n}\r\n.k-splitter .k-ghost-splitbar-horizontal,\r\n.k-splitter .k-splitbar-horizontal {\r\n  top: 0;\r\n  width: 5px;\r\n  border-width: 0 1px;\r\n  background-repeat: repeat-y;\r\n}\r\n.k-ghost-splitbar-vertical,\r\n.k-splitbar-vertical {\r\n  left: 0;\r\n  height: 5px;\r\n  border-width: 1px 0;\r\n  background-repeat: repeat-x;\r\n}\r\n.k-splitbar-draggable-horizontal {\r\n  cursor: w-resize;\r\n}\r\n.k-splitbar-draggable-vertical {\r\n  cursor: n-resize;\r\n}\r\n.k-splitbar .k-resize-handle {\r\n  display: none;\r\n}\r\n.k-restricted-size-horizontal,\r\n.k-restricted-size-vertical {\r\n  background-color: #f00;\r\n}\r\n.k-splitbar-horizontal .k-icon {\r\n  position: absolute;\r\n  top: 50%;\r\n  width: 5px;\r\n  height: 20px;\r\n  margin-top: -10px;\r\n}\r\n.k-collapse-prev,\r\n.k-collapse-next,\r\n.k-expand-prev,\r\n.k-expand-next {\r\n  cursor: pointer;\r\n}\r\n.k-splitbar-horizontal .k-collapse-prev {\r\n  margin-top: -31px;\r\n}\r\n.k-splitbar-horizontal .k-collapse-next {\r\n  margin-top: 11px;\r\n}\r\n.k-splitbar-static-horizontal {\r\n  width: 1px;\r\n}\r\n.k-splitbar-static-vertical {\r\n  height: 1px;\r\n}\r\n.k-splitbar-vertical .k-icon {\r\n  position: absolute;\r\n  left: 50%;\r\n  width: 20px;\r\n  height: 5px;\r\n  margin-left: -10px;\r\n}\r\n.k-splitbar-vertical .k-collapse-prev {\r\n  margin-left: -31px;\r\n}\r\n.k-splitbar-vertical .k-collapse-next {\r\n  margin-left: 11px;\r\n}\r\n.k-splitbar-draggable-vertical .k-resize-handle,\r\n.k-splitbar-draggable-horizontal .k-resize-handle {\r\n  display: inline-block;\r\n}\r\n.k-splitbar-horizontal .k-resize-handle {\r\n  background-position: -165px -6px;\r\n}\r\n.k-splitbar-horizontal-hover > .k-resize-handle {\r\n  background-position: -181px -6px;\r\n}\r\n.k-splitbar-horizontal .k-collapse-prev,\r\n.k-splitbar-horizontal .k-expand-next {\r\n  background-position: -6px -174px;\r\n}\r\n.k-splitbar-horizontal-hover > .k-collapse-prev,\r\n.k-splitbar-horizontal-hover > .k-expand-next {\r\n  background-position: -22px -174px;\r\n}\r\n.k-splitbar-horizontal .k-collapse-next,\r\n.k-splitbar-horizontal .k-expand-prev {\r\n  background-position: -5px -142px;\r\n}\r\n.k-splitbar-horizontal-hover > .k-collapse-next,\r\n.k-splitbar-horizontal-hover > .k-expand-prev {\r\n  background-position: -21px -142px;\r\n}\r\n.k-splitbar-vertical .k-resize-handle {\r\n  background-position: -38px -309px;\r\n}\r\n.k-splitbar-vertical-hover > .k-resize-handle {\r\n  background-position: -70px -309px;\r\n}\r\n.k-splitbar-vertical .k-collapse-prev,\r\n.k-splitbar-vertical .k-expand-next {\r\n  background-position: 2px -134px;\r\n}\r\n.k-splitbar-vertical-hover > .k-collapse-prev,\r\n.k-splitbar-vertical-hover > .k-expand-next {\r\n  background-position: -14px -134px;\r\n}\r\n.k-splitbar-vertical .k-collapse-next,\r\n.k-splitbar-vertical .k-expand-prev {\r\n  background-position: 2px -165px;\r\n}\r\n.k-splitbar-vertical-hover > .k-collapse-next,\r\n.k-splitbar-vertical-hover > .k-expand-prev {\r\n  background-position: -14px -165px;\r\n}\r\n.k-splitter-resizing {\r\n  overflow: hidden;\r\n}\r\n/* Upload */\r\nhtml .k-upload {\r\n  position: relative;\r\n}\r\n.k-dropzone em,\r\n.k-upload-button {\r\n  vertical-align: middle;\r\n}\r\n.k-dropzone,\r\n.k-file {\r\n  position: relative;\r\n}\r\n.k-dropzone {\r\n  border-style: solid;\r\n  border-width: 0;\r\n  padding: .8em;\r\n  background-color: transparent;\r\n}\r\n.k-dropzone em {\r\n  visibility: hidden;\r\n  margin-left: .6em;\r\n}\r\n.k-dropzone-active em {\r\n  visibility: visible;\r\n}\r\n.k-upload-button {\r\n  position: relative;\r\n  overflow: hidden;\r\n  direction: ltr;\r\n}\r\n.k-upload .k-upload-button {\r\n  min-width: 7.167em;\r\n}\r\n.k-upload-sync .k-upload-button,\r\n.k-ie8 .k-upload-button,\r\n.k-ie9 .k-upload-button {\r\n  margin: .8em;\r\n}\r\n.k-upload-button input {\r\n  position: absolute;\r\n  bottom: 0;\r\n  right: 0;\r\n  z-index: 1;\r\n  font: 170px monospace !important;\r\n  /* critical for correct operation; larger values lead to ignoring or text layout problems in IE */\r\n  filter: alpha(opacity=0);\r\n  opacity: 0;\r\n  margin: 0;\r\n  padding: 0;\r\n  cursor: pointer;\r\n}\r\n.k-upload-files {\r\n  margin: 0 0 .6em;\r\n  line-height: 2.66;\r\n  border-style: solid;\r\n  border-width: 1px 0 0;\r\n}\r\n.k-upload-files .k-button {\r\n  padding: 0;\r\n}\r\n.k-upload-files .k-button,\r\n.k-upload-status-total .k-icon {\r\n  margin-left: 8px;\r\n}\r\n.k-upload .k-fail {\r\n  background-position: -161px -111px;\r\n}\r\n.k-si-refresh {\r\n  background-position: -160px -128px;\r\n}\r\n.k-link:not(.k-state-disabled):hover > .k-si-refresh,\r\n.k-state-hover > .k-si-refresh,\r\n.k-state-hover > * > .k-si-refresh,\r\n.k-button:not(.k-state-disabled):hover .k-si-refresh,\r\n.k-textbox:hover .k-si-refresh,\r\n.k-button:active .k-si-refresh {\r\n  background-position: -160px -128px;\r\n}\r\n.k-si-tick,\r\n.k-success {\r\n  background-position: -160px -96px;\r\n}\r\n.k-link:not(.k-state-disabled):hover > .k-si-tick,\r\n.k-link:not(.k-state-disabled):hover > .k-success,\r\n.k-state-hover > .k-si-tick,\r\n.k-state-hover > .k-success,\r\n.k-state-hover > * > .k-si-tick,\r\n.k-state-hover > * > .k-success,\r\n.k-button:not(.k-state-disabled):hover .k-si-tick,\r\n.k-button:not(.k-state-disabled):hover .k-success,\r\n.k-textbox:hover .k-si-tick,\r\n.k-textbox:hover .k-success,\r\n.k-button:active .k-si-tick,\r\n.k-button:active .k-success {\r\n  background-position: -160px -96px;\r\n}\r\n.k-si-cancel {\r\n  background-position: -160px -112px;\r\n}\r\n.k-link:not(.k-state-disabled):hover > .k-si-cancel,\r\n.k-state-hover > .k-si-cancel,\r\n.k-state-hover > * > .k-si-cancel,\r\n.k-button:not(.k-state-disabled):hover .k-si-cancel,\r\n.k-textbox:hover .k-si-cancel,\r\n.k-button:active .k-si-cancel {\r\n  background-position: -160px -112px;\r\n}\r\n.k-file {\r\n  border-style: solid;\r\n  border-width: 0 0 1px;\r\n  padding: .167em .167em .167em .8em;\r\n}\r\n.k-file .k-icon {\r\n  position: relative;\r\n}\r\n.k-file > .k-icon {\r\n  background-position: -112px -288px;\r\n}\r\n.k-link:not(.k-state-disabled):hover > .k-file > .k-icon,\r\n.k-state-hover > .k-file > .k-icon,\r\n.k-state-hover > * > .k-file > .k-icon,\r\n.k-button:not(.k-state-disabled):hover .k-file > .k-icon,\r\n.k-textbox:hover .k-file > .k-icon,\r\n.k-button:active .k-file > .k-icon {\r\n  background-position: -112px -288px;\r\n}\r\n.k-filename {\r\n  position: relative;\r\n  display: inline-block;\r\n  min-width: 10em;\r\n  max-width: 16.667em;\r\n  vertical-align: middle;\r\n  margin-left: 1em;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n}\r\n.k-upload-status {\r\n  position: absolute;\r\n  right: 12px;\r\n}\r\n.k-upload-status .k-button,\r\n.k-upload-status .k-warning {\r\n  vertical-align: text-bottom;\r\n}\r\n.k-dropzone .k-upload-status {\r\n  line-height: 2.4;\r\n}\r\n.k-ie8 .k-upload-status-total {\r\n  line-height: 29px;\r\n}\r\n.k-upload-action {\r\n  line-height: normal;\r\n}\r\n.k-progress {\r\n  position: absolute;\r\n  top: 0;\r\n  bottom: 0;\r\n  left: 0;\r\n}\r\n.k-upload-selected {\r\n  min-width: 7.167em;\r\n  margin: 0.25em 0 0;\r\n  -webkit-box-sizing: content-box;\r\n          box-sizing: content-box;\r\n}\r\n.k-upload-selected,\r\n.k-upload-cancel {\r\n  margin-bottom: .8em;\r\n}\r\n.k-upload-selected {\r\n  margin-left: .8em;\r\n  margin-right: .2em;\r\n}\r\n/* ImageBrowser */\r\n.k-toolbar-wrap .k-dropzone em,\r\n.k-toolbar-wrap .k-upload-files {\r\n  display: none;\r\n}\r\n.k-toolbar-wrap .k-dropzone {\r\n  border: 0;\r\n  padding: 0;\r\n}\r\n.k-toolbar-wrap .k-dropzone-active {\r\n  text-align: center;\r\n}\r\n.k-toolbar-wrap .k-dropzone-active em {\r\n  display: inline;\r\n  margin: 0;\r\n  font-size: 5em;\r\n  font-style: normal;\r\n}\r\n.k-toolbar-wrap .k-dropzone-active .k-upload-button {\r\n  display: none;\r\n}\r\n.k-filebrowser-dropzone {\r\n  z-index: 10010;\r\n  filter: alpha(opacity=40);\r\n  opacity: .4;\r\n  position: fixed;\r\n}\r\n.k-search-wrap {\r\n  position: relative;\r\n  float: right;\r\n  width: 20%;\r\n  padding: 0;\r\n}\r\n.k-search-wrap label {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 4px;\r\n  line-height: 20px;\r\n  font-style: italic;\r\n}\r\n.k-search-wrap input.k-input {\r\n  padding-left: 0;\r\n  padding-right: 0;\r\n}\r\n.k-search-wrap .k-search {\r\n  position: absolute;\r\n  top: 4px;\r\n  right: 2px;\r\n  margin: 0;\r\n}\r\n.k-breadcrumbs {\r\n  position: relative;\r\n  float: left;\r\n  width: 79%;\r\n}\r\n.k-breadcrumbs-wrap {\r\n  position: absolute;\r\n  top: 3px;\r\n  left: 0;\r\n  z-index: 1;\r\n  padding-left: 5px;\r\n  line-height: 18px;\r\n}\r\n.k-breadcrumbs > .k-input {\r\n  width: 100%;\r\n  font-size: inherit;\r\n  font-family: inherit;\r\n  border: 0;\r\n}\r\n.k-breadcrumbs .k-link,\r\n.k-breadcrumbs-wrap .k-icon {\r\n  margin-top: 0;\r\n  text-decoration: none;\r\n  vertical-align: middle;\r\n  position: static;\r\n}\r\n.k-breadcrumbs .k-link:hover {\r\n  text-decoration: underline;\r\n}\r\n.k-filebrowser .k-breadcrumbs .k-i-seek-w {\r\n  text-decoration: none;\r\n  cursor: default;\r\n}\r\n.k-filebrowser .k-filebrowser-toolbar {\r\n  border-style: solid;\r\n  border-width: 1px;\r\n  margin: 8px 0 0;\r\n  padding: .25em;\r\n  line-height: 23px;\r\n  white-space: nowrap;\r\n  /*required by WebKit*/\r\n}\r\n.k-filebrowser .k-filebrowser-toolbar .k-button.k-state-disabled {\r\n  display: none;\r\n}\r\n.k-filebrowser .k-toolbar-wrap {\r\n  float: left;\r\n}\r\n.k-filebrowser .k-tiles-arrange {\r\n  float: right;\r\n}\r\n.k-filebrowser .k-tiles-arrange .k-dropdown {\r\n  width: 75px;\r\n}\r\n.k-filebrowser .k-upload {\r\n  float: left;\r\n  z-index: 10010;\r\n  border-width: 0;\r\n  background-color: transparent;\r\n}\r\n.k-filebrowser .k-upload .k-upload-status {\r\n  display: none;\r\n}\r\n.k-filebrowser .k-upload .k-upload-button {\r\n  width: auto;\r\n  margin-left: 0;\r\n  vertical-align: top;\r\n}\r\n.k-filebrowser .k-upload .k-icon {\r\n  vertical-align: bottom;\r\n}\r\n.k-tiles {\r\n  clear: both;\r\n  height: 390px;\r\n  border-style: solid;\r\n  border-width: 1px;\r\n  border-top-width: 0;\r\n  margin: 0 0 1.4em;\r\n  padding: 9px;\r\n  overflow: auto;\r\n  line-height: 1.2;\r\n}\r\n.k-tile {\r\n  float: left;\r\n  width: 223px;\r\n  height: 88px;\r\n  overflow: hidden;\r\n  border-style: solid;\r\n  border-width: 1px;\r\n  margin: 1px;\r\n  padding: 0 0 4px;\r\n  background-position: 0 100px;\r\n  background-repeat: repeat-x;\r\n  cursor: pointer;\r\n}\r\n.k-tiles li.k-state-hover,\r\n.k-tiles li.k-state-selected {\r\n  background-position: 0 center;\r\n}\r\n.k-filebrowser .k-thumb {\r\n  float: left;\r\n  display: inline;\r\n  width: 80px;\r\n  height: 80px;\r\n  margin: 4px 10px 0 4px;\r\n  -webkit-user-select: none;\r\n     -moz-user-select: none;\r\n      -ms-user-select: none;\r\n          user-select: none;\r\n}\r\n.k-filebrowser .k-file {\r\n  width: 80px;\r\n  height: 80px;\r\n}\r\n.k-filebrowser .k-image {\r\n  margin: 2px 0 0 2px;\r\n}\r\n.k-filebrowser .k-folder {\r\n  width: 80px;\r\n  height: 80px;\r\n  background-position: 0 -200px;\r\n  background-repeat: no-repeat;\r\n}\r\n.k-filebrowser .k-loading {\r\n  margin: 35px 0 0 33px;\r\n}\r\n.k-tile strong,\r\n.k-tile input {\r\n  margin: 10px 0 4px;\r\n  font-weight: normal;\r\n}\r\n.k-tile strong {\r\n  float: left;\r\n  width: 120px;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n}\r\n.k-tile input {\r\n  width: 100px;\r\n}\r\n.k-tile strong,\r\n.k-tile input,\r\n.k-tile .k-filesize {\r\n  display: block;\r\n}\r\n.k-filebrowser .k-form-text-row {\r\n  text-align: right;\r\n}\r\n.k-filebrowser .k-form-text-row label {\r\n  width: 14%;\r\n}\r\n.k-filebrowser .k-form-text-row input {\r\n  width: 80%;\r\n}\r\n.k-tile-empty {\r\n  margin: 160px 0 0;\r\n}\r\n.k-tile-empty .k-dialog-upload {\r\n  font-weight: bold;\r\n  font-size: 120%;\r\n}\r\n.k-tile-empty strong {\r\n  display: block;\r\n  margin: 0 0 0.2em;\r\n  font-size: 3em;\r\n  font-weight: normal;\r\n}\r\n.k-tile-empty,\r\n.k-tile-empty .k-button-wrapper {\r\n  text-align: center;\r\n}\r\n.k-chart,\r\n.k-gauge,\r\n.k-sparkline,\r\n.k-stockchart {\r\n  -webkit-touch-callout: none;\r\n  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);\r\n}\r\n.k-chart,\r\n.k-stockchart {\r\n  height: 400px;\r\n}\r\ndiv.k-chart,\r\ndiv.k-gauge,\r\nspan.k-sparkline,\r\n.k-stockchart {\r\n  background-color: transparent;\r\n}\r\n.k-gauge {\r\n  text-align: left;\r\n  position: relative;\r\n}\r\n.k-baseline-marker {\r\n  zoom: 1;\r\n  *display: inline;\r\n}\r\n.k-chart-tooltip {\r\n  border-radius: 4px;\r\n  padding: 6px;\r\n  white-space: nowrap;\r\n  z-index: 12000;\r\n  line-height: normal;\r\n  background-repeat: repeat-x;\r\n  background-position: 0 0;\r\n  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAWCAYAAADAQbwGAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAADNJREFUeNpi/P//vwMDFQELEP8beQb+HTWQYgP/DHoD/466cAR4edRAyg38P6hLbIAAAwCnWhhVsxvdCAAAAABJRU5ErkJggg==);\r\n  color: #fff;\r\n}\r\n.k-chart-tooltip-inverse {\r\n  color: #000;\r\n}\r\n.k-chart-tooltip table {\r\n  border-spacing: 0;\r\n  border-collapse: collapse;\r\n}\r\n.k-chart-tooltip th {\r\n  width: auto;\r\n  text-align: center;\r\n  padding: 1px;\r\n}\r\n.k-chart-tooltip td {\r\n  width: auto;\r\n  text-align: left;\r\n  padding: .1em .2em;\r\n}\r\n/*Stock Charts*/\r\n/* Selection */\r\n.k-selector {\r\n  position: absolute;\r\n  -webkit-transform: translateZ(0);\r\n}\r\n.k-selection {\r\n  position: absolute;\r\n  border-width: 1px;\r\n  border-style: solid;\r\n  border-color: #d2d2d2;\r\n  border-bottom: 0;\r\n  height: 100%;\r\n}\r\n.k-selection-bg {\r\n  position: absolute;\r\n  width: 100%;\r\n  height: 100%;\r\n  background-color: #fff;\r\n  background-color: rgba(255, 255, 255, 0.01);\r\n  filter: alpha(opacity=1);\r\n}\r\n.k-handle {\r\n  background: #d2d2d2;\r\n  width: 7px;\r\n  height: 26px;\r\n  cursor: e-resize;\r\n  z-index: 1;\r\n  border-radius: 6px;\r\n  position: absolute;\r\n}\r\n.k-handle div {\r\n  width: 100%;\r\n  height: 100%;\r\n  background-color: transparent;\r\n}\r\n.k-leftHandle {\r\n  left: -4px;\r\n}\r\n.k-rightHandle {\r\n  right: -4px;\r\n}\r\n.k-leftHandle div {\r\n  margin: -20px 0 0 -15px;\r\n  padding: 40px 30px 0 0;\r\n}\r\n.k-leftHandle.k-handle-active div {\r\n  margin-left: -40px;\r\n  padding-right: 55px;\r\n}\r\n.k-rightHandle div {\r\n  margin: -20px 0 0 -15px;\r\n  padding: 40px 0 0 30px;\r\n}\r\n.k-rightHandle.k-handle-active div {\r\n  padding-left: 55px;\r\n}\r\n.k-mask {\r\n  position: absolute;\r\n  height: 100%;\r\n  background-color: #fff;\r\n  filter: alpha(opacity=80);\r\n  opacity: 0.80;\r\n}\r\n.k-border {\r\n  background: #d2d2d2;\r\n  width: 1px;\r\n  height: 100%;\r\n  position: absolute;\r\n}\r\n/* Navigator hint */\r\n.k-navigator-hint div {\r\n  position: absolute;\r\n}\r\n.k-navigator-hint .k-scroll {\r\n  position: absolute;\r\n  height: 4px;\r\n  border-radius: 4px;\r\n  background: #d2d2d2;\r\n}\r\n.k-navigator-hint .k-tooltip {\r\n  margin-top: 20px;\r\n  min-width: 160px;\r\n  opacity: 1;\r\n  text-align: center;\r\n  border: 0;\r\n  -webkit-box-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);\r\n          box-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);\r\n  background: #fff;\r\n}\r\n/* Sparklines */\r\n.k-sparkline,\r\n.k-sparkline span {\r\n  display: inline-block;\r\n  vertical-align: top;\r\n}\r\n.k-sparkline span {\r\n  height: 100%;\r\n  width: 100%;\r\n}\r\n/* Map */\r\n.k-map,\r\n.k-diagram {\r\n  height: 600px;\r\n}\r\n.k-map .km-scroll-wrapper,\r\n.k-diagram .km-scroll-wrapper {\r\n  padding-bottom: 0;\r\n  -webkit-user-select: none;\r\n     -moz-user-select: none;\r\n      -ms-user-select: none;\r\n          user-select: none;\r\n}\r\n.k-map .km-scroll-wrapper,\r\n.k-diagram .km-scroll-wrapper,\r\n.k-map .km-scroll-container,\r\n.k-diagram .km-scroll-container {\r\n  position: absolute;\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n.k-map .k-layer,\r\n.k-diagram .k-layer {\r\n  position: absolute;\r\n  left: 0;\r\n  top: 0;\r\n}\r\n.k-map .km-touch-scrollbar,\r\n.k-diagram .km-touch-scrollbar {\r\n  display: none;\r\n}\r\n.k-map .k-marker {\r\n  position: absolute;\r\n  width: 28px;\r\n  height: 40px;\r\n  margin: -40px 0 0 -14px;\r\n  cursor: pointer;\r\n}\r\n.k-map .k-marker-pin {\r\n  background-position: 0px 40px;\r\n}\r\n.k-map .k-marker-pin-target {\r\n  background-position: 0px 0px;\r\n}\r\n@media only screen and (-webkit-min-device-pixel-ratio: 2), only screen and (min-device-pixel-ratio: 2) {\r\n  .k-map .k-marker {\r\n    width: 56px;\r\n    height: 80px;\r\n    margin: -80px 0 0 -28px;\r\n  }\r\n  .k-map .k-marker-pin {\r\n    background-position: 0px 80px;\r\n  }\r\n}\r\n/* Control positions */\r\n.k-map .k-pos-top {\r\n  top: 0;\r\n}\r\n.k-map .k-pos-bottom {\r\n  bottom: 0;\r\n}\r\n.k-map .k-pos-left {\r\n  left: 0;\r\n}\r\n.k-map .k-pos-right {\r\n  right: 0;\r\n}\r\n.k-map-controls {\r\n  position: absolute;\r\n}\r\n.k-map-controls.k-pos-left .k-widget:first-child {\r\n  margin-right: 0;\r\n}\r\n.k-map-controls.k-pos-right .k-widget:first-child {\r\n  margin-left: 0;\r\n}\r\n/* Map navigator */\r\n.k-navigator {\r\n  width: 50px;\r\n  height: 50px;\r\n  margin: 20px;\r\n  border-radius: 80px;\r\n  position: relative;\r\n  display: inline-block;\r\n  vertical-align: middle;\r\n}\r\n.k-pdf-export .k-navigator {\r\n  display: none;\r\n}\r\n.k-navigator > button {\r\n  border-color: transparent;\r\n  background: none;\r\n}\r\ndiv.k-navigator > .k-button {\r\n  margin: 0;\r\n  padding: 0;\r\n  line-height: 10px;\r\n  border-radius: 16px;\r\n  position: absolute;\r\n  font-size: 1px;\r\n  /*IE7*/\r\n  line-height: 1px;\r\n}\r\ndiv.k-navigator .k-navigator-n {\r\n  top: 2px;\r\n  left: 50%;\r\n  margin-left: -9px;\r\n}\r\ndiv.k-navigator .k-navigator-e {\r\n  right: 2px;\r\n  top: 50%;\r\n  margin-top: -9px;\r\n}\r\ndiv.k-navigator .k-navigator-s {\r\n  bottom: 2px;\r\n  left: 50%;\r\n  margin-left: -9px;\r\n}\r\ndiv.k-navigator .k-navigator-w {\r\n  left: 2px;\r\n  top: 50%;\r\n  margin-top: -9px;\r\n}\r\n/* Attribution */\r\n.k-map .k-attribution {\r\n  background-color: rgba(255, 255, 255, 0.8);\r\n  font-size: 10px;\r\n  padding: 2px 4px;\r\n  z-index: 1000;\r\n}\r\n/* Zoom */\r\n.k-zoom-control {\r\n  margin: 14px;\r\n  vertical-align: middle;\r\n}\r\n.k-pdf-export .k-zoom-control {\r\n  display: none;\r\n}\r\n.k-button-wrap {\r\n  border-radius: 4px;\r\n  display: inline-block;\r\n}\r\n.k-button-wrap .k-button {\r\n  position: relative;\r\n  font: bold 17px/1.18 monospace;\r\n}\r\n.k-buttons-horizontal :first-child {\r\n  border-radius: 4px 0 0 4px;\r\n}\r\n.k-buttons-horizontal :first-child + .k-zoom-in {\r\n  border-radius: 0;\r\n  margin-left: -1px;\r\n}\r\n.k-buttons-horizontal .k-zoom-out {\r\n  border-radius: 0 4px 4px 0;\r\n  margin-left: -1px;\r\n}\r\n.k-button-wrap .k-button:hover {\r\n  z-index: 1;\r\n}\r\n.k-buttons-vertical .k-button {\r\n  display: block;\r\n}\r\n.k-buttons-vertical :first-child {\r\n  border-radius: 4px 4px 0 0;\r\n}\r\n.k-buttons-vertical .k-zoom-out {\r\n  border-radius: 0 0 4px 4px;\r\n  margin-top: -1px;\r\n}\r\n.k-zoom-text {\r\n  margin: 0;\r\n  width: 4.3em;\r\n  vertical-align: top;\r\n}\r\n/* RTL */\r\n.k-rtl .k-buttons-horizontal :first-child {\r\n  border-radius: 0 4px 4px 0;\r\n}\r\n.k-rtl .k-buttons-horizontal :first-child + .k-zoom-in {\r\n  border-radius: 0;\r\n  margin-left: 0;\r\n  margin-right: -1px;\r\n}\r\n.k-rtl .k-buttons-horizontal .k-zoom-out {\r\n  border-radius: 4px 0 0 4px;\r\n  margin-left: 0;\r\n  margin-right: -1px;\r\n}\r\n/* Diagram */\r\n.k-diagram {\r\n  height: 600px;\r\n}\r\n.k-diagram .km-scroll-wrapper {\r\n  width: 100%;\r\n  height: 100%;\r\n  position: relative;\r\n}\r\n.k-diagram .km-scroll-wrapper {\r\n  width: 100%;\r\n  height: 100%;\r\n  position: relative;\r\n}\r\n.k-canvas-container {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n/* IE8- */\r\n.k-diagram img {\r\n  -webkit-box-sizing: content-box;\r\n          box-sizing: content-box;\r\n}\r\n/* TreeMap start */\r\n.k-treemap {\r\n  overflow: hidden;\r\n  height: 400px;\r\n}\r\n.k-treemap-tile {\r\n  -webkit-box-sizing: border-box;\r\n          box-sizing: border-box;\r\n  border-style: solid;\r\n  border-width: 1px;\r\n  position: absolute;\r\n  margin: -1px 0 0 -1px;\r\n  overflow: hidden;\r\n}\r\n.k-treemap-tile.k-leaf {\r\n  padding: .6em;\r\n}\r\n.k-treemap-wrap.k-last > .k-treemap-tile {\r\n  padding: .3em;\r\n}\r\n.k-treemap-tile.k-state-hover,\r\n.k-treemap-tile.k-state-hover:hover {\r\n  z-index: 2;\r\n  background-image: none;\r\n}\r\n.k-treemap > .k-treemap-tile {\r\n  position: relative;\r\n  height: 100%;\r\n}\r\n.k-treemap-title {\r\n  -webkit-box-sizing: border-box;\r\n          box-sizing: border-box;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  line-height: 2.42em;\r\n  height: 2.42em;\r\n  padding: 0 .6em;\r\n  white-space: nowrap;\r\n}\r\n.k-treemap-wrap .k-treemap-title {\r\n  border-width: 0 0 1px;\r\n  border-style: solid;\r\n}\r\n.k-treemap-wrap {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  bottom: 0;\r\n  right: 0;\r\n}\r\n.k-treemap-title + .k-treemap-wrap {\r\n  top: 2.42em;\r\n}\r\n.k-treemap-title-vertical {\r\n  -webkit-box-sizing: border-box;\r\n          box-sizing: border-box;\r\n  text-overflow: ellipsis;\r\n  position: absolute;\r\n  top: 0;\r\n  bottom: 0;\r\n  width: 2.42em;\r\n  line-height: 2.42em;\r\n  overflow: hidden;\r\n  padding: .6em 0;\r\n  white-space: nowrap;\r\n}\r\n.k-treemap-title-vertical > div {\r\n  position: absolute;\r\n  top: 0;\r\n  right: 1.23em;\r\n  -webkit-transform-origin: right;\r\n      -ms-transform-origin: right;\r\n          transform-origin: right;\r\n  -webkit-transform: rotate(-90deg);\r\n      -ms-transform: rotate(-90deg);\r\n          transform: rotate(-90deg);\r\n}\r\n.k-treemap-title-vertical + .k-treemap-wrap {\r\n  left: 2.42em;\r\n}\r\n/* TreeMap end */\r\n/* common mobile css */\r\n.km-root,\r\n.km-pane,\r\n.km-pane-wrapper {\r\n  width: 100%;\r\n  height: 100%;\r\n  -ms-touch-action: none;\r\n  -ms-content-zooming: none;\r\n  -webkit-user-select: none;\r\n     -moz-user-select: none;\r\n      -ms-user-select: none;\r\n          user-select: none;\r\n  -webkit-text-size-adjust: none;\r\n      -ms-text-size-adjust: none;\r\n          text-size-adjust: none;\r\n}\r\n.km-pane-wrapper {\r\n  position: absolute;\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n.km-pane,\r\n.km-shim {\r\n  font-family: sans-serif;\r\n}\r\n.km-pane {\r\n  overflow-x: hidden;\r\n}\r\n.km-view {\r\n  top: 0;\r\n  left: 0;\r\n  position: absolute;\r\n  display: -webkit-box;\r\n  display: -webkit-flex;\r\n  display: -ms-flexbox;\r\n  display: flex;\r\n  height: 100%;\r\n  width: 100%;\r\n  -webkit-box-orient: vertical;\r\n  -webkit-box-direction: normal;\r\n  -webkit-flex-direction: column;\r\n      -ms-flex-direction: column;\r\n          flex-direction: column;\r\n  -webkit-box-align: stretch;\r\n  -webkit-align-items: stretch;\r\n      -ms-flex-align: stretch;\r\n          align-items: stretch;\r\n  -webkit-align-content: stretch;\r\n      -ms-flex-line-pack: stretch;\r\n          align-content: stretch;\r\n  vertical-align: top;\r\n}\r\n.k-ff .km-view,\r\n.k-ff .km-pane {\r\n  overflow: hidden;\r\n}\r\n.k-ff18 .km-view,\r\n.k-ff18 .km-pane,\r\n.k-ff19 .km-view,\r\n.k-ff19 .km-pane,\r\n.k-ff20 .km-view,\r\n.k-ff20 .km-pane,\r\n.k-ff21 .km-view,\r\n.k-ff21 .km-pane {\r\n  position: relative;\r\n}\r\n.k-ff .km-view {\r\n  display: -moz-inline-box;\r\n  display: -webkit-box;\r\n  display: -webkit-flex;\r\n  display: -ms-flexbox;\r\n  display: flex;\r\n}\r\n.km-content {\r\n  min-height: 1px;\r\n  -webkit-box-flex: 1;\r\n  -webkit-flex: 1;\r\n      -ms-flex: 1;\r\n          flex: 1;\r\n  flex-align: stretch;\r\n  display: block;\r\n  width: auto;\r\n  overflow: hidden;\r\n  position: relative;\r\n}\r\n.km-actionsheet > li {\r\n  list-style-type: none;\r\n  padding: inherit 1em;\r\n  line-height: 2em;\r\n}\r\n.km-actionsheet {\r\n  padding: 0;\r\n  margin: 0;\r\n}\r\n.km-shim {\r\n  left: 0;\r\n  bottom: 0;\r\n  position: fixed;\r\n  width: 100%;\r\n  height: 100%;\r\n  background: rgba(0, 0, 0, 0.6);\r\n  z-index: 10001;\r\n}\r\n.km-shim .k-animation-container,\r\n.km-actionsheet-wrapper {\r\n  width: 100%;\r\n  -webkit-box-shadow: none;\r\n          box-shadow: none;\r\n  border: 0;\r\n}\r\n.km-shim .k-animation-container {\r\n  width: auto;\r\n}\r\n/* /common mobile css */\r\n.km-pane-wrapper .k-grid-edit-form > .km-header,\r\n.km-pane-wrapper .k-grid-column-menu > .km-header,\r\n.km-pane-wrapper .k-grid-filter-menu > .km-header,\r\n.km-pane-wrapper .k-scheduler-edit-form > .km-header {\r\n  border-style: solid;\r\n  border-width: 1px;\r\n  padding: .3em .6em;\r\n  text-align: center;\r\n  width: auto;\r\n  line-height: 2em;\r\n}\r\n.k-ie .km-pane-wrapper .k-scheduler > .k-scheduler-toolbar,\r\n.k-ie .km-pane-wrapper .k-scheduler > .k-scheduler-footer {\r\n  line-height: 2em;\r\n}\r\n.km-pane-wrapper .k-grid-edit-form .k-multiselect,\r\n.km-pane-wrapper .k-scheduler-edit-form .k-multiselect {\r\n  width: 15em;\r\n}\r\n.km-pane-wrapper .k-grid-edit-form .k-dropdown-wrap,\r\n.km-pane-wrapper .k-scheduler-edit-form .k-dropdown-wrap {\r\n  display: block;\r\n}\r\n.km-pane-wrapper .k-grid-column-menu .k-done,\r\n.km-pane-wrapper .k-grid-filter-menu .k-submit,\r\n.km-pane-wrapper .k-grid-edit-form .k-grid-update,\r\n.km-pane-wrapper .k-scheduler-edit-form .k-scheduler-update {\r\n  float: right;\r\n}\r\n.km-pane-wrapper .k-grid-filter-menu .k-cancel,\r\n.km-pane-wrapper .k-grid-edit-form .k-grid-cancel,\r\n.km-pane-wrapper .k-scheduler-edit-form .k-scheduler-cancel {\r\n  float: left;\r\n}\r\n/* Actiosheet Styles */\r\n.km-pane-wrapper .k-scheduler-edit-form .k-scheduler-delete,\r\n*:not(.km-pane) > .km-shim .km-actionsheet .k-button {\r\n  display: block;\r\n  text-align: center;\r\n}\r\n*:not(.km-pane) > .km-shim .km-actionsheet .k-button {\r\n  font-size: 1.4em;\r\n  margin: .3em 1em;\r\n}\r\n*:not(.km-pane) > .km-shim .km-actionsheet-title {\r\n  text-align: center;\r\n  line-height: 3em;\r\n  margin-bottom: -0.3em;\r\n}\r\n*:not(.km-pane) > .km-shim > .k-animation-container {\r\n  margin: 0 !important;\r\n  padding: 0 !important;\r\n  left: 0 !important;\r\n}\r\n/* Adaptive Grid */\r\n.km-pane-wrapper > div.km-pane {\r\n  -webkit-box-shadow: none;\r\n          box-shadow: none;\r\n  font-weight: normal;\r\n}\r\n.km-pane-wrapper .k-popup-edit-form .km-content > .km-scroll-container,\r\n.km-pane-wrapper .k-grid-edit-form .km-content > .km-scroll-container,\r\n.km-pane-wrapper .k-grid-column-menu .km-content > .km-scroll-container,\r\n.km-pane-wrapper .k-grid-filter-menu .km-content > .km-scroll-container {\r\n  position: absolute;\r\n  width: 100%;\r\n  min-height: 100%;\r\n  -webkit-box-sizing: border-box;\r\n          box-sizing: border-box;\r\n}\r\n.km-pane-wrapper .k-mobile-list .k-edit-field {\r\n  width: 74%;\r\n}\r\n.km-pane-wrapper .k-grid-edit-form .k-popup-edit-form,\r\n.km-pane-wrapper .k-grid-edit-form .k-edit-form-container {\r\n  width: auto;\r\n}\r\n.km-pane-wrapper .k-filter-menu .k-button {\r\n  width: 100%;\r\n  margin: 0;\r\n}\r\n.k-grid-mobile {\r\n  border-width: 0;\r\n}\r\n.k-grid-mobile .k-resize-handle-inner {\r\n  position: absolute;\r\n  top: 50%;\r\n  margin-top: -10px;\r\n  left: -7px;\r\n  width: 17px;\r\n  height: 17px;\r\n  border-style: solid;\r\n  border-width: 2px;\r\n  border-radius: 10px;\r\n}\r\n.k-grid-mobile .k-resize-handle-inner:before {\r\n  content: \"\";\r\n  position: absolute;\r\n  top: 50%;\r\n  margin-top: -3px;\r\n  left: 1px;\r\n  width: 6px;\r\n  height: 6px;\r\n  background-position: -5px -53px;\r\n}\r\n.k-grid-mobile .k-resize-handle-inner:after {\r\n  content: \"\";\r\n  position: absolute;\r\n  top: 50%;\r\n  margin-top: -3px;\r\n  right: 1px;\r\n  width: 6px;\r\n  height: 6px;\r\n  background-position: -5px -21px;\r\n}\r\n/* Adaptive Grid & Scheduler */\r\n.km-pane-wrapper .km-pane * {\r\n  -webkit-background-clip: border-box;\r\n          background-clip: border-box;\r\n}\r\n.km-pane-wrapper .km-pane .k-mobile-list,\r\n.km-pane-wrapper .k-mobile-list ul {\r\n  padding: 0;\r\n  margin: 0;\r\n  list-style-type: none;\r\n  border-radius: 0;\r\n  background: none;\r\n}\r\n.km-pane-wrapper .km-switch {\r\n  top: 50%;\r\n  right: .8rem;\r\n  position: absolute;\r\n  margin-top: -1.1rem;\r\n}\r\n.km-pane-wrapper .k-mobile-list .k-state-disabled {\r\n  opacity: 1;\r\n}\r\n.km-pane-wrapper .k-mobile-list .k-state-disabled > * {\r\n  opacity: .7;\r\n}\r\n.km-pane-wrapper .k-mobile-list .k-item,\r\n.km-pane-wrapper .k-mobile-list .k-item > .k-link,\r\n.km-pane-wrapper .k-mobile-list .k-item > .k-label,\r\n.km-pane-wrapper .k-mobile-list .k-edit-label {\r\n  display: block;\r\n  position: relative;\r\n  list-style-type: none;\r\n  vertical-align: middle;\r\n  -webkit-box-sizing: border-box;\r\n          box-sizing: border-box;\r\n  padding: .5em 0 .5em 1em;\r\n  font-size: 1em;\r\n}\r\n.km-pane-wrapper .k-edit-form-container,\r\n.km-pane-wrapper .k-scheduler-edit-form .km-scroll-container {\r\n  padding-top: 1em;\r\n  width: 100%;\r\n}\r\n.km-pane-wrapper .k-mobile-list .k-edit-label {\r\n  position: absolute;\r\n  margin: 0;\r\n  float: none;\r\n  clear: none;\r\n  width: 100%;\r\n}\r\n.km-pane-wrapper .k-mobile-list .k-edit-field,\r\n.km-pane-wrapper .k-mobile-list .k-edit-label label {\r\n  display: block;\r\n  text-align: left;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  -webkit-box-sizing: border-box;\r\n          box-sizing: border-box;\r\n  padding: .1em 0;\r\n  margin: 0;\r\n}\r\n.km-pane-wrapper .k-mobile-list .k-item,\r\n.km-pane-wrapper .k-mobile-list .k-edit-field,\r\n.km-pane-wrapper .k-mobile-list .k-edit-label {\r\n  font-size: 1em;\r\n  line-height: 1.6em;\r\n  overflow: hidden;\r\n}\r\n.km-pane-wrapper .k-mobile-list .k-edit-field,\r\n.km-pane-wrapper .k-mobile-list .k-edit-label {\r\n  width: 100%;\r\n  float: none;\r\n  clear: none;\r\n  min-height: 2.7em;\r\n}\r\n.km-pane-wrapper .km-header .k-icon,\r\n.km-pane-wrapper .k-grid-toolbar .k-icon,\r\n.km-pane-wrapper .k-grid-edit .k-icon,\r\n.km-pane-wrapper .k-grid-delete .k-icon {\r\n  display: none;\r\n}\r\n.km-pane-wrapper .k-mobile-list .k-edit-field {\r\n  padding: .5em 0;\r\n}\r\n.km-pane-wrapper .k-mobile-list .k-scheduler-toolbar {\r\n  padding: .3em 0;\r\n}\r\n.km-pane-wrapper .k-mobile-list .k-scheduler-toolbar ul li {\r\n  line-height: 2em;\r\n}\r\n.km-pane-wrapper .k-mobile-list .k-item > * {\r\n  line-height: normal;\r\n}\r\n.km-pane-wrapper .k-mobile-list .k-edit-buttons,\r\n.km-pane-wrapper .k-mobile-list .k-button-container {\r\n  -webkit-box-sizing: border-box;\r\n          box-sizing: border-box;\r\n  padding: .5em 1em;\r\n  margin: 0;\r\n}\r\n.km-pane-wrapper .k-mobile-list > ul > li > .k-link,\r\n.km-pane-wrapper .k-mobile-list .k-filter-help-text > li > .k-link,\r\n.km-pane-wrapper .k-mobile-list .k-recur-view > .k-edit-label:nth-child(3),\r\n.km-pane-wrapper #recurrence .km-scroll-container > .k-edit-label:first-child {\r\n  display: block;\r\n  padding: .2em 1em;\r\n  font-size: .95em;\r\n  position: -webkit-sticky;\r\n  margin: 0;\r\n  font-weight: normal;\r\n  line-height: 2em;\r\n  background: transparent;\r\n  border-top: 1em solid transparent;\r\n}\r\n.km-pane-wrapper .k-mobile-list .k-recur-view > .k-edit-label:nth-child(3),\r\n.km-pane-wrapper #recurrence .km-scroll-container > .k-edit-label:first-child {\r\n  position: relative;\r\n}\r\n.km-pane-wrapper .k-mobile-list .k-item:first-child {\r\n  border-top: 0;\r\n}\r\n.km-pane-wrapper .k-mobile-list .k-item:last-child {\r\n  border-bottom: 0;\r\n}\r\n.km-pane-wrapper .k-mobile-list .k-item > .k-link,\r\n.km-pane-wrapper .k-mobile-list .k-item > .k-label {\r\n  line-height: inherit;\r\n  text-decoration: none;\r\n  margin: -0.5em 0 -0.5em -1em;\r\n}\r\n/* Mobile list form elements */\r\n.k-check[type=checkbox],\r\n.k-check[type=radio],\r\n.k-mobile-list .k-edit-field [type=checkbox],\r\n.k-mobile-list .k-edit-field [type=radio] {\r\n  -webkit-appearance: none;\r\n     -moz-appearance: none;\r\n          appearance: none;\r\n  background-color: transparent;\r\n}\r\n.km-pane-wrapper .k-mobile-list .k-link .k-check,\r\n.km-pane-wrapper .k-mobile-list .k-label .k-check,\r\n.k-mobile-list .k-edit-field [type=checkbox],\r\n.k-mobile-list .k-edit-field [type=radio] {\r\n  border: 0;\r\n  font-size: inherit;\r\n  width: 13px;\r\n  height: 13px;\r\n  margin: .26em 1em .26em 0;\r\n}\r\n.k-ie .km-pane-wrapper .k-icon,\r\n.k-ie .km-pane-wrapper .k-mobile-list .k-link .k-check,\r\n.k-ie .km-pane-wrapper .k-mobile-list .k-label .k-check,\r\n.k-ie .k-mobile-list .k-edit-field [type=checkbox],\r\n.k-ie .k-mobile-list .k-edit-field [type=radio] {\r\n  font-size: inherit;\r\n  text-indent: -9999px;\r\n  width: 1.01em;\r\n  height: 1em;\r\n}\r\n.km-pane-wrapper .k-column-menu .k-label .k-check {\r\n  height: 16px;\r\n  width: 16px;\r\n}\r\n.km-pane-wrapper .km-pane .k-mobile-list input:not([type=\"checkbox\"]):not([type=\"radio\"]),\r\n.km-pane-wrapper .km-pane .k-mobile-list select:not([multiple]),\r\n.km-pane-wrapper .km-pane .k-mobile-list textarea,\r\n.km-pane-wrapper .k-mobile-list .k-widget,\r\n.km-pane-wrapper .k-edit-field > *:not([type=\"checkbox\"]):not([type=\"radio\"]):not(.k-button) {\r\n  text-indent: 0;\r\n  font-size: 1em;\r\n  line-height: 1.6em;\r\n  vertical-align: middle;\r\n  height: auto;\r\n  padding: 0;\r\n  border: 0;\r\n  margin: 0;\r\n  background: transparent;\r\n  -webkit-box-shadow: none;\r\n          box-shadow: none;\r\n  border-radius: 0;\r\n}\r\n.km-pane-wrapper .k-mobile-list .k-widget {\r\n  border: 0;\r\n  border-radius: 0;\r\n}\r\n.k-ie .km-pane-wrapper .k-mobile-list .k-widget {\r\n  height: initial;\r\n}\r\n.km-pane-wrapper .k-mobile-list .k-widget .k-input,\r\n.km-pane-wrapper .k-mobile-list .k-widget .k-state-default {\r\n  border: 0;\r\n  background: transparent;\r\n}\r\n.km-pane-wrapper *:not(.k-state-default):not(.k-pager-input) > input:not([type=\"checkbox\"]):not([type=\"radio\"]),\r\n.km-pane-wrapper .k-mobile-list select:not([multiple]),\r\n.km-pane-wrapper .k-mobile-list textarea,\r\n.km-pane-wrapper .k-mobile-list .k-widget,\r\n.km-pane-wrapper .k-edit-field > *:not([type=\"checkbox\"]):not([type=\"radio\"]):not(.k-button) {\r\n  width: 80%;\r\n  padding: .6em 0;\r\n  margin: -0.5em 0;\r\n}\r\n.km-pane-wrapper .km-pane .k-mobile-list input,\r\n.km-pane-wrapper .km-pane .k-mobile-list select:not([multiple]),\r\n.km-pane-wrapper .km-pane .k-mobile-list textarea,\r\n.km-pane-wrapper .k-mobile-list .k-widget,\r\n.km-pane-wrapper .k-mobile-list .k-edit-field > * {\r\n  -webkit-appearance: none;\r\n     -moz-appearance: none;\r\n          appearance: none;\r\n  float: right;\r\n  z-index: 1;\r\n  position: relative;\r\n}\r\n.km-pane-wrapper .km-pane .k-mobile-list.k-filter-menu .k-space-right {\r\n  padding: 10px;\r\n  border-radius: 0;\r\n}\r\n.km-pane-wrapper .km-pane .k-mobile-list.k-filter-menu .k-space-right > input {\r\n  float: none;\r\n  width: 100%;\r\n  padding: 1px 0;\r\n  margin: 0;\r\n  border-radius: 3px;\r\n  text-indent: 30px;\r\n  border-width: 1px;\r\n  border-style: solid;\r\n}\r\n.km-pane-wrapper .km-pane .k-mobile-list.k-filter-menu .k-font-icon {\r\n  position: absolute;\r\n  left: 20px;\r\n  z-index: 1;\r\n}\r\n.km-pane-wrapper .k-scheduler-views {\r\n  width: 18em;\r\n}\r\n.km-pane-wrapper .k-mobile-list .k-edit-field.k-scheduler-toolbar {\r\n  background: transparent;\r\n  border: 0;\r\n  -webkit-box-shadow: none;\r\n          box-shadow: none;\r\n  padding: .5em 1em;\r\n}\r\n.km-pane-wrapper #recurrence .k-scheduler-navigation {\r\n  width: 100%;\r\n}\r\n.km-pane-wrapper .k-scheduler-views,\r\n.km-pane-wrapper .k-mobile-list .k-scheduler-navigation {\r\n  display: table;\r\n  table-layout: fixed;\r\n}\r\n.km-pane-wrapper .k-scheduler-views li,\r\n.km-pane-wrapper .k-mobile-list .k-scheduler-navigation li {\r\n  display: table-cell;\r\n  text-align: center;\r\n}\r\n.km-pane-wrapper .k-mobile-list .k-recur-view > .k-edit-field .k-check {\r\n  margin: 0;\r\n  padding-left: 1em;\r\n  -webkit-box-sizing: border-box;\r\n          box-sizing: border-box;\r\n}\r\n.km-pane-wrapper .k-mobile-list .k-recur-view > .k-edit-field .k-check:first-child {\r\n  margin-top: -0.5em;\r\n}\r\n.km-pane-wrapper .k-mobile-list .k-recur-view > .k-edit-field .k-check:last-child {\r\n  margin-bottom: -0.5em;\r\n}\r\n.km-pane-wrapper .k-mobile-list .k-scheduler-timezones .k-edit-field label.k-check {\r\n  text-indent: 1em;\r\n}\r\n.km-pane-wrapper .k-mobile-list .k-edit-field > .k-button {\r\n  margin-left: 20%;\r\n  float: left;\r\n}\r\n.km-pane-wrapper .k-mobile-list .k-picker-wrap,\r\n.km-pane-wrapper .k-mobile-list .k-numeric-wrap,\r\n.km-pane-wrapper .k-mobile-list .k-dropdown-wrap {\r\n  position: static;\r\n  -webkit-box-shadow: none;\r\n          box-shadow: none;\r\n}\r\n.km-pane-wrapper .k-mobile-list .k-datepicker .k-select,\r\n.km-pane-wrapper .k-mobile-list .k-datetimepicker .k-select,\r\n.km-pane-wrapper .k-mobile-list .k-numerictextbox .k-select {\r\n  position: absolute;\r\n  top: 0;\r\n  right: 0;\r\n  line-height: normal;\r\n}\r\n.km-pane-wrapper .k-mobile-list .k-datepicker .k-select:before,\r\n.km-pane-wrapper .k-mobile-list .k-datetimepicker .k-select:before {\r\n  content: \"\\a0\";\r\n  display: inline-block;\r\n  width: 0;\r\n  height: 100%;\r\n  vertical-align: middle;\r\n}\r\n.km-pane-wrapper .k-mobile-list .k-numerictextbox .k-link {\r\n  height: 50%;\r\n}\r\n.km-pane-wrapper .k-grid .k-button,\r\n.km-pane-wrapper .k-edit-form-container .k-button {\r\n  margin: 0;\r\n}\r\n.km-pane-wrapper .k-grid .k-button + .k-button,\r\n.km-pane-wrapper .k-edit-form-container .k-button + .k-button {\r\n  margin: 0 0 0 .18em;\r\n}\r\n.km-pane-wrapper .k-pager-numbers .k-link,\r\n.km-pane-wrapper .k-pager-numbers .k-state-selected,\r\n.km-pane-wrapper .k-pager-wrap > .k-link {\r\n  width: 2.4em;\r\n  height: 2.4em;\r\n  line-height: 2.1em;\r\n  border-radius: 2em;\r\n  -webkit-box-sizing: border-box;\r\n          box-sizing: border-box;\r\n}\r\n.km-pane-wrapper .k-pager-numbers .k-link,\r\n.km-pane-wrapper .k-pager-numbers .k-state-selected {\r\n  width: auto;\r\n  line-height: 2.2em;\r\n  padding: 0 .86em;\r\n  min-width: .7em;\r\n}\r\n.km-pane-wrapper .k-pager-wrap {\r\n  line-height: 2.4em;\r\n}\r\n@media all and (max-width: 1024px) {\r\n  .km-pane-wrapper .k-pager-nav + .k-pager-nav ~ .k-pager-nav {\r\n    position: absolute;\r\n    right: .3em;\r\n    top: .3em;\r\n  }\r\n  .km-pane-wrapper .k-pager-wrap .k-pager-numbers + .k-pager-nav,\r\n  .km-pane-wrapper .k-pager-nav:first-child + .k-pager-nav + .k-pager-nav {\r\n    right: 3em;\r\n  }\r\n  .km-pane-wrapper .k-pager-info,\r\n  .km-pane-wrapper .k-pager-refresh {\r\n    display: none;\r\n  }\r\n}\r\n@media all and (max-width: 699px), (-ms-high-contrast: active) and (-ms-high-contrast: none) and (max-width: 800px) {\r\n  .km-pane-wrapper *:not(.k-state-default):not(.k-pager-input) > input:not([type=\"checkbox\"]):not([type=\"radio\"]),\r\n  .km-pane-wrapper .k-mobile-list select:not([multiple]),\r\n  .km-pane-wrapper .k-mobile-list textarea,\r\n  .km-pane-wrapper .k-mobile-list .k-widget,\r\n  .km-pane-wrapper .k-edit-field > *:not([type=\"checkbox\"]):not([type=\"radio\"]):not(.k-button) {\r\n    width: 50%;\r\n  }\r\n  .km-pane-wrapper .k-mobile-list .k-edit-field > .k-button {\r\n    margin-left: 50%;\r\n  }\r\n  .km-pane-wrapper .k-mobile-list .k-edit-field > .k-timezone-button {\r\n    margin-left: 1em;\r\n  }\r\n  .km-pane-wrapper .k-nav-today a {\r\n    padding-left: .6em;\r\n    padding-right: .6em;\r\n  }\r\n  .km-pane-wrapper li.k-nav-current {\r\n    margin-left: 0;\r\n    margin-right: 0;\r\n  }\r\n  .km-pane-wrapper .k-pager-wrap {\r\n    position: relative;\r\n  }\r\n  .km-pane-wrapper .k-pager-numbers {\r\n    width: auto;\r\n    display: block;\r\n    overflow: hidden;\r\n    margin-right: 5.5em;\r\n    float: none;\r\n    text-overflow: ellipsis;\r\n    height: 2.4em;\r\n    text-align: center;\r\n  }\r\n  .km-pane-wrapper .k-pager-numbers li {\r\n    float: none;\r\n    display: inline-block;\r\n  }\r\n  .km-pane-wrapper .k-pager-nav {\r\n    float: left;\r\n  }\r\n}\r\n.km-pane-wrapper .k-mobile-list .k-recur-view > .k-edit-field .k-check,\r\n.km-pane-wrapper .k-mobile-list .k-edit-field > * > select:not([multiple]),\r\n.km-pane-wrapper .k-mobile-list .k-scheduler-timezones .k-edit-field label.k-check {\r\n  width: 100%;\r\n}\r\n/* Mobile Scroller */\r\n.km-scroll-container {\r\n  -webkit-user-select: none;\r\n     -moz-user-select: none;\r\n      -ms-user-select: none;\r\n          user-select: none;\r\n  -webkit-margin-collapse: separate;\r\n  -webkit-transform: translatez(0);\r\n}\r\n.k-widget .km-scroll-wrapper {\r\n  position: relative;\r\n  padding-bottom: 0;\r\n}\r\n.km-touch-scrollbar {\r\n  position: absolute;\r\n  visibility: hidden;\r\n  z-index: 200000;\r\n  height: .3em;\r\n  width: .3em;\r\n  background-color: rgba(0, 0, 0, 0.7);\r\n  opacity: 0;\r\n  -webkit-transition: opacity 0.3s linear;\r\n          transition: opacity 0.3s linear;\r\n  -webkit-transition: \"opacity 0.3s linear\";\r\n          transition: \"opacity 0.3s linear\";\r\n}\r\n.km-vertical-scrollbar {\r\n  height: 100%;\r\n  right: 2px;\r\n  top: 2px;\r\n}\r\n.km-horizontal-scrollbar {\r\n  width: 100%;\r\n  left: 2px;\r\n  bottom: 2px;\r\n}\r\n/* Responsive styles  */\r\n@media only screen and (max-width: 1024px) {\r\n  .k-scheduler-toolbar > ul.k-scheduler-views {\r\n    position: absolute;\r\n    right: 6px;\r\n    top: 6px;\r\n    z-index: 10000;\r\n  }\r\n  .k-rtl .k-scheduler-toolbar > ul.k-scheduler-views {\r\n    right: auto;\r\n    left: 6px;\r\n  }\r\n  .k-scheduler-toolbar > ul.k-scheduler-views > li:not(.k-current-view) {\r\n    display: none;\r\n  }\r\n  .k-scheduler-toolbar li.k-nav-current .k-lg-date-format {\r\n    display: none;\r\n  }\r\n  .k-scheduler-toolbar li.k-nav-current .k-sm-date-format {\r\n    display: inline;\r\n  }\r\n  .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view {\r\n    display: block;\r\n    border-width: 1px;\r\n  }\r\n  .k-rtl .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view {\r\n    text-align: left;\r\n    padding-left: 1em;\r\n  }\r\n  .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view > .k-link {\r\n    display: block;\r\n    position: relative;\r\n    padding-right: 2.5em;\r\n    padding-left: 1em;\r\n    -webkit-box-sizing: border-box;\r\n            box-sizing: border-box;\r\n  }\r\n  .k-rtl .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view > .k-link {\r\n    padding-left: 0;\r\n  }\r\n  .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view > .k-link:after {\r\n    display: block;\r\n    content: \"\";\r\n    position: absolute;\r\n    top: 50%;\r\n    margin-top: -0.6em;\r\n    right: 0.333em;\r\n    width: 1.333em;\r\n    height: 1.333em;\r\n  }\r\n  .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded > li,\r\n  .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded > li:first-child + li {\r\n    display: block;\r\n    border: 0;\r\n    border-radius: 0;\r\n  }\r\n  .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded {\r\n    border: 1px solid #c5c5c5;\r\n    background-color: #fff;\r\n    background-image: none;\r\n    -webkit-box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.3);\r\n            box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.3);\r\n  }\r\n  .k-rtl .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded {\r\n    text-align: left;\r\n  }\r\n}\r\n/* animation classes */\r\n.k-fx-end .k-fx-next,\r\n.k-fx-end .k-fx-current {\r\n  -webkit-transition: all 350ms ease-out;\r\n          transition: all 350ms ease-out;\r\n}\r\n.k-fx {\r\n  position: relative;\r\n}\r\n.k-fx .k-fx-current {\r\n  z-index: 0;\r\n}\r\n.k-fx .k-fx-next {\r\n  z-index: 1;\r\n}\r\n.k-fx-hidden,\r\n.k-fx-hidden * {\r\n  visibility: hidden !important;\r\n}\r\n.k-fx-reverse .k-fx-current {\r\n  z-index: 1;\r\n}\r\n.k-fx-reverse .k-fx-next {\r\n  z-index: 0;\r\n}\r\n/* Zoom */\r\n.k-fx-zoom.k-fx-start .k-fx-next {\r\n  -webkit-transform: scale(0) !important;\r\n      -ms-transform: scale(0) !important;\r\n          transform: scale(0) !important;\r\n}\r\n.k-fx-zoom.k-fx-end .k-fx-next {\r\n  -webkit-transform: scale(1) !important;\r\n      -ms-transform: scale(1) !important;\r\n          transform: scale(1) !important;\r\n}\r\n.k-fx-zoom.k-fx-reverse.k-fx-start .k-fx-next,\r\n.k-fx-zoom.k-fx-reverse.k-fx-end .k-fx-next {\r\n  -webkit-transform: scale(1) !important;\r\n      -ms-transform: scale(1) !important;\r\n          transform: scale(1) !important;\r\n}\r\n.k-fx-zoom.k-fx-reverse.k-fx-start .k-fx-current {\r\n  -webkit-transform: scale(1) !important;\r\n      -ms-transform: scale(1) !important;\r\n          transform: scale(1) !important;\r\n}\r\n.k-fx-zoom.k-fx-reverse.k-fx-end .k-fx-current {\r\n  -webkit-transform: scale(0) !important;\r\n      -ms-transform: scale(0) !important;\r\n          transform: scale(0) !important;\r\n}\r\n/* Fade */\r\n.k-fx-fade.k-fx-start .k-fx-next {\r\n  will-change: opacity;\r\n  opacity: 0;\r\n}\r\n.k-fx-fade.k-fx-end .k-fx-next {\r\n  opacity: 1;\r\n}\r\n.k-fx-fade.k-fx-reverse.k-fx-start .k-fx-current {\r\n  will-change: opacity;\r\n  opacity: 1;\r\n}\r\n.k-fx-fade.k-fx-reverse.k-fx-end .k-fx-current {\r\n  opacity: 0;\r\n}\r\n/* Slide */\r\n.k-fx-slide {\r\n  /* left */\r\n  /* left reverse */\r\n  /* right */\r\n}\r\n.k-fx-slide.k-fx-end .k-fx-next .km-content,\r\n.k-fx-slide.k-fx-end .k-fx-next .km-header,\r\n.k-fx-slide.k-fx-end .k-fx-next .km-footer,\r\n.k-fx-slide.k-fx-end .k-fx-current .km-content,\r\n.k-fx-slide.k-fx-end .k-fx-current .km-header,\r\n.k-fx-slide.k-fx-end .k-fx-current .km-footer {\r\n  -webkit-transition: all 350ms ease-out;\r\n          transition: all 350ms ease-out;\r\n}\r\n.k-fx-slide.k-fx-start .k-fx-next .km-content {\r\n  will-change: transform;\r\n  -webkit-transform: translatex(100%);\r\n      -ms-transform: translatex(100%);\r\n          transform: translatex(100%);\r\n}\r\n.k-fx-slide.k-fx-start .k-fx-next .km-header,\r\n.k-fx-slide.k-fx-start .k-fx-next .km-footer {\r\n  will-change: opacity;\r\n  opacity: 0;\r\n}\r\n.k-fx-slide.k-fx-end .k-fx-current .km-content {\r\n  -webkit-transform: translatex(-100%);\r\n      -ms-transform: translatex(-100%);\r\n          transform: translatex(-100%);\r\n}\r\n.k-fx-slide.k-fx-end .k-fx-next .km-header,\r\n.k-fx-slide.k-fx-end .k-fx-next .km-footer {\r\n  opacity: 1;\r\n}\r\n.k-fx-slide.k-fx-reverse.k-fx-start .k-fx-current .km-content {\r\n  will-change: transform;\r\n  -webkit-transform: translatex(0);\r\n      -ms-transform: translatex(0);\r\n          transform: translatex(0);\r\n}\r\n.k-fx-slide.k-fx-reverse.k-fx-end .k-fx-current .km-content {\r\n  -webkit-transform: translatex(100%);\r\n      -ms-transform: translatex(100%);\r\n          transform: translatex(100%);\r\n}\r\n.k-fx-slide.k-fx-reverse.k-fx-start .k-fx-next .km-content {\r\n  -webkit-transform: translatex(-100%);\r\n      -ms-transform: translatex(-100%);\r\n          transform: translatex(-100%);\r\n}\r\n.k-fx-slide.k-fx-reverse.k-fx-end .k-fx-next .km-content {\r\n  -webkit-transform: translatex(0);\r\n      -ms-transform: translatex(0);\r\n          transform: translatex(0);\r\n}\r\n.k-fx-slide.k-fx-reverse.k-fx-start .k-fx-current .km-header,\r\n.k-fx-slide.k-fx-reverse.k-fx-start .k-fx-current .km-footer {\r\n  will-change: opacity;\r\n  opacity: 1;\r\n}\r\n.k-fx-slide.k-fx-reverse.k-fx-start .k-fx-next .km-header,\r\n.k-fx-slide.k-fx-reverse.k-fx-start .k-fx-next .km-footer {\r\n  opacity: 1;\r\n}\r\n.k-fx-slide.k-fx-reverse.k-fx-end .k-fx-current .km-header,\r\n.k-fx-slide.k-fx-reverse.k-fx-end .k-fx-current .km-footer {\r\n  opacity: 0;\r\n}\r\n.k-fx-slide.k-fx-reverse.k-fx-end .k-fx-next .km-header,\r\n.k-fx-slide.k-fx-reverse.k-fx-end .k-fx-next .km-footer {\r\n  opacity: 1;\r\n}\r\n.k-fx-slide.k-fx-right {\r\n  /* right reverse */\r\n}\r\n.k-fx-slide.k-fx-right.k-fx-start .k-fx-next .km-content {\r\n  -webkit-transform: translatex(-100%);\r\n      -ms-transform: translatex(-100%);\r\n          transform: translatex(-100%);\r\n}\r\n.k-fx-slide.k-fx-right.k-fx-end .k-fx-current .km-content {\r\n  -webkit-transform: translatex(100%);\r\n      -ms-transform: translatex(100%);\r\n          transform: translatex(100%);\r\n}\r\n.k-fx-slide.k-fx-right.k-fx-reverse.k-fx-start .k-fx-current .km-content {\r\n  -webkit-transform: translatex(0);\r\n      -ms-transform: translatex(0);\r\n          transform: translatex(0);\r\n}\r\n.k-fx-slide.k-fx-right.k-fx-reverse.k-fx-end .k-fx-current .km-content {\r\n  -webkit-transform: translatex(-100%);\r\n      -ms-transform: translatex(-100%);\r\n          transform: translatex(-100%);\r\n}\r\n.k-fx-slide.k-fx-right.k-fx-reverse.k-fx-start .k-fx-next .km-content {\r\n  -webkit-transform: translatex(100%);\r\n      -ms-transform: translatex(100%);\r\n          transform: translatex(100%);\r\n}\r\n.k-fx-slide.k-fx-right.k-fx-reverse.k-fx-end .k-fx-next .km-content {\r\n  -webkit-transform: translatex(0%);\r\n      -ms-transform: translatex(0%);\r\n          transform: translatex(0%);\r\n}\r\n/* Tile */\r\n.k-fx-tile {\r\n  /* left */\r\n  /* left reverse */\r\n  /* right */\r\n}\r\n.k-fx-tile.k-fx-start .k-fx-next {\r\n  will-change: transform;\r\n  -webkit-transform: translatex(100%);\r\n      -ms-transform: translatex(100%);\r\n          transform: translatex(100%);\r\n}\r\n.k-fx-tile.k-fx-end .k-fx-current {\r\n  -webkit-transform: translatex(-100%);\r\n      -ms-transform: translatex(-100%);\r\n          transform: translatex(-100%);\r\n}\r\n.k-fx-tile.k-fx-reverse.k-fx-start .k-fx-current {\r\n  will-change: transform;\r\n  -webkit-transform: translatex(0);\r\n      -ms-transform: translatex(0);\r\n          transform: translatex(0);\r\n}\r\n.k-fx-tile.k-fx-reverse.k-fx-end .k-fx-current {\r\n  -webkit-transform: translatex(100%);\r\n      -ms-transform: translatex(100%);\r\n          transform: translatex(100%);\r\n}\r\n.k-fx-tile.k-fx-reverse.k-fx-start .k-fx-next {\r\n  -webkit-transform: translatex(-100%);\r\n      -ms-transform: translatex(-100%);\r\n          transform: translatex(-100%);\r\n}\r\n.k-fx-tile.k-fx-reverse.k-fx-end .k-fx-next {\r\n  -webkit-transform: translatex(0);\r\n      -ms-transform: translatex(0);\r\n          transform: translatex(0);\r\n}\r\n.k-fx-tile.k-fx-right {\r\n  /* right reverse */\r\n}\r\n.k-fx-tile.k-fx-right.k-fx-start .k-fx-next {\r\n  -webkit-transform: translatex(-100%);\r\n      -ms-transform: translatex(-100%);\r\n          transform: translatex(-100%);\r\n}\r\n.k-fx-tile.k-fx-right.k-fx-end .k-fx-current {\r\n  -webkit-transform: translatex(100%);\r\n      -ms-transform: translatex(100%);\r\n          transform: translatex(100%);\r\n}\r\n.k-fx-tile.k-fx-right.k-fx-reverse.k-fx-start .k-fx-current {\r\n  -webkit-transform: translatex(0);\r\n      -ms-transform: translatex(0);\r\n          transform: translatex(0);\r\n}\r\n.k-fx-tile.k-fx-right.k-fx-reverse.k-fx-end .k-fx-current {\r\n  -webkit-transform: translatex(-100%);\r\n      -ms-transform: translatex(-100%);\r\n          transform: translatex(-100%);\r\n}\r\n.k-fx-tile.k-fx-right.k-fx-reverse.k-fx-start .k-fx-next {\r\n  -webkit-transform: translatex(100%);\r\n      -ms-transform: translatex(100%);\r\n          transform: translatex(100%);\r\n}\r\n.k-fx-tile.k-fx-right.k-fx-reverse.k-fx-end .k-fx-next {\r\n  -webkit-transform: translatex(0%);\r\n      -ms-transform: translatex(0%);\r\n          transform: translatex(0%);\r\n}\r\n/* Tile */\r\n.k-fx-tile {\r\n  /* left */\r\n  /* left reverse */\r\n  /* right */\r\n}\r\n.k-fx-tile.k-fx-start .k-fx-next {\r\n  will-change: transform;\r\n  -webkit-transform: translatex(100%);\r\n      -ms-transform: translatex(100%);\r\n          transform: translatex(100%);\r\n}\r\n.k-fx-tile.k-fx-end .k-fx-current {\r\n  -webkit-transform: translatex(-100%);\r\n      -ms-transform: translatex(-100%);\r\n          transform: translatex(-100%);\r\n}\r\n.k-fx-tile.k-fx-reverse.k-fx-start .k-fx-current {\r\n  will-change: transform;\r\n  -webkit-transform: translatex(0);\r\n      -ms-transform: translatex(0);\r\n          transform: translatex(0);\r\n}\r\n.k-fx-tile.k-fx-reverse.k-fx-end .k-fx-current {\r\n  -webkit-transform: translatex(100%);\r\n      -ms-transform: translatex(100%);\r\n          transform: translatex(100%);\r\n}\r\n.k-fx-tile.k-fx-reverse.k-fx-start .k-fx-next {\r\n  -webkit-transform: translatex(-100%);\r\n      -ms-transform: translatex(-100%);\r\n          transform: translatex(-100%);\r\n}\r\n.k-fx-tile.k-fx-reverse.k-fx-end .k-fx-next {\r\n  -webkit-transform: translatex(0);\r\n      -ms-transform: translatex(0);\r\n          transform: translatex(0);\r\n}\r\n.k-fx-tile.k-fx-right {\r\n  /* right reverse */\r\n}\r\n.k-fx-tile.k-fx-right.k-fx-start .k-fx-next {\r\n  -webkit-transform: translatex(-100%);\r\n      -ms-transform: translatex(-100%);\r\n          transform: translatex(-100%);\r\n}\r\n.k-fx-tile.k-fx-right.k-fx-end .k-fx-current {\r\n  -webkit-transform: translatex(100%);\r\n      -ms-transform: translatex(100%);\r\n          transform: translatex(100%);\r\n}\r\n.k-fx-tile.k-fx-right.k-fx-reverse.k-fx-start .k-fx-current {\r\n  -webkit-transform: translatex(0);\r\n      -ms-transform: translatex(0);\r\n          transform: translatex(0);\r\n}\r\n.k-fx-tile.k-fx-right.k-fx-reverse.k-fx-end .k-fx-current {\r\n  -webkit-transform: translatex(-100%);\r\n      -ms-transform: translatex(-100%);\r\n          transform: translatex(-100%);\r\n}\r\n.k-fx-tile.k-fx-right.k-fx-reverse.k-fx-start .k-fx-next {\r\n  -webkit-transform: translatex(100%);\r\n      -ms-transform: translatex(100%);\r\n          transform: translatex(100%);\r\n}\r\n.k-fx-tile.k-fx-right.k-fx-reverse.k-fx-end .k-fx-next {\r\n  -webkit-transform: translatex(0%);\r\n      -ms-transform: translatex(0%);\r\n          transform: translatex(0%);\r\n}\r\n/* Overlay */\r\n.k-fx.k-fx-overlay.k-fx-start .k-fx-next,\r\n.k-fx.k-fx-overlay.k-fx-left.k-fx-start .k-fx-next {\r\n  will-change: transform;\r\n  -webkit-transform: translatex(100%);\r\n      -ms-transform: translatex(100%);\r\n          transform: translatex(100%);\r\n}\r\n.k-fx.k-fx-overlay.k-fx-right.k-fx-start .k-fx-next {\r\n  -webkit-transform: translatex(-100%);\r\n      -ms-transform: translatex(-100%);\r\n          transform: translatex(-100%);\r\n}\r\n.k-fx.k-fx-overlay.k-fx-up.k-fx-start .k-fx-next {\r\n  -webkit-transform: translatey(100%);\r\n      -ms-transform: translatey(100%);\r\n          transform: translatey(100%);\r\n}\r\n.k-fx.k-fx-overlay.k-fx-down.k-fx-start .k-fx-next {\r\n  -webkit-transform: translatey(-100%);\r\n      -ms-transform: translatey(-100%);\r\n          transform: translatey(-100%);\r\n}\r\n.k-fx.k-fx-overlay.k-fx-reverse.k-fx-start .k-fx-next {\r\n  -webkit-transform: none;\r\n      -ms-transform: none;\r\n          transform: none;\r\n}\r\n.k-fx.k-fx-overlay.k-fx-reverse.k-fx-start .k-fx-current {\r\n  will-change: transform;\r\n  -webkit-transform: none;\r\n      -ms-transform: none;\r\n          transform: none;\r\n}\r\n.k-fx.k-fx-overlay.k-fx-reverse.k-fx-end .k-fx-current,\r\n.k-fx.k-fx-overlay.k-fx-reverse.k-fx-left.k-fx-end .k-fx-current {\r\n  -webkit-transform: translatex(100%);\r\n      -ms-transform: translatex(100%);\r\n          transform: translatex(100%);\r\n}\r\n.k-fx.k-fx-overlay.k-fx-reverse.k-fx-right.k-fx-end .k-fx-current {\r\n  -webkit-transform: translatex(-100%);\r\n      -ms-transform: translatex(-100%);\r\n          transform: translatex(-100%);\r\n}\r\n.k-fx.k-fx-overlay.k-fx-reverse.k-fx-up.k-fx-end .k-fx-current {\r\n  -webkit-transform: translatey(100%);\r\n      -ms-transform: translatey(100%);\r\n          transform: translatey(100%);\r\n}\r\n.k-fx.k-fx-overlay.k-fx-reverse.k-fx-down.k-fx-end .k-fx-current {\r\n  -webkit-transform: translatey(-100%);\r\n      -ms-transform: translatey(-100%);\r\n          transform: translatey(-100%);\r\n}\r\n/* Virtual List */\r\n.k-virtual-wrap {\r\n  position: relative;\r\n}\r\n.k-virtual-wrap .k-list.k-virtual-list {\r\n  height: auto;\r\n}\r\n.k-virtual-content {\r\n  overflow-y: scroll;\r\n  /* has to be scroll, not auto */\r\n  -webkit-overflow-scrolling: touch;\r\n  position: relative;\r\n}\r\n.k-virtual-list > .k-virtual-content {\r\n  position: absolute;\r\n  top: 0;\r\n  width: 100%;\r\n  margin: 0;\r\n  padding: 0;\r\n  list-style-type: none;\r\n}\r\n.k-virtual-option-label {\r\n  width: 100%;\r\n  margin: 0;\r\n  padding: 0;\r\n  list-style-type: none;\r\n}\r\n.k-virtual-wrap > .k-virtual-header {\r\n  text-align: right;\r\n}\r\n.k-popup .k-item.k-first {\r\n  position: relative;\r\n}\r\n.k-virtual-content > .k-virtual-list > .k-virtual-item {\r\n  position: absolute;\r\n  width: 100%;\r\n  -webkit-box-sizing: border-box;\r\n          box-sizing: border-box;\r\n  overflow: hidden;\r\n  white-space: nowrap;\r\n}\r\n.k-popup .k-list .k-item > .k-group,\r\n.k-popup > .k-group-header,\r\n.k-popup > .k-virtual-wrap > .k-group-header {\r\n  text-transform: uppercase;\r\n  font-size: .857em;\r\n}\r\n.k-popup .k-list .k-item > .k-group {\r\n  position: absolute;\r\n  top: 0;\r\n  right: 0;\r\n  padding: 0 .5em;\r\n  line-height: 1.8;\r\n}\r\n.k-popup .k-state-hover > .k-group {\r\n  right: -1px;\r\n}\r\n.k-virtual-item.k-first,\r\n.k-group-header + .k-list > .k-item.k-first,\r\n.k-static-header + .k-list > .k-item.k-first {\r\n  border-top-style: solid;\r\n  border-top-width: 1px;\r\n  padding-top: 0;\r\n}\r\n.k-popup > .k-group-header,\r\n.k-popup > .k-virtual-wrap > .k-group-header {\r\n  text-align: right;\r\n}\r\n@font-face {\r\n  font-family: 'KendoUIGlyphs';\r\n  src: url('fonts/glyphs/KendoUIGlyphs.eot?y6oa7j');\r\n  src: url('fonts/glyphs/KendoUIGlyphs.eot?y6oa7j?#iefix') format('embedded-opentype'), url('fonts/glyphs/KendoUIGlyphs.ttf?y6oa7j') format('truetype'), url('fonts/glyphs/KendoUIGlyphs.woff?y6oa7j') format('woff'), url('fonts/glyphs/KendoUIGlyphs.svg?y6oa7j#KendoUIGlyphs') format('svg');\r\n  font-weight: normal;\r\n  font-style: normal;\r\n}\r\n.k-i-arrow-n:before {\r\n  content: \"\\e600\";\r\n}\r\n.k-i-arrow-e:before {\r\n  content: \"\\e601\";\r\n}\r\n.k-i-arrow-s:before {\r\n  content: \"\\e602\";\r\n}\r\n.k-i-arrow-w:before {\r\n  content: \"\\e603\";\r\n}\r\n.k-i-seek-n:before {\r\n  content: \"\\e604\";\r\n}\r\n.k-i-seek-e:before {\r\n  content: \"\\e605\";\r\n}\r\n.k-i-seek-s:before {\r\n  content: \"\\e606\";\r\n}\r\n.k-i-seek-w:before {\r\n  content: \"\\e607\";\r\n}\r\n.k-i-sarrow-n:before {\r\n  content: \"\\e608\";\r\n}\r\n.k-i-sarrow-e:before {\r\n  content: \"\\e609\";\r\n}\r\n.k-i-sarrow-s:before {\r\n  content: \"\\e60a\";\r\n}\r\n.k-i-sarrow-w:before {\r\n  content: \"\\e60b\";\r\n}\r\n.k-i-expand-n:before {\r\n  content: \"\\e60c\";\r\n}\r\n.k-i-expand-e:before {\r\n  content: \"\\e60d\";\r\n}\r\n.k-i-expand-s:before {\r\n  content: \"\\e60e\";\r\n}\r\n.k-i-expand-w:before {\r\n  content: \"\\e60f\";\r\n}\r\n.k-i-collapse-ne:before {\r\n  content: \"\\e610\";\r\n}\r\n.k-i-collapse-se:before {\r\n  content: \"\\e611\";\r\n}\r\n.k-i-collapse-sw:before {\r\n  content: \"\\e612\";\r\n}\r\n.k-i-collapse-nw:before {\r\n  content: \"\\e613\";\r\n}\r\n.k-i-resize-ne:before {\r\n  content: \"\\e614\";\r\n}\r\n.k-i-resize-se:before {\r\n  content: \"\\e615\";\r\n}\r\n.k-i-resize-sw:before {\r\n  content: \"\\e616\";\r\n}\r\n.k-i-resize-nw:before {\r\n  content: \"\\e617\";\r\n}\r\n.k-i-arrowhead-n:before {\r\n  content: \"\\e618\";\r\n}\r\n.k-i-arrowhead-e:before {\r\n  content: \"\\e619\";\r\n}\r\n.k-i-arrowhead-s:before {\r\n  content: \"\\e61a\";\r\n}\r\n.k-i-arrowhead-w:before {\r\n  content: \"\\e61b\";\r\n}\r\n.k-i-pencil:before {\r\n  content: \"\\e61c\";\r\n}\r\n.k-i-x:before {\r\n  content: \"\\e61d\";\r\n}\r\n.k-i-checkmark:before {\r\n  content: \"\\e61e\";\r\n}\r\n.k-i-deny:before {\r\n  content: \"\\e61f\";\r\n}\r\n.k-i-trash:before {\r\n  content: \"\\e620\";\r\n}\r\n.k-i-plus:before {\r\n  content: \"\\e621\";\r\n}\r\n.k-i-splus:before {\r\n  content: \"\\e622\";\r\n}\r\n.k-i-minus:before {\r\n  content: \"\\e623\";\r\n}\r\n.k-i-sminus:before {\r\n  content: \"\\e624\";\r\n}\r\n.k-i-filter:before {\r\n  content: \"\\e625\";\r\n}\r\n.k-i-filter-clear:before {\r\n  content: \"\\e626\";\r\n}\r\n.k-i-refresh:before {\r\n  content: \"\\e627\";\r\n}\r\n.k-i-refresh-clear:before {\r\n  content: \"\\e628\";\r\n}\r\n.k-i-restore:before {\r\n  content: \"\\e629\";\r\n}\r\n.k-i-maximize:before {\r\n  content: \"\\e62a\";\r\n}\r\n.k-i-minimize:before {\r\n  content: \"\\e62b\";\r\n}\r\n.k-i-pin:before {\r\n  content: \"\\e62c\";\r\n}\r\n.k-i-unpin:before {\r\n  content: \"\\e62d\";\r\n}\r\n.k-i-calendar:before {\r\n  content: \"\\e62e\";\r\n}\r\n.k-i-clock:before {\r\n  content: \"\\e62f\";\r\n}\r\n.k-i-search:before {\r\n  content: \"\\e630\";\r\n}\r\n.k-i-zoom-in:before {\r\n  content: \"\\e631\";\r\n}\r\n.k-i-zoom-out:before {\r\n  content: \"\\e632\";\r\n}\r\n.k-i-print:before {\r\n  content: \"\\e633\";\r\n}\r\n.k-i-folder-add:before {\r\n  content: \"\\e634\";\r\n}\r\n.k-i-folder-up:before {\r\n  content: \"\\e635\";\r\n}\r\n.k-i-folder-open:before {\r\n  content: \"\\e634\";\r\n}\r\n.k-i-insert-image:before {\r\n  content: \"\\e636\";\r\n}\r\n.k-i-image:before {\r\n  content: \"\\e637\";\r\n}\r\n.k-i-insert-file:before {\r\n  content: \"\\e638\";\r\n}\r\n.k-i-file:before {\r\n  content: \"\\e639\";\r\n}\r\n.k-i-files:before {\r\n  content: \"\\e63a\";\r\n}\r\n.k-i-pdf:before {\r\n  content: \"\\e63b\";\r\n}\r\n.k-i-pdfa:before {\r\n  content: \"\\e68d\";\r\n}\r\n.k-i-xls:before {\r\n  content: \"\\e63c\";\r\n}\r\n.k-i-xlsa:before {\r\n  content: \"\\e63d\";\r\n}\r\n.k-i-lock:before {\r\n  content: \"\\e63e\";\r\n}\r\n.k-i-unlock:before {\r\n  content: \"\\e63f\";\r\n}\r\n.k-i-rows:before {\r\n  content: \"\\e640\";\r\n}\r\n.k-i-columns:before {\r\n  content: \"\\e641\";\r\n}\r\n.k-i-hamburger:before {\r\n  content: \"\\e642\";\r\n}\r\n.k-i-vbars:before {\r\n  content: \"\\e643\";\r\n}\r\n.k-i-hbars:before {\r\n  content: \"\\e644\";\r\n}\r\n.k-i-move:before {\r\n  content: \"\\e645\";\r\n}\r\n.k-i-group:before {\r\n  content: \"\\e646\";\r\n}\r\n.k-i-ungroup:before {\r\n  content: \"\\e647\";\r\n}\r\n.k-i-dimension:before {\r\n  content: \"\\e648\";\r\n}\r\n.k-i-connector:before {\r\n  content: \"\\e649\";\r\n}\r\n.k-i-kpi:before {\r\n  content: \"\\e64a\";\r\n}\r\n.k-i-undo:before {\r\n  content: \"\\e64b\";\r\n}\r\n.k-i-redo:before {\r\n  content: \"\\e64c\";\r\n}\r\n.k-i-undo-large:before {\r\n  content: \"\\e64d\";\r\n}\r\n.k-i-redo-large:before {\r\n  content: \"\\e64e\";\r\n}\r\n.k-i-rotate-ccw:before {\r\n  content: \"\\e64f\";\r\n}\r\n.k-i-rotate-cw:before {\r\n  content: \"\\e650\";\r\n}\r\n.k-i-cut:before {\r\n  content: \"\\e651\";\r\n}\r\n.k-i-copy:before {\r\n  content: \"\\e652\";\r\n}\r\n.k-i-paste:before {\r\n  content: \"\\e653\";\r\n}\r\n.k-i-bold:before {\r\n  content: \"\\e654\";\r\n}\r\n.k-i-italic:before {\r\n  content: \"\\e655\";\r\n}\r\n.k-i-underline:before {\r\n  content: \"\\e656\";\r\n}\r\n.k-i-strike-through:before {\r\n  content: \"\\e657\";\r\n}\r\n.k-i-text:before {\r\n  content: \"\\e658\";\r\n}\r\n.k-i-font-size:before {\r\n  content: \"\\e68e\";\r\n}\r\n.k-i-font-family:before {\r\n  content: \"\\e68f\";\r\n}\r\n.k-i-fx:before {\r\n  content: \"\\e659\";\r\n}\r\n.k-i-subscript:before {\r\n  content: \"\\e65a\";\r\n}\r\n.k-i-superscript:before {\r\n  content: \"\\e65b\";\r\n}\r\n.k-i-background:before {\r\n  content: \"\\e65c\";\r\n}\r\n.k-i-sum:before {\r\n  content: \"\\e65d\";\r\n}\r\n.k-i-increase-decimal:before {\r\n  content: \"\\e65e\";\r\n}\r\n.k-i-decrease-decimal:before {\r\n  content: \"\\e65f\";\r\n}\r\n.k-i-justify-left:before {\r\n  content: \"\\e660\";\r\n}\r\n.k-i-justify-center:before {\r\n  content: \"\\e661\";\r\n}\r\n.k-i-justify-right:before {\r\n  content: \"\\e662\";\r\n}\r\n.k-i-justify-full:before {\r\n  content: \"\\e663\";\r\n}\r\n.k-i-justify-clear:before {\r\n  content: \"\\e664\";\r\n}\r\n.k-i-align-top:before {\r\n  content: \"\\e665\";\r\n}\r\n.k-i-align-middle:before {\r\n  content: \"\\e666\";\r\n}\r\n.k-i-align-bottom:before {\r\n  content: \"\\e667\";\r\n}\r\n.k-i-indent:before {\r\n  content: \"\\e668\";\r\n}\r\n.k-i-outdent:before {\r\n  content: \"\\e669\";\r\n}\r\n.k-i-insert-n:before {\r\n  content: \"\\e66a\";\r\n}\r\n.k-i-insert-m:before {\r\n  content: \"\\e66b\";\r\n}\r\n.k-i-insert-s:before {\r\n  content: \"\\e66c\";\r\n}\r\n.k-i-insert-unordered-list:before {\r\n  content: \"\\e66d\";\r\n}\r\n.k-i-insert-ordered-list:before {\r\n  content: \"\\e66e\";\r\n}\r\n.k-i-sort-asc:before {\r\n  content: \"\\e66f\";\r\n}\r\n.k-i-sort-desc:before {\r\n  content: \"\\e670\";\r\n}\r\n.k-i-unsort:before {\r\n  content: \"\\e671\";\r\n}\r\n.k-i-hyperlink:before {\r\n  content: \"\\e672\";\r\n}\r\n.k-i-hyperlink-remove:before {\r\n  content: \"\\e673\";\r\n}\r\n.k-i-clearformat:before {\r\n  content: \"\\e674\";\r\n}\r\n.k-i-html:before {\r\n  content: \"\\e675\";\r\n}\r\n.k-i-exception:before {\r\n  content: \"\\e676\";\r\n}\r\n.k-i-custom:before {\r\n  content: \"\\e677\";\r\n}\r\n.k-i-cog:before {\r\n  content: \"\\e678\";\r\n}\r\n.k-i-create-table:before {\r\n  content: \"\\e679\";\r\n}\r\n.k-i-add-column-left:before {\r\n  content: \"\\e67a\";\r\n}\r\n.k-i-add-column-right:before {\r\n  content: \"\\e67b\";\r\n}\r\n.k-i-delete-column:before {\r\n  content: \"\\e67c\";\r\n}\r\n.k-i-add-row-above:before {\r\n  content: \"\\e67d\";\r\n}\r\n.k-i-add-row-below:before {\r\n  content: \"\\e67e\";\r\n}\r\n.k-i-delete-row:before {\r\n  content: \"\\e67f\";\r\n}\r\n.k-i-merge-cells:before {\r\n  content: \"\\e680\";\r\n}\r\n.k-i-normal-layout:before {\r\n  content: \"\\e681\";\r\n}\r\n.k-i-page-layout:before {\r\n  content: \"\\e682\";\r\n}\r\n.k-i-all-borders:before {\r\n  content: \"\\e683\";\r\n}\r\n.k-i-inside-borders:before {\r\n  content: \"\\e684\";\r\n}\r\n.k-i-inside-horizontal-borders:before {\r\n  content: \"\\e685\";\r\n}\r\n.k-i-inside-vertical-borders:before {\r\n  content: \"\\e686\";\r\n}\r\n.k-i-outside-borders:before {\r\n  content: \"\\e687\";\r\n}\r\n.k-i-top-border:before {\r\n  content: \"\\e688\";\r\n}\r\n.k-i-right-border:before {\r\n  content: \"\\e689\";\r\n}\r\n.k-i-bottom-border:before {\r\n  content: \"\\e68a\";\r\n}\r\n.k-i-left-border:before {\r\n  content: \"\\e68b\";\r\n}\r\n.k-i-no-borders:before {\r\n  content: \"\\e68c\";\r\n}\r\n.k-i-merge-horizontally:before {\r\n  content: \"\\e690\";\r\n}\r\n.k-i-merge-vertically:before {\r\n  content: \"\\e691\";\r\n}\r\n.k-i-text-wrap:before {\r\n  content: \"\\e692\";\r\n}\r\n.k-i-dollar:before {\r\n  content: \"\\e693\";\r\n}\r\n.k-i-percent:before {\r\n  content: \"\\e694\";\r\n}\r\n.k-i-freeze-col:before {\r\n  content: \"\\e695\";\r\n}\r\n.k-i-freeze-row:before {\r\n  content: \"\\e696\";\r\n}\r\n.k-i-freeze-panes:before {\r\n  content: \"\\e697\";\r\n}\r\n.k-i-format-number:before {\r\n  content: \"\\e698\";\r\n}\r\n.k-i-reset-color:before {\r\n  content: \"\\e900\";\r\n}\r\n.k-i-file-horizontal:before {\r\n  content: \"\\e901\";\r\n}\r\n.k-i-folder:before {\r\n  content: \"\\e902\";\r\n}\r\n.k-i-folder-open:before {\r\n  content: \"\\e903\";\r\n}\r\n.k-spreadsheet {\r\n  width: 800px;\r\n  height: 600px;\r\n  position: relative;\r\n  border-width: 1px;\r\n  border-style: solid;\r\n  cursor: default;\r\n}\r\n.k-spreadsheet .k-spreadsheet-pane {\r\n  padding-right: 1px;\r\n  position: absolute;\r\n  border-width: 1px;\r\n  border-style: solid;\r\n  overflow: hidden;\r\n  -webkit-box-sizing: border-box;\r\n          box-sizing: border-box;\r\n}\r\n.k-spreadsheet .k-spreadsheet-pane.k-top {\r\n  border-top-width: 0;\r\n}\r\n.k-spreadsheet .k-spreadsheet-pane.k-left {\r\n  border-left-width: 0;\r\n}\r\n.k-spreadsheet .k-spreadsheet-pane .k-spreadsheet-cell {\r\n  position: absolute;\r\n  white-space: pre;\r\n  -webkit-box-sizing: border-box;\r\n          box-sizing: border-box;\r\n  overflow: hidden;\r\n  padding: 1px;\r\n  -webkit-background-clip: padding-box;\r\n          background-clip: padding-box;\r\n}\r\n.k-spreadsheet .k-spreadsheet-vaxis,\r\n.k-spreadsheet .k-spreadsheet-haxis {\r\n  position: absolute;\r\n  border-style: solid;\r\n}\r\n.k-spreadsheet .k-spreadsheet-vaxis {\r\n  top: 0;\r\n  border-width: 0 0 0 1px;\r\n}\r\n.k-spreadsheet .k-spreadsheet-haxis {\r\n  left: 0;\r\n  border-width: 1px 0 0;\r\n}\r\n.k-spreadsheet .k-filter-range,\r\n.k-spreadsheet .k-spreadsheet-selection {\r\n  border-width: 1px;\r\n  border-style: solid;\r\n  position: absolute;\r\n  -webkit-box-sizing: border-box;\r\n          box-sizing: border-box;\r\n}\r\n.k-spreadsheet .k-spreadsheet-active-cell {\r\n  position: absolute;\r\n  -webkit-box-sizing: border-box;\r\n          box-sizing: border-box;\r\n}\r\n.k-spreadsheet .k-spreadsheet-formula-bar {\r\n  border-width: 0 1px 1px;\r\n  border-style: solid;\r\n  padding-left: 31px;\r\n  position: relative;\r\n  font-size: 12px;\r\n  font-family: Arial, Verdana, sans-serif;\r\n}\r\n.k-spreadsheet .k-spreadsheet-formula-bar:before {\r\n  background-image: none;\r\n  font-family: 'KendoUIGlyphs';\r\n  speak: none;\r\n  font-style: normal;\r\n  font-weight: normal;\r\n  font-variant: normal;\r\n  text-transform: none;\r\n  font-size: 1.3em;\r\n  line-height: 1;\r\n  opacity: 1;\r\n  text-indent: 0;\r\n  -webkit-font-smoothing: antialiased;\r\n  -moz-osx-font-smoothing: grayscale;\r\n  content: \"\\e659\";\r\n  display: block;\r\n  height: 20px;\r\n  top: 3px;\r\n  border-width: 0 1px 0 0;\r\n  border-style: solid;\r\n  position: absolute;\r\n  width: 24px;\r\n  margin-left: -24px;\r\n}\r\n.k-spreadsheet .k-spreadsheet-formula-bar:after {\r\n  content: \"\";\r\n  display: block;\r\n  border-width: 0 0 1px;\r\n  border-style: solid;\r\n  position: absolute;\r\n  bottom: 0;\r\n  left: 0;\r\n  width: 100%;\r\n}\r\n.k-spreadsheet .k-spreadsheet-formula-input {\r\n  outline: none;\r\n  border-width: 0;\r\n  height: 26px;\r\n  line-height: 20px;\r\n  width: 100%;\r\n  -webkit-box-sizing: border-box;\r\n          box-sizing: border-box;\r\n  white-space: pre;\r\n  padding: 3px 0.5em 0;\r\n}\r\n.k-spreadsheet .k-spreadsheet-cell-editor {\r\n  position: absolute;\r\n  display: none;\r\n  padding: 0 3px;\r\n  line-height: 20px;\r\n  z-index: 2000;\r\n  overflow: hidden;\r\n}\r\n.k-spreadsheet > .k-spreadsheet-view {\r\n  position: relative;\r\n  font-size: 12px;\r\n  font-family: Arial, Verdana, sans-serif;\r\n}\r\n.k-spreadsheet .k-tabstrip-wrapper {\r\n  position: relative;\r\n  line-height: 1.7em;\r\n}\r\n.k-spreadsheet .k-tabstrip-wrapper .k-tabstrip-items {\r\n  padding: 0 0 0;\r\n}\r\n.k-spreadsheet .k-spreadsheet-quick-access-toolbar {\r\n  display: inline-block;\r\n  position: absolute;\r\n  z-index: 1;\r\n  top: 0;\r\n  left: 0;\r\n  padding: 0 0 0;\r\n}\r\n.k-spreadsheet .k-spreadsheet-quick-access-toolbar .k-button {\r\n  padding: .3em;\r\n  line-height: 1.4em;\r\n}\r\n.k-spreadsheet .k-merged-cells-wrapper,\r\n.k-spreadsheet .k-selection-wrapper,\r\n.k-spreadsheet .k-filter-wrapper,\r\n.k-spreadsheet .k-spreadsheet-row-header {\r\n  position: relative;\r\n}\r\n.k-spreadsheet .k-spreadsheet-column-header {\r\n  position: absolute;\r\n}\r\n.k-spreadsheet .k-spreadsheet-sheets-bar {\r\n  border-width: 1px 0 0;\r\n}\r\n.k-spreadsheet .k-vertical-align-center {\r\n  position: relative;\r\n  top: 50%;\r\n  -webkit-transform: translateY(-50%);\r\n      -ms-transform: translateY(-50%);\r\n          transform: translateY(-50%);\r\n}\r\n.k-spreadsheet .k-vertical-align-bottom {\r\n  position: relative;\r\n  top: 100%;\r\n  -webkit-transform: translateY(-100%);\r\n      -ms-transform: translateY(-100%);\r\n          transform: translateY(-100%);\r\n}\r\n.k-spreadsheet .k-dirty {\r\n  position: absolute;\r\n  top: 0;\r\n  right: 0;\r\n  margin: -2px 0 0 -7px;\r\n  border-width: 4px;\r\n  border-color: #f00 #f00 transparent transparent;\r\n}\r\n.k-spreadsheet .k-single-selection::after {\r\n  content: \" \";\r\n  height: 6px;\r\n  width: 6px;\r\n  position: absolute;\r\n  display: block;\r\n  bottom: 0;\r\n  right: 0;\r\n  border-radius: 50%;\r\n  margin-bottom: -5px;\r\n  margin-right: -5px;\r\n  border-width: 1px;\r\n  border-style: solid;\r\n  z-index: 100;\r\n  cursor: crosshair;\r\n}\r\n.k-spreadsheet .k-spreadsheet-edit-container .k-single-selection::after {\r\n  display: none;\r\n}\r\n.k-spreadsheet .k-auto-fill,\r\n.k-spreadsheet .k-auto-fill-punch {\r\n  position: absolute;\r\n  -webkit-box-sizing: border-box;\r\n          box-sizing: border-box;\r\n}\r\n.k-spreadsheet .k-auto-fill {\r\n  border-width: 1px;\r\n  border-style: solid;\r\n  cursor: crosshair;\r\n}\r\n.k-spreadsheet .k-auto-fill-wrapper {\r\n  position: relative;\r\n}\r\n.k-horizontal-resize {\r\n  cursor: col-resize;\r\n}\r\n.k-vertical-resize {\r\n  cursor: row-resize;\r\n}\r\n.k-spreadsheet-data,\r\n.k-merged-cells-wrapper,\r\n.k-selection-wrapper,\r\n.k-spreadsheet-active-cell {\r\n  cursor: cell;\r\n}\r\n.k-horizontal-resize .k-spreadsheet-data,\r\n.k-horizontal-resize .k-merged-cells-wrapper,\r\n.k-horizontal-resize .k-selection-wrapper,\r\n.k-horizontal-resize .k-spreadsheet-active-cell {\r\n  cursor: col-resize;\r\n}\r\n.k-vertical-resize .k-spreadsheet-data,\r\n.k-vertical-resize .k-merged-cells-wrapper,\r\n.k-vertical-resize .k-selection-wrapper,\r\n.k-vertical-resize .k-spreadsheet-active-cell {\r\n  cursor: row-resize;\r\n}\r\n.k-font-icon,\r\n.k-font-icon.k-icon,\r\n.k-font-icon.k-tool-icon {\r\n  font-family: 'KendoUIGlyphs';\r\n  speak: none;\r\n  font-style: normal;\r\n  font-weight: normal;\r\n  font-variant: normal;\r\n  text-transform: none;\r\n  font-size: 1.3em;\r\n  line-height: 1;\r\n  opacity: 1;\r\n  text-indent: 0;\r\n  -webkit-font-smoothing: antialiased;\r\n  -moz-osx-font-smoothing: grayscale;\r\n  background-image: none;\r\n  font-size: 16px;\r\n}\r\n.k-spreadsheet .k-colorpicker .k-tool-icon {\r\n  overflow: visible;\r\n}\r\n.k-button.k-reset-color,\r\n.k-button.k-custom-color {\r\n  display: block;\r\n  width: 100%;\r\n  border-radius: 0;\r\n  border: none;\r\n  text-align: left;\r\n  line-height: 2em;\r\n}\r\n.k-spreadsheet-colorpicker .k-colorpalette {\r\n  border: 1px solid #dbdbdb;\r\n  border-left: none;\r\n  border-right: none;\r\n  padding: 0.4em;\r\n}\r\n.k-spreadsheet-filter {\r\n  position: absolute;\r\n  cursor: pointer;\r\n}\r\n.k-spreadsheet-sample {\r\n  float: right;\r\n}\r\n.k-spreadsheet-clipboard-paste,\r\n.k-spreadsheet-clipboard {\r\n  position: absolute;\r\n  opacity: 0;\r\n  top: 0;\r\n  left: 0;\r\n  overflow: hidden;\r\n  padding: 0;\r\n  margin: 0;\r\n  border: 0;\r\n  width: 1px;\r\n  height: 1px;\r\n  -webkit-box-sizing: border-box;\r\n          box-sizing: border-box;\r\n}\r\ntextarea.k-spreadsheet-clipboard {\r\n  position: fixed;\r\n}\r\n.k-spreadsheet-top-corner {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  z-index: 10000;\r\n  border-width: 0 1px 1px 0;\r\n  border-style: solid;\r\n}\r\n.k-spreadsheet-top-corner:after {\r\n  content: \"\";\r\n  display: block;\r\n  width: 0;\r\n  height: 0;\r\n  overflow: hidden;\r\n  position: absolute;\r\n  bottom: 0;\r\n  right: 0;\r\n  border-width: 6px;\r\n  border-style: solid;\r\n}\r\n.k-spreadsheet-scroller {\r\n  width: 100%;\r\n  height: 100%;\r\n  overflow: scroll;\r\n  -webkit-overflow-scrolling: touch;\r\n  position: absolute;\r\n  z-index: 1;\r\n}\r\n.k-spreadsheet-fixed-container {\r\n  position: absolute;\r\n  width: 100%;\r\n  height: 100%;\r\n  -webkit-box-sizing: border-box;\r\n          box-sizing: border-box;\r\n  z-index: 2;\r\n  -webkit-user-select: none;\r\n     -moz-user-select: none;\r\n      -ms-user-select: none;\r\n          user-select: none;\r\n}\r\n.k-spreadsheet-view-size {\r\n  position: relative;\r\n}\r\n.k-spreadsheet-column-header,\r\n.k-spreadsheet-row-header {\r\n  text-align: center;\r\n}\r\n.k-spreadsheet-column-header .k-spreadsheet-cell,\r\n.k-spreadsheet-row-header .k-spreadsheet-cell {\r\n  border-style: solid;\r\n  border-width: 0 1px 1px 0;\r\n}\r\n.k-spreadsheet-window .k-root-tabs {\r\n  border-width: 0;\r\n  margin: -1em -1em 0;\r\n  padding: .5em 1em 0;\r\n}\r\n.k-spreadsheet-window .k-root-tabs .k-loading {\r\n  display: none;\r\n}\r\n.k-spreadsheet-window .k-list-wrapper {\r\n  padding: .15em 0;\r\n  border-width: 1px;\r\n  border-style: solid;\r\n  height: 190px;\r\n}\r\n.k-spreadsheet-window .k-list .k-item {\r\n  border-radius: 0;\r\n  padding: .5em .6em .4em .6em;\r\n  cursor: pointer;\r\n  line-height: 1.1em;\r\n}\r\n.k-spreadsheet-window .k-format-filter {\r\n  width: 100%;\r\n}\r\n.k-spreadsheet-window .k-format-filter + .k-group-header + .k-list-wrapper {\r\n  margin-top: 1em;\r\n}\r\n.k-spreadsheet-window .k-action-buttons {\r\n  padding: 1em;\r\n}\r\n.k-spreadsheet-window .k-edit-label {\r\n  width: 20%;\r\n}\r\n.k-spreadsheet-window .k-edit-field {\r\n  width: 70%;\r\n}\r\n.k-spreadsheet-window .k-edit-field .k-textbox,\r\n.k-spreadsheet-window .k-edit-field .k-dropdown {\r\n  width: 100%;\r\n}\r\n.k-spreadsheet-window .export-config {\r\n  border-width: 1px 0 0;\r\n  border-style: solid;\r\n  padding: 1em;\r\n  margin: 0 -1em;\r\n}\r\n.k-spreadsheet-window .export-config .k-edit-field {\r\n  width: 40%;\r\n  float: left;\r\n  margin-left: 30px;\r\n}\r\n.k-spreadsheet-window .k-edit-field > input[type=\"radio\"] {\r\n  display: inline;\r\n  opacity: 0;\r\n  width: 0;\r\n  margin: 0;\r\n}\r\n.k-spreadsheet-window .k-edit-field > .k-orientation-label {\r\n  position: relative;\r\n  display: inline-block;\r\n  text-align: center;\r\n  width: 28px;\r\n  height: 28px;\r\n  line-height: 28px;\r\n  border-width: 1px;\r\n  border-style: solid;\r\n}\r\n.k-spreadsheet-window .k-edit-field > .k-orientation-label:before {\r\n  font-family: 'KendoUIGlyphs';\r\n  font-size: 16px;\r\n}\r\n.k-spreadsheet-window .k-orientation-portrait-label:before {\r\n  content: \"\\e639\";\r\n}\r\n.k-spreadsheet-window .k-orientation-landscape-label:before {\r\n  content: \"\\e901\";\r\n}\r\n.k-spreadsheet-window .k-page-orientation {\r\n  position: absolute;\r\n  right: 4em;\r\n  top: 22%;\r\n  width: 64px;\r\n  height: 92px;\r\n  border-width: 1px;\r\n  border-style: solid;\r\n}\r\n.k-spreadsheet-window .k-page-orientation:before {\r\n  position: absolute;\r\n  top: 0;\r\n  right: 0;\r\n  content: \"\";\r\n  display: inline-block;\r\n  width: 0;\r\n  height: 0;\r\n  border-width: 3px;\r\n  border-style: solid;\r\n  margin-top: -1px;\r\n  margin-right: -1px;\r\n}\r\n.k-spreadsheet-window .k-page-landscape {\r\n  top: 26%;\r\n  width: 92px;\r\n  height: 64px;\r\n}\r\n.k-spreadsheet-window .k-margins-horizontal {\r\n  position: absolute;\r\n  top: 50%;\r\n  -webkit-transform: translateY(-50%);\r\n      -ms-transform: translateY(-50%);\r\n          transform: translateY(-50%);\r\n  width: 100%;\r\n  height: 62px;\r\n  border-width: 1px 0;\r\n  border-style: solid;\r\n}\r\n.k-spreadsheet-window .k-margins-vertical {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 50%;\r\n  -webkit-transform: translateX(-50%);\r\n      -ms-transform: translateX(-50%);\r\n          transform: translateX(-50%);\r\n  width: 34px;\r\n  height: 100%;\r\n  border-width: 0 1px;\r\n  border-style: solid;\r\n}\r\n.k-spreadsheet-window .k-page-landscape .k-margins-horizontal {\r\n  height: 34px;\r\n}\r\n.k-spreadsheet-window .k-page-landscape .k-margins-vertical {\r\n  width: 62px;\r\n}\r\n.k-spreadsheet-window .export-config:after,\r\n.k-spreadsheet-window > div:after {\r\n  content: \" \";\r\n  display: block;\r\n  clear: both;\r\n  height: 0;\r\n}\r\n.k-spreadsheet-format-cells .k-spreadsheet-preview {\r\n  font-weight: bold;\r\n  text-align: center;\r\n  font-size: 1.16em;\r\n  line-height: 3.64em;\r\n  margin: 0 -0.863em 0.72em;\r\n  border-style: solid;\r\n  border-width: 1px 0;\r\n}\r\n.k-spreadsheet-border-palette {\r\n  width: 153px;\r\n}\r\n.k-spreadsheet-border-palette .k-spreadsheet-border-type-palette .k-button {\r\n  -webkit-box-sizing: border-box;\r\n          box-sizing: border-box;\r\n  width: 20%;\r\n}\r\n.k-spreadsheet-border-palette .k-colorpalette {\r\n  vertical-align: bottom;\r\n}\r\n.k-spreadsheet-popup .k-separator {\r\n  width: 1px;\r\n  height: 1.8em;\r\n  vertical-align: middle;\r\n  display: inline-block;\r\n}\r\n.k-spreadsheet-popup .k-colorpalette {\r\n  vertical-align: top;\r\n}\r\n.k-spreadsheet-popup .k-button.k-button-icon {\r\n  padding: 0.6em;\r\n  border-width: 0;\r\n  border-radius: 0;\r\n}\r\n.k-spreadsheet-popup .k-button.k-button-icontext {\r\n  display: block;\r\n  text-align: left;\r\n  text-transform: initial;\r\n  padding: 0.6em 1.2em 0.6em 1em;\r\n  border-width: 0;\r\n  border-radius: 0;\r\n}\r\n.k-spreadsheet-filter-menu {\r\n  width: 280px;\r\n}\r\n.k-spreadsheet-filter-menu > .k-menu {\r\n  border-width: 0;\r\n}\r\n.k-spreadsheet-filter-menu > .k-menu .k-link {\r\n  padding-left: 26px;\r\n}\r\n.k-spreadsheet-filter-menu > .k-menu .k-icon.k-font-icon {\r\n  margin-left: -26px;\r\n  width: 26px;\r\n}\r\n.k-spreadsheet-filter-menu .k-spreadsheet-value-treeview-wrapper {\r\n  height: 200px;\r\n  overflow-y: scroll;\r\n  overflow-x: auto;\r\n  border-width: 1px;\r\n  border-style: solid;\r\n}\r\n.k-spreadsheet-filter-menu .k-spreadsheet-value-treeview-wrapper .k-treeview {\r\n  overflow: visible;\r\n  padding: 6px 7px;\r\n}\r\n.k-spreadsheet-filter-menu .k-details {\r\n  border-top-width: 1px;\r\n  border-top-style: solid;\r\n  padding: 4px 0;\r\n}\r\n.k-spreadsheet-filter-menu .k-details-summary {\r\n  cursor: pointer;\r\n  line-height: 26px;\r\n}\r\n.k-spreadsheet-filter-menu .k-details-summary .k-icon {\r\n  margin: 0 5px;\r\n}\r\n.k-spreadsheet-filter-menu .k-details-content {\r\n  padding: 0 8px 0 26px;\r\n}\r\n.k-spreadsheet-filter-menu .k-details-content > .k-textbox,\r\n.k-spreadsheet-filter-menu .k-details-content > .k-widget {\r\n  width: 100%;\r\n  margin-bottom: 3px;\r\n}\r\n.k-spreadsheet-filter-menu .k-details-content .k-space-right {\r\n  background-image: none;\r\n}\r\n.k-spreadsheet-filter-menu .k-details-content .k-filter-and {\r\n  width: 75px;\r\n  margin: 8px 0;\r\n}\r\n.k-spreadsheet-filter-menu .k-action-buttons {\r\n  border-top-width: 0;\r\n  margin: 8px;\r\n  padding: 0;\r\n  position: static;\r\n}\r\n.k-resize-handle,\r\n.k-resize-hint {\r\n  position: absolute;\r\n}\r\n.k-resize-hint-handle {\r\n  width: 100%;\r\n  height: 20px;\r\n}\r\n.k-resize-hint-marker {\r\n  width: 2px;\r\n  height: 100%;\r\n  margin: 0 auto;\r\n}\r\n.k-resize-hint-vertical .k-resize-hint-handle {\r\n  height: 100%;\r\n  width: 20px;\r\n  float: left;\r\n}\r\n.k-resize-hint-vertical .k-resize-hint-marker {\r\n  height: 2px;\r\n  width: 100%;\r\n}\r\n.k-spreadsheet-quick-access-toolbar .k-button,\r\n.k-button.k-spreadsheet-sheets-bar-add {\r\n  border-color: transparent;\r\n  background-color: transparent;\r\n  background-image: none;\r\n}\r\n/* top TabStrip */\r\n.k-spreadsheet-tabstrip {\r\n  border-left-width: 0;\r\n  border-right-width: 0;\r\n  border-top-width: 0;\r\n}\r\n.k-spreadsheet-tabstrip > .k-content {\r\n  border-left-width: 0;\r\n  border-right-width: 0;\r\n  border-bottom-width: 0;\r\n  margin: 0;\r\n  padding: 0;\r\n}\r\n.k-spreadsheet-tabstrip .k-loading {\r\n  display: none;\r\n}\r\n.k-spreadsheet-toolbar.k-toolbar {\r\n  border-top-width: 0;\r\n  border-left-width: 0;\r\n  border-right-width: 0;\r\n}\r\n.k-spreadsheet-toolbar.k-toolbar .k-overflow-anchor + * {\r\n  margin-left: 0;\r\n}\r\n.k-spreadsheet-toolbar.k-toolbar > .k-button-group {\r\n  border-width: 0;\r\n}\r\n.k-spreadsheet-toolbar > .k-widget,\r\n.k-spreadsheet-toolbar .k-button,\r\n.k-spreadsheet-toolbar > .k-button-group,\r\n.k-spreadsheet-toolbar > .k-widget .k-state-default:not(.k-state-hover):not(.k-state-active) {\r\n  margin: 0;\r\n  border-color: transparent;\r\n  background-color: transparent;\r\n  background-image: none;\r\n}\r\n.k-spreadsheet-toolbar > .k-widget[data-property='fontSize'] {\r\n  width: 75px;\r\n}\r\n.k-spreadsheet-toolbar > .k-widget[data-property='format'] {\r\n  width: 100px;\r\n}\r\n.k-spreadsheet-toolbar > .k-widget[data-property='fontFamily'] {\r\n  width: 130px;\r\n}\r\n.k-spreadsheet-toolbar > .k-combobox .k-state-default:not(.k-state-hover):not(.k-state-active) .k-input {\r\n  background-color: transparent;\r\n  -webkit-box-shadow: none;\r\n          box-shadow: none;\r\n}\r\n.k-spreadsheet-toolbar > .k-widget .k-state-default:not(.k-state-hover):not(.k-state-active) .k-select {\r\n  border-color: transparent;\r\n}\r\n.k-spreadsheet-toolbar .k-separator {\r\n  margin: 0 3px;\r\n  width: 0;\r\n  overflow: hidden;\r\n  height: 1.8em;\r\n  vertical-align: middle;\r\n  display: inline-block;\r\n}\r\n.k-spreadsheet-formula-input > .k-syntax-func.k-syntax-at-point,\r\n.k-spreadsheet-formula-input > .k-syntax-bool.k-syntax-at-point,\r\n.k-spreadsheet-formula-input > .k-syntax-ref.k-syntax-at-point,\r\n.k-spreadsheet-formula-input > .k-syntax-str.k-syntax-at-point,\r\n.k-spreadsheet-formula-input > .k-syntax-num.k-syntax-at-point {\r\n  text-decoration: underline;\r\n}\r\n.k-spreadsheet-formula-input > .k-series-a,\r\n.k-spreadsheet-formula-input > .k-series-b,\r\n.k-spreadsheet-formula-input > .k-series-c,\r\n.k-spreadsheet-formula-input > .k-series-d {\r\n  background-color: transparent;\r\n}\r\n.k-spreadsheet-selection-highlight {\r\n  border-width: 1px;\r\n  border-style: solid;\r\n  position: absolute;\r\n  -webkit-box-sizing: border-box;\r\n          box-sizing: border-box;\r\n}\r\n.k-spreadsheet-formula-list {\r\n  min-width: 100px;\r\n}\r\n.k-spreadsheet-formula-list .k-item {\r\n  padding: 0 .3em;\r\n  -webkit-box-shadow: none;\r\n          box-shadow: none;\r\n}\r\n/* bottom TabStrip */\r\n.k-spreadsheet-sheets-bar {\r\n  position: relative;\r\n}\r\n.k-button.k-spreadsheet-sheets-bar-add {\r\n  position: absolute;\r\n  bottom: .2em;\r\n  left: .4em;\r\n  z-index: 1;\r\n}\r\n.k-spreadsheet-sheets-bar .k-spreadsheet-sheets-remove {\r\n  padding: 0;\r\n  margin: 0.5em 0.5em 0.5em -0.5em;\r\n}\r\n.k-spreadsheet-sheets-bar .k-spreadsheet-sheets-remove .k-button-icon {\r\n  padding: 2px;\r\n  line-height: normal;\r\n}\r\n.k-spreadsheet-sheets-bar .k-spreadsheet-sheets-remove .k-icon {\r\n  margin: 0;\r\n}\r\n.k-spreadsheet-sheets-items-hint {\r\n  margin-top: .1em;\r\n  background: none !important;\r\n  border-width: 0 !important;\r\n}\r\n/* Default fonts for PDF export */\r\n/* sans-serif */\r\n@font-face {\r\n  font-family: \"DejaVu Sans\";\r\n  src: url(\"fonts/DejaVu/DejaVuSans.ttf?v=1.1\") format(\"truetype\");\r\n}\r\n@font-face {\r\n  font-family: \"DejaVu Sans\";\r\n  font-weight: bold;\r\n  src: url(\"fonts/DejaVu/DejaVuSans-Bold.ttf?v=1.1\") format(\"truetype\");\r\n}\r\n@font-face {\r\n  font-family: \"DejaVu Sans\";\r\n  font-style: italic;\r\n  src: url(\"fonts/DejaVu/DejaVuSans-Oblique.ttf?v=1.1\") format(\"truetype\");\r\n}\r\n@font-face {\r\n  font-family: \"DejaVu Sans\";\r\n  font-weight: bold;\r\n  font-style: italic;\r\n  src: url(\"fonts/DejaVu/DejaVuSans-BoldOblique.ttf?v=1.1\") format(\"truetype\");\r\n}\r\n/* serif */\r\n@font-face {\r\n  font-family: \"DejaVu Serif\";\r\n  src: url(\"fonts/DejaVu/DejaVuSerif.ttf?v=1.1\") format(\"truetype\");\r\n}\r\n@font-face {\r\n  font-family: \"DejaVu Serif\";\r\n  font-weight: bold;\r\n  src: url(\"fonts/DejaVu/DejaVuSerif-Bold.ttf?v=1.1\") format(\"truetype\");\r\n}\r\n@font-face {\r\n  font-family: \"DejaVu Serif\";\r\n  font-style: italic;\r\n  src: url(\"fonts/DejaVu/DejaVuSerif-Italic.ttf?v=1.1\") format(\"truetype\");\r\n}\r\n@font-face {\r\n  font-family: \"DejaVu Serif\";\r\n  font-weight: bold;\r\n  font-style: italic;\r\n  src: url(\"fonts/DejaVu/DejaVuSerif-BoldItalic.ttf?v=1.1\") format(\"truetype\");\r\n}\r\n/* monospace */\r\n@font-face {\r\n  font-family: \"DejaVu Mono\";\r\n  src: url(\"fonts/DejaVu/DejaVuSansMono.ttf?v=1.1\") format(\"truetype\");\r\n}\r\n@font-face {\r\n  font-family: \"DejaVu Mono\";\r\n  font-weight: bold;\r\n  src: url(\"fonts/DejaVu/DejaVuSansMono-Bold.ttf?v=1.1\") format(\"truetype\");\r\n}\r\n@font-face {\r\n  font-family: \"DejaVu Mono\";\r\n  font-style: italic;\r\n  src: url(\"fonts/DejaVu/DejaVuSansMono-Oblique.ttf?v=1.1\") format(\"truetype\");\r\n}\r\n@font-face {\r\n  font-family: \"DejaVu Mono\";\r\n  font-weight: bold;\r\n  font-style: italic;\r\n  src: url(\"fonts/DejaVu/DejaVuSansMono-BoldOblique.ttf?v=1.1\") format(\"truetype\");\r\n}\r\n.kendo-pdf-hide-pseudo-elements:before,\r\n.kendo-pdf-hide-pseudo-elements:after {\r\n  display: none !important;\r\n}\r\n.k-button,\r\n.k-toolbar .k-button {\r\n  line-height: 1.143em;\r\n  padding: 9px 14px;\r\n}\r\n.k-widget.k-tabstrip {\r\n  background-image: none;\r\n  border-style: none;\r\n  -webkit-box-shadow: none;\r\n          box-shadow: none;\r\n}\r\n.k-tabstrip .k-tabstrip-items {\r\n  padding: 0;\r\n}\r\n.k-tabstrip > div.k-content,\r\n.k-panelbar .k-tabstrip > div.k-content {\r\n  margin: 0;\r\n}\r\n.k-panelbar > .k-item > .k-link,\r\n.k-panel > .k-item > .k-link {\r\n  line-height: 3.5em;\r\n}\r\n.k-panelbar .k-image {\r\n  margin-top: 12px;\r\n}\r\n.k-panelbar .k-link > .k-sprite {\r\n  margin-top: 16px;\r\n}\r\n.k-tabstrip > .k-tabstrip-items > .k-item {\r\n  text-transform: uppercase;\r\n  border-width: 0;\r\n  border-style: solid;\r\n  padding: 0;\r\n}\r\n.k-tabstrip-left > .k-tabstrip-items .k-loading,\r\n.k-tabstrip-right > .k-tabstrip-items .k-loading {\r\n  display: none;\r\n}\r\n.k-tabstrip-top > .k-tabstrip-items .k-tab-on-top,\r\n.k-tabstrip-top > .k-tabstrip-items .k-state-active {\r\n  margin-bottom: -1px;\r\n}\r\n.k-tabstrip-top > .k-tabstrip-items > .k-item {\r\n  border-bottom-width: 2px;\r\n}\r\n.k-tabstrip-left > .k-tabstrip-items .k-tab-on-top,\r\n.k-tabstrip-left > .k-tabstrip-items .k-state-active {\r\n  margin-right: -2px;\r\n}\r\n.k-tabstrip-left > .k-tabstrip-items > .k-item {\r\n  border-right-width: 2px;\r\n}\r\n.k-tabstrip-right > .k-tabstrip-items > .k-item {\r\n  border-left-width: 2px;\r\n}\r\n.k-tabstrip-bottom > .k-tabstrip-items > .k-item {\r\n  border-top-width: 2px;\r\n}\r\n.k-tabstrip .k-tabstrip-items .k-link {\r\n  padding: 1.071em;\r\n}\r\n.k-slider-track {\r\n  border-width: 1px;\r\n  border-style: solid;\r\n}\r\n.k-grouping-dropclue {\r\n  margin-top: 10px;\r\n}\r\n.k-grid-header th.k-header,\r\n.k-filter-row th {\r\n  padding: .786em .6em;\r\n}\r\n.k-grid-header th.k-header {\r\n  padding-left: 1.286em;\r\n}\r\n.k-filtercell > span {\r\n  padding-right: 5.714em;\r\n}\r\n.k-filtercell > span > .k-button {\r\n  padding-left: .714em;\r\n  padding-right: .714em;\r\n  line-height: 1.286em;\r\n  margin: 0;\r\n}\r\n.k-filter-row .k-dropdown-operator {\r\n  width: 2.714em;\r\n  right: 2.857em;\r\n}\r\n.k-filter-menu {\r\n  padding: .5em .5em 0;\r\n}\r\n.k-filter-menu .k-button {\r\n  margin: 0;\r\n  width: 50%;\r\n  border-radius: 0;\r\n}\r\n.k-filter-menu .k-primary {\r\n  float: right;\r\n}\r\n.k-filter-menu > div > div:last-child {\r\n  margin: 0.5em -0.5em 0;\r\n  border-top-width: 1px;\r\n  border-top-style: solid;\r\n}\r\n.k-filter-menu .k-primary {\r\n  border-left-width: 1px;\r\n  border-left-style: solid;\r\n}\r\n.k-grouping-header .k-group-indicator {\r\n  padding: .5em .15em .429em .4em;\r\n}\r\n.k-grid .k-grouping-row td {\r\n  padding: .6em .6em 0.643em;\r\n}\r\n.k-grouping-header a,\r\n.k-grouping-header .k-button {\r\n  vertical-align: baseline;\r\n}\r\n.k-grid td {\r\n  padding: 0.929em 1.286em;\r\n}\r\n.k-grid-header th.k-header > .k-link {\r\n  padding: 0;\r\n  margin: 0;\r\n  min-height: 16px;\r\n  line-height: inherit;\r\n}\r\n.k-grouping-header {\r\n  line-height: 2.6;\r\n}\r\n.k-grid-content tr:last-child > td {\r\n  border-bottom-width: 0;\r\n}\r\n.k-grid tr td,\r\n.k-pivot-layout .k-grid tr td {\r\n  border-bottom-width: 1px;\r\n  border-style: solid;\r\n}\r\n.k-grouping-row + tr td {\r\n  border-top-width: 0;\r\n}\r\n.k-grid-content table tr:first-child td,\r\n.k-grid-content-locked table tr:first-child td {\r\n  border-top: 0;\r\n}\r\n.k-grid-content tr td {\r\n  border-left-width: 0;\r\n}\r\n.k-pager-numbers .k-state-selected {\r\n  line-height: 2.429em;\r\n  cursor: pointer;\r\n}\r\n.k-pager-numbers .k-link,\r\n.k-pager-numbers .k-state-selected {\r\n  border-width: 2px 0 0;\r\n  padding-top: .7em;\r\n}\r\n.k-pager-wrap {\r\n  line-height: 1.286em;\r\n  padding: .429em 0 .429em .25em;\r\n}\r\n.k-pager-wrap > .k-link,\r\n.k-pager-numbers .k-link {\r\n  height: 2.429em;\r\n  line-height: 2.429em;\r\n}\r\n.k-pager-wrap .k-link,\r\n.k-pager-sizes {\r\n  padding: 0;\r\n  min-width: 2.429em;\r\n}\r\n.k-pager-wrap .k-pager-numbers .k-state-selected {\r\n  margin-top: -0.45em;\r\n  padding-top: .45em;\r\n  min-width: 2.429em;\r\n}\r\n.k-pager-wrap input.k-textbox {\r\n  height: 2.571em;\r\n}\r\n.k-pager-info {\r\n  padding: .714em 1.333em .643em 1.333em;\r\n}\r\n.k-pager-wrap .k-pager-refresh {\r\n  margin-right: 1.214em;\r\n}\r\n.k-pager-wrap .k-dropdown {\r\n  width: 5.2em;\r\n}\r\n.k-autocomplete .k-loading,\r\n.k-multiselect .k-loading {\r\n  bottom: 10px;\r\n}\r\n.k-dropdown-wrap,\r\n.k-picker-wrap,\r\n.k-numeric-wrap {\r\n  padding: 0 2.571em 0 0;\r\n}\r\n.k-picker-wrap .k-select,\r\n.k-numeric-wrap .k-select,\r\n.k-dropdown-wrap .k-select {\r\n  width: 2.571em;\r\n}\r\n.k-datetimepicker {\r\n  width: 19.5em;\r\n}\r\n.k-datetimepicker .k-select {\r\n  width: 5em;\r\n}\r\n.k-datetimepicker .k-picker-wrap {\r\n  padding-right: 5em;\r\n}\r\n.k-datetimepicker .k-picker-wrap .k-icon {\r\n  margin: 0 6px;\r\n}\r\n.k-calendar {\r\n  width: 19em;\r\n}\r\n.k-calendar .k-header {\r\n  margin: 0 -5px;\r\n}\r\n.k-calendar td {\r\n  padding: 0;\r\n}\r\n.k-calendar .k-content .k-link {\r\n  min-height: 2.571em;\r\n  line-height: 2.571em;\r\n  padding: 0;\r\n  text-align: center;\r\n}\r\n.k-calendar th {\r\n  border-bottom-width: 0;\r\n  padding: .714em .45em .714em 0;\r\n}\r\n.k-calendar .k-footer {\r\n  border-top-width: 1px;\r\n  border-top-style: solid;\r\n  margin: 0 -5px;\r\n}\r\n.k-popup.k-list-container {\r\n  padding: 0.286em 0;\r\n}\r\n.k-popup.k-calendar-containe {\r\n  padding: 0;\r\n}\r\n.k-popup .k-list .k-item,\r\n.k-fieldselector .k-list .k-item {\r\n  min-height: 2.143em;\r\n  line-height: 2.143em;\r\n}\r\n.k-fieldselector .k-pivot-configurator-settings li.k-item {\r\n  padding-right: 3.8em;\r\n}\r\n.k-popup .k-list .k-item {\r\n  padding: 1px 11px;\r\n}\r\n.k-button,\r\n.k-calendar .k-header .k-link,\r\n.k-calendar .k-footer {\r\n  text-transform: uppercase;\r\n}\r\n.k-widget.k-calendar .k-nav-fast {\r\n  width: 65%;\r\n  height: 2.571em;\r\n  line-height: 2.571em;\r\n  margin: 0.429em -0.08333em 0.429em 0;\r\n}\r\n.k-calendar .k-header .k-icon {\r\n  vertical-align: middle;\r\n}\r\n.k-widget.k-calendar .k-nav-prev,\r\n.k-widget.k-calendar .k-nav-next {\r\n  position: absolute;\r\n  top: 0.429em;\r\n  line-height: 2.571em;\r\n  height: 2.571em;\r\n}\r\n.k-calendar .k-header .k-link.k-nav-prev,\r\n.k-calendar .k-header .k-link.k-nav-next {\r\n  height: 2.571em;\r\n  width: 2.571em;\r\n}\r\n.k-widget.k-calendar .k-nav-prev {\r\n  left: 0.429em;\r\n}\r\n.k-widget.k-calendar .k-nav-next {\r\n  right: 0.429em;\r\n}\r\n.k-calendar .k-footer .k-nav-today,\r\n.k-calendar .k-footer > .k-state-disabled {\r\n  padding: 1.143em 0 1.071em;\r\n}\r\n.k-popup.k-calendar-container {\r\n  padding: 0;\r\n  border: 0;\r\n}\r\n.k-multiselect-wrap .k-input {\r\n  height: 2.214em;\r\n}\r\n.k-multiselect-wrap li {\r\n  border-radius: 1.071em;\r\n  margin: 3px 0 3px 3px;\r\n  padding: 0 1.6em 0 .857em;\r\n  line-height: 1.86em;\r\n}\r\n.k-multiselect-wrap li span {\r\n  margin-right: .4em;\r\n}\r\n.k-multiselect-wrap .k-select {\r\n  padding-top: 0;\r\n}\r\n.k-numeric-wrap .k-select {\r\n  vertical-align: baseline;\r\n}\r\n.k-numerictextbox .k-link {\r\n  height: 1.284em;\r\n  line-height: 1.357em;\r\n}\r\n.k-numerictextbox .k-link .k-i-arrow-n {\r\n  vertical-align: bottom;\r\n}\r\n.k-numerictextbox .k-link .k-i-arrow-s {\r\n  vertical-align: top;\r\n}\r\n.k-menu.k-header,\r\n.k-menu .k-item,\r\n.k-widget.k-menu-horizontal > .k-item {\r\n  border-width: 0;\r\n}\r\n.k-popup.k-context-menu {\r\n  border-width: 1px;\r\n  border-style: solid;\r\n}\r\n.k-context-menu.k-menu-vertical > .k-item > .k-link,\r\n.k-menu .k-menu-group .k-item > .k-link {\r\n  padding: 0.5em 4.929em 0.5em 1.714em;\r\n}\r\n.k-menu .k-item > .k-link > .k-i-arrow-e {\r\n  right: 2.143rem;\r\n}\r\n.k-menu .k-animation-container .k-menu-group {\r\n  padding: 1.143em 0;\r\n}\r\n.k-column-menu .k-menu .k-animation-container .k-menu-group {\r\n  padding: 0;\r\n}\r\n.k-column-menu .k-menu-vertical .k-separator {\r\n  height: 0;\r\n}\r\ntable.k-editor {\r\n  border-spacing: 0;\r\n}\r\n.k-editor .k-editable-area {\r\n  border-width: 1px 0 0;\r\n}\r\n.k-editor .k-editor-toolbar-wrap {\r\n  padding-left: 10px;\r\n}\r\n.k-toolbar {\r\n  line-height: 3.42em;\r\n}\r\n.k-toolbar > div > label {\r\n  line-height: 3.42em;\r\n}\r\n.k-toolbar .k-overflow-anchor > .k-icon {\r\n  margin-top: -6px;\r\n  margin-bottom: -6px;\r\n}\r\n.k-toolbar .k-dropdown {\r\n  margin-top: -1px;\r\n}\r\n.k-toolbar .k-split-button .k-button {\r\n  padding-top: 10px;\r\n  padding-bottom: 10px;\r\n}\r\n.k-toolbar .k-button-group {\r\n  line-height: 3.286em;\r\n}\r\n.k-toolbar .k-button-group .k-button {\r\n  line-height: inherit;\r\n  padding: 0 .857em;\r\n}\r\n.k-toolbar .k-input {\r\n  height: 2.214em;\r\n  line-height: 2.214em;\r\n  padding: 0.177em 0;\r\n  text-indent: 0.8em;\r\n  border: 0;\r\n  margin: 0;\r\n}\r\n.k-toolbar .k-overflow-anchor {\r\n  border-width: 0;\r\n  line-height: 3.286em;\r\n  width: 3.42em;\r\n  height: 3.42em;\r\n  padding: 0;\r\n}\r\n.k-overflow-container .k-overflow-button,\r\n.k-split-container .k-button {\r\n  font-size: 1.2em;\r\n}\r\n.k-button-group .k-tool {\r\n  margin: 6px 0;\r\n}\r\n.k-toolbar .k-split-button-arrow {\r\n  padding-left: .4em;\r\n  padding-right: .4em;\r\n}\r\n.k-editor-toolbar .k-button-group .k-tool-icon,\r\n.k-toolbar .k-button-group .k-tool-icon {\r\n  width: 24px;\r\n  height: 24px;\r\n}\r\n.k-editor-toolbar li.k-tool-group,\r\n.k-editor-toolbar li.k-overflow-tools {\r\n  padding: 0;\r\n}\r\n.k-editor-toolbar .k-button-group .k-tool {\r\n  width: 48px;\r\n  height: 48px;\r\n  line-height: 48px;\r\n  margin: 0;\r\n}\r\n.k-editor-toolbar .k-tool,\r\n.k-editor-toolbar .k-button-group .k-tool.k-group-end {\r\n  border-width: 0;\r\n}\r\n.k-editor-toolbar .k-state-selected {\r\n  font-weight: bold;\r\n}\r\n.k-filebrowser .k-filebrowser-toolbar {\r\n  line-height: 3.5em;\r\n}\r\n.k-filebrowser .k-filebrowser-toolbar .k-upload {\r\n  margin-top: 3px;\r\n}\r\n.k-filebrowser .k-filebrowser-toolbar .k-button-icon {\r\n  margin-top: -3px;\r\n}\r\n.k-filebrowser .k-upload .k-upload-button {\r\n  vertical-align: bottom;\r\n}\r\n.k-filebrowser .k-search-wrap .k-search {\r\n  top: 25%;\r\n  right: 4px;\r\n}\r\n.k-filebrowser .k-search-wrap {\r\n  padding: 2px .3em;\r\n}\r\n.k-filebrowser .k-tiles-arrange .k-dropdown {\r\n  width: 80px;\r\n}\r\n.k-draghandle {\r\n  border-width: 2px;\r\n}\r\nspan.k-tooltip {\r\n  padding: 9px 17px;\r\n}\r\n.k-block > .k-header,\r\n.k-window-titlebar {\r\n  padding: 0.5em 0 0.571em;\r\n}\r\n.k-window-titlebar .k-window-actions {\r\n  right: 0.929em;\r\n}\r\ndiv.k-window {\r\n  border-width: 0;\r\n}\r\n.k-window-title {\r\n  right: 1.143em;\r\n  left: 1.143em;\r\n}\r\ndiv.k-window-content {\r\n  padding: 1.333em;\r\n}\r\ndiv.editorToolbarWindow.k-window-content {\r\n  padding: 0;\r\n}\r\n.editorToolbarWindow .k-editortoolbar-dragHandle {\r\n  line-height: 30px;\r\n}\r\n.k-file {\r\n  padding: 0.357em .167em 0.357em .8em;\r\n}\r\n.k-scheduler-table td,\r\n.k-scheduler-table th {\r\n  height: 1.643em;\r\n}\r\n.k-gantt-treelist .k-grid-header tr {\r\n  height: 5.714em;\r\n}\r\n.k-gantt-timeline .k-grid-header tr {\r\n  height: 2.857em;\r\n}\r\n.k-gantt .k-task-summary:before,\r\n.k-gantt .k-task-summary-complete:before,\r\n.k-gantt .k-task-summary:after,\r\n.k-gantt .k-task-summary-complete:after {\r\n  border-width: 0;\r\n}\r\n.k-gantt .k-task {\r\n  border-width: 0;\r\n}\r\n.k-gantt .k-task-actions {\r\n  line-height: 2.429em;\r\n}\r\n.k-gantt .k-task-single {\r\n  margin-top: -0.429em;\r\n}\r\n.k-gantt .k-task-template {\r\n  padding: .643em 1.4em .643em .6em;\r\n}\r\n.k-gantt .k-task-milestone {\r\n  width: 17px;\r\n  height: 17px;\r\n}\r\n.k-gantt .k-task-draghandle {\r\n  margin-left: 24px;\r\n  border-width: 8px;\r\n  border-radius: 0 50% 50% 50%;\r\n  border-style: solid;\r\n  bottom: -11px;\r\n}\r\n.k-gantt .k-gantt-timeline th {\r\n  text-align: left;\r\n}\r\n.k-notification-wrap {\r\n  padding: 1.786em;\r\n}\r\n.k-notification-wrap > .k-i-close {\r\n  top: 21px;\r\n}\r\n.k-slider-track {\r\n  border-width: 0;\r\n}\r\n.k-slider .k-button .k-icon {\r\n  margin-top: 5px;\r\n}\r\n.k-slider-horizontal .k-slider-track,\r\n.k-slider-horizontal .k-slider-selection {\r\n  height: 2px;\r\n  margin-top: -1px;\r\n}\r\n.k-slider-vertical .k-slider-track,\r\n.k-slider-vertical .k-slider-selection {\r\n  width: 2px;\r\n  margin-left: -1px;\r\n}\r\n.k-slider-horizontal .k-draghandle,\r\n.k-slider-vertical .k-draghandle,\r\n.k-flatcolorpicker .k-hue-slider .k-draghandle,\r\n.k-flatcolorpicker .k-transparency-slider .k-draghandle {\r\n  width: 6px;\r\n  height: 6px;\r\n}\r\n.k-flatcolorpicker .k-hue-slider .k-draghandle {\r\n  margin-top: -1px;\r\n}\r\n.k-colorpicker .k-picker-wrap {\r\n  line-height: 2.214em;\r\n}\r\n.k-colorpicker .k-selected-color {\r\n  height: 2.214em;\r\n  padding: 0.177em 0;\r\n}\r\n.k-draghandle.k-state-selected,\r\n.k-draghandle.k-state-selected:link,\r\n.k-flatcolorpicker .k-hue-slider .k-draghandle.k-state-selected,\r\n.k-flatcolorpicker .k-transparency-slider .k-draghandle.k-state-selected {\r\n  width: 10px;\r\n  height: 10px;\r\n}\r\n.k-draghandle.k-state-focused.k-state-selected {\r\n  margin-left: -2px;\r\n}\r\n.k-slider-horizontal .k-draghandle.k-state-selected,\r\n.k-flatcolorpicker .k-hue-slider .k-draghandle.k-state-selected {\r\n  top: -6px;\r\n}\r\n.k-slider-vertical .k-draghandle.k-state-selected {\r\n  left: -4px;\r\n}\r\n/* Editor */\r\n/* PanelBar */\r\n.k-panelbar-expand,\r\n.k-panelbar-collapse {\r\n  right: 16px;\r\n}\r\n/* Grid */\r\n.k-header.k-grid-toolbar {\r\n  border-color: #3343a4;\r\n}\r\n.k-grouping-header,\r\n.k-grid-toolbar {\r\n  padding: 0.429em;\r\n}\r\n.k-grouping-header {\r\n  padding-left: 1.286em;\r\n}\r\n.k-grid .k-icon {\r\n  opacity: 0.45;\r\n}\r\n.k-grid th.k-group-cell,\r\n.k-grid td.k-group-cell {\r\n  text-overflow: clip;\r\n}\r\nform.k-filter-menu .k-textbox {\r\n  margin-bottom: 7px;\r\n}\r\n/* Scheduler */\r\n.k-gantt-toolbar,\r\n.k-scheduler-toolbar .k-nav-today,\r\n.k-scheduler-toolbar .k-scheduler-views,\r\n.k-scheduler-footer .k-scheduler-fullday {\r\n  text-transform: uppercase;\r\n}\r\n.k-scheduler-footer .k-icon.k-i-clock {\r\n  display: none;\r\n}\r\n.k-gantt-toolbar > ul > li,\r\n.k-scheduler-toolbar > ul > li {\r\n  border-width: 0 0 2px 0;\r\n}\r\n.k-gantt-toolbar li .k-link,\r\n.k-scheduler-toolbar li .k-link,\r\n.k-gantt-toggle {\r\n  vertical-align: middle;\r\n  line-height: 46px;\r\n}\r\n.k-gantt-toggle {\r\n  padding: 0;\r\n}\r\n.k-gantt-actions {\r\n  vertical-align: middle;\r\n  line-height: 48px;\r\n}\r\n.k-gantt-actions > .k-button {\r\n  vertical-align: middle;\r\n}\r\n.k-gantt-toolbar,\r\n.k-scheduler-toolbar,\r\n.k-scheduler-footer {\r\n  line-height: 44px;\r\n  vertical-align: middle;\r\n}\r\n.k-scheduler-footer {\r\n  padding: 0;\r\n}\r\n.k-popup-edit-form .k-primary {\r\n  float: right;\r\n}\r\n.k-popup-edit-form:after {\r\n  content: \" \";\r\n  display: block;\r\n  clear: both;\r\n}\r\n.k-gantt-toolbar,\r\n.k-scheduler-toolbar {\r\n  padding: 0 1.286em;\r\n}\r\n.k-scheduler-navigation {\r\n  margin-left: -0.786em;\r\n}\r\n.k-drag-clue {\r\n  font-size: 1em;\r\n  padding: .65em 1em;\r\n}\r\n.k-state-border-down .k-select .k-i-arrow-s {\r\n  background-position: 0 0;\r\n}\r\n.k-numerictextbox .k-select .k-link span.k-i-arrow-n,\r\n.k-grid-header .k-numerictextbox .k-select .k-link span.k-i-arrow-n {\r\n  background-position: 0 -3px;\r\n}\r\n.k-numerictextbox .k-select .k-link span.k-i-arrow-s,\r\n.k-grid-header .k-numerictextbox .k-select .k-link span.k-i-arrow-s {\r\n  background-position: 0 -35px;\r\n}\r\n.k-i-close,\r\n.k-delete,\r\n.k-group-delete {\r\n  background-position: -32px -16px;\r\n}\r\n.k-multiselect .k-delete {\r\n  margin-top: -2px;\r\n}\r\n.k-multiselect .k-button:not(.k-state-disabled):hover .k-delete {\r\n  background-position: -160px -80px;\r\n}\r\n.k-window-titlebar .k-i-maximize,\r\n.k-window-titlebar .k-link:not(.k-state-disabled):hover > .k-i-maximize {\r\n  background-position: -48px -144px;\r\n}\r\n.k-window-titlebar .k-i-minimize,\r\n.k-window-titlebar .k-link:not(.k-state-disabled):hover > .k-i-minimize {\r\n  background-position: -80px -288px;\r\n}\r\n.k-window-titlebar .k-i-pin,\r\n.k-window-titlebar .k-link:not(.k-state-disabled):hover > .k-i-pin {\r\n  background-position: -176px -256px;\r\n}\r\n.k-window-titlebar .k-i-custom,\r\n.k-window-titlebar .k-link:not(.k-state-disabled):hover > .k-i-custom {\r\n  background-position: -141px -113px;\r\n}\r\n.k-window-titlebar .k-i-refresh,\r\n.k-window-titlebar .k-link:not(.k-state-disabled):hover > .k-i-refresh {\r\n  background-position: -48px -112px;\r\n}\r\n.k-window-titlebar .k-i-close,\r\n.k-window-titlebar .k-link:not(.k-state-disabled):hover > .k-i-close {\r\n  background-position: -48px -16px;\r\n}\r\n.k-window-titlebar .k-i-restore,\r\n.k-window-titlebar .k-link:not(.k-state-disabled):hover > .k-i-restore {\r\n  background-position: -48px -128px;\r\n}\r\n.k-calendar .k-icon.k-i-arrow-w,\r\n.k-calendar .k-state-hover .k-icon.k-i-arrow-w,\r\n.k-calendar .k-link:not(.k-state-disabled):hover > .k-i-arrow-w {\r\n  background-position: -16px -48px;\r\n  opacity: 1;\r\n}\r\n.k-calendar .k-icon.k-i-arrow-e,\r\n.k-calendar .k-state-hover .k-icon.k-i-arrow-e,\r\n.k-calendar .k-link:not(.k-state-disabled):hover > .k-i-arrow-e {\r\n  background-position: -16px -16px;\r\n  opacity: 1;\r\n}\r\n.k-treeview .k-minus {\r\n  background-position: 0 -32px;\r\n}\r\n.k-treeview .k-plus {\r\n  background-position: 0 -16px;\r\n}\r\n.k-treeview .k-loading {\r\n  background-position: 50%;\r\n}\r\n.k-splitbar-horizontal-hover .k-resize-handle {\r\n  background-position: -165px -6px;\r\n}\r\n.k-splitbar-horizontal-hover .k-collapse-next {\r\n  background-position: -5px -142px;\r\n}\r\n.k-splitbar-horizontal-hover .k-collapse-prev {\r\n  background-position: -6px -174px;\r\n}\r\n.k-splitbar-vertical-hover .k-resize-handle {\r\n  background-position: -38px -309px;\r\n}\r\n.k-splitbar-vertical-hover .k-collapse-next {\r\n  background-position: 2px -165px;\r\n}\r\n.k-splitbar-vertical-hover .k-collapse-prev {\r\n  background-position: 2px -134px;\r\n}\r\n.k-splitbar-horizontal.k-state-focused .k-resize-handle {\r\n  background-position: -181px -6px;\r\n}\r\n.k-splitbar-horizontal.k-state-focused .k-collapse-next {\r\n  background-position: -21px -142px;\r\n}\r\n.k-splitbar-horizontal.k-state-focused .k-collapse-prev {\r\n  background-position: -22px -174px;\r\n}\r\n.k-splitbar-vertical.k-state-focused .k-resize-handle {\r\n  background-position: -70px -309px;\r\n}\r\n.k-splitbar-vertical.k-state-focused .k-collapse-next {\r\n  background-position: -14px -165px;\r\n}\r\n.k-splitbar-vertical.k-state-focused .k-collapse-prev {\r\n  background-position: -14px -134px;\r\n}\r\n.k-grid .k-delete {\r\n  background-position: -32px -16px;\r\n}\r\n.k-grid-header .k-i-arrow-n,\r\n.k-grid-header .k-link:not(.k-state-disabled):hover > .k-i-arrow-n {\r\n  background-position: 0 -256px;\r\n}\r\n.k-grid-header .k-i-arrow-s,\r\n.k-grid-header .k-link:not(.k-state-disabled):hover > .k-i-arrow-s {\r\n  background-position: 0 -288px;\r\n}\r\n.k-grid-header .k-grid-filter,\r\n.k-grid-header .k-header-column-menu {\r\n  padding: .714em;\r\n  margin: -0.786em -0.6em;\r\n}\r\n.k-grid-header .k-header-column-menu .k-i-arrowhead-s {\r\n  background-position: -64px -32px;\r\n}\r\n.k-header .k-i-pdf,\r\n.k-header .k-button:hover > .k-i-pdf {\r\n  background-position: -80px -80px;\r\n}\r\n.k-header .k-i-excel,\r\n.k-header .k-button:hover > .k-i-excel {\r\n  background-position: -80px -96px;\r\n}\r\n.k-grid-toolbar .k-add,\r\n.k-grid-toolbar .k-button:hover .k-add {\r\n  background-position: -48px -64px;\r\n}\r\n.k-grid-toolbar .k-update,\r\n.k-grid-toolbar .k-button:hover .k-update {\r\n  background-position: -48px -32px;\r\n}\r\n.k-grid-toolbar .k-cancel,\r\n.k-grid-toolbar .k-button:hover .k-cancel {\r\n  background-position: -48px -48px;\r\n}\r\n.k-grouping-header .k-group-delete,\r\n.k-grouping-header .k-button-icon:hover > .k-icon.k-group-delete {\r\n  background-position: -176px -80px;\r\n}\r\n.k-grouping-header .k-si-arrow-n,\r\n.k-grouping-header .k-link:hover > .k-icon.k-si-arrow-n {\r\n  background-position: -16px -288px;\r\n}\r\n.k-grouping-header .k-si-arrow-s,\r\n.k-grouping-header .k-link:hover > .k-icon.k-si-arrow-s {\r\n  background-position: -16px -256px;\r\n}\r\n.k-scheduler .k-state-default .k-link .k-icon.k-i-calendar,\r\n.k-scheduler .k-state-default.k-state-hover .k-link .k-i-calendar {\r\n  background-position: -48px -176px;\r\n}\r\n.k-scheduler .k-i-arrow-e,\r\n.k-scheduler .k-state-hover .k-link .k-i-arrow-e {\r\n  background-position: -16px -16px;\r\n}\r\n.k-scheduler .k-i-arrow-w,\r\n.k-scheduler .k-state-hover .k-link .k-i-arrow-w {\r\n  background-position: -16px -48px;\r\n}\r\n.k-scheduler .k-i-clock,\r\n.k-scheduler .k-state-hover .k-link .k-i-clock {\r\n  opacity: 1;\r\n  background-position: -48px -192px;\r\n}\r\n.k-scheduler .k-si-close,\r\n.k-scheduler .k-link:not(.k-state-disabled):hover > .k-si-close {\r\n  background-position: -176px -80px;\r\n}\r\n.k-scheduler .k-i-refresh {\r\n  background-position: -48px -112px;\r\n}\r\n.k-gantt .k-i-plus,\r\n.k-gantt .k-link:hover > .k-icon.k-si-arrow-n,\r\n.k-gantt .k-button:hover .k-i-plus {\r\n  background-position: -48px -64px;\r\n}\r\n.k-gantt .k-i-collapse {\r\n  background-position: 0 -32px;\r\n}\r\n.k-gantt .k-i-expand {\r\n  background-position: 0 0;\r\n}\r\n.k-gantt .k-i-gantt-toggle,\r\n.k-gantt .k-button:not(.k-state-disabled):hover .k-i-gantt-toggle {\r\n  background-position: -80px -240px;\r\n}\r\n.k-i-arrowhead-s,\r\n.k-pivot-setting .k-item.k-state-hover .k-i-arrowhead-s {\r\n  background-position: 0 -288px;\r\n}\r\n.k-pivot-toolbar .k-field-actions .k-i-sort-asc,\r\n.k-pivot-toolbar .k-button:not(.k-state-disabled):hover .k-i-sort-asc {\r\n  background-position: -128px -240px;\r\n}\r\n.k-pivot-toolbar .k-field-actions .k-i-sort-desc,\r\n.k-pivot-toolbar .k-button:not(.k-state-disabled):hover .k-i-sort-desc {\r\n  background-position: -128px -256px;\r\n}\r\n.k-item.k-state-hover .k-i-arrowhead-s,\r\n.k-button:hover .k-i-arrowhead-s {\r\n  background-position: -16px -288px;\r\n}\r\n.k-si-close,\r\n.k-item.k-state-hover .k-si-close,\r\n.k-button:hover .k-si-close {\r\n  background-position: -176px -80px;\r\n}\r\n.k-panelbar .k-link.k-state-selected .k-i-arrow-n,\r\n.k-pivot .k-link.k-state-selected .k-i-arrow-n,\r\n.k-panelbar .k-link:not(.k-state-disabled):hover > .k-i-arrow-n,\r\n.k-pivot .k-link:not(.k-state-disabled):hover > .k-i-arrow-n {\r\n  background-position: 0 -192px;\r\n}\r\n.k-panelbar .k-link.k-state-selected .k-i-arrow-s,\r\n.k-pivot .k-link.k-state-selected .k-i-arrow-s,\r\n.k-panelbar .k-link:not(.k-state-disabled):hover > .k-i-arrow-s,\r\n.k-pivot .k-link:not(.k-state-disabled):hover > .k-i-arrow-s {\r\n  background-position: 0 -224px;\r\n}\r\n.k-panelbar .k-link.k-state-selected .k-i-arrow-e,\r\n.k-pivot .k-link.k-state-selected .k-i-arrow-e,\r\n.k-panelbar .k-link:not(.k-state-disabled):hover > .k-i-arrow-e,\r\n.k-pivot .k-link:not(.k-state-disabled):hover > .k-i-arrow-e {\r\n  background-position: 0 -190px;\r\n}\r\n.k-pivot .k-i-arrow-n {\r\n  background-position: 0 -192px;\r\n}\r\n.k-pivot .k-i-arrow-s {\r\n  background-position: 0 -224px;\r\n}\r\n.k-pivot .k-i-arrow-e {\r\n  background-position: 0 -192px;\r\n}\r\n.k-panelbar .k-link > .k-i-arrow-n {\r\n  background-position: 0 -192px;\r\n}\r\n.k-panelbar .k-link > .k-i-arrow-s {\r\n  background-position: 0 -224px;\r\n}\r\n.k-panelbar .k-link > .k-i-arrow-e {\r\n  background-position: 0 -190px;\r\n}\r\n.k-tabstrip .k-button .k-i-pdf,\r\n.k-tabstrip .k-button:hover .k-i-pdf {\r\n  background-position: -64px -80px;\r\n}\r\n.k-pivot-configurator-settings .k-si-close,\r\n.k-pivot-configurator-settings .k-item.k-state-hover .k-si-close,\r\n.k-pivot-configurator-settings .k-button:hover .k-si-close {\r\n  background-position: -160px -80px;\r\n}\r\n.k-pivot .k-header .k-i-arrowhead-s,\r\n.k-pivot .k-button:not(.k-state-disabled):hover .k-i-arrowhead-s {\r\n  background-position: -16px -288px;\r\n}\r\n.k-pivot .k-header .k-si-close,\r\n.k-pivot .k-button:not(.k-state-disabled):hover .k-si-close {\r\n  background-position: -176px -80px;\r\n}\r\n.k-notification .k-i-note {\r\n  background-position: -176px -240px;\r\n}\r\n.k-checkbox:indeterminate + .k-checkbox-label:before {\r\n  border-width: 2px;\r\n}\r\n.k-checkbox + .k-checkbox-label:before {\r\n  z-index: 1;\r\n}\r\n.k-checkbox:indeterminate + .k-checkbox-label:after {\r\n  width: 10px;\r\n  height: 10px;\r\n  border-width: 0;\r\n  margin-left: 0;\r\n  top: 4px;\r\n  left: 4px;\r\n  -webkit-transform: none;\r\n      -ms-transform: none;\r\n          transform: none;\r\n  z-index: 2;\r\n}\r\n.k-radio:checked + .k-radio-label:after {\r\n  top: 4px;\r\n  left: 4px;\r\n}\r\n.k-rtl .k-widget .k-dropdown-wrap,\r\n.k-rtl .k-widget .k-picker-wrap,\r\n.k-rtl .k-widget .k-numeric-wrap {\r\n  padding-left: 2.5em;\r\n}\r\n.k-rtl .k-widget.k-datetimepicker .k-picker-wrap {\r\n  padding-left: 5em;\r\n}\r\n.k-rtl .k-widget.k-datetimepicker .k-picker-wrap .k-icon {\r\n  margin: 0 6px;\r\n}\r\n.k-rtl .k-calendar .k-icon.k-i-arrow-w,\r\n.k-rtl .k-calendar .k-link:not(.k-state-disabled):hover > .k-i-arrow-w,\r\n.k-rtl .k-scheduler .k-i-arrow-w,\r\n.k-rtl .k-scheduler .k-link:not(.k-state-disabled):hover > .k-i-arrow-w,\r\n.k-rtl .k-scheduler .k-state-hover .k-link .k-i-arrow-w {\r\n  background-position: -16px -16px;\r\n}\r\n.k-rtl .k-calendar .k-icon.k-i-arrow-e,\r\n.k-rtl .k-calendar .k-link:not(.k-state-disabled):hover > .k-i-arrow-e,\r\n.k-rtl .k-scheduler .k-i-arrow-e,\r\n.k-rtl .k-scheduler .k-link:not(.k-state-disabled):hover > .k-i-arrow-e,\r\n.k-rtl .k-scheduler .k-state-hover .k-link .k-i-arrow-e {\r\n  background-position: -16px -48px;\r\n}\r\n.km-pane-wrapper .k-pager-numbers .k-link,\r\n.km-pane-wrapper .k-pager-numbers .k-state-selected,\r\n.km-pane-wrapper .k-pager-wrap > .k-link {\r\n  border-radius: 0;\r\n}\r\n.km-pane-wrapper .k-pager-numbers .k-link,\r\n.km-pane-wrapper .k-pager-wrap > .k-link,\r\n.km-pane-wrapper .k-pager-wrap > .k-pager-info {\r\n  padding: .571em .86em;\r\n}\r\n.km-pane-wrapper .k-pager-numbers .k-state-selected {\r\n  padding: .971em .86em .571em;\r\n}\r\n.km-pane-wrapper .k-pager-numbers .k-link {\r\n  min-width: 2.429em;\r\n}\r\n.km-pane-wrapper .k-mobile-list .k-edit-field.k-scheduler-toolbar {\r\n  border-bottom-width: 1px;\r\n  border-bottom-style: solid;\r\n  padding-bottom: 0;\r\n}\r\n.km-pane-wrapper .k-mobile-list .k-edit-field.k-scheduler-toolbar .k-scheduler-navigation {\r\n  margin-bottom: -0.6em;\r\n}\r\n"]}