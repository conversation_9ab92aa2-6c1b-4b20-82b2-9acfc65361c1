
2a79bbd8eb5a1cb1dc4a4f098f5a834a73f7cb47	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.25.1754018536329.js\",\"contentHash\":\"1e0e8f9ce5e71f2fbea59c9dac14a63c\"}","integrity":"sha512-HuErM//ySLqk5UJ5s37PigYeMxmlIl6kyNzUTxdC3+1ObTQ8m1VN+txCsVQWMiZmLIlvLmrWEuZgPvnv6rBRyA==","time":1754018575955,"size":49648}