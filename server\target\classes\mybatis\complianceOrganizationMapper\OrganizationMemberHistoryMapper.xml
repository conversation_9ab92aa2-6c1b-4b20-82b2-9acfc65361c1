<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.klaw.dao.complianceOrganizationDao.OrganizationMemberHistoryMapper">
    <select id="queryHistory"
            resultType="com.klaw.entity.complianceOrganizationBean.OrganizationMemberHistory">
        SELECT
            id, member_code, member_name, organization, position, graduation_school, professional_qualification, job_status, create_ogn_id, create_ogn_name, create_dept_id, create_dept_name, create_group_id, create_group_name, create_psn_id, create_psn_name, create_org_id, create_org_name, create_psn_full_id, create_psn_full_name, create_time, update_time, data_state, data_state_code, role_id, enable_time, disable_time, description, role_code, member_gender, birth_date, attend_worker_date, plate, profession_now, technical_qualification, phone, start_org_id, stop_org_id, organization_name
        FROM
            organization_member
        <where>
            <!-- 成员名称模糊查询 -->
            <if test="memberName != null and memberName != ''">
                AND member_name LIKE CONCAT('%', #{memberName}, '%')
            </if>
            <!-- 成员编码模糊查询 -->
            <if test="memberCode != null and memberCode != ''">
                AND member_code LIKE CONCAT('%', #{memberCode}, '%')
            </if>
            <!-- 数据状态查询 -->
            <if test="dataState != null and dataState != ''">
                AND data_state = #{dataState}
            </if>
            <!-- 是否台账功能 -->
            <choose>
                <when test="isQuery">
                    <!-- 台账功能：添加数据权限过滤 -->
                    AND (
                    create_psn_full_id LIKE CONCAT('%', #{orgId}, '%')
                    <if test="roleIds != null and roleIds.size() > 0">
                        OR (
                        <foreach collection="roleIds" item="roleId" separator=" OR ">
                            create_psn_full_id LIKE CONCAT(
                            '%',
                            (SELECT DISTINCT unit_id
                            FROM sys_role_function_unit
                            WHERE function_id = #{functionId}
                            AND role_id = #{roleId}),
                            '%'
                            )
                        </foreach>
                        )
                    </if>
                    )
                </when>
                <otherwise>
                    <!-- 非台账功能：只查询当前组织 -->
                    AND create_org_id = #{orgId}
                </otherwise>
            </choose>
        </where>
        UNION ALL
        SELECT
            id, member_code, member_name, organization, position, graduation_school, professional_qualification, job_status, create_ogn_id, create_ogn_name, create_dept_id, create_dept_name, create_group_id, create_group_name, create_psn_id, create_psn_name, create_org_id, create_org_name, create_psn_full_id, create_psn_full_name, create_time, update_time, data_state, data_state_code, role_id, enable_time, disable_time, description, role_code, member_gender, birth_date, attend_worker_date, plate, profession_now, technical_qualification, phone, start_org_id, stop_org_id, organization_name
        FROM
            organization_member_history
        <where>
            <!-- 成员名称模糊查询 -->
            <if test="memberName != null and memberName != ''">
                AND member_name LIKE CONCAT('%', #{memberName}, '%')
            </if>
            <!-- 成员编码模糊查询 -->
            <if test="memberCode != null and memberCode != ''">
                AND member_code LIKE CONCAT('%', #{memberCode}, '%')
            </if>
            <!-- 数据状态查询 -->
            <if test="dataState != null and dataState != ''">
                AND data_state = #{dataState}
            </if>
            <!-- 是否台账功能 -->
            <choose>
                <when test="isQuery">
                    <!-- 台账功能：添加数据权限过滤 -->
                    AND (
                    create_psn_full_id LIKE CONCAT('%', #{orgId}, '%')
                    <if test="roleIds != null and roleIds.size() > 0">
                        OR (
                        <foreach collection="roleIds" item="roleId" separator=" OR ">
                            create_psn_full_id LIKE CONCAT(
                            '%',
                            (SELECT DISTINCT unit_id
                            FROM sys_role_function_unit
                            WHERE function_id = #{functionId}
                            AND role_id = #{roleId}),
                            '%'
                            )
                        </foreach>
                        )
                    </if>
                    )
                </when>
                <otherwise>
                    <!-- 非台账功能：只查询当前组织 -->
                    AND create_org_id = #{orgId}
                </otherwise>
            </choose>
            order by create_time desc
            limit #{page},#{limit}

        </where>
    </select>
    <select id="queryHistoryCount" resultType="java.lang.Integer">
        select sum(count) from (
        SELECT
        count(*) as count
        FROM
        organization_member
        <where>
            <!-- 成员名称模糊查询 -->
            <if test="memberName != null and memberName != ''">
                AND member_name LIKE CONCAT('%', #{memberName}, '%')
            </if>
            <!-- 成员编码模糊查询 -->
            <if test="memberCode != null and memberCode != ''">
                AND member_code LIKE CONCAT('%', #{memberCode}, '%')
            </if>
            <!-- 数据状态查询 -->
            <if test="dataState != null and dataState != ''">
                AND data_state = #{dataState}
            </if>
            <!-- 是否台账功能 -->
            <choose>
                <when test="isQuery">
                    AND (
                    create_psn_full_id LIKE CONCAT('%', #{orgId}, '%')
                    <if test="roleIds != null and roleIds.size() > 0">
                        OR (
                        <foreach collection="roleIds" item="roleId" separator=" OR ">
                            create_psn_full_id LIKE CONCAT(
                            '%',
                            (SELECT DISTINCT unit_id
                            FROM sys_role_function_unit
                            WHERE function_id = #{functionId}
                            AND role_id = #{roleId}),
                            '%'
                            )
                        </foreach>
                        )
                    </if>
                    )
                </when>
                <otherwise>
                    <!-- 非台账功能：只查询当前组织 -->
                    AND create_org_id = #{orgId}
                </otherwise>
            </choose>
        </where>
        UNION ALL
        SELECT
        count(*) as count
        FROM
        organization_member_history
        <where>
            <!-- 成员名称模糊查询 -->
            <if test="memberName != null and memberName != ''">
                AND member_name LIKE CONCAT('%', #{memberName}, '%')
            </if>
            <!-- 成员编码模糊查询 -->
            <if test="memberCode != null and memberCode != ''">
                AND member_code LIKE CONCAT('%', #{memberCode}, '%')
            </if>
            <!-- 数据状态查询 -->
            <if test="dataState != null and dataState != ''">
                AND data_state = #{dataState}
            </if>
            <!-- 是否台账功能 -->
            <choose>
                <when test="isQuery">
                    AND (
                    create_psn_full_id LIKE CONCAT('%', #{orgId}, '%')
                    <if test="roleIds != null and roleIds.size() > 0">
                        OR (
                        <foreach collection="roleIds" item="roleId" separator=" OR ">
                            create_psn_full_id LIKE CONCAT(
                            '%',
                            (SELECT DISTINCT unit_id
                            FROM sys_role_function_unit
                            WHERE function_id = #{functionId}
                            AND role_id = #{roleId}),
                            '%'
                            )
                        </foreach>
                        )
                    </if>
                    )
                </when>
                <otherwise>
                    <!-- 非台账功能：只查询当前组织 -->
                    AND create_org_id = #{orgId}
                </otherwise>
            </choose>
        </where>
        )  as counts

    </select>
</mapper>