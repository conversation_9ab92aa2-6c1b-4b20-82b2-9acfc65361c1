
3096c1a4ba4c9c658e83869021ac7ec25632acc9	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.32.1754018536329.js\",\"contentHash\":\"014fc14d5c11a582644eb0ad00973cb5\"}","integrity":"sha512-+hKFouKjhppsSn/HTfLqRXELQUpUyoEIxPAaHroHhf+848pBS68uRRTTpUpgWFLCyjBo471EDSI5UnMixfemUA==","time":1754018575958,"size":117402}