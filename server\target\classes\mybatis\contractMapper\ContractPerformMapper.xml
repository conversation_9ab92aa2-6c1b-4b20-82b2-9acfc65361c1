<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.klaw.dao.contractDao.ContractPerformMapper">

    <update id="updateStatus">
         update SG_CONTRACT_APPROVAL set perform_STATE='正常履行中',perform_STATE_CODE='1' where id = #{id}
    </update>

    <update id="updatePerformStatus">
        update SG_CONTRACT_APPROVAL set PERFORM_STATE = #{status},PERFORM_STATE_CODE = #{statusId} where id=#{id}
    </update>

    <update id="updatePlanStatus">
        update SG_CONTRACT_PERFORM set PERFORM_STATUS = #{status},PERFORM_STATUS_ID = #{statusId} where id=#{id}
    </update>

    <select id="searchPerformStartTime" resultType="java.util.Map">
       select max(P.CREATE_TIME) as CREATETIME,p.CONTRACT_NAME,p.CREATE_PSN_ID,p.CREATE_PSN_NAME,p.create_org_id,p.create_org_name,
p.create_psn_full_id,p.create_psn_full_name,p.relation_id from sg_contract_approval a
       inner join sg_contract_perform p on a.id = p.relation_id where (a.change_state is null) and (a.perform_state = '正常履行中' or a.perform_state = '延期履行中')
       group by p.relation_id,p.contract_name,p.create_psn_id,p.create_psn_name,p.create_org_id,p.create_org_name,
p.create_psn_full_id,p.create_psn_full_name
    </select>

    <select id="queryNoticeStart" resultType="com.klaw.entity.contractBean.ContractPerform">
        select *
        from SG_CONTRACT_PERFORM where perform_Start_Time is not null
        <foreach collection="list" index="index" item="item" open="  and (TRUNC(perform_Start_Time,'dd') - TRUNC(sysdate,'dd')) in (" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="queryNoticeEnd" resultType="com.klaw.entity.contractBean.ContractPerform">
        select *
        from SG_CONTRACT_PERFORM where perform_end_Time is not null and  (perform_status <![CDATA[ <> ]]> '履行完成' or perform_status <![CDATA[ <> ]]> '逾期履行完成')
        <foreach collection="list" index="index" item="item" open="  and (TRUNC(perform_end_Time,'dd') - TRUNC(sysdate,'dd')) in (" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="queryNoticeAfterEnd" resultType="com.klaw.entity.contractBean.ContractPerform">
        select *
        from SG_CONTRACT_PERFORM where perform_end_Time is not null
        <foreach collection="list" index="index" item="item" open="  and (TRUNC(sysdate,'dd') - TRUNC(perform_end_Time,'dd')) in (" separator="," close=")">
            #{item}
        </foreach>
    </select>
</mapper>

