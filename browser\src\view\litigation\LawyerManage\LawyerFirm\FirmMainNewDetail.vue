<template>
  <FormWindow ref="formWindow" @initData="initData" @loadData="loadData">
    <el-container style="height: calc(100vh - 84px);">
      <el-main>
        <el-scrollbar style="height: 100%" wrap-style="overflow-x: hidden;">
          <el-form ref="dataForm" :model="mainData"
                   :rules="dataState !== 'view' ? rules : {}"
                   :style="dataState !== utils.formState.VIEW ? 'margin-right: 50px;' : 'margin-right: 0px;'"
                   label-width="100px"
                   style="padding-right: 10px;">
            <el-row
                style="padding: 10px 0 10px 0; position:fixed;  width: 100%;overflow-y:auto;background-color: white;z-index: 999">
              <el-button v-if="!isView" class="normal-btn" icon="el-icon-folder-checked" size="mini" @click="save_">
                保存
              </el-button>
              <el-button v-if="!isView" class="normal-btn" icon="el-icon-folder-checked" size="mini" @click="approval_">
                提交并入库
              </el-button>
            </el-row>
            <div style="padding-top: 50px;"></div>
            <span style="text-align: left;font-size: 20px;margin-left: 43%;font-weight: 900;">律所入库详情单</span>

            <div v-if="dataState !== 'view'">
              <div style="margin: 10px">
                <span style="font-weight: bold;font-size: 16px;color: #5A5A5F;">基本信息</span>
              </div>
              <el-divider></el-divider>

              <el-row style="margin-top: 10px">
                <el-col :span="16">
                  <el-form-item label="事项名称" prop="itemName">
                    <el-input v-if="dataState !== 'view'" v-model.trim="mainData.itemName"
                              clearable maxlength="100" placeholder="请输入..." show-word-limit style="width: 100%"/>
                    <span v-else class="viewSpan">{{ mainData.itemName }}</span>
                  </el-form-item>
                </el-col>

                <el-col :span="8">
                  <el-form-item label="事项编号" prop="sequenceCode">
                    <el-input v-if="dataState !== 'view'" v-model.trim="mainData.sequenceCode" disabled
                              show-word-limit style="width: 100%"/>
                    <span v-else class="viewSpan">{{ mainData.sequenceCode }}</span>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row>
                <el-col :span="24">
                  <el-form-item label="律所名称" prop="lawyerFirm">
                    <el-input
                        v-if="dataState !== 'view'"
                        v-model.trim="mainData.lawyerFirm"
                        clearable maxlength="50" placeholder="请输入..."
                        show-word-limit style="width: 100%"/>
                    <span v-else>
                      <i v-if="topLawFirm(mainData.licenseCode)" class="el-icon-s-help"></i>
                      <span class="viewSpan">{{ mainData.lawyerFirm }}</span>
                  </span>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row>
                <el-col :span="24">
                  <el-form-item label="律所地址" prop="registerAddress">
                    <el-input v-if="dataState !== 'view'"
                              v-model="mainData.registerAddress"
                              clearable maxlength="50" placeholder="请输入..." show-word-limit style="width: 100%"/>
                    <span v-else class="viewSpan">{{ mainData.registerAddress }}</span>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row>
                <el-col :span="8">
                  <el-form-item label="律所类型" prop="lawFirmType">
                    <template>
                      <el-select v-if="dataState !== 'view'"
                                 v-model="mainData.lawFirmType"
                                 clearable disabled placeholder="请选择" style="width: 100%"
                      >
                      </el-select>
                      <span v-else class="viewSpan">{{ mainData.lawFirmType }}</span>
                    </template>
                  </el-form-item>
                </el-col>

                <el-col :span="8">
                  <el-form-item label="律所电话" prop="lawFirmPhone">
                    <el-input v-if="dataState !== 'view'"
                              v-model="mainData.lawFirmPhone"
                              clearable maxlength="20" placeholder="请输入..."
                              show-word-limit style="width: 100%"/>
                    <span v-else class="viewSpan">{{ mainData.lawFirmPhone }}</span>
                  </el-form-item>
                </el-col>

                <el-col :span="8">
                  <el-form-item label="律所邮箱" prop="lawFirmEmail">
                    <el-input v-if="dataState !== 'view'"
                              v-model="mainData.lawFirmEmail"
                              clearable maxlength="40" placeholder="请输入..."
                              show-word-limit style="width: 100%"/>
                    <span v-else class="viewSpan">{{ mainData.lawFirmEmail }}</span>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row>
                <el-col :span="8">
                  <el-form-item label="负责人" prop="functionary">
                    <el-input v-if="dataState !== 'view'"
                              v-model="mainData.functionary"
                              clearable maxlength="20" placeholder="请输入..."
                              show-word-limit style="width: 100%"/>
                    <span v-else class="viewSpan">{{ mainData.functionary }}</span>
                  </el-form-item>
                </el-col>

                <el-col :span="8">
                  <el-form-item label="邮政编码" prop="postalCode">
                    <el-input v-if="dataState !== 'view'"
                              v-model="mainData.postalCode"
                              clearable maxlength="20" placeholder="请输入..."
                              show-word-limit style="width: 100%"/>
                    <span v-else class="viewSpan">{{ mainData.postalCode }}</span>
                  </el-form-item>
                </el-col>

                <el-col :span="8">
                  <el-form-item label="主管机关" prop="issuingAuthority">
                    <el-input v-if="dataState !== 'view'"
                              v-model="mainData.issuingAuthority"
                              clearable maxlength="30" placeholder="请输入..."
                              show-word-limit style="width: 100%"/>
                    <span v-else class="viewSpan">{{ mainData.issuingAuthority }}</span>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row>
                <el-col :span="8" class="myLabel">
                  <el-form-item class="custom-word-break" label="统一社会信用代码" prop="licenseCode">
                    <span slot="label">统一社会<br>信用代码</span>
                    <el-input v-if="dataState !== 'view'"
                              v-model="mainData.licenseCode"
                              clearable maxlength="20" placeholder="请输入..." show-word-limit style="width: 100%"
                              @blur="licenseCodeBlur"/>
                    <span v-else class="viewSpan">{{ mainData.licenseCode }}</span>
                  </el-form-item>
                </el-col>

                <el-col :span="8">
                  <el-form-item label="批准文号" prop="licenseNumber">
                    <el-input v-if="dataState !== 'view'"
                              v-model="mainData.licenseNumber"
                              clearable maxlength="40" placeholder="请输入..." show-word-limit style="width: 100%"/>
                    <span v-else class="viewSpan">{{ mainData.licenseNumber }}</span>
                  </el-form-item>
                </el-col>

                <el-col :span="8">
                  <el-form-item label="设立资产" prop="registeredCapital">
                    <el-input v-if="dataState !== 'view'"
                              v-model="mainData.registeredCapital"
                              clearable maxlength="20" placeholder="请输入..." show-word-limit style="width: 100%"/>
                    <span v-else class="viewSpan">{{ mainData.registeredCapital }}</span>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row>
                <el-col :span="8">
                  <el-form-item label="成立时间" prop="foundTime">
                    <el-date-picker v-if="dataState !== 'view'"
                                    v-model="mainData.foundTime"
                                    clearable style="width: 100%" type="date" value-format="yyyy-MM-dd"/>
                    <span v-else class="viewSpan">{{ mainData.foundTime | parseTime }}</span>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="执业人数" prop="workNumber">
                    <el-input-number v-if="dataState !== 'view'"
                                     v-model="mainData.workNumber"
                                     :controls="false"
                                     :max="99999999"
                                     :precision="0"
                                     clearable placeholder="只能输入数字" show-word-limit style="width: 100%"
                                     @blur="setLawFirmLevel"
                    />
                    <span v-else class="viewSpan">{{ mainData.workNumber }}</span>
                  </el-form-item>
                </el-col>

                <el-col :span="8">
                  <el-form-item label="律所规模" prop="lawFirmLevel">
                    <el-input v-if="dataState !== 'view'"
                              v-model="mainData.lawFirmLevel"
                              disabled maxlength="50" placeholder="请输入..." show-word-limit style="width: 100%"/>
                    <span v-else class="viewSpan">{{ mainData.lawFirmLevel }}</span>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row>
                <el-col :span="8">
                  <el-form-item label="申请部门" prop="applyDeptName">
                    <el-input v-if="dataState !== 'view'"
                              v-model="mainData.applyDeptName"
                              class="input-with-select" clearable disabled placeholder="请选择">
                      <el-button slot="append" icon="el-icon-search" @click="chooseApprovalDeptClick"/>
                    </el-input>
                    <span v-else class="viewSpan">{{ mainData.applyDeptName }}</span>
                  </el-form-item>
                </el-col>

                <el-col :span="8">
                  <el-form-item label="年检情况" prop="annualInspectionId">
                    <template>
                      <el-select v-if="dataState !== 'view'"
                                 v-model="mainData.annualInspectionId"
                                 clearable placeholder="请选择" style="width: 100%"
                                 @change="annualInspectionChange">
                        <el-option
                            v-for="item in NJQKData"
                            :key="item.id"
                            :label="item.dicName"
                            :value="item.id"
                        />
                      </el-select>
                      <span v-else class="viewSpan">{{ mainData.annualInspectionName }}</span>
                    </template>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row v-if="mainData.annualInspectionName === '其他情况'">
                <el-col :span="24">
                  <el-form-item label="年检情况说明" prop="annualInspectionDesc">
                    <el-input
                        v-if="dataState !== 'view'"
                        v-model.trim="mainData.annualInspectionDesc"
                        clearable maxlength="50" placeholder="请输入..."
                        show-word-limit style="width: 100%"/>
                    <span v-else class="viewSpan">{{ mainData.annualInspectionDesc }}</span>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row>
                <el-col :span="24">
                  <el-form-item label="擅长业务领域" prop="beGoodAtDomainIds">
                    <el-select v-if="dataState !== 'view'"
                               v-model="beGoodAtDomainIds_1"
                               multiple placeholder="请选择" style="width: 100%"
                               @change="beGoodAtDomainChange">
                      <el-option
                          v-for="item in SCLYData"
                          :key="item.id"
                          :label="item.dicName"
                          :value="item.id"
                      />
                    </el-select>
                    <span v-else class="viewSpan">{{ mainData.beGoodAtDomain }}</span>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row>
                <el-col :span="24">
                  <el-form-item label="律所简介" prop="introduction">
                    <el-input v-if="dataState !== 'view'"
                              v-model="mainData.introduction"
                              :autosize="{ minRows: 3, maxRows: 6}" clearable
                              maxlength="1000"
                              placeholder="(重点介绍近两年执业成功)" show-word-limit style="width: 100%" type="textarea"/>
                    <text-span v-else :text=" mainData.introduction" class="viewSpan"/>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row>
                <el-col :span="24">
                  <el-form-item label="入库事由" prop="remark">
                    <el-input v-if="dataState !== 'view'"
                              v-model="mainData.remark"
                              :autosize="{ minRows: 3, maxRows: 6}" clearable
                              maxlength="1000"
                              placeholder="" show-word-limit style="width: 100%" type="textarea"/>
                    <text-span v-else :text=" mainData.remark" class="viewSpan"/>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row>
                <el-col :span="24" prop="businessLicense">
                  <el-form-item label="附件资料" prop="businessLicense">
                    <uploadDoc
                        v-model="mainData.businessLicense"
                        :disabled="dataState === 'view'"
                        :doc-path="docURL"
                        :files.sync="mainData.businessLicense"
                        :tips="tip_"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>

            <SimpleBoardTitle v-if="dataState === 'view'" style="margin-top: 5px;" title="基本信息">
              <table class="table_content" style="margin-top: 10px;">
                <tbody>
                <tr>
                  <th class="th_label" colspan="2">事项名称</th>
                  <td class="td_value" colspan="14">{{ mainData.itemName }}</td>
                  <th class="th_label" colspan="2">事项编号</th>
                  <td class="td_value" colspan="6">{{ mainData.sequenceCode }}</td>
                </tr>
                <tr>
                  <th class="th_label" colspan="2">律所名称</th>
                  <td class="td_value" colspan="22"><i v-if="topLawFirm(mainData.licenseCode)"
                                                       class="el-icon-s-help"></i> {{ mainData.lawyerFirm }}
                  </td>
                </tr>
                </tbody>

                <tbody>
                <tr>
                  <th class="th_label" colspan="2">律所地址</th>
                  <td class="td_value" colspan="22">{{ mainData.registerAddress }}</td>
                </tr>
                </tbody>

                <tbody>
                <tr>
                  <th class="th_label" colspan="2">律所类型</th>
                  <td class="td_value" colspan="6">{{ mainData.lawFirmType }}</td>
                  <th class="th_label" colspan="2">律所电话</th>
                  <td class="td_value" colspan="6">{{ mainData.lawFirmPhone }}</td>
                  <th class="th_label" colspan="2">律所邮箱</th>
                  <td class="td_value" colspan="6">{{ mainData.lawFirmEmail }}</td>
                </tr>
                </tbody>

                <tbody>
                <tr>
                  <th class="th_label" colspan="2">负责人</th>
                  <td class="td_value" colspan="6">{{ mainData.functionary }}</td>
                  <th class="th_label" colspan="2">邮政编码</th>
                  <td class="td_value" colspan="6">{{ mainData.postalCode }}</td>
                  <th class="th_label" colspan="2">主管机关</th>
                  <td class="td_value" colspan="6">{{ mainData.issuingAuthority }}</td>
                </tr>
                </tbody>

                <tbody>
                <tr>
                  <th class="th_label" colspan="2">统一社会<br>信用代码</th>
                  <td class="td_value" colspan="6">{{ mainData.licenseCode }}</td>
                  <th class="th_label" colspan="2">批准文号</th>
                  <td class="td_value" colspan="6">{{ mainData.licenseNumber }}</td>
                  <th class="th_label" colspan="2">设立资产</th>
                  <td class="td_value" colspan="6">{{ mainData.registeredCapital }}</td>
                </tr>
                </tbody>

                <tbody>
                <tr>
                  <th class="th_label" colspan="2">成立时间</th>
                  <td class="td_value" colspan="6">{{ mainData.foundTime | parseTime }}</td>
                  <th class="th_label" colspan="2">执业人数</th>
                  <td class="td_value" colspan="6">{{ mainData.workNumber }}</td>
                  <th class="th_label" colspan="2">律所规模</th>
                  <td class="td_value" colspan="6">{{ mainData.lawFirmLevel }}</td>
                </tr>
                </tbody>

                <tbody>
                <tr>
                  <th class="th_label" colspan="2">申请部门</th>
                  <td class="td_value" colspan="6">{{ mainData.applyDeptName }}</td>
                  <th class="th_label" colspan="2">年检情况</th>
                  <td class="td_value" colspan="14">{{ mainData.annualInspectionName }}</td>
                </tr>
                <tr v-if="mainData.annualInspectionName === '其他情况'">
                  <th class="th_label" colspan="2">年检情况</th>
                  <td class="td_value" colspan="22">{{ mainData.annualInspectionDesc }}</td>
                </tr>
                </tbody>

                <tbody>
                <tr>
                  <th class="th_label" colspan="2">擅长业务领域</th>
                  <td class="td_value" colspan="22">{{ mainData.beGoodAtDomain }}</td>
                </tr>
                </tbody>

                <tbody>
                <tr>
                  <th class="th_label_" colspan="2">律所简介</th>
                  <td class="td_value_" colspan="22">{{ mainData.introduction }}</td>
                </tr>
                </tbody>

                <tbody>
                <tr>
                  <th class="th_label_" colspan="2">入库事由</th>
                  <td class="td_value_" colspan="22">{{ mainData.remark }}</td>
                </tr>
                </tbody>

                <tbody>
                <tr>
                  <th class="th_label_" colspan="2">附件资料</th>
                  <td class="td_value_" colspan="22">
                    <uploadDoc
                        v-model="mainData.businessLicense"
                        :disabled="dataState === 'view'"
                        :doc-path="docURL"
                        :files.sync="mainData.businessLicense"
                        :tips="tip_"
                    />
                  </td>
                </tr>
                </tbody>
              </table>
            </SimpleBoardTitle>

            <simple-board :data-state="dataState"
                          :has-value="true"
                          :hasAdd="true"
                          style="margin-top: 20px;"
                          title="主责律师/对接律师"
                          @addBtn="addLawyer">
              <el-table
                  :data="mainData.lawyerList"
                  :height="200"
                  :show-overflow-tooltip="true"
                  border
                  fit
                  highlight-current-row
                  stripe
                  style="width: 100%">
                <el-table-column align="center" label="序号" show-overflow-tooltip type="index" width="60"/>
                <el-table-column align="center" label="姓名" min-width="100" prop="lawyerName" show-overflow-tooltip/>
                <el-table-column align="center" label="律师身份" min-width="100" prop="whetherHostLawyer"
                                 show-overflow-tooltip>
                  <template slot-scope="scope">
                    <span>{{ scope.row.whetherHostLawyer | whetherHostLawyerFilter }}</span>
                  </template>
                </el-table-column>
                <el-table-column align="center" label="联系电话" min-width="100" prop="lawyerPhone"
                                 show-overflow-tooltip/>
                <el-table-column align="center" label="电子邮箱" min-width="100" prop="lawyerEmail"
                                 show-overflow-tooltip/>
                <el-table-column align="center" label="擅长领域" min-width="150" prop="beGoodAtDomain"
                                 show-overflow-tooltip/>
                <el-table-column align="center" label="主要执业地域" min-width="150" prop="majorRegion"
                                 show-overflow-tooltip/>
                <el-table-column align="center" label="操作" width="200px">
                  <template slot-scope="scope">
                    <el-button
                        v-if="dataState !== 'view'&& scope.row.whetherSystem && scope.row.currentStateCode === utils.dataState_LAWYER.YRK.code"
                        size="mini" type="primary"
                        @click.native.prevent="CKRow(scope.$index, scope.row)">出库
                    </el-button>
                    <el-button
                        v-if="dataState !== 'view' && scope.row.whetherSystem && scope.row.currentStateCode === utils.dataState_LAWYER.YCK.code"
                        size="mini" type="text" @click.native.prevent="QXCKRow(scope.$index, scope.row)">取消出库
                    </el-button>
                    <el-button
                        v-if="dataState !== 'view' && scope.row.currentStateCode !== utils.dataState_LAWYER.YCK.code"
                        size="mini" type="text" @click.native.prevent="editRow(scope.$index, scope.row)">编辑
                    </el-button>
                    <el-button v-if="dataState !== 'view' && !scope.row.whetherSystem" size="mini" type="text"
                               @click.native.prevent="deleteRow(scope.$index, scope.row)">删除
                    </el-button>
                    <el-button v-if="dataState === 'view'" size="mini" type="text"
                               @click.native.prevent="look(scope.$index,scope.row)">查看
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </simple-board>

            <div style="height: 100%;">
              <el-row class="rowCol1" style="margin-top: 20px">
                <el-col :span="16">
                  <el-form-item label="经办单位">
                    <span class="viewSpan">{{ mainData.createOgnName }}</span>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="经办人">
                    <span class="viewSpan">{{ mainData.createPsnName }}</span>
                  </el-form-item>
                </el-col>
                <el-col :span="16">
                  <el-form-item label="经办部门">
                    <span class="viewSpan">{{ mainData.createDeptName }}</span>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="经办时间">
                    <span class="viewSpan">{{ mainData.createTime | parseTime }}</span>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>

            <!--选择会知部门-->
            <el-dialog :close-on-click-modal="false" :visible.sync="deptOrgVisible" title="选择部门" width="50%">
              <div class="el-dialog-div">
                <orgTree :accordion="false" :checked-data.sync="zxcheckedData" :is-check="true" :is-checked-user="false"
                         :is-filter="true" :is-not-cascade="true" :show-user="false"/>
              </div>
              <span slot="footer" class="dialog-footer">
                <el-button class="negative-btn" icon="" @click="deptOrgCancel">取消</el-button>
                <el-button class="active-btn" icon="" type="primary" @click="choiceNoticeDeptSure">确定</el-button>
              </span>
            </el-dialog>

            <!--选择部门或者人员-->
            <el-dialog :close-on-click-modal="false" :visible.sync="orgVisible" title="选择部门" width="50%">
              <div class="el-dialog-div">
                <orgTree :accordion="false" :checked-data.sync="zxcheckedData" :is-check="is_Check" :is-checked-user="isCheckedUser"
                         :is-filter="true" :is-not-cascade="true" :show-user="showUser"/>
              </div>
              <span slot="footer" class="dialog-footer">
                <el-button class="negative-btn" icon="" @click="cancel_">取消</el-button>
                <el-button class="active-btn" icon="" type="primary" @click="choiceDeptSure_">确定
                </el-button>
              </span>
            </el-dialog>

          </el-form>
        </el-scrollbar>
      </el-main>
    </el-container>
  </FormWindow>
</template>

<script>
import {mapGetters} from 'vuex'
import {recommended} from '@/view/utils/constants'

import lawyerFirmApi from "@/api/LawyerManage/LawyerFirm/lawyerFirm"
import lawyerFirm from "@/api/LawyerManage/LawyerFirm/lawyerFirm"
import lawyerApi from '@/api/LawyerManage/LawyerFirm/lawyerInApproval'

import FormWindow from '@/view/components/FormWindow/FormWindow'
import UploadDoc from '@/view/components/UploadDoc/UploadDoc'
import textSpan from '@/view/components/TextSpan/TextSpan'
import orgTree from '@/view/components/OrgTree/OrgTree'
import SimpleBoard from "@/view/components/SimpleBoard/SimpleBoardViewCase"
import SimpleBoardTitle from "../../../components/SimpleBoard/SimpleBoardTitle"
import ProcessOpinion from '@/view/components/ProcessOpinion/ProcessOpinion'
import SimpleBoardTitleApproval from "@/view/components/SimpleBoard/SimpleBoardTitleApproval";
import taskApi from "@/api/_system/task";

export default {
  name: "FirmMainNewDetail",
  inject: ['layout', 'mcpLayout'],
  components: {
    SimpleBoardTitleApproval, FormWindow, UploadDoc, SimpleBoard, textSpan, orgTree, SimpleBoardTitle, ProcessOpinion
  },
  filters: {
    whetherHostLawyerFilter(val) {
      return val ? '主责律师' : '助理律师'
    }
  },
  data() {
    return {
      type: null,
      dataState: null,//表单状态，新增、查看、编辑
      functionId: null,//终止的时候要用，需要手动关闭
      typeCode: null,
      typeName: null,
      changeId: null,
      tableData: [],
      tabId: null,
      view: 'new',
      rules: {
        lawyerFirm: [{required: true, message: '请输入律所名称', trigger: 'blur'}],
        registerAddress: [{required: true, message: '请输入律所地址', trigger: 'blur'}],
        // lawFirmType: [{required: true, message: '请选择律所类型', trigger: 'blur'}],
        functionary: [{required: true, message: '请输入负责人', trigger: 'blur'}],
        licenseCode: [{required: true, message: '请输入统一社会信用代码', trigger: 'blur'}],
        applyDeptName: [{required: true, message: '请选择申请部门', trigger: 'blur'}],
        workNumber: [{required: true, message: '请输入执业人数', trigger: 'blur'}],
        annualInspectionDesc: [{required: true, message: '请输入年检情况', trigger: 'blur'}],
        businessLicense: [{required: true, message: '请上传资质文件', trigger: 'blur'}],
      },
      mainData: {
        id: this.utils.createUUID(), // id
        itemName: null, // 事项名称
        noticeDeptName: null, //会知部门名称
        noticeDeptId: null, //会知部门id
        sequenceCode: null, // 流水号
        lawyerFirm: null, // 律所名称
        registerAddress: null, // 注册地址/律所地址
        functionary: null, // 负责人
        postalCode: null, // 邮政编码
        issuingAuthority: null, // 主管机关
        licenseCode: null, // 统一社会信用代码
        licenseNumber: null, // 批准文号
        registeredCapital: null, // 设立资产
        foundTime: null, // 成立时间
        workNumber: null, // 执业人数
        applyDeptName: null, // 申请部门/所属公司
        applyDeptId: null, // 申请部门/所属公司
        annualInspectionName: null, // 年检情况
        annualInspectionId: null, // 年检情况
        annualInspectionDesc: null, // 年检情况描述
        noResponseTimes: null, // 未响应次数
        recommendedInstructionsName: null, // 推荐说明
        recommendedInstructionsId: null, // 推荐说明
        recommended: null, // 推荐
        introduction: null, // 律所简介
        remark: null, // 入库事由
        businessLicense: null, // 营业执照、附件资料
        updateTime: null, // 更新时间
        typeCode: null,
        typeName: null,
        eventReport: null,
        dataState: this.utils.dataState_BPM.SAVE.name, //数据状态
        dataStateCode: this.utils.dataState_BPM.SAVE.code, //数据状态编码
        createOgnName: null, //  经办单位
        createDeptName: null, // 经办部门
        createPsnName: null, // 经办人员
        createTime: null, // 经办时间
        createOgnId: null,
        createDeptId: null,
        createPsnId: null,
        createPsnFullId: null,
        createPsnFullName: null,
        createGroupId: null,
        createGroupName: null,
        createOrgId: null,
        createOrgName: null,
        changeTimes: 0,
        parentId: null,
        dataSource: 'new',
        sourceId: null,
        whetherMy: 'no',
        lawyerList: [], // 弹框的表格数据源
        operatingType: 'new', // 弹框的表格数据源
        beGoodAtDomain: null, // 擅长领域
        beGoodAtDomainIds: null, // 擅长领域
        changeTypeName: null,
        changeTypeId: null,
        changeReason: null,
        lawFirmPhone: null, // 律所电话
        lawFirmEmail: null, // 律所邮箱
        lawFirmType: null, // 律所类型
        lawFirmSelected: false, // 是否选聘
        lawFirmLevel: null, // 律所规模
      },
      approvalData: null,
      recommendedTitle: null,
      NJQKData: [],
      TJSMData: this.utils.recommended_data,
      SCLYData: [],
      tip_: '附件要求包括机构及负责人简介、通过年检的《营业执照》/《职业资格许可证》（或副本）的复印件、相应资质证书复印件、负责人和从业人员名单及3人以上资格证书复印件、其他材料。',
      docURL: '/firmIn',
      lawFirmTypeData: [{id: '0', name: '正式律所'}, {id: '1', name: '临时律所'}],
      deptOrgVisible: false,
      zxcheckedData: [],
      showUser: false,
      is_Check: true,
      isCheckedUser: false,
      orgVisible: false,
      hasAdd: true,
      module: null,
      moduleName: null,
    }
  },
  computed: {
    ...mapGetters(['orgContext']),
    TJSM() {
      const val = this.mainData.recommendedInstructionsId
      const me = this
      if (val === recommended.HYJGTJ.code) {
        me.recommendedTitle = '推荐机构全称'
      } else if (val === recommended.HZQYTJ.code) {
        me.recommendedTitle = '合作企业全称'
      } else if (val === recommended.QTYY.code) {
        me.recommendedTitle = '其他原因'
      } else {
        me.recommendedTitle = null
      }
      return val === recommended.HYJGTJ.code || val === recommended.HZQYTJ.code || val === recommended.QTYY.code
    },
    recommendedRules() {
      const val = this.mainData.recommendedInstructionsId
      const me = this
      let rule = {}
      if (val === recommended.HYJGTJ.code) {
        me.recommendedTitle = '推荐机构全称'
        rule = {required: me.dataState !== 'view', message: '请填写推荐机构全称', trigger: 'blur'}
      } else if (val === recommended.HZQYTJ.code) {
        me.recommendedTitle = '合作企业全称'
        rule = {required: me.dataState !== 'view', message: '请填写合作企业全称', trigger: 'blur'}
      } else if (val === recommended.QTYY.code) {
        me.recommendedTitle = '其他原因'
        rule = {required: me.dataState !== 'view', message: '请填写其他原因', trigger: 'blur'}
      }
      return rule
    },
    beGoodAtDomainIds_1: {
      set: function (data) {
        this.mainData.beGoodAtDomainIds = data.join(',')
      },
      get: function () {
        if (this.mainData.beGoodAtDomainIds) {
          return this.mainData.beGoodAtDomainIds.split(',')
        }
        return []
      }
    },
    changeTypeIds: {
      set: function (data) {
        this.mainData.changeTypeId = data.join(',')
      },
      get: function () {
        if (this.mainData.changeTypeId) {
          return this.mainData.changeTypeId.split(',')
        }
        return []
      }
    },
    isView: function () {
      return this.dataState === this.utils.formState.VIEW
    },
  },
  created() {
    this.isHeadquarter()
    this.baseDataLoad()
  },
  activated() {
    this.refresh_()
  },
  methods: {

    baseDataLoad() {
      const codes = ["LS-NJQK", "LS-SCLY", "LS-BGLX"];
      this.utils.getDic(codes).then(response => {
        this.NJQKData = response.data.data[codes[0]];
        this.SCLYData = response.data.data[codes[1]];
        this.BGLXData = response.data.data[codes[2]];
      });
    },
    cancel_() {
      this.orgVisible = false
    },
    choiceDeptSure_() {
      let c = ''
      let cid = ''
      this.zxcheckedData.forEach((item) => {
        if (c.length === 0) {
          c = c + item.name
          cid = cid + item.unitId
        } else {
          c = c + ',' + item.name
          cid = cid + ',' + item.unitId
        }
      })
      this.mainData.applyDeptName = c
      this.mainData.applyDeptId = cid
      this.orgVisible = false
    },
    addLawyer() {
      return new Promise((resolve, reject) => {
        this.mainData.updateTime = new Date()
        lawyerFirmApi.save(this.mainData).then(response => {
          const tabId = this.utils.createUUID()
          this.layout.openNewTab(
              "律师信息",
              "main_lawyer",
              "main_lawyer",
              tabId,
              {
                mainData: this.mainData,
                ...this.utils.routeState.NEW(this.utils.createUUID()),
                tabId: tabId
              })

          resolve(response)
        }).catch(error => {
          reject(error)
        })
      })
    },
    editRow(index, row) {
      this.save().then(() => {
            const tabId = this.utils.createUUID()
            this.layout.openNewTab("律师信息", "main_lawyer", "main_lawyer",
                tabId,
                {
                  id: this.mainData.id,
                  lawyerFirm: this.mainData.lawyerFirm, ...this.utils.routeState.EDIT(row.id),
                  tabId: tabId
                })
          }
      )
    },
    deleteRow(index, row) {
      this.$confirm('您确定要删除主责律师吗？删除后主责律师以及下属助理律师都会被删掉！', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        new Promise((resolve) => {
          lawyerApi.delete(row).then((response) => {
            resolve(response)
          })
        }).then(() => {
          this.mainData.lawyerList.splice(index, 1)
          this.$message.success('删除成功!')
        })
      }).catch(() => {
        this.$message.info('已取消删除!')
      })
    },
    look(index, row) {
      const tabId = this.utils.createUUID()
      this.layout.openNewTab("律师信息", "main_lawyer", "main_lawyer", tabId, {
        id: this.mainData.id,
        lawyerFirm: this.mainData.lawyerFirm,
        ...this.utils.routeState.VIEW(row.id),
        tabId: tabId
      })
    },
    CKRow(index, row) {
      this.$confirm('您确定要出库主责律师吗？出库后主责律师以及下属助理律师都会被出库！', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        row.currentState = this.utils.dataState_LAWYER.YCK.name
        row.currentStateCode = this.utils.dataState_LAWYER.YCK.code
        lawyerApi.changeOut(row).then(() => {
        })
      }).catch(() => {
        this.$message.info('已取消删除!')
      })
    },
    QXCKRow(index, row) {
      this.$confirm('您确定要取消出库主责律师吗？取消出库后主责律师以及下属助理律师都会被取消出库！', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        row.currentState = this.utils.dataState_LAWYER.YRK.name
        row.currentStateCode = this.utils.dataState_LAWYER.YRK.code
        lawyerApi.changeOut(row).then(() => {
        })
      }).catch(() => {
        this.$message.info('已取消删除!')
      })
    },
    // 数据初始化,主要是新增的时候触发
    initData(temp, dataState) {
      // 判断当前库的类型
      this.isHeadquarter()

      temp.typeName = this.typeName
      temp.typeCode = this.typeCode
      this.dataState = dataState
      Object.assign(this.mainData, temp)
      this.mainData.applyDeptName = this.mainData.createDeptName
      this.mainData.applyDeptId = this.mainData.createDeptId
      let year = new Date().getFullYear()
      this.utils.createKvsequence('LSRK' + year, 6).then(value => {
        this.mainData.sequenceCode = value.data.kvsequence
      })

      this.mainData.lawFirmType = this.$route.query.lawFirmType
      this.loading = false
    },
    loadData(dataState, dataId) {
      this.dataState = dataState
      if (this.$route.query.view !== undefined && this.$route.query.view !== '')
        this.view = this.$route.query.view
      if (this.$route.query.type !== undefined && this.$route.query.type !== '')
        this.type = this.$route.query.type
      this.functionId = this.$route.query.functionId
      lawyerFirmApi.queryDataById({id: dataId}).then(res => {
        this.mainData = res.data.data
        this.loading = false
      })
    },
    refresh_() {
      lawyerFirmApi.queryDataById({id: this.mainData.id}).then(res => {
        if (res.data.data !== undefined) {
          this.mainData.lawyerList = res.data.data.lawyerList
          this.loading = false
        }
      })
    },
    save() {
      return new Promise((resolve, reject) => {
        this.mainData.updateTime = new Date()
        lawyerFirmApi.save(this.mainData).then(response => {
          resolve(response)
        }).catch(error => {
          reject(error)
        })
      })
    },
    licenseCodeBlur() {
      lawyerFirm.isBlackList({
        id: this.mainData.id,
        code: this.mainData.licenseCode,
        createOgnId: this.mainData.createOgnId
      }).then((res) => {
        const flag = res.data.data
        if (flag) {
          this.$message({
            showClose: true,
            message: '该律所已被拉黑！',
            type: 'warning'
          })
          this.mainData.licenseCode = null
        }
      })
      lawyerFirmApi.distinct({
        id: this.mainData.id,
        code: this.mainData.licenseCode,
        createOgnId: this.mainData.createOgnId
      }).then((res) => {
        const flag = res.data.data
        if (flag) {
          this.$message({
            showClose: true,
            message: '统一社会信用代码重复！',
            type: 'warning'
          })
          this.mainData.licenseCode = null
        }
      })
    },
    chooseApprovalDeptClick() {
      this.isCheckedUser = false
      this.showUser = false
      this.orgVisible = true
      this.is_Check = false
    },
    annualInspectionChange(newVal) {
      this.mainData.annualInspectionName = this.utils.getDicName(this.NJQKData, newVal)
    },
    beGoodAtDomainChange(newVal) {
      if (newVal && newVal.length > 0) {
        const arr = []
        for (let i = 0; i < newVal.length; i++) {
          const newValElement = newVal[i]
          const name = this.utils.getDicName(this.SCLYData, newValElement)
          arr.push(name)
        }
        this.mainData.beGoodAtDomain = arr.join('、')
      }
    },
    isHeadquarter() {
      const createOgnId = this.orgContext.currentOgnId

      // if (createOgnId === '15033708970596') {
      this.typeCode = '1'
      this.typeName = '推荐库'
      // } else {
      //   this.typeCode = '0'
      //   this.typeName = '资源库'
      // }
    },
    setLawFirmLevel() {
      if (this.mainData.workNumber < 10)
        this.mainData.lawFirmLevel = '小型所'
      else if (this.mainData.workNumber > 10 && this.mainData.workNumber <= 50)
        this.mainData.lawFirmLevel = '中型所'
      else if (this.mainData.workNumber > 50 && this.mainData.workNumber <= 100)
        this.mainData.lawFirmLevel = '中大型所'
      else if (this.mainData.workNumber > 100)
        this.mainData.lawFirmLevel = '大型所'
    },
    topLawFirm(val) {
      const firms = ['31110000E00017891P', '31110000E000169525', '31310000425097733Y', '31110000E00016813E',
        '31110000E00016266T', '31110000400834282L', '31110000E00017525U', '31110000E00018675X']

      return firms.indexOf(val) > -1
    },
    save_() {
      this.save().then(() => {
        this.$message.success("保存成功!");
      });
    },
    approval_() {
      this.$refs['dataForm'].validate((valid) => {
        this.licenseCodeBlur()

        debugger
        if (valid) {
          if (this.mainData.lawFirmType === '正式律所') {
            this.mainData.dataState = this.utils.dataState_LAWYER.YRK.name
            this.mainData.dataStateCode = this.utils.dataState_LAWYER.YRK.code
          } else {
            this.mainData.dataState = this.utils.dataState_LAWYER.DXP.name
            this.mainData.dataStateCode = this.utils.dataState_LAWYER.DXP.code
          }

          this.save().then(() => {
            this.$message.success("入库成功!");
          }).then(() => {
            this.mcpLayout.closeTab()
          })
        }
      })
    },
    chooseNoticeDeptClick() {
      this.deptOrgVisible = true
    },
    deptOrgCancel() {
      this.deptOrgVisible = false
    },
    choiceNoticeDeptSure() {
      let c = ''
      let cid = ''
      this.zxcheckedData.forEach((item) => {
        if (c.length === 0) {
          c = c + item.name
          cid = cid + item.unitId
        } else {
          c = c + ',' + item.name
          cid = cid + ',' + item.unitId
        }
      })
      this.mainData.noticeDeptName = c
      this.mainData.noticeDeptId = cid
      this.deptOrgVisible = false
    },
  }
}
</script>

<style scoped>
.el-dialog-div {
  height: 60vh;
  overflow: auto;
}

.el-icon-s-help {
  color: red;
}

.rowCol1 .el-form-item {
  margin-bottom: 0 !important;
}
</style>
