export default {
    "column": [
      {
        "type": "mcp-table",
        "label": "表格",
        "display": true,
        "isView": true,
        "dsId": "unfinished",
        "data": [],
        "options": {
          "sumText": "合计",
          "title": "表格的标题",
          "emptyText": "暂无数据",
          "size": "small",
          "menuType": "text",
          "labelWidth": 80,
          "menuWidth": 135,
          "defaultExpandAll": true,
          "showSummary": false,
          "lazy": false,
          "index": true,
          "indexWidth": 35,
          "indexFixed": true,
          "selection": false,
          "selectionFixed": true,
          "selectionWidth": 30,
          "border": true,
          "stripe": true,
          "showHeader": true,
          "searchShow": true,
          "searchMenuSpan": 6,
          "page": true,
          "addBtn": false,
          "viewBtn": false,
          "editBtn": false,
          "delBtn": false,
          "menu": false,
          "delBtnText": "删除",
          "editBtnText": "编辑",
          "viewBtnText": "查看",
          "menuTitle": "操作",
          "sortable": true,
          "menuPosition": "right",
          "align": "center",
          "menuAlign": "center",
          "searchLabelWidth": 80,
          "column": [
            {
              "label": "任务名称",
              "prop": "name",
              "showColumn": true,
              "editDisabled": false,
              "editDisplay": true,
              "addDisabled": false,
              "addDisplay": true,
              "search": true,
              "searchSpan": 6,
              "searchOrder": 1,
              "order": 1,
              "minWidth": "100",
              "compare": "=",
              "event": {
                "cellContentClick": "\n"
              },
              "isCustomContent": true,
              "cellContentType": "textBtn",
              "slot": true,
              "slotComponent": "{\n\tname: \"customContent\",\n\ttemplate: \"<el-button type='text' @click='goRoute'>{{ row[prop] }}</el-button>\",\n\tdata(){\n\t\treturn {\n\t\t\t\n\t\t}\n\t},\n\tprops: {\n\t\trow: {\n\t\t\ttype: Object,\n\t\t\tdefault: () => {}\n\t\t},\n\t\tprop: {\n\t\t\ttype: String,\n\t\t\tdefault: ''\n\t\t},\n\t\tmainScope: {\n\t\t\ttype: Object,\n\t\t\tdefault: () => {}\n\t\t}\n\t},\n\tmethods: {\n\t\tgoRoute(){\n\t\t\tthis.mainScope.select(this.row)\n\t\t}\n\t}\n}\n\n\n\n\n\n"
            },
            {
              "label": "任务办理人",
              "prop": "assignee",
              "showColumn": true,
              "editDisabled": false,
              "editDisplay": true,
              "addDisabled": false,
              "addDisplay": true,
              "search": false,
              "searchSpan": 8,
              "searchOrder": 1,
              "order": 1,
              "minWidth": "100",
              "event": {
                "cellContentClick": ""
              },
              "type": "select",
              "dicOption": "remote",
              "dicData": [
                {
                  "label": "选项一",
                  "value": 0
                },
                {
                  "label": "选项二",
                  "value": 1
                },
                {
                  "label": "选项三",
                  "value": 2
                }
              ],
              "cascaderItem": [],
              "display": true,
              "props": {
                "label": "label",
                "value": "value"
              },
              "disabled": false,
              "dicQueryConfig": [],
              "dicMethod": "post"
            },
            {
              "label": "创建时间",
              "prop": "claimTime",
              "showColumn": true,
              "editDisabled": false,
              "editDisplay": true,
              "addDisabled": false,
              "addDisplay": true,
              "search": false,
              "searchSpan": 8,
              "searchOrder": 1,
              "order": 1,
              "minWidth": "100",
              "event": {
                "cellContentClick": ""
              }
            }
          ],
          "sumColumnList": [],
          "requestDataOnTableLoad": true
        },
        "page": {
          "total": 10,
          "pagerCount": 7,
          "currentPage": 1,
          "pageSize": 20,
          "pageSizes": [
            1,
            10,
            20,
            50,
            100,
            200,
            210
          ],
          "layout": "total,sizes,prev,pager,next,jumper",
          "background": false
        },
        "events": {
          "onLoad": "this.sourceObject.dataSourceQuery({\n                                callback:{}\n                            });",
          "rowDelete": "this.sourceObject.dataSourceDel()",
          "rowUpdate": "this.sourceObject.dataSourceUpdate()",
          "rowInsert": "this.sourceObject.dataSourceInsert()",
          "searchChange": "//this.sourceObject.query()\nlet page = this.pageObj.currentPage;\nlet pageSize = this.pageObj.pageSize;\nthis.getCmpt(\"mainTable\").getDs().query(\n         {\n\t\t\t \t...params,\n\t\t\t\t pageNum:page,\n\t\t\t\t pageSize:pageSize,\n\t\t\t },(data)=>{\n\t\t\tthis.pageObj.total = data.total\n\t\t\t\t done()\n\t})\n\n\n\n\n\n\n"
        },
        "prop": "1603270878569_34153",
        "componentId": "mainTable"
      }
    ],
    "datasources": [
      {
        "dsId": "unfinished",
        "dsName": "我得待办",
        "requestUrl": {
          "query": "/wfl/query/my/task/unfinished",
          "submit": ""
        },
        "data": [],
        "dsType": "CM",
        "fieldParseURL": "",
        "fields": [
          {
            "fieldName": "name",
            "fieldType": "CLOB",
            "businessName": "任务名称",
            "uiComponent": "4",
            "dictionary": []
          },
          {
            "fieldName": "assignee",
            "fieldType": "String",
            "businessName": "任务办理人",
            "uiComponent": "4",
            "dictionary": []
          },
          {
            "fieldName": "claimTime",
            "fieldType": "Datetime",
            "businessName": "创建时间",
            "uiComponent": "3",
            "dictionary": []
          }
        ],
        "type": "mcp-data-source",
        "isView": true
      }
    ]
  }