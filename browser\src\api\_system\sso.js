import {request} from '@/api/index'

export default {
    getAuthCode(data) {
        return request({
            url: '/sso/getAuthCode?param='+data.param +data.uri_,
            method: 'get'
        })
    },
    getAuthCode1(data) {
        return request({
            url: '/sso/getAuthCode1?param='+data.param +data.uri_,
            method: 'get'
        })
    },
    getLogOutUrl() {
        return request({
            url: '/sso/getLogOutUrl',
            method: 'post'
        })
    },
    tianyancha(data) {
        return request({
            url: '/sso/tianyancha',
            method: 'post',
            data
        })
    },
    getWkLoginUrl(data) {
        return request({
            url: '/sso/getWkLoginUrl',
            method: 'post',
            data
        })
    },

}
