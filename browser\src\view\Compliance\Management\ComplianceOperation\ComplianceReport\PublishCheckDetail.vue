<template>
	<div>
		<FormWindow ref="formWindow" @initData="initData" @loadData="loadData">
			<el-container v-loading="loading" style="height: calc(100vh - 84px)" :element-loading-text="loadingText">
				<el-main>
					<el-scrollbar style="height: 100%" wrap-style="overflow-x: hidden;">
						<!--数据表单块-->
						<el-form ref="dataForm" :model="mainData" :rules="!isView ? rules : {}" label-width="100px"
							:style="mainData.dataStateCode === utils.dataState_BPM.FINISH.code ? 'margin-right: 10px;' : ' margin-right: 10px;'">
							<el-row
								style="padding: 10px 0 10px 0; position: fixed; width: 100%; overflow-y: auto; background-color: white; z-index: 999">
								<el-button
									:disabled="mainData.reviewCategory == 'HG-SCLX-HTHG' && mainData.relatedSignificantReview"
									v-if="!isView && !(mainData.reviewCategory == 'HG-SCLX-HTHG' && mainData.relatedSignificantReview)"
									type="primary" size="mini" @click="save_">
									保存
								</el-button>
								<el-button v-if="!isView" type="success" size="mini"
									@click="approval_">生成审批单</el-button>
							</el-row>
							<div style="padding-top: 50px"></div>

							<!--基础信息块-->
							<div v-if="dataState !== 'view'">
								<span style="text-align: left; font-size: 23px; margin-left: 43%; font-weight: 900">{{
									this.$route.query.title }}</span>
								<div
									style="padding-left: 10px; margin-bottom: 20px; font-size: 15px; color: red; font-weight: bolder">
								</div>
								<div style="margin: 10px">
									<span style="font-weight: bold; font-size: 16px; color: #5a5a5f">基本信息</span>
								</div>
								<el-divider />
								<el-row style="margin-top: 10px">
									<el-col :span="16">
										<el-form-item label="检查名称" prop="chackInfo.itemName">
											<el-input v-if="!isView" v-model="mainData.chackInfo.itemName"
												maxlength="100" show-word-limit placeholder="请输入..." clearable />
										</el-form-item>
									</el-col>
									<el-col :span="8">
										<el-form-item label="事项编码" prop="itemCode">
											<el-input v-if="!isView" v-model.trim="mainData.chackInfo.itemCode" disabled
												maxlength="100" show-word-limit clearable />
											<span v-else class="viewSpan">{{ mainData.chackInfo.itemCode }}</span>
										</el-form-item>
									</el-col>
								</el-row>
								<el-row>
									<el-col :span="8">
										<el-form-item :label="'计划开始\n检查时间'" class="fold_label"
											prop="chackInfo.plannedStartTime">
											<el-date-picker v-model="mainData.chackInfo.plannedStartTime"
												value-format="yyyy-MM-dd" type="date" style="width: 100%" />
										</el-form-item>
									</el-col>
									<el-col :span="8">
										<el-form-item :label="'计划结束\n检查时间'" class="fold_label"
											prop="chackInfo.plannedEndTime">
											<el-date-picker v-model="mainData.chackInfo.plannedEndTime"
												value-format="yyyy-MM-dd" type="date" style="width: 100%" />
										</el-form-item>
									</el-col>
									<el-col :span="8">
										<el-form-item label="被检查单位" prop="chackInfo.inspectedUnitName">
											<el-input v-model="mainData.chackInfo.inspectedUnitName" clearable
												placeholder="请选择..." maxlength="50" show-word-limit disabled>
												<el-button slot="append" icon="el-icon-search" @click="choiceOrg()" />
											</el-input>
										</el-form-item>
									</el-col>
									<el-col :span="16">
										<el-form-item label="参与部门" prop="chackInfo.involvedDepartmentList">
											<el-input v-if="dataState !== 'view'"
												v-model="mainData.chackInfo.involvedDepartmentList" clearable
												placeholder="请选择" class="input-with-select" disabled>
												<el-button slot="append" icon="el-icon-search" @click="choiceOrg1()" />
											</el-input>
										</el-form-item>
									</el-col>
									<el-col :span="24">
										<el-form-item label="检查通知" prop="chackInfo.inspectionNotice">
											<span slot="label">检查通知</span>
											<!--<el-input v-if="!isView" v-model="mainData.chackInfo.inspectionNotice"
												:autosize="{ minRows: 3, maxRows: 20 }" type="textarea"
												placeholder="请输入检查要点" maxlength="500" show-word-limit />-->
											<UploadDoc :files.sync="mainData.chackInfo.inspectionNotice"
													   doc-path="/case" :disabled="isView" />
										</el-form-item>
									</el-col>
									<el-col :span="24">
										<el-form-item label="相关附件">
											<UploadDoc :files.sync="mainData.chackInfo.relatedAttachmentsCreater"
												doc-path="/case" :disabled="isView" />
										</el-form-item>
									</el-col>
								</el-row>
							</div>

							<el-dialog :close-on-click-modal="false" title="选择部门" :visible.sync="orgVisible1"
								width="50%">
								<div class="el-dialog-div">
									<orgTree :accordion="false" :is-checked-user="false" :show-user="false"
										:is-check="true" :checked-data.sync="zxcheckedData" :is-not-cascade="true"
										:is-filter="true" />
								</div>
								<span slot="footer" class="dialog-footer">
									<el-button icon="" class="negative-btn" @click="orgCancel1">取消</el-button>
									<el-button type="primary" icon="" class="active-btn"
										@click="orgSure1">确定</el-button>
								</span>
							</el-dialog>

							<el-dialog :close-on-click-modal="false" title="选择组织" :visible.sync="orgVisible"
								width="50%">
								<div class="el-dialog-div">
									<orgTree :accordion="false" :is-checked-user="false" :show-user="false"
										:is-check="false" :checked-data.sync="checkedData" :is-not-cascade="true"
										:is-filter="true" />
								</div>
								<span slot="footer" class="dialog-footer">
									<el-button icon="" class="negative-btn" @click="orgCancel">取消</el-button>
									<el-button type="primary" icon="" class="active-btn" @click="orgSure">确定</el-button>
								</span>
							</el-dialog>

							<!-- 查看时的判断 -->
							<div v-if="dataState == 'view'">
								<span style="text-align: left; font-size: 23px; margin-left: 43%; font-weight: 900">{{
									this.$route.query.type }}</span>
								<SimpleBoardTitle title="基本信息" style="margin-top: 5px">
									<table class="table_content" style="margin-top: 10px">
										<tbody>
											<tr>
												<th colspan="3" class="th_label">检查名称</th>
												<td colspan="13" class="td_value">{{ mainData.chackInfo.itemName }}</td>
												<th colspan="3" class="th_label">事项编码</th>
												<td colspan="5" class="td_value">{{ mainData.chackInfo.itemCode }}</td>
											</tr>
											<tr>
												<th colspan="3" class="th_label">计划开始检查时间</th>
												<td colspan="5" class="td_value">{{ mainData.chackInfo.plannedStartTime}}</td>
												<th colspan="3" class="th_label">计划结束检查时间</th>
												<td colspan="5" class="td_value">{{ mainData.chackInfo.plannedEndTime }}</td>
												<th colspan="3" class="th_label">被检查单位</th>
												<td colspan="5" class="td_value">{{ mainData.chackInfo.inspectedUnitName}}</td>
											</tr>
											<tr>
												<th colspan="3" class="th_label">参与部门</th>
												<td colspan="21" class="td_value">{{mainData.chackInfo.involvedDepartmentList }}</td>
											</tr>
											<tr>
												<th colspan="3" class="th_label">检查通知</th>
												<td colspan="21" class="td_value">
													<UploadDoc
															:files.sync="mainData.chackInfo.inspectionNotice"
															doc-path="/case" :disabled="isView" />
												</td>
											</tr>
											<tr>
												<th colspan="3" class="th_label">相关附件</th>
												<td colspan="21" class="td_value">
													<UploadDoc
														:files.sync="mainData.chackInfo.relatedAttachmentsCreater"
														doc-path="/case" :disabled="isView" />
												</td>
											</tr>
										</tbody>
									</table>
								</SimpleBoardTitle>
							</div>

							<!-- 选择重大事项 -->
							<SectionDataDialog :dialog-visible.sync="sectionVisible" @sectionSure="sectionSure" />
							<!--公共信息-->
							<OtherInfo :data.sync="mainData" :main-id="mainData.id" :data-state="dataState"
								style="bottom: 0; width: 100%; margin-top: 20px" />
						</el-form>

					</el-scrollbar>
				</el-main>
				<!-- 选择模版 -->
				<!-- <Shortcut :templateShow="templateShow" @templateClick="templateClick" /> -->
			</el-container>
		</FormWindow>
	</div>
</template>

<script>
// vuex缓存数据
import { mapGetters } from 'vuex';

// 接口api
// import complianceReviewApi from '@/api/ComplianceReview/complianceReview.js';
import HgjcApi from '@/api/Hgjc/Hgjc.js';
import taskApi from '@/api/_system/task';
import commonApi from '@/api/_system/common';
import SimpleBoardTitle from '@/view/components/SimpleBoard/SimpleBoardTitle'
// 组件
import FormWindow from '@/view/components/FormWindow/FormWindow';
import OtherInfo from '@/view/litigation/caseManage/case/caseChild/OtherInfo';
import OrgSingleDialogSelect from '@/view/components/OrgSingleDialogSelect/index';
import ProsecutionDialog from './ProsecutionDialog';
import QsBaseInfo from './qisubaseInfo';
import CaseData from '@/view/litigation/caseManage/case/caseChild/CaseData';
import CaseEvidenceData from '@/view/litigation/caseManage/case/caseChild/CaseEvidenceData';
import Shortcut from '@/view/litigation/caseManage/caseExamine/Shortcut';
import SimpleBoardTitleApproval from '@/view/components/SimpleBoard/SimpleBoardTitleApproval';
import UploadDoc from '@/view/components/UploadDoc/UploadDoc.vue';
import SectionDataDialog from './dialog/SelectionDialog.vue';
import orgTree from '@/view/components/OrgTree/OrgTree';

export default {
	name: 'PublishCheckDetail',
	inject: ['layout', 'mcpLayout'],
	components: {
		SimpleBoardTitle,
		SimpleBoardTitleApproval,
		CaseData,
		QsBaseInfo,
		ProsecutionDialog,
		OrgSingleDialogSelect,
		FormWindow,
		OtherInfo,
		CaseEvidenceData,
		Shortcut,
		UploadDoc,
		SectionDataDialog,
		orgTree,
	},
	computed: {
		...mapGetters(['orgContext']),
		isView: function () {
			return this.dataState === this.utils.formState.VIEW;
		},
		templateShow() {
			return this.mainData.dataStateCode === this.utils.dataState_BPM.SAVE.code && this.dataState !== this.utils.formState.VIEW;
		},
	},
	data() {
		// 时间的校验
		const validateStartTime = (rule, value, callback) => {
			if (value === undefined) {
				callback(new Error('开始时间不能为空'))
			} else {
				if (new Date(this.mainData.chackInfo.plannedStartTime).getTime() <= new Date().getTime()) {
					callback()
				} else {
					callback()
				}
			}
		};
		const validateEndTime = (rule, value, callback) => {
			if (value === undefined) {
				callback(new Error('结束时间不能为空'))
			} else {
				if (
					new Date(this.mainData.chackInfo.plannedStartTime).getTime() >=
					new Date(this.mainData.chackInfo.plannedEndTime).getTime()
				) {
					callback(new Error('截止时间必须大于开始时间！'))
				} else {
					callback()
				}
			}
		};
		return {
			sectionVisible: false,
			orgVisible1: false,
			orgVisible: false,
			checkedData: [],
			zxcheckedData: [],
			businessAreaData: [],
			radio: true,
			radio1: true,
			title: null,
			type: null,
			tabId: null,
			oarecordsDialog: false,
			loading: false,
			dataState: null,
			functionId: null, //终止的时候要用，需要手动关闭
			dataId: null,
			taskId: null,

			view: 'old',
			mainData: {
				chackInfo: {
					checkType: this.$route.query.type == '1' ? '合规专项检查' : '合规有效性评价',
					itemName: null,
					itemCode: null,
					plannedStartTime: new Date().toISOString().substring(0, 10),
					plannedEndTime: null,
					inspectedUnitName: null,
					inspectedUnitCode: null,
					involvedDepartmentList: null,
					involvedDepartmentListCode: null,
					inspectionNotice: null,
					relatedAttachmentsCreater: null,
					chackInfoIsView: true,
				},
				isSubmit: false,
				currentProcess: null,
				currentHandler: null,
				currentProcessType: null,
				currentProcessId: null,
				currentCheckNode: null,
				currentTaskType: null,
				reviewResult: {
					reviewResultIsView: true,
					inspectionResult: null,
					needsRectification: null,
					latestRectificationTime: null,
					relatedAttachments: null,
				},
				rectificationPlan: {
					rectificationPlanIsView: true,
					plans: [],
				},
				rectificationResult: {
					rectificationResultPlanIsView: true,
					rectificationSituation: null,
					rectificationStatus: null,
					rectificationReport: null,
				},
				id: null, //主键
				createOgnId: null, //当前机构ID
				createOgnName: null, //当前机构名称
				createDeptId: null, //当前部门ID
				createDeptName: null, //当前部门名称
				createGroupId: null, //当前部门ID
				createGroupName: null, //当前部门名称
				createPsnId: null, //当前人ID
				createPsnName: null, //当前人名称
				createOrgId: null, //当前组织ID
				createOrgName: null, //当前组织名称
				createPsnFullId: null, //当前人全路径ID
				createPsnFullName: null, //当前人全路径名称
				createPsnPhone: null, //经办人电话
				createTime: null, //创建时间
				// auditStatus: this.utils.dataState_BPM.SAVE.name, //状态
				dataState: this.utils.dataState_BPM.SAVE.name, //数据状态
				dataStateCode: this.utils.dataState_BPM.SAVE.code, //数据状态编码
			},
			orgTreeDialog: false,
			orgDialogTitle: '组织信息',
			isAssign: false,
			prosecutionDialog: false,
			rules: {
				'chackInfo.itemName': [{ required: true, message: '请输入检查名称', trigger: 'blur' }],
				'chackInfo.plannedStartTime': [{ required: true, validator: validateStartTime, message: '请选择计划开始检查时间', trigger: 'blur' }],
				'chackInfo.plannedEndTime': [{ required: true, validator: validateEndTime, message: '请选择预计检查结束时间并且检查结束时间需大于开始时间', trigger: 'blur' }],
				'chackInfo.inspectedUnitName': [{ required: true, message: '请选择被检查单位', trigger: 'blur' }],
				'chackInfo.inspectionNotice': [{ required: true, message: '请上传检查通知', trigger: 'blur' }],
				'chackInfo.involvedDepartmentList': [{ required: true, message: '请选择参与部门', trigger: 'blur' }],
			},
			activity: null, //记录当前待办处于流程实例的哪个环节
			obj: {
				// 流程处理逻辑需要的各种参数
				taskId: null,
				processInstanceId: null,
				businessKey: null,
				title: null,
				functionName: null,
				sid: null,
			},
			noticeParams: {},
			noticeData: {
				moduleName: '', // 模块名称
				dataId: '', // 数据ID
				url: '', // 地址
				title: '', // 地址
				params: {}, // 其他参数
			},
			loadingText: '加载中...',
		};
	},
	provide() {
		return {
			parentCase: this,
		};
	},
	methods: {
		initData(temp, dataState) {
			let year = new Date().getFullYear();
			if (this.$route.query.type == '1') {
				this.utils.createKvsequence('HGZX' + year, 6).then((value) => {
					this.mainData.chackInfo.itemCode = value.data.kvsequence;
					console.log("this.mainData.chackInfo.itemCode3 ", this.mainData.chackInfo.itemCode)
				});
			} else {
				this.utils.createKvsequence('HGPJ' + year, 6).then((value) => {
					this.mainData.chackInfo.itemCode = value.data.kvsequence;
					console.log("this.mainData.chackInfo.itemCode3 ", this.mainData.chackInfo.itemCode)
				});
			}
			this.dataState = dataState;
			Object.assign(this.mainData, temp);
			this.mainData.reviewCategory = this.$route.query.reviewCategory;
			this.title = this.utils.getDicName(this.utils.compliance_review_type, this.mainData.reviewCategory);
			let obj = this.utils.getManagementUnit(this.mainData.createPsnFullName, this.mainData.createPsnFullId);
			this.mainData.managementUnit = obj.name;
			this.mainData.managementUnitId = obj.id;
			this.mainData.unitType = obj.unitType;
			this.mainData.unitTypeId = obj.unitTypeId;
			commonApi
				.createKvsequence({
					key: 'AJ' + new Date().getFullYear(),
					length: 5,
				})
				.then((res) => {
					this.mainData.reviewNumber = res.data.kvsequence;
				});
			this.view = 'old';
			const code = ['businessDomainDic'];
			this.utils.getDic(code).then((response) => {
				this.businessAreaData = response.data.data[code[0]];
			});

			// this.mainData.currentUnit = this.mainData.createOgnName;
			// this.mainData.currentUnitId = this.mainData.createOgnId;

			this.mainData.riskDepartment = this.mainData.createOgnName; //有点问题
			this.mainData.riskDepartmentId = this.mainData.createOgnId;

			const interCode = 'AJ_GC_CLMC_QS';
			const codes = [interCode];
			this.utils.getDic(codes).then((response) => {
				const datas = response.data.data[codes[0]].filter((item) => item.whetherSolidified === true);
				if (datas && datas.length > 0) {
					datas.forEach((item, index) => {
						const data = this.childData(index);
						data.name = item.dicName;
						data.whetherSys = true;
						this.mainData.otherDataList.push(data);
					});
				}
			});
			this.loading = false;

			
		},
		loadData(dataState, dataId) {
			this.title = this.$route.title
			this.functionId = this.$route.query.functionId;
			if (this.$route.query.view !== undefined && this.$route.query.view !== '') this.view = this.$route.query.view;
			if (this.$route.query.type !== undefined && this.$route.query.type !== '') this.type = this.$route.query.type;
			this.dataState = dataState;
			HgjcApi.queryById(dataId).then((response) => {
				this.mainData = response.data.data;
				this.loading = false;
			});
		},
		// },
		sectionSure(val) {
			this.mainData.relatedSignificantReview = val.reviewNumber;
			this.sectionVisible = false;
		},
		save() {
			return new Promise((resolve, reject) => {
				HgjcApi.save(this.mainData)
					.then((response) => {
						resolve(response);
					})
					.catch((error) => {
						reject(error);
					});
			});
		},
		choiceOrg() {
			this.orgVisible = true;
		},
		choiceOrg1() {
			this.orgVisible1 = true;
		},
		orgCancel1() {
			this.orgVisible1 = false;
		},
		orgCancel() {
			this.orgVisible = false;
		},
		orgSure1() {
			let c = '';
			let cid = '';
			this.zxcheckedData.forEach((item) => {
				if (c.length === 0) {
					c = c + item.name;
					cid = cid + item.unitId;
				} else {
					c = c + ',' + item.name;
					cid = cid + ',' + item.unitId;
				}
			});
			this.mainData.chackInfo.involvedDepartmentList = c;
			this.mainData.chackInfo.involvedDepartmentListCode = cid;
			this.orgVisible1 = false;
		},
		orgSure() {
			const res = this.checkedData[0];
			console.log(res, 'res');
			this.mainData.chackInfo.inspectedUnitName = res.name;
			this.mainData.chackInfo.inspectedUnitCode = res.unitId;
			this.orgVisible = false;
		},
		submit() {
			return new Promise((resolve, reject) => {
				//判断 reporter 和 reportingUnit 是否有空值
				if (!this.mainData.reporter || !this.mainData.reportingUnit) {
					this.mainData.reporter = this.mainData.createPsnName; // 上报人
					this.mainData.reportingUnit = this.mainData.createDeptName; // 上报单位
				} else {
					this.mainData.reporter = this.mainData.reporter; // 上报人
					this.mainData.reportingUnit = this.mainData.reportingUnit; // 上报单位
				}
				this.mainData.reviewStatus = '已提交';

				// 获取当前时间并格式化
				// const now = new Date();
				// const year = now.getFullYear();
				// const month = String(now.getMonth() + 1).padStart(2, '0');
				// const day = String(now.getDate()).padStart(2, '0');
				// const hours = String(now.getHours()).padStart(2, '0');
				// const minutes = String(now.getMinutes()).padStart(2, '0');
				// const seconds = String(now.getSeconds()).padStart(2, '0');

				// this.mainData.reportTime = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
				this.mainData.reportTime = new Date();
				HgjcApi.save(this.mainData)
					.then((response) => {
						resolve(response);
					})
					.catch((error) => {
						reject(error);
					});
			});
		},

		//选择模板
		templateClick(val) {
			if (val) {
				this.prosecutionDialog = true;
			}
		},

		approval_() {
			this.$refs['dataForm'].validate((valid) => {
				if (valid) {
					this.mainData.isSubmit = true
					this.save()
						.then(() => {
							const tabId = this.mainData.id;
							if (this.mainData.dataStateCode == this.utils.dataState_BPM.SAVE.code) {
								if (this.$route.query.type == '合规专项检查') {
									taskApi.selectFunctionId({ functionCode: 'hgzxjh_main' }).then((res) => {
										const functionId = res.data.data[0].ID;
										this.layout.openNewTab('合规专项检查', 'design_page', 'design_page', tabId, {
											...this.utils.routeState.NEW(tabId),
											functionId: functionId,
											businessKey: tabId,
											entranceType: 'FLOWABLE',
											create: 'create',
											view: 'new',
										});
									});
								} else {
									taskApi.selectFunctionId({ functionCode: 'hgyxxpj_main' }).then((res) => {
										const functionId = res.data.data[0].ID;
										this.layout.openNewTab('合规有效性评价', 'design_page', 'design_page', tabId, {
											...this.utils.routeState.NEW(tabId),
											functionId: functionId,
											businessKey: tabId,
											entranceType: 'FLOWABLE',
											create: 'create',
											view: 'new',
										});
									});
								}
							} else {
								const tabId = this.mainData.id;
								taskApi.selectTaskId({ businessKey: tabId, isView: '' }).then((res) => {
									const functionId = res.data.data[0].ID;
									const uuid = this.utils.createUUID();
									this.layout.openNewTab('案件风险告知审批信息', 'design_page', 'design_page', uuid, {
										processInstanceId: res.data.data[0].PID, //流程实例
										taskId: res.data.data[0].ID, //任务ID
										businessKey: tabId, //业务数据ID
										functionId: functionId, //不传也可以，只有首环节需要传，实例启动就可以不传了
										entranceType: 'FLOWABLE',
										type: 'toDeal',
										view: 'new',
										create: 'create',
									});
								});
							}
						})
						.then(() => {
							this.mcpLayout.closeTab();
						});
				}
			});
		},
		save_() {
			this.$refs.dataForm.validate((valid) => {
			if (valid) {
				// 校验通过，执行保存逻辑
				this.mainData.submit = false;
				this.save().then(() => {
				this.$message.success('保存成功!');
				});
			} else {
				// 校验未通过，提示用户
				//被检查单位不填就报错
				this.$message.error('表单填写不完整，请检查后重新保存!');
			}
			});
		},
		submit_() {
			this.$refs['dataForm'].validate((valid) => {
				if (valid) {
					this.submit().then(() => {
						this.mainData.reviewStatus = '已提交';
						this.$message.success('提交成功!');
					});
				} else {
					this.$message.error('表单填写不完整，请检查后重新提交!');
				}
			});
		},
		saveCurrentTime() {
			const now = new Date();
			const year = now.getFullYear();
			const month = String(now.getMonth() + 1).padStart(2, '0');
			const day = String(now.getDate()).padStart(2, '0');
			const hours = String(now.getHours()).padStart(2, '0');
			const minutes = String(now.getMinutes()).padStart(2, '0');
			const seconds = String(now.getSeconds()).padStart(2, '0');
		},

	},
};
</script>

<style scoped>
/* 过于长的label分两行展示样式 */
/deep/.fold_label .el-form-item__label {
	white-space: pre-line;
	/* text-align-last: justify; 
  text-align: justify;*/
	margin-top: -4px;
	line-height: 23px;
	text-justify: distribute-all-lines;
}
</style>
