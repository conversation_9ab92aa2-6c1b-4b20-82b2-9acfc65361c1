
d63dccc96e283e93b4e3aef5b1bbf783c77ad309	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.1.1754018536329.js\",\"contentHash\":\"842e1c089519f20377de6682e80270f1\"}","integrity":"sha512-XubWzJ0CggXMpYDP5pR+iSAStfPYaq4iQSasJ3ceMC6k8cXpVAQPq4fIFwbMoQw8SxCrcPaRsiVtwshhFCE0Zw==","time":1754018575806,"size":58947}