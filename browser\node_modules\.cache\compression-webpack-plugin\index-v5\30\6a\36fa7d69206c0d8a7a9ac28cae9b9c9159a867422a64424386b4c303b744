
4fc2b42541b679e2d192fc39055515d332530c36	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.390.1754018536329.js\",\"contentHash\":\"eda977b70151dcbd9ebae7a7c761beab\"}","integrity":"sha512-FxPKqCfT7IIdjiqu6oEd8wobcqVjITOu6lWxUUFGisjPxXchpeXz0ESBRe6ithX7p3l33rZQ6gaaQplrGNPEsQ==","time":1754018575976,"size":101392}