<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.klaw.dao.mainDataDao.SgPartTimeMapper">


    <select id="queryPageData" resultType="java.util.Map">
        select
            p.code as "code",
            p.staff_code as "staffCode",
            p.staff_name as "staffName",
            p.org_name as "orgName",
            p.state_desc as "stateDesc"
        from
            SG_ORGANIZATION s join
            SG_PART_TIME p
        on
            p.staff_code = s.user_id
        <where>
            ${ew.sqlSegment}
        </where>
    </select>
</mapper>