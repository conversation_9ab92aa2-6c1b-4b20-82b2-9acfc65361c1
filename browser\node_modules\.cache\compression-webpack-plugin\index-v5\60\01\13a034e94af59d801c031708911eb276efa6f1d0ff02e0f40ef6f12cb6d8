
13480eaf15d2632c28ec07a10c8911df85ebb1f7	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.392.1754018536329.js\",\"contentHash\":\"8cd911f2c47ce609a5f128f94cd3b9f9\"}","integrity":"sha512-kkA0JSIm7pgC9fvnEVHD3CQpLcDE6Kfnhf1iTXh1+zcIVd4rW1ZsblgxH2binGxAuuc0Axrzgf9egD7pW02s7Q==","time":1754018575957,"size":34720}