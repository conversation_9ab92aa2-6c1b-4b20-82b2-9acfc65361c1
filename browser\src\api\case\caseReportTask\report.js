import {request} from '@/api/index'
export default {
    reportBefore(data) {
        return request({
            url: '/reporttask/reportBefore',
            method: 'post',
            data
        })
    },
    singleCaseReport(data) {
        return request({
            url: '/reporttask/singleCaseReport',
            method: 'post',
            data
        })
    },
    report(data) {
        return request({
            url: '/reporttask/report',
            method: 'post',
            data
        })
    },
    queryById(data) {
        return request({
            url: '/reporttask/queryById',
            method: 'post',
            data
        })
    },
    save(data) {
        return request({
            url: '/reporttask/save',
            method: 'post',
            data
        })
    },
    exportCurrentYear(data) {
        return request({
            url: '/reporttask/exportCurrentYear',
            method: 'post',
            responseType: 'blob',
            data
        })
    },
}
