
1272ccd6e8acf79ffea1798db40140b36dbfffd8	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.181.1754018536329.js\",\"contentHash\":\"121d67debd4922e851b082c560ec4284\"}","integrity":"sha512-0I1V4fzn3w1s/8bRN4gK9ZObFEmY+zDEveRyMICpFFfxNl1WClf4BYOdG/bLILWvat2EWkTSnlw0b8zz5Nzjxg==","time":1754018576096,"size":268417}