<template>
  <div>
    <FormWindow ref="formWindow" @initData="initData" @loadData="loadData">
      <el-container v-loading="loading" :element-loading-text="loadingText" style="height: calc(100vh - 84px)">
        <el-main>
          <el-scrollbar style="height: 100%" wrap-style="overflow-x: hidden;">
            <!--数据表单块-->
            <el-form ref="dataForm" :model="{mainData,trainingCourseDetailsList}" :rules="!isView ? rules : {}"
                     :style="mainData.dataStateCode === utils.dataState_BPM.FINISH.code ? 'margin-right: 10px;' : ' margin-right: 10px;'"
                     label-width="100px">
              <el-row
                  style="padding: 10px 0 10px 0; position: fixed; width: 100%; overflow-y: auto; background-color: white; z-index: 999">
                <!-- <el-button v-if="!isView" type="success" size="mini" @click="approval_">生成审批单</el-button> -->
                <el-button v-if="!isView" size="mini" type="primary" @click="save_"> 提交</el-button>
              </el-row>
              <div style="padding-top: 50px"></div>
              <span
                  style="text-align: left; font-size: 23px; margin-left: 43%; font-weight: 900">合规培训</span>
              <!--基础信息块-->
              <div v-if="dataState !== 'view'">
                <div
                    style="padding-left: 10px; margin-bottom: 20px; font-size: 15px; color: red; font-weight: bolder">
                </div>
                <div style="margin: 10px">
                  <span style="font-weight: bold; font-size: 16px; color: #5a5a5f">基本信息</span>
                  <el-divider></el-divider>
                </div>
                <el-row style="margin-top: 10px">
                  <el-col :span="8">
                    <el-form-item label="编码">
                      <el-input v-if="!isView" v-model.trim="mainData.code" disabled
                                show-word-limit style="width: 100%"/>
                      <span v-else class="viewSpan">{{ mainData.code }}</span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="16">
                    <el-form-item label="培训主题" prop="mainData.trainingTopic">
                      <el-input v-model="mainData.trainingTopic" clearable maxlength="100"
                                placeholder="请输入..." show-word-limit/>
                    </el-form-item>
                  </el-col>

                  <el-col :span="8">
                    <el-form-item label="培训分类" prop="mainData.trainingCategory">
                      <el-select v-model="mainData.trainingCategory" clearable placeholder="请选择"
                                 style="width: 100%">
                        <el-option key="1" label="党委中心组专题合规学习" value="dwzxzzthgxx"></el-option>
                        <el-option key="2" label="管理层（董监高）培训学习" value="glcpxxx"></el-option>
                        <el-option key="3" label="合规管理员培训学习" value="hgglypxxx"></el-option>
                        <el-option key="4" label="新员工入职培训学习" value="xygrzpxxx"></el-option>
                        <el-option key="5" label="全员普适性培训宣贯" value="qypsxpxxg"></el-option>
                        <el-option key="6" label="其他" value="qt"></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="培训开始时间" prop="mainData.trainingStartTime">
                      <el-date-picker v-model="mainData.trainingStartTime" clearable
                                      maxlength="20" placeholder="请输入..." show-word-limit
                                      style="width: 100%"/>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="培训结束时间" prop="mainData.trainingEndTime">
                      <el-date-picker v-model="mainData.trainingEndTime" clearable
                                      maxlength="20" placeholder="请输入..." show-word-limit
                                      style="width: 100%"/>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="业务领域" prop="mainData.businessField">
                      <el-select v-model="mainData.businessField" clearable placeholder="请选择"
                                 style="width: 100%" @change="businessFieldChange">
                        <el-option v-for="item in businessFieldData" :key="item.dicCode"
                                   :label="item.dicName" :value="item.dicName"/>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="培训地点类型" prop="mainData.trainingLocationType">
                      <el-select v-model="mainData.trainingLocationType" style="width: 100%;">
                        <el-option key="1" label="内部" value="nb"></el-option>
                        <el-option key="2" label="外部" value="wb"></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="培训地点" prop="mainData.trainingLocation">
                      <el-input v-model="mainData.trainingLocation" clearable
                                maxlength="100" placeholder="请输入..." show-word-limit/>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="培训形式" prop="mainData.trainingForm">
                      <el-select v-model="mainData.trainingForm" style="width: 100%;">
                        <el-option key="1" label="线上" value="xs"></el-option>
                        <el-option key="2" label="线下" value="xx"></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="16">
                    <el-form-item label="培训对象" prop="mainData.mandatoryObjectName">
                      <el-input v-if="dataState !== 'view'" v-model="mainData.mandatoryObjectName"
                                class="input-with-select" clearable disabled placeholder="请选择">
                        <el-button slot="append" icon="el-icon-search"
                                   @click="chooseNoticeDeptClick"/>
                      </el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="培训讲师" prop="mainData.trainingDirector">
                      <el-input v-if="dataState !== 'view'" v-model="mainData.trainingDirector"
                                clearable disabled maxlength="50" placeholder="请选择..." show-word-limit>
                        <el-button slot="append" icon="el-icon-search"
                                   @click="chooiseApprovalDeptClick()"/>
                      </el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="培训人次" prop="mainData.trainingPersonCount">
                      <el-input-number v-model="mainData.trainingPersonCount" :controls="false" clearable
                                       placeholder="请输入..." style="width:100%"/>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="培训课时" prop="mainData.trainingCourseHours">
                      <el-input-number v-model="mainData.trainingCourseHours" :controls="false" clearable
                                       placeholder="请输入..." style="width:100%"/>
                    </el-form-item>
                  </el-col>
                  <el-col :span="24">
                    <el-form-item label="培训介绍" prop="mainData.trainingIntroduction">
                      <span slot="label">培训介绍</span>
                      <el-input v-if="!isView" v-model="mainData.trainingIntroduction"
                                :autosize="{ minRows: 3, maxRows: 20 }" maxlength="500"
                                placeholder="请输入事项描述" show-word-limit type="textarea"/>
                      <text-span v-else :text="mainData.matterDescription" class="viewSpan"/>
                    </el-form-item>
                  </el-col>
                </el-row>
                <!--选择授权单位-->
                <el-dialog :close-on-click-modal="false" :visible.sync="entrustedUnitOrgVisible"
                           title="选择授权单位" width="50%">
                  <div class="el-dialog-div">
                    <orgTree :accordion="false" :checked-data.sync="zxcheckedData" :is-check="false"
                             :is-checked-user="false" :is-filter="true" :is-not-cascade="true"
                             :show-user="false"/>
                  </div>
                  <span slot="footer" class="dialog-footer">
										<el-button class="negative-btn" icon=""
                               @click="entrustedUnitOrgCancel">取消</el-button>
										<el-button class="active-btn" icon="" type="primary"
                               @click="entrustedUnitSure">确定</el-button>
									</span>
                </el-dialog>

                <!--选择部门-->
                <el-dialog :close-on-click-modal="false" :visible.sync="orgVisible" title="选择授权人"
                           width="50%">
                  <div class="el-dialog-div">
                    <orgTree :accordion="false" :checked-data.sync="zxcheckedData"
                             :is-check="is_Check" :is-checked-user="isCheckedUser"
                             :is-filter="true" :is-not-cascade="true"
                             :show-user="showUser"/>
                  </div>
                  <span slot="footer" class="dialog-footer">
										<el-button class="negative-btn" icon="" @click="cancel">取消</el-button>
										<el-button class="active-btn" icon="" type="primary"
                               @click="choiceDeptSure">确定</el-button>
									</span>
                </el-dialog>

                <el-dialog :close-on-click-modal="false" :visible.sync="deptOrgVisible" title="选择人员"
                           width="50%">
                  <div class="el-dialog-div">
                    <orgTree :accordion="false" :checked-data.sync="zxcheckedData" :is-check="true"
                             :is-checked-user="false" :is-filter="true" :is-not-cascade="true"
                             :show-user="true"/>
                  </div>
                  <span slot="footer" class="dialog-footer">
										<el-button class="negative-btn" icon="" @click="deptOrgCancel">取消</el-button>
										<el-button class="active-btn" icon="" type="primary"
                               @click="choiceNoticeDeptSure">确定</el-button>
									</span>
                </el-dialog>

                <el-dialog :close-on-click-modal="false" :visible.sync="deptOrgVisible1" title="选择部门"
                           width="50%">
                  <div class="el-dialog-div">
                    <orgTree :accordion="false" :checked-data.sync="zxcheckedData" :is-check="true"
                             :is-checked-user="false" :is-filter="true" :is-not-cascade="true"
                             :show-user="true"/>
                  </div>
                  <span slot="footer" class="dialog-footer">
										<el-button class="negative-btn" icon="" @click="deptOrgCancel1">取消</el-button>
										<el-button class="active-btn" icon="" type="primary"
                               @click="choiceNoticeDeptSure1">确定</el-button>
									</span>
                </el-dialog>
                 <el-row>
                  <el-col :span="24">
                    <el-form-item label="审查资料">
                      <uploadDoc :files.sync="mainData.reviewMaterials" doc-path="/case" :disabled="isView" />
                    </el-form-item>
                  </el-col>
                </el-row>
                <!-- 第二部分 -->
<!--                <div-->
<!--                    style="padding-left: 10px; margin-bottom: 20px; font-size: 15px; color: red; font-weight: bolder">-->
<!--                </div>-->
<!--                <div style="margin: 10px">-->
<!--                  <span style="font-weight: bold; font-size: 16px; color: #5a5a5f">培训内容</span>-->
<!--                  <el-divider></el-divider>-->
<!--                </div>-->
<!--                <div style="display: inline;float: right;margin-top: 10px;margin-right: 30px">-->

<!--&lt;!&ndash;                  <el-link size="mini" style="margin-right: 12px;" type="primary" @click="addRow">新增</el-link>&ndash;&gt;-->
<!--                  <el-link v-show="this.currentRow.trainingCourse!=null" size="mini" style="margin-right: 10px;"-->
<!--                           type="danger" @click="delRow">删除-->
<!--                  </el-link>-->
<!--                  <el-link v-show="this.currentRow.trainingCourse!=null" size="mini" style="margin-right: 10px;"-->
<!--                           type="primary" @click="upRow">上移-->
<!--                  </el-link>-->
<!--                  <el-link v-show="this.currentRow.trainingCourse!=null" size="mini" style="margin-right: 10px;"-->
<!--                           type="primary" @click="downRow">下移-->
<!--                  </el-link>-->
<!--                </div>-->
                <simple-board :data-state="dataState" :has-value="true" :hasAdd="true"
                              :hasDown="this.currentRow.trainingCourse!=null"
                              :hasUp="this.currentRow.trainingCourse!=null"
                              :hasDle="this.currentRow.trainingCourse!=null"
                              :title="'培训内容'"
                              style="margin-top: 10px"
                              @addBtn="addRow" @delBtn="delRow" @upBtn="upRow" @downBtn="downRow">
                  <el-table ref="table" :data="trainingCourseDetailsList" :show-overflow-tooltip="true"
                            border  class="custom-table" fit highlight-current-row stripe style="width: 100%"
                            @current-change="handleCurrentChange">
                    <el-table-column align="center" label="序号" type="index"></el-table-column>
                    <el-table-column :width="'auto'" header-align="center" label="培训课程" prop="trainingCourse"
                                     show-overflow-tooltip>
                      <template slot-scope="{row, $index}">
                        <el-form-item
                            :class="{ 'has-margin': !row.trainingCourse }"
                            :prop="`trainingCourseDetailsList.${$index}.trainingCourse`"
                            :rules="{ required: true, message: '请输入培训课程名称', trigger: 'blur' }"
                        >
                          <el-input v-model="row.trainingCourse" maxlength="300" placeholder="请输入"
                                    show-word-limit></el-input>
                        </el-form-item>
                      </template>
                    </el-table-column>
                    <el-table-column :width="'auto'" label="培训课件" prop="trainingMaterials" sortable="custom">
                      <template slot-scope="{ row, $index }">
                        <el-form-item
                            :class="{ 'has-margin': !row.trainingMaterials }"
                            :prop="`trainingCourseDetailsList.${$index}.trainingMaterials`"
                            :rules="{ required: true, message: '请上传培训课件', trigger: 'blur' }"
                        >
                          <uploadDoc
                              v-model="row.trainingMaterials"
                              :doc-path="docURL"
                              :files.sync="row.trainingMaterials"
                              :isHasTips=false
                              :limitNum="1"
                              :showPreview=false
                          />
                        </el-form-item>
                      </template>
                    </el-table-column>
                    <el-table-column :width="'auto'" header-align="center" label="课程说明" prop="courseDescription">
                      <template slot-scope="{row, $index}">
                        <el-form-item
                            :class="{ 'has-margin': !row.courseDescription }"
                            :prop="`trainingCourseDetailsList.${$index}.courseDescription`"
                            :rules="{ required: true, message: '请输入课程说明名称', trigger: 'blur' }"
                        >
                          <el-input v-model="row.courseDescription" maxlength="1000" placeholder="请输入"
                                    show-word-limit
                                    type="textarea"></el-input>
                        </el-form-item>
                      </template>
                    </el-table-column>
                  </el-table>
                </simple-board>

                <!-- 第三部分 -->
              </div>

              <!-- 查看时的判断 -->
              <div v-if="dataState == 'view' && this.mainData.reviewCategory == 'HG-SCLX-ZDSX'">
                <SimpleBoardTitle style="margin-top: 5px" title="基本信息">
                  <table class="table_content" style="margin-top: 10px">
                    <tbody>
                    <tr>
                      <th class="th_label" colspan="3">审查主题</th>
                      <td class="td_value" colspan="13">{{ mainData.reviewSubject }}</td>
                      <th class="th_label" colspan="3">审查编号</th>
                      <td class="td_value" colspan="5">{{ mainData.reviewNumber }}</td>
                    </tr>
                    <tr>
                      <th class="th_label" colspan="3">是否已合规审查</th>
                      <td class="td_value" colspan="5">{{
                          mainData.isComplianceReviewed == '1'
                              ? '是' : '否'
                        }}
                      </td>
                      <th class="th_label" colspan="3">议案是否有变化</th>
                      <td class="td_value" colspan="5">{{
                          mainData.hasProposalChanged == '1' ?
                              '是' : '否'
                        }}
                      </td>
                      <th class="th_label" colspan="3">业务领域</th>
                      <td class="td_value" colspan="5">{{ mainData.businessArea }}</td>
                    </tr>
                    <tr>
                      <th class="th_label" colspan="3">事项描述</th>
                      <td class="td_value" colspan="21">{{ mainData.matterDescription }}</td>
                    </tr>
                    <tr>
                      <th class="th_label" colspan="3">附件材料</th>
                      <td class="td_value" colspan="21">
                        <uploadDoc :disabled="isView" :files.sync="mainData.reviewMaterials"
                                   :tips="''" doc-path="/case"/>
                      </td>
                    </tr>
                    </tbody>
                  </table>
                </SimpleBoardTitle>
              </div>

              <!--公共信息-->
              <OtherInfo :data-state="dataState" :data.sync="mainData" :main-id="mainData.id"
                         style="width: 100%; margin-top: 50px"/>
            </el-form>

            <!--案件审批表-->
            <prosecution-dialog :is-multiple="false" :visible.sync="prosecutionDialog"
                                @onSure="prosecutionSelect"/>
          </el-scrollbar>
        </el-main>
        <!-- 选择模版 -->
        <!-- <Shortcut :templateShow="templateShow" @templateClick="templateClick" /> -->
      </el-container>
    </FormWindow>
  </div>
</template>

<script>
// vuex缓存数据
import {mapGetters} from 'vuex';

// 接口api
import ComplianceTrainingApi from '@/api/ComplianceTraining/ComplianceTraining.js';
import taskApi from '@/api/_system/task';
import commonApi from '@/api/_system/common';
import dictApi from '@/api/_system/dict'
// 组件
import FormWindow from '@/view/components/FormWindow/FormWindow';
import OtherInfo from '@/view/litigation/caseManage/case/caseChild/OtherInfo';
import OrgSingleDialogSelect from '@/view/components/OrgSingleDialogSelect/index';
import ProsecutionDialog from './ProsecutionDialog';
import QsBaseInfo from './qisubaseInfo';
import CaseData from '@/view/litigation/caseManage/case/caseChild/CaseData';
import CaseEvidenceData from '@/view/litigation/caseManage/case/caseChild/CaseEvidenceData';
import Shortcut from '@/view/litigation/caseManage/caseExamine/Shortcut';
import SimpleBoardTitleApproval from '@/view/components/SimpleBoard/SimpleBoardTitleApproval';
import uploadDoc from '@/view/components/UploadDoc/UploadDoc';
import SectionDataDialog from './dialog/SelectionDialog.vue';
import orgTree from '@/view/components/OrgTree/OrgTree';
import SimpleBoard from "@/view/components/SimpleBoard/SimpleBoardViewCase.vue";

export default {
  name: 'HgpxxxglMainDetail',
  inject: ['layout', 'mcpLayout'],

  components: {
    SimpleBoard,
    SimpleBoardTitleApproval,
    CaseData,
    QsBaseInfo,
    ProsecutionDialog,
    OrgSingleDialogSelect,
    FormWindow,
    OtherInfo,
    CaseEvidenceData,
    Shortcut,
    uploadDoc,
    SectionDataDialog,
    orgTree
  },
  computed: {
    ...mapGetters(['orgContext']),
    isView: function () {
      return this.dataState === this.utils.formState.VIEW;
    },
    templateShow() {
      return this.mainData.dataStateCode === this.utils.dataState_BPM.SAVE.code && this.dataState !== this.utils.formState.VIEW;
    },
  },
  data() {
    const validateTrainingTime = (rule, value, callback) => {
      console.log(rule)
      console.log(rule.field)
      if (rule.field === 'mainData.trainingStartTime') {
        if (this.mainData.trainingEndTime && this.mainData.trainingStartTime > this.mainData.trainingEndTime) {
          callback(new Error('培训开始时间不得晚于结束时间'));
        } else {
          callback();
        }
      } else if (rule.field === 'mainData.trainingEndTime') {
        if (this.mainData.trainingStartTime && this.mainData.trainingEndTime < this.mainData.trainingStartTime) {
          callback(new Error('培训结束时间不得早于开始时间'));
        } else {
          callback();
        }
      } else {
        callback();
      }
    }
    return {
      zxcheckedData: [],
      docURL: '/case',
      deptOrgVisible: false,
      deptOrgVisible1: false,
      showUser: false,
      is_Check: true,
      isCheckedUser: false,
      orgVisible: false,
      entrustedUnitOrgVisible: false,
      sectionVisible: false,
      businessAreaData: [],
      businessFieldData: [],//业务领域
      radio: true,
      radio1: true,
      title: null,
      type: null,
      tabId: null,
      oarecordsDialog: false,
      loading: false,
      dataState: null,
      functionId: null, //终止的时候要用，需要手动关闭
      dataId: null,
      taskId: null,

      view: 'old',
      mainData: {
        code: null,//编码
        trainingCourseHours: null,//培训课时
        businessField: null,//业务领域
        trainingCourseDetailsList: [], //培训内容
        trainingDirector: null,
        responsibleDepartment: null,
        trainingTopic: null,
        trainingCategory: null,
        trainingStartTime: null,
        trainingEndTime: null,
        trainingLocation: null,
        trainingForm: null,//培训形式
        trainingIntroduction: null,
        trainingPersonCount: null,
        trainingLocationType: null,//培训地点类型
        mandatoryObjectId: null,
        electiveObjectId: null,
        mandatoryObjectName: null,
        electiveObjectName: null,
        id: null, //主键
        reviewCategory: null, //审查类别
        createOgnId: null, //当前机构ID
        createOgnName: null, //当前机构名称
        createDeptId: null, //当前部门ID
        createDeptName: null, //当前部门名称
        createGroupId: null, //当前部门ID
        createGroupName: null, //当前部门名称
        createPsnId: null, //当前人ID
        createPsnName: null, //当前人名称
        createOrgId: null, //当前组织ID
        createOrgName: null, //当前组织名称
        createPsnFullId: null, //当前人全路径ID
        createPsnFullName: null, //当前人全路径名称
        createPsnPhone: null, //经办人电话
        createTime: null, //创建时间
        // auditStatus: this.utils.dataState_BPM.SAVE.name, //状态
        dataState: this.utils.dataState_BPM.SAVE.name, //数据状态
        dataStateCode: this.utils.dataState_BPM.SAVE.code, //数据状态编码
      },
      orgTreeDialog: false,
      orgDialogTitle: '组织信息',
      isAssign: false,
      prosecutionDialog: false,
      rules: {
        "mainData.trainingTopic": [{required: true, message: '请输入培训主题', trigger: 'blur'}],
        "mainData.trainingCategory": [{required: true, message: '请选择培训分类', trigger: 'blur'}],
        "mainData.trainingDirector": [{required: true, message: '请选择培训讲师', trigger: 'blur'}],
        "mainData.mandatoryObjectName": [{required: true, message: '请选择培训对象', trigger: 'blur'}],
        "mainData.trainingLocation": [{required: true, message: '请输入培训地点', trigger: 'blur'}],
        "mainData.trainingStartTime": [
          {required: true, message: '请选择培训开始时间', trigger: 'change'},
          {required: true, validator: validateTrainingTime, trigger: 'change'}
        ],
        "mainData.trainingEndTime": [
          {required: true, message: '请选择培训结束时间', trigger: 'change'},
          {required: true, validator: validateTrainingTime, trigger: 'change'}
        ]
      },
      activity: null, //记录当前待办处于流程实例的哪个环节
      obj: {
        // 流程处理逻辑需要的各种参数
        taskId: null,
        processInstanceId: null,
        businessKey: null,
        title: null,
        functionName: null,
        sid: null,
      },
      noticeParams: {},
      noticeData: {
        moduleName: '', // 模块名称
        dataId: '', // 数据ID
        url: '', // 地址
        title: '', // 地址
        params: {}, // 其他参数
      },
      legalObligations: [],
      currentRow: {},
      currentIndex:{},
      loadingText: '加载中...',
      trainingCourseDetailsList: [],
    };
  },
  created() {
    this.initDic();
    let year = new Date().getFullYear();
    this.utils.createKvsequence('HGPX' + year, 6).then((value) => {
      this.mainData.code = value.data.kvsequence;
    }).catch((error) => {
      console.error('生成编码失败', error);
      this.$message.error('生成编码失败，请稍后再试!');
    });
  },
  provide() {
    return {
      parentCase: this,
    };
  },
  methods: {
    initData(temp, dataState) {
      this.dataState = dataState;
      Object.assign(this.mainData, temp);
      this.mainData.reviewCategory = this.$route.query.reviewCategory;
      // this.title = this.utils.getDicName(this.utils.compliance_review_type, this.mainData.reviewCategory);
      let obj = this.utils.getManagementUnit(this.mainData.createPsnFullName, this.mainData.createPsnFullId);
      this.mainData.managementUnit = obj.name;
      this.mainData.managementUnitId = obj.id;
      this.mainData.unitType = obj.unitType;
      this.mainData.unitTypeId = obj.unitTypeId;
      this.mainData.responsibleDepartment = obj.currentOgnName;
      this.mainData.trainingDirector = obj.currentPsnName;
      commonApi
          .createKvsequence({
            key: 'AJ' + new Date().getFullYear(),
            length: 5,
          })
          .then((res) => {
            this.mainData.reviewNumber = res.data.kvsequence;
          });
      this.view = 'old';
      const code = ['businessDomainDic'];
      this.utils.getDic(code).then((response) => {
        this.businessAreaData = response.data.data[code[0]];
      });
      let year = new Date().getFullYear();
      this.utils.createKvsequence('HGPX' + year, 6).then((value) => {
        this.mainData.code = value.data.kvsequence;
      });
      // this.mainData.currentUnit = this.mainData.createOgnName;
      // this.mainData.currentUnitId = this.mainData.createOgnId;

      this.mainData.riskDepartment = this.mainData.createOgnName; //有点问题
      this.mainData.riskDepartmentId = this.mainData.createOgnId;

      const interCode = 'AJ_GC_CLMC_QS';
      const codes = [interCode];
      this.utils.getDic(codes).then((response) => {
        const datas = response.data.data[codes[0]].filter((item) => item.whetherSolidified === true);
        if (datas && datas.length > 0) {
          datas.forEach((item, index) => {
            const data = this.childData(index);
            data.name = item.dicName;
            data.whetherSys = true;
            this.mainData.otherDataList.push(data);
          });
        }
      });
      this.loading = false;
    },
    loadData(dataState, dataId) {
      this.functionId = this.$route.query.functionId;
      if (this.$route.query.view !== undefined && this.$route.query.view !== '') this.view = this.$route.query.view;
      if (this.$route.query.type !== undefined && this.$route.query.type !== '') this.type = this.$route.query.type;
      this.dataState = dataState;
      ComplianceTrainingApi.queryById(dataId).then((response) => {
        this.mainData = response.data.data;
        this.authorizationData = response.data.data.authorization;
        if (response.data.data.authorization !== null) this.authorizationList = response.data.data.authorization.authorizationLitigationList;
        this.loading = false;
      });
    },
    // },
    sectionSure(val) {
      this.mainData.relatedSignificantReview = val.reviewNumber;
      this.sectionVisible = false;
    },
    save() {
      this.mainData.trainingCourseDetailsList = this.trainingCourseDetailsList;
      return new Promise((resolve, reject) => {
        if (this.mainData.trainingCourseDetailsList.length == 0) {
          this.$message.warning('请录入培训内容');
          return;
        } else {
          ComplianceTrainingApi.save(this.mainData)
              .then((response) => {
                resolve(response);
                this.mcpLayout.closeTab();
              })
              .catch((error) => {
                reject(error);
              });
        }
      });
    },
    // submit() {
    // 	return new Promise((resolve, reject) => {
    // 		//判断 reporter 和 reportingUnit 是否有空值
    // 		if (!this.mainData.reporter || !this.mainData.reportingUnit) {
    // 			this.mainData.reporter = this.mainData.createPsnName; // 上报人
    // 			this.mainData.reportingUnit = this.mainData.createDeptName; // 上报单位
    // 		} else {
    // 			this.mainData.reporter = this.mainData.reporter; // 上报人
    // 			this.mainData.reportingUnit = this.mainData.reportingUnit; // 上报单位
    // 		}
    // 		this.mainData.reviewStatus = '已提交';

    // 		// 获取当前时间并格式化
    // 		// const now = new Date();
    // 		// const year = now.getFullYear();
    // 		// const month = String(now.getMonth() + 1).padStart(2, '0');
    // 		// const day = String(now.getDate()).padStart(2, '0');
    // 		// const hours = String(now.getHours()).padStart(2, '0');
    // 		// const minutes = String(now.getMinutes()).padStart(2, '0');
    // 		// const seconds = String(now.getSeconds()).padStart(2, '0');

    // 		// this.mainData.reportTime = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    // 		this.mainData.reportTime = new Date();
    // 		complianceReportApi
    // 			.save(this.mainData)
    // 			.then((response) => {
    // 				resolve(response);
    // 			})
    // 			.catch((error) => {
    // 				reject(error);
    // 			});
    // 	});
    // },

    //选择模板
    templateClick(val) {
      if (val) {
        this.prosecutionDialog = true;
      }
    },
    prosecutionSelect(data) {
      this.mainData.reportSubject = data.reportSubject;
      this.mainData.reportYear = data.reportYear;
      this.mainData.reportCategory = data.reportCategory;
      this.mainData.internalExternalRisk = data.internalExternalRisk;
      this.mainData.positiveNegativeImpact = data.positiveNegativeImpact;
      this.mainData.involvedAmount = data.involvedAmount;
      this.mainData.riskDescription = data.riskDescription;
      this.mainData.riskReason = data.riskReason;
      this.mainData.potentialConsequences = data.potentialConsequences;
      this.mainData.reportFile = data.reportFile;
      this.mainData.unitTypeId = data.unitTypeId; //单位类型id
      this.mainData.riskDepartment = data.riskDepartment; //风险部门
      this.mainData.riskDepartmentId = data.riskDepartmentId; //风险部门Id
      this.mainData.currentUnit = data.currentUnit; //当事单位
      this.mainData.currentUnitId = data.currentUnitId; //当事单位id
      this.mainData.reporter = data.createPsnName; //经办人
      this.mainData.reportingUnit = data.createDeptName; //经办单位
      this.mainData.createPsnPhone = data.createPsnPhone; //经办人电话
      this.mainData.caseInterest = data.caseInterest; //利息
      this.mainData.createOgnName = data.createOgnName; //经办单位
      this.mainData.createPsnName = data.createPsnName; //经办人
      this.mainData.createDeptName = data.createDeptName; //经办部门
      // this.mainData.auditStatus = data.dataState;//状态
      this.mainData.partiesList = data.partiesList;
      this.mainData.partiesList.forEach((item) => {
        item.id = this.utils.createUUID();
        item.masterId = this.mainData.id;
      });

      this.mainData.claimList = data.claimList;
      this.mainData.claimList.forEach((item) => {
        item.id = this.utils.createUUID();
        item.parentId = this.mainData.id;
      });

      this.mainData.otherDataList = data.otherDataList;
      this.mainData.otherDataList.forEach((item) => {
        item.id = this.utils.createUUID();
        item.parentId = this.mainData.id;
        item.files = null;
      });

      this.mainData.relations = data.relations;
      this.mainData.relations.forEach((item) => {
        item.id = this.utils.createUUID();
        item.relationId = this.mainData.id;
      });
    },
    approval_() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.save()
              .then(() => {
                const tabId = this.mainData.id;
                if (this.mainData.dataStateCode == this.utils.dataState_BPM.SAVE.code) {
                  taskApi.selectFunctionId({functionCode: 'case_risk_main'}).then((res) => {
                    const functionId = res.data.data[0].ID;
                    this.layout.openNewTab('案件风险告知审批信息', 'design_page', functionId, tabId, {
                      ...this.utils.routeState.NEW(tabId),
                      functionId: functionId,
                      businessKey: tabId,
                      entranceType: 'FLOWABLE',
                      create: 'create',
                      view: 'new',
                    });
                  });
                } else {
                  const tabId = this.mainData.id;
                  taskApi.selectTaskId({businessKey: tabId, isView: ''}).then((res) => {
                    const functionId = res.data.data[0].ID;
                    const uuid = this.utils.createUUID();
                    this.layout.openNewTab('案件风险告知审批信息', 'design_page', 'design_page', uuid, {
                      processInstanceId: res.data.data[0].PID, //流程实例
                      taskId: res.data.data[0].ID, //任务ID
                      businessKey: tabId, //业务数据ID
                      functionId: functionId, //不传也可以，只有首环节需要传，实例启动就可以不传了
                      entranceType: 'FLOWABLE',
                      type: 'toDeal',
                      view: 'new',
                      create: 'create',
                    });
                  });
                }
              })
              .then(() => {
                this.mcpLayout.closeTab();
              });
        }
      });
    },
    save_() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.save().then(() => {
            this.$message.success('保存成功!');
          });
        } else {
          this.$message.error('表单填写不完整，请检查后重新提交!');
        }
      });
    },
    submit_() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.submit().then(() => {
            this.mainData.reviewStatus = '已提交';
            this.$message.success('提交成功!');
          });
        } else {
          this.$message.error('表单填写不完整，请检查后重新提交!');
        }
      });
    },
    saveCurrentTime() {
      const now = new Date();
      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, '0');
      const day = String(now.getDate()).padStart(2, '0');
      const hours = String(now.getHours()).padStart(2, '0');
      const minutes = String(now.getMinutes()).padStart(2, '0');
      const seconds = String(now.getSeconds()).padStart(2, '0');
    },
    //添加表格数据
    addTableData() {
      this.mainData.trainingCourseDetailsList.push({
        trainingCourse: '',
        courseDescription: '',
        trainingMaterials: null,
      });
    },
    //负责人选择弹框
    chooiseApprovalDeptClick() {
      this.isCheckedUser = true;
      this.showUser = true;
      this.orgVisible = true;
      this.is_Check = false;
    },
    //单位选择弹框
    choiceEntrustedUnitClick() {
      this.entrustedUnitOrgVisible = true;
    },
    entrustedUnitOrgCancel() {
      this.entrustedUnitOrgVisible = false;
    },
    entrustedUnitSure() {
      const res = this.zxcheckedData[0];

      this.mainData.responsibleDepartment = res.name;
      this.entrustedUnitOrgVisible = false;
    },
    cancel() {
      this.orgVisible = false;
      this.orgVisible_trus = false;
      this.ChangeRecordDialogVisible = false;
    },
    choiceDeptSure() {
      let c = '';
      let cid = '';
      this.zxcheckedData.forEach((item) => {
        if (c.length === 0) {
          c = c + item.name;
          cid = cid + item.unitId;
        } else {
          c = c + ',' + item.name;
          cid = cid + ',' + item.unitId;
        }
      });
      this.mainData.trainingDirector = c;
      this.orgVisible = false;
    },
    chooseNoticeDeptClick() {
      this.deptOrgVisible = true;
    },
    chooseNoticeDeptClick1() {
      this.deptOrgVisible1 = true;
    },
    deptOrgCancel() {
      this.deptOrgVisible = false;
    },
    deptOrgCancel1() {
      this.deptOrgVisible1 = false;
    },
    choiceNoticeDeptSure() {
      let c = '';
      let cid = '';
      debugger;
      console.log(this.zxcheckedData);
      this.zxcheckedData.forEach((item) => {
        if (c.length === 0) {
          c = c + item.name;
          cid = cid + item.unitCode;
        } else {
          c = c + ',' + item.name;
          cid = cid + ',' + item.unitCode;
        }
      });
      this.mainData.mandatoryObjectName = c;
      this.mainData.mandatoryObjectId = cid;
      this.deptOrgVisible = false;
    },
    choiceNoticeDeptSure1() {
      let c = '';
      let cid = '';
      this.zxcheckedData.forEach((item) => {
        if (c.length === 0) {
          c = c + item.name;
          cid = cid + item.unitId;
        } else {
          c = c + ',' + item.name;
          cid = cid + ',' + item.unitId;
        }
      });
      this.mainData.electiveObjectName = c;
      this.mainData.electiveObjectId = cid;
      this.deptOrgVisible1 = false;
    },
    delete_(index, row) {
      console.log(index, row)
      this.$message({
        type: 'success',
        message: '删除成功!'
      });
      this.mainData.trainingCourseDetailsList.splice(index, 1)
    },

    businessFieldChange(val) {
      this.mainData.businessField = this.utils.getDicName(this.businessFieldData, val)
    },
    initDic() {
      dictApi.showAllSelect({
        dicCode: 'businessDomainDic'
      }).then(response => {
        this.businessFieldData = response.data.data
      })
    },
    addRow() {
      let obj = {'trainingCourse': '', 'courseDescription': ''};
      this.trainingCourseDetailsList.push(obj);
    },
    upRow() {
      debugger
      if (this.currentRow) {
        const index = this.currentIndex
        if (index > 0) {
          this.trainingCourseDetailsList.splice(index, 1);
          this.trainingCourseDetailsList.splice(index - 1, 0, this.currentRow);
        }
      }
    },
    downRow() {
      debugger
      if (this.currentRow) {
        const index = this.currentIndex
        if (index < this.trainingCourseDetailsList.length - 1) {
          this.trainingCourseDetailsList.splice(index, 1);
          this.trainingCourseDetailsList.splice(index + 1, 0, this.currentRow);
        }
      }
    },
    delRow() {
      const index = this.currentIndex
      this.trainingCourseDetailsList.splice(index, 1);
      this.currentRow = {};
    },
    handleCurrentChange(currentRow) {
      this.currentIndex = this.trainingCourseDetailsList.indexOf(currentRow);
      this.currentRow = currentRow;
    },
  }
};
</script>

<style scoped>
/* 使用 ::v-deep 穿透 scoped，同时限制样式作用范围 */
::v-deep .custom-table .el-form-item.is-error {
  margin-bottom: 20px !important; /* Element 默认值 */
}

::v-deep .custom-table .el-form-item {
  margin-left: 0 !important;
  margin-bottom: 0 !important;
}

::v-deep .custom-table .el-form-item__content {
  margin-left: 0 !important;
}
</style>
