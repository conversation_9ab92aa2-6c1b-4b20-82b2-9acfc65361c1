
8c52674fc03d43b27a3bc49e01266e71ca08c9c3	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.13.1754018536329.js\",\"contentHash\":\"9f53c3dfe60e670f7c855b5711f109c0\"}","integrity":"sha512-GLJfhnRSGlquyorexJXa4SdPt6HPaDhaDkSgubJP8zi8NrJB37OVBFLG7dvDH5+k3TSMByTdcH8r3u6LU/bqFQ==","time":1754018575958,"size":108884}