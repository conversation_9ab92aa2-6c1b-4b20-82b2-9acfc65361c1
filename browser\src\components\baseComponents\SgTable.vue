<template>
    <el-table :height="height" border highlight-current-row element-loading-text="拼命加载中" :data.sync="data"
              element-loading-background="rgba(0, 0, 0, 0.1)" v-loading="loading" size="small" :header-cell-style="{background:'#f5f7fa'}" :ref="refVal"
              stripe v-bind="$attrs" v-on="$listeners">
        <el-table-column v-if="showIndexColumn" fixed="left" label="序号" type="index" :index="indexMethod" :width="indexColumnWidth" class-name="sg-index-column"></el-table-column>
        <slot></slot>
    </el-table>
</template>

<script>
	export default {
		name: "sg-table",
		props:{
			refVal:String,
			height:{// table高度
				type:[Number,String],
				default:'100%',
			},
			data:Array,//table数据源
			showIndexColumn:{//是否显示索引列
				type:Boolean,
				default:true
			},
			indexColumnWidth:{//索引列的宽度
				type:String,
				default:'35'
			},
			loading: {
				type: Boolean,
				default: false
			}
		},
		methods:{
			indexMethod(index){
				let parent = this.$parent;
				return (parent.currentPage-1)*parent.pageSize+index+1;
			}
		}
	}
</script>

<style scoped>

</style>