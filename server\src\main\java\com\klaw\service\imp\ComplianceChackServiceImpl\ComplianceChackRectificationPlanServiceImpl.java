package com.klaw.service.imp.ComplianceChackServiceImpl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.klaw.dao.ComplianceChackDao.ComplianceChackRectificationPlanMapper;
import com.klaw.entity.ComplianceChack.ComplianceChackRectificationPlan;
import com.klaw.service.ComplianceChackService.ComplianceChackRectificationPlanService;
import org.springframework.stereotype.Service;

@Service
public class ComplianceChackRectificationPlanServiceImpl extends ServiceImpl<ComplianceChackRectificationPlanMapper, ComplianceChackRectificationPlan> implements ComplianceChackRectificationPlanService {
    @Override
    public boolean saveOrUpdate(ComplianceChackRectificationPlan complianceChackRectificationPlan) {
        return super.saveOrUpdate(complianceChackRectificationPlan);
    }
}
