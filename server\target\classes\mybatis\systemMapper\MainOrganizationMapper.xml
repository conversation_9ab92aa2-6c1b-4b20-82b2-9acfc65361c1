<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.klaw.dao.mainDataDao.MainOrganizationMapper">
    <resultMap id="BaseResultMap" type="com.klaw.entity.mainDataBean.MainOrganization">
        <id column="code" jdbcType="VARCHAR" property="code"/>
        <result column="code_name" jdbcType="VARCHAR" property="codeName"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="abbreviation" jdbcType="VARCHAR" property="abbreviation"/>
        <result column="organization" jdbcType="VARCHAR" property="organization"/>
        <result column="credit_code" jdbcType="VARCHAR" property="creditCode"/>
        <result column="business" jdbcType="VARCHAR" property="business"/>
        <result column="business_place" jdbcType="VARCHAR" property="businessPlace"/>
        <result column="tax_registration" jdbcType="VARCHAR" property="taxRegistration"/>
        <result column="mechanism_code" jdbcType="VARCHAR" property="mechanismCode"/>
        <result column="vat_code" jdbcType="VARCHAR" property="vatCode"/>
        <result column="vat_taxpayer_type" jdbcType="VARCHAR" property="vatTaxpayerType"/>
        <result column="is_merge_code" jdbcType="VARCHAR" property="isMergeCode"/>
        <result column="is_merge" jdbcType="VARCHAR" property="isMerge"/>
        <result column="is_accounting_code" jdbcType="VARCHAR" property="isAccountingCode"/>
        <result column="is_accounting" jdbcType="VARCHAR" property="isAccounting"/>
        <result column="unit_type_code" jdbcType="VARCHAR" property="unitTypeCode"/>
        <result column="unit_type" jdbcType="VARCHAR" property="unitType"/>
        <result column="equity_type_code" jdbcType="VARCHAR" property="equityTypeCode"/>
        <result column="equity_type" jdbcType="VARCHAR" property="equityType"/>
        <result column="level_sort" jdbcType="VARCHAR" property="levelSort"/>
        <result column="plate_type_code" jdbcType="VARCHAR" property="plateTypeCode"/>
        <result column="plate_type" jdbcType="VARCHAR" property="plateType"/>
        <result column="industry_type_code" jdbcType="VARCHAR" property="industryTypeCode"/>
        <result column="industry_type" jdbcType="VARCHAR" property="industryType"/>
        <result column="country_code" jdbcType="VARCHAR" property="countryCode"/>
        <result column="country_name" jdbcType="VARCHAR" property="countryName"/>
        <result column="province_name" jdbcType="VARCHAR" property="provinceName"/>
        <result column="city_name" jdbcType="VARCHAR" property="cityName"/>
        <result column="district_name" jdbcType="VARCHAR" property="districtName"/>
        <result column="registered_address" jdbcType="VARCHAR" property="registeredAddress"/>
        <result column="phone" jdbcType="VARCHAR" property="phone"/>
        <result column="superior_code" jdbcType="VARCHAR" property="superiorCode"/>
        <result column="superior_name" jdbcType="VARCHAR" property="superiorName"/>
        <result column="management_level" jdbcType="VARCHAR" property="managementLevel"/>
        <result column="unique_code" jdbcType="VARCHAR" property="uniqueCode"/>
        <result column="business_registration_date" jdbcType="TIMESTAMP" property="businessRegistrationDate"/>
        <result column="domestic_and_overseas" jdbcType="VARCHAR" property="domesticAndOverseas"/>
        <result column="major_industries" jdbcType="VARCHAR" property="majorIndustries"/>
        <result column="business_state" jdbcType="VARCHAR" property="businessState"/>
        <result column="status_code" jdbcType="VARCHAR" property="statusCode"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="mcp_unit_id" jdbcType="BIGINT" property="mcpUnitId"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="unit_nature_code" jdbcType="VARCHAR" property="unitNatureCode"/>

    </resultMap>
    <sql id="Base_Column_List">
        code,
        code_name,
        credit_code,
        status_code,
        status

    </sql>

    <select id="queryOrgUnit" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from main_organization
        <where>
            ${ew.sqlSegment}
        </where>
    </select>
</mapper>