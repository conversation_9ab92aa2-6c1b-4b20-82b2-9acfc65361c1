<template>
	<FormWindow ref="formWindow" @loadData="loadData">
		<el-container v-loading="loading" style="height: calc(100vh - 84px)">
			<el-main>
				<el-scrollbar style="height: 100%" wrap-style="overflow-x: hidden;">
					<el-form
						ref="dataForm"
						:model="mainData"
						label-width="100px"
						:style="dataState !== utils.formState.VIEW ? 'margin-right: 50px;' : 'margin-right: 10px'">
						<el-row style="padding: 10px 0 10px 0; position: fixed; width: 100%; overflow-y: auto; background-color: white; z-index: 999">
							<el-button v-if="!isView" class="normal-btn" size="mini" icon="el-icon-folder-checked" @click="save_()"> 保存 </el-button>
						</el-row>
						<div style="padding-top: 50px"></div>
						<span style="text-align: left; font-size: 20px; margin-left: 43%; font-weight: 900">{{ tabTitle }}<br /></span>

						<SimpleBoardTitle title="基本信息" style="margin-top: 5px">
							<table class="table_content" style="margin-top: 10px">
								<tbody>
									<tr>
										<th colspan="2" class="th_label">事项名称</th>
										<td colspan="14" class="td_value">{{ mainData.title }}</td>
										<th colspan="2" class="th_label">事项编号</th>
										<td colspan="6" class="td_value">{{ mainData.sequenceCode }}</td>
									</tr>
								</tbody>

								<tbody v-if="mainData.selectionTypeId === '1'">
									<tr>
										<th colspan="2" class="th_label">选聘类型</th>
										<td colspan="6" class="td_value">{{ mainData.selectionTypeName }}</td>
										<th colspan="2" class="th_label">服务项目</th>
										<td colspan="14" class="td_value">{{ mainData.serviceProject }}</td>
									</tr>
								</tbody>

								<tbody v-else-if="mainData.selectionTypeId === '2'">
									<tr>
										<th colspan="2" class="th_label">选聘类型</th>
										<td colspan="6" class="td_value">{{ mainData.selectionTypeName }}</td>
										<th colspan="2" class="th_label">关联案件</th>
										<td colspan="6" class="td_value">{{ mainData.relationCaseName }}</td>
										<th colspan="2" class="th_label">代理阶段</th>
										<td colspan="6" class="td_value">{{ mainData.agentStageName }}</td>
									</tr>
								</tbody>

								<tbody v-else-if="mainData.selectionTypeId === '3'">
									<tr>
										<th colspan="2" class="th_label">选聘类型</th>
										<td colspan="6" class="td_value">{{ mainData.selectionTypeName }}</td>
										<th colspan="2" class="th_label">开始时间</th>
										<td colspan="6" class="td_value">{{ mainData.beginDate | parseTime }}</td>
										<th colspan="2" class="th_label">结束时间</th>
										<td colspan="6" class="td_value">{{ mainData.endDate | parseTime }}</td>
									</tr>
								</tbody>

								<tbody v-else>
									<tr>
										<th colspan="2" class="th_label">选聘类型</th>
										<td colspan="22" class="td_value">{{ mainData.selectionTypeName }}</td>
									</tr>
								</tbody>

								<tbody>
									<tr>
										<th colspan="2" class="th_label">选聘方式</th>
										<td colspan="14" class="td_value">{{ mainData.selectionModeName }}</td>
										<th colspan="2" class="th_label">服务费用</th>
										<td colspan="6" class="td_value">{{ mainData.serviceCharge }}</td>
									</tr>
								</tbody>
								<tbody>
									<tr>
										<th colspan="2" class="th_label_">摘要说明</th>
										<td colspan="22" class="td_value_">{{ mainData.remark }}</td>
									</tr>
								</tbody>
							</table>
						</SimpleBoardTitle>
						<lawyerTask
							:my-use-data="mainData.useList"
							:data-state="dataState"
							:relation-id="mainData.id"
							:lawyer-list="mainData.lawyerList"
							:has-value.sync="this.isView"
							@updateUseData="updateUseData" />

            <el-row style="margin-top: 20px" class="rowCol1">
              <el-col :span="16">
                <el-form-item label="经办单位">
                  <span class="viewSpan">{{ mainData.createOgnName }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="经办人">
                  <span class="viewSpan">{{ mainData.createPsnName }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="16">
                <el-form-item label="经办部门">
                  <span class="viewSpan">{{ mainData.createDeptName }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="经办时间">
                  <span class="viewSpan">{{ mainData.createTime | parseTime }}</span>
                </el-form-item>
              </el-col>
            </el-row>

          </el-form>
				</el-scrollbar>
			</el-main>
		</el-container>
	</FormWindow>
</template>

<script>
	import AuthorApi from '@/api/authorization/authorization';
	import FormWindow from '@/view/components/FormWindow/FormWindow';
	import uploadDoc from '@/view/components/UploadDoc/UploadDoc';
	import Shortcut from '@/view/litigation/caseManage/caseExamine/Shortcut';
	import { parseTime } from '@/view/utils';
	import SimpleBoardTitle from '@/view/components/SimpleBoard/SimpleBoardTitle';
	import ProcessOpinion from '@/view/components/ProcessOpinion/ProcessOpinion';
	import lawyerTask from './LawyerTask';

	import { mapGetters } from 'vuex';
	import lawFirmSelectionApi from '@/api/LawyerManage/LawFirmSelection/lawFirmSelection';

	export default {
		name: 'LawyerUseMainDetail',
		inject: ['layout', 'mcpLayout'],
		components: {
			FormWindow,
			uploadDoc,
			Shortcut,
			SimpleBoardTitle,
			ProcessOpinion,
			lawyerTask,
		},
		computed: {
			...mapGetters(['orgContext', 'currentFunctionId']),
			isView: function () {
				return this.dataState === this.utils.formState.VIEW;
			},
			tabTitle: function () {
				let title = '律师对接登记';
				return title;
			},
		},

		data() {
			return {
				type: null,
				functionId: null, //终止的时候要用，需要手动关闭
				dataState: null, //表单状态，新增、查看、编辑
				loading: false, //查看时，加载中的动画
				from: false,
				// tabTitle: '长期授权审批详情单',
				tableData: [],
				view: 'new',
				mainData: {
					id: null,
					dataState: this.utils.dataState_BPM.SAVE.name, // 状态 新增时默认是已保存
					dataStateCode: this.utils.dataState_BPM.SAVE.code, // 状态编码
					createOgnId: null,
					createOgnName: null,
					createDeptId: null,
					createDeptName: null,
					createPsnId: null,
					createPsnName: null,
					createPsnFullId: null,
					createPsnFullName: null,
					createOrgId: null,
					createOrgName: null,
					createTime: null,
					updatePsnFullId: null,
					updatePsnFullName: null,
					updatePsnName: null,
					updatePsnId: null,
					updateTime: null,
					createLegalUnitId: null,
					createLegalUnitName: null,
				},
				tip_: '支持多种格式上传，如doc、pdf、zip等',
				docURL: '/design',
			};
		},
		methods: {
			initData(temp, dataState) {
				this.dataState = dataState;
				Object.assign(this.mainData, temp);
				let year = new Date().getFullYear();
				this.utils.createKvsequence('BXXP' + year, 6).then((value) => {
					this.mainData.sequenceCode = value.data.kvsequence;
				});
			},
			loadData(dataState, dataId) {
				this.functionId = this.$route.query.functionId;
				if (this.$route.query.view !== undefined && this.$route.query.view !== '') this.view = this.$route.query.view;
				if (this.$route.query.type !== undefined && this.$route.query.type !== '') this.type = this.$route.query.type;
				this.dataState = dataState;
				lawFirmSelectionApi.queryDataById({ id: dataId }).then((res) => {
					this.mainData = res.data.data;
				});
			},
			save() {
				return new Promise((resolve, reject) => {
					lawFirmSelectionApi
						.save({ lawFirm: this.mainData })
						.then((response) => {
							resolve(response);
						})
						.catch((error) => {
							reject(error);
						});
				});
			},
			save_() {
				this.save().then(() => {
					this.$message.success('保存成功!');
					this.mcpLayout.closeTab();
				});
			},
			parseTime,
			updateUseData(val) {
				this.useList = val;
			},
		},
	};
</script>

<style scoped>
	.el-dialog-div {
		height: 60vh;
		overflow: auto;
	}

	.mylabel .el-form-item .el-form-item__label {
		line-height: 15px;
	}
  .rowCol1 .el-form-item {
    margin-bottom: 0 !important;
  }
</style>
