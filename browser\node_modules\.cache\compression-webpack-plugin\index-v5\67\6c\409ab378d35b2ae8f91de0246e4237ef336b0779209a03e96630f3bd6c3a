
7c31f21cd0f9f703ef1faaa3795d1b4920eab98a	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.5.1754018536329.js\",\"contentHash\":\"5c7764f1b301f9a9a78cce5e116b2029\"}","integrity":"sha512-9PAV4O6DUGYa0zAtgxAeSciB5Yf9ScKNrSVd3fF2lNf/iO4UOqCWIoDkosjFz3jt82STm/8M3DJ5awV+OoaMWQ==","time":1754018575806,"size":46666}