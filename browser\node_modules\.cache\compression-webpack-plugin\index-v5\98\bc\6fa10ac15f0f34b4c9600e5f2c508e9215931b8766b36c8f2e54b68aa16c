
e202eabb5732c0b54e3fb0f74110c75c145d9e7a	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.436.1754018536329.js\",\"contentHash\":\"5bd155de8486ba0a7a98de2f437bd8ce\"}","integrity":"sha512-aEwv74GEO9v0R055jQnBuRKPIp+J+tH5gTkT9wR84mOxccWgn5DmFI/ZfUoRUbfDz3s/v7JU5HULxHD6/HCrkg==","time":1754018575976,"size":81199}