/** 
 * Kendo UI v2016.2.714 (http://www.telerik.com/kendo-ui)                                                                                                                                               
 * Copyright 2016 Telerik AD. All rights reserved.                                                                                                                                                      
 *                                                                                                                                                                                                      
 * Kendo UI commercial licenses may be obtained at                                                                                                                                                      
 * http://www.telerik.com/purchase/license-agreement/kendo-ui-complete                                                                                                                                  
 * If you do not own a commercial license, this file shall be governed by the trial license terms.                                                                                                      
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       

*/
/* Kendo skin */
/*!
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       

*/
/*!
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       

*/
.k-theme-test-class,
.ktb-theme-id-default {
  opacity: 0;
}
.ktb-var-accent {
  color: #eee;
}
.ktb-var-base {
  color: #fff;
}
.ktb-var-background {
  color: #fff;
}
.ktb-var-border-radius {
  border-radius: 6px;
}
.ktb-var-normal-background {
  color: #fff;
}
.ktb-var-normal-gradient {
  background-image: url('textures/highlight.png');
  background-image: none, -webkit-gradient(linear, left top, left bottom, from(rgba(0,0,0,.01)), to(rgba(0,0,0,.08)));
  background-image: none, -webkit-linear-gradient(top, rgba(0,0,0,.01) 0%, rgba(0,0,0,.08) 100%);
  background-image: none, linear-gradient(to bottom, rgba(0,0,0,.01) 0%, rgba(0,0,0,.08) 100%);
}
.ktb-var-normal-text-color {
  color: #676767;
}
.ktb-var-hover-background {
  color: #fff;
}
.ktb-var-hover-gradient {
  background-image: url('textures/highlight.png');
  background-image: none, -webkit-gradient(linear, left top, left bottom, from(rgba(0,0,0,.01)), to(rgba(0,0,0,.06)));
  background-image: none, -webkit-linear-gradient(top, rgba(0,0,0,.01) 0%, rgba(0,0,0,.06) 100%);
  background-image: none, linear-gradient(to bottom, rgba(0,0,0,.01) 0%, rgba(0,0,0,.06) 100%);
}
.ktb-var-hover-text-color {
  color: #676767;
}
.ktb-var-selected-background {
  color: #eee;
}
.ktb-var-selected-gradient {
  background-image: url('textures/highlight.png');
  background-image: none, -webkit-gradient(linear, left top, left bottom, from(rgba(0,0,0,.13)), to(rgba(0,0,0,.08)));
  background-image: none, -webkit-linear-gradient(top, rgba(0,0,0,.13) 0%, rgba(0,0,0,.08) 100%);
  background-image: none, linear-gradient(to bottom, rgba(0,0,0,.13) 0%, rgba(0,0,0,.08) 100%);
}
.ktb-var-selected-text-color {
  color: #454545;
}
.ktb-var-error {
  color: #ffe0d9;
}
.ktb-var-warning {
  color: #ffe9a8;
}
.ktb-var-success {
  color: #eaf7ec;
}
.ktb-var-info {
  color: #e5f5fa;
}
.ktb-var-series-a {
  color: #527aa3;
}
.ktb-var-series-b {
  color: #6f91b3;
}
.ktb-var-series-c {
  color: #8ca7c2;
}
.ktb-var-series-d {
  color: #a8bdd1;
}
.ktb-var-series-e {
  color: #c5d3e0;
}
.ktb-var-series-f {
  color: #e2e9f0;
}
.k-grid-norecords-template {
  background-color: #fff;
  border: 1px solid #ebebeb;
}
.k-in,
.k-item,
.k-window-action {
  border-color: transparent;
}
/* main colors */
.k-block,
.k-widget {
  background-color: #fff;
}
.k-block,
.k-widget,
.k-input,
.k-textbox,
.k-group,
.k-content,
.k-header,
.k-filter-row > th,
.k-editable-area,
.k-separator,
.k-colorpicker .k-i-arrow-s,
.k-textbox > input,
.k-autocomplete,
.k-dropdown-wrap,
.k-toolbar,
.k-group-footer td,
.k-grid-footer,
.k-footer-template td,
.k-state-default,
.k-state-default .k-select,
.k-state-disabled,
.k-grid-header,
.k-grid-header-wrap,
.k-grid-header-locked,
.k-grid-footer-locked,
.k-grid-content-locked,
.k-grid td,
.k-grid td.k-state-selected,
.k-grid-footer-wrap,
.k-pager-wrap,
.k-pager-wrap .k-link,
.k-pager-refresh,
.k-grouping-header,
.k-grouping-header .k-group-indicator,
.k-panelbar > .k-item > .k-link,
.k-panel > .k-item > .k-link,
.k-panelbar .k-panel,
.k-panelbar .k-content,
.k-treemap-tile,
.k-calendar th,
.k-slider-track,
.k-splitbar,
.k-dropzone-active,
.k-tiles,
.k-toolbar,
.k-tooltip,
.k-button-group .k-tool,
.k-upload-files {
  border-color: #ebebeb;
}
.k-group,
.k-toolbar,
.k-grouping-header,
.k-pager-wrap,
.k-group-footer td,
.k-grid-footer,
.k-footer-template td,
.k-widget .k-status,
.k-calendar th,
.k-dropzone-hovered,
.k-widget.k-popup {
  background-color: #f5f5f5;
}
.k-grouping-row td,
td.k-group-cell,
.k-resize-handle-inner {
  background-color: #ffffff;
}
.k-list-container {
  border-color: #dbdbdb;
  background-color: #fff;
}
.k-content,
.k-editable-area,
.k-panelbar > li.k-item,
.k-panel > li.k-item,
.k-tiles {
  background-color: #fff;
}
.k-alt,
.k-separator,
.k-resource.k-alt,
.k-pivot-layout > tbody > tr:first-child > td:first-child {
  background-color: #ffffff;
}
.k-pivot-rowheaders .k-alt .k-alt,
.k-header.k-alt {
  background-color: #ebebeb;
}
.k-textbox,
.k-autocomplete.k-header,
.k-dropdown-wrap.k-state-active,
.k-picker-wrap.k-state-active,
.k-numeric-wrap.k-state-active {
  border-color: #ebebeb;
  background-color: #fff;
}
.k-textbox > input,
.k-autocomplete .k-input,
.k-dropdown-wrap .k-input,
.k-autocomplete.k-state-focused .k-input,
.k-dropdown-wrap.k-state-focused .k-input,
.k-picker-wrap.k-state-focused .k-input,
.k-numeric-wrap.k-state-focused .k-input {
  border-color: #ebebeb;
}
input.k-textbox,
textarea.k-textbox,
input.k-textbox:hover,
textarea.k-textbox:hover,
.k-textbox > input {
  background: none;
}
.k-input,
input.k-textbox,
textarea.k-textbox,
input.k-textbox:hover,
textarea.k-textbox:hover,
.k-textbox > input,
.k-multiselect-wrap {
  background-color: #fff;
  color: #676767;
}
.k-input[readonly] {
  background-color: #fff;
  color: #676767;
}
.k-block,
.k-widget,
.k-popup,
.k-content,
.k-toolbar,
.k-dropdown .k-input {
  color: #676767;
}
.k-inverse {
  color: #ffffff;
}
.k-block {
  color: #464646;
}
.k-link:link,
.k-link:visited,
.k-nav-current.k-state-hover .k-link {
  color: #686666;
}
.k-tabstrip-items .k-link,
.k-panelbar > li > .k-link {
  color: #676767;
}
.k-header,
.k-treemap-title,
.k-grid-header .k-header > .k-link {
  color: #464646;
}
.k-header,
.k-grid-header,
.k-toolbar,
.k-dropdown-wrap,
.k-picker-wrap,
.k-numeric-wrap,
.k-grouping-header,
.k-pager-wrap,
.k-textbox,
.k-button,
.k-progressbar,
.k-draghandle,
.k-autocomplete,
.k-state-highlight,
.k-tabstrip-items .k-item,
.k-panelbar .k-tabstrip-items .k-item,
.km-pane-wrapper > .km-pane > .km-view > .km-content {
  background-image: url('textures/highlight.png');
  background-image: none, -webkit-gradient(linear, left top, left bottom, from(rgba(0,0,0,.01)), to(rgba(0,0,0,.08)));
  background-image: none, -webkit-linear-gradient(top, rgba(0,0,0,.01) 0%, rgba(0,0,0,.08) 100%);
  background-image: none, linear-gradient(to bottom, rgba(0,0,0,.01) 0%, rgba(0,0,0,.08) 100%);
  background-position: 50% 50%;
  background-color: #ffffff;
}
.k-block,
.k-header,
.k-grid-header,
.k-toolbar,
.k-grouping-header,
.k-pager-wrap,
.k-button,
.k-draghandle,
.k-treemap-tile,
html .km-pane-wrapper .k-header {
  background-color: #ffffff;
}
/* icons */
.k-icon:hover,
.k-state-hover .k-icon,
.k-state-selected .k-icon,
.k-state-focused .k-icon,
.k-column-menu .k-state-hover .k-sprite,
.k-column-menu .k-state-active .k-sprite,
.k-pager-numbers .k-current-page .k-link:hover:after,
.k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view.k-state-hover > .k-link:after {
  opacity: 1;
}
.k-icon,
.k-state-disabled .k-icon,
.k-column-menu .k-sprite,
.k-pager-numbers .k-current-page .k-link:after,
.k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view > .k-link:after {
  opacity: 0.9;
}
.k-mobile-list .k-check:checked,
.k-mobile-list .k-edit-field [type=checkbox]:checked,
.k-mobile-list .k-edit-field [type=radio]:checked {
  opacity: 0.9;
}
.k-tool {
  border-color: transparent;
}
.k-icon,
.k-tool-icon,
.k-grouping-dropclue,
.k-drop-hint,
.k-column-menu .k-sprite,
.k-grid-mobile .k-resize-handle-inner:before,
.k-grid-mobile .k-resize-handle-inner:after,
.k-pager-numbers .k-current-page .k-link:after,
.k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view > .k-link:after,
.k-gantt-views > .k-current-view > .k-link:after {
  background-image: url('Uniform/sprite.png');
  border-color: transparent;
}
/* IE will ignore the above selectors if these are added too */
.k-mobile-list .k-check:checked,
.k-mobile-list .k-edit-field [type=checkbox]:checked,
.k-mobile-list .k-edit-field [type=radio]:checked {
  background-image: url('Uniform/sprite.png');
  border-color: transparent;
}
.k-loading,
.k-state-hover .k-loading,
.k-i-loading,
.k-state-hover .k-i-loading {
  background-image: url('Uniform/loading.gif');
  background-position: 50% 50%;
}
.k-loading-image {
  background-image: url('Uniform/loading-image.gif');
}
.k-loading-color {
  background-color: #fcfcfc;
}
.k-button {
  color: #676767;
  border-color: #dbdbdb;
  background-color: #ffffff;
}
.k-draghandle {
  border-color: #a0dba9;
  background-color: #ffffff;
  -webkit-box-shadow: none;
          box-shadow: none;
}
.k-draghandle:hover {
  border-color: #95d79f;
  background-color: #ffffff;
  -webkit-box-shadow: none;
          box-shadow: none;
}
/* Scheduler */
.k-scheduler {
  color: #646464;
  background-color: #fff;
}
.k-scheduler-layout {
  color: #676767;
}
.k-scheduler-datecolumn,
.k-scheduler-groupcolumn {
  background-color: #fff;
  color: #676767;
}
.k-scheduler-times tr,
.k-scheduler-times th,
.k-scheduler-table td,
.k-scheduler-header th,
.k-scheduler-header-wrap,
.k-scheduler-times {
  border-color: #dbdbdb;
}
.k-nonwork-hour {
  background-color: #f2f2f2;
}
.k-gantt .k-nonwork-hour {
  background-color: rgba(255, 255, 255, 0.2);
}
.k-gantt .k-header.k-nonwork-hour {
  background-color: rgba(255, 255, 255, 0.15);
}
.k-scheduler-table .k-today,
.k-today > .k-scheduler-datecolumn,
.k-today > .k-scheduler-groupcolumn {
  background-color: #f7f7f7;
}
.k-scheduler-now-arrow {
  border-left-color: #ff6745;
}
.k-scheduler-now-line {
  background-color: #ff6745;
}
.k-event,
.k-task-complete {
  border-color: #dcdcdc;
  background: #b6b6b6 0 -257px url('textures/highlight.png') repeat-x;
  color: #646464;
}
.k-event-inverse {
  color: #fff;
}
.k-event.k-state-selected {
  background-position: 0 0;
  -webkit-box-shadow: 0 0 0 2px #676767;
          box-shadow: 0 0 0 2px #676767;
}
.k-event .k-resize-handle:after,
.k-task-single .k-resize-handle:after {
  background-color: #646464;
}
.k-scheduler-marquee:before,
.k-scheduler-marquee:after {
  border-color: #eee;
}
.k-panelbar .k-content,
.k-panelbar .k-panel,
.k-panelbar .k-item {
  background-color: #fff;
  color: #676767;
  border-color: #dbdbdb;
}
.k-panelbar > li > .k-link {
  color: #676767;
}
.k-panelbar > .k-item > .k-link {
  border-color: #dbdbdb;
}
.k-panel > li.k-item {
  background-color: #fff;
}
/* states */
.k-state-active,
.k-state-active:hover,
.k-active-filter,
.k-tabstrip .k-state-active {
  background-color: #ffffff;
  border-color: #b5b5b5;
  color: #464646;
}
.k-fieldselector .k-list-container {
  background-color: #ffffff;
}
.k-button:focus,
.k-button.k-state-focused {
  border-color: #dbdbdb;
}
.k-button:hover,
.k-button.k-state-hover {
  color: #676767;
  border-color: #cccccc;
  background-color: #fff;
}
.k-button:active,
.k-button.k-state-active {
  color: #454545;
  background-color: #eee;
  border-color: #cdcdcd;
}
.k-button:active:hover,
.k-button.k-state-active:hover {
  color: #646464;
  border-color: #cccccc;
  background-color: #f6f6f6;
}
.k-button[disabled],
.k-button.k-state-disabled,
.k-state-disabled .k-button,
.k-state-disabled .k-button:hover,
.k-button.k-state-disabled:hover,
.k-state-disabled .k-button:active,
.k-button.k-state-disabled:active {
  color: #b5b5b5;
  border-color: #dbdbdb;
  background-color: #ffffff;
  background-image: url('textures/highlight.png');
  background-image: none, -webkit-gradient(linear, left top, left bottom, from(rgba(0,0,0,.01)), to(rgba(0,0,0,.08)));
  background-image: none, -webkit-linear-gradient(top, rgba(0,0,0,.01) 0%, rgba(0,0,0,.08) 100%);
  background-image: none, linear-gradient(to bottom, rgba(0,0,0,.01) 0%, rgba(0,0,0,.08) 100%);
}
.k-button:focus:not(.k-state-disabled):not([disabled]) {
  -webkit-box-shadow: inset 0 0 3px 1px #cccccc;
          box-shadow: inset 0 0 3px 1px #cccccc;
}
.k-button:focus:active:not(.k-state-disabled):not([disabled]) {
  -webkit-box-shadow: inset 0 0 3px 1px #b3b3b3;
          box-shadow: inset 0 0 3px 1px #b3b3b3;
}
.k-menu .k-state-hover > .k-state-active {
  background-color: transparent;
}
.k-menu .k-state-selected > .k-link {
  color: #454545;
  background-color: #eee;
  border-color: #cdcdcd;
  background-image: url('textures/highlight.png');
  background-image: none, -webkit-gradient(linear, left top, left bottom, from(rgba(0,0,0,.13)), to(rgba(0,0,0,.08)));
  background-image: none, -webkit-linear-gradient(top, rgba(0,0,0,.13) 0%, rgba(0,0,0,.08) 100%);
  background-image: none, linear-gradient(to bottom, rgba(0,0,0,.13) 0%, rgba(0,0,0,.08) 100%);
}
.k-menu .k-link.k-state-active {
  background-color: #ffffff;
  border-color: #b5b5b5;
  color: #464646;
}
.k-menu .k-state-hover > .k-link {
  color: #676767;
  background-color: #fff;
  border-color: #cccccc;
  background-image: url('textures/highlight.png');
  background-image: none, -webkit-gradient(linear, left top, left bottom, from(rgba(0,0,0,.01)), to(rgba(0,0,0,.06)));
  background-image: none, -webkit-linear-gradient(top, rgba(0,0,0,.01) 0%, rgba(0,0,0,.06) 100%);
  background-image: none, linear-gradient(to bottom, rgba(0,0,0,.01) 0%, rgba(0,0,0,.06) 100%);
}
.k-state-highlight {
  background: #ffffff;
  color: #464646;
}
.k-state-focused,
.k-grouping-row .k-state-focused {
  border-color: #ebebeb;
}
.k-calendar .k-link {
  color: #686666;
}
.k-calendar .k-footer {
  padding: 0;
}
.k-calendar .k-footer .k-nav-today {
  color: #686666;
  text-decoration: none;
  background-color: #fff;
}
.k-calendar .k-footer .k-nav-today:hover,
.k-calendar .k-footer .k-nav-today.k-state-hover {
  background-color: #fff;
  text-decoration: underline;
}
.k-calendar .k-footer .k-nav-today:active {
  background-color: #fff;
}
.k-calendar .k-link.k-nav-fast {
  color: #686666;
}
.k-calendar .k-nav-fast.k-state-hover {
  text-decoration: none;
  background-color: #fff;
  color: #676767;
}
.k-calendar .k-link.k-state-hover {
  border-radius: 4px;
}
.k-calendar .k-today {
  -webkit-box-shadow: inset 0 0 0 1px #eee;
          box-shadow: inset 0 0 0 1px #eee;
}
.k-calendar .k-today .k-link {
  color: #454545;
  font-weight: bold;
}
.k-calendar td.k-today.k-state-focused,
.k-calendar td.k-today.k-state-focused.k-state-hover {
  -webkit-box-shadow: inset 0 0 3px 1px #b3b3b3;
          box-shadow: inset 0 0 3px 1px #b3b3b3;
}
.k-calendar td.k-today.k-state-hover,
.k-calendar td.k-today.k-state-selected {
  -webkit-box-shadow: none;
          box-shadow: none;
}
.k-calendar td.k-today.k-state-hover .k-link {
  color: #686666;
}
.k-calendar .k-footer .k-link {
  border-radius: 0;
}
.k-calendar th {
  background-color: #f5f5f5;
}
.k-calendar td.k-state-selected.k-state-hover {
  background-color: #efeded;
  background-image: none;
}
.k-calendar td.k-state-selected .k-link,
.k-calendar td.k-today.k-state-selected.k-state-hover .k-link {
  color: #454545;
}
.k-calendar td.k-state-focused.k-state-selected,
.k-calendar td.k-state-selected.k-state-active,
.k-calendar td.k-state-selected:active,
.k-calendar td.k-state-selected.k-state-hover:active {
  -webkit-box-shadow: inset 0 0 3px 1px #b3b3b3;
          box-shadow: inset 0 0 3px 1px #b3b3b3;
}
.k-window-titlebar .k-link {
  border-radius: 4px;
}
.k-calendar-container.k-group {
  border-color: #dbdbdb;
}
.k-state-selected,
.k-state-selected:link,
.k-state-selected:visited,
.k-list > .k-state-selected,
.k-list > .k-state-highlight,
.k-panel > .k-state-selected,
.k-ghost-splitbar-vertical,
.k-ghost-splitbar-horizontal,
.k-draghandle.k-state-selected:hover,
.k-scheduler .k-scheduler-toolbar .k-state-selected,
.k-scheduler .k-today.k-state-selected,
.k-marquee-color {
  color: #454545;
  background-color: #eee;
  border-color: #cdcdcd;
}
.k-virtual-item.k-first,
.k-group-header + .k-list > .k-item.k-first,
.k-static-header + .k-list > .k-item.k-first {
  border-top-color: #cccccc;
}
.k-group-header + div > .k-list > .k-item.k-first:before {
  border-top-color: #cccccc;
}
.k-popup > .k-group-header,
.k-popup > .k-virtual-wrap > .k-group-header {
  background: #cccccc;
  color: #454545;
}
.k-popup .k-list .k-item > .k-group {
  background: #cccccc;
  color: #676767;
  border-bottom-left-radius: 5px;
}
.k-popup .k-treeview .k-item > .k-group {
  background: transparent;
  color: #676767;
}
.k-marquee-text {
  color: #454545;
}
.k-state-focused,
.k-list > .k-state-focused,
.k-listview > .k-state-focused,
.k-grid-header th.k-state-focused,
td.k-state-focused,
.k-button.k-state-focused {
  -webkit-box-shadow: inset 0 0 3px 1px #b3b3b3;
          box-shadow: inset 0 0 3px 1px #b3b3b3;
}
.k-state-focused.k-state-selected,
.k-list > .k-state-focused.k-state-selected,
.k-listview > .k-state-focused.k-state-selected,
td.k-state-focused.k-state-selected {
  -webkit-box-shadow: inset 0 0 3px 1px #b3b3b3;
          box-shadow: inset 0 0 3px 1px #b3b3b3;
}
.k-ie8 .k-panelbar span.k-state-focused,
.k-ie8 .k-menu li.k-state-focused,
.k-ie8 .k-listview > .k-state-focused,
.k-ie8 .k-grid-header th.k-state-focused,
.k-ie8 td.k-state-focused,
.k-ie8 .k-tool.k-state-hover,
.k-ie8 .k-button:focus,
.k-ie8 .k-button.k-state-focused {
  background-color: #fff;
}
.k-list > .k-state-selected.k-state-focused,
.k-list-optionlabel.k-state-selected.k-state-focused {
  -webkit-box-shadow: none;
          box-shadow: none;
}
.k-state-selected > .k-link,
.k-panelbar > li > .k-state-selected,
.k-panelbar > li.k-state-default > .k-link.k-state-selected {
  color: #454545;
}
.k-state-hover,
.k-state-hover:hover,
.k-splitbar-horizontal-hover:hover,
.k-splitbar-vertical-hover:hover,
.k-list > .k-state-hover,
.k-scheduler .k-scheduler-toolbar ul li.k-state-hover,
.k-pager-wrap .k-link:hover,
.k-dropdown .k-state-focused,
.k-filebrowser-dropzone,
.k-mobile-list .k-item > .k-link:active,
.k-mobile-list .k-item > .k-label:active,
.k-mobile-list .k-edit-label.k-check:active,
.k-mobile-list .k-recur-view .k-check:active {
  color: #676767;
  background-color: #fff;
  border-color: #cccccc;
}
/* this selector should be used separately, otherwise old IEs ignore the whole rule */
.k-mobile-list .k-scheduler-timezones .k-edit-field:nth-child(2):active {
  color: #676767;
  background-color: #fff;
  border-color: #cccccc;
}
.k-ie8 .k-window-titlebar .k-state-hover {
  border-color: #cccccc;
}
.k-state-hover > .k-select,
.k-state-focused > .k-select {
  border-color: #cccccc;
}
.k-button:hover,
.k-button.k-state-hover,
.k-button:focus,
.k-button.k-state-focused,
.k-textbox:hover,
.k-state-hover,
.k-state-hover:hover,
.k-pager-wrap .k-link:hover,
.k-other-month.k-state-hover .k-link,
div.k-filebrowser-dropzone em,
.k-draghandle:hover {
  background-image: url('textures/highlight.png');
  background-image: none, -webkit-gradient(linear, left top, left bottom, from(rgba(0,0,0,.01)), to(rgba(0,0,0,.06)));
  background-image: none, -webkit-linear-gradient(top, rgba(0,0,0,.01) 0%, rgba(0,0,0,.06) 100%);
  background-image: none, linear-gradient(to bottom, rgba(0,0,0,.01) 0%, rgba(0,0,0,.06) 100%);
}
.k-pager-wrap {
  background-color: #ffffff;
  color: #464646;
}
.k-autocomplete.k-state-active,
.k-picker-wrap.k-state-active,
.k-numeric-wrap.k-state-active,
.k-dropdown-wrap.k-state-active,
.k-state-active,
.k-state-active:hover,
.k-state-active > .k-link,
.k-button:active,
.k-panelbar > .k-item > .k-state-focused {
  background-image: none;
}
.k-state-selected,
.k-button:active,
.k-button.k-state-active,
.k-draghandle.k-state-selected:hover {
  background-image: url('textures/highlight.png');
  background-image: none, -webkit-gradient(linear, left top, left bottom, from(rgba(0,0,0,.13)), to(rgba(0,0,0,.08)));
  background-image: none, -webkit-linear-gradient(top, rgba(0,0,0,.13) 0%, rgba(0,0,0,.08) 100%);
  background-image: none, linear-gradient(to bottom, rgba(0,0,0,.13) 0%, rgba(0,0,0,.08) 100%);
}
.k-button:active,
.k-button.k-state-active,
.k-draghandle.k-state-selected:hover {
  background-position: 50% 50%;
}
.k-tool-icon {
  background-image: url('Uniform/sprite.png');
}
.k-state-hover > .k-link,
.k-other-month.k-state-hover .k-link,
div.k-filebrowser-dropzone em {
  color: #676767;
}
.k-autocomplete.k-state-hover,
.k-autocomplete.k-state-focused,
.k-picker-wrap.k-state-hover,
.k-picker-wrap.k-state-focused,
.k-numeric-wrap.k-state-hover,
.k-numeric-wrap.k-state-focused,
.k-dropdown-wrap.k-state-hover,
.k-dropdown-wrap.k-state-focused {
  background-color: #ffffff;
  background-image: url('textures/highlight.png');
  background-image: none, -webkit-gradient(linear, left top, left bottom, from(rgba(0,0,0,.01)), to(rgba(0,0,0,.06)));
  background-image: none, -webkit-linear-gradient(top, rgba(0,0,0,.01) 0%, rgba(0,0,0,.06) 100%);
  background-image: none, linear-gradient(to bottom, rgba(0,0,0,.01) 0%, rgba(0,0,0,.06) 100%);
  background-position: 50% 50%;
  border-color: #cccccc;
}
.km-pane-wrapper .k-mobile-list input:not([type="checkbox"]):not([type="radio"]),
.km-pane-wrapper .km-pane .k-mobile-list select:not([multiple]),
.km-pane-wrapper .k-mobile-list textarea,
.k-dropdown .k-state-focused .k-input {
  color: #676767;
}
.km-pane-wrapper .km-pane .k-mobile-list.k-filter-menu .k-space-right {
  background-image: url('textures/highlight.png');
  background-image: none, -webkit-gradient(linear, left top, left bottom, from(rgba(0,0,0,.01)), to(rgba(0,0,0,.08)));
  background-image: none, -webkit-linear-gradient(top, rgba(0,0,0,.01) 0%, rgba(0,0,0,.08) 100%);
  background-image: none, linear-gradient(to bottom, rgba(0,0,0,.01) 0%, rgba(0,0,0,.08) 100%);
  background-position: 50% 50%;
  background: #e8e8e8;
  border-color: #dbdbdb;
}
.km-pane-wrapper .km-pane .k-mobile-list.k-filter-menu .k-space-right > input {
  background: #fff;
  border-color: #dbdbdb;
}
.km-pane-wrapper .km-pane .k-mobile-list.k-filter-menu .k-space-right > input:focus {
  -webkit-box-shadow: 0 0 3px 0 rgba(0, 0, 0, 0.3);
          box-shadow: 0 0 3px 0 rgba(0, 0, 0, 0.3);
}
.k-dropdown .k-state-hover .k-input {
  color: #676767;
}
.k-state-error {
  border-color: #ff6745;
  background-color: #b5b5b5;
  color: #e4e4e4;
}
.k-state-disabled {
  opacity: .7;
}
.k-ie8 .k-state-disabled {
  filter: alpha(opacity=70);
}
.k-tile-empty.k-state-selected,
.k-loading-mask.k-state-selected {
  border-width: 0;
  background-image: none;
  background-color: transparent;
}
.k-state-disabled,
.k-state-disabled .k-link,
.k-state-disabled .k-button,
.k-other-month,
.k-other-month .k-link,
.k-dropzone em,
.k-tile-empty strong,
.k-slider .k-draghandle {
  color: #b5b5b5;
}
.k-dropzone .k-upload-status {
  color: #676767;
}
/* Progressbar */
.k-progressbar-indeterminate {
  background: url('Uniform/indeterminate.gif');
}
.k-progressbar-indeterminate .k-progress-status-wrap,
.k-progressbar-indeterminate .k-state-selected {
  display: none;
}
/* Slider */
.k-slider-track {
  background-color: #ebebeb;
}
.k-slider-selection {
  background-color: #eee;
}
.k-slider-horizontal .k-tick {
  background-image: url('Uniform/slider-h.gif');
}
.k-slider-vertical .k-tick {
  background-image: url('Uniform/slider-v.gif');
}
/* Tooltip */
.k-widget.k-tooltip {
  background-image: url('textures/highlight.png');
  background-image: none, -webkit-gradient(linear, left top, left bottom, from(rgba(0,0,0,.01)), to(rgba(0,0,0,.08)));
  background-image: none, -webkit-linear-gradient(top, rgba(0,0,0,.01) 0%, rgba(0,0,0,.08) 100%);
  background-image: none, linear-gradient(to bottom, rgba(0,0,0,.01) 0%, rgba(0,0,0,.08) 100%);
  background-position: 50% 50%;
  background-color: #ffffff;
  color: #464646;
  border-color: transparent;
  -webkit-box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
          box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
}
.k-widget.k-tooltip-validation {
  border-color: #ffe9a8;
  background-color: #ffe9a8;
  color: #755700;
}
/* Bootstrap theme fix */
.input-prepend .k-tooltip-validation,
.input-append .k-tooltip-validation {
  font-size: 12px;
  position: relative;
  top: 3px;
}
.k-callout-n {
  border-bottom-color: #ffffff;
}
.k-callout-w {
  border-right-color: #ffffff;
}
.k-callout-s {
  border-top-color: #ffffff;
}
.k-callout-e {
  border-left-color: #ffffff;
}
.k-tooltip-validation .k-callout-n {
  border-bottom-color: #ffe9a8;
}
.k-tooltip-validation .k-callout-w {
  border-right-color: #ffe9a8;
}
.k-tooltip-validation .k-callout-s {
  border-top-color: #ffe9a8;
}
.k-tooltip-validation .k-callout-e {
  border-left-color: #ffe9a8;
}
/* Splitter */
.k-splitbar {
  background-color: #ffffff;
}
.k-restricted-size-vertical,
.k-restricted-size-horizontal {
  background-color: #e4e4e4;
}
/* Upload */
.k-file {
  background-color: #ffffff;
  border-color: #dbdbdb;
}
.k-file-progress {
  color: #2498bc;
}
.k-file-progress .k-progress {
  background-color: #e5f5fa;
}
.k-file-success {
  color: #3ea44e;
}
.k-file-success .k-progress {
  background-color: #eaf7ec;
}
.k-file-error {
  color: #d92800;
}
.k-file-error .k-progress {
  background-color: #ffe0d9;
}
/* ImageBrowser */
.k-tile {
  border-color: #fff;
}
.k-textbox:hover,
.k-tiles li.k-state-hover {
  border-color: #cccccc;
}
.k-tiles li.k-state-selected {
  border-color: #cdcdcd;
}
.k-filebrowser .k-tile .k-folder,
.k-filebrowser .k-tile .k-file {
  background-image: url('Uniform/imagebrowser.png');
  -webkit-background-size: auto auto;
          background-size: auto auto;
}
/* TreeMap */
.k-leaf,
.k-leaf.k-state-hover:hover {
  color: #fff;
}
.k-leaf.k-inverse,
.k-leaf.k-inverse.k-state-hover:hover {
  color: #000;
}
/* Shadows */
.k-widget,
.k-button {
  -webkit-box-shadow: none;
          box-shadow: none;
}
.k-slider,
.k-treeview,
.k-upload {
  -webkit-box-shadow: none;
          box-shadow: none;
}
.k-state-hover {
  -webkit-box-shadow: none;
          box-shadow: none;
}
.k-textbox:focus,
.k-autocomplete.k-state-focused,
.k-dropdown-wrap.k-state-focused,
.k-picker-wrap.k-state-focused,
.k-numeric-wrap.k-state-focused {
  -webkit-box-shadow: 0 0 3px 0 rgba(0, 0, 0, 0.3);
          box-shadow: 0 0 3px 0 rgba(0, 0, 0, 0.3);
}
.k-state-selected {
  -webkit-box-shadow: none;
          box-shadow: none;
}
.k-state-active {
  -webkit-box-shadow: none;
          box-shadow: none;
}
.k-grid tr:hover {
  background-color: #ffffff;
  background-image: url('textures/highlight.png');
  background-image: none, -webkit-gradient(linear, left top, left bottom, from(rgba(0,0,0,.01)), to(rgba(0,0,0,.06)));
  background-image: none, -webkit-linear-gradient(top, rgba(0,0,0,.01) 0%, rgba(0,0,0,.06) 100%);
  background-image: none, linear-gradient(to bottom, rgba(0,0,0,.01) 0%, rgba(0,0,0,.06) 100%);
  background-position: 50% 50%;
}
.k-pivot-rowheaders .k-grid tr:hover {
  background: none;
}
.k-grid tr.k-state-selected:hover,
.k-grid td.k-state-selected:hover {
  background-color: #efeded;
  background-image: none;
}
.k-popup,
.k-menu .k-menu-group,
.k-grid .k-filter-options,
.k-time-popup,
.k-datepicker-calendar,
.k-autocomplete.k-state-border-down,
.k-autocomplete.k-state-border-up,
.k-dropdown-wrap.k-state-active,
.k-picker-wrap.k-state-active,
.k-multiselect.k-state-focused,
.k-filebrowser .k-image,
.k-tooltip {
  -webkit-box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.3);
          box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.3);
}
.k-treemap-tile.k-state-hover {
  -webkit-box-shadow: inset 0 0 0 3px #ebebeb;
          box-shadow: inset 0 0 0 3px #ebebeb;
}
/* Window */
.k-window {
  border-color: rgba(0, 0, 0, 0.3);
  -webkit-box-shadow: 1px 1px 7px 1px rgba(128, 128, 128, 0.3);
          box-shadow: 1px 1px 7px 1px rgba(128, 128, 128, 0.3);
  background-color: #fff;
}
.k-window.k-state-focused {
  border-color: rgba(0, 0, 0, 0.3);
  -webkit-box-shadow: 1px 1px 7px 1px rgba(0, 0, 0, 0.3);
          box-shadow: 1px 1px 7px 1px rgba(0, 0, 0, 0.3);
}
.k-window.k-window-maximized,
.k-window-maximized .k-window-titlebar,
.k-window-maximized .k-window-content {
  border-radius: 0;
}
.k-shadow {
  -webkit-box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
          box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
}
.k-inset {
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.3);
          box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.3);
}
/* Selection */
.k-editor-inline ::-moz-selection {
  background-color: #cdcdcd;
  text-shadow: none;
  color: #fff;
}
.k-editor-inline ::selection {
  background-color: #cdcdcd;
  text-shadow: none;
  color: #fff;
}
.k-editor-inline ::-moz-selection {
  background-color: #cdcdcd;
  text-shadow: none;
  color: #fff;
}
/* Notification */
.k-widget.k-notification.k-notification-info {
  background-color: #e5f5fa;
  color: #2498bc;
  border-color: #b6e3f1;
}
.k-widget.k-notification.k-notification-success {
  background-color: #eaf7ec;
  color: #3ea44e;
  border-color: #c5e9cb;
}
.k-widget.k-notification.k-notification-warning {
  background-color: #ffe9a8;
  color: #a87e00;
  border-color: #ffe599;
}
.k-widget.k-notification.k-notification-error {
  background-color: #ffe0d9;
  color: #d92800;
  border-color: #ffb6a6;
}
/* Gantt */
.k-gantt .k-treelist {
  background: #ffffff;
}
.k-gantt .k-treelist .k-alt {
  background-color: #f2f2f2;
}
.k-gantt .k-treelist tr:hover {
  background-image: url('textures/highlight.png');
  background-image: none, -webkit-gradient(linear, left top, left bottom, from(rgba(0,0,0,.01)), to(rgba(0,0,0,.08)));
  background-image: none, -webkit-linear-gradient(top, rgba(0,0,0,.01) 0%, rgba(0,0,0,.08) 100%);
  background-image: none, linear-gradient(to bottom, rgba(0,0,0,.01) 0%, rgba(0,0,0,.08) 100%);
  background-color: #cccccc;
}
.k-gantt .k-treelist .k-state-selected,
.k-gantt .k-treelist .k-state-selected td,
.k-gantt .k-treelist .k-alt.k-state-selected,
.k-gantt .k-treelist .k-alt.k-state-selected > td {
  background-color: #eee;
  background-image: url('textures/highlight.png');
  background-image: none, -webkit-gradient(linear, left top, left bottom, from(rgba(0,0,0,.13)), to(rgba(0,0,0,.08)));
  background-image: none, -webkit-linear-gradient(top, rgba(0,0,0,.13) 0%, rgba(0,0,0,.08) 100%);
  background-image: none, linear-gradient(to bottom, rgba(0,0,0,.13) 0%, rgba(0,0,0,.08) 100%);
}
.k-gantt .k-treelist .k-state-selected:hover,
.k-gantt .k-treelist .k-state-selected:hover td {
  background-color: #efeded;
  background-image: none;
}
.k-task-dot:after {
  background-color: #676767;
  border-color: #676767;
}
.k-task-dot:hover:after {
  background-color: #ffffff;
}
.k-task-summary {
  border-color: #a7a7a7;
  background: #a7a7a7;
}
.k-task-milestone,
.k-task-summary-complete {
  border-color: #676767;
  background: #676767;
}
.k-state-selected.k-task-summary {
  border-color: #ffffff;
  background: #ffffff;
}
.k-state-selected.k-task-milestone,
.k-state-selected .k-task-summary-complete {
  border-color: #eee;
  background: #eee;
}
.k-task-single {
  background-color: #eeeeee;
  border-color: #dcdcdc;
  color: #646464;
}
.k-state-selected.k-task-single {
  border-color: #cdcdcd;
}
.k-line {
  background-color: #676767;
  color: #676767;
}
.k-state-selected.k-line {
  background-color: #eee;
  color: #eee;
}
.k-resource {
  background-color: #fff;
}
/* PivotGrid */
.k-i-kpi-decrease,
.k-i-kpi-denied,
.k-i-kpi-equal,
.k-i-kpi-hold,
.k-i-kpi-increase,
.k-i-kpi-open {
  background-image: url('Uniform/sprite_kpi.png');
}
/* Border radius */
.k-block,
.k-button,
.k-textbox,
.k-drag-clue,
.k-touch-scrollbar,
.k-window,
.k-window-titleless .k-window-content,
.k-window-action,
.k-inline-block,
.k-grid .k-filter-options,
.k-grouping-header .k-group-indicator,
.k-autocomplete,
.k-multiselect,
.k-combobox,
.k-dropdown,
.k-dropdown-wrap,
.k-lov,
.k-tledit,
.k-datepicker,
.k-timepicker,
.k-colorpicker,
.k-datetimepicker,
.k-notification,
.k-numerictextbox,
.k-picker-wrap,
.k-numeric-wrap,
.k-colorpicker,
.k-list-container,
.k-calendar-container,
.k-calendar td,
.k-calendar .k-link,
.k-treeview .k-in,
.k-editor-inline,
.k-tooltip,
.k-tile,
.k-slider-track,
.k-slider-selection,
.k-upload,
.k-split-button .k-gantt-views,
.k-gantt-views > .k-current-view {
  border-radius: 6px;
}
.k-tool {
  text-align: center;
  vertical-align: middle;
}
.k-tool.k-group-start,
.k-toolbar .k-split-button .k-button,
.k-toolbar .k-button-group .k-group-start {
  border-radius: 6px 0 0 6px;
}
.k-rtl .k-tool.k-group-start,
.k-rtl .k-toolbar .k-split-button .k-button,
.k-rtl .k-toolbar .k-button-group .k-group-start {
  border-radius: 0 6px 6px 0;
}
.k-toolbar .k-button-group > .k-group-end {
  border-radius: 6px;
}
.k-tool.k-group-end,
.k-toolbar .k-button-group .k-button + .k-group-end,
.k-toolbar .k-split-button .k-split-button-arrow {
  border-radius: 0 6px 6px 0;
}
.k-rtl .k-tool.k-group-end,
.k-rtl .k-toolbar .k-button-group .k-group-end,
.k-rtl .k-toolbar .k-split-button .k-split-button-arrow {
  border-radius: 6px 0 0 6px;
}
.k-group-start.k-group-end.k-tool {
  border-radius: 6px;
}
.k-calendar-container.k-state-border-up,
.k-list-container.k-state-border-up,
.k-autocomplete.k-state-border-up,
.k-multiselect.k-state-border-up,
.k-dropdown-wrap.k-state-border-up,
.k-picker-wrap.k-state-border-up,
.k-numeric-wrap.k-state-border-up,
.k-window-content,
.k-filter-menu {
  border-radius: 0 0 6px 6px;
}
.k-autocomplete.k-state-border-up .k-input,
.k-dropdown-wrap.k-state-border-up .k-input,
.k-picker-wrap.k-state-border-up .k-input,
.k-picker-wrap.k-state-border-up .k-selected-color,
.k-numeric-wrap.k-state-border-up .k-input {
  border-radius: 0 0 0 6px;
}
.k-multiselect.k-state-border-up .k-multiselect-wrap {
  border-radius: 0 0 6px 6px;
}
.k-window-titlebar,
.k-block > .k-header,
.k-tabstrip-items .k-item,
.k-panelbar .k-tabstrip-items .k-item,
.k-tabstrip-items .k-link,
.k-calendar-container.k-state-border-down,
.k-list-container.k-state-border-down,
.k-autocomplete.k-state-border-down,
.k-multiselect.k-state-border-down,
.k-dropdown-wrap.k-state-border-down,
.k-picker-wrap.k-state-border-down,
.k-numeric-wrap.k-state-border-down,
.k-gantt-views.k-state-expanded,
.k-gantt-views.k-state-expanded > .k-current-view {
  border-radius: 6px 6px 0 0;
}
.k-split-button.k-state-border-down > .k-button {
  border-radius: 6px 0 0 0;
}
.k-split-button.k-state-border-up > .k-button {
  border-radius: 0 0 0 6px;
}
.k-split-button.k-state-border-down > .k-split-button-arrow {
  border-radius: 0 6px 0 0;
}
.k-split-button.k-state-border-up > .k-split-button-arrow {
  border-radius: 0 0 6px 0;
}
.k-dropdown-wrap .k-input,
.k-picker-wrap .k-input,
.k-numeric-wrap .k-input {
  border-radius: 5px 0 0 5px;
}
.k-rtl .k-dropdown-wrap .k-input,
.k-rtl .k-picker-wrap .k-input,
.k-rtl .k-numeric-wrap .k-input {
  border-radius: 0 5px 5px 0;
}
.k-numeric-wrap .k-link {
  border-radius: 0 5px 0 0;
}
.k-numeric-wrap .k-link + .k-link {
  border-radius: 0 0 5px 0;
}
.k-colorpicker .k-selected-color {
  border-radius: 5px 0 0 5px;
}
.k-rtl .k-colorpicker .k-selected-color {
  border-radius: 0 5px 5px 0;
}
.k-autocomplete.k-state-border-down .k-input {
  border-radius: 6px 6px 0 0;
}
.k-dropdown-wrap.k-state-border-down .k-input,
.k-picker-wrap.k-state-border-down .k-input,
.k-picker-wrap.k-state-border-down .k-selected-color,
.k-numeric-wrap.k-state-border-down .k-input {
  border-radius: 6px 0 0 0;
}
.k-numeric-wrap .k-link.k-state-selected {
  background-color: #eee;
}
.k-multiselect.k-state-border-down .k-multiselect-wrap {
  border-radius: 5px 5px 0 0;
}
.k-dropdown-wrap .k-select,
.k-picker-wrap .k-select,
.k-numeric-wrap .k-select,
.k-datetimepicker .k-select + .k-select,
.k-list-container.k-state-border-right {
  border-radius: 0 6px 6px 0;
}
.k-rtl .k-dropdown-wrap .k-select,
.k-rtl .k-picker-wrap .k-select,
.k-rtl .k-numeric-wrap .k-select,
.k-rtl .k-datetimepicker .k-select + .k-select,
.k-rtl .k-list-container.k-state-border-right {
  border-radius: 6px 0 0 6px;
}
.k-numeric-wrap.k-expand-padding .k-input {
  border-radius: 6px;
}
.k-textbox > input,
.k-autocomplete .k-input,
.k-multiselect-wrap {
  border-radius: 5px;
}
.k-list .k-state-hover,
.k-list .k-state-focused,
.k-list .k-state-highlight,
.k-list .k-state-selected,
.k-fieldselector .k-list .k-item,
.k-list-optionlabel,
.k-dropzone {
  border-radius: 5px;
}
.k-slider .k-button,
.k-grid .k-slider .k-button {
  border-radius: 13px;
}
.k-draghandle {
  border-radius: 7px;
}
.k-scheduler-toolbar > ul li:first-child,
.k-scheduler-toolbar > ul li:first-child .k-link,
.k-scheduler-toolbar > ul.k-scheduler-views li:first-child + li,
.k-scheduler-toolbar > ul.k-scheduler-views li:first-child + li .k-link {
  border-radius: 6px 0 0 6px;
}
.k-rtl .k-scheduler-toolbar > ul li:first-child,
.k-rtl .k-scheduler-toolbar > ul li:first-child .k-link,
.k-rtl .k-scheduler-toolbar > ul.k-scheduler-views li:first-child + li,
.k-rtl .k-scheduler-toolbar > ul.k-scheduler-views li:first-child + li .k-link,
.km-view.k-popup-edit-form .k-scheduler-toolbar > ul li:last-child,
.km-view.k-popup-edit-form .k-scheduler-toolbar > ul li:last-child .k-link {
  border-radius: 0 6px 6px 0;
}
.k-scheduler-phone .k-scheduler-toolbar > ul li.k-nav-today,
.k-scheduler-phone .k-scheduler-toolbar > ul li.k-nav-today .k-link,
.k-edit-field > .k-scheduler-navigation {
  border-radius: 6px;
}
.k-scheduler-toolbar .k-nav-next,
.k-scheduler-toolbar ul + ul li:last-child,
.k-scheduler-toolbar .k-nav-next .k-link,
.k-scheduler-toolbar ul + ul li:last-child .k-link {
  border-top-right-radius: 6px;
  border-bottom-right-radius: 6px;
}
.k-rtl .k-scheduler-toolbar .k-nav-next,
.k-rtl .k-scheduler-toolbar ul + ul li:last-child,
.k-rtl .k-scheduler-toolbar .k-nav-next .k-link,
.k-rtl .k-scheduler-toolbar ul + ul li:last-child .k-link {
  border-radius: 6px 0 0 6px;
}
.k-scheduler div.k-scheduler-footer ul li,
.k-scheduler div.k-scheduler-footer .k-link {
  border-radius: 6px;
}
.k-more-events,
.k-event,
.k-task-single,
.k-task-complete,
.k-event .k-link {
  border-radius: 5px;
}
.k-scheduler-mobile .k-event {
  border-radius: 4px;
}
/* Adaptive Grid */
.k-grid-mobile .k-column-active + th.k-header {
  border-left-color: #676767;
}
html .km-pane-wrapper .km-widget,
.k-ie .km-pane-wrapper .k-widget,
.k-ie .km-pane-wrapper .k-group,
.k-ie .km-pane-wrapper .k-content,
.k-ie .km-pane-wrapper .k-header,
.k-ie .km-pane-wrapper .k-popup-edit-form .k-edit-field .k-button,
.km-pane-wrapper .k-mobile-list .k-item,
.km-pane-wrapper .k-mobile-list .k-edit-label,
.km-pane-wrapper .k-mobile-list .k-edit-field {
  color: #676767;
}
@media screen and (-ms-high-contrast: active) and (-ms-high-contrast: none) {
  div.km-pane-wrapper a {
    color: #676767;
  }
}
.km-pane-wrapper .k-mobile-list .k-item,
.km-pane-wrapper .k-mobile-list .k-edit-field,
.km-pane-wrapper .k-mobile-list .k-recur-view > .k-edit-field .k-check {
  background-color: #fff;
  border-top: 1px solid #dbdbdb;
}
.km-pane-wrapper .k-mobile-list .k-edit-field textarea {
  outline-width: 0;
}
.km-pane-wrapper .k-mobile-list .k-item.k-state-selected {
  background-color: #eee;
  border-top-color: #cdcdcd;
}
.km-pane-wrapper .k-mobile-list .k-recur-view > .k-edit-field .k-check:first-child {
  border-top-color: transparent;
}
.km-pane-wrapper .k-mobile-list .k-item:last-child {
  -webkit-box-shadow: inset 0 -1px 0 #dbdbdb;
          box-shadow: inset 0 -1px 0 #dbdbdb;
}
.km-pane-wrapper .k-mobile-list > ul > li > .k-link,
.km-pane-wrapper .k-mobile-list .k-recur-view > .k-edit-label:nth-child(3),
.km-pane-wrapper #recurrence .km-scroll-container > .k-edit-label:first-child {
  color: #8f8f8f;
}
.km-pane-wrapper .k-mobile-list > ul > li > .k-link {
  border-bottom: 1px solid #dbdbdb;
}
.km-pane-wrapper .k-mobile-list .k-edit-field {
  -webkit-box-shadow: 0 1px 1px #dbdbdb;
          box-shadow: 0 1px 1px #dbdbdb;
}
.km-actionsheet .k-grid-delete,
.km-actionsheet .k-scheduler-delete,
.km-pane-wrapper .k-scheduler-delete,
.km-pane-wrapper .k-filter-menu .k-button[type=reset] {
  color: #fff;
  border-color: #ff6745;
  background-color: red;
  background-image: -webkit-gradient(linear, left top, left bottom, from(rgba(255, 255, 255, 0.3)), to(rgba(255, 255, 255, 0.15)));
  background-image: -webkit-linear-gradient(top, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.15));
  background-image: linear-gradient(to bottom, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.15));
}
.km-actionsheet .k-grid-delete:active,
.km-actionsheet .k-scheduler-delete:active,
.km-pane-wrapper .k-scheduler-delete:active,
.km-pane-wrapper .k-filter-menu .k-button[type=reset]:active {
  background-color: #990000;
}
/* /Column Menu */
.k-autocomplete.k-state-default,
.k-picker-wrap.k-state-default,
.k-numeric-wrap.k-state-default,
.k-dropdown-wrap.k-state-default {
  background-image: url('textures/highlight.png');
  background-image: none, -webkit-gradient(linear, left top, left bottom, from(rgba(0,0,0,.01)), to(rgba(0,0,0,.08)));
  background-image: none, -webkit-linear-gradient(top, rgba(0,0,0,.01) 0%, rgba(0,0,0,.08) 100%);
  background-image: none, linear-gradient(to bottom, rgba(0,0,0,.01) 0%, rgba(0,0,0,.08) 100%);
  background-position: 50% 50%;
  background-color: #e8e8e8;
  border-color: #dbdbdb;
}
.k-autocomplete.k-state-hover,
.k-picker-wrap.k-state-hover,
.k-numeric-wrap.k-state-hover,
.k-dropdown-wrap.k-state-hover {
  background-color: #ffffff;
  background-image: url('textures/highlight.png');
  background-image: none, -webkit-gradient(linear, left top, left bottom, from(rgba(0,0,0,.01)), to(rgba(0,0,0,.06)));
  background-image: none, -webkit-linear-gradient(top, rgba(0,0,0,.01) 0%, rgba(0,0,0,.06) 100%);
  background-image: none, linear-gradient(to bottom, rgba(0,0,0,.01) 0%, rgba(0,0,0,.06) 100%);
  background-position: 50% 50%;
  border-color: #cccccc;
}
input.k-textbox,
textarea.k-textbox,
.k-multiselect.k-header {
  border-color: #dbdbdb;
}
.k-multiselect.k-header.k-state-hover {
  border-color: #cccccc;
}
.k-autocomplete.k-state-focused,
.k-picker-wrap.k-state-focused,
.k-numeric-wrap.k-state-focused,
.k-dropdown-wrap.k-state-focused,
.k-multiselect.k-header.k-state-focused {
  background-color: #ffffff;
  background-image: url('textures/highlight.png');
  background-image: none, -webkit-gradient(linear, left top, left bottom, from(rgba(0,0,0,.01)), to(rgba(0,0,0,.06)));
  background-image: none, -webkit-linear-gradient(top, rgba(0,0,0,.01) 0%, rgba(0,0,0,.06) 100%);
  background-image: none, linear-gradient(to bottom, rgba(0,0,0,.01) 0%, rgba(0,0,0,.06) 100%);
  background-position: 50% 50%;
  border-color: #bdbdbd;
  -webkit-box-shadow: 0 0 3px 0 rgba(0, 0, 0, 0.3);
          box-shadow: 0 0 3px 0 rgba(0, 0, 0, 0.3);
}
.k-list-container {
  color: #676767;
}
.k-dropdown .k-input,
.k-dropdown .k-state-focused .k-input,
.k-menu .k-popup {
  color: #676767;
}
.k-state-default > .k-select {
  border-color: #dbdbdb;
}
.k-state-hover > .k-select {
  border-color: #cccccc;
}
.k-state-focused > .k-select {
  border-color: #bdbdbd;
}
.k-tabstrip:focus {
  -webkit-box-shadow: 0 0 3px 0 rgba(0, 0, 0, 0.3);
          box-shadow: 0 0 3px 0 rgba(0, 0, 0, 0.3);
}
.k-tabstrip-items .k-state-default .k-link,
.k-panelbar > li.k-state-default > .k-link {
  color: #676767;
}
.k-tabstrip-items .k-state-hover .k-link,
.k-panelbar > li.k-state-hover > .k-link,
.k-panelbar > li.k-state-default > .k-link.k-state-hover {
  color: #676767;
}
.k-panelbar > .k-state-focused.k-state-hover {
  background: #fff;
  -webkit-box-shadow: none;
          box-shadow: none;
}
.k-tabstrip-items .k-state-default {
  border-color: #dbdbdb;
}
.k-tabstrip-items .k-state-hover {
  border-color: #cccccc;
}
.k-tabstrip-items .k-state-active,
.k-panelbar .k-tabstrip-items .k-state-active {
  background-color: #ffffff;
  background-image: none;
  border-color: #b5b5b5;
}
.k-tabstrip .k-content.k-state-active {
  background-color: #ffffff;
  color: #676767;
}
.k-menu.k-header,
.k-menu .k-item {
  border-color: #dbdbdb;
}
.k-column-menu,
.k-column-menu .k-item,
.k-overflow-container .k-overflow-group {
  border-color: #dbdbdb;
}
.k-overflow-container .k-overflow-group {
  -webkit-box-shadow: inset 0 1px 0 #ffffff, 0 1px 0 #ffffff;
          box-shadow: inset 0 1px 0 #ffffff, 0 1px 0 #ffffff;
}
.k-toolbar-first-visible.k-overflow-group,
.k-overflow-container .k-overflow-group + .k-overflow-group {
  -webkit-box-shadow: 0 1px 0 #ffffff;
          box-shadow: 0 1px 0 #ffffff;
}
.k-toolbar-last-visible.k-overflow-group {
  -webkit-box-shadow: inset 0 1px 0 #ffffff;
          box-shadow: inset 0 1px 0 #ffffff;
}
.k-column-menu .k-separator {
  border-color: #dbdbdb;
  background-color: transparent;
}
.k-menu .k-group {
  border-color: #dbdbdb;
}
.k-grid-filter.k-state-active {
  background-color: #ffffff;
}
.k-grouping-row td,
.k-group-footer td,
.k-grid-footer td {
  color: #676767;
  border-color: #dbdbdb;
  font-weight: bold;
}
.k-grouping-header {
  color: #676767;
}
.k-grid td.k-state-focused {
  -webkit-box-shadow: inset 0 0 3px 1px #b3b3b3;
          box-shadow: inset 0 0 3px 1px #b3b3b3;
}
.k-header,
.k-grid-header-wrap,
.k-grid .k-grouping-header,
.k-grid-header,
.k-pager-wrap,
.k-pager-wrap .k-textbox,
.k-pager-wrap .k-link,
.k-grouping-header .k-group-indicator,
.k-gantt-toolbar .k-state-default {
  border-color: #dbdbdb;
}
.k-primary,
.k-overflow-container .k-primary {
  color: #646464;
  border-color: #e6e6e6;
  background-image: url('textures/highlight.png');
  background-image: none, -webkit-gradient(linear, left top, left bottom, from(rgba(0,0,0,.13)), to(rgba(0,0,0,.08)));
  background-image: none, -webkit-linear-gradient(top, rgba(0,0,0,.13) 0%, rgba(0,0,0,.08) 100%);
  background-image: none, linear-gradient(to bottom, rgba(0,0,0,.13) 0%, rgba(0,0,0,.08) 100%);
  background-position: 50% 50%;
  background-color: #ffffff;
  -webkit-box-shadow: none;
          box-shadow: none;
}
.k-primary:focus,
.k-primary.k-state-focused {
  color: #646464;
  border-color: #e6e6e6;
  background-image: url('textures/highlight.png');
  background-image: none, -webkit-gradient(linear, left top, left bottom, from(rgba(0,0,0,.01)), to(rgba(0,0,0,.06)));
  background-image: none, -webkit-linear-gradient(top, rgba(0,0,0,.01) 0%, rgba(0,0,0,.06) 100%);
  background-image: none, linear-gradient(to bottom, rgba(0,0,0,.01) 0%, rgba(0,0,0,.06) 100%);
  -webkit-box-shadow: 0 0 3px 0 #eee;
          box-shadow: 0 0 3px 0 #eee;
}
.k-primary:hover {
  color: #646464;
  border-color: #cccccc;
  background-image: url('textures/highlight.png');
  background-image: none, -webkit-gradient(linear, left top, left bottom, from(rgba(0,0,0,.01)), to(rgba(0,0,0,.06)));
  background-image: none, -webkit-linear-gradient(top, rgba(0,0,0,.01) 0%, rgba(0,0,0,.06) 100%);
  background-image: none, linear-gradient(to bottom, rgba(0,0,0,.01) 0%, rgba(0,0,0,.06) 100%);
  background-color: #f6f6f6;
  -webkit-box-shadow: none;
          box-shadow: none;
}
.k-primary:focus:active:not(.k-state-disabled):not([disabled]),
.k-primary:focus:not(.k-state-disabled):not([disabled]) {
  -webkit-box-shadow: 0 0 3px 0 #eee;
          box-shadow: 0 0 3px 0 #eee;
}
.k-primary:active {
  color: #454545;
  border-color: #cdcdcd;
  background-image: url('textures/highlight.png');
  background-image: none, -webkit-gradient(linear, left top, left bottom, from(rgba(0,0,0,.13)), to(rgba(0,0,0,.08)));
  background-image: none, -webkit-linear-gradient(top, rgba(0,0,0,.13) 0%, rgba(0,0,0,.08) 100%);
  background-image: none, linear-gradient(to bottom, rgba(0,0,0,.13) 0%, rgba(0,0,0,.08) 100%);
  background-color: #eeeeee;
  -webkit-box-shadow: none;
          box-shadow: none;
}
.k-primary.k-state-disabled,
.k-state-disabled .k-primary,
.k-primary.k-state-disabled:hover,
.k-state-disabled .k-primary:hover,
.k-primary.k-state-disabled:hover,
.k-state-disabled .k-primary:active,
.k-primary.k-state-disabled:active {
  color: #2b2b2b;
  border-color: #2b2b2b;
  background-color: #ffffff;
  background-image: url('textures/highlight.png');
  background-image: none, -webkit-gradient(linear, left top, left bottom, from(rgba(0,0,0,.13)), to(rgba(0,0,0,.08)));
  background-image: none, -webkit-linear-gradient(top, rgba(0,0,0,.13) 0%, rgba(0,0,0,.08) 100%);
  background-image: none, linear-gradient(to bottom, rgba(0,0,0,.13) 0%, rgba(0,0,0,.08) 100%);
  -webkit-box-shadow: none;
          box-shadow: none;
}
.k-pager-numbers .k-link,
.k-treeview .k-in {
  border-color: transparent;
}
.k-treeview .k-icon,
.k-scheduler-table .k-icon,
.k-grid .k-hierarchy-cell .k-icon {
  background-color: transparent;
  border-radius: 4px;
}
.k-scheduler-table .k-state-hover .k-icon {
  background-color: transparent;
}
.k-button:focus,
.k-split-button:focus {
  outline: none;
}
.k-split-button:focus {
  -webkit-box-shadow: inset 0 0 4px 2px #cccccc;
          box-shadow: inset 0 0 4px 2px #cccccc;
}
.k-split-button:focus > .k-button {
  background: transparent;
  border-color: #dbdbdb;
}
.k-editor .k-tool:focus {
  outline: 0;
  border-color: #dbdbdb;
  -webkit-box-shadow: inset 0 0 3px 1px #cccccc;
          box-shadow: inset 0 0 3px 1px #cccccc;
}
/*!
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       

*/
.k-checkbox-label:before {
  border-color: #dbdbdb;
  background: #fff;
  border-radius: 3px;
}
.k-checkbox-label:hover:before,
.k-checkbox:checked + .k-checkbox-label:hover:before {
  border-color: #c2c2c2;
  -webkit-box-shadow: none;
          box-shadow: none;
}
.k-checkbox:checked + .k-checkbox-label:before {
  background-color: #ffffff;
  border-color: #a0dba9;
  color: #a4a4a4;
}
.k-checkbox-label:active:before {
  -webkit-box-shadow: 0 0 3px 0 #eee;
          box-shadow: 0 0 3px 0 #eee;
  border-color: #a4a4a4;
}
.k-checkbox:checked + .k-checkbox-label:active:before {
  -webkit-box-shadow: 0 0 3px 0 #eee;
          box-shadow: 0 0 3px 0 #eee;
  border-color: #a4a4a4;
}
.k-checkbox:disabled + .k-checkbox-label {
  color: #b5b5b5;
}
.k-checkbox:disabled + .k-checkbox-label:hover:before {
  -webkit-box-shadow: none;
          box-shadow: none;
}
.k-checkbox:disabled + .k-checkbox-label:before,
.k-checkbox:checked:disabled + .k-checkbox-label:before,
.k-checkbox:checked:disabled + .k-checkbox-label:active:before,
.k-checkbox:checked:disabled + .k-checkbox-label:hover:before {
  color: #b5b5b5;
  background: #ffffff;
  border-color: #9cd9a6;
  border-radius: 3px;
}
.k-checkbox:focus + .k-checkbox-label:before {
  border-color: #a4a4a4;
  -webkit-box-shadow: 0 0 3px 0 #eee;
          box-shadow: 0 0 3px 0 #eee;
}
.k-checkbox:indeterminate + .k-checkbox-label:after {
  background-color: #a4a4a4;
  background-image: url('textures/highlight.png');
  background-image: none, -webkit-gradient(linear, left top, left bottom, from(rgba(0,0,0,.13)), to(rgba(0,0,0,.08)));
  background-image: none, -webkit-linear-gradient(top, rgba(0,0,0,.13) 0%, rgba(0,0,0,.08) 100%);
  background-image: none, linear-gradient(to bottom, rgba(0,0,0,.13) 0%, rgba(0,0,0,.08) 100%);
  border-color: #a4a4a4;
  border-radius: 2px;
}
.k-checkbox:indeterminate:hover + .k-checkbox-label:after {
  border-color: #a4a4a4;
  background-color: #a4a4a4;
}
/*!
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       

*/
.k-radio-label:before {
  border-color: #dbdbdb;
  border-radius: 50%;
  background-color: #fff;
  border-width: 1px;
}
.k-radio-label:hover:before,
.k-radio:checked + .k-radio-label:hover:before {
  border-color: #c2c2c2;
  -webkit-box-shadow: none;
          box-shadow: none;
}
.k-radio:checked + .k-radio-label:after {
  background-color: #a4a4a4;
  border-radius: 50%;
}
.k-radio-label:active:before {
  border-color: #a4a4a4;
  -webkit-box-shadow: 0 0 3px 0 #eee;
          box-shadow: 0 0 3px 0 #eee;
}
.k-radio:checked + .k-radio-label:active:before {
  -webkit-box-shadow: 0 0 3px 0 #eee;
          box-shadow: 0 0 3px 0 #eee;
  border-color: #a4a4a4;
}
.k-radio:disabled + .k-radio-label {
  color: #b5b5b5;
}
.k-radio:disabled + .k-radio-label:before,
.k-radio:disabled + .k-radio-label:active:before,
.k-radio:disabled + .k-radio-label:hover:after,
.k-radio:disabled + .k-radio-label:hover:before {
  background: #ffffff;
  border-color: #bfbfbf;
  -webkit-box-shadow: none;
          box-shadow: none;
}
.k-radio:disabled:checked + .k-radio-label:after {
  background-color: #a4a4a4;
  opacity: .5;
}
.k-radio:focus + .k-radio-label:before {
  border-color: #a4a4a4;
  -webkit-box-shadow: 0 0 3px 0 #eee;
          box-shadow: 0 0 3px 0 #eee;
}
@media only screen and (-webkit-min-device-pixel-ratio: 1.2), only screen and (min-device-pixel-ratio: 1.2) {
  .k-icon:not(.k-loading),
  .k-grouping-dropclue,
  .k-drop-hint,
  .k-callout,
  .k-tool-icon,
  .k-state-hover .k-tool-icon,
  .k-state-active .k-tool-icon,
  .k-state-active.k-state-hover .k-tool-icon,
  .k-state-selected .k-tool-icon,
  .k-state-selected.k-state-hover .k-tool-icon,
  .k-column-menu .k-sprite,
  .k-mobile-list .k-check:checked,
  .k-mobile-list .k-edit-field [type=checkbox]:checked,
  .k-mobile-list .k-edit-field [type=radio]:checked {
    background-image: url('Uniform/sprite_2x.png');
    -webkit-background-size: 340px 336px;
            background-size: 340px 336px;
  }
  .k-dropdown-wrap .k-input,
  .k-picker-wrap .k-input,
  .k-numeric-wrap .k-input {
    border-radius: 5px 0 0 5px;
  }
  .k-i-kpi-decrease,
  .k-i-kpi-denied,
  .k-i-kpi-equal,
  .k-i-kpi-hold,
  .k-i-kpi-increase,
  .k-i-kpi-open {
    background-image: url('Uniform/sprite_kpi_2x.png');
    -webkit-background-size: 96px 16px;
            background-size: 96px 16px;
  }
}
@media screen and (-ms-high-contrast: active) {
  .k-editor-toolbar-wrap .k-dropdown-wrap.k-state-focused,
  .k-editor-toolbar-wrap .k-button-group .k-tool:focus {
    border-color: #fff;
  }
}
/* Responsive styles */
@media only screen and (max-width: 1024px) {
  .k-webkit .k-pager-numbers,
  .k-ff .k-pager-numbers,
  .k-ie11 .k-pager-numbers,
  .k-safari .k-pager-numbers,
  .k-webkit .k-grid .k-pager-numbers,
  .k-ff .k-grid .k-pager-numbers,
  .k-ie11 .k-grid .k-pager-numbers,
  .k-safari .k-grid .k-pager-numbers {
    -ms-transform: translatey(-100%);
        transform: translatey(-100%);
    -webkit-transform: translatey(-100%);
  }
  .k-webkit .k-pager-numbers .k-current-page,
  .k-ff .k-pager-numbers .k-current-page,
  .k-ie11 .k-pager-numbers .k-current-page,
  .k-safari .k-pager-numbers .k-current-page,
  .k-webkit .k-grid .k-pager-numbers .k-current-page,
  .k-ff .k-grid .k-pager-numbers .k-current-page,
  .k-ie11 .k-grid .k-pager-numbers .k-current-page,
  .k-safari .k-grid .k-pager-numbers .k-current-page {
    -ms-transform: translatey(100%);
        transform: translatey(100%);
    -webkit-transform: translatey(100%);
  }
  .k-webkit .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view,
  .k-ff .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view,
  .k-ie11 .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view,
  .k-safari .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view,
  .k-webkit .k-pager-numbers .k-current-page .k-link,
  .k-ff .k-pager-numbers .k-current-page .k-link,
  .k-ie11 .k-pager-numbers .k-current-page .k-link,
  .k-safari .k-pager-numbers .k-current-page .k-link {
    background-image: url('textures/highlight.png');
    background-image: none, -webkit-gradient(linear, left top, left bottom, from(rgba(0,0,0,.01)), to(rgba(0,0,0,.08)));
    background-image: none, -webkit-linear-gradient(top, rgba(0,0,0,.01) 0%, rgba(0,0,0,.08) 100%);
    background-image: none, linear-gradient(to bottom, rgba(0,0,0,.01) 0%, rgba(0,0,0,.08) 100%);
    background-position: 50% 50%;
    background-color: #e8e8e8;
    border-color: #ebebeb;
  }
  .k-webkit .k-pager-numbers .k-current-page .k-link,
  .k-ff .k-pager-numbers .k-current-page .k-link,
  .k-ie11 .k-pager-numbers .k-current-page .k-link,
  .k-safari .k-pager-numbers .k-current-page .k-link {
    border-color: #dbdbdb;
  }
  .k-webkit .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view,
  .k-ff .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view,
  .k-ie11 .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view,
  .k-safari .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view {
    border-radius: 6px;
  }
  .k-webkit .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded > li,
  .k-ff .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded > li,
  .k-ie11 .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded > li,
  .k-safari .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded > li {
    border-radius: 0;
  }
  .k-webkit .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded > li.k-current-view,
  .k-ff .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded > li.k-current-view,
  .k-ie11 .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded > li.k-current-view,
  .k-safari .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded > li.k-current-view {
    border-radius: 5px 5px 0 0;
  }
  .k-webkit .k-scheduler-toolbar > ul li:first-child,
  .k-ff .k-scheduler-toolbar > ul li:first-child,
  .k-ie11 .k-scheduler-toolbar > ul li:first-child,
  .k-safari .k-scheduler-toolbar > ul li:first-child,
  .k-webkit .k-scheduler-toolbar > ul li:first-child .k-link,
  .k-ff .k-scheduler-toolbar > ul li:first-child .k-link,
  .k-ie11 .k-scheduler-toolbar > ul li:first-child .k-link,
  .k-safari .k-scheduler-toolbar > ul li:first-child .k-link,
  .k-webkit .k-scheduler-toolbar > ul.k-scheduler-views li,
  .k-ff .k-scheduler-toolbar > ul.k-scheduler-views li,
  .k-ie11 .k-scheduler-toolbar > ul.k-scheduler-views li,
  .k-safari .k-scheduler-toolbar > ul.k-scheduler-views li,
  .k-webkit .k-scheduler-toolbar > ul.k-scheduler-views li .k-link,
  .k-ff .k-scheduler-toolbar > ul.k-scheduler-views li .k-link,
  .k-ie11 .k-scheduler-toolbar > ul.k-scheduler-views li .k-link,
  .k-safari .k-scheduler-toolbar > ul.k-scheduler-views li .k-link {
    border-radius: 0;
  }
  .k-webkit .k-scheduler-toolbar > ul.k-scheduler-views li:last-child,
  .k-ff .k-scheduler-toolbar > ul.k-scheduler-views li:last-child,
  .k-ie11 .k-scheduler-toolbar > ul.k-scheduler-views li:last-child,
  .k-safari .k-scheduler-toolbar > ul.k-scheduler-views li:last-child,
  .k-webkit .k-scheduler-toolbar > ul.k-scheduler-views li:last-child .k-link,
  .k-ff .k-scheduler-toolbar > ul.k-scheduler-views li:last-child .k-link,
  .k-ie11 .k-scheduler-toolbar > ul.k-scheduler-views li:last-child .k-link,
  .k-safari .k-scheduler-toolbar > ul.k-scheduler-views li:last-child .k-link {
    border-radius: 0 0 5px 5px;
  }
  .k-webkit .k-pager-numbers .k-current-page .k-link:hover,
  .k-ff .k-pager-numbers .k-current-page .k-link:hover,
  .k-ie11 .k-pager-numbers .k-current-page .k-link:hover,
  .k-safari .k-pager-numbers .k-current-page .k-link:hover,
  .k-webkit .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view:hover,
  .k-ff .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view:hover,
  .k-ie11 .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view:hover,
  .k-safari .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view:hover {
    border-color: #cccccc;
    background-image: url('textures/highlight.png');
    background-image: none, -webkit-gradient(linear, left top, left bottom, from(rgba(0,0,0,.01)), to(rgba(0,0,0,.06)));
    background-image: none, -webkit-linear-gradient(top, rgba(0,0,0,.01) 0%, rgba(0,0,0,.06) 100%);
    background-image: none, linear-gradient(to bottom, rgba(0,0,0,.01) 0%, rgba(0,0,0,.06) 100%);
    background-color: #fff;
  }
  .k-webkit .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view > .k-link,
  .k-ff .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view > .k-link,
  .k-ie11 .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view > .k-link,
  .k-safari .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view > .k-link {
    color: #676767;
    min-width: 75px;
  }
  .k-webkit .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view:hover > .k-link,
  .k-ff .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view:hover > .k-link,
  .k-ie11 .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view:hover > .k-link,
  .k-safari .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view:hover > .k-link {
    color: #676767;
  }
  .k-webkit .k-pager-numbers .k-current-page .k-link:after,
  .k-ff .k-pager-numbers .k-current-page .k-link:after,
  .k-ie11 .k-pager-numbers .k-current-page .k-link:after,
  .k-safari .k-pager-numbers .k-current-page .k-link:after,
  .k-webkit .k-scheduler-views > li.k-state-selected > .k-link:after,
  .k-ff .k-scheduler-views > li.k-state-selected > .k-link:after,
  .k-ie11 .k-scheduler-views > li.k-state-selected > .k-link:after,
  .k-safari .k-scheduler-views > li.k-state-selected > .k-link:after {
    display: block;
    content: "";
    position: absolute;
    top: 50%;
    margin-top: -0.5em;
    right: 0.333em;
    width: 1.333em;
    height: 1.333em;
  }
  .k-webkit .k-pager-numbers.k-state-expanded,
  .k-ff .k-pager-numbers.k-state-expanded,
  .k-ie11 .k-pager-numbers.k-state-expanded,
  .k-safari .k-pager-numbers.k-state-expanded,
  .k-webkit .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded,
  .k-ff .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded,
  .k-ie11 .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded,
  .k-safari .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded {
    border-width: 1px 1px 0 1px;
    border-style: solid;
    border-color: #dbdbdb;
    background-color: #ffffff;
    border-radius: 6px 6px 0 0;
    -webkit-box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.3);
            box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.3);
  }
  .k-webkit .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded,
  .k-ff .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded,
  .k-ie11 .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded,
  .k-safari .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded {
    border-width: 1px;
    background-image: none;
    border-radius: 6px;
  }
  .k-webkit .k-pager-numbers .k-state-selected,
  .k-ff .k-pager-numbers .k-state-selected,
  .k-ie11 .k-pager-numbers .k-state-selected,
  .k-safari .k-pager-numbers .k-state-selected,
  .k-webkit .k-pager-numbers .k-link,
  .k-ff .k-pager-numbers .k-link,
  .k-ie11 .k-pager-numbers .k-link,
  .k-safari .k-pager-numbers .k-link {
    border-radius: 5px;
  }
  .k-webkit .k-widget.k-grid .k-pager-nav + .k-pager-numbers,
  .k-ff .k-widget.k-grid .k-pager-nav + .k-pager-numbers,
  .k-ie11 .k-widget.k-grid .k-pager-nav + .k-pager-numbers,
  .k-safari .k-widget.k-grid .k-pager-nav + .k-pager-numbers {
    position: absolute;
  }
}
.k-chart .k-mask {
  background-color: #fff;
  filter: alpha(opacity=68);
  opacity: 0.68;
}
.k-chart .k-selection {
  border-color: rgba(0, 0, 0, 0.2);
  -webkit-box-shadow: inset 0 1px 8px rgba(0, 0, 0, 0.1);
          box-shadow: inset 0 1px 8px rgba(0, 0, 0, 0.1);
  -webkit-transition: border-color 0.2s linear, -webkit-box-shadow 0.2s linear;
  transition: border-color 0.2s linear, -webkit-box-shadow 0.2s linear;
  transition: box-shadow 0.2s linear, border-color 0.2s linear;
  transition: box-shadow 0.2s linear, border-color 0.2s linear, -webkit-box-shadow 0.2s linear;
}
.k-chart .k-selection:hover {
  border-color: rgba(0, 0, 0, 0.3);
  -webkit-box-shadow: inset 0 3px 8px rgba(0, 0, 0, 0.2);
          box-shadow: inset 0 3px 8px rgba(0, 0, 0, 0.2);
}
.k-chart .k-handle {
  background-color: #fcfcfc;
  -webkit-box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.1);
          box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.1);
}
.k-chart .k-handle:hover {
  background-color: #ffffff;
  border-color: #b8b8b8;
  -webkit-box-shadow: 0 0 0 2px rgba(111, 101, 96, 0.5);
          box-shadow: 0 0 0 2px rgba(111, 101, 96, 0.5);
}
.k-chart .k-navigator-hint .k-tooltip {
  border: 3px solid #ffffff;
  -webkit-box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.2);
          box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.2);
  background: #ffffff;
  color: #242424;
}
.k-chart .k-navigator-hint .k-scroll {
  background: rgba(238, 238, 238, 0.7);
  height: 4px;
}
/* Map */
.k-map .k-marker {
  background-image: url("Uniform/markers.png");
}
@media only screen and (-webkit-min-device-pixel-ratio: 1.2), only screen and (min-device-pixel-ratio: 1.2) {
  .k-map .k-marker {
    background-image: url("Uniform/markers_2x.png");
  }
}
.k-map .k-attribution {
  color: #666666;
}
/*!
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       

*/
.k-spreadsheet-row-header,
.k-spreadsheet-column-header {
  background-color: #fff;
}
.k-spreadsheet-top-corner,
.k-spreadsheet-row-header,
.k-spreadsheet-column-header {
  background-color: #fff;
  background-image: none;
  color: #000000;
  border-color: #cccccc;
}
.k-spreadsheet-top-corner {
  border-color: #cccccc;
}
.k-spreadsheet-top-corner:after {
  border-color: transparent #cccccc #cccccc transparent;
}
.k-spreadsheet-pane {
  border-color: #cccccc;
}
.k-spreadsheet-pane .k-spreadsheet-vaxis,
.k-spreadsheet-pane .k-spreadsheet-haxis {
  border-color: #e6e6e6;
}
.k-spreadsheet-pane .k-spreadsheet-column-header,
.k-spreadsheet-pane .k-spreadsheet-row-header {
  border-color: #cccccc;
}
.k-spreadsheet-pane .k-spreadsheet-merged-cell {
  background-color: #fff;
}
.k-spreadsheet-pane .k-selection-partial,
.k-spreadsheet-pane .k-selection-full {
  border-color: rgba(238, 238, 238, 0.2);
  background-color: rgba(238, 238, 238, 0.2);
}
.k-spreadsheet-pane .k-filter-range {
  border-color: #eee;
}
.k-spreadsheet-pane .k-spreadsheet-column-header .k-selection-partial,
.k-spreadsheet-pane .k-spreadsheet-column-header .k-selection-full {
  border-bottom-color: #eeeeee;
}
.k-spreadsheet-pane .k-spreadsheet-row-header .k-selection-partial,
.k-spreadsheet-pane .k-spreadsheet-row-header .k-selection-full {
  border-right-color: #eeeeee;
}
.k-auto-fill,
.k-spreadsheet-selection {
  border-color: #eee;
  -webkit-box-shadow: inset 0 0 0 1px #fff, 0 0 0 1px #eee;
          box-shadow: inset 0 0 0 1px #fff, 0 0 0 1px #eee;
}
.k-spreadsheet-selection {
  background-color: rgba(238, 238, 238, 0.2);
}
.k-spreadsheet-active-cell {
  border-color: #eee !important;
  background-color: #fff;
}
.k-spreadsheet-active-cell.k-single {
  color: #676767;
  background-color: #fff;
}
.k-spreadsheet .k-spreadsheet-action-bar {
  background-color: #fff;
  border-color: #ebebeb;
}
.k-spreadsheet .k-spreadsheet-action-bar .k-spreadsheet-name-editor {
  border-color: #cccccc;
}
.k-spreadsheet .k-spreadsheet-action-bar .k-spreadsheet-formula-bar::before {
  border-color: #cccccc;
}
.k-spreadsheet .k-spreadsheet-formula-input {
  background-color: #fff;
  color: #676767;
}
.k-spreadsheet .k-resize-handle,
.k-spreadsheet .k-resize-hint-handle,
.k-spreadsheet .k-resize-hint-marker {
  background-color: #eee;
}
.k-spreadsheet .k-resize-hint-vertical .k-resize-hint-handle,
.k-spreadsheet .k-resize-hint-vertical .k-resize-hint-marker {
  background-color: #eee;
}
.k-spreadsheet .k-single-selection::after {
  background-color: #eee;
  border-color: #fff;
}
.k-spreadsheet .k-auto-fill-punch {
  background-color: rgba(255, 255, 255, 0.5);
}
.k-spreadsheet .k-single-selection.k-dim-auto-fill-handle::after {
  background-color: rgba(238, 238, 238, 0.5);
}
.k-spreadsheet-format-cells .k-spreadsheet-preview {
  border-color: #ebebeb;
}
.k-spreadsheet-filter {
  border-radius: 6px;
  background-color: #fff;
  -webkit-box-shadow: inset 0 0 0 1px #e6e6e6;
          box-shadow: inset 0 0 0 1px #e6e6e6;
}
.k-spreadsheet-filter.k-state-active {
  color: #454545;
  background-color: #eee;
}
.k-spreadsheet-filter:hover {
  color: #676767;
  background: #fff;
  border-color: #ebebeb;
}
.k-action-window .k-action-buttons {
  border-color: #ebebeb;
}
.k-spreadsheet-sample {
  color: #b3b3b3;
}
.k-state-selected .k-spreadsheet-sample {
  color: inherit;
}
.k-spreadsheet-window .k-list-wrapper,
.k-spreadsheet-window .k-list {
  border-color: #ebebeb;
  border-radius: 6px;
}
.k-spreadsheet-window .export-config,
.k-spreadsheet-window .k-edit-field > .k-orientation-label {
  border-color: #ebebeb;
}
.k-spreadsheet-window .k-edit-field > input[type="radio"]:checked + .k-orientation-label {
  background-image: url('textures/highlight.png');
  background-image: none, -webkit-gradient(linear, left top, left bottom, from(rgba(0,0,0,.13)), to(rgba(0,0,0,.08)));
  background-image: none, -webkit-linear-gradient(top, rgba(0,0,0,.13) 0%, rgba(0,0,0,.08) 100%);
  background-image: none, linear-gradient(to bottom, rgba(0,0,0,.13) 0%, rgba(0,0,0,.08) 100%);
  background-color: #eee;
  color: #646464;
}
.k-spreadsheet-window .k-page-orientation {
  border-color: #dbdbdb;
  -webkit-box-shadow: 0 5px 5px 0 rgba(0, 0, 0, 0.1);
          box-shadow: 0 5px 5px 0 rgba(0, 0, 0, 0.1);
}
.k-spreadsheet-window .k-page-orientation:before {
  background: #fff;
  border-color: transparent;
  border-bottom-color: #dbdbdb;
  border-left-color: #dbdbdb;
}
.k-spreadsheet-window .k-margins-horizontal,
.k-spreadsheet-window .k-margins-vertical {
  background: transparent;
  border-color: #ebebeb;
}
.k-spreadsheet-window .hint-wrapper:before {
  background: #ebebeb;
}
.k-spreadsheet-toolbar.k-toolbar .k-button-group .k-button {
  border-radius: 6px;
}
.k-spreadsheet-toolbar > .k-widget,
.k-spreadsheet-toolbar > .k-button,
.k-spreadsheet-toolbar > .k-button-group {
  border-radius: 6px;
}
.k-spreadsheet-toolbar > .k-separator {
  border-color: #ebebeb;
}
.k-spreadsheet-toolbar .k-overflow-anchor {
  border-radius: 0;
}
.k-spreadsheet-popup {
  border-radius: 6px;
}
.k-spreadsheet-popup .k-separator {
  background-color: #ebebeb;
}
.k-spreadsheet-popup .k-button {
  background-color: transparent;
}
.k-spreadsheet-popup .k-button:hover {
  background-color: #fff;
}
.k-spreadsheet-popup .k-state-active {
  background-color: #eee;
  color: #ffffff;
}
.k-spreadsheet-popup .k-state-active:hover {
  background-color: #d5d5d5;
}
.k-spreadsheet-filter-menu .k-details {
  border-color: #ebebeb;
}
.k-spreadsheet-filter-menu .k-details-content .k-space-right {
  background-color: #fff;
}
.k-spreadsheet-filter-menu .k-spreadsheet-value-treeview-wrapper {
  background-color: #fff;
  border-color: #ebebeb;
  border-radius: 6px 0 0 6px;
}
.k-syntax-ref {
  color: #ff8822;
}
.k-syntax-num {
  color: #0099ff;
}
.k-syntax-func {
  font-weight: bold;
}
.k-syntax-str {
  color: #38b714;
}
.k-syntax-error {
  color: red;
}
.k-syntax-bool {
  color: #a9169c;
}
.k-syntax-startexp {
  font-weight: bold;
}
.k-syntax-paren-match {
  background-color: #caf200;
}
.k-series-a {
  border-color: #527aa3;
  background-color: rgba(82, 122, 163, 0.15);
}
.k-series-b {
  border-color: #6f91b3;
  background-color: rgba(111, 145, 179, 0.15);
}
.k-series-c {
  border-color: #8ca7c2;
  background-color: rgba(140, 167, 194, 0.15);
}
.k-series-d {
  border-color: #a8bdd1;
  background-color: rgba(168, 189, 209, 0.15);
}
.k-series-e {
  border-color: #c5d3e0;
  background-color: rgba(197, 211, 224, 0.15);
}
.k-series-f {
  border-color: #e2e9f0;
  background-color: rgba(226, 233, 240, 0.15);
}
.k-spreadsheet-sheets-remove:hover .k-icon {
  color: #cc2222;
}
.k-spreadsheet-formula-list .k-state-focused {
  background-color: #eee;
  color: #454545;
}
@media only screen and (-webkit-min-device-pixel-ratio: 2) {
  .k-icon.k-font-icon {
    background-image: none;
  }
}
.k-spreadsheet .k-widget[data-property='fontSize'] {
  width: 60px;
}
.k-spreadsheet .k-widget[data-property='format'] {
  width: 100px;
}
.k-spreadsheet .k-widget[data-property='fontFamily'] {
  width: 130px;
}
.k-spreadsheet-toolbar .k-combobox .k-input {
  color: #676767;
}
.k-spreadsheet-toolbar .k-combobox .k-state-hover .k-input,
.k-spreadsheet-toolbar .k-combobox .k-state-active .k-input,
.k-spreadsheet-toolbar .k-combobox .k-state-focused .k-input {
  color: #676767;
}
/* Checkbox */
.k-checkbox {
  width: 22px;
  height: 20px;
  background: url('Uniform/checkbox.png') no-repeat 2px center;
}
.k-checkbox.checked {
  background-position: -38px center;
}
.k-checkbox.checked.readonly {
  background-position: -58px center;
}
.k-checkbox.readonly {
  background-position: -18px center;
}
.k-checkbox:focus {
  outline: none;
  -webkit-box-shadow: 0 0 3px 0 rgba(0, 0, 0, 0.3);
          box-shadow: 0 0 3px 0 rgba(0, 0, 0, 0.3);
}
.k-state-selected .k-rowbox.k-checkbox {
  background-position: -38px center;
}
.k-autocomplete > .k-i-close,
.k-lov > .k-dropdown-wrap > .k-i-close,
.k-tledit > .k-dropdown-wrap > .k-i-close,
.k-combobox > .k-dropdown-wrap > .k-i-close,
.k-datepicker > .k-picker-wrap > .k-i-close,
.k-datetimepicker > .k-picker-wrap > .k-i-close,
.k-multiselect > .k-multiselect-wrap > .k-i-close {
  display: none;
  position: absolute;
  right: 0;
  right: 30px !important;
}
.k-autocomplete > .k-i-close {
  margin-right: 8px;
}
.k-datetimepicker > .k-picker-wrap > .k-i-close {
  right: 4em !important;
}
