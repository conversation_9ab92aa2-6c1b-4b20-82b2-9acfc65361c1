<template>
  <FormWindow ref="formWindow" @initData="initData" @loadData="loadData">
    <el-container v-loading="loading" style="height: calc(100vh - 84px);">
      <el-header
          style="padding-top: 10px;padding-bottom: 10px;border-bottom: solid 1px #f7f7f7;background-color: #FCFCFC">
        <span style="text-align: left;font-size: 24px;margin: 0;font-weight: 1000;">律师信息登记表</span>
        <div style="display: inline;float: right;margin-top: 0;margin-right: 30px">
          <el-button v-if="dataState !== 'view'" class="normal-btn" icon="el-icon-folder-checked" size="mini"
                     @click="save_()">保存
          </el-button>
        </div>
      </el-header>

      <el-main v-if="dataState !== 'view'">
        <el-scrollbar style="height: 100%" wrap-style="overflow-x: hidden;">
          <div style="margin: 10px">
            <span style="font-weight: bold;font-size: 16px;color: #5A5A5F;">基本信息</span>
          </div>
          <el-divider></el-divider>

          <el-form ref="dataForm" :model="detailData"
                   :rules="dataState !== 'view' ? rules : {}"
                   label-width="100px"
                   style="padding-right: 10px;">
            <div>
              <el-row style="margin-top: 10px">
                <el-col :span="24">
                  <el-form-item label="" prop="avatar">
                    <el-avatar :size="100" :src="detailData.avatar"></el-avatar>
                    <el-button v-if="dataState !== 'view'" style="margin-left: 10px;" type="primary"
                               @click="changeAvatar">修改头像
                    </el-button>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row>
                <el-col :span="8">
                  <el-form-item label="律师名称" prop="lawyerName">
                    <el-input v-if="dataState !== 'view'" v-model.trim="detailData.lawyerName" clearable
                              maxlength="20" placeholder="请输入..." show-word-limit style="width: 100%"
                              @clear="lawyerNameClear('main')">
                      <el-button slot="append" icon="el-icon-search" @click="chooseLawyerClick('main')"/>
                    </el-input>
                    <span v-else class="viewSpan">{{ detailData.lawyerName }}</span>
                  </el-form-item>
                </el-col>
                <el-col :span="16">
                  <el-form-item label="所属律所" prop="lawFirm">
                    <el-input v-if="dataState !== 'view'"
                              v-model="detailData.lawFirm" :title="detailData.lawFirm"
                              disabled style="width: 100%"/>
                    <span v-else class="viewSpan">{{ detailData.lawFirm }}</span>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row>
                <el-col :span="8">
                  <el-form-item label="性别" prop="sex">
                    <el-radio-group v-if="dataState !== 'view'" v-model="detailData.sex">
                      <el-radio border label="男">男</el-radio>
                      <el-radio border label="女">女</el-radio>
                    </el-radio-group>
                    <span v-else class="viewSpan">{{ detailData.sex }}</span>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="联系电话" prop="lawyerPhone">
                    <el-input v-if="dataState !== 'view'"
                              v-model="detailData.lawyerPhone"
                              clearable maxlength="20" placeholder="请输入..."
                              show-word-limit style="width: 100%"/>
                    <span v-else class="viewSpan">{{ detailData.lawyerPhone }}</span>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="电子邮箱" prop="lawyerEmail">
                    <el-input v-if="dataState !== 'view'"
                              v-model="detailData.lawyerEmail"
                              clearable maxlength="30" placeholder="请输入..."
                              show-word-limit style="width: 100%"/>
                    <span v-else class="viewSpan">{{ detailData.lawyerEmail }}</span>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row>
                <el-col :span="8" class="mylabel">
                  <el-form-item class="custom-word-break" label="从事律师执业时间" prop="lawyerTime">
                    <span slot="label">从事律师<br>执业时间</span>
                    <el-date-picker v-if="dataState !== 'view'"
                                    v-model="detailData.lawyerTime"
                                    clearable style="width: 100%"
                                    type="date" value-format="yyyy-MM-dd"/>
                    <span v-else class="viewSpan">{{ detailData.lawyerTime | parseTime }}</span>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="执业证号" prop="charteredNo">
                    <el-input v-if="dataState !== 'view'"
                              v-model="detailData.charteredNo"
                              :disabled="detailData.sourceHostLawyer" clearable maxlength="20" placeholder="请输入..."
                              show-word-limit style="width: 100%" @blur="charteredNoBlur"/>
                    <span v-else class="viewSpan">{{ detailData.charteredNo }}</span>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="主要执业地域" prop="majorRegion">
                    <el-input v-if="dataState !== 'view'"
                              v-model="detailData.majorRegion"
                              clearable maxlength="200" placeholder="请输入..." show-word-limit
                              style="width: 100%"/>
                    <span v-else class="viewSpan">{{ detailData.majorRegion }}</span>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row>
                <el-col :span="24">
                  <el-form-item label="擅长业务领域" prop="beGoodAtDomainIds">
                    <el-select v-if="dataState !== 'view'"
                               v-model="beGoodAtDomainIds_1"
                               multiple placeholder="请选择" style="width: 100%"
                               @change="beGoodAtDomainChange">
                      <el-option
                          v-for="item in SCLYData"
                          :key="item.id"
                          :label="item.dicName"
                          :value="item.id"
                      />
                    </el-select>
                    <span v-else class="viewSpan">{{ detailData.beGoodAtDomain }}</span>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row>
                <el-col :span="24">
                  <el-form-item label="备注" prop="remarks">
                    <el-input v-if="dataState !== 'view'"
                              v-model="detailData.remarks"
                              :autosize="{ minRows: 3, maxRows: 6}" clearable
                              maxlength="1000"
                              placeholder="请输入..." show-word-limit style="width: 100%" type="textarea"/>
                    <text-span v-else :text="detailData.remarks" class="viewSpan"/>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row>
                <el-col :span="24">
                  <el-form-item label="律师资质文件" prop="attachment">
                    <uploadDoc
                        v-model="detailData.attachment"
                        :disabled="dataState === 'view'"
                        :doc-path="docURL"
                        :files.sync="detailData.attachment"
                        :tips="tip_"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>

            <simple-board :data-state="dataState" :has-add="hasAdd" title="助理律师" @addBtn="addLawyer">
              <el-table
                  :data="detailData.lawyerList"
                  :height="200"
                  :show-overflow-tooltip="true"
                  border
                  fit
                  highlight-current-row
                  stripe
                  style="width: 100%"
              >
                <el-table-column align="center" label="序号" show-overflow-tooltip type="index" width="60"/>
                <el-table-column align="center" label="姓名" prop="lawyerName" show-overflow-tooltip/>
                <el-table-column align="center" label="联系电话" prop="lawyerPhone" show-overflow-tooltip/>
                <el-table-column align="center" label="擅长领域" prop="beGoodAtDomain" show-overflow-tooltip/>
                <el-table-column align="center" label="备注" prop="remarks" show-overflow-tooltip/>
                <el-table-column align="center" label="经办时间" prop="createTime" show-overflow-tooltip>
                  <template slot-scope="scope">
                    <span>{{ scope.row.createTime | parseTime }}</span>
                  </template>
                </el-table-column>
                <el-table-column align="center" label="操作" width="200px">
                  <template slot-scope="scope">
                    <el-button v-if="dataState !== 'view'
                     && scope.row.whetherSystem
                      && scope.row.currentStateCode === utils.dataState_LAWYER.YRK.code"
                               size="mini" type="text"
                               @click.native.prevent="CKRow(scope.$index, scope.row)">出库
                    </el-button>
                    <el-button v-if="dataState !== 'view'
                     && scope.row.whetherSystem
                     && scope.row.currentStateCode === utils.dataState_LAWYER.YCK.code"
                               size="mini" type="text"
                               @click.native.prevent="QXCKRow(scope.$index, scope.row)">取消出库
                    </el-button>
                    <el-button v-if="dataState !== 'view'
                     && scope.row.currentStateCode !== utils.dataState_LAWYER.YCK.code"
                               size="mini" type="text"
                               @click="editRow(scope.$index, scope.row)">编辑
                    </el-button>
                    <el-button
                        v-if="dataState !== 'view'
                         && !scope.row.whetherSystem"
                        size="mini" type="text"
                        @click="deleteRow(scope.$index, scope.row)">删除
                    </el-button>
                    <el-button
                        v-if="dataState === 'view'"
                        size="mini" type="text"
                        @click="look(scope.$index,scope.row)">查看
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </simple-board>

            <simple-board :data-state="dataState" :has-add="hasAdd" title="代理信息" @addBtn="addAgent">
              <el-table
                  :data="detailData.agentList"
                  :height="200"
                  :show-overflow-tooltip="true"
                  border
                  fit
                  highlight-current-row
                  stripe
                  style="width: 100%"
              >
                <el-table-column align="center" label="序号" show-overflow-tooltip type="index" width="60"/>
                <el-table-column align="center" label="类型" prop="selectionTypeName" show-overflow-tooltip/>
                <el-table-column align="center" label="名称" prop="name" show-overflow-tooltip/>
                <!--                <el-table-column align="center" label="状态" prop="type" show-overflow-tooltip/>-->
                <el-table-column align="center" label="经办时间" prop="createTime" show-overflow-tooltip>
                  <template slot-scope="scope">
                    <span>{{ scope.row.createTime | parseTime }}</span>
                  </template>
                </el-table-column>
                <el-table-column align="center" label="操作" width="200px">
                  <template slot-scope="scope">
                    <el-button v-if="dataState !== 'view'"
                               size="mini" type="text"
                               @click="editAgentRow(scope.$index, scope.row)">编辑
                    </el-button>
                    <el-button
                        v-if="dataState !== 'view'"
                        size="mini" type="text"
                        @click="deleteAgentRow(scope.$index, scope.row)">删除
                    </el-button>
                    <el-button
                        v-if="dataState === 'view'"
                        size="mini" type="text"
                        @click="lookAgent(scope.$index,scope.row)">查看
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </simple-board>

            <el-row style="margin-top: 20px;">
              <el-col :span="18">
                <el-form-item label="经办组织">
                  <span class="viewSpan">{{ detailData.createPsnFullName }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="经办时间">
                  <span class="viewSpan">{{ detailData.createTime| parseTime }}</span>
                </el-form-item>
              </el-col>
            </el-row>

          </el-form>
        </el-scrollbar>
      </el-main>

      <el-main v-else>
        <el-scrollbar style="height: 100%" wrap-style="overflow-x: hidden;">
          <el-form ref="dataForm"
                   :model="detailData"
                   :rules="dataState !== 'view' ? rules : {}"
                   label-width="100px"
                   style="margin-right: 20px;"
          >
            <SimpleBoardTitle style="margin-top: 5px;" title="基本信息">
              <table class="table_content" style="margin-top: 10px;">
                <tbody>
                <tr>
                  <th class="th_label" colspan="2">律师名称</th>
                  <td class="td_value" colspan="6">{{ detailData.lawyerName }}</td>
                  <th class="th_label" colspan="2">所属律所</th>
                  <td class="td_value" colspan="13">{{ detailData.lawFirm }}</td>
                  <th class="td_value" colspan="1"><img :src="detailData.avatar" alt=""
                                                        style="width: 100%;height: auto;"/></th>
                </tr>
                </tbody>

                <tbody>
                <tr>
                  <th class="th_label" colspan="2">性别</th>
                  <td class="td_value" colspan="6">{{ detailData.sex }}</td>
                  <th class="th_label" colspan="2">联系电话</th>
                  <td class="td_value" colspan="6">{{ detailData.lawyerPhone }}</td>
                  <th class="th_label" colspan="2">电子邮箱</th>
                  <td class="td_value" colspan="6">{{ detailData.lawyerEmail }}</td>
                </tr>
                </tbody>

                <tbody>
                <tr>
                  <th class="th_label" colspan="2">从事律师<br>执业时间</th>
                  <td class="td_value" colspan="6">{{ detailData.lawyerTime | parseTime }}</td>
                  <th class="th_label" colspan="2">执业证号</th>
                  <td class="td_value" colspan="14">{{ detailData.charteredNo }}</td>
                </tr>
                </tbody>

                <tbody>
                <tr>
                  <th class="th_label" colspan="2">擅长业务领域</th>
                  <td class="td_value" colspan="22">{{ detailData.beGoodAtDomain }}</td>
                </tr>
                </tbody>

                <tbody>
                <tr>
                  <th class="th_label" colspan="2">主要执业地域</th>
                  <td class="td_value" colspan="22">{{ detailData.majorRegion }}</td>
                </tr>
                </tbody>

                <tbody>
                <tr>
                  <th class="th_label_" colspan="2">备注</th>
                  <td class="td_value_" colspan="22">{{ detailData.remarks }}</td>
                </tr>
                </tbody>

                <tbody>
                <tr>
                  <th class="th_label_" colspan="2">律师资质文件</th>
                  <td class="td_value_" colspan="22">
                    <uploadDoc
                        v-model="detailData.attachment"
                        :disabled="dataState === 'view'"
                        :doc-path="docURL"
                        :files.sync="detailData.attachment"
                        :tips="tip_"
                    />
                  </td>
                </tr>
                </tbody>
              </table>
            </SimpleBoardTitle>

            <SimpleBoardTitle style="margin-top: 10px;" title="律师信息">
              <simple-board
                  :data-state="dataState"
                  :has-add="false"
                  style="margin-top: 10px;"
                  title="助理律师"
              >
                <el-table
                    :data="detailData.lawyerList"
                    :height="200"
                    :show-overflow-tooltip="true"
                    border
                    fit
                    highlight-current-row
                    stripe
                    style="width: 100%"
                >
                  <el-table-column align="center" label="序号" show-overflow-tooltip type="index" width="60"/>
                  <el-table-column align="center" label="姓名" prop="lawyerName" show-overflow-tooltip/>
                  <el-table-column align="center" label="联系电话" prop="lawyerPhone" show-overflow-tooltip/>
                  <el-table-column align="center" label="擅长领域" prop="beGoodAtDomain" show-overflow-tooltip/>
                  <el-table-column align="center" label="备注" prop="remarks" show-overflow-tooltip/>
                  <el-table-column align="center" label="经办时间" prop="createTime" show-overflow-tooltip>
                    <template slot-scope="scope">
                      <span>{{ scope.row.createTime | parseTime }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column align="center" label="操作" width="200px">
                    <template slot-scope="scope">
                      <el-button v-if="dataState !== 'view'
                     && scope.row.whetherSystem
                      && scope.row.currentStateCode === utils.dataState_LAWYER.YRK.code"
                                 size="mini" type="text"
                                 @click.native.prevent="CKRow(scope.$index, scope.row)">出库
                      </el-button>
                      <el-button v-if="dataState !== 'view'
                     && scope.row.whetherSystem
                     && scope.row.currentStateCode === utils.dataState_LAWYER.YCK.code"
                                 size="mini" type="text"
                                 @click.native.prevent="QXCKRow(scope.$index, scope.row)">取消出库
                      </el-button>
                      <el-button v-if="dataState !== 'view'
                     && scope.row.currentStateCode !== utils.dataState_LAWYER.YCK.code"
                                 size="mini" type="text"
                                 @click="editRow(scope.$index, scope.row)">编辑
                      </el-button>
                      <el-button
                          v-if="dataState !== 'view'
                         && !scope.row.whetherSystem"
                          size="mini" type="text"
                          @click="deleteRow(scope.$index, scope.row)">删除
                      </el-button>
                      <el-button
                          v-if="dataState === 'view'"
                          size="mini" type="text"
                          @click="look(scope.$index,scope.row)">查看
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </simple-board>
            </SimpleBoardTitle>

            <SimpleBoardTitle style="margin-top: 10px;" title="代理信息">
              <simple-board
                  :data-state="dataState"
                  :has-add="false" style="margin-top: 10px;"
                  title="代理信息"
              >
                <el-table
                    :data="detailData.agentList"
                    :height="200"
                    :show-overflow-tooltip="true"
                    border
                    fit
                    highlight-current-row
                    stripe
                    style="width: 100%"
                >
                  <el-table-column align="center" label="序号" show-overflow-tooltip type="index" width="60"/>
                  <el-table-column align="center" label="类型" prop="selectionTypeName" show-overflow-tooltip/>
                  <el-table-column align="center" label="名称" prop="name" show-overflow-tooltip/>
                  <el-table-column align="center" label="状态" prop="type" show-overflow-tooltip/>
                  <el-table-column align="center" label="经办时间" prop="createTime" show-overflow-tooltip>
                    <template slot-scope="scope">
                      <span>{{ scope.row.createTime | parseTime }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column align="center" label="操作" width="200px">
                    <template slot-scope="scope">
                      <el-button v-if="dataState !== 'view'"
                                 size="mini" type="text"
                                 @click="editAgentRow(scope.$index, scope.row)">编辑
                      </el-button>
                      <el-button
                          v-if="dataState !== 'view'"
                          size="mini" type="text"
                          @click="deleteAgentRow(scope.$index, scope.row)">删除
                      </el-button>
                      <el-button
                          v-if="dataState === 'view'"
                          size="mini" type="text"
                          @click="lookAgent(scope.$index,scope.row)">查看
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </simple-board>
            </SimpleBoardTitle>

            <div style="height: 100%;">
              <el-row style="margin-top: 20px;">
                <el-col :span="18">
                  <el-form-item label="经办组织">
                    <span class="viewSpan">{{ detailData.createPsnFullName }}</span>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="经办时间">
                    <span class="viewSpan">{{ detailData.createTime| parseTime }}</span>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-form>
        </el-scrollbar>
      </el-main>

      <el-form ref="lawyerRow"
               :model="lawyerRow"
               :rules="dataState !== 'view' ? rules2 : {}" label-width="80px">
        <!--新增律师人员-->
        <el-dialog :close-on-click-modal="false" :title="lawyerRowTitle" :visible.sync="orgVisible" width="50%">
          <div class="el-dialog-div">
            <el-row>
              <el-col :span="24">
                <el-form-item label="律师姓名" prop="lawyerName">
                  <el-input v-if="dataState !== 'view'"
                            v-model.trim="lawyerRow.lawyerName"
                            clearable maxlength="20" placeholder="请输入..." show-word-limit
                            style="width: 100%" @clear="lawyerNameClear('detail')">
                    <el-button slot="append" icon="el-icon-search" @click="chooseLawyerClick('detail')"/>
                  </el-input>
                  <span v-else class="viewSpan">{{ lawyerRow.lawyerName }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="联系电话">
                  <el-input v-if="dataState !== 'view'"
                            v-model="lawyerRow.lawyerPhone"
                            clearable maxlength="20" placeholder="请输入..." show-word-limit style="width: 100%"/>
                  <span v-else class="viewSpan">{{ lawyerRow.lawyerPhone }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="擅长领域">
                  <el-select v-if="dataState !== 'view'"
                             v-model="beGoodAtDomainIds_2"
                             multiple placeholder="请选择" style="width: 99.5%"
                             @change="beGoodAtDomainChange_">
                    <el-option
                        v-for="item in SCLYData"
                        :key="item.id"
                        :label="item.dicName"
                        :value="item.id"
                    />
                  </el-select>
                  <span v-else class="viewSpan">{{ lawyerRow.beGoodAtDomain }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="备注">
                  <el-input v-if="dataState !== 'view'"
                            v-model="lawyerRow.remarks"
                            :autosize="{ minRows: 3, maxRows: 6}" clearable
                            maxlength="1000"
                            placeholder="请输入..." show-word-limit style="width: 100%" type="textarea"/>
                  <text-span v-else :text="lawyerRow.remarks" class="viewSpan"/>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
          <span slot="footer" class="dialog-footer">
            <el-button class="negative-btn" icon="" @click="cancel_">取消</el-button>
            <el-button v-if="dataState !== 'view'"
                       class="active-btn" icon="" type="primary"
                       @click="choiceDeptSure_">确定</el-button>
          </span>
        </el-dialog>
      </el-form>

      <el-form ref="agentRow"
               :model="agentRow"
               :rules="dataState !== 'view' ? rules3 : {}" label-width="80px">
        <!--新增代理信息-->
        <el-dialog :close-on-click-modal="false" :title="agentRowTitle" :visible.sync="agentDialogVisible" width="50%">
          <div class="el-dialog-div">
            <el-row>
              <el-col :span="24">
                <el-form-item label="类型" prop="selectionTypeId">
                  <el-select v-if="dataState !== 'view'" v-model="agentRow.selectionTypeId"
                             clearable placeholder="请选择" style="width:100%"
                             @change="selectionTypeChange" @clear="selectionTypeClear">
                    <el-option
                        v-for="item in utils.selectionType_data"
                        :key="item.id"
                        :label="item.dicName"
                        :value="item.id"
                    />
                  </el-select>
                  <span v-else class="viewSpan">{{ agentRow.selectionTypeName }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="名称" prop="name">
                  <el-input v-if="dataState !== 'view'" v-model="agentRow.name"
                            class="input-with-select" clearable maxlength="50"
                            placeholder="请输入..." show-word-limit
                            style="width: 100%"/>
                  <span v-else class="viewSpan">{{ agentRow.relationCaseName }}</span>
                </el-form-item>
              </el-col>

              <el-col v-if="agentRow.selectionTypeId === '2'" :span="12">
                <el-form-item label="关联案件" prop="relationCaseName">
                  <el-input v-if="dataState !== 'view'" v-model="agentRow.relationCaseName" disabled
                            maxlength="20" placeholder="请输入..." show-word-limit style="width: 100%">
                    <el-button slot="append" icon="el-icon-search" @click="choiceCaseClick"/>
                  </el-input>
                  <span v-else class="viewSpan">{{ agentRow.relationCaseName }}</span>
                </el-form-item>
              </el-col>

              <el-col v-if="agentRow.selectionTypeId === '2'" :span="12">
                <el-form-item label="代理阶段">
                  <el-select v-if="dataState !== 'view'" v-model="agentStageIds" multiple style="width: 100%;"
                             @change="agentStageOnChange">
                    <el-option v-for="item in processData" :key="item.code" :label="item.name" :value="item.code"/>
                  </el-select>
                  <span v-else class="viewSpan">{{ agentRow.agentStageName }}</span>
                </el-form-item>
              </el-col>

              <el-col v-if="agentRow.selectionTypeId === '3'" :span="12">
                <el-form-item label="开始时间" prop="beginTime">
                  <el-date-picker v-if="dataState !== 'view'"
                                  v-model="agentRow.beginTime"
                                  clearable style="width: 100%" type="date" value-format="yyyy-MM-dd"/>
                  <span v-else class="viewSpan">{{ agentRow.beginTime | parseTime }}</span>
                </el-form-item>
              </el-col>

              <el-col v-if="agentRow.selectionTypeId === '3'" :span="12">
                <el-form-item label="结束时间" prop="endTime">
                  <el-date-picker v-if="dataState !== 'view'"
                                  v-model="agentRow.endTime"
                                  clearable style="width: 100%" type="date" value-format="yyyy-MM-dd"/>
                  <span v-else class="viewSpan">{{ agentRow.endTime | parseTime }}</span>
                </el-form-item>
              </el-col>

              <el-col :span="24">
                <el-form-item label="详细描述" prop="description">
                  <el-input v-if="dataState !== 'view'" v-model="agentRow.description"
                            :autosize="{ minRows: 3, maxRows: 6}" clearable
                            maxlength="1000" placeholder="请输入..."
                            show-word-limit style="width: 100%" type="textarea"/>
                  <text-span v-else :text=" agentRow.description" class="viewSpan"/>
                </el-form-item>
              </el-col>

              <el-col :span="24" prop="attachment">
                <el-form-item label="附件" prop="attachment">
                  <uploadDoc
                      v-model="agentRow.attachment"
                      :disabled="dataState === 'view'"
                      :doc-path="docURL"
                      :files.sync="agentRow.attachment"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
          <span slot="footer" class="dialog-footer">
            <el-button class="negative-btn" icon="" @click="cancelAgent">取消</el-button>
            <el-button v-if="dataState !== 'view'"
                       class="active-btn" icon="" type="primary"
                       @click="agentSure">确定</el-button>
          </span>
        </el-dialog>
      </el-form>

      <lawyerDialog :dialog-visible.sync="lawyerDialogVisible" @lawyerSure="lawyerSure"/>

      <!--案件弹框-->
      <caseDialog :is-multiple="false" :visible.sync="caseDialogVisible" @onSure="caseSure_"/>

      <!--上传头像-->
      <UploadAvatarDialog :dialog-visible.sync="avatarDialogVisible" @avatarSure="avatarSure"/>

    </el-container>
  </FormWindow>
</template>

<script>
import FormWindow from '@/view/components/FormWindow/FormWindow'
import SimpleBoard from "@/view/components/SimpleBoard/SimpleBoardViewCase"
import textSpan from '@/view/components/TextSpan/TextSpan'
import lawyerDialog from '@/view/litigation/LawyerManage/LawyerFirm/dialog/LawyerSelectOutDialog'
import lawyerApi from '@/api/LawyerManage/LawyerFirm/lawyer'
import dictApi from '@/api/_system/dict'
import {mapGetters} from 'vuex'
import UploadDoc from '@/view/components/UploadDoc/UploadDoc'
import caseDialog from '@/view/litigation/dialog/CaseDialog'
import {isZC} from "@/view/utils/constants"
import SimpleBoardView from "@/view/components/SimpleBoard/SimpleBoardView"
import SimpleBoardTitle from "../../../components/SimpleBoard/SimpleBoardTitle"
import UploadAvatarDialog from "@/view/litigation/LawyerManage/LawyerFirm/dialog/UploadAvatarDialog"


export default {
  name: 'MainLawyer',
  inject: ['layout', 'mcpLayout'],
  components: {
    FormWindow,
    SimpleBoard,
    textSpan,
    lawyerDialog,
    UploadDoc,
    caseDialog,
    SimpleBoardView,
    SimpleBoardTitle,
    UploadAvatarDialog
  },
  computed: {
    ...mapGetters([
      'orgContext'
    ]),
    beGoodAtDomainIds_1: {
      set: function (data) {
        this.detailData.beGoodAtDomainIds = data.join(',')
      },
      get: function () {
        if (this.detailData.beGoodAtDomainIds) {
          return this.detailData.beGoodAtDomainIds.split(',')
        }
        return []
      }
    },
    beGoodAtDomainIds_2: {
      set: function (data) {
        this.lawyerRow.beGoodAtDomainIds = data.join(',')
      },
      get: function () {
        if (this.lawyerRow.beGoodAtDomainIds) {
          return this.lawyerRow.beGoodAtDomainIds.split(',')
        }
        return []
      }
    },
    lawyerRowTitle() {
      return this.dataState !== 'view' ? '新增助理律师' : '查看助理律师'
    },
    agentRowTitle() {
      return this.dataState !== 'view' ? '新增代理信息' : '查看代理信息'
    },
    agentStageIds: {
      set: function (data) {
        this.agentRow.agentStageId = data.join(',')
      },
      get: function () {
        if (this.agentRow.agentStageId) {
          return this.agentRow.agentStageId.split(',')
        }
        return []
      }
    },
    isZc: function () {
      return isZC(this.processName)
    },
    processData: function () {
      if (this.isZc) return [{code: 'all', name: '全过程'}, ...this.utils.caseProcessData.ZC]
      return [{code: 'all', name: '全过程'}, ...this.utils.caseProcessData.MSSS]
    },
  },
  data() {
    return {
      avatarDialogVisible: false,
      fileList: [],
      proofImage: null,
      typeName: null,   // 库类别名称
      typeCode: null,  // 库类别代码
      loading: true,
      detailData: {
        id: this.utils.createUUID(),
        lawyerName: null, // 律师姓名
        lawFirm: null, // 律所名称
        lawFirmId: null, // 律所ID
        beginDate: null, // 服务开始时间
        lawyerPhone: null, // 联系电话
        lawyerEmail: null, // 电子邮箱
        lawyerTime: null, // 从事律师执业时间
        charteredNo: null, // 执业证号
        beGoodAtDomain: null, // 擅长领域
        beGoodAtDomainIds: null, // 擅长领域
        remarks: null, // 简介/备注
        attachment: null, // 依据资料
        whetherHostLawyer: true, // 是否主办律师（是-主律师；否-助理律师）
        createOgnName: null, //  经办单位
        createDeptName: null, // 经办部门
        createPsnName: null, // 经办人员
        createTime: null, // 经办时间
        createOgnId: null,
        createDeptId: null,
        createPsnId: null,
        createPsnFullId: null,
        createPsnFullName: null,
        createGroupId: null,
        createGroupName: null,
        createOrgId: null,
        createOrgName: null,
        parentId: null,
        dataSource: 'new',
        sourceId: null,
        sourceHostLawyer: null,
        updateTime: null, // 更新时间
        whetherSystem: false,
        currentState: null,
        currentStateCode: null,
        lawyerList: [], // 弹框的表格数据源
        agentList: [],  // 代理信息
        typeName: null,   // 库类别名称
        typeCode: null,  // 库类别代码
        majorRegion: null, // 主要执业地域
        lawFirmType: null, // 律所类型
        sex: null, //性别
        avatar: null, //头像
      },
      rules: {
        lawyerName: [{required: true, message: '请输入律师名称', trigger: 'blur'}],
        charteredNo: [{required: true, message: '请输入执业证号', trigger: 'blur'}],
        lawyerPhone: [{required: true, message: '请输入联系电话', trigger: 'blur'}],
        lawyerTime: [{required: true, message: '请输入从事律师执业时间', trigger: 'blur'}],
        beGoodAtDomainIds: [{required: true, message: '请输入擅长业务领域', trigger: 'blur'}],
        majorRegion: [{required: true, message: '请输入主要执业地域', trigger: 'blur'}],
        //attachment: [{required: true, message: '请上传律师资质文件', trigger: 'blur'}],
      },
      rules2: {
        lawyerName: [{required: true, message: '请输入律师名称', trigger: 'blur'}]
      },
      rules3: {
        selectionTypeId: [{required: true, message: '请选择名称', trigger: 'blur'}],
        name: [{required: true, message: '请输入名称', trigger: 'blur'}],
      },
      dataState: null,
      SCLYData: [],
      hasAdd: true,
      orgVisible: false,
      lawyerRow: {
        id: null,
        lawyerName: null, // 律师姓名
        lawFirm: null, // 律所名称
        lawFirmId: null, // 律所ID
        beginDate: null, // 服务开始时间
        lawyerPhone: null, // 联系电话
        lawyerEmail: null, // 电子邮箱
        lawyerTime: null, // 从事律师执业时间
        charteredNo: null, // 执业证号
        beGoodAtDomain: null, // 擅长领域
        beGoodAtDomainIds: null, // 擅长领域
        remarks: null, // 简介/备注
        whetherHostLawyer: false, // 是否主办律师（是-主律师；否-助理律师）
        createOgnName: null, //  经办单位
        createDeptName: null, // 经办部门
        createPsnName: null, // 经办人员
        createTime: null, // 经办时间
        createOgnId: null,
        createDeptId: null,
        createPsnId: null,
        createPsnFullId: null,
        createPsnFullName: null,
        createGroupId: null,
        createGroupName: null,
        createOrgId: null,
        createOrgName: null,
        dataSource: 'new',
        sourceId: null,
        whetherSystem: false,
        updateTime: null, // 更新时间
        parentId: null,
        typeName: null,   // 库类别名称
        typeCode: null,  // 库类别代码
        majorRegion: null, // 主要执业地域
        lawFirmType: null, //律所类型
      },
      agentRow: {
        id: null,
        lawFirm: null, // 律所名称
        lawFirmId: null, // 律所ID
        lawyerName: null, // 律师姓名
        lawyerId: null, // 律师id
        charteredNo: null, // 执业证号
        parentId: null,
        selectionTypeId: null, // 类型id
        selectionTypeName: null, // 类型名称
        beginTime: null, // 开始时间
        endTime: null, // 结束时间
        name: null,    // 名称
        type: null, // 状态
        attachment: null, // 附件
        relationCaseName: null, // 关联案件
        relationCaseId: null, // 关联案件ID
        agentStageName: null, // 代理阶段
        agentStageId: null, // 代理阶段ID
        description: null, // 详细描述
        dataSource: 'new',
        createOgnName: null, //  经办单位
        createDeptName: null, // 经办部门
        createPsnName: null, // 经办人员
        createTime: null, // 经办时间
        updateTime: null, // 更新时间
        createOgnId: null,
        createDeptId: null,
        createPsnId: null,
        createPsnFullId: null,
        createPsnFullName: null,
        createGroupId: null,
        createGroupName: null,
        createOrgId: null,
        createOrgName: null,
      },
      index: null,
      lawyerDialogVisible: false,
      agentDialogVisible: false,
      caseDialogVisible: false,
      lawyerDialogVisibleSource: null,
      docURL: '/firmIn',
      tip_: '上传律师证等其他文件，支持上传pdf、word文件',
      typeData: [
        {id: 1, name: '未完成'},
        {id: 2, name: '已完成'},
        {id: 3, name: '不适用'},
      ],
    }
  },
  created() {
    this.baseDataLoad()
    this.isHeadquarter()
  },
  methods: {
    deleteRow(index) {
      this.detailData.lawyerList.splice(index, 1)
    },
    editRow(index, row) {
      this.lawyerRow = {}
      this.lawyerRow = Object.assign({}, row)
      this.lawyerRow.state = 'edit'
      this.index = index
      this.orgVisible = true
    },
    look(index, row) {
      this.lawyerRow = {}
      this.lawyerRow = Object.assign({}, row)
      this.orgVisible = true
    },
    editAgentRow(index, row) {
      this.agentRow = {}
      this.agentRow = Object.assign({}, row)
      this.agentRow.state = 'edit'
      this.index = index
      this.agentDialogVisible = true
    },
    deleteAgentRow(index) {
      this.detailData.agentList.splice(index, 1)
    },
    lookAgent(index, row) {
      this.agentRow = {}
      this.agentRow = Object.assign({}, row)
      this.agentDialogVisible = true
    },
    CKRow(index, row) {
      row.currentState = this.utils.dataState_LAWYER.YCK.name
      row.currentStateCode = this.utils.dataState_LAWYER.YCK.code
      lawyerApi.changeOut(row).then(() => {
      })
    },
    QXCKRow(index, row) {
      row.currentState = this.utils.dataState_LAWYER.YRK.name
      row.currentStateCode = this.utils.dataState_LAWYER.YRK.code
      lawyerApi.changeOut(row).then(() => {
      })
    },
    lawyerSure(val) {
      if (val) {
        if (this.lawyerDialogVisibleSource === 'main') {
          this.detailData.dataSource = 'old'
          this.detailData.lawyerName = val.lawyerName
          this.detailData.sourceId = val.id
          this.detailData.sourceHostLawyer = val.whetherHostLawyer
          this.detailData.charteredNo = val.charteredNo
          this.detailData.beginDate = val.beginDate
          this.detailData.lawyerPhone = val.lawyerPhone // 联系电话
          this.detailData.lawyerEmail = val.lawyerEmail // 电子邮箱
          this.detailData.lawyerTime = val.lawyerTime // 从事律师执业时间
          this.detailData.beGoodAtDomain = val.beGoodAtDomain // 擅长领域
          this.detailData.beGoodAtDomainIds = val.beGoodAtDomainIds // 擅长领域
          this.detailData.remarks = val.remarks // 简介/备注
          this.detailData.majorRegion = val.majorRegion // 主要执业地区
        } else if (this.lawyerDialogVisibleSource === 'detail') {
          this.lawyerRow.dataSource = 'old'
          this.lawyerRow.lawyerName = val.lawyerName
          this.lawyerRow.sourceId = val.id
          this.lawyerRow.sourceHostLawyer = val.whetherHostLawyer
          this.lawyerRow.charteredNo = val.charteredNo
          this.lawyerRow.beginDate = val.beginDate
          this.lawyerRow.lawyerPhone = val.lawyerPhone // 联系电话
          this.lawyerRow.lawyerEmail = val.lawyerEmail // 电子邮箱
          this.lawyerRow.lawyerTime = val.lawyerTime // 从事律师执业时间
          this.lawyerRow.beGoodAtDomain = val.beGoodAtDomain // 擅长领域
          this.lawyerRow.beGoodAtDomainIds = val.beGoodAtDomainIds // 擅长领域
          this.lawyerRow.remarks = val.remarks // 简介/备注
          this.detailData.majorRegion = val.majorRegion // 主要执业地区
        }
      }
      this.lawyerDialogVisible = false
    },
    choiceDeptSure_() {
      this.$refs['lawyerRow'].validate((valid) => {
        if (valid) {
          this.lawyerRow.updateTime = new Date()
          if (this.lawyerRow.state === 'edit') {
            Object.assign(this.detailData.lawyerList[this.index], JSON.parse(JSON.stringify(this.lawyerRow)))
          } else {
            this.detailData.lawyerList.push(JSON.parse(JSON.stringify(this.lawyerRow)))
          }
          this.orgVisible = false
        } else {
          return false
        }
      })
    },
    agentSure() {
      this.$refs['agentRow'].validate((valid) => {
        if (valid) {
          this.agentRow.updateTime = new Date()
          if (this.agentRow.state === 'edit') {
            Object.assign(this.detailData.agentList[this.index], JSON.parse(JSON.stringify(this.agentRow)))
          } else {
            this.detailData.agentList.push(JSON.parse(JSON.stringify(this.agentRow)))
          }
          this.agentDialogVisible = false
        } else {
          return false
        }
      })
    },
    cancel_() {
      this.orgVisible = false
    },
    cancelAgent() {
      this.agentDialogVisible = false
    },
    addLawyer() {
      const orgutil = this.orgContext
      const row = {
        id: this.utils.createUUID(),
        lawFirm: this.detailData.lawFirm, // 律所名称
        lawFirmId: this.detailData.lawFirmId, // 律所名称
        beginDate: new Date(),
        lawyerPhone: null, // 联系电话
        lawyerEmail: null, // 电子邮箱
        lawyerTime: null, // 从事律师执业时间
        charteredNo: null, // 执业证号
        beGoodAtDomain: null, // 擅长领域
        beGoodAtDomainIds: null, // 擅长领域
        remarks: null, // 简介/备注
        majorRegion: null, // 主要执业地区
        whetherHostLawyer: false,
        dataSource: 'new',
        whetherSystem: false,
        parentId: this.detailData.id,
        createOgnId: orgutil.currentOgnId,
        createOgnName: orgutil.currentOgnName,
        createDeptId: orgutil.currentDeptId,
        createDeptName: orgutil.currentDeptName,
        createPsnId: orgutil.currentPsnId,
        createPsnName: orgutil.currentPsnName,
        createPsnFullId: orgutil.currentPsnFullId,
        createPsnFullName: orgutil.currentPsnFullName,
        createGroupId: orgutil.currentGroupId,
        createGroupName: orgutil.currentGroupName,
        createOrgId: orgutil.currentOrgId,
        createOrgName: orgutil.currentOrgName,
        createTime: new Date(),
        updateTime: null,
        sourceId: null,
        state: 'new',
        typeCode: this.detailData.typeCode,
        typeName: this.detailData.typeName,
      }
      for (const key in this.lawyerRow) {
        this.lawyerRow[key] = null
      }
      Object.assign(this.lawyerRow, JSON.parse(JSON.stringify(row)))
      this.orgVisible = true
    },
    addAgent() {
      for (const key in this.agentRow) {
        this.agentRow[key] = null
      }
      const orgutil = this.orgContext
      const row = {
        id: this.utils.createUUID(),
        lawFirm: this.detailData.lawFirm, // 律所名称
        lawFirmId: this.detailData.lawFirmId, // 律所id
        lawyerName: this.detailData.lawyerName, // 律师名称
        lawyerId: this.detailData.id, // 律师id
        charteredNo: this.detailData.charteredNo, // 执业证号
        dataSource: 'new',
        parentId: this.detailData.id,
        state: 'new',
        createOgnId: orgutil.currentOgnId,
        createOgnName: orgutil.currentOgnName,
        createDeptId: orgutil.currentDeptId,
        createDeptName: orgutil.currentDeptName,
        createPsnId: orgutil.currentPsnId,
        createPsnName: orgutil.currentPsnName,
        createPsnFullId: orgutil.currentPsnFullId,
        createPsnFullName: orgutil.currentPsnFullName,
        createGroupId: orgutil.currentGroupId,
        createGroupName: orgutil.currentGroupName,
        createOrgId: orgutil.currentOrgId,
        createOrgName: orgutil.currentOrgName,
        createTime: new Date(),
        updateTime: null,
      }
      Object.assign(this.agentRow, JSON.parse(JSON.stringify(row)))
      this.agentDialogVisible = true
    },
    initData(temp, dataState) {
      Object.assign(this.detailData, temp)
      this.dataState = dataState
      this.detailData.lawFirmId = this.$route.query.mainData.id
      if (this.$route.query.mainData.lawFirmType === '正式律所') {
        this.detailData.dataState = this.utils.dataState_LAWYER.YRK.name
        this.detailData.dataStateCode = this.utils.dataState_LAWYER.YRK.code
      } else {
        this.detailData.dataState = this.utils.dataState_LAWYER.DXP.name
        this.detailData.dataStateCode = this.utils.dataState_LAWYER.DXP.code
      }
      this.detailData.lawFirm = this.$route.query.mainData.lawyerFirm
      this.detailData.beginDate = new Date()
      this.detailData.typeCode = this.typeCode
      this.detailData.typeName = this.typeName
      this.loading = false
    },
    beGoodAtDomainChange(newVal) {
      if (newVal && newVal.length > 0) {
        let arr = []
        for (let i = 0; i < newVal.length; i++) {
          const newValElement = newVal[i]
          const name = this.utils.getDicName(this.SCLYData, newValElement)
          arr.push(name)
        }
        this.detailData.beGoodAtDomain = arr.join('、')
      }
    },
    beGoodAtDomainChange_(newVal) {
      if (newVal && newVal.length > 0) {
        let arr = []
        for (let i = 0; i < newVal.length; i++) {
          const newValElement = newVal[i]
          const name = this.utils.getDicName(this.SCLYData, newValElement)
          arr.push(name)
        }
        this.lawyerRow.beGoodAtDomain = arr.join('、')
      }
    },
    charteredNoBlur() {
      lawyerApi.distinct({
        id: this.detailData.id,
        code: this.detailData.charteredNo,
        createOgnId: this.detailData.createOgnId
      }).then((res) => {
        const flag = res.data.data
        if (flag) {
          this.$message({
            showClose: true,
            message: '执业证号重复！',
            type: 'warning'
          })
          this.detailData.charteredNo = null
        }
      })
    },
    // 根据数据ID加载数据
    loadData(dataState, dataId) {
      const me = this;
      me.dataState = dataState
      lawyerApi.queryDataById({id: dataId}).then(res => {
        me.detailData = res.data.data
      })
      if (dataState !== 'view') {
        me.detailData.lawFirmId = this.$route.query.id
        me.detailData.lawFirm = this.$route.query.lawyerFirm
      }
      this.loading = false
    },
    save_() {
      if (!this.detailData.sourceHostLawyer) {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            new Promise((resolue) => {
              lawyerApi.distinct({
                id: this.detailData.id,
                code: this.detailData.charteredNo,
                createOgnId: this.detailData.createOgnId
              }).then((res) => {
                const flag = res.data.data
                if (flag) {
                  this.$message({
                    showClose: true,
                    message: '执业证号重复！',
                    type: 'warning'
                  })
                  this.detailData.charteredNo = null
                }
                resolue(res)
              })
            }).then(() => {
              this.detailData.updateTime = new Date()
              lawyerApi.save(this.detailData).then(() => {
                this.$message.success('保存成功！')
                this.mcpLayout.closeTab();//关闭页面
              })
            })
          } else {
            return false
          }
        })
      } else {
        this.detailData.updateTime = new Date()
        lawyerApi.save(this.detailData).then(() => {
          this.$message.success('保存成功！')
          this.mcpLayout.closeTab();//关闭页面
        })
      }
    },
    chooseLawyerClick(val) {
      this.lawyerDialogVisibleSource = val
      this.lawyerDialogVisible = true
    },
    lawyerNameClear(val) {
      if (val === 'main') {
        if (this.detailData.dataSource === 'old') {
          this.detailData.sourceId = null
          this.detailData.dataSource = 'new'
          this.detailData.sourceHostLawyer = null
          this.detailData.charteredNo = null
          this.detailData.beginDate = null
          this.detailData.lawyerPhone = null // 联系电话
          this.detailData.lawyerEmail = null // 电子邮箱
          this.detailData.lawyerTime = null // 从事律师执业时间
          this.detailData.beGoodAtDomain = null // 擅长领域
          this.detailData.beGoodAtDomainIds = null // 擅长领域
          this.detailData.remarks = null // 简介/备注
          this.detailData.majorRegion = null // 主要执业地区
        }
      } else {
        if (this.lawyerRow.dataSource === 'old') {
          this.lawyerRow.sourceId = null
          this.lawyerRow.dataSource = 'new'
          this.lawyerRow.sourceHostLawyer = null
          this.lawyerRow.charteredNo = null
          this.lawyerRow.beginDate = null
          this.lawyerRow.lawyerPhone = null // 联系电话
          this.lawyerRow.lawyerEmail = null // 电子邮箱
          this.lawyerRow.lawyerTime = null // 从事律师执业时间
          this.lawyerRow.beGoodAtDomain = null // 擅长领域
          this.lawyerRow.beGoodAtDomainIds = null // 擅长领域
          this.lawyerRow.remarks = null // 简介/备注
          this.detailData.majorRegion = null // 主要执业地区
        }
      }
    },
    baseDataLoad() {
      dictApi.showSelect({
        dicCode: 'LS-SCLY'
      }).then(response => {
        this.SCLYData = response.data.data
      })
    },
    isHeadquarter() {
      // const createOgnId = this.orgContext.currentOgnId
      //
      // if (createOgnId === '15033708970596') {
      this.typeCode = 1
      this.typeName = '推荐库'
      // } else {
      //   this.typeCode = 0
      //   this.typeName = '资源库'
      // }
    },
    selectionTypeChange(newVal) {
      this.agentRow.selectionTypeName = this.utils.getDicName(this.utils.selectionType_data, newVal)
      this.agentRow.lawFirmSelectCaseList = []
    },
    selectionTypeClear() {
      this.agentRow.selectionTypeId = null
    },
    choiceCaseClick() {
      this.caseDialogVisible = true
    },
    agentStageOnChange(val) {
      if (val && val.length > 0) {
        const k = []
        this.processData.forEach(item => {
          if (val.includes(item.code)) {
            k.push(item.name)
          }
        })
        this.agentRow.agentStageName = k.join('、')
      } else {
        this.agentRow.agentStageName = null
      }
    },
    caseSure_(data) {
      if (data) {
        this.agentRow.relationCaseId = data.id
        this.agentRow.relationCaseName = data.caseName
      }
    },
    changeAvatar(val) {
      this.avatarDialogVisible = true
    },
    avatarSure(val) {
      this.detailData.avatar = val
    }
  }
}
</script>

<style scoped>
.el-dialog-div {
  height: 40vh;
  overflow: auto;
}
</style>
