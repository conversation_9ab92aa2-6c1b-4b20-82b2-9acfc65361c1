import {request} from '@/api/index'

export default {

    query(data) {
        return request({
            url: '/complianceRiskWaring/query',
            method: 'post',
            data
        })
    },

    save(data) {
        return request({
            url: '/complianceRiskWaring/save',
            method: 'post',
            data
        })
    },

    save(data) {
        return request({
            url: '/complianceRiskWaring/commit',
            method: 'post',
            data
        })
    },

    queryById(data) {
        return request({
            url: '/complianceRiskWaring/queryById',
            method: 'post',
            data: {
                id: data
            }
        })
    },
    deleteById(data) {
        return request({
            url: '/complianceRiskWaring/deleteById',
            method: 'post',
            data

        })
    },


}

