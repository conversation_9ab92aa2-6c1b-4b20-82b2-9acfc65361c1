
383afaeb5317e96ece32cd3878bf80812f44c202	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.251.1754018536329.js\",\"contentHash\":\"ff06c1a3893734a9c00ec65560b5f5c3\"}","integrity":"sha512-wUfdUNt4hAaKKpSGLklDfOF5HH46yT9uyfiF688fRA6DHyjnvG/0mVz6QewHPsY8Dk4OMv38xkr/nbs6F6H4oA==","time":1754018576001,"size":131400}