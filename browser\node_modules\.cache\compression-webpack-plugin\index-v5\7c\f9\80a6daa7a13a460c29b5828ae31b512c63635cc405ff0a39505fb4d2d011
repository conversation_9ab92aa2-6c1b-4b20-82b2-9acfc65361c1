
4dff75735765cba72c6e0183bfc921470d067f39	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.432.1754018536329.js\",\"contentHash\":\"d3f2d8f88ed5b30a1817aab5d0425d30\"}","integrity":"sha512-K5RIMZB0C0f7+i7BiQRJZS/G29C+TiZBPM8mwgxWvOdd9R1eR4akWMq9lhP8+Nszm1G+wsBWz3oWC8j8NhMpSw==","time":1754018575957,"size":58123}