
eafa8ee234599fae4fa4973ef85cfc7e976452ee	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.331.1754018536329.js\",\"contentHash\":\"5aec4f656ebb436daea38331303769a0\"}","integrity":"sha512-3dgkijhFxFBbOiHP/uCTODiJamMbkSbdesRfbvUC+fz/QBFZyKprII8F1l1zftmndwkCN5W7CrOA+Qr2jeyW/A==","time":1754018575975,"size":108913}