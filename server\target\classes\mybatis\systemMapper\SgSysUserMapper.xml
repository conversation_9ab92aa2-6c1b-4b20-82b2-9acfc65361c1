<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.klaw.dao.systemDao.SgSysUserMapper">

    <select id="queryUserNameById" resultType="java.util.Map">
        select USER_NAME,FULL_NAME from SYS_USER where USER_ID = #{userId}
    </select>

    <select id="getUserIds" resultType="java.util.Map">
        select
                A.USER_ID as userId
        from
                HR_UNIT_LEADER_DISTRIBUTION A
        JOIN HR_UNIT_LEADER_TYPE C
        ON
                C.LEADER_TYPE_CODE = A.LEADER_TYPE_CODE
            and C.DELETED = 0
            and A.DELETED = 0
        where
                A.UNIT_ID in (${unitId})
            and A.LEADER_TYPE_CODE = 'HZBMLCJSR'
        order by FIELD(A<PERSON>UNIT_ID, ${unitId});
    </select>

    <select id="queryUserIds" resultType="java.util.Map">
        select
            A.USER_ID as userId
        from
            HR_UNIT_LEADER_DISTRIBUTION A
                JOIN HR_UNIT_LEADER_TYPE C
                     ON
                         C.LEADER_TYPE_CODE = A.LEADER_TYPE_CODE
                             and C.DELETED = 0
                             and A.DELETED = 0
        where
            A.UNIT_ID in (${unitId})
          and A.LEADER_TYPE_CODE = '${typeCode}'
        order by FIELD(A.UNIT_ID, ${unitId});
    </select>
</mapper>