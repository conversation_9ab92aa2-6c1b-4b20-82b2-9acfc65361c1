
a3777826b307e0c474bb9d7c5b989447237817d3	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.20.1754018536329.js\",\"contentHash\":\"e706f214c577686faa9c392293a5e72b\"}","integrity":"sha512-kFseh9/ZK0ULZBq456bMOC3VtchVHKGmZhjhkchi9E3mFNIOAwUPfyJcWKzK/Im1+9GPPfnfBRqTpoeMmpvGOg==","time":1754018575958,"size":84225}