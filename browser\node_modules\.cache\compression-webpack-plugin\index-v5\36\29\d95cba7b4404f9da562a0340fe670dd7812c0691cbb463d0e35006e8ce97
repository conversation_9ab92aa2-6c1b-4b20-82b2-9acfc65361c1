
01bebb1425c41c44c616a987a817de7393c78970	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.393.1754018536329.js\",\"contentHash\":\"26b963cb692cb3fe90a3504dc7fe98bb\"}","integrity":"sha512-qJP0R+ulueejBWrxgHOgCNCJ2332i/soNRc++ftQpmc0FjAkCEiLiz2adojJK5ChgBvwCHA3sQ2EAb+D7KCQ2A==","time":1754018576025,"size":157159}