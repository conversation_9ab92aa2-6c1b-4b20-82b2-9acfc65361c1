
cc7b12dd6894067ab597d491fe57d74347ce19d5	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.63.1754018536329.js\",\"contentHash\":\"91e1c02b90236af343acd4933003ae5c\"}","integrity":"sha512-l6w7hetdUgRdB8dEBEDq+42FRBDgGiX3I00NXxmHC66PR8Sd+bJ9nwLTNggO5qvtyDXkPqErtEf1IehThL6XIw==","time":1754018575955,"size":40146}