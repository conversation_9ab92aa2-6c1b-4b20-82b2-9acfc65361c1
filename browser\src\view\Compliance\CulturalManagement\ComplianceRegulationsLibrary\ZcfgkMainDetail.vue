<template>
	<div>
		<FormWindow ref="formWindow" @initData="initData" @loadData="loadData">
			<el-container v-loading="loading" style="height: calc(100vh - 84px)" :element-loading-text="loadingText">
				<el-main>
					<el-scrollbar style="height: 100%" wrap-style="overflow-x: hidden;">
						<!--数据表单块-->
						<el-form ref="dataForm" :model="mainData" :rules="!isView ? rules : {}" label-width="100px"
							:style="mainData.dataStateCode === utils.dataState_BPM.FINISH.code ? 'margin-right: 10px;' : ' margin-right: 10px;'">
							<el-row
								style="padding: 10px 0 10px 0; position: fixed; width: 100%; overflow-y: auto; background-color: white; z-index: 999">
								<el-button v-if="!isView" type="primary" size="mini" @click="save_">保存</el-button>
								<el-button v-if="!isView" type="success" size="mini" @click="submit_">提交</el-button>
							</el-row>
							<div style="padding-top: 50px"></div>
							<span
								style="text-align: left; font-size: 23px; margin-left: 43%; font-weight: 900">政策法规</span>
							<!--基础信息块-->
							<qs-base-info :data.sync="mainData" :data-state="dataState"
								:authorization-data="authorizationData" :view="view" />


							<!--公共信息-->
							<OtherInfo :data.sync="mainData" :main-id="mainData.id" :data-state="dataState"
								style="position: absolute; bottom: 0; width: 100%; margin-top: 20px" />
						</el-form>
					</el-scrollbar>
				</el-main>
				<!-- 选择模版 -->
				<!-- <Shortcut :templateShow="templateShow" @templateClick="templateClick" /> -->
			</el-container>
		</FormWindow>
	</div>
</template>

<script>
// vuex缓存数据
import { mapGetters } from 'vuex';

// 接口api
import ComplianceRegulationsLibrary from '@/api/risk/ComplianceRegulationsLibrary'
import taskApi from '@/api/_system/task';
// 组件
import FormWindow from '@/view/components/FormWindow/FormWindow';
import OtherInfo from '@/view/litigation/caseManage/case/caseChild/OtherInfo';
import OrgSingleDialogSelect from '@/view/components/OrgSingleDialogSelect/index';
import QsBaseInfo from './qisubaseInfo';
import CaseData from '@/view/litigation/caseManage/case/caseChild/CaseData';
import CaseEvidenceData from '@/view/litigation/caseManage/case/caseChild/CaseEvidenceData';
import Shortcut from '@/view/litigation/caseManage/caseExamine/Shortcut';
import SimpleBoardTitleApproval from '@/view/components/SimpleBoard/SimpleBoardTitleApproval';

export default {
	name: 'ZcfgkMainDetail',
	inject: ['layout', 'mcpLayout'],
	components: {
		SimpleBoardTitleApproval,
		CaseData,
		QsBaseInfo,
		OrgSingleDialogSelect,
		FormWindow,
		OtherInfo,
		CaseEvidenceData,
		Shortcut,
	},
	computed: {
		...mapGetters(['orgContext']),
		isView: function () {
			return this.dataState === this.utils.formState.VIEW;
		},
		templateShow() {
			return this.mainData.dataStateCode === this.utils.dataState_BPM.SAVE.code && this.dataState !== this.utils.formState.VIEW;
		},
	},
	data() {
		return {
			type: null,
			tabId: null,
			oarecordsDialog: false,
			loading: false,
			dataState: null,
			functionId: null, //终止的时候要用，需要手动关闭
			dataId: null,
			taskId: null,
			event: null,
			view: 'old',
			mainData: {
				complianceArea: null,//合规领域
				regulationName: null,//政策法规名称
				fileNumber: null,//文件编号
				fileNature: null,//文件性质
				issuingUnit: null,//颁布单位
				effectiveDate: null,//实施日期
				applicableUnit: null,//适用单位
				uploadAttachment: null,//上传附件
				createOrganization: null,//创建组织
				createDate: null,//创建日期
				id: null, //主键
				reviewStatus: null,//状态
				// cultureId:null,//编号
				partiesList: [],
				claimList: [],
				otherDataList: [],
				relations: [],
				sealList: [],
				createOgnId: null, //当前机构ID
				createOgnName: null, //当前机构名称
				createDeptId: null, //当前部门ID
				createDeptName: null, //当前部门名称
				createGroupId: null, //当前部门ID
				createGroupName: null, //当前部门名称
				createPsnId: null, //当前人ID
				createPsnName: null, //当前人名称
				createOrgId: null, //当前组织ID
				createOrgName: null, //当前组织名称
				createPsnFullId: null, //当前人全路径ID
				createPsnFullName: null, //当前人全路径名称
				createPsnPhone: null, //经办人电话
				createTime: null, //创建时间
				// auditStatus:this.utils.dataState_BPM.SAVE.name,//状态
				// dataState: this.utils.dataState_BPM.SAVE.name, //数据状态
				// dataStateCode: this.utils.dataState_BPM.SAVE.code, //数据状态编码

			},
			rules: {
				complianceArea: [{ required: true, message: '请选择合规领域', trigger: 'blur' }],
				fileNumber: [{ required: true, message: '请输入文件编号', trigger: 'blur' }],
				fileNature: [{ required: true, message: '请选择文件性质', trigger: 'blur' }],
				applicableUnit: [{ required: true, message: '请选择适用单位', trigger: 'blur' }],
				regulationName: [{ required: true, message: '请输入政策法规名称', trigger: 'blur' }],
				effectiveDate: [{ required: true, message: '请选择实施日期', trigger: 'blur' }],
				issuingUnit: [{ required: true, message: '请选择颁布单位', trigger: 'blur' }],

			},
			loadingText: '加载中...',
		};
	},
	methods: {
		initData(temp, dataState) {
			this.dataState = dataState;
			Object.assign(this.mainData, temp);

			let obj = this.utils.getManagementUnit(this.mainData.createPsnFullName, this.mainData.createPsnFullId);
			this.mainData.managementUnit = obj.name;
			this.mainData.managementUnitId = obj.id;
			this.mainData.unitType = obj.unitType;
			this.mainData.unitTypeId = obj.unitTypeId;

			this.view = 'old';
			//自动编号
			// let year = new Date().getFullYear();
			// this.utils.createKvsequence('zcfgk' + year, 6).then((value) => {
			// 	this.mainData.fileNumber = value.data.kvsequence;
			// });

			this.loading = false;
		},
		loadData(dataState, dataId) {
			this.functionId = this.$route.query.functionId;
			if (this.$route.query.view !== undefined && this.$route.query.view !== '') this.view = this.$route.query.view;
			if (this.$route.query.type !== undefined && this.$route.query.type !== '') this.type = this.$route.query.type;
			this.dataState = dataState;
			ComplianceRegulationsLibrary.queryById(dataId).then((response) => {
				this.mainData = response.data.data;
				this.authorizationData = response.data.data.authorization;
				if (response.data.data.authorization !== null) this.authorizationList = response.data.data.authorization.authorizationLitigationList;
				this.loading = false;
			});
		},
		// },
		save() {
			return new Promise((resolve, reject) => {
				//判断  createOrganization 是否有空值
				if (!this.mainData.createOrganization) {
					this.mainData.createOrganization = this.mainData.createDeptName; // 创建单位
				} else {
					this.mainData.createOrganization = this.mainData.createOrganization; // 创建单位
				}
				this.mainData.reviewStatus = '暂存';
				ComplianceRegulationsLibrary
					.save(this.mainData)
					.then((response) => {
						resolve(response);
					})
					.catch((error) => {
						reject(error);
					});
			});
		},
		submit() {
			return new Promise((resolve, reject) => {
				//判断  createOrganization 是否有空值
				if (!this.mainData.createOrganization) {
					this.mainData.createOrganization = this.mainData.createDeptName; // 创建单位
				} else {
					this.mainData.createOrganization = this.mainData.createOrganization; // 创建单位
				}
				this.mainData.reviewStatus = '已提交';
				this.mainData.releaseDate = new Date();
				ComplianceRegulationsLibrary
					.save(this.mainData)
					.then((response) => {
						resolve(response);
					})
					.catch((error) => {
						reject(error);
					});
			});
		},


		prosecutionSelect(data) {
			this.mainData.complianceArea = data.complianceArea;
			this.mainData.regulationName = data.regulationName;
			this.mainData.fileNumber = data.fileNumber;
			this.mainData.fileNature = data.fileNature;
			this.mainData.issuingUnit = data.issuingUnit;
			this.mainData.effectiveDate = data.effectiveDate;
			this.mainData.applicableUnit = data.applicableUnit;
			this.mainData.uploadAttachment = data.uploadAttachment;
			this.mainData.createOrganization = data.createOrganization;
			this.mainData.createDate = data.createDate;
			this.mainData.unitTypeId = data.unitTypeId; //单位类型id
			this.mainData.riskDepartmentId = data.riskDepartmentId;//风险部门Id
			this.mainData.currentUnit = data.currentUnit; //当事单位
			this.mainData.currentUnitId = data.currentUnitId; //当事单位id
			this.mainData.createOrganization = data.createDeptName; //经办单位
			this.mainData.createPsnPhone = data.createPsnPhone;//经办人电话
			this.mainData.createOgnName = data.createOgnName;//经办单位
			this.mainData.createPsnName = data.createPsnName;//经办人
			this.mainData.createDeptName = data.createDeptName;//经办部门
			this.mainData.partiesList = data.partiesList;
			this.mainData.partiesList.forEach((item) => {
				item.id = this.utils.createUUID();
				item.masterId = this.mainData.id;
			});

			this.mainData.claimList = data.claimList;
			this.mainData.claimList.forEach((item) => {
				item.id = this.utils.createUUID();
				item.parentId = this.mainData.id;
			});

			this.mainData.otherDataList = data.otherDataList;
			this.mainData.otherDataList.forEach((item) => {
				item.id = this.utils.createUUID();
				item.parentId = this.mainData.id;
				item.files = null;
			});

			this.mainData.relations = data.relations;
			this.mainData.relations.forEach((item) => {
				item.id = this.utils.createUUID();
				item.relationId = this.mainData.id;
			});
		},
		save_() {
			this.save().then(() => {
				this.mainData.reviewStatus = '暂存';
				this.$message.success('保存成功!');
			});
		},
		submit_() {
			this.$refs['dataForm'].validate((valid) => {
				if (valid) {
					this.submit().then(() => {
						this.mainData.reviewStatus = '已提交';
						this.$message.success('提交成功!');
						this.mcpLayout.closeTab();//关闭页面
						const uuid = this.utils.createUUID();//打开新页面
						this.layout.openNewTab('政策法规库', 'zcfgk_index', 'zcfgk_index', uuid)
					});
				} else {
					this.$message.error('表单填写不完整，请检查后重新提交!');
				}
			});
		},
	}
}
</script>

<style scoped></style>
