
29fe82ae83cbdc08a4ee0756144207b508728abb	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.358.1754018536329.js\",\"contentHash\":\"37c86d36a6279716869bbaa231722251\"}","integrity":"sha512-g+yvx65ZfNGl71eiiCLn9JsE1K/JvO82X4Hs5LM+TOTe5yPDgBUM3kf5k7Mq+BR0xZ7I9aKdEhgRI6XneOtugg==","time":1754018575975,"size":93453}