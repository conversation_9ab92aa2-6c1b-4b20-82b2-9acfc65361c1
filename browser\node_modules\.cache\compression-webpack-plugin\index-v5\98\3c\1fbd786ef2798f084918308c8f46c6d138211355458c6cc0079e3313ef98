
dbb9d413996fa96f04c6304aa608bf696b2f05a5	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.408.1754018536329.js\",\"contentHash\":\"35daf75004da3e49b9d24c989a2505ab\"}","integrity":"sha512-Kb1ZhMTgZAIT4NTY487Qc5bH3iN+YM6l7q5n/nq4n54siKbFtwcgUVYa6psgrmHLta+mnwU/WyQRXXtPRvY4Jg==","time":1754018575976,"size":87408}