
0909a5deef75ce61b407253369d821b1db79b50c	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.122.1754018536329.js\",\"contentHash\":\"def56bd9636aadebb2fb29fdb1341904\"}","integrity":"sha512-9agiurUrzPz6TyNgJhbGgouL04i3taHol8NLSrIpI6XOMatSl/FMLZq0wa0J6ppDc5e0jsyN9Y4e6I3fM0v2Qg==","time":1754018575979,"size":147622}