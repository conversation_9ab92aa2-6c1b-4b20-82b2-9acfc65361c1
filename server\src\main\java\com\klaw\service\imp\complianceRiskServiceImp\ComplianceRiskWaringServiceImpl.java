package com.klaw.service.imp.complianceRiskServiceImp;

import cn.hutool.core.lang.hash.Hash;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yitter.idgen.YitIdHelper;
import com.klaw.dao.complianceRiskDao.ComplianceRiskWaringMapper;
import com.klaw.entity.complianceRiskBean.ComplianceRiskWarning;
import com.klaw.entity.complianceRiskBean.RiskWarningDataBase;
import com.klaw.service.complianceRiskService.ComplianceRiskMitigationService;
import com.klaw.service.complianceRiskService.ComplianceRiskWaringService;
import com.klaw.service.complianceRiskService.RiskWarningDataBaseService;
import com.klaw.utils.DataAuthUtils;
import com.klaw.utils.PageUtils;
import com.klaw.utils.StringUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;


@Service
public class ComplianceRiskWaringServiceImpl extends ServiceImpl<ComplianceRiskWaringMapper, ComplianceRiskWarning> implements ComplianceRiskWaringService {
    @Autowired
    private ComplianceRiskMitigationService complianceRiskMitigationService;

    @Autowired
    private RiskWarningDataBaseService riskWarningDataBaseService;
    @Override
    public void saveData(ComplianceRiskWarning complianceRiskWarning) {
        /*if ("".equals(complianceRiskWarning.getId())){
            complianceRiskWarning.setId(YitIdHelper.nextId()+"");
            this.save(complianceRiskWarning);
        }else{
            this.updateById(complianceRiskWarning);
        }*/
        this.saveOrUpdate(complianceRiskWarning);
        if (complianceRiskWarning.getMitigationList()!=null && !complianceRiskWarning.getMitigationList().isEmpty()){
            complianceRiskWarning.getMitigationList().forEach(t->{
                t.setRiskWarningId(complianceRiskWarning.getId());
                t.setParentId(complianceRiskWarning.getId());
            });
            complianceRiskMitigationService.saveMutiData(complianceRiskWarning.getMitigationList());
        }
    }

    @Override
    public Page<ComplianceRiskWarning> queryPageData(JSONObject json) {
        QueryWrapper<ComplianceRiskWarning> wrapper = new QueryWrapper<>();
        getFilter(json, wrapper);
        return page(new PageUtils<>(json), wrapper);
    }

    private void getFilter(JSONObject json, QueryWrapper<ComplianceRiskWarning> wrapper) {
        //是否台账功能（登记只查自己的，台账和弹框查自己和数据权限的）
        boolean isQuery = json.containsKey("isQuery") ? json.getBoolean("isQuery") : false;
        String orgId = json.containsKey("orgId") ? json.getString("orgId") : "";
        //风险名称
        String riskName = json.containsKey("riskName") ? json.getString("riskName") : null;
        //风险状态
        String reportStatus = json.containsKey("reportStatus")?json.getString("reportStatus"):null;
        //业务领域
        String businessDomainArr = json.containsKey("businessDomainArrStr")?json.getString("businessDomainArrStr"):null;
        //风险等级
        String expectedRiskLevelArr = json.containsKey("expectedRiskLevelArrStr")?json.getString("expectedRiskLevelArrStr"):null;
        //模糊查询
        String fuzzyValue = json.containsKey("fuzzyValue")?json.getString("fuzzyValue"):null;

        String createDeptName = json.containsKey("createDeptName")?json.getString("createDeptName"):null;
        String reportingDateStart = json.containsKey("reportingDateStart")?json.getString("reportingDateStart"):null;
        String reportingDateEnd = json.containsKey("reportingDateEnd")?json.getString("reportingDateEnd"):null;
        String riskMitigationStatus = json.containsKey("riskMitigationStatus")?json.getString("riskMitigationStatus"):null;
        if (StringUtils.isNotBlank(riskName)) {
            wrapper.like("risk_name", riskName);
        }
        if (StringUtils.isNotBlank(reportStatus)) {
            wrapper.eq("report_status", reportStatus);
        }
        if (StringUtils.isNotBlank(createDeptName)) {
            wrapper.eq("create_dept_name", createDeptName);
        }
        if (StringUtils.isNotBlank(reportingDateStart)) {
            wrapper.gt("create_time", reportingDateStart);
        }
        if (StringUtils.isNotBlank(reportingDateEnd)) {
            wrapper.lt("create_time", reportingDateEnd);
        }
        if (StringUtils.isNotBlank(businessDomainArr) && businessDomainArr.split(",").length>0) {
            wrapper.and(i->{
                i.in("business_domain", Arrays.asList(businessDomainArr.split(",")));
            });
        }
        if (StringUtils.isNotBlank(riskMitigationStatus)) {
            wrapper.eq("report_status", riskMitigationStatus);
        }
        //台账权限隔离
        if (isQuery) {
            Long functionId = DataAuthUtils.getFunctionIdByCode("Risk_Waring_Event_Book");
            DataAuthUtils.dataPermSql(wrapper, null, functionId, orgId);
        }else{
            wrapper.eq("create_org_id", orgId);
        }
        if (StringUtils.isNotBlank(expectedRiskLevelArr) && expectedRiskLevelArr.split(",").length>0) {
            wrapper.and(i->{
                i.in("expected_risk_level",Arrays.asList(expectedRiskLevelArr.split(",")));
            });
        }
        if (StringUtils.isNotBlank(fuzzyValue)) {
            wrapper.and(i-> i.like("risk_name",fuzzyValue).or().like("expected_risk_level_name",fuzzyValue).or().like("business_domain_name",fuzzyValue));
        }
        wrapper.orderByDesc("update_time");
    }

    @Override
    public ComplianceRiskWarning queryDataById(String id) {
        return baseMapper.selectById(id);
    }

    @Override
    public int deleteDataById(String id) {
        return baseMapper.deleteById(id);
    }

    @Override
    public JSONObject queryEventData(JSONObject jsonObject) {
        String orgId = jsonObject.getString("orgId");
        if (jsonObject.containsKey("bookFlag")){
            orgId = "";
        }
        //业务领域
        String businessDomainArr = StringUtil.emptyStr(jsonObject.getString("businessDomainArrStr"));
        //风险等级
        String expectedRiskLevelArr = StringUtil.emptyStr(jsonObject.getString("expectedRiskLevelArrStr"));
        Map<String,Object> searchParams = new HashMap<>(jsonObject.getInnerMap());
        searchParams.remove("expectedRiskLevelArr");
        searchParams.remove("businessDomainArr");
        if (!businessDomainArr.isEmpty() && businessDomainArr.split(",").length>0){
            searchParams.put("businessDomainArr",businessDomainArr.split(","));
        }
        if (!expectedRiskLevelArr.isEmpty() && expectedRiskLevelArr.split(",").length>0){
            searchParams.put("expectedRiskLevelArr",expectedRiskLevelArr.split(","));
        }
        HashMap<String,String> map = baseMapper.queryEventData(orgId,searchParams);
        JSONObject resultObj = new JSONObject();
        resultObj.putAll(map);
        return resultObj;
    }

    @Override
    public void updateDataStatus(String businessKey, String processInstanceId) {
        ComplianceRiskWarning complianceRiskWarning = this.getById(businessKey);
        //上报中- 审批完成后变更为 未应对 【2】
        if ("1".equals(complianceRiskWarning.getReportStatus())){
            complianceRiskWarning.setReportStatus("2");
            complianceRiskWarning.setDataStateCode(1);
            complianceRiskWarning.setRiskType("FXYD");
            complianceRiskWarning.setDataState("已保存");
        }//风险总结 审批完成后变更为 已总结 【6】
        else if ("7".equals(complianceRiskWarning.getReportStatus())){
            complianceRiskWarning.setReportStatus("6");
        }
        complianceRiskWarning.setUpdateTime(new Date());
        this.updateById(complianceRiskWarning);
        RiskWarningDataBase riskWarningDataBase = new RiskWarningDataBase();
        BeanUtils.copyProperties(complianceRiskWarning,riskWarningDataBase);
        riskWarningDataBase.setReportingOrganization(complianceRiskWarning.getCreatePsnFullName());
        riskWarningDataBase.setReportingPerson(complianceRiskWarning.getCreatePsnName());
        riskWarningDataBase.setReportingDate(complianceRiskWarning.getCreateTime());
        riskWarningDataBase.setWarningType("事件风险");
        switch (complianceRiskWarning.getExpectedRiskLevelName()){
            case "一般风险":
                riskWarningDataBase.setExpectedRiskLevel("FX_QWFX");
                break;
            case "较大风险":
                riskWarningDataBase.setExpectedRiskLevel("FX_ZDFX");
                break;
            case "重大风险":
                riskWarningDataBase.setExpectedRiskLevel("FX_ZHONGDAFX");
                break;
        }
        riskWarningDataBaseService.saveOrUpdate(riskWarningDataBase);
    }
}
