
227a8347a8c63c1394a4a2d08299c5122936195b	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.123.1754018536329.js\",\"contentHash\":\"9cacdb36598f3ab113293028d1721d2b\"}","integrity":"sha512-sc7vUf8vyqovqSwFPGGWocDWmK81HRrWEDdFf53QejhvqQjmXmgQC/2ThpFJeYzGLJqiO+g+RN99p4y2wQ+wNg==","time":1754018576094,"size":242013}