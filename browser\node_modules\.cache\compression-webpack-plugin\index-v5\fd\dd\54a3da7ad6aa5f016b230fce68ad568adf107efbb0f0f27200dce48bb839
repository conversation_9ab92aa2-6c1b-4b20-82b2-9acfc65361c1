
b0a52e051d9a317dbed0432a6328240772bc572a	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.248.1754018536329.js\",\"contentHash\":\"d377232af8e5b434efbebebf38002da9\"}","integrity":"sha512-5o0hH9JZ2FqrheVA0rS9TUBIGFfHav6uLCLhBxKz78ekuhAJFUP1Kxda5ZN7e/9AzvsKGv414VBl2tEwmYhBhQ==","time":1754018576002,"size":135456}