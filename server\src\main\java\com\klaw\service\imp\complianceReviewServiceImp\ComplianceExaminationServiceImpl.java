package com.klaw.service.imp.complianceReviewServiceImp;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.klaw.dao.complianceReviewDao.ComplianceExaminationMapper;
import com.klaw.entity.complianceReviewBean.ComplianceExamination;
import com.klaw.service.complianceReviewService.ComplianceExaminationService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;
import java.util.List;

/**
 * 服务实现类
 *
 * <AUTHOR>
 * @since 2025-01-08
 */
@Service
public class ComplianceExaminationServiceImpl extends ServiceImpl<ComplianceExaminationMapper, ComplianceExamination> implements ComplianceExaminationService {

    @Autowired
    ComplianceExaminationMapper complianceExaminationMapper;


    @Override
    public ComplianceExamination getComplianceExamination(Integer id) {

        return complianceExaminationMapper.selectById(id);
    }

    @Override
    public List<ComplianceExamination> getAllComplianceExamination() {
        return complianceExaminationMapper.selectList(null);

    }

    @Override
    public void add(ComplianceExamination complianceExamination) {
        complianceExaminationMapper.insert(complianceExamination);
    }

    @Override
    public int modify(ComplianceExamination complianceExamination) {
        //乐观锁更新
        ComplianceExamination currentComplianceExamination = complianceExaminationMapper.selectById(complianceExamination.getId());
        return complianceExaminationMapper.updateById(complianceExamination);
    }

    @Override
    public void remove(String ids) {

        if (StringUtils.isNotEmpty(ids)) {
            String[] array = ids.split(",");
            if (!CollectionUtils.isEmpty(Arrays.asList(array))) {
                complianceExaminationMapper.deleteBatchIds(Arrays.asList(array));
            }
        }

    }

}


