<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.klaw.dao.maPushDao.MaPushMapper">
  <resultMap id="BaseResultMap" type="com.klaw.entity.maPush.MaPush">
    <id column="ID" jdbcType="DECIMAL" property="id"/>
    <result column="BUS_CODE" jdbcType="VARCHAR" property="busCode"/>
    <result column="BUS_TYPE" jdbcType="VARCHAR" property="busType" />
    <result column="PUSH_DATA" jdbcType="VARCHAR" property="pushData"/>
    <result column="PUSH_URL" jdbcType="VARCHAR" property="pushUrl" />
    <result column="SEND_STATUS" jdbcType="VARCHAR" property="sendStatus" />
    <result column="ACCEPT_SUCCESS" jdbcType="VARCHAR" property="acceptSuccess" />
    <result column="DELETED" jdbcType="DECIMAL" property="deleted" />
    <result column="CREATE_DATE" jdbcType="DATE" property="createDate" />
    <result column="OBJECT_VERSION_NUMBER" jdbcType="DECIMAL" property="objectVersionNumber" />
  </resultMap>
  <sql id="Base_Column_List">
    ID,
    BUS_TYPE,
    PUSH_DATA,
    PUSH_URL,
    SEND_STATUS,
    ACCEPT_SUCCESS,
    DELETED,
    CREATE_DATE,
    OBJECT_VERSION_NUMBER
  </sql>
</mapper>