
270e32eba64b25044d9c7aa6518a293a200c671c	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.424.1754018536329.js\",\"contentHash\":\"5102e99441cb045e0c12b9ebdf0f3f69\"}","integrity":"sha512-U27h3kAPrUQuv27TRDAU2v44UYDpaa6LjaPGbrvil5FvvAtXaxEW3l44/BgDnkZpVXBDKojTq0ZLBmImHkaIlg==","time":1754018575976,"size":77813}