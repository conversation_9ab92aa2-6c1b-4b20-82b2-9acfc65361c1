
5405c5c7622fb162a11e5b5acf117cf523321f99	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.410.1754018536329.js\",\"contentHash\":\"7a77dcd1a71b006d0998d7617907264c\"}","integrity":"sha512-Eqm5w+QCrxBt+KccLly+XdlDyCuaHfJ3cn1GffxhyNsjWiywIjH0wbRoa49kMDUhPEKgtj+F/6ADUIFG/wq3Gg==","time":1754018575957,"size":30097}