
4fe67ca9b994975fb58129761f1c7d946a78fed0	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.420.1754018536329.js\",\"contentHash\":\"5de7ab82e5ddacb96d464ab75b78f447\"}","integrity":"sha512-F0PzUQxUYCBISTPQjr7XuCs8cZQJ6utaZwfmjrq9sE84kdUg7X2JUWWsXRCibkTs5WoSAao5MfoGRugndAMMgg==","time":1754018575957,"size":32257}