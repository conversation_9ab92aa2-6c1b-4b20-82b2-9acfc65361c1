<!-- 合规规范首页 -->
<template>
	<el-container direction="vertical" class="container-manage-sg">
		<el-header>
			<!--检索条件块-->
			<el-card>
				<div>
					<el-input
						v-model="selectData.fuzzyValue"
						class="filter_input"
						placeholder="检索字段"
						clearable
						@keyup.enter.native="search_"
						@clear="search_">
						<el-popover slot="prepend" placement="bottom-start" width="1000" trigger="click">
							<el-form ref="queryForm" label-width="100px" size="mini">
								<!-- 选择部门 -->
								
								<el-dialog :close-on-click-modal="false" title="选择组织" :visible.sync="orgVisible1" width="50%">
									<div class="el-dialog-div" style="z-index: 999999999">
										<orgTree
											:accordion="false"
											:is-checked-user="false"
											:show-user="false"
											:is-check="false"
											:checked-data.sync="checkedData"
											:is-not-cascade="true"
											:is-filter="true" />
									</div>
									<span slot="footer" class="dialog-footer">
										<el-button icon="" class="negative-btn" @click="orgCancel1">取消</el-button>
										<el-button type="primary" icon="" class="active-btn" @click="orgSure1">确定</el-button>
									</span>
								</el-dialog>


								<el-dialog title="请选择人员" :visible.sync="dialogRoleVisible1" width="60%" :before-close="handleRoleClose1">
									<div class="el-dialog-div" style="z-index: 999999999">
										<orgTree
											:accordion="false"
											:is-checked-user="false"
											:show-user="true"
											:is-check="true"
											:checked-data.sync="zxcheckedData"
											:is-not-cascade="true"
											:is-filter="true" />
									</div>
									<span slot="footer" class="dialog-footer">
										<el-button @click="dialogRoleVisible1 = false">取 消</el-button>
										<el-button type="primary" @click="sureDialog1">确 定</el-button>
									</span>
								</el-dialog>

								
								<el-row>
									<el-col :span="12">
										<el-form-item label="角色名称">
											<el-input v-model="selectData.roleName" clearable placeholder="请输入..." />
										</el-form-item>
									</el-col>
									<el-col :span="12">
										<el-form-item label="所属组织">
											<el-input v-model="selectData.organization" clearable placeholder="请选择..." maxlength="50" show-word-limit disabled>
												<el-button slot="append" icon="el-icon-search" @click="choiceOrg1()" />
											</el-input>
										</el-form-item>
									</el-col>

									<el-col :span="12">
										<el-form-item label="人员姓名">
											<!-- <el-input v-model="selectData.memberName" style="width: 100%" clearable>
												<el-option v-for="item in utils.dataState_BPM_data" :key="item.code" :label="item.name" :value="item.name" />
											</el-select> -->
											<el-input v-model="selectData.memberName" clearable placeholder="请选择..." maxlength="50" show-word-limit disabled>
												<el-button slot="append" icon="el-icon-search" @click="choicePerson()" />
											</el-input>
										</el-form-item>
									</el-col>
								</el-row>
								<el-button-group style="float: right">
									<el-button type="primary" size="mini" icon="el-icon-search" @click="search_">搜索</el-button>
									<el-button type="primary" size="mini" icon="el-icon-refresh-left" @click="empty_">重置</el-button>
								</el-button-group>
							</el-form>
							<el-button slot="reference" size="small" type="primary">高级检索</el-button>
						</el-popover>
						<el-button slot="append" icon="el-icon-search" @click="search_" />
					</el-input>
				</div>
			</el-card>
		</el-header>

		<el-main>
			<KSplitpanes>
				<template slot="left">
					<el-card id="myCard_" class="myCard" style="overflow-y: auto" shadow="never">
						<el-collapse v-model="activeNames">
							<el-collapse-item title="组织角色" name="1">
								<!-- <ul>
                    <li :class="[isActive?'isLiClicked':'noLiClicked']" v-for="item in tempData.unitTypeData" class="link_" @click="fastQuery(item.NAME_,'unit')">
                      <a :class="[isActive?'isClicked':'noClicked']">{{ item.NAME_ }}（{{ item.NUM }}）</a>
                    </li>
                  </ul> -->
								<el-tree
									ref="tree"
									:data="treeData"
									@node-contextmenu="handleContextMenu"
									:props="defaultProps"
									@node-click="handleNodeClick"></el-tree>
								<div class="box-menu" v-if="menuVisible" :style="{ left: menu_left + 'px', top: menu_top + 'px' }">
									<div @click="addSameLevelNode(0)" v-if="menuVisible"><i class="el-icon-circle-plus-outline"></i>&nbsp;&nbsp;新增同级</div>
									<div class="add" @click="addChildNode()">
                						<i class="el-icon-circle-plus-outline"></i>&nbsp;&nbsp;新增子级
            						</div>
									<div class="delete" @click="deleteNode()"><i class="el-icon-remove-outline"></i>&nbsp;&nbsp;删除</div>
									<div class="edit" @click="editNode()"><i class="el-icon-edit"></i>&nbsp;&nbsp;修改</div>
								</div>
							</el-collapse-item>
						</el-collapse>
					</el-card>
				</template>

				<template slot="right">
					<div style="padding-bottom: 5px" v-if="tempData.tags.length > 0">
						<el-tag size="small" v-for="tag in tempData.tags" :key="tag.name" closable style="margin-right: 5px" @close="tagClose(tag.name, tag.type)"
							>{{ tag.name }}
						</el-tag>
					</div>
					<SimpleBoardIndex :title="'组织角色列表'">
						<template slot="button">
							<el-button v-if="showBtn" class="normal-btn" size="mini" @click="assignPerson">分配人员</el-button>
							</template>
							<!-- 新增时选择的弹框 -->
							<el-dialog :close-on-click-modal="false" title="选择组织" :visible.sync="orgVisible" width="50%">
									<div class="el-dialog-div" style="z-index: 999999999">
										<orgTree
											:accordion="false"
											:is-checked-user="false"
											:show-user="false"
											:is-check="false"
											:checked-data.sync="zxcheckedData"
											:is-not-cascade="true"
											:is-filter="true" />
									</div>
									<span slot="footer" class="dialog-footer">
										<el-button icon="" class="negative-btn" @click="orgCancel">取消</el-button>
										<el-button type="primary" icon="" class="active-btn" @click="orgSure">确定</el-button>
									</span>
								</el-dialog>

							
							<el-dialog title="请选择人员" :visible.sync="dialogRoleVisible" width="60%" :before-close="handleRoleClose2">
									<div class="el-dialog-div" style="z-index: 999999999">
										<orgTree
											:accordion="false"
											:is-checked-user="false"
											:show-user="true"
											:is-check="true"
											:checked-data.sync="zxcheckedData"
											:is-not-cascade="true"
											:is-filter="true" />
									</div>
									<span slot="footer" class="dialog-footer">
										<el-button @click="dialogRoleVisible = false">取 消</el-button>
										<el-button type="primary" @click="sureDialog">确 定</el-button>
									</span>
								</el-dialog>

							<el-dialog title="组织角色设置" :visible.sync="dialogVisible" width="30%" :before-close="handleRoleClose">
								<el-form ref="dataForm" :model="dataForm" :rules="!isView ? rules : {}" label-width="100px" style="margin-right: 10px">
									<el-row>
										<el-col :span="12">
											<el-form-item label="角色名称" prop="roleName">
												<el-input v-model="dataForm.roleName" show-word-limit clearable />
											</el-form-item>
										</el-col>
										<el-col :span="12">
											<el-form-item label="角色编码" prop="roleCode">
												<el-input v-model="dataForm.roleCode" show-word-limit clearable />
											</el-form-item>
										</el-col>
									</el-row>
									<el-row>
										<el-col :span="24">
											<el-form-item label="所属组织" prop="organization">
												<el-input
													v-if="dataState !== 'view'"
													v-model="dataForm.organization"
													clearable
													placeholder="请选择..."
													maxlength="50"
													show-word-limit
													disabled>
													<el-button slot="append" icon="el-icon-search" @click="choiceOrg()" />
												</el-input>
											</el-form-item>
										</el-col>
									</el-row>
									<!-- <el-row>
										<el-col :span="24">
											<el-form-item label="是否集团统管" prop="" required>
												<el-radio-group v-model="dataForm.d">
													<el-radio :label="1" border>是</el-radio>
													<el-radio :label="0" border>否</el-radio>
												</el-radio-group>
											</el-form-item>
										</el-col>
									</el-row> -->
									<el-row>
										<el-col :span="24">
											<el-form-item label="角色描述" prop="roleDescription">
												<span slot="label">角色描述</span>
												<el-input
													v-if="!isView"
													v-model="dataForm.roleDescription"
													:autosize="{ minRows: 3, maxRows: 20 }"
													type="textarea"
													placeholder="请输入角色描述"
													maxlength="500"
													show-word-limit />
											</el-form-item>
										</el-col>
									</el-row>
								</el-form>

								<span slot="footer" class="dialog-footer">
									<el-button @click="dialogVisible = false">取 消</el-button>
									<el-button type="primary" @click="roleClick">确 定</el-button>
								</span>
							</el-dialog>
						<el-card>
							<div style="width: 100%">
								
								<el-table
									ref="table"
									v-loading="tableLoading"
									class="indexTable"
									:data="tableData"
									row-key="id"
									style="width: 100%"
									:height="table_height"
									fit
									stripe
									highlight-current-row
									border
									:show-overflow-tooltip="true"
									lazy
									:load="load"
									id="out-table"
									:header-cell-style="{ backgroundColor: '#EAF1FF' }"
									:tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
									:row-style="rowStyle"
									@sort-change="tableSort"
									@row-click="tableClick"
									@row-dblclick="tableDblClick"
									@current-change="contractCurrentChange">
									<el-table-column type="index" width="50" label="序号" align="center" />
									<el-table-column prop="roleCode" label="角色编码" min-width="150" show-overflow-tooltip> </el-table-column>
									<el-table-column prop="memberName" label="人员姓名" min-width="150" show-overflow-tooltip />
									<!-- <el-table-column prop="organization" label="所属组织" min-width="150" show-overflow-tooltip />
									<el-table-column prop="position" label="岗位" min-width="150" show-overflow-tooltip />
									<el-table-column prop="roleName" label="角色" min-width="300" show-overflow-tooltip />
									<el-table-column prop="graduationSchool" label="毕业院校" min-width="150" show-overflow-tooltip> </el-table-column>
									<el-table-column prop="professionalQualification" label="专业资质" width="100" show-overflow-tooltip />
									<el-table-column prop="jobStatus" label="在岗状态" width="100" show-overflow-tooltip>
										<template slot-scope="scope">
											<div :class="scope.row.jobStatus == '在职' ? 'jobOk' : 'jobNoOk'">
												<div>{{ scope.row.jobStatus }}</div>
											</div>
										</template>
									</el-table-column> -->
									<el-table-column label="操作" align="center" width="150" fixed="right">
										<template slot-scope="scope">
											<el-button type="text" @click="delete_(scope.$index, scope.row)">删除</el-button>
										</template>
									</el-table-column>
								</el-table>
							</div>
						</el-card>
					</SimpleBoardIndex>
				</template>
			</KSplitpanes>
		</el-main>
		<el-footer>
			<pagination
				v-show="currentTab == 'contract'"
				:total="selectData.total"
				:page.sync="selectData.page"
				:limit.sync="selectData.limit"
				@pagination="queryData" />
		</el-footer>
	</el-container>
</template>
  
  <script>
import contract from '@/api/contract/contract';
import pagination from '@/components/Pagination';

import { mapGetters } from 'vuex';
import dictApi from '@/api/_system/dict';
import tableTools from '@/view/components/TableTools/index';
import KSplitpanes from '@/view/components/KSplitpanes/KSplitpanes';
import contractApi from '@/api/contract/contract';
import SimpleBoard2 from '@/view/components/SimpleBoard/SimpleBoardIndex';
import SimpleBoardIndex from '@/view/components/SimpleBoard/SimpleBoardIndex';
import taskApi from '@/api/_system/task';
import { caseProcess } from '@/view/utils/constants';
import TypeDialog from '@/view/litigation/contractManage/contractTypeManage/TypeDialog';
import orgApi from '@/api/_system/org';
import orgTree from '@/view/components/OrgTree/OrgTree';
import XLSX from 'xlsx';
import FileSaver from 'file-saver';

import ComplianceOrgApi from '@/api/ComplianceOrg/ComplianceOrg.js';

export default {
	name: 'HgzzjsIndex',
	components: { pagination, tableTools, KSplitpanes, SimpleBoard2, TypeDialog, orgTree,SimpleBoardIndex },
	computed: {
		...mapGetters(['orgContext']),
	},
	inject: ['layout'],
	mounted() {
		this.$nextTick(function () {
			this.table_height = window.innerHeight - this.$refs.table.$el.offsetTop - 84 - 65 - 140 - 30;
			this.default_table_height = this.table_height;

			document.getElementById('myCard_').style.height = this.table_height + 40 + 'px';
			// 监听窗口大小变化
			const self = this;
			window.onresize = function () {
				self.table_height = window.innerHeight - self.$refs.table.$el.offsetTop - 84 - 65 - 140 - 30;
				self.default_table_height = self.table_height;
				document.getElementById('myCard_').style.height = self.table_height + 40 + 'px';
			};
		});
	},
	created() {
		this.currentTab = 'contract';
		// this.queryCount()
		this.queryData();
		this.queryTreeData();
	},
	activated() {
		this.currentTab = 'contract';
		// 长连接页面第二次激活的时候,不会走created方法,会走此方法
		this.queryData();
		this.queryTreeData();
	},
	data() {
		return {
			currentData: null,
			treeNodeCode:null,
			addChild:false,
			parentId: null,
			checkedData: [],
			personData: [],
			menuVisible: false,
			tableDataOne: [],
			dialogRoleVisible: false,
			dialogRoleVisible1: false,
			currentNode: null,
			menu_left: 0,
			menu_top: 0,
			dataForm: {
				roleName: null,
				roleCode: null,
				organization: null,
				parentId: null,
				id: null,
				roleDescription: null,
			},
			formRole: {},
			treeNodeId: null,
			showBtn: false,
			zxcheckedData: [],
			treeData: [],
			defaultProps: {
				children: 'children',
				label: 'label',
			},
			activeNames: ['1', '2'],
			typeTreeDialog: false,
			isCheckedUser: false,
			isCheckedUser1: false,
			orgVisible: false,
			orgVisible1: false,
			zxcheckedData: [],
			zxcheckedData1: [],
			outOrIn: [
				{ id: '0', dicName: '支出' },
				{ id: '1', dicName: '收入' },
				{ id: '2', dicName: '无' },
			],
			defaultProps: {
				label: 'label',
				children: 'children',
				isLeaf: 'isLeaf',
				parentId: 'parentId',
			},
			// nodeDate1: {
			//   data: undefined,
			//   level: 0,
			//   Tyye: 1
			// },
			//
			// GSname:null,
			// tablemData:null,
			isActive: false,
			isClicked: false,
			tableLoading: true,
			caseTableLoading: true,
			authTableLoading: true,
			tableRow: null,
			authTableRow: null,
			caseTableRow: null,
			isResouceShow: 0,
			// mainData:null,
			// codeValue: '',
			// code: '',
			// lastTime: '',
			// nextTime: '',
			// lastCode: '',
			// nextCode: '',
			selectData: {
				isQuery: true,
				contractName: null,
				roleName: null,
				roleId:null,
				organization: null,
				memberName: null,
				memberId: null,
				typeName: null,
				nodeLevel: null,
				includingTaxRmbMin: null,
				includingTaxRmbMax: null,
				moneyType: null,
				revenueExpenditure: null,
				dataTypeName: null,
				projectName: null,
				otherParty: null,
				ourParty: null,
				fileType: null,
				contractCode: null,
				contractOgnCode: null,
				whetherOgn: null,
				whetherMajorOgn: null,
				dataState: null,
				changeState: null,
				takeEffectName: null,
				dataSource: null,
				fuzzyValue: null,
				year: null,
				page: 1,
				limit: 10,
				total: 0,
				sortName: null,
				order: false,
				orgId: null,
				createPsnName: null,
				startTimeMin: null,
				startTimeMax: null,
				approvalCode: null,
				effectiveTimeMax: null,
				effectiveTimeMin: null,
				createOgnName: null,
				createDeptName: null,
				closeState: null,
			},
			options: [],
			dialogVisible: false,
			region: null,
			regionName: null,
			regionData: null,
			// typeold:null,
			casess: {
				data: this.caseData,
				searchToggle: true,
				tableColumns: [
					{ key: 'contractName', label: '合同名称', visible: true },
					{ key: 'caseName', label: '案件名称', visible: true },
					{ key: 'caseProcessType', label: '案件阶段', visible: true },
					{ key: 'caseNumber', label: '案号', visible: true },
					{ key: 'causeName', label: '案由类型', visible: true },
					{ key: 'caseTime', label: '案发时间', visible: true, isTime: true },
					{ key: 'caseMoney', label: '标的额(元)', visible: true },
					{ key: 'caseKind', label: '案件性质', visible: true },
					{ key: 'createTime', label: '经办时间', visible: true, isTime: true },
					{ key: 'createOgnName', label: '经办单位', visible: true },
					{ key: 'createPsnName', label: '经办人', visible: true },
				],
			},
			authss: {
				data: this.authData,
				searchToggle: true,
				tableColumns: [
					{ key: 'contractName', label: '合同名称', visible: true },
					{ key: 'contractTypeName', label: '合同类型', visible: true },
					{ key: 'contractNature', label: '合同性质', visible: true },
					{ key: 'contractMoneyString', label: '合同金额', visible: true },
					{ key: 'myPartiesName', label: '我方签约主体', visible: true },
					{ key: 'partiesName', label: '对方签约主体', visible: true },
					{ key: 'contectName', label: '对方联系人', visible: true },
					{ key: 'contectTel', label: '联系方式', visible: true },
					{ key: 'autoCode', label: '合同编号', visible: true },
					{ key: 'contractStartDate', label: '合同开始日', visible: true },
					{ key: 'contractEndDate', label: '合同结束日', visible: true },
					{ key: 'createTime', label: '经办时间', visible: true },
					{ key: 'createOgnName', label: '经办单位', visible: true },
					{ key: 'createPsnName', label: '经办人', visible: true },
					{ key: 'contractFrom', label: '合同来源', visible: true },
					{ key: 'textFrom', label: '文本来源', visible: true },
					{ key: 'processState', label: '合同状态', visible: true },
					{ key: 'effectiveStatus', label: '生效状态', visible: true },
					{ key: 'proState', label: '归档状态', visible: true },
					{ key: 'performState', label: '履行状态', visible: true },
					{ key: 'changeState', label: '变更状态', visible: true },
				],
			},
			// moneyTypeData: [],
			dataStateData: [
				{
					dicName: '已保存',
				},
				{
					dicName: '审批中',
				},
				{
					dicName: '已回退',
				},
				{
					dicName: '已完成',
				},
				{
					dicName: '已停止',
				},
				{
					dicName: '待用印',
				},
				{
					dicName: '已废除',
				},
				{
					dicName: '待生效',
				},
				{
					dicName: '待归档',
				},
				{
					dicName: '已归档',
				},
			], // 履行状态数据
			typeData: [
				{ id: '1', dicName: '未履行' },
				{ id: '2', dicName: '正常履行中' },
				{
					id: '3',
					dicName: '逾期履行中',
				},
				{ id: '4', dicName: '正常履行完成' },
				{ id: '5', dicName: '逾期履行完成' },
				{ id: '6', dicName: '合同关闭' },
			],
			contractNatureData: [
				{
					dicName: '原合同',
				},
				{
					dicName: '补充变更',
				},
				{
					dicName: '解除协议',
				},
				{
					dicName: '其他',
				},
			],
			changeStateData: [
				{
					dicName: '未变更',
				},
				{
					dicName: '已补充',
				},
				{
					dicName: '已覆盖',
				},
				{
					dicName: '已解除',
				},
			], // 变更状态数据
			textFromData: [
				{
					dicName: '使用范本',
				},
				{
					dicName: '使用非范本',
				},
				{
					dicName: '其他制式合同',
				},
			], // 文本来源数据
			default_table_height: null,
			table_height: '100%',
			tableData: [],
			// tableData_: [],
			authData: [],
			caseData: [],
			tableQuery: {
				isQuery: true,
				orgId: true,
				page: 1,
				limit: 10,
				total: 0,
				name: null,
				val: null,
				tag: null,
				showAll: true,
				functionCode: null,
			},
			caseTableQuery: {
				isQuery: true,
				orgId: true,
				page: 1,
				limit: 50,
				total: 0,
				name: null,
				val: null,
				tag: null,
				showAll: true,
			},
			authTableQuery: {
				isQuery: true,
				orgId: true,
				page: 1,
				limit: 10,
				total: 0,
				name: null,
				val: null,
				tag: null,
				id: null,
				showAll: true,
			},
			// drawerDialog: false,
			// buttonText: '收缩快速检索',
			// bigWidth: 20,
			// smallWidth: 4,
			// leftSwitch: true,
			activeName: 'contract',
			// contractId: null,
			currentTab: 'contract',
			// buttonBool: false
			treeList: [],
			memberList: [],
			tempData: {
				unitTypeData: [],
				yearData: [
					{
						label: '公司治理',
						value: '1',
					},
					{
						label: '对外投资',
						value: '2',
					},
				],
				tags: [],
				timeAxis: [],
				GLDWData: [],
			},
			thisEvent: 'contract',
			props: {
				children: 'childList',
				label: 'name',
			},
			selectOrg: {
				orgsid: [],
			},
		};
	},
	methods: {
		datepass(data) {
			if (data.takeEffects[0] && data.takeEffects[0].contractTakeEffectDate) {
				return data.takeEffects[0].contractTakeEffectDate;
			} else {
				return null;
			}
		},
		moneyFocus(event) {
			event.currentTarget.select();
		},
		refreshData() {
			this.tableQuery.page = 1;
			this.queryData();
		},
		queryTreeData() {
			this.selectData.orgId = this.orgContext.currentOrgId;
			this.tableQuery.orgId = this.orgContext.currentOrgId;
			// this.temp.orgId = this.orgContext.currentOrgId
			ComplianceOrgApi.queryRole(this.selectData)
				.then((response) => {
					response.data.data.map((item) => {
						item.id = item.id;
						item.label = item.name;
						if (item.childList && item.childList.length > 0) {
							item.children = item.childList;
							item.children.map((item1) => {
								item1.id = item1.id;
								item1.label = item1.name;
							});
						}
					});
					this.treeData = response.data.data;
				})
				.catch({});
		},
		// // 刷新或查询数据
		queryData() {
			this.tableLoading = true;
			var flag = false;
			this.selectData.orgId = this.orgContext.currentOrgId;
			this.tableQuery.orgId = this.orgContext.currentOrgId;
			// this.temp.orgId = this.orgContext.currentOrgId
			ComplianceOrgApi.query(this.selectData)
				.then((response) => {
					this.tableLoading = false;
					this.tableData = response.data.data.records;
					this.selectData.total = response.data.data.total;
					this.typeName = null;
					if (this.tempData.tags.length > 0) {
						this.table_height = this.default_table_height - 30;
					} else {
						this.table_height = this.default_table_height;
					}
				})
				.catch({});
		},
		searchTypeChange() {
			this.selectData.contractTypeName = '';
			const names = this.$refs.cascadersAddr.getCheckedNodes()[0].pathLabels;
			for (let i = 0; i < names.length; i++) {
				this.selectData.contractTypeName += names[i] + '/';
			}
			this.selectData.contractTypeName = this.selectData.contractTypeName.substring(0, this.selectData.contractTypeName.length - 1);
			var resultLableArray = this.getLabelArray(names);
			this.selectData.contractCascadeTypeName = resultLableArray.join(',');
		},
		// // 点击清空按钮事件,清空查询条件属性,回到第一页,重新刷新数据
		empty_() {
			this.selectData = {
				isQuery: true,
				contractName: null,
				twoTypeName: null,
				includingTaxRmbMin: null,
				includingTaxRmbMax: null,
				moneyType: null,
				revenueExpenditure: null,
				dataTypeName: null,
				projectName: null,
				otherParty: null,
				ourParty: null,
				fileType: null,
				contractCode: null,
				whetherOgn: null,
				whetherMajorOgn: null,
				dataState: null,
				changeState: null,
				fuzzyValue: null,
				year: null,
				page: 1,
				limit: 10,
				total: 0,
				sortName: null,
				order: false,
				orgId: null,
			};
			this.refreshData();
		},
		// // 点击搜索按钮事件,回到第一页,重新刷新数据
		search_: function () {
			this.tableQuery.page = 1;
			this.refreshData();
		},
		tabClick(event) {
			this.thisEvent = event.name;
			if (event.name === 'contract') {
				this.currentTab = 'contract';
				this.leftSwitch = true;
				this.bigWidth = 20;
				this.smallWidth = 4;
				this.$nextTick(function () {
					document.getElementById('myCard_').style.height = this.table_height + 180 + 'px';
				});
			} else if (event.name === 'case') {
				this.currentTab = 'case';
				this.leftSwitch = false;
				this.bigWidth = 24;
				this.smallWidth = 0;
				this.$nextTick(function () {
					document.getElementById('myCard_').style.height = this.table_height + 180 + 'px';
				});
				if (this.tableRow === undefined || this.tableRow === null) {
					this.$message.error('请先在合同列表中选择数据后再查看关联案件！');
					this.caseTableLoading = false;
					return;
				}
				this.caseRefresh_();
			} else if (event.name === 'Authorization') {
				this.currentTab = 'Authorization';
				this.leftSwitch = false;
				this.bigWidth = 24;
				this.smallWidth = 0;
				this.$nextTick(function () {
					document.getElementById('myCard_').style.height = this.table_height + 180 + 'px';
				});
				if (this.tableRow === undefined || this.tableRow === null) {
					this.$message.error('请先在合同列表中选择数据后再查看关联合同！');
					this.authTableLoading = false;
					return;
				}
				this.authRefreshData();
			}
		},
		viewContract() {
			this.dialogVisible = true;
		},
		/* 双击table行*/
		tableDblClick(row, column, event) {
			this.tableRow = row;
			this.viewContract();
		},
		// 获取子进程收立案的Id
		getStartId(row) {
			if (row.id.indexOf('start') !== -1) {
				return row.id.substring(5);
			} else {
				return '';
			}
		},
		otherView(row, address) {
			const uuid = this.utils.createUUID();
			this.layout.openNewTab('合同信息查看', 'get_contract_info', 'get_contract_info', uuid, {
				functionId: 'get_contract_info,' + uuid, //不涉及手动关闭，可以不传
				...this.utils.routeState.VIEW(row.id),
				view: 'old',
				contractType: address + '_detail',
				dataSourceCode: 1,
			});
			// const uuid = this.utils.createUUID()
			// this.layout.openNewTab('合同信息',
			//     address,
			//     address,
			//     uuid,
			//     {
			//       functionId: address + "," + uuid,
			//       ...this.utils.routeState.VIEW(row.id),
			//       view: 'old',
			//       dataSourceCode: 1
			//     }
			// )
		},
		moreView(row) {
			if (row.dataStateCode !== this.utils.dataState_BPM.SAVE.code) {
				taskApi.selectTaskId({ businessKey: row.parentId, isView: 'true' }).then((res) => {
					const functionId = res.data.data[0].ID;
					const tabId = this.utils.createUUID();
					this.layout.openNewTab('合同合并审批', 'design_page', 'design_page', tabId, {
						processInstanceId: res.data.data[0].PID, //流程实例
						taskId: res.data.data[0].ID, //任务ID
						businessKey: row.parentId, //业务数据ID
						functionId: functionId, //不传也可以，只有首环节需要传，实例启动就可以不传了
						entranceType: 'FLOWABLE',
						type: 'haveDealt',
						channel: 'business',
						view: 'new',
					});
				});
			} else {
				taskApi.selectFunctionId({ functionCode: 'contract_more_main' }).then((res) => {
					const functionId = res.data.data[0].ID;
					const tabId = this.utils.createUUID();
					this.layout.openNewTab('合同合并审批', 'design_page', 'design_page', tabId, {
						functionId: functionId,
						entranceType: 'FLOWABLE',
						...this.utils.routeState.VIEW(row.parentId),
						view: 'old',
						channel: 'business',
					});
				});
			}
		},
		contractView(row) {
			const uuid = this.utils.createUUID();
			this.layout.openNewTab('合同信息查看', 'get_contract_info', 'get_contract_info', uuid, {
				functionId: 'get_contract_info,' + uuid, //不涉及手动关闭，可以不传
				...this.utils.routeState.VIEW(row.id),
				view: 'old',
				contractType: 'contract_approval_main_detail',
			});
			// if (row.dataStateCode === this.utils.dataState_BPM.SAVE.code)
			// {
			//   const tabId = this.utils.createUUID();
			//   this.layout.openNewTab(
			//       "合同审批信息",
			//       "contract_approval_main_detail",
			//       "contract_approval_main_detail",
			//       tabId,
			//       {
			//         functionId: "contract_approval_main_detail," + tabId,
			//         ...this.utils.routeState.VIEW(row.id)
			//       }
			//   )
			// }
			// else
			// {
			//   const tabId = this.utils.createUUID()
			//   this.layout.openNewTab("合同审批信息","contract_approval_main_oa","ContractApprovalMainOa",tabId, { ...this.utils.routeState.VIEW(row.id) })
			//   return
			// }
			// if (row.dataStateCode !== this.utils.dataState_BPM.SAVE.code) {
			//   taskApi.selectTaskId({businessKey: row.id, isView: 'true'}).then(res => {
			//     console.log(res.data.data)
			//
			//     const functionId = res.data.data[0].ID
			//     const tabId = this.utils.createUUID()
			//     this.layout.openNewTab("合同审批信息",
			//         "design_page",
			//         "design_page",
			//         tabId,
			//         {
			//           processInstanceId: res.data.data[0].PID,//流程实例
			//           taskId: res.data.data[0].ID,//任务ID
			//           businessKey: row.id, //业务数据ID
			//           functionId: functionId, //不传也可以，只有首环节需要传，实例启动就可以不传了
			//           entranceType: "FLOWABLE",
			//           type: "haveDealt",
			//           channel: 'business',
			//           view: 'old',
			//         }
			//     )
			//   })
			// } else {
			//   taskApi.selectFunctionId({functionCode: 'contract_approval_main'}).then(res => {
			//     const functionId = res.data.data[0].ID
			//     const tabId = this.utils.createUUID()
			//     this.layout.openNewTab("合同审批信息",
			//         "design_page",
			//         "design_page",
			//         tabId,
			//         {
			//           functionId: functionId,
			//           entranceType: "FLOWABLE",
			//           ...this.utils.routeState.VIEW(row.id),
			//           view: 'old',
			//           channel: 'business'
			//         }
			//     )
			//   })
			// }
		},
		personSelectChange(selection) {
			this.personData = selection;
		},
		changeView(row) {
			const uuid = this.utils.createUUID();
			this.layout.openNewTab('合同变更信息查看', 'get_contract_info', 'get_contract_info', uuid, {
				functionId: 'get_contract_info,' + uuid, //不涉及手动关闭，可以不传
				...this.utils.routeState.VIEW(row.id),
				view: 'old',
				contractType: 'contract_change_main_detail',
			});
			// if (row.dataStateCode === this.utils.dataState_BPM.SAVE.code)
			// {
			//   const tabId = this.utils.createUUID();
			//   this.layout.openNewTab(
			//       "合同变更",
			//       "contract_change_main_detail",
			//       "contract_change_main_detail",
			//       tabId,
			//       {
			//         functionId: "contract_change_main_detail," + tabId,
			//         ...this.utils.routeState.VIEW(row.id)
			//       }
			//   )
			// }
			// else
			// {
			//   const dataId = row.id;
			//   const tabId = this.utils.createUUID()
			//   this.layout.openNewTab("合同变更审批单信息","contract_change_main_oa","ContractChangeMainOa",tabId, { ...this.utils.routeState.VIEW(dataId) })
			// }
		},
		stopView(row) {
			const uuid = this.utils.createUUID();
			this.layout.openNewTab('合同终止信息查看', 'get_contract_info', 'get_contract_info', uuid, {
				functionId: 'get_contract_info,' + uuid, //不涉及手动关闭，可以不传
				...this.utils.routeState.VIEW(row.id),
				view: 'old',
				contractType: 'contract_termination_main_detail',
			});
			// if (row.dataStateCode === this.utils.dataState_BPM.SAVE.code)
			// {
			//   const tabId = this.utils.createUUID();
			//   this.layout.openNewTab(
			//       "合同终止协议",
			//       "contract_stop_main_detail",
			//       "contract_stop_main_detail",
			//       tabId,
			//       {
			//         functionId: "contract_stop_main_detail," + tabId,
			//         ...this.utils.routeState.VIEW(row.id)
			//       }
			//   )
			// }
			// else
			// {
			//   const dataId = row.id;
			//   const tabId = this.utils.createUUID()
			//   this.layout.openNewTab("合同终止协议","contract_stop_main_oa","ContractStopMainOa",tabId, { ...this.utils.routeState.VIEW(dataId) })
			// }
		},
		supMainView(row) {
			const uuid = this.utils.createUUID();
			this.layout.openNewTab('合同补录信息查看', 'get_contract_info', 'get_contract_info', uuid, {
				functionId: 'get_contract_info,' + uuid, //不涉及手动关闭，可以不传
				...this.utils.routeState.VIEW(row.id),
				view: 'old',
				contractType: 'contract_supplement_main',
			});
			// const uuid = this.utils.createUUID()
			// this.layout.openNewTab("合同补录信息",
			//     "contract_supplement_main",
			//     "contract_supplement_main",
			//     uuid,
			//     {
			//       functionId: "contract_supplement_main," + uuid,//不涉及手动关闭，可以不传
			//       ...this.utils.routeState.VIEW(row.id),
			//       view: 'old'
			//     }
			// )
		},
		supChangeView(row) {
			const uuid = this.utils.createUUID();
			this.layout.openNewTab('合同补录变更信息查看', 'get_contract_info', 'get_contract_info', uuid, {
				functionId: 'get_contract_info,' + uuid, //不涉及手动关闭，可以不传
				...this.utils.routeState.VIEW(row.id),
				view: 'old',
				contractType: 'contract_supplement_change',
			});
			// const uuid = this.utils.createUUID()
			// this.layout.openNewTab("合同信息查看",
			//     "get_contract_info",
			//     "get_contract_info",
			//     uuid,
			//     {
			//       functionId: "get_contract_info," + uuid,//不涉及手动关闭，可以不传
			//       ...this.utils.routeState.VIEW(row.id),
			//       view: 'old'
			//     }
			// )
			// const uuid = this.utils.createUUID()
			// this.layout.openNewTab("合同补录信息",
			//     "contract_supplement_change",
			//     "contract_supplement_change",
			//     uuid,
			//     {
			//       functionId: "contract_supplement_change," + uuid,//不涉及手动关闭，可以不传
			//       ...this.utils.routeState.VIEW(row.id),
			//       view: 'old'
			//     }
			// )
		},

		load(tree, treeNode, resolve) {
			const parentId = tree.id;
			contract.queryChildById({ changeOriginalContractId: parentId }).then((response) => {
				let rows = [];
				rows = response.data.data;
				if (rows.length > 0) {
					resolve(rows);
				} else {
					this.$message({
						type: 'error',
						message: '本节点以下暂无子节点!',
					});
				}
			});
		},
		rowStyle({ row, index }) {
			if (this.utils.isNotNullString(row.changeOriginalContractId)) {
				return 'background-color:rgb(200, 224, 200)';
			}
		},
		tableSort(column, prop, order) {
			this.selectData.sortName = column.prop;
			//this.selectData.order = column.order
			this.selectData.order = column.order == 'ascending' ? true : false;
			this.refreshData();
		},
		// 单击选中合同
		tableClick(row, column, event) {
			this.tableRow = row;
		},

		contractCurrentChange(currentRow, oldCurrentRow) {
			this.contractId = currentRow.id;
		},
		caseRefresh_() {
			contract
				.queryCase({
					id: this.tableRow.id,
				})
				.then((response) => {
					const rows = response.data.data;
					const caseData = [];
					for (let i = 0; i < rows.records.length; i++) {
						rows.records[i].relationData.contractName = this.tableRow.contractName;
						caseData.push(rows.records[i].relationData);
					}
					this.caseData = caseData;
					this.casess.data = caseData;
					this.caseTableQuery.total = response.data.data.total;
					this.caseTableLoading = false;
				})
				.catch({});
		},
		// 双击查看案件
		caseTableDblClick(row, column, event) {},
		// 单击选中案件
		caseTableClick(row, column, event) {
			this.caseTableRow = row;
		},

		authTableSort(column, prop, order) {
			this.selectData.sortName = column.prop;
			this.selectData.order = column.order;
			this.refreshData();
		},
		// 双击查看授权
		authTableDblClick(row, column, event) {},
		// 单击选中授权
		authTableClick(row, column, event) {
			this.authTableRow = row;
		},
		// // 刷新或查询数据
		authRefreshData() {
			this.authTableQuery.id = this.tableRow.id;
			contract
				.queryByUnion({
					tableQuery: this.authTableQuery,
				})
				.then((response) => {
					const rows = response.data.contractList;
					this.authData = rows;
					this.authss.data = rows;
					this.authTableQuery.total = response.data.data.total;
					this.authTableLoading = false;
				})
				.catch({});
		},
		// 懒加载数据值
		// 懒加载数据值
		loadNode(node, resolve) {
			contractApi
				.queryContractLeftOrgTree({
					parentId: node.level === 0 ? null : node.data.id,
					backdating: this.backdating,
					TimeCreat: node.level === 0 ? null : node.data.parentId,
					TimeChan: node.level === 0 ? 'aaa' : node.parent.data,
				})
				.then((res) => {
					const myList = [];
					res.data.data.forEach((e) => {
						myList.push(e);
					});
					resolve(myList);
					// if (node.level === '0') {
					//   this.loadNode1(this.node_had, this.resolve_had)
					// }
				})
				.catch((res) => {
					resolve([]);
				});
		},
		loadNode1(node, resolve) {
			this.resolve_had = resolve;
			if (this.arrStrList == null) {
				this.arrStrList = resolve;
			}
			console.log(this.arrList);
			if (node.data != undefined) {
				this.str = node.data.level;
			}
			if (this.str === '0') {
				this.str = '';
				contractApi
					.queryContractLeftOrgTree2({
						select: node.data,
					})
					.then((res) => {
						for (let i = 0; i < this.arrList.length; i++) {
							this.$refs.tree1.remove(this.arrList[i].id);
						}
						const myList = [];
						res.data.data.forEach((e) => {
							myList.push(e);
						});
						if (myList.length > 0) {
							var falg = true;
							if (myList[0].label.indexOf('保密合同') > -1) {
								falg = false;
							}
							if (myList[0].groupComp === '3') {
								falg = true;
							}

							if (myList[0].groupComp === '3') {
								falg = true;
							}
							var str = myList[0].label;
							var strA = str.split('(');
							var strB = strA[0];
							if (
								(strB === '采购合同' ||
									strB === '保密合同' ||
									strB === '财务合同' ||
									strB === '销售合同' ||
									strB === '工程合同' ||
									strB === '会展及宣传合同' ||
									strB === '投资经营合同' ||
									strB === '租赁合同' ||
									strB === '技术及知识产权合同' ||
									strB === '战略合同' ||
									strB === '进出口审批' ||
									strB === '中介服务合同' ||
									strB === '合规合同') &&
								falg
							) {
								this.arrList = [];
								this.arrList = myList;
							} else {
								this.arrStrList = myList;
							}
						}
						if (myList.length > 0) {
							if (myList[0].groupComp === '3' && this.resolve_had1 != null) {
								resolve = this.resolve_had1;
							}
						}

						resolve(myList);
					})
					.catch((res) => {
						console.log('........................................');
						resolve([]);
					});
			} else {
				if (this.str === '1') {
					node.data.parentId = this.node_had.data.parentId + '%' + this.node_had.data.label;
				}
				var tyoe = 0;
				if (node.Tyye != undefined) {
					tyoe = node.Tyye;
				}
				if (node.data != undefined) {
					if (node.data.parentId != null) {
						if (node.data.id.indexOf('保密合同') > -1 && node.data.parentId.indexOf('保密合同') > -1) {
							node.data.id = '二级保密合同(1)';
						}
					}
				}
				contractApi
					.queryContractLeftOrgTree1({
						parentId: node.level === 0 ? null : node.data.id,
						backdating: this.backdating,
						TimeCreat: node.level === 0 ? null : node.data.parentId,
						TimeChan: node.level === 0 ? 'aaa' : node.parent.data,
					})
					.then((res) => {
						const myList = [];
						res.data.data.forEach((e) => {
							myList.push(e);
						});
						if (myList.length > 0) {
							if (myList[0].groupComp === '3' && tyoe !== 1) {
								this.resolve_had1 = resolve;
							} else if (tyoe === 1) {
								for (let i = 0; i < this.arrList.length; i++) {
									this.$refs.tree1.remove(this.arrList[i].id);
								}
								this.arrList = myList;
								resolve = this.resolve_had1;
							}
						}
						if (myList.length > 0) {
							var falg = true;
							if (myList[0].label.indexOf('保密合同') > -1) {
								falg = false;
							}
							if (myList[0].groupComp === '3') {
								falg = true;
							}
							var str = myList[0].label;
							var strA = str.split('(');
							var strB = strA[0];
							if (
								(strB === '采购合同' ||
									strB === '保密合同' ||
									strB === '财务合同' ||
									strB === '销售合同' ||
									strB === '工程合同' ||
									strB === '会展及宣传合同' ||
									strB === '投资经营合同' ||
									strB === '租赁合同' ||
									strB === '技术及知识产权合同' ||
									strB === '战略合同' ||
									strB === '进出口审批' ||
									strB === '中介服务合同' ||
									strB === '合规合同') &&
								tyoe !== 1 &&
								falg
							) {
								this.arrList = [];
								this.arrList = myList;
							} else {
								this.arrStrList = myList;
							}
						}
						resolve(myList);
					})
					.catch((res) => {
						resolve([]);
					});
			}
		},
		// 当前行点击事件
		currentRowClick(data, node, ex) {
			this.node_had = node;
			if (data.parentId.length > 10) {
				data.parentId = node.parent.data.parentId;
			}
			if (this.node_had.data.level === '0') {
				this.loadNode1(this.node_had, this.resolve_had);
			}
			console.log(node.parent.data.parentId);
			var label = data.label;
			label = data.label;
			this.currentData = data;
			this.currentNodekey = data.id;
			this.fastQuery(data.id, label, data.parentId, 'SSGS');
		},
		currentRowClick1(data, node, ex) {
			// if (data.parentId.length > 10) {
			//   data.parentId = node.parent.data.parentId
			// }
			console.log(node.parent.data.parentId);
			var label = data.label;
			label = data.label;
			this.currentData = data;
			this.currentNodekey = data.id;
			if ((data.parentId.indexOf('合同') > -1 || data.parentId.indexOf('进出口审批') > -1) && data.level === null) {
				if (node.parent.data != undefined) {
					if (node.parent.data.parentId != null) {
						var qq = node.parent.data.parentId;
						var aa = qq.split('%');
						var Gsname = aa[1];
						this.fastQuery1(data.id, label, data.parentId, 'SSGS', Gsname);
					} else {
						this.fastQuery(data.id, label, data.parentId, 'SSGS');
					}
				}
			}
		},
		//点击跳转详情
		getToDetail(row) {
			console.log(row, 'row');
			if (row.dataStateCode == this.utils.dataState_BPM.SAVE.code) {
				const tabId = this.utils.createUUID();
				this.layout.openNewTab('合规规范', 'hggfgl_main_detail', 'hggfgl_main_detail', tabId, {
					functionId: 'hggfgl_main_detail,' + tabId,
					...this.utils.routeState.VIEW(row.id),
				});
			}
		},
		fastQuery1(data, node) {
			this.foo();
		},
		fastQuery(name, type) {
			// 定义查询参数，需判断是否有同类型的数据
			const index_ = this.getDistinct(type);
			const row = { name: name, type: type };
			if (index_ > -1) {
				this.tempData.tags.splice(index_, 1, row);
			} else {
				this.tempData.tags.push(row);
			}
			if (type === 'unit') {
				this.selectData.oneTypeName = name;
				this.isActive = !this.isActive;
			}
			if (type === 'year') {
				this.selectData.year = name;
				//   this.isClicked = !this.isClicked;
			}
			// 执行查询
			this.refreshData();
		},
		// 快速查询点击

		tagClose(name, type) {
			const index_ = this.getDistinct(type);
			this.tempData.tags.splice(index_, 1);
			if (type === 'unit') {
				this.selectData.oneTypeName = '';
				this.isActive = false;
			}
			if (type === 'year') {
				this.selectData.year = '';
				this.isClicked = false;
			}
			this.refreshData();
		},
		// 删除
		delete_(index, row) {
			this.$confirm('此操作将永久删除该数据及相关数据, 是否继续?', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning',
			})
				.then(() => {
					new Promise((resolve, reject) => {
						ComplianceOrgApi.delete({
							id: row.id,
						}).then((response) => {
							resolve(response);
						});
					}).then((value) => {
						this.tableData.splice(index, 1);
						this.$message.success('删除成功!');
					});
				})
				.catch(() => {
					this.$message({
						type: 'info',
						message: '已取消删除',
					});
				});
		},
		getDistinct(item) {
			var num = -1;
			var len = this.tempData.tags.length;
			var tags = this.tempData.tags;
			for (let i = 0; i < len; i++) {
				if (tags[i].type === item) {
					num = i;
					break;
				}
			}
			return num;
		},
		updateSystem() {
			if (this.tableRow === undefined || this.tableRow === null) {
				this.$message.error('请选择需要查看的的数据！');
				return;
			}
			this.initDic();
			this.dialogVisible = true;
		},
		handleClose(done) {
			this.$confirm('确认关闭？')
				.then((_) => {
					done();
				})
				.catch((_) => {});
		},
		initDic() {
			const codes = ['HT-HTCQ-TSXT'];
			this.utils.getDic(codes).then((response) => {
				this.regionData = response.data.data[codes[0]];
			});
		},
		regionChange(val) {
			this.regionName = this.utils.getDicName2(this.regionData, val);
		},
		regionClick() {
			contractApi.saveById({ id: this.tableRow.id, regionName: this.regionName, region: this.region }).then((res) => {
				if (res.data) {
					this.$message.success('保存成功!');
				} else {
					this.$message.error('保存失败');
				}
				this.dialogVisible = false;
			});
		},
		onSure(data) {
			this.selectData.nodeLevel = data.nodeLevel;
			this.selectData.typeName = data.typeName;
			console.log('this.selectData:' + JSON.stringify(this.mainData));
		},
		isTypeTreeDialog() {
			this.selectData.typeName = null;
			this.typeTreeDialog = true;
		},
		//选择单位
		chooseApprovalDeptClick() {
			this.isCheckedUser = false;
			this.orgVisible = true;
		},
		//选择部门
		chooseApprovalDeptClick1() {
			this.isCheckedUser1 = false;
			this.orgVisible1 = true;
		},
		choiceDeptSure_() {
			let c = '';
			let cid = '';
			const me = this;
			const res = this.zxcheckedData[0];

			if (res.unitType !== 'ogn' && res.unitType !== 'branch') {
				this.$message.error('请选择法人单位或分公司');
			} else {
				this.zxcheckedData.forEach((item) => {
					if (c.length === 0) {
						c = c + item.name;
						cid = cid + item.unitId;
					} else {
						c = c + ',' + item.name;
						cid = cid + ',' + item.unitId;
					}
				});

				this.selectData.createOgnName = c;
				orgApi.queryMainDataOrgIds({ ids: cid }).then((res) => {
					me.selectData.createOgnId = res.data.msg;
				});
				this.orgVisible = false;
			}
		},
		choiceDeptSure_1() {
			let c = '';
			let cid = '';
			const me = this;
			const res = this.zxcheckedData1[0];

			if (res.unitType !== 'dept') {
				this.$message.error('请选择部门');
			} else {
				this.zxcheckedData1.forEach((item) => {
					if (c.length === 0) {
						c = c + item.name;
						cid = cid + item.unitId;
					} else {
						c = c + ',' + item.name;
						cid = cid + ',' + item.unitId;
					}
				});

				this.selectData.createDeptName = c;
				orgApi.queryMainDataOrgIds({ ids: cid }).then((res) => {
					me.selectData.createDeptId = res.data.msg;
				});
				this.orgVisible1 = false;
			}
		},
		cancel_() {
			this.orgVisible = false;
		},
		cancel_1() {
			this.orgVisible1 = false;
		},
		// 导出
		exportExcel1() {
			this.tableData_ = this.tableData;
			let data = this.selectData;
			// data.page= 1
			data.limit = data.total;
			// data.total= 0
			contract
				.query(data)
				.then((response) => {
					let rows = response.data.data.records;
					this.tableData = rows;
				})
				.catch({});
			var self = this;
			setTimeout(function () {
				self.exportExcel();
			}, 1500);
		},
		exportExcel() {
			var xlsxParam = { raw: true }; //转换成excel时，使用原始的格式
			var wb = XLSX.utils.table_to_book(document.querySelector('#out-table'), xlsxParam);
			var wbout = XLSX.write(wb, {
				bookType: 'xlsx',
				bookSST: true,
				type: 'array',
			});
			var name = new Date().getTime();
			name = name + '.xlsx';
			try {
				FileSaver.saveAs(new Blob([wbout], { type: 'application/octet-stream;charset=utf-8' }), name);
			} catch (e) {
				if (typeof console !== 'undefined') console.log(e, wbout);
			}
			this.tableData = this.tableData_;
			this.selectData.limit = 10;
			this.refreshData();
			return wbout;
		},
		editNode() {
			this.dataForm.roleName = this.currentData.data.roleName;
			this.dataForm.roleCode = this.currentData.data.roleCode;
			this.dataForm.organization = this.currentData.data.organization;
			this.dataForm.roleDescription = this.currentData.data.roleDescription;
			this.treeNodeId = this.currentData.data.id;
			this.type = '2';
			this.dialogVisible = true;
		},
		deleteNode() {
			this.$confirm('此操作将删除当前角色以及角色下的所有人员, 是否继续?', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning',
			})
				.then(() => {
					new Promise((resolve, reject) => {
						ComplianceOrgApi.deleteNode({
							id: this.treeNodeId,
						}).then((response) => {
							resolve(response);
						});
					}).then((value) => {
						this.queryTreeData();
						this.$message.success('删除成功!');
					});
				})
				.catch(() => {
					this.$message({
						type: 'info',
						message: '已取消删除',
					});
				});
		},
		handleNodeClick(event, object, Node) {
			this.treeNodeId = event.id;
			this.treeNodeCode = event.data.roleCode;
			this.selectData.roleId = event.id;
			this.showBtn = true;
			this.refreshData();
		},
		handleContextMenu(event, object, Node, element) {
			this.currentData = object;
			this.currentNode = Node;
			this.menuVisible = true;
			document.addEventListener('click', this.foo);
			// 将菜单显示在鼠标点击旁边定位
			this.menu_left = event.clientX - 140;
			this.menu_top = event.clientY - 40;
		},
		foo() {
			this.menuVisible = false;
			document.removeEventListener('click', this.foo);
		},
		choicePerson() {
			this.dialogRoleVisible1 = true;
		},
		assignPerson() {
			this.dialogRoleVisible = true;
			// this.tableQuery.functionCode = this.currentFunctionId.functionCode;
		},
		addSameLevelNode() {
			this.dialogVisible = true;
			this.type = '1';
			this.addChild = false;
			this.dataForm.roleName = null;
			this.dataForm.roleCode = null;
			this.dataForm.organization = null;
			this.dataForm.parentId = null;
			this.dataForm.id = null;
			this.dataForm.roleDescription = null;
		},
		addChildNode(){
			this.dialogVisible = true;
			this.type = '1';
			this.addChild = true;
			this.dataForm.roleName = null;
			this.dataForm.roleCode = null;
			this.dataForm.organization = null;
			this.dataForm.parentId = null;
			this.dataForm.id = null;
			this.dataForm.roleDescription = null;
		},
		roleClick() {
			if(this.addChild){
				this.dataForm.parentId = this.currentData.id;
				this.dataForm.id = null;
			}else{
				this.dataForm.parentId = this.currentData.parentId;
				this.dataForm.id = this.currentData.id;
			}
			if(this.type == '1'){
				this.dataForm.dataStateCode = '1';
			}else{
				this.dataForm.dataSourceCode = '2';
			}
			ComplianceOrgApi.saveRole(this.dataForm).then((res) => {
				this.$message.success('成功');
				this.queryTreeData();
				this.dialogVisible = false;
			});
		},
		sureDialog1() {
			var c = '';
			var cid = '';
			if (this.zxcheckedData !== undefined && this.zxcheckedData != null && this.zxcheckedData.length > 0) {
				this.zxcheckedData.forEach((item) => {
					if (c.length === 0) {
						c = c + item.name;
						cid = cid + item.unitId;
					} else {
						c = c + ',' + item.name;
						cid = cid + ',' + item.unitId;
					}
				});
			}
			this.selectData.memberName = c;
			this.selectData.memberId = cid;
			this.dialogRoleVisible1 = false;
		},
		sureDialog() {
			var c = '';
			var cid = '';
			if (this.zxcheckedData !== undefined && this.zxcheckedData != null && this.zxcheckedData.length > 0) {
				this.zxcheckedData.forEach((item) => {
					this.memberList.push({
						memberName: item.name,
						id: item.unitId,
					});
				});
			}
			this.formRole.memberList = this.memberList;
			this.formRole.roleId = this.treeNodeId;
			this.formRole.roleCode = this.treeNodeCode;
			ComplianceOrgApi.save(this.formRole).then((res) => {
				this.$message.success('分配成功');
				this.memberList = [];
				this.dialogRoleVisible = false;
				this.refreshData();
			});
		},
		handleRoleClose() {
			this.dialogVisible = false;
		},
		handleRoleClose1(){
			this.dialogRoleVisible1 = false;
		},
		handleRoleClose2(){
			this.dialogRoleVisible = false;
		},
		choiceOrg() {
			this.orgVisible = true;
		},
		choiceOrg1() {
			this.orgVisible1 = true;
		},
		orgSure1(){
			const ress = this.checkedData[0];
			this.dataForm.organization = ress.name;
			this.selectData.organization = ress.name;
			this.orgVisible1 = false;
		},
		orgSure() {
			const ress = this.zxcheckedData[0];
			this.dataForm.organization = ress.name;
			this.selectData.organization = ress.name;
			this.orgVisible = false;
		},
		orgCancel() {
			this.orgVisible = false;
		},
		orgCancel1(){
			this.orgVisible1 = false;
		},
		handleCheckChange(data, checked, indeterminate) {
			console.log(data, '数据');
			console.log(checked, '选中状态');
			console.log(indeterminate, '子树中选中状态');
			// 获取当前选择的id在数组中的索引
			const indexs = this.selectOrg.orgsid.indexOf(data.id);
			// 如果不存在数组中，并且数组中已经有一个id并且checked为true的时候，代表不能再次选择。
			if (indexs < 0 && this.selectOrg.orgsid.length === 1 && checked) {
				console.log('only one');
				// this.$message({
				//   message: '只能选择一个区域！',
				//   type: 'error',
				//   showClose: true
				// })
				// 设置已选择的节点为false 很重要
				// this.$refs.tree.setChecked(this.selectOrg, false)
				this.$refs.tree.setChecked(data, false);
			} else if (this.selectOrg.orgsid.length === 0 && checked) {
				// 发现数组为空 并且是已选择
				// 防止数组有值，首先清空，再push
				this.selectOrg.orgsid = [];
				this.selectOrg.orgsid.push(data.id);
			} else if (indexs >= 0 && this.selectOrg.orgsid.length === 1 && !checked) {
				// 再次直接进行赋值为空操作
				this.selectOrg.orgsid = [];
			}
		},
	},
};
</script>
  <style>
.el-popover {
	max-height: 500px;
	overflow: auto;
}
</style>
  
  <style scoped >
/*左侧样式*/
.myCard .el-collapse-item__header {
	padding-left: 10px !important;
	/*font-weight: 800;
    font-size: 16px;*/
}
.myCard .link_ {
	height: 36px;
	/* margin-left: 26px; */
	line-height: 36px;
}
.myCard .link_ a {
	margin-left: 26px;
}
.isLiClicked,
.isLiYearClicked {
	height: 36px;
	line-height: 36px;
	background: #eaf1ff;
}
.noLiClicked,
.noLiYearClicked {
	height: 36px;
	line-height: 36px;
}
.isClicked,
.isYearClicked {
	color: #3e7eff;
}
.noClicked,
.noYearClicked {
	color: #5d616b;
}
.el-dialog {
	width: 100% !important;
	height: 70vh;
	overflow: auto;
	box-shadow: 0 0px 0px rgba(0, 0, 0, 0) !important;
	margin: 0 auto 0px !important;
}
.el-table__fixed-body-wrapper {
	top: 50px !important;
}

.filter_input .el-input-group__prepend {
	color: #ffffff;
	background-color: #1890ff;
	border-color: #1890ff;
}

.label_1 {
	text-align: right;
	padding-right: 5px;
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
	-webkit-appearance: none;
}

input[type='number'] {
	-moz-appearance: textfield;
}

.myNew .el-input__inner {
	background: #1f95ff;
}

.myNew .el-input__suffix {
	color: white;
}

.myNew input::-webkit-input-placeholder {
	color: white;
}

.mylabel .el-form-item--mini .el-form-item__label {
	line-height: 15px;
}

::v-deep .el-scrollbar__wrap {
	overflow-x: hidden !important;
}

/deep/ .el-scrollbar__wrap {
	overflow-x: hidden !important;
}

/* 树形结构添加横向滚动条 */
::v-deep .el-tree > .el-tree-node {
	width: 120%;
	display: inline-block;
}

/deep/ .el-tree > .el-tree-node {
	width: 100%;
	height: 100%;
}
.style1 {
	background: #ecf5ff;
	color: #409eff;
}
.style2 {
	background: #fef0f0;
	color: #f56c6c;
}
.style3 {
	background: #fdf6ec;
	color: #e6a23c;
}
.normalHref {
	color: #409eff;
	cursor: pointer;
}
.el-tree > .is-leaf {
	color: transparent;
}
.tree-container /deep/ .el-tree-node__expand-icon.expanded {
	-webkit-transform: rotate(0deg);
	transform: rotate(0deg);
}
.tree-container /deep/ .el-tree-node__expand-icon.expanded {
	-webkit-transform: rotate(0deg);
	transform: rotate(0deg);
}
.tree-container /deep/ .el-icon-caret-right:before {
	/* background: url("../../assets/node-collapse.png") no-repeat; */
	content: '';
	display: block;
	width: 12px;
	height: 12px;
	font-size: 12px;
	background-size: 10px;
}
/*
  //有子节点 且已展开*/
.tree-container /deep/ .el-tree-node__expand-icon.expanded.el-icon-caret-right:before {
	/* background: url("../../assets/node-expand.png") no-repeat; */
	content: '';
	display: block;
	width: 12px;
	height: 12px;
	font-size: 12px;
	background-size: 10px;
}

/*  //没有子节点*/
.tree-container /deep/ .el-tree-node__expand-icon.is-leaf::before {
	background: transparent no-repeat 0 3px;
	content: '';
	display: block;
	width: 12px;
	height: 12px;
	font-size: 12px;
	background-size: 10px;
}
/* 点击节点时的选中颜色 */
.tree-container /deep/.el-tree-node.is-current > .el-tree-node__content {
	color: #3d5ecc !important;
}
.tree-container /deep/ .el-tree-node__expand-icon {
	margin-left: 15px;
}
.tree-container /deep/ .el-tree-node__expand-icon.is-leaf {
	margin-left: 0px;
}
.tree-container /deep/ .el-tree-node {
	position: relative;
	padding-left: 16px;
}
.tree-container /deep/ .el-tree-node__children {
	padding-left: 16px;
}
.tree-container /deep/ .el-tree > .el-tree-node:before {
	border-left: none;
}
.tree-container /deep/ .el-tree > .el-tree-node:after {
	border-top: none;
}
.tree-container /deep/ .el-tree > .el-tree-node:before {
	border-left: none;
}
.tree-container /deep/ .el-tree > .el-tree-node:after {
	border-top: none;
}
.tree-container /deep/ .el-tree-node:before {
	content: '';
	left: 10px;
	position: absolute;
	right: auto;
	border-width: 1px;
}
.tree-container /deep/ .el-tree-node:after {
	content: '';
	left: 10px;
	position: absolute;
	right: auto;
	border-width: 1px;
}
.tree-container /deep/ .el-tree-node:before {
	border-left: 1px dashed #ccc;
	bottom: 0px;
	height: 100%;
	top: -19px;
	width: 1px;
}
.tree-container /deep/ .el-tree-node:after {
	border-top: 1px dashed #ccc;
	height: 25px;
	top: 20px;
	width: 20px;
}
.el-tree-node :last-child:before {
	height: 40px;
}
.tree-container {
	margin: 10px;
}
.tree-container /deep/ .el-tree .el-tree-node {
	position: relative;
}
.tree-container /deep/ .el-tree-node .el-tree-node__content {
	height: 34px;
	padding-left: 0px !important;
	border: none;
}
.tree-container /deep/ .el-tree-node .el-tree-node__content::before {
	border-left: 1px dashed #ccc;
	height: 100%;
	top: 0;
	width: 1px;
	margin-left: 1px;
	margin-top: 0px;
	z-index: 8;
}
.tree-container /deep/ .el-tree-node .el-tree-node__children .el-tree-node__content::before {
	border-left: 0px dashed #ccc;
	height: 100%;
	top: 0;
	width: 1px;
	margin-left: 1px;
	margin-top: 0px;
	z-index: 8;
}
.tree-container /deep/ .el-tree-node .el-tree-node__content::after {
	border-top: 1px dashed #ccc;
	height: 1px;
	top: 18px;
	width: 13px;
	margin-left: 1px;
	z-index: 8;
}
.tree-container /deep/ .el-tree-node .el-tree-node__children .el-tree-node__content::after {
	border-top: 0px dashed #ccc;
}
.tree-container .el-tree-node .el-tree-node__content::before,
.tree-container .el-tree-node .el-tree-node__content::after {
	content: '';
	position: absolute;
	right: auto;
}

.el-tree {
	width: 100%;
	margin-top: 10px;
}

.search {
	width: 20%;
	cursor: pointer;
}
.box-menu {
	width: 150px;
	position: absolute;
	z-index: 1000;
	background-color: #fff;
	box-shadow: 0px 0px 10px #ccc, 0px 0px 20px #ccc, 0px 0px 30px #ccc;
	padding: 10px;

	div {
		cursor: pointer;
		line-height: 30px;
	}

	div:active {
		color: blue;
	}
}
</style>
  
  