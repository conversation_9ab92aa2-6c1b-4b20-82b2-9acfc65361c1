
ae4761ee172606ec712f1b961cdb1809de99ac5f	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.60.1754018536329.js\",\"contentHash\":\"ac8c4c51119384c3d656411b166de1cd\"}","integrity":"sha512-6f9lPr9ZXetsXFfkqtIGPrHnCDaoyo3Nhmh49W4VEvMSWHj+k+TVKfg7qoXOpA0Gtndnz6NfuNNC//cHxTNxXg==","time":1754018575978,"size":166973}