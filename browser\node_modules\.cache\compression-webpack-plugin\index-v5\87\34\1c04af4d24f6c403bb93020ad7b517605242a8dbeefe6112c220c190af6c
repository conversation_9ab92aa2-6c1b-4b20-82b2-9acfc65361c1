
8fd611796715b0f69cee3313cf87a3c34468a841	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.292.1754018536329.js\",\"contentHash\":\"f306a58746192ee0675108fb56118111\"}","integrity":"sha512-Fob72lkx6eQhzmeK+HBlyVUhKcyT62AfoglVWyPwDOPmCRQAiEWPEOuGbnc79LpQSjakd70Nij44QksSa3EuGg==","time":1754018575963,"size":113185}