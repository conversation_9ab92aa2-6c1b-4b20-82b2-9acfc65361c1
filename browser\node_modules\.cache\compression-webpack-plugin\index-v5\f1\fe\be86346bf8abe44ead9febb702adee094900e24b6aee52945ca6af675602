
4b2daf10fe1ceb1508df2b317011986507b30a25	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.19.1754018536329.js\",\"contentHash\":\"32fd65ee8c38d2f1271d55fa56c9c850\"}","integrity":"sha512-ipjxer87wt5CwA7k9BfLRak7lcMSfwr+kKK7/zRXXWh4kDohYq6/cW6LGJ1lqi9dgDXS2V1jGjv+qyqiFsgjbQ==","time":1754018575955,"size":39489}