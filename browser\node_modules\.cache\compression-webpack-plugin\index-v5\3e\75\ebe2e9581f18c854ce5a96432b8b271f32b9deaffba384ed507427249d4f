
2530f91c311c9dc531c471b0a73678902a78f5d1	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.396.1754018536329.js\",\"contentHash\":\"f823ab71c7af91e6913e200619b465c2\"}","integrity":"sha512-qtJhNbjTFiN8pChpHTFAEdobCGTaRBKteT0JxNSNwmpE5P9iq5iMFl6JsOFQK5JzXHYb4/2ARHhO5U9AE3OOXw==","time":1754018576023,"size":124861}