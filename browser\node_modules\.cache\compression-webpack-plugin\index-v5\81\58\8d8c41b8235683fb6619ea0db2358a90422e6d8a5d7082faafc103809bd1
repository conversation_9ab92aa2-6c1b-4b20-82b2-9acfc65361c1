
57d2286dd37a1717b7ca9a7e1def86525e155737	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.66.1754018536329.js\",\"contentHash\":\"fe8173f14ef3fe07fdbe30ed35650bcf\"}","integrity":"sha512-ndNbfzB30UUrNPqtsNTqlzY0M9wxuAR5pz1dZr09XVfm4gkZoU5GqMiIU5vxmVGaE0C8Cc3z2FrPcIhhuMXBmA==","time":1754018575956,"size":45752}