import {request} from '@/api/index'

export default {
    //风险事件管理查询
    query(data) {
        return request({
            url: '/complianceRiskWaring/query',
            method: 'post',
            data
        })
    },
    //风险事件管理保存
    save(data) {
        return request({
            url: '/complianceRiskWaring/save',
            method: 'post',
            data
        })
    },
    //根据id查询风险事件预警
    getById(data) {
        return request({
            url: '/complianceRiskWaring/getById',
            method: 'post',
            data
        })
    },
    //根据id删除风险事件预警
    deleteById(data) {
        return request({
            url: '/complianceRiskWaring/deleteById',
            method: 'post',
            data
        })
    },
    //根据id更新风险事件预警
    update(data) {
        return request({
            url: '/complianceRiskWaring/update',
            method: 'post',
            data
        })
    },
    queryEventData(data) {
        return request({
            url: '/complianceRiskWaring/queryEventData',
            method: 'post',
            data

        })
    },


}