/** 
 *  项目上请勿修改此文件内容！
 */
html,body{
    margin:0; 
    padding:0;
    background-color:#fff;
    border: none;
}

body {
    font-family: "Helvetica Neue", "Luxi Sans", "DejaVu Sans", <PERSON><PERSON><PERSON>, "Microsoft Yahei", "Hiragino Sans GB", sans-serif;
    color:#3b3b3b;
    font-size: 12px;
    color: #758697;
    -moz-osx-font-smoothing: grayscale;
    -webkit-font-smoothing: antialiased !important;
}
h1, .h1 {
    font-size: 34px
}

h2, .h2 {
    font-size: 28px
}

h3, .h3 {
    font-size: 22px
}

h4, .h4 {
    font-size: 16px
}

h5, .h5 {
    font-size: 12px
}

h6, .h6 {
    font-size: 10px
}

h1, h2, h3, h4, h5, h6, .h1, .h2, .h3, .h4, .h5, .h6 {
    font-weight: 600;
    color: #2b425b
}

::selection {
    background-color: #42a5f5;
    color: #fff
}

::-moz-selection {
    background-color: #42a5f5;
    color: #fff
}

#container {
    min-height: 100vh;
    height: auto;
    position: relative;
    min-width: 290px;
    overflow: hidden
}

#container.boxed-layout {
    background-color: #dfe5ee
}



#page-title {
    padding: 10px 15px;
    width: 100%;
    color: #2b425b
}

.page-header {
    border: 0 none;
    font-size: 1.7em;
    font-weight: normal;
    margin: 0;
    padding: 10px 0
}

.page-header .label {
    margin-right: .5em
}

#page-content, .page-content{
    padding: 10px
}

#page-content:not (:first-class ){
    padding: 5px 15px 0
}



#navbar {
    position: absolute;
    width: 100%;
    z-index: 10;
    background-color: #ecf0f5;
    left: 0;
    top: 0
}

#navbar-container {
    box-shadow: 0 0 2px 0 rgba(0, 0, 0, 0.2)
}

.navbar-header {
    left: 0;
    position: relative;
    float: none;
    background-color: #263238
}

.navbar-content {
    position: relative;
    left: 0;
    background-color: #fff
}

.navbar-aside-icon>*:before {
    background-color: #758697 !important;
    box-shadow: 0 .45em 0 #758697, 0 .9em 0 #758697 !important
}

.navbar-brand {
    background-color: transparent;
    color: #fff;
    padding: 0;
    height: 40px;
    position: relative;
    z-index: 2;
    overflow: hidden;
    width: 220px
}

.navbar-brand:hover, .navbar-brand:focus {
    color: #fff
}

.brand-title, .brand-icon {
    display: block;
    line-height: 40px
}

.brand-icon {
    width: 40px;
    height: 40px;
    float: left;
    margin: 0 5px
}

.brand-text {
    display: block;
    font-size: 18px;
    font-weight: 600
}

.navbar-icon-color, .navbar-top-links>li>a:focus:not (.mainnav-toggle )>i,
    .navbar-top-links>li>a:hover:not (.mainnav-toggle )>i {
    color: #6d7e90;
    transition: color .4s
}

.navbar-top-links>li {
    float: left
}

.navbar-top-links>li>a {
    display: table-cell;
    padding: 0 12px;
    vertical-align: middle;
    height: 40px;
    color: #758697;
    transition: all .4s
}

.navbar-top-links>li>a:not (.mainnav-toggle )>i {
    color: #758697;
    transition: color .4s
}

.navbar-top-links>li>a:focus {
    background-color: transparent
}

.navbar-top-links>li>a:hover {
    background-color: #f2f2f2;
    color: #6f8193;
    transition: all .4s
}

.navbar-top-links>li>a>i {
    font-size: 1.2em;
    line-height: .75em
}

.navbar-top-links:first-child>li {
    border-right: 1px solid rgba(0, 0, 0, 0.05)
}

.navbar-top-links:last-child>li {
    border-left: 1px solid rgba(0, 0, 0, 0.05)
}

.navbar-top-links>.open>a, .navbar-top-links>.open>a:focus {
    background-color: #f2f2f2;
    color: #758697
}

.navbar-top-links .dropdown-menu .panel-body {
    padding: 0
}

.navbar-top-links .dropdown-menu .nano:not (.scrollable ){
    max-height: 180px
}

.navbar-top-links .dropdown-menu .nano:not (.scrollable ) .nano-content
    {
    position: static
}

.navbar-top-links .tgl-menu-btn {
    position: absolute;
    top: -100%;
    right: 0
}

.navbar-top-links .tgl-menu-btn>a, .navbar-top-links .tgl-menu-btn>a:hover,
    .navbar-top-links .tgl-menu-btn>a:focus {
    color: #fff
}

.navbar-top-links .tgl-menu-btn>a:hover, .navbar-top-links .tgl-menu-btn a:focus
    {
    background-color: transparent
}

.navbar-top-links .head-list {
    list-style: none;
    padding: 0;
    margin: 0
}

.navbar-top-links .head-list li a {
    display: block;
    padding: 10px 15px;
    background: transparent
}

.navbar-top-links .head-list li a:hover {
    background: rgba(0, 0, 0, 0.05);
    color: #758697
}

.badge-header {
    position: absolute;
    font-size: .85em;
    font-weight: normal;
    top: 50%;
    margin-top: -1.5em;
    min-width: 1.85em;
    padding: 3px 5px;
    right: 3px
}

.badge-header:empty {
    display: inline;
    border-radius: 50%;
    min-width: 0;
    padding: 5px;
    right: .55em;
    top: 2.2em;
    animation-iteration-count: 5
}

.navbar-top-links .dropdown-menu .nano {
    max-height: 265px
}

.img-user {
    width: 32px;
    height: 32px;
    box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.1)
}

#dropdown-user .ic-user {
    font-size: 1.5em;
    line-height: 40px
}

.username {
    float: right;
    white-space: nowrap;
    line-height: 40px;
    margin: 0 10px
}



#container.navbar-fixed>#navbar {
    position: fixed
}

#container.navbar-fixed>#navbar .shadow {
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1)
}

#container.navbar-fixed .navbar-top-links .dropdown-menu .nano>.nano-content
    {
    position: absolute
}


#mainnav-container {
    position: absolute;
    width: 220px;
    left: -220px;
    padding-top: 40px;
    top: 0;
    bottom: 0;
    z-index: 4;
    min-height: 100%
}

#mainnav {
    height: 100%;
    background-color: #263238
}

#mainnav .list-header {
    font-weight: 600;
    color: #747e88
}

#mainnav .list-divider {
    border-color: rgba(0, 0, 0, 0.1);
    margin: 10px 0
}

#mainnav-menu-wrap {
    height: 100%
}

#mainnav-shortcut {
    overflow: hidden
}

#container:not (.mainnav-sm ) #mainnav-shortcut {
    margin-top: 10px;
    margin-bottom: 10px
}

#container:not (.mainnav-sm ) #mainnav-menu-wrap>.nano>.nano-content {
    padding-top: 20px
}

#container:not (.mainnav-sm ) #mainnav-menu {
    margin-top: 10px
}

#container:not (.mainnav-sm ) .mainnav-profile {
    margin-top: -20px
}

.mainnav-sm .mainnav-profile {
    visibility: hidden;
    max-height: 0;
    overflow: hidden;
    opacity: 0;
    margin: 0
}

.mainnav-profile {
    color: #fff;
    opacity: 1;
    margin-bottom: 7px;
    max-height: 350px
}

.mainnav-profile .mnp-name {
    color: #fff;
    margin: 0;
    font-size: 1.1em;
    font-weight: 600
}

.mainnav-profile .dropdown-caret, .mainnav-profile .mnp-desc {
    color: #fff
}

/*.mainnav-profile .profile-wrap {*/
    /*padding: 30px 20px 12px;*/
    /*background-image: url('../img/nav-profile.png');*/
    /*background-size: cover*/
/*}*/

#mainnav .mainnav-profile .list-group {
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    background-color: #202a2f;
    margin: 0
}

#mainnav .mainnav-profile .list-group-item {
    color: #abb1b7;
    padding: 12px 20px
}

#mainnav .mainnav-profile .list-group-item:hover {
    color: #fff;
    background-color: transparent
}

#mainnav-menu ul, .mainnav-widget ul, .menu-popover ul {
    list-style: none;
    padding-left: 0
}

#mainnav li .arrow {
    float: right;
    line-height: 1.42857
}

#mainnav li .arrow:before {
    content: '';
    border-style: solid;
    border-width: .1em .1em 0 0;
    display: inline-block;
    height: .4em;
    left: 0;
    position: relative;
    top: 0;
    width: .4em;
    -webkit-transform: rotate(45deg);
    -ms-transform: rotate(45deg);
    transform: rotate(45deg)
}

#mainnav li.active>a>.arrow:before {
    -webkit-transform: rotate(135deg);
    -ms-transform: rotate(135deg);
    transform: rotate(135deg)
}

#mainnav-menu a {
    display: block;
    color: #abb1b7;
    padding: 12px 20px
}

#mainnav-menu a strong {
    font-weight: 600
}

#mainnav-menu>li>a:hover, #mainnav-menu>li>a:active {
    color: #fff
}

#mainnav-menu>li>a.hover {
    color: #fff;
    background-color: #2c3a41;
    box-shadow: inset 2px 0 0 0 #0c80df
}

#mainnav-menu>.active-link>a, #mainnav-menu>.active-link>a:hover {
    color: #fff;
    padding-left: 20px;
    font-weight: 600;
    box-shadow: inset 2px 0 0 0 #0c80df
}

#mainnav-menu>.active-sub>a, #mainnav-menu>.active-sub>a:hover {
    color: #fff;
    font-weight: 600
}

#mainnav-menu>.active {
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    background-color: #202a2f
}

#mainnav-menu>.active .active {
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    background-color: #1c2429
}

#mainnav-menu ul .list-header {
    padding: 10px 20px 12px 55px
}

#mainnav-menu ul ul a {
    padding: 10px 20px 12px 65px
}

#mainnav-menu i {
    padding: 0 10px 0 0;
    font-size: 1.15em
}

#mainnav-menu .hide-lg {
    display: none
}

#mainnav-menu ul a, .menu-popover .sub-menu ul a {
    padding: 10px 20px 12px 53px
}

#mainnav-menu ul a:hover, .menu-popover .sub-menu ul a:hover {
    color: #fff
}

#mainnav-menu ul ul a:hover, .menu-popover .sub-menu ul ul a:hover {
    color: #fff
}

#mainnav-menu ul .active-link a, .menu-popover .sub-menu ul .active-link a
    {
    font-weight: 600;
    color: #fff;
    box-shadow: inset 2px 0 0 0 #0c80df
}

#container.mainnav-sm #mainnav-menu>.active-link>a, #container.mainnav-sm #mainnav-menu>.active-sub>a,
    #container.mainnav-sm #mainnav-menu>.active-link a:hover, #container.mainnav-sm #mainnav-menu>.active-sub a:hover
    {
    background-color: #0c80df;
    box-shadow: none
}

#mainnav-shortcut {
    max-height: 1.7em;
    overflow: hidden
}

#mainnav-shortcut ul {
    width: 100%;
    margin: 0;
    padding: 0
}

#mainnav-shortcut li {
    padding: 0;
    vertical-align: middle;
    text-align: center
}

#mainnav-shortcut .shortcut-grid {
    display: table;
    border: 0;
    width: 100%;
    height: 1.7em;
    color: #fff
}

#mainnav-shortcut .shortcut-grid>i {
    display: table-cell;
    vertical-align: middle;
    font-size: 15px
}

#mainnav-shortcut .shortcut-grid>span {
    display: table-cell;
    vertical-align: middle
}

#container.mainnav-sm #mainnav-shortcut {
    max-height: 250px
}

#container.mainnav-sm #mainnav-shortcut>ul>li {
    width: 100%;
    text-align: left
}

#container.mainnav-sm #mainnav-shortcut>ul>li .shortcut-grid {
    height: auto;
    display: block;
    padding: 12px 18.5px
}

#container.mainnav-sm #mainnav-shortcut>ul>li i {
    font-size: 13px;
    line-height: 1.42857
}

#container.mainnav-sm #mainnav-shortcut .shortcut-grid>.hide-sm {
    display: none
}

.mainnav-widget {
    background-color: rgba(0, 0, 0, 0.05);
    color: #abb1b7
}

.mainnav-widget-content {
    padding: 10px 15px
}

.mainnav-widget .show-small a {
    display: none
}

#container.mainnav-fixed #mainnav-container {
    position: fixed
}

#container.mainnav-fixed #mainnav {
    height: 100%;
    height: 100vh
}

#container.mainnav-fixed #mainnav .nano-content {
    position: absolute
}


#container.mainnav-fixed:not (.navbar-fixed ) #mainnav-container.affix {
    top: -40px;
    position: fixed
}

#container.mainnav-fixed:not (.navbar-fixed ) #mainnav-container.affix-top
    {
    top: 0;
    position: absolute
}

#container.mainnav-fixed:not (.navbar-fixed ) #mainnav-container.affix-top #mainnav-menu-wrap>.nano>.nano-content
    {
    bottom: 40px
}


.popover.mainnav-shortcut {
    white-space: nowrap
}

.popover.mainnav-shortcut .popover-content {
    padding: 12px 15px
}

/*.mainnav-sm .popover.mainnav-shortcut {*/
    /*display: block !important;*/
    /*border: 0;*/
    /*margin-top: -42.57141px;*/
    /*margin-left: -1px;*/
    /*padding: 0;*/
    /*box-shadow: none;*/
    /*overflow: hidden;*/
    /*width: auto;*/
    /*max-width: 300px;*/
    /*border-radius: 0;*/
    /*background-color: #222d32;*/
    /*color: #fff;*/
    /*left: 50px !important*/
/*}*/

/*.mainnav-sm .popover.mainnav-shortcut.in {*/
    /*-webkit-animation: fadeIn .3s;*/
    /*animation: fadeIn .3s*/
/*}*/

/*.mainnav-sm .popover.mainnav-shortcut.in .popover-content {*/
    /*height: 100%;*/
    /*margin: 0*/
/*}*/

/*.mainnav-sm .popover.mainnav-shortcut>.arrow {*/
    /*display: none*/
/*}*/

/*@*/
/*-webkit-keyframes fadeIn {*/
    /*from {opacity: 0*/
/*}*/

/*to {*/
    /*opacity: 1*/
/*}*/

/*}*/
/*@*/
/*keyframes fadeIn {*/
    /*from {opacity: 0*/
/*}*/

/*to {*/
    /*opacity: 1*/
/*}*/

/*}*/
#container.mainnav-in.footer-fixed #footer, #container.mainnav-in #navbar,
    #container.mainnav-in #content-container, #container.mainnav-in #footer
    {
    left: 220px
}

#container.mainnav-out #content-container, #container.mainnav-in #mainnav-container
    {
    left: 0
}

#container.mainnav-in .navbar-header {
    left: -220px
}

#container.mainnav-in .tgl-menu-btn {
    right: 220px
}

#container.mainnav-in #mainnav-menu-wrap {
    height: 100%
}


#container #aside-container {
    padding: 0 20px;
    position: relative;
    top: 0;
    z-index: 3
}

#container #aside-container .nano-content {
    position: static;
    outline: 0
}

#container #aside .list-link li a:not (.btn ){
    color: #d1d1d1
}

#container #aside .list-link li a:not (.btn ):hover {
    color: #fff
}

#container #aside .badge-stat {
    color: #32424a
}

#container #aside .text-main {
    color: #fff
}

#container.aside-float.aside-in #aside-container {
    box-shadow: 0 0 5px 6px rgba(0, 0, 0, 0.15)
}

#aside {
    background-color: #32424a;
    color: #fff;
    overflow: hidden;
    -webkit-transform-style: preserve-3d;
    -webkit-backface-visibility: hidden;
    -webkit-perspective: 1000;
    -webkit-transform: translateZ(0)
}

#aside a:not (.btn ):not (.btn-link ){
    color: inherit
}

#aside.aside-xs-in {
    max-height: none;
    overflow: hidden;
    margin-bottom: 70px
}

#aside .bord-all, #aside .bord-top, #aside .bord-btm, #aside .bord-left,
    #aside .bord-rgt, #aside .bord-hor, #aside .bord-ver, #aside .list-divider
    {
    border-color: #394a53
}

#aside .btn-link {
    color: #fff
}

#aside .text-muted {
    color: #849eab
}

#aside hr {
    border-color: rgba(0, 0, 0, 0.15)
}

#aside-container #aside .nav-tabs.nav-justified>li {
    display: table-cell;
    width: 1%
}

#aside-container #aside .nav-tabs li {
    border-bottom: 1px solid #28353b
}

#aside-container #aside .nav-tabs li>a {
    border-radius: 0;
    border: 0;
    background-color: #32424a;
    padding: .9em 0;
    margin: 0
}

#aside-container #aside .nav-tabs li>a>i {
    font-size: 1.35em;
    vertical-align: sub
}

#aside-container #aside .nav-tabs li>a:before {
    content: '';
    display: block;
    background: #42a5f5;
    height: 2px;
    position: absolute;
    bottom: -1px;
    left: 50%;
    right: 50%;
    transition: left .05s, right .05s
}

#aside-container #aside .nav-tabs li:first-child>a {
    border-left: 0
}

#aside-container #aside .nav-tabs li:last-child>a {
    border-right: 0
}

#aside-container #aside .nav-tabs li:not (.active ) a {
    opacity: .5
}

#aside-container #aside .nav-tabs .active a:before {
    content: '';
    display: block;
    background: #42a5f5;
    height: 2px;
    position: absolute;
    bottom: -1px;
    left: 0;
    right: 0;
    transition: left .15s, right .15s
}

#aside-container #aside .tab-content {
    padding: 5px 0
}

#container.aside-bright #aside .nav-tabs li {
    border-bottom: 1px solid #f2f2f2
}

#container.aside-bright #aside .nav-tabs li>a {
    background-color: #fff
}



#aside {
    padding-top: 40px;
    visibility: visible;
    background-color: #32424a;
    height: 100%
}

#container #aside-container {
    padding: 0;
    position: absolute;
    width: 255px;
    height: auto;
    left: auto;
    right: -255px;
    top: 0;
    bottom: 0;
    min-height: 100vh;
    overflow: hidden;
    z-index: 3
}

#container #aside-container .nano-content {
    position: absolute
}

#container #content-container, #container #footer {
    padding-right: 0
}

#container.mainnav-in:not (.slide ) #footer, #container.mainnav-in #content-container
    {
    padding-right: 0
}


#container.aside-in #aside-container {
    left: auto;
    right: 0
}

#container.aside-in.aside-left.mainnav-in #aside-container {
    left: 220px;
    right: auto
}



#container.aside-fixed #aside-container {
    position: fixed
}

#container.aside-fixed #aside {
    height: 100%
}

#container.aside-fixed #aside .nano-content {
    position: absolute
}

#container.aside-fixed:not (.navbar-fixed ) #aside-container.affix {
    top: -40px;
    position: fixed
}

#container.aside-fixed:not (.navbar-fixed ) #aside-container.affix-top {
    top: 0;
    position: absolute
}

#container.aside-bright #aside {
    background-color: #fff;
    border: 1px solid #dbe3ec;
    color: #758697
}

#container.aside-bright #aside .badge-stat {
    color: #fff
}

#container.aside-bright #aside .text-main {
    color: #2b425b
}

#container.aside-bright #aside .text-light {
    color: #758697
}

#container.aside-bright #aside hr {
    border-color: #e9e9e9
}

#container.aside-bright #aside .bord-all, #container.aside-bright #aside .bord-top,
    #container.aside-bright #aside .bord-btm, #container.aside-bright #aside .bord-lft,
    #container.aside-bright #aside .bord-rgt, #container.aside-bright #aside .bord-hor,
    #container.aside-bright #aside .bord-ver, #container.aside-bright #aside .list-divider
    {
    border-color: #d3dce8
}

#container.aside-bright #aside .text-muted {
    color: #afb9c3
}

#container.aside-bright #aside .progress {
    background-color: #e6e6e6
}

#container.aside-bright #aside .list-link li a:not (.btn ){
    color: #8493a2
}

#container.aside-bright #aside .list-link li a:not (.btn ):hover {
    color: #758697
}

#container.aside-bright #aside a:not (.btn ){
    color: #758697
}

#container.aside-bright #aside a:not (.btn ):hover, #container.aside-bright #aside a:not
    (.btn ):focus {
    color: #5d6d7c
}

#container.aside-bright #aside .btn-link {
    color: #758697
}

#footer {
    border-top: 1px solid rgba(0, 0, 0, 0.07);
    background-color: #f3f5f9;
    color: #758697;
    position: absolute;
    padding-top: 10px;
    bottom: 0;
    z-index: 2;
    left: 0;
    right: 0;
    height: 35px
}

#footer p {
    margin-bottom: 5px
}

.footer-list {
    margin-bottom: 0
}

.footer-list>li {
    vertical-align: top
}

#container.footer-fixed #footer {
    left: 0;
    position: fixed;
    bottom: 0;
    margin-top: -35px;
    z-index: 1
}

#footer .show-fixed {
    display: none
}

#footer .hide-fixed {
    display: block;
    height: 100%
}

#container.footer-fixed #footer .show-fixed {
    display: block;
    height: 100%
}

#container.footer-fixed #footer .hide-fixed {
    display: none
}

.collapsing {
    transition-duration: 10ms
}



.open.mega-dropdown {
    position: static
}

.open.mega-dropdown>.mega-dropdown-toggle:before, .open.mega-dropdown>.mega-dropdown-toggle:after
    {
    content: "";
    border-left: 7px solid transparent;
    border-right: 7px solid transparent;
    display: block;
    height: 0;
    position: absolute;
    width: 0
}

.open.mega-dropdown>.mega-dropdown-toggle:before {
    border-bottom: 7px solid #d0d0d0;
    margin: -8px 0 0 0;
    bottom: -10px
}

.open.mega-dropdown>.mega-dropdown-toggle:after {
    border-bottom: 7px solid #fff;
    margin: -7px 0 0 0;
    bottom: -11px;
    z-index: 10000
}

.navbar-top-links>.mega-dropdown>.dropdown-menu.mega-dropdown-menu {
    left: 5px;
    right: 5px;
    max-width: 1250px
}

.dropdown-menu.mega-dropdown-menu {
    padding: 15px
}

.dropdown-menu.mega-dropdown-menu:after {
    content: '';
    display: table;
    clear: both
}

.dropdown-menu.mega-dropdown-menu .list-unstyled .dropdown-header {
    font-size: 1.2em;
    font-weight: 600;
    padding: 10px 4px
}

.dropdown-menu.mega-dropdown-menu .list-unstyled li a {
    display: block;
    padding: 4px;
    background-color: transparent
}

.dropdown-menu.mega-dropdown-menu .list-unstyled li a:not (.disabled-link
    ):hover {
    background-color: rgba(0, 0, 0, 0.05)
}

.widget-header {
    padding: 15px 15px 50px 15px;
    min-height: 125px;
    position: relative;
    overflow: hidden
}

.widget-bg {
    position: absolute;
    top: 0;
    left: 0;
    min-width: 100%;
    min-height: 100%
}

.widget-title {
    position: relative
}

.widget-body {
    padding: 50px 15px 15px;
    position: relative
}

.widget-img {
    position: absolute;
    width: 64px;
    height: 64px;
    left: 50%;
    margin-left: -32px;
    top: -32px
}

.speech {
    position: relative;
    background: #b7dcfe;
    color: #317787;
    display: inline-block;
    border-radius: 0;
    padding: 12px 20px
}

.speech .media-heading {
    color: #317787;
    display: block;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    margin-bottom: 10px;
    padding-bottom: 5px;
    font-weight: 600
}

.speech .media-heading:hover {
    text-decoration: underline
}

.speech-time {
    margin-top: 20px;
    margin-bottom: 0;
    font-size: .8em
}

.speech-right {
    text-align: right
}

.speech-right>.speech {
    background: #ffda87;
    color: #a07617;
    text-align: right
}

.speech-right>.speech .media-heading {
    color: #a07617
}

.speech-right>.speech:before {
    left: auto;
    right: 0;
    border-top: 7px solid transparent;
    border-bottom: 7px solid transparent;
    border-left: 7px solid #ffdc91;
    border-right: 0;
    margin: 15px -6px 0 0
}

.speech:before {
    content: "";
    display: block;
    position: absolute;
    width: 0;
    height: 0;
    left: 0;
    top: 0;
    border-top: 7px solid transparent;
    border-bottom: 7px solid transparent;
    border-right: 7px solid #b7dcfe;
    margin: 15px 0 0 -6px
}

.timeline {
    position: relative;
    padding-bottom: 40px;
    background-color: #ecf0f5;
    color: #758697
}

.timeline:before, .timeline:after {
    background-color: #bec6ce;
    bottom: 20px;
    content: "";
    display: block;
    position: absolute
}

.timeline:before {
    left: 49px;
    top: 20px;
    width: 1px
}

.timeline:after {
    left: 46px;
    width: 7px;
    height: 7px;
    border-radius: 50%;
    border: 1px solid #bec6ce;
    background-color: #ecf0f5
}

.timeline-header {
    border-radius: 0;
    clear: both;
    margin-bottom: 50px;
    margin-top: 50px;
    position: relative
}

.timeline-header .timeline-header-title {
    display: inline-block;
    text-align: center;
    padding: 7px 15px;
    min-width: 100px
}

.timeline .timeline-header:first-child {
    margin-bottom: 30px;
    margin-top: 15px
}

.timeline-stat {
    width: 100px;
    float: left;
    text-align: center;
    padding-bottom: 15px
}

.timeline-entry {
    margin-bottom: 25px;
    margin-top: 5px;
    position: relative;
    clear: both
}

.timeline-entry-inner {
    position: relative
}

.timeline-time {
    display: inline-block;
    padding: 2px 3px;
    background-color: #ecf0f5;
    color: #758697;
    font-size: .85em;
    max-width: 70px
}

.timeline-icon {
    border-radius: 50%;
    display: block;
    margin: 0 auto;
    height: 40px;
    line-height: 40px;
    text-align: center;
    width: 40px;
    margin-top: 5px;
    background-color: #ecf0f5
}

.timeline-icon>i {
    line-height: 40px;
    vertical-align: .1em
}

.timeline-icon img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    vertical-align: top
}

.timeline-icon:empty {
    height: 12px;
    width: 12px;
    margin-top: 20px;
    border: 2px solid #bec6ce
}

.timeline-label {
    background-color: #fff;
    border-radius: 0;
    margin-left: 85px;
    padding: 15px;
    position: relative;
    min-height: 50px;
    border: 1px solid #e7ecf3;
    border-bottom: 1px solid rgba(0, 0, 0, 0.17)
}

.timeline-label:before, .timeline-label:after {
    content: "";
    display: block;
    position: absolute;
    width: 0;
    height: 0;
    left: 0;
    top: 0
}

.timeline-label:before {
    border-top: 10px solid transparent;
    border-bottom: 10px solid transparent;
    border-right: 10px solid #e6e6e6;
    margin: 15px 0 0 -10px
}

.timeline-label:after {
    border-top: 9px solid transparent;
    border-bottom: 9px solid transparent;
    border-right: 9px solid #fff;
    margin: 15px 0 0 -8px
}

.panel .timeline, .panel .timeline:after, .panel .timeline-time, .panel .timeline-label,
    .panel .timeline-icon:not ([class^="bg-"] ):not ([class*=" bg-"] ){
    background-color: #fff
}

.panel .timeline-label {
    box-shadow: none;
    border: 0;
    background-color: #f3f5f9;
    margin-left: 100px
}

.panel .timeline-label:before {
    display: none;
    border-right-color: #e3e3e3
}

.panel .timeline-label:after {
    border-right-color: #f3f5f9
}


.tag:not (.label ){
    background-color: #fff;
    font-size: .9em;
    padding: 5px 10px;
    border-radius: 0;
    border: 1px solid #d1d9de;
    line-height: 1.42857;
    vertical-align: middle;
    -webkit-transition: all .15s;
    transition: all .15s;
    margin-bottom: 4px
}

.tag:not (.label ) i {
    vertical-align: middle
}

.tag.tag-lg, .btn-group-lg>.tag {
    padding: 10px 16px
}

.tag.tag-sm, .btn-group-sm>.tag {
    padding: 4px 7px
}

.tag.tag-xs, .btn-group-xs>.tag {
    padding: 1px 5px
}



.wz-nav-off>li a {
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    cursor: default !important
}

.wz-icon-inline li>a .icon-wrap {
    display: inline-block;
    line-height: 1;
    text-align: center;
    white-space: nowrap;
    vertical-align: middle;
    color: inherit
}

.wz-icon-bw li>a .icon-wrap, .wz-icon-bw li>a p {
    transition: all .5s
}

.wz-icon-bw li.active ~ li>a .icon-wrap {
    color: #758697;
    background-color: rgba(0, 0, 0, 0.17);
    transition: all .5s
}

.wz-icon-bw li:not (.active )>a p {
    color: #758697 !important;
    transition: all .5s
}

.wz-classic {
    margin: 0;
    padding: 0;
    list-style: none;
    display: block;
    position: relative
}

.wz-classic li, .wz-steps li {
    transition: all .5s
}

.wz-classic li>a {
    color: inherit;
    display: block;
    text-align: center;
    padding: 20px 0
}

.wz-classic li>a .icon-wrap {
    display: inline-block;
    line-height: 1;
    text-align: center;
    white-space: nowrap;
    vertical-align: middle;
    color: inherit
}

.wz-classic:after, .wz-steps:after {
    content: '';
    display: table;
    clear: both
}

.wz-classic .active ~ li {
    color: inherit;
    background-color: inherit
}

.wz-classic .active ~ li a {
    opacity: .5
}

.wz-heading {
    position: relative
}

.wz-heading .progress {
    position: absolute;
    left: 0;
    right: 0;
    top: 50%;
    background-color: transparent
}

.wz-heading.wz-w-label .progress {
    margin-top: -0.5em
}

.wz-steps {
    margin: 0;
    padding: 10px 0;
    list-style: none;
    display: block;
    position: relative
}

.wz-steps li>a {
    color: inherit;
    display: block;
    text-align: center;
    padding: 0
}

.wz-steps li>a .icon-wrap {
    display: inline-block;
    line-height: 1;
    text-align: center;
    white-space: nowrap;
    vertical-align: middle
}

.wz-steps li .wz-desc {
    opacity: 0;
    transition: opacity .5s
}

.wz-steps li:not (.active ) .wz-icon {
    display: none
}

.wz-steps .active ~ li {
    color: inherit
}

.wz-steps .active ~ li a {
    opacity: .5
}

.wz-steps .active ~ li .wz-icon {
    display: inline-block
}

.wz-steps .active .wz-icon-done, .wz-steps .active ~ li .wz-icon-done {
    display: none
}

.wz-steps .active .wz-desc {
    opacity: 1;
    transition: opacity .5s
}

.scroll-top {
    display: none
}

.scroll-top.in {
    display: block;
    background-color: #42a5f5;
    color: #fff;
    cursor: pointer;
    position: fixed;
    bottom: 45px;
    right: 15px;
    z-index: 999;
    opacity: 1;
    padding: 10px;
    font-size: 1.5em;
    border-radius: 100%;
    box-shadow: none;
    transition: all .15s
}

.scroll-top.in:hover {
    opacity: 1;
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.2);
    transform: scale(1.1);
    transition: all .15s
}

.scroll-top.in:active {
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.2) !important;
    transform: scale(1.05);
    transition: all .15s
}

.scroll-top.in>i {
    display: block;
    width: 1em;
    height: 1em;
    line-height: 1.1em
}

.panel-overlay-wrap {
    position: relative
}

.panel-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.75);
    text-align: center;
    z-index: 795
}

.panel-overlay:before {
    content: "";
    display: inline-block;
    height: 100%;
    width: 1px;
    vertical-align: middle;
    margin-left: -5px
}

.panel-overlay-title {
    margin: 10px 0 5px
}

.panel-overlay-icon {
    display: inline-block;
    vertical-align: middle
}

.panel-overlay-content {
    display: inline-block;
    vertical-align: middle
}





.alert-wrap {
    margin: 0;
    max-height: 0;
    overflow: hidden;
    padding: 0;
    transition: max-height .7s linear
}

.alert-wrap>.alert {
    margin: 0;
    box-shadow: 0 1px 7px 0 rgba(0, 0, 0, 0.35);
    border-radius: 0;
    text-align: left
}

.alert-wrap>.alert>.media {
    margin: 0
}

.alert-wrap>.alert>.media>.media-body {
    min-width: 150px;
    width: auto;
    vertical-align: middle
}

#page-alert>.alert-wrap>.alert {
    box-shadow: inset 0 1px 0 0 rgba(0, 0, 0, 0.04)
}

.alert-wrap.in {
    max-height: 500px;
    transition: max-height 1s linear
}

.floating-container .animated.alert-wrap {
    overflow: visible
}

.floating-container .animated.alert-wrap>.alert {
    border-radius: 0
}

.alert-message, .alert-title {
    margin-bottom: 1px;
    padding-right: 2ex
}

.alert-title {
    font-size: 1.12em
}

.alert-title:empty {
    display: none
}

.alert-primary .alert-icon {
    color: #fff
}

.alert-info .alert-icon {
    color: #fff
}

.alert-success .alert-icon {
    color: #fff
}

.alert-warning .alert-icon {
    color: #fff
}

.alert-danger .alert-icon {
    color: #fff
}

.alert-mint .alert-icon {
    color: #fff
}

.alert-purple .alert-icon {
    color: #fff
}

.alert-pink .alert-icon {
    color: #fff
}

.alert-dark .alert-icon {
    color: #fff
}

#floating-top-right {
    position: fixed;
    text-align: right;
    top: 7px;
    right: 7px;
    left: 7px;
    z-index: 15
}

#floating-top-right .alert-wrap {
    display: inline-block;
    clear: right;
    float: right;
    margin-bottom: 5px;
    position: relative;
    opacity: 1;
    z-index: 990
}

.animated {
    -webkit-animation-duration: 1s;
    animation-duration: 1s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both
}

.fadeOut {
    -webkit-animation-name: fadeOut;
    animation-name: fadeOut
}

.jellyIn {
    -webkit-animation: jellyIn .7s linear both;
    animation: jellyIn .7s linear both
} /*! Generated with Bounce.js. Edit at http://goo.gl/whUyiv */

.form-checkbox:not (.btn ), .form-radio:not (.btn ){
    display: inline-block;
    background-color: transparent;
    border: 0;
    position: relative;
    padding: 3px;
    line-height: 1em;
    min-width: 19px;
    margin: 0
}

.input-group-addon>.form-checkbox, .input-group-addon>.form-radio {
    margin-bottom: 5px
}

.form-text.form-checkbox:not (.btn ), .form-text.form-radio:not (.btn ){
    padding-left: 25.5px
}

.form-checkbox>input[type="checkbox"], .form-radio>input[type="radio"] {
    margin-left: -50px;
    opacity: 0;
    position: absolute !important;
    z-index: -1
}

#container.show-form .form-checkbox>input[type="checkbox"], #container.show-form .form-radio>input[type="radio"]
    {
    margin-top: -0.1em;
    opacity: 1;
    visibility: visible;
    z-index: 1
}

.form-checkbox:hover, .form-radio:hover {
    cursor: pointer
}

fieldset[disabled] .form-checkbox:hover, fieldset[disabled] .form-radio:hover
    {
    cursor: no-drop
}

fieldset[disabled] .form-checkbox, fieldset[disabled] .form-radio,
    .form-checkbox.disabled, .form-radio.disabled {
    opacity: .5;
    cursor: default;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none
}

.form-checkbox.disabled::selection, .form-radio.disabled::selection {
    background: transparent
}

.form-checkbox.disabled::-moz-selection, .form-radio.disabled::-moz-selection
    {
    background: transparent
}

.form-checkbox.form-normal:before, .form-radio.form-normal:before {
    content: '';
    display: block;
    position: absolute;
    width: 16px;
    height: 16px;
    background-color: transparent;
    border: 1px solid #c8d1d8;
    border-radius: 0;
    left: 0;
    top: 50%;
    margin-top: -9px
}

.form-radio.form-normal:before {
    background-color: transparent
}

.has-success .form-checkbox {
    color: #24692f
}

.has-success .form-checkbox.form-normal:before, .has-success .form-checkbox.form-normal:not
    (.disabled ):hover:before, .has-success .form-radio.form-normal:before,
    .has-success .form-radio.form-normal:not (.disabled ):hover:before {
    background-color: transparent;
    border-color: #71a436
}

#container .has-success .form-radio.form-normal.active:before {
    background-color: #71a436;
    border-color: #71a436
}

.has-warning .form-checkbox {
    color: #f29000
}

.has-warning .form-checkbox.form-normal:before, .has-warning .form-checkbox.form-normal:not
    (.disabled ):hover:before, .has-warning .form-radio.form-normal:before,
    .has-warning .form-radio.form-normal:not (.disabled ):hover:before {
    background-color: transparent;
    border-color: #f29000
}

#container .has-warning .form-radio.form-normal:before {
    background-color: #f29000;
    border-color: #f29000
}

.has-error .form-checkbox {
    color: #eb2521
}

.has-error .form-checkbox.form-normal:before, .has-error .form-checkbox.form-normal:not
    (.disabled ):hover:before, .has-error .form-radio.form-normal:before,
    .has-error .form-radio.form-normal:not (.disabled ):hover:before {
    background-color: transparent;
    border-color: #eb2521
}

#container .has-error .form-radio.form-normal.active:before {
    background-color: #eb2521;
    border-color: #eb2521
}

.form-radio.form-normal:before {
    border-radius: 50%
}

fieldset:not ([disabled] ) .form-checkbox.form-normal:not (.disabled ):hover:after,
    fieldset:not ([disabled] ) .form-checkbox.form-normal.active:after,
    .form-checkbox.form-normal:not (.disabled ):hover:after,
    .form-checkbox.form-normal.active:after {
    content: '';
    position: absolute;
    height: 6px;
    width: 11px;
    left: 3px;
    top: 50%;
    margin-top: -5px;
    -webkit-transform: rotate(-45deg);
    -ms-transform: rotate(-45deg);
    transform: rotate(-45deg);
    border-bottom: 2.5px solid #515151;
    border-left: 2.5px solid #515151
}

fieldset:not ([disabled] ) .has-success .form-checkbox.form-normal:not (.disabled
    ):hover:after, fieldset:not ([disabled] ) .has-success .form-checkbox.form-normal.active:after,
    .has-success .form-checkbox.form-normal:not (.disabled ):hover:after,
    .has-success .form-checkbox.form-normal.active:after {
    border-color: #24692f
}

fieldset:not ([disabled] ) .has-warning .form-checkbox.form-normal:not (.disabled
    ):hover:after, fieldset:not ([disabled] ) .has-warning .form-checkbox.form-normal.active:after,
    .has-warning .form-checkbox.form-normal:not (.disabled ):hover:after,
    .has-warning .form-checkbox.form-normal.active:after {
    border-color: #f0a238
}

fieldset:not ([disabled] ) .has-error .form-checkbox.form-normal:not (.disabled
    ):hover:after, fieldset:not ([disabled] ) .has-error .form-checkbox.form-normal.active:after,
    .has-error .form-checkbox.form-normal:not (.disabled ):hover:after,
    .has-error .form-checkbox.form-normal.active:after {
    border-color: #e33a4b
}

fieldset:not ([disabled] ) .form-radio.form-normal:not (.disabled ):hover:after,
    fieldset:not ([disabled] ) .form-radio.form-normal.active:after,
    .form-radio.form-normal:not (.disabled ):hover:after, .form-radio.form-normal.active:after
    {
    content: '';
    display: block;
    position: absolute;
    width: 8px;
    height: 8px;
    background-color: #515151;
    border-radius: 50%;
    left: 4px;
    bottom: 50%;
    margin-bottom: -3px
}

fieldset:not ([disabled] ) .has-success .form-radio.form-normal:not (.disabled
    ):not (.active ):hover:after, fieldset:not ([disabled] ) .has-success .form-radio.form-normal.active:after,
    .has-success .form-radio.form-normal:not (.disabled ):not (.active ):hover:after,
    .has-success .form-radio.form-normal.active:after {
    background-color: #71a436
}

fieldset:not ([disabled] ) .has-warning .form-radio.form-normal:not (.disabled
    ):not (.active ):hover:after, fieldset:not ([disabled] ) .has-warning .form-radio.form-normal.active:after,
    .has-warning .form-radio.form-normal:not (.disabled ):not (.active ):hover:after,
    .has-warning .form-radio.form-normal.active:after {
    background-color: #f29000
}

fieldset:not ([disabled] ) .has-error .form-radio.form-normal:not (.disabled
    ):not (.active ):hover:after, fieldset:not ([disabled] ) .has-error .form-radio.form-normal.active:after,
    .has-error .form-radio.form-normal:not (.disabled ):not (.active ):hover:after,
    .has-error .form-radio.form-normal.active:after {
    background-color: #eb2521
}

.form-checkbox.form-normal:not (.active ):hover:after, .form-radio.form-normal:not
    (.active ):hover:after {
    opacity: .3
}

.form-checkbox.form-normal.form-primary.active:after, .form-checkbox.form-normal.form-info.active:after,
    .form-checkbox.form-normal.form-success.active:after, .form-checkbox.form-normal.form-warning.active:after,
    .form-checkbox.form-normal.form-danger.active:after, .form-checkbox.form-normal.form-mint.active:after,
    .form-checkbox.form-normal.form-purple.active:after, .form-checkbox.form-normal.form-pink.active:after,
    .form-checkbox.form-normal.form-dark.active:after {
    border-color: #fff
}

.form-checkbox.form-normal:not (.disabled ):hover:before, .form-radio.form-normal:not
    (.disabled ):hover:before {
    border-color: #42a5f5
}

.form-checkbox.form-normal.active:after {
    border-color: #404449
}

.form-checkbox.form-normal.form-primary:hover:before, .form-radio.form-normal.form-primary.active:before,
    .form-checkbox.form-normal.form-primary:not (.active ):hover:after {
    border-color: #489eed
}

.form-checkbox.form-normal.form-primary.active:before, .form-radio.form-normal.form-primary.active:after
    {
    background-color: #42a5f5;
    border-color: #42a5f5
}

.form-checkbox.form-normal.form-info:hover:before, .form-radio.form-normal.form-info.active:before,
    .form-checkbox.form-normal.form-info:not (.active ):hover:after {
    border-color: #00bcd4
}

.form-checkbox.form-normal.form-info.active:before, .form-radio.form-normal.form-info.active:after
    {
    background-color: #00bcd4;
    border-color: #00bcd4
}

.form-checkbox.form-normal.form-success:hover:before, .form-radio.form-normal.form-success.active:before,
    .form-checkbox.form-normal.form-success:not (.active ):hover:after {
    border-color: #8bc34a
}

.form-checkbox.form-normal.form-success.active:before, .form-radio.form-normal.form-success.active:after
    {
    background-color: #8bc34a;
    border-color: #8bc34a
}

.form-checkbox.form-normal.form-warning:hover:before, .form-radio.form-normal.form-warning.active:before,
    .form-checkbox.form-normal.form-warning:not (.active ):hover:after {
    border-color: #ffa726
}

.form-checkbox.form-normal.form-warning.active:before, .form-radio.form-normal.form-warning.active:after
    {
    background-color: #ffa726;
    border-color: #ffa726
}

.form-checkbox.form-normal.form-danger:hover:before, .form-radio.form-normal.form-danger.active:before,
    .form-checkbox.form-normal.form-danger:not (.active ):hover:after {
    border-color: #ef5350
}

.form-checkbox.form-normal.form-danger.active:before, .form-radio.form-normal.form-danger.active:after
    {
    background-color: #ef5350;
    border-color: #ef5350
}

.form-checkbox.form-normal.form-mint:hover:before, .form-radio.form-normal.form-mint.active:before,
    .form-checkbox.form-normal.form-mint:not (.active ):hover:after {
    border-color: #26a69a
}

.form-checkbox.form-normal.form-mint.active:before, .form-radio.form-normal.form-mint.active:after
    {
    background-color: #26a69a;
    border-color: #26a69a
}

.form-checkbox.form-normal.form-purple:hover:before, .form-radio.form-normal.form-purple.active:before,
    .form-checkbox.form-normal.form-purple:not (.active ):hover:after {
    border-color: #ba68c8
}

.form-checkbox.form-normal.form-purple.active:before, .form-radio.form-normal.form-purple.active:after
    {
    background-color: #ba68c8;
    border-color: #ba68c8
}

.form-checkbox.form-normal.form-pink:hover:before, .form-radio.form-normal.form-pink.active:before,
    .form-checkbox.form-normal.form-pink:not (.active ):hover:after {
    border-color: #ec407a
}

.form-checkbox.form-normal.form-pink.active:before, .form-radio.form-normal.form-pink.active:after
    {
    background-color: #ec407a;
    border-color: #ec407a
}

.form-checkbox.form-normal.form-dark:hover:before, .form-radio.form-normal.form-dark.active:before,
    .form-checkbox.form-normal.form-dark:not (.active ):hover:after {
    border-color: #263238
}

.form-checkbox.form-normal.form-dark.active:before, .form-radio.form-normal.form-dark.active:after
    {
    background-color: #263238;
    border-color: #263238
}

.form-inline .form-checkbox, .form-inline .form-radio {
    padding-right: 15px
}

.form-checkbox.form-icon:after, .form-radio.form-icon:after {
    content: "\f096";
    color: inherit;
    font-size: 19px;
    display: inline-block;
    font-family: FontAwesome;
    font-style: normal;
    font-weight: normal;
    line-height: 1;
    position: absolute;
    left: .1em;
    top: 50%;
    margin-top: -0.44em;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale
}

.input-group .form-checkbox:after, .input-group .form-radio:after {
    left: 1px
}

.input-group .form-checkbox, .input-group .form-radio {
    padding: 0
}

.form-checkbox.active.form-icon:after {
    content: "\f046"
}

.form-radio.form-icon:after {
    content: "\f10c"
}

.form-radio.form-icon.active:after {
    content: "\f192"
}

.form-checkbox.form-icon.form-primary:after, .form-radio.form-icon.form-primary:after
    {
    color: #42a5f5
}

.form-checkbox.form-icon.form-info:after, .form-radio.form-icon.form-info:after
    {
    color: #00bcd4
}

.form-checkbox.form-icon.form-success:after, .form-radio.form-icon.form-success:after
    {
    color: #8bc34a
}

.form-checkbox.form-icon.form-warning:after, .form-radio.form-icon.form-warning:after
    {
    color: #ffa726
}

.form-checkbox.form-icon.form-danger:after, .form-radio.form-icon.form-danger:after
    {
    color: #ef5350
}

.form-checkbox.form-icon.form-mint:after, .form-radio.form-icon.form-mint:after
    {
    color: #26a69a
}

.form-checkbox.form-icon.form-purple:after, .form-radio.form-icon.form-purple:after
    {
    color: #ba68c8
}

.form-checkbox.form-icon.form-pink:after, .form-radio.form-icon.form-pink:after
    {
    color: #ec407a
}

.form-checkbox.form-icon.form-dark:after, .form-radio.form-icon.form-dark:after
    {
    color: #263238
}

.form-checkbox.form-icon.btn, .form-radio.form-icon.btn {
    position: relative;
    padding-left: 2.7em;
    margin-bottom: 12px
}

.form-checkbox.form-icon.btn:active, .form-radio.form-icon.btn:active {
    margin-bottom: 12px
}

.form-checkbox.form-icon.btn.form-no-label, .form-radio.form-icon.btn.form-no-label
    {
    padding-left: 0;
    padding-right: 0;
    min-height: 2.5em;
    min-width: 2.85em
}

.form-checkbox.form-icon.btn:after, .form-radio.form-icon.btn:after {
    margin-top: -0.46em;
    left: .45em
}

.form-icon.btn.btn-primary:after, .form-icon.btn.btn-info:after,
    .form-icon.btn.btn-success:after, .form-icon.btn.btn-warning:after,
    .form-icon.btn.btn-danger:after, .form-icon.btn.btn-mint:after,
    .form-icon.btn.btn-purple:after, .form-icon.btn.btn-pink:after,
    .form-icon.btn.btn-dark:after {
    color: #fff
}

.form-checkbox.form-icon.btn.btn-labeled, .form-radio.form-icon.btn.btn-labeled
    {
    padding-left: 3em
}

.form-checkbox.form-icon.btn.btn-labeled:before, .form-radio.form-icon.btn.btn-labeled:before
    {
    content: '';
    position: absolute;
    display: inline-block;
    margin-left: 0;
    width: 2.7em;
    height: 100%;
    top: 0;
    left: 0;
    background-color: rgba(0, 0, 0, 0.15)
}

.checkbox.form-block {
    padding-top: 4px
}

.form-radio.form-block, .form-radio.form-block:active, .form-checkbox.form-block,
    .form-checkbox.form-block:active, .form-block>.form-radio,
    .form-block>.form-radio:active, .form-block>.form-checkbox,
    .form-block .form-checkbox:active {
    width: 100%;
    margin: 5px 0
}

.form-checkbox>input[type="checkbox"], .form-radio>input[type="radio"] {
    visibility: visible !important
}
    /*! ========================================================================*/
/*! Toggle Switch - v1.0 */
/*! A trowel component to display a switch */
/*! https://github.com/Trowel/switch/*!  */
/*! designed by Clément Menant (https://www.behance.net/clementmenant) and Loïc Goyet (https://twitter.com/earvinpepper) for AppVentus (http://appventus.com/) */
/*! Under MIT License */
/*! ========================================================================*/
/*! IMPROVEMENT BY THEMEON.NET */
.toggle-switch {
    display: none
}

.toggle-switch+label {
    display: inline-block;
    position: relative;
    height: 16px;
    max-height: 1em;
    cursor: pointer;
    -webkit-touch-callout: none;
    margin-right: 0;
    margin-bottom: 0;
    padding-left: 40px;
    line-height: 16px
}

.toggle-switch+label:empty {
    padding-left: 30px
}

.toggle-switch+label:before {
    position: absolute;
    display: inline-block;
    left: 0;
    content: '';
    border-style: solid;
    width: 30px;
    height: 16px;
    border-width: 0;
    border-color: transparent;
    background-color: #afafaf;
    box-shadow: 0;
    border-radius: 8px;
    transition: all .3s
}

.toggle-switch+label:after {
    position: absolute;
    content: '';
    width: 14px;
    height: 14px;
    top: 1px;
    left: 1px;
    background-color: white;
    box-shadow: 0 2px 5px 0 rgba(51, 51, 51, 0.25);
    border-radius: 16px;
    transition: left .3s, right .3s
}

.toggle-switch:checked+label:before {
    background-color: #4db446;
    transition: all .3s
}

.toggle-switch:checked+label:after {
    left: 15px;
    transition: left .3s, right .3s
}

.toggle-switch:disabled+label {
    cursor: no-drop
}

.toggle-switch:disabled+label:before {
    opacity: .3
}

.preload .toggle-switch+*, .preload .toggle-switch+*:before, .preload .toggle-switch+*:after
    {
    transition: 0 !important
}

.select {
    position: relative;
    display: inline-block
}

.select:before {
    content: '';
    border-style: solid;
    border-width: 7px 5px 0 5px;
    border-left-color: transparent;
    border-right-color: transparent;
    border-bottom-color: transparent;
    border-top-color: #afb9c3;
    display: inline-block;
    height: 0;
    margin: 0 3px;
    vertical-align: middle;
    width: 0;
    position: absolute;
    right: .7em;
    top: 1em;
    z-index: 1
}

.select select {
    padding: 5px 2.5em 5px 10px;
    border: 1px solid #e9e9e9;
    border-radius: 0;
    display: inline-block;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    cursor: pointer;
    outline: 0;
    background-color: #fff;
    color: inherit;
    font-size: 13px;
    line-height: 1.42857;
    vertical-align: middle;
    transition: border-color .5s
}

.select select:disabled {
    cursor: not-allowed;
    opacity: .65
}

.select select:focus {
    border-color: #42a5f5;
    transition: border-color .5s
}

.select select:-moz-focusring {
    color: transparent;
    text-shadow: 0 0 0 #444
}

.select select::-ms-expand {
    display: none
}

.select select:disabled {
    opacity: .5;
    cursor: not-allowed
}

.pci-hor-dots, .pci-ver-dots {
    height: 1.2em;
    width: .85em;
    display: block;
    position: relative
}

.pci-hor-dots:before, .pci-ver-dots:before {
    content: '';
    height: .27em;
    width: .27em;
    background-color: #758697;
    display: block;
    position: absolute;
    top: 0;
    left: .25em;
    box-shadow: 0 .45em 0 #758697, 0 .9em 0 #758697
}

.pci-hor-dots {
    transform: rotate(90deg)
}

.pci-cross:after, .pci-cross:before {
    content: '';
    box-shadow: inset 0 0 0 1px;
    position: absolute;
    left: 50%;
    top: 50%;
    -webkit-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%)
}

.pci-cross:after {
    height: .8em;
    width: 2px
}

.pci-cross:before {
    width: .78em;
    height: 2px
}

.pci-cross {
    display: block;
    border: 1px solid;
    width: 1.3em;
    height: 1.3em;
    -webkit-transform: rotate(45deg);
    transform: rotate(45deg)
}

.pci-circle {
    border-radius: 50%
}

.pci-chevron:before {
    border-style: solid;
    border-width: 2px 2px 0 0;
    content: '';
    display: inline-block;
    height: .75em;
    position: relative;
    top: 0;
    left: 0;
    vertical-align: middle;
    width: .75em
}

.pci-chevron.chevron-up:before {
    transform: rotate(-45deg)
}

.pci-chevron.chevron-right:before {
    transform: rotate(45deg)
}

.pci-chevron.chevron-down:before {
    top: -0.5em;
    transform: rotate(135deg)
}

.pci-chevron.chevron-left:before {
    transform: rotate(-135deg)
}

.plan {
    text-align: center
}

.plan .plan-title {
    font-size: 2em;
    font-weight: 100
}

.plan .plan-icon {
    font-size: 7em;
    color: rgba(0, 0, 0, 0.1)
}

.list-todo .form-checkbox input:checked ~ span {
    text-decoration: line-through;
    opacity: .7
}

.list-todo input:checked ~ label>span {
    text-decoration: line-through;
    opacity: .7
}

.flot-full-content {
    min-height: 120px;
    margin: -8px;
    bottom: -8px
}

.pie-title-center {
    display: inline-block;
    position: relative;
    text-align: center
}

.pie-value {
    display: block;
    position: absolute;
    font-size: 14px;
    height: 40px;
    top: 50%;
    left: 0;
    right: 0;
    margin-top: -20px;
    line-height: 40px
}

.range-vertical {
    height: 135px
}

.collapse {
    display: none
}

.jumbotron {
    padding: 30px;
    background-color: #e2e8f0
}

.navbar-toggle .icon-bar {
    background-color: #aaa
}

.canvas-responsive {
    max-width: 100%
}

a {
    text-decoration: none;
    color: #758697;
    outline: 0
}

a:hover, a:focus {
    text-decoration: none;
    color: #68798a;
    outline: 0 !important
}

button, button:focus {
    outline: 0 !important
}

code {
    background-color: #e7e3f2;
    color: red;
    padding: 2px 7px;
    border-radius: 2px;
    font-size: 97%
}

kbd {
    border-radius: 2px;
    box-shadow: none
}

label {
    font-weight: normal
}

legend {
    padding: 10px;
    font-size: 18px;
    font-weight: 600;
    border-color: #eee
}

mark, .mark {
    background-color: #ffe3a2;
    color: #563c00;
    padding: .1em
}

.close {
    font-size: 15px
}

hr {
    border-color: #e9e9e9
}

.hr-wide {
    margin-left: -7.5px;
    margin-right: -7.5px
}

.hr-xs {
    margin: 5px 0
}

.hr-sm {
    margin: 10px 0
}

.new-section-xs {
    margin: 12px 0;
    min-height: 1px
}

.new-section-sm {
    margin: 25px 0;
    min-height: 1px
}

.new-section-md {
    margin: 50px 0;
    min-height: 1px
}

.new-section-lg {
    margin: 100px 0;
    min-height: 1px
}

.new-section-xl {
    margin: 150px 0;
    min-height: 1px
}

.row {
    margin: 0 -7.5px
}

[class^="col-"]:not (.pad-no ){
    padding-left: 7.5px;
    padding-right: 7.5px
}

.media-block .media-left {
    display: block;
    float: left
}

.media-block .media-right {
    float: right
}

.media-block .media-body {
    display: block;
    overflow: hidden;
    width: auto
}

.middle .media-left, .middle .media-right, .middle .media-body {
    vertical-align: middle
}

.thumbnail {
    padding: 5px;
    border-radius: 0;
    border-color: #e9e9e9
}

.thumbnail .caption {
    padding: 10px 0 5px;
    color: #758697
}

.thumbnail.selected {
    box-shadow: inset 0 0 0 3px #42a5f5
}

#container .table th {
    font-size: 1.05em;
    font-weight: 600;
    border-bottom: 1px solid #e9e9e9;
    color: #2b425b
}

#container .table td {
    border-top: 1px solid #e9e9e9
}

#container .table.table-vcenter th, #container .table.table-vcenter td {
    vertical-align: middle
}

#container .table .min-width {
    width: 1%;
    white-space: nowrap;
    padding-left: 15px !important;
    padding-right: 15px !important
}

#container .table-bordered, #container .table-bordered td, #container .table-bordered th
    {
    border-color: #e9e9e9
}

#container .table-striped>tbody>tr:nth-child(2n+1) {
    background-color: #f8f9fa
}

#container .table-hover>tbody>tr:hover {
    background-color: #f2f4f6
}

.form-control {
    font-size: 13px;
    height: 100%;
    border-radius: 0;
    box-shadow: none;
    border: 1px solid #e9e9e9;
    transition-duration: .5s
}

.form-control:focus {
    border-color: #42a5f5;
    box-shadow: none;
    transition-duration: .5s
}

/*.form-control:focus-feedback {*/
    /*z-index: 10*/
/*}*/

.has-error .form-control, .has-warning .form-control, .has-success .form-control
    {
    box-shadow: none !important
}

i.form-control-feedback {
    line-height: 25px
}

.input-group-addon {
    border: 1px solid #e1e5ea;
    background-color: transparent;
    border-radius: 0;
    min-width: 45px
}

.nav-pills>li>a {
    border-radius: 0
}

.nav-pills>.active>a, .nav-pills>.active>a:hover, .nav-pills>.active>a:focus
    {
    background-color: #42a5f5
}

.nav-tabs>li>a {
    border-radius: 0
}

.list-group.bg-trans .list-group-item:not (.active ):not (.disabled ){
    background-color: transparent;
    border-color: transparent;
    color: inherit
}

.list-group.bg-trans .list-group-item .disabled {
    opacity: .5
}

.list-group.bg-trans a.list-group-item:hover:not (.active ){
    background-color: rgba(0, 0, 0, 0.05)
}

.list-group.bord-no .list-group-item {
    border-color: transparent
}

.list-group .list-divider {
    display: block
}

.list-group-item {
    border-color: #e9e9e9
}

.list-group-item, a.list-group-item, button.list-group-item, a.list-group-item:hover,
    a.list-group-item:focus, button.list-group-item:hover, button.list-group-item:focus
    {
    color: #758697
}

.list-group-item .list-group-item-heading, a.list-group-item .list-group-item-heading,
    button.list-group-item .list-group-item-heading {
    color: #2b425b
}

.list-group-item-heading {
    margin-top: 5px
}

.list-group-item:first-child {
    border-top-left-radius: 0;
    border-top-right-radius: 0
}

.list-group-item:last-child {
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0
}

.list-group-item .list-group-item.disabled, .list-group-item .list-group-item.disabled:hover,
    .list-group-item .list-group-item.disabled:focus {
    background-color: rgba(0, 0, 0, 0.07);
    border-color: transparent
}

.list-group-item.active, .list-group-item.active:hover, .list-group-item.active:focus
    {
    background-color: #42a5f5;
    border-color: #42a5f5;
    color: #fff
}

.list-group-item.active .list-group-item-text, .list-group-item.active:hover .list-group-item-text,
    .list-group-item.active:focus .list-group-item-text {
    color: #fff
}

a.list-group-item:hover, a.list-group-item:focus {
    background-color: rgba(0, 0, 0, 0.05)
}

a.list-group-item-primary, .list-group-item-primary {
    background-color: #64b5f7;
    border-color: transparent;
    color: #fff
}

a.list-group-item-primary:hover, a.list-group-item-primary:focus {
    background-color: #72bcf8;
    color: #fff
}

a.list-group-item-info, .list-group-item-info {
    background-color: #00dcf8;
    border-color: transparent;
    color: #fff
}

a.list-group-item-info:hover, a.list-group-item-info:focus {
    background-color: #08e3ff;
    color: #fff
}

a.list-group-item-success, .list-group-item-success {
    background-color: #9ccc65;
    border-color: transparent;
    color: #fff
}

a.list-group-item-success:hover, a.list-group-item-success:focus {
    background-color: #a4d070;
    color: #fff
}

a.list-group-item-warning, .list-group-item-warning {
    background-color: #ffb54a;
    border-color: transparent;
    color: #fff
}

a.list-group-item-warning:hover, a.list-group-item-warning:focus {
    background-color: #ffbc59;
    color: #fff
}

a.list-group-item-danger, .list-group-item-danger {
    background-color: #f27371;
    border-color: transparent;
    color: #fff
}

a.list-group-item-danger:hover, a.list-group-item-danger:focus {
    background-color: #f3817f;
    color: #fff
}

a.list-group-item-mint, .list-group-item-mint {
    background-color: #2dc3b5;
    border-color: transparent;
    color: #fff
}

a.list-group-item-mint:hover, a.list-group-item-mint:focus {
    background-color: #30d0c1;
    color: #fff
}

a.list-group-item-purple, .list-group-item-purple {
    background-color: #c682d2;
    border-color: transparent;
    color: #fff
}

a.list-group-item-purple:hover, a.list-group-item-purple:focus {
    background-color: #cb8dd6;
    color: #fff
}

a.list-group-item-pink, .list-group-item-pink {
    background-color: #ef6091;
    border-color: transparent;
    color: #fff
}

a.list-group-item-pink:hover, a.list-group-item-pink:focus {
    background-color: #f16e9a;
    color: #fff
}

a.list-group-item-dark, .list-group-item-dark {
    background-color: #34454d;
    border-color: transparent;
    color: #fff
}

a.list-group-item-dark:hover, a.list-group-item-dark:focus {
    background-color: #3b4d56;
    color: #fff
}

.label {
    border-radius: 0;
    font-weight: 600
}

.label:empty {
    display: inline-block;
    width: 1.5em;
    height: 1.5em;
    vertical-align: sub
}

.label.label-fw {
    margin-right: .5em
}

.labels .label {
    display: inline-block;
    margin-right: 3px;
    margin-bottom: 3px
}

.label-md {
    font-size: 100%
}

.label-table {
    display: inline-block;
    width: 80%;
    min-width: 8ex;
    font-size: 1em;
    max-width: 100px;
    padding: 5px;
    text-overflow: ellipsis;
    overflow: hidden;
    vertical-align: top
}

.label-default {
    background-color: #e3e8ee;
    color: #333
}

.label-primary {
    background-color: #42a5f5
}

.label-info {
    background-color: #00bcd4
}

.label-success {
    background-color: #8bc34a
}

.label-warning {
    background-color: #ffa726
}

.label-danger {
    background-color: #ef5350
}

.label-mint {
    background-color: #26a69a
}

.label-purple {
    background-color: #ba68c8
}

.label-pink {
    background-color: #ec407a
}

.label-dark {
    background-color: #263238
}

.breadcrumb {
    border-radius: 0;
    background-color: transparent;
    margin-bottom: 10px;
    padding: 0 15px
}

.breadcrumb li, .breadcrumb li a {
    color: #afb9c3
}

.breadcrumb>li+li:before {
    content: "\f105";
    display: inline-block;
    font-family: FontAwesome;
    font-style: normal;
    font-weight: normal;
    line-height: 1
}

.breadcrumb .active {
    font-weight: normal
}

.alert {
    border-radius: 0;
    border: 0;
    padding: 15px 2em 15px 15px;
    position: relative
}

.alert .alert-link {
    text-decoration: underline;
    font-weight: 600
}

.alert .alert-link:hover {
    text-decoration: underline
}

.alert .close {
    font-size: 12.5px;
    text-shadow: none;
    opacity: .7;
    position: absolute;
    left: auto;
    right: 10px;
    top: 10px
}

.alert button.close {
    padding: 1px;
    border-radius: 20px;
    transition: all, 0.3s
}

.alert button.close>span:not (.sr-only ){
    display: block;
    width: 1em;
    height: 1em;
    line-height: .8em
}

.alert button.close:hover {
    opacity: .55
}

.alert button.close:active {
    opacity: .3
}

.alert-primary {
    background-color: #6ab5f1;
    border-color: transparent;
    border-left: 3px solid #0f80db;
    color: #fff
}

.alert-primary .close, .alert-primary .alert-link {
    color: #fff
}

/*.alert-info {*/
    /*background-color: #1bc7dc;*/
    /*border-color: transparent;*/
    /*border-left: 3px solid #0c6f7b;*/
    /*color: #fff*/
/*}*/

.alert-info .close, .alert-info .alert-link {
    color: #fff
}

.alert-success {
    background-color: #9cc56c;
    border-color: transparent;
    border-left: 3px solid #648e33;
    color: #fff
}

.alert-success .close, .alert-success .alert-link {
    color: #fff
}

.alert-warning {
    background-color: #f9b450;
    border-color: transparent;
    border-left: 3px solid #d58003;
    color: #fff
}

.alert-warning .close, .alert-warning .alert-link {
    color: #fff
}

.alert-danger {
    background-color: #ed7876;
    border-color: transparent;
    border-left: 3px solid #db1c18;
    color: #fff
}

.alert-danger .close, .alert-danger .alert-link {
    color: #fff
}

.alert-mint {
    background-color: #35bbae;
    border-color: transparent;
    border-left: 3px solid #1a665f;
    color: #fff
}

.alert-mint .close, .alert-mint .alert-link {
    color: #fff
}

.alert-purple {
    background-color: #c288cc;
    border-color: transparent;
    border-left: 3px solid #9540a3;
    color: #fff
}

.alert-purple .close, .alert-purple .alert-link {
    color: #fff
}

.alert-pink {
    background-color: #e96793;
    border-color: transparent;
    border-left: 3px solid #c81853;
    color: #fff
}

.alert-pink .close, .alert-pink .alert-link {
    color: #fff
}

.alert-dark {
    background-color: #4f5e65;
    border-color: transparent;
    border-left: 3px solid #1d2428;
    color: #fff
}

.alert-dark .close, .alert-dark .alert-link {
    color: #fff
}

.modal {
    text-align: center
}

.modal:before {
    content: '';
    display: inline-block;
    height: 100%;
    width: 0;
    margin-left: -1em;
    vertical-align: middle
}

.modal.fade:not (.animated ) .modal-dialog {
    opacity: 0;
    -webkit-transform: translateY(-150%);
    -ms-transform: translateY(-150%);
    transform: translateY(-150%);
    -webkit-transition: all .5s linear .5s;
    transition: all .5s linear .5s
}

.modal.fade.in:not (.animated ) .modal-dialog {
    opacity: 1;
    -webkit-transform: translateY(0);
    -ms-transform: translateY(0);
    transform: translateY(0);
    -webkit-transition: all .5s;
    transition: all .5s
}

.bootbox.modal.in:not (.fade ){
    opacity: 1;
    transition: opacity, 0.5s
}

.bootbox.modal:not (.fade ){
    opacity: 0;
    -webkit-transition: opacity .5s linear .5s;
    transition: opacity .5s linear .5s
}

.modal-dialog {
    display: inline-block;
    vertical-align: middle;
    text-align: left;
    margin-top: -1%;
    min-width: 90%
}

.modal-dialog.animated {
    -webkit-animation-duration: .7s;
    animation-duration: .7s
}



.modal-header:after {
    content: '';
    position: absolute;
    display: block;
    bottom: 0;
    left: 0;
    right: 0;
    height: 1px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.07)
}

.modal-footer {
    background-color: transparent;
    color: #758697;
    border-color: rgba(0, 0, 0, 0.07);
    border-top-left-radius: 0;
    border-top-right-radius: 0
}





.modal-body>.close, .modal-header>.close {
    top: 50%;
    margin-top: -0.5em;
    right: 10px;
    left: auto;
    position: absolute;
    background-color: transparent !important
}

.modal-body>.close {
    top: 10px;
    margin-top: 0 !important
}



.modal-footer {
    padding: 10px 15px;
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0
}

.modal-backdrop.in {
    opacity: .75
}


.tooltip {
    font-size: 13px;
    z-index: 999999
}

.tooltip-inner {
    font-size: 13px;
    border-radius: 0;
    padding: 5px 10px;
    background-color: #263238
}

.tooltip.top .tooltip-arrow, .tooltip.top-left .tooltip-arrow, .tooltip.top-right .tooltip-arrow
    {
    border-top-color: #263238
}

.tooltip.right .tooltip-arrow {
    border-right-color: #263238
}

.tooltip.left .tooltip-arrow {
    border-left-color: #263238
}

.tooltip.bottom .tooltip-arrow, .tooltip.bottom-left .tooltip-arrow,
    .tooltip.bottom-right .tooltip-arrow {
    border-bottom-color: #263238
}

.tooltip.in {
    opacity: 1
}

.tooltip h1, .tooltip h2, .tooltip h3, .tooltip h4, .tooltip h5,
    .tooltip h6, .tooltip .h1, .tooltip .h2, .tooltip .h3, .tooltip .h4,
    .tooltip .h5, .tooltip .h6 {
    color: inherit
}

.popover {
    font-family: inherit;
    font-size: 13px;
    border-radius: 0;
    box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.15)
}

.popover-title {
    background-color: transparent;
    color: #2b425b;
    font-size: 1.2em;
    font-weight: 600;
    border-bottom: 1px solid rgba(0, 0, 0, 0.07);
    border-radius: 0
}

.popover>.arrow {
    border-width: 9px
}

.popover>.arrow:after {
    border-width: 9px
}

.popover.left>.arrow {
    right: -9px;
    margin-top: -9px
}

.popover.left>.arrow:after {
    bottom: -9px
}

.popover.right>.arrow {
    left: -9px;
    margin-top: -9px
}

.popover.right>.arrow:after {
    bottom: -9px
}

.popover.top>.arrow {
    bottom: -9px
}

.popover.top>.arrow:after {
    margin-left: -9px
}

.popover.bottom>.arrow {
    top: -9px
}

.popover.bottom>.arrow:after {
    margin-left: -9px
}

.popover-content {
    padding: 10px 15px 20px
}

#container .badge {
    color: #fff;
    font-size: .9em;
    font-weight: 600
}

.badge:empty.badge-icon {
    display: inline-block;
    width: .85em;
    height: .85em;
    padding: 0;
    min-width: 5px;
    margin: .5em;
    border-radius: 50%
}

.badge.badge-fw, .badge:empty.badge-fw {
    margin-right: 1em
}

.badge-stat {
    position: absolute;
    right: 0;
    top: 0;
    margin: 0 7px 0 0 !important;
    box-shadow: 0 0 0 2px
}

.badge-default {
    background-color: #e3e8ee;
    color: #333
}

.badge-primary {
    background-color: #42a5f5
}

.badge-info {
    background-color: #00bcd4
}

.badge-success {
    background-color: #8bc34a
}

.badge-warning {
    background-color: #ffa726
}

.badge-danger {
    background-color: #ef5350
}

.badge-mint {
    background-color: #26a69a
}

.badge-purple {
    background-color: #ba68c8
}

.badge-pink {
    background-color: #ec407a
}

.badge-dark {
    background-color: #263238
}

.dropdown-header {
    font-weight: 600;
    font-size: 1.11em;
    color: #2b425b;
    padding: 5px 20px 5px 10px
}

.dropdown-toggle>.dropdown-caret {
    display: inline-block;
    width: 0;
    height: 0;
    margin: 0 3px;
    border-style: solid;
    border-width: 6px 4px 0 4px;
    border-left-color: transparent;
    border-right-color: transparent;
    border-bottom-color: transparent;
    vertical-align: baseline
}

.dropdown-toggle>.dropdown-caret.caret-up {
    border-width: 0 4px 6px 4px;
    border-bottom-color: initial;
    border-top-color: transparent
}

.dropdown-menu {
    font-size: 13px;
    border-radius: 0;
    box-shadow: 0 5px 12px 2px rgba(0, 0, 0, 0.25);
    margin: 0;
    padding: 0;
    border: 0
}

.dropdown-menu>li>a {
    color: #758697
}

.dropdown-menu-right {
    left: auto;
    right: 0
}

.dropup .dropdown-menu {
    box-shadow: 0 -5px 12px 2px rgba(0, 0, 0, 0.25)
}

.dropdown-menu:not (.head-list )>li>a {
    padding: 5px 10px
}

.dropdown-menu:not (.head-list )>li>a:hover {
    background-color: #42a5f5;
    color: #fff
}

.dropdown-menu.with-arrow:before, .dropdown-menu.with-arrow:after {
    content: "";
    display: block;
    position: absolute;
    width: 0;
    height: 0;
    left: 0;
    top: 0;
    border-left: 7px solid transparent;
    border-right: 7px solid transparent
}

.dropdown-menu.with-arrow:before {
    border-bottom: 7px solid #d0d0d0;
    margin: -7px 0 0 15px
}

.dropdown-menu.with-arrow:after {
    border-bottom: 7px solid #fff;
    margin: -6px 0 0 15px
}

.dropdown-menu-right.dropdown-menu.with-arrow:before {
    left: auto;
    right: 0;
    margin: -7px 25px 0 0
}

.dropdown-menu-right.dropdown-menu.with-arrow:after {
    left: auto;
    right: 0;
    margin: -6px 25px 0 0
}

.dropdown-menu-sm {
    min-width: 220px
}

.dropdown-menu-md {
    min-width: 270px
}

.dropdown-menu-lg {
    min-width: 300px
}

.dropdown.open>.btn, .btn-group.open .dropdown-toggle {
    box-shadow: inset 0 3px 1px rgba(0, 0, 0, 0.3)
}

.well {
    background-color: #e5ebf1;
    border-color: #dbe3ec;
    border-radius: 0;
    box-shadow: none
}

.well-xs {
    padding: 5px
}

.progress {
    height: 12px;
    margin-bottom: 15px;
    border-radius: 0;
    box-shadow: none;
    background-color: rgba(0, 0, 0, 0.1)
}

.progress-bar {
    font-size: 10px;
    background-color: #42a5f5;
    line-height: 1.05em;
    box-shadow: none
}

.progress-light-base {
    background-color: #fff
}

.progress-dark-base {
    background-color: rgba(255, 255, 255, 0.2)
}

.progress-xl {
    height: 30px;
    margin-bottom: 20px
}

.progress-xl .progress-bar {
    font-size: 13px;
    line-height: 30px
}

.progress-lg {
    height: 20px;
    margin-bottom: 20px
}

.progress-lg .progress-bar {
    font-size: 13px;
    line-height: 20px
}

.progress-md {
    height: 8px;
    margin-bottom: 5px
}

.progress-md .progress-bar {
    font-size: 5px;
    line-height: 8px
}

.progress-sm {
    height: 4px;
    margin-bottom: 5px
}

.progress-sm .progress-bar {
    font-size: 0;
    line-height: 4px
}

.progress-xs {
    height: 2px;
    margin-bottom: 10px
}

.progress-xs .progress-bar {
    font-size: 0;
    line-height: 2px
}

.progress-bar-light {
    background-color: #fff
}

.progress-bar-primary {
    background-color: #42a5f5
}

.progress-bar-info {
    background-color: #00bcd4
}

.progress-bar-success {
    background-color: #8bc34a
}

.progress-bar-warning {
    background-color: #ffa726
}

.progress-bar-danger {
    background-color: #ef5350
}

.progress-bar-mint {
    background-color: #26a69a
}

.progress-bar-purple {
    background-color: #ba68c8
}

.progress-bar-pink {
    background-color: #ec407a
}

.progress-bar-dark {
    background-color: #263238
}

.pager li>a:active, .pagination>li a:active {
    box-shadow: inset 0 3px 1px rgba(0, 0, 0, 0.2)
}

.pager li>a:hover, .pager li>a:focus, .pagination>li a:hover,
    .pagination>li a:focus {
    background-color: #fff;
    border-color: #42a5f5;
    color: #42a5f5;
    box-shadow: inset 0 0 1px #42a5f5;
    z-index: 2;
    transition: border-color, 0.3s
}

.pager li>a, .pager li>span {
    border-radius: 0;
    border-color: #dcdcdc
}

.pager.pager-rounded li>a, .pager.pager-rounded li>span {
    border-radius: 15px
}

.pager .disabled>a, .pager .disabled>span, .pager .disabled>a:hover,
    .pager .disabled>span:hover, .pager .disabled>a:focus, .pager .disabled>span:focus,
    .pagination .disabled>a, .pagination .disabled>span, .pagination .disabled>a:hover,
    .pagination .disabled>span:hover, .pagination .disabled>a:focus,
    .pagination .disabled>span:focus {
    opacity: .7;
    border-color: #dcdcdc;
    box-shadow: none
}

.pagination>li>a, .pagination>li>span {
    color: inherit;
    border-color: #dcdcdc;
    transition: border-color, 0.3s
}

.pagination>li>span {
    cursor: default
}

.pagination>li>span:hover {
    background-color: #fff
}

.pagination>li:first-child>a, .pagination>li:first-child span {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0
}

.pagination>li:last-child>a, .pagination>li:last-child span {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0
}

.pagination>.active>a, .pagination>.active>span, .pagination>.active>a:hover,
    .pagination>.active>span:hover, .pagination>.active>a:focus,
    .pagination>.active>span:focus {
    background-color: #42a5f5;
    border-color: #42a5f5
}

.carousel-inner>.item {
    padding-top: 15px
}

.carousel-control.left, .carousel-control.right {
    background-image: none;
    background-repeat: no-repeat;
    color: inherit
}

.carousel-control, .carousel-control:focus {
    font-size: 1em;
    text-shadow: none;
    width: auto;
    padding: 10px;
    top: 0;
    bottom: 0;
    opacity: .5;
    transition: opacity .5s
}

.carousel-control.auto-hide {
    opacity: 0
}

.carousel-control:before {
    content: '';
    display: inline-block;
    height: 100%;
    width: 0;
    vertical-align: middle
}

.carousel-control i {
    position: relative;
    top: .25em
}

.carousel:hover .carousel-control {
    opacity: 1;
    transition: opacity .5s
}

.carousel-indicators.out {
    bottom: 0
}

.carousel-indicators.out+.carousel-inner {
    padding-bottom: 30px
}

.carousel-indicators.square li {
    border-radius: 0
}

.carousel-indicators>.active {
    background-color: transparent;
    box-shadow: inset 0 0 0 50px;
    border-color: transparent
}

.carousel-indicators>li {
    border-color: inherit
}

blockquote {
    border-left: 3px solid #cbd6e3
}


.form-horizontal .control-label {
    /* line-height:30px;
    padding-top:0px; */
}

.form-horizontal .control-label.text-left {
    text-align: left
}

.panel>.panel-heading+.panel-collapse>.panel-body {
    border-top: 0
}



.tab-base {
    margin-bottom: 35px
}

.tab-base .tab-content {
    background-color: #fff;
    box-shadow: 0 2px 0 rgba(0, 0, 0, 0.05);
    border-bottom-left-radius: 2px;
    border-bottom-right-radius: 2px;
    padding: 15px
}

.tab-base .tab-content .tab-footer {
    background-color: #f6f8fa;
    color: #758697;
    border-color: #eff3f7;
    position: relative;
    margin: 0 -15px -15px -15px;
    padding: 10px 15px
}

.tab-base .nav-tabs {
    border: 0
}

.tab-base .nav-tabs>li {
    margin-bottom: -2px
}

.tab-base .nav-tabs>li:not (.active )>a {
    background-color: rgba(0, 0, 0, 0.05);
    opacity: .7;
    transition: opacity, 0.3s
}

.tab-base .nav-tabs>li:not (.active )>a:hover {
    opacity: 1;
    background-color: rgba(255, 255, 255, 0.55);
    border-bottom-color: transparent;
    transition: opacity, 0.3s
}

.tab-base .nav-tabs>.active>a, .tab-base .nav-tabs>.active a:hover,
    .tab-base .nav-tabs>.active>a:focus {
    border-color: transparent
}

.tab-base .nav-tabs.tabs-right {
    text-align: right
}

.tab-base .nav-tabs.tabs-right>li {
    float: none;
    display: inline-block;
    margin-right: -2px
}

.tab-footer:after {
    content: '';
    display: table;
    clear: both
}

.nav-tabs li a {
    border-radius: 0
}

.nav-tabs.tab-right {
    text-align: right
}

.nav-tabs.tab-right>li {
    display: inline-block;
    text-align: left;
    float: none
}

.nav-tabs.tab-right>li>a {
    margin-right: 0
}

.nav-tabs .label, .nav-tabs .badge {
    margin-left: 4px
}

.tab-stacked-left, .tab-stacked-right {
    display: table;
    height: 100%;
    width: 100%
}

.tab-stacked-left .nav-tabs>li, .tab-stacked-right .nav-tabs>li {
    float: none;
    margin: 0
}

.tab-stacked-left .nav-tabs>li>a, .tab-stacked-right .nav-tabs>li>a {
    margin: 0 0 2px
}

.tab-stacked-left .nav-tabs>li:last-child>a, .tab-stacked-right .nav-tabs>li:last-child>a
    {
    margin-bottom: 0
}

.tab-stacked-left .nav-tabs, .tab-stacked-right .nav-tabs,
    .tab-stacked-left .tab-content, .tab-stacked-right .tab-content {
    display: table-cell;
    vertical-align: top
}

.tab-stacked-left .tab-content, .tab-stacked-right .tab-content {
    overflow: hidden
}

.tab-stacked-left .nav-tabs {
    width: 1%;
    border: 0
}

.tab-stacked-left .nav-tabs>li a {
    border-right-color: transparent;
    border-radius: 0
}

.tab-stacked-left .nav-tabs>.active>a:hover, .tab-stacked-left .nav-tabs>.active>a:focus
    {
    border-right-color: transparent
}

.tab-stacked-left.tab-base .nav-tabs>li:not (.active ) a:hover {
    border-right-color: transparent
}

.tab-stacked-left .tab-content {
    border-left-color: transparent;
    border-radius: 0
}

.tab-stacked-right .nav-tabs {
    width: 1%;
    border: 0
}

.tab-stacked-right .nav-tabs>li a {
    border-left-color: transparent;
    border-radius: 0
}

.tab-stacked-right .nav-tabs>.active>a:hover, .tab-stacked-right .nav-tabs>.active>a:focus
    {
    border-left-color: transparent
}

.tab-stacked-right.tab-base .nav-tabs>li:not (.active ) a:hover {
    border-left-color: transparent
}

.tab-stacked-right .tab-content {
    border-right-color: transparent;
    border-radius: 0
}

.bg-trans {
    background-color: transparent
}

.bg-light {
    background-color: #fff
}

.bg-light, .bg-light a {
    color: #758697
}

.bg-gray-light {
    background-color: #f5f6f8
}

.bg-gray-light, .bg-gray-light a {
    color: #758697
}

.bg-gray {
    background-color: #e6eaed
}

.bg-gray, .bg-gray a {
    color: #758697
}

.bg-gray-dark {
    background-color: #c8d1d8
}

.bg-gray-dark, .bg-gray-dark a {
    color: #758697
}

.bg-trans-light {
    background-color: rgba(255, 255, 255, 0.1)
}

.bg-trans-light, .bg-trans-light a {
    color: inherit
}

.bg-trans-dark {
    background-color: rgba(0, 0, 0, 0.05)
}

.bg-trans-dark, .bg-trans-dark a {
    color: inherit
}

.bg-primary {
    background-color: #42a5f5
}

.bg-primary, .bg-primary a {
    color: #fff
}

.bg-info {
    background-color: #00bcd4
}

.bg-info, .bg-info a {
    color: #fff
}

.bg-success {
    background-color: #8bc34a
}

.bg-success, .bg-success a {
    color: #fff
}

.bg-warning {
    background-color: #ffa726
}

.bg-warning, .bg-warning a {
    color: #fff
}

.bg-danger {
    background-color: #ef5350
}

.bg-danger, .bg-danger a {
    color: #fff
}

.bg-mint {
    background-color: #26a69a
}

.bg-mint, .bg-mint a {
    color: #fff
}

.bg-purple {
    background-color: #ba68c8
}

.bg-purple, .bg-purple a {
    color: #fff
}

.bg-pink {
    background-color: #ec407a
}

.bg-pink, .bg-pink a {
    color: #fff
}

.bg-dark {
    background-color: #263238
}

.bg-dark, .bg-dark a {
    color: #fff
}

.text-light, a.text-light:hover, a.text-light:focus {
    color: #fff
}

.text-muted, a.text-muted:hover, a.text-muted:focus {
    color: #afb9c3
}

.text-primary, a.text-primary:hover, a.text-primary:focus {
    color: #128ef2
}

.text-info, a.text-info:hover, a.text-info:focus {
    color: #008fa1
}

.text-success, a.text-success:hover, a.text-success:focus {
    color: #71a436
}

.text-warning, a.text-warning:hover, a.text-warning:focus {
    color: #f29000
}

.text-danger, a.text-danger:hover, a.text-danger:focus {
    color: #eb2521
}

.text-main, a.text-main:hover, a.text-main:focus {
    color: #2b425b
}

.text-mint, a.text-mint:hover, a.text-mint:focus {
    color: #1c7d74
}

.text-purple, a.text-purple:hover, a.text-purple:focus {
    color: #a844b9
}

.text-pink, a.text-pink:hover, a.text-pink:focus {
    color: #e2175b
}

.text-dark, a.text-dark:hover, a.text-dark:focus {
    color: #11171a
}

.btn {
    cursor: pointer;
    padding: 3px 15px;
    border-radius: 0px;
    -webkit-border-radius: 0px;
    -moz-border-radius: 0px;
    font-size: 13px;
    line-height: 1.7;
    vertical-align: middle;
    transition: all .25s;
    /* box-shadow: inset 0 -2px 0 rgba(0, 0, 0, .05);
    -moz-box-shadow: inset 0 -2px 0 rgba(0, 0, 0, .05);
    -webkit-box-shadow: inset 0 -2px 0 rgba(0, 0, 0, .05) */
    
}

.btn:not (.disabled ):not (:disabled ):active, .btn:not (.disabled ):not
    (:disabled ).active {
    box-shadow: inset 0 2px 1px rgba(0, 0, 0, 0.2)
}

.btn-lg, .btn-icon.btn-lg {
    font-size: 17px;
    line-height: 1.33
}

.btn-sm, .btn-icon.btn-sm {
    font-size: 11px;
    line-height: 1.5
}

.btn-xs, .btn-icon.btn-xs {
    font-size: 11px;
    line-height: 1.5
}

.btn-icon {
    padding-left: 9px;
    padding-right: 9px
}

.btn-icon>i, .btn-icon:before {
    display: inline-block;
    min-width: 1.05em
}

.btn-file {
    position: relative;
    overflow: hidden
}

.btn-file input[type=file] {
    position: absolute;
    top: 0;
    right: 0;
    min-width: 100%;
    min-height: 100%;
    font-size: 100px;
    text-align: right;
    filter: alpha(opacity = 0);
    opacity: 0;
    outline: 0;
    background: white;
    cursor: inherit;
    display: block
}

.btn-link {
    border-color: transparent
}

.btn.btn-link:focus, .btn.btn-link:active {
    box-shadow: none
}

.btn-link.disabled:hover, .btn-link.disabled:focus {
    text-decoration: none
}

.btn-trans {
    background-color: transparent;
    border-color: transparent;
    color: #afb9c3
}

.btn-trans:focus, .btn-trans:active {
    box-shadow: none !important
}


.btn-group-vertical .btn:not (.btn-default ), .btn-group .btn:not (.btn-default
    ){
    border-color: rgba(0, 0, 0, 0.09)
}

.btn-lg, .btn-group-lg>.btn, .btn-icon.btn-lg {
    padding: 10px 16px
}

.btn-sm, .btn-group-sm>.btn, .btn-icon.btn-sm {
    padding: 5px 10px
}

.btn-xs, .btn-group-xs>.btn, .btn-icon.btn-xs {
    padding: 1px 5px
}

.btn-lg, .btn-group-lg>.btn, .btn-icon.btn-lg {
    border-radius: 0
}

.btn-lg, .btn-group-sm>.btn, .btn-lg, .btn-group-xs>.btn, .btn-icon.btn-lg,
    .btn-icon.btn-lg {
    border-radius: 0
}

.btn-group-vertical>.btn:first-child:not (:last-child ){
    border-top-left-radius: 0;
    border-top-right-radius: 0
}

.btn-group-vertical>.btn:last-child:not (:first-child ){
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0
}

.btn-circle {
    padding: 7px;
    border-radius: 50%
}

.btn-lg.btn-circle {
    padding: 17px
}

.btn-sm.btn-circle {
    padding: 7px
}

.btn-xs.btn-circle {
    padding: 4px
}

.btn-icon.btn-circle:before, .btn-icon.btn-circle>i {
    display: block;
    width: 1.4em;
    height: 1.4em;
    line-height: 1.4
}

.btn-rounded {
    border-radius: 17px;
    overflow: hidden
}

.btn-lg.btn-rounded {
    border-radius: 30px
}

.btn-sm.btn-rounded {
    border-radius: 15px
}

.btn-xs.btn-rounded {
    border-radius: 10px
}

.btn-group.btn-rounded {
    overflow: hidden
}

.btn-labeled, .btn-labeled.fa {
    overflow: hidden
}

.btn-labeled:not (.btn-block ):not (.form-icon ){
    font-family: inherit;
    font-size: 13px;
    line-height: 1.42857;
    padding-bottom: 0;
    padding-top: 0
}

.btn-block.btn-labeled:not (.form-icon ){
    font-family: inherit;
    font-size: 13px;
    line-height: 1.42857
}

.btn-block.btn-labeled:not (.form-icon ):before {
    float: left;
    margin-top: -7px;
    margin-bottom: -7px
}

.btn-labeled .btn-label {
    background-color: rgba(0, 0, 0, 0.05);
    display: inline-block;
    margin-left: -12px;
    margin-right: 6px;
    padding: 6px 12px;
    line-height: 1.42857
}

.btn-labeled:before {
    background-color: rgba(0, 0, 0, 0.1);
    display: inline-block;
    margin-left: -12px;
    margin-right: 6px;
    padding: 6px 12px;
    line-height: 1.42857
}

.btn-labeled.fa:before, .btn-labeled .fa:before {
    font-family: fontAwesome
}

.btn-default.btn-labeled:before, .btn-default .btn-label {
    background-color: rgba(0, 0, 0, 0.05);
    color: inherit
}

.btn-lg.btn-labeled {
    font-size: 18px;
    line-height: 1.33
}

.btn-sm.btn-labeled {
    font-size: 12px;
    line-height: 1.5
}

.btn-xs.btn-labeled {
    font-size: 12px;
    line-height: 1.5
}

.btn-lg.btn-labeled:before, .btn-lg .btn-label {
    margin-left: -16px;
    margin-right: 10px;
    padding: 10px 16px
}

.btn-lg.btn-block.btn-labeled:not (.form-icon ):before {
    margin-top: -10px;
    margin-bottom: -10px
}

.btn-sm.btn-labeled:before, .btn-sm .btn-label {
    margin-left: -10px;
    margin-right: 5px;
    padding: 5px 10px
}

.btn-sm.btn-block.btn-labeled:not (.form-icon ):before {
    margin-top: -5px;
    margin-bottom: -5px
}

.btn-xs.btn-labeled:before, .btn-xs .btn-label {
    margin-left: -5px;
    margin-right: 1px;
    padding: 1px 5px
}

.btn-xs.btn-block.btn-labeled:not (.form-icon ):before {
    margin-top: -1px;
    margin-bottom: -1px
}

.btn-labeled.icon-2x:before, .btn-labeled .btn-label.icon-2x:before {
    vertical-align: -0.15em
}

.btn-labeled.icon-3x:before, .btn-labeled .btn-label.icon-3x:before {
    vertical-align: -0.18em
}

.btn-labeled.icon-4x:before, .btn-labeled .btn-label.icon-4x:before {
    vertical-align: -0.2em
}

.btn-labeled.icon-5x:before, .btn-labeled .btn-label.icon-5x:before {
    vertical-align: -0.25em
}

.panel {
    border-radius: 0;
    border: 1px solid #e7ecf1;
    /*border-bottom: 1px solid rgba(0, 0, 0, 0.17);*/
    margin-bottom: 10px
}

.panel hr {
    border-color: rgba(0, 0, 0, 0.1)
}

.panel .panel-bg-cover {
    max-height: 180px;
    overflow: hidden
}

.panel .panel-bg-cover img {
    min-width: 100%;
    min-height: 100%;
    background-size: cover
}

.panel.remove {
    opacity: 0;
    transition: opacity, 0.5s
}

.panel .alert {
    border-radius: 0
}

.panel.panel-bg-img {
    position: relative
}

.panel .panel-bg-wrap {
    overflow: hidden;
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0
}

.panel .panel-bg-wrap>img {
    position: absolute;
    top: 0;
    left: 0
}

.panel .panel-bg-wrap+.panel-body {
    position: relative
}

.panel-media {
    box-shadow: 0 -50px 20px -10px rgba(0, 0, 0, 0.2);
    padding: 10px 15px 15px 140px;
    position: relative
}

.panel-media-img {
    position: absolute;
    width: 96px;
    height: 96px;
    left: 20px;
    top: -48px
}

.panel-media-heading {
    color: #fff;
    position: absolute;
    top: -2.7em
}

.panel .panel-heading, .panel>:first-child {
    border-top-left-radius: 0;
    border-top-right-radius: 0
}

.panel .panel-footer, .panel>:last-child {
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0
}

.panel-body-full {
    margin-left: -20px;
    margin-right: -20px
}

.panel-body {
    padding: 15px 20px 25px
}

.panel-body>.row, .panel-body>[class^="form-"]>.row {
    margin: 0
}

/* 

.panel-body>.row>[class^="col-"]:first-child, .panel-body>[class^="form-"]>.row>[class^="col-"]:first-child
    {
    padding-left: 0 !important
}

.panel-body>.row>[class^="col-"]:last-child, .panel-body>[class^="form-"]>.row>[class^="col-"]:last-child
    {
    padding-right: 0 !important
} 
*/

.panel-body>.row>.col-xs-12, .panel-body>.row>.col-sm-12, .panel-body>.row>.col-md-12,
    .panel-body>.row>.col-lg-12 {
    padding-right: 0
}


.panel-trans {
    border-color: transparent;
    box-shadow: none;
    background-color: transparent
}

.panel-heading {
    position: relative;
    height: 40px;
    padding: 0;
    color: #2b425b;
    border-bottom:1px solid #e7ecf1;
}

.panel-title {
    padding: 0 20px 0 20px;
    font-size: 16px;
    line-height: 40px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis
}

.panel-default.panel-colorful {
    background-color: #e3e8eb;
    color: #758697
}

.panel-default .panel-heading {
    background-color: #faf9f9;
}

.panel-footer {
    background-color: rgba(249,249,249,.9);
    color: #758697;
    border-color: rgba(0, 0, 0, 0.07);
    position: relative
}

.panel-primary .panel-heading, .panel-primary .panel-footer,
    .panel-primary.panel-colorful {
    background-color: #42a5f5;
    border-color: #42a5f5;
    color: #fff
}

.panel-info .panel-heading, .panel-info .panel-footer, .panel-info.panel-colorful
    {
    background-color: #00bcd4;
    border-color: #00bcd4;
    color: #fff
}

.panel-success .panel-heading, .panel-success .panel-footer,
    .panel-success.panel-colorful {
    background-color: #8bc34a;
    border-color: #8bc34a;
    color: #fff
}

.panel-warning .panel-heading, .panel-warning .panel-footer,
    .panel-warning.panel-colorful {
    background-color: #ffa726;
    border-color: #ffa726;
    color: #fff
}

.panel-danger .panel-heading, .panel-danger .panel-footer, .panel-danger.panel-colorful
    {
    background-color: #ef5350;
    border-color: #ef5350;
    color: #fff
}

.panel-mint .panel-heading, .panel-mint .panel-footer, .panel-mint.panel-colorful
    {
    background-color: #26a69a;
    border-color: #26a69a;
    color: #fff
}

.panel-purple .panel-heading, .panel-purple .panel-footer, .panel-purple.panel-colorful
    {
    background-color: #ba68c8;
    border-color: #ba68c8;
    color: #fff
}

.panel-pink .panel-heading, .panel-pink .panel-footer, .panel-pink.panel-colorful
    {
    background-color: #ec407a;
    border-color: #ec407a;
    color: #fff
}

.panel-dark .panel-heading, .panel-dark .panel-footer, .panel-dark.panel-colorful
    {
    background-color: #263238;
    border-color: #263238;
    color: #fff
}

.panel>.panel-heading:after, .panel.panel-colorful>.panel-heading:after
    {
    content: '';
    display: block;
    position: absolute;
    height: 0;
    left: 0;
    right: 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.07)
}

.panel-colorful>.panel-heading {
    border: 0
}

.panel-default>.panel-heading:after, .panel-primary>.panel-heading:after,
    .panel-info>.panel-heading:after, .panel-success>.panel-heading:after,
    .panel-warning>.panel-heading:after, .panel-danger>.panel-heading:after,
    .panel-purple>.panel-heading:after, .panel-pink>.panel-heading:after,
    .panel-dark>.panel-heading:after {
    display: none
}

.panel-bordered-default, .panel-default.panel-bordered {
    border: 1px solid #bac5cd
}

.panel-bordered-primary, .panel-primary.panel-bordered {
    border: 1px solid #42a5f5
}

.panel-bordered-info, .panel-info.panel-bordered {
    border: 1px solid #00bcd4
}

.panel-bordered-success, .panel-success.panel-bordered {
    border: 1px solid #8bc34a
}

.panel-bordered-warning, .panel-warning.panel-bordered {
    border: 1px solid #ffa726
}

.panel-bordered-danger, .panel-danger.panel-bordered {
    border: 1px solid #ef5350
}

.panel-bordered-mint, .panel-mint.panel-bordered {
    border: 1px solid #26a69a
}

.panel-bordered-purple, .panel-purple.panel-bordered {
    border: 1px solid #ba68c8
}

.panel-bordered-pink, .panel-pink.panel-bordered {
    border: 1px solid #ec407a
}

.panel-bordered-dark, .panel-dark.panel-bordered {
    border: 1px solid #263238
}

.panel-group .panel {
    border-radius: 0;
    margin-bottom: 20px
}

.panel-group>div {
    padding-left: 0;
    padding-right: 0
}

.panel-group>div:first-child>.panel {
    border-top-left-radius: 2px;
    border-bottom-left-radius: 2px
}

.panel-group>div:last-child>.panel {
    border-top-right-radius: 2px;
    border-bottom-right-radius: 2px
}

.panel-group>div+div>.panel {
    margin-left: -1px
}

.panel-control {
    height: 100%;
    position: relative;
    float: right;
    padding: 0 15px
}

.panel-control:before {
    content: '';
    display: inline-block;
    height: 100%;
    vertical-align: middle;
    left: -1em;
    position: relative
}

.panel-control .btn {
    padding-left: 7px;
    padding-right: 7px
}

.panel-control>i, .panel-control>.badge, .panel-control>label {
    vertical-align: middle
}

.panel-control>.toggle-switch+label {
    vertical-align: baseline
}

.panel-control .nav-tabs {
    display: inline-block;
    height: 40px;
    margin-top: 10px;
    vertical-align: bottom;
    border: 0
}

.panel-control .nav-tabs>li {
    margin-top: 1px;
    margin-right: 5px;
    height: 100%
}

.panel-control .nav-tabs>li>a {
    border-radius: 0;
    margin-right: 0;
    height: 100%;
    line-height: 40px;
    border-top-left-radius: 0;
    border-top-right-radius: 0;
    padding: 0 15px
}

.panel-control .nav-tabs>li:not (.active ){
    background-color: transparent;
    opacity: .6
}

.panel-control .nav-tabs>li:not (.active ) a {
    border-bottom: 0 !important;
    color: inherit
}

.panel-control .nav-tabs>li:not (.active ) a:hover {
    background-color: rgba(0, 0, 0, 0.15);
    border-color: transparent
}

.panel-control .nav-tabs>.active>a {
    z-index: 1
}

.panel-control .progress {
    min-width: 150px;
    margin: 0;
    display: inline-block;
    vertical-align: middle
}

.panel-control .switchery {
    margin-left: 15px;
    vertical-align: middle
}

.panel-control .pager {
    margin: 0;
    display: inline-block;
    vertical-align: middle;
    border-radius: 0
}

.panel-control .pagination {
    margin: 0;
    border-radius: 0;
    vertical-align: middle
}

.panel-control .pagination>li>a, .panel-control .pagination>li>span {
    padding: 0 10px;
    border: 0;
    border-color: rgba(0, 0, 0, 0.09);
    box-shadow: none;
    height: 100%;
    line-height: 30px
}

.panel-control .pagination>li:not (.active ):not (.disabled )>a:hover {
    background-color: rgba(0, 0, 0, 0.05);
    border-color: rgba(0, 0, 0, 0.09)
}

.panel-control .pagination>.disabled>a, .panel-control .pagination>.disabled>a:hover,
    .panel-control .pagination>.disabled>a:active {
    border-color: rgba(0, 0, 0, 0.09)
}

.panel-control .pagination>li:not (.active )>a, .pagination>li>a {
    background-color: transparent;
    color: inherit
}

.panel-control .pagination>li>a:hover, .pagination>li>a:focus {
    box-shadow: none
}

.panel-control .btn, .panel-control .dropdown-toggle.btn {
    border: 0
}

.panel-control .open>.btn, .panel-control .btn.active, .panel-control .btn:active
    {
    box-shadow: none !important
}

.panel-control .btn-default {
    background-color: transparent;
    color: inherit
}

.panel-control>.btn:first-child {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0
}

.panel-control>.btn:last-child, .panel-control>.btn-group:last-child>.btn:first-child
    {
    border-bottom-right-radius: 0
}

.table-toolbar-left, .table-toolbar-right {
    text-align: center;
    padding-bottom: 10px
}

.table-toolbar-right>.form-group {
    display: inline-block;
    vertical-align: top;
    margin: 0
}


.pos-rel {
    position: relative
}

.pos-abs {
    position: absolute
}

.pos-fix {
    position: fixed
}

.pos-sta {
    position: static
}

.list-group-striped>li:nth-child(odd), .list-group-striped>a:nth-child(odd):not
    (.active ):not (.disabled ){
    background-color: rgba(0, 0, 0, 0.06)
}

.list-divider {
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    margin: 15px 0;
    height: 1px
}

.list-header {
    font-weight: 300;
    padding: 10px 15px;
    position: relative
}

.list-item-sm {
    padding: 5px 15px
}

.list-item-lg {
    padding: 15px
}

.list-item-xl {
    padding: 20px 15px
}

.box-block {
    display: block
}

.box-inline {
    display: inline-block
}

.box-vmiddle {
    display: inline-block;
    max-width: 500px;
    vertical-align: middle;
    margin-bottom: 15px
}

.box-vmiddle-wrap:before {
    content: '';
    display: inline-block;
    height: 100%;
    vertical-align: middle;
    margin-left: -15px
}

.bord-no {
    border: 0 !important
}

.bord-all {
    border: 1px solid #e9e9e9
}

.bord-top {
    border-top: 1px solid #e9e9e9
}

.bord-btm {
    border-bottom: 1px solid #e9e9e9
}

.bord-lft {
    border-left: 1px solid #e9e9e9
}

.bord-rgt {
    border-right: 1px solid #e9e9e9
}

.bord-ver {
    border-top: 1px solid #e9e9e9;
    border-bottom: 1px solid #e9e9e9
}

.bord-hor {
    border-right: 1px solid #e9e9e9;
    border-left: 1px solid #e9e9e9
}

.text-thin {
    font-weight: 300
}

.text-normal {
    font-weight: normal
}

.text-semibold {
    font-weight: 600
}

.text-bold {
    font-weight: 700
}

.text-5x, .text-4x, .text-5x, .text-2x, .text-lg, .text-sm, .text-xs {
    line-height: 1.25;
    font-size: 4em
}

.text-4x {
    font-size: 4em
}

.text-3x {
    font-size: 3em
}

.text-2x {
    font-size: 2em
}

.text-lg {
    font-size: 1.2em
}

.text-sm {
    font-size: .9em
}

.text-xs {
    font-size: .8em
}

.text-overflow {
    display: block;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis
}

.text-unit {
    font-size: 15px;
    vertical-align: top;
    line-height: 1.5em
}

.unselectable {
    cursor: default;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none
}

.unselectable ::selection {
    background-color: transparent;
    color: inherit
}

.unselectable ::-moz-selection {
    background-color: transparent;
    color: inherit
}

.text-justify {
    text-align: justify
}

.text-justify:after {
    content: '';
    display: inline-block;
    width: 100%
}


.icon-wrap {
    display: inline-block;
    padding: 10px;
    border-radius: 2px
}

.icon-wrap i {
    display: block;
    line-height: 1em;
    text-align: center;
    position: relative;
    width: 1em;
    padding-top: 1em;
    vertical-align: middle
}

.icon-wrap i:before {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0
}

.icon-wrap .icon-txt {
    display: block;
    line-height: 1em;
    text-align: center;
    position: relative;
    width: 1em;
    vertical-align: top
}

.icon-wrap-lg {
    padding: 20px
}

.icon-wrap-md {
    padding: 17px
}

.icon-wrap-sm {
    padding: 12px
}

.icon-wrap-xs {
    padding: 7px
}

.icon-circle {
    border-radius: 50%
}

.icon-fw {
    width: 1.33em;
    margin-right: 4px;
    text-align: center
}

.icon-lg {
    font-size: 1.333em;
    line-height: 1.095em;
    vertical-align: middle
}

.icon-2x {
    font-size: 2em;
    line-height: 1em
}

.icon-3x {
    font-size: 3em;
    line-height: 1em
}

.icon-4x {
    font-size: 4em;
    line-height: 1em
}

.icon-5x {
    font-size: 5em;
    line-height: 1em
}

.img-mar {
    margin: 5px
}

.img-border {
    box-shadow: 0 0 0 4px rgba(0, 0, 0, 0.1)
}

.img-border-light {
    box-shadow: 0 0 0 4px #fff
}

.img-xs {
    width: 32px;
    height: 32px
}

.img-md {
    width: 64px;
    height: 64px
}

.img-sm {
    width: 46px;
    height: 46px
}

.img-lg {
    width: 96px;
    height: 96px
}

.img-holder img {
    max-width: 100%;
    border-radius: 0
}

.mar-no {
    margin: 0 !important
}

.mar-all {
    margin: 15px
}

.mar-top {
    margin-top: 15px
}

.mar-btm {
    margin-bottom: 15px
}

.mar-lft {
    margin-left: 15px
}

.mar-rgt {
    margin-right: 15px
}

.mar-hor {
    margin-left: 15px;
    margin-right: 15px
}

.mar-ver {
    margin-top: 15px;
    margin-bottom: 15px
}

.pad-no {
    padding: 0 !important
}

.pad-all {
    padding: 15px
}

.pad-top {
    padding-top: 15px
}

.pad-btm {
    padding-bottom: 15px
}

.pad-lft {
    padding-left: 15px
}

.pad-rgt {
    padding-right: 15px
}

.pad-hor {
    padding-left: 15px;
    padding-right: 15px
}

.pad-ver {
    padding-top: 15px;
    padding-bottom: 15px
}

a.disabled-link, a.disabled-link:visited, a.disabled-link:active, a.disabled-link:hover
    {
    color: #aaa !important;
    cursor: default
}

.eq-height, .eq-height.eq-auto {
    display: table;
    table-layout: fixed;
    height: 100%;
    margin-bottom: 0;
    width: 100%
}

.eq-height.eq-auto {
    table-layout: auto
}

.eq-height .eq-box-xs {
    display: table-cell;
    height: 100%;
    vertical-align: top;
    float: none
}

.eq-height>*>.panel {
    display: table;
    table-layout: fixed;
    height: 100%;
    width: 100%
}

.eq-height [class*="eq-box"].eq-no-panel {
    padding-bottom: 0
}

.eq-min-width {
    width: 1%
}

.eq-no-panel:after {
    content: '';
    display: table;
    width: 100%;
    table-layout: fixed
}







/** ------------------- add by  -------------------  **/
/*.btn {*/
    /*min-width:75px;*/
/*}*/
.btn i.fa {
  margin-right:3px;
}
.btn-primary {
    background-color: #1D92AF;
    border-color: #198099;
}
.btn-primary:hover, .btn-primary:focus, .btn-primary:active, .btn-primary.active, .btn-primary .open .dropdown-toggle.btn-primary {
    background-color: #198099;
}

.btn-info {
    background-color: #46B0CF;
    border-color: #34a6c8;
}
.btn-info:hover, .btn-info:focus, .btn-info:active, .btn-info.active, .btn-info .open .dropdown-toggle.btn-info {
    background-color: #34a6c8;
}


.btn-success:hover, .btn-success:focus, .btn-success:active, .btn-success.active, .btn-success .open .dropdown-toggle.btn-success {
    background-color: #439643;
}
.btn-success {
    color: #fff;
    background-color: #449d44;
    border-color: #398439;
}

.btn-warning:hover, .btn-warning:focus, .btn-warning:active, .btn-warning.active, .btn-warning .open .dropdown-toggle.btn-warning {
    background-color: #e49626;
}
.btn-warning {
    color: #fff;
    background-color: #ec971f;
    border-color: #d58512;
}

    
span.k-tooltip {
    margin-top: 5px;
    line-height: 1.7em;
}
.content-container {
    position: relative;
    background-color: #ecf0f5
}

/**Dialog**/
.modal-header {
    padding: 10px;
    position: relative;
    border: 0
}
.modal-header span.modal-title{
    font-size: 14px;
    font-weight:bold;
    padding-left:5px;
}
.modal-body {
  padding: 15px;
  overflow-y:auto;
}
.modal-content.k-window-content{
    box-shadow: none;
    border-radius: 0;
    border:none;
    padding:0px;
}
.modal-body table .fa {
    font-size:44px;
}
.k-dialog-confirm td, .k-dialog-information td,.k-dialog-warning td,.k-dialog-error td{
    font-size:13px;
}
.k-dialog-information >.modal-body .fa {
    color:#5fa2dd;
}
.k-dialog-warning >.modal-body .fa {
    color:#c79121;
}
.k-dialog-error >.modal-body .fa {
    color:#a90329;
}
 
.k-dialog-confirm >.modal-body .fa {
    color:#9e9e9e;
}
/** kendo的bootstrap样式将k-widget下的所有元素设置成了content-box导致栅格布局有问题 **/
.panel * {
    box-sizing:border-box;
}
 .panel .k-input {
    box-sizing:content-box;
} 
.panel-footer {
    padding:4px 15px;
}
.panel-body {
    padding:15px;
}
.form-group label.control-label {
    /* padding-right:0px; */
}
.form-group {
    /* margin-bottom:5px; */
}


.k-grid .k-button .fa {
    font-size:14px;
    
}
.k-grid tbody .k-button {
    min-width:25px;
    padding:0px;
}
.k-grid-content {
    position: static !important;
}

.k-query-panel {
    border:1px solid #CCC;
    box-shadow:0px 3px 12px rgba(0, 0, 0,0.15);
    position: absolute;
    display:none;
    z-index: 9999;
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
    border-radius: 5px;
    background: #fff;
}
.k-query-panel .k-query-detail {
    margin:15px;
}
.k-query-panel .triangle {
    position: absolute;
    top:0px;
}
.k-query-panel .triangle div{
    position: absolute;
    width: 0px;
    height: 0px;    
    border-style: solid;
}

.k-query-panel .triangle .triangle1{
    top: -10px;
    border-width: 0 10px 10px;
    left:0px;
    border-color: transparent transparent rgb(204, 204, 204);
}

.k-query-panel .triangle .triangle2{
    top: -8px;
    left:1px;
    border-width: 0px 9px 9px;
    border-color: transparent transparent #fff;
}    
.k-lov-form{
   padding:0;
}

.k-lov-form .k-lov-input{
	padding-right:0;
}    
    
.k-valid-custom, .k-valid-custom > span{
    border-color:#d9534f !important;
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgba(217, 83, 79, 0.6) !important;
}

.k-tooltip-validation .triangle {
    position: absolute;
    top:0px;
}
.k-tooltip-validation .triangle div{
    position: absolute;
    width: 0px;
    height: 0px;    
    border-style: solid;
}

.k-tooltip-validation .triangle .triangle1{
    top: -5px;
    border-width: 0 5px 5px;
    left:0px;
    border-color: transparent transparent #ebccd1;
}
.k-window-toolbar {
    bottom:0px;
    position:fixed;
    padding:15px;
    border-top:1px solid #ebebeb;
    width:100%;
    background: #fff;
}

.k-tooltip-validation .triangle .triangle2{
    top: -4px;
    left:1px;
    border-width: 0px 4px 4px;
    border-color: transparent transparent #f2dede;
}
textarea[required], input[required], .k-state-required .k-input , input[required]:hover, textarea[required]:hover, input[required]:focus, textarea[required]:focus{
    background-color: #fff8c5;
}


input.k-invalid, textarea.k-invalid, .k-state-invalid .k-input, .k-state-invalid .k-input:hover, input.k-invalid:hover,textarea.k-invalid:hover, input.k-invalid:focus,textarea.k-invalid:focus {
    background-color: #fbe1e3;
}
.k-state-disabled input, input.k-state-disabled, textarea.k-state-disabled, .k-state-disabled input[required], input[required].k-state-disabled {
    background-color: #ededed !important;
}
/*input.k-invalid, input[required].k-invalid{*/
    /**/
/*}*/


/**
 *    表单提交遮罩
 */
.loading-message{
    display: inline-block;
    min-width: 125px;
    margin-left: -60px;
    padding: 10px;
    margin: 0 auto;
    color: #000 !important;
    font-size: 13px;
    font-weight: 400;
    text-align: center;
    vertical-align: middle;
}
.loading-message.loading-message-boxed{
    border: 1px solid #ddd;
    background-color: #eee;
    -webkit-border-radius: 2px;
    -moz-border-radius: 2px;
    -ms-border-radius: 2px;
    -o-border-radius: 2px;
    border-radius: 2px;
    -webkit-box-shadow: 0 1px 8px rgba(0, 0, 0, 0.1);
    -moz-box-shadow: 0 1px 8px rgba(0, 0, 0, 0.1);
    box-shadow: 0 1px 8px rgba(0, 0, 0, 0.1);
}
.loading-message > span {
    line-height: 20px;
    vertical-align: middle;
}