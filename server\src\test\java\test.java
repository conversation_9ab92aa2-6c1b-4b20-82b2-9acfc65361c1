//import com.alibaba.fastjson.JSONObject;
//import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
//import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
//import com.klaw.ServerApplication;
//import com.klaw.dao.caseDao.CaseMapper;
//import com.klaw.entity.caseBean.CaseRecord;
//import com.klaw.service.caseService.CaseService;
//import com.klaw.service.contractService.SgContractSealService;
//import com.klaw.utils.PageUtils;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.junit4.SpringRunner;
//
//import javax.annotation.Resource;
//import java.util.List;
//
//
//@RunWith(SpringRunner.class)
//@SpringBootTest(classes = ServerApplication.class,webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
//public class test {
//
////         @Resource
////         private CaseService caseService;
////         @Resource
////         private CaseMapper caseMapper;
////         @Resource
////         private SgContractSealService SgContractSealService;
////
////         @Test
////          public void getLearn(){
////                  QueryWrapper<CaseRecord> wrapper = new QueryWrapper<CaseRecord>();
////                  JSONObject jsonObject = new JSONObject();
////                  jsonObject.put("page","1");
////                  jsonObject.put("limit","10");
////                  jsonObject.put("total","0");
////                  Page<CaseRecord> recordPage = caseService.page(new PageUtils<CaseRecord>(jsonObject), wrapper);
////                  System.out.printf("Page:", recordPage);
////                  SgContractSeal sgContractSeal = new SgContractSeal();
////                  sgContractSeal.setSealName("11");
////                  SgContractSealService.insert(sgContractSeal);
////         }
//}
