import {request} from '@/api/index'

export default {
    stop(data) {
        return request({
            url: '/sysFunction/stop',
            method: 'post',
            data
        })
    },
    end(procId) {
        return request({
            url: '/wfl/runtime/prc/end/'+ procId,
            method: 'post'
        })
    },
    move(data) {
        return request({
            url: '/process/move',
            method: 'post',
            data
        })
    },
    flowMonitor(data) {
        return request({
          url: '/process/flowMonitor',
          method: 'post',
          data
        })
    },
    /*** 发送OA待办，参数如下：
    * code --审批完成后回调的code
    * processInstanceId --流程实例ID
    * businessKey --业务数据ID
    * taskId --任务ID
    * title --业务表单中的标题
    * functionName --功能名称
    * functionCode --业务功能编码
    * */
    sendOATask(data) {
        return request({
            url: '/process/sendOATask',
            method: 'post',
            data
        })
    },
    /**
     * 未读更新已读，参数如下：
     * processInstanceId --流程实例ID
     * taskId --任务ID
     * title --业务表单中的标题
     * functionName --功能名称
     */
    finishOATask(data) {
        return request({
            url: '/process/finishOATask',
            method: 'post',
            data
        })
    },
    queryProcess(procId) {
        return request({
          url: '/wfl/monitor/instance/'+ procId,
          method: 'get'
        })
    },
    queryProcessOpinion(data) {
        return request({
            url: '/process/queryProcessOpinion',
            method: 'post',
            data
        })
    },
    queryRevenueSetting(data) {
        return request({
            url: '/process/queryRevenueSetting',
            method: 'post',
            data
        })
    },
}