<template>
    <el-dialog title="模板信息" width="70%" :visible.sync="dialogVisible">
        <div class="filter-container">
            <el-input v-model="tableQuery.fuzzyValue" style="width: 200px;" placeholder="请输入内容" clearable
                      @keyup.enter.native="refreshData" @clear="refreshData">
                <el-button slot="append" icon="el-icon-search" @click="refreshData"/>
            </el-input>
        </div>
        <el-table
                v-loading="tableLoading"
                size="mini"
                :data="tableData"
                tooltip-effect="dark"
                style="width: 100%"
                border
                height="455"
                highlight-current-row
                @current-change="handleCurrentChange"
                @selection-change="handleSelectionChange"
        >
            <el-table-column v-if="isMultiple" type="selection" width="50" align="center"/>
            <el-table-column type="index" width="50" label="序号" align="center"/>
            <el-table-column prop="caseName" show-overflow-tooltip label="事项名称" min-width="300"/>
            <el-table-column prop="caseCode" show-overflow-tooltip label="事项编号" width="150"/>
            <el-table-column prop="causeOfIn" show-overflow-tooltip label="案件类型" min-width="150"/>

            <el-table-column prop="createTime" show-overflow-tooltip label="经办时间" width="100">
                <template slot-scope="scope">
                    <!--<i class="el-icon-time" />-->
                    <span>{{ scope.row.createTime | parseTime('{y}-{m}-{d}') }}</span>
                </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip prop="createOgnName" label="经办单位" min-width="100"/>
            <el-table-column show-overflow-tooltip prop="createPsnName" label="经办人员" width="100"/>

        </el-table>
        <div slot="footer" class="dialog-footer">
            <pagination
                    v-show="tableQuery.total>0"
                    :total="tableQuery.total"
                    :page.sync="tableQuery.page"
                    :limit.sync="tableQuery.limit"
                    style="float:left;padding:0px 16px;"
                    @pagination="refreshData"
            />
            <el-button type="primary" @click="sure_">确定</el-button>
            <el-button @click="cancel_">取消</el-button>
        </div>
    </el-dialog>
</template>

<script>
    // 组件
    import pagination from '@/view/components/Pagination/PaginationIndex'
    // 接口api
    // import caseProsecutionApi from '@/api/case/caseProsecution'
    import ComplianceRisksApi from '@/api/case/ComplianceRisks'
    import taskApi from '@/api/_system/task'

    import {mapGetters} from 'vuex'

    export default {
        name: 'PrepositionDialog',
        components: {pagination},
        computed: {
            ...mapGetters([
                'orgContext'
            ])
        },
        props: {
            visible: {
                type: Boolean,
                default: false
            },
            isMultiple: {
                type: Boolean,
                default: true
            },
            isCache: {
                type: Boolean,
                default: true
            },
            type: {
                type: String,
                default: null
            }
        },
        data() {
            return {
                tableLoading: true,
                dialogVisible: this.visible,
                tableData: [],
                tableQuery: {
                    page: 1,
                    limit: 10,
                    total: 0,
                    prosecutionType: this.type,
                    functionId: null,
                    orgId: null,
                    fuzzyValue: null
                },
                multipleSelection: [],
                singleSelection: null
            }
        },
        watch: {
            dialogVisible(val) {
                this.$emit('update:visible', val)
            },
            visible(val) {
                this.dialogVisible = val
                if (!this.isCache && val) {
                    this.multipleSelection = []
                    this.singleSelection = null
                }
            }
        },
        created() {
            taskApi.selectFunctionId({functionCode: 'case_prosecution_main'}).then(res=>{
                const functionId = res.data.data[0].ID
                this.tableQuery.functionId = functionId
                this.refreshData()
            })

        },
        methods: {
            refreshData() {
                this.tableQuery.orgId = this.orgContext.currentOrgId
                ComplianceRisksApi.queryDataForDialog(this.tableQuery).then(response => {
                    var rows = response.data.data.records
                    this.tableData = rows
                    this.tableQuery.total = response.data.data.total
                    this.tableLoading = false
                }).catch({})
            },
            handleSelectionChange(val) {
                this.multipleSelection = val
            },
            handleCurrentChange(val) {
                this.singleSelection = val
            },
            sure_() {
                this.dialogVisible = false
                this.$emit('onSure', this.isMultiple ? this.multipleSelection : this.singleSelection)
            },
            cancel_() {
                this.dialogVisible = false
                this.$emit('onCancel')
            }
        }
    }
</script>

<style scoped>

</style>
