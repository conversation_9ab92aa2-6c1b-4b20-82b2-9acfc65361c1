
c348fc8957115cf57704ae4d702afc68d6980a44	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.231.1754018536329.js\",\"contentHash\":\"823c591d244cc0f590d228bb715bb6cc\"}","integrity":"sha512-Sv6QGf3Ps/5GJfWtsuD4zYh4Qs3WkQolJoqfo7Neti9LxjE5tzoiqaB8vy/yMDOrQxOf0d7ztAzpyvHc8D1tCQ==","time":1754018575994,"size":151202}