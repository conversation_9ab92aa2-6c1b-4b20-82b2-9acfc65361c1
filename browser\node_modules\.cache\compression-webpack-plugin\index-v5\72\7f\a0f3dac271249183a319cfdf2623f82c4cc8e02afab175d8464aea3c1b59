
064eea6301587b51b198dd2cecd5c47576da8acc	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.49.1754018536329.js\",\"contentHash\":\"5a4cc2214b6a23b4b27102e1c4a7cde4\"}","integrity":"sha512-ZrDeP3acdymxPoJCUUN42ITIfSI4hkUXaid5FKCk8z0MiYUd/4fBzIZi/TZsOsk5oUYVfZ9DX83LW4HvTgKWVQ==","time":1754018575958,"size":109779}