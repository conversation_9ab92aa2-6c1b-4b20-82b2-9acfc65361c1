
1ab310deca073de8230489625b0b9b9643e7266f	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.353.1754018536329.js\",\"contentHash\":\"5b65eb0b82646c509b71b80346b3f69d\"}","integrity":"sha512-T1DLyIH9OYpYzZGm9V0eKU4UFhltBs1p9zQpYKTf32niC86b0iu8VSRV8/SZSleeGeB/IGStYLd90XqF4nl+Mg==","time":1754018575975,"size":98132}