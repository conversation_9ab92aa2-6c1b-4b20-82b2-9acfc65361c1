
c58509f9acfb3faf28fd3093358c10dfa9a8a4ba	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.340.1754018536329.js\",\"contentHash\":\"222c4e89aebb3adfd611b115920ba2f0\"}","integrity":"sha512-Jnr+9G4joCVqZiXtMy5odj5Z/YTRPEI68WpUr7t2JvF/Rig40pVsz9WEeBS3mlNDbtXqfDaBV+0fcWql8wZR1g==","time":1754018575975,"size":101823}