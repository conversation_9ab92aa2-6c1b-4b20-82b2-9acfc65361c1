import { request } from '@/api/index';

export default {
	queryAll(data) {
		return request({
			url: '/authorization/queryAll',
			method: 'post',
			data,
		});
	},
	queryMatterAll(data) {
		return request({
			url: '/authorization/queryMatterAll',
			method: 'post',
			data,
		});
	},
	query(data) {
		return request({
			url: '/authorization/query',
			method: 'post',
			data,
		});
	},
	queryDialog(data) {
		return request({
			url: '/authorization/queryDialog',
			method: 'post',
			data,
		});
	},
	queryTurnoverDialog(data) {
		return request({
			url: '/authorization/queryTurnoverDialog',
			method: 'post',
			data,
		});
	},
	queryId(data) {
		return request({
			url: '/authorization/queryById',
			method: 'post',
			data,
		});
	},
	updateStatus(data) {
		return request({
			url: '/authorization/updateStatus',
			method: 'post',
			data,
		});
	},
	save(data) {
		return request({
			url: '/authorization/save',
			method: 'post',
			data,
		});
	},
	createPdf(data) {
		return request({
			url: '/authorization/createPdf',
			method: 'post',
			data,
		});
	},
	createPdfAut(data) {
		return request({
			url: '/authorization/createPdfAut',
			method: 'post',
			data,
		});
	},
	distinct(data) {
		return request({
			url: '/authorization/distinct',
			method: 'post',
			data,
		});
	},
	queryName(data) {
		return request({
			url: '/authorization/queryName',
			method: 'post',
			data,
		});
	},
	queryAllGroupBy(data) {
		return request({
			url: '/authorization/queryAllGroupBy',
			method: 'post',
			data,
		});
	},
	queryAllGroupBy1(data) {
		return request({
			url: '/authorization/queryAllGroupBy1',
			method: 'post',
			data,
		});
	},
	delete(data) {
		return request({
			url: '/authorization/delete',
			method: 'post',
			data,
		});
	},
	setParam(data) {
		return request({
			url: '/authorization/setParam',
			method: 'post',
			data,
		});
	},
	queryAuthorizationByIndex(data) {
		return request({
			url: '/authorization/queryAuthorizationByIndex',
			method: 'post',
			data,
		});
	},
	selectAuthorization(data) {
		return request({
			url: '/authorization/selectAuthorization',
			method: 'post',
			data,
		});
	},
    selectAutMore(data) {
        return request({
            url: '/authorization/selectAutMore',
            method: 'post',
            data,
        });
    },
	queryAuthorization(data) {
		return request({
			url: '/authorization/queryAuthorization',
			method: 'post',
			data,
		});
	},
	queryAuthorizationStopDialog(data) {
		return request({
			url: '/authorization/queryAuthorizationStopDialog',
			method: 'post',
			data,
		});
	},
	exportAutExcel(data) {
		return request.post('/authorization/exportAut', data, { responseType: 'blob' });
	},
    querySimpleById(data) {
        return request({
            url: '/authorization/querySimpleById',
            method: 'post',
            data
        })
    },
	exportContractLedger(data) {
		return request({
			url: '/authorization/exportContractLedger',
			method: 'post',
			responseType: 'blob',
			data
		})
	},
};
