
e423426cfbc1f300bed72af7a03db8750f5df86b	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.256.1754018536329.js\",\"contentHash\":\"481ed853bc7a2de4639d9cd8cf1e1ae0\"}","integrity":"sha512-gpyAzKR1n0uiNPIVajHZJv1B8Hj5zAyew3VnUxcBlPCVTnIi0/Es1peWmxZey75sc+9TymesyN1dEQ6eqHH7eg==","time":1754018576064,"size":212330}