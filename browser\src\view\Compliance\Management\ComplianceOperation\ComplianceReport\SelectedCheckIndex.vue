<template>
    <el-container direction="vertical" class="container-manage-sg">
      <el-header>
        <el-card>
          <div>
            <el-input v-model="tableQuery.fuzzyValue" class="filter_input" placeholder="检索字段问责主题"
                      clearable @keyup.enter.native="refreshData" @clear="refreshData">
              <el-popover slot="prepend" placement="bottom-start" width="1000" trigger="click">
                <el-form ref="queryForm" label-width="100px" size="mini">
  
                  <el-row>
                    <el-col :span="12">
                      <el-form-item label="问责主题">
                        <el-input v-model="tableQuery.accountabilitySubject" clearable placeholder="请输入..."/>
                      </el-form-item>
                    </el-col>
  
                  </el-row>
                  <el-button-group style="float: right;">
                    <el-button type="primary" size="mini" icon="el-icon-search" @click="search_">搜索</el-button>
                    <el-button type="primary" size="mini" icon="el-icon-refresh-left" @click="empty_">重置</el-button>
                  </el-button-group>
  
                </el-form>
  
                <el-button slot="reference" size="small" type="primary">高级检索</el-button>
              </el-popover>
  
              <el-button slot="append" icon="el-icon-search" @click="search_"/>
            </el-input>
  
          </div>
        </el-card>
      </el-header>
      <el-main>
        <SimpleBoardIndex :title="'选定检查小组列表'" :isButton="true" @addBtn="add_">
          <el-table
              ref="table"
              v-loading="tableLoading"
              :data="tableData"
              size="mini"
              border
              :height="table_height"
              stripe
              fit
              highlight-current-row
              :show-overflow-tooltip="true"
              row-key="id"
              style="table-layout: fixed;width: 100%;"
              @sort-change="tableSort"
              @row-dblclick="rowDblclick"
          >
  
            <el-table-column type="index" width="50" label="序号" align="center"/>
            <el-table-column  prop="a"
                             show-overflow-tooltip label="检查分类" min-width="150"/>
            <el-table-column  prop="b"
                             show-overflow-tooltip label="检查名称" min-width="130"/>
            <el-table-column  prop="c"
                             show-overflow-tooltip label="是否需要整改" min-width="100" sortable="custom"/>
  
            <el-table-column  prop="d"
                             show-overflow-tooltip label="被检查单位" min-width="100" sortable="custom"/>
            <el-table-column  prop="e"
                             show-overflow-tooltip label="计划开始检查时间" min-width="100" sortable="custom"/>
            <el-table-column  prop="f"
                             show-overflow-tooltip label="计划结束检查时间" min-width="100" sortable="custom"/>
            <el-table-column  prop="g"
                             show-overflow-tooltip label="承办部门" min-width="100" sortable="custom"/>
            <el-table-column  prop="h"
                             show-overflow-tooltip label="检查小组" min-width="100" sortable="custom"/>
            <el-table-column  prop="i"
                             show-overflow-tooltip label="整改状态" min-width="100" sortable="custom"/>
            <el-table-column  prop="j"
                             show-overflow-tooltip label="当前处理人" min-width="100" sortable="custom"/>
            <el-table-column  prop="k"
                             show-overflow-tooltip label="任务状态" min-width="100" sortable="custom"/>
            <el-table-column  prop="h"
                             show-overflow-tooltip label="状态" min-width="100" sortable="custom"/>
            <el-table-column label="操作" align="center" width="150" fixed="right">
              <template slot-scope="scope">
                <el-button type="text" v-if="isEdit(scope.row)" @click="edit_(scope.$index,scope.row)">编辑</el-button>
                <el-button type="text" @click="view_(scope.$index,scope.row)">查看</el-button>
                <el-button type="text" v-if="isDelete(scope.row)" @click="delete_(scope.$index,scope.row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </SimpleBoardIndex>
  
      </el-main>
      <el-footer>
        <!--分页工具栏-->
        <pagination :total="tableQuery.total" :page.sync="tableQuery.page" :limit.sync="tableQuery.limit"
                    @pagination="refreshData"/>
      </el-footer>
  
    </el-container>
  </template>
  
  <script>
  // 组件
  import pagination from '@/view/components/Pagination/PaginationIndex'
  import TableTools from '@/view/components/TableTools/index'
  import SimpleBoardIndex from "@/view/components/SimpleBoard/SimpleBoardIndex";
  // vuex状态值
  import {mapGetters} from 'vuex'
  
  // 接口api
  import taskApi from '@/api/_system/task'
  import ComplianceAccountabilityApi from "../../../../../api/risk/ComplianceAccountability";
  
  export default {
    name: 'SelectedCheckIndex',
    inject: ["layout"],
    components: {pagination, TableTools, SimpleBoardIndex},
    data() {
      return {
        tableQuery: {
          page: 1, // 当前页码
          limit: 10, // 每页显示的记录数
          total: 0, // 总记录数
          accountabilitySubject: null, // 问责主题
          responsibleParty:null,//责任对象
          accountabilityTime:null,//问责时间
          violationMatter:null,//违规事项
          reportingUnit: null, // 上报单位
          reportingDateMin: null, // 上报日期最小值
          reportingDateMax: null, // 上报日期最大值
          currentNode: null, // 当前节点
          reviewStatus: null, // 审核状态
          projectOverview: null, // 项目概述
          riskAssessmentReport: null, // 风险评估报告
          relevantMaterials: null, // 相关资料
          reportingOrganization: null, // 上报组织
          participants: null, // 参会人员
          reviewOpinion: null, // 评审意见
          relevantAttachments: null, // 相关附件
          participatingDepartment: null, // 参与部门
          participatingPersonnel: null, // 参与人员
          attachments: null, // 附件
          reviewTimeMin: null, // 评审时间最小值
          reviewTimeMax: null, // 评审时间最大值
          createOgnId: null, // 数据创建单位ID
          createOgnName: null, // 数据创建单位
          createDeptId: null, // 数据创建部门ID
          createDeptName: null, // 数据创建部门
          createGroupId: null, // 数据创建班组id
          createGroupName: null, // 数据创建班组
          createPsnId: null, // 数据创建人id
          createPsnName: null, // 数据创建人
          createOrgId: null, // 数据创建组织id
          createOrgName: null, // 数据创建组织
          createPsnFullId: null, // 数据创建人ID全路径
          createPsnFullName: null, // 数据创建人全路径
          createTimeMin: null, // 数据创建时间最小值
          createTimeMax: null, // 数据创建时间最大值
          updateTimeMin: null, // 数据更新时间最小值
          updateTimeMax: null, // 数据更新时间最大值
          dataState: null, // 数据状态
          dataStateCode: null, // 数据状态编码
          fuzzyValue: null, // 模糊搜索值
          orderCol: null, // 排序字段
          orderColValue: false, // 排序字段是否升序
          functionCode: null, // 功能代码
          orgId: null, // 组织ID
          managementUnit: null, // 管理单位
          unitType: null, // 单位类型
          isQuery: false // 是否查询
        },
        tableQuery1: {
      'projectName': '项目名称',
      'reviewStatus':'审核状态',
        },
        table_height: '100%',
        tableData: [],
        tableLoading: false,
        ss: {
          data: this.tableData,
          tableColumns: [
            {key: 'accountabilitySubject', label: '问责主题', visible: true},
            {key: 'responsibleParty', label: '责任对象', visible: true},
            {key: 'accountabilityTime', label: '问责时间', visible: true},
            {key: 'violationMatter', label: '违规事项', visible: true},
            {key: 'createTime', label: '经办时间', visible: true, isTime: true},
            {key: 'createPsnName', label: '上报人', visible: true},
            // {key: 'createDeptName', label: '上报单位', visible: true},
            {key: 'createOgnName', label: '上报单位', visible: true},
            {key: 'createTime', label: '上报日期', visible: true ,isTime: true},
            {key: 'currentNode', label: '当前节点', visible: true},
            {key: 'dataState', label: '审核状态', visible: true}
          ]
        }
      }
    },
    computed: {
      ...mapGetters([
        'orgContext', 'currentFunctionId'
      ])
    },
    activated() {
      // 长连接页面第二次激活的时候,不会走created方法,会走此方法
      this.refreshData()
    },
    created() {
      this.refreshData()
    },
  
    mounted() {
      this.$nextTick(function () {
        this.table_height = window.innerHeight - this.$refs.table.$el.offsetTop - 84 - 40
  
        // 监听窗口大小变化
        const self = this
        window.onresize = function () {
          self.table_height = window.innerHeight - self.$refs.table.$el.offsetTop - 84 - 40
        }
      })
    },
    methods: {
      isEdit(row) {
        return row.dataStateCode === this.utils.dataState_BPM.SAVE.code || row.dataStateCode === this.utils.dataState_BPM.BACK.code
      },
      isDelete(row) {
        return row.dataStateCode === this.utils.dataState_BPM.SAVE.code
      },
      // 刷新数据
      refreshData()
      {
        // 赋值当前人组织全路径
        this.tableQuery.functionCode = this.currentFunctionId.functionCode
        this.tableQuery.orgId = this.orgContext.currentOrgId
        this.tableQuery.currentPsnFullId = this.orgContext.currentPsnFullId
        // 主要用于删除,当前页只有一条数据的时候,删除需要回到上一页
        if (this.tableData.length === 0 && this.tableQuery.page > 1) {
          this.tableQuery.page--
        }
        ComplianceAccountabilityApi.query(this.tableQuery).then(response => {
          let rows = response.data.data.records
          this.tableData = rows
          this.ss.data = rows
          this.tableQuery.total = response.data.data.total
          this.tableLoading = false
        }).catch({})
      },
      add_() {
        const tabId = this.utils.createUUID();
        //新页面添加
        this.layout.openNewTab(
            //此时页面没有，路径需要修改下面同理
            "合规问责",
            "hgwz_main_detail",
            "hgwz_main_detail",
            tabId,
            {
              functionId: "hgwz_main_detail,"+tabId,
              ...this.utils.routeState.NEW(tabId)
            }
        );
      },
      // 编辑
      edit_(index, row)
      {
        const tabId = this.utils.createUUID();
        this.layout.openNewTab(
            "合规问责",
            "hgwz_main_detail",
            "hgwz_main_detail",
            row.id,
            {
              functionId: "hgwz_main_detail," + row.id,
              ...this.utils.routeState.EDIT(row.id),
              view: 'old',
            }
        );
      },
      // 删除
      delete_(index, row) {
        this.$confirm('此操作将永久删除该数据及相关数据, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          new Promise((resolve, reject) => {
            ComplianceAccountabilityApi.deletebyid({
              id: row.id
            }).then((response) => {
              resolve(response)
            })
          }).then(value => {
            this.tableData.splice(index, 1)
            this.$message.success('删除成功!')
          })
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
      },
      // 查看
      view_(index, row) {
        if (row.dataStateCode === this.utils.dataState_BPM.SAVE.code)
        {
          const tabId = this.utils.createUUID();
          this.layout.openNewTab(
              "合规问责",
              "hgwz_main_detail",
              "hgwz_main_detail",
              tabId,
              {
                functionId: "hgwz_main_detail," + tabId,
                ...this.utils.routeState.VIEW(row.id)
              }
          )
        }
        else
        {
          taskApi.selectTaskId({businessKey: row.id,isView: 'true'}).then(res=>{
            const functionId = res.data.data[0].ID
            const tabId = this.utils.createUUID()
            this.layout.openNewTab("起诉审批信息",
                "design_page",
                "design_page",
                tabId,
                {
                  processInstanceId: res.data.data[0].PID,//流程实例
                  taskId: res.data.data[0].ID,//任务ID
                  businessKey: row.id, //业务数据ID
                  functionId:functionId, //不传也可以，只有首环节需要传，实例启动就可以不传了
                  entranceType: "FLOWABLE",
                  type: "haveDealt",
                  channel: 'business', //查看渠道，默认就传这个值即可，表示是从业务功能传过来的，首环节提交后过滤掉撤回按钮
                  view: 'new',
                }
            )
          })
        }
      },
      rowDblclick(row, column, event) {
        if (row.dataStateCode === this.utils.dataState_BPM.SAVE.code)
        {
          const tabId = this.utils.createUUID();
          this.layout.openNewTab(
              "合规问责",
              "hgwz_main_detail",
              "hgwz_main_detail",
              tabId,
              {
                functionId: "contract_approval_main_detail," + tabId,
                ...this.utils.routeState.VIEW(row.id)
              }
          )
        }
        else
        {
          taskApi.selectTaskId({businessKey: row.id,isView: 'true'}).then(res=>{
            const functionId = res.data.data[0].ID
            const tabId = this.utils.createUUID()
            this.layout.openNewTab("起诉审批信息",
                "design_page",
                "design_page",
                tabId,
                {
                  processInstanceId: res.data.data[0].PID,//流程实例
                  taskId: res.data.data[0].ID,//任务ID
                  businessKey: row.id, //业务数据ID
                  functionId:functionId, //不传也可以，只有首环节需要传，实例启动就可以不传了
                  entranceType: "FLOWABLE",
                  type: "haveDealt",
                  channel: 'business', //查看渠道，默认就传这个值即可，表示是从业务功能传过来的，首环节提交后过滤掉撤回按钮
                  view: 'new',
                }
            )
          })
        }
      },
      tableSort(column, prop, order) {
        this.tableQuery.sortName = column.prop
        this.tableQuery.order = column.order === "ascending"
        this.refreshData()
      },
      // 点击搜索按钮事件,回到第一页,重新刷新数据
      search_: function () {
        this.tableQuery.page = 1
        this.refreshData()
      },
      // 点击清空按钮事件,清空查询条件属性,回到第一页,重新刷新数据
      empty_() {
        this.tableQuery = {
          page: 1,
          limit: 10,
          total: 0,
          prosecutionType: '起诉',
          isQuery: false,
          caseName: null,
          createTimeMin: null,
          createTimeMax: null,
          caseStartTimeMin: null,
          caseStartTimeMax: null,
          dataStateCode: null,
          currentPsnFullId: null,
          fuzzyValue: null,
          orderCol: null,
          orderColValue: false,
          functionCode: null,
          orgId: null,
          managementUnit: null,
          unitType: null,
        }
        this.refreshData()
      },
      // 点击刷新按钮事件
      refresh_() {
        this.tableQuery.sortName = null
        this.tableQuery.order = null
        this.empty_()
      },
    }
  }
  </script>
  
  <style scoped>
  .el-table__fixed-body-wrapper {
    top: 50px !important;
  }
  </style>
  