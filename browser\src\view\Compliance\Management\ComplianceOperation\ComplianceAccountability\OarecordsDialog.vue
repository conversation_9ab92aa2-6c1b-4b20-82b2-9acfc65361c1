<template>
  <div>
  <el-dialog title="流程审批记录" width="70%" :visible.sync="dialogVisible">
    <div class="block">
      <div class="radio">
        排序：
        <el-radio-group v-model="reverse">
          <el-radio :label="true">倒序</el-radio>
          <el-radio :label="false">正序</el-radio>
        </el-radio-group>
      </div>

      <el-timeline :reverse="reverse">
        <el-timeline-item v-for="(item,index) in tableData" :key="index" :timestamp="item.fdCreateTime"  placement="top">
          <el-card style="width: 99%">
            <p>
              <el-tag>    审批时间:</el-tag><tag style="white-space:pre-wrap;color: black;">{{item.fdCreateTime}}</tag>
            </p>
            <p>
              <el-tag>    节点名称:</el-tag><tag style="white-space:pre-wrap;color: black;">{{item.fdFactNodeName}}</tag>
            </p>
            <p>
              <el-tag>    审批操作:</el-tag><tag style="white-space:pre-wrap;color: black;">{{item.fdActionName}}</tag>
            </p>
            <p>
              <el-tag>    审批信息:</el-tag><tag style="white-space:pre-wrap;color: black;">{{item.fdAcionInfo}}</tag>
            </p>
            <p>
              <el-tag>    处理意见:</el-tag><tag style="white-space:pre-wrap;color: black;">{{item.fdAuditNote}}</tag>
            </p>
            <p>
              <el-tag>操作者名称:</el-tag><tag style="white-space:pre-wrap;color: black;">{{item.fdHandlerName}}</tag>
            </p>

          </el-card>
        </el-timeline-item>
      </el-timeline>
    </div>
  </el-dialog>
  </div>
</template>

<script>
// 组件

// 接口api
// import systaskApi from '@/api/systask'

import { mapGetters } from 'vuex'

export default {
  name: 'oarecordsDialog',
  components: {},
  computed: {
    ...mapGetters([
      'orgContext'
    ])
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    isCache: {
      type: Boolean,
      default: true
    },
    type: {
      type: String,
      default: null
    },
    dataid:{
      type: String,
      default: null
    }
  },
  data() {
    return {
      reverse: true,
      tableLoading: true,
      dialogVisible: this.visible,
      tableData: [],
    }
  },
  watch: {
    dialogVisible(val) {
      this.$emit('update:visible', val)
    },
    visible(val) {
      this.dialogVisible = val
      if (!this.isCache && val) {
        this.multipleSelection = []
        this.singleSelection = null
      }
    },
    dataid(val){
      this.refreshData()
    }
  },
  created() {
    this.refreshData()
  },
  methods: {
    refreshData() {
      if(this.dataid != null && this.dataid != ""){
        this.tableLoading = true
        // systaskApi.queryOArecordForDialog({id:this.dataid}).then(response => {
        //   this.tableData = response.data.list;
        //   this.tableLoading = false
        // }).catch({})
      }

    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    handleCurrentChange(val) {
      this.singleSelection = val
    },
    cancel_() {
      this.dialogVisible = false
      this.$emit('onCancel')
    }
  }
}
</script>

<style scoped>

</style>
