<template xmlns:v-slot="http://www.w3.org/1999/XSL/Transform">

  <div style="margin-top: 10px" v-if="view === 'old'">
    <!--基础信息表单块-->
    <div v-if="dataState !== 'view'">
      <div style="padding-left: 10px;margin-bottom: 20px;font-size: 15px;color: red;font-weight: bolder">

      </div>
      <div style="margin: 10px">
        <span style="font-weight: bold;font-size: 16px;color: #5A5A5F;">基本信息</span>
      </div>
      <el-row style="margin-top: 10px">
        <el-col :span="8">
          <el-form-item label="合规领域" prop="complianceArea" required>
            <el-select v-model="mainData.complianceArea" clearable placeholder="请选择" style="width: 100%"
              @change="complianceAreaChange">
              <el-option v-for="item in complianceAreaData" :key="item.id" :label="item.dicName" :value="item.id" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="文件编号" prop="fileNumber" required>
            <el-input v-if="!isView" v-model="mainData.fileNumber" maxlength="100" show-word-limit placeholder="请输入..."
              clearable />
            <span v-else class="viewSpan">{{ mainData.fileNumber }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="文件性质" prop="fileNature" required>
            <el-select v-model="mainData.fileNature" clearable placeholder="请选择" style="width: 100%"
              @change="fileNatureChange">
              <el-option v-for="item in fileNatureData" :key="item.id" :label="item.dicName" :value="item.id" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="政策法规名称" prop="regulationName">
            <el-input v-if="!isView" v-model="mainData.regulationName" maxlength="100" show-word-limit
              placeholder="请输入..." clearable />
            <span v-else class="viewSpan">{{ mainData.regulationName }}</span>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item label="实施时间" required>
            <el-date-picker v-model="mainData.effectiveDate" type="datetime" placeholder="选择具体时间" clearable
              style="width: 100%;"> </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="颁布单位" required>
            <el-input v-model="mainData.issuingUnit" placeholder="请选择" class="input-with-select" readonly>
              <el-button slot="append" icon="el-icon-search" @click="showOrgTreeDialog1" />
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="适用单位" required>
            <el-input v-model="mainData.applicableUnit" placeholder="请选择" class="input-with-select" readonly>
              <el-button slot="append" icon="el-icon-search" @click="showOrgTreeDialog2" />
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="附件材料">
              <UploadDoc :files.sync="mainData.uploadAttachment" doc-path="/case" :disabled="isView" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-row>
      <!-- el-dialog 组件 -->
      <el-dialog :close-on-click-modal="false" title="选择单位/部门" :visible.sync="dialogVisible1" width="50%">
        <div class="el-dialog-div">
          <orgTree :accordion="false" :is-checked-user="false" :show-user="false" :is-check="true"
            :checked-data.sync="bbcheckedData" :is-not-cascade="true" :is-filter="true" />
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button icon="" class="negative-btn" @click="cancel">取消</el-button>
          <el-button type="primary" icon="" class="active-btn" @click="choiceDeptSure1">确定</el-button>
        </span>
      </el-dialog>
      <!-- el-dialog 组件 -->
      <el-dialog :close-on-click-modal="false" title="选择单位/部门" :visible.sync="dialogVisible2" width="50%">
        <div class="el-dialog-div">
          <orgTree :accordion="false" :is-checked-user="false" :show-user="false" :is-check="true"
            :checked-data.sync="sycheckedData" :is-not-cascade="true" :is-filter="true" />
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button icon="" class="negative-btn" @click="cancel">取消</el-button>
          <el-button type="primary" icon="" class="active-btn" @click="choiceDeptSure2">确定</el-button>
        </span>
      </el-dialog>
    </div>

    <!-- 查看 -->
    <div v-else>
      <SimpleBoardTitle title="基本信息">
        <table class="table_content">
          <tbody>
            <tr>
              <th colspan="2" class="th_label">合规领域</th>
              <td colspan="6" class="td_value">{{ mainData.complianceArea }}</td>
              <th colspan="2" class="th_label">文件编号</th>
              <td colspan="6" class="td_value">{{ mainData.fileNumber }}</td>
              <th colspan="2" class="th_label">文件性质</th>
              <td colspan="6" class="td_value">{{ mainData.fileNature }}</td>
            </tr>
            <tr>
              <th colspan="2" class="th_label">政策法规名称</th>
              <td colspan="22" class="td_value">{{ mainData.regulationName }}</td>
            </tr>
            <tr>
              <th colspan="2" class="th_label">实施时间</th>
              <td colspan="6" class="td_value">{{ mainData.effectiveDate }}</td>
              <th colspan="2" class="th_label">颁布单位</th>
              <td colspan="6" class="td_value">{{ mainData.issuingUnit }}</td>
              <th colspan="2" class="th_label">适用单位</th>
              <td colspan="6" class="td_value">{{ mainData.applicableUnit }}</td>
            </tr>
            <tr>
              <th colspan="2" class="th_label">附件材料</th>
              <td colspan="22" class="td_value">
                <UploadDoc :files.sync="mainData.uploadAttachment" doc-path="/case" :disabled="isView" />
              </td>
            </tr>
          </tbody>

        </table>

      </SimpleBoardTitle>
    </div>

  </div>
</template>

<script>
import AreaSelect from '@/view/components/AreaSelect/AreaSelect'
import OrgSingleDialogSelect from '@/view/components/OrgSingleDialogSelect/index.vue'
import UploadDoc from '@/view/components/UploadDoc/UploadDoc.vue'
import orgTree from '@/view/components/OrgTree/OrgTree'
import SimpleBoardTitle from "@/view/components/SimpleBoard/SimpleBoardTitle"
import SimpleBoardTitleApproval from "@/view/components/SimpleBoard/SimpleBoardTitleApproval"
import money from "@/view/components/Money/index"
import OrgLeader from '@/view/components/OrgLeader/OrgLeader'
import dictApi from '@/api/_system/dict'

export default {
  name: 'QsBaseInfo',
  components: {
    OrgSingleDialogSelect, AreaSelect, UploadDoc, orgTree, SimpleBoardTitleApproval, money,
    SimpleBoardTitle, OrgLeader
  },
  props: {
    data: {
      type: Object,
      default: function () {
        return {}
      }
    },
    dataState: {
      type: String,
      default: ''
    },
    view: {
      type: String,
      default: 'new'
    },
    create: {
      type: String,
      default: ''
    },
    authorizationData: {
      type: Object,
      default: function () {
        return {}
      }
    },
  },
  data() {
    return {
      orgDialogTitle: '组织信息',
      caseDialogVisible: false,
      mainData: this.data,
      dicTreeDialogVisible: false,
      orgTreeDialog: false,
      caseNatures: [],
      plateData: [],
      causeOfIns: [],
      applications: [],
      bbcheckedData: [],
      sycheckedData: [],
      complianceAreaData: [],//合规领域
      fileNatureData: [],//案件性质
      dialogVisible1: false,
      dialogVisible2: false,
      orgTreeDialog: false,
      orgVisible: false,
    }
  },
  computed: {
    isView: function () {
      return this.dataState === this.utils.formState.VIEW
    },
    causeOfInIds: {
      set: function (data) {
        this.mainData.causeOfInId = data.join(',')
      },
      get: function () {
        if (this.mainData.causeOfInId) {
          return this.mainData.causeOfInId.split(',')
        }
        return []
      }
    },
    isCreate: function () {
      return this.create === 'create'
    },
  },
  watch: {
    mainData: {
      handler(val, oldVal) {
        this.$emit('update:data', val)
      },
      deep: true
    },
    data(val) {
      this.mainData = Object.assign(this.mainData, val)
    },
    'mainData.involvedAmount': {
      handler(val, oldVal) {
        this.involvedAmountChange(val)
      },
      deep: true, immediate: true
    },
    'mainData.caseInterest': {
      handler(val, oldVal) {
        this.caseInterestChange(val)
      },
      deep: true, immediate: true
    }
  },
  created() {
    this.initDic()
    window.vm = this;
  },

  methods: {
    complianceAreaChange(val) {
      this.mainData.complianceArea = this.utils.getDicName(this.complianceAreaData, val)
    },
    fileNatureChange(val) {
      this.mainData.fileNature = this.utils.getDicName(this.fileNatureData, val)
    },
    initDic() {
      dictApi.showAllSelect({
        dicCode: 'AJ_YWLY'
      }).then(response => {
        this.complianceAreaData = response.data.data
      })
      dictApi.showAllSelect({
        dicCode: 'HG-WJXZ'
      }).then(response => {
        this.fileNatureData = response.data.data
      })
    },

    orgSelect(data) {
      this.mainData.currentUnitId = data.unitId
      this.mainData.currentUnit = data.name

      this.mainData.cultureCategory = data.name
    },
    showOrgTreeDialog1() {
      this.dialogVisible1 = true;
    },
    showOrgTreeDialog2() {
      this.dialogVisible2 = true;
    },
    cancel1() {
      this.dialogVisible1 = false
    },
    cancel2() {
      this.dialogVisible2 = false
    },
    choiceDeptSure1() {
      let selectedUnits = this.bbcheckedData.map(item => item.name).join(', ');
      this.mainData.issuingUnit = selectedUnits;
      this.dialogVisible1 = false;
    },
    choiceDeptSure2() {
      let selectedUnits = this.sycheckedData.map(item => item.name).join(', ');
      this.mainData.applicableUnit = selectedUnits;
      this.dialogVisible2 = false;
    }
  }
}
</script>

<style lang="scss" scoped>
.hideContent {

  .el-input__inner,
  .el-radio.is-bordered,
  .el-textarea__inner,
  .el-input__count {
    background-color: #f9e8bb;
  }
}

.money-label-width .el-form-item__label {
  width: 150px;
  /* 指定宽度 */
}
</style>
