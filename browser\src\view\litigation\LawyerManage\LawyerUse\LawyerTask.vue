<template>
	<!--律师使用登记功能子表-->
	<div>
		<SimpleBoard :title="pro_title" :has-value.sync="pro_hasValue" :has-add="pro_hasAdd" @addBtn="addBtn" @deleteData="deleteAll">
			<el-table
				v-draggable="useData"
				:data="useData"
				border
				style="width: 99%"
				:max-height="table_height"
				stripe
				size="mini"
				fit
				highlight-current-row>
				<el-table-column type="index" label="序号" align="center" />
				<el-table-column prop="lawyerName" label="律师名称" align="center" min-width="30" show-overflow-tooltip />
				<el-table-column prop="assignedName" label="交办人名称" align="center" min-width="30" show-overflow-tooltip />
				<el-table-column prop="assignedTask" label="交办任务" align="center" min-width="30" show-overflow-tooltip />
				<el-table-column prop="assignedDate" label="交办时间" align="center" min-width="30" show-overflow-tooltip>
					<template slot-scope="scope">
						<span>{{ scope.row.assignedDate | parseTime }}</span>
					</template>
				</el-table-column>
				<el-table-column prop="completedDate" label="完成时间" align="center" min-width="30" show-overflow-tooltip>
					<template slot-scope="scope">
						<span>{{ scope.row.completedDate | parseTime }}</span>
					</template>
				</el-table-column>
				<el-table-column prop="completedSituation" label="完成情况" align="center" min-width="30" show-overflow-tooltip />
				<el-table-column label="操作" width="160" align="center">
					<template slot-scope="scope">
						<el-button v-if="dataState !== 'view'" size="mini" @click="processEdit(scope.$index, scope.row)">编辑 </el-button>
						<el-button v-if="dataState !== 'view'" size="mini" type="danger" @click="processDelete(scope.$index, scope.row)">删除 </el-button>
					</template>
				</el-table-column>
			</el-table>
		</SimpleBoard>
		<el-dialog :title="recordTitle" :visible.sync="useDialogVisible" :close-on-click-modal="false" width="40%" class="myDialog">
			<!--      <el-divider />-->
			<el-form :ref="processRow" style="margin-top: 20px" :model="processRow" label-width="110px">
				<el-row>
					<el-col :span="24">
						<el-form-item prop="lawyerId" label="律师名称">
							<el-select
								v-if="processRow.state !== 'view'"
								v-model="processRow.lawyerId"
								clearable
								placeholder="请选择"
								style="width: 100%"
								@change="lawyerChange">
								<el-option v-for="item in lawyerData" :key="item.lawyerId" :label="item.lawyerName" :value="item.lawyerId">
									<span style="float: left">{{ item.lawyerName }}</span>
									<span style="float: right; color: #8492a6; font-size: 13px">{{ item.lawFirm }}</span>
								</el-option>
							</el-select>
							<span v-else class="viewSpan">{{ processRow.lawyerName }}</span>
						</el-form-item>
					</el-col>
				</el-row>
				<el-row>
					<el-col :span="24">
						<el-form-item prop="assignedTask" label="交办任务">
							<el-input
								v-if="processRow.state !== 'view'"
								v-model.trim="processRow.assignedTask"
								clearable
								placeholder="请输入..."
								:autosize="{ minRows: 3, maxRows: 6 }"
								maxlength="1000"
								type="textarea"
								show-word-limit />
							<text-span v-else class="viewSpan" :text="processRow.assignedTask" />
						</el-form-item>
					</el-col>
				</el-row>
				<el-row>
					<el-col :span="24">
						<el-form-item prop="assignedDate" label="交办时间">
							<el-date-picker
								v-if="processRow.state !== 'view'"
								v-model="processRow.assignedDate"
								clearable
								type="date"
								style="width: 100%"
								value-format="yyyy-MM-dd" />
							<span v-else class="viewSpan">{{ processRow.assignedDate | parseTime }}</span>
						</el-form-item>
					</el-col>
				</el-row>
				<el-row>
					<el-col :span="24">
						<el-form-item prop="assignedName" label="交办人">
							<el-input
								v-if="processRow.state != 'view'"
								v-model="processRow.assignedName"
								clearable
								placeholder="请选择"
								class="input-with-select"
								disabled>
								<el-button slot="append" icon="el-icon-search" @click="assignedClick" />
							</el-input>
							<span v-else class="viewSpan">{{ processRow.assignedName }}</span>
						</el-form-item>
					</el-col>
				</el-row>
				<el-row>
					<el-col :span="24">
						<el-form-item prop="completedDate" label="交办时间">
							<el-date-picker
								v-if="processRow.state !== 'view'"
								v-model="processRow.completedDate"
								clearable
								type="date"
								style="width: 100%"
								value-format="yyyy-MM-dd" />
							<span v-else class="viewSpan">{{ processRow.completedDate | parseTime }}</span>
						</el-form-item>
					</el-col>
				</el-row>

				<el-row>
					<el-col :span="24">
						<el-form-item prop="completedSituation" label="完成情况">
							<el-input
								v-if="processRow.state !== 'view'"
								v-model.trim="processRow.completedSituation"
								clearable
								placeholder="请输入..."
								:autosize="{ minRows: 3, maxRows: 6 }"
								maxlength="1000"
								type="textarea"
								show-word-limit />
							<text-span v-else class="viewSpan" :text="processRow.completedSituation" />
						</el-form-item>
					</el-col>
				</el-row>
			</el-form>
			<span slot="footer" class="dialog-footer">
				<el-button icon="" class="negative-btn" @click="cancel_">取消</el-button>
				<el-button v-if="processRow.state !== 'view'" type="primary" icon="" class="active-btn" @click="processSure">确定 </el-button>
			</span>
		</el-dialog>

		<!--法务承办人-->
		<org-single-dialog-select :dialog-visible.sync="orgVisible" title="选择法务承办人" :is-checked-user="true" @sureBtn="sureBtn" />
	</div>
</template>

<script>
	import { mapGetters } from 'vuex';
	import SimpleBoard from '@/view/components/SimpleBoard/SimpleBoardViewCase';
	import util from '@/view/utils/simpleUtils';
	import uploadDoc from '@/view/components/UploadDoc/UploadDoc';
	import { parseTime } from '@/view/utils';
	import OrgSingleDialogSelect from '@/view/components/OrgSingleDialogSelect/index.vue';

	export default {
		name: 'lawyerTask',
		computed: {
			...mapGetters(['orgContext']),
		},
		components: { SimpleBoard, uploadDoc, OrgSingleDialogSelect },
		props: {
			myUseData: {
				type: Array,
				default: function () {
					return [];
				},
			},
			dataState: {
				type: String,
				default: '',
			},
			relationId: {
				type: String,
				default: '',
			},
			lawyerList: {
				type: Array,
				default: function () {
					return [];
				},
			},
			hasValue: {
				type: Boolean,
				default: false,
			},
		},
		data() {
			return {
				pro_title: '任务事项',
				pro_hasValue: true,
				pro_hasAdd: this.dataState !== this.utils.formState.VIEW,
				editQuery: true,
				useData: [],
				lawyerData: [],
				table_height: 260,
				useDialogVisible: false,
				orgVisible: false, // 选人弹框
				recordTitle: '新增关联信息',
				tip_: '支持多种格式上传，如doc、pdf、zip等',
				docURL: '/design',
				processRow: {
					id: null,
					dataSource: null, //选聘带过来的
					lawyerName: null, //律师名称
					lawyerId: null, //律师id
					assignedId: null, //交办人id
					assignedName: null, //交办人名称
					assignedTask: null, //交办任务
					assignedDate: null, //交办时间
					isCompleted: null, //是否完成
					completedDate: null, //完成时间
					completedSituation: null, //完成情况
				},
				endDate: [
					{ value: '0', label: '是' },
					{ value: '1', label: '否' },
				],
			};
		},
		watch: {
			myUseData: {
				handler(val) {
					this.useData = val || [];
				},
				deep: true,
				immediate: true,
			},
			lawyerList: {
				handler(val) {
					this.lawyerData = val || [];
				},
				deep: true,
			},
			useData: {
				handler(val) {
					this.$emit('updateUseData', val);
				},
				deep: true,
			},
			hasValue(val) {
				this.pro_hasValue = val;
			},
			pro_hasValue(val) {
				this.$emit('update:hasValue', val);
			},
		},
		methods: {
			parseTime,
			addBtn() {
				this.useDialogVisible = true;
				this.processRow = {
					id: null,
					parentId: null,
					dataSource: null,
					lawyerName: null,
					lawyerId: null,
					assignedId: null,
					assignedName: null,
					assignedTask: null,
					assignedDate: null,
					isCompleted: null,
					completedDate: null,
					completedSituation: null,
				};
				this.processRow.id = util.createUUID();
				this.processRow.parentId = this.relationId;
				this.processRow.state = 'new';
				this.recordTitle = '新增事务信息';
			},
			// 编辑
			processEdit(index, row) {
				this.processRow = {};
				this.processRow = Object.assign({}, row);
				this.processRow.state = 'edit'; // 操作状态
				this.index = index;
				this.useDialogVisible = true;
				this.recordTitle = '编辑事务信息';
			},
			processDelete(index) {
				this.useData.splice(index, 1);
			},
			// 查看
			processView(index, row) {
				this.processRow = row;
				this.processRow.state = 'view'; // 操作状态
				this.useDialogVisible = true;
				this.recordTitle = '查看事务信息';
			},
			/* 弹框取消*/
			cancel_() {
				this.useDialogVisible = false;
			},
			cancel1_() {
				this.orgVisible = false;
			},
			// 弹框确认
			processSure() {
				if (this.processRow.state === 'edit') {
					Object.assign(this.useData[this.index], this.processRow);
				} else {
					this.useData.push(this.processRow);
				}
				this.useDialogVisible = false;
			},
			deleteAll() {
				this.useData = [];
			},
			lawyerChange(val) {
				this.processRow.lawyerName = this.getDicName(this.lawyerData, val);
			},
			getDicName: function (array, val) {
				if (val === '') {
					return null;
				} else {
					let dic = {};
					dic = array.find((item) => {
						return item.lawyerId === val;
					});
					return dic.lawyerName;
				}
			},
			// 选择被移交人框显示
			assignedClick() {
				this.orgVisible = true;
			},
			sureBtn(data) {
				this.processRow.assignedId = data.unitId;
				this.processRow.assignedName = data.name;
			},
		},
	};
</script>

<style scoped></style>
