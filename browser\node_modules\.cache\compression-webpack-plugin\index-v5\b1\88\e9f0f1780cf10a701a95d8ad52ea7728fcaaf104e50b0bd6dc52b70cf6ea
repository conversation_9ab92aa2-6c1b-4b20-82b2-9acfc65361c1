
b9a1e130857477f5af3697d0739a9f6028fe9e8b	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.197.1754018536329.js\",\"contentHash\":\"a74659b1d1c7be6e02cea2501ca26107\"}","integrity":"sha512-qod88xEMCfeBevQaRIxGp1pZSugdfuZ2v1a8jHKQC43JAO7gz9FStoVPj0YaDJACPLLX2EvlV09VbnLCklUlJg==","time":1754018576097,"size":240567}