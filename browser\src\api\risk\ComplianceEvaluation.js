import {request} from '@/api/index'

export default {

    query(data) {
        return request({
            url: '/ComplianceEvaluation/query',
            method: 'post',
            data
        })
    },

    save(data) {
        return request({
            url: '/ComplianceEvaluation/save',
            method: 'post',
            data
        })
    },

    queryById(data) {
        return request({
            url: '/ComplianceEvaluation/query_by_id',
            method: 'post',
            data: {
                id: data
            }
        })
    },
    deletebyid(data) {
        return request({
            url: '/ComplianceEvaluation/deletebyid',
            method: 'post',
            data

        })
    },

    assignCase(data){
        return request({
            url: '/prosecution/assign',
            method: 'post',
            data

        })
    },

    queryDataForDialog(data){
        return request({
            url: '/prosecution/queryDataForDialog',
            method: 'post',
            data

        })
    },
    startOAFlow(data){
        return request({
            url: '/prosecution/startOAFlow',
            method: 'post',
            data

        })
    },
    queryOArecordForDialog(data){
        return request({
            url: '/prosecution/queryOArecordForDialog',
            method: 'post',
            data

        })
    },
    setParam(data) {
        return request({
            url: '/ComplianceEvaluation/setParam',
            method: 'post',
            data
        })
    }
}

