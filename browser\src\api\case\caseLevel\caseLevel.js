import {request} from '@/api/index'
export default {
    query(data) {
        return request({
            url: '/level/query',
            method: 'post',
            data
        })
    },
    save(data) {
        return request({
            url: '/level/save',
            method: 'post',
            data
        })
    },
    delete(data) {
        return request({
            url: '/level/delete',
            method: 'post',
            data
        })
    },
    queryById(data) {
        return request({
            url: '/level/queryById',
            method: 'post',
            data
        })
    },
    setParam(data) {
        return request({
            url: '/caseExamineManage/setParam',
            method: 'post',
            data
        })
    }
}
