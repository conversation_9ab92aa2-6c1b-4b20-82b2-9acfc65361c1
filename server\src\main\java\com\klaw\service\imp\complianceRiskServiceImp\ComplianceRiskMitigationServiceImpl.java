package com.klaw.service.imp.complianceRiskServiceImp;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yitter.idgen.YitIdHelper;
import com.klaw.dao.complianceRiskDao.ComplianceReportMapper;
import com.klaw.dao.complianceRiskDao.ComplianceRiskMitigationMapper;
import com.klaw.entity.complianceRiskBean.ComplianceReportEntity;
import com.klaw.entity.complianceRiskBean.ComplianceRiskMitigation;
import com.klaw.service.complianceRiskService.ComplianceReportService;
import com.klaw.service.complianceRiskService.ComplianceRiskMitigationService;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service("/complianceRiskMitigationService")
public class ComplianceRiskMitigationServiceImpl extends ServiceImpl<ComplianceRiskMitigationMapper, ComplianceRiskMitigation> implements ComplianceRiskMitigationService {
    @Override
    public void saveData(ComplianceRiskMitigation complianceRiskMitigation) {
        complianceRiskMitigation.setId(YitIdHelper.nextId() + "");
        baseMapper.insert(complianceRiskMitigation);
    }

    @Override
    public int deleteDataById(String id) {
        return baseMapper.deleteById(id);
    }

    @Override
    public void delByParentId(String parentId) {
        Map<String, Object> map = new HashMap<>();
        map.put("parent_id", parentId);
        baseMapper.deleteByMap(map);
    }

    @Override
    public void saveMutiData(List<ComplianceRiskMitigation> complianceRiskMitigationList) {
        List<ComplianceRiskMitigation> addList = new ArrayList<>();
        int i = 1;
        this.delByParentId(complianceRiskMitigationList.get(0).getRiskWarningId());
        for (ComplianceRiskMitigation complianceRiskMitigation : complianceRiskMitigationList) {
            complianceRiskMitigation.setSort(i);
            complianceRiskMitigation.setId(YitIdHelper.nextId() + "");
            complianceRiskMitigation.setCreateTime(new Date());
            complianceRiskMitigation.setUpdateTime(new Date());
            addList.add(complianceRiskMitigation);
            i++;
        }
        if (!addList.isEmpty()) {
            this.saveBatch(addList);
        }
    }

    @Override
    public List<ComplianceRiskMitigation> queryByParnetId(String parentId) {
        QueryWrapper<ComplianceRiskMitigation> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("risk_warning_id", parentId);
        queryWrapper.orderByAsc("sort");
        List<ComplianceRiskMitigation> detailList = baseMapper.selectList(queryWrapper);
        return detailList==null?new ArrayList<>():detailList;
    }
}
