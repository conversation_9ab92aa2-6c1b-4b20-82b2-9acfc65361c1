<!-- 合规规范详情页 -->
<template>
	<div>
	<FormWindow ref="formWindow" @initData="initData" @loadData="loadData">
		<el-container v-loading="loading" style="height: calc(100vh - 84px)" :element-loading-text="loadingText">
			<el-main>
				<el-scrollbar style="height: 100%" wrap-style="overflow-x: hidden;">
					<!--数据表单块-->
					<el-form
						ref="dataForm"
						:model="mainData"
						:rules="!isView ? rules : {}"
						label-width="130px"
						:style="mainData.dataStateCode === utils.dataState_BPM.FINISH.code ? 'margin-right: 50px;' : ' margin-right: 10px;'">
						<el-row style="padding: 10px 0 10px 0; position: fixed; width: 100%; overflow-y: auto; background-color: white; z-index: 999">
							<!-- <el-button v-if="!isView" type="success" size="mini" @click="approval_">生成审批单</el-button> -->
							<el-button v-if="!isView" class="normal-btn" size="mini" icon="el-icon-folder-checked" @click="save_">保存</el-button>
							<el-button v-if="!isView" class="normal-btn" size="mini" icon="el-icon-folder-checked" @click="submit_">提交</el-button>
						</el-row>
						<div style="padding-top: 50px"></div>
						<span style="text-align: left; font-size: 23px; margin-left: 43%; font-weight: 900">合规规范</span>
						<!--基础信息块-->
						<hg-base-info :data.sync="mainData" :data-state="dataState" :authorization-data="authorizationData" :view="view" />
	

						<!--公共信息-->
						<OtherInfo :data.sync="mainData" :main-id="mainData.id" :data-state="dataState" style="position: absolute; bottom: 0; width: 100%; margin-top: 20px" />
					</el-form>
					
					<!--案件审批表-->
					<prosecution-dialog :visible.sync="prosecutionDialog" :is-multiple="false" @onSure="prosecutionSelect" />
				</el-scrollbar>
			</el-main>
			<!-- 选择模版 -->
			<!-- <Shortcut :templateShow="templateShow" @templateClick="templateClick" /> -->
		</el-container>
	</FormWindow>
	</div>
</template>

<script>
	// vuex缓存数据
	import { mapGetters } from 'vuex';

	// 接口api
	import complianceReportApi from '@/api/risk/complianceReport.js'
	import taskApi from '@/api/_system/task';
	// 组件
	import FormWindow from '@/view/components/FormWindow/FormWindow';
	import OtherInfo from '@/view/litigation/caseManage/case/caseChild/OtherInfo';
	import OrgSingleDialogSelect from '@/view/components/OrgSingleDialogSelect/index';
	import ProsecutionDialog from './ProsecutionDialog';
	import QsBaseInfo from './qisubaseInfo';
    import HgBaseInfo from './heguibaseInfo';
	import CaseData from '@/view/litigation/caseManage/case/caseChild/CaseData';
	import CaseEvidenceData from '@/view/litigation/caseManage/case/caseChild/CaseEvidenceData';
	import Shortcut from '@/view/litigation/caseManage/caseExamine/Shortcut';
	import SimpleBoardTitleApproval from '@/view/components/SimpleBoard/SimpleBoardTitleApproval';

	export default {
		name: 'HggfglMainDetail',
		inject: ['layout', 'mcpLayout'],
		components: {
			SimpleBoardTitleApproval,
			CaseData,
			QsBaseInfo,
			ProsecutionDialog,
			OrgSingleDialogSelect,
			FormWindow,
			OtherInfo,
			CaseEvidenceData,
			Shortcut,
            HgBaseInfo
		},
		computed: {
			...mapGetters(['orgContext']),
			isView: function () {
				return this.dataState === this.utils.formState.VIEW;
			},
			templateShow() {
				return this.mainData.dataStateCode === this.utils.dataState_BPM.SAVE.code && this.dataState !== this.utils.formState.VIEW;
			},
		},
		data() {
			return {
				type: null,
				tabId: null,
				oarecordsDialog: false,
				loading: false,
				dataState: null,
				functionId: null, //终止的时候要用，需要手动关闭
				dataId: null,
				taskId: null,

				view: 'old',
				mainData: {
					reportSubject: null,//报告主题
					reportCategory: null,//报告类别
					reportYear: null,//报告年度
					reportExplanation: null,//报告说明
					reportFile: null,//相关附件
					reporter: null,//上报人
					reportingUnit: null,//上报单位
					reportTime:null,//上报时间
					// reviewStatus:this.utils.dataState_BPM.SAVE.name,//状态
					reviewStatus:null,
					id: null, //主键
					partiesList: [],
					claimList: [],
					otherDataList: [],
					relations: [],
					sealList: [],
					createOgnId: null, //当前机构ID
					createOgnName: null, //当前机构名称
					createDeptId: null, //当前部门ID
					createDeptName: null, //当前部门名称
					createGroupId: null, //当前部门ID
					createGroupName: null, //当前部门名称
					createPsnId: null, //当前人ID
					createPsnName: null, //当前人名称
					createOrgId: null, //当前组织ID
					createOrgName: null, //当前组织名称
					createPsnFullId: null, //当前人全路径ID
					createPsnFullName: null, //当前人全路径名称
					createPsnPhone: null, //经办人电话
					createTime: null, //创建时间
					auditStatus:this.utils.dataState_BPM.SAVE.name,//状态
					dataState: this.utils.dataState_BPM.SAVE.name, //数据状态
					dataStateCode: this.utils.dataState_BPM.SAVE.code, //数据状态编码

				},
				orgTreeDialog: false,
				orgDialogTitle: '组织信息',
				isAssign: false,
				prosecutionDialog: false,
				rules: {
					reportSubject: [{ required: true, message: '请输入报告主题', trigger: 'blur' }],
					reportYear: [{ required: true, message: '请选择报告年度', trigger: 'blur' }],
					reportCategory: [{ required: true, message: '请选择报告类别', trigger: 'blur' }],
					reportExplanation: [{ required: true, message: '请输入报告说明', trigger: 'blur' }],
				},
				activity: null, //记录当前待办处于流程实例的哪个环节
				obj: {
					// 流程处理逻辑需要的各种参数
					taskId: null,
					processInstanceId: null,
					businessKey: null,
					title: null,
					functionName: null,
					sid: null,
				},
				noticeParams: {},
				noticeData: {
					moduleName: '', // 模块名称
					dataId: '', // 数据ID
					url: '', // 地址
					title: '', // 地址
					params: {}, // 其他参数
				},
				loadingText: '加载中...',
			};
		},
		provide() {
			return {
				parentCase: this,
			};
		},
		methods: {
			initData(temp, dataState) {
				this.dataState = dataState;
				Object.assign(this.mainData, temp);

				let obj = this.utils.getManagementUnit(this.mainData.createPsnFullName, this.mainData.createPsnFullId);
				this.mainData.managementUnit = obj.name;
				this.mainData.managementUnitId = obj.id;
				this.mainData.unitType = obj.unitType;
				this.mainData.unitTypeId = obj.unitTypeId;

				this.view = 'old';

				let year = new Date().getFullYear();
				this.utils.createKvsequence('HGBG' + year, 6).then((value) => {
					this.mainData.caseCode = value.data.kvsequence;
				});
				// this.mainData.currentUnit = this.mainData.createOgnName;
				// this.mainData.currentUnitId = this.mainData.createOgnId;
				
				this.mainData.riskDepartment = this.mainData.createOgnName;//有点问题
				this.mainData.riskDepartmentId = this.mainData.createOgnId;

				const interCode = 'AJ_GC_CLMC_QS';
				const codes = [interCode];
				this.utils.getDic(codes).then((response) => {
					const datas = response.data.data[codes[0]].filter((item) => item.whetherSolidified === true);
					if (datas && datas.length > 0) {
						datas.forEach((item, index) => {
							const data = this.childData(index);
							data.name = item.dicName;
							data.whetherSys = true;
							this.mainData.otherDataList.push(data);
						});
					}
				});
				this.loading = false;
			},
			loadData(dataState, dataId) {
				this.functionId = this.$route.query.functionId;
				if (this.$route.query.view !== undefined && this.$route.query.view !== '') this.view = this.$route.query.view;
				if (this.$route.query.type !== undefined && this.$route.query.type !== '') this.type = this.$route.query.type;
				this.dataState = dataState;
				complianceReportApi.queryById(dataId).then((response) => {
					this.mainData = response.data.data;
					this.authorizationData = response.data.data.authorization;
					if (response.data.data.authorization !== null) this.authorizationList = response.data.data.authorization.authorizationLitigationList;
					this.loading = false;
				});
			},
			// },
			save() {
				return new Promise((resolve, reject) => {
					//判断 reporter 和 reportingUnit 是否有空值
					if (!this.mainData.reporter || !this.mainData.reportingUnit) {
						this.mainData.reporter = this.mainData.createPsnName; // 上报人
						this.mainData.reportingUnit = this.mainData.createDeptName; // 上报单位
					} else {
						this.mainData.reporter = this.mainData.reporter; // 上报人
						this.mainData.reportingUnit = this.mainData.reportingUnit; // 上报单位
					}
					this.mainData.reviewStatus = '已编辑';
					complianceReportApi
						.save(this.mainData)
						.then((response) => {
							resolve(response);
						})
						.catch((error) => {
							reject(error);
						});
				});
			},
			submit() {
				return new Promise((resolve, reject) => {
					//判断 reporter 和 reportingUnit 是否有空值
					if (!this.mainData.reporter || !this.mainData.reportingUnit) {
						this.mainData.reporter = this.mainData.createPsnName; // 上报人
						this.mainData.reportingUnit = this.mainData.createDeptName; // 上报单位
					} else {
						this.mainData.reporter = this.mainData.reporter; // 上报人
						this.mainData.reportingUnit = this.mainData.reportingUnit; // 上报单位
					}
					this.mainData.reviewStatus = '已提交';

					// 获取当前时间并格式化
					// const now = new Date();
					// const year = now.getFullYear();
					// const month = String(now.getMonth() + 1).padStart(2, '0');
					// const day = String(now.getDate()).padStart(2, '0');
					// const hours = String(now.getHours()).padStart(2, '0');
					// const minutes = String(now.getMinutes()).padStart(2, '0');
					// const seconds = String(now.getSeconds()).padStart(2, '0');

    				// this.mainData.reportTime = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
					this.mainData.reportTime = new Date();
					complianceReportApi
						.save(this.mainData)
						.then((response) => {
							resolve(response);
						})
						.catch((error) => {
							reject(error);
						});
				});
			},

			//选择模板
			templateClick(val) {
				if (val) {
					this.prosecutionDialog = true;
				}
			},
			prosecutionSelect(data) {
				this.mainData.reportSubject = data.reportSubject; 
				this.mainData.reportYear = data.reportYear; 
				this.mainData.reportCategory = data.reportCategory;
				this.mainData.internalExternalRisk = data.internalExternalRisk; 
				this.mainData.positiveNegativeImpact = data.positiveNegativeImpact; 
				this.mainData.involvedAmount = data.involvedAmount;
				this.mainData.riskDescription = data.riskDescription; 
				this.mainData.riskReason = data.riskReason;
				this.mainData.potentialConsequences = data.potentialConsequences; 
				this.mainData.reportFile = data.reportFile; 
				this.mainData.unitTypeId = data.unitTypeId; //单位类型id
				this.mainData.riskDepartment = data.riskDepartment;//风险部门
				this.mainData.riskDepartmentId = data.riskDepartmentId;//风险部门Id
				this.mainData.currentUnit = data.currentUnit; //当事单位
				this.mainData.currentUnitId = data.currentUnitId; //当事单位id
				this.mainData.reporter = data.createPsnName; //经办人				
				this.mainData.reportingUnit = data.createDeptName; //经办单位
				this.mainData.createPsnPhone = data.createPsnPhone;//经办人电话
				this.mainData.caseInterest = data.caseInterest;//利息
				this.mainData.createOgnName = data.createOgnName;//经办单位
				this.mainData.createPsnName = data.createPsnName;//经办人
				this.mainData.createDeptName = data.createDeptName;//经办部门
				// this.mainData.auditStatus = data.dataState;//状态
				this.mainData.partiesList = data.partiesList;
				this.mainData.partiesList.forEach((item) => {
					item.id = this.utils.createUUID();
					item.masterId = this.mainData.id;
				});

				this.mainData.claimList = data.claimList;
				this.mainData.claimList.forEach((item) => {
					item.id = this.utils.createUUID();
					item.parentId = this.mainData.id;
				});

				this.mainData.otherDataList = data.otherDataList;
				this.mainData.otherDataList.forEach((item) => {
					item.id = this.utils.createUUID();
					item.parentId = this.mainData.id;
					item.files = null;
				});

				this.mainData.relations = data.relations;
				this.mainData.relations.forEach((item) => {
					item.id = this.utils.createUUID();
					item.relationId = this.mainData.id;
				});
			},
			approval_() {
				this.$refs['dataForm'].validate((valid) => {
					if (valid) {
						this.save()
							.then(() => {
								const tabId = this.mainData.id;
								if (this.mainData.dataStateCode == this.utils.dataState_BPM.SAVE.code) {
									taskApi.selectFunctionId({ functionCode: 'case_risk_main' }).then((res) => {
										const functionId = res.data.data[0].ID;
										this.layout.openNewTab('案件风险告知审批信息', 'design_page', functionId, tabId, {
											...this.utils.routeState.NEW(tabId),
											functionId: functionId,
											businessKey: tabId,
											entranceType: 'FLOWABLE',
											create: 'create',
											view: 'new',
										});
									});
								} else {
									const tabId = this.mainData.id;
									taskApi.selectTaskId({ businessKey: tabId, isView: '' }).then((res) => {
										const functionId = res.data.data[0].ID;
										const uuid = this.utils.createUUID();
										this.layout.openNewTab('案件风险告知审批信息', 'design_page', 'design_page', uuid, {
											processInstanceId: res.data.data[0].PID, //流程实例
											taskId: res.data.data[0].ID, //任务ID
											businessKey: tabId, //业务数据ID
											functionId: functionId, //不传也可以，只有首环节需要传，实例启动就可以不传了
											entranceType: 'FLOWABLE',
											type: 'toDeal',
											view: 'new',
											create: 'create',
										});
									});
								}
							})
							.then(() => {
								this.mcpLayout.closeTab();
							});
					}
				});
			},
			save_() {
				this.save().then(() => {
					this.mainData.reviewStatus = '已编辑';
					this.$message.success('保存成功!');
				});
			},
			submit_() {
        this.$refs['dataForm'].validate((valid) => {
            if (valid) {
                this.submit().then(() => {
                    this.mainData.reviewStatus = '已提交';
                    this.$message.success('提交成功!');
                });
            } else {
                this.$message.error('表单填写不完整，请检查后重新提交!');
            }
        });
    },
	saveCurrentTime() {
      const now = new Date();
      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, '0');
      const day = String(now.getDate()).padStart(2, '0');
      const hours = String(now.getHours()).padStart(2, '0');
      const minutes = String(now.getMinutes()).padStart(2, '0');
      const seconds = String(now.getSeconds()).padStart(2, '0');
    },
}}
</script>

<style scoped></style>
