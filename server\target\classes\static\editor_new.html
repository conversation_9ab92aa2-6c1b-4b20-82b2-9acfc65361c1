<div ng-controller="StencilController">
    <div class="subheader editor-toolbar" id="editor-header">
        <div class="btn-group">
            <div class="btn-toolbar pull-left" ng-controller="ToolbarController" ng-cloak>
                <!-- 顶部工具栏 -->
                <button id="{{item.id}}" title="{{item.title | translate}}" ng-repeat="item in items" ng-switch on="item.type" class="btn btn-inverse" ng-class="{'separator': item.type == 'separator'}" ng-disabled="item.type == 'separator' || item.enabled == false" ng-click="toolbarButtonClicked($index)">
                    <i ng-switch-when="button" ng-class="item.cssClass" class="toolbar-button" data-toggle="tooltip"
                       title="{{item.title | translate}}"></i>
                       {{ item.text | translate }}
                    <div ng-switch-when="separator" ng-class="item.cssClass"></div>
                </button>
            </div>
        </div>
        <div class="btn-group pull-right" ng-show="!secondaryItems.length">
            <div class="btn-toolbar pull-right" ng-controller="ToolbarController">
                <button title="{{item.title | translate}}" ng-repeat="item in secondaryItems" ng-switch on="item.type"
                        class="btn btn-inverse" ng-class="{'separator': item.type == 'separator'}"
                        ng-disabled="item.type == 'separator'" ng-click="toolbarSecondaryButtonClicked($index)"
                        id="{{item.id}}">
                    <i ng-switch-when="button" ng-class="item.cssClass" class="toolbar-button" data-toggle="tooltip"
                       title="{{item.title | translate}}"></i>

                    <div ng-switch-when="separator" ng-class="item.cssClass"></div>
                </button>
            </div>

        </div>
    </div>
    <div class="full">
        <div class="row row-no-gutter">
            <div id="paletteHelpWrapper" class="col-xs-2 col-sm-2 col-md-2 col-lg-2" style="padding:0px;float:left;z-index: 10;background-color: #fff;">
                <div class="stencils" id="paletteSection">
                    <div ng-if="stencilItemGroups.length > 1">
                        <div ng-repeat="group in stencilItemGroups">
                            <!-- 左侧流程组件 -->
                            <ul ng-if="group.visible && group.items" class="stencil-group" ng-class="{collapsed: !group.expanded, 'first': $first}">
                                <li ng-include="'activiti-editor/partials/stencil-item-template.html?version=4'"></li>
                            </ul>

                            <div ng-if="!group.items" ng-include="'activiti-editor/partials/root-stencil-item-template.html?version=4'"></div>

                        </div>
                    </div>
                    <div ng-if="stencilItemGroups.length == 1">
                        <ul class="stencil-group">
                            <li ng-repeat="item in stencilItemGroups[0].paletteItems" class="stencil-item" id="{{item.id}}" title="{{item.description}}" ng-model="draggedElement" data-drag="true" jqyoui-draggable="{onStart:'startDragCallback', onDrag:'dragCallback'}" data-jqyoui-options="{revert: 'invalid', helper: 'clone', opacity : 0.5}">

                                <img ng-src="editor-app/stencilsets/bpmn2.0/icons/{{item.icon}}" width="16px;" height="16px;" /> {{item.name}}
                            </li>
                        </ul>
                    </div>
                </div>
            </div>

            <div id="canvasHelpWrapper" class="col-xs-7 col-sm-7 col-md-7 col-lg-7" style="padding:0px;">
                <div class="canvas-wrapper" id="canvasSection" ng-model="droppedElement" data-drop="true" data-jqyoui-options jqyoui-droppable="{onDrop:'dropCallback',onOver: 'overCallback', onOut: 'outCallback'}">
                    <div class="canvas-message" id="model-modified-date"></div>
                    <div class="Oryx_button" id="delete-button" title="{{'BUTTON.ACTION.DELETE.TOOLTIP' | translate}}" ng-click="deleteShape()" style="display:none">
                        <img src="editor-app/images/delete.png" />
                    </div>
                    <div class="Oryx_button" id="morph-button" title="{{'BUTTON.ACTION.MORPH.TOOLTIP' | translate}}" ng-click="morphShape()" style="display:none">
                        <img src="editor-app/images/wrench.png" />
                    </div>
                    <div class="Oryx_button" ng-repeat="item in quickMenuItems" id="{{item.id}}" title="{{item.description}}" ng-click="quickAddItem(item.id)" ng-model="draggedElement" data-drag="true" jqyoui-draggable="{onStart:'startDragCallbackQuickMenu', onDrag:'dragCallbackQuickMenu'}"
                         data-jqyoui-options="{revert: 'invalid', helper: 'clone', opacity : 0.5}" style="display:none">
                        <img ng-src="editor-app/stencilsets/bpmn2.0/icons/{{item.icon}}" />
                    </div>
                </div>
            </div>
            <!-- 右侧组件 -->
            <div id="propertiesHelpWrapper" class="col-xs-3 col-sm-3 col-md-3 col-lg-3" style="padding:0px;float:right;background-color: #fff;">
                <div class="propertySection" id="propertySection" ng-class="{collapsed: propertyWindowState.collapsed}">
                    <div class="selected-item-section">
                        <div class="clearfix">
                            <div class="pull-right" ng-if="selectedItem.auditData.createDate">
                                <strong>{{'ELEMENT.DATE_CREATED' | translate}}: </strong> {{selectedItem.auditData.createDate}}
                            </div>
                            <div class="pull-right" ng-if="selectedItem.auditData.author">
                                <strong>{{'ELEMENT.AUTHOR' | translate}}: </strong> {{selectedItem.auditData.author}}
                            </div>
                            <div class="selected-item-title" ng-show="!propertyWindowState.collapsed">
                                <!-- 标题 -->
                                <span>{{selectedItem.title}}</span>
                                <span>{{modelData.name}}</span>
                            </div>
                        </div>

                        <div class="selected-item-body" ng-show="!propertyWindowState.collapsed">
                            <div>
                                <div class="property-row" ng-repeat="property in selectedItem.properties" ng-click="propertyClicked($index)" ng-class="{'clear' : $index%2 == 0}">
                                    <!-- 表单行 -->
                                    <span class="title" ng-if="selectedItem.activityNodeTitle != '人工任务' ||!property.hidden && property.key != 'oryx-multiinstance_type' && property.key !='oryx-isforcompensation' && property.key != 'oryx-asynchronousdefinition' && property.key !='oryx-exclusivedefinition'">{{ property.title }}&nbsp;:</span>
                                    <span class="title-removed" ng-if="property.hidden"><i>{{ property.title }}&nbsp;({{'PROPERTY.REMOVED'
                    | translate}})&nbsp;:</i></span>
                                    <span class="value" ng-if=" selectedItem.activityNodeTitle != '人工任务' || property.key != 'oryx-multiinstance_type' && property.key !='oryx-isforcompensation' && property.key != 'oryx-asynchronousdefinition' && property.key !='oryx-exclusivedefinition'">
                                        <!-- stencil-controller.js -->
                                        <!-- approve-rule-display-template.html -->
                                        <ng-include src="getPropertyTemplateUrl($index)" ng-if="!property.hasReadWriteMode"></ng-include>
                                        <ng-include src="getPropertyReadModeTemplateUrl($index)" ng-if="property.hasReadWriteMode && property.mode == 'read'"></ng-include>
                                        <ng-include src="getPropertyWriteModeTemplateUrl($index)" ng-if="property.hasReadWriteMode && property.mode == 'write'"></ng-include>
                                    </span>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>

        </div>
    </div>
</div>