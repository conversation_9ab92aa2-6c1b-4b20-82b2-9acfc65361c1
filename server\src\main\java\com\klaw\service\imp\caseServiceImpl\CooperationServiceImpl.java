package com.klaw.service.imp.caseServiceImpl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.klaw.constant.DataState;
import com.klaw.dao.caseDao.CooperationMapper;
import com.klaw.entity.caseBean.Cooperation;
import com.klaw.entity.caseBean.CooperationReply;
import com.klaw.service.caseService.CooperationReplyService;
import com.klaw.service.caseService.CooperationService;
import com.klaw.utils.DataAuthUtils;
import com.klaw.utils.OrgUtils;
import com.klaw.utils.PageUtils;
import com.klaw.utils.Utils;
import com.klaw.vo.OrgContextVo;
import com.sgai.mcp.core.impl.LoginHelper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

@Service("caseCooperationServiceI")
@Transactional
public class CooperationServiceImpl extends ServiceImpl<CooperationMapper, Cooperation> implements CooperationService {
//	@Resource
//	SysNoticeService sysNoticeService;

	@Autowired
    CooperationReplyService cooperationReplyService;

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void saveData(Cooperation cooperation) {
		//如果是提交则生成消息提醒
		if(cooperation.getDataStateCode().equals(DataState.SUBMIT.getKey())){
			//获取任务协作人和任务知晓人
			List<String> orgIds=getOrgIds(cooperation.getHandleOrgId(),cooperation.getKnowOrgId());

			//去重
			orgIds = Utils.repeatListWayTwo(orgIds);

			//新增消息提醒
//			sysNoticeService.createNotices(cooperation.getName(),"协作模块",NoticeType.XZ_NOTICE,cooperation.getId(),"CooperationView","{\"dataId\":\""+cooperation.getId()+"\",\"dataState\":\"view\"}",cooperation.getCreateOrgId(),orgIds);
		}

		saveOrUpdate(cooperation);

	}

	private static List<String> getOrgIds(String... orgIds){
		List<String> orgIdList = new ArrayList<>();
		for (String orgId : orgIds) {
			if (StringUtils.isNotBlank(orgId)){
				orgIdList.addAll(Arrays.asList(orgId.split(",")));
			}
		}
		return orgIdList;
	}

	@Override
	public Cooperation queryDataById(String id) {
		Cooperation cooperation = getById(id);
		cooperation.setCooperationReplyList(cooperationReplyService.list(new QueryWrapper<CooperationReply>().eq("parent_id",id).orderBy(true,true,"create_time")));
		return cooperation;
	}

	@Override
	public Page<Cooperation> queryPageData(JSONObject json) {
		QueryWrapper<Cooperation> wrapper = new QueryWrapper<>();
		getFilter(json,wrapper);
		return page(new PageUtils<Cooperation>(json), wrapper);
	}

	private void getFilter(JSONObject json, QueryWrapper<Cooperation> wrapper) {
		//是否台账
		boolean isQuery = json.containsKey("isQuery") ? json.getBoolean("isQuery") : false;
		//协作名称
		String name = json.containsKey("name") ? json.getString("name") : null;
		//
//		Long functionId = json.containsKey("functionId") ? json.getLong("functionId") : null;
		//协作人
		String handleOrgName = json.containsKey("handleOrgName") ? json.getString("handleOrgName") : null;
		//知晓人
		String knowOrgName = json.containsKey("knowOrgName") ? json.getString("knowOrgName") : null;
		//我发起或者我收到
		Integer sw = json.containsKey("sw") ? json.getInteger("sw") : null;
		//顺序字段
		String orderCol = json.containsKey("orderCol") ? json.getString("orderCol") : null;
		//顺序
		boolean orderColValue = json.containsKey("orderColValue") ? json.getBoolean("orderColValue") : false;
		//创建时间(最小值)
		Date createTimeMin = json.containsKey("createTimeMin") ? json.getDate("createTimeMin") : null;
		//创建时间(最大值)
		Date createTimeMax = json.containsKey("createTimeMax") ? json.getDate("createTimeMax") : null;

		String orgId = json.containsKey("orgId") ? json.getString("orgId") : "";


		if (StringUtils.isNotBlank(name)) {
			wrapper.and(i->i.like("name", name));
		}
		if (StringUtils.isNotBlank(handleOrgName)) {
			wrapper.and(i->i.like("handle_Org_Name", handleOrgName));
		}
		if (StringUtils.isNotBlank(knowOrgName)) {
			wrapper.and(i -> i.like("know_Org_Name", knowOrgName));
		}


		if(createTimeMin != null){
			wrapper.and(i -> i.ge("create_time",createTimeMin));
		}
		if(createTimeMax != null){
			wrapper.and(i -> i.le("create_time",createTimeMax));
		}


		//模糊搜索值
		String fuzzyValue = json.containsKey("fuzzyValue") ? json.getString("fuzzyValue") : null;
		//模糊搜索匹配字段
		String[] cols =  {"name",  "content", "handle_org_name","know_org_name"};

		Utils.fuzzyValueQuery(wrapper,cols,fuzzyValue);

		long id = LoginHelper.getUnit_ID();
		System.err.println("id:"+id);
		if (sw != null && sw == 2) {
			OrgContextVo org = OrgUtils.getOrgContext(id);
			wrapper.and(i ->i.like("know_org_id", org.getCurrentOrgId()).or().like("handle_org_id",org.getCurrentOrgId()));
		}else{
			OrgContextVo org = OrgUtils.getOrgContext(id);
			Long functionId = DataAuthUtils.getFunctionIdByCode("cooperation_index");
			DataAuthUtils.dataPermSql(wrapper,null,functionId,orgId);
//            Utils.dataPermSql(wrapper,null,isQuery);
        }

		if(StringUtils.isNotBlank(orderCol)){
			wrapper.orderBy(true,orderColValue,Utils.humpToLine2(orderCol));
		}else{
			wrapper.orderBy(true,false,"create_time");
		}
	}

	@Override
	public List<Cooperation> selectListByCaseProcessId(String caseProcessId) {
		return list(new QueryWrapper<Cooperation>().eq("relation_case_process_id",caseProcessId));
	}

	@Override
	public List<Cooperation> selectListByDisputeId(String disputeId) {
		return list(new QueryWrapper<Cooperation>().eq("relation_dispute_id",disputeId));
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void saveReplyData(CooperationReply cooperationReply) {

		//获取协作信息
		Cooperation cooperation = getById(cooperationReply.getParentId());

		//获取任务协作人和任务知晓人
		List<String> orgIds=getOrgIds(cooperation.getHandleOrgId(),cooperation.getKnowOrgId());

		//去重
		orgIds = Utils.repeatListWayTwo(orgIds);

		//添加协作创建人
		orgIds.add(cooperation.getCreateOrgId());

		//去除回复人
		orgIds.removeIf(item -> item.equals(cooperationReply.getCreateOrgId()));

		//新增消息提醒
//		sysNoticeService.createNotices("新回复--"+cooperation.getName(),"协作模块",NoticeType.XZ_NOTICE,cooperation.getId(),"CooperationView","{\"dataId\":\""+cooperation.getId()+"\",\"dataState\":\"view\"}",cooperation.getCreateOrgId(),orgIds);

		cooperationReplyService.save(cooperationReply);

	}

	@Override
	public boolean deleteDataById(String id) {
		return removeById(id);
	}
}
