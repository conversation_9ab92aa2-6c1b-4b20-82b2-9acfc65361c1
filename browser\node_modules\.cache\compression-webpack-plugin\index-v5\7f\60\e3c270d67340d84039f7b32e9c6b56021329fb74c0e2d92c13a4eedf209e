
76783511497bb43201cd14bb0894b85ab98d2fc0	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.475.1754018536329.js\",\"contentHash\":\"cb24b235fd9ae47ef024b47e4d2068b9\"}","integrity":"sha512-Ucu+TcDySGOGkdXKoR3HKD/+HX7/6P7SZpAAyU0r9WL6qzvcbVPDbjC5MQscQFmhyb8xFKDDoGq8oIEA7QA8fw==","time":1754018576042,"size":168254}