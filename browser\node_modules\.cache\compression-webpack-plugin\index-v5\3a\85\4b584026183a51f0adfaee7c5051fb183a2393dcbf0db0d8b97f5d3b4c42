
a402138f646cd5cf61a2e6abe109672628cdee9b	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.217.1754018536329.js\",\"contentHash\":\"1c3b55f4a4460d5850d4a7e81e994b85\"}","integrity":"sha512-bt3CH4+qt6rAVx4c/6zGrNa2cFeCr2SrN2IqhvVEYTl8NlJmTmVRHhO52neSsHgWKQjFrvkb+K2/8ZWrWm23Cg==","time":1754018575991,"size":153943}