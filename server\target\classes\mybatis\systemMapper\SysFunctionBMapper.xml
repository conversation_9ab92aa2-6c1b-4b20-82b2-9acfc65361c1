<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.klaw.dao.systemDao.SysFunctionBMapper">
  <resultMap id="BaseResultMap" type="com.klaw.entity.systemBean.SysFunctionB">
    <!--@mbg.generated-->
    <id column="FUNCTION_ID" jdbcType="BIGINT" property="functionId" />
    <result column="MODULE_CODE" jdbcType="VARCHAR" property="moduleCode" />
    <result column="FUNCTION_ICON" jdbcType="VARCHAR" property="functionIcon" />
    <result column="FUNCTION_CODE" jdbcType="VARCHAR" property="functionCode" />
    <result column="FUNCTION_NAME" jdbcType="VARCHAR" property="functionName" />
    <result column="FUNCTION_DESCRIPTION" jdbcType="VARCHAR" property="functionDescription" />
    <result column="RESOURCE_ID" jdbcType="BIGINT" property="resourceId" />
    <result column="TYPE" jdbcType="VARCHAR" property="type" />
    <result column="PARENT_FUNCTION_ID" jdbcType="BIGINT" property="parentFunctionId" />
    <result column="ENABLED_FLAG" jdbcType="VARCHAR" property="enabledFlag" />
    <result column="FUNCTION_SEQUENCE" jdbcType="BIGINT" property="functionSequence" />
    <result column="EASY_PAGE_ID" jdbcType="BIGINT" property="easyPageId" />
    <result column="ENTRANCE_TYPE" jdbcType="VARCHAR" property="entranceType" />
    <result column="COMPONENG_RIGHTS" jdbcType="DECIMAL" property="componengRights" />
    <result column="DATA_RIGHTS" jdbcType="DECIMAL" property="dataRights" />
    <result column="CREATED_BY" jdbcType="BIGINT" property="createdBy" />
    <result column="CREATION_DATE" jdbcType="TIMESTAMP" property="creationDate" />
    <result column="LAST_UPDATED_BY" jdbcType="BIGINT" property="lastUpdatedBy" />
    <result column="LAST_UPDATE_DATE" jdbcType="TIMESTAMP" property="lastUpdateDate" />
    <result column="OBJECT_VERSION_NUMBER" jdbcType="INTEGER" property="objectVersionNumber" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    FUNCTION_ID, MODULE_CODE, FUNCTION_ICON, FUNCTION_CODE, FUNCTION_NAME, FUNCTION_DESCRIPTION, 
    RESOURCE_ID, `TYPE`, PARENT_FUNCTION_ID, ENABLED_FLAG, FUNCTION_SEQUENCE, EASY_PAGE_ID, 
    ENTRANCE_TYPE, COMPONENG_RIGHTS, DATA_RIGHTS, CREATED_BY, CREATION_DATE, LAST_UPDATED_BY, 
    LAST_UPDATE_DATE, OBJECT_VERSION_NUMBER
  </sql>
</mapper>