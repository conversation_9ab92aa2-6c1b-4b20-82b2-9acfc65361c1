
76fae26b21de1a8f34e34d0610b22bddcbff12b0	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.65.1754018536329.js\",\"contentHash\":\"4266ba8d4206137786840b6ea3639624\"}","integrity":"sha512-YDfTrX4G1oNh+UMTsaLQK85MZnEI8ueIWM6PwRVOJG9dctBfv+57X7+e3tSRpoZbCuXBp93opx2hn7GaZeF6mA==","time":1754018575959,"size":74373}