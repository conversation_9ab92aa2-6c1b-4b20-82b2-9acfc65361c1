
cd6a111eff797afe7907dda685b36f5be29c54f2	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.56.1754018536329.js\",\"contentHash\":\"a306b055e10bc0b38bcaafff319e7329\"}","integrity":"sha512-q2vOpbHLb2sAQa4Hj5ua9FfFVz686blPhpQgiEmc02GOca+vfBG6Sw30ctg02zlTwRvEumSb3x0gza4XgMEfrA==","time":1754018575958,"size":116928}