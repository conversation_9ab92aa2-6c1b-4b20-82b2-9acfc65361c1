
44ddc5407d65ae19183074fa8c69f8f0dde2e6ee	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.196.1754018536329.js\",\"contentHash\":\"bfeb9c429dc3937c0c39e3f02ec06315\"}","integrity":"sha512-cfPkqNY5MFkSvfo7jycvuZYDxUOf53P3fZSi3e+kR0pE39z2LTfpIS2ycedyPR1bUQWK3Cac1y9EHf9pQ3yGkw==","time":1754018576054,"size":231996}