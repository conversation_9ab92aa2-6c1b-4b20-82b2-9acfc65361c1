
90c7958cf64b7726432aee54f3e0c4c87080ff24	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.223.1754018536329.js\",\"contentHash\":\"8542cc1678f52d729c7828bb664241b0\"}","integrity":"sha512-Lv2wsK/nmT8TfkIRQF5u88mHEBUp56JIO/pa3C32nzsjHCaRbp5h4qR8eccpRrpr45jChKkhUACQ4bdB++043A==","time":1754018576059,"size":207951}