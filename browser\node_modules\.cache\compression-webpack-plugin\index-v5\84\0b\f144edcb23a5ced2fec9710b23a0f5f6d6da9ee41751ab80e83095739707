
19e3d916be35ee4ab3863ea6ced366c864bd82d8	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.343.1754018536329.js\",\"contentHash\":\"141b89725451c5079712490dec85f90c\"}","integrity":"sha512-qObTYwSdGSye6/UZKj/OvIzf+rlgE6TsFBiSmQR2nJsDX2OahIiBtq5IRrsinWwjrNQIH8CpICI368FogNBrcQ==","time":1754018575975,"size":73170}