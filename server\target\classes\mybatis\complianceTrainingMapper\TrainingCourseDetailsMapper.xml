<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.klaw.dao.complianceTrainingDao.TrainingCourseDetailsMapper">

    <resultMap id="BaseResultMap" type="com.klaw.entity.complianceTrainingBean.TrainingCourseDetails">
            <id property="courseId" column="course_id" jdbcType="INTEGER"/>
            <result property="complianceTrainingLearningTableId" column="compliance_training_learning_table_id" jdbcType="VARCHAR"/>
            <result property="trainingCourse" column="training_course" jdbcType="VARCHAR"/>
            <result property="courseDescription" column="course_description" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        course_id,compliance_training_learning_table_id,training_course,
        course_description,training_materials
    </sql>
</mapper>
