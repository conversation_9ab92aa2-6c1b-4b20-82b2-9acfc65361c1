<template>
  <div class="sg-page-wrap" style="padding-left: 0 !important;">
    <sg-table :data.sync="tableData" v-loading="loading" :show-index-column="false" @row-click="rowClick">
      <el-table-column label="发件人" prop="sendUserName"></el-table-column>
      <el-table-column label="标题" prop="subject"></el-table-column>
      <el-table-column label="消息类型" prop="messageType" width="100">
        <template slot-scope="scope">
          {{ messageTypes.find(item => item.value === scope.row.messageType) ? messageTypes.find(item => item.value === scope.row.messageType).meaning : '' }}
        </template>
      </el-table-column>
    </sg-table>
  </div>
</template>

<script>
import SgTable from '../../baseComponents/SgTable'
import { dictionaryCode} from '../../../../src/api'
import qs from 'qs'

export default {
  name: "unread-message",
  inject: ['layout'],
  components: {
    SgTable
  },
  data() {
    return {
      loading: false,
      messageTypes: [],
      tableData: [],
    }
  },
  methods: {
    getMessageTypes() {
      dictionaryCode({code: 'SYS.MESSAGE_TYPE'}).then(res => {
        if (res.status === 200) {
          this.messageTypes = res.data.code
          this.getData()
        }
      })
    },
    getData() {
      let reqData = {
        page: this.currentPage,
        pageSize: this.pageSize,
        messageReadType: 0
      }
    },
    rowClick() {
      this.layout.openNewTab('消息中心', 'message_center', null, null)
    }
  },
  mounted() {
    this.getMessageTypes()
  }
}
</script>

<style scoped>

</style>