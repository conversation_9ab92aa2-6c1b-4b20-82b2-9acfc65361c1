
a79445d1cae365f63ca458db3b08701320b72b01	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.72.1754018536329.js\",\"contentHash\":\"91439209b9fca19d484e37de8db73a6f\"}","integrity":"sha512-P/Mw+aRhR5r5S3hm+3Qr/OaItnwbU3MeOIRDLA+9Kj84q619Wih8Y06roEX/yoRLxBdT/CHyYlXzAGl8/eMMjg==","time":1754018576043,"size":217526}