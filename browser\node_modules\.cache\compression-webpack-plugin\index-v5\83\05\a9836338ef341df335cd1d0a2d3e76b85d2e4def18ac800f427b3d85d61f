
2cfab81b20a58ef3554ef3f545a04c68ec7e6b95	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.140.1754018536329.js\",\"contentHash\":\"6bb8cf09ce56dd87a8dcb1970e2e19b5\"}","integrity":"sha512-wqc9tCqqjPOAM8DxXuxcg9Bz1N6b1f5vTIrSo/GmeTJUryQL8nYHgqAs8xNc6duEtmDOYzW/yaf2Ylkh/fAY9Q==","time":1754018575956,"size":38890}