<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.klaw.dao.systemDao.SysFlowExampleStateMapper">
  <resultMap id="BaseResultMap" type="com.klaw.entity.systemBean.SysFlowExampleState">
    <!--@mbg.generated-->
    <id column="SID" jdbcType="DECIMAL" property="sid" />
    <result column="FLOW_EXAMPLE_ID" jdbcType="VARCHAR" property="flowExampleId" />
    <result column="FLOW_EXAMPLE_STATE" jdbcType="VARCHAR" property="flowExampleState" />
    <result column="FLOW_MODEL_KEY" jdbcType="VARCHAR" property="flowModelKey" />
    <result column="CREATED_BY" jdbcType="DECIMAL" property="createdBy" />
    <result column="CREATION_DATE" jdbcType="TIMESTAMP" property="creationDate" />
    <result column="LAST_UPDATED_BY" jdbcType="DECIMAL" property="lastUpdatedBy" />
    <result column="LAST_UPDATE_DATE" jdbcType="TIMESTAMP" property="lastUpdateDate" />
    <result column="OBJECT_VERSION_NUMBER" jdbcType="DECIMAL" property="objectVersionNumber" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    SID, FLOW_EXAMPLE_ID, FLOW_EXAMPLE_STATE, FLOW_MODEL_KEY, CREATED_BY, CREATION_DATE, 
    LAST_UPDATED_BY, LAST_UPDATE_DATE, OBJECT_VERSION_NUMBER
  </sql>
</mapper>