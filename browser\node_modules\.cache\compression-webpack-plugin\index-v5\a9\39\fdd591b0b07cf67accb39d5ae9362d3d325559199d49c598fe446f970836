
aba13d663b0571631cff0b4625d986ece210087b	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.245.1754018536329.js\",\"contentHash\":\"c9a9588f402b2f5c81a1879f2352cd26\"}","integrity":"sha512-JWO9PuPbbtjK/Z/7v+MorEIh2poVgcStJEF1pI2Oyf6RMfwWjdX7Hc+Xvx1v/2JkNsgJ6dy8mpI1+6P0gNFbHw==","time":1754018575998,"size":152291}