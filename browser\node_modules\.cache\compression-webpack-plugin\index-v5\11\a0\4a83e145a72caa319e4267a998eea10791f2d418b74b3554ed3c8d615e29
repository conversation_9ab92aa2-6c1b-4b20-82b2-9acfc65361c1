
a2ec232a80c49fda43cb5a3b89fc77a7178f0f05	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fapp.1754018536329.js\",\"contentHash\":\"ba6df68f99c138558291a3e56fdf2b3f\"}","integrity":"sha512-YpJeMANKYuMaMLtbAFsnQz+IfS8lSbGfMcgM+UmGXhWY0ct8Wdhn+2fzIeE8grnobXoT+6GJw0qgDzvER4iurQ==","time":1754018897015,"size":60520746}