
0dad06d0aa10a190ac3e613d187d92fe98886205	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.386.1754018536329.js\",\"contentHash\":\"8346de6fea7090bb434192bbcee20e64\"}","integrity":"sha512-RbPq9r/Js0R7BbMFCqJYVwsidB9JgCopXlz6sSgqcDA5ojsqQJFbhTjs+NRtbVL5cnNwNQjpbCbb4/nyEYdiOg==","time":1754018575957,"size":31719}