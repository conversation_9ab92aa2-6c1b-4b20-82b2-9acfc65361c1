
f6ec2df16fb997462df627234ce25666009643f8	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.399.1754018536329.js\",\"contentHash\":\"3a2570b8c4737dd54f2cf99287ba322b\"}","integrity":"sha512-8P2A2KvhJLeT0i8vqj8Ys3v4CQhURFFi/wdQVtYoF/QfWG1QjvOdWDORHsR9pkdmxOa0mxcgP4iJ0Bw416LlEg==","time":1754018576027,"size":165139}