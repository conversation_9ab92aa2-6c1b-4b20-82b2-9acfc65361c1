package com.klaw.service.imp.caseServiceImpl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.klaw.dao.caseDao.CaseMapper;
import com.klaw.service.caseService.CaseECharstService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.text.DecimalFormat;
import java.util.*;

@Service
public class CaseECharstServiceImpl implements CaseECharstService {


      @Autowired
      private CaseMapper caseMapper;


      @Override
      public JSONObject selectEChartsList(JSONObject json) {
            JSONObject msg = json.getJSONObject("mas");

            QueryWrapper wrapper = new QueryWrapper();
            wrapper.isNotNull("our_position");
            wrapper.isNull("parent_id");
            String id = msg.containsKey("input2") ? msg.getString("input2") :null;
            //案发时间(最小值)
            Date startTime = msg.containsKey("startTime") ? msg.getDate("startTime") : null;
            //案发时间(stopTime)

            Date stopTime = msg.containsKey("stopTime") ? msg.getDate("stopTime") : null;

            if (startTime != null) {
                  wrapper.ge("case_time", startTime);
            }
            if (stopTime != null) {
                  wrapper.le("case_time", stopTime);

            }
            if(StringUtils.isNotBlank(id)){
                  wrapper.apply("belong_Plate_Full_Id like '/%/"+id+"/%'");
//                  wrapper.like("belong_Plate_Full_Id", "/"+id+"/%");
            }else{
                  wrapper.apply("belong_Plate_Full_Id like '/%/%'");
                  wrapper.apply("belong_Plate_Full_Id not like '/%/%/%'");
//                  wrapper.notLike("belong_Plate_Full_Id", "/%/%/%");
//                  wrapper.like("belong_Plate_Full_Id", "/%/%");
            }


            List<Integer> Total = new ArrayList<>();
            List<Integer> PTotal = new ArrayList<>();
            List<Integer> DTotal = new ArrayList<>();
            List<Integer> TTotal = new ArrayList<>();
            List<BigDecimal> PMoney = new ArrayList<>();
            List<BigDecimal> DMoney = new ArrayList<>();
            List<BigDecimal> TMoney = new ArrayList<>();
            List<String> name = new ArrayList<>();

            List<Map<String, Object>> maps = caseMapper.selectEChartsList(wrapper);
            for (Map<String, Object> map : maps) {

                  Total.add(Integer.parseInt(map.get("Total").toString()));
                  PTotal.add(Integer.parseInt(map.get("PTotal").toString()));
                  DTotal.add(Integer.parseInt(map.get("DTotal").toString()));
                  TTotal.add(Integer.parseInt(map.get("TTotal").toString()));
                  name.add(map.get("name").toString());
                  PMoney.add(getBigDecimal(map.get("PMoney")));
                  DMoney.add(getBigDecimal(map.get("DMoney")));
                  TMoney.add(getBigDecimal(map.get("TMoney")));

            }
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("Total", Total);
            jsonObject.put("PTotal", PTotal);
            jsonObject.put("DTotal", DTotal);
            jsonObject.put("TTotal", TTotal);
            jsonObject.put("PMoney", PMoney);
            jsonObject.put("DMoney", DMoney);
            jsonObject.put("TMoney", TMoney);
            jsonObject.put("name", name);

            return jsonObject;
      }

      public static BigDecimal getBigDecimal(Object value) {
            BigDecimal ret = null;
            if (value != null) {
                  if (value instanceof BigDecimal) {
                        ret = (BigDecimal) value;
                  } else if (value instanceof String) {
                        ret = new BigDecimal((String) value);
                  } else if (value instanceof BigInteger) {
                        ret = new BigDecimal((BigInteger) value);
                  } else if (value instanceof Number) {
                        ret = new BigDecimal(value.toString());
                  } else {
                        throw new ClassCastException("Not possible to coerce [" + value + "] from class " + value.getClass() + " into a BigDecimal.");
                  }
            }
            return ret;
      }
      @Override
      public JSONArray selectNightingale(JSONObject json) {

            QueryWrapper wrapper = new QueryWrapper();
            wrapper.isNotNull("case_type");

//            wrapper.like("belong_plate_full_id",);
            JSONArray jsonArray = JSONArray.parseArray(JSON.toJSONString(caseMapper.selectNightingale(wrapper)));

            return jsonArray;
      }
}
