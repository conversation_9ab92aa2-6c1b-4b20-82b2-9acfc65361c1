<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.klaw.dao.contractDao.contract.BmContractPerformMapper">

    <!-- 通用查询映射结果 -->
    <sql id="query_Page_List">
        contract.contract_name,
        contract.contract_code,
        contract.data_type_name,
        contract.contract_type,
        contract.contract_money,
        contract.after_change_money,
        contract.contract_executed_money,
        contract.contract_executory_money,
        perform.perform_code,
        perform.contract_id,
        perform.perform_text,
        perform.our_party_name,
        perform.other_party_name,
        perform.contract_terms,
        perform.revenue_expenditure,
        perform.settlement_method,
        perform.plan_date,
        perform.plan_amount,
        perform.plan_ratio,
        perform.project_code,
        perform.currency,
        perform.exchange_rate_method,
        perform.exchange_rate,
        perform.plan_amount_rmb,
        perform.plan_invoice_date,
        perform.perform_explain,
        perform.perform_progress,
        perform.perform_state,
        perform.perform_state_desc,
        perform.perform_executory_money,
        perform.perform_executed_money
    </sql>
    <select id="loadPerfomList" resultType="com.klaw.entity.contractBean.contract.vo.BmContractPerformVO">
        select
        <include refid="query_Page_List"/>
        from
        bm_contract contract right join
        bm_contract_perform perform on contract.id = perform.contract_id
        <where>
            ${ew.sqlSegment}
        </where>
    </select>
    <select id="loadPerfomDataList" resultType="com.klaw.entity.contractBean.contract.vo.BmContractPerformVO">
        select
        <include refid="query_Page_List"/>
        from
        bm_contract contract right join
        bm_contract_perform perform on contract.id = perform.contract_id
        <where>
            ${ew.sqlSegment}
        </where>
    </select>

</mapper>
