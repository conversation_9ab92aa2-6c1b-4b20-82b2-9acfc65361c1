
ed96d969d9b3d03cb1a2baab90551dc41d231af8	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.86.1754018536329.js\",\"contentHash\":\"7722f2cff1e57f49bc862356c70f9bd0\"}","integrity":"sha512-HwVz+or7oJifcmATOFU5/4V3RG5gP14iEx8hhYTYUP7gRrEnpAb+6qwJatnAGzwzD0gSueu+Mg5U6dJ3NDltkg==","time":1754018575979,"size":141451}