<div ng-controller="ApproveRuleInstanceCtrl">
    <style>
        .panel * {
            box-sizing: border-box !important;
        }
    </style>
    <script>
        var viewModel = kendo.observable({
           model: {},
           createFunction: function () {
               jQuery('#grid').data('kendoGrid').addRow();
           },
           saveFunction: function () {
               debugger
               //将数据形成一个json发送到父页面去
               jQuery('#grid').data('kendoGrid').saveChanges();
               var gridData = jQuery('#grid').data('kendoGrid')._data;
               
               var data = [];
               jQuery.each(gridData,function(i,v){
                  data.push({
                	  'id':i,
                     'code':v.code,
                     'assignee':v.assignee,
                     'assigneeCode':v.assigneeCode,
                     'assigneeName':v.assigneeName,
                     'description':v.description,
                     'rules':v.rules
                  })
               })
               var appElement = document.querySelector('[ng-controller=ApproveRuleInstanceCtrl]');
               var $scope = angular.element(appElement).scope();
               $scope.assignment = data;
               $scope.save();
               $scope.$apply();
           }
       });
        
        var viewModel2 = kendo.observable({
            model: {},
            createFunction: function () {
                jQuery('#gridCondition').data('kendoGrid').addRow();
            },
            saveFunction: function () {
                var grid = jQuery('#gridCondition').data('kendoGrid');
                if(grid.validate()) {
                    //将数据形成一个json发送到父页面去
                    jQuery('#gridCondition').data('kendoGrid').saveChanges();
                    jQuery('#dialog').data('kendoWindow').close();
                }
            }
        });
    </script>
    
    <div id="win" style="display:none;">
        <div class="pull-left" id="toolbar-btn" style="padding-bottom:10px;">
            <span class="btn btn-primary k-grid-add" style="float:left;margin-right:5px;"
              data-bind="click:createFunction"><i class="fa fa-plus-square" style="margin-right:3px;"></i><@spring.message "hap.new"/></span>
            <span class="btn btn-success k-grid-save-changes" style="float:left;margin-right:5px;"
              data-bind="click:saveFunction"><i class="fa fa-save" style="margin-right:3px;"></i><@spring.message "hap.save"/></span>
            <span onclick="deleteData()" class="btn btn-danger"
              style="float:left;margin-right:5px;"><i class="fa fa-trash-o" style="margin-right:3px;"></i><@spring.message "hap.delete"/></span>
        </div>
        <script>kendo.bind(jQuery('#toolbar-btn'), viewModel);</script>
        <div style="clear:both">
            <div id="grid"></div>
        </div>
    </div>
    
    <div id="dialog" style="display:none;">
    <div class="pull-left" id="toolbar-btn2" style="padding-bottom:10px;">
            <span class="btn btn-primary k-grid-add" style="float:left;margin-right:5px;"
              data-bind="click:createFunction"><i class="fa fa-plus-square" style="margin-right:3px;"></i><@spring.message "hap.new"/></span>
            <span class="btn btn-success k-grid-save-changes" style="float:left;margin-right:5px;"
              data-bind="click:saveFunction"><i class="fa fa-save" style="margin-right:3px;"></i><@spring.message "hap.save"/></span>
            <span onclick="deleteDataCondition()" class="btn btn-danger"
              style="float:left;margin-right:5px;"><i class="fa fa-trash-o" style="margin-right:3px;"></i><@spring.message "hap.delete"/></span>
        </div>
        <script>kendo.bind(jQuery('#toolbar-btn2'), viewModel2);</script>
        <div style="clear:both">
            <div id="gridCondition"></div>
        </div>
    </div>
    <script>
     editCondition = function (uid) {
        var dialog = jQuery("#dialog").kendoWindow({
            actions: ["Close"],
            width: 500,
            height: 450,
            title: '条件选择',
            modal: true,
            open:function(e){
                 var gridCondition = jQuery('#gridCondition').data('kendoGrid');
                 if(gridCondition && gridCondition.dataSource){
                     gridCondition.dataSource.page(1)
                 }
            },
            close:function(e){
                var a =jQuery('#gridCondition').data('kendoGrid');
                var gridData = a._data;
                var data = [];
                jQuery.each(gridData,function(i,v){
                   data.push({
                       'code': v.code,
                      'businessRuleCode':v.businessRuleCode,
                      'businessRuleDescription':v.businessRuleDescription
                   })
                })
                var appElement = document.querySelector('[ng-controller=ApproveRuleInstanceCtrl]');
                var $scope = angular.element(appElement).scope();
                $scope.saveCondition(uid,data);
                $scope.$apply();
            }
        }).data("kendoWindow");
        dialog.center().open();
        
        var dataSourceCondition = new kendo.data.DataSource({
            transport:{
                read:function(options){
                    var appElement = document.querySelector('[ng-controller=ApproveRuleInstanceCtrl]');
                    var $scope = angular.element(appElement).scope();
                    var data = $scope.queryCondition(uid);
                    if(!data){
                        data = []
                    } 
                    //var data=[]
                    options.success(data);
                 },
                 create: function(options) {
                     options.success(options.data.models)
                 },
                 update: function(options) {
                     options.success(options.data.models)
                 },
                 destroy: function(options) {
                     options.success(options.data.models)
                 }
            },
            batch: true,
            serverPaging: true,
            serverSorting: true,
            pageSize: 10,
            schema: {
                model: {
                id: "businessRuleCode",
                    fields: {
                        "businessRuleCode": {},
                        "businessRuleDescription": {}
                    },
                    editable: function (col) {
                        if(col == "businessRuleDescription" && !this['flag']){
                             return false;
                        }
                        if(col == "businessRuleDescription" && this['flag']){
                            this['flag'] = false;
                            return true;
                        }
                        return true;
                    }
                }

            }
        }); 
        jQuery("#gridCondition").kendoGrid({
            navigatable: true,
            dataSource:dataSourceCondition,
            height: 380,
            scrollable: true,
            selectable:'rowbox,multiple',
            pageable: {
                pageSizes: [5, 10, 20, 50],
                refresh: true,
                buttonCount: 5,
                pageSize:5
            },
            columns: [
                {
                    field: "businessRuleCode",
                    title: '条件代码',
                    width: 150,
                    template: function (dataItem) {
                        return dataItem['businessRuleCode'] || '';
                    },
                    editor: function (container, options) {
                        jQuery('<input required name="' + options.field + '"/>')
                                .appendTo(container)
                                .kendoLov({
                                    contextPath:_basePath,
                                    locale:_locale,
                                    code:'LOV_BUSINESS_RULE',
                                    model:options.model,
                                    textField:'code',
                                    select:function(e){
                                         var data = e.item;
                                         options.model.flag = true;
                                         options.model.set("businessRuleDescription",data.description);
                                    },
                                    query:function(e){
                                    	e.param['enableFlag'] = 'Y';
                                    }
                                });
                   }
                },
                {
                    field: "businessRuleDescription",
                    title: '条件描述',
                    width: 150
                }],
             editable: true
        }); 

    };
    
    function deleteData() {
        var grid = jQuery('#grid').data('kendoGrid');
        var checked = grid.selectedDataItems();
        if (grid.selectedDataItems().length) {
            kendo.ui.showConfirmDialog({
                title: $l('hap.tip.info'),
                message: $l('hap.tip.delete_confirm')
            }).done(function (event) {
                if (event.button == 'OK') {
                    jQuery.each(checked, function (i, v) {
                        grid.dataSource.remove(v)
                    })
                    grid.dataSource.sync();
                }
            })
        }
       
   }
    
    function deleteDataCondition() {
        var grid = jQuery('#gridCondition').data('kendoGrid');
        var checked = grid.selectedDataItems();
        if (grid.selectedDataItems().length) {
            kendo.ui.showConfirmDialog({
                title: $l('hap.tip.info'),
                message: $l('hap.tip.delete_confirm')
            }).done(function (event) {
                if (event.button == 'OK') {
                    jQuery.each(checked, function (i, v) {
                        grid.dataSource.remove(v)
                    })
                    grid.dataSource.sync();
                }
            })
        }
       
   }
    function Test(){
        alert(1);
        $("#Test").kendoLov($.extend(${lovProvider.getLov(base.contextPath, base.locale, "LOV_COMPANY")}, {
            query: function (e) {
                e.param['enabledFlag'] = 'Y';
            }
        }))
    }
    </script>
    <button  ng-click="showCustom()">选择</button>
</div>