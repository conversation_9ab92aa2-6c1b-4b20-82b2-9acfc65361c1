package com.klaw.service.imp.complianceTrainingServiceImp;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.klaw.dao.complianceTrainingDao.HouseholdComplianceTrainingLearningTableMapper;
import com.klaw.entity.complianceTrainingBean.ComplianceTrainingLearningTable;
import com.klaw.entity.complianceTrainingBean.ComplianceTrainingReviews;
import com.klaw.entity.complianceTrainingBean.HouseholdComplianceTrainingLearningTable;
import com.klaw.entity.complianceTrainingBean.TrainingCourseDetails;
import com.klaw.service.complianceTrainingService.ComplianceTrainingReviewsService;
import com.klaw.service.complianceTrainingService.HouseholdComplianceTrainingLearningTableService;
import com.klaw.service.complianceTrainingService.TrainingCourseDetailsService;
import com.klaw.utils.PageUtils;
import com.klaw.utils.Utils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【household_compliance_training_learning_table(户合用规培训学习表)】的数据库操作Service实现
 * @createDate 2024-12-02 09:05:42
 */
@Service
public class HouseholdComplianceTrainingLearningTableServiceImpl extends ServiceImpl<HouseholdComplianceTrainingLearningTableMapper, HouseholdComplianceTrainingLearningTable>
        implements HouseholdComplianceTrainingLearningTableService {

    @Resource
    private TrainingCourseDetailsService trainingCourseDetailsService;
    @Resource
    private ComplianceTrainingReviewsService complianceTrainingReviewsService;

    @Override
    public Page<HouseholdComplianceTrainingLearningTable> queryPageData(JSONObject jsonObject) {
        QueryWrapper<HouseholdComplianceTrainingLearningTable> queryWrapper = new QueryWrapper<>();
        getFilter(jsonObject, queryWrapper);
        PageUtils<HouseholdComplianceTrainingLearningTable> page = page(new PageUtils<>(jsonObject), queryWrapper);
        List<HouseholdComplianceTrainingLearningTable> records = page.getRecords();
        for (HouseholdComplianceTrainingLearningTable record : records) {
            // 学习人员字段统计
            setTrainingStatus(record);
            setCourseDetails(record);
            setReviews(record);
        }
        page.setRecords(records);
        return page;
    }

    private void setReviews(HouseholdComplianceTrainingLearningTable record) {
        List<ComplianceTrainingReviews> reviewsList = complianceTrainingReviewsService.list(new QueryWrapper<ComplianceTrainingReviews>().eq("compliance_training_learning_table_id", record.getId()));
        record.setReviewsList(reviewsList);
    }

    private void setCourseDetails(HouseholdComplianceTrainingLearningTable record) {
        List<TrainingCourseDetails> courseDetailsList = trainingCourseDetailsService.list(new QueryWrapper<TrainingCourseDetails>().eq("compliance_training_learning_table_id", record.getId()));
        record.setTrainingCourseDetailsList(courseDetailsList);
    }

    private void setTrainingStatus(HouseholdComplianceTrainingLearningTable record) {
        // 清除时间部分，只比较日期
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        Date currentDate = calendar.getTime();

        calendar.setTime(record.getTrainingStartTime());
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        Date trainingStartTime = calendar.getTime();

        calendar.setTime(record.getTrainingEndTime());
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        Date trainingEndTime = calendar.getTime();

        if (currentDate.before(trainingStartTime)) {
            record.setTrainingStatus("培训未开始");
        }
        // 修改后（包含等于边界）
        if (!currentDate.before(trainingStartTime) && !currentDate.after(trainingEndTime)) {
            record.setTrainingStatus("培训进行中");
        }
        if (currentDate.after(trainingEndTime)) {
            record.setTrainingStatus("培训已结束");
        }
    }

//    private void setTrainingStatus(HouseholdComplianceTrainingLearningTable record) {
//        // 当天日期＜培训开始时间，则培训状态=培训未开始
//        Date currentDate = new Date();
//        if (currentDate.before(record.getTrainingStartTime())) {
//            record.setTrainingStatus("培训未开始");
//        }
//        // 培训开始时间≤当天日期≤培训结束日期，则培训状态=培训进行中
//        if (currentDate.after(record.getTrainingStartTime()) && currentDate.before(record.getTrainingEndTime())) {
//            record.setTrainingStatus("培训进行中");
//        }
//        // 当天日期＞培训结束时间，则培训状态=培训已结束
//        if (currentDate.after(record.getTrainingEndTime())) {
//            record.setTrainingStatus("培训已结束");
//        }
//    }

    @Override
    public Page<HouseholdComplianceTrainingLearningTable> queryLearningProcessDia(JSONObject jsonObject) {
        QueryWrapper<HouseholdComplianceTrainingLearningTable> queryWrapper = new QueryWrapper<>();
        getDiaFilter(jsonObject, queryWrapper);
        return page(new PageUtils<>(jsonObject), queryWrapper);
    }

    @Override
    public void updateLearningProcess(JSONObject jsonObject) {
        String id = jsonObject.containsKey("id") ? jsonObject.getString("id") : null;
        String learningProgress = jsonObject.containsKey("learningProgress") ? jsonObject.getString("learningProgress") : null;
        HouseholdComplianceTrainingLearningTable learningTable = getById(id);
        learningTable.setLearningProgress(learningProgress);
        learningTable.setStartTime(new Date());
        // 根据主题id和当前登录用户修改数据
        updateById(learningTable);
    }

    @Override
    public HouseholdComplianceTrainingLearningTable getDataById(String id) {
        HouseholdComplianceTrainingLearningTable householdComplianceTrainingLearningTable = getById(id);
        // 课程信息
        List<TrainingCourseDetails> trainingCourseDetails = trainingCourseDetailsService.list(new QueryWrapper<TrainingCourseDetails>().eq("compliance_training_learning_table_id", householdComplianceTrainingLearningTable.getComplianceTrainingLearningTableId()));
        if (householdComplianceTrainingLearningTable.getFinishedCourseIds() != null) {
            for (TrainingCourseDetails trainingCourseDetail : trainingCourseDetails) {
                // 如果当前课程id存在于当前用户已完成id中，则完成状态为true
                trainingCourseDetail.setFinished(householdComplianceTrainingLearningTable.getFinishedCourseIds().contains(trainingCourseDetail.getCourseId()));
            }
        }

        householdComplianceTrainingLearningTable.setTrainingCourseDetailsList(trainingCourseDetails);
        // 评论信息
        List<ComplianceTrainingReviews> reviewsList = complianceTrainingReviewsService.list(new QueryWrapper<ComplianceTrainingReviews>()
                .eq("compliance_training_learning_table_id", householdComplianceTrainingLearningTable.getComplianceTrainingLearningTableId())
                .orderByDesc("create_time"));
        householdComplianceTrainingLearningTable.setReviewsList(reviewsList);
        return householdComplianceTrainingLearningTable;
    }

    @Override
    public void updateCourseProcess(JSONObject jsonObject) {
        String houseHoldId = jsonObject.containsKey("houseHoldId") ? jsonObject.getString("houseHoldId") : null;
        String courseId = jsonObject.containsKey("courseId") ? jsonObject.getString("courseId") : null;
        HouseholdComplianceTrainingLearningTable learningTable = getOne(new QueryWrapper<HouseholdComplianceTrainingLearningTable>().eq("id", houseHoldId));
        if (learningTable != null) {
            if (learningTable.getFinishedCourseIds() != null) {
                String finishedCourseIds = learningTable.getFinishedCourseIds();
                finishedCourseIds += "," + courseId;
                learningTable.setFinishedCourseIds(finishedCourseIds);
            } else {
                learningTable.setFinishedCourseIds(courseId);
            }
            update(learningTable, new UpdateWrapper<HouseholdComplianceTrainingLearningTable>().eq("id", houseHoldId));
        }
        HouseholdComplianceTrainingLearningTable learningTable1 = getOne(new QueryWrapper<HouseholdComplianceTrainingLearningTable>().eq("id", houseHoldId));
        String[] finishedCourseIds = null;
        if (learningTable1.getFinishedCourseIds() != null) {
            finishedCourseIds = learningTable1.getFinishedCourseIds().split(",");
        }
        List<TrainingCourseDetails> trainingCourseDetails = trainingCourseDetailsService.list(new QueryWrapper<TrainingCourseDetails>().eq("compliance_training_learning_table_id", learningTable1.getComplianceTrainingLearningTableId()));
        String[] courseIds = trainingCourseDetails.stream()
                .map(TrainingCourseDetails::getCourseId)
                .toArray(String[]::new);
        if (Arrays.equals(finishedCourseIds, courseIds)) {
            learningTable1.setLearningProgress("已完成");
            learningTable1.setEndTime(new Date());
            updateById(learningTable1);
        }
    }

    @Override
    public void updateCourseVideoProgress(TrainingCourseDetails details) {
        trainingCourseDetailsService.updateById(details);
    }

    private void getDiaFilter(JSONObject jsonObject, QueryWrapper<HouseholdComplianceTrainingLearningTable> queryWrapper) {
        String id = jsonObject.containsKey("id") ? jsonObject.getString("id") : null;

        String fuzzyValue = jsonObject.containsKey("fuzzyValue") ? jsonObject.getString("fuzzyValue") : null;
        // 模糊搜索匹配字段
        String[] cols = {"create_org_name", "create_psn_name", "learning_progress"};
        Utils.fuzzyValueQuery(queryWrapper, cols, fuzzyValue);
        queryWrapper.eq("compliance_training_learning_table_id", id);
    }

    private void getFilter(JSONObject jsonObject, QueryWrapper<HouseholdComplianceTrainingLearningTable> queryWrapper) {
        String trainingTopic = jsonObject.containsKey("trainingTopic") ? jsonObject.getString("trainingTopic") : null;
        String trainingCategory = jsonObject.containsKey("trainingCategory") ? jsonObject.getString("trainingCategory") : null;
        String createPsnId = jsonObject.containsKey("createPsnId") ? jsonObject.getString("createPsnId") : null;
        String fuzzyValue = jsonObject.containsKey("fuzzyValue") ? jsonObject.getString("fuzzyValue") : null;
        //顺序字段
        String sortName = jsonObject.containsKey("sortName") ? jsonObject.getString("sortName") : null;
        //顺序
        boolean order = jsonObject.containsKey("order") ? jsonObject.getBoolean("order") : false;
        if (StringUtils.isNotBlank(trainingTopic)) {
            queryWrapper.like("training_topic", trainingTopic);
        }
        if (StringUtils.isNotBlank(trainingCategory)) {
            queryWrapper.like("training_category", trainingCategory);
        }
        //按送审时间倒序展示
        queryWrapper.orderByDesc("training_start_time");
        // 模糊搜索匹配字段
        String[] cols = {"training_topic", "training_category"};
        Utils.fuzzyValueQuery(queryWrapper, cols, fuzzyValue);

        if (StringUtils.isNotBlank(sortName)) {
            queryWrapper.orderBy(true, order, Utils.humpToLine2(sortName));
        } else {
            queryWrapper.orderBy(true, order, "training_start_time");
        }
        // 只显示当前登录用户的数据
        queryWrapper.eq("create_psn_id", createPsnId);
    }
}
