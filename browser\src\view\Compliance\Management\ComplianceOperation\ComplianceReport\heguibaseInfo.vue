<!-- 合规规范详情引用页面 -->
<template xmlns:v-slot="http://www.w3.org/1999/XSL/Transform">

    <div style="margin-top: 10px" v-if="view === 'old'">
      <!--基础信息表单块-->
      <div v-if="dataState !== 'view'">
        <div style="padding-left: 10px;margin-bottom: 20px;font-size: 15px;color: red;font-weight: bolder">
         
        </div>
        <div style="margin: 10px">
          <span style="font-weight: bold;font-size: 16px;color: #5A5A5F;">基本信息</span>
        </div>
        <el-row style="margin-top: 10px">
          <el-col :span="24">
            <el-form-item label="报告主题" prop="reportSubject" required>
              <el-input v-if="!isView" v-model="mainData.reportSubject" maxlength="100" show-word-limit
                        placeholder="请输入..." clearable/>
              <span v-else class="viewSpan">{{ mainData.reportSubject }}</span>
            </el-form-item>
          </el-col>
          <!-- <el-col :span="24">
            <el-form-item label="审批编号">
              <el-input v-if="!isView" v-model.trim="mainData.caseCode" disabled
                        show-word-limit style="width: 100%"/>
              <span v-else class="viewSpan">{{ mainData.caseCode }}</span>
            </el-form-item>
          </el-col> -->
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="报告类型" prop="reportCategory" required>
              <el-select  v-model="mainData.reportCategory" clearable placeholder="请选择"
                         style="width: 100%" @change="reportCategoryChange">
                <el-option
                    v-for="item in reportCategoryData"
                    :key="item.id"
                    :label="item.dicName"
                    :value="item.id"/>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="报告年度" prop="reportYear" required>
              <el-select  v-model="mainData.reportYear" clearable placeholder="请选择"
                         style="width: 100%" >
                         <el-option
                            v-for="year in yearOptions"
                            :key="year"
                            :label="year"
                            :value="year"
                        />
                    </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <!-- <el-col :span="24">
            <el-form-item label="风险发生部门" required>
              <el-input v-if="!isView" v-model="mainData.riskDepartment" placeholder="请选择" class="input-with-select"
                        disabled>
                <el-button slot="append" icon="el-icon-search" @click="orgTreeDialog = true"/>
              </el-input>
              <span v-else class="viewSpan">{{ mainData.riskDepartment }}</span>
            </el-form-item>
          </el-col> -->
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="报告说明" prop="reportExplanation" required>
              <span slot="label">报告说明</span>
              <el-input v-if="!isView" v-model="mainData.reportExplanation" :autosize="{ minRows: 3, maxRows: 20 }"
                        type="textarea" placeholder="请输入报告说明" maxlength="500"
                        show-word-limit/>
              <text-span v-else class="viewSpan" :text="mainData.reportExplanation"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="附件材料">
              <UploadDoc :files.sync="mainData.reportFile" doc-path="/case" :disabled="isView"/>
            </el-form-item>
          </el-col>
        </el-row>     
      </el-row>
  
  
  
       <!-- 集团包案领导 -->
        <!-- <OrgLeader :dialog-visible.sync="dicTreeDialogVisible3" @leaderSure="sureBtn3"></OrgLeader>
        <org-single-dialog-select :dialog-visible.sync="orgTreeDialog" :show-user="false" :show-group="false"
                                  :title="orgDialogTitle" :is-checked-user="false" @sureBtn="orgSelect"/> -->
  
        <!--选择部门或者人员-->
        <!-- <el-dialog :close-on-click-modal="false" title="选择部门" :visible.sync="orgVisible" width="50%">
          <div class="el-dialog-div">
            <orgTree :accordion="false" :is-checked-user="false" :show-user="false" :is-check="true"
                     :checked-data.sync="zxcheckedData" :is-not-cascade="true" :is-filter="true"/>
          </div>
                <span slot="footer" class="dialog-footer">
            <el-button icon="" class="negative-btn" @click="cancel_">取消</el-button>
            <el-button type="primary" icon="" class="active-btn" @click="choiceDeptSure_">确定
            </el-button>
                </span>
        </el-dialog> -->
      </div>
  <!-- 查看 -->
      <div v-else>
        <SimpleBoardTitle title="基本信息">
          <table class="table_content">
            <tbody>
              <tr>
                <th colspan="2" class="th_label">编码</th>
                <td colspan="6" class="td_value">{{ mainData.reportSubject }}</td>
                <th colspan="2" class="th_label">名称</th>
                <td colspan="14" class="td_value">{{ mainData.reportSubject }}</td>
              </tr>
              <tr>
                <th colspan="2" class="th_label">业务领域</th>
                <td colspan="6" class="td_value">{{ mainData.reportCategory }}</td>
                <th colspan="2" class="th_label">制度生效日期</th>
                <td colspan="6" class="td_value">{{ mainData.reportYear }}</td>
                <th colspan="2" class="th_label">制度废止日期</th>
                <td colspan="6" class="td_value">{{ mainData.reportYear }}</td>
              </tr>
              <tr>
                <th colspan="2" class="th_label">报告说明</th>
                <td colspan="22" class="td_value">{{ mainData.reportExplanation}}</td>
              </tr>
              <tr>
                <th colspan="2" class="th_label">附件材料</th>
                <td colspan="22" class="td_value">{{ mainData.reportFile}}</td>
              </tr>
            </tbody>
            
          </table>
  
        </SimpleBoardTitle>
      </div>
  
    </div>
  
    <div v-else>
      <SimpleBoardTitleApproval title="基本信息">
        <table class="table_content">
          <tbody>
            <tr>
              <th colspan="3" class="th_label_approval">风险名称</th>
              <td colspan="9" class="td_value_approval">{{ mainData.caseName }}</td>
              <th colspan="3" class="th_label_approval">审批单号</th>
              <td colspan="9" class="td_value_approval">{{ mainData.caseCode }}</td>
            </tr>
            <tr>
              <th colspan="3" class="th_label_approval">经办人</th>
              <td colspan="9" class="td_value_approval">{{ mainData.createPsnFullName }}</td>
              <th colspan="3" class="th_label_approval">经办时间</th>
              <td colspan="9" class="td_value_approval">{{ mainData.createTime | parseTime }}</td>
            </tr>
            <tr>
              <th colspan="3" class="th_label_approval">经办人电话</th>
              <td colspan="9" class="td_value_approval">{{ mainData.createPsnPhone }}</td>
              <th colspan="3" class="th_label_approval">风险发生部门</th>
              <td colspan="9" class="td_value_approval">{{ queryOtherPartys() }}</td>
  
            </tr>
            <tr>
              <th colspan="3" class="th_label_approval">涉及金额(元)</th>
              <td colspan="9" class="td_value_approval">{{ mainData.caseMoney | toThousandFilter }}</td>
            </tr>
  
            <tr>
              <th colspan="3" class="th_label_approval_">风险描述</th>
              <td colspan="21" class="td_value_approval_">{{ mainData.riskDescription }}</td>
            </tr>
          </tbody>
  
          <tbody>
          <tr>
            <th colspan="3" class="th_label_approval">事项报告</th>
            <td colspan="21" class="td_value_approval" style="height: 100%">
              <div v-if="mainData.eventReport || isCreate">
                <uploadDoc
                    v-model="mainData.eventReport"
                    :files.sync="mainData.eventReport"
                    :disabled="!isCreate"
                    doc-path="/case"
                />
              </div>
            </td>
          </tr>
          </tbody>
  
          <tbody>
          <tr>
            <th colspan="3" class="th_label_approval">附件材料</th>
            <td colspan="21" class="td_value_approval">
              <div v-if="mainData.relatedAttachments">
                <UploadDoc :files.sync="mainData.relatedAttachments" doc-path="/case" :disabled="true"/>
              </div>
              <div v-else style="font-size: 15px">无</div>
            </td>
          </tr>
          </tbody>
        </table>
      </SimpleBoardTitleApproval>
    </div>
  </template>
  
  <script>
  import dictApi from '@/api/_system/dict'
  import AreaSelect from '@/view/components/AreaSelect/AreaSelect'
  import OrgSingleDialogSelect from '@/view/components/OrgSingleDialogSelect/index.vue'
  import UploadDoc from '@/view/components/UploadDoc/UploadDoc.vue'
  import orgTree from '@/view/components/OrgTree/OrgTree'
  import SimpleBoardTitle from "@/view/components/SimpleBoard/SimpleBoardTitle"
  import SimpleBoardTitleApproval from "@/view/components/SimpleBoard/SimpleBoardTitleApproval"
  import money from "@/view/components/Money/index"
  import OrgLeader from '@/view/components/OrgLeader/OrgLeader'
  
  
  export default {
    name: 'HgBaseInfo',
    components: {
      OrgSingleDialogSelect, AreaSelect, UploadDoc, orgTree, SimpleBoardTitleApproval, money,
      SimpleBoardTitle, OrgLeader
    },
    props: {
      data: {
        type: Object,
        default: function () {
          return {}
        }
      },
      dataState: {
        type: String,
        default: ''
      },
      view: {
        type: String,
        default: 'new'
      },
      create: {
        type: String,
        default: ''
      },
      authorizationData: {
        type: Object,
        default: function () {
          return {}
        }
      },
    },
    data() {
      return {
        orgDialogTitle: '组织信息',
        caseDialogVisible: false,
        mainData: this.data,
        dicTreeDialogVisible: false,
        dicTreeDialogVisible2: false,
        dicTreeDialogVisible3: false,
        orgTreeDialog: false,
        caseNatures: [],
        plateData: [],
        causeOfIns: [],
        applications: [],
        involvedLevelData: [],//涉案单位管理层级
        suedUnitTypeData: [], //被诉单位性质
        unitTypeData: [],//单位类型
        reportCategoryData:[],//报告类别
        zxcheckedData: [],
        orgVisible: false,
        tableQuery: {
              reportYear: '',
          },
          yearOptions: Array.from({ length: 10 }, (_, i) => new Date().getFullYear() - i)
      }
    },
    computed: {
      isView: function () {
        return this.dataState === this.utils.formState.VIEW
      },
      causeOfInIds: {
        set: function (data) {
          this.mainData.causeOfInId = data.join(',')
        },
        get: function () {
          if (this.mainData.causeOfInId) {
            return this.mainData.causeOfInId.split(',')
          }
          return []
        }
      },
      isCreate: function () {
        return this.create === 'create'
      },
    },
    watch: {
      mainData: {
        handler(val, oldVal) {
          this.$emit('update:data', val)
        },
        deep: true
      },
      data(val) {
        this.mainData = Object.assign(this.mainData, val)
      },
      'mainData.involvedAmount': {
        handler(val, oldVal) {
          this.involvedAmountChange(val)
        },
        deep: true, immediate: true
      },
      'mainData.caseInterest': {
        handler(val, oldVal) {
          this.caseInterestChange(val)
        },
        deep: true, immediate: true
      }
    },
    created() {
      this.initDic()
      window.vm = this;
    },
    methods: {
  
      reportCategoryChange(val) {
        this.mainData.reportCategory = this.utils.getDicName(this.reportCategoryData, val)
      },
      venueIsOutOnChange(val) {
        this.mainData.venueAddress = null
        this.mainData.venueProvince = null
        this.mainData.venueCity = null
        this.mainData.venueRegion = null
      },
      sureBtn(data) {
        this.mainData.caseUndertakerId = data.unitId
        this.mainData.caseUndertaker = data.name
      },
      sureBtn2(data) {
        this.mainData.ognPackageId = data.unitId
        this.mainData.ognPackage = data.name
      },
      sureBtn3(data) {
        this.mainData.groupPackageId = data.unitId
        this.mainData.groupPackage = data.name
      },
      orgSelect(data) {
        this.mainData.currentUnitId = data.unitId
        this.mainData.currentUnit = data.name
        
        this.mainData.riskDepartmentId = data.unitId
        this.mainData.riskDepartment = data.name
      },
      moneyFocus(event) {
        event.currentTarget.select()
      },
      initDic() {
        dictApi.showAllSelect({
          dicCode: 'SSBK'
        }).then(response => {
          this.plateData = response.data.data
        })
        dictApi.showAllSelect({
          dicCode: 'AJ_AJLX'
        }).then(response => {
          this.causeOfIns = response.data.data
        })
        dictApi.showAllSelect({
          dicCode: 'AJ_SQSX'
        }).then(response => {
          this.applications = response.data.data
        })
        dictApi.showAllSelect({
          dicCode: 'AJ-SADWCJ'
        }).then(response => {
          this.involvedLevelData = response.data.data
        })
        dictApi.showAllSelect({
          dicCode: 'AJ-DWLX'
        }).then(response => {
          this.unitTypeData = response.data.data
        })
        dictApi.showAllSelect({
          dicCode: 'HG-BGLX'
        }).then(response => {
          this.reportCategoryData= response.data.data
        })
      },
      chooseApprovalDeptClick() {
        this.isCheckedUser = false
        this.showUser = false
        this.orgVisible = true
        this.is_Check = false
      },
      cancel_() {
        this.orgVisible = false
      },
      choiceDeptSure_() {
        let c = ''
        let cid = ''
        this.zxcheckedData.forEach((item) => {
          if (c.length === 0) {
            c = c + item.name
            cid = cid + item.unitId
          } else {
            c = c + ',' + item.name
            cid = cid + ',' + item.unitId
          }
        })
        this.mainData.noticeDeptName = c
        this.mainData.noticeDeptId = cid
        this.orgVisible = false
      },
      queryOtherPartys() {
        let partys = this.mainData.partiesList
        let otherPartys = ""
  
        if (partys.length > 0)
        {
          for (let i = 0; i < partys.length; i++)
          {
            if (partys[i].partyType !== '原告' && partys[i].party !== null && partys[i].party !== undefined)
              otherPartys +=  partys[i].party + ","
          }
  
          if (otherPartys !== "")
            otherPartys = otherPartys.slice(0, -1)
        }
  
        return otherPartys
      }
    }
  }
  </script>
  
  <style lang="scss" scoped>
  .hideContent {
    .el-input__inner, .el-radio.is-bordered, .el-textarea__inner, .el-input__count {
      background-color: #f9e8bb;
    }
  }
  .money-label-width .el-form-item__label {
    width: 150px; /* 指定宽度 */
  }
  
  </style>
  