<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.klaw.dao.contractDao.contract.BmManualMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.klaw.entity.contractBean.contract.BmManual">
        <id column="id" property="id" />
        <result column="manual_file" property="manualFile" />
        <result column="manual_psn_id" property="manualPsnId" />
        <result column="manual_psn_name" property="manualPsnName" />
        <result column="manual_date" property="manualDate" />
        <result column="manual_explain" property="manualExplain" />
        <result column="sort" property="sort" />
        <result column="state" property="state" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, manual_file, manual_psn_id, manual_psn_name, manual_date, manual_explain, sort, state
    </sql>

</mapper>
