
690995e43216097f75a75624ad8089f863f12ca7	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.142.1754018536329.js\",\"contentHash\":\"f771856b921059464ec78d1c58d945c4\"}","integrity":"sha512-avFpJznS5kt0dszc8sfVx5ThwSdjOkpbxYha3Vq+fNagUFqW8CoGW/r6FR7fLEmwTFrE6X3KvDbA3PmcQDQZgg==","time":1754018576049,"size":189371}