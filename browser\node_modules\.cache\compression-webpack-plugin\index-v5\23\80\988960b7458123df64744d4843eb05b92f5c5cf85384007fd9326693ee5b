
7437aefb2be90f2f96a35f3ce89ed2c02111aa0d	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.290.1754018536329.js\",\"contentHash\":\"18acea371394670a59138312a3ab1f4c\"}","integrity":"sha512-mrm+cEkES+KHHN4K9W3pOiTpZyfCt9SfVgnDGFNeGwTHd8oHt3oXBJbkpA3ebRRq6i+BMdKpaUpekJL0h3flyQ==","time":1754018575963,"size":100259}