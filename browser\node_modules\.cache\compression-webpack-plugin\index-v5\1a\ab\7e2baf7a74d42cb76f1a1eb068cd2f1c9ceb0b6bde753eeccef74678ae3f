
39e3071f4dfbac163262d9ceaf2065012dd30f79	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.273.1754018536329.js\",\"contentHash\":\"ffba937185abf1ee2c890dc46adb7855\"}","integrity":"sha512-bafELQGNrkYiVYtNnvZzoAyReIr/Sn/mUElIdRSUqIiPcg0V+YgtL5wtH28zZ7zIRRReXpeW5R9RetnMcM7cWg==","time":1754018575963,"size":116755}