
7178cf19293508ee4bc280862dec7f2a77a9f981	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.152.1754018536329.js\",\"contentHash\":\"91b5cc79aa947a1eb253ae2a20bb2542\"}","integrity":"sha512-4FOPpXoC0d+wl1CRhKOolMO8ReETbOUk/08hrTmpNPu9PL0BvNoSqfk6oNzO6SOzRQuTl5dK7zLAbTnJztg/qQ==","time":1754018575960,"size":62121}