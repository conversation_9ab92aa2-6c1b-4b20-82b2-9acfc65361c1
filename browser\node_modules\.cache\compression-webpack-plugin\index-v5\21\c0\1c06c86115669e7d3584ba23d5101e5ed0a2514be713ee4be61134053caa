
5ae5403235c390a89ac94e71ab63223920f5b069	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.467.1754018536329.js\",\"contentHash\":\"03d0f0f25e33d605f66d508f59083d04\"}","integrity":"sha512-wxwEK47PSbpLn+XOanG4aGeEQz3FNYcxWD0cFW/Gb08Vg4k9nNENzqvegqK7/lqm9FsdPRqI2/1qk4M2O9FK2A==","time":1754018575957,"size":18031}