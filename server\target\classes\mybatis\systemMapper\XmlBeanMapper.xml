<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.klaw.dao.systemDao.XmlBeanMapper">

	<!--更新数据状态-->
	<update id="updateDataStatusById" parameterType="com.klaw.entity.systemBean.TaskDataStatus">
		update ${tableName}
		<set>
			<if test="tableName == 'sg_project_manage' ">
				<if test="status !=  null  and status != '' ">
					project_state_code=#{status},
				</if>
				<if test="statusName !=  null  and statusName != '' ">
					project_state=#{statusName}
				</if>
			</if>
			<if test="tableName != 'sg_project_manage' ">
				<if test="status !=  null  and status != '' ">
					data_state_code=#{status},
				</if>
				<if test="statusName !=  null  and statusName != '' ">
					data_state=#{statusName}
				</if>
			</if>
		</set>
		<if test="tableName == 'sg_project_manage' ">
			where code = #{dataId}
		</if>
		<if test="tableName != 'sg_project_manage' ">
			where id = #{dataId}
		</if>
	</update>
</mapper>
