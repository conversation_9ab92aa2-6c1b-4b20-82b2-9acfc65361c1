
174095eb90c9fb63d98236a74a4777fccbdb3818	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.57.1754018536329.js\",\"contentHash\":\"d89a132cb22df16973c4aa7cd5c38c0c\"}","integrity":"sha512-VrP3ZxDHWbLZlvLL1P/x7CMlCoEdbb0jWdLzPygHobQpVpeHzvtTeZOcQVCHpvUZ9KgNNMod2eEEGLrm5mWhQw==","time":1754018575955,"size":46849}