// 颜色
$colors: (
  "primary": #1A5CD7,
  "info-1": #4394e4,
  "info": #4b67af,
  "white": #ffffff,
  "light": #f9f9f9,
  "grey-1": #999999,
  "grey": #666666,
  "dark-1": #5f5f5f,
  "dark": #222222,
  "black-1": #171823,
  "black": #000000,
  "icon": #5cd9e8
);

// 字体大小
$base-font-size: 0.2rem;
$font-sizes: (
  xxs: 0.1,
  //8px
    xs: 0.125,
  //10px
    sm: 0.2875,
  //12px
    md: 0.1625,
  //13px
    lg: 0.175,
  //14px
    xl: 0.2,
  //16px
    xxl: 0.225,
  //18px
    xxxl: 0.25 //20px,,,,
);

// 宽高
.w-100 {
  width: 100%;
}
.h-100 {
  height: 100%;
}

//flex
.d-flex {
  display: flex;
}
.flex-column {
  flex-direction: column;
}
.flex-wrap {
  flex-wrap: wrap;
}
.flex-nowrap {
  flex-wrap: nowrap;
}
$flex-jc: (
  start: flex-start,
  end: flex-end,
  center: center,
  between: space-between,
  around: space-around,
  evenly: space-evenly,
);

$flex-ai: (
  start: flex-start,
  end: flex-end,
  center: center,
  stretch: stretch,
);

.flex-1 {
  flex: 1;
}

//.mt-1 => margin top
//spacing
$spacing-types: (
  m: margin,
  p: padding,
);
$spacing-directions: (
  t: top,
  r: right,
  b: bottom,
  l: left,
);
$spacing-base-size: 0.5rem;
$spacing-sizes: (
  0: 0,
  1: 0.5,
  2: 1,
  3: 1.5,
  4: 2,
  5: 2.5,
);
