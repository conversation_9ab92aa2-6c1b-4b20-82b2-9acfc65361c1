<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.klaw.dao.contractDao.contract.BmContractEffectMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.klaw.entity.contractBean.contract.BmContractEffect">
        <id column="id" property="id" />
        <result column="parent_id" property="parentId" />
        <result column="seal_type" property="sealType" />
        <result column="seal_type_code" property="sealTypeCode" />
        <result column="our_seal_name" property="ourSealName" />
        <result column="our_seal_code" property="ourSealCode" />
        <result column="our_seal_date" property="ourSealDate" />
        <result column="other_seal_date" property="otherSealDate" />
        <result column="contract_take_effect_date" property="contractTakeEffectDate" />
        <result column="contract_take_effect_file" property="contractTakeEffectFile" />
        <result column="effective_conditions" property="effectiveConditions" />
        <result column="seal_user" property="sealUser" />
        <result column="seal_user_id" property="sealUserId" />
        <result column="create_ogn_id" property="createOgnId" />
        <result column="create_ogn_name" property="createOgnName" />
        <result column="create_dept_id" property="createDeptId" />
        <result column="create_dept_name" property="createDeptName" />
        <result column="create_group_id" property="createGroupId" />
        <result column="create_group_name" property="createGroupName" />
        <result column="create_psn_id" property="createPsnId" />
        <result column="create_psn_name" property="createPsnName" />
        <result column="create_org_id" property="createOrgId" />
        <result column="create_org_name" property="createOrgName" />
        <result column="create_psn_full_id" property="createPsnFullId" />
        <result column="create_psn_full_name" property="createPsnFullName" />
        <result column="create_legal_unit_id" property="createLegalUnitId" />
        <result column="create_legal_unit_name" property="createLegalUnitName" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, parent_id, seal_type, seal_type_code, our_seal_name, our_seal_code, our_seal_date, other_seal_date, contract_take_effect_date, contract_take_effect_file, effective_conditions, seal_user, seal_user_id, create_ogn_id, create_ogn_name, create_dept_id, create_dept_name, create_group_id, create_group_name, create_psn_id, create_psn_name, create_org_id, create_org_name, create_psn_full_id, create_psn_full_name, create_legal_unit_id, create_legal_unit_name, create_time, update_time
    </sql>

</mapper>
