<template>
  <el-container direction="vertical" class="container-manage-sg">
    <el-header>
      <el-card>
        <el-input
            v-model="temp.fuzzyValue"
            class="filter_input"
            placeholder="检索条件（律所名称、统一社会信用代码、负责人、传真、电子邮箱）"
            clearable
            @keyup.enter.native="refreshData"
            @clear="refreshData"
        >
          <el-popover slot="prepend" placement="bottom-start" width="1000" trigger="click">
            <el-form ref="queryForm" label-width="100px" size="mini">
              <el-row>
                <el-col :span="12">
                  <el-form-item label="律所名称">
                    <el-input v-model="temp.lawyerFirm" placeholder="请输入..." style="width:100%"
                              clearable/>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="负责人">
                    <el-input v-model="temp.functionary" placeholder="请输入..." style="width:100%" clearable/>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="12" class="myLabel">
                  <el-form-item label="统一社会信用代码" class="custom-word-break">
                    <span slot="label">统一社会<br>信用代码</span>
                    <el-input v-model="temp.licenseCode" placeholder="请输入..." style="width:100%" clearable/>
                  </el-form-item>
                </el-col>
<!--                <el-col :span="12">-->
<!--                  <el-form-item label="所属库">-->
<!--                    <el-select v-model="temp.typeCode" placeholder="请选择" style="width:100%" clearable-->
<!--                               @keyup.enter.native="refreshData" @clear="refreshData">-->
<!--                      <el-option-->
<!--                          v-for="item in this.selectData"-->
<!--                          :key="item.code"-->
<!--                          :label="item.name"-->
<!--                          :value="item.code"-->
<!--                      />-->
<!--                    </el-select>-->
<!--                  </el-form-item>-->
<!--                </el-col>-->
              </el-row>
              <el-row>
                <el-col :span="12">
                  <el-form-item label="状态">
                    <el-select v-model="temp.dataState" placeholder="请选择" style="width:100%" clearable
                               @keyup.enter.native="refreshData" @clear="refreshData">
                      <el-option
                          v-for="item in this.utils.dataState_BPM_data"
                          :key="item.code"
                          :label="item.name"
                          :value="item.name"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="经办日期">
                    <el-row>
                      <el-col :span="11">
                        <el-date-picker
                            v-model="temp.beginTime"
                            type="date"
                            placeholder="选择日期"
                            style="width: 100%"
                            format="yyyy-MM-dd"
                            value-format="yyyy-MM-dd"
                        />
                      </el-col>
                      <el-col :span="2" style="text-align: center;">至</el-col>
                      <el-col :span="11">
                        <el-date-picker
                            v-model="temp.endTime"
                            type="date"
                            placeholder="选择日期"
                            style="width: 100%"
                            format="yyyy-MM-dd"
                            value-format="yyyy-MM-dd"
                        />
                      </el-col>
                    </el-row>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-button-group style="float: right">
                <el-button type="primary" size="mini" icon="el-icon-search" @click="search_">搜索</el-button>
                <el-button type="primary" size="mini" icon="el-icon-refresh-left" @click="empty_">重置</el-button>
              </el-button-group>
            </el-form>
            <el-button slot="reference" size="mini" type="primary">高级检索</el-button>
          </el-popover>
          <el-button slot="append" icon="el-icon-search" @click="search_"/>
        </el-input>
      </el-card>
    </el-header>

    <el-main>
      <SimpleBoard2 :title="'律所入库信息'">
        <el-table
              ref="table"
              :data="tableData"
              border
              :show-overflow-tooltip="true"
              style="width: 100%"
              :height="table_height"
              stripe
              fit
              highlight-current-row
              @sort-change="tableSort"
              @row-dblclick="rowDblclick"
          >
            <el-table-column label="序号" type="index" align="center" width="50" />
<!--            <el-table-column-->
<!--                v-if="ss.tableColumns.find(item => item.key === 'operatingType').visible"-->
<!--                label="入库类型"-->
<!--                min-width="100"-->
<!--                prop="operatingType"-->
<!--                align="center"-->
<!--                show-overflow-tooltip sortable="custom">-->
<!--              <template slot-scope="scope">-->
<!--                <span>{{ scope.row.operatingType | operatingTypeFilter }}</span>-->
<!--              </template>-->
<!--            </el-table-column>-->
            <el-table-column
                v-if="ss.tableColumns.find(item => item.key === 'lawFirmType').visible"
                label="律所类型"
                prop="lawFirmType"
                align="center"
                min-width="160"
                show-overflow-tooltip sortable="custom"
            />
            <el-table-column
                v-if="ss.tableColumns.find(item => item.key === 'lawyerFirm').visible"
                label="律所名称"
                prop="lawyerFirm"
                align="center"
                min-width="160"
                show-overflow-tooltip sortable="custom"
            />
            <el-table-column
                v-if="ss.tableColumns.find(item => item.key === 'licenseCode').visible"
                label="统一社会信用代码" prop="licenseCode"
                min-width="145"
                align="center"
                show-overflow-tooltip sortable="custom"
            />
            <el-table-column
                v-if="ss.tableColumns.find(item => item.key === 'functionary').visible"
                label="负责人"
                prop="functionary"
                align="center"
                min-width="80"
                show-overflow-tooltip sortable="custom"
            />
            <el-table-column
                v-if="ss.tableColumns.find(item => item.key === 'lawFirmEmail').visible"
                label="电子邮箱"
                prop="lawFirmEmail" min-width="120"
                align="center"
                show-overflow-tooltip sortable="custom"
            />
<!--            <el-table-column-->
<!--                v-if="ss.tableColumns.find(item => item.key === 'typeName').visible"-->
<!--                label="所属库"-->
<!--                prop="typeName"-->
<!--                min-width="80"-->
<!--                align="center"-->
<!--                show-overflow-tooltip sortable="custom"-->
<!--            />-->
            <el-table-column
                v-if="ss.tableColumns.find(item => item.key === 'dataState').visible"
                label="审批状态"
                min-width="80"
                prop="dataState"
                align="center"
                show-overflow-tooltip sortable="custom"
            />
            <el-table-column
                v-if="ss.tableColumns.find(item => item.key === 'createTime').visible"
                label="经办时间"
                min-width="90"
                prop="createTime"
                align="center"
                show-overflow-tooltip sortable="custom"
            >
              <template slot-scope="scope">
                <span>{{scope.row.createTime | parseTime}}</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" align="center" min-width="100" fixed="right">
              <template slot-scope="scope">
                <el-button type="text" @click="view_(scope.$index,scope.row)">查看</el-button>
              </template>
            </el-table-column>
          </el-table>
      </SimpleBoard2>
    </el-main>

    <el-footer>
      <pagination
          align="center"
          :total="temp.total"
          :page.sync="temp.page"
          :limit.sync="temp.limit"
          @pagination="refreshData"
      />
    </el-footer>

  </el-container>
</template>

<script>

import pagination from '../../../components/Pagination/PaginationIndex'
import lawyerFirmApi from '@/api/LawyerManage/LawyerFirm/lawFirmInApproval'
import lawFirmApi from '@/api/LawyerManage/LawyerFirm/lawyerFirm'
import taskApi from '@/api/_system/task'
import SimpleBoard2 from "@/view/components/SimpleBoard/SimpleBoardIndex"
// // vuex状态值
import { mapGetters } from "vuex"

export default {
  name: 'FirmInLedger',
  inject: ['layout'],
  computed: {
    ...mapGetters(["orgContext", "currentFunctionId"])
  },
  components: {pagination, SimpleBoard2},
  filters: {
    operatingTypeFilter(val) {
      let type = ''
      if (val === 'change') {
        type = '律所变更'
      } else if (val === 'in') {
        type = '重新入库'
      } else if (val === 'new') {
        type = '新增律所'
      } else if (val === 'recommend') {
        type = '推荐入库'
      }
      return type
    }
  },
  data() {
    return {
      table_height: '100%', // 定义表格高度
      tableData: null, // 定义表格数据源
      typeName:null,   // 库类别名称
      typeCode:null,  // 库类别代码
      temp: {
        lawyerFirm: null, // 律所名称
        functionary: null, // 负责人
        licenseCode: null, // 统一社会信用代码
        typeCode: null, //库类别代码
        dataState: null, // 状态
        beginTime: null, //开始时间
        endTime: null,  //结束时间
        fuzzyValue: null, // 模糊字段
        sortName: null, // 排序
        order: false,
        page: 1,
        limit: 10,
        total: 0,
        isQuery: true,
        functionId: null,
        orgId: null,
      },
      ss: {
        data: this.tableData,
        tableColumns: [
          {key: 'lawyerFirm', label: '律所名称', visible: true},
          {key: 'licenseCode', label: '统一社会信用代码', visible: true},
          {key: 'functionary', label: '负责人', visible: true},
          {key: 'lawFirmEmail', label: '电子邮箱', visible: true},
          // {key: 'typeName', label: '所属库', visible: true},
          {key: 'operatingType', label: '操作类型', visible: true},
          {key: 'lawFirmType', label: '律所类型', visible: true},
          {key: 'dataState', label: '审批状态', visible: true},
          {key: 'createTime', label: '经办时间', visible: true},
        ]
      },
      taskId: null,
      processInstanceId: null,
      // selectData: [{ code: '0', name: '资源库' }, { code: '1', name: '推荐库' },],
    }
  },
  created() {
    this.refreshData()
    this.isHeadquarter()
  },
  activated() {
    // 长连接页面第二次激活的时候,不会走created方法,会走此方法
    this.refreshData()
  },
  mounted() {
    this.$nextTick(function () {
      this.table_height = window.innerHeight - this.$refs.table.$el.offsetTop - 84 - 65
      // 监听窗口大小变化
      const self = this
      window.onresize = function () {
        self.table_height = window.innerHeight - self.$refs.table.$el.offsetTop - 84 - 65
      }
    })
  },
  methods: {
    isHeadquarter() {
      // const createOgnId = this.orgContext.currentOgnId
      //
      // if (createOgnId === '15033708970596')
      // {
        this.typeCode = '1'
        this.typeName = '推荐库'
      // }
      // else
      // {
      //   this.typeCode = '0'
      //   this.typeName = '资源库'
      // }
    },
    refreshData() {
      const me = this
      this.functionId = this.currentFunctionId.id
      this.temp.orgId = this.orgContext.currentOrgId
      lawFirmApi.query(this.temp).then((res) => {
        me.tableData = res.data.page.records
        me.ss.data = this.tableData
        me.temp.total = res.data.page.total
      })
    },
    search_() {// 查询
      this.refreshData()
    },
    empty_() {
      this.temp = {
        lawyerFirm: null, // 律所名称
        functionary: null, // 负责人
        licenseCode: null, // 统一社会信用代码
        dataState: null, // 状态
        fuzzyValue: null, // 模糊字段
        sortName: null, // 排序
        order: false,
        page: 1,
        limit: 10,
        total: 0,
        isQuery: true,
        functionId: null,
        orgId: null,
      }
      this.refreshData()
    },
    refresh_() {
      this.temp.sortName = null
      this.empty_()
    },
    tableSort(column, prop, order) {
      this.temp.sortName = column.prop
      this.temp.order = column.order === 'ascending'
      this.refreshData()
    },
    rowDblclick(row, column, event) {
      if(row.dataStateCode !== this.utils.dataState_BPM.SAVE.code)
      {
        taskApi.selectTaskId({businessKey: row.id, isView: 'true'}).then(res=>{
          const functionId = res.data.data[0].ID
          const tabId = this.utils.createUUID()
          this.layout.openNewTab("入库信息",
              "design_page",
              "design_page",
              tabId,
              {
                processInstanceId: res.data.data[0].PID,//流程实例
                taskId: res.data.data[0].ID,//任务ID
                businessKey: row.id, //业务数据ID
                functionId:functionId, //不传也可以，只有首环节需要传，实例启动就可以不传了
                entranceType: "FLOWABLE",
                type: "haveDealt",
                view: 'new'
              }
          )
        })
      }
      else
      {
        let functionCode = null
        if (row.operatingType === 'new')
        {
          functionCode = 'firm_main_new'
        }
        else if (row.operatingType === 'change' || row.operatingType === 'in')
        {
          functionCode = 'firm_main_change'
        } else if (row.operatingType === 'recommend')
        {
          functionCode = 'firm_main_recommend'
        }
        taskApi.selectFunctionId({functionCode: functionCode}).then(res=>{
          const functionId = res.data.data[0].ID
          const tabId = this.utils.createUUID()
          this.layout.openNewTab("入库信息",
              "design_page",
              "design_page",
              tabId,
              {
                functionId:functionId,
                entranceType: "FLOWABLE",
                ...this.utils.routeState.VIEW(row.id),
                channel: 'business',
                view: 'old'
              }
          )
        })
      }
    },
    view_(index, row) {
      if(row.dataStateCode !== this.utils.dataState_BPM.SAVE.code)
      {
        taskApi.selectTaskId({businessKey: row.id, isView: 'true'}).then(res=>{
          if (res.data.data.length > 0) {
            const functionId = res.data.data[0].ID
            const tabId = this.utils.createUUID()
            this.layout.openNewTab("入库信息",
                "design_page",
                "design_page",
                tabId,
                {
                  processInstanceId: res.data.data[0].PID,//流程实例
                  taskId: res.data.data[0].ID,//任务ID
                  businessKey: row.id, //业务数据ID
                  functionId:functionId, //不传也可以，只有首环节需要传，实例启动就可以不传了
                  entranceType: "FLOWABLE",
                  type: "haveDealt",
                  view: 'new'
                }
            )
          } else {
            let functionCode = 'firm_main_new_detail';
            // if (row.operatingType === 'new')
            // {
            //   functionCode = 'firm_main_new'
            // }
            // else if (row.operatingType === 'change' || row.operatingType === 'in')
            // {
            //   functionCode = 'firm_main_change'
            // } else if (row.operatingType === 'recommend')
            // {
            //   functionCode = 'firm_main_recommend'
            // }
            taskApi.selectFunctionId({functionCode: functionCode}).then(res=>{
              // const functionId = res.data.data[0].ID
              const tabId = this.utils.createUUID()
              this.layout.openNewTab("入库信息",
                  functionCode,
                  functionCode,
                  tabId,
                  {
                    functionId: functionCode + "," + tabId,
                    ...this.utils.routeState.VIEW(row.id),
                  }
              )
            })
          }
        })
      }
    },
  }
}

</script>

<style scoped>
.myLabel .el-form-item--mini .el-form-item__label {
  line-height: 15px;
}
</style>