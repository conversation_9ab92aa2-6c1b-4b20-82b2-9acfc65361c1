package com.klaw.service.imp.caseServiceImpl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.klaw.constant.DataState;
import com.klaw.constant.DataStateBPM;
import com.klaw.dao.caseDao.CaseExamineMapper;
import com.klaw.entity.authorizationBean.Authorization;
import com.klaw.entity.caseBean.CaseExamine;
import com.klaw.entity.contractBean.SgSealApproval;
import com.klaw.entity.contractBean.SgSealApprovalDetail;
import com.klaw.entity.contractBean.contract.BmContractSeal;
import com.klaw.service.authorizationService.AuthorizationService;
import com.klaw.service.caseService.CaseExamineService;
import com.klaw.service.contractService.SgSealApprovalDetailService;
import com.klaw.service.contractService.SgSealApprovalService;
import com.klaw.service.contractService.contract.BmContractSealService;
import com.klaw.utils.StringUtil;
import com.klaw.vo.Json;
import com.klaw.vo.OaDeal;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Service
@Transactional(rollbackFor = Exception.class)
public class CaseExamineServiceImpl extends ServiceImpl<CaseExamineMapper, CaseExamine> implements CaseExamineService {
    @Resource
    private CaseExamineMapper caseExamineMapper;
    @Resource
    private BmContractSealService bmContractSealService;
    @Resource
    private SgSealApprovalService sgSealApprovalService;
    @Resource
    private SgSealApprovalDetailService sgSealApprovalDetailService;
    @Resource
    private AuthorizationService authorizationService;

    @Override
    public void saveData(CaseExamine caseExamine)  {
        //插入或更新案件审批数据
        saveOrUpdate(caseExamine);
    }

    @Override
    public CaseExamine queryDataById(String id) {
        return getById(id);
    }

    @Override
    public List<Map<String,String>> queryIdAndName(String caseId) {
        return caseExamineMapper.queryIdAndName(caseId);
    }

    @Override
    public List<CaseExamine> queryInfoByCaseId(String caseProcessId) {
        return caseExamineMapper.queryInfoByCaseId(caseProcessId);
    }

    @Override
    public boolean finish(String dataId, Boolean isOK) {
        CaseExamine caseExamine = getById(dataId);
        caseExamine.setDataStateCode(isOK ? DataState.FINISH.getKey() : DataState.RETURNED.getKey());
        caseExamine.setDataState(isOK ? DataState.FINISH.getValue() : DataState.RETURNED.getValue());
        boolean b = updateById(caseExamine);

        return b;
    }

    @Override
    public void updateDataState(List<OaDeal> list) {
        caseExamineMapper.updateDataState(list);
    }

    @Override
    public void saveDataFinish(String businessKey) {
        CaseExamine caseExamine = queryDataById(businessKey);
        sealStamp(caseExamine);
        authorization(caseExamine);
    }
    public void sealStamp(CaseExamine caseExamine) {
        String uuid = StringUtil.makeUUID();
        List<SgSealApprovalDetail> detailList = new ArrayList();
        String sealNames = "";
        String sealIds = "";
        String sealTypes = "";
        String sealTypeIds = "";
        String sealAdmins = "";
        String sealAdminIds = "";

        List<BmContractSeal> list = bmContractSealService.list(new QueryWrapper<BmContractSeal>().eq("parent_id", caseExamine.getId()));
        if(!org.springframework.util.CollectionUtils.isEmpty(list)){
            for (BmContractSeal bmContractSeal : list) {
                if("".equals(sealNames)){
                    sealNames = bmContractSeal.getSealName();
                    sealIds = bmContractSeal.getSealId();
                }else{
                    sealNames += "、" + bmContractSeal.getSealName();
                    sealIds += "," + bmContractSeal.getSealId();
                }
                if("".equals(sealTypes)){
                    sealTypes = bmContractSeal.getSealType();
                    sealTypeIds = bmContractSeal.getSealTypeId();
                }else{
                    sealTypes += "、" + bmContractSeal.getSealType();
                    sealTypeIds += "," + bmContractSeal.getSealTypeId();
                }
                if("".equals(sealAdmins)){
                    sealAdmins = bmContractSeal.getSealAdmin();
                    sealAdminIds = bmContractSeal.getSealAdminId();
                }else{
                    sealAdmins += "、" + bmContractSeal.getSealAdmin();
                    sealAdminIds += "," + bmContractSeal.getSealAdminId();
                }

                SgSealApprovalDetail sgSealApprovalDetail = new SgSealApprovalDetail();
                sgSealApprovalDetail.setId(StringUtil.makeUUID());
                sgSealApprovalDetail.setParentId(uuid);

                sgSealApprovalDetail.setSealName(bmContractSeal.getSealName());
                sgSealApprovalDetail.setSealId(bmContractSeal.getSealId());
                sgSealApprovalDetail.setSealType(bmContractSeal.getSealType());
                sgSealApprovalDetail.setSealTypeId(bmContractSeal.getSealTypeId());
                sgSealApprovalDetail.setSealNumberOld(String.valueOf(bmContractSeal.getSealNumber()));
                sgSealApprovalDetail.setSealNumber(String.valueOf(bmContractSeal.getSealNumber()));
                sgSealApprovalDetail.setPrintsNumberOld(String.valueOf(bmContractSeal.getPrintsNumber()));
                sgSealApprovalDetail.setPrintsNumber(String.valueOf(bmContractSeal.getPrintsNumber()));
                sgSealApprovalDetail.setSealAdminOld(bmContractSeal.getSealAdmin());
                sgSealApprovalDetail.setSealAdminIdOld(bmContractSeal.getSealAdminId());
                sgSealApprovalDetail.setDataState("待用印");
                sgSealApprovalDetail.setDataStateCode(0);
                if ("法人签字".equals(sealNames)) {
                    sgSealApprovalDetail.setDataState("已用印");
                    sgSealApprovalDetail.setDataStateCode(4);
                    sgSealApprovalDetail.setSealNumber(String.valueOf(bmContractSeal.getSealNumber()));
                    sgSealApprovalDetail.setPrintsNumber(String.valueOf(bmContractSeal.getPrintsNumber()));
                    sgSealApprovalDetail.setOurSealTime(new Date());
                }
                sgSealApprovalDetail.setCreateTime(new Date());
                detailList.add(sgSealApprovalDetail);
            }
        }
        SgSealApproval sgSealApproval = new SgSealApproval();
        sgSealApproval.setId(uuid);
        sgSealApproval.setContractName(caseExamine.getItemName());
        sgSealApproval.setContractId(caseExamine.getId());
        sgSealApproval.setContractCode(caseExamine.getItemNumber());
        sgSealApproval.setSealPurpose("授权审批");
        sgSealApproval.setSealPurposeId("6");
        sgSealApproval.setSealNames(sealNames);
        if (!"法人签字".equals(sealNames)){
            sgSealApproval.setTakeEffectCode(DataStateBPM.TAKE_EFFECT_1.getKey());
            sgSealApproval.setTakeEffectName(DataStateBPM.TAKE_EFFECT_1.getValue());
        }else{
            sgSealApproval.setTakeEffectCode(DataStateBPM.TAKE_EFFECT_2.getKey());
            sgSealApproval.setTakeEffectName(DataStateBPM.TAKE_EFFECT_2.getValue());
        }
        sgSealApproval.setSealNames(sealNames);
        sgSealApproval.setSealIds(sealIds);
        sgSealApproval.setSealTypes(sealTypes);
        sgSealApproval.setSealTypeIds(sealTypeIds);
        sgSealApproval.setSealAdmins(sealAdmins);
        sgSealApproval.setSealAdminIds(sealAdminIds);
        sgSealApproval.setCreateOgnId(caseExamine.getCreateOgnId());
        sgSealApproval.setCreateOgnName(caseExamine.getCreateOgnName());
        sgSealApproval.setCreateDeptId(caseExamine.getCreateDeptId());
        sgSealApproval.setCreateDeptName(caseExamine.getCreateDeptName());
        sgSealApproval.setCreatePsnId(caseExamine.getCreatePsnId());
        sgSealApproval.setCreatePsnName(caseExamine.getCreatePsnName());
        sgSealApproval.setCreatePsnFullId(caseExamine.getCreatePsnFullId());
        sgSealApproval.setCreatePsnFullName(caseExamine.getCreatePsnFullName());
        sgSealApproval.setCreateOrgId(caseExamine.getCreateOrgId());
        sgSealApproval.setCreateOrgName(caseExamine.getCreateOrgName());
        sgSealApproval.setCreateGroupId(caseExamine.getCreateGroupId());
        sgSealApproval.setCreateGroupName(caseExamine.getCreateGroupName());
        sgSealApproval.setCreateTime(new Date());
        sgSealApproval.setDataState(DataStateBPM.FINISH.getValue());
        sgSealApproval.setDataStateCode(DataStateBPM.FINISH.getKey());
        sgSealApprovalService.saveOrUpdate(sgSealApproval);
        if(!org.springframework.util.CollectionUtils.isEmpty(detailList)){
            sgSealApprovalDetailService.saveOrUpdateBatch(detailList);
        }
    }

    private void authorization(CaseExamine caseExamine)
    {
        String id = caseExamine.getAuthorizationId();

        if (id != null)
        {
            Authorization authorization = authorizationService.getById(id);

            if (authorization != null)
            {
                authorization.setDataState("审批完成");
                authorization.setDataStateCode(5);
                authorization.setAuthStatus("正常");
                authorization.setSublicenseStatus("未生效");
                authorization.setArchive("未归档");
                authorizationService.saveOrUpdate(authorization);
            }
        }
    }
}
