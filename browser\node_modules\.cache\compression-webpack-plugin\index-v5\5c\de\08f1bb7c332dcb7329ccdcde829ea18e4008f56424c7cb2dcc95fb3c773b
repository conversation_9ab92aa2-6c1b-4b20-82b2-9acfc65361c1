
cd5a24182e27090fd19dc13778ad13e0eff6c23f	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.423.1754018536329.js\",\"contentHash\":\"41f8f7aa14176e140024f0a3517c2284\"}","integrity":"sha512-LRtRiw+TPQthG6A0J23O14IRG8NxzwqaBM4XLAaf2iQrMJwPB7Memwe1HRrOhyYqncRfMLpiz5ACyN/Zr/2m4Q==","time":1754018575976,"size":68729}