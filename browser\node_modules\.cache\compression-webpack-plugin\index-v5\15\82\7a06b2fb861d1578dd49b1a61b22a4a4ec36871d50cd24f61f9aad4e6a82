
2095aa27fb63603730b0825a34b88505e5f918ff	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.55.1754018536329.js\",\"contentHash\":\"a83346ce4ca7f081f10b05c82d254fc5\"}","integrity":"sha512-7pPA1EMVAbZ5nn+9HLsN+TYtJgmrKI/1MJYMcQJIxG/O8xBXdl14SRGQ9Wr+iJhIRMEMa13R9zuja7CRbytJeg==","time":1754018575978,"size":149104}