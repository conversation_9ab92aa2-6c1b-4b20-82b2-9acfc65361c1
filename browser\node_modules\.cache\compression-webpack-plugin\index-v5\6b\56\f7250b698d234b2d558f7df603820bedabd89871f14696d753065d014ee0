
2560e4b6b52bfa29daccceb76ab0480b0109b442	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.422.1754018536329.js\",\"contentHash\":\"1327301a6340ab639f97de3c53650215\"}","integrity":"sha512-3NccPuTvpI5cduVV38f7RvExknMoR8RzKmiCzNq7dffhXyhibBnE8xHhSGrwtYyKORVjxBTvTo+U7fVWrAQ17Q==","time":1754018575957,"size":32294}