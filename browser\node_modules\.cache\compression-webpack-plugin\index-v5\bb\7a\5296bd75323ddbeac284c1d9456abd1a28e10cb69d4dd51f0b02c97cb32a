
de7506fc5d3bdbda8b15527b14bf9cca0c90e55f	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.228.1754018536329.js\",\"contentHash\":\"8063df79636378517892f70dc3271879\"}","integrity":"sha512-XKJWhgL/q3nhzsoQbmr7lEg/uBRRYx7YPCdtDEiA4KlLHG55t8Ex6WqSFjjXq6dn+ZHYKGmD9vn3GXc7sQ0Tbw==","time":1754018575991,"size":166436}