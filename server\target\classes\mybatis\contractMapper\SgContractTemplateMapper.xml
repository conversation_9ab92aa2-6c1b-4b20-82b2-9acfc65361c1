<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.klaw.dao.contractDao.SgContractTemplateMapper">

    <resultMap id="BaseResultMap" type="com.klaw.entity.contractBean.SgContractTemplate">
        <id column="ID" jdbcType="VARCHAR" property="id" />
        <result column="STANDARD_ATTACHMENT_ID" jdbcType="VARCHAR" property="standardAttachmentId" />
        <result column="STANDARD_ATTACHMENT_NAME" jdbcType="VARCHAR" property="standardAttachmentName" />
        <result column="OTHER_PARTY_KEY" jdbcType="VARCHAR" property="otherPartyKey" />
        <result column="OUR_PARTY_KEY" jdbcType="VARCHAR" property="ourPartyKey" />
        <result column="REMARKS" jdbcType="VARCHAR" property="remarks" />
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
        <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="DATA_STATE" jdbcType="VARCHAR" property="dataState" />
        <result column="DATA_STATE_CODE" jdbcType="DECIMAL" property="dataStateCode" />
        <result column="STANDARD_ATTACHMENT_CODE" jdbcType="VARCHAR" property="standardAttachmentCode" />
        <result column="FINAL_PARTY_KEY" jdbcType="VARCHAR" property="finalPartyKey" />
        <result column="PSN_PARTY_KEY" jdbcType="VARCHAR" property="psnPartyKey" />
        <result column="CREATE_OGN_ID" jdbcType="VARCHAR" property="createOgnId" />
        <result column="CREATE_OGN_NAME" jdbcType="VARCHAR" property="createOgnName" />
        <result column="CREATE_DEPT_ID" jdbcType="VARCHAR" property="createDeptId" />
        <result column="CREATE_DEPT_NAME" jdbcType="VARCHAR" property="createDeptName" />
        <result column="CREATE_GROUP_ID" jdbcType="VARCHAR" property="createGroupId" />
        <result column="CREATE_GROUP_NAME" jdbcType="VARCHAR" property="createGroupName" />
        <result column="CREATE_PSN_ID" jdbcType="VARCHAR" property="createPsnId" />
        <result column="CREATE_PSN_NAME" jdbcType="VARCHAR" property="createPsnName" />
        <result column="CREATE_ORG_ID" jdbcType="VARCHAR" property="createOrgId" />
        <result column="CREATE_ORG_NAME" jdbcType="VARCHAR" property="createOrgName" />
        <result column="CREATE_PSN_FULL_ID" jdbcType="VARCHAR" property="createPsnFullId" />
        <result column="CREATE_PSN_FULL_NAME" jdbcType="VARCHAR" property="createPsnFullName" />
        <result column="CREATE_LEGAL_UNIT_ID" jdbcType="VARCHAR" property="createLegalUnitId" />
        <result column="CREATE_LEGAL_UNIT_NAME" jdbcType="VARCHAR" property="createLegalUnitName" />
        <result column="CONTRACT_VERSION" jdbcType="VARCHAR" property="contractVersion" />
        <result column="ATTACHMENT" jdbcType="VARCHAR" property="attachment" />
    </resultMap>

    <sql id="Base_Column_List">
        ID, STANDARD_ATTACHMENT_ID, STANDARD_ATTACHMENT_NAME, OTHER_PARTY_KEY, OUR_PARTY_KEY, REMARKS,
        CREATE_TIME, DATA_STATE, DATA_STATE_CODE, STANDARD_ATTACHMENT_CODE, FINAL_PARTY_KEY, PSN_PARTY_KEY,
        CREATE_OGN_ID, CREATE_OGN_NAME, CREATE_DEPT_ID, CREATE_DEPT_NAME, CREATE_GROUP_ID, CREATE_GROUP_NAME,
        CREATE_PSN_ID, CREATE_PSN_NAME, CREATE_ORG_ID, CREATE_ORG_NAME, CREATE_PSN_FULL_ID, CREATE_PSN_FULL_NAME,
        CREATE_LEGAL_UNIT_ID, CREATE_LEGAL_UNIT_NAME, UPDATE_TIME, CONTRACT_VERSION
    </sql>

    <sql id="Map_Column_List">
        SC.ID, SC.STANDARD_ATTACHMENT_ID, SC.STANDARD_ATTACHMENT_NAME, SC.OTHER_PARTY_KEY, SC.OUR_PARTY_KEY, SC.REMARKS,
        SC.CREATE_TIME, SC.DATA_STATE, SC.DATA_STATE_CODE, SC.STANDARD_ATTACHMENT_CODE, SC.FINAL_PARTY_KEY, SC.PSN_PARTY_KEY,
        SC.CREATE_OGN_ID, SC.CREATE_OGN_NAME, SC.CREATE_DEPT_ID, SC.CREATE_DEPT_NAME, SC.CREATE_GROUP_ID, SC.CREATE_GROUP_NAME,
        SC.CREATE_PSN_ID, SC.CREATE_PSN_NAME, SC.CREATE_ORG_ID, SC.CREATE_ORG_NAME, SC.CREATE_PSN_FULL_ID, SC.CREATE_PSN_FULL_NAME,
        SC.CREATE_LEGAL_UNIT_ID, SC.CREATE_LEGAL_UNIT_NAME, SC.UPDATE_TIME, SC.CONTRACT_VERSION,
        SCT.ATTACHMENT
    </sql>

    <select id="getSgContractTemplateByParentId" resultMap="BaseResultMap">
        SELECT <include refid="Map_Column_List"/>
        FROM SG_CONTRACT_TEMPLATE SC
        INNER JOIN SG_CONTRACT_TEXT SCT
            ON SC.STANDARD_ATTACHMENT_CODE  = SCT.STANDARD_ATTACHMENT_CODE
        WHERE EXISTS (
                SELECT SCT.ID FROM SG_CONTRACT_APPROVAL SCA
                WHERE SCA.ID = SCT.PARENT_ID AND SCA.ID = #{parentId}
            )
          AND SCT.FILE_TYPE_CODE  ='0' AND SCT.FILE_FROM_CODE = '1'
          AND SC.DATA_STATE_CODE = 1
    </select>
</mapper>