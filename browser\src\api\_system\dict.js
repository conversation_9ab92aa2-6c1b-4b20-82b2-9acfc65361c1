import {request} from '@/api/index'

export default {

  queryDic(data) {
    return request({
      url: '/sys_dict/queryDic',
      method: 'post',
      data
    })
  },
  queryDicNode(data) {
    return request({
      url: '/sys_dict/queryDicNode',
      method: 'post',
      data: {
        ...data,
        current: data.page,
        size: data.limit
      }
    })
  },
  queryDicSort(data) {
    return request({
      url: '/sys_dict/queryDicSort',
      method: 'post',
      data
    })
  },
  showTable(data) {
    return request({
      url: '/sys_dict/showTable',
      method: 'post',
      data: {
        ...data,
        current: data.page,
        size: data.limit
      }
    })
  },
  updateStatus(data) {
    return request({
      url: 'sys_dict/updateStatus',
      method: 'post',
      data
    })
  },
  queryCode(data) {
    return request({
      url: 'sys_dict/queryCode',
      method: 'post',
      data
    })
  },
  addDict(data) {
    return request({
      url: 'sys_dict/addDict',
      method: 'post',
      data
    })
  },
  addDict2(data) {
    return request({
      url: 'sys_dict/addDict2',
      method: 'post',
      data
    })
  },
  addDictArr(data) {
    return request({
      url: 'sys_dict/addDictArr',
      method: 'post',
      data
    })
  },
  updateIsLeaf(data) {
    return request({
      url: 'sys_dict/updateIsLeaf',
      method: 'post',
      data
    })
  },
  updateDict(data) {
    return request({
      url: 'sys_dict/updateDict',
      method: 'post',
      data
    })
  },
  searchDic(data) {
    return request({
      url: 'sys_dict/searchDic',
      method: 'post',
      data
    })
  },
  showSelect(data) {
    return request({
      url: 'sys_dict/showSelect',
      method: 'post',
      data
    })
  },
  showAllSelect(data) {
    return request({
      url: 'sys_dict/showAllSelect',
      method: 'post',
      data
    })
  },
  showregion() {
    return request({
      url: 'sys_dict/showregion',
      method: 'post'

    })
  },
  showAllSelectbyid(data) {
    return request({
      url: 'sys_dict/showAllSelectbyid',
      method: 'post',
      data
    })
  },

  queryDicData(queryString, parentId) {
    return request({
      url: 'sys_dict/queryDicData',
      method: 'post',
      data: {
        queryString: queryString,
        parentId: parentId
      }
    })
  },
  updateSort(data) {
    return request({
      url: 'sys_dict/updateSort',
      method: 'post',
      data
    })
  },
  querySeachData(data) {
    return request({
      url: '/sys_dict/query_seach_data',
      method: 'post',
      data
    })
  },
  queryTreeData(data) {
    return request({
      url: '/sys_dict/queryTreeData',
      method: 'post',
      data: {
        dicCode: data
      }
    })
  },
  queryTreeData1(data) {
    return request({
      url: '/sys_dict/queryTreeData1',
      method: 'post',
      data: {
        dicCode: data
      }
    })
  },
  queryDataByArray(data) {
    return request({
      url: '/sys_dict/queryDataByArray',
      method: 'post',
      data
    })
  },

  queryAreaData(data) {
    return request({
      url: '/sys_dict/queryAreaData',
      method: 'post',
      data
    })
  },

  queryGHXData(data) {
    return request({
      url: '/sys_dict/queryGHXData',
      method: 'post',
      data
    })
  },

  querySolid(data) {
    return request({
      url: '/sys_dict/querySolid',
      method: 'post',
      data
    })
  },

  querySolidByCode(data) {
    return request({
      url: '/sys_dict/querySolidByCode',
      method: 'post',
      data
    })
  },

  queryData(data) {
    return request({
      url: '/sys_dict/queryData',
      method: 'post',
      data
    })
  },

  solidItem(data) {
    return request({
      url: '/sys_dict/solidItem',
      method: 'post',
      data
    })
  },

  deleteSolidItem(data) {
    return request({
      url: '/sys_dict/deleteSolidItem',
      method: 'post',
      data
    })
  },
  solidifiedModeChange(data) {
    return request({
      url: '/sys_dict/solidifiedModeChange',
      method: 'post',
      data
    })
  },
  cancelSolidItem(data) {
    return request({
      url: '/sys_dict/cancelSolidItem',
      method: 'post',
      data
    })
  },
}
