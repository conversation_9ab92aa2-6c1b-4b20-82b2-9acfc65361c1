
52de491e09cace206bf9d89bec1659f55d1abdd9	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.158.1754018536329.js\",\"contentHash\":\"61cf2a4836983abac25b5d63c8594821\"}","integrity":"sha512-3crWPHGimV61cOiQcJO5uN0zxJ00oV13wKZitvFd+dHn9MmPEuQPHtElxH02+YvQRzJI1j7oIu7mjW/VNVRRYQ==","time":1754018575960,"size":117437}