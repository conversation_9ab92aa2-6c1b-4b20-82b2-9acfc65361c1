
a19a9f31bcc90c85095fc418d882c0b5d7cf185a	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.188.1754018536329.js\",\"contentHash\":\"0735178c2a0e196a69d0d7581ad89d63\"}","integrity":"sha512-8ChAirczq88e4h94sgU8w90HyvbAveNd7D6xWjFZ6utYgceWjTvzG27KzoNX5VH4G17t6XbAMwiCLup8+jRyVA==","time":1754018576097,"size":270652}