
36e9fb2f1d85e11a980099bf1240eace0c3fcffa	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.371.1754018536329.js\",\"contentHash\":\"8d2f087710cda78c9eaa78fdd52de955\"}","integrity":"sha512-9In4a1JFSCpZZCmSN8+T5P/92N3I3KmWoBTyZsnHeIxO6gyhVahrgG0amC/M6XPT04bDaofcXICs/44G5pjxcA==","time":1754018575975,"size":95232}