
2609f933c4a893cc71ca532af45edc08df9ec5e2	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.447.1754018536329.js\",\"contentHash\":\"9597be1c9a20a46264a4807416797472\"}","integrity":"sha512-FancaLfJAJP6zuJ9KgTw/8MM08mDqvICdyqwtJjVbvvkFxYE1Rlly2BnwzbuGmUa0px+QkIUbwMWee6Pejz0pg==","time":1754018575957,"size":23748}