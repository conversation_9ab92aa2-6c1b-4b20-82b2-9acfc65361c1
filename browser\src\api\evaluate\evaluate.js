import {request} from '@/api/index'

export default {
    query(data) {
        return request({
            url: '/contractEvaluate/query',
            method: 'post',
            data
        })
    },
    queryDataById(data) {
        return request({
            url: '/contractEvaluate/queryDataById',
            method: 'post',
            data
        })
    },
    queryDataByPsnId(data) {
        return request({
            url: '/contractEvaluate/queryDataByPsnId',
            method: 'post',
            data
        })
    },
    queryBusinessList(data) {
        return request({
            url: '/contractEvaluate/queryBusinessList',
            method: 'post',
            data
        })
    },
    save(data) {
        return request({
            url: '/contractEvaluate/save',
            method: 'post',
            data
        })
    },
    saveByPerformComplete(data) {
        return request({
            url: '/contractEvaluate/saveByPerformComplete',
            method: 'post',
            data
        })
    },
    delete(data) {
        return request({
            url: '/contractEvaluate/delete',
            method: 'post',
            data
        })
    },
    getDetailList(data) {
        return request({
            url: '/contractEvaluate/getDetailList',
            method: 'post',
            data
        })
    },
    grade(data) {
        return request({
            url: '/contractEvaluate/grade',
            method: 'post',
            data
        })
    },
    queryData(data) {
        return request({
            url: '/contractEvaluate/queryData',
            method: 'post',
            data
        })
    },
    queryEvaluateDialog(data){
        return request({
            url: '/contractEvaluate/queryEvaluateDialog',
            method: 'post',
            data
        })
    }
}
