
8511ef28444b6607274b3329bacff5487a285a35	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.332.1754018536329.js\",\"contentHash\":\"c435ff3323f0bbbbde14e1b0e6f37577\"}","integrity":"sha512-SMm9Xs1FjTPaxN7FrOzmdVyhCahJEggqsVXsTwl2RGRqzGyS/DB6mUU4Glcx5FeLhn3GQ3M9ylmMFrZTpUEx6w==","time":1754018575975,"size":109014}