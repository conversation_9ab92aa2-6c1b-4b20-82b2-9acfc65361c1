
1371fc398dc36cd6d497b740a6fe467441d6700b	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.0.1754018536329.js\",\"contentHash\":\"47086b0918c2686379f1cbab94f09217\"}","integrity":"sha512-xUK+GDkmGRZ3r0GZrv7nTT/T+lgMaJ2c7XI7ZdkNIrHVSqsM7fvfYzGaHHi6Cx2VjNFX0j5H+U9QjouKaCpy4Q==","time":1754018575955,"size":37800}