
0b01d5df6c901c1fbb022d07a2afb7df0ce811a7	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.352.1754018536329.js\",\"contentHash\":\"2ee9f568e179d30b03a29d476557ecc2\"}","integrity":"sha512-5tLHbyXlWTVGj9hZGREYSleEWKU0qhXsgPoIQWN/+uG4HO73SkIMqhQKEn+LcdRd8QaNI0FT6K3EsjHvQK03Wg==","time":1754018576018,"size":140465}