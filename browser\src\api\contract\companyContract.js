import {request} from '@/api'

export default {

    querySeachData(data) {
        return request({
            url: '/CompanyContractTest/query_seach_data',
            method: 'post',
            data
        })
    },

    getCon(data) {
        return request({
            url: '/CompanyContractTest/getCon',
            method: 'post',
            data
        })
    },
    getConTwo(data) {
        return request({
            url: '/CompanyContractTest/getConTwo',
            method: 'post',
            data
        })
    },

    query(data) {
        return request({
            url: '/CompanyContractTest/query',
            method: 'post',
            data
        })
    },

    queryTable(data) {
        return request({
            url: '/CompanyContractTest/queryTable',
            method: 'post',
            data
        })
    },


    queryStatus(data) {
        return request({
            url: '/CompanyContractTest/queryStatus',
            method: 'post',
            data
        })
    },
    addText(data) {
        return request({
            url: '/CompanyContractTest/addCompanyContract',
            method: 'post',
            data
        })
    },
    updateText(data) {
        return request({
            url: '/CompanyContractTest/updateText',
            method: 'post',
            data
        })
    },

    distinct(data) {
        return request({
            url: '/CompanyContractTest/distinct',
            method: 'post',
            data
        })
    },

}
