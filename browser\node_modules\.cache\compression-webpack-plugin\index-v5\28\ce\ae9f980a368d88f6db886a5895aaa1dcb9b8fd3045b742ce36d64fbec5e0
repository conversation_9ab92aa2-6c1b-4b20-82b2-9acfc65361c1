
33341444fec2ef2f7bb32c9a5b5fc55a7b100b3a	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.227.1754018536329.js\",\"contentHash\":\"8f74e97bdd8b2a2c572d8d045062e20d\"}","integrity":"sha512-kpagkmQuFy9pXbi+5Y3q181oyMJlLm8zny2olyoah6W2Vqnm5bwg1miHWL3KvecZLvnX2knTg/QslhGXbH/X2w==","time":1754018575993,"size":165764}