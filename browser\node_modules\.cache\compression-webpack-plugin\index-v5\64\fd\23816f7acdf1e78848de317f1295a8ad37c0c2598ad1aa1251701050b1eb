
bd8dc892f6f4b4c382fc38a02094bdde87dae220	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.276.1754018536329.js\",\"contentHash\":\"101ca390dfdcd2e046d7e04a9be2cdf2\"}","integrity":"sha512-pqpMmTcOuIXU4EstUcS+CzA6//ujt0JACXn/JkD84oWzQWbaYxsPcloiiH0n0Lsr42s2krQsS5uLrlwELZU+AA==","time":1754018575962,"size":82583}