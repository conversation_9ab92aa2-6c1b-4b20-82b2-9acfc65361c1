package com.klaw.service.imp.complianceReviewServiceImp;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.klaw.constant.DataStateBPM;
import com.klaw.dao.complianceReviewDao.ComplianceReviewMapper;
import com.klaw.entity.authorizationBean.Authorization;
import com.klaw.entity.complianceReviewBean.ComplianceExamination;
import com.klaw.entity.complianceReviewBean.ComplianceReview;
import com.klaw.entity.complianceReviewBean.ComplianceReviewFixedItem;
import com.klaw.entity.contractBean.contract.*;
import com.klaw.service.complianceReviewService.ComplianceExaminationService;
import com.klaw.service.complianceReviewService.ComplianceReviewFixedItemService;
import com.klaw.service.complianceReviewService.ComplianceReviewService;
import com.klaw.utils.DataAuthUtils;
import com.klaw.utils.PageUtils;
import com.klaw.utils.Utils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【compliance_review(合规审查表)】的数据库操作Service实现
 * @createDate 2024-11-26 18:23:57
 */
@Service
public class ComplianceReviewServiceImpl extends ServiceImpl<ComplianceReviewMapper, ComplianceReview>
        implements ComplianceReviewService {

    @Resource
    private ComplianceReviewFixedItemService complianceReviewFixedItemService;

    @Resource
    private ComplianceExaminationService complianceExaminationService;

    @Override
    public Page<ComplianceReview> queryPageData(JSONObject jsonObject) {
        QueryWrapper<ComplianceReview> queryWrapper = new QueryWrapper<>();
        getFilter(jsonObject, queryWrapper);
        return page(new PageUtils<>(jsonObject), queryWrapper);
    }

    @Override
    public ComplianceReview queryDataById(String id) {
        ComplianceReview complianceReview = getById(id);

        List<ComplianceReviewFixedItem> cReviewFixedItems = complianceReviewFixedItemService.list(new QueryWrapper<ComplianceReviewFixedItem>().eq("parent_id", id));
        if (!CollectionUtils.isEmpty(cReviewFixedItems)) {
            complianceReview.setCheckList(cReviewFixedItems);
        }

        List<ComplianceExamination> complianceExaminations = complianceExaminationService.list(new QueryWrapper<ComplianceExamination>().eq("parent_id", id).orderByAsc("sort"));
        if (!CollectionUtils.isEmpty(complianceExaminations)) {
            complianceReview.setExaminationLists(complianceExaminations);
        }
        return complianceReview;
    }

    @Override
    public Page<ComplianceReview> queryMajorMatter(JSONObject jsonObject) {
        String orgId = jsonObject.containsKey("orgId") ? jsonObject.getString("orgId") : null;
        QueryWrapper<ComplianceReview> queryWrapper = new QueryWrapper<>();
        queryWrapper
                .eq("data_state", DataStateBPM.FINISH.getValue())
                .and(wrapper -> wrapper.eq("create_org_id", orgId))
                .and(wrapper -> wrapper.eq("review_category", "HG-SCLX-ZDSX"));
        queryWrapper.orderByDesc("submission_date");
        return page(new PageUtils<>(jsonObject), queryWrapper);
    }

    @Override
    @Transactional
    public void saveData(ComplianceReview complianceReview) {
        complianceReview.setSubmitter(complianceReview.getCreatePsnName());
        complianceReview.setSubmissionDate(complianceReview.getCreateTime());
        complianceReview.setSubmittingUnit(complianceReview.getCreateOrgName());
        // 如果是合同合规并且关联了重大事项，将状态改为审批完成
        if ("HG-SCLX-HTHG".equals(complianceReview.getReviewCategory()) && complianceReview.getRelatedSignificantReview() != null) {
            complianceReview.setDataState(DataStateBPM.FINISH.getValue());
            complianceReview.setDataStateCode(DataStateBPM.FINISH.getKey());
        }
        List<ComplianceReviewFixedItem> fixedItemList = complianceReview.getCheckList();
        if (!CollectionUtils.isEmpty(fixedItemList)) {
            Utils.saveChilds(fixedItemList, "parent_id", complianceReview.getId(), complianceReviewFixedItemService);
        }

        List<ComplianceExamination> cExaminations = complianceReview.getExaminationLists();
        if (!CollectionUtils.isEmpty(cExaminations)) {
            Utils.saveChilds(cExaminations, "parent_id", complianceReview.getId(), complianceExaminationService);
        }
        saveOrUpdate(complianceReview);
    }

    @Override
    public Page<ComplianceReview> getOriginalComplianceReview(JSONObject jsonObject) {
        QueryWrapper<ComplianceReview> queryWrapper = new QueryWrapper<>();
        getOriginalFilter(jsonObject, queryWrapper);
        return page(new PageUtils<>(jsonObject), queryWrapper);
    }

    private void getOriginalFilter(JSONObject jsonObject, QueryWrapper<ComplianceReview> queryWrapper) {
        String fuzzyValue = jsonObject.containsKey("fuzzyValue") ? jsonObject.getString("fuzzyValue") : null;
        String orgId = jsonObject.containsKey("orgId") ? jsonObject.getString("orgId") : null;

        //按送审时间倒序展示
        queryWrapper.orderByDesc("submission_date");
        // 模糊搜索匹配字段
        String[] cols = {"review_subject", "review_category", "submitting_unit"};
        Utils.fuzzyValueQuery(queryWrapper, cols, fuzzyValue);
        // 当前用户发起过并且审批通过的重大决策事项合规审查数据
        queryWrapper
                .eq("create_org_id", orgId)
                .and(wrapper -> wrapper.eq("data_state", DataStateBPM.FINISH.getValue()))
                .and(wrapper -> wrapper.eq("review_category", "HG-SCLX-ZDSX"));
        queryWrapper.orderBy(true, false, "submission_date");
    }

    private void getFilter(JSONObject jsonObject, QueryWrapper<ComplianceReview> queryWrapper) {
        //是否台账功能（登记只查自己的，台账和弹框查自己和数据权限的）
        boolean isQuery = jsonObject.containsKey("isQuery") ? jsonObject.getBoolean("isQuery") : false;
        String reviewSubject = jsonObject.containsKey("reviewSubject") ? jsonObject.getString("reviewSubject") : null;
        String reviewCategory = jsonObject.containsKey("reviewCategory") ? jsonObject.getString("reviewCategory") : null;
        String submittingUnit = jsonObject.containsKey("submittingUnit") ? jsonObject.getString("submittingUnit") : null;
        String submitter = jsonObject.containsKey("submitter") ? jsonObject.getString("submitter") : null;
        Date submissionDateStart = jsonObject.containsKey("submissionDateStart") ? jsonObject.getDate("submissionDateStart") : null;
        Date submissionDateEnd = jsonObject.containsKey("submissionDateEnd") ? jsonObject.getDate("submissionDateEnd") : null;
        String fuzzyValue = jsonObject.containsKey("fuzzyValue") ? jsonObject.getString("fuzzyValue") : null;
        //顺序字段
        String sortName = jsonObject.containsKey("sortName") ? jsonObject.getString("sortName") : null;
        String orgId = jsonObject.containsKey("orgId") ? jsonObject.getString("orgId") : null;
        String dataState = jsonObject.containsKey("dataState") ? jsonObject.getString("dataState") : null;
        String createOgnName = jsonObject.containsKey("createOgnName") ? jsonObject.getString("createOgnName") : null;
        //顺序
        boolean order = jsonObject.containsKey("order") ? jsonObject.getBoolean("order") : false;
        if (StringUtils.isNotBlank(reviewSubject)) {
            queryWrapper.like("review_subject", reviewSubject);
        }
        if (StringUtils.isNotBlank(reviewCategory)) {
            queryWrapper.like("review_category", reviewCategory);
        }
        if (StringUtils.isNotBlank(submittingUnit)) {
            queryWrapper.like("submitting_unit", submittingUnit);
        }
        if (StringUtils.isNotBlank(submitter)) {
            queryWrapper.like("submitter", submitter);
        }
        if (submissionDateStart != null) {
            queryWrapper.ge("submission_date", submissionDateStart);
        }
        if (submissionDateEnd != null) {
            queryWrapper.le("submission_date", submissionDateEnd);
        }
        if (StringUtils.isNotBlank(dataState)) {
            queryWrapper.ne("data_state", dataState);
        }
        if (StringUtils.isNotBlank(createOgnName)) {
            queryWrapper.eq("create_ogn_name", createOgnName);
        }
        //台账权限
        if (isQuery){
            Long functionId = DataAuthUtils.getFunctionIdByCode("hgsc_tz");
            DataAuthUtils.dataPermSql(queryWrapper, null, functionId, orgId);
        }else {
            //经办人权限隔离
            queryWrapper.eq("create_org_id", orgId);
        }

        //按送审时间倒序展示
//        queryWrapper.orderByDesc("submission_date");
        // 模糊搜索匹配字段
        String[] cols = {"review_subject", "review_category", "submitting_unit"};
        Utils.fuzzyValueQuery(queryWrapper, cols, fuzzyValue);

        if (StringUtils.isNotBlank(sortName)) {
            queryWrapper.orderBy(true, order, Utils.humpToLine2(sortName));
        } else {
            queryWrapper.orderBy(true, order, "create_time");
        }
    }
}




