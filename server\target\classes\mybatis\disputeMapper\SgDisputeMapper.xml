<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.klaw.dao.disputeDao.SgDisputeMapper">
    <resultMap id="BaseResultMap" type="com.klaw.entity.disputeBean.SgDispute">
        <id column="ID" jdbcType="VARCHAR" property="id"/>
        <result column="NAME" jdbcType="VARCHAR" property="name"/>
        <result column="CODE" jdbcType="VARCHAR" property="code"/>
        <result column="OCCUR_TIME" jdbcType="TIMESTAMP" property="occurTime"/>
        <result column="DISPUTE_TYPE_CODE" jdbcType="VARCHAR" property="disputeTypeCode"/>
        <result column="DISPUTE_TYPE_NAME" jdbcType="VARCHAR" property="disputeTypeName"/>
        <result column="PUNISH_CODE" jdbcType="VARCHAR" property="punishCode"/>
        <result column="OUR_POSITION_CODE" jdbcType="VARCHAR" property="ourPositionCode"/>
        <result column="OUR_POSITION_NAME" jdbcType="VARCHAR" property="ourPositionName"/>
        <result column="UNIT_TYPE_CODE" jdbcType="VARCHAR" property="unitTypeCode"/>
        <result column="UNIT_TYPE_NAME" jdbcType="VARCHAR" property="unitTypeName"/>
        <result column="PUNISH_SUBJECT" jdbcType="VARCHAR" property="punishSubject"/>
        <result column="WHETHER_HAS_CONTACTS" jdbcType="DECIMAL" property="whetherHasContacts"/>
        <result column="PUNISH_TYPE_CODE" jdbcType="VARCHAR" property="punishTypeCode"/>
        <result column="PUNISH_TYPE_NAME" jdbcType="VARCHAR" property="punishTypeName"/>
        <result column="BE_PUNISH_OJECT" jdbcType="VARCHAR" property="bePunishOject"/>
        <result column="PUNISH_TIME" jdbcType="TIMESTAMP" property="punishTime"/>
        <result column="PUNISH_RESULT" jdbcType="VARCHAR" property="punishResult"/>
        <result column="RECTIFICATION_CODE" jdbcType="VARCHAR" property="rectificationCode"/>
        <result column="RECTIFICATION_NAME" jdbcType="VARCHAR" property="rectificationName"/>
        <result column="RECTIFICATION_DESC" jdbcType="VARCHAR" property="rectificationDesc"/>
        <result column="WHETHER_LITIGATION" jdbcType="DECIMAL" property="whetherLitigation"/>
        <result column="WHETHER_OVER" jdbcType="DECIMAL" property="whetherOver"/>
        <result column="PUNISH_REASONS" jdbcType="CLOB" property="punishReasons"/>
        <result column="HELP_EXECUTE_TYPE_CODE" jdbcType="VARCHAR" property="helpExecuteTypeCode"/>
        <result column="HELP_EXECUTE_TYPE_NAME" jdbcType="VARCHAR" property="helpExecuteTypeName"/>
        <result column="HELP_EXECUTE_NO" jdbcType="VARCHAR" property="helpExecuteNo"/>
        <result column="HELP_EXECUTE_START" jdbcType="TIMESTAMP" property="helpExecuteStart"/>
        <result column="HELP_EXECUTE_END" jdbcType="TIMESTAMP" property="helpExecuteEnd"/>
        <result column="CASE_STAGE_CODE" jdbcType="VARCHAR" property="caseStageCode"/>
        <result column="CASE_STAGE_NAME" jdbcType="VARCHAR" property="caseStageName"/>
        <result column="WHETHER_RELATION_CASE" jdbcType="DECIMAL" property="whetherRelationCase"/>
        <result column="INVOLVED_PERSON" jdbcType="VARCHAR" property="involvedPerson"/>
        <result column="WHETHER_NUBMER_MONEY" jdbcType="DECIMAL" property="whetherNubmerMoney"/>
        <result column="RECEPTIONIST" jdbcType="VARCHAR" property="receptionist"/>
        <result column="RECEPTIONIST_TIME" jdbcType="VARCHAR" property="receptionistTime"/>
        <result column="HELP_EXECUTE_ASK" jdbcType="CLOB" property="helpExecuteAsk"/>
        <result column="RISK_LEVEL_CODE" jdbcType="VARCHAR" property="riskLevelCode"/>
        <result column="RISK_LEVEL_NAME" jdbcType="VARCHAR" property="riskLevelName"/>
        <result column="DISPUTE_MONEY" jdbcType="DECIMAL" property="disputeMoney"/>
        <result column="PROJECT_NAME" jdbcType="VARCHAR" property="projectName"/>
        <result column="DISPUTE_DESCRIPTION" jdbcType="CLOB" property="disputeDescription"/>
        <result column="REMARKS" jdbcType="CLOB" property="remarks"/>
        <result column="CREATE_OGN_ID" jdbcType="VARCHAR" property="createOgnId"/>
        <result column="CREATE_OGN_NAME" jdbcType="VARCHAR" property="createOgnName"/>
        <result column="CREATE_DEPT_ID" jdbcType="VARCHAR" property="createDeptId"/>
        <result column="CREATE_DEPT_NAME" jdbcType="VARCHAR" property="createDeptName"/>
        <result column="CREATE_GROUP_ID" jdbcType="VARCHAR" property="createGroupId"/>
        <result column="CREATE_GROUP_NAME" jdbcType="VARCHAR" property="createGroupName"/>
        <result column="CREATE_PSN_ID" jdbcType="VARCHAR" property="createPsnId"/>
        <result column="CREATE_PSN_NAME" jdbcType="VARCHAR" property="createPsnName"/>
        <result column="CREATE_ORG_ID" jdbcType="VARCHAR" property="createOrgId"/>
        <result column="CREATE_ORG_NAME" jdbcType="VARCHAR" property="createOrgName"/>
        <result column="CREATE_PSN_FULL_ID" jdbcType="VARCHAR" property="createPsnFullId"/>
        <result column="CREATE_PSN_FULL_NAME" jdbcType="VARCHAR" property="createPsnFullName"/>
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="DATA_STATE" jdbcType="VARCHAR" property="dataState"/>
        <result column="DATA_STATE_CODE" jdbcType="DECIMAL" property="dataStateCode"/>
    </resultMap>
    <sql id="Base_Column_List">
    ID, "NAME", CODE, OCCUR_TIME, DISPUTE_TYPE_CODE, DISPUTE_TYPE_NAME, PUNISH_CODE, 
    OUR_POSITION_CODE, OUR_POSITION_NAME, UNIT_TYPE_CODE, UNIT_TYPE_NAME, PUNISH_SUBJECT, 
    WHETHER_HAS_CONTACTS, PUNISH_TYPE_CODE, PUNISH_TYPE_NAME, BE_PUNISH_OJECT, PUNISH_TIME, 
    PUNISH_RESULT, PUNISH_MONEY, RECTIFICATION_CODE, RECTIFICATION_NAME, RECTIFICATION_DESC, 
    WHETHER_LITIGATION, WHETHER_OVER, PUNISH_REASONS, HELP_EXECUTE_TYPE_CODE, HELP_EXECUTE_TYPE_NAME, 
    HELP_EXECUTE_NO, HELP_EXECUTE_START, HELP_EXECUTE_END, CASE_STAGE_CODE, CASE_STAGE_NAME, 
    WHETHER_RELATION_CASE, INVOLVED_PERSON, WHETHER_NUBMER_MONEY, HELP_EXECUTE_MONEY_STR, 
    HELP_EXECUTE_MONEY, RECEPTIONIST, RECEPTIONIST_TIME, HELP_EXECUTE_ASK, RISK_LEVEL_CODE, 
    RISK_LEVEL_NAME, DISPUTE_MONEY, PROJECT_NAME, DISPUTE_DESCRIPTION, REMARKS, CREATE_OGN_ID, 
    CREATE_OGN_NAME, CREATE_DEPT_ID, CREATE_DEPT_NAME, CREATE_GROUP_ID, CREATE_GROUP_NAME, 
    CREATE_PSN_ID, CREATE_PSN_NAME, CREATE_ORG_ID, CREATE_ORG_NAME, CREATE_PSN_FULL_ID, 
    CREATE_PSN_FULL_NAME, CREATE_TIME, UPDATE_TIME, DATA_STATE, DATA_STATE_CODE
  </sql>

    <select id="queryLeftByUnit" resultType="java.util.Map">
        SELECT
			count( unit_type_name ) AS num,
			unit_type_name AS name_
		FROM
			( SELECT case when unit_type_name is null or unit_type_name = '' then '无' else unit_type_name end as unit_type_name
        FROM SG_DISPUTE
			<where>
				${ew.sqlSegment}
			</where>
			) A
		GROUP BY
			unit_type_name
		ORDER BY unit_type_name
    </select>
    <select id="queryLeftByYear" resultType="java.util.Map">
        SELECT
			count( time_ ) AS num,
			time_ AS name_ 
		FROM
			(
			SELECT
                case when occur_time is null then '无' else
				date_format(occur_time, '%Y') end AS time_
			FROM
				SG_DISPUTE 
				<where>
					${ew.sqlSegment} 
				</where>
			) A 
		GROUP BY
			time_ 
		ORDER BY time_
    </select>

    <select id="queryLeftByType" resultType="java.util.Map">
        SELECT
        count( dispute_type_name ) AS num,
        dispute_type_name AS name_
        FROM
        ( SELECT case when dispute_type_name is null or dispute_type_name = '' then '无' else dispute_type_name end as dispute_type_name
        FROM SG_DISPUTE
        <where>
            ${ew.sqlSegment}
        </where>
        ) A
        GROUP BY
        dispute_type_name
        ORDER BY dispute_type_name
    </select>

    <!--上报-->
    <select id="queryLeftByUnit2" resultType="java.util.Map">
        SELECT
        count( unit_type_name ) AS num,
        unit_type_name AS name_
        FROM
        ( SELECT case when c.unit_type_name is null or c.unit_type_name = '' then '无' else c.unit_type_name end as unit_type_name
        FROM SG_CASE_REPORT_TASK t join SG_MIDDLE_RELATION m
        on t.ID = m.RELATION_ID join SG_DISPUTE c on m.ASSOCIATED_ID = c.ID
        <where>
            ${ew.sqlSegment}
        </where>
        ) A
        GROUP BY
        unit_type_name
        ORDER BY unit_type_name
    </select>
    <select id="queryLeftByYear2" resultType="java.util.Map">
        SELECT
        count( time_ ) AS num,
        time_ AS name_
        FROM
        (
        SELECT
        case when c.occur_time is null or c.occur_time = '' then '无' else
        date_format(c.occur_time, '%Y') end AS time_
        FROM
        SG_CASE_REPORT_TASK t join SG_MIDDLE_RELATION m
        on t.ID = m.RELATION_ID join SG_DISPUTE c on m.ASSOCIATED_ID = c.ID
        <where>
            ${ew.sqlSegment}
        </where>
        ) A
        GROUP BY
        time_
        ORDER BY time_
    </select>
</mapper>