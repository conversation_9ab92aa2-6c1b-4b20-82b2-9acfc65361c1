
896d9d0bb854d542b8ddb579880589079eaa12be	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.15.1754018536329.js\",\"contentHash\":\"e3088e8594103eeb06bb9110df4ed552\"}","integrity":"sha512-aea+tmHxBGLPBv4egQxI7WOCQ9s8jOsPfaV+iI9J5ZwwcOng/R6TQKdJeIYd2x+PVEqNruXZVssMcFdvNYcRxA==","time":1754018575958,"size":96158}