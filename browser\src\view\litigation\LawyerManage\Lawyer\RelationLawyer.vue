<template>
  <el-dialog title="关联律师" :visible.sync="isShow" width="60%" :close-on-click-modal="false" :modal-append-to-body="false">
    <el-form>
      <el-main>
        <div v-if="!isMind" style="width: 100%">
          <el-row style="margin-bottom: 20px">
            <el-col :span="6">
              <el-input v-model="temp.content" clearable placeholder="请输入内容" @keyup.enter.native="refreshData" @clear="refreshData" />
            </el-col>
            <el-col :span="8" style="margin-left: 10px">
              <el-button type="primary" @click="select_">查询</el-button>
            </el-col>
          </el-row>
        </div>
        <div>
          <el-table :data="tableData" border style="table-layout: fixed;width: 99%;" :height="350" stripe fit highlight-current-row @selection-change="lawyerSelect" @row-click="CurrentChange" @row-dblclick="seeLawyer">
            <el-table-column v-if="isMultiple" type="selection" width="40" />
            <el-table-column v-if="!isMind" type="index" label="序号" align="center" />
            <el-table-column v-if="isMind" type="index" label="智能排名" width="100" align="center" />
            <el-table-column prop="lawyerName" label="姓名" show-overflow-tooltip align="center" />
            <el-table-column prop="lawyerSex" label="性别" show-overflow-tooltip align="center" />
            <el-table-column prop="createTime" label="服务开始时间" width="140" show-overflow-tooltip align="center">
              <template slot-scope="scope">
                <span>{{ scope.row.createTime | parseTime }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="lawFirm" label="所在律所" show-overflow-tooltip align="center" />
          </el-table>
        </div>
      </el-main>
      <el-footer v-if="!isMind">
        <pagination :total="temp.total" :page.sync="temp.page" :limit.sync="temp.limit" style="text-align: center" @pagination="refreshData" />
      </el-footer>
      <span slot="footer" class="dialog-footer">
        <el-button class="negative-btn" @click="cancel_">取消</el-button>
        <el-button type="primary" icon="" class="active-btn" @click="lawyerSure_">确定</el-button>
      </span>
    </el-form>

  </el-dialog>
</template>
<script>
import pagination from "@/components/Pagination";
import lawyerApi from "@/api/LawyerManage/Lawyer";
export default {
  name: "RelationLawyer",
  components: { pagination },
  props: {
    isShow: {
      type: Boolean,
      default: false
    },
    isMultiple: {
      type: Boolean,
      default: true
    },
    mainId: {
      type: String,
      default: ""
    },
    dialog: {
      type: String,
      default: "lawyer"
    },
    isMind: {
      type: Boolean,
      default: false
    },
    hasOut: {
      type: Boolean,
      default: false
    },
    caseId: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      tableData: [],
      lawyerList: [],
      lawyer: null,
      myIsShow: this.isShow,
      lawFirmId: null,
      temp: {
        page: 1,
        total: 0,
        limit: 10,
        content: null,
        isDialog: this.dialog,
        lawFirmId: this.mainId,
        // typeDialog: true
        hasOut: this.hasOut,
        isQuery: true
      }
    };
  },
  computed: {
    getDialog() {
      return this.temp.isDialog;
    }
  },
  watch: {
    isShow(val) {
      this.myIsShow = val;
      if (val) {
        this.refreshData();
      }
    },
    mainId(val) {
      this.temp.lawFirmId = val;
      this.lawFirmId = val;
    },
    lawFirmId(val) {
      this.$emit("update:mainId", val);
    },
    dialog(val) {
      this.temp.isDialog = val;
    },
    getDialog(val) {
      this.$emit("update:isDialog", val);
    },
    myIsShow(val) {
      this.$emit("update:isShow", val);
    }
  },
  created() {
    this.refreshData();
  },
  methods: {
    select_() {
      this.temp.page = 1;
      this.refreshData();
    },
    refreshData() {
      if (this.isMind) {
        this.temp.caseId = this.caseId;
        lawyerApi.queryMind(this.temp).then(res => {
          this.tableData = res.data.page;
        });
      } else {
        lawyerApi.query(this.temp).then(res => {
          this.tableData = res.data.page.records;
          this.temp.total = res.data.page.total;
        });
      }
    },
    cancel_() {
      this.myIsShow = false;
    },
    lawyerSelect(val) {
      this.lawyerList = val;
    },
    CurrentChange(row, column, event) {
      const index = this.tableData.findIndex(item => item.id === row.id);
      this.lawyer = row;
      this.lawyer.mindOrder = index + 1;
    },
    lawyerSure_() {
      this.$emit(
        "lawyerSure_",
        this.isMultiple ? this.lawyerList : this.lawyer
      );
      this.cancel_();
    },

    /* mindIndex(index) {
      return (index + 1) + this.temp.limit * (this.temp.page - 1)
    }*/
    seeLawyer(row, column, event) {
      if (this.isMind) {
        this.$router.push({
          name: "LawyerMain",
          params: { ...this.utils.routeState.VIEW(row.id) }
        });
      }
    }
  }
};
</script>

<style scoped>
</style>
