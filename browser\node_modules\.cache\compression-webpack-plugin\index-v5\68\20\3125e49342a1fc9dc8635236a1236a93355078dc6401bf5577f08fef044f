
122b11110d6fe4ae615f44ca974c2ae2c3defb2f	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.324.1754018536329.js\",\"contentHash\":\"f9c51516b9a8cb2fad4f2957d92832bf\"}","integrity":"sha512-+Xvtc5l98FDf5q6ASJZ3SKNv2iZV4DyJoR65LyXM6c2fzdmWFfRPEEE2FMos9qH5y8sJfWNrepvoZlxnYjPfUQ==","time":1754018575974,"size":86926}