package com.klaw.service.imp.complianceRiskServiceImp;


import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.klaw.dao.complianceRiskDao.UserCustomMenuMapper;
import com.klaw.entity.complianceRiskBean.UserCustomMenuEntity;
import com.klaw.service.complianceRiskService.UserCustomMenuService;
import com.klaw.utils.PageUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;

@Service
public class UserCustomMenuServiceImpl extends ServiceImpl<UserCustomMenuMapper, UserCustomMenuEntity> implements UserCustomMenuService {
    @Autowired
    UserCustomMenuMapper userCustomMenuMapper;


    @Override
    public UserCustomMenuEntity getUserCustomMenu(Integer id) {

        return userCustomMenuMapper.selectById(id);
    }

    @Override
    public List<UserCustomMenuEntity> getAllUserCustomMenu() {
        return userCustomMenuMapper.selectList(null);

    }

    @Override
    public void add(UserCustomMenuEntity userCustomMenu) {
        userCustomMenuMapper.insert(userCustomMenu);
    }

    @Override
    public int modify(UserCustomMenuEntity userCustomMenu) {
        //乐观锁更新
        UserCustomMenuEntity currentUserCustomMenu = userCustomMenuMapper.selectById(userCustomMenu.getId());
        return userCustomMenuMapper.updateById(userCustomMenu);
    }

    @Override
    public void remove(String ids) {

        if (StringUtils.isNotEmpty(ids)) {
            String[] array = ids.split(",");
            if (!CollectionUtils.isEmpty(Arrays.asList(array))) {
                userCustomMenuMapper.deleteBatchIds(Collections.singletonList(array));
            }
        }

    }

    @Override
    public PageUtils<UserCustomMenuEntity> queryPageData(JSONObject jsonObject) {
        //模糊搜索
        String fuzzyValue = jsonObject.getString("fuzzyValue");
        QueryWrapper<UserCustomMenuEntity> queryWrapper = new QueryWrapper<>();
        if (StringUtils.isNotBlank(fuzzyValue)) {
            queryWrapper.and(i -> i.like("custom_name", fuzzyValue)
                    .or().like("menu_name", fuzzyValue));
        }

        String orgId = jsonObject.containsKey("orgId")? jsonObject.getString( "orgId"):"";

        if(com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(orgId)){
            queryWrapper.eq("create_psn_id", orgId);
        }
        String dataState = jsonObject.getString("dataState");
        if (StringUtils.isNotBlank(dataState)) {
            queryWrapper.eq("data_state", dataState);
        }
        queryWrapper.orderByAsc("sort_order");
        return page(new PageUtils<>(jsonObject), queryWrapper);
    }

    @Override
    public PageUtils<UserCustomMenuEntity> queryHome(JSONObject jsonObject) {
        //模糊搜索
        String fuzzyValue = jsonObject.getString("fuzzyValue");
        QueryWrapper<UserCustomMenuEntity> queryWrapper = new QueryWrapper<>();
        if (StringUtils.isNotBlank(fuzzyValue)) {
            queryWrapper.and(i -> i.like("custom_name", fuzzyValue)
                    .or().like("menu_name", fuzzyValue));
        }

        String orgId = jsonObject.containsKey("orgId")? jsonObject.getString( "orgId"):"";
        queryWrapper.ne("data_state_code", "02");
        if(StringUtils.isNotBlank(orgId)){
            queryWrapper.eq("create_psn_id", orgId);
        }
        String dataState = jsonObject.getString("dataState");
        if (StringUtils.isNotBlank(dataState)) {
            queryWrapper.eq("data_state", dataState);
        }
        queryWrapper.orderByAsc("sort_order");
        return page(new PageUtils<>(jsonObject), queryWrapper);
    }

    @Override
    public List<UserCustomMenuEntity> queryList() {
        //模糊搜索
        QueryWrapper<UserCustomMenuEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("is_visible", "1");
        queryWrapper.orderByAsc("creat_time");
        return list(queryWrapper);
    }


    @Override
    public void saveData(UserCustomMenuEntity userCustomMenu) {
        userCustomMenu.setUpdateTime(new Date());
        saveOrUpdate(userCustomMenu);
    }
}
