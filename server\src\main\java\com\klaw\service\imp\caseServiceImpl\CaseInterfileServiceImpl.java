package com.klaw.service.imp.caseServiceImpl;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.klaw.constant.DataState;
import com.klaw.dao.caseDao.CaseInterfileMapper;
import com.klaw.entity.caseBean.CaseEasyModel;
import com.klaw.entity.caseBean.CaseInterfile;
import com.klaw.entity.caseBean.CaseRecord;
import com.klaw.entity.systemBean.SgSysDoc;
import com.klaw.service.caseService.CaseInterfileService;
import com.klaw.service.caseService.CaseService;
import com.klaw.service.systemService.SysDocService;
import com.klaw.utils.Utils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;


@Service
public class CaseInterfileServiceImpl extends ServiceImpl<CaseInterfileMapper, CaseInterfile> implements CaseInterfileService {

    @Autowired
    private CaseService caseService;
    @Autowired
    private SysDocService sysDocService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<CaseInterfile> initInterFile(String caseId, String rootId) {
        CaseRecord caseRecord = caseService.getById(caseId);
        String[] ids = caseRecord.getCaseProcessIds().split(",");
        List<CaseRecord> caseRecords = new ArrayList<>();
        for (int i = 0; i < ids.length; i++) {
            caseRecords.add(caseService.queryDataById(ids[i]));
        }

        List<CaseInterfile> caseInterfiles = getCaseInterfileChilds(caseRecords, caseRecord, rootId);

        return caseInterfiles;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveData(CaseInterfile root, List<CaseInterfile> childs) {
        saveOrUpdate(root);

        Utils.saveChilds(childs, "parent_id", root.getId(), this);

        CaseRecord caseRecord = caseService.getById(root.getCaseId());

        caseRecord.setInterfile(root.getDataStateCode() == DataState.SUBMIT.getKey() ? 2 : 1);

        caseService.updateById(caseRecord);
    }

    @Override
    public JSONObject queryDataById(String id) {
        QueryWrapper<CaseInterfile> wrapper = new QueryWrapper<>();
        wrapper.eq("case_id", id).isNull("parent_id");
        CaseInterfile main = getOne(wrapper);
        List<CaseInterfile> childs = list(new QueryWrapper<CaseInterfile>().eq("parent_id", main.getId()).orderBy(true,true,"create_time"));
        JSONObject result = new JSONObject();
        result.put("main", main);
        result.put("childs", childs);
        return result;
    }

    private List<CaseInterfile> getCaseInterfileChilds(List<CaseRecord> caseRecords, CaseRecord caseBean, String rootId) {
        List<CaseInterfile> caseInterfiles = new ArrayList<>();
        for (int i = 0; i < caseRecords.size(); i++) {
            CaseRecord caseRecord = caseRecords.get(i);
            List<String> files = new ArrayList<>();
            if (i == 0) {
                if (CollectionUtils.isNotEmpty(caseRecord.getAgentList()))
                    files.addAll(caseRecord.getAgentList().stream().map(item -> item.getFiles()).collect(Collectors.toList()));//授权委托书 代理人
                if (CollectionUtils.isNotEmpty(caseRecord.getAgentContractList()))
                    files.addAll(caseRecord.getAgentContractList().stream().map(item -> item.getFiles()).collect(Collectors.toList()));//委托合同 代理合同
                if (CollectionUtils.isNotEmpty(caseRecord.getPreservationList()))
                    files.addAll(caseRecord.getPreservationList().stream().map(item -> item.getPreservationFiles()).collect(Collectors.toList()));//保全文书
                if (caseRecord.getWhetherEasy()) {
                    fileIdsToCaseInterFile(files, caseInterfiles, rootId, caseBean.getId(), caseBean.getCaseName(), caseRecord.getCaseProcessType(), caseRecord.getId());
                    CaseEasyModel easyModel = caseRecord.getCaseEasyModel();
                    if (easyModel != null) {
                        if (CollectionUtils.isNotEmpty(easyModel.getEasyDataList())) {
                            easyModel.getEasyDataList().stream().forEach(item -> {
                                fileIdsToCaseInterFile(Arrays.asList(item.getFiles()), caseInterfiles, rootId, caseBean.getId(), caseBean.getCaseName(), item.getCaseStage(), item.getId());
                            });
                        }
                        /*if (CollectionUtils.isNotEmpty(easyModel.getCaseProcessList())){
                            List<String> processFiles = easyModel.getCaseProcessList().stream().map(item->item.getFiles()).collect(Collectors.toList());//案件过程记录
                            fileIdsToCaseInterFile(processFiles,caseInterfiles,rootId,caseBean.getId(),caseBean.getCaseName(),caseRecord.getCaseProcessType(),caseRecord.getId());
                        }*/
                    }
                    break;
                }
            } else {
                if (CollectionUtils.isNotEmpty(caseRecord.getCaseProcessList()))
                    files.addAll(caseRecord.getCaseProcessList().stream().map(item -> item.getFiles()).collect(Collectors.toList()));//过程记录
                if (CollectionUtils.isNotEmpty(caseRecord.getAppraisalList()))
                    files.addAll(caseRecord.getAppraisalList().stream().map(item -> item.getAppraisalFiles()).collect(Collectors.toList()));//鉴定文书
                if (CollectionUtils.isNotEmpty(caseRecord.getOtherDataList()))
                    files.addAll(caseRecord.getOtherDataList().stream().filter(z -> StringUtils.isBlank(z.getRelationId())).map(item -> item.getFiles()).collect(Collectors.toList()));//其他资料附件
            }

            fileIdsToCaseInterFile(files, caseInterfiles, rootId, caseBean.getId(), caseBean.getCaseName(), caseRecord.getCaseProcessType(), caseRecord.getId());


        }
        return caseInterfiles;
    }

    public void fileIdsToCaseInterFile(List<String> files, List<CaseInterfile> caseInterfiles, String rootId, String caseId, String caseName, String caseProcess, String caseProcessId) {
        List<String> docIds = new ArrayList<>();
        for (int j = 0; j < files.size(); j++) {
            String file = files.get(j);
            if (StringUtils.isNotBlank(file)) {
                JSONArray ja = JSONArray.parseArray(file);
                for (int k = 0; k < ja.size(); k++) {
                    JSONObject jb = ja.getJSONObject(k);
                    if (jb.containsKey("docId")) docIds.add(jb.getString("docId"));
                }
            }
        }

        if (docIds.size() > 0) {
            List<SgSysDoc> docs = sysDocService.listByIds(docIds);
            for (int j = 0; j < docs.size(); j++) {
                SgSysDoc doc = docs.get(j);
                CaseInterfile caseInterfile = new CaseInterfile();
                caseInterfile.setId(Utils.createUUID());
                caseInterfile.setCaseId(caseId);
                caseInterfile.setCaseName(caseName);
                caseInterfile.setParentId(rootId);
                caseInterfile.setDocId(doc.getId());
                caseInterfile.setDocName(doc.getDocName());
                caseInterfile.setDocType(doc.getDocType());
                caseInterfile.setDocSize(doc.getDocSize());
                JSONObject jb = new JSONObject();
                jb.put("docId", doc.getId());
                jb.put("name", doc.getDocName() + "." + doc.getDocType());
                JSONArray ja = new JSONArray();
                ja.add(jb);
                caseInterfile.setDocContent(ja.toJSONString());
                caseInterfile.setCopyNum(new BigDecimal("0"));
                caseInterfile.setOriginalNum(new BigDecimal("0"));
                caseInterfile.setCaseProcess(caseProcess);
                caseInterfile.setCaseProcessId(caseProcessId);
//                caseInterfile.copyCurrentInfo();
                caseInterfiles.add(caseInterfile);
            }
        }
    }

}
