<template>
    
    <div>
        <el-divider/>
        <el-row style="margin-top: 10px">
            <el-col :span="16">
                <el-form-item label="关联风险预警" prop="associatedRiskWarning">
                    <el-input clearable disabled
                              maxlength="50" placeholder="请输入..." show-word-limit
                              style="width: 100%" v-model.trim="mainData.associatedRiskWarning">
                        <el-button @click="riskWaringVisible=true" icon="el-icon-search" slot="append"></el-button>
                    </el-input>

                </el-form-item>
            </el-col>

            <el-col :span="8">
                <el-form-item label="风险编号" prop="riskNumber">
                    <el-input disabled show-word-limit style="width: 100%"
                               v-model.trim="mainData.riskNumber"/>
                </el-form-item>
            </el-col>
        </el-row>

        <el-row>
            <el-col :span="16">
                <el-form-item label="风险名称" prop="riskName">
                    <el-input clearable placeholder="请输入..." style="width: 100%" disabled maxlength="100"  show-word-limit
                              v-model="mainData.riskName"/>
                </el-form-item>
            </el-col>

            <el-col :span="8">
                <el-form-item label="事件性质" prop="eventNature">
                    <el-select disabled style="width:100%" disabled v-model.trim="mainData.eventNature">
                        <el-option :key="item.id" :label="item.dicName" :value="item.dicCode"
                                   v-for="item in eventNatureData"></el-option>
                    </el-select>
                </el-form-item>
            </el-col>

        </el-row>

        <el-row>
            <el-col :span="8">
                <el-form-item label="风险一级分类" prop="riskClassification">
                    <el-input placeholder="请输入..." style="width: 100%"
                              type="text"
                              v-model.trim="mainData.riskClassificationName" disabled>
                    </el-input>
                </el-form-item>
            </el-col>
            <el-col :span="8">
                <el-form-item label="风险二级分类" prop="riskClassificationSec">
                    <el-input placeholder="请输入..." style="width: 100%"
                              type="text"
                              v-model.trim="mainData.riskClassificationSecName" disabled>
                    </el-input>
                </el-form-item>
            </el-col>
            <el-col :span="8">
                <el-form-item label="风险等级" prop="expectedRiskLevel">
                    <el-select show-word-limit style="width: 100%" disabled
                               v-model.trim="mainData.expectedRiskLevel">
                        <el-option :key="item.id" :label="item.dicName" :value="item.dicCode"
                                   v-for="item in expectedRiskLevelData"></el-option>
                    </el-select>
                </el-form-item>
            </el-col>
        </el-row>
        <el-row>
            <el-col :span="8">
                <el-form-item label="业务领域" prop="businessDomain">
                    <el-select style="width: 100%;" disabled v-model="mainData.businessDomain">
                        <el-option  :label="item.dicName" :value="item.dicCode"
                                    v-for="item in businessDomainData"></el-option>
                    </el-select>
                </el-form-item>
            </el-col>
            <el-col :span="8" v-if="mainData.businessDomain=='FX_QT'">
                <el-form-item label="业务领域" prop="mainData.businessDomain">
                    <el-input v-model="mainData.otherBusinessArea" clearable placeholder="请输入业务领域..."></el-input>
                </el-form-item>
            </el-col>
            <el-col :span="8">
                <el-form-item label="涉及金额（元）" prop="involvedAmount" class="custom-word-break">
                    <Money :isDW="true" :isDX="true"  :moneyParam.sync="mainData.involvedAmount"
                           :is-disabled="true"></Money>
                </el-form-item>
            </el-col>
        </el-row>
        <el-row>
            <el-col :span="24">
                <el-form-item label="风险描述" prop="riskDescription">
                    <el-input placeholder="请输入..."  style="width: 100%"
                              :autosize="{ minRows: 3, maxRows: 6 }"
                              maxlength="1000"
                              show-word-limit
                              type="textarea"
                              disabled v-model.trim="mainData.riskDescription">
                    </el-input>
                </el-form-item>
            </el-col>
        </el-row>
        <el-row>
            <el-col :span="24">
                <el-form-item label="风险原因" prop="riskReason">
                    <el-input placeholder="请输入..." style="width: 100%"
                              :autosize="{ minRows: 3, maxRows: 6 }"
                              maxlength="1000"
                              show-word-limit
                              type="textarea"
                              v-model.trim="mainData.riskReason" disabled>
                    </el-input>
                </el-form-item>
            </el-col>
        </el-row>
        <el-row>
            <el-col :span="24">
                <el-form-item label="监管部门" prop="receivingAgency">
                    <el-input clearable placeholder="请输入..."
                              show-word-limit
                              style="width: 100%"
                              disabled
                              v-model.trim="mainData.receivingAgency"
                    >
                    </el-input>
                </el-form-item>
            </el-col>
        </el-row>
        <!--<el-row style="margin-top: 10px">
            <el-col :span="16">
                <el-form-item label="风险名称" prop="riskName">
                    <el-input disabled  v-model="mainData.riskName"   style="width: 100%" clearable placeholder="请输入..."/>
                </el-form-item>
            </el-col>

            <el-col :span="8">
                <el-form-item label="风险编号">
                    <el-input  disabled v-model.trim="mainData.riskNumber"
                              show-word-limit style="width: 100%"/>
                </el-form-item>
            </el-col>
        </el-row>

        <el-row>


            <el-col :span="8">
                <el-form-item label="事件性质">
                    <el-select   disabled v-model.trim="mainData.eventNature" style="width:100%">
                        <el-option v-for="item in eventNatureData" :label="item.dicName" :value="item.dicCode" :key="item.id"></el-option>
                    </el-select>
                </el-form-item>
            </el-col>
            <el-col :span="8">
                <el-form-item label="风险等级">
                    <el-select disabled v-model.trim="mainData.expectedRiskLevel"  show-word-limit style="width: 100%">
                        <el-option v-for="item in expectedRiskLevelData" :label="item.dicName" :value="item.dicCode" :key="item.id"></el-option>
                    </el-select>
                </el-form-item>
            </el-col>


            <el-col :span="8">
                <el-form-item label="业务领域">
                    <el-select disabled  v-model="mainData.businessDomain" style="width: 100%;">
                        <el-option v-for="item in businessDomainData"  :key="item.id" :label="item.dicName"  :value="item.dicCode"></el-option>
                    </el-select>
                </el-form-item>
            </el-col>


        </el-row>

        &lt;!&ndash;<el-row>
            <el-col :span="16">
                <el-form-item label="风险源">
                    <el-input disabled  v-model.trim="mainData.riskSource"  show-word-limit style="width: 100%" placeholder="请输入...">
                    </el-input>
                </el-form-item>
            </el-col>
            &lt;!&ndash;<el-col :span="8">
                <el-form-item label="涉及金额（万元）"  class="custom-word-break">
                    <el-input  disabled v-model="mainData.aboutMoney" ></el-input>
                </el-form-item>
            </el-col>&ndash;&gt;
        </el-row>&ndash;&gt;

        <el-row>
            <el-col :span="24">
                <el-form-item label="风险描述">
                    <el-input disabled  v-model.trim="mainData.riskDescription"  show-word-limit style="width: 100%" placeholder="请输入...">
                    </el-input>
                </el-form-item>
            </el-col>
        </el-row>
        <el-row>
            <el-col :span="24">
                <el-form-item label="风险原因">
                    <el-input disabled  v-model.trim="mainData.riskReason" show-word-limit style="width: 100%" placeholder="请输入...">
                    </el-input>
                </el-form-item>
            </el-col>
        </el-row>
        <el-row>
            <el-col :span="24">
                <el-form-item label="监管部门">
                    <el-input disabled  v-model.trim="mainData.receivingAgency"  show-word-limit style="width: 100%" placeholder="请输入...">
                    </el-input>
                </el-form-item>
            </el-col>
        </el-row>-->
        <!-- <div style="font-size:20px;">涉及法律义务条款及相关法律责任条款13</div>
        <el-divider></el-divider> -->
        <simple-board
				:data-state="!isView"
				:has-value="true"
				:hasAdd="false"
				:hasDle="currentRow != null"
				:hasDown="currentRow != null"
				:hasUp="currentRow != null"
				:title="'涉及法律义务条款及相关法律责任条款'"
				
				@addBtn="addRow"
				@delBtn="delRow"
				@downBtn="downRow"
				@upBtn="upRow"
			>
<el-table
    :data="legalObligations"
    :row-style="{height:'20px'}"
    :show-overflow-tooltip="true"
    border
    fit
    highlight-current-row
    ref="table"
    stripe
    style="table-layout: fixed;width: 100%;"
>
    <el-table-column align="center" label="序号" type="index"></el-table-column>
    <el-table-column header-align="center" label="法律法规名称" prop="lawsName" show-overflow-tooltip
                     width="300">
        <template slot-scope="scope">
            <el-tooltip :content="scope.row.lawsName" placement="top">
                <el-input placeholder="请输入" v-model="scope.row.lawsName" disabled></el-input>
            </el-tooltip>
        </template>
    </el-table-column>
    <el-table-column header-align="center" label="条款内容" prop="clauseContent" show-overflow-tooltip>
        <template slot-scope="scope">
            <el-tooltip :content="scope.row.clauseContent" placement="top">
                <el-input placeholder="请输入" v-model="scope.row.clauseContent" disabled></el-input>
            </el-tooltip>
        </template>
    </el-table-column>
</el-table>
    </simple-board>
    </div>
</template>
<script>
    import Money from '@/view/components/Money/index'
    import SimpleBoard from '@/view/components/SimpleBoard/SimpleBoardViewCase.vue';
    export default{
        name:'Baseinfo',
        components: {
            Money,SimpleBoard
        },
        props: {
            mainData: {
                type: Object,
                default:  function () {
                    return {}
                }
            },
            businessDomainData:{
                type:Array,
                default : function () {
                    return [];
                }
            },
            expectedRiskLevelData:{
                type:Array,
                default : function () {
                    return [];
                }
            },
            eventNatureData:{
                type:Array,
                default : function () {
                    return [];
                }
            },
            riskClassificationData:{
                type:Array,
                default : function () {
                    return [];
                }
            },
        },

        data(){
            return{
                legalObligations:[],
            }
        },
        watch:{
            mainData() {
                console.log(this.mainData);
                let legalObligations = this.mainData.legalObligations;
                if(legalObligations!=null){
                    this.legalObligations = JSON.parse(legalObligations);
                }
            },
        }

    }
</script>