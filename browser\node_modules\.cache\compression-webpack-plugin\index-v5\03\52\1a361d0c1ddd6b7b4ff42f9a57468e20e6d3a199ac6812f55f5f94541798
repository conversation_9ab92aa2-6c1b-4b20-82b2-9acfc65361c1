
363cf12f28224aaf6380603027fbf3bc18f2fbeb	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.461.1754018536329.js\",\"contentHash\":\"046b5e2c75c536117feec1b300c2f58e\"}","integrity":"sha512-vbC3MiHJwEuz4CKr2ipbJcfUry+uQzQfFhcULBBn6y+HoO+bXPJA0Tg8nuM3cwSEDw+Cmz2xMqapS1CQ+tyPng==","time":1754018575957,"size":37254}