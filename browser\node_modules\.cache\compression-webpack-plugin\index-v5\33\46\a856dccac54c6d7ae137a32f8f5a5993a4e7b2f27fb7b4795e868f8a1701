
4bbd4c2f332f804292e82e2ff755bf91fbe25692	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.135.1754018536329.js\",\"contentHash\":\"cbf4fe3383287b9d5d6f07ee47129058\"}","integrity":"sha512-isIvb/GAO/4VaraB/4iQYl2AxX3VmDwdE+vIuJQFnH0a31RBOqauzGslK/+R8As0A2uHM7357Oq8hEkuOVGMdA==","time":1754018575956,"size":41716}