
ed5af6d6eaea3cf2587bdd5ab20d7df33ba05a32	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.363.1754018536329.js\",\"contentHash\":\"1cf59f452e434afb61eb1b4a47a039c6\"}","integrity":"sha512-wlifsLNkmnWrrLbGGu+NCnp5qHXKx7C6wjVcJe04QPLAEr6e9WEAfHuu73FZVUSr4Da3+b/lAdx+4R2w5giaAg==","time":1754018575975,"size":104621}