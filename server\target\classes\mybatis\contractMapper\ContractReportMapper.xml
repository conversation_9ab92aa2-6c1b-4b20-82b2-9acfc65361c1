<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.klaw.dao.contractDao.ContractReportMapper">

    <select id="query" resultType="com.klaw.vo.contractVo.ContractReportVo">
        SELECT CREATE_PSN_FULL_NEW_ID,
               CREATE_PSN_FULL_ID,
               CREATE_LEGAL_UNIT_NAME,
               DECODE(CREATE_LEGAL_UNIT_ID, null, CREATE_PSN_FULL_NEW_ID, CREATE_LEGAL_UNIT_ID) AS CREATE_LEGAL_UNIT_ID,
               EFFECT_YEAR,
               SUM(DECODE(EFFECT_MONTH, 1, 1, 0))                                               AS JAN_COUNT,
               SUM(DECODE(EFFECT_MONTH, 1, DECODE(IS_FLE, 1, 1, 0), 0))                         AS JAN_FLE_COUNT,
               SUM(DECODE(EFFECT_MONTH, 2, 1, 0))                                               AS FEB_COUNT,
               SUM(DECODE(EFFECT_MONTH, 2, DECODE(IS_FLE, 1, 1, 0), 0))                         AS FEB_FLE_COUNT,
               SUM(DECODE(EFFECT_MONTH, 3, 1, 0))                                               AS MAR_COUNT,
               SUM(DECODE(EFFECT_MONTH, 3, DECODE(IS_FLE, 1, 1, 0), 0))                         AS MAR_FLE_COUNT,
               SUM(DECODE(EFFECT_MONTH, 4, 1, 0))                                               AS APR_COUNT,
               SUM(DECODE(EFFECT_MONTH, 4, DECODE(IS_FLE, 1, 1, 0), 0))                         AS APR_FLE_COUNT,
               SUM(DECODE(EFFECT_MONTH, 5, 1, 0))                                               AS MAY_COUNT,
               SUM(DECODE(EFFECT_MONTH, 5, DECODE(IS_FLE, 1, 1, 0), 0))                         AS MAY_FLE_COUNT,
               SUM(DECODE(EFFECT_MONTH, 6, 1, 0))                                               AS JUNE_COUNT,
               SUM(DECODE(EFFECT_MONTH, 6, DECODE(IS_FLE, 1, 1, 0), 0))                         AS JUNE_FLE_COUNT,
               SUM(DECODE(EFFECT_MONTH, 7, 1, 0))                                               AS JULY_COUNT,
               SUM(DECODE(EFFECT_MONTH, 7, DECODE(IS_FLE, 1, 1, 0), 0))                         AS JULY_FLE_COUNT,
               SUM(DECODE(EFFECT_MONTH, 8, 1, 0))                                               AS AUG_COUNT,
               SUM(DECODE(EFFECT_MONTH, 8, DECODE(IS_FLE, 1, 1, 0), 0))                         AS AUG_FLE_COUNT,
               SUM(DECODE(EFFECT_MONTH, 9, 1, 0))                                               AS SEP_COUNT,
               SUM(DECODE(EFFECT_MONTH, 9, DECODE(IS_FLE, 1, 1, 0), 0))                         AS SEP_FLE_COUNT,
               SUM(DECODE(EFFECT_MONTH, 10, 1, 0))                                              AS OCT_COUNT,
               SUM(DECODE(EFFECT_MONTH, 10, DECODE(IS_FLE, 1, 1, 0), 0))                        AS OCT_FLE_COUNT,
               SUM(DECODE(EFFECT_MONTH, 11, 1, 0))                                              AS NOV_COUNT,
               SUM(DECODE(EFFECT_MONTH, 11, DECODE(IS_FLE, 1, 1, 0), 0))                        AS NOV_FLE_COUNT,
               SUM(DECODE(EFFECT_MONTH, 12, 1, 0))                                              AS DEC_COUNT,
               SUM(DECODE(EFFECT_MONTH, 12, DECODE(IS_FLE, 1, 1, 0), 0))                        AS DEC_FLE_COUNT,
               COUNT(1)                                                                         AS TOTAL_COUNT,
               SUM(DECODE(IS_FLE, 1, 1, 0))                                                     AS TOTAL_FLE_COUNT
        FROM SG_CONTRACT_APPROVAL_VIEW
        <where>
            <if test="data.effectYear != null">
                AND EFFECT_YEAR = #{data.effectYear}
            </if>
            <if test="data.manageUnitIdList != null and data.manageUnitIdList.size() != 0">
                AND (
                <foreach collection="data.manageUnitIdList" item="item" open="(" close=")" separator="OR">
                    INSTR(CREATE_PSN_FULL_ID, #{item}) > 0
                </foreach>
                )
            </if>
            <if test="data.roleUnitIdList != null and data.roleUnitIdList.size() != 0">
                AND (
                <foreach collection="data.roleUnitIdList" item="item" open="(" close=")" separator="OR">
                    INSTR(CREATE_PSN_FULL_ID, #{item}) > 0
                </foreach>
                )
            </if>
        </where>
        GROUP BY CREATE_PSN_FULL_ID, CREATE_PSN_FULL_NEW_ID, CREATE_LEGAL_UNIT_NAME, CREATE_LEGAL_UNIT_ID, EFFECT_YEAR
    </select>

    <select id="queryLastYear" resultType="com.klaw.vo.contractVo.ContractReportVo">
        SELECT CREATE_PSN_FULL_NEW_ID,
               EFFECT_YEAR,
               COUNT(1)                     AS TOTAL_COUNT,
               SUM(DECODE(IS_FLE, 1, 1, 0)) AS TOTAL_FLE_COUNT
        FROM SG_CONTRACT_APPROVAL_VIEW
        WHERE EFFECT_YEAR = #{lastYear}
            <if test="createPsnFullNewIdList != null and createPsnFullNewIdList.size() != 0">
                AND CREATE_LEGAL_UNIT_ID IN
                <foreach collection="createPsnFullNewIdList" item="item" open="(" close=")" separator=",">
        #{item}
                </foreach>
            </if>
        GROUP BY CREATE_PSN_FULL_NEW_ID, EFFECT_YEAR
    </select>

    <select id="queryType" resultType="com.klaw.vo.contractVo.ContractTypeVo">
        SELECT ONE_TYPE_NAME,
               ONE_TYPE_CODE,
               COUNT(1)                                                           AS CONTRACT_COUNT,
               SUM(DECODE(SIGNED_AMOUNT, NULL, 0, SIGNED_AMOUNT))                 AS CONTRACT_AMOUNT,
               SUM(DECODE(WHETHER_MAJOR_OGN, 1, 1, 0))                            AS MAJOR_OGN_COUNT,
               SUM(CASE WHEN WHETHER_MAJOR_OGN = 1 THEN SIGNED_AMOUNT ELSE 0 END) AS MAJOR_OGN_AMOUNT
        FROM (SELECT ONE_TYPE_NAME,
                     ONE_TYPE_CODE,
                     CASE
                         WHEN SIGNED_AMOUNT_TAX = 0 OR SIGNED_AMOUNT_TAX IS NULL THEN NO_INCLUDING_TAX
                         ELSE INCLUDING_TAX_RMB END AS SIGNED_AMOUNT,
                     WHETHER_MAJOR_OGN
              FROM SG_CONTRACT_APPROVAL
        <where>
            TAKE_EFFECT_CODE = '2'
              AND DATA_TYPE_CODE != 2
            <if test="data.effectYear != null">
                AND EXTRACT(YEAR FROM EFFECT_TIME) = #{data.effectYear}
            </if>
            <if test="data.ourPartys != null and data.ourPartys != ''">
                AND OUR_PARTYS LIKE '%' || #{data.ourPartys} || '%'
            </if>
            <if test="data.manageUnitIdList != null and data.manageUnitIdList.size() != 0">
                AND (
                <foreach collection="data.manageUnitIdList" item="item" open="(" close=")" separator="OR">
                    INSTR(CREATE_PSN_FULL_ID, #{item}) > 0
                </foreach>
                )
            </if>
            <if test="data.roleUnitIdList != null and data.roleUnitIdList.size() != 0">
                AND (
                <foreach collection="data.roleUnitIdList" item="item" open="(" close=")" separator="OR">
                    INSTR(CREATE_PSN_FULL_ID, #{item}) > 0
                </foreach>
                )
            </if>
        </where>
        ) A
        GROUP BY A.ONE_TYPE_NAME, A.ONE_TYPE_CODE
        ORDER BY A.ONE_TYPE_CODE
    </select>

    <select id="queryUnit" resultType="com.klaw.vo.contractVo.ContractReportVo">
        SELECT CREATE_PSN_FULL_ID,
               CREATE_LEGAL_UNIT_NAME,
               CREATE_LEGAL_UNIT_ID,
               COUNT(1)                                                           AS CONTRACT_COUNT,
               SUM(DECODE(SIGNED_AMOUNT, NULL, 0, SIGNED_AMOUNT))                 AS CONTRACT_AMOUNT,
               SUM(DECODE(WHETHER_MAJOR_OGN, 1, 1, 0))                            AS MAJOR_OGN_COUNT,
               SUM(CASE WHEN WHETHER_MAJOR_OGN = 1 THEN SIGNED_AMOUNT ELSE 0 END) AS MAJOR_OGN_AMOUNT,
               SUM(DECODE(PERFORM_STATE, '履行完成', 1, 0))                       AS PERFORMED_COUNT
        FROM (SELECT CREATE_PSN_FULL_ID,
                     CREATE_LEGAL_UNIT_NAME,
                     CREATE_LEGAL_UNIT_ID,
                     CASE
                         WHEN MONEY_TYPE_ID = 1 OR MONEY_TYPE_ID = 2 THEN ESTIMATED_TOTAL
                         WHEN SIGNED_AMOUNT_TAX = 0 OR SIGNED_AMOUNT_TAX IS NULL THEN NO_INCLUDING_TAX
                         ELSE INCLUDING_TAX_RMB END AS SIGNED_AMOUNT,
                     WHETHER_MAJOR_OGN,
                     PERFORM_STATE
              FROM SG_CONTRACT_APPROVAL
        <where>
            TAKE_EFFECT_CODE = '2' AND DATA_TYPE_CODE != 2
            <if test="data.effectYear != null">
                AND EXTRACT(YEAR FROM EFFECT_TIME) = #{data.effectYear}
            </if>
            <if test="data.manageUnitIdList != null and data.manageUnitIdList.size() != 0">
                AND (
                <foreach collection="data.manageUnitIdList" item="item" open="(" close=")" separator="OR">
                    INSTR(CREATE_PSN_FULL_ID, #{item}) > 0
                </foreach>
                )
            </if>
            <if test="data.manageUnitIdList == null or data.manageUnitIdList.size() == 0">
                AND REGEXP_LIKE(CREATE_PSN_FULL_ID, '15033708970596|' ||
                                                    '15033732656527|15033732617465|15033732387350|15033726526853|15033718790137|15033718021480|' ||
                                                    '15033717361091|15033725861405|15033718114424|15033719321351|15033718914844|' ||
                                                    '15033717684114|15033717585866|15033717537416|15033735057837|15033734116668|' ||
                                                    '15033733043321|15033732947135|15033732818376|15033732687039|15033732493555|' ||
                                                    '15033732041421|15033717494638|15033732446295|15033718952375|15033719246842|' ||
                                                    '15033718933648|15033718562917') > 0
            </if>
            <if test="data.roleUnitIdList != null and data.roleUnitIdList.size() != 0">
                AND (
                <foreach collection="data.roleUnitIdList" item="item" open="(" close=")" separator="OR">
                    INSTR(CREATE_PSN_FULL_ID, #{item}) > 0
                </foreach>
                )
            </if>
        </where>
        ) A
        GROUP BY CREATE_PSN_FULL_ID, CREATE_LEGAL_UNIT_NAME, CREATE_LEGAL_UNIT_ID
    </select>

    <select id="queryMain" resultType="com.klaw.vo.contractVo.ContractReportVo">
        SELECT OUR_PARTYS,
               COUNT(1)                                                           AS CONTRACT_COUNT,
               SUM(DECODE(SIGNED_AMOUNT, NULL, 0, SIGNED_AMOUNT))                 AS CONTRACT_AMOUNT,
               SUM(DECODE(WHETHER_MAJOR_OGN, 1, 1, 0))                            AS MAJOR_OGN_COUNT,
               SUM(CASE WHEN WHETHER_MAJOR_OGN = 1 THEN SIGNED_AMOUNT ELSE 0 END) AS MAJOR_OGN_AMOUNT,
               SUM(DECODE(PERFORM_STATE, '履行完成', 1, 0))                       AS PERFORMED_COUNT
        FROM (SELECT id,
                     OUR_PARTYS,
                     CASE
                         WHEN MONEY_TYPE_ID = 1 OR MONEY_TYPE_ID = 2 THEN ESTIMATED_TOTAL
                         WHEN SIGNED_AMOUNT_TAX = 0 OR SIGNED_AMOUNT_TAX IS NULL THEN NO_INCLUDING_TAX
                         ELSE INCLUDING_TAX_RMB END AS SIGNED_AMOUNT,
                     WHETHER_MAJOR_OGN,
                     PERFORM_STATE
              FROM SG_CONTRACT_APPROVAL
        WHERE TAKE_EFFECT_CODE = '2'
          AND DATA_TYPE_CODE != 2
        <if test="data.effectYear != null">
            AND EXTRACT(YEAR FROM EFFECT_TIME) = #{data.effectYear}
        </if>
        <if test="data.manageUnitIdList == null or data.manageUnitIdList.size() == 0">
            AND REGEXP_LIKE(CREATE_PSN_FULL_ID, '15033708970596|' ||
                                                '15033732656527|15033732617465|15033732387350|15033726526853|15033718790137|15033718021480|' ||
                                                '15033717361091|15033725861405|15033718114424|15033719321351|15033718914844|' ||
                                                '15033717684114|15033717585866|15033717537416|15033735057837|15033734116668|' ||
                                                '15033733043321|15033732947135|15033732818376|15033732687039|15033732493555|' ||
                                                '15033732041421|15033717494638|15033732446295|15033718952375|15033719246842|' ||
                                                '15033718933648|15033718562917') > 0
        </if>
        <if test="data.roleUnitIdList != null and data.roleUnitIdList.size() != 0">
            AND (
            <foreach collection="data.roleUnitIdList" item="item" open="(" close=")" separator="OR">
                INSTR(CREATE_PSN_FULL_ID, #{item}) > 0
            </foreach>
            )
        </if>
        ) A
        GROUP BY OUR_PARTYS
    </select>
</mapper>

