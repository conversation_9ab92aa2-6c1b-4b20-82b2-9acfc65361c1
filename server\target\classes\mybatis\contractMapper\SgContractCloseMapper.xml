<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.klaw.dao.contractDao.SgContractCloseMapper">
    <select id="selectHotels" resultType="java.util.Map">

        select a.id                as "id",
               a.data_Type_Name    as "dataTypeName",
               a.data_Type_Code    as "dataTypeCode",
               a.contract_code     as "contractCode",
               a.contract_money     as "contractMoney",
               a.money_type_code     as "moneyTypeCode",
               a.money_Type        as "moneyType",
               p.id                as "id",
               p.project_Name      as "projectName",
               p.project_Code      as "projectCode",
               p.project_Type      as "projectType",
               p.assign_Money      as "assignMoney"

        from bm_contract a
                 join bm_contract_project p
                      on a.id = p.parent_id
        where (contract_code = #{contractCode} or a.original_contract_code = #{contractCode})
        order by a.data_Type_Name
    </select>
</mapper>