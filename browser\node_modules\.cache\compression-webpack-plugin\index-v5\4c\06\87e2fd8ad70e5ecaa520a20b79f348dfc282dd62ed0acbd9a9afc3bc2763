
b795fa215fc2a3bfe56dcd5784e62418288432a1	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.230.1754018536329.js\",\"contentHash\":\"c2540d05b070e99a9ca25655f327bf95\"}","integrity":"sha512-fAOLis/SWMOoMk2FqjFzE+itkAhULMgzUD7wPQqECj67oSws7NnUtG+VYiS+QIInNTfa0CRb4G5k3Pzybw8m/Q==","time":1754018576058,"size":213736}