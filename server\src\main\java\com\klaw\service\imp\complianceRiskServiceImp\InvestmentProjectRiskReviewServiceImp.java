package com.klaw.service.imp.complianceRiskServiceImp;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.klaw.dao.complianceRiskDao.InvestmentProjectRiskReviewMapper;
import com.klaw.entity.complianceRiskBean.InvestmentProjectRiskReview;
import com.klaw.entity.contractBean.contract.BmContract;
import com.klaw.entity.contractBean.contract.BmContractClose;
import com.klaw.service.complianceRiskService.InvestmentProjectRiskReviewService;
import com.klaw.utils.DataAuthUtils;
import com.klaw.utils.PageUtils;
import com.klaw.utils.Utils;
import com.klaw.vo.Json;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class InvestmentProjectRiskReviewServiceImp extends ServiceImpl<InvestmentProjectRiskReviewMapper, InvestmentProjectRiskReview> implements InvestmentProjectRiskReviewService {

    @Autowired
    private InvestmentProjectRiskReviewMapper investmentProjectRiskReviewMapper;

    @Override
    public void saveData(InvestmentProjectRiskReview investmentProjectRiskReview) {
        investmentProjectRiskReview.setUpdateTime(new Date());
        saveOrUpdate(investmentProjectRiskReview);
    }


    @Override
    public Page<InvestmentProjectRiskReview> queryPageData(JSONObject json) {
        QueryWrapper<InvestmentProjectRiskReview> wrapper = new QueryWrapper<>();
        getFilter(json, wrapper);
        return page(new PageUtils<>(json), wrapper);
    }


    public void getFilter(JSONObject json, QueryWrapper<InvestmentProjectRiskReview> wrapper){
        String projectName = json.containsKey("projectName") ? json.getString("projectName") : null;
        // 项目编号
        String projectNumber = json.containsKey("projectNumber") ? json.getString("projectNumber") : null;
        // 项目金额
        BigDecimal projectAmount = json.containsKey("projectAmount") ? json.getBigDecimal("projectAmount") : null;
        // 业务领域
        String businessDomain = json.containsKey("businessDomain") ? json.getString("businessDomain") : null;
        // 参与评审部门
        String participatingReviewDepartment = json.containsKey("participatingReviewDepartment") ? json.getString("participatingReviewDepartment") : null;
        // 上报人
        String reporter = json.containsKey("reporter") ? json.getString("reporter") : null;
        // 上报单位
        String reportingUnit = json.containsKey("reportingUnit") ? json.getString("reportingUnit") : null;
        // 上报日期
        String reportingDate = json.containsKey("reportingDate") ? json.getString("reportingDate") : null;
        //经办时间(最小值)
        Date reportingDateMin = json.containsKey("reportingDateMin") ? json.getDate("reportingDateMin") : null;
        //经办时间(最大值)
        Date reportingDateMax = json.containsKey("reportingDateMax") ? json.getDate("reportingDateMax") : null;
        // 当前节点
        String currentNode = json.containsKey("currentNode") ? json.getString("currentNode") : null;
        // 审核状态
        String reviewStatus = json.containsKey("reviewStatus") ? json.getString("reviewStatus") : null;
        // 项目概述
        String projectOverview = json.containsKey("projectOverview") ? json.getString("projectOverview") : null;
        // 风险评估报告
        String riskAssessmentReport = json.containsKey("riskAssessmentReport") ? json.getString("riskAssessmentReport") : null;
        // 相关资料
        String relevantMaterials = json.containsKey("relevantMaterials") ? json.getString("relevantMaterials") : null;
        // 上报组织
        String reportingOrganization = json.containsKey("reportingOrganization") ? json.getString("reportingOrganization") : null;
        // 参会人员
        String participants = json.containsKey("participants") ? json.getString("participants") : null;
        // 评审意见
        String reviewOpinion = json.containsKey("reviewOpinion") ? json.getString("reviewOpinion") : null;
        // 相关附件
        String relevantAttachments = json.containsKey("relevantAttachments") ? json.getString("relevantAttachments") : null;
        // 参与部门
        String participatingDepartment = json.containsKey("participatingDepartment") ? json.getString("participatingDepartment") : null;
        // 参与人员
        String participatingPersonnel = json.containsKey("participatingPersonnel") ? json.getString("participatingPersonnel") : null;
        // 附件
        String attachments = json.containsKey("attachments") ? json.getString("attachments") : null;
        // 评审时间
        String reviewTime = json.containsKey("reviewTime") ? json.getString("reviewTime") : null;
        // 数据创建单位ID
        String createOgnId = json.containsKey("createOgnId") ? json.getString("createOgnId") : null;
        // 数据创建单位
        String createOgnName = json.containsKey("createOgnName") ? json.getString("createOgnName") : null;
        // 数据创建部门ID
        String createDeptId = json.containsKey("createDeptId") ? json.getString("createDeptId") : null;
        // 数据创建部门
        String createDeptName = json.containsKey("createDeptName") ? json.getString("createDeptName") : null;
        // 数据创建班组id
        String createGroupId = json.containsKey("createGroupId") ? json.getString("createGroupId") : null;
        // 数据创建班组
        String createGroupName = json.containsKey("createGroupName") ? json.getString("createGroupName") : null;
        // 数据创建人id
        String createPsnId = json.containsKey("createPsnId") ? json.getString("createPsnId") : null;
        // 数据创建人
        String createPsnName = json.containsKey("createPsnName") ? json.getString("createPsnName") : null;
        // 数据创建组织id
        String createOrgId = json.containsKey("createOrgId") ? json.getString("createOrgId") : null;
        // 数据创建组织
        String createOrgName = json.containsKey("createOrgName") ? json.getString("createOrgName") : null;
        // 数据创建人ID全路径
        String createPsnFullId = json.containsKey("createPsnFullId") ? json.getString("createPsnFullId") : null;
        // 数据创建人全路径
        String createPsnFullName = json.containsKey("createPsnFullName") ? json.getString("createPsnFullName") : null;
        // 数据状态
        String dataState = json.containsKey("dataState") ? json.getString("dataState") : null;
        // 数据状态id
        String dataStateCode = json.containsKey("dataStateCode") ? json.getString("dataStateCode") : null;
        // 模糊搜索值
        String fuzzyValue = json.containsKey("fuzzyValue") ? json.getString("fuzzyValue") : null;
        // 排序字段
        String sortName = json.containsKey("sortName") ? json.getString("sortName") : null;
        // 排序字段是否升序
        boolean order = json.containsKey("order") ? json.getBoolean("order") : false;
        String orgId = json.containsKey("orgId")? json.getString( "orgId"):"";

        if(StringUtils.isNotBlank(orgId)){
            wrapper.eq("create_org_id", orgId);
        }
        // 应用查询条件
        if (StringUtils.isNotBlank(projectName)) {
            wrapper.like("project_name", projectName);
        }
        if (StringUtils.isNotBlank(projectNumber)) {
            wrapper.like("project_number", projectNumber);
        }
        if (projectAmount != null) {
            wrapper.eq("project_amount", projectAmount);
        }
        if (StringUtils.isNotBlank(businessDomain)) {
            wrapper.like("business_domain", businessDomain);
        }
        if (StringUtils.isNotBlank(participatingReviewDepartment)) {
            wrapper.like("participating_review_department", participatingReviewDepartment);
        }
        if (StringUtils.isNotBlank(reporter)) {
            wrapper.like("reporter", reporter);
        }
        if (StringUtils.isNotBlank(reportingUnit)) {
            wrapper.like("reporting_unit", reportingUnit);
        }
        if (StringUtils.isNotBlank(reportingDate)) {
            wrapper.eq("reporting_date", reportingDate);
        }
        if (StringUtils.isNotBlank(currentNode)) {
            wrapper.like("current_node", currentNode);
        }
        if (StringUtils.isNotBlank(reviewStatus)) {
            wrapper.like("review_status", reviewStatus);
        }
        if (StringUtils.isNotBlank(projectOverview)) {
            wrapper.like("project_overview", projectOverview);
        }
        if (StringUtils.isNotBlank(riskAssessmentReport)) {
            wrapper.like("risk_assessment_report", riskAssessmentReport);
        }
        if (StringUtils.isNotBlank(relevantMaterials)) {
            wrapper.like("relevant_materials", relevantMaterials);
        }
        if (StringUtils.isNotBlank(reportingOrganization)) {
            wrapper.like("reporting_organization", reportingOrganization);
        }
        if (StringUtils.isNotBlank(participants)) {
            wrapper.like("participants", participants);
        }
        if (StringUtils.isNotBlank(reviewOpinion)) {
            wrapper.like("review_opinion", reviewOpinion);
        }
        if (StringUtils.isNotBlank(relevantAttachments)) {
            wrapper.like("relevant_attachments", relevantAttachments);
        }
        if (StringUtils.isNotBlank(participatingDepartment)) {
            wrapper.like("participating_department", participatingDepartment);
        }
        if (StringUtils.isNotBlank(participatingPersonnel)) {
            wrapper.like("participating_personnel", participatingPersonnel);
        }
        if (StringUtils.isNotBlank(attachments)) {
            wrapper.like("attachments", attachments);
        }
        if (StringUtils.isNotBlank(reviewTime)) {
            wrapper.eq("review_time", reviewTime);
        }
        if (StringUtils.isNotBlank(createOgnId)) {
            wrapper.eq("create_ogn_id", createOgnId);
        }
        if (StringUtils.isNotBlank(createOgnName)) {
            wrapper.like("create_ogn_name", createOgnName);
        }
        if (StringUtils.isNotBlank(createDeptId)) {
            wrapper.eq("create_dept_id", createDeptId);
        }
        if (StringUtils.isNotBlank(createDeptName)) {
            wrapper.like("create_dept_name", createDeptName);
        }
        if (StringUtils.isNotBlank(createGroupId)) {
            wrapper.eq("create_group_id", createGroupId);
        }
        if (StringUtils.isNotBlank(createGroupName)) {
            wrapper.like("create_group_name", createGroupName);
        }
        if (StringUtils.isNotBlank(createPsnId)) {
            wrapper.eq("create_psn_id", createPsnId);
        }
        if (StringUtils.isNotBlank(createPsnName)) {
            wrapper.like("create_psn_name", createPsnName);
        }
        if (StringUtils.isNotBlank(createOrgId)) {
            wrapper.eq("create_org_id", createOrgId);
        }
        if (StringUtils.isNotBlank(createOrgName)) {
            wrapper.like("create_org_name", createOrgName);
        }
        if (StringUtils.isNotBlank(createPsnFullId)) {
            wrapper.like("create_psn_full_id", createPsnFullId);
        }
        if (StringUtils.isNotBlank(createPsnFullName)) {
            wrapper.like("create_psn_full_name", createPsnFullName);
        }
        if (StringUtils.isNotBlank(dataStateCode)) {
            wrapper.like("data_state_code", dataStateCode);
        }
        if (StringUtils.isNotBlank(fuzzyValue)) {
            wrapper.and(i -> i.like("project_name", fuzzyValue)
                    .or().like("project_number", fuzzyValue)
                    .or().like("data_state", fuzzyValue))

            ;
        }
        if (reportingDateMin != null) {
            wrapper.and(i -> i.ge("reporting_date", reportingDateMin));
        }
        if (reportingDateMax != null) {
            wrapper.and(i -> i.le("reporting_date", reportingDateMax));
        }
        wrapper.orderByDesc("create_time");
        if (StringUtils.isNotBlank(sortName) && order) {
            wrapper.orderByAsc(sortName);
        } else if (StringUtils.isNotBlank(sortName)) {
            wrapper.orderByDesc(sortName);
        }
    }

    @Override
    public InvestmentProjectRiskReview queryDataById(String id) {
        InvestmentProjectRiskReview investmentProjectRiskReview = getById(id);
        return investmentProjectRiskReview;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Json deleteDataById(String id) {
        boolean flag = removeById(id);
        if (!flag) {
            return Json.fail().msg("未找到需要删除的数据");
        }
        return Json.succ().msg("删除成功!");
    }

    @Override
    public Page<InvestmentProjectRiskReview> queryPageDataForDialog(JSONObject json) {
        QueryWrapper<InvestmentProjectRiskReview> wrapper = new QueryWrapper<InvestmentProjectRiskReview>();
        String functionId = json.containsKey("functionId") ? json.getString("functionId") : "";
        String orgId = json.containsKey("orgId") ? json.getString("orgId") : "";
        //模糊搜索值
        String fuzzyValue = json.containsKey("fuzzyValue") ? json.getString("fuzzyValue") : null;
        //模糊搜索匹配字段
        String[] cols = {"project_name", "project_number", "to_char(create_time,'yyyy-MM-dd')"};

        Utils.fuzzyValueQuery(wrapper, cols, fuzzyValue);

        wrapper.orderBy(true, false, "create_time");

        DataAuthUtils.dataPermSql(wrapper, null, Long.parseLong(functionId), orgId);

        Page<InvestmentProjectRiskReview> investmentProjectRiskReviewPage = page(new PageUtils<InvestmentProjectRiskReview>(json), wrapper);


        return investmentProjectRiskReviewPage;
    }



    public void updateDataState(String businessKey) {
        //终止的流程，风评编号前加“退”
        InvestmentProjectRiskReview investmentProjectRiskReview = getById(businessKey);
        if (investmentProjectRiskReview != null) {
            investmentProjectRiskReview.setProjectNumber("退" + investmentProjectRiskReview.getProjectNumber());
            saveOrUpdate(investmentProjectRiskReview);
        }
    }
}

