package com.klaw.service.imp.contractServiceImpl.contract;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.klaw.dao.contractDao.contract.BmContractTextMapper;
import com.klaw.entity.contractBean.contract.BmContractText;
import com.klaw.service.contractService.contract.BmContractTextService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;
import java.util.List;
/**
*  服务实现类
*
* <AUTHOR>
* @since 2024-10-14
*/
@Service
    public class BmContractTextServiceImpl extends ServiceImpl<BmContractTextMapper, BmContractText> implements BmContractTextService {

    @Autowired
    BmContractTextMapper bmContractTextMapper;


    @Override
    public BmContractText getBmContractText(Integer id){

    return bmContractTextMapper.selectById(id);
    }
    @Override
    public List<BmContractText> getAllBmContractText(){
    return bmContractTextMapper.selectList(null);

    }

    @Override
    public void add( BmContractText bmContractText) {
    bmContractTextMapper.insert(bmContractText);
    }
    @Override
    public int modify( BmContractText bmContractText) {
    //乐观锁更新
    BmContractText currentBmContractText= bmContractTextMapper.selectById(bmContractText.getId());
    return  bmContractTextMapper.updateById(bmContractText);
    }

    @Override
    public void remove( String ids) {

    if(StringUtils.isNotEmpty(ids)){
    String[] array = ids.split(",");
    if (!CollectionUtils.isEmpty(Arrays.asList(array))) {
    bmContractTextMapper.deleteBatchIds(Arrays.asList(array));
    }
    }

    }

    }


