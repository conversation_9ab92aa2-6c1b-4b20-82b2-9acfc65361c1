<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.klaw.dao.systemDao.SgSysDocMapper">
  <resultMap id="BaseResultMap" type="com.klaw.entity.systemBean.SgSysDoc">
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="DOC_NAME" jdbcType="VARCHAR" property="docName" />
    <result column="DOC_TYPE" jdbcType="VARCHAR" property="docType" />
    <result column="DOC_SIZE" jdbcType="DECIMAL" property="docSize" />
    <result column="DOC_URL" jdbcType="VARCHAR" property="docUrl" />
    <result column="IS_DOC" jdbcType="DECIMAL" property="isDoc" />

    <result column="REGION_HASH" jdbcType="VARCHAR" property="regionHash" />
    <result column="UPLOAD_ID" jdbcType="VARCHAR" property="uploadId" />
    <result column="FILE_ID" jdbcType="VARCHAR" property="fileId" />
    <result column="FILE_VER_ID" jdbcType="VARCHAR" property="fileVerId" />
    <result column="DOC_CODE" jdbcType="VARCHAR" property="docCode" />

    <result column="PARENT_ID" jdbcType="VARCHAR" property="parentId" />
    <result column="PARENT_NAME" jdbcType="VARCHAR" property="parentName" />
    <result column="CREATE_OGN_ID" jdbcType="VARCHAR" property="createOgnId" />
    <result column="CREATE_OGN_NAME" jdbcType="VARCHAR" property="createOgnName" />
    <result column="CREATE_DEPT_ID" jdbcType="VARCHAR" property="createDeptId" />
    <result column="CREATE_DEPT_NAME" jdbcType="VARCHAR" property="createDeptName" />
    <result column="CREATE_GROUP_ID" jdbcType="VARCHAR" property="createGroupId" />
    <result column="CREATE_GROUP_NAME" jdbcType="VARCHAR" property="createGroupName" />
    <result column="CREATE_ORG_ID" jdbcType="VARCHAR" property="createOrgId" />
    <result column="CREATE_ORG_NAME" jdbcType="VARCHAR" property="createOrgName" />
    <result column="CREATE_PSN_ID" jdbcType="VARCHAR" property="createPsnId" />
    <result column="CREATE_PSN_NAME" jdbcType="VARCHAR" property="createPsnName" />
    <result column="CREATE_PSN_FULL_ID" jdbcType="VARCHAR" property="createPsnFullId" />
    <result column="CREATE_PSN_FULL_NAME" jdbcType="VARCHAR" property="createPsnFullName" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="DATA_STATE_CODE" jdbcType="DECIMAL" property="dataStateCode" />
    <result column="DATA_STATE" jdbcType="VARCHAR" property="dataState" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    ID, DOC_NAME, DOC_TYPE, DOC_SIZE, DOC_URL, IS_DOC, PARENT_ID, PARENT_NAME, CREATE_OGN_ID,
    CREATE_OGN_NAME, CREATE_DEPT_ID, CREATE_DEPT_NAME, CREATE_GROUP_ID, CREATE_GROUP_NAME, 
    CREATE_ORG_ID, CREATE_ORG_NAME, CREATE_PSN_ID, CREATE_PSN_NAME, CREATE_PSN_FULL_ID, 
    CREATE_PSN_FULL_NAME, CREATE_TIME, DATA_STATE_CODE, DATA_STATE, UPDATE_TIME,REGION_HASH,UPLOAD_ID,FILE_ID,FILE_VER_ID,DOC_CODE,FILE_PLACE
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from SG_SYS_DOC
    where ID = #{id,jdbcType=VARCHAR}
  </select>
  <select id="queryDocByUrl" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from SG_SYS_DOC
    where DOC_CODE = #{url,jdbcType=VARCHAR}
  </select>
  <select id="queryDocByUrlAndId" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from SG_SYS_DOC
    where DOC_CODE = #{url,jdbcType=VARCHAR} and PARENT_ID = #{parentId,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from SG_SYS_DOC
    where ID = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.klaw.entity.systemBean.SgSysDoc">
    insert into SG_SYS_DOC (ID, DOC_NAME, DOC_TYPE,
      DOC_SIZE, DOC_URL, IS_DOC, 
      PARENT_ID, PARENT_NAME, CREATE_OGN_ID, 
      CREATE_OGN_NAME, CREATE_DEPT_ID, CREATE_DEPT_NAME, 
      CREATE_GROUP_ID, CREATE_GROUP_NAME, CREATE_ORG_ID, 
      CREATE_ORG_NAME, CREATE_PSN_ID, CREATE_PSN_NAME, 
      CREATE_PSN_FULL_ID, CREATE_PSN_FULL_NAME, CREATE_TIME, 
      DATA_STATE_CODE, DATA_STATE, UPDATE_TIME
      )
    values (#{id,jdbcType=VARCHAR}, #{docName,jdbcType=VARCHAR}, #{docType,jdbcType=VARCHAR}, 
      #{docSize,jdbcType=DECIMAL}, #{docUrl,jdbcType=VARCHAR}, #{isDoc,jdbcType=DECIMAL}, 
      #{parentId,jdbcType=VARCHAR}, #{parentName,jdbcType=VARCHAR}, #{createOgnId,jdbcType=VARCHAR}, 
      #{createOgnName,jdbcType=VARCHAR}, #{createDeptId,jdbcType=VARCHAR}, #{createDeptName,jdbcType=VARCHAR}, 
      #{createGroupId,jdbcType=VARCHAR}, #{createGroupName,jdbcType=VARCHAR}, #{createOrgId,jdbcType=VARCHAR}, 
      #{createOrgName,jdbcType=VARCHAR}, #{createPsnId,jdbcType=VARCHAR}, #{createPsnName,jdbcType=VARCHAR}, 
      #{createPsnFullId,jdbcType=VARCHAR}, #{createPsnFullName,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{dataStateCode,jdbcType=DECIMAL}, #{dataState,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.klaw.entity.systemBean.SgSysDoc">
    insert into SG_SYS_DOC
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="docName != null">
        DOC_NAME,
      </if>
      <if test="docCode != null">
        DOC_CODE,
      </if>
      <if test="fileId != null">
        FILE_ID,
      </if>
      <if test="uploadId != null">
        UPLOAD_ID,
      </if>
      <if test="regionHash != null">
        REGION_HASH,
      </if>
      <if test="fileVerId != null">
        FILE_VER_ID,
      </if>
      <if test="docType != null">
        DOC_TYPE,
      </if>
      <if test="docSize != null">
        DOC_SIZE,
      </if>
      <if test="docUrl != null">
        DOC_URL,
      </if>
      <if test="isDoc != null">
        IS_DOC,
      </if>
      <if test="parentId != null">
        PARENT_ID,
      </if>
      <if test="parentName != null">
        PARENT_NAME,
      </if>
      <if test="createOgnId != null">
        CREATE_OGN_ID,
      </if>
      <if test="createOgnName != null">
        CREATE_OGN_NAME,
      </if>
      <if test="createDeptId != null">
        CREATE_DEPT_ID,
      </if>
      <if test="createDeptName != null">
        CREATE_DEPT_NAME,
      </if>
      <if test="createGroupId != null">
        CREATE_GROUP_ID,
      </if>
      <if test="createGroupName != null">
        CREATE_GROUP_NAME,
      </if>
      <if test="createOrgId != null">
        CREATE_ORG_ID,
      </if>
      <if test="createOrgName != null">
        CREATE_ORG_NAME,
      </if>
      <if test="createPsnId != null">
        CREATE_PSN_ID,
      </if>
      <if test="createPsnName != null">
        CREATE_PSN_NAME,
      </if>
      <if test="createPsnFullId != null">
        CREATE_PSN_FULL_ID,
      </if>
      <if test="createPsnFullName != null">
        CREATE_PSN_FULL_NAME,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="dataStateCode != null">
        DATA_STATE_CODE,
      </if>
      <if test="dataState != null">
        DATA_STATE,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
      <if test="filePlace != null">
        FILE_PLACE,
      </if>
      <if test="obsVerId != null">
        OBS_VER_ID,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="docName != null">
        #{docName,jdbcType=VARCHAR},
      </if>

      <if test=" docCode!= null">
        #{docCode,jdbcType=VARCHAR},
      </if>
      <if test="fileId != null">
        #{fileId,jdbcType=VARCHAR},
      </if>
      <if test="uploadId != null">
        #{uploadId,jdbcType=VARCHAR},
      </if>
      <if test="regionHash != null">
        #{regionHash,jdbcType=VARCHAR},
      </if>
      <if test="fileVerId != null">
        #{fileVerId,jdbcType=VARCHAR},
      </if>
      <if test="docType != null">
        #{docType,jdbcType=VARCHAR},
      </if>
      <if test="docSize != null">
        #{docSize,jdbcType=DECIMAL},
      </if>
      <if test="docUrl != null">
        #{docUrl,jdbcType=VARCHAR},
      </if>
      <if test="isDoc != null">
        #{isDoc,jdbcType=DECIMAL},
      </if>
      <if test="parentId != null">
        #{parentId,jdbcType=VARCHAR},
      </if>
      <if test="parentName != null">
        #{parentName,jdbcType=VARCHAR},
      </if>
      <if test="createOgnId != null">
        #{createOgnId,jdbcType=VARCHAR},
      </if>
      <if test="createOgnName != null">
        #{createOgnName,jdbcType=VARCHAR},
      </if>
      <if test="createDeptId != null">
        #{createDeptId,jdbcType=VARCHAR},
      </if>
      <if test="createDeptName != null">
        #{createDeptName,jdbcType=VARCHAR},
      </if>
      <if test="createGroupId != null">
        #{createGroupId,jdbcType=VARCHAR},
      </if>
      <if test="createGroupName != null">
        #{createGroupName,jdbcType=VARCHAR},
      </if>
      <if test="createOrgId != null">
        #{createOrgId,jdbcType=VARCHAR},
      </if>
      <if test="createOrgName != null">
        #{createOrgName,jdbcType=VARCHAR},
      </if>
      <if test="createPsnId != null">
        #{createPsnId,jdbcType=VARCHAR},
      </if>
      <if test="createPsnName != null">
        #{createPsnName,jdbcType=VARCHAR},
      </if>
      <if test="createPsnFullId != null">
        #{createPsnFullId,jdbcType=VARCHAR},
      </if>
      <if test="createPsnFullName != null">
        #{createPsnFullName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="dataStateCode != null">
        #{dataStateCode,jdbcType=DECIMAL},
      </if>
      <if test="dataState != null">
        #{dataState,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="filePlace != null">
        #{filePlace,jdbcType=VARCHAR},
      </if>
      <if test="obsVerId != null">
        #{obsVerId,jdbcType=VARCHAR}
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.klaw.entity.systemBean.SgSysDoc">
    update SG_SYS_DOC
    <set>
      <if test="docName != null">
        DOC_NAME = #{docName,jdbcType=VARCHAR},
      </if>
      <if test="docType != null">
        DOC_TYPE = #{docType,jdbcType=VARCHAR},
      </if>
      <if test="docSize != null">
        DOC_SIZE = #{docSize,jdbcType=DECIMAL},
      </if>
      <if test="docUrl != null">
        DOC_URL = #{docUrl,jdbcType=VARCHAR},
      </if>
      <if test="isDoc != null">
        IS_DOC = #{isDoc,jdbcType=DECIMAL},
      </if>
      <if test="parentId != null">
        PARENT_ID = #{parentId,jdbcType=VARCHAR},
      </if>
      <if test="parentName != null">
        PARENT_NAME = #{parentName,jdbcType=VARCHAR},
      </if>
      <if test="createOgnId != null">
        CREATE_OGN_ID = #{createOgnId,jdbcType=VARCHAR},
      </if>
      <if test="createOgnName != null">
        CREATE_OGN_NAME = #{createOgnName,jdbcType=VARCHAR},
      </if>
      <if test="createDeptId != null">
        CREATE_DEPT_ID = #{createDeptId,jdbcType=VARCHAR},
      </if>
      <if test="createDeptName != null">
        CREATE_DEPT_NAME = #{createDeptName,jdbcType=VARCHAR},
      </if>
      <if test="createGroupId != null">
        CREATE_GROUP_ID = #{createGroupId,jdbcType=VARCHAR},
      </if>
      <if test="createGroupName != null">
        CREATE_GROUP_NAME = #{createGroupName,jdbcType=VARCHAR},
      </if>
      <if test="createOrgId != null">
        CREATE_ORG_ID = #{createOrgId,jdbcType=VARCHAR},
      </if>
      <if test="createOrgName != null">
        CREATE_ORG_NAME = #{createOrgName,jdbcType=VARCHAR},
      </if>
      <if test="createPsnId != null">
        CREATE_PSN_ID = #{createPsnId,jdbcType=VARCHAR},
      </if>
      <if test="createPsnName != null">
        CREATE_PSN_NAME = #{createPsnName,jdbcType=VARCHAR},
      </if>
      <if test="createPsnFullId != null">
        CREATE_PSN_FULL_ID = #{createPsnFullId,jdbcType=VARCHAR},
      </if>
      <if test="createPsnFullName != null">
        CREATE_PSN_FULL_NAME = #{createPsnFullName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="dataStateCode != null">
        DATA_STATE_CODE = #{dataStateCode,jdbcType=DECIMAL},
      </if>
      <if test="dataState != null">
        DATA_STATE = #{dataState,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="filePlace != null">
        #{FILE_PLACE,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.klaw.entity.systemBean.SgSysDoc">
    update SG_SYS_DOC
    set DOC_NAME = #{docName,jdbcType=VARCHAR},
      DOC_TYPE = #{docType,jdbcType=VARCHAR},
      DOC_SIZE = #{docSize,jdbcType=DECIMAL},
      DOC_URL = #{docUrl,jdbcType=VARCHAR},
      IS_DOC = #{isDoc,jdbcType=DECIMAL},
      PARENT_ID = #{parentId,jdbcType=VARCHAR},
      PARENT_NAME = #{parentName,jdbcType=VARCHAR},
      CREATE_OGN_ID = #{createOgnId,jdbcType=VARCHAR},
      CREATE_OGN_NAME = #{createOgnName,jdbcType=VARCHAR},
      CREATE_DEPT_ID = #{createDeptId,jdbcType=VARCHAR},
      CREATE_DEPT_NAME = #{createDeptName,jdbcType=VARCHAR},
      CREATE_GROUP_ID = #{createGroupId,jdbcType=VARCHAR},
      CREATE_GROUP_NAME = #{createGroupName,jdbcType=VARCHAR},
      CREATE_ORG_ID = #{createOrgId,jdbcType=VARCHAR},
      CREATE_ORG_NAME = #{createOrgName,jdbcType=VARCHAR},
      CREATE_PSN_ID = #{createPsnId,jdbcType=VARCHAR},
      CREATE_PSN_NAME = #{createPsnName,jdbcType=VARCHAR},
      CREATE_PSN_FULL_ID = #{createPsnFullId,jdbcType=VARCHAR},
      CREATE_PSN_FULL_NAME = #{createPsnFullName,jdbcType=VARCHAR},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      DATA_STATE_CODE = #{dataStateCode,jdbcType=DECIMAL},
      DATA_STATE = #{dataState,jdbcType=VARCHAR},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      FILE_PLACE = #{filePlace,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <select id="selectByAll" resultMap="BaseResultMap" parameterType="com.klaw.entity.systemBean.SgSysDoc">
    select
    <include refid="Base_Column_List" />
    from SG_SYS_DOC
    <where>
      <if test="docName != null">
         DOC_NAME = #{docName,jdbcType=VARCHAR }
      </if>
      <if test="docType != null">
        and DOC_TYPE = #{docType,jdbcType=VARCHAR }
      </if>
      <if test="parentId != null">
        and PARENT_ID = #{parentId,jdbcType=VARCHAR }
      </if>
      <if test="parentName != null">
        and PARENT_NAME = #{parentName,jdbcType=VARCHAR}
      </if>

    </where>
  </select>
</mapper>