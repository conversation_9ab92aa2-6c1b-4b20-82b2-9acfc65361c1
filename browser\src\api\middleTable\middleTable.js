import {request} from '@/api/index'

export default {
    queryByContractId(data) {
        return request({
            url: '/middleTable/queryByContractId',
            method: 'post',
            data
        })
    },
    updateContractPerformResult(data) {
        return request({
            url: '/middleTable/updateContractPerformResult',
            method: 'post',
            data
        })
    },
}

