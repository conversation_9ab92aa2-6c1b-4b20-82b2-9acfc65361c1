<template>
  <FormWindow ref="formWindow">
    <el-container v-loading="loading" style="height: calc(100vh - 150px);" :element-loading-text="loadingText">



      <el-main id="scrollbar">
        <el-card ref="printRef" style="height: auto;margin-left: 1%;margin-right: 2%;margin-top: 1%;">
          <el-scrollbar style="height: 100%;overflow-y: hidden">
            <el-form ref="dataForm" style="margin-left: 10px;margin-right: 10px;" :model="mainData" :rules="rules"
              :class="className">
              <el-row style="margin-top: 20px;">
                <span style="text-align: left;font-size: 23px;margin-left: 43%;font-weight: 900;">协同监督审批单</span>
              </el-row>
              <!--              <qs-base-info :data.sync="mainData" :data-state="dataState" :authorization-data="authorizationData" :view="view" :create="create"/>-->
              <SimpleBoardTitleApproval title="基本信息">
                <table class="table_content">
                  <tbody>
                    <tr>
                      <th colspan="3" class="th_label_approval">主题</th>
                      <td colspan="9" class="td_value_approval">{{ mainData.accountabilitySubject }}</td>
                      <th colspan="3" class="th_label_approval">审批单号</th>
                      <td colspan="9" class="td_value_approval">{{ mainData.code }}</td>
                    </tr>
                    <tr>
                      <th colspan="3" class="th_label_approval">时间</th>
                      <td colspan="9" class="td_value_approval">{{ mainData.accountabilityTime | parseTime }}</td>
                      <th colspan="3" class="th_label_approval">对象</th>
                      <td colspan="9" class="td_value_approval">{{ mainData.responsibleParty }}</td>
                    </tr>
                    <tr>
                      <th colspan="3" class="th_label_approval">协同单位</th>
                      <td colspan="21" class="td_value_approval">{{ mainData.oaDept }}</td>
                    </tr>
                    <tr>
                      <th colspan="3" class="th_label_approval_">关联事件</th>
                      <td colspan="21" class="td_value_approval_">{{ mainData.relatedMatter }}</td>
                    </tr>
                    <tr>
                      <th colspan="3" class="th_label_approval_">违规事项</th>
                      <td colspan="21" class="td_value_approval_">{{ mainData.violationMatter }}</td>
                    </tr>
                  </tbody>

                  <tbody>
                    <tr>
                      <th colspan="3" class="th_label_approval">附件材料</th>
                      <td colspan="21" class="td_value_approval">
                        <div v-if="mainData.uploadAttachment">
                          <UploadDoc :files.sync="mainData.uploadAttachment" doc-path="/case" :disabled="true" />
                        </div>
                        <div v-else style="font-size: 15px">无</div>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </SimpleBoardTitleApproval>
              <!--审批历史 -->
              <SimpleBoardTitleApproval style="margin-top: 10px;" title="审查意见" class="print-table-wrap">
                <ProcessOpinion style="border: 1px solid #606266;" :proc-inst-id="this.obj.processInstanceId"
                  :task-id="this.obj.taskId" type-code="1" />
                <div v-if="approvalIs && isParseElement">
                  <el-input type="textarea" :rows="2" style="border-radius: 0 !important;" placeholder="请输入审批意见"
                    v-model="approvalOpinion">
                  </el-input>
                </div>
              </SimpleBoardTitleApproval>

              <SimpleBoardTitleApproval style="margin-top: 10px;" title="领导意见" class="leadership-opinions-section-wrap">
                <div style="border: solid 1px #606266;overflow: hidden">
                  <ProcessOpinion :show-header="false" :proc-inst-id="this.obj.processInstanceId"
                    :task-id="this.obj.taskId" type-code="2" />
                  <div v-if="approvalIs && isParseElementlg">
                    <el-input type="textarea" :rows="2" style="border-radius: 0 !important;" placeholder="请输入审批意见"
                      v-model="approvalOpinion">
                    </el-input>
                  </div>
                </div>
              </SimpleBoardTitleApproval>

            </el-form>
          </el-scrollbar>
        </el-card>
      </el-main>

      <oa-records-dialog :visible.sync="oarecordsDialog" :dataid.sync="oaid" />

      <!--      <Shortcut :approval-show="approvalShow" :noticeShow="noticeShow" :detail-show="detailShow"-->
      <!--                :print-show="printShow" @approvalClick="approvalClick" @detailClick="detailClick"-->
      <!--                @noticeClick="noticeClick" @templateClick="templateClick" @printClick="printClick"/>-->
      <Shortcut :approval-show="approvalShow" :download-show="downloadShow" :noticeShow="noticeShow"
        :detail-show="detailShow" :print-show="printShow" @approvalClick="approvalClick" @detailClick="detailClick"
        @noticeClick="noticeClick" @printClick="printClick" @downloadClick="downloadClick" />
    </el-container>
  </FormWindow>
</template>

<script>
// vuex缓存数据
import { mapGetters } from 'vuex'

// 接口api
import ComplianceAccountabilityApi from '@/api/risk/ComplianceAccountability'
import processApi from '@/api/_system/process'
import taskApi from "@/api/_system/task"
import noticeApi from "@/api/_system/notice";
import orgApi from '@/api/_system/org'
// 组件
import FormWindow from '@/view/components/FormWindow/FormWindow'
import OtherInfo from '@/view/litigation/caseManage/case/caseChild/OtherInfo'
import OrgSingleDialogSelect from '@/view/components/OrgSingleDialogSelect/index'
import ProsecutionDialog from './ProsecutionDialog'
import QsBaseInfo from './qisubaseInfo'
import Claim from '@/view/litigation/caseManage/case/caseChild/Claim.vue'
import Litigant from '@/view/litigation/caseManage/case/caseChild/Litigant'
import CaseData from '@/view/litigation/caseManage/case/caseChild/CaseData'
import CaseEvidenceData from '@/view/litigation/caseManage/case/caseChild/CaseEvidenceData'
import OaRecordsDialog from '@/view/litigation/caseManage/caseProsecution/OarecordsDialog'
import Shortcut from '@/view/litigation/caseManage/caseExamine/Shortcut'
import relations from '@/view/litigation/caseManage/caseProsecution/child/Relations'
import SimpleBoardTitle from "@/view/components/SimpleBoard/SimpleBoardTitle"
import Authorization from '@/view/litigation/authorizationManage/authorization/child/AuthorizationLitigation'
import ProcessOpinion from "@/view/components/ProcessOpinion/ProcessOpinion";
import SimpleBoardTitleApproval from "@/view/components/SimpleBoard/SimpleBoardTitleApproval";
import seal from '@/view/litigation/contractManage/contractApproval/child/seal'
import doc from "@/api/_system/doc";
import UploadDoc from "@/view/components/UploadDoc/UploadDoc.vue";

export default {
  name: 'HgwzMain',
  inject: ['layout', 'mcpLayout', 'mcpDesignPage'],
  components: {
    UploadDoc,
    SimpleBoardTitleApproval,
    CaseData, Litigant, Claim, QsBaseInfo, ProsecutionDialog, OrgSingleDialogSelect,
    FormWindow, OtherInfo, CaseEvidenceData, OaRecordsDialog, Shortcut, relations,
    SimpleBoardTitle, Authorization, ProcessOpinion, seal
  },
  computed: {
    ...mapGetters(['orgContext']),
    isView: function () {
      return this.dataState === this.utils.formState.VIEW
    },
    isNotice() {
      const isNotice = this.$route.query.isNotice
      return this.mainData.dataStateCode !== this.utils.dataState_BPM.FINISH.code &&
        this.mainData.createPsnFullId === this.orgContext.currentPsnFullId && isNotice
    },
    noticeShow() {
      return this.mainData.dataStateCode === this.utils.dataState_BPM.FINISH.code
    },
    detailShow() {
      return this.view === 'new'
    },
    approvalShow() {
      return this.view === 'old'
    },
    printShow() {
      return this.view === 'new'
    },
    // 如果是从oa打开，则需需要加上这两个参数
    originOa() {
      let { origin, fullScreen } = this.$route.query
      return origin === 'oa' && fullScreen
    },
    downloadShow() {
      if (this.view === undefined) {
        return true
      }
      return this.view === 'new' || this.view === 'detail'
    },
    isParseElement() {
      return this.parseElement === '部门意见'
    },
    isParseElementlg() {
      return this.parseElement === '领导意见'
    }
  },
  data() {
    return {
      approvalOpinion: '',
      parseElement: null,
      approvalIs: false,
      className: '',
      qpShow: false,
      object1: null,
      type: null,
      timer: false,
      tabId: null,
      oarecordsDialog: false,
      loading: false,
      dataState: null,
      functionId: null,//终止的时候要用，需要手动关闭
      dataId: null,
      taskId: null,
      oaid: null,
      authorizationData: {},//授权信息
      authorizationList: [],//授权受托人信息
      view: 'new',
      create: '',
      mainData: {
        riskName: null, // 风险名称
        riskCode: null, // 风险编号
        eventNature: null, // 事件性质
        expectedRiskLevel: null, // 预计风险等级
        businessField: null, // 业务领域
        internalExternalRisk: null, // 内/外部风险
        positiveNegativeImpact: null, // 正/负面影响
        involvedAmount: null, // 涉及金额(元)/案件金额-本金(元)
        caseInterest: null, // 案件金额-利息(元)
        riskDescription: null, // 风险描述
        riskReason: null, // 风险原因
        reportingPerson: null,//上报人
        party: null,//对方当事人
        reportingUnit: null,//上报单位
        potentialConsequences: null, // 潜在后果
        relatedAttachments: null, // 附件材料
        id: null, //主键
        authorizationId: null, //授权主键id
        accountabilitySubject: null, //事项名称
        caseCode: null, //编号
        causeOfInId: null,//案件类型ID
        causeOfIn: null,//案件类型
        causeOfInDes: null,//案件类型说明
        caseMoney: null, //标的额(元)
        projectName: null,//项目名称
        whetherMajor: false,//是否重大案件
        whetherAuthorized: false, //是否单项授权
        venueIsOut: false, //是否集团内部诉讼
        venueAddress: null, //详细地址
        venueProvince: null, //省
        venueCity: null, //市
        venueRegion: null, //区
        caseUndertaker: null,//法务承办人
        caseUndertakerId: null,//法务承办人id
        ognPackage: null,//单位包案领导
        ognPackageId: null,//单位包案领导id
        groupPackage: null,//集团包案领导
        groupPackageId: null,//集团包案领导id
        involvedLevel: null,//涉案单位管理层级
        involvedLevelId: null,//涉案单位管理层级
        unitType: null,//单位类型
        unitTypeId: null,//单位类型id
        currentUnit: null,//当事单位
        currentUnitId: null,//当事单位id
        des: null,//情况说明
        greatReason: null,//重大案件案发原因
        greatPlan: null,//重大案件下一步工作计划
        files: null,//汇报材料
        managementUnit: null,//管理单位
        managementUnitId: null,//管理单位
        noticeDeptName: null, //会知部门名称
        noticeDeptId: null, //会知部门id
        ourPosition: '原告',
        suedUnitType: null, //被诉单位性质
        suedUnitTypeId: null, //被诉单位性质id
        partiesList: [],
        claimList: [],
        otherDataList: [],
        relations: [],
        sealList: [],
        createOgnId: null,            //当前机构ID
        createOgnName: null,     //当前机构名称
        createDeptId: null,           //当前部门ID
        createDeptName: null,   //当前部门名称
        createGroupId: null,           //当前部门ID
        createGroupName: null,   //当前部门名称
        createPsnId: null,            //当前人ID
        createPsnName: null,          //当前人名称
        createOrgId: null,          //当前组织ID
        createOrgName: null,          //当前组织名称
        createPsnFullId: null,            //当前人全路径ID
        createPsnFullName: null,//当前人全路径名称
        createTime: null,//创建时间
        dataState: this.utils.dataState_BPM.SAVE.name, //数据状态
        dataStateCode: this.utils.dataState_BPM.SAVE.code, //数据状态编码
        eventReport: null, //事项报告
      },

      orgTreeDialog: false,
      orgDialogTitle: '组织信息',
      isAssign: false,

      prosecutionDialog: false,

      rules: {
        accountabilitySubject: [
          { required: true, message: '请填写事项名称', trigger: 'blur' }
        ],
        belongPlate: [
          { required: true, message: '请选择所属单位', trigger: 'blur' }
        ],
        application: [
          { required: true, message: '请选择申请事项', trigger: 'blur' }
        ],
        projectName: [
          { required: true, message: '请填写项目名称', trigger: 'blur' }
        ]
      },

      activity: null,//记录当前待办处于流程实例的哪个环节
      obj: {// 流程处理逻辑需要的各种参数
        taskId: null,
        processInstanceId: null,
        businessKey: null,
        title: null,
        functionName: '协同监督审批',
        functionCode: null,
        sid: null,
      },

      noticeParams: {},
      noticeData: {
        moduleName: '', // 模块名称
        dataId: '', // 数据ID
        url: '', // 地址
        title: '', // 地址
        params: {} // 其他参数
      },

      loadingText: '加载中...'
    }
  },
  mounted() {
    //挂载完毕后，设置回调函数
    this.$emit('setCallBack', {
      beforeCallBack: this.beforeApproval,//点击办理或提交前的回调，效验必填控制
      afterCallBack: this.afterApproval,//点击办理弹框中再点击确定后的回调，审批完成后处理业务逻辑
      setTaskNodeInfo: this.setTaskNodeInfo,//挂载完毕后执行的回调，用于页面已进入需要处理的业务逻辑
      filterBtn: this.utils.filterBtn,
      beforeConfirmCallBack: this.beforeConfirmCallBack,
      beforeApprovalCb: this.beforeApprovalCb
    })
  },
  provide() {
    return {
      parentCase: this
    }
  },
  created() {
    //因为是流程功能，知会、抄送，都需要按照流程抄送的配置打开，
    // 只是知会的需要更新消息表，抄送需要更新日志表
    //判断是知会还是抄送，可以根据param中的参数isNotice判断
    this.obj.functionName = '协同监督审批'
    const isNotice = this.$route.query.isNotice
    // isNotice在OA中打开会解析成Boolean，系统内会被转成字符"true"
    if (isNotice === true || isNotice === "true") {
      //知会
      if (this.$route.query.processInstanceId !== null && this.$route.query.processInstanceId !== undefined) {
        //保存的数据，获取不到实例ID，只有启动流程实例才能拿到
        this.obj.processInstanceId = this.$route.query.processInstanceId
        this.obj.taskId = this.$route.query.taskId
      }
      const read = this.$route.query.read
      const sid = this.$route.query.sid
      if (read === false || read === 'false') {
        noticeApi.read({ sid: sid })
      }
    } else {
      //这里除了抄送会走，其他正常逻辑也会走，所以下面的参数判断了type === 'toRead'，即未读时，才会更新OA消息和日志记录
      this.obj.sid = this.$route.query.sid//消息表中的消息ID 日志表中的日志ID
      if (this.$route.query.processInstanceId !== null && this.$route.query.processInstanceId !== undefined) {
        //保存的数据，获取不到实例ID，只有启动流程实例才能拿到
        this.obj.processInstanceId = this.$route.query.processInstanceId
        this.obj.taskId = this.$route.query.taskId
      }
      const type = this.$route.query.type//获取消息状态类型，toRead-》未读，此时需要更新，haveRead-》已读，就不需要更新了
      if (type === 'toRead') {
        this.obj.pathname = window.location.pathname
        //返回url路径名（https://www.runoob.com/try/try.php?filename=tryjsref_loc_pathname，返回/try/try.php），判断是在法务系统打开还是在OA中打开
        //法务中打开会把相同流程实例的全部消息改为已读，所以是更新OA多条，OA中打开是只更新OA一条
        this.obj.title = "协同监督审批"
        //更新OA需要参数processInstanceId, title, functionName, oldTaskId,
        processApi.finishOATask(this.obj)
      }
    }
  },
  methods: {
    setTaskNodeInfo(event) {
      const customProperties = event.customProperties.find(item => item.key === 'FlowCustomPropertiesSettingsServiceImpl')
      if (customProperties !== null && customProperties !== undefined) {
        this.parseElement = JSON.parse(customProperties.value)['name']
      }
      console.log("部门意见：" + this.parseElement)
      //（toDeal-待办处理，haveDealt-已办查看，toRead-未读，haveRead-已读）
      const type = this.$route.query.type
      // 业务ID
      const id = this.$route.query.businessKey
      //是否首环节（submitNode-首环节）
      this.activity = event.taskNodeType
      /*
      * 首环节
      *   1、businessKey-是null，说明是刚发起---执行init方法
      *   2、businessKey-有值
      *       1、回退处理--环节是submitNode   ---执行init
      *       2、已办查看--优先判断  haveDealt---执行load
      *       3、未读查看--优先判断  toRead   ---执行load
      *       4、已读查看--优先判断  haveRead ---执行load
      * */
      if (type === 'haveDealt' || type === 'toRead' || type === 'haveRead') {
        this.loadData(this.utils.formState.VIEW, id)
      }
      else { // 不是以上3种只能是待处理，只需要判断是否首环节即可
        if (event.taskNodeType === 'submitNode') {
          this.loadData(this.utils.formState.NEW, id)
        } else {
          this.loadData(this.utils.formState.VIEW, id)
        }
      }
    },
    loadData(dataState, dataId) {
      this.functionId = this.$route.query.functionId
      if (this.$route.query.view !== undefined && this.$route.query.view !== '')
        this.view = this.$route.query.view
      if (this.$route.query.type !== undefined && this.$route.query.type !== '')
        this.type = this.$route.query.type
      if (this.$route.query.create !== undefined && this.$route.query.create !== '')
        this.create = this.$route.query.create

      this.dataState = dataState
      ComplianceAccountabilityApi.queryById(dataId).then(response => {
        this.mainData = response.data.data
        this.authorizationData = response.data.data.authorization
        if (response.data.data.authorization !== null)
          this.authorizationList = response.data.data.authorization.authorizationLitigationList
        this.loading = false
      }).then(() => {
        this.approvalOpinionIs()
      })
    },
    //提交前 校验必填，resolve--校验成功或者失败需要将结果返回
    beforeApproval(code, resolve) {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.save().then(response => {
            this.mainData.title = this.mainData.accountabilitySubject
            this.mainData.code = code === null ? '' : code
            this.mainData.functionName = '协同监督审批'
            this.$emit('submit-success', this.mainData, 'id')
            this.$emit('setCallBack', {
              variables: { formData: { ...this.mainData } }
            })
            resolve({ success: true, formData: { ...this.mainData }, approvalOpinion: this.approvalOpinion })
          })
        } else {
          resolve(false)
          this.$nextTick(function () {
            document.querySelector('.is-error').scrollIntoView(false)
          })
          return false
        }
      })
    },
    //撤回转办
    beforeApprovalCb(code) {
      debugger
      if (code.type === "cancelTransfer") {
        this.obj.processInstanceId = this.$route.query.processInstanceId
        this.obj.taskId = this.$route.query.taskId
        this.obj.title = this.mainData.accountabilitySubject
        this.obj.code = code === null ? '' : code
        this.obj.functionName = '协同监督审批'
        let pathname = window.location.pathname
        this.obj.functionCode = 'xtjd_main'
        processApi.sendOATask(this.obj).then(res => {
          if (pathname === '/base/design_page') {
            this.mcpLayout.closeTab()
          } else {
            window.close()
          }
        })
      }

    },
    afterApproval(code, data) {
      // 回退到首环节，修改业务数据状态，修改任务标题
      console.log("code==" + code)
      console.log("data==" + data)
      this.obj.businessKey = this.mainData.id
      //获取参数，为后续操作准备，processInstanceId和taskId其实在created中赋值了，这里在赋值一次也行
      if (data != null && data.data != null) {
        this.obj.processInstanceId = data.data.id
      }
      if (this.obj.businessKey == null || this.obj.processInstanceId == null) {
        this.obj.processInstanceId = this.$route.query.processInstanceId
        this.obj.taskId = this.$route.query.taskId
      }

      //需要传参数到流程中，这里操作，不限于首环节传参数
      if (this.activity === 'submitNode') {
        ComplianceAccountabilityApi.setParam(this.obj).then(response => {
          console.log("传值成功")
        })
      }
      // 将部分参数传给OA
      this.obj.title = this.mainData.accountabilitySubject
      this.obj.code = code === null ? '' : code
      this.obj.functionName = '协同监督审批'
      //不是动态节点，给OA传待办
      if (code !== 'dynamic') {
        let loading = this.$loading({
          target: document.querySelector('.sg-page-wrap'),
          lock: false,
          text: '请稍后...',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        })
        processApi.sendOATask(this.obj).then(res => {
          loading.close()
          if (this.originOa) {
            window.close()
          }
          /*let pathname = window.location.pathname
          if (pathname.indexOf('/design_pages') !== -1) {
            window.close()
          }*/
        })
      }
    },
    beforeConfirmCallBack(data, resolve, reject) {
      const customProperties = data.nodeInfo.customProperties.find(item => item.key === 'FlowCustomPropertiesSettingsServiceImpl')
      if (customProperties && customProperties.value && JSON.parse(customProperties.value)['id'] === "1" && data.approvalFormData.comment == "") {
        // 消息按需求编辑 这只是个示例
        this.$confirm('未填写意见，请确认是否继续。', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          resolve()()
        }).catch(() => {
          reject()
        })
      } else {
        resolve()
      }
    },
    handleConditionBranch(branch, resolve) {
      console.log('大写：' + JSON.stringify(branch))
      for (const b of branch) {
        let el = b.sequenceFlows[0].conditionExpression
      }
      let aa = branch[0]
      resolve([aa])
    },
    submit() {
      this.save().then(response => {
        this.$emit('submit-success', this.mainData, 'id')
        this.$message.success('保存成功!')
      })
    },
    save() {
      return new Promise((resolve, reject) => {
        ComplianceAccountabilityApi.save(this.mainData).then(response => {
          resolve(response)
        }).catch(error => {
          reject(error)
        })
      })
    },
    // save() {
    //   return new Promise((resolve, reject) => {
    //     //判断 reportingPerson 和 reportingUnit 是否有空值
    //     if (!this.mainData.reportingPerson || !this.mainData.reportingUnit) {
    //       this.mainData.reportingPerson = this.mainData.createPsnName; // 上报人
    //       this.mainData.reportingUnit = this.mainData.createDeptName; // 上报单位
    //     } else {
    //       this.mainData.reportingPerson = this.mainData.reportingPerson; // 上报人
    //       this.mainData.reportingUnit = this.mainData.reportingUnit; // 上报单位
    //     }
    //     ComplianceRisksApi
    //         .save(this.mainData)
    //         .then((response) => {
    //           resolve(response);
    //         })
    //         .catch((error) => {
    //           reject(error);
    //         });
    //   });
    // },
    stopClick() {
      this.$confirm('您确定要终止当前流程吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let processInstanceId = this.$route.query.processInstanceId
        this.obj.processInstanceId = this.$route.query.processInstanceId
        this.obj.taskId = this.$route.query.taskId
        this.obj.title = this.mainData.name
        this.obj.functionName = '协同监督审批'
        this.obj.code = 'stop'
        let pathname = window.location.pathname
        new Promise((resolve, reject) => {
          let processInstanceId = this.$route.query.processInstanceId
          processApi.end(processInstanceId).then(res => {
            resolve(res)
          })
        }).then(val => {
          if (val.data.code === 200) {
            this.obj.functionCode = 'case_risk_main'
            processApi.sendOATask(this.obj).then(res => {
              if (pathname === '/base/design_page') {
                this.mcpLayout.closeTab()
              } else {
                window.close()
              }
            })
          }
        })
      }).catch(() => {
        this.$message.info('已取消删除!')
      })
    },
    finishClick() {
      this.$confirm('您确定要结束当前流程吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let processInstanceId = this.$route.query.processInstanceId
        this.obj.processInstanceId = this.$route.query.processInstanceId
        this.obj.taskId = this.$route.query.taskId
        this.obj.title = this.mainData.name
        this.obj.functionName = '协同监督审批'
        this.obj.code = 'pass'
        let pathname = window.location.pathname
        new Promise((resolve, reject) => {
          processApi.move({
            proInstId: this.$route.query.processInstanceId,
            taskId: this.$route.query.taskId
          }).then(res => {
            resolve(res)
          })
        }).then(val => {
          if (val.status === 200) {
            this.obj.functionCode = 'case_risk_main'
            processApi.sendOATask(this.obj).then(res => {
              if (pathname === '/base/design_page') {
                this.mcpLayout.closeTab()
              } else {
                window.close()
              }
            })
          }
        })
      }).catch(() => {
        this.$message.info('已取消删除!')
      })
    },
    // 发起知会
    noticeClick(cooperateFunc) {
      //必传参数
      this.noticeParams = {
        ...this.utils.routeState.VIEW(this.mainData.id),//主要作用是后台逻辑需要，其他作用各业务看情况使用
        processInstanceId: this.$route.query.processInstanceId,//流程实例
        taskId: this.$route.query.taskId,//任务ID
        businessKey: this.mainData.id, //业务数据ID
        entranceType: "FLOWABLE", //流程特定的标识
        type: "haveRead",//已处理
        whetherProcess: true,//系统内部打开的时候判断是否是流程，通过不同的方式打开查看
        isNotice: true//告知是知会消息打开的功能，有些按钮可以隐藏,只有知会消息，在created钩子函数中才执行相关，如果是流程抄送不需要传，可以根据上面created描述判断
      }
      this.noticeData.dataId = this.mainData.id
      this.noticeData.moduleName = '风险告知审批'
      this.noticeData.params = this.noticeParams
      this.noticeData.url = 'xtjd_main'//这个需要与功能维护中的值一样
      this.noticeData.title = this.mainData.accountabilitySubject
      cooperateFunc(this.noticeData)
    },
    //选择模板
    templateClick(val) {
      if (val) {
        this.prosecutionDialog = true
      }
    },
    prosecutionSelect(data) {
      this.mainData.accountabilitySubject = data.accountabilitySubject //事项名称
      this.mainData.causeOfInId = data.causeOfInId//案件类型ID
      this.mainData.causeOfIn = data.causeOfIn//案件类型
      this.mainData.causeOfInDes = data.causeOfInDes//案件类型说明
      this.mainData.caseMoney = data.caseMoney //标的额(元)
      this.mainData.projectName = data.projectName//项目名称
      this.mainData.whetherMajor = data.whetherMajor//是否重大案件
      this.mainData.venueIsOut = data.venueIsOut //是否集团内部诉讼
      this.mainData.venueAddress = data.venueAddress //详细地址
      this.mainData.venueProvince = data.venueProvince //省
      this.mainData.venueCity = data.venueCity //市
      this.mainData.venueRegion = data.venueRegion //区
      this.mainData.caseUndertaker = data.caseUndertaker//法务承办人
      this.mainData.caseUndertakerId = data.caseUndertakerId//法务承办人id
      this.mainData.ognPackage = data.ognPackage//单位包案领导
      this.mainData.ognPackageId = data.ognPackageId//单位包案领导id
      this.mainData.groupPackage = data.groupPackage//集团包案领导
      this.mainData.groupPackageId = data.groupPackageId//集团包案领导id
      this.mainData.involvedLevel = data.involvedLevel//涉案单位管理层级
      this.mainData.involvedLevelId = data.involvedLevelId//涉案单位管理层级
      this.mainData.unitType = data.unitType//单位类型
      this.mainData.unitTypeId = data.unitTypeId//单位类型id
      this.mainData.currentUnit = data.currentUnit//当事单位
      this.mainData.currentUnitId = data.currentUnitId//当事单位id
      this.mainData.des = data.des//情况说明
      this.mainData.greatReason = data.greatReason//重大案件案发原因
      this.mainData.greatPlan = data.greatPlan//重大案件下一步工作计划

      this.mainData.partiesList = data.partiesList
      this.mainData.partiesList.forEach(item => {
        item.id = this.utils.createUUID()
        item.masterId = this.mainData.id
      })

      this.mainData.claimList = data.claimList
      this.mainData.claimList.forEach(item => {
        item.id = this.utils.createUUID()
        item.parentId = this.mainData.id
      })

      this.mainData.otherDataList = data.otherDataList
      this.mainData.otherDataList.forEach(item => {
        item.id = this.utils.createUUID()
        item.parentId = this.mainData.id
        item.files = null
      })

      this.mainData.relations = data.relations
      this.mainData.relations.forEach(item => {
        item.id = this.utils.createUUID()
        item.relationId = this.mainData.id
      })
    },
    detailClick() {
      const me = this;
      const tabId = this.utils.createUUID();

      // 将当前页面的数据作为参数传递
      const params = {
        functionId: "hgwz_main_detail," + tabId,
        ...this.utils.routeState.VIEW(me.id),
        mainData: JSON.stringify(this.mainData) // 将当前页面的数据序列化为字符串
      };

      this.layout.openNewTab(
        "协同监督详情",
        "hgwz_main_detail",
        "hgwz_main_detail",
        tabId,
        params
      );
    },
    approvalClick() {
      const me = this
      if (this.mainData.dataStateCode !== this.utils.dataState_BPM.SAVE.code) {
        taskApi.selectTaskId({ businessKey: this.mainData.id, isView: 'true' }).then(res => {
          const functionId = res.data.data[0].ID
          const tabId = this.utils.createUUID()

          let urlParam = {
            processInstanceId: res.data.data[0].PID,//流程实例
            taskId: res.data.data[0].ID,//任务ID
            businessKey: this.mainData.id, //业务数据ID
            functionId: functionId, //不传也可以，只有首环节需要传，实例启动就可以不传了
            entranceType: "FLOWABLE",
            type: "haveDealt",
            channel: 'business', //查看渠道，默认就传这个值即可，表示是从业务功能传过来的，首环节提交后过滤掉撤回按钮
            view: 'new',
          }

          if (me.$route.query.origin !== undefined && me.$route.query.origin === 'oa') {
            me.$set(urlParam, "origin", "oa")
            me.$set(urlParam, "fullScreen", "true")
          }

          this.layout.openNewTab("审批信息",
            "design_page",
            "design_page",
            tabId,
            urlParam
          )
        })
      } else {
        taskApi.selectFunctionId({ functionCode: 'case_risk_main' }).then(res => {
          const functionId = res.data.data[0].ID
          const tabId = this.utils.createUUID()

          let urlParam = {
            processDefinitionKey: this.mcpDesignPage.processKey,
            functionId: functionId,
            entranceType: "FLOWABLE",
            ...this.utils.routeState.VIEW(this.mainData.id),
            channel: 'business',
            view: 'new',
          }

          if (me.$route.query.origin !== undefined && me.$route.query.origin === 'oa') {
            this.$set(urlParam, "origin", "oa")
            this.$set(urlParam, "fullScreen", "true")
          }

          this.layout.openNewTab("审批信息",
            "design_page",
            "design_page",
            tabId,
            urlParam
          )
        })
      }
    },
    approvalOpinionIs() {
      orgApi.roleCheck({ orgId: this.orgContext.currentOrgId, roleName: 'LDYJ' }).then(res => {
        console.log('approvalOpinionIs：', res)
        this.approvalIs = !(this.type !== 'toDeal' || res.data.data === false)
      })
    },
    queryHistoricalNode() {
      let taskId = this.$route.query.taskId
      taskApi.queryHistoricalNode({ taskId: taskId }).then(res => {
        return res.data.data
      })
    },
    printClick() {
      this.$print(this.$refs.dataForm)
    },
    downloadClick() {
      // 初始化一个空数组来存储合并后的结果
      let mergedArray = [];
      // 使用forEach()方法循环遍历列表并合并JSON数组
      if (this.mainData.files) {
        mergedArray = mergedArray.concat(JSON.parse(this.mainData.files));
      }
      if (this.mainData.authorization && this.mainData.authorization.authorizedBook) {
        mergedArray = mergedArray.concat(JSON.parse(this.mainData.authorization.authorizedBook));
      }
      if (this.mainData.eventReport) {
        mergedArray = mergedArray.concat(JSON.parse(this.mainData.eventReport));
      }
      console.log("附件合并的结果")
      console.log(mergedArray)
      //下载全部的文件
      const loading = this.$loading({
        lock: true,
        text: "文件下载中...",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)"
      });
      doc.fileDown(mergedArray).then(response => {
        if (response) {
          const blob = response.data;
          const fileName = this.mainData.accountabilitySubject + ".zip";
          if ("download" in document.createElement("a")) {
            // 非IE下载
            const elink = document.createElement("a");
            elink.download = fileName;
            elink.style.display = "none";
            elink.href = URL.createObjectURL(blob);
            document.body.appendChild(elink);
            elink.click();
            URL.revokeObjectURL(elink.href); // 释放URL 对象
            document.body.removeChild(elink);
          } else {
            // IE10+下载
            navigator.msSaveBlob(blob, fileName);
          }
        }
        loading.close();
      }).catch(res => {
        loading.close();
      });
    },
    // queryOtherPartys() {
    //   let partys = this.mainData.partiesList
    //   let otherPartys = ""

    //   if (partys.length > 0)
    //   {
    //     for (let i = 0; i < partys.length; i++)
    //     {
    //       if (partys[i].partyType !== '原告' && partys[i].party !== null && partys[i].party !== undefined)
    //         otherPartys +=  partys[i].party + ","
    //     }

    //     if (otherPartys !== "")
    //       otherPartys = otherPartys.slice(0, -1)
    //   }

    //   return otherPartys
    // }
  }
}
</script>

<style scoped>
.row_ {
  margin-top: 10px;
}

.label_ {
  margin-top: 10px;
  text-align: right;
  padding-right: 6px;
}

.title {
  font-size: 16px;
  font-weight: bold;
}

html,
body,
el-container,
el-main,
el-form,
el-card {
  width: 100%;
}
</style>
