package com.klaw.service.imp.complianceRiskServiceImp;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.klaw.entity.ComplianceChack.ComplianceChackEntity;
import com.klaw.entity.ComplianceChack.ComplianceChackTable;
import com.klaw.entity.complianceOrganizationBean.OrganizationMember;
import com.klaw.entity.complianceReviewBean.ComplianceReview;
import com.klaw.entity.complianceRiskBean.*;
import com.klaw.service.ComplianceChackService.ComplianceChackService;
import com.klaw.service.complianceOrganizationService.OrganizationMemberService;
import com.klaw.service.complianceReviewService.ComplianceReviewService;
import com.klaw.service.complianceRiskService.*;
import com.klaw.utils.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static org.apache.poi.hssf.usermodel.HeaderFooter.page;


@Service
public class ComplianceManageIndexServiceImpl {
    @Autowired
    private OrganizationMemberService organizationMemberService;

    @Autowired
    private ComplianceReviewService complianceReviewService;

    @Autowired
    private ComplianceChackService complianceChackService;
    @Autowired
    private ComplianceCaseLibraryService complianceCaseLibraryService;

    @Autowired
    private ComplianceRegulationsLibraryService complianceRegulationsLibraryService;
    @Autowired
    private ComplianceRiskIdentificationService complianceRiskIdentificationService;

    @Autowired
    private JobResponsibilitiesListService jobResponsibilitiesListService;

    @Autowired
    private ProcessControlListService processControlListService;

    /**
     * 统计启用的合规人员数量
     * @param jsonObject
     * @return
     */
    public int getEnabledCompliancePersonCount(JSONObject jsonObject){
        QueryWrapper<OrganizationMember> queryWrapper = new QueryWrapper<>();
        getAuthWrapper(jsonObject, queryWrapper);
        return organizationMemberService.count(queryWrapper);
    }

    /**
     * 统计各单位启用的合规人员数量及占比
     * @param jsonObject
     * @return
     */
    public List<Map<String,Object>> getEnabledCompliancePersonCountByOrg(JSONObject jsonObject){
        List<Map<String,Object>> dataList = new ArrayList<>();
        QueryWrapper<OrganizationMember> wrapper = new QueryWrapper<>();
        wrapper.eq("data_state", "启用");
        List<OrganizationMember> organizationMemberList = organizationMemberService.list(wrapper);
        // 修改后的分组逻辑（添加空值过滤）
        Map<Object, Long> countMap = organizationMemberList.stream()
                .filter(om -> om.getCreateOgnName() != null) // 添加空值过滤
                .map(OrganizationMember::getCreateOgnName)
                .collect(Collectors.groupingBy(e -> e, Collectors.counting()));
        countMap.forEach((k,v)->{
            Map<String,Object> map = new HashMap<>();
            map.put("name",k);
            map.put("value",v);
            dataList.add(map);
        });
        return dataList;
    }

    /**
     * 统计已提交的合规审查总数
     * @param jsonObject
     * @return
     */
    public int getComplianceReviewCount(JSONObject jsonObject){
        QueryWrapper<ComplianceReview> queryWrapper = new QueryWrapper<>();
        getReviewWrapper(jsonObject, queryWrapper);
        return complianceReviewService.count(queryWrapper);
    }

    /**
     * 统计已提交的各种审查分类的数量占比
     * @param jsonObject
     * @return
     */
    public List<Map<String,Object>> getSubmittedComplianceReviewCountByCategory(JSONObject jsonObject){
        List<Map<String,Object>> dataList = new ArrayList<>();
        QueryWrapper<ComplianceReview> wrapper = new QueryWrapper<>();
        getReviewWrapper(jsonObject, wrapper);
        List<ComplianceReview> complianceReviewList = complianceReviewService.list(wrapper);
        // 修改后的分组逻辑（添加空值过滤）
        Map<Object, Long> countMap = complianceReviewList.stream()
                .filter(om -> om.getReviewCategoryName() != null) // 添加空值过滤
                .map(ComplianceReview::getReviewCategoryName)
                .collect(Collectors.groupingBy(e -> e, Collectors.counting()));
        countMap.forEach((k,v)->{
            Map<String,Object> map = new HashMap<>();
            map.put("name",k);
            map.put("value",v);
            dataList.add(map);
        });
        return dataList;
    }



    /**
     * 统计需整改的合规检查总数
     * @param jsonObject
     * @return
     */
    public int getNeedRectifyComplianceReviewCount(JSONObject jsonObject){
        QueryWrapper<ComplianceChackTable> queryWrapper = new QueryWrapper<>();
        getNeedRectifyWrapper(jsonObject, queryWrapper);
        return complianceChackService.count(queryWrapper);
    }


    /**
     * 统计合规检查的检查通知分类数量占比
     * @param jsonObject
     * @return
     */
    public List<Map<String,Object>> getComplianceRectificationFeedbackCount(JSONObject jsonObject){
        List<Map<String,Object>> dataList = new ArrayList<>();
        QueryWrapper<ComplianceChackTable> wrapper = new QueryWrapper<>();
        getblNeedRectifyWrapper(jsonObject, wrapper);
        List<ComplianceChackTable> complianceReviewList = complianceChackService.list(wrapper);
        // 修改后的分组逻辑（添加空值过滤）
        Map<Object, Long> countMap = complianceReviewList.stream()
                .filter(om -> om.getCurrentTaskType() != null)
                .map(om -> "已归档".equals(om.getCurrentTaskType())
                        ? "整改结果反馈"
                        : om.getCurrentTaskType())
                .collect(Collectors.groupingBy(e -> e, Collectors.counting()));

        countMap.forEach((k,v)->{
            Map<String,Object> map = new HashMap<>();
            map.put("name",k);
            map.put("value",v);
            dataList.add(map);
        });
        return dataList;
    }
    /**
     * 统计已提交的合规案例总数
     * @param jsonObject
     * @return
     */
    public int getSubmittedComplianceCaseCount(JSONObject jsonObject) {
        QueryWrapper<ComplianceCaseLibraryEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("review_status", "已提交"); // 确保添加状态条件
        getKnowWrapper(jsonObject, queryWrapper, "create_time", "create_Psn_Full_Id", "create_ogn_id");
        return complianceCaseLibraryService.count(queryWrapper);
    }

    /**
     * 统计已提交的政策法规总数
     * @param jsonObject
     * @return
     */
    public int getSubmittedPolicyCount(JSONObject jsonObject) {
        QueryWrapper<ComplianceRegulationsLibraryEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("review_status", "已提交"); // 确保添加状态条件
        getKnowWrapper(jsonObject, queryWrapper, "create_time", "create_Psn_Full_Id", "create_ogn_id");
        return complianceRegulationsLibraryService.count(queryWrapper);
    }

    /**
     * 统计合规风险识别清单总数
     * @param jsonObject
     * @return
     */
    public int getComplianceRiskIdentificationCount(JSONObject jsonObject) {
        QueryWrapper<ComplianceRiskIdentification> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("data_state", "已提交"); // 确保添加状态条件
        getKnowWrapper(jsonObject, queryWrapper, "create_time", "create_Psn_Full_Id", "create_ogn_id");
        return complianceRiskIdentificationService.count(queryWrapper);
    }

    /**
     * 统计岗位职责总数
     * @param jsonObject
     * @return
     */
    public int getJobDutyCount(JSONObject jsonObject) {
        QueryWrapper<JobResponsibilitiesList> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("data_state", "已提交"); // 确保添加状态条件
        getKnowWrapper(jsonObject, queryWrapper, "create_time", "create_Psn_Full_Id", "create_ogn_id");
        return jobResponsibilitiesListService.count(queryWrapper);
    }

    /**
     * 统计流程管控总数
     * @param jsonObject
     * @return
     */
    public int getProcessControlCount(JSONObject jsonObject) {
        QueryWrapper<ProcessControlList> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("data_state", "已提交"); // 确保添加状态条件
        getKnowWrapper(jsonObject, queryWrapper, "create_time", "create_Psn_Full_Id", "create_ogn_id");
        return processControlListService.count(queryWrapper);
    }

    /**
     * 统计合规制度数量
     * @param jsonObject
     * @return
     */
    public int getComplianceSystemCount(JSONObject jsonObject) {
        // 返回一个固定的假数据
        return 0;
    }

    /**
     * 统计现行有效、已废止、已修订的制度数量
     * @param jsonObject
     * @return
     */
    public List<Map<String, Object>> getComplianceSystemStatusCount(JSONObject jsonObject) {
        List<Map<String, Object>> dataList = new ArrayList<>();
        // 添加假数据
        Map<String, Object> map1 = new HashMap<>();
        map1.put("name", "现行有效");
        map1.put("value", 80);
        dataList.add(map1);

        Map<String, Object> map2 = new HashMap<>();
        map2.put("name", "已废止");
        map2.put("value", 10);
        dataList.add(map2);

        Map<String, Object> map3 = new HashMap<>();
        map3.put("name", "已修订");
        map3.put("value", 10);
        dataList.add(map3);

        return dataList;
    }


//    合规人员筛选条件-启用状态、时间、组织
    private void getAuthWrapper(JSONObject jsonObject, QueryWrapper<OrganizationMember> wrapper) {
        wrapper.eq("data_state", "启用");
        if(!StringUtil.emptyStr(jsonObject.getString("startDate")).isEmpty()){
            wrapper.ge("create_time", jsonObject.getString("startDate"));
        }
        if(!StringUtil.emptyStr(jsonObject.getString("endDate")).isEmpty()){
            wrapper.le("create_time", jsonObject.getString("endDate"));
        }
        if (StringUtil.emptyStr(jsonObject.getString("startDate")).isEmpty()
                && StringUtil.emptyStr(jsonObject.getString("endDate")).isEmpty()) {
            wrapper.like("create_time", new SimpleDateFormat("yyyy").format(new Date()));
        }

        if (jsonObject.containsKey("currentOgnId") && "Y".equals(jsonObject.getString("isContains"))){
            wrapper.like("create_Psn_Full_Id",jsonObject.getString("currentOgnId"));
        }else if(StringUtil.emptyStr(jsonObject.getString("selectUnitIds")).isEmpty()){
            wrapper.eq("create_ogn_id",jsonObject.getString("currentOgnId"));
        }
        if (jsonObject.containsKey("selectUnitIds") && !StringUtil.emptyStr(jsonObject.getString("selectUnitIds")).isEmpty()){
            StringBuilder sqlCon = new StringBuilder(" (");
            if ("Y".equals(jsonObject.getString("isContains"))){
                String selectUnitIds = jsonObject.getString("selectUnitIds");
                for (String unitId : selectUnitIds.split(",")) {
                    sqlCon.append(" create_Psn_Full_Id LIKE '%").append(unitId).append("%' OR");
                }

            }else{
                String selectUnitIds = jsonObject.getString("selectUnitIds");
                for (String unitId : selectUnitIds.split(",")) {
                    sqlCon.append(" create_ogn_id = '").append(unitId).append("' OR");
                }
            }
            if (sqlCon.toString().endsWith("OR")){
                wrapper.apply(sqlCon.substring(0,sqlCon.length()-2) +")");
            }
        }
    }


    //    合规审查筛选条件-审批状态、送审时间、组织
    private void getReviewWrapper(JSONObject jsonObject, QueryWrapper<ComplianceReview> wrapper) {
        wrapper.in("data_state", "审批中", "审批完成"); // 确保添加状态条件
        if(!StringUtil.emptyStr(jsonObject.getString("startDate")).isEmpty()){
            wrapper.ge("submission_date", jsonObject.getString("startDate"));
        }
        if(!StringUtil.emptyStr(jsonObject.getString("endDate")).isEmpty()){
            wrapper.le("submission_date", jsonObject.getString("endDate"));
        }
        if (StringUtil.emptyStr(jsonObject.getString("startDate")).isEmpty()
                && StringUtil.emptyStr(jsonObject.getString("endDate")).isEmpty()) {
            wrapper.like("submission_date", new SimpleDateFormat("yyyy").format(new Date()));
        }

        if (jsonObject.containsKey("currentOgnId") && "Y".equals(jsonObject.getString("isContains"))){
            wrapper.like("create_Psn_Full_Id",jsonObject.getString("currentOgnId"));
        }else if(StringUtil.emptyStr(jsonObject.getString("selectUnitIds")).isEmpty()){
            wrapper.eq("create_ogn_id",jsonObject.getString("currentOgnId"));
        }

        if (jsonObject.containsKey("selectUnitIds") && !StringUtil.emptyStr(jsonObject.getString("selectUnitIds")).isEmpty()){
            StringBuilder sqlCon = new StringBuilder(" (");
            if ("Y".equals(jsonObject.getString("isContains"))){
                String selectUnitIds = jsonObject.getString("selectUnitIds");
                for (String unitId : selectUnitIds.split(",")) {
                    sqlCon.append(" create_Psn_Full_Id LIKE '%").append(unitId).append("%' OR");
                }

            }else{
                String selectUnitIds = jsonObject.getString("selectUnitIds");
                for (String unitId : selectUnitIds.split(",")) {
                    sqlCon.append(" create_ogn_id = '").append(unitId).append("' OR");
                }
            }
            if (sqlCon.toString().endsWith("OR")){
                wrapper.apply(sqlCon.substring(0,sqlCon.length()-2) +")");
            }
        }
    }


    //    合规整改筛选条件-整改状态、计划检查时间、组织
    private void getNeedRectifyWrapper(JSONObject jsonObject, QueryWrapper<ComplianceChackTable> wrapper) {
        wrapper.eq("needs_rectification", "1");
        if(!StringUtil.emptyStr(jsonObject.getString("startDate")).isEmpty()){
            wrapper.ge("planned_start_time", jsonObject.getString("startDate"));
        }
        if(!StringUtil.emptyStr(jsonObject.getString("endDate")).isEmpty()){
            wrapper.le("planned_start_time", jsonObject.getString("endDate"));
        }
        if (StringUtil.emptyStr(jsonObject.getString("startDate")).isEmpty()
                && StringUtil.emptyStr(jsonObject.getString("endDate")).isEmpty()) {
            wrapper.like("planned_start_time", new SimpleDateFormat("yyyy").format(new Date()));
        }

        if (jsonObject.containsKey("currentOgnId") && "Y".equals(jsonObject.getString("isContains"))){
            wrapper.like("create_Psn_Full_Id",jsonObject.getString("currentOgnId"));
        }else if(StringUtil.emptyStr(jsonObject.getString("selectUnitIds")).isEmpty()){
            wrapper.eq("create_ogn_id",jsonObject.getString("currentOgnId"));
        }

        if (jsonObject.containsKey("selectUnitIds") && !StringUtil.emptyStr(jsonObject.getString("selectUnitIds")).isEmpty()){
            StringBuilder sqlCon = new StringBuilder(" (");
            if ("Y".equals(jsonObject.getString("isContains"))){
                String selectUnitIds = jsonObject.getString("selectUnitIds");
                for (String unitId : selectUnitIds.split(",")) {
                    sqlCon.append(" create_Psn_Full_Id LIKE '%").append(unitId).append("%' OR");
                }

            }else{
                String selectUnitIds = jsonObject.getString("selectUnitIds");
                for (String unitId : selectUnitIds.split(",")) {
                    sqlCon.append(" create_ogn_id = '").append(unitId).append("' OR");
                }
            }
            if (sqlCon.toString().endsWith("OR")){
                wrapper.apply(sqlCon.substring(0,sqlCon.length()-2) +")");
            }
        }
    }


    //    合规检查筛选条件-审批状态、计划检查时间、组织
    private void getblNeedRectifyWrapper(JSONObject jsonObject, QueryWrapper<ComplianceChackTable> wrapper) {
        wrapper.in("data_state", "审批中", "审批完成");
        if(!StringUtil.emptyStr(jsonObject.getString("startDate")).isEmpty()){
            wrapper.ge("planned_start_time", jsonObject.getString("startDate"));
        }
        if(!StringUtil.emptyStr(jsonObject.getString("endDate")).isEmpty()){
            wrapper.le("planned_start_time", jsonObject.getString("endDate"));
        }
        if (StringUtil.emptyStr(jsonObject.getString("startDate")).isEmpty()
                && StringUtil.emptyStr(jsonObject.getString("endDate")).isEmpty()) {
            wrapper.like("planned_start_time", new SimpleDateFormat("yyyy").format(new Date()));
        }

        if (jsonObject.containsKey("currentOgnId") && "Y".equals(jsonObject.getString("isContains"))){
            wrapper.like("create_Psn_Full_Id",jsonObject.getString("currentOgnId"));
        }else if(StringUtil.emptyStr(jsonObject.getString("selectUnitIds")).isEmpty()){
            wrapper.eq("create_ogn_id",jsonObject.getString("currentOgnId"));
        }

        if (jsonObject.containsKey("selectUnitIds") && !StringUtil.emptyStr(jsonObject.getString("selectUnitIds")).isEmpty()){
            StringBuilder sqlCon = new StringBuilder(" (");
            if ("Y".equals(jsonObject.getString("isContains"))){
                String selectUnitIds = jsonObject.getString("selectUnitIds");
                for (String unitId : selectUnitIds.split(",")) {
                    sqlCon.append(" create_Psn_Full_Id LIKE '%").append(unitId).append("%' OR");
                }

            }else{
                String selectUnitIds = jsonObject.getString("selectUnitIds");
                for (String unitId : selectUnitIds.split(",")) {
                    sqlCon.append(" create_ogn_id = '").append(unitId).append("' OR");
                }
            }
            if (sqlCon.toString().endsWith("OR")){
                wrapper.apply(sqlCon.substring(0,sqlCon.length()-2) +")");
            }
        }
    }


    //合规知识数量统计-时间、组织
    public <T> void getKnowWrapper(JSONObject jsonObject, QueryWrapper<T> wrapper,
                                   String createTimeField, String createPsnFullIdField,
                                   String createOgnIdField) {
        // 日期处理
        if(!StringUtil.emptyStr(jsonObject.getString("startDate")).isEmpty()){
            wrapper.ge(createTimeField, jsonObject.getString("startDate"));
        }
        if(!StringUtil.emptyStr(jsonObject.getString("endDate")).isEmpty()){
            wrapper.le(createTimeField, jsonObject.getString("endDate"));
        }
        if (StringUtil.emptyStr(jsonObject.getString("startDate")).isEmpty()
                && StringUtil.emptyStr(jsonObject.getString("endDate")).isEmpty()) {
            wrapper.like(createTimeField, new SimpleDateFormat("yyyy").format(new Date()));
        }

        // 组织机构处理
        if (jsonObject.containsKey("currentOgnId") && "Y".equals(jsonObject.getString("isContains"))){
            wrapper.like(createPsnFullIdField, jsonObject.getString("currentOgnId"));
        } else if(StringUtil.emptyStr(jsonObject.getString("selectUnitIds")).isEmpty()){
            wrapper.eq(createOgnIdField, jsonObject.getString("currentOgnId"));
        }

        // 选择单位处理
        if (jsonObject.containsKey("selectUnitIds") && !StringUtil.emptyStr(jsonObject.getString("selectUnitIds")).isEmpty()){
            StringBuilder sqlCon = new StringBuilder(" (");
            if ("Y".equals(jsonObject.getString("isContains"))){
                String selectUnitIds = jsonObject.getString("selectUnitIds");
                for (String unitId : selectUnitIds.split(",")) {
                    sqlCon.append(" ").append(createPsnFullIdField).append(" LIKE '%").append(unitId).append("%' OR");
                }
            } else {
                String selectUnitIds = jsonObject.getString("selectUnitIds");
                for (String unitId : selectUnitIds.split(",")) {
                    sqlCon.append(" ").append(createOgnIdField).append(" = '").append(unitId).append("' OR");
                }
            }
            if (sqlCon.toString().endsWith("OR")){
                wrapper.apply(sqlCon.substring(0,sqlCon.length()-2) +")");
            }
        }
    }
}
