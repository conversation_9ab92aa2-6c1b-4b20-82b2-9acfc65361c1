
67b9942e6deaa40b7f070226dbded30a3c29819c	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.266.1754018536329.js\",\"contentHash\":\"f5f3146a8baa6166d4b54b2755baef7a\"}","integrity":"sha512-purgDa53E11Rn/38QIIgoEVVNN4XxaXD1M8a4601ceZW0kU+e1Nc8t5xjvIf9HpEaG5Mnh636WIfxCMLiOJEAw==","time":1754018576006,"size":130791}