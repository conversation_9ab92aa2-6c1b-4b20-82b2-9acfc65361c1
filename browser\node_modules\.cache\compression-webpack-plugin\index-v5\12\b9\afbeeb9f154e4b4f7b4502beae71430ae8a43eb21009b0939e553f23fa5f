
15374581ccab43171140dc7dd852b6d07459e1cc	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.268.1754018536329.js\",\"contentHash\":\"21f92e51e147b382e641752a2f15bb1b\"}","integrity":"sha512-5JJokvvyVgCuE218pOhX2v53FnMaELD4t5ScBoBhbscaliY6ZRUTuTDg13PVzetEhI3HOxstybi0vYOHl6LcRA==","time":1754018576008,"size":162708}