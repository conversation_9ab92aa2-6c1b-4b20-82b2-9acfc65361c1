<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.klaw.dao.evaluateDao.SgContractEvaluateMapper">

    <select id="queryPageList" resultType="java.util.Map">
        select
        c.id as "id",
        c.contract_name as "contractName",
        c.contract_code as "contractCode",
        c.contract_id as "contractId"
        from
        bm_contract s,
        bm_contract_close c,
        sg_business_man m
        <where>
            ${ew.sqlSegment}
        </where>
        and
        (S.ID = C.CONTRACT_ID and
        (
        s.other_party_list LIKE CONCAT( '%', m.CODE, '%' )
        OR s.our_party_list LIKE CONCAT( '%', m.CODE, '%' )
        ) )
        ORDER BY
        c.create_time DESC
    </select>
    <select id="queryBusinessList" resultType="java.util.Map">
        SELECT
        c.id AS "id",
        c.contract_name AS "contractname",
        c.contract_code AS "contractcode",
        c.contract_id AS "contractid"
        FROM
        bm_contract s,
        bm_contract_close c,
        sg_business_man m
        <where>
            ${ew.sqlSegment}
        </where>
        order by p.subject_Status_Name
    </select>
</mapper>
