
34927a8317c816d62a9adc0e997422a171a5eb29	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.376.1754018536329.js\",\"contentHash\":\"cbfacade3f55720977a84195c4aaea55\"}","integrity":"sha512-ZKer7yz8yD0rg3Lnz9++ZzrdaAajRciJvHCxScIsxiFE34deovS/UK0HcJrT8Egz5TKGgOj0e70vEB7qPDtEpQ==","time":1754018575956,"size":36344}