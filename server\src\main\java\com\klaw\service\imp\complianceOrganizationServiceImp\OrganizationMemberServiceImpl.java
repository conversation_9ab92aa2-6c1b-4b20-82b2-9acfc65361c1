package com.klaw.service.imp.complianceOrganizationServiceImp;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.klaw.dao.complianceOrganizationDao.OrganizationMemberMapper;
import com.klaw.entity.complianceOrganizationBean.OrganizationMember;
import com.klaw.entity.complianceOrganizationBean.OrganizationMemberDto;
import com.klaw.entity.complianceOrganizationBean.OrganizationMemberHistory;
import com.klaw.entity.complianceOrganizationBean.OrganizationRole;
import com.klaw.entity.mainDataBean.MainMidStaff;
import com.klaw.entity.mainDataBean.MainMidStaffUnitOrg;
import com.klaw.entity.systemBean.SgHrOrgUnitB;
import com.klaw.service.complianceOrganizationService.OrganizationMemberHistoryService;
import com.klaw.service.complianceOrganizationService.OrganizationMemberService;
import com.klaw.service.complianceOrganizationService.OrganizationRoleService;
import com.klaw.service.mainDataService.MainMidStaffService;
import com.klaw.service.mainDataService.MainMidStaffUnitOrgService;
import com.klaw.service.systemService.SgHrOrgUnitBService;
import com.klaw.utils.DataAuthUtils;
import com.klaw.utils.PageUtils;
import com.klaw.utils.StringUtil;
import com.klaw.utils.Utils;
import com.klaw.vo.Json;
import com.klaw.xxlJob.request.XxlJobReq;
import com.klaw.xxlJob.service.XxlJobService;
import com.sgai.mcp.core.impl.LoginHelper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.session.Configuration;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【organization_member(组织人员表)】的数据库操作Service实现
 * @createDate 2024-12-05 16:10:40
 */
@Service
@Slf4j
public class OrganizationMemberServiceImpl extends ServiceImpl<OrganizationMemberMapper, OrganizationMember>
        implements OrganizationMemberService {

    @Autowired
    private SqlSessionFactory sqlSessionFactory;
    @Resource
    private OrganizationRoleService organizationRoleService;
    @Resource
    private MainMidStaffService mainMidStaffService;
    @Resource
    private OrganizationMemberHistoryService organizationMemberHistoryService;
    @Resource
    private SgHrOrgUnitBService sgHrOrgUnitBService;
    @Resource
    private MainMidStaffUnitOrgService mainMidStaffUnitOrgService;
    @Resource
    private XxlJobService xxlJobService;

    @Override
    public Page<OrganizationMember> queryPageData(JSONObject jsonObject) {
        QueryWrapper<OrganizationMember> queryWrapper = new QueryWrapper<>();
        getFilter(jsonObject, queryWrapper);
        PageUtils<OrganizationMember> page = page(new PageUtils<>(jsonObject), queryWrapper);
        return page;
    }

    @Override
    public Page<MainMidStaff> queryDia(JSONObject jsonObject) {
        QueryWrapper<MainMidStaff> queryWrapper = new QueryWrapper<>();
        List<MainMidStaffUnitOrg> diaFilter = getDiaFilter(jsonObject, queryWrapper);
        Map<String, MainMidStaffUnitOrg> collect = null;
        if (!CollectionUtils.isEmpty(diaFilter)) {
            collect  = diaFilter.stream()
                    .collect(Collectors.toMap(MainMidStaffUnitOrg::getStaffCode, o -> o, (existing, replacement) -> existing));
        }
        PageUtils page = mainMidStaffService.page(new PageUtils<>(jsonObject), queryWrapper);
        List<MainMidStaff> records = page.getRecords();
        JSONArray jsonArray = new JSONArray();
        for (MainMidStaff record : records) {
            JSONObject object = new JSONObject();
            object.put("memberCode",record.getCode());
            object.put("memberName",record.getName());
            object.put("memberGender",record.getGender());
            object.put("birthDate",record.getBirth());
            object.put("attendWorkerDate",record.getFirstWorkDate());
            object.put("phone",record.getMobilePhone());
            object.put("idCardNo",record.getIdCardNo());
            if (collect != null) {
                MainMidStaffUnitOrg mainMidStaffUnitOrg = collect.get(record.getCode());
                if (mainMidStaffUnitOrg != null) {
                    object.put("organizationName",mainMidStaffUnitOrg.getDeptName());
                    object.put("position",mainMidStaffUnitOrg.getPostName());
                    //object.put("createOrgId",mainMidStaffUnitOrg.getOrgCode());

//                    object.put("currentOrgCode",mainMidStaffUnitOrg.getCode());
                }
            }
            jsonArray.add(object);
        }
        page.setRecords(jsonArray);
        return page;
    }


    @Override
    public void updateDataState(OrganizationMember organizationMember) {
        // 先拿到原数据插入到历史表
        OrganizationMember member = getById(organizationMember.getId());
        member.setDataState("停用");
        member.setDataStateCode(2);
        member.setDisableTime(LocalDate.now());
        member.setUpdateTime(new Date());
        //删除定时任务
        if (member.getStopOrgId() != null) {
            List<Long> list = new ArrayList<>();
            list.add(member.getStartOrgId().longValue());
            xxlJobService.removalJob(list);
        }
        updateById(member);
    }


    @Override
    public Page<OrganizationMemberHistory> queryHistory(JSONObject jsonObject) {
        return organizationMemberHistoryService.queryHistory(jsonObject);
    }

    @Override
    public Json saveData(String enableTime, String disableTime, List<OrganizationMember> list) {

        for (OrganizationMember member : list) {
            OrganizationMember one = this.getOne(new QueryWrapper<OrganizationMember>().eq("member_code", member.getMemberCode()).eq("member_name", member.getMemberName()));

            if (one != null) {
                member.setId(one.getId());
                if ("启用".equals(one.getDataState())){
                    return Json.fail("该用户存在正在使用数据，无法创建!");
                } else if ("停用".equals(one.getDataState())){
                    OrganizationMemberHistory memberHistory = new OrganizationMemberHistory();
                    BeanUtils.copyProperties(one, memberHistory);
                    memberHistory.setId(Utils.createUUID());
                    memberHistory.setParentId(one.getId());
                    organizationMemberHistoryService.save(memberHistory);
                    this.removeById(one.getId());
                } else if ("待启用".equals(one.getDataState())){
                    return Json.fail("该用户已存在，还未到启用时间，请核定信息后重新操作!");
                }
            } else {
                member.setId(Utils.createUUID());
            }
            if (enableTime != null){
                XxlJobReq xxlJobReq = new XxlJobReq();
                xxlJobReq.setExecutorName("startUserInfo");
                xxlJobReq.setExecutorParam(member.getId());
                LocalDate date = LocalDate.parse(enableTime);
                LocalDateTime localDateTime = null;
                if (date.equals(LocalDate.now())){
                    localDateTime = LocalDateTime.now().plusSeconds(60);
                } else if (date.isBefore(LocalDate.now())){
                    localDateTime = LocalDateTime.now().plusSeconds(30);
                } else {
                    localDateTime = date.atStartOfDay();
                }

                member.setEnableTime(date);
                xxlJobReq.setStartTime(localDateTime);
                xxlJobReq.setJobDesc("启动-"+member.getMemberCode()+":"+member.getMemberName()+"-用户");
                xxlJobReq.setHour(0);
                xxlJobReq.setMin(0);
                xxlJobReq.setAuthor(LoginHelper.getUserName());
                Integer jobId = xxlJobService.addJob(xxlJobReq);
                member.setStartOrgId(jobId);
                xxlJobService.startJob(jobId);
            }
            if (disableTime != null){
                XxlJobReq xxlJobReq = new XxlJobReq();
                xxlJobReq.setExecutorName("stopUserInfo");
                xxlJobReq.setExecutorParam(member.getId());
                LocalDate date = LocalDate.parse(disableTime);
                LocalDateTime localDateTime = date.atStartOfDay();
                member.setDisableTime(date);
                xxlJobReq.setStartTime(localDateTime);
                xxlJobReq.setJobDesc("停止-"+member.getMemberCode()+":"+member.getMemberName()+"-用户");
                xxlJobReq.setHour(0);
                xxlJobReq.setMin(0);
                xxlJobReq.setAuthor(LoginHelper.getUserName());
                Integer jobId = xxlJobService.addJob(xxlJobReq);
                member.setStopOrgId(jobId);
                xxlJobService.startJob(jobId);
            }
            save(member);
        }
        return Json.succ();
    }

    @Override
    public String removeAndUpdate(String id) {
        OrganizationMember member = this.getById(id);
        if (member != null) {
            List<Long> jobIds = new ArrayList<>();
            if (member.getStartOrgId() != null) {
                jobIds.add(member.getStartOrgId().longValue());
            }
            if (member.getStopOrgId() != null) {
                jobIds.add(member.getStopOrgId().longValue());
            }
            xxlJobService.removalJob(jobIds);

            //历史记录中最新一条转为现用数据
            QueryWrapper<OrganizationMemberHistory> historyQueryWrapper = new QueryWrapper<>();
            historyQueryWrapper.eq("parent_id", member.getId());
            historyQueryWrapper.orderByDesc("enable_time");
            historyQueryWrapper.last("limit 1");
            OrganizationMemberHistory one = organizationMemberHistoryService.getOne(historyQueryWrapper);

            //删除现有数据
            this.removeById(member.getId());
            //创建新的现有数据
            if (one != null) {
                OrganizationMember organizationMember = new OrganizationMember();
                BeanUtils.copyProperties(one, organizationMember);
                organizationMember.setId(one.getParentId());
                this.save(organizationMember);
                //删除历史数据信息
                organizationMemberHistoryService.removeById(one.getId());
            }

        }
        return "";
    }

    @Override
    public void updateMember(OrganizationMember member) {
        List<Long> jobIds = new ArrayList<>();
        if (member.getEnableTime() != null) {
            jobIds.add(member.getStartOrgId().longValue());
            XxlJobReq xxlJobReq = new XxlJobReq();
            xxlJobReq.setExecutorName("startUserInfo");
            xxlJobReq.setExecutorParam(member.getId());
            LocalDate date = member.getEnableTime();
            LocalDateTime localDateTime = null;
            if (date.equals(LocalDate.now())){
                localDateTime = LocalDateTime.now().plusMinutes(2);
            } else {
                localDateTime = date.atStartOfDay();
            }

            member.setEnableTime(date);
            xxlJobReq.setStartTime(localDateTime);
            xxlJobReq.setJobDesc("启动-"+member.getMemberCode()+":"+member.getMemberName()+"-用户");
            xxlJobReq.setHour(0);
            xxlJobReq.setMin(0);
            xxlJobReq.setAuthor(LoginHelper.getUserName());
            Integer jobId = xxlJobService.addJob(xxlJobReq);
            member.setStartOrgId(jobId);
            xxlJobService.startJob(jobId);
        }
        if (member.getDisableTime() != null) {
            jobIds.add(member.getStopOrgId().longValue());
            XxlJobReq xxlJobReq = new XxlJobReq();
            xxlJobReq.setExecutorName("stopUserInfo");
            xxlJobReq.setExecutorParam(member.getId());
            LocalDate date = member.getDisableTime();
            LocalDateTime localDateTime = date.atStartOfDay();
            member.setDisableTime(date);
            xxlJobReq.setStartTime(localDateTime);
            xxlJobReq.setJobDesc("停止-"+member.getMemberCode()+":"+member.getMemberName()+"-用户");
            xxlJobReq.setHour(0);
            xxlJobReq.setMin(0);
            xxlJobReq.setAuthor(LoginHelper.getUserName());
            Integer jobId = xxlJobService.addJob(xxlJobReq);
            member.setStopOrgId(jobId);
            xxlJobService.startJob(jobId);
        }
        xxlJobService.removalJob(jobIds);
        updateById(member);

    }

    private void getHistoryFilter(JSONObject jsonObject, QueryWrapper<OrganizationMemberHistory> queryWrapper) {
        //是否台账功能（登记只查自己的，台账和弹框查自己和数据权限的）
        boolean isQuery = jsonObject.containsKey("isQuery") ? jsonObject.getBoolean("isQuery") : false;
        String memberName = jsonObject.containsKey("memberName") ? jsonObject.getString("memberName") : null;
        String memberCode = jsonObject.containsKey("memberCode") ? jsonObject.getString("memberCode") : null;
        String dataState = jsonObject.containsKey("dataState") ? jsonObject.getString("dataState") : null;
        String orgId = StringUtil.emptyStr(jsonObject.getString("orgId"));
        if (StringUtils.isNotBlank(memberName)) {
            queryWrapper.like("member_name", memberName);
        }
        if (StringUtils.isNotBlank(memberCode)) {
            queryWrapper.like("member_code", memberCode);
        }
        if (StringUtils.isNotBlank(dataState)) {
            queryWrapper.eq("data_state", dataState);
        }
        if (isQuery){
            Long functionId = DataAuthUtils.getFunctionIdByCode("hgryk_index");
            DataAuthUtils.dataPermSql(queryWrapper, null, functionId, orgId);
        } else {
            queryWrapper.eq("create_org_id", orgId);
        }
    }

    private List<MainMidStaffUnitOrg> getDiaFilter(JSONObject jsonObject, QueryWrapper<MainMidStaff> queryWrapper) {
        String organization = jsonObject.containsKey("organization") ? jsonObject.getString("organization") : null;
        String memberName = jsonObject.containsKey("memberName") ? jsonObject.getString("memberName") : null;
        if (StringUtils.isNotBlank(organization)) {
            queryWrapper.like("code", organization);
        }
        if (StringUtils.isNotBlank(memberName)) {
            queryWrapper.like("name", memberName);
        }
        QueryWrapper<OrganizationMember> organizationMemberQueryWrapper = new QueryWrapper<>();
        organizationMemberQueryWrapper.select("member_code");
        organizationMemberQueryWrapper.ne("data_state_code",2);
        List<OrganizationMember> list1 = this.list(organizationMemberQueryWrapper);
        List<String> list = list1.stream().map(o -> {
            if (StringUtils.isNotBlank(o.getMemberCode())) {
                return o.getMemberCode();
            } else {
                return "";
            }
        }).collect(Collectors.toList());
        if (list.size() > 0){
            queryWrapper.notIn("code", list);
        }
        Long unitId = LoginHelper.getUnit_ID();
        QueryWrapper<SgHrOrgUnitB> sgHrOrgUnitBQueryWrapper = new QueryWrapper<>();
        sgHrOrgUnitBQueryWrapper.select("parent_id");
        sgHrOrgUnitBQueryWrapper.eq("unit_id", unitId);
        SgHrOrgUnitB hrOrgUnitB = sgHrOrgUnitBService.getOne(sgHrOrgUnitBQueryWrapper);
        if (hrOrgUnitB != null) {
            Long parentId = hrOrgUnitB.getParentId();
            if (parentId != null) {
                QueryWrapper<MainMidStaffUnitOrg> staffUnitOrgQueryWrapper = new QueryWrapper<>();
                staffUnitOrgQueryWrapper.eq("dept_code", parentId);
                List<MainMidStaffUnitOrg> list2 = mainMidStaffUnitOrgService.list(staffUnitOrgQueryWrapper);
                if (list2.size()>0) {
                    List<String> list3 = list2.stream().map(MainMidStaffUnitOrg::getStaffCode).collect(Collectors.toList());
                    queryWrapper.in("code", list3);
                } else {
                    queryWrapper.eq("1","0");
                }
                return list2;
            }
        }
        return null;
    }

    private void getFilter(JSONObject jsonObject, QueryWrapper<OrganizationMember> queryWrapper) {
        //是否台账功能（登记只查自己的，台账和弹框查自己和数据权限的）
        boolean isQuery = jsonObject.containsKey("isQuery") ? jsonObject.getBoolean("isQuery") : false;
        //当前登录人任职id
        String orgId = StringUtil.emptyStr(jsonObject.getString("orgId"));
        String currentOgnId = jsonObject.containsKey("currentOgnId")? jsonObject.getString("currentOgnId") : null;;
        String organizations = jsonObject.containsKey("organizations") ? jsonObject.getString("organizations") : null;
        String memberNames = jsonObject.containsKey("memberNames") ? jsonObject.getString("memberNames") : null;
        String memberName = jsonObject.containsKey("memberName") ? jsonObject.getString("memberName") : null;
        String memberCode = jsonObject.containsKey("memberCode") ? jsonObject.getString("memberCode") : null;
        String dataState = jsonObject.containsKey("dataState") ? jsonObject.getString("dataState") : null;
        // 模糊搜索值
        String fuzzyValue = jsonObject.containsKey("fuzzyValue") ? jsonObject.getString("fuzzyValue") : null;

        // organizations、memberNames为，拼接的字符串转换为数组
        if (StringUtils.isNotBlank(organizations)) {
            String[] organizationList = organizations.split(",");
            queryWrapper.in("organization", organizationList);
        }
        if (StringUtils.isNotBlank(memberNames)) {
            String[] memberNameList = memberNames.split(",");
            queryWrapper.in("member_name", memberNameList);
        }


        if (StringUtils.isNotBlank(memberName)) {
            queryWrapper.like("member_name", memberName);
        }
        if (StringUtils.isNotBlank(memberCode)) {
            queryWrapper.like("member_code", memberCode);
        }
        if (StringUtils.isNotBlank(dataState)) {
            switch (dataState){
                case "0":
                    queryWrapper.eq("data_state", "待启用");
                    break;
                case "1":
                    queryWrapper.eq("data_state", "启用");
                    break;
                case "2":
                    queryWrapper.eq("data_state", "停用");
                    break;
            }

        }
        if (StringUtils.isNotBlank(currentOgnId)) {
            queryWrapper.eq("create_ogn_id", currentOgnId);
        }
        if (com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(fuzzyValue)) {
            queryWrapper.and(i -> i
                    .like("member_code", fuzzyValue)
                    .or().like("member_name", fuzzyValue));
        }
//台账查询权限
        if (isQuery){
            Long functionId = DataAuthUtils.getFunctionIdByCode("hgryk_index");
            DataAuthUtils.dataPermSql(queryWrapper, null, functionId, orgId);
        } else {
            queryWrapper.eq("create_org_id", orgId);
        }
        queryWrapper.orderByDesc("enable_time");
    }
}