package com.klaw.service.imp.caseServiceImpl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.klaw.constant.CaseKind;
import com.klaw.constant.DataState;
import com.klaw.constant.DataStateBPM;
import com.klaw.dao.caseDao.CaseProsecutionMapper;
import com.klaw.entity.authorizationBean.Authorization;
import com.klaw.entity.authorizationBean.AuthorizationLitigation;
import com.klaw.entity.caseBean.CaseProsecution;
import com.klaw.entity.caseBean.CaseRecord;
import com.klaw.entity.caseBean.child.Claim;
import com.klaw.entity.caseBean.child.MiddleRelation;
import com.klaw.entity.caseBean.child.OtherData;
import com.klaw.entity.caseBean.child.Parties;
import com.klaw.entity.contractBean.SgSealApproval;
import com.klaw.entity.contractBean.SgSealApprovalDetail;
import com.klaw.entity.contractBean.contract.BmContractSeal;
import com.klaw.service.authorizationService.AuthorizationLitigationService;
import com.klaw.service.authorizationService.AuthorizationService;
import com.klaw.service.caseService.CaseProsecutionService;
import com.klaw.service.caseService.childService.*;
import com.klaw.service.contractService.SgSealApprovalDetailService;
import com.klaw.service.contractService.SgSealApprovalService;
import com.klaw.service.contractService.contract.BmContractSealService;
import com.klaw.utils.*;
import com.klaw.vo.Json;
import com.klaw.vo.OaDeal;
import com.klaw.vo.OrgContextVo;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;


@Service
public class CaseProsecutionServiceImpl extends ServiceImpl<CaseProsecutionMapper, CaseProsecution> implements CaseProsecutionService {

    @Autowired
    private PartiesService partiesService;
    @Resource
    private BmContractSealService sgContractSealService;
    //    @Autowired
//    private SysOrgService sysOrgService;
    @Autowired
    private MiddleRelationService middleRelationService;
    //    @Autowired
//    private SysNoticeService sysNoticeService;
    @Autowired
    private ClaimService claimService;
    @Autowired
    private EvidenceService evidenceService;
    @Autowired
    private OtherDataService otherDataService;
    @Autowired
    private CaseEvidenceDataService caseEvidenceDataService;
    @Resource
    private CaseProsecutionMapper prosecutionMapper;
    @Resource
    private AuthorizationService authorizationService;
    @Resource
    private AuthorizationLitigationService authorizationLitigationService;
    @Resource
    private SgSealApprovalService sgSealApprovalService;
    @Resource
    private SgSealApprovalDetailService sgSealApprovalDetailService;
    @Resource
    private TagService tagService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveData(CaseProsecution caseProsecution) {
        caseProsecution.setUpdateTime(new Date());

        //保存当事人信息子表
        Utils.saveChilds(caseProsecution.getPartiesList(), "master_id", caseProsecution.getId(), partiesService, true);
        //保存诉讼请求子表
        Utils.saveChilds(caseProsecution.getClaimList(), "parent_id", caseProsecution.getId(), claimService, true);
        //保存其他资料子表
        Utils.saveChilds(caseProsecution.getOtherDataList(), "parent_id", caseProsecution.getId(), otherDataService, true);
        //保存用印信息子表
        Utils.saveChilds(caseProsecution.getSealList(), "parent_id", caseProsecution.getId(), sgContractSealService, true);
        //关联信息
        middleRelationService.saveData(caseProsecution.getId(), new String[]{"qisu", "contract", "case", "dispute"}, caseProsecution.getRelations());

        Authorization authorization = caseProsecution.getAuthorization();

        if (authorization != null) {
            List<AuthorizationLitigation> authorizationLitigationList = new ArrayList<>();
            authorizationLitigationList = authorization.getAuthorizationLitigationList();
            // 正常填写授权信息
            if (caseProsecution.getAuthorizationId() != null) {
                if (authorizationLitigationList != null) {
                    // 保存授权受托人子表
                    Utils.saveChilds(authorizationLitigationList, "relation_id", authorization.getId(), authorizationLitigationService, true);
                }

                String trusteeName = null;
                String trusteeId = null;
                // 保存授权信息
                if (authorizationLitigationList != null && !authorizationLitigationList.isEmpty()) {
                    trusteeName = authorizationLitigationList.stream()
                            .filter(Objects::nonNull)
                            .map(AuthorizationLitigation::getTrusteeName)
                            .filter(Objects::nonNull)
                            .filter(name -> !name.isEmpty())
                            .collect(Collectors.joining(", "));

                    trusteeId = authorizationLitigationList.stream()
                            .filter(Objects::nonNull)
                            .map(AuthorizationLitigation::getTrusteeId)
                            .filter(Objects::nonNull)
                            .filter(id -> !id.isEmpty())
                            .collect(Collectors.joining(", "));
                }
                authorization.setParentId(caseProsecution.getId());
                authorization.setDataState(caseProsecution.getDataState());
                authorization.setDataStateCode(caseProsecution.getDataStateCode());
                // 保存被授权人信息
                authorization.setTrustee(trusteeName);
                authorization.setTrusteeId(trusteeId);
                authorization.setAuthStatus("未开始");
                authorization.setSublicenseStatus("未生效");
                authorization.setAuthorizationType("特别授权-诉讼");
                authorization.setAuthorizationName("特别授权-诉讼");
                authorization.setArchive("未归档");
                authorization.setShowCode("0");
                authorizationService.saveData(authorization);
                // 设置主诉和授权关联
                caseProsecution.setAuthorizationId(authorization.getId());
            } else {
                // 删除历史关联授权信息
                if (authorizationLitigationList != null) {
                    for (int i = 0; i < authorizationLitigationList.size(); i++) {
                        authorizationLitigationService.removeById(authorizationLitigationList.get(i).getId());
                    }
                    authorizationService.removeById(authorization.getId());
                    caseProsecution.setAuthorizationId(null);
                }
            }
        }

        //更新当前案件数据表
        saveOrUpdate(caseProsecution);


        /*
            if(caseProsecution.getDataStateCode() == DataState.SUBMIT.getKey()){
                TaskUtils.getInstance().createTodo("分配案件负责人:"+caseProsecution.getCaseName(),caseProsecution.getId(),"CaseProsecutionForm","{\"dataId\":\""+caseProsecution.getId()+"\",\"dataState\":\"view\"}",new Date(),caseProsecution.getCreateOrgId(),new Date(),caseProsecution.getAssignOrgId());
            }
        */

    }

    @Override
    public void saveDataFinish(String businessKey) {
        CaseProsecution caseProsecution = queryDataById(businessKey);
//        //保存当事人信息子表
//        Utils.saveChilds(caseProsecution.getPartiesList(), "master_id", caseProsecution.getId(), partiesService,true);
//        //保存诉讼请求子表
//        Utils.saveChilds(caseProsecution.getClaimList(), "parent_id", caseProsecution.getId(), claimService,true);
//        //保存其他资料子表
//        Utils.saveChilds(caseProsecution.getOtherDataList(), "parent_id", caseProsecution.getId(), otherDataService,true);
//        //关联信息
//        middleRelationService.saveData(caseProsecution.getId(), new String[]{"qisu", "contract", "case"}, caseProsecution.getRelations());
        //需流程通过后才能分配
        assignData(caseProsecution, null);
        sealStamp(caseProsecution);
    }


    @Override
    public CaseProsecution queryDataById(String id) {
        CaseProsecution caseProsecution = getById(id);
        caseProsecution.setPartiesList(partiesService.list(new QueryWrapper<Parties>().eq("master_id", caseProsecution.getId()).orderBy(true, true, "seq")));
        caseProsecution.setSealList(sgContractSealService.list(new QueryWrapper<BmContractSeal>().eq("parent_id", caseProsecution.getId())));
        caseProsecution.setClaimList(claimService.list(new QueryWrapper<Claim>().eq("parent_id", caseProsecution.getId()).orderBy(true, true, "seq")));
        caseProsecution.setOtherDataList(otherDataService.list(new QueryWrapper<OtherData>().eq("parent_id", caseProsecution.getId()).orderBy(true, true, "seq")));
        Map<String, List<MiddleRelation>> map = middleRelationService.queryData(id, "qisu", new String[]{"case", "contract", "dispute"});
        List<MiddleRelation> list = new ArrayList<>();
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(map.get("case"))) {
            list.addAll(map.get("case"));
        }
        if (CollectionUtils.isNotEmpty(map.get("contract"))) {
            list.addAll(map.get("contract"));
        }
        if (CollectionUtils.isNotEmpty(map.get("dispute"))) {
            list.addAll(map.get("dispute"));
        }
        caseProsecution.setRelations(list);
        return caseProsecution;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Json deleteDataById(String id) {

        boolean flag = removeById(id);

        partiesService.remove(new QueryWrapper<Parties>().eq("master_id", id));

        claimService.remove(new QueryWrapper<Claim>().eq("parent_id", id));

        sgContractSealService.remove(new QueryWrapper<BmContractSeal>().eq("parent_id", id));

        otherDataService.remove(new QueryWrapper<OtherData>().eq("parent_id", id));

        middleRelationService.remove(new QueryWrapper<MiddleRelation>().eq("relation_id", "id").eq("relation_type", "qisu"));
        if (!flag) {
            return Json.fail().msg("未找到需要删除的数据");
        }

        return Json.succ().msg("删除成功!");
    }


    @Override
    public Page<CaseProsecution> queryPageData(JSONObject json) {
        QueryWrapper<CaseProsecution> wrapper = new QueryWrapper<>();
        getFilter(json, wrapper);
        return page(new PageUtils<>(json), wrapper);
    }

    //获取查询条件
    private void getFilter(JSONObject json, QueryWrapper<CaseProsecution> wrapper) {
        //是否台账功能（登记只查自己的，台账和弹框查自己和数据权限的）
        boolean isQuery = json.containsKey("isQuery") ? json.getBoolean("isQuery") : false;
        String orgId = json.containsKey("orgId") ? json.getString("orgId") : "";

        //案件名称
        String caseName = json.containsKey("caseName") ? json.getString("caseName") : null;

        //当前人ID
        String transferId = json.containsKey("transferId") ? json.getString("transferId") : null;

        //经办时间(最小值)
        Date createTimeMin = json.containsKey("createTimeMin") ? json.getDate("createTimeMin") : null;
        //经办时间(最大值)
        Date createTimeMax = json.containsKey("createTimeMax") ? json.getDate("createTimeMax") : null;

        String functionCode = json.containsKey("functionCode") ? json.getString("functionCode") : null;

        //数据状态
        Integer dataStateCode = json.containsKey("dataStateCode") ? json.getInteger("dataStateCode") : null;

        //管理单位
        String managementUnit = json.containsKey("managementUnit") ? json.getString("managementUnit") : null;
        //单位类型
        String unitType = json.containsKey("unitType") ? json.getString("unitType") : null;
        //模糊搜索值
        String fuzzyValue = json.containsKey("fuzzyValue") ? json.getString("fuzzyValue") : null;

        //排序字段
        String sortName = json.containsKey("sortName") ? json.getString("sortName") : null;
        //排序字段是否升序
        boolean order = json.containsKey("order") ? json.getBoolean("order") : false;

        if (StringUtils.isNotBlank(caseName)) {
            wrapper.and(i -> i.like("case_name", caseName));
        }

        if (createTimeMin != null) {
            wrapper.and(i -> i.ge("create_time", createTimeMin));
        }
        if (createTimeMax != null) {
            wrapper.and(i -> i.le("create_time", createTimeMax));
        }

        if (dataStateCode != null) {
            wrapper.and(i -> i.eq("data_state_code", dataStateCode));
        }

        if (StringUtils.isNotBlank(fuzzyValue)) {
            wrapper.and(i -> i.like("case_name", fuzzyValue).or().like("cause_of_in", fuzzyValue).or().like("case_code", fuzzyValue).or().like("date_format(create_time,'%Y-%m-%d')", fuzzyValue));
        }
        if (StringUtils.isNotBlank(managementUnit)) {
            wrapper.and(i -> i.like("MANAGEMENT_UNIT", managementUnit));
        }
        if (StringUtils.isNotBlank(unitType)) {
            wrapper.and(i -> i.like("UNIT_TYPE", unitType));
        }

//        //模糊搜索匹配字段
//        String[] cols = {"case_name", "cause_of_in", "case_code", "to_char(create_time,'yyyy-MM-dd')"};
//
//        Utils.fuzzyValueQuery(wrapper, cols, fuzzyValue);
        Long functionId = DataAuthUtils.getFunctionIdByCode("case_sued_index");
        if (isQuery) {
            wrapper.and(i -> i.ne("data_state_code", 1));
            DataAuthUtils.dataPermSql(wrapper, null, functionId, orgId);
        } else {
            wrapper.like("create_psn_full_id", orgId);
        }

        if (org.apache.commons.lang.StringUtils.isNotBlank(sortName)) {
            wrapper.orderBy(true, order, "to_char(" + Utils.humpToLine2(sortName) + ")");
        } else {
            wrapper.orderBy(true, order, "create_time");
        }

    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean assignData(CaseProsecution caseProsecution, String taskId) {
        String orgId = caseProsecution.getCreateOrgId();

        CaseRecord caseRecord = new CaseRecord();
        caseRecord.setId(Utils.createUUID());
        caseRecord.setCaseName(caseProsecution.getCaseName());
        caseRecord.setCaseCode(caseProsecution.getCaseCode());
        caseRecord.setCaseTypeId(caseProsecution.getCauseOfInId());
        caseRecord.setCaseType(caseProsecution.getCauseOfIn());
        caseRecord.setCaseTypeDes(caseProsecution.getCauseOfInDes());

        caseRecord.setCaseMoney(caseProsecution.getCaseMoney());
        caseRecord.setProjectName(caseProsecution.getProjectName());
        caseRecord.setWhetherMajor(caseProsecution.getWhetherMajor());

        caseRecord.setVenueAddress(caseProsecution.getVenueAddress());
        caseRecord.setVenueCity(caseProsecution.getVenueCity());
        caseRecord.setVenueIsOut(caseProsecution.getVenueIsOut());
        caseRecord.setVenueProvince(caseProsecution.getVenueProvince());
        caseRecord.setVenueRegion(caseProsecution.getVenueRegion());

        caseRecord.setOgnPackage(caseProsecution.getOgnPackage());
        caseRecord.setOgnPackageId(caseProsecution.getOgnPackageId());
        caseRecord.setGroupPackage(caseProsecution.getGroupPackage());
        caseRecord.setGroupPackageId(caseProsecution.getGroupPackageId());
        caseRecord.setInvolvedOgnLevel(caseProsecution.getInvolvedLevel());
        caseRecord.setInvolvedOgnLevelId(caseProsecution.getInvolvedLevelId());
        caseRecord.setUnitType(caseProsecution.getUnitType());
        caseRecord.setUnitTypeId(caseProsecution.getUnitTypeId());

        caseRecord.setCaseDetails(caseProsecution.getDes());
        caseRecord.setDataState(DataState.NEED.getValue());
        caseRecord.setDataStateCode(DataState.NEED.getKey());
        caseRecord.setPrepositionType("起诉");
        caseRecord.setPrepositionId(caseProsecution.getId());
        caseRecord.setWhetherEasy(false);
        caseRecord.setVersion(0);

        OrgContextVo contextVo = OrgUtils.getOrgContext(Long.parseLong(orgId));
        caseRecord.setCreateOgnId(contextVo.getCurrentOgnId());
        caseRecord.setCreateOgnName(contextVo.getCurrentOgnName());
        caseRecord.setCreateOrgId(contextVo.getCurrentOrgId());
        caseRecord.setCreateOrgName(contextVo.getCurrentOrgName());
        caseRecord.setCreateDeptId(contextVo.getCurrentDeptId());
        caseRecord.setCreateDeptName(contextVo.getCurrentDeptName());
        caseRecord.setCreateGroupId(contextVo.getCurrentGroupId());
        caseRecord.setCreateGroupName(contextVo.getCurrentGroupName());
        caseRecord.setCreatePsnId(contextVo.getCurrentPsnId());
        caseRecord.setCreatePsnName(contextVo.getCurrentPsnName());
        caseRecord.setCreatePsnFullId(contextVo.getCurrentPsnFullId());
        caseRecord.setCreatePsnFullName(contextVo.getCurrentPsnFullName());
        caseRecord.setCreateTime(new Date());
        caseRecord.setUpdateTime(new Date());

        caseRecord.setCaseCurrentProcess(CaseKind.WSA.getValue());
        caseRecord.setCaseCurrentProcessId(CaseKind.WSA.getKey());
        caseRecord.setCaseProcessType(CaseKind.WSA.getValue());
        caseRecord.setCaseProcessTypeCode(CaseKind.WSA.getKey());
        caseRecord.setCaseProcess(CaseKind.WSA.getValue());

        caseRecord.setWhether1(false);
        caseRecord.setWhether2(false);
        caseRecord.setWhether3(false);
        caseRecord.setWhether4(false);
        caseRecord.setWhether5(false);
        caseRecord.setWhether6(false);
        caseRecord.setWhether7(false);
        caseRecord.setWhether8(false);
        caseRecord.setWhether9(false);
        caseRecord.setWhether10(false);
        caseRecord.setWhether11(false);
        caseRecord.setWhether12(false);
        caseRecord.setWhether13(false);
        caseRecord.setWhether14(false);
        caseRecord.setWhether15(false);
        caseRecord.setWhether16(false);
        caseRecord.setWhether17(false);
        caseRecord.setWhether18(false);
        caseRecord.setWhether19(false);
        caseRecord.setVersion(0);

        caseRecord.insert();

        if (StringUtils.isNotEmpty(caseProsecution.getSuedUnitType())) {
            tagService.createTag(caseRecord, caseRecord.getId(), caseProsecution.getSuedUnitType());
        }

        //当事人列表
        List<Parties> partiesList = caseProsecution.getPartiesList();
        partiesList.forEach(item -> {
            item.setId(Utils.createUUID());
            item.setMasterId(caseRecord.getId());
        });
        if (CollectionUtils.isNotEmpty(partiesList)) {
            partiesService.saveBatch(partiesList);
        }
        //诉讼请求列表
        List<Claim> claimList = caseProsecution.getClaimList();
        claimList.forEach(item -> {
            item.setId(Utils.createUUID());
            item.setParentId(caseRecord.getId());
        });
        if (CollectionUtils.isNotEmpty(claimList)) {
            claimService.saveBatch(claimList);
        }
        //其他资料列表
        List<OtherData> otherDataList = caseProsecution.getOtherDataList();
        otherDataList.forEach(item -> {
            item.setId(Utils.createUUID());
            item.setFiles(null);
            item.setParentId(caseRecord.getId());
        });
        if (CollectionUtils.isNotEmpty(otherDataList)) {
            otherDataService.saveBatch(otherDataList);
        }

        // 判断是否有关联案件、关联合同，如果有一起带到新的案件里
        Map<String, List<MiddleRelation>> map = middleRelationService.queryData(caseProsecution.getId(), "beisu", new String[]{"case", "contract"});
        List<MiddleRelation> relations = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(map.get("case")) || CollectionUtils.isNotEmpty(map.get("contract"))) {
            List<MiddleRelation> preList = map.get("case");
            List<MiddleRelation> preList2 = map.get("contract");
            if (preList != null && preList.size() > 0) {
                relations.addAll(preList);
            }
            if (preList2 != null && preList2.size() > 0) {
                relations.addAll(preList2);
            }
            if (relations.size() > 0) {
                for (int i = 0; i < relations.size(); i++) {
                    MiddleRelation middleRelation = relations.get(i);
                    middleRelation.setId(Utils.createUUID());
                    middleRelation.setRelationType("case");
                    middleRelation.setRelationId(caseRecord.getId());

                    middleRelation.setCreateDeptId(contextVo.getCurrentDeptId());
                    middleRelation.setCreateDeptName(contextVo.getCurrentDeptName());
                    middleRelation.setCreateGroupId(contextVo.getCurrentGroupId());
                    middleRelation.setCreateGroupName(contextVo.getCurrentGroupName());
                    middleRelation.setCreateOgnId(contextVo.getCurrentOgnId());
                    middleRelation.setCreateOgnName(contextVo.getCurrentOgnName());
                    middleRelation.setCreateOrgId(contextVo.getCurrentOrgId());
                    middleRelation.setCreateOrgName(contextVo.getCurrentOrgName());
                    middleRelation.setCreatePsnId(contextVo.getCurrentPsnId());
                    middleRelation.setCreatePsnName(contextVo.getCurrentPsnName());
                    middleRelation.setCreatePsnFullId(contextVo.getCurrentPsnFullId());
                    middleRelation.setCreatePsnFullName(contextVo.getCurrentPsnFullName());
                    middleRelation.setCreateTime(new Date());
                    middleRelation.setUpdateTime(new Date());
                }
                if (relations.size() > 0) {
                    middleRelationService.saveBatch(relations);
                }
            }
        }

        return true;
    }

    @Override
    public Page<CaseProsecution> queryPageDataForDialog(JSONObject json) {
        QueryWrapper<CaseProsecution> wrapper = new QueryWrapper<CaseProsecution>();
        String functionId = json.containsKey("functionId") ? json.getString("functionId") : "";
        String orgId = json.containsKey("orgId") ? json.getString("orgId") : "";
        //模糊搜索值
        String fuzzyValue = json.containsKey("fuzzyValue") ? json.getString("fuzzyValue") : null;
        //模糊搜索匹配字段
        String[] cols = {"case_name", "cause_of_in", "case_code", "to_char(create_time,'yyyy-MM-dd')"};

        Utils.fuzzyValueQuery(wrapper, cols, fuzzyValue);

        wrapper.orderBy(true, false, "create_time");

        DataAuthUtils.dataPermSql(wrapper, null, Long.parseLong(functionId), orgId);

        Page<CaseProsecution> caseProsecutionPage = page(new PageUtils<CaseProsecution>(json), wrapper);

        List<String> ids = caseProsecutionPage.getRecords().stream().map(item -> item.getId()).collect(Collectors.toList());

        if (ids != null && ids.size() > 0) {
            List<Parties> partiesList = partiesService.list(new QueryWrapper<Parties>().in("master_id", ids).orderBy(true, true, "seq"));
            List<Claim> claimList = claimService.list(new QueryWrapper<Claim>().in("parent_id", ids).orderBy(true, true, "seq"));
            List<OtherData> otherDataList = otherDataService.list(new QueryWrapper<OtherData>().in("parent_id", ids).orderBy(true, true, "seq"));

            caseProsecutionPage.getRecords().stream().forEach(item -> {
                item.setPartiesList(partiesList.stream().filter(k -> k.getMasterId().equals(item.getId())).collect(Collectors.toList()));
                item.setClaimList(claimList.stream().filter(k -> k.getParentId().equals(item.getId())).collect(Collectors.toList()));
                item.setOtherDataList(otherDataList.stream().filter(k -> k.getParentId().equals(item.getId())).collect(Collectors.toList()));

                Map<String, List<MiddleRelation>> map = middleRelationService.queryData(item.getId(), "qisu", new String[]{"case", "contract"});
                List<MiddleRelation> list = new ArrayList<MiddleRelation>();
                if (org.apache.commons.collections.CollectionUtils.isNotEmpty(map.get("case"))) {
                    list.addAll(map.get("case"));
                }
                if (CollectionUtils.isNotEmpty(map.get("contract"))) {
                    list.addAll(map.get("contract"));
                }
                item.setRelations(list);
            });
        }

        return caseProsecutionPage;
    }

    @Override
    public void updateDataState(List<OaDeal> list) {
        prosecutionMapper.updateDataState(list);
    }

    public void sealStamp(CaseProsecution caseProsecution) {
        String uuid = StringUtil.makeUUID();
        List<SgSealApprovalDetail> detailList = new ArrayList();
        String sealNames = "";
        String sealIds = "";
        String sealTypes = "";
        String sealTypeIds = "";
        String sealAdmins = "";
        String sealAdminIds = "";

        List<BmContractSeal> list = sgContractSealService.list(new QueryWrapper<BmContractSeal>().eq("parent_id", caseProsecution.getAuthorizationId()));
        if (!org.springframework.util.CollectionUtils.isEmpty(list)) {
            for (BmContractSeal bmContractSeal : list) {
                if ("".equals(sealNames)) {
                    sealNames = bmContractSeal.getSealName();
                    sealIds = bmContractSeal.getSealId();
                } else {
                    sealNames += "、" + bmContractSeal.getSealName();
                    sealIds += "," + bmContractSeal.getSealId();
                }
                if ("".equals(sealTypes)) {
                    sealTypes = bmContractSeal.getSealType();
                    sealTypeIds = bmContractSeal.getSealTypeId();
                } else {
                    sealTypes += "、" + bmContractSeal.getSealType();
                    sealTypeIds += "," + bmContractSeal.getSealTypeId();
                }
                if ("".equals(sealAdmins)) {
                    sealAdmins = bmContractSeal.getSealAdmin();
                    sealAdminIds = bmContractSeal.getSealAdminId();
                } else {
                    sealAdmins += "、" + bmContractSeal.getSealAdmin();
                    sealAdminIds += "," + bmContractSeal.getSealAdminId();
                }

                SgSealApprovalDetail sgSealApprovalDetail = new SgSealApprovalDetail();
                sgSealApprovalDetail.setId(StringUtil.makeUUID());
                sgSealApprovalDetail.setParentId(uuid);

                sgSealApprovalDetail.setSealName(bmContractSeal.getSealName());
                sgSealApprovalDetail.setSealId(bmContractSeal.getSealId());
                sgSealApprovalDetail.setSealType(bmContractSeal.getSealType());
                sgSealApprovalDetail.setSealTypeId(bmContractSeal.getSealTypeId());
                sgSealApprovalDetail.setSealNumberOld(String.valueOf(bmContractSeal.getSealNumber()));
                sgSealApprovalDetail.setSealNumber(String.valueOf(bmContractSeal.getSealNumber()));
                sgSealApprovalDetail.setPrintsNumber(String.valueOf(bmContractSeal.getPrintsNumber()));
                sgSealApprovalDetail.setPrintsNumberOld(String.valueOf(bmContractSeal.getPrintsNumber()));
                sgSealApprovalDetail.setSealAdminOld(bmContractSeal.getSealAdmin());
                sgSealApprovalDetail.setSealAdminIdOld(bmContractSeal.getSealAdminId());
                sgSealApprovalDetail.setDataState("待用印");
                sgSealApprovalDetail.setDataStateCode(1);
                if ("法人签字".equals(sealNames)) {
                    sgSealApprovalDetail.setDataState("已用印");
                    sgSealApprovalDetail.setDataStateCode(4);
                    sgSealApprovalDetail.setSealNumber(String.valueOf(bmContractSeal.getSealNumber()));
                    sgSealApprovalDetail.setPrintsNumber(String.valueOf(bmContractSeal.getPrintsNumber()));
                    sgSealApprovalDetail.setOurSealTime(new Date());
                }
                sgSealApprovalDetail.setCreateTime(new Date());
                detailList.add(sgSealApprovalDetail);
            }
        }
        SgSealApproval sgSealApproval = new SgSealApproval();
        sgSealApproval.setId(uuid);
        sgSealApproval.setContractName(caseProsecution.getCaseName());
        sgSealApproval.setContractId(caseProsecution.getId());
        sgSealApproval.setContractCode(caseProsecution.getCaseCode());
        sgSealApproval.setSealPurpose("授权审批");
        sgSealApproval.setSealPurposeId("6");

        sgSealApproval.setSealNames(sealNames);
        if (!"法人签字".equals(sealNames)) {
            sgSealApproval.setTakeEffectCode(DataStateBPM.TAKE_EFFECT_1.getKey());
            sgSealApproval.setTakeEffectName(DataStateBPM.TAKE_EFFECT_1.getValue());
        } else {
            sgSealApproval.setTakeEffectCode(DataStateBPM.TAKE_EFFECT_2.getKey());
            sgSealApproval.setTakeEffectName(DataStateBPM.TAKE_EFFECT_2.getValue());
        }
        sgSealApproval.setSealIds(sealIds);
        sgSealApproval.setSealTypes(sealTypes);
        sgSealApproval.setSealTypeIds(sealTypeIds);
        sgSealApproval.setSealAdmins(sealAdmins);
        sgSealApproval.setSealAdminIds(sealAdminIds);
        sgSealApproval.setCreateOgnId(caseProsecution.getCreateOgnId());
        sgSealApproval.setCreateOgnName(caseProsecution.getCreateOgnName());
        sgSealApproval.setCreateDeptId(caseProsecution.getCreateDeptId());
        sgSealApproval.setCreateDeptName(caseProsecution.getCreateDeptName());
        sgSealApproval.setCreatePsnId(caseProsecution.getCreatePsnId());
        sgSealApproval.setCreatePsnName(caseProsecution.getCreatePsnName());
        sgSealApproval.setCreatePsnFullId(caseProsecution.getCreatePsnFullId());
        sgSealApproval.setCreatePsnFullName(caseProsecution.getCreatePsnFullName());
        sgSealApproval.setCreateOrgId(caseProsecution.getCreateOrgId());
        sgSealApproval.setCreateOrgName(caseProsecution.getCreateOrgName());
        sgSealApproval.setCreateGroupId(caseProsecution.getCreateGroupId());
        sgSealApproval.setCreateGroupName(caseProsecution.getCreateGroupName());
        sgSealApproval.setCreateTime(new Date());
        sgSealApproval.setDataState(DataStateBPM.FINISH.getValue());
        sgSealApproval.setDataStateCode(DataStateBPM.FINISH.getKey());
        sgSealApprovalService.saveOrUpdate(sgSealApproval);
        if (!org.springframework.util.CollectionUtils.isEmpty(detailList)) {
            sgSealApprovalDetailService.saveOrUpdateBatch(detailList);
        }
        // 将授权状态改为正常
        String authorizationId = caseProsecution.getAuthorizationId();
        Authorization authorization = authorizationService.getById(authorizationId);
        if (authorization != null) {
            authorization.setDataState(DataStateBPM.FINISH.getValue());
            authorization.setDataStateCode(DataStateBPM.FINISH.getKey());
            authorization.setAuthStatus("正常");
            authorizationService.updateById(authorization);
        }
    }

}
