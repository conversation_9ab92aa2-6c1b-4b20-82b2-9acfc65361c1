
06480fed36588333c03d0f7c1e514489944f77c1	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.344.1754018536329.js\",\"contentHash\":\"d53d8622f466e3b5e15d1a31d2c6fb19\"}","integrity":"sha512-HCkDEM6Iay/vgDxTYlfKRYhpNoDrhi7/5TFBggUMT05ZnaMn74ZWfnHIDu5W3+8XI/2PJkb0e4S4/ASlaRILdw==","time":1754018576017,"size":162548}