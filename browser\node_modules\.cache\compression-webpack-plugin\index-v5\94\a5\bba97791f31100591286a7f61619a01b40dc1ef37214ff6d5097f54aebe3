
b0a55d983836a7fef42472d8e79888a65e55d0fd	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.22.1754018536329.js\",\"contentHash\":\"61744e4310ee424e9b64655dd45dd89f\"}","integrity":"sha512-JcnDbWCqJN0g4G2m6R4V9l0xLnET2MPPbdjIaJ2rSEfVCfj8EENbykymqE3QaE5mWlwoKnDjRuo/ww6S4JqVcg==","time":1754018576042,"size":177431}