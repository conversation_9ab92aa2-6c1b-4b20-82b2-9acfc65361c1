
9c8527c3ea26550b6907a49658fb7a1bd336d969	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.349.1754018536329.js\",\"contentHash\":\"66a063254a3a9d1739eecb747f6aed8f\"}","integrity":"sha512-44zQxwySX6bZSVuOyOodoNS2XxRBehG9lxS7hSumY0yT0U5GBNkg2yD/ExIbJtJLIaCsQGmqX2Me2xDQGcmi5g==","time":1754018576069,"size":187221}