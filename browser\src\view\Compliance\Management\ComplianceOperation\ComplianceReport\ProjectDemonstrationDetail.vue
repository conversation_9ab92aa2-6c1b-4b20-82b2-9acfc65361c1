<template>
    <div>
        <FormWindow ref="formWindow" @initData="initData" @loadData="loadData">
            <el-container v-loading="loading" style="height: calc(100vh - 84px)" :element-loading-text="loadingText">
                <el-main>
                    <el-scrollbar style="height: 100%" wrap-style="overflow-x: hidden;">
                        <!--数据表单块-->
                        <el-form ref="dataForm" :model="mainData" :rules="!isView ? rules : {}" label-width="100px"
                            style="margin-right: 10px;">
                            <el-row
                                style="padding: 10px 0 10px 0; position: fixed; width: 100%; overflow-y: auto; background-color: white; z-index: 999">
                                <el-button v-if="!isView" type="primary" size="mini" @click="save_">保存</el-button>
                                <el-button v-if="!isView" type="success" size="mini"
                                    @click="approval_">生成审批单</el-button>
                            </el-row>
                            <div style="padding-top: 50px"></div>

                            <!--基础信息块-->
                            <div v-if="dataState !== 'view'">
                                <span style="text-align: left; font-size: 23px; margin-left: 43%; font-weight: 900">{{
                                    this.$route.query.title }}</span>
                                <div
                                    style="padding-left: 10px; margin-bottom: 20px; font-size: 15px; color: red; font-weight: bolder">
                                </div>
                                <div style="margin: 10px">
                                    <span style="font-weight: bold; font-size: 16px; color: #5a5a5f">基本信息</span>
                                </div>
                                <el-divider />
                                <el-row style="margin-top: 10px">
                                    <el-col :span="16">
                                        <el-form-item label="项目名称" prop="projectName">
                                            <el-input v-model="mainData.projectName" maxlength="100" show-word-limit
                                                placeholder="请输入..." clearable />
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="8">
                                        <el-form-item label="项目编码" prop="projectNumber" style="margin-bottom: 0px;">
                                            <el-input v-model="mainData.projectNumber" disabled show-word-limit
                                                clearable />
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="8">
                                        <el-form-item prop="involvedAmount" :label="'涉及金额\n(元)'" class="fold_label"
                                            label-position="top">
                                            <Money :isDW="true" :isDX="false"
                                                :moneyParam.sync="mainData.involvedAmount"></Money>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="24">
                                        <el-form-item label="项目背景" prop="projectBackground">
                                            <el-input type="textarea" :autosize="{ minRows: 3, maxRows: 6 }"
                                                v-model="mainData.projectBackground" maxlength="1000" show-word-limit
                                                clearable placeholder="请输入" />
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="24">
                                        <el-form-item label="相关方情况" prop="relatedPartySituation">
                                            <el-input v-model="mainData.relatedPartySituation" clearable type="textarea"
                                                :autosize="{ minRows: 3, maxRows: 6 }" placeholder="请输入"
                                                maxlength="1000" show-word-limit />
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="24">
                                        <el-form-item label="初步思路" prop="preliminaryIdeas">
                                            <el-input v-model="mainData.preliminaryIdeas" clearable type="textarea"
                                                :autosize="{ minRows: 3, maxRows: 6 }" placeholder="请输入"
                                                maxlength="1000" show-word-limit />
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="24">
                                        <el-form-item label="论证关注重点" prop="keyFocusOfArgumentation">
                                            <el-input v-model="mainData.keyFocusOfArgumentation" clearable
                                                type="textarea" :autosize="{ minRows: 3, maxRows: 6 }" placeholder="请输入"
                                                maxlength="1000" show-word-limit />
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="24">
                                        <el-form-item label="存在的问题" prop="existsProblem">
                                            <el-input v-model="mainData.existsProblem" clearable type="textarea"
                                                :autosize="{ minRows: 3, maxRows: 6 }" placeholder="请输入"
                                                maxlength="1000" show-word-limit />
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                              <el-row>
                                <el-col :span="24">
                                  <el-form-item label="上传附件">
                                    <UploadDoc  :files.sync="mainData.attachments" doc-path="/case"/>
                                  </el-form-item>
                                </el-col>
                              </el-row>
                            </div>

                            <!-- 查看时的判断 -->
                            <div v-if="dataState == 'view'">
                                <span style="text-align: left; font-size: 23px; margin-left: 43%; font-weight: 900">{{
                                    this.$route.query.title }}</span>
                                <SimpleBoardTitle title="基本信息" style="margin-top: 5px">
                                    <table class="table_content" style="margin-top: 10px">
                                        <tbody>
                                            <tr>
                                                <th colspan="3" class="th_label">项目名称</th>
                                                <td colspan="13" class="td_value">{{ mainData.projectName }}</td>
                                                <th colspan="3" class="th_label">项目编码</th>
                                                <td colspan="5" class="td_value">{{ mainData.projectNumber }}</td>
                                            </tr>
                                            <tr>
                                                <th colspan="3" class="th_label">涉及金额(元)</th>
                                                <td colspan="21" class="td_value">{{ mainData.involvedAmount }}</td>
                                            </tr>
                                            <tr>
                                                <th colspan="3" class="th_label">项目背景</th>
                                                <td colspan="21" class="td_value">{{ mainData.projectBackground }}</td>
                                            </tr>
                                            <tr>
                                               <th colspan="3" class="th_label">相关方情况</th>
                                               <td colspan="21" class="td_value" style="white-space: normal">{{ mainData.relatedPartySituation }}
                                               </td>
                                           </tr>
                                            <tr>
                                                <th colspan="3" class="th_label">初步思路</th>
                                                <td colspan="21" class="td_value">{{ mainData.preliminaryIdeas }}</td>
                                            </tr>
                                            <tr>
                                                <th colspan="3" class="th_label" >论证关注重点</th>
                                                <td colspan="21" class="td_value" style="white-space: normal">{{ mainData.keyFocusOfArgumentation }}
                                                </td>
                                            </tr>
                                            <tr>
                                                <th colspan="3" class="th_label">存在的问题</th>
                                                <td colspan="21" class="td_value">{{ mainData.existsProblem }}</td>
                                            </tr>
                                            <tr>
                                              <th class="th_label" colspan="3">上传附件</th>
                                              <td class="td_value" colspan="21">
                                                <UploadDoc :disabled="isView" :files.sync="mainData.attachments" doc-path="/case"/>
                                              </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </SimpleBoardTitle>
                            </div>
                            <!--公共信息-->
                            <el-row style="margin-top: 90px;margin-left: 40px;">
                                <!-- <el-col :span="12">
                                    <el-form-item label="提报部门">
                                        <span class="viewSpan">{{ mainData.createDeptName }}</span>
                                    </el-form-item>
                                </el-col>

                                <el-col :span="12">
                                    <el-form-item label="提报人">
                                        <span class="viewSpan">{{ mainData.createPsnName }}</span>
                                    </el-form-item>
                                </el-col> -->
                                <OtherInfo :data.sync="mainData" :main-id="mainData.id" :data-state="dataState"
                                    style="position: absolute; bottom: 0; width: 100%; margin-top: 20px" />
                            </el-row>
                        </el-form>
                    </el-scrollbar>
                </el-main>
                <!-- 选择模版 -->
                <!-- <Shortcut :templateShow="templateShow" @templateClick="templateClick" /> -->
            </el-container>
        </FormWindow>
    </div>
</template>

<script>
// vuex缓存数据
import { mapGetters } from 'vuex';

// 接口api
import SimpleBoardTitle from '@/view/components/SimpleBoard/SimpleBoardTitle'
import pd from '@/api/projectDemonstration/projectDemonstration.js'
import taskApi from '@/api/_system/task';
// 组件
import FormWindow from '@/view/components/FormWindow/FormWindow';
import OtherInfo from '@/view/litigation/caseManage/case/caseChild/OtherInfo';
import Money from '@/view/components/Money/index';
import UploadDoc from '@/view/components/UploadDoc/UploadDoc.vue'

export default {
    name: 'ProjectDemonstrationDetail',
    inject: ['layout', 'mcpLayout'],
    components: {
        FormWindow,
        OtherInfo,
        SimpleBoardTitle,
        Money,
        UploadDoc
    },
    computed: {
        ...mapGetters(['orgContext']),
        isView: function () {
            return this.dataState === this.utils.formState.VIEW;
        },
        templateShow() {
            return this.mainData.dataStateCode === this.utils.dataState_BPM.SAVE.code && this.dataState !== this.utils.formState.VIEW;
        },
    },
    data() {
        return {
            title: null,
            type: null,
            view: 'old',
            dataState: null,
            functionId: null,
            mainData: {
                id: null,
                projectNumber: null,
                projectName: null,
                involvedAmount: null,
                projectBackground: null,
                relatedPartySituation: null,
                preliminaryIdeas: null,
                keyFocusOfArgumentation: null,
                existsProblem: null,
                createOgnId: null, //当前机构ID
                createOgnName: null, //当前机构名称
                createDeptId: null, //当前部门ID
                createDeptName: null, //当前部门名称
                createGroupId: null, //当前部门ID
                createGroupName: null, //当前部门名称
                createPsnId: null, //当前人ID
                createPsnName: null, //当前人名称
                createOrgId: null, //当前组织ID
                createOrgName: null, //当前组织名称
                createPsnFullId: null, //当前人全路径ID
                createPsnFullName: null, //当前人全路径名称
                createPsnPhone: null, //经办人电话
                createTime: null, //创建时间
                dataState: this.utils.dataState_BPM.SAVE.name, //数据状态
                dataStateCode: this.utils.dataState_BPM.SAVE.code, //数据状态编码
            },
            loading: false,
            loadingText: '加载中...',
            rules: {
                projectName: [{ required: true, message: '请输入项目名称', trigger: 'blur' }],
                involvedAmount: [{ required: true, message: '请输入涉及金额', trigger: 'blur' }],
                projectBackground: [{ required: true, message: '请输入项目背景', trigger: 'blur' }],
                relatedPartySituation: [{ required: true, message: '请输入相关方情况', trigger: 'blur' }],
                preliminaryIdeas: [{ required: true, message: '请输入初步思路', trigger: 'blur' }],
                keyFocusOfArgumentation: [{ required: true, message: '请输入论证关注重点', trigger: 'blur' }],
                existsProblem: [{ required: true, message: '请输入存在的问题', trigger: 'blur' }],
            },
        };

    },
    provide() {
        return {
            parentCase: this,
        };
    },
    methods: {
        initData(temp, dataState) {
            this.dataState = dataState;
            Object.assign(this.mainData, temp);
            const year = new Date().getFullYear();
            this.utils.createKvsequence('ZDXMCL' + year, 6).then((value) => {
                this.mainData.projectNumber = value.data.kvsequence;
            });
        },
        loadData(dataState, dataId) {
            this.title = this.$route.title
            this.functionId = this.$route.query.functionId;
            if (this.$route.query.view !== undefined && this.$route.query.view !== '') this.view = this.$route.query.view;
            if (this.$route.query.type !== undefined && this.$route.query.type !== '') this.type = this.$route.query.type;
            this.dataState = dataState;
            pd.queryById(dataId).then((response) => {
                this.mainData = response.data.data;
                this.loading = false;
            });
        },
        save() {
            return new Promise((resolve, reject) => {
                pd.saveOrUpdate(this.mainData)
                    .then((response) => {
                        resolve(response);
                    })
                    .catch((error) => {
                        reject(error);
                    });
            });
        },
        save_: function () {
            this.save().then(() => {
                this.$message.success('保存成功!');
            })
        },
        approval_: function () {
            this.$refs['dataForm'].validate((valid) => {
                if (valid) {
                    this.mainData.isSubmit = true;
                    this.save().then(() => {
                        const tabId = this.mainData.id;
                        if (this.mainData.dataStateCode == this.utils.dataState_BPM.SAVE.code) {
                            taskApi.selectFunctionId({ functionCode: 'project_demonstration_main' }).then((res) => {
                                const functionId = res.data.data[0].ID;
                                this.layout.openNewTab('重大事项初步审查', 'design_page', 'design_page', tabId, {
                                    ...this.utils.routeState.NEW(tabId),
                                    functionId: functionId,
                                    businessKey: tabId,
                                    entranceType: 'FLOWABLE',
                                    create: 'create',
                                    view: 'new'
                                });
                            });
                        } else {
                            const tabId = this.mainData.id;
                            taskApi.selectTaskId({ businessKey: tabId, isView: '' }).then((res) => {
                                const functionId = res.data.data[0].ID;
                                const uuid = this.utils.createUUID();
                                this.layout.openNewTab('重大事项初步审查', 'design_page', 'design_page', uuid, {
                                    processInstanceId: res.data.data[0].PID, //流程实例
                                    taskId: res.data.data[0].ID, //任务ID
                                    businessKey: tabId, //业务数据ID
                                    functionId: functionId, //不传也可以，只有首环节需要传，实例启动就可以不传了
                                    entranceType: 'FLOWABLE',
                                    type: 'toDeal',
                                    view: 'new',
                                    create: 'create',
                                });
                            });
                        }
                    }).then(() => {
                        this.mcpLayout.closeTab();
                    });
                }
            });
        },

    },
};
</script>

<style scoped>
.viewSpan {
    font-size: 12px;
    color: #1890ff;
    padding: 0 5px;
    word-wrap: break-word;
}

.grid-content {
    border-radius: 4px;
    min-height: 100px;
}

/* 过于长的label分两行展示样式 */
/deep/.fold_label .el-form-item__label {
    white-space: pre-line;
    /* text-align-last: justify;
  text-align: justify;*/
    margin-top: -4px;
    line-height: 25px;
    text-justify: distribute-all-lines;
}

/* 其他label根据宽度自动撑开 */
/* /deep/.el-form-item__label {
  text-align-last: justify;
  text-align: justify;
  text-justify: distribute-all-lines;
} */
</style>
