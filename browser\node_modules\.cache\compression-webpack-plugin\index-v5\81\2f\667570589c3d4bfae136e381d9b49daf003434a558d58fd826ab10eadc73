
cdcb24d6086977eaafe2c22f606a12218629f754	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.176.1754018536329.js\",\"contentHash\":\"7f00170f84053fd47b058d0d36461121\"}","integrity":"sha512-K3ftf+aDce28nzS5GmK9A2DZhEpCTogdZyv6seU/4pf8cCVKFPbdLIgHhbEwrbFZNigKXXzeYenNWxxnGn4zsA==","time":1754018576283,"size":767269}