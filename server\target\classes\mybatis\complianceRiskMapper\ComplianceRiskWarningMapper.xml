<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.klaw.dao.complianceRiskDao.ComplianceRiskWaringMapper">

    <select id="queryEventData" resultType="java.util.HashMap" >
        SELECT
        -- 风险事件数量
        count(id) fxCount,
        --  重大风险数量
        count(case when expected_risk_level = 'FX_ZHONGDAFX' then 1 else null end) zdCount,
        -- 已预警风险
        count(case when associated_risk_warning is not null and associated_risk_warning!='' then 1 else null end) yyjCount,

        --  应对中
        count(case when risk_mitigation_status = '3' then 1 else null end) ydzCount,
        --  已应对
        count(case when risk_mitigation_status = '4' then 1 else null end) yydCount,
        -- 已总结
        count(case when report_status = '6' then 1 else null end) yzjCount
        FROM
        compliance_risk_warning
        WHERE
         1=1
        <if test="orgId!='' and orgId!=null"> and create_org_id = #{orgId}</if>
        <if test="params.riskName!='' and params.riskName!=null"> and risk_name LIKE '%${params.riskName}%'</if>
        <if test="params.reportStatus!='' and params.reportStatus!=null"> and report_status = #{params.reportStatus}</if>
        <if test="params.fuzzyValue!='' and params.fuzzyValue!=null"> and (
            risk_name LIKE '%${params.fuzzyValue}%' OR expected_risk_level_name LIKE '%${params.fuzzyValue}%' OR business_domain_name LIKE '%${params.fuzzyValue}%'
        )</if>
        <if test="params.expectedRiskLevelArr!=null and params.expectedRiskLevelArr.length>0"> and (
            expected_risk_level IN
            <foreach collection="params.expectedRiskLevelArr" item="riskLevel" separator="," open="(" close=")">
                #{riskLevel}
            </foreach>
            )
        </if>
        <if test="params.businessDomainArr!=null and params.businessDomainArr.length>0"> and (
            business_domain IN
            <foreach collection="params.businessDomainArr" item="businessDomain" separator="," open="(" close=")">
                #{businessDomain}
            </foreach>
            )
        </if>
        <if test="params.reportingDateStart!=null">
            and create_time &gt;= #{params.reportingDateStart}
        </if>
        <if test="params.reportingDateEnd!=null">
            and create_time &lt;= #{params.reportingDateEnd}
        </if>

    </select>
</mapper>