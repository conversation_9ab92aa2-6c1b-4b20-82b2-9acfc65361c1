
10f4e4ee818b663c7643698aeaaeee9c060ce347	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.238.1754018536329.js\",\"contentHash\":\"eb0715af67def5ed086f04a1b9df3ba8\"}","integrity":"sha512-f35Ox0T0x9pfJwW6ui+E6dPypPmWBMo1YHM5qvDRkw3hK81mJD/AtfzrVBeydhvXd0tPkpuGaVBsnRGrYbifSQ==","time":1754018575996,"size":172129}