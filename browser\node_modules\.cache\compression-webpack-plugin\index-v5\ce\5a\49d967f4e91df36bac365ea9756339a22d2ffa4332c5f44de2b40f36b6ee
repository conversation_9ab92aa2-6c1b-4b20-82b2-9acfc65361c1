
4f3be4584bc34d1ab68f7164a59b8d8d3abaef7a	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.76.1754018536329.js\",\"contentHash\":\"8fb202d7cd624e1a4e575086c938fc97\"}","integrity":"sha512-w4+IahLy+Tcj8US3ziVTFMvaiX/EZFcoHFkIivmpvXF6tRdoadu5KT2L8Owi9M+GNY50Yp6P8JJmdiul+/2M9A==","time":1754018575959,"size":74862}