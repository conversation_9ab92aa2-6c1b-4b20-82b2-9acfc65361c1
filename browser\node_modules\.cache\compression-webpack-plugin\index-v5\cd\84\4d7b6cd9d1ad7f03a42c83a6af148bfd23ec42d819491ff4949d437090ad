
c5f10119f1f070766265ffaf6359675fa9b53132	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.430.1754018536329.js\",\"contentHash\":\"8bff1b0d7baad2fe237e02b1168ff610\"}","integrity":"sha512-CxraOvV+RJ4RkKogIYkZnRbh4drMiLLn1VB5DfDkYTpYwd99DD1lAXPMerfKJ3ZFOdo06IonvlDC1kU/A4VUJw==","time":1754018576041,"size":124822}