<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.klaw.dao.caseDao.ExcelMapper">
	<select id="xinzengleixingjine"  parameterType="java.util.Map" resultType="java.util.HashMap">
		SELECT
		*
		FROM
		(
		SELECT
		SUM (case_Money) num,
		diwei
		FROM
		(
		SELECT
		c.case_Money,
		CASE c.OUR_POSITION
		WHEN '原告' THEN 'zhusu'
		WHEN '申请人' THEN 'zhusu'
		WHEN '被告' THEN 'beisu'
		WHEN '被申请人' THEN 'beisu'
		WHEN '第三人' THEN 'beisu'
		END diwei
		FROM
		CASE_RECORDS c
		WHERE
		OUR_POSITION is not null
		<if test="casetype != null and casetype != ''">
			AND case_type = #{casetype}
		</if>
		<if test="modularname !=  null  and modularname != '' ">
			and  BELONG_PLATE = #{modularname}
		</if>
		and to_char(to_date(#{date},'yyyy-mm'),'yyyy-mm') = to_char(case_time,'yyyy-mm')   and ${perms}
		)
		GROUP BY
		diwei
		) PIVOT (
		MAX (num) FOR diwei IN ('beisu' AS beisu, 'zhusu' AS zhusu)
		)

	</select>
	<select id="weijieleixingjine"  parameterType="java.util.Map" resultType="java.util.HashMap">
		SELECT
		*
		FROM
		(
		SELECT
		SUM (case_Money) num,
		diwei
		FROM
		(
		SELECT
		c.case_Money,
		CASE c.OUR_POSITION
		WHEN '原告' THEN 'zhusu'
		WHEN '申请人' THEN 'zhusu'
		WHEN '被告' THEN 'beisu'
		WHEN '被申请人' THEN 'beisu'
		WHEN '第三人' THEN 'beisu'
		END diwei
		FROM
		CASE_RECORDS c
		WHERE
		OUR_POSITION IS NOT NULL
		and ID  in( select id from (select a.ID,a.CASE_NAME,a.CASE_NUMBER,a.CASE_TIME,a.CAUSE_NAME,a.CAUSE_ID,a.CASE_TYPE,a.CASE_TYPE_ID,a.OUR_POSITION,a.VENUE_IS_OUT,a.VENUE_PROVINCE,a.VENUE_CITY,
a.VENUE_REGION,a.VENUE_ADDRESS,a.CASE_MONEY,a.WHETHER_MAJOR,a.COURT,a.COURT_ID,a.TRIAL,a.TRIAL_ID,a.JUDGE_MAIN,a.JUDGE_MAIN_ID,a.JUDGE_MAIN_PHONE,a.JUDGE_VICE,
a.PROOF_DATE,a.REPLY_DATE,a.DATA_STATE,a.DATA_STATE_CODE,a.CASE_PROCESS,a.CASE_PROCESS_IDS,a.CREATE_OGN_ID,a.CREATE_OGN_NAME,a.CREATE_DEPT_ID,a.CREATE_DEPT_NAME,
a.CREATE_GROUP_ID,a.CREATE_GROUP_NAME,a.CREATE_ORG_ID,a.CREATE_ORG_NAME,a.CREATE_PSN_ID,a.CREATE_PSN_NAME,a.CREATE_PSN_FULL_ID,a.CREATE_PSN_FULL_NAME,a.CREATE_TIME,
a.CASE_DETAILS,a.FILES,a.JUDGE_VICE_PHONE,a.CASE_ACCEPT_MONEY,a.CASE_ACCEPT_MONEY_OUR,a.HANDLE_SITUATION,a.HANDLE_SITUATION_ID,a.WHETHER_FORCE_EXECUTE,a.FORCE_EXECUTE,
a.FORCE_EXECUTE_ID,a.RECEIVE_MONEY,a.SEND_MONEY,a.CASE_CURRENT_PROCESS,a.CASE_CURRENT_PROCESS_ID,a.CASE_PROCESS_TYPE,a.CASE_PROCESS_TYPE_CODE,a.PARENT_ID,a.CASE_KIND,
a.CASE_KIND_CODE,a.CASE_ALIAS,a.PREPOSITION_ID,a.INTERFILE,a.PREPOSITION_TYPE,a.BELONG_PLATE_ID,a.BELONG_PLATE,a.WHETHER_EASY,a.HAS_AGENT,a.HAS_CASE_PROJECT,
a.HAS_DISPUTE,a.HAS_PRESERVATION,a.HAS_OTHER_DATA,a.HAS_CASE_PROCESS,a.HAS_CHANGE_CLAIM,a.HAS_OPPOSITE_CLAIM,a.HAS_APPRAISAL,a.HAS_SEIZE,a.HAS_JUDGE_CONTENT,
a.HAS_JUDGE_FILES,a.RESULT_ADDRESS,a.RESULT_ADDRESS_ID,a.RESULT,a.RESULT_ID,a.ACTUAL_RECEIVE_MONEY,a.ACTUAL_RECEIVE_OTHER,a.ACTUAL_SEND_MONEY,a.ACTUAL_SEND_OTHER,
a.REMARKS,a.WHETHER_CONTINUE,a.WHETHER_INCLUDE,a.CONTINUE_END_DATE,a.UPDATE_TIME,a.CASE_CODE,a.HAS_CASE,a.WHETHER_BLANK,a.WIN_ANALYSIS,a.EXP_SUMMARY,a.ACCOUNT_DES,
a.HAS_CHILDREN,a.CAUSE_OF_IN_ID,a.CAUSE_OF_IN,a.JUDGE_PERSONS,
b.close_time as easy_end_time,c.case_time as process_end_time,
case a.whether_easy when 1 then b.close_time else c.case_time end end_time,
case a.whether_easy when 1 then b.close_model else c.HANDLE_SITUATION end end_model,
case a.whether_easy when 1 then b.result else c.result end end_result
from case_records a
left join case_easy b on a.id = b.parent_id
left join case_records c on c.parent_id = a.id and c.case_process_type_code = 'end'
where a.parent_id is null and a.case_current_process != '不起诉') AA where to_char(to_date(#{date},'yyyy-mm'),'yyyy-mm') <![CDATA[ >= ]]> to_char(case_time,'yyyy-mm') and (end_time is null or  to_char(to_date(#{date},'yyyy-mm'),'yyyy-mm') <![CDATA[ < ]]> to_char(end_time,'yyyy-mm')   ) and ${perms} )
		<if test="casetype != null and casetype != ''">
			AND case_type = #{casetype}
		</if>
		<if test="modularname !=  null  and modularname != '' ">
			and  BELONG_PLATE = #{modularname}
		</if>
		)
		GROUP BY
		diwei
		) PIVOT (
		MAX (num) FOR diwei IN ('beisu' AS beisu, 'zhusu' AS zhusu)
		)
	</select>
	<select id="xinzengleixingshuliang"  parameterType="java.util.Map" resultType="java.util.HashMap">

		SELECT
		*
		FROM
		(
		SELECT
		count (1) num,
		diwei
		FROM
		(
		SELECT
		c.case_Money,
		CASE c.OUR_POSITION
		WHEN '原告' THEN 'zhusu'
		WHEN '申请人' THEN 'zhusu'
		WHEN '被告' THEN 'beisu'
		WHEN '被申请人' THEN 'beisu'
		WHEN '第三人' THEN 'beisu'
		END diwei
		FROM
		CASE_RECORDS c
		WHERE
		OUR_POSITION IS NOT NULL
		<if test="casetype != null and casetype != ''">
			AND case_type = #{casetype}
		</if>
		<if test="modularname !=  null  and modularname != '' ">
			and  BELONG_PLATE = #{modularname}
		</if>
		and to_char(to_date(#{date},'yyyy-mm'),'yyyy-mm') = to_char(case_time,'yyyy-mm')   and ${perms}
		)
		GROUP BY
		diwei
		) PIVOT (
		MAX (num) FOR diwei IN ('beisu' AS beisu, 'zhusu' AS zhusu)
		)

	</select>
	<select id="weijieleixingshuliang"  parameterType="java.util.Map" resultType="java.util.HashMap">


		SELECT
		*
		FROM
		(
		SELECT
		count(1) num,
		diwei
		FROM
		(
		SELECT
		c.case_Money,
		CASE c.OUR_POSITION
		WHEN '原告' THEN 'zhusu'
		WHEN '申请人' THEN 'zhusu'
		WHEN '被告' THEN 'beisu'
		WHEN '被申请人' THEN 'beisu'
		WHEN '第三人' THEN 'beisu'
		END diwei
		FROM
		CASE_RECORDS c
		WHERE
		OUR_POSITION IS NOT NULL
		and ID  in( select id from (select a.ID,a.CASE_NAME,a.CASE_NUMBER,a.CASE_TIME,a.CAUSE_NAME,a.CAUSE_ID,a.CASE_TYPE,a.CASE_TYPE_ID,a.OUR_POSITION,a.VENUE_IS_OUT,a.VENUE_PROVINCE,a.VENUE_CITY,
a.VENUE_REGION,a.VENUE_ADDRESS,a.CASE_MONEY,a.WHETHER_MAJOR,a.COURT,a.COURT_ID,a.TRIAL,a.TRIAL_ID,a.JUDGE_MAIN,a.JUDGE_MAIN_ID,a.JUDGE_MAIN_PHONE,a.JUDGE_VICE,
a.PROOF_DATE,a.REPLY_DATE,a.DATA_STATE,a.DATA_STATE_CODE,a.CASE_PROCESS,a.CASE_PROCESS_IDS,a.CREATE_OGN_ID,a.CREATE_OGN_NAME,a.CREATE_DEPT_ID,a.CREATE_DEPT_NAME,
a.CREATE_GROUP_ID,a.CREATE_GROUP_NAME,a.CREATE_ORG_ID,a.CREATE_ORG_NAME,a.CREATE_PSN_ID,a.CREATE_PSN_NAME,a.CREATE_PSN_FULL_ID,a.CREATE_PSN_FULL_NAME,a.CREATE_TIME,
a.CASE_DETAILS,a.FILES,a.JUDGE_VICE_PHONE,a.CASE_ACCEPT_MONEY,a.CASE_ACCEPT_MONEY_OUR,a.HANDLE_SITUATION,a.HANDLE_SITUATION_ID,a.WHETHER_FORCE_EXECUTE,a.FORCE_EXECUTE,
a.FORCE_EXECUTE_ID,a.RECEIVE_MONEY,a.SEND_MONEY,a.CASE_CURRENT_PROCESS,a.CASE_CURRENT_PROCESS_ID,a.CASE_PROCESS_TYPE,a.CASE_PROCESS_TYPE_CODE,a.PARENT_ID,a.CASE_KIND,
a.CASE_KIND_CODE,a.CASE_ALIAS,a.PREPOSITION_ID,a.INTERFILE,a.PREPOSITION_TYPE,a.BELONG_PLATE_ID,a.BELONG_PLATE,a.WHETHER_EASY,a.HAS_AGENT,a.HAS_CASE_PROJECT,
a.HAS_DISPUTE,a.HAS_PRESERVATION,a.HAS_OTHER_DATA,a.HAS_CASE_PROCESS,a.HAS_CHANGE_CLAIM,a.HAS_OPPOSITE_CLAIM,a.HAS_APPRAISAL,a.HAS_SEIZE,a.HAS_JUDGE_CONTENT,
a.HAS_JUDGE_FILES,a.RESULT_ADDRESS,a.RESULT_ADDRESS_ID,a.RESULT,a.RESULT_ID,a.ACTUAL_RECEIVE_MONEY,a.ACTUAL_RECEIVE_OTHER,a.ACTUAL_SEND_MONEY,a.ACTUAL_SEND_OTHER,
a.REMARKS,a.WHETHER_CONTINUE,a.WHETHER_INCLUDE,a.CONTINUE_END_DATE,a.UPDATE_TIME,a.CASE_CODE,a.HAS_CASE,a.WHETHER_BLANK,a.WIN_ANALYSIS,a.EXP_SUMMARY,a.ACCOUNT_DES,
a.HAS_CHILDREN,a.CAUSE_OF_IN_ID,a.CAUSE_OF_IN,a.JUDGE_PERSONS,
b.close_time as easy_end_time,c.case_time as process_end_time,
case a.whether_easy when 1 then b.close_time else c.case_time end end_time,
case a.whether_easy when 1 then b.close_model else c.HANDLE_SITUATION end end_model,
case a.whether_easy when 1 then b.result else c.result end end_result
from case_records a
left join case_easy b on a.id = b.parent_id
left join case_records c on c.parent_id = a.id and c.case_process_type_code = 'end'
where a.parent_id is null and a.case_current_process != '不起诉') AA where to_char(to_date(#{date},'yyyy-mm'),'yyyy-mm') <![CDATA[ >= ]]> to_char(case_time,'yyyy-mm') and (end_time is null or  to_char(to_date(#{date},'yyyy-mm'),'yyyy-mm') <![CDATA[ < ]]> to_char(end_time,'yyyy-mm')   ) and ${perms} )
		<if test="casetype != null and casetype != ''">
			AND case_type = #{casetype}
		</if>
		<if test="modularname !=  null  and modularname != '' ">
			and  BELONG_PLATE = #{modularname}
		</if>
		)
		GROUP BY
		diwei
		) PIVOT (
		MAX (num) FOR diwei IN ('beisu' AS beisu, 'zhusu' AS zhusu)
		)


	</select>
	<select id="yijieleixingjine"  parameterType="java.util.Map" resultType="java.util.HashMap">

		select sum(case_money) yijie from (
		SELECT
		case_money
		FROM
		CASE_RECORDS
		WHERE 1=1
		<if test="casetype != null and casetype != ''">
			AND case_type = #{casetype}
		</if>
		<if test="modularname !=  null  and modularname != '' ">
			and  BELONG_PLATE = #{modularname}
		</if>

		and ID  in( select id from (select a.ID,a.CASE_NAME,a.CASE_NUMBER,a.CASE_TIME,a.CAUSE_NAME,a.CAUSE_ID,a.CASE_TYPE,a.CASE_TYPE_ID,a.OUR_POSITION,a.VENUE_IS_OUT,a.VENUE_PROVINCE,a.VENUE_CITY,
a.VENUE_REGION,a.VENUE_ADDRESS,a.CASE_MONEY,a.WHETHER_MAJOR,a.COURT,a.COURT_ID,a.TRIAL,a.TRIAL_ID,a.JUDGE_MAIN,a.JUDGE_MAIN_ID,a.JUDGE_MAIN_PHONE,a.JUDGE_VICE,
a.PROOF_DATE,a.REPLY_DATE,a.DATA_STATE,a.DATA_STATE_CODE,a.CASE_PROCESS,a.CASE_PROCESS_IDS,a.CREATE_OGN_ID,a.CREATE_OGN_NAME,a.CREATE_DEPT_ID,a.CREATE_DEPT_NAME,
a.CREATE_GROUP_ID,a.CREATE_GROUP_NAME,a.CREATE_ORG_ID,a.CREATE_ORG_NAME,a.CREATE_PSN_ID,a.CREATE_PSN_NAME,a.CREATE_PSN_FULL_ID,a.CREATE_PSN_FULL_NAME,a.CREATE_TIME,
a.CASE_DETAILS,a.FILES,a.JUDGE_VICE_PHONE,a.CASE_ACCEPT_MONEY,a.CASE_ACCEPT_MONEY_OUR,a.HANDLE_SITUATION,a.HANDLE_SITUATION_ID,a.WHETHER_FORCE_EXECUTE,a.FORCE_EXECUTE,
a.FORCE_EXECUTE_ID,a.RECEIVE_MONEY,a.SEND_MONEY,a.CASE_CURRENT_PROCESS,a.CASE_CURRENT_PROCESS_ID,a.CASE_PROCESS_TYPE,a.CASE_PROCESS_TYPE_CODE,a.PARENT_ID,a.CASE_KIND,
a.CASE_KIND_CODE,a.CASE_ALIAS,a.PREPOSITION_ID,a.INTERFILE,a.PREPOSITION_TYPE,a.BELONG_PLATE_ID,a.BELONG_PLATE,a.WHETHER_EASY,a.HAS_AGENT,a.HAS_CASE_PROJECT,
a.HAS_DISPUTE,a.HAS_PRESERVATION,a.HAS_OTHER_DATA,a.HAS_CASE_PROCESS,a.HAS_CHANGE_CLAIM,a.HAS_OPPOSITE_CLAIM,a.HAS_APPRAISAL,a.HAS_SEIZE,a.HAS_JUDGE_CONTENT,
a.HAS_JUDGE_FILES,a.RESULT_ADDRESS,a.RESULT_ADDRESS_ID,a.RESULT,a.RESULT_ID,a.ACTUAL_RECEIVE_MONEY,a.ACTUAL_RECEIVE_OTHER,a.ACTUAL_SEND_MONEY,a.ACTUAL_SEND_OTHER,
a.REMARKS,a.WHETHER_CONTINUE,a.WHETHER_INCLUDE,a.CONTINUE_END_DATE,a.UPDATE_TIME,a.CASE_CODE,a.HAS_CASE,a.WHETHER_BLANK,a.WIN_ANALYSIS,a.EXP_SUMMARY,a.ACCOUNT_DES,
a.HAS_CHILDREN,a.CAUSE_OF_IN_ID,a.CAUSE_OF_IN,a.JUDGE_PERSONS,
b.close_time as easy_end_time,c.case_time as process_end_time,
case a.whether_easy when 1 then b.close_time else c.case_time end end_time,
case a.whether_easy when 1 then b.close_model else c.HANDLE_SITUATION end end_model,
case a.whether_easy when 1 then b.result else c.result end end_result
from case_records a
left join case_easy b on a.id = b.parent_id
left join case_records c on c.parent_id = a.id and c.case_process_type_code = 'end'
where a.parent_id is null and a.case_current_process != '不起诉') AA where  to_char(to_date(#{date},'yyyy-mm'),'yyyy-mm') =  to_char(end_time,'yyyy-mm') and ${perms} )


		)

	</select>
	<select id="yijieleixingshuliang"  parameterType="java.util.Map" resultType="java.util.HashMap">
		select count(1) yijie from (
		SELECT
		case_money
		FROM
		CASE_RECORDS
		WHERE 1=1
		<if test="casetype != null and casetype != ''">
			AND case_type = #{casetype}
		</if>
		<if test="modularname !=  null  and modularname != '' ">
			and  BELONG_PLATE = #{modularname}
		</if>


		and ID  in( select id from (select a.ID,a.CASE_NAME,a.CASE_NUMBER,a.CASE_TIME,a.CAUSE_NAME,a.CAUSE_ID,a.CASE_TYPE,a.CASE_TYPE_ID,a.OUR_POSITION,a.VENUE_IS_OUT,a.VENUE_PROVINCE,a.VENUE_CITY,
a.VENUE_REGION,a.VENUE_ADDRESS,a.CASE_MONEY,a.WHETHER_MAJOR,a.COURT,a.COURT_ID,a.TRIAL,a.TRIAL_ID,a.JUDGE_MAIN,a.JUDGE_MAIN_ID,a.JUDGE_MAIN_PHONE,a.JUDGE_VICE,
a.PROOF_DATE,a.REPLY_DATE,a.DATA_STATE,a.DATA_STATE_CODE,a.CASE_PROCESS,a.CASE_PROCESS_IDS,a.CREATE_OGN_ID,a.CREATE_OGN_NAME,a.CREATE_DEPT_ID,a.CREATE_DEPT_NAME,
a.CREATE_GROUP_ID,a.CREATE_GROUP_NAME,a.CREATE_ORG_ID,a.CREATE_ORG_NAME,a.CREATE_PSN_ID,a.CREATE_PSN_NAME,a.CREATE_PSN_FULL_ID,a.CREATE_PSN_FULL_NAME,a.CREATE_TIME,
a.CASE_DETAILS,a.FILES,a.JUDGE_VICE_PHONE,a.CASE_ACCEPT_MONEY,a.CASE_ACCEPT_MONEY_OUR,a.HANDLE_SITUATION,a.HANDLE_SITUATION_ID,a.WHETHER_FORCE_EXECUTE,a.FORCE_EXECUTE,
a.FORCE_EXECUTE_ID,a.RECEIVE_MONEY,a.SEND_MONEY,a.CASE_CURRENT_PROCESS,a.CASE_CURRENT_PROCESS_ID,a.CASE_PROCESS_TYPE,a.CASE_PROCESS_TYPE_CODE,a.PARENT_ID,a.CASE_KIND,
a.CASE_KIND_CODE,a.CASE_ALIAS,a.PREPOSITION_ID,a.INTERFILE,a.PREPOSITION_TYPE,a.BELONG_PLATE_ID,a.BELONG_PLATE,a.WHETHER_EASY,a.HAS_AGENT,a.HAS_CASE_PROJECT,
a.HAS_DISPUTE,a.HAS_PRESERVATION,a.HAS_OTHER_DATA,a.HAS_CASE_PROCESS,a.HAS_CHANGE_CLAIM,a.HAS_OPPOSITE_CLAIM,a.HAS_APPRAISAL,a.HAS_SEIZE,a.HAS_JUDGE_CONTENT,
a.HAS_JUDGE_FILES,a.RESULT_ADDRESS,a.RESULT_ADDRESS_ID,a.RESULT,a.RESULT_ID,a.ACTUAL_RECEIVE_MONEY,a.ACTUAL_RECEIVE_OTHER,a.ACTUAL_SEND_MONEY,a.ACTUAL_SEND_OTHER,
a.REMARKS,a.WHETHER_CONTINUE,a.WHETHER_INCLUDE,a.CONTINUE_END_DATE,a.UPDATE_TIME,a.CASE_CODE,a.HAS_CASE,a.WHETHER_BLANK,a.WIN_ANALYSIS,a.EXP_SUMMARY,a.ACCOUNT_DES,
a.HAS_CHILDREN,a.CAUSE_OF_IN_ID,a.CAUSE_OF_IN,a.JUDGE_PERSONS,
b.close_time as easy_end_time,c.case_time as process_end_time,
case a.whether_easy when 1 then b.close_time else c.case_time end end_time,
case a.whether_easy when 1 then b.close_model else c.HANDLE_SITUATION end end_model,
case a.whether_easy when 1 then b.result else c.result end end_result
from case_records a
left join case_easy b on a.id = b.parent_id
left join case_records c on c.parent_id = a.id and c.case_process_type_code = 'end'
where a.parent_id is null and a.case_current_process != '不起诉') AA where  to_char(to_date(#{date},'yyyy-mm'),'yyyy-mm') =  to_char(end_time,'yyyy-mm') and ${perms} )

		)
	</select>
	<select id="xinfacasejine"  parameterType="java.util.Map" resultType="java.util.HashMap">
		SELECT
		reasonname,
		case when yuangao is null then 0 else yuangao end +case when sheqing is null then 0 else sheqing end yuangao,
		case when beigao is null then 0 else beigao end +case when disan is null then 0 else disan end +case when bshenqing is null then 0 else bshenqing end   beigao ,
		case when yuangao is null then 0 else yuangao end + case when beigao is null then 0 else beigao end + case when disan is null then 0 else disan end+case when bshenqing is null then 0 else bshenqing end+case when sheqing is null then 0 else sheqing end  heji
		from (
		SELECT
		SUM (CASE_MONEY) num,
		b.cause_name reasonname,OUR_POSITION	 from(
		select * from CASE_RECORDS  c
		where   PARENT_ID is null and cause_name is not null and  OUR_POSITION is not null
		<if test="casetype !=  null  and casetype != '' ">
			and	BELONG_PLATE=#{casetype}
		</if>
			and to_char(to_date(#{date},'yyyy-mm'),'yyyy-mm') = to_char(case_time,'yyyy-mm')   and ${perms}
		) b
		group by b.cause_name,OUR_POSITION)
		PIVOT (
		max (num) FOR OUR_POSITION IN ('原告' as yuangao, '被告' as beigao,'第三' as disan,'申请人' as sheqing,'被申请人' as bshenqing)
		)
	</select>
	<select id="weijiecasejine"  parameterType="java.util.Map" resultType="java.util.HashMap">
			SELECT
			reasonname,
		case when yuangao is null then 0 else yuangao end +case when sheqing is null then 0 else sheqing end yuangao,
		case when beigao is null then 0 else beigao end +case when disan is null then 0 else disan end +case when bshenqing is null then 0 else bshenqing end   beigao ,
		case when yuangao is null then 0 else yuangao end + case when beigao is null then 0 else beigao end + case when disan is null then 0 else disan end+case when bshenqing is null then 0 else bshenqing end+case when sheqing is null then 0 else sheqing end  heji
		from (
		SELECT
			SUM (CASE_MONEY) num,
			b.cause_name reasonname,OUR_POSITION	 from(
			select * from CASE_RECORDS  c
		where   PARENT_ID is null and cause_name is not null and  OUR_POSITION is not null
		and ID  in( select id from (select a.ID,a.CASE_NAME,a.CASE_NUMBER,a.CASE_TIME,a.CAUSE_NAME,a.CAUSE_ID,a.CASE_TYPE,a.CASE_TYPE_ID,a.OUR_POSITION,a.VENUE_IS_OUT,a.VENUE_PROVINCE,a.VENUE_CITY,
a.VENUE_REGION,a.VENUE_ADDRESS,a.CASE_MONEY,a.WHETHER_MAJOR,a.COURT,a.COURT_ID,a.TRIAL,a.TRIAL_ID,a.JUDGE_MAIN,a.JUDGE_MAIN_ID,a.JUDGE_MAIN_PHONE,a.JUDGE_VICE,
a.PROOF_DATE,a.REPLY_DATE,a.DATA_STATE,a.DATA_STATE_CODE,a.CASE_PROCESS,a.CASE_PROCESS_IDS,a.CREATE_OGN_ID,a.CREATE_OGN_NAME,a.CREATE_DEPT_ID,a.CREATE_DEPT_NAME,
a.CREATE_GROUP_ID,a.CREATE_GROUP_NAME,a.CREATE_ORG_ID,a.CREATE_ORG_NAME,a.CREATE_PSN_ID,a.CREATE_PSN_NAME,a.CREATE_PSN_FULL_ID,a.CREATE_PSN_FULL_NAME,a.CREATE_TIME,
a.CASE_DETAILS,a.FILES,a.JUDGE_VICE_PHONE,a.CASE_ACCEPT_MONEY,a.CASE_ACCEPT_MONEY_OUR,a.HANDLE_SITUATION,a.HANDLE_SITUATION_ID,a.WHETHER_FORCE_EXECUTE,a.FORCE_EXECUTE,
a.FORCE_EXECUTE_ID,a.RECEIVE_MONEY,a.SEND_MONEY,a.CASE_CURRENT_PROCESS,a.CASE_CURRENT_PROCESS_ID,a.CASE_PROCESS_TYPE,a.CASE_PROCESS_TYPE_CODE,a.PARENT_ID,a.CASE_KIND,
a.CASE_KIND_CODE,a.CASE_ALIAS,a.PREPOSITION_ID,a.INTERFILE,a.PREPOSITION_TYPE,a.BELONG_PLATE_ID,a.BELONG_PLATE,a.WHETHER_EASY,a.HAS_AGENT,a.HAS_CASE_PROJECT,
a.HAS_DISPUTE,a.HAS_PRESERVATION,a.HAS_OTHER_DATA,a.HAS_CASE_PROCESS,a.HAS_CHANGE_CLAIM,a.HAS_OPPOSITE_CLAIM,a.HAS_APPRAISAL,a.HAS_SEIZE,a.HAS_JUDGE_CONTENT,
a.HAS_JUDGE_FILES,a.RESULT_ADDRESS,a.RESULT_ADDRESS_ID,a.RESULT,a.RESULT_ID,a.ACTUAL_RECEIVE_MONEY,a.ACTUAL_RECEIVE_OTHER,a.ACTUAL_SEND_MONEY,a.ACTUAL_SEND_OTHER,
a.REMARKS,a.WHETHER_CONTINUE,a.WHETHER_INCLUDE,a.CONTINUE_END_DATE,a.UPDATE_TIME,a.CASE_CODE,a.HAS_CASE,a.WHETHER_BLANK,a.WIN_ANALYSIS,a.EXP_SUMMARY,a.ACCOUNT_DES,
a.HAS_CHILDREN,a.CAUSE_OF_IN_ID,a.CAUSE_OF_IN,a.JUDGE_PERSONS,
b.close_time as easy_end_time,c.case_time as process_end_time,
case a.whether_easy when 1 then b.close_time else c.case_time end end_time,
case a.whether_easy when 1 then b.close_model else c.HANDLE_SITUATION end end_model,
case a.whether_easy when 1 then b.result else c.result end end_result
from case_records a
left join case_easy b on a.id = b.parent_id
left join case_records c on c.parent_id = a.id and c.case_process_type_code = 'end'
where a.parent_id is null and a.case_current_process != '不起诉') AA where to_char(to_date(#{date},'yyyy-mm'),'yyyy-mm') <![CDATA[ >= ]]> to_char(case_time,'yyyy-mm') and (end_time is null or  to_char(to_date(#{date},'yyyy-mm'),'yyyy-mm') <![CDATA[ < ]]> to_char(end_time,'yyyy-mm')   ) and ${perms} )
		<if test="casetype !=  null  and casetype != '' ">
			and	BELONG_PLATE=#{casetype}
		</if>
		) b
		group by b.cause_name,OUR_POSITION)
		PIVOT (
				max (num) FOR OUR_POSITION IN ('原告' as yuangao, '被告' as beigao,'第三' as disan,'申请人' as sheqing,'被申请人' as bshenqing)
			)
	</select>
	<select id="weijiecase"  parameterType="java.util.Map" resultType="java.util.HashMap">
		SELECT
			reasonname,
		case when yuangao is null then 0 else yuangao end +case when sheqing is null then 0 else sheqing end yuangao,
		case when beigao is null then 0 else beigao end +case when disan is null then 0 else disan end +case when bshenqing is null then 0 else bshenqing end   beigao ,
		case when yuangao is null then 0 else yuangao end + case when beigao is null then 0 else beigao end + case when disan is null then 0 else disan end+case when bshenqing is null then 0 else bshenqing end+case when sheqing is null then 0 else sheqing end  heji
		from (
		SELECT
			COUNT (1) num,
			b.cause_name reasonname,OUR_POSITION	 from(
			select * from CASE_RECORDS  c
		where   PARENT_ID is null and cause_name is not null and  OUR_POSITION is not null
		and ID  in( select id from (select a.ID,a.CASE_NAME,a.CASE_NUMBER,a.CASE_TIME,a.CAUSE_NAME,a.CAUSE_ID,a.CASE_TYPE,a.CASE_TYPE_ID,a.OUR_POSITION,a.VENUE_IS_OUT,a.VENUE_PROVINCE,a.VENUE_CITY,
a.VENUE_REGION,a.VENUE_ADDRESS,a.CASE_MONEY,a.WHETHER_MAJOR,a.COURT,a.COURT_ID,a.TRIAL,a.TRIAL_ID,a.JUDGE_MAIN,a.JUDGE_MAIN_ID,a.JUDGE_MAIN_PHONE,a.JUDGE_VICE,
a.PROOF_DATE,a.REPLY_DATE,a.DATA_STATE,a.DATA_STATE_CODE,a.CASE_PROCESS,a.CASE_PROCESS_IDS,a.CREATE_OGN_ID,a.CREATE_OGN_NAME,a.CREATE_DEPT_ID,a.CREATE_DEPT_NAME,
a.CREATE_GROUP_ID,a.CREATE_GROUP_NAME,a.CREATE_ORG_ID,a.CREATE_ORG_NAME,a.CREATE_PSN_ID,a.CREATE_PSN_NAME,a.CREATE_PSN_FULL_ID,a.CREATE_PSN_FULL_NAME,a.CREATE_TIME,
a.CASE_DETAILS,a.FILES,a.JUDGE_VICE_PHONE,a.CASE_ACCEPT_MONEY,a.CASE_ACCEPT_MONEY_OUR,a.HANDLE_SITUATION,a.HANDLE_SITUATION_ID,a.WHETHER_FORCE_EXECUTE,a.FORCE_EXECUTE,
a.FORCE_EXECUTE_ID,a.RECEIVE_MONEY,a.SEND_MONEY,a.CASE_CURRENT_PROCESS,a.CASE_CURRENT_PROCESS_ID,a.CASE_PROCESS_TYPE,a.CASE_PROCESS_TYPE_CODE,a.PARENT_ID,a.CASE_KIND,
a.CASE_KIND_CODE,a.CASE_ALIAS,a.PREPOSITION_ID,a.INTERFILE,a.PREPOSITION_TYPE,a.BELONG_PLATE_ID,a.BELONG_PLATE,a.WHETHER_EASY,a.HAS_AGENT,a.HAS_CASE_PROJECT,
a.HAS_DISPUTE,a.HAS_PRESERVATION,a.HAS_OTHER_DATA,a.HAS_CASE_PROCESS,a.HAS_CHANGE_CLAIM,a.HAS_OPPOSITE_CLAIM,a.HAS_APPRAISAL,a.HAS_SEIZE,a.HAS_JUDGE_CONTENT,
a.HAS_JUDGE_FILES,a.RESULT_ADDRESS,a.RESULT_ADDRESS_ID,a.RESULT,a.RESULT_ID,a.ACTUAL_RECEIVE_MONEY,a.ACTUAL_RECEIVE_OTHER,a.ACTUAL_SEND_MONEY,a.ACTUAL_SEND_OTHER,
a.REMARKS,a.WHETHER_CONTINUE,a.WHETHER_INCLUDE,a.CONTINUE_END_DATE,a.UPDATE_TIME,a.CASE_CODE,a.HAS_CASE,a.WHETHER_BLANK,a.WIN_ANALYSIS,a.EXP_SUMMARY,a.ACCOUNT_DES,
a.HAS_CHILDREN,a.CAUSE_OF_IN_ID,a.CAUSE_OF_IN,a.JUDGE_PERSONS,
b.close_time as easy_end_time,c.case_time as process_end_time,
case a.whether_easy when 1 then b.close_time else c.case_time end end_time,
case a.whether_easy when 1 then b.close_model else c.HANDLE_SITUATION end end_model,
case a.whether_easy when 1 then b.result else c.result end end_result
from case_records a
left join case_easy b on a.id = b.parent_id
left join case_records c on c.parent_id = a.id and c.case_process_type_code = 'end'
where a.parent_id is null and a.case_current_process != '不起诉') AA where  to_char(to_date(#{date},'yyyy-mm'),'yyyy-mm') <![CDATA[ >= ]]> to_char(case_time,'yyyy-mm') and (end_time is null or  to_char(to_date(#{date},'yyyy-mm'),'yyyy-mm') <![CDATA[ < ]]> to_char(end_time,'yyyy-mm')   ) and ${perms} )
		<if test="casetype !=  null  and casetype != '' ">
			and	BELONG_PLATE=#{casetype}
		</if>
		) b
		group by b.cause_name,OUR_POSITION)
		PIVOT (
				max (num) FOR OUR_POSITION IN ('原告' as yuangao, '被告' as beigao,'第三人' as disan,'申请人' as sheqing,'被申请人' as bshenqing)
			)
	</select>
	<select id="xinfacase"  parameterType="java.util.Map" resultType="java.util.HashMap">
		SELECT
		reasonname,
		case when yuangao is null then 0 else yuangao end +case when sheqing is null then 0 else sheqing end yuangao,
		case when beigao is null then 0 else beigao end +case when disan is null then 0 else disan end +case when bshenqing is null then 0 else bshenqing end   beigao ,
		case when yuangao is null then 0 else yuangao end + case when beigao is null then 0 else beigao end + case when disan is null then 0 else disan end+case when bshenqing is null then 0 else bshenqing end+case when sheqing is null then 0 else sheqing end  heji
		from (
		SELECT
		COUNT (1) num,
		b.cause_name reasonname,OUR_POSITION	 from(
		select * from CASE_RECORDS  c
		where   PARENT_ID is null and cause_name is not null and  OUR_POSITION is not null
		<if test="casetype !=  null  and casetype != '' ">
			and	BELONG_PLATE=#{casetype}
		</if>
		and to_char(to_date(#{date},'yyyy-mm'),'yyyy-mm') = to_char(case_time,'yyyy-mm')   and ${perms}
		) b
		group by b.cause_name,OUR_POSITION)
		PIVOT (
		max (num) FOR OUR_POSITION IN ('原告' as yuangao, '被告' as beigao,'第三人' as disan,'申请人' as sheqing,'被申请人' as bshenqing)
		)
	</select>
	<select id="getbigcaseofficer"  parameterType="java.util.Map" resultType="java.util.HashMap">
		select * from Parties where master_id =#{caseid}
	</select>
	<select id="getbenyueyijieData"  parameterType="java.util.Map" resultType="java.util.HashMap">
		select  AA.* ,AA.end_model as jieanfangshi from (select a.ID,a.CASE_NAME,a.CASE_NUMBER,a.CASE_TIME,a.CAUSE_NAME,a.CAUSE_ID,a.CASE_TYPE,a.CASE_TYPE_ID,a.OUR_POSITION,a.VENUE_IS_OUT,a.VENUE_PROVINCE,a.VENUE_CITY,
a.VENUE_REGION,a.VENUE_ADDRESS,a.CASE_MONEY,a.WHETHER_MAJOR,a.COURT,a.COURT_ID,a.TRIAL,a.TRIAL_ID,a.JUDGE_MAIN,a.JUDGE_MAIN_ID,a.JUDGE_MAIN_PHONE,a.JUDGE_VICE,
a.PROOF_DATE,a.REPLY_DATE,a.DATA_STATE,a.DATA_STATE_CODE,a.CASE_PROCESS,a.CASE_PROCESS_IDS,a.CREATE_OGN_ID,a.CREATE_OGN_NAME,a.CREATE_DEPT_ID,a.CREATE_DEPT_NAME,
a.CREATE_GROUP_ID,a.CREATE_GROUP_NAME,a.CREATE_ORG_ID,a.CREATE_ORG_NAME,a.CREATE_PSN_ID,a.CREATE_PSN_NAME,a.CREATE_PSN_FULL_ID,a.CREATE_PSN_FULL_NAME,a.CREATE_TIME,
a.CASE_DETAILS,a.FILES,a.JUDGE_VICE_PHONE,a.CASE_ACCEPT_MONEY,a.CASE_ACCEPT_MONEY_OUR,a.HANDLE_SITUATION,a.HANDLE_SITUATION_ID,a.WHETHER_FORCE_EXECUTE,a.FORCE_EXECUTE,
a.FORCE_EXECUTE_ID,a.RECEIVE_MONEY,a.SEND_MONEY,a.CASE_CURRENT_PROCESS,a.CASE_CURRENT_PROCESS_ID,a.CASE_PROCESS_TYPE,a.CASE_PROCESS_TYPE_CODE,a.PARENT_ID,a.CASE_KIND,
a.CASE_KIND_CODE,a.CASE_ALIAS,a.PREPOSITION_ID,a.INTERFILE,a.PREPOSITION_TYPE,a.BELONG_PLATE_ID,a.BELONG_PLATE,a.WHETHER_EASY,a.HAS_AGENT,a.HAS_CASE_PROJECT,
a.HAS_DISPUTE,a.HAS_PRESERVATION,a.HAS_OTHER_DATA,a.HAS_CASE_PROCESS,a.HAS_CHANGE_CLAIM,a.HAS_OPPOSITE_CLAIM,a.HAS_APPRAISAL,a.HAS_SEIZE,a.HAS_JUDGE_CONTENT,
a.HAS_JUDGE_FILES,a.RESULT_ADDRESS,a.RESULT_ADDRESS_ID,a.RESULT,a.RESULT_ID,a.ACTUAL_RECEIVE_MONEY,a.ACTUAL_RECEIVE_OTHER,a.ACTUAL_SEND_MONEY,a.ACTUAL_SEND_OTHER,
a.REMARKS,a.WHETHER_CONTINUE,a.WHETHER_INCLUDE,a.CONTINUE_END_DATE,a.UPDATE_TIME,a.CASE_CODE,a.HAS_CASE,a.WHETHER_BLANK,a.WIN_ANALYSIS,a.EXP_SUMMARY,a.ACCOUNT_DES,
a.HAS_CHILDREN,a.CAUSE_OF_IN_ID,a.CAUSE_OF_IN,a.JUDGE_PERSONS,
b.close_time as easy_end_time,c.case_time as process_end_time,
case a.whether_easy when 1 then b.close_time else c.case_time end end_time,
case a.whether_easy when 1 then b.close_model else c.HANDLE_SITUATION end end_model,
case a.whether_easy when 1 then b.result else c.result end end_result
from case_records a
left join case_easy b on a.id = b.parent_id
left join case_records c on c.parent_id = a.id and c.case_process_type_code = 'end'
where a.parent_id is null and a.case_current_process != '不起诉') AA where   to_char(to_date(#{date},'yyyy-mm'),'yyyy-mm') <![CDATA[=]]> to_char(end_time,'yyyy-mm')   and ${perms}
	</select>
	<select id="getbigcaseData"  parameterType="java.util.Map" resultType="java.util.HashMap">
		select *  from (select a.ID,a.CASE_NAME,a.CASE_NUMBER,a.CASE_TIME,a.CAUSE_NAME,a.CAUSE_ID,a.CASE_TYPE,a.CASE_TYPE_ID,a.OUR_POSITION,a.VENUE_IS_OUT,a.VENUE_PROVINCE,a.VENUE_CITY,
a.VENUE_REGION,a.VENUE_ADDRESS,a.CASE_MONEY,a.WHETHER_MAJOR,a.COURT,a.COURT_ID,a.TRIAL,a.TRIAL_ID,a.JUDGE_MAIN,a.JUDGE_MAIN_ID,a.JUDGE_MAIN_PHONE,a.JUDGE_VICE,
a.PROOF_DATE,a.REPLY_DATE,a.DATA_STATE,a.DATA_STATE_CODE,a.CASE_PROCESS,a.CASE_PROCESS_IDS,a.CREATE_OGN_ID,a.CREATE_OGN_NAME,a.CREATE_DEPT_ID,a.CREATE_DEPT_NAME,
a.CREATE_GROUP_ID,a.CREATE_GROUP_NAME,a.CREATE_ORG_ID,a.CREATE_ORG_NAME,a.CREATE_PSN_ID,a.CREATE_PSN_NAME,a.CREATE_PSN_FULL_ID,a.CREATE_PSN_FULL_NAME,a.CREATE_TIME,
a.CASE_DETAILS,a.FILES,a.JUDGE_VICE_PHONE,a.CASE_ACCEPT_MONEY,a.CASE_ACCEPT_MONEY_OUR,a.HANDLE_SITUATION,a.HANDLE_SITUATION_ID,a.WHETHER_FORCE_EXECUTE,a.FORCE_EXECUTE,
a.FORCE_EXECUTE_ID,a.RECEIVE_MONEY,a.SEND_MONEY,a.CASE_CURRENT_PROCESS,a.CASE_CURRENT_PROCESS_ID,a.CASE_PROCESS_TYPE,a.CASE_PROCESS_TYPE_CODE,a.PARENT_ID,a.CASE_KIND,
a.CASE_KIND_CODE,a.CASE_ALIAS,a.PREPOSITION_ID,a.INTERFILE,a.PREPOSITION_TYPE,a.BELONG_PLATE_ID,a.BELONG_PLATE,a.WHETHER_EASY,a.HAS_AGENT,a.HAS_CASE_PROJECT,
a.HAS_DISPUTE,a.HAS_PRESERVATION,a.HAS_OTHER_DATA,a.HAS_CASE_PROCESS,a.HAS_CHANGE_CLAIM,a.HAS_OPPOSITE_CLAIM,a.HAS_APPRAISAL,a.HAS_SEIZE,a.HAS_JUDGE_CONTENT,
a.HAS_JUDGE_FILES,a.RESULT_ADDRESS,a.RESULT_ADDRESS_ID,a.RESULT,a.RESULT_ID,a.ACTUAL_RECEIVE_MONEY,a.ACTUAL_RECEIVE_OTHER,a.ACTUAL_SEND_MONEY,a.ACTUAL_SEND_OTHER,
a.REMARKS,a.WHETHER_CONTINUE,a.WHETHER_INCLUDE,a.CONTINUE_END_DATE,a.UPDATE_TIME,a.CASE_CODE,a.HAS_CASE,a.WHETHER_BLANK,a.WIN_ANALYSIS,a.EXP_SUMMARY,a.ACCOUNT_DES,
a.HAS_CHILDREN,a.CAUSE_OF_IN_ID,a.CAUSE_OF_IN,a.JUDGE_PERSONS,
b.close_time as easy_end_time,c.case_time as process_end_time,
case a.whether_easy when 1 then b.close_time else c.case_time end end_time,
case a.whether_easy when 1 then b.close_model else c.HANDLE_SITUATION end end_model,
case a.whether_easy when 1 then b.result else c.result end end_result
from sg_case_records a
left join sg_case_easy b on a.id = b.parent_id
left join sg_case_records c on c.parent_id = a.id and c.case_process_type_code = 'end'
where a.parent_id is null and a.case_current_process != '不起诉') AA where   CASE_MONEY>=******** and to_char(to_date(#{date},'yyyy-mm'),'yyyy-mm') <![CDATA[ >= ]]> to_char(case_time,'yyyy-mm')  and  (end_time is null or  to_char(to_date(#{date},'yyyy-mm'),'yyyy-mm') <![CDATA[ <= ]]> to_char(end_time,'yyyy-mm')   ) and ${perms}
	</select>
	<select id="getCaseExcelData"  parameterType="java.util.List" resultType="com.klaw.entity.caseBean.CaseExcelData">
		select * from caseexcel where id in (
		select id from (
		select c.id,c.casetime,c.createtime,b1.fname reasonname,c.lastsubmit,
		cld.status ,c.money,'民事诉讼' casetype,bdic.fname gxjg,c.caseplace afd ,cld.enddate,c.progress

       	from case c
       	left join CASE_CLOSE cld on c.id = cld.caseid and cld.isdel = 0
       	left join bceg_casedict b1 on b1.fid = c.reason
       	left join BCEG_CASEDICT bdic on bdic.fid = c.institution

		where c.isdel = 0
		${orgsql}

		union all

        select c.id,c.casetime,c.createtime,b1.fname reasonname,c.lastsubmit ,
        cld.status,c.money,'商事仲裁' casetype ,bdic.fname gxjg,c.caseplace afd ,cld.enddate,c.progress

       	from case_arbitration c

		left join BCEG_CASEDICT bdic on bdic.fid = c.institution
		left join CASE_CLOSE cld on c.id = cld.caseid and cld.isdel = 0
        left join bceg_casedict b1 on b1.fid = c.reason

		 where c.isdel = 0
		  ${orgsql}
		  )
         <where>
         <if test="ctimebegin != null and ctimebegin != ''">
             AND casetime BETWEEN #{ctimebegin} AND #{ctimeend}
         </if>
         <if test="reason != null and reason != ''">
             AND reasonname LIKE #{reason}
         </if>
         <if test="status != null and status != ''">
             AND substr(lastsubmit,instr(lastsubmit,',',-1)+1)=#{status}
         </if>
         <if test="lastsubmitstatus != null and lastsubmitstatus != ''">
             <if test="lastsubmitstatus == 2">
                 AND status = '2'
             </if>
             <if test="lastsubmitstatus != 2">
                 AND (status IS NULL OR status &lt;&gt; 2)
             </if>

         </if>
         <if test="moneystart != null and moneystart != ''">
             AND money &gt;= #{moneystart}
         </if>
         <if test="moneyend != null and moneyend != ''">
             AND money  &lt;= #{moneyend}
         </if>
         <if test="casetype != null and casetype != ''">
             AND casetype = #{casetype}
         </if>
         <if test="gxjg != null and gxjg != ''">
             AND gxjg LIKE #{gxjg}
         </if>
         <if test="afd != null and afd != ''">
             AND afd LIKE #{afd}
         </if>
         <if test="enddatebegin != null and enddatebegin != ''">
             AND enddate BETWEEN #{enddatebegin} AND #{enddateend}
         </if>


         <if test="casecode != null and casecode != ''">
             AND casecode LIKE #{casecode}
         </if>
         <if test="createusername != null and createusername != ''">
             AND createusername LIKE #{createusername}
         </if>
          <if test="createorgname != null and createorgname != ''">
             AND createorgname LIKE #{createorgname}
         </if>
         </where>
        ) order  BY createtime
	</select>
</mapper>
