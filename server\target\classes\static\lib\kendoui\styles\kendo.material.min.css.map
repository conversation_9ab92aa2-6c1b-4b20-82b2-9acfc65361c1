{"version": 3, "sources": ["web/kendo.material.css"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAKA,oBACA,uBACE,QAAS,EAEX,gBACE,MAAO,QAET,cACE,MAAO,KAET,oBACE,MAAO,KAET,uBACE,cAAe,EAEjB,2BACE,MAAO,KAET,yBACE,iBAAkB,KAEpB,2BACE,MAAO,KAET,0BACE,MAAO,QAET,wBACE,iBAAkB,KAEpB,0BACE,MAAO,KAET,6BACE,MAAO,QAET,2BACE,iBAAkB,KAEpB,6BACE,MAAO,QAET,eACE,MAAO,QAET,iBACE,MAAO,QAET,iBACE,MAAO,QAET,cACE,MAAO,QAET,kBACE,MAAO,QAET,kBACE,MAAO,QAET,kBACE,MAAO,QAET,kBACE,MAAO,QAET,kBACE,MAAO,QAET,kBACE,MAAO,QAET,2BACE,iBAAkB,KAClB,OAAQ,IAAI,MAAM,QAEpB,MACA,QACA,iBACE,aAAc,YAGhB,SACA,UACE,iBAAkB,KAcpB,gBAZA,SA8CA,wBAPA,eA7BA,4BALA,WAQA,iBA6BA,mBAlCA,iBADA,iBAUA,sBASA,WACA,4BAFA,uBATA,eAQA,sBAIA,oBAPA,eAEA,sBADA,oBAlBA,SAWA,mBAiBA,mBACA,sCA3BA,UAJA,SA6BA,iBAFA,cACA,sBAKA,yBAEA,uBADA,qBAFA,4BAzBA,aA+BA,gBACA,YAvBA,iBACA,2BACA,kBAjBA,WAQA,iBAgCA,SA7BA,WA+BA,WAPA,gBASA,gBA9CA,UA+CE,aAAc,QAUhB,eACA,oBAHA,sBADA,eALA,SAIA,mBAFA,mBACA,cAFA,WAMA,oBAGA,kBACE,iBAAkB,QAEpB,mBAEA,uBADA,gBAEE,iBAAkB,QAEpB,kBACE,aAAc,eACd,iBAAkB,KAEpB,WACA,iBAEA,mBADA,sBAEA,SACE,iBAAkB,KAEpB,OAGA,oDADA,kBADA,aAGE,iBAAkB,QAGpB,gBADA,kCAEE,iBAAkB,QAGpB,yBACA,gCAEA,+BADA,8BAHA,WAKE,aAAc,QACd,iBAAkB,KAGpB,yBAEA,yCADA,0BAEA,0CAEA,yCADA,wCALA,iBAOE,aAAc,QAMhB,iBAJA,gBAEA,sBADA,mBAEA,yBAEE,WAAY,IAEd,SAMA,oBADA,iBAJA,gBAEA,sBADA,mBAEA,yBAGE,iBAAkB,KAClB,MAAO,KAET,mBACE,iBAAkB,KAClB,MAAO,KAET,SAGA,WAEA,qBAHA,SAEA,WAHA,UAKE,MAAO,KAET,WACE,MAAO,KAET,SACE,MAAO,KAET,aACA,gBACA,qCACE,MAAO,QAGT,uBADA,0BAEE,MAAO,KAIT,iCAFA,UACA,iBAEE,MAAO,KAcT,gBAHA,UAEA,cARA,iBAFA,eAKA,mBANA,UAKA,gBAEA,cAQA,sCAXA,eAMA,eAGA,mBACA,0BANA,WANA,WAcA,+CACE,iBAAkB,KAClB,oBAAqB,IAAI,IACzB,iBAAkB,QAEpB,oBACE,iBAAkB,KAEpB,SAMA,UACA,cALA,eAEA,mBAHA,UAIA,cAFA,WAKA,gBACA,gCACE,iBAAkB,QAQpB,yCADA,wCAJA,cAMA,qDACA,wFAJA,yBAFA,uBACA,0BAME,QAAS,EAIX,yBAFA,QAGA,+CACA,0EAHA,0BAIE,QAAS,GAEX,gCACA,qDACA,kDACE,QAAS,GAEX,QACE,aAAc,YAMhB,yBADA,aAMA,6CAHA,4CADA,6CAHA,qBAFA,QAOA,+CACA,0EAPA,aASE,iBAAkB,yBAClB,aAAc,YAGhB,gCACA,qDACA,kDACE,iBAAkB,yBAClB,aAAc,YAEhB,WACA,0BACE,iBAAkB,0BAClB,oBAAqB,IAAI,IAE3B,iBACE,iBAAkB,gCAEpB,iBACE,iBAAkB,KAEpB,UACE,MAAO,KACP,aAAc,QACd,iBAAkB,QAEpB,cACE,aAAc,QACd,iBAAkB,QAClB,mBAAoB,KACZ,WAAY,KAEtB,oBACE,aAAc,QACd,iBAAkB,QAClB,mBAAoB,EAAE,EAAE,EAAE,IAAI,mBACtB,WAAY,EAAE,EAAE,EAAE,IAAI,mBAGhC,aACE,MAAO,KACP,iBAAkB,KAEpB,oBACE,MAAO,KAET,wBACA,yBACE,iBAAkB,KAClB,MAAO,KAKT,uBACA,yBAFA,sBAGA,mBAJA,sBADA,sBAME,aAAc,QAEhB,gBACE,iBAAkB,QAEpB,yBACE,iBAAkB,gBAEpB,kCACE,iBAAkB,eAEpB,4BACA,iCACA,kCACE,iBAAkB,QAEpB,uBACE,kBAAmB,QAErB,sBACE,iBAAkB,QAEpB,SACA,iBACE,aAAc,QACd,WAAY,QAAQ,EAAE,OAAO,KAAK,SAClC,MAAO,KAET,iBACE,MAAO,KAET,0BACE,oBAAqB,EAAE,EACvB,mBAAoB,EAAE,EAAE,EAAE,IAAI,KACtB,WAAY,EAAE,EAAE,EAAE,IAAI,KAEhC,gCACA,sCACE,iBAAkB,KAGpB,2BADA,4BAEE,aAAc,KAEhB,uBAEA,oBADA,qBAEE,iBAAkB,KAClB,MAAO,KACP,aAAc,KAEhB,uBACE,MAAO,KAET,4BACE,aAAc,KAEhB,mBACE,iBAAkB,KAKpB,iBAFA,gBACA,sBAEA,4BACE,iBAAkB,KAClB,aAAc,KACd,MAAO,KAET,mCACE,iBAAkB,KAGpB,0BADA,gBAEE,aAAc,QAGhB,wBADA,gBAEE,MAAO,KACP,aAAc,QACd,iBAAkB,QAGpB,yBADA,iBAEE,MAAO,QACP,iBAAkB,QAClB,aAAc,QAGhB,+BADA,uBAEE,MAAO,KACP,aAAc,QACd,iBAAkB,QAEpB,uDACE,mBAAoB,EAAE,IAAI,KAAK,EAAE,QACzB,WAAY,EAAE,IAAI,KAAK,EAAE,QAEnC,8DACE,mBAAoB,EAAE,IAAI,KAAK,EAAE,qBACzB,WAAY,EAAE,IAAI,KAAK,EAAE,qBAEnC,uCACE,iBAAkB,YAEpB,mBACE,WAAY,KACZ,MAAO,KAGT,iCADA,iBAEE,aAAc,QAEhB,oBACE,MAAO,KAET,sBACE,QAAS,EAEX,mCACE,MAAO,KACP,gBAAiB,KACjB,iBAAkB,KAGpB,iDADA,yCAEE,iBAAkB,KAClB,gBAAiB,UAEnB,0CACE,iBAAkB,KAEpB,+BACE,MAAO,KAET,sCACE,gBAAiB,KACjB,iBAAkB,QAClB,MAAO,KAET,kCACE,cAAe,IAEjB,8BACE,cAAe,EAEjB,eACE,iBAAkB,QAEpB,2BACE,cAAe,IAEjB,8BACE,aAAc,eAUhB,qCADA,6BADA,2BAFA,2BADA,0BAQA,iBANA,2BAIA,oDACA,uCAVA,kBACA,uBACA,0BAUE,MAAO,QACP,iBAAkB,KAClB,aAAc,KAGhB,wCACA,yCAFA,wBAGE,iBAAkB,QAEpB,mDACE,iBAAkB,QAEpB,yBACA,yCACE,WAAY,QACZ,MAAO,QAET,kCACE,WAAY,QACZ,MAAO,QACP,0BAA2B,KAE7B,gBACE,MAAO,QAOT,0BAJA,yBACA,6BACA,8CAHA,iBAIA,mBAEE,mBAAoB,MAAM,EAAE,EAAE,EAAE,IAAI,KAC5B,WAAY,MAAM,EAAE,EAAE,EAAE,IAAI,KAGtC,0CADA,kCAEA,oCACE,mBAAoB,KACZ,WAAY,KAStB,iCADA,uBAHA,yCADA,oCADA,kCADA,wCAKA,6BADA,0BAKA,qDADA,0CAEE,mBAAoB,KACZ,WAAY,KAItB,wDADA,iCADA,0BAGE,MAAO,QAST,6BACA,wBAJA,uBAOA,4CADA,uCADA,sCAGA,6CANA,4BADA,sDAHA,mCACA,iCAHA,eACA,qBAYE,MAAO,KACP,iBAAkB,QAClB,aAAc,QAGhB,wEACE,MAAO,KACP,iBAAkB,QAClB,aAAc,QAEhB,yCACE,aAAc,QAGhB,2BADA,yBAEE,aAAc,QAKhB,0BAFA,wBACA,gBAFA,gBAUA,oBAFA,qCADA,4BAFA,eACA,qBAFA,iBAKA,8BAEE,iBAAkB,KAEpB,cACE,iBAAkB,QAClB,MAAO,KAET,+BAOA,iBAJA,gCADA,+BAMA,qCAPA,8BAGA,gBACA,sBACA,wBAGE,iBAAkB,KAIpB,yBADA,iBAEA,qCAHA,kBAIE,iBAAkB,KAGpB,yBADA,iBAEA,qCACE,oBAAqB,IAAI,IAE3B,aACE,iBAAkB,yBAGpB,qCADA,uBAEA,8BACE,MAAO,KAGT,gCADA,8BAOA,iCADA,+BADA,gCADA,8BADA,+BADA,6BAME,iBAAkB,KAClB,iBAAkB,KAClB,oBAAqB,IAAI,IACzB,aAAc,QAKhB,sCAHA,6EAEA,yCADA,gEAGE,MAAO,KAET,sEACE,WAAY,KACZ,aAAc,QAEhB,4EACE,iBAAkB,KAClB,aAAc,QAEhB,+FACE,MAAO,KAET,kFACE,mBAAoB,EAAE,IAAI,IAAI,eAAoB,EAAE,IAAI,IAAI,gBACpD,WAAY,EAAE,IAAI,IAAI,eAAoB,EAAE,IAAI,IAAI,gBAE9D,oCACE,MAAO,KAET,eACE,aAAc,QACd,iBAAkB,QAClB,MAAO,QAET,kBACE,QAAS,GAEX,yBACE,OAAQ,kBAGV,iCADA,+BAEE,aAAc,EACd,iBAAkB,KAClB,iBAAkB,YAQpB,6BADA,eAFA,eACA,uBAIA,wBARA,kBAEA,4BADA,0BAMA,qBAEE,MAAO,KAGT,6BACE,WAAY,gCAEd,qDACA,+CACE,QAAS,KAGX,gBACE,iBAAkB,QAEpB,oBACE,iBAAkB,KAEpB,6BACE,iBAAkB,2BAEpB,2BACE,iBAAkB,2BAGpB,oBACE,aAAc,qBACd,iBAAkB,qBAClB,MAAO,KAET,+BACE,aAAc,QACd,iBAAkB,QAClB,MAAO,QAIT,oCADA,qCAEE,UAAW,KACX,SAAU,SACV,IAAK,IAEP,aACE,oBAAqB,qBAEvB,aACE,mBAAoB,qBAEtB,aACE,iBAAkB,qBAEpB,aACE,kBAAmB,qBAErB,mCACE,oBAAqB,QAEvB,mCACE,mBAAoB,QAEtB,mCACE,iBAAkB,QAEpB,mCACE,kBAAmB,QAGrB,YACE,iBAAkB,QAGpB,8BADA,4BAEE,iBAAkB,QAGpB,QACE,iBAAkB,KAClB,aAAc,QAEhB,iBACE,MAAO,QAET,6BACE,iBAAkB,QAEpB,gBACE,MAAO,QAET,4BACE,iBAAkB,QAEpB,cACE,MAAO,QAET,0BACE,iBAAkB,QAGpB,QACE,aAAc,KAEhB,iBACA,0BACE,aAAc,QAEhB,6BACE,aAAc,KAGhB,+BADA,iCAEE,iBAAkB,+BAClB,wBAAyB,KAAK,KACtB,gBAAiB,KAAK,KAGhC,QACA,4BACE,MAAO,KAET,kBACA,sCACE,MAAO,KAIT,UADA,UAEE,mBAAoB,KACZ,WAAY,KAEtB,UACA,YACA,UACE,mBAAoB,KACZ,WAAY,KAEtB,eACE,mBAAoB,EAAE,IAAI,EAAE,qBACpB,WAAY,EAAE,IAAI,EAAE,qBAG9B,gCACA,iCAEA,gCADA,+BAHA,iBAKE,mBAAoB,EAAE,IAAI,IAAI,eAAoB,EAAE,IAAI,IAAI,gBACpD,WAAY,EAAE,IAAI,IAAI,eAAoB,EAAE,IAAI,IAAI,gBAE9D,kBACE,mBAAoB,EAAE,IAAI,IAAI,iBAAqB,MAC3C,WAAY,EAAE,IAAI,IAAI,iBAAqB,MAErD,gBACE,mBAAoB,KACZ,WAAY,KAEtB,4CACE,iBAAkB,KAOpB,oCACA,kCAFA,uBAGA,gCAGA,wBARA,0BADA,sBAQA,+BADA,8BARA,SAGA,cAQA,WACE,mBAAoB,EAAE,IAAI,IAAI,EAAE,eACxB,WAAY,EAAE,IAAI,IAAI,EAAE,eAElC,8BACE,mBAAoB,EAAE,EAAI,IAAI,IAAI,eAC1B,WAAY,EAAE,EAAI,IAAI,IAAI,eAEpC,8BACE,mBAAoB,MAAM,EAAE,EAAE,EAAE,IAAI,QAC5B,WAAY,MAAM,EAAE,EAAE,EAAE,IAAI,QAGtC,UACE,aAAc,eACd,mBAAoB,IAAI,IAAI,IAAI,IAAI,qBAC5B,WAAY,IAAI,IAAI,IAAI,IAAI,qBACpC,iBAAkB,KAEpB,0BACE,aAAc,eACd,mBAAoB,IAAI,IAAI,IAAI,IAAI,eAC5B,WAAY,IAAI,IAAI,IAAI,IAAI,eAItC,sCADA,uCADA,6BAGE,cAAe,EAEjB,UACE,mBAAoB,EAAE,IAAI,IAAI,EAAE,eACxB,WAAY,EAAE,IAAI,IAAI,EAAE,eAElC,SACE,mBAAoB,MAAM,EAAE,IAAI,IAAI,eAC5B,WAAY,MAAM,EAAE,IAAI,IAAI,eAGtC,kCACE,iBAAkB,QAClB,YAAa,KACb,MAAO,KAET,6BACE,iBAAkB,QAClB,YAAa,KACb,MAAO,KAET,kCACE,iBAAkB,QAClB,YAAa,KACb,MAAO,KAGT,6CACE,iBAAkB,QAClB,MAAO,QACP,aAAc,QAEhB,gDACE,iBAAkB,QAClB,MAAO,QACP,aAAc,QAEhB,gDACE,iBAAkB,QAClB,MAAO,QACP,aAAc,QAEhB,8CACE,iBAAkB,QAClB,MAAO,QACP,aAAc,QAGhB,qBACE,WAAY,QAEd,4BACE,iBAAkB,QAEpB,8BACE,iBAAkB,QAIpB,6CACA,gDAHA,uCACA,0CAGE,iBAAkB,KAEpB,mDACA,sDACE,iBAAkB,QAEpB,kBACE,iBAAkB,KAClB,aAAc,KAEhB,wBACE,iBAAkB,KAEpB,gBACE,aAAc,QACd,WAAY,QAEd,kBACA,yBACE,aAAc,KACd,WAAY,KAEd,iCACE,aAAc,QACd,WAAY,QAGd,2CADA,mCAEE,aAAc,KACd,WAAY,KAEd,eACE,iBAAkB,QAClB,aAAc,QACd,MAAO,KAET,gCACE,aAAc,KAEhB,QACE,iBAAkB,KAClB,MAAO,KAET,yBACE,iBAAkB,KAClB,MAAO,KAET,YACE,iBAAkB,KAGpB,kBACA,gBACA,eACA,cACA,kBACA,cACE,iBAAkB,6BAcpB,gBAXA,SACA,UA2BA,oBADA,eADA,sBARA,eALA,YAGA,cAGA,kBAhBA,aAWA,YACA,iBAeA,iBArBA,0BACA,sCAFA,gBAiBA,kBAbA,eAQA,gBAGA,gBAFA,kBACA,eAYA,oBADA,gBA/BA,WA8BA,QAfA,cAcA,WA3BA,mBAyBA,kBAMA,UA9BA,UAEA,iBADA,sCA8BE,cAAe,EAEjB,QACE,WAAY,OACZ,eAAgB,OAElB,qCACE,cAAe,EAEjB,6BAEA,iDADA,4CAEE,cAAe,EAEjB,iDACE,cAAe,EAEjB,2BACA,+CACA,wDACE,cAAe,EAIjB,kCAFA,wCAIA,mCAIA,eAPA,oCAEA,iCAGA,kCADA,iCAEA,kBAEE,cAAe,EAEjB,2CACA,4CAGA,2CAFA,0CACA,mDAEE,cAAe,EAEjB,qDACE,cAAe,EASjB,oCANA,mBAIA,0CAIA,qCAHA,sCAEA,mCAGA,oCARA,sCAOA,mCARA,0BAEA,0BAJA,mBAYE,cAAe,EAEjB,8CACE,cAAe,EAEjB,4CACE,cAAe,EAEjB,0DACE,cAAe,EAEjB,wDACE,cAAe,EAEjB,0BAEA,yBADA,wBAEE,cAAe,KAAK,EAAE,EAAE,KAE1B,iCAEA,gCADA,+BAEE,cAAe,EAAE,KAAK,KAAK,EAE7B,wBACE,cAAe,EAAE,KAAK,EAAE,EAE1B,gCACE,cAAe,EAAE,EAAE,KAAK,EAE1B,iCACE,cAAe,KAAK,EAAE,EAAE,KAE1B,wCACE,cAAe,EAAE,KAAK,KAAK,EAE7B,6CACE,cAAe,EAEjB,8CAGA,6CAFA,4CACA,qDAEE,cAAe,EAEjB,yCACE,iBAAkB,QAEpB,uDACE,cAAe,KAAK,KAAK,EAAE,EAK7B,sCAHA,2BAIA,uCAFA,0BADA,yBAIE,cAAe,EAKjB,6CAHA,kCAIA,8CAFA,iCADA,gCAIE,cAAe,EAEjB,0CACE,cAAe,EAGjB,yBACA,oBAFA,iBAGE,cAAe,EAQjB,YAFA,iCAHA,yBACA,2BAFA,uBAGA,0BAEA,oBAEE,cAAe,EAGjB,4BADA,oBAEE,cAAe,KAEjB,cACE,cAAe,KAEjB,uCACA,+CACA,4DACA,oEACE,cAAe,EAEjB,8CACA,sDACA,mEACA,2EACA,iEACA,yEACE,cAAe,EAIjB,sCAFA,0DACA,kEAEE,cAAe,EAEjB,iCAEA,yCADA,yCAEA,iDACE,wBAAyB,EACzB,2BAA4B,EAE9B,wCAEA,gDADA,gDAEA,wDACE,cAAe,EAGjB,4CADA,0CAEE,cAAe,EAGjB,SAGA,iBAJA,eAGA,iBADA,eAGE,cAAe,KAEjB,6BACE,cAAe,KAGjB,4CACE,kBAAmB,KAKrB,kCADA,gCAEA,iCACA,kEAJA,iCAOA,8CADA,8CADA,wCANA,iCASE,MAAO,KAET,0EACE,sBACE,MAAO,MAIX,8CADA,wCAEA,qEACE,iBAAkB,KAClB,WAAY,IAAI,MAAM,QAExB,uDACE,cAAe,EAEjB,yDACE,iBAAkB,KAClB,iBAAkB,KAEpB,iFACE,iBAAkB,YAEpB,mDACE,mBAAoB,MAAM,EAAE,KAAK,EAAE,QAC3B,WAAY,MAAM,EAAE,KAAK,EAAE,QAIrC,4EADA,yEADA,8CAGE,MAAO,QAET,8CACE,cAAe,IAAI,MAAM,QAE3B,8CACE,mBAAoB,EAAE,IAAI,IAAI,QACtB,WAAY,EAAE,IAAI,IAAI,QAEhC,+BACA,oCAEA,sDADA,qCAEE,MAAO,KACP,aAAc,QACd,iBAAkB,IAClB,iBAAkB,mGAClB,iBAAkB,wEAClB,iBAAkB,sEAEpB,sCACA,2CAEA,6DADA,4CAEE,iBAAkB,KAGpB,gCAGA,iCADA,gCADA,+BAGE,iBAAkB,KAClB,oBAAqB,IAAI,IACzB,iBAAkB,QAClB,aAAc,QAEhB,8BAGA,+BADA,8BADA,6BAGE,iBAAkB,KAClB,iBAAkB,KAClB,oBAAqB,IAAI,IACzB,aAAc,QAEhB,wBACE,aAAc,QAEhB,sCACE,aAAc,QAEhB,gCAGA,iCACA,wCAFA,gCADA,+BAIE,iBAAkB,KAClB,iBAAkB,KAClB,oBAAqB,IAAI,IACzB,aAAc,QACd,mBAAoB,EAAE,IAAI,IAAI,eAAoB,EAAE,IAAI,IAAI,gBACpD,WAAY,EAAE,IAAI,IAAI,eAAoB,EAAE,IAAI,IAAI,gBAE9D,kBACE,MAAO,KAET,qBACA,sCACA,iBACE,MAAO,KAET,2BACE,aAAc,QAEhB,2BACE,aAAc,QAEhB,yBACE,aAAc,QAEhB,kBACE,mBAAoB,EAAE,IAAI,IAAI,eAAoB,EAAE,IAAI,IAAI,gBACpD,WAAY,EAAE,IAAI,IAAI,eAAoB,EAAE,IAAI,IAAI,gBAG9D,uCADA,2CAEE,MAAO,KAIT,qDADA,qCADA,yCAGE,MAAO,KAET,8CACE,WAAY,QACZ,mBAAoB,KACZ,WAAY,KAGtB,+CADA,mCAEE,aAAc,YAEhB,iCACE,aAAc,QAEhB,sCACE,iBAAkB,KAClB,MAAO,KAGT,gBADA,iBAEE,aAAc,QAEhB,eACA,uBACA,wCACE,aAAc,KAEhB,wCACE,mBAAoB,MAAM,EAAE,IAAI,EAAE,KAAS,EAAE,IAAI,EAAE,KAC3C,WAAY,MAAM,EAAE,IAAI,EAAE,KAAS,EAAE,IAAI,EAAE,KAGrD,0DADA,0CAEE,mBAAoB,EAAE,IAAI,EAAE,KACpB,WAAY,EAAE,IAAI,EAAE,KAE9B,yCACE,mBAAoB,MAAM,EAAE,IAAI,EAAE,KAC1B,WAAY,MAAM,EAAE,IAAI,EAAE,KAEpC,4BACE,aAAc,KACd,iBAAkB,YAEpB,iBACE,aAAc,eAEhB,8BACE,iBAAkB,KAIpB,kBADA,mBADA,mBAGE,MAAO,KACP,aAAc,KACd,YAAa,IAEf,mBACE,MAAO,KAUT,kCANA,2BACA,eAFA,oBAMA,sCAPA,UAIA,cAEA,sBADA,yBAIE,aAAc,KAGhB,iCADA,WAEE,MAAO,KACP,aAAc,QACd,iBAAkB,KAClB,oBAAqB,IAAI,IACzB,iBAAkB,QAClB,mBAAoB,KACZ,WAAY,KAGtB,2BADA,iBAEE,MAAO,KACP,aAAc,QACd,iBAAkB,KAClB,mBAAoB,EAAE,EAAE,IAAI,EAAE,QACtB,WAAY,EAAE,EAAE,IAAI,EAAE,QAEhC,iBACE,MAAO,KACP,aAAc,QACd,iBAAkB,KAClB,iBAAkB,QAClB,mBAAoB,KACZ,WAAY,KAEtB,+DACA,wDACE,mBAAoB,EAAE,EAAE,IAAI,EAAE,QACtB,WAAY,EAAE,EAAE,IAAI,EAAE,QAEhC,kBACE,MAAO,KACP,aAAc,QACd,iBAAkB,KAClB,iBAAkB,QAClB,mBAAoB,EAAE,IAAI,KAAK,EAAE,eACzB,WAAY,EAAE,IAAI,KAAK,EAAE,eAEnC,4BAMA,mCAJA,kCADA,6BAIA,oCAFA,mCAIE,MAAO,QACP,aAAc,QACd,iBAAkB,QAClB,iBAAkB,KAClB,mBAAoB,KACZ,WAAY,KAEtB,yBACA,kBACE,aAAc,YAIhB,kCADA,2BADA,oBAGE,iBAAkB,YAClB,cAAe,IAEjB,0CACE,iBAAkB,YAEpB,gBACA,sBACE,QAAS,EAEX,sBACE,iBAAkB,QAEpB,gCACE,WAAY,IACZ,aAAc,QAEhB,qDACE,kBAAmB,QAErB,wBACE,QAAS,EACT,aAAc,QACd,mBAAoB,EAAE,IAAI,KAAK,EAAE,QACzB,WAAY,EAAE,IAAI,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;;AAGnC,yBACE,aAAc,QACd,WAAY,KACZ,cAAe,IAEjB,+BACA,mDACE,aAAc,QACd,mBAAoB,KACZ,WAAY,KAEtB,6CACE,iBAAkB,QAClB,aAAc,QACd,MAAO,KAET,gCACE,mBAAoB,KACZ,WAAY,KACpB,aAAc,QAEhB,oDACE,mBAAoB,KACZ,WAAY,KACpB,aAAc,QAEhB,uCACE,MAAO,KAET,oDACE,mBAAoB,KACZ,WAAY,KAItB,6DADA,sDAEA,4DAHA,8CAIE,MAAO,KACP,WAAY,QACZ,aAAc,QACd,cAAe,IAEjB,2CACE,aAAc,QACd,mBAAoB,KACZ,WAAY,KAEtB,kDACE,iBAAkB,QAClB,iBAAkB,KAClB,aAAc,QACd,cAAe,EAEjB,wDACE,aAAc,QACd,iBAAkB,QAEpB,oCACE,QAAS,GACT,SAAU,SACV,IAAK,IACL,KAAM,IACN,cAAe,IACf,MAAO,IACP,OAAQ,IAEV,0CACE,mBAAoB,EAAE,EAAE,EAAE,KAAK,qBACvB,WAAY,EAAE,EAAE,EAAE,KAAK,qBAEjC,2CACE,mBAAoB,EAAE,EAAE,EAAE,KAAK,qBACvB,WAAY,EAAE,EAAE,EAAE,KAAK,qBAEjC,mDACE,mBAAoB,EAAE,EAAE,EAAE,KAAK,mBACvB,WAAY,EAAE,EAAE,EAAE,KAAK,mBAEjC,oDACE,mBAAoB,KACZ,WAAY,KAEtB,mDACE,aAAc;;;;;;;;;;;;;;;;;;;;;;;AAGhB,sBACE,aAAc,QACd,cAAe,IACf,iBAAkB,KAClB,aAAc,IAEhB,4BACA,6CACE,aAAc,QACd,mBAAoB,KACZ,WAAY,KAEtB,sCACE,iBAAkB,QAClB,cAAe,IAEjB,6BACE,aAAc,QACd,mBAAoB,EAAE,EAAE,IAAI,EAAE,QACtB,WAAY,EAAE,EAAE,IAAI,EAAE,QAEhC,8CACE,mBAAoB,EAAE,EAAE,IAAI,EAAE,QACtB,WAAY,EAAE,EAAE,IAAI,EAAE,QAC9B,aAAc,QAEhB,iCACE,MAAO,QAGT,+CADA,wCAEA,6CACA,8CACE,WAAY,KACZ,aAAc,QACd,mBAAoB,KACZ,WAAY,KAEtB,+CACE,iBAAkB,QAClB,QAAS,GAEX,qCACE,aAAc,QACd,mBAAoB,EAAE,EAAE,IAAI,EAAE,QACtB,WAAY,EAAE,EAAE,IAAI,EAAE,QAEhC,uCACA,6CACE,aAAc,QAEhB,sCACE,aAAc,QACd,mBAAoB,EAAE,EAAE,EAAE,KAAK,qBACvB,WAAY,EAAE,EAAE,EAAE,KAAK,qBAEjC,8CACE,mBAAoB,EAAE,EAAE,EAAE,KAAK,mBACvB,WAAY,EAAE,EAAE,EAAE,KAAK,mBAEjC,qCACE,aAAc,QACd,mBAAoB,EAAE,EAAE,EAAE,KAAK,qBACvB,WAAY,EAAE,EAAE,EAAE,KAAK,qBAEjC,gDACA,sDACE,aAAc,QAEhB,uDACE,mBAAoB,KACZ,WAAY,KAEtB,yGAIE,WAOA,yBARA,aADA,qBADA,wBAWA,gCACA,qDACA,kDAPA,6BACA,2CAFA,4BAGA,+BACA,6CALA,aAUE,iBAAkB,4BAClB,wBAAyB,MAAM,MACvB,gBAAiB,MAAM,MAEjC,0BAEA,yBADA,wBAEE,cAAe,KAAK,EAAE,EAAE,KAE1B,kBACA,gBACA,eACA,cACA,kBACA,cACE,iBAAkB,gCAClB,wBAAyB,KAAK,KACtB,gBAAiB,KAAK,MAGlC,6CAEE,qDADA,wDAEE,aAAc,MAGlB,wBAKA,6BAoBA,yBADA,yBALA,2CAFA,yCACA,2BAFA,yBAIA,wBATA,+DADA,mCAGA,sDAEA,sDAHA,iCAEA,iCAYA,wBACA,6CAJA,6BADA,4BADA,6BAfA,gDACA,8CAHA,8BAFA,4BACA,+BAMA,yBARA,mBA2BA,2BApBA,uCAqBE,QAAS,EAIX,uCADA,oBADA,aAGE,QAAS,GAEX,+CACE,QAAS,IAEX,UAGA,0BADA,wBAEA,gBACA,uDAJA,gBAKE,mBAAoB,EAAE,IAAI,IAAI,eAAoB,EAAE,IAAI,IAAI,gBACpD,WAAY,EAAE,IAAI,IAAI,eAAoB,EAAE,IAAI,IAAI,gBAE9D,UACA,UACE,YAAa,IAGf,yBAEA,0BAHA,iBAEA,gBAEE,MAAO,KACP,iBAAkB,QAClB,aAAc,QAGhB,+BADA,uBAEE,MAAO,KACP,aAAc,QACd,iBAAkB,QAClB,mBAAoB,EAAE,IAAI,KAAK,EAAE,qBACzB,WAAY,EAAE,IAAI,KAAK,EAAE,qBAKnC,+BAFA,wBACA,uBAFA,gBAIE,MAAO,KACP,aAAc,QACd,iBAAkB,QAGpB,0BAEA,2BAHA,kBAEA,iBAEE,MAAO,KACP,aAAc,QACd,iBAAkB,KAClB,iBAAkB,QAClB,mBAAoB,EAAE,IAAI,KAAK,EAAE,eACzB,WAAY,EAAE,IAAI,KAAK,EAAE,eAKnC,gCAFA,yBACA,wBAFA,iBAIE,MAAO,KACP,aAAc,QACd,iBAAkB,QAGpB,+DADA,wDAEE,mBAAoB,EAAE,IAAI,KAAK,EAAE,eACzB,WAAY,EAAE,IAAI,KAAK,EAAE,eAEnC,4BAMA,mCAJA,kCADA,6BAIA,oCAFA,mCAIE,MAAO,QACP,aAAc,QACd,iBAAkB,QAClB,iBAAkB,KAClB,mBAAoB,KACZ,WAAY,KAGtB,mCADA,2BAEE,MAAO,KACP,iBAAkB,QAClB,aAAc,QAEhB,6CACA,kDACE,iBAAkB,KAGpB,yCADA,iCAEE,MAAO,KACP,aAAc,QACd,iBAAkB,QAGpB,2BAKA,kCACA,wCAHA,iCAJA,oBAEA,4BAGA,mCAFA,kCAKE,MAAO,KACP,aAAc,QACd,iBAAkB,QAClB,iBAAkB,KAClB,mBAAoB,KACZ,WAAY,KAEtB,6BACE,aAAc,QACd,iBAAkB,KAClB,oBAAqB,IAAI,IACzB,iBAAkB,QAEpB,YACA,mBACE,iBAAkB,QAEpB,WACE,iBAAkB,QAClB,aAAc,QAEhB,YACA,cAEA,kBADA,cAEE,iBAAkB,QAEpB,yCACE,aAAc,QAEhB,uBACA,uBACE,iBAAkB,QAEpB,wCAEA,wCADA,uCAEE,iBAAkB,KAGpB,yCADA,uCAEE,aAAc,KAGhB,wCADA,sCAEE,iBAAkB,KAIpB,8BAGA,+BADA,8BAHA,yBAEA,6BAHA,iBAME,iBAAkB,KAClB,aAAc,QACd,mBAAoB,EAAE,IAAI,IAAI,eAAoB,EAAE,IAAI,IAAI,gBACpD,WAAY,EAAE,IAAI,IAAI,eAAoB,EAAE,IAAI,IAAI,gBAG9D,gCAGA,iCACA,wCAFA,gCADA,+BAFA,iBAME,iBAAkB,QAClB,iBAAkB,KAClB,oBAAqB,IAAI,IACzB,aAAc,QACd,mBAAoB,EAAE,IAAI,IAAI,eAAoB,EAAE,IAAI,IAAI,gBACpD,WAAY,EAAE,IAAI,IAAI,eAAoB,EAAE,IAAI,IAAI,gBAE9D,0CACE,iBAAkB,QAGpB,+BAGA,gCACA,uCAFA,+BADA,8BAFA,iBAME,iBAAkB,KAClB,iBAAkB,KAClB,oBAAqB,IAAI,IACzB,aAAc,QACd,mBAAoB,EAAE,IAAI,IAAI,eAAoB,EAAE,IAAI,IAAI,gBACpD,WAAY,EAAE,IAAI,IAAI,eAAoB,EAAE,IAAI,IAAI,gBAE9D,iCAKA,kCACA,yCAJA,iCACA,0CACA,2CAHA,gCAME,iBAAkB,QAEpB,2CACE,aAAc,QAEhB,4BACE,iBAAkB,KAClB,aAAc,KAEhB,0CACE,mBAAoB,KACZ,WAAY,KACpB,MAAO,QAET,0BACE,mBAAoB,KACZ,WAAY,KAEtB,yBACE,aAAc,YACd,mBAAoB,KACZ,WAAY,KAEtB,uBACA,wCACE,iBAAkB,QAClB,aAAc,QAEhB,kBACE,aAAc,QAEhB,4CACE,mBAAoB,MAAM,EAAE,EAAE,EAAE,IAAI,KAC5B,WAAY,MAAM,EAAE,EAAE,EAAE,IAAI,KAEtC,+BACA,gDACE,mBAAoB,MAAM,EAAE,EAAE,EAAE,IAAI,KAC5B,WAAY,MAAM,EAAE,EAAE,EAAE,IAAI,KAEtC,gCACE,iBAAkB,QAClB,mBAAoB,KACZ,WAAY,KAEtB,8CACE,iBAAkB,QAEpB,sCACE,MAAO,KAGT,8BACE,MAAO,KAET,sBACE,aAAc,QAEhB,eACE,cAAe,IAEjB,0BACE,iBAAkB,KAEpB,qCACE,iBAAkB,QAEpB,mCACE,MAAO,QAET,sCACE,cAAe,EAEjB,qBACE,iBAAkB,QAEpB,6BACE,MAAO,KAET,mCACE,iBAAkB,QAEpB,4BACE,mBAAoB,MAAM,EAAE,EAAE,EAAE,IAAI,QAC5B,WAAY,MAAM,EAAE,EAAE,EAAE,IAAI,QAEtC,kCACA,2BACE,mBAAoB,KACZ,WAAY,KAEtB,kCACE,iBAAkB,QAClB,aAAc,QAGhB,sCACE,cAAe,EAGjB,8CADA,kCAEE,iBAAkB,QAClB,iBAAkB,KAClB,aAAc,QAEhB,sCACE,aAAc,YAEhB,wCACE,WAAY,QACZ,aAAc,QAEhB,yCACE,MAAO,KAIT,2BADA,SAEA,QACA,iBACA,iCACE,MAAO,KACP,iBAAkB,KAEpB,iBACA,gCACE,aAAc,QAGhB,gBADA,iBAEA,oCACE,mBAAoB,KACZ,WAAY,KAEtB,wBACA,+CACE,iBAAkB,QAClB,aAAc,QAGhB,WACE,iBAAkB,QAClB,aAAc,QAEhB,kCACE,iBAAkB,YAClB,aAAc,QAEhB,kCACE,iBAAkB,QAEpB,2CACE,MAAO,KACP,iBAAkB,QAClB,aAAc,QAEhB,iDACE,iBAAkB,QAClB,aAAc,QAEhB,iDACE,MAAO,KACP,iBAAkB,QAClB,aAAc,QAEhB,qBACE,mBAAoB,KACZ,WAAY,KAQtB,2BADA,2BADA,sBAJA,4BACA,iCAEA,+BADA,0BAKA,qCACE,MAAO,KACP,iBAAkB,QAClB,aAAc,QACd,mBAAoB,KACZ,WAAY,KAQtB,0CAFA,0DACA,kEALA,kCACA,uCACA,sEACA,8EAIA,2CACE,iBAAkB,QAClB,aAAc,QAEhB,kDACE,MAAO,KAIT,2BADA,oDADA,sDAGE,iBAAkB,QAClB,aAAc,QAEhB,2CACE,WAAY,KACZ,mBAAoB,KACZ,WAAY,KAGtB,2EADA,oDAEA,uFACA,4DACE,oBAAqB,QAEvB,kBACE,cAAe,IAGjB,eACE,iBAAkB,KAEpB,mBAEA,uBADA,gBAEE,MAAO,KACP,iBAAkB,QAEpB,sCACA,2BACE,MAAO,KACP,iBAAkB,QAClB,aAAc,QACd,mBAAoB,KACZ,WAAY,KAMtB,eACA,kBALA,eACA,yBAEA,mCADA,cAIA,oBACA,0CACE,MAAO,KACP,iBAAkB,QAEpB,uCACA,oDACE,MAAO,QACP,iBAAkB,QAEpB,4CACE,iBAAkB,QAClB,aAAc,QAShB,kCALA,2BAQA,kBAPA,eAFA,oBAQA,mBADA,mBARA,UAIA,cAEA,sBADA,yBAME,aAAc,QAIhB,kCADA,sBADA,mBAGE,MAAO,KACP,iBAAkB,QAEpB,2BACE,MAAO,qBAGT,kBADA,cAEE,MAAO,KAET,2BACA,gCACE,MAAO,KAUT,6CACA,gDAHA,uCACA,0CAJA,4BACA,4CAFA,4BAQA,8BALA,iBAJA,uCADA,sCAWA,yBACE,iBAAkB,QAEpB,yBACE,MAAO,QAGT,4BADA,4BAEA,8BACA,qCACE,MAAO,KAET,iBACE,iBAAkB,QAEpB,qCACE,WAAY,IAEd,4BACA,+BACE,aAAc,QAEhB,kCACA,qCACE,iBAAkB,QAGpB,yBADA,iCAEA,uBACA,4BACA,cAEA,gDADA,oBAEE,MAAO,QAET,4BACE,aAAc,QAEhB,sBACA,2BACE,MAAO,KAET,yBACA,sBACE,cAAe,EAEjB,mCACE,aAAc,QAAQ,YAAY,YAClC,cAAe,EACf,mBAAoB,KACZ,WAAY,KACpB,MAAO,QAET,sBACE,aAAc,QACd,OAAQ,QAEV,4BACE,iBAAkB,YAClB,aAAc,YAQhB,gCACA,wCACA,+BACA,uCAPA,iCAEA,yCADA,yCAEA,iDALA,uCACA,+CASE,cAAe,EAEjB,QAEA,gBADA,YAGA,yCAMA,uCAHA,0CAMA,wCALA,iDAFA,yCAMA,uCAPA,wCAMA,sCARA,0BAMA,0BAKE,mBAAoB,KACZ,WAAY,KAMtB,mBAHA,YAEA,uBADA,sBAGA,8BACE,iBAAkB,QAEpB,4BACE,iBAAkB,QAEpB,uCACE,MAAO,KAET,8BACE,iBAAkB,QAIpB,uBAEA,oBADA,qBAFA,qBADA,4BAKE,aAAc,QAGhB,YACE,aAAc,QAEhB,4BACE,iBAAkB,QAClB,aAAc,QACd,mBAAoB,KACZ,WAAY,KAGtB,UACE,MAAO,KACP,iBAAkB,KAEpB,0BACA,gCACA,uEACE,mBAAoB,KACZ,WAAY,KAMtB,2CADA,mCAFA,kBACA,yBAGE,iBAAkB,QAClB,aAAc,QAEhB,eACE,iBAAkB,QAEpB,iBACE,WAAY,QAAQ,EAAE,OAAO,KAAK,SAIpC,oCACA,uCAHA,8BACA,iCAGE,iBAAkB,QAClB,aAAc,QAQhB,+EADA,wEAHA,6BACA,iFACA,0EAJA,6EAUA,0CADA,kCARA,yEAMA,kEAIE,mBAAoB,KACZ,WAAY,KAEtB,sCACE,MAAO,KACP,iBAAkB,QAClB,aAAc,QAEhB,iCACE,mBAAoB,KACZ,WAAY,KAGtB,eACE,aAAc,QAGhB,8BADA,mBAEA,yBACE,iBAAkB,QAClB,mBAAoB,KACZ,WAAY,KAEtB,yBACE,MAAO,KAET,0BACA,kCACE,aAAc,QAIhB,8BACA,gCAFA,6BADA,8BAIE,mBAAoB,KACZ,WAAY,KAGtB,gCADA,6BAEE,iBAAkB,QAEpB,mDACA,4CACE,aAAc,QAGhB,iDADA,wCAGA,0CADA,iCAEE,MAAO,KACP,aAAc,QACd,mBAAoB,KACZ,WAAY,KAEtB,2CACA,oDACE,mBAAoB,KACZ,WAAY,KACpB,iBAAkB,QAClB,aAAc,QAEhB,wCACA,2CACE,iBAAkB,QAClB,aAAc,QAGhB,eACE,iBAAkB,QAClB,aAAc,QAEhB,uBACA,wCACE,aAAc,KAEhB,iCACE,iBAAkB,QAClB,aAAc,QAEhB,+BACE,mBAAoB,KACZ,WAAY,KAGtB,cACE,iBAAkB,QAGpB,kCADA,4BAEE,iBAAkB,QAGpB,yBADA,uBAEE,MAAO,KACP,iBAAkB,QAClB,aAAc,QAGhB,gBACE,iBAAkB,KAClB,mBAAoB,KACZ,WAAY,KAEtB,oBACE,iBAAkB,QAClB,aAAc,QAEhB,oBACA,kCAGA,2BAFA,iCACA,0BAEE,WAAY,IACZ,OAAQ,KACR,mBAAoB,KACZ,WAAY,KAEtB,cACA,wDACE,mBAAoB,KACZ,WAAY,KAEtB,+CACA,wDACE,aAAc,QACd,iBAAkB,QAClB,mBAAoB,KACZ,WAAY,KAEtB,qDACA,8DACE,aAAc,QACd,iBAAkB,QAClB,mBAAoB,EAAE,EAAE,EAAE,IAAI,mBACtB,WAAY,EAAE,EAAE,EAAE,IAAI,mBAEhC,+BAEA,qCADA,oCAEA,gEACA,yEACE,iBAAkB,KAClB,aAAc,KAEhB,8BACA,mCACA,+DACA,wEACE,mBAAoB,KACZ,WAAY,KACpB,aAAc,QACd,iBAAkB,QAEpB,uCACE,iBAAkB,QAEpB,mBACA,gCACE,mBAAoB,KACZ,WAAY,KAEtB,iCAIA,yCAHA,mBACA,2BACA,iCAEE,MAAO,KACP,iBAAkB,QAClB,aAAc,QACd,mBAAoB,KACZ,WAAY,KAGtB,kCADA,oBAEE,MAAO,QACP,iBAAkB,QAClB,aAAc,QAIhB,yBADA,gCADA,2BAGE,WAAY,IACZ,aAAc,YAGhB,gCADA,2BAEE,eAAgB,KAGlB,sCADA,iCAEE,iBAAkB,QAClB,aAAc,QAGhB,sCAEA,6EAHA,iCAEA,wEAEE,MAAO,QACP,mBAAoB,KACZ,WAAY,KAEtB,yBACE,WAAY,IACZ,aAAc,YAEhB,0BACE,kBAAmB,QAErB,kCACE,aAAc,QAGhB,qFADA,uEAEE,mBAAoB,KACZ,WAAY,KAEtB,2CACE,MAAO,QAGT,8BADA,+BAEE,aAAc,QAEhB,+CACE,MAAO,QACP,iBAAkB,KAEpB,mEACE,MAAO,QAET,wBACE,mBAAoB,EAAE,IAAI,IAAI,EAAE,eACxB,WAAY,EAAE,IAAI,IAAI,EAAE,eAElC,aACE,MAAO,KACP,iBAAkB,QAClB,aAAc,QACd,mBAAoB,MAAM,EAAE,EAAE,EAAE,IAAI,KAC5B,WAAY,MAAM,EAAE,EAAE,EAAE,IAAI,KAEtC,yBACA,yCACE,MAAO,KAET,0BACE,MAAO,KAGT,yCAEE,uBACA,yBACA,2BAHA,2BAIE,QAAS,MAGb,yCAEE,uBACA,yBACA,2BAHA,2BAIE,aAAc,GAGlB,yCAEE,qBACA,uBACA,yBAHA,yBAIE,QAAS,MAGb,yCAEE,oBACA,sBACA,wBAHA,wBAIE,QAAS,MAGb,0CACE,0CACE,MAAO,KACP,IAAK,EAUP,gDACA,kDACA,oDAHA,oDAIE,MAAO,KACP,IAAK,EAGP,kEAIA,wEAHA,oEAIA,0EAHA,sEAIA,4EAPA,sEAIA,4EAIE,iBAAkB,KAClB,oBAAqB,IAAI,IACzB,iBAAkB,YAClB,aAAc,YACd,cAAe,EACf,WAAY,MAGd,oEACA,sEACA,wEAHA,wEAIE,cAAe,EAGjB,mFACA,qFACA,uFAHA,uFAIE,cAAe,KAAK,KAAK,EAAE,EAG7B,6CAIA,qDAIA,mDAIA,2DAXA,+CAIA,uDAIA,qDAIA,6DAXA,iDAIA,yDAIA,uDAIA,+DAfA,iDAIA,yDAIA,uDAIA,+DAIE,cAAe,EAGjB,8DAIA,sEAHA,gEAIA,wEAHA,kEAIA,0EAPA,kEAIA,0EAIE,cAAe,EAAE,EAAE,KAAK,KAG1B,wEACA,0EACA,4EAHA,4EAIE,aAAc,YACd,iBAAkB,KAClB,iBAAkB,YAOpB,gFAJA,0EAKA,kFAJA,4EAKA,oFAJA,8EACA,oFAJA,8EAQE,MAAO,KACP,UAAW,KAGb,2DACA,6DACA,+DAHA,+DAIE,QAAS,MACT,QAAS,GACT,SAAU,SACV,IAAK,IACL,WAAY,MACZ,MAAO,OACP,MAAO,QACP,OAAQ,QAGV,iEACA,mEACA,qEAHA,qEAIE,aAAc,IACd,aAAc,MACd,aAAc,YAEd,iBAAg9yB,KACh9yB,iBAAkB,QAClB,cAAe,EACf,mBAAoB,EAAE,IAAI,IAAI,EAAE,eACxB,WAAY,EAAE,IAAI,IAAI,EAAE,eAGlC,oBACA,sBACA,wBAHA,wBAIE,WAAY,OAOd,qBAJA,iCAKA,uBAJA,mCAKA,yBAJA,qCACA,yBAJA,qCAQE,QAAS,aACT,eAAgB,IAOlB,+BAJA,uBAKA,iCAJA,yBAKA,mCAJA,2BACA,mCAJA,2BAQE,SAAU,SACV,KAAM,MACN,QAAS,mBACT,QAAS,oBACT,QAAS,mBACT,QAAS,YACT,mBAAoB,SACpB,sBAAuB,QACvB,uBAAwB,eACpB,mBAAoB,eAChB,eAAgB,eACxB,SAAU,QACV,OAAQ,KAOV,gDAJA,wCAKA,kDAJA,0CAKA,oDAJA,4CACA,oDAJA,4CAQE,kBAAmB,kBACf,cAAe,kBACX,UAAW,kBAOrB,gDAJA,wCAKA,kDAJA,0CAKA,oDAJA,4CACA,oDAJA,4CAQE,SAAU,SACV,KAAM,IACN,cAAe,kBACX,UAAW,kBACf,kBAAmB,kBAOrB,iEAJA,yDAKA,mEAJA,2DAKA,qEAJA,6DACA,qEAJA,6DAQE,kBAAmB,sBACnB,cAAe,sBACX,UAAW,sBAGjB,gDAIA,0DAIA,6CAIA,mDAXA,kDAIA,4DAIA,+CAIA,qDAXA,oDAIA,8DAIA,iDAIA,uDAfA,oDAIA,8DAIA,iDAIA,uDAIE,YAAa,EACb,eAAgB,EAOlB,sCAJA,8BAKA,wCAJA,gCAKA,0CAJA,kCACA,0CAJA,kCAQE,MAAO,MACP,MAAO,OAOT,+CAJA,uCAKA,iDAJA,yCAKA,mDAJA,2CACA,mDAJA,2CAQE,QAAS,MACT,YAAa,EAGf,+CACA,iDACA,mDAHA,mDAIE,QAAS,KAGX,+CACA,iDACA,mDAHA,mDAIE,MAAO,MACP,YAAa,QACb,QAAS,EAAE,OAAO,EAAE,KACpB,cAAe,EACf,iBAAkB,KAClB,oBAAqB,IAAI,IACzB,iBAAkB,QAClB,OAAQ,IAAI,MAAM,YAClB,WAAY,EACZ,mBAAoB,EAAE,IAAI,IAAI,EAAE,QACxB,WAAY,EAAE,IAAI,IAAI,EAAE,QAGlC,qDACA,uDACA,yDAHA,yDAIE,cAAe,EACf,iBAAkB,KAClB,OAAQ,IAAI,MAAM,QAClB,WAAY,EACZ,mBAAoB,EAAE,IAAI,IAAI,eAAoB,EAAE,IAAI,IAAI,gBACpD,WAAY,EAAE,IAAI,IAAI,eAAoB,EAAE,IAAI,IAAI,gBAG9D,qDACA,uDACA,yDAHA,yDAIE,QAAS,MACT,QAAS,GACT,SAAU,SACV,IAAK,IACL,WAAY,MACZ,MAAO,KACP,MAAO,QACP,OAAQ,QACR,oBAAqB,EAAE,MAGzB,+BACA,iCACA,mCAHA,mCAIE,YAAa,MAGf,sCACA,wCACA,0CAHA,0CAIE,aAAc,MACd,YAAa,EAOf,6CAJA,uDAKA,+CAJA,yDAKA,iDAJA,2DACA,iDAJA,2DAQE,QAAS,MACT,WAAY,EACZ,aAAc,EACd,QAAS,IAAI,IAAI,IAAI,KACrB,WAAY,KACZ,WAAY,EACZ,cAAe,EAGjB,2EACA,6EACA,+EAHA,+EAIE,iBAAkB,QAGpB,wCACA,0CACA,4CAHA,4CAIE,mBAAoB,WACZ,WAAY,WACpB,QAAS,IAAI,EAAE,EACf,aAAc,IAAI,IAAI,EAAE,IACxB,aAAc,MACd,aAAc,QAEd,iBAA4+yB,KAC5+yB,cAAe,EACf,mBAAoB,EAAE,IAAI,IAAI,EAAE,eACxB,WAAY,EAAE,IAAI,IAAI,EAAE,eAGlC,wDACA,0DACA,4DAHA,4DAIE,OAAQ,OAAO,KAAK,EACpB,QAAS,EAGX,gEACA,kEACA,oEAHA,oEAIE,cAAe,EACf,iBAAkB,KAClB,OAAQ,IAAI,MAAM,QAClB,WAAY,EACZ,mBAAoB,EAAE,IAAI,IAAI,EAAE,eACxB,WAAY,EAAE,IAAI,IAAI,EAAE,eAGlC,2CACA,6CACA,+CAHA,+CAIE,QAAS,aAGX,wCACA,0CACA,4CAHA,4CAIE,IAAK,GAGT,yCAEE,oBACA,sBACA,wBAHA,wBAIE,QAAS,MAGb,yCAEE,qBACA,uBACA,yBAHA,yBAIE,QAAS,MAIb,iBACE,iBAAkB,KAClB,OAAQ,kBACR,QAAS,IAEX,sBACE,aAAc,QAEhB,mBACE,MAAO,KACP,OAAQ,KACR,iBAAkB,QAClB,cAAe,KAEjB,uBACE,KAAM,KAER,wBACE,MAAO,KAET,yBACE,iBAAkB,QAClB,aAAc,QAEhB,sCACE,OAAQ,IAAI,MAAM,KAClB,mBAAoB,EAAE,EAAE,EAAE,IAAI,eACtB,WAAY,EAAE,EAAE,EAAE,IAAI,eAC9B,WAAY,KACZ,MAAO,QAET,qCACE,WAAY,QACZ,OAAQ,IAEV,iBACE,iBAAkB,KAGpB,iBACE,iBAAkB,0BAEpB,yGACE,iBACE,iBAAkB,8BAGtB,sBACE,MAAO,KAET,iBACE,iBAAkB,QAClB,aAAc,QAEhB,uBACE,aAAc,KACd,mBAAoB,EAAE,IAAI,IAAI,EAAE,eACxB,WAAY,EAAE,IAAI,IAAI,EAAE,eAChC,cAAe,IAEjB,iCACE,mBAAoB,KACZ,WAAY,KAGtB,wCADA,uCAEA,8CACE,iBAAkB,QAClB,aAAc,QACd,mBAAoB,KACZ,WAAY,KAEtB,kCACE,cAAe,EAAE,IAAI,IAAI,EAE3B,mCACE,cAAe,IAAI,EAAE,EAAE,IAEzB,yCACE,cAAe,IAAI,EAAE,EAAE,IAEzB,0CACE,cAAe,EAAE,IAAI,IAAI,EAE3B,yBACE,UAAW,KACX,QAAS,IAAI;;;;;;;;;;;;;;;;;;;;;;;AAIf,6BADA,0BAEE,iBAAkB,KAIpB,6BADA,0BADA,0BAGE,iBAAkB,KAClB,iBAAkB,KAClB,MAAO,KACP,aAAc,KAEhB,0BACE,aAAc,KAEhB,gCACE,aAAc,YAAY,KAAQ,KAAQ,YAE5C,oBACE,aAAc,KAGhB,yCADA,yCAEE,aAAc,QAEhB,iDACA,8CACE,aAAc,KAEhB,+CACE,iBAAkB,KAGpB,sCADA,yCAEE,aAAc,mBACd,iBAAkB,mBAEpB,oCACE,aAAc,QAGhB,mEADA,sEAEE,oBAAqB,QAGvB,gEADA,mEAEE,mBAAoB,QAEtB,aACA,yBACE,aAAc,QACd,mBAAoB,MAAM,EAAE,EAAE,EAAE,IAAI,KAAM,EAAE,EAAE,EAAE,IAAI,QAC5C,WAAY,MAAM,EAAE,EAAE,EAAE,IAAI,KAAM,EAAE,EAAE,EAAE,IAAI,QAEtD,yBACE,iBAAkB,mBAEpB,2BACE,aAAc,kBACd,iBAAkB,KAEpB,oCACE,MAAO,KACP,iBAAkB,KAEpB,yCACE,iBAAkB,KAClB,aAAc,QAEhB,oEACE,aAAc,KAEhB,4EACE,aAAc,KAEhB,4CACE,iBAAkB,KAClB,MAAO,KAET,gCACA,qCACA,qCACE,iBAAkB,QAEpB,6DACA,6DACE,iBAAkB,QAEpB,0CACE,iBAAkB,QAClB,aAAc,KAEhB,kCACE,iBAAkB,qBAEpB,iEACE,iBAAkB,mBAEpB,mDACE,aAAc,QAEhB,sBACE,cAAe,EACf,iBAAkB,KAClB,mBAAoB,MAAM,EAAE,EAAE,EAAE,IAAI,QAC5B,WAAY,MAAM,EAAE,EAAE,EAAE,IAAI,QAEtC,qCACE,MAAO,QACP,iBAAkB,QAEpB,4BACE,MAAO,KACP,WAAY,QACZ,aAAc,QAEhB,mCACE,aAAc,QAEhB,sBACE,MAAO,QAET,wCACE,MAAO,QAGT,8BADA,sCAEE,aAAc,QACd,cAAe,EAEjB,qCACA,yDACE,aAAc,QAEhB,mFACE,iBAAkB,KAClB,iBAAkB,QAClB,MAAO,QAET,0CACE,aAAc,QACd,mBAAoB,EAAE,IAAI,IAAI,EAAE,eACxB,WAAY,EAAE,IAAI,IAAI,EAAE,eAElC,iDACE,WAAY,KACZ,aAAc,YACd,oBAAqB,QACrB,kBAAmB,QAErB,4CACA,0CACE,WAAY,IACZ,aAAc,QAEhB,2CACE,WAAY,QAEd,2DACE,cAAe,EAGjB,iCACA,uCAFA,iCAGE,cAAe,EAEjB,oCACE,aAAc,QAEhB,0CACE,cAAe,EAEjB,qBACE,cAAe,EAEjB,kCACE,iBAAkB,QAEpB,+BACE,iBAAkB,YAEpB,qCACE,iBAAkB,QAEpB,qCACE,iBAAkB,QAClB,MAAO,KAET,2CACE,iBAAkB,QAEpB,sCACE,aAAc,QAEhB,6DACE,iBAAkB,KAEpB,iEACE,iBAAkB,KAClB,aAAc,QACd,cAAe,EAEjB,cACE,MAAO,KAET,cACE,MAAO,KAET,eACE,YAAa,IAEf,cACE,MAAO,QAET,gBACE,MAAO,IAET,eACE,MAAO,QAET,mBACE,YAAa,IAEf,sBACE,iBAAkB,QAEpB,YACE,aAAc,QACd,iBAAkB,oBAEpB,YACE,aAAc,QACd,iBAAkB,oBAEpB,YACE,aAAc,QACd,iBAAkB,oBAEpB,YACE,aAAc,QACd,iBAAkB,qBAEpB,YACE,aAAc,QACd,iBAAkB,oBAEpB,YACE,aAAc,QACd,iBAAkB,oBAEpB,2CACE,MAAO,KAET,6CACE,iBAAkB,QAClB,MAAO,QAET,0DACE,oBACE,iBAAkB,MAGtB,6DACA,mDACE,mBAAoB,KACZ,WAAY,KACpB,MAAO,KACP,cAAe,EACf,YAAa,MACb,MAAO,IAET,mEACA,yDACE,iBAAkB,QAClB,aAAc,QAEhB,mDACE,KAAM,EACN,OAAQ,EACR,YAAa,KACb,eAAgB,KAChB,YAAa,MAEf,4CACE,OAAQ,EAAE,EAAE,EAAE,KAEhB,qDACA,iDACE,MAAO,QAIT,mDAEA,oDAJA,kDAGA,+CAEA,gDAJA,8CAKE,iBAAkB,YAIpB,2DAEA,4DAJA,0DAGA,uDAEA,wDAJA,sDAKE,MAAO,KAET,oDACA,gDACE,MAAO,KAET,yDACE,YAAa,IAEf,2BAGA,wCACA,wCACA,4CAJA,mCAKA,8CAJA,sCAKE,QAAS,KACT,SAAU,SACV,MAAO,gBAET,2BACE,aAAc,IAEhB,4CACE,MAAO", "file": "web/kendo.material.min.css", "sourceRoot": "/source/", "sourcesContent": ["/** \r\n * Kendo UI v2016.2.714 (http://www.telerik.com/kendo-ui)                                                                                                                                               \r\n * Copyright 2016 Telerik AD. All rights reserved.                                                                                                                                                      \r\n *                                                                                                                                                                                                      \r\n * Kendo UI commercial licenses may be obtained at                                                                                                                                                      \r\n * http://www.telerik.com/purchase/license-agreement/kendo-ui-complete                                                                                                                                  \r\n * If you do not own a commercial license, this file shall be governed by the trial license terms.                                                                                                      \r\n                                                                                                                                                                                                       \r\n                                                                                                                                                                                                       \r\n                                                                                                                                                                                                       \r\n                                                                                                                                                                                                       \r\n                                                                                                                                                                                                       \r\n                                                                                                                                                                                                       \r\n                                                                                                                                                                                                       \r\n                                                                                                                                                                                                       \r\n                                                                                                                                                                                                       \r\n                                                                                                                                                                                                       \r\n                                                                                                                                                                                                       \r\n                                                                                                                                                                                                       \r\n                                                                                                                                                                                                       \r\n                                                                                                                                                                                                       \r\n                                                                                                                                                                                                       \r\n\r\n*/\r\n/* Kendo skin */\r\n.k-theme-test-class,\r\n.ktb-theme-id-material {\r\n  opacity: 0;\r\n}\r\n.ktb-var-accent {\r\n  color: #3f51b5;\r\n}\r\n.ktb-var-base {\r\n  color: #fff;\r\n}\r\n.ktb-var-background {\r\n  color: #fff;\r\n}\r\n.ktb-var-border-radius {\r\n  border-radius: 2px;\r\n}\r\n.ktb-var-normal-background {\r\n  color: #fff;\r\n}\r\n.ktb-var-normal-gradient {\r\n  background-image: none;\r\n}\r\n.ktb-var-normal-text-color {\r\n  color: #444444;\r\n}\r\n.ktb-var-hover-background {\r\n  color: #ebebeb;\r\n}\r\n.ktb-var-hover-gradient {\r\n  background-image: none;\r\n}\r\n.ktb-var-hover-text-color {\r\n  color: #444444;\r\n}\r\n.ktb-var-selected-background {\r\n  color: #00b0ff;\r\n}\r\n.ktb-var-selected-gradient {\r\n  background-image: none;\r\n}\r\n.ktb-var-selected-text-color {\r\n  color: #3f51b5;\r\n}\r\n.ktb-var-error {\r\n  color: #ffcdd2;\r\n}\r\n.ktb-var-warning {\r\n  color: #fdefba;\r\n}\r\n.ktb-var-success {\r\n  color: #c8e6c9;\r\n}\r\n.ktb-var-info {\r\n  color: #bbdefb;\r\n}\r\n.ktb-var-series-a {\r\n  color: #3f51b5;\r\n}\r\n.ktb-var-series-b {\r\n  color: #03a9f4;\r\n}\r\n.ktb-var-series-c {\r\n  color: #4caf50;\r\n}\r\n.ktb-var-series-d {\r\n  color: #f9ce1d;\r\n}\r\n.ktb-var-series-e {\r\n  color: #ff9800;\r\n}\r\n.ktb-var-series-f {\r\n  color: #ff5722;\r\n}\r\n.k-grid-norecords-template {\r\n  background-color: #fff;\r\n  border: 1px solid #e6e6e6;\r\n}\r\n.k-in,\r\n.k-item,\r\n.k-window-action {\r\n  border-color: transparent;\r\n}\r\n/* main colors */\r\n.k-block,\r\n.k-widget {\r\n  background-color: #fff;\r\n}\r\n.k-block,\r\n.k-widget,\r\n.k-input,\r\n.k-textbox,\r\n.k-group,\r\n.k-content,\r\n.k-header,\r\n.k-filter-row > th,\r\n.k-editable-area,\r\n.k-separator,\r\n.k-colorpicker .k-i-arrow-s,\r\n.k-textbox > input,\r\n.k-autocomplete,\r\n.k-dropdown-wrap,\r\n.k-toolbar,\r\n.k-group-footer td,\r\n.k-grid-footer,\r\n.k-footer-template td,\r\n.k-state-default,\r\n.k-state-default .k-select,\r\n.k-state-disabled,\r\n.k-grid-header,\r\n.k-grid-header-wrap,\r\n.k-grid-header-locked,\r\n.k-grid-footer-locked,\r\n.k-grid-content-locked,\r\n.k-grid td,\r\n.k-grid td.k-state-selected,\r\n.k-grid-footer-wrap,\r\n.k-pager-wrap,\r\n.k-pager-wrap .k-link,\r\n.k-pager-refresh,\r\n.k-grouping-header,\r\n.k-grouping-header .k-group-indicator,\r\n.k-panelbar > .k-item > .k-link,\r\n.k-panel > .k-item > .k-link,\r\n.k-panelbar .k-panel,\r\n.k-panelbar .k-content,\r\n.k-treemap-tile,\r\n.k-calendar th,\r\n.k-slider-track,\r\n.k-splitbar,\r\n.k-dropzone-active,\r\n.k-tiles,\r\n.k-toolbar,\r\n.k-tooltip,\r\n.k-button-group .k-tool,\r\n.k-upload-files {\r\n  border-color: #e6e6e6;\r\n}\r\n.k-group,\r\n.k-toolbar,\r\n.k-grouping-header,\r\n.k-pager-wrap,\r\n.k-group-footer td,\r\n.k-grid-footer,\r\n.k-footer-template td,\r\n.k-widget .k-status,\r\n.k-calendar th,\r\n.k-dropzone-hovered,\r\n.k-widget.k-popup {\r\n  background-color: #3f51b5;\r\n}\r\n.k-grouping-row td,\r\ntd.k-group-cell,\r\n.k-resize-handle-inner {\r\n  background-color: #3f51b5;\r\n}\r\n.k-list-container {\r\n  border-color: rgba(0, 0, 0, 0.2);\r\n  background-color: #ffffff;\r\n}\r\n.k-content,\r\n.k-editable-area,\r\n.k-panelbar > li.k-item,\r\n.k-panel > li.k-item,\r\n.k-tiles {\r\n  background-color: #fff;\r\n}\r\n.k-alt,\r\n.k-separator,\r\n.k-resource.k-alt,\r\n.k-pivot-layout > tbody > tr:first-child > td:first-child {\r\n  background-color: #fafafa;\r\n}\r\n.k-pivot-rowheaders .k-alt .k-alt,\r\n.k-header.k-alt {\r\n  background-color: #e6e6e6;\r\n}\r\n.k-textbox,\r\n.k-autocomplete.k-header,\r\n.k-dropdown-wrap.k-state-active,\r\n.k-picker-wrap.k-state-active,\r\n.k-numeric-wrap.k-state-active {\r\n  border-color: #e6e6e6;\r\n  background-color: #fff;\r\n}\r\n.k-textbox > input,\r\n.k-autocomplete .k-input,\r\n.k-dropdown-wrap .k-input,\r\n.k-autocomplete.k-state-focused .k-input,\r\n.k-dropdown-wrap.k-state-focused .k-input,\r\n.k-picker-wrap.k-state-focused .k-input,\r\n.k-numeric-wrap.k-state-focused .k-input {\r\n  border-color: #e6e6e6;\r\n}\r\ninput.k-textbox,\r\ntextarea.k-textbox,\r\ninput.k-textbox:hover,\r\ntextarea.k-textbox:hover,\r\n.k-textbox > input {\r\n  background: none;\r\n}\r\n.k-input,\r\ninput.k-textbox,\r\ntextarea.k-textbox,\r\ninput.k-textbox:hover,\r\ntextarea.k-textbox:hover,\r\n.k-textbox > input,\r\n.k-multiselect-wrap {\r\n  background-color: #fff;\r\n  color: #444444;\r\n}\r\n.k-input[readonly] {\r\n  background-color: #fff;\r\n  color: #444444;\r\n}\r\n.k-block,\r\n.k-widget,\r\n.k-popup,\r\n.k-content,\r\n.k-toolbar,\r\n.k-dropdown .k-input {\r\n  color: #444444;\r\n}\r\n.k-inverse {\r\n  color: #ffffff;\r\n}\r\n.k-block {\r\n  color: #ffffff;\r\n}\r\n.k-link:link,\r\n.k-link:visited,\r\n.k-nav-current.k-state-hover .k-link {\r\n  color: #428bca;\r\n}\r\n.k-tabstrip-items .k-link,\r\n.k-panelbar > li > .k-link {\r\n  color: #444444;\r\n}\r\n.k-header,\r\n.k-treemap-title,\r\n.k-grid-header .k-header > .k-link {\r\n  color: #ffffff;\r\n}\r\n.k-header,\r\n.k-grid-header,\r\n.k-toolbar,\r\n.k-dropdown-wrap,\r\n.k-picker-wrap,\r\n.k-numeric-wrap,\r\n.k-grouping-header,\r\n.k-pager-wrap,\r\n.k-textbox,\r\n.k-button,\r\n.k-progressbar,\r\n.k-draghandle,\r\n.k-autocomplete,\r\n.k-state-highlight,\r\n.k-tabstrip-items .k-item,\r\n.k-panelbar .k-tabstrip-items .k-item,\r\n.km-pane-wrapper > .km-pane > .km-view > .km-content {\r\n  background-image: none;\r\n  background-position: 50% 50%;\r\n  background-color: #3f51b5;\r\n}\r\n.k-widget.k-tooltip {\r\n  background-image: none;\r\n}\r\n.k-block,\r\n.k-header,\r\n.k-grid-header,\r\n.k-toolbar,\r\n.k-grouping-header,\r\n.k-pager-wrap,\r\n.k-button,\r\n.k-draghandle,\r\n.k-treemap-tile,\r\nhtml .km-pane-wrapper .k-header {\r\n  background-color: #3f51b5;\r\n}\r\n/* icons */\r\n.k-icon:hover,\r\n.k-state-hover .k-icon,\r\n.k-state-selected .k-icon,\r\n.k-state-focused .k-icon,\r\n.k-column-menu .k-state-hover .k-sprite,\r\n.k-column-menu .k-state-active .k-sprite,\r\n.k-pager-numbers .k-current-page .k-link:hover:after,\r\n.k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view.k-state-hover > .k-link:after {\r\n  opacity: 1;\r\n}\r\n.k-icon,\r\n.k-state-disabled .k-icon,\r\n.k-column-menu .k-sprite,\r\n.k-pager-numbers .k-current-page .k-link:after,\r\n.k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view > .k-link:after {\r\n  opacity: 0.7;\r\n}\r\n.k-mobile-list .k-check:checked,\r\n.k-mobile-list .k-edit-field [type=checkbox]:checked,\r\n.k-mobile-list .k-edit-field [type=radio]:checked {\r\n  opacity: 0.7;\r\n}\r\n.k-tool {\r\n  border-color: transparent;\r\n}\r\n.k-icon,\r\n.k-tool-icon,\r\n.k-grouping-dropclue,\r\n.k-drop-hint,\r\n.k-column-menu .k-sprite,\r\n.k-grid-mobile .k-resize-handle-inner:before,\r\n.k-grid-mobile .k-resize-handle-inner:after,\r\n.k-pager-numbers .k-current-page .k-link:after,\r\n.k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view > .k-link:after,\r\n.k-gantt-views > .k-current-view > .k-link:after {\r\n  background-image: url('Material/sprite.png');\r\n  border-color: transparent;\r\n}\r\n/* IE will ignore the above selectors if these are added too */\r\n.k-mobile-list .k-check:checked,\r\n.k-mobile-list .k-edit-field [type=checkbox]:checked,\r\n.k-mobile-list .k-edit-field [type=radio]:checked {\r\n  background-image: url('Material/sprite.png');\r\n  border-color: transparent;\r\n}\r\n.k-loading,\r\n.k-state-hover .k-loading {\r\n  background-image: url('Material/loading.gif');\r\n  background-position: 50% 50%;\r\n}\r\n.k-loading-image {\r\n  background-image: url('Material/loading-image.gif');\r\n}\r\n.k-loading-color {\r\n  background-color: #ffffff;\r\n}\r\n.k-button {\r\n  color: #444444;\r\n  border-color: #fafafa;\r\n  background-color: #fafafa;\r\n}\r\n.k-draghandle {\r\n  border-color: #3f51b5;\r\n  background-color: #3f51b5;\r\n  -webkit-box-shadow: none;\r\n          box-shadow: none;\r\n}\r\n.k-draghandle:hover {\r\n  border-color: #3f51b5;\r\n  background-color: #3f51b5;\r\n  -webkit-box-shadow: 0 0 0 8px rgba(63, 81, 181, 0.3);\r\n          box-shadow: 0 0 0 8px rgba(63, 81, 181, 0.3);\r\n}\r\n/* Scheduler */\r\n.k-scheduler {\r\n  color: #ffffff;\r\n  background-color: #fff;\r\n}\r\n.k-scheduler-layout {\r\n  color: #444444;\r\n}\r\n.k-scheduler-datecolumn,\r\n.k-scheduler-groupcolumn {\r\n  background-color: #fff;\r\n  color: #444444;\r\n}\r\n.k-scheduler-times tr,\r\n.k-scheduler-times th,\r\n.k-scheduler-table td,\r\n.k-scheduler-header th,\r\n.k-scheduler-header-wrap,\r\n.k-scheduler-times {\r\n  border-color: #e6e6e6;\r\n}\r\n.k-nonwork-hour {\r\n  background-color: #fafafa;\r\n}\r\n.k-gantt .k-nonwork-hour {\r\n  background-color: rgba(0, 0, 0, 0.02);\r\n}\r\n.k-gantt .k-header.k-nonwork-hour {\r\n  background-color: rgba(0, 0, 0, 0.2);\r\n}\r\n.k-scheduler-table .k-today,\r\n.k-today > .k-scheduler-datecolumn,\r\n.k-today > .k-scheduler-groupcolumn {\r\n  background-color: #e9e9e9;\r\n}\r\n.k-scheduler-now-arrow {\r\n  border-left-color: #eed3d7;\r\n}\r\n.k-scheduler-now-line {\r\n  background-color: #eed3d7;\r\n}\r\n.k-event,\r\n.k-task-complete {\r\n  border-color: #606fc7;\r\n  background: #606fc7 0 -257px none repeat-x;\r\n  color: #ffffff;\r\n}\r\n.k-event-inverse {\r\n  color: #444444;\r\n}\r\n.k-event.k-state-selected {\r\n  background-position: 0 0;\r\n  -webkit-box-shadow: 0 0 0 2px #444444;\r\n          box-shadow: 0 0 0 2px #444444;\r\n}\r\n.k-event .k-resize-handle:after,\r\n.k-task-single .k-resize-handle:after {\r\n  background-color: #ffffff;\r\n}\r\n.k-scheduler-marquee:before,\r\n.k-scheduler-marquee:after {\r\n  border-color: #fff;\r\n}\r\n.k-panelbar .k-content,\r\n.k-panelbar .k-panel,\r\n.k-panelbar .k-item {\r\n  background-color: #fff;\r\n  color: #444444;\r\n  border-color: #cccccc;\r\n}\r\n.k-panelbar > li > .k-link {\r\n  color: #444444;\r\n}\r\n.k-panelbar > .k-item > .k-link {\r\n  border-color: #cccccc;\r\n}\r\n.k-panel > li.k-item {\r\n  background-color: #fff;\r\n}\r\n/* states */\r\n.k-state-active,\r\n.k-state-active:hover,\r\n.k-active-filter,\r\n.k-tabstrip .k-state-active {\r\n  background-color: #ffffff;\r\n  border-color: #cccccc;\r\n  color: #444444;\r\n}\r\n.k-fieldselector .k-list-container {\r\n  background-color: #ffffff;\r\n}\r\n.k-button:focus,\r\n.k-button.k-state-focused {\r\n  border-color: #dbdbdb;\r\n}\r\n.k-button:hover,\r\n.k-button.k-state-hover {\r\n  color: #444444;\r\n  border-color: #ebebeb;\r\n  background-color: #ebebeb;\r\n}\r\n.k-button:active,\r\n.k-button.k-state-active {\r\n  color: #3f51b5;\r\n  background-color: #dbdbdb;\r\n  border-color: #dbdbdb;\r\n}\r\n.k-button:active:hover,\r\n.k-button.k-state-active:hover {\r\n  color: #ffffff;\r\n  border-color: #5c6bc0;\r\n  background-color: #5c6bc0;\r\n}\r\n.k-button:focus:not(.k-state-disabled):not([disabled]) {\r\n  -webkit-box-shadow: 0 6px 17px 0 #c4c4c4;\r\n          box-shadow: 0 6px 17px 0 #c4c4c4;\r\n}\r\n.k-button:focus:active:not(.k-state-disabled):not([disabled]) {\r\n  -webkit-box-shadow: 0 6px 17px 0 rgba(235, 235, 235, 0.3);\r\n          box-shadow: 0 6px 17px 0 rgba(235, 235, 235, 0.3);\r\n}\r\n.k-menu .k-state-hover > .k-state-active {\r\n  background-color: transparent;\r\n}\r\n.k-state-highlight {\r\n  background: #ffffff;\r\n  color: #444444;\r\n}\r\n.k-state-focused,\r\n.k-grouping-row .k-state-focused {\r\n  border-color: #67afe9;\r\n}\r\n.k-calendar .k-link {\r\n  color: #444444;\r\n}\r\n.k-calendar .k-footer {\r\n  padding: 0;\r\n}\r\n.k-calendar .k-footer .k-nav-today {\r\n  color: #444444;\r\n  text-decoration: none;\r\n  background-color: #fff;\r\n}\r\n.k-calendar .k-footer .k-nav-today:hover,\r\n.k-calendar .k-footer .k-nav-today.k-state-hover {\r\n  background-color: #fff;\r\n  text-decoration: underline;\r\n}\r\n.k-calendar .k-footer .k-nav-today:active {\r\n  background-color: #fff;\r\n}\r\n.k-calendar .k-link.k-nav-fast {\r\n  color: #444444;\r\n}\r\n.k-calendar .k-nav-fast.k-state-hover {\r\n  text-decoration: none;\r\n  background-color: #ebebeb;\r\n  color: #444444;\r\n}\r\n.k-calendar .k-link.k-state-hover {\r\n  border-radius: 50%;\r\n}\r\n.k-calendar .k-footer .k-link {\r\n  border-radius: 0;\r\n}\r\n.k-calendar th {\r\n  background-color: #3f51b5;\r\n}\r\n.k-window-titlebar .k-link {\r\n  border-radius: 50%;\r\n}\r\n.k-calendar-container.k-group {\r\n  border-color: rgba(0, 0, 0, 0.2);\r\n}\r\n.k-state-selected,\r\n.k-state-selected:link,\r\n.k-state-selected:visited,\r\n.k-list > .k-state-selected,\r\n.k-list > .k-state-highlight,\r\n.k-panel > .k-state-selected,\r\n.k-ghost-splitbar-vertical,\r\n.k-ghost-splitbar-horizontal,\r\n.k-draghandle.k-state-selected:hover,\r\n.k-scheduler .k-scheduler-toolbar .k-state-selected,\r\n.k-scheduler .k-today.k-state-selected,\r\n.k-marquee-color {\r\n  color: #3f51b5;\r\n  background-color: #fff;\r\n  border-color: #ffffff;\r\n}\r\n.k-virtual-item.k-first,\r\n.k-group-header + .k-list > .k-item.k-first,\r\n.k-static-header + .k-list > .k-item.k-first {\r\n  border-top-color: #ebebeb;\r\n}\r\n.k-group-header + div > .k-list > .k-item.k-first:before {\r\n  border-top-color: #ebebeb;\r\n}\r\n.k-popup > .k-group-header,\r\n.k-popup > .k-virtual-wrap > .k-group-header {\r\n  background: #ebebeb;\r\n  color: #3f51b5;\r\n}\r\n.k-popup .k-list .k-item > .k-group {\r\n  background: #ebebeb;\r\n  color: #3f51b5;\r\n  border-bottom-left-radius: 1px;\r\n}\r\n.k-marquee-text {\r\n  color: #3f51b5;\r\n}\r\n.k-state-focused,\r\n.k-list > .k-state-focused,\r\n.k-listview > .k-state-focused,\r\n.k-listview > .k-state-focused.k-state-selected,\r\ntd.k-state-focused,\r\n.k-button.k-state-focused {\r\n  -webkit-box-shadow: inset 0 0 0 1px #808080;\r\n          box-shadow: inset 0 0 0 1px #808080;\r\n}\r\n.k-state-focused.k-state-selected,\r\n.k-list > .k-state-focused.k-state-selected,\r\ntd.k-state-focused.k-state-selected {\r\n  -webkit-box-shadow: none;\r\n          box-shadow: none;\r\n}\r\n.k-ie8 .k-panelbar span.k-state-focused,\r\n.k-ie8 .k-menu li.k-state-focused,\r\n.k-ie8 .k-listview > .k-state-focused,\r\n.k-ie8 .k-grid-header th.k-state-focused,\r\n.k-ie8 td.k-state-focused,\r\n.k-ie8 .k-tool.k-state-hover,\r\n.k-ie8 .k-button:focus,\r\n.k-ie8 .k-button.k-state-focused,\r\n.k-list > .k-state-selected.k-state-focused,\r\n.k-list-optionlabel.k-state-selected.k-state-focused {\r\n  -webkit-box-shadow: none;\r\n          box-shadow: none;\r\n}\r\n.k-state-selected > .k-link,\r\n.k-panelbar > li > .k-state-selected,\r\n.k-panelbar > li.k-state-default > .k-link.k-state-selected {\r\n  color: #3f51b5;\r\n}\r\n.k-state-hover,\r\n.k-state-hover:hover,\r\n.k-splitbar-horizontal-hover:hover,\r\n.k-splitbar-vertical-hover:hover,\r\n.k-list > .k-state-hover,\r\n.k-scheduler .k-scheduler-toolbar ul li.k-state-hover,\r\n.k-pager-wrap .k-link:hover,\r\n.k-dropdown .k-state-focused,\r\n.k-filebrowser-dropzone,\r\n.k-mobile-list .k-item > .k-link:active,\r\n.k-mobile-list .k-item > .k-label:active,\r\n.k-mobile-list .k-edit-label.k-check:active,\r\n.k-mobile-list .k-recur-view .k-check:active {\r\n  color: #444444;\r\n  background-color: #ebebeb;\r\n  border-color: #ebebeb;\r\n}\r\n/* this selector should be used separately, otherwise old IEs ignore the whole rule */\r\n.k-mobile-list .k-scheduler-timezones .k-edit-field:nth-child(2):active {\r\n  color: #444444;\r\n  background-color: #ebebeb;\r\n  border-color: #ebebeb;\r\n}\r\n.k-ie8 .k-window-titlebar .k-state-hover {\r\n  border-color: #ebebeb;\r\n}\r\n.k-state-hover > .k-select,\r\n.k-state-focused > .k-select {\r\n  border-color: #ebebeb;\r\n}\r\n.k-button:hover,\r\n.k-button.k-state-hover,\r\n.k-button:focus,\r\n.k-button.k-state-focused,\r\n.k-textbox:hover,\r\n.k-state-hover,\r\n.k-state-hover:hover,\r\n.k-pager-wrap .k-link:hover,\r\n.k-other-month.k-state-hover .k-link,\r\ndiv.k-filebrowser-dropzone em,\r\n.k-draghandle:hover {\r\n  background-image: none;\r\n}\r\n.k-pager-wrap {\r\n  background-color: #3f51b5;\r\n  color: #ffffff;\r\n}\r\n.k-autocomplete.k-state-active,\r\n.k-picker-wrap.k-state-active,\r\n.k-numeric-wrap.k-state-active,\r\n.k-dropdown-wrap.k-state-active,\r\n.k-state-active,\r\n.k-state-active:hover,\r\n.k-state-active > .k-link,\r\n.k-button:active,\r\n.k-panelbar > .k-item > .k-state-focused {\r\n  background-image: none;\r\n}\r\n.k-state-selected,\r\n.k-button:active,\r\n.k-button.k-state-active,\r\n.k-draghandle.k-state-selected:hover {\r\n  background-image: none;\r\n}\r\n.k-button:active,\r\n.k-button.k-state-active,\r\n.k-draghandle.k-state-selected:hover {\r\n  background-position: 50% 50%;\r\n}\r\n.k-tool-icon {\r\n  background-image: url('Material/sprite.png');\r\n}\r\n.k-state-hover > .k-link,\r\n.k-other-month.k-state-hover .k-link,\r\ndiv.k-filebrowser-dropzone em {\r\n  color: #444444;\r\n}\r\n.k-autocomplete.k-state-hover,\r\n.k-autocomplete.k-state-focused,\r\n.k-picker-wrap.k-state-hover,\r\n.k-picker-wrap.k-state-focused,\r\n.k-numeric-wrap.k-state-hover,\r\n.k-numeric-wrap.k-state-focused,\r\n.k-dropdown-wrap.k-state-hover,\r\n.k-dropdown-wrap.k-state-focused {\r\n  background-color: #ffffff;\r\n  background-image: none;\r\n  background-position: 50% 50%;\r\n  border-color: #ebebeb;\r\n}\r\n.km-pane-wrapper .k-mobile-list input:not([type=\"checkbox\"]):not([type=\"radio\"]),\r\n.km-pane-wrapper .km-pane .k-mobile-list select:not([multiple]),\r\n.km-pane-wrapper .k-mobile-list textarea,\r\n.k-dropdown .k-state-focused .k-input {\r\n  color: #444444;\r\n}\r\n.km-pane-wrapper .km-pane .k-mobile-list.k-filter-menu .k-space-right {\r\n  background: #fff;\r\n  border-color: #e6e6e6;\r\n}\r\n.km-pane-wrapper .km-pane .k-mobile-list.k-filter-menu .k-space-right > input {\r\n  background-color: #fff;\r\n  border-color: #f0f0f0;\r\n}\r\n.km-pane-wrapper .km-pane .k-mobile-list.k-filter-menu .k-space-right > input + .k-i-search:before {\r\n  color: #444444;\r\n}\r\n.km-pane-wrapper .km-pane .k-mobile-list.k-filter-menu .k-space-right > input:focus {\r\n  -webkit-box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2), 0 2px 3px rgba(0, 0, 0, 0.05);\r\n          box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2), 0 2px 3px rgba(0, 0, 0, 0.05);\r\n}\r\n.k-dropdown .k-state-hover .k-input {\r\n  color: #444444;\r\n}\r\n.k-state-error {\r\n  border-color: #eed3d7;\r\n  background-color: #f2dede;\r\n  color: #b94a48;\r\n}\r\n.k-state-disabled {\r\n  opacity: .7;\r\n}\r\n.k-ie8 .k-state-disabled {\r\n  filter: alpha(opacity=70);\r\n}\r\n.k-tile-empty.k-state-selected,\r\n.k-loading-mask.k-state-selected {\r\n  border-width: 0;\r\n  background-image: none;\r\n  background-color: transparent;\r\n}\r\n.k-state-disabled,\r\n.k-state-disabled .k-link,\r\n.k-state-disabled .k-button,\r\n.k-other-month,\r\n.k-other-month .k-link,\r\n.k-dropzone em,\r\n.k-dropzone .k-upload-status,\r\n.k-tile-empty strong,\r\n.k-slider .k-draghandle {\r\n  color: #999999;\r\n}\r\n/* Progressbar */\r\n.k-progressbar-indeterminate {\r\n  background: url('Material/indeterminate.gif');\r\n}\r\n.k-progressbar-indeterminate .k-progress-status-wrap,\r\n.k-progressbar-indeterminate .k-state-selected {\r\n  display: none;\r\n}\r\n/* Slider */\r\n.k-slider-track {\r\n  background-color: #e6e6e6;\r\n}\r\n.k-slider-selection {\r\n  background-color: #fff;\r\n}\r\n.k-slider-horizontal .k-tick {\r\n  background-image: url('Material/slider-h.gif');\r\n}\r\n.k-slider-vertical .k-tick {\r\n  background-image: url('Material/slider-v.gif');\r\n}\r\n/* Tooltip */\r\n.k-widget.k-tooltip {\r\n  border-color: rgba(100, 100, 100, 0.9);\r\n  background-color: rgba(100, 100, 100, 0.9);\r\n  color: #ffffff;\r\n}\r\n.k-widget.k-tooltip-validation {\r\n  border-color: #fdefba;\r\n  background-color: #fdefba;\r\n  color: #816704;\r\n}\r\n/* Bootstrap theme fix */\r\n.input-prepend .k-tooltip-validation,\r\n.input-append .k-tooltip-validation {\r\n  font-size: 12px;\r\n  position: relative;\r\n  top: 3px;\r\n}\r\n.k-callout-n {\r\n  border-bottom-color: rgba(100, 100, 100, 0.9);\r\n}\r\n.k-callout-w {\r\n  border-right-color: rgba(100, 100, 100, 0.9);\r\n}\r\n.k-callout-s {\r\n  border-top-color: rgba(100, 100, 100, 0.9);\r\n}\r\n.k-callout-e {\r\n  border-left-color: rgba(100, 100, 100, 0.9);\r\n}\r\n.k-tooltip-validation .k-callout-n {\r\n  border-bottom-color: #fdefba;\r\n}\r\n.k-tooltip-validation .k-callout-w {\r\n  border-right-color: #fdefba;\r\n}\r\n.k-tooltip-validation .k-callout-s {\r\n  border-top-color: #fdefba;\r\n}\r\n.k-tooltip-validation .k-callout-e {\r\n  border-left-color: #fdefba;\r\n}\r\n/* Splitter */\r\n.k-splitbar {\r\n  background-color: #fafafa;\r\n}\r\n.k-restricted-size-vertical,\r\n.k-restricted-size-horizontal {\r\n  background-color: #b94a48;\r\n}\r\n/* Upload */\r\n.k-file {\r\n  background-color: #fff;\r\n  border-color: #e6e6e6;\r\n}\r\n.k-file-progress {\r\n  color: #0d7fdd;\r\n}\r\n.k-file-progress .k-progress {\r\n  background-color: #bbdefb;\r\n}\r\n.k-file-success {\r\n  color: #479b49;\r\n}\r\n.k-file-success .k-progress {\r\n  background-color: #c8e6c9;\r\n}\r\n.k-file-error {\r\n  color: #ff011a;\r\n}\r\n.k-file-error .k-progress {\r\n  background-color: #ffcdd2;\r\n}\r\n/* ImageBrowser */\r\n.k-tile {\r\n  border-color: #fff;\r\n}\r\n.k-textbox:hover,\r\n.k-tiles li.k-state-hover {\r\n  border-color: #ebebeb;\r\n}\r\n.k-tiles li.k-state-selected {\r\n  border-color: #ffffff;\r\n}\r\n.k-filebrowser .k-tile .k-folder,\r\n.k-filebrowser .k-tile .k-file {\r\n  background-image: url('Material/imagebrowser.png');\r\n  -webkit-background-size: auto auto;\r\n          background-size: auto auto;\r\n}\r\n/* TreeMap */\r\n.k-leaf,\r\n.k-leaf.k-state-hover:hover {\r\n  color: #fff;\r\n}\r\n.k-leaf.k-inverse,\r\n.k-leaf.k-inverse.k-state-hover:hover {\r\n  color: #000;\r\n}\r\n/* Shadows */\r\n.k-widget,\r\n.k-button {\r\n  -webkit-box-shadow: none;\r\n          box-shadow: none;\r\n}\r\n.k-slider,\r\n.k-treeview,\r\n.k-upload {\r\n  -webkit-box-shadow: none;\r\n          box-shadow: none;\r\n}\r\n.k-state-hover {\r\n  -webkit-box-shadow: 0 1px 0 rgba(255, 255, 255, 0.2);\r\n          box-shadow: 0 1px 0 rgba(255, 255, 255, 0.2);\r\n}\r\n.k-textbox:focus,\r\n.k-autocomplete.k-state-focused,\r\n.k-dropdown-wrap.k-state-focused,\r\n.k-picker-wrap.k-state-focused,\r\n.k-numeric-wrap.k-state-focused {\r\n  -webkit-box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2), 0 2px 3px rgba(0, 0, 0, 0.05);\r\n          box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2), 0 2px 3px rgba(0, 0, 0, 0.05);\r\n}\r\n.k-state-selected {\r\n  -webkit-box-shadow: 0 1px 1px rgba(0, 0, 0, 0.075) inset;\r\n          box-shadow: 0 1px 1px rgba(0, 0, 0, 0.075) inset;\r\n}\r\n.k-state-active {\r\n  -webkit-box-shadow: none;\r\n          box-shadow: none;\r\n}\r\n.k-grid td.k-state-selected.k-state-focused {\r\n  background-color: #ffffff;\r\n}\r\n.k-popup,\r\n.k-menu .k-menu-group,\r\n.k-grid .k-filter-options,\r\n.k-time-popup,\r\n.k-datepicker-calendar,\r\n.k-autocomplete.k-state-border-down,\r\n.k-autocomplete.k-state-border-up,\r\n.k-dropdown-wrap.k-state-active,\r\n.k-picker-wrap.k-state-active,\r\n.k-multiselect.k-state-focused,\r\n.k-filebrowser .k-image,\r\n.k-tooltip {\r\n  -webkit-box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.2);\r\n          box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.2);\r\n}\r\n.k-calendar-container.k-popup {\r\n  -webkit-box-shadow: 0 0px 6px 1px rgba(0, 0, 0, 0.2);\r\n          box-shadow: 0 0px 6px 1px rgba(0, 0, 0, 0.2);\r\n}\r\n.k-treemap-tile.k-state-hover {\r\n  -webkit-box-shadow: inset 0 0 0 3px #e6e6e6;\r\n          box-shadow: inset 0 0 0 3px #e6e6e6;\r\n}\r\n/* Window */\r\n.k-window {\r\n  border-color: rgba(0, 0, 0, 0.2);\r\n  -webkit-box-shadow: 1px 1px 7px 1px rgba(128, 128, 128, 0.2);\r\n          box-shadow: 1px 1px 7px 1px rgba(128, 128, 128, 0.2);\r\n  background-color: #fff;\r\n}\r\n.k-window.k-state-focused {\r\n  border-color: rgba(0, 0, 0, 0.2);\r\n  -webkit-box-shadow: 1px 1px 7px 1px rgba(0, 0, 0, 0.2);\r\n          box-shadow: 1px 1px 7px 1px rgba(0, 0, 0, 0.2);\r\n}\r\n.k-window.k-window-maximized,\r\n.k-window-maximized .k-window-titlebar,\r\n.k-window-maximized .k-window-content {\r\n  border-radius: 0;\r\n}\r\n.k-shadow {\r\n  -webkit-box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.2);\r\n          box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.2);\r\n}\r\n.k-inset {\r\n  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.2);\r\n          box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.2);\r\n}\r\n/* Selection */\r\n.k-editor-inline ::-moz-selection {\r\n  background-color: #3f51b5;\r\n  text-shadow: none;\r\n  color: #fff;\r\n}\r\n.k-editor-inline ::selection {\r\n  background-color: #3f51b5;\r\n  text-shadow: none;\r\n  color: #fff;\r\n}\r\n.k-editor-inline ::-moz-selection {\r\n  background-color: #3f51b5;\r\n  text-shadow: none;\r\n  color: #fff;\r\n}\r\n/* Notification */\r\n.k-widget.k-notification.k-notification-info {\r\n  background-color: #bbdefb;\r\n  color: #2b98f3;\r\n  border-color: #bbdefb;\r\n}\r\n.k-widget.k-notification.k-notification-success {\r\n  background-color: #c8e6c9;\r\n  color: #5fb662;\r\n  border-color: #c8e6c9;\r\n}\r\n.k-widget.k-notification.k-notification-warning {\r\n  background-color: #fdefba;\r\n  color: #f9cd25;\r\n  border-color: #fdefba;\r\n}\r\n.k-widget.k-notification.k-notification-error {\r\n  background-color: #ffcdd2;\r\n  color: #ff3448;\r\n  border-color: #ffcdd2;\r\n}\r\n/* Gantt */\r\n.k-gantt .k-treelist {\r\n  background: #fafafa;\r\n}\r\n.k-gantt .k-treelist .k-alt {\r\n  background-color: #e0e0e0;\r\n}\r\n.k-gantt .k-treelist tr:hover {\r\n  background-color: #ebebeb;\r\n}\r\n.k-gantt .k-treelist .k-state-selected,\r\n.k-gantt .k-treelist .k-state-selected td,\r\n.k-gantt .k-treelist .k-alt.k-state-selected,\r\n.k-gantt .k-treelist .k-alt.k-state-selected > td {\r\n  background-color: #fff;\r\n}\r\n.k-gantt .k-treelist .k-alt.k-state-selected:hover,\r\n.k-gantt .k-treelist .k-alt.k-state-selected:hover td {\r\n  background-color: #00a2eb;\r\n}\r\n.k-task-dot:after {\r\n  background-color: #444444;\r\n  border-color: #444444;\r\n}\r\n.k-task-dot:hover:after {\r\n  background-color: #ffffff;\r\n}\r\n.k-task-summary {\r\n  border-color: #98a2db;\r\n  background: #98a2db;\r\n}\r\n.k-task-milestone,\r\n.k-task-summary-complete {\r\n  border-color: #444444;\r\n  background: #444444;\r\n}\r\n.k-state-selected.k-task-summary {\r\n  border-color: #98a2db;\r\n  background: #98a2db;\r\n}\r\n.k-state-selected.k-task-milestone,\r\n.k-state-selected .k-task-summary-complete {\r\n  border-color: #fff;\r\n  background: #fff;\r\n}\r\n.k-task-single {\r\n  background-color: #7a87d1;\r\n  border-color: #606fc7;\r\n  color: #ffffff;\r\n}\r\n.k-state-selected.k-task-single {\r\n  border-color: #ffffff;\r\n}\r\n.k-line {\r\n  background-color: #444444;\r\n  color: #444444;\r\n}\r\n.k-state-selected.k-line {\r\n  background-color: #fff;\r\n  color: #fff;\r\n}\r\n.k-resource {\r\n  background-color: #fff;\r\n}\r\n/* PivotGrid */\r\n.k-i-kpi-decrease,\r\n.k-i-kpi-denied,\r\n.k-i-kpi-equal,\r\n.k-i-kpi-hold,\r\n.k-i-kpi-increase,\r\n.k-i-kpi-open {\r\n  background-image: url('Material/sprite_kpi.png');\r\n}\r\n/* Border radius */\r\n.k-block,\r\n.k-button,\r\n.k-textbox,\r\n.k-drag-clue,\r\n.k-touch-scrollbar,\r\n.k-window,\r\n.k-window-titleless .k-window-content,\r\n.k-window-action,\r\n.k-inline-block,\r\n.k-grid .k-filter-options,\r\n.k-grouping-header .k-group-indicator,\r\n.k-autocomplete,\r\n.k-multiselect,\r\n.k-combobox,\r\n.k-dropdown,\r\n.k-dropdown-wrap,\r\n.k-datepicker,\r\n.k-timepicker,\r\n.k-colorpicker,\r\n.k-datetimepicker,\r\n.k-notification,\r\n.k-numerictextbox,\r\n.k-picker-wrap,\r\n.k-numeric-wrap,\r\n.k-colorpicker,\r\n.k-list-container,\r\n.k-calendar-container,\r\n.k-calendar td,\r\n.k-calendar .k-link,\r\n.k-treeview .k-in,\r\n.k-editor-inline,\r\n.k-tooltip,\r\n.k-tile,\r\n.k-slider-track,\r\n.k-slider-selection,\r\n.k-upload {\r\n  border-radius: 2px;\r\n}\r\n.k-tool {\r\n  text-align: center;\r\n  vertical-align: middle;\r\n}\r\n.k-toolbar .k-split-button .k-button {\r\n  border-radius: 2px 0 0 2px;\r\n}\r\n.k-rtl .k-tool.k-group-start,\r\n.k-rtl .k-toolbar .k-split-button .k-button,\r\n.k-rtl .k-toolbar .k-button-group .k-group-start {\r\n  border-radius: 0 2px 2px 0;\r\n}\r\n.k-toolbar .k-split-button .k-split-button-arrow {\r\n  border-radius: 0 2px 2px 0;\r\n}\r\n.k-rtl .k-tool.k-group-end,\r\n.k-rtl .k-toolbar .k-button-group .k-group-end,\r\n.k-rtl .k-toolbar .k-split-button .k-split-button-arrow {\r\n  border-radius: 2px 0 0 2px;\r\n}\r\n.k-calendar-container.k-state-border-up,\r\n.k-list-container.k-state-border-up,\r\n.k-autocomplete.k-state-border-up,\r\n.k-multiselect.k-state-border-up,\r\n.k-dropdown-wrap.k-state-border-up,\r\n.k-picker-wrap.k-state-border-up,\r\n.k-numeric-wrap.k-state-border-up,\r\n.k-window-content,\r\n.k-filter-menu {\r\n  border-radius: 0 0 2px 2px;\r\n}\r\n.k-autocomplete.k-state-border-up .k-input,\r\n.k-dropdown-wrap.k-state-border-up .k-input,\r\n.k-picker-wrap.k-state-border-up .k-input,\r\n.k-picker-wrap.k-state-border-up .k-selected-color,\r\n.k-numeric-wrap.k-state-border-up .k-input {\r\n  border-radius: 0 0 0 2px;\r\n}\r\n.k-multiselect.k-state-border-up .k-multiselect-wrap {\r\n  border-radius: 0 0 2px 2px;\r\n}\r\n.k-window-titlebar,\r\n.k-block > .k-header,\r\n.k-tabstrip-items .k-item,\r\n.k-panelbar .k-tabstrip-items .k-item,\r\n.k-tabstrip-items .k-link,\r\n.k-calendar-container.k-state-border-down,\r\n.k-list-container.k-state-border-down,\r\n.k-autocomplete.k-state-border-down,\r\n.k-multiselect.k-state-border-down,\r\n.k-dropdown-wrap.k-state-border-down,\r\n.k-picker-wrap.k-state-border-down,\r\n.k-numeric-wrap.k-state-border-down {\r\n  border-radius: 2px 2px 0 0;\r\n}\r\n.k-split-button.k-state-border-down > .k-button {\r\n  border-radius: 2px 0 0 0;\r\n}\r\n.k-split-button.k-state-border-up > .k-button {\r\n  border-radius: 0 0 0 2px;\r\n}\r\n.k-split-button.k-state-border-down > .k-split-button-arrow {\r\n  border-radius: 0 2px 0 0;\r\n}\r\n.k-split-button.k-state-border-up > .k-split-button-arrow {\r\n  border-radius: 0 0 2px 0;\r\n}\r\n.k-dropdown-wrap .k-input,\r\n.k-picker-wrap .k-input,\r\n.k-numeric-wrap .k-input {\r\n  border-radius: 1px 0 0 1px;\r\n}\r\n.k-rtl .k-dropdown-wrap .k-input,\r\n.k-rtl .k-picker-wrap .k-input,\r\n.k-rtl .k-numeric-wrap .k-input {\r\n  border-radius: 0 1px 1px 0;\r\n}\r\n.k-numeric-wrap .k-link {\r\n  border-radius: 0 1px 0 0;\r\n}\r\n.k-numeric-wrap .k-link + .k-link {\r\n  border-radius: 0 0 1px 0;\r\n}\r\n.k-colorpicker .k-selected-color {\r\n  border-radius: 1px 0 0 1px;\r\n}\r\n.k-rtl .k-colorpicker .k-selected-color {\r\n  border-radius: 0 1px 1px 0;\r\n}\r\n.k-autocomplete.k-state-border-down .k-input {\r\n  border-radius: 2px 2px 0 0;\r\n}\r\n.k-dropdown-wrap.k-state-border-down .k-input,\r\n.k-picker-wrap.k-state-border-down .k-input,\r\n.k-picker-wrap.k-state-border-down .k-selected-color,\r\n.k-numeric-wrap.k-state-border-down .k-input {\r\n  border-radius: 2px 0 0 0;\r\n}\r\n.k-numeric-wrap .k-link.k-state-selected {\r\n  background-color: #ebebeb;\r\n}\r\n.k-multiselect.k-state-border-down .k-multiselect-wrap {\r\n  border-radius: 1px 1px 0 0;\r\n}\r\n.k-dropdown-wrap .k-select,\r\n.k-picker-wrap .k-select,\r\n.k-numeric-wrap .k-select,\r\n.k-datetimepicker .k-select + .k-select,\r\n.k-list-container.k-state-border-right {\r\n  border-radius: 0 2px 2px 0;\r\n}\r\n.k-rtl .k-dropdown-wrap .k-select,\r\n.k-rtl .k-picker-wrap .k-select,\r\n.k-rtl .k-numeric-wrap .k-select,\r\n.k-rtl .k-datetimepicker .k-select + .k-select,\r\n.k-rtl .k-list-container.k-state-border-right {\r\n  border-radius: 2px 0 0 2px;\r\n}\r\n.k-numeric-wrap.k-expand-padding .k-input {\r\n  border-radius: 2px;\r\n}\r\n.k-textbox > input,\r\n.k-autocomplete .k-input,\r\n.k-multiselect-wrap {\r\n  border-radius: 1px;\r\n}\r\n.k-list .k-state-hover,\r\n.k-list .k-state-focused,\r\n.k-list .k-state-highlight,\r\n.k-list .k-state-selected,\r\n.k-fieldselector .k-list .k-item,\r\n.k-list-optionlabel,\r\n.k-dropzone {\r\n  border-radius: 1px;\r\n}\r\n.k-slider .k-button,\r\n.k-grid .k-slider .k-button {\r\n  border-radius: 13px;\r\n}\r\n.k-draghandle {\r\n  border-radius: 13px;\r\n}\r\n.k-scheduler-toolbar > ul li:first-child,\r\n.k-scheduler-toolbar > ul li:first-child .k-link,\r\n.k-scheduler-toolbar > ul.k-scheduler-views li:first-child + li,\r\n.k-scheduler-toolbar > ul.k-scheduler-views li:first-child + li .k-link {\r\n  border-radius: 2px 0 0 2px;\r\n}\r\n.k-rtl .k-scheduler-toolbar > ul li:first-child,\r\n.k-rtl .k-scheduler-toolbar > ul li:first-child .k-link,\r\n.k-rtl .k-scheduler-toolbar > ul.k-scheduler-views li:first-child + li,\r\n.k-rtl .k-scheduler-toolbar > ul.k-scheduler-views li:first-child + li .k-link,\r\n.km-view.k-popup-edit-form .k-scheduler-toolbar > ul li:last-child,\r\n.km-view.k-popup-edit-form .k-scheduler-toolbar > ul li:last-child .k-link {\r\n  border-radius: 0 2px 2px 0;\r\n}\r\n.k-scheduler-phone .k-scheduler-toolbar > ul li.k-nav-today,\r\n.k-scheduler-phone .k-scheduler-toolbar > ul li.k-nav-today .k-link,\r\n.k-edit-field > .k-scheduler-navigation {\r\n  border-radius: 2px;\r\n}\r\n.k-scheduler-toolbar .k-nav-next,\r\n.k-scheduler-toolbar ul + ul li:last-child,\r\n.k-scheduler-toolbar .k-nav-next .k-link,\r\n.k-scheduler-toolbar ul + ul li:last-child .k-link {\r\n  border-top-right-radius: 2px;\r\n  border-bottom-right-radius: 2px;\r\n}\r\n.k-rtl .k-scheduler-toolbar .k-nav-next,\r\n.k-rtl .k-scheduler-toolbar ul + ul li:last-child,\r\n.k-rtl .k-scheduler-toolbar .k-nav-next .k-link,\r\n.k-rtl .k-scheduler-toolbar ul + ul li:last-child .k-link {\r\n  border-radius: 2px 0 0 2px;\r\n}\r\n.k-scheduler div.k-scheduler-footer ul li,\r\n.k-scheduler div.k-scheduler-footer .k-link {\r\n  border-radius: 2px;\r\n}\r\n.k-more-events,\r\n.k-event,\r\n.k-task-single,\r\n.k-task-complete,\r\n.k-event .k-link {\r\n  border-radius: 1px;\r\n}\r\n.k-scheduler-mobile .k-event {\r\n  border-radius: 0px;\r\n}\r\n/* Adaptive Grid */\r\n.k-grid-mobile .k-column-active + th.k-header {\r\n  border-left-color: #444444;\r\n}\r\nhtml .km-pane-wrapper .km-widget,\r\n.k-ie .km-pane-wrapper .k-widget,\r\n.k-ie .km-pane-wrapper .k-group,\r\n.k-ie .km-pane-wrapper .k-content,\r\n.k-ie .km-pane-wrapper .k-header,\r\n.k-ie .km-pane-wrapper .k-popup-edit-form .k-edit-field .k-button,\r\n.km-pane-wrapper .k-mobile-list .k-item,\r\n.km-pane-wrapper .k-mobile-list .k-edit-label,\r\n.km-pane-wrapper .k-mobile-list .k-edit-field {\r\n  color: #444444;\r\n}\r\n@media screen and (-ms-high-contrast: active) and (-ms-high-contrast: none) {\r\n  div.km-pane-wrapper a {\r\n    color: #444444;\r\n  }\r\n}\r\n.km-pane-wrapper .k-mobile-list .k-item,\r\n.km-pane-wrapper .k-mobile-list .k-edit-field,\r\n.km-pane-wrapper .k-mobile-list .k-recur-view > .k-edit-field .k-check {\r\n  background-color: #fff;\r\n  border-top: 1px solid #e7e7e7;\r\n}\r\n.km-pane-wrapper .k-mobile-list .k-edit-field textarea {\r\n  outline-width: 0;\r\n}\r\n.km-pane-wrapper .k-mobile-list .k-item.k-state-selected {\r\n  background-color: #fff;\r\n  border-top-color: #ffffff;\r\n}\r\n.km-pane-wrapper .k-mobile-list .k-recur-view > .k-edit-field .k-check:first-child {\r\n  border-top-color: transparent;\r\n}\r\n.km-pane-wrapper .k-mobile-list .k-item:last-child {\r\n  -webkit-box-shadow: inset 0 -1px 0 #e7e7e7;\r\n          box-shadow: inset 0 -1px 0 #e7e7e7;\r\n}\r\n.km-pane-wrapper .k-mobile-list > ul > li > .k-link,\r\n.km-pane-wrapper .k-mobile-list .k-recur-view > .k-edit-label:nth-child(3),\r\n.km-pane-wrapper #recurrence .km-scroll-container > .k-edit-label:first-child {\r\n  color: #9b9b9b;\r\n}\r\n.km-pane-wrapper .k-mobile-list > ul > li > .k-link {\r\n  border-bottom: 1px solid #e7e7e7;\r\n}\r\n.km-pane-wrapper .k-mobile-list .k-edit-field {\r\n  -webkit-box-shadow: 0 1px 1px #e7e7e7;\r\n          box-shadow: 0 1px 1px #e7e7e7;\r\n}\r\n.km-actionsheet .k-grid-delete,\r\n.km-actionsheet .k-scheduler-delete,\r\n.km-pane-wrapper .k-scheduler-delete,\r\n.km-pane-wrapper .k-filter-menu .k-button[type=reset] {\r\n  color: #fff;\r\n  border-color: #eed3d7;\r\n  background-color: red;\r\n  background-image: -webkit-gradient(linear, left top, left bottom, from(rgba(255, 255, 255, 0.3)), to(rgba(255, 255, 255, 0.15)));\r\n  background-image: -webkit-linear-gradient(top, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.15));\r\n  background-image: linear-gradient(to bottom, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.15));\r\n}\r\n.km-actionsheet .k-grid-delete:active,\r\n.km-actionsheet .k-scheduler-delete:active,\r\n.km-pane-wrapper .k-scheduler-delete:active,\r\n.km-pane-wrapper .k-filter-menu .k-button[type=reset]:active {\r\n  background-color: #990000;\r\n}\r\n/* /Column Menu */\r\n.k-autocomplete.k-state-default,\r\n.k-picker-wrap.k-state-default,\r\n.k-numeric-wrap.k-state-default,\r\n.k-dropdown-wrap.k-state-default {\r\n  background-image: none;\r\n  background-position: 50% 50%;\r\n  background-color: #fafafa;\r\n  border-color: #f0f0f0;\r\n}\r\n.k-autocomplete.k-state-hover,\r\n.k-picker-wrap.k-state-hover,\r\n.k-numeric-wrap.k-state-hover,\r\n.k-dropdown-wrap.k-state-hover {\r\n  background-color: #ffffff;\r\n  background-image: none;\r\n  background-position: 50% 50%;\r\n  border-color: #f5f5f5;\r\n}\r\n.k-multiselect.k-header {\r\n  border-color: #f0f0f0;\r\n}\r\n.k-multiselect.k-header.k-state-hover {\r\n  border-color: #f5f5f5;\r\n}\r\n.k-autocomplete.k-state-focused,\r\n.k-picker-wrap.k-state-focused,\r\n.k-numeric-wrap.k-state-focused,\r\n.k-dropdown-wrap.k-state-focused,\r\n.k-multiselect.k-header.k-state-focused {\r\n  background-color: #ffffff;\r\n  background-image: none;\r\n  background-position: 50% 50%;\r\n  border-color: #f5f5f5;\r\n  -webkit-box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2), 0 2px 3px rgba(0, 0, 0, 0.05);\r\n          box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2), 0 2px 3px rgba(0, 0, 0, 0.05);\r\n}\r\n.k-list-container {\r\n  color: #444444;\r\n}\r\n.k-dropdown .k-input,\r\n.k-dropdown .k-state-focused .k-input,\r\n.k-menu .k-popup {\r\n  color: #444444;\r\n}\r\n.k-state-default > .k-select {\r\n  border-color: #f0f0f0;\r\n}\r\n.k-state-focused > .k-select {\r\n  border-color: #f5f5f5;\r\n}\r\n.k-state-hover > .k-select {\r\n  border-color: #f5f5f5;\r\n}\r\n.k-tabstrip:focus {\r\n  -webkit-box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2), 0 2px 3px rgba(0, 0, 0, 0.05);\r\n          box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2), 0 2px 3px rgba(0, 0, 0, 0.05);\r\n}\r\n.k-tabstrip-items .k-state-default .k-link,\r\n.k-panelbar > li.k-state-default > .k-link {\r\n  color: #ffffff;\r\n}\r\n.k-tabstrip-items .k-state-hover .k-link,\r\n.k-panelbar > li.k-state-hover > .k-link,\r\n.k-panelbar > li.k-state-default > .k-link.k-state-hover {\r\n  color: #444444;\r\n}\r\n.k-panelbar > li > .k-state-focused.k-state-hover {\r\n  background: #ebebeb;\r\n  -webkit-box-shadow: none;\r\n          box-shadow: none;\r\n}\r\n.k-tabstrip-items .k-state-default,\r\n.k-panelbar .k-tabstrip-items .k-state-default {\r\n  border-color: transparent;\r\n}\r\n.k-tabstrip-items .k-state-hover {\r\n  border-color: #ebebeb;\r\n}\r\n.k-tabstrip .k-content.k-state-active {\r\n  background-color: #fff;\r\n  color: #444444;\r\n}\r\n.k-menu.k-header,\r\n.k-menu .k-item {\r\n  border-color: #e6e6e6;\r\n}\r\n.k-column-menu,\r\n.k-column-menu .k-item,\r\n.k-overflow-container .k-overflow-group {\r\n  border-color: #cccccc;\r\n}\r\n.k-overflow-container .k-overflow-group {\r\n  -webkit-box-shadow: inset 0 1px 0 #ffffff, 0 1px 0 #ffffff;\r\n          box-shadow: inset 0 1px 0 #ffffff, 0 1px 0 #ffffff;\r\n}\r\n.k-toolbar-first-visible.k-overflow-group,\r\n.k-overflow-container .k-overflow-group + .k-overflow-group {\r\n  -webkit-box-shadow: 0 1px 0 #ffffff;\r\n          box-shadow: 0 1px 0 #ffffff;\r\n}\r\n.k-toolbar-last-visible.k-overflow-group {\r\n  -webkit-box-shadow: inset 0 1px 0 #ffffff;\r\n          box-shadow: inset 0 1px 0 #ffffff;\r\n}\r\n.k-column-menu .k-separator {\r\n  border-color: #cccccc;\r\n  background-color: transparent;\r\n}\r\n.k-menu .k-group {\r\n  border-color: rgba(0, 0, 0, 0.2);\r\n}\r\n.k-grid-filter.k-state-active {\r\n  background-color: #ffffff;\r\n}\r\n.k-grouping-row td,\r\n.k-group-footer td,\r\n.k-grid-footer td {\r\n  color: #ffffff;\r\n  border-color: #cccccc;\r\n  font-weight: bold;\r\n}\r\n.k-grouping-header {\r\n  color: #ffffff;\r\n}\r\n.k-header,\r\n.k-grid-header-wrap,\r\n.k-grid .k-grouping-header,\r\n.k-grid-header,\r\n.k-pager-wrap,\r\n.k-pager-wrap .k-textbox,\r\n.k-pager-wrap .k-link,\r\n.k-grouping-header .k-group-indicator,\r\n.k-gantt-toolbar .k-state-default {\r\n  border-color: #cccccc;\r\n}\r\n.k-primary,\r\n.k-overflow-container .k-primary {\r\n  color: #ffffff;\r\n  border-color: #3f51b5;\r\n  background-image: none;\r\n  background-position: 50% 50%;\r\n  background-color: #3f51b5;\r\n  -webkit-box-shadow: none;\r\n          box-shadow: none;\r\n}\r\n.k-primary:focus,\r\n.k-primary.k-state-focused {\r\n  color: #ffffff;\r\n  border-color: #eff8ff;\r\n  background-image: none;\r\n  -webkit-box-shadow: 0 0 8px 0 #cfe6f8;\r\n          box-shadow: 0 0 8px 0 #cfe6f8;\r\n}\r\n.k-primary:hover {\r\n  color: #ffffff;\r\n  border-color: #5c6bc0;\r\n  background-image: none;\r\n  background-color: #5c6bc0;\r\n  -webkit-box-shadow: none;\r\n          box-shadow: none;\r\n}\r\n.k-primary:focus:active:not(.k-state-disabled):not([disabled]),\r\n.k-primary:focus:not(.k-state-disabled):not([disabled]) {\r\n  -webkit-box-shadow: 0 0 8px 0 #cfe6f8;\r\n          box-shadow: 0 0 8px 0 #cfe6f8;\r\n}\r\n.k-primary:active {\r\n  color: #ffffff;\r\n  border-color: #283593;\r\n  background-image: none;\r\n  background-color: #283593;\r\n  -webkit-box-shadow: 0 6px 17px 0 rgba(0, 0, 0, 0.3);\r\n          box-shadow: 0 6px 17px 0 rgba(0, 0, 0, 0.3);\r\n}\r\n.k-primary.k-state-disabled,\r\n.k-state-disabled .k-primary,\r\n.k-primary.k-state-disabled:hover,\r\n.k-state-disabled .k-primary:hover,\r\n.k-primary.k-state-disabled:hover,\r\n.k-state-disabled .k-primary:active,\r\n.k-primary.k-state-disabled:active {\r\n  color: #a8a8a8;\r\n  border-color: #eaeaea;\r\n  background-color: #eaeaea;\r\n  background-image: none;\r\n  -webkit-box-shadow: none;\r\n          box-shadow: none;\r\n}\r\n.k-pager-numbers .k-link,\r\n.k-treeview .k-in {\r\n  border-color: transparent;\r\n}\r\n.k-treeview .k-icon,\r\n.k-scheduler-table .k-icon,\r\n.k-grid .k-hierarchy-cell .k-icon {\r\n  background-color: transparent;\r\n  border-radius: 50%;\r\n}\r\n.k-scheduler-table .k-state-hover .k-icon {\r\n  background-color: transparent;\r\n}\r\n.k-button:focus,\r\n.k-split-button:focus {\r\n  outline: none;\r\n}\r\n.k-split-button:focus {\r\n  background-color: #dbdbdb;\r\n}\r\n.k-split-button:focus > .k-button {\r\n  background: transparent;\r\n  border-color: #dbdbdb;\r\n}\r\n.k-split-button:focus > .k-button.k-split-button-arrow {\r\n  border-left-color: #fafafa;\r\n}\r\n.k-editor .k-tool:focus {\r\n  outline: 0;\r\n  border-color: #dbdbdb;\r\n  -webkit-box-shadow: 0 6px 17px 0 #c4c4c4;\r\n          box-shadow: 0 6px 17px 0 #c4c4c4;\r\n}\r\n.k-checkbox-label:before {\r\n  border-color: #7f7f7f;\r\n  background: #fff;\r\n  border-radius: 1px;\r\n}\r\n.k-checkbox-label:hover:before,\r\n.k-checkbox:checked + .k-checkbox-label:hover:before {\r\n  border-color: #7f7f7f;\r\n  -webkit-box-shadow: none;\r\n          box-shadow: none;\r\n}\r\n.k-checkbox:checked + .k-checkbox-label:before {\r\n  background-color: #3f51b5;\r\n  border-color: #3f51b5;\r\n  color: #ffffff;\r\n}\r\n.k-checkbox-label:active:before {\r\n  -webkit-box-shadow: none;\r\n          box-shadow: none;\r\n  border-color: #7f7f7f;\r\n}\r\n.k-checkbox:checked + .k-checkbox-label:active:before {\r\n  -webkit-box-shadow: none;\r\n          box-shadow: none;\r\n  border-color: #7f7f7f;\r\n}\r\n.k-checkbox:disabled + .k-checkbox-label {\r\n  color: #999999;\r\n}\r\n.k-checkbox:disabled + .k-checkbox-label:hover:before {\r\n  -webkit-box-shadow: none;\r\n          box-shadow: none;\r\n}\r\n.k-checkbox:disabled + .k-checkbox-label:before,\r\n.k-checkbox:checked:disabled + .k-checkbox-label:before,\r\n.k-checkbox:checked:disabled + .k-checkbox-label:active:before,\r\n.k-checkbox:checked:disabled + .k-checkbox-label:hover:before {\r\n  color: #999999;\r\n  background: #f5f5f5;\r\n  border-color: #bfbfbf;\r\n  border-radius: 1px;\r\n}\r\n.k-checkbox:focus + .k-checkbox-label:before {\r\n  border-color: #7f7f7f;\r\n  -webkit-box-shadow: none;\r\n          box-shadow: none;\r\n}\r\n.k-checkbox:indeterminate + .k-checkbox-label:after {\r\n  background-color: #3f51b5;\r\n  background-image: none;\r\n  border-color: #3f51b5;\r\n  border-radius: 0px;\r\n}\r\n.k-checkbox:indeterminate:hover + .k-checkbox-label:after {\r\n  border-color: #3f51b5;\r\n  background-color: #3f51b5;\r\n}\r\n.k-checkbox + .k-checkbox-label:after {\r\n  content: \"\";\r\n  position: absolute;\r\n  top: 1px;\r\n  left: 1px;\r\n  border-radius: 50%;\r\n  width: 1em;\r\n  height: 1em;\r\n}\r\n.k-checkbox:focus + .k-checkbox-label:after {\r\n  -webkit-box-shadow: 0 0 0 12px rgba(235, 235, 235, 0.3);\r\n          box-shadow: 0 0 0 12px rgba(235, 235, 235, 0.3);\r\n}\r\n.k-checkbox + .k-checkbox-label:active:after {\r\n  -webkit-box-shadow: 0 0 0 12px rgba(235, 235, 235, 0.3);\r\n          box-shadow: 0 0 0 12px rgba(235, 235, 235, 0.3);\r\n}\r\n.k-checkbox:checked + .k-checkbox-label:active:after {\r\n  -webkit-box-shadow: 0 0 0 12px rgba(63, 81, 181, 0.3);\r\n          box-shadow: 0 0 0 12px rgba(63, 81, 181, 0.3);\r\n}\r\n.k-checkbox:disabled + .k-checkbox-label:active:after {\r\n  -webkit-box-shadow: none;\r\n          box-shadow: none;\r\n}\r\n.k-checkbox:indeterminate + .k-checkbox-label:before {\r\n  border-color: #3f51b5;\r\n}\r\n.k-radio-label:before {\r\n  border-color: #7f7f7f;\r\n  border-radius: 50%;\r\n  background-color: #fff;\r\n  border-width: 2px;\r\n}\r\n.k-radio-label:hover:before,\r\n.k-radio:checked + .k-radio-label:hover:before {\r\n  border-color: #7f7f7f;\r\n  -webkit-box-shadow: none;\r\n          box-shadow: none;\r\n}\r\n.k-radio:checked + .k-radio-label:after {\r\n  background-color: #3f51b5;\r\n  border-radius: 50%;\r\n}\r\n.k-radio-label:active:before {\r\n  border-color: #6b7acb;\r\n  -webkit-box-shadow: 0 0 2px 0 #6b7acb;\r\n          box-shadow: 0 0 2px 0 #6b7acb;\r\n}\r\n.k-radio:checked + .k-radio-label:active:before {\r\n  -webkit-box-shadow: 0 0 2px 0 #6b7acb;\r\n          box-shadow: 0 0 2px 0 #6b7acb;\r\n  border-color: #6b7acb;\r\n}\r\n.k-radio:disabled + .k-radio-label {\r\n  color: #bfbfbf;\r\n}\r\n.k-radio:disabled + .k-radio-label:before,\r\n.k-radio:disabled + .k-radio-label:active:before,\r\n.k-radio:disabled + .k-radio-label:hover:after,\r\n.k-radio:disabled + .k-radio-label:hover:before {\r\n  background: #ffffff;\r\n  border-color: #bfbfbf;\r\n  -webkit-box-shadow: none;\r\n          box-shadow: none;\r\n}\r\n.k-radio:disabled:checked + .k-radio-label:after {\r\n  background-color: #3f51b5;\r\n  opacity: .5;\r\n}\r\n.k-radio:focus + .k-radio-label:before {\r\n  border-color: #6b7acb;\r\n  -webkit-box-shadow: 0 0 2px 0 #6b7acb;\r\n          box-shadow: 0 0 2px 0 #6b7acb;\r\n}\r\n.k-radio:checked + .k-radio-label:before,\r\n.k-radio:checked + .k-radio-label:hover:before {\r\n  border-color: #3f51b5;\r\n}\r\n.k-radio + .k-radio-label:active:before {\r\n  border-color: #7f7f7f;\r\n  -webkit-box-shadow: 0 0 0 12px rgba(235, 235, 235, 0.3);\r\n          box-shadow: 0 0 0 12px rgba(235, 235, 235, 0.3);\r\n}\r\n.k-radio:checked + .k-radio-label:active:before {\r\n  -webkit-box-shadow: 0 0 0 12px rgba(63, 81, 181, 0.3);\r\n          box-shadow: 0 0 0 12px rgba(63, 81, 181, 0.3);\r\n}\r\n.k-radio:focus + .k-radio-label:before {\r\n  border-color: #7f7f7f;\r\n  -webkit-box-shadow: 0 0 0 12px rgba(235, 235, 235, 0.3);\r\n          box-shadow: 0 0 0 12px rgba(235, 235, 235, 0.3);\r\n}\r\n.k-radio:disabled:checked + .k-radio-label:before,\r\n.k-radio:disabled:checked + .k-radio-label:hover:before {\r\n  border-color: #bfbfbf;\r\n}\r\n.k-radio:disabled:checked + .k-radio-label:active:before {\r\n  -webkit-box-shadow: none;\r\n          box-shadow: none;\r\n}\r\n@media only screen and (-webkit-min-device-pixel-ratio: 1.2), only screen and (min-device-pixel-ratio: 1.2) {\r\n  .k-icon:not(.k-loading),\r\n  .k-grouping-dropclue,\r\n  .k-drop-hint,\r\n  .k-callout,\r\n  .k-tool-icon,\r\n  .k-state-hover .k-tool-icon,\r\n  .k-state-active .k-tool-icon,\r\n  .k-state-active.k-state-hover .k-tool-icon,\r\n  .k-state-selected .k-tool-icon,\r\n  .k-state-selected.k-state-hover .k-tool-icon,\r\n  .k-column-menu .k-sprite,\r\n  .k-mobile-list .k-check:checked,\r\n  .k-mobile-list .k-edit-field [type=checkbox]:checked,\r\n  .k-mobile-list .k-edit-field [type=radio]:checked {\r\n    background-image: url('Material/sprite_2x.png');\r\n    -webkit-background-size: 340px 336px;\r\n            background-size: 340px 336px;\r\n  }\r\n  .k-dropdown-wrap .k-input,\r\n  .k-picker-wrap .k-input,\r\n  .k-numeric-wrap .k-input {\r\n    border-radius: 1px 0 0 1px;\r\n  }\r\n  .k-i-kpi-decrease,\r\n  .k-i-kpi-denied,\r\n  .k-i-kpi-equal,\r\n  .k-i-kpi-hold,\r\n  .k-i-kpi-increase,\r\n  .k-i-kpi-open {\r\n    background-image: url('Material/sprite_kpi_2x.png');\r\n    -webkit-background-size: 96px 16px;\r\n            background-size: 96px 16px;\r\n  }\r\n}\r\n@media screen and (-ms-high-contrast: active) {\r\n  .k-editor-toolbar-wrap .k-dropdown-wrap.k-state-focused,\r\n  .k-editor-toolbar-wrap .k-button-group .k-tool:focus {\r\n    border-color: #fff;\r\n  }\r\n}\r\n.k-button:hover .k-icon,\r\n.k-tool-icon:hover,\r\n.k-state-hover .k-tool-icon,\r\n.k-state-selected .k-tool-icon,\r\n.k-state-focused .k-tool-icon,\r\n.k-button:hover .k-tool-icon,\r\n.k-splitbar.k-splitbar-horizontal-hover .k-icon,\r\n.k-splitbar.k-splitbar-vertical-hover .k-icon,\r\ndiv.k-splitbar.k-state-focused .k-icon,\r\n.k-textbox:hover > .k-icon,\r\n.k-grouping-header .k-group-delete,\r\n.k-grouping-header .k-button-icon:hover > .k-icon.k-group-delete,\r\n.k-grouping-header .k-si-arrow-n,\r\n.k-grouping-header .k-link:hover > .k-icon.k-si-arrow-n,\r\n.k-grouping-header .k-si-arrow-s,\r\n.k-grouping-header .k-link:hover > .k-icon.k-si-arrow-s,\r\n.k-grid-toolbar .k-i-pdf,\r\n.k-grid-toolbar .k-button:hover > .k-i-pdf,\r\n.k-grid-toolbar .k-i-excel,\r\n.k-grid-toolbar .k-button:hover > .k-i-excel,\r\n.k-grid-toolbar .k-icon,\r\n.k-scheduler-toolbar .k-icon,\r\n.k-scheduler-footer .k-icon,\r\n.k-scheduler-content .k-icon,\r\n.k-gantt-toolbar .k-icon,\r\n.k-field-actions .k-icon,\r\n.k-notification .k-icon,\r\n.k-pivot-configurator-settings .k-icon:hover,\r\n.k-window-titlebar .k-icon {\r\n  opacity: 1;\r\n}\r\n.k-tool-icon,\r\n.k-splitbar .k-icon,\r\n.k-pivot-configurator-settings .k-icon {\r\n  opacity: 0.7;\r\n}\r\n.k-pager-wrap .k-link.k-state-disabled .k-icon {\r\n  opacity: 0.25;\r\n}\r\n.k-button,\r\n.k-button:hover,\r\n.k-button.k-state-hover,\r\n.k-button.k-state-focused,\r\n.k-button:focus,\r\n.k-button:focus:not(.k-state-disabled):not([disabled]) {\r\n  -webkit-box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2), 0 2px 3px rgba(0, 0, 0, 0.05);\r\n          box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2), 0 2px 3px rgba(0, 0, 0, 0.05);\r\n}\r\n.k-button,\r\n.k-header {\r\n  font-weight: 500;\r\n}\r\n.k-button:active,\r\n.k-button.k-state-active,\r\n.k-button:focus,\r\n.k-button.k-state-focused {\r\n  color: #444444;\r\n  background-color: #dbdbdb;\r\n  border-color: #dbdbdb;\r\n}\r\n.k-button:active:hover,\r\n.k-button.k-state-active:hover {\r\n  color: #444444;\r\n  border-color: #dbdbdb;\r\n  background-color: #dbdbdb;\r\n  -webkit-box-shadow: 0 6px 17px 0 rgba(235, 235, 235, 0.3);\r\n          box-shadow: 0 6px 17px 0 rgba(235, 235, 235, 0.3);\r\n}\r\n.k-button:hover,\r\n.k-button.k-state-hover,\r\n.k-button:active:hover,\r\n.k-button.k-state-active:hover {\r\n  color: #444444;\r\n  border-color: #ebebeb;\r\n  background-color: #ebebeb;\r\n}\r\n.k-primary:active,\r\n.k-primary.k-state-active,\r\n.k-primary:focus,\r\n.k-primary.k-state-focused {\r\n  color: #ffffff;\r\n  border-color: #283593;\r\n  background-image: none;\r\n  background-color: #283593;\r\n  -webkit-box-shadow: 0 6px 17px 0 rgba(0, 0, 0, 0.3);\r\n          box-shadow: 0 6px 17px 0 rgba(0, 0, 0, 0.3);\r\n}\r\n.k-primary:hover,\r\n.k-primary.k-state-hover,\r\n.k-primary:active:hover,\r\n.k-primary.k-state-active:hover {\r\n  color: #ffffff;\r\n  border-color: #5c6bc0;\r\n  background-color: #5c6bc0;\r\n}\r\n.k-primary:focus:not(.k-state-disabled):not([disabled]),\r\n.k-primary:focus:active:not(.k-state-disabled):not([disabled]) {\r\n  -webkit-box-shadow: 0 6px 17px 0 rgba(0, 0, 0, 0.3);\r\n          box-shadow: 0 6px 17px 0 rgba(0, 0, 0, 0.3);\r\n}\r\n.k-primary.k-state-disabled,\r\n.k-state-disabled .k-primary,\r\n.k-primary.k-state-disabled:hover,\r\n.k-state-disabled .k-primary:hover,\r\n.k-primary.k-state-disabled:hover,\r\n.k-state-disabled .k-primary:active,\r\n.k-primary.k-state-disabled:active {\r\n  color: #a8a8a8;\r\n  border-color: #eaeaea;\r\n  background-color: #eaeaea;\r\n  background-image: none;\r\n  -webkit-box-shadow: none;\r\n          box-shadow: none;\r\n}\r\n.k-widget .k-button:active,\r\n.k-widget .k-button.k-state-active {\r\n  color: #444444;\r\n  background-color: #dbdbdb;\r\n  border-color: #ebebeb;\r\n}\r\n.k-toolbar .k-overflow-anchor.k-state-active,\r\n.k-toolbar .k-overflow-anchor.k-state-border-down {\r\n  background-color: #ffffff;\r\n}\r\n.k-widget .k-button:active:hover,\r\n.k-widget .k-button.k-state-active:hover {\r\n  color: #444444;\r\n  border-color: #ebebeb;\r\n  background-color: #ebebeb;\r\n}\r\n.k-button[disabled],\r\n.k-button.k-state-disabled,\r\n.k-state-disabled .k-button,\r\n.k-state-disabled .k-button:hover,\r\n.k-button.k-state-disabled:hover,\r\n.k-state-disabled .k-button:active,\r\n.k-button.k-state-disabled:active,\r\n.k-button.k-state-disabled:active:hover {\r\n  color: #999999;\r\n  border-color: #fafafa;\r\n  background-color: #fafafa;\r\n  background-image: none;\r\n  -webkit-box-shadow: none;\r\n          box-shadow: none;\r\n}\r\n.k-dropdown .k-state-default {\r\n  border-color: #fafafa;\r\n  background-image: none;\r\n  background-position: 50% 50%;\r\n  background-color: #fafafa;\r\n}\r\n.k-dropdown,\r\nspan.k-colorpicker {\r\n  background-color: #fafafa;\r\n}\r\n.k-textbox {\r\n  background-color: #fafafa;\r\n  border-color: #f0f0f0;\r\n}\r\n.k-combobox,\r\n.k-datepicker,\r\n.k-timepicker,\r\n.k-datetimepicker {\r\n  background-color: #fafafa;\r\n}\r\n.k-picker-wrap.k-state-default > .k-select {\r\n  border-color: #fafafa;\r\n}\r\n.k-datepicker .k-input,\r\n.k-timepicker .k-input {\r\n  background-color: #fafafa;\r\n}\r\n.k-autocomplete.k-state-active .k-input,\r\n.k-picker-wrap.k-state-active .k-input,\r\n.k-numeric-wrap.k-state-active .k-input {\r\n  background-color: #fff;\r\n}\r\n.k-picker-wrap.k-state-hover > .k-select,\r\n.k-picker-wrap.k-state-focused > .k-select {\r\n  border-color: #ffffff;\r\n}\r\n.k-picker-wrap.k-state-hover .k-input,\r\n.k-picker-wrap.k-state-focused .k-input {\r\n  background-color: #ffffff;\r\n}\r\n.k-textbox:hover,\r\n.k-overflow-anchor:hover,\r\n.k-autocomplete.k-state-hover,\r\n.k-picker-wrap.k-state-hover,\r\n.k-numeric-wrap.k-state-hover,\r\n.k-dropdown-wrap.k-state-hover {\r\n  background-color: #fff;\r\n  border-color: #f5f5f5;\r\n  -webkit-box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2), 0 2px 3px rgba(0, 0, 0, 0.05);\r\n          box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2), 0 2px 3px rgba(0, 0, 0, 0.05);\r\n}\r\n.k-textbox:focus,\r\n.k-autocomplete.k-state-focused,\r\n.k-picker-wrap.k-state-focused,\r\n.k-numeric-wrap.k-state-focused,\r\n.k-dropdown-wrap.k-state-focused,\r\n.k-multiselect.k-header.k-state-focused {\r\n  background-color: #ebebeb;\r\n  background-image: none;\r\n  background-position: 50% 50%;\r\n  border-color: #ebebeb;\r\n  -webkit-box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2), 0 2px 3px rgba(0, 0, 0, 0.05);\r\n          box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2), 0 2px 3px rgba(0, 0, 0, 0.05);\r\n}\r\n.k-numeric-wrap.k-state-focused > .k-select {\r\n  background-color: #ebebeb;\r\n}\r\n.k-textbox:focus,\r\n.k-autocomplete.k-state-active,\r\n.k-picker-wrap.k-state-active,\r\n.k-numeric-wrap.k-state-active,\r\n.k-dropdown-wrap.k-state-active,\r\n.k-multiselect.k-header.k-state-active {\r\n  background-color: #fff;\r\n  background-image: none;\r\n  background-position: 50% 50%;\r\n  border-color: #f5f5f5;\r\n  -webkit-box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2), 0 2px 3px rgba(0, 0, 0, 0.05);\r\n          box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2), 0 2px 3px rgba(0, 0, 0, 0.05);\r\n}\r\n.k-autocomplete.k-state-disabled,\r\n.k-picker-wrap.k-state-disabled,\r\n.k-numeric-wrap.k-state-disabled,\r\n.k-numeric-wrap.k-state-disabled .k-input,\r\n.k-numeric-wrap.k-state-disabled .k-select,\r\n.k-dropdown-wrap.k-state-disabled,\r\n.k-multiselect.k-header.k-state-disabled {\r\n  background-color: #fafafa;\r\n}\r\n.k-numeric-wrap.k-state-disabled .k-select {\r\n  border-color: #fafafa;\r\n}\r\n.k-numerictextbox .k-select {\r\n  background-color: #fff;\r\n  border-color: #fff;\r\n}\r\n.k-list > .k-state-selected.k-state-focused {\r\n  -webkit-box-shadow: none;\r\n          box-shadow: none;\r\n  color: #3f51b5;\r\n}\r\n.k-list > .k-state-selected {\r\n  -webkit-box-shadow: none;\r\n          box-shadow: none;\r\n}\r\n.k-list > .k-state-focused {\r\n  border-color: transparent;\r\n  -webkit-box-shadow: none;\r\n          box-shadow: none;\r\n}\r\n.k-list > .k-state-hover,\r\n.k-list > .k-state-selected.k-state-hover {\r\n  background-color: #ebebeb;\r\n  border-color: #ebebeb;\r\n}\r\n.k-list-container {\r\n  border-color: #ebebeb;\r\n}\r\n.k-grid td.k-state-focused.k-state-selected {\r\n  -webkit-box-shadow: inset 0 0 0 1px #808080;\r\n          box-shadow: inset 0 0 0 1px #808080;\r\n}\r\n.k-calendar td.k-state-focused,\r\n.k-calendar td.k-state-selected.k-state-focused {\r\n  -webkit-box-shadow: inset 0 0 0 1px #808080;\r\n          box-shadow: inset 0 0 0 1px #808080;\r\n}\r\n.k-calendar td.k-state-selected {\r\n  background-color: #00b0ff;\r\n  -webkit-box-shadow: none;\r\n          box-shadow: none;\r\n}\r\n.k-calendar td.k-state-selected.k-state-hover {\r\n  background-color: #00a2eb;\r\n}\r\n.k-calendar .k-state-selected > .k-link {\r\n  color: #fff;\r\n}\r\n/* Calendar */\r\n.k-calendar .k-header .k-link {\r\n  color: #ffffff;\r\n}\r\n.k-calendar .k-footer {\r\n  border-color: #e6e6e6;\r\n}\r\n.k-calendar td {\r\n  border-radius: 50%;\r\n}\r\n.k-calendar .k-content th {\r\n  background-color: #fff;\r\n}\r\n.k-calendar .k-header .k-state-hover {\r\n  background-color: #32408f;\r\n}\r\n.k-calendar .k-footer .k-nav-today {\r\n  color: #3f51b5;\r\n}\r\n.k-calendar .k-nav-fast.k-state-hover {\r\n  border-radius: 0;\r\n}\r\n.k-calendar .k-today {\r\n  background-color: #3f51b5;\r\n}\r\n.k-calendar .k-today .k-link {\r\n  color: #fff;\r\n}\r\n.k-calendar .k-today.k-state-hover {\r\n  background-color: #32408f;\r\n}\r\n.k-calendar .k-today:active {\r\n  -webkit-box-shadow: inset 0 0 0 1px #2b387c;\r\n          box-shadow: inset 0 0 0 1px #2b387c;\r\n}\r\n.k-calendar .k-link.k-state-hover,\r\n.k-window-titlebar .k-link {\r\n  -webkit-box-shadow: none;\r\n          box-shadow: none;\r\n}\r\n.k-window-titlebar .k-state-hover {\r\n  background-color: #5c6dc4;\r\n  border-color: #5c6dc4;\r\n}\r\n/* TabStrip */\r\n.k-tabstrip > .k-tabstrip-items > .k-item {\r\n  border-radius: 0;\r\n}\r\n.k-tabstrip-items .k-state-active,\r\n.k-panelbar .k-tabstrip-items .k-state-active {\r\n  background-color: #3f51b5;\r\n  background-image: none;\r\n  border-color: #00b0ff;\r\n}\r\n.k-tabstrip .k-content.k-state-active {\r\n  border-color: transparent;\r\n}\r\n.k-tabstrip-items .k-item.k-state-hover {\r\n  background: #5c6dc4;\r\n  border-color: #5c6dc4;\r\n}\r\n.k-tabstrip-items .k-state-hover .k-link {\r\n  color: #ffffff;\r\n}\r\n/* Menu */\r\n.k-group,\r\n.k-flatcolorpicker.k-group,\r\n.k-menu,\r\n.k-menu .k-group,\r\n.k-popup.k-widget.k-context-menu {\r\n  color: #444444;\r\n  background-color: #fff;\r\n}\r\n.k-menu .k-group,\r\n.k-popup.k-context-menu.k-group {\r\n  border-color: #e6e6e6;\r\n}\r\n.k-menu.k-header,\r\n.k-menu .k-item,\r\n.k-widget.k-menu-horizontal > .k-item {\r\n  -webkit-box-shadow: none;\r\n          box-shadow: none;\r\n}\r\n.k-menu .k-state-active,\r\n.k-popup.k-context-menu.k-group .k-state-hover {\r\n  background-color: #ebebeb;\r\n  border-color: #ebebeb;\r\n}\r\n/* Toolbar */\r\n.k-toolbar {\r\n  background-color: #fafafa;\r\n  border-color: #e6e6e6;\r\n}\r\n.k-toolbar .k-toggle-button:focus {\r\n  background-color: transparent;\r\n  border-color: #b3b3b3;\r\n}\r\n.k-toolbar .k-toggle-button:hover {\r\n  background-color: #ebebeb;\r\n}\r\n.k-toolbar .k-toggle-button.k-state-active {\r\n  color: #fff;\r\n  background-color: #3f51b5;\r\n  border-color: #3f51b5;\r\n}\r\n.k-toolbar .k-toggle-button.k-state-active:focus {\r\n  background-color: #3f51b5;\r\n  border-color: #2b387c;\r\n}\r\n.k-toolbar .k-toggle-button.k-state-active:hover {\r\n  color: #fff;\r\n  background-color: #32408f;\r\n  border-color: #32408f;\r\n}\r\n.k-toolbar .k-button {\r\n  -webkit-box-shadow: none;\r\n          box-shadow: none;\r\n}\r\n.k-grid .k-header .k-button,\r\n.k-scheduler .k-header .k-button,\r\n.k-scheduler .k-header li,\r\n.k-scheduler .k-header .k-link,\r\n.k-gantt > .k-header li,\r\n.k-gantt > .k-header .k-link,\r\n.k-gantt-toolbar .k-button,\r\n.km-pane-wrapper .k-header .k-button {\r\n  color: #ffffff;\r\n  background-color: #3f51b5;\r\n  border-color: #3f51b5;\r\n  -webkit-box-shadow: none;\r\n          box-shadow: none;\r\n}\r\n.k-grid .k-header .k-button:hover,\r\n.k-scheduler .k-header .k-button:hover,\r\n.k-scheduler .k-scheduler-toolbar .k-scheduler-views li.k-state-hover,\r\n.k-scheduler .k-scheduler-toolbar .k-scheduler-views li.k-state-hover .k-link,\r\n.k-gantt .k-gantt-toolbar .k-gantt-views li.k-state-hover,\r\n.k-gantt .k-gantt-toolbar .k-gantt-views li.k-state-hover .k-link,\r\n.k-gantt .k-gantt-toolbar .k-button:hover,\r\n.km-pane-wrapper .k-header .k-button:hover {\r\n  background-color: #5c6dc4;\r\n  border-color: #5c6dc4;\r\n}\r\n.km-pane-wrapper .k-header .k-button:active:hover {\r\n  color: #ffffff;\r\n}\r\n.k-scheduler .k-scheduler-toolbar ul li.k-state-hover,\r\n.k-scheduler .k-scheduler-toolbar .k-state-selected,\r\n.k-gantt-toolbar .k-button {\r\n  background-color: #3f51b5;\r\n  border-color: #3f51b5;\r\n}\r\n.k-gantt .k-gantt-toolbar .k-button:active {\r\n  background: #fff;\r\n  -webkit-box-shadow: none;\r\n          box-shadow: none;\r\n}\r\n.k-gantt-toolbar > .k-gantt-views > li.k-state-selected,\r\n.k-gantt .k-gantt-toolbar .k-gantt-views li.k-state-selected.k-state-hover,\r\n.k-scheduler .k-scheduler-toolbar .k-scheduler-views li.k-state-selected.k-state-hover,\r\n.k-scheduler-toolbar > .k-scheduler-views > li.k-state-selected {\r\n  border-bottom-color: #00b0ff;\r\n}\r\n.k-scheduler-mark {\r\n  border-radius: 50%;\r\n}\r\n/* Grid */\r\n.k-grid .k-alt {\r\n  background-color: #fff;\r\n}\r\n.k-grouping-row td,\r\ntd.k-group-cell,\r\n.k-resize-handle-inner {\r\n  color: #444444;\r\n  background-color: #fafafa;\r\n}\r\n.k-grouping-header .k-group-indicator,\r\n.k-pivot-toolbar .k-button {\r\n  color: #ffffff;\r\n  background-color: #32408f;\r\n  border-color: #32408f;\r\n  -webkit-box-shadow: none;\r\n          box-shadow: none;\r\n}\r\n.k-grid-header,\r\n.k-grid-header .k-header,\r\n.k-pager-wrap,\r\n.k-pager-numbers .k-state-selected,\r\n.k-grid-footer,\r\n.k-grid-footer td,\r\n.k-scheduler-header,\r\n.km-pane-wrapper .k-grid-header .k-header {\r\n  color: #444444;\r\n  background-color: #fafafa;\r\n}\r\n.k-header.k-scheduler-footer .k-header,\r\n.k-header.k-scheduler-footer ul.k-header li .k-link {\r\n  color: #00b0ff;\r\n  background-color: #fafafa;\r\n}\r\n.k-header.k-scheduler-footer ul.k-header li {\r\n  background-color: #fafafa;\r\n  border-color: #fafafa;\r\n}\r\n.k-header,\r\n.k-grid-header-wrap,\r\n.k-grid .k-grouping-header,\r\n.k-grid-header,\r\n.k-pager-wrap,\r\n.k-pager-wrap .k-textbox,\r\n.k-pager-wrap .k-link,\r\n.k-gantt-toolbar .k-state-default,\r\n.k-grouping-row td,\r\n.k-group-footer td,\r\n.k-grid-footer td {\r\n  border-color: #e6e6e6;\r\n}\r\n.k-group-footer td,\r\n.k-footer-template td,\r\n.k-fieldselector .k-item.k-header {\r\n  color: #444444;\r\n  background-color: #fafafa;\r\n}\r\n.k-grid .k-grouping-header {\r\n  color: rgba(255, 255, 255, 0.5);\r\n}\r\n.k-pager-wrap,\r\n.k-editor-toolbar {\r\n  color: #444444;\r\n}\r\n.k-grouping-header .k-link,\r\n.k-grouping-header .k-link:link {\r\n  color: #ffffff;\r\n}\r\n.k-scheduler-layout .k-state-selected,\r\n.k-scheduler .k-today.k-state-selected,\r\n.k-grid tr.k-state-selected,\r\n.k-grid td.k-state-selected,\r\n.k-grid td.k-state-selected.k-state-focused,\r\n.k-marquee-color,\r\n.k-gantt .k-treelist .k-state-selected,\r\n.k-gantt .k-treelist .k-state-selected td,\r\n.k-gantt .k-treelist .k-alt.k-state-selected,\r\n.k-gantt .k-treelist .k-alt.k-state-selected > td,\r\n.k-listview > .k-state-selected,\r\n.k-state-selected.k-line {\r\n  background-color: #00b0ff;\r\n}\r\n.k-state-selected.k-line {\r\n  color: #00b0ff;\r\n}\r\n.k-grid tr.k-state-selected,\r\n.k-grid td.k-state-selected,\r\n.k-listview > .k-state-selected,\r\n.k-state-selected .k-progress-status {\r\n  color: #fff;\r\n}\r\n.k-grid tr:hover {\r\n  background-color: #ebebeb;\r\n}\r\n.k-pivot-rowheaders .k-grid tr:hover {\r\n  background: none;\r\n}\r\n.k-grid td.k-state-selected,\r\n.k-grid tr.k-state-selected > td {\r\n  border-color: #008dcc;\r\n}\r\n.k-grid td.k-state-selected:hover,\r\n.k-grid tr.k-state-selected:hover td {\r\n  background-color: #00a2eb;\r\n}\r\n.k-grid-header .k-header .k-link,\r\n.k-grid-header .k-header,\r\n.k-grid-header .k-link,\r\n.k-grid-header .k-link:link,\r\n.k-pager-info,\r\n.k-scheduler-header,\r\n.k-scheduler-agendaview .k-scheduler-datecolumn {\r\n  color: #a8a8a8;\r\n}\r\n.k-gantt .k-task-draghandle {\r\n  border-color: #00b0ff;\r\n}\r\n.k-grid-pager .k-link,\r\n.k-grid-pager .k-link:link {\r\n  color: #444444;\r\n}\r\n.k-pager-numbers .k-link,\r\n.k-pager-wrap > .k-link {\r\n  border-radius: 0;\r\n}\r\n.k-pager-numbers .k-state-selected {\r\n  border-color: #3f51b5 transparent transparent;\r\n  border-radius: 0;\r\n  -webkit-box-shadow: none;\r\n          box-shadow: none;\r\n  color: #3f51b5;\r\n}\r\n.k-pager-wrap .k-link {\r\n  border-color: #fafafa;\r\n  cursor: pointer;\r\n}\r\n.k-pager-wrap .k-link:hover {\r\n  background-color: transparent;\r\n  border-color: transparent;\r\n}\r\n.k-scheduler-toolbar > ul li:first-child,\r\n.k-scheduler-toolbar > ul li:first-child .k-link,\r\n.k-scheduler-toolbar .k-nav-next,\r\n.k-scheduler-toolbar ul + ul li:last-child,\r\n.k-scheduler-toolbar .k-nav-next .k-link,\r\n.k-scheduler-toolbar ul + ul li:last-child .k-link,\r\n.k-gantt-toolbar li:first-child,\r\n.k-gantt-toolbar li:first-child > .k-link,\r\n.k-gantt-toolbar li:last-child,\r\n.k-gantt-toolbar li:last-child > .k-link {\r\n  border-radius: 0;\r\n}\r\n.k-grid,\r\n.k-panelbar,\r\n.k-notification,\r\n.k-popup .k-textbox:focus,\r\n.k-popup .k-autocomplete.k-state-focused,\r\n.k-popup .k-picker-wrap.k-state-focused,\r\n.k-popup .k-numeric-wrap.k-state-focused,\r\n.k-popup .k-dropdown-wrap.k-state-focused,\r\n.k-popup .k-multiselect.k-header.k-state-focused,\r\n.k-popup .k-textbox:hover,\r\n.k-popup .k-autocomplete.k-state-hover,\r\n.k-popup .k-picker-wrap.k-state-hover,\r\n.k-popup .k-numeric-wrap.k-state-hover,\r\n.k-popup .k-dropdown-wrap.k-state-hover {\r\n  -webkit-box-shadow: none;\r\n          box-shadow: none;\r\n}\r\n/* PanelBar */\r\n.k-panelbar,\r\n.k-panelbar .k-header,\r\n.k-panelbar .k-content,\r\n.k-panel > li.k-item,\r\n.k-panelbar .k-state-selected {\r\n  background-color: #fafafa;\r\n}\r\n.k-panelbar .k-grid-toolbar {\r\n  background-color: #3f51b5;\r\n}\r\n.k-panelbar > li.k-state-default > .k-link {\r\n  color: #444444;\r\n}\r\n.k-panelbar > li > .k-state-hover {\r\n  background-color: #ebebeb;\r\n}\r\n.k-panelbar > .k-item > .k-link,\r\n.k-panelbar.k-header,\r\n.k-panelbar .k-content,\r\n.k-panelbar .k-panel,\r\n.k-panelbar .k-item {\r\n  border-color: #e6e6e6;\r\n}\r\n/* Splitter */\r\n.k-splitbar {\r\n  border-color: #fafafa;\r\n}\r\n.k-splitbar.k-state-focused {\r\n  background-color: #3f51b5;\r\n  border-color: #3f51b5;\r\n  -webkit-box-shadow: none;\r\n          box-shadow: none;\r\n}\r\n/* Upload */\r\n.k-upload {\r\n  color: #444444;\r\n  background-color: #fff;\r\n}\r\n.k-upload-files .k-button,\r\n.k-upload-files .k-button:focus,\r\n.k-upload-files .k-button:focus:not(.k-state-disabled):not([disabled]) {\r\n  -webkit-box-shadow: none;\r\n          box-shadow: none;\r\n}\r\n/* Gantt */\r\n.k-task-milestone,\r\n.k-task-summary-complete,\r\n.k-state-selected.k-task-milestone,\r\n.k-state-selected .k-task-summary-complete {\r\n  background-color: #3f51b5;\r\n  border-color: #3f51b5;\r\n}\r\n.k-task-single {\r\n  background-color: #66d0ff;\r\n}\r\n.k-task-complete {\r\n  background: #00b0ff 0 -257px none repeat-x;\r\n}\r\n.k-treelist .k-state-selected,\r\n.k-treelist .k-state-selected td,\r\n.k-treelist .k-alt.k-state-selected,\r\n.k-treelist .k-alt.k-state-selected > td {\r\n  background-color: #00b0ff;\r\n  border-color: #00b0ff;\r\n}\r\n.k-multiselect .k-button:focus:active:not(.k-state-disabled):not([disabled]),\r\n.k-toolbar .k-button:focus:active:not(.k-state-disabled):not([disabled]),\r\n.k-group-indicator .k-button,\r\n.k-group-indicator .k-button:focus:active:not(.k-state-disabled):not([disabled]),\r\n.k-group-indicator .k-button:focus:not(.k-state-disabled):not([disabled]),\r\n.k-gantt-toolbar .k-button:focus:not(.k-state-disabled):not([disabled]),\r\n.k-gantt-toolbar .k-button:focus:active:not(.k-state-disabled):not([disabled]),\r\n.k-toolbar .k-button:focus:not(.k-state-disabled):not([disabled]),\r\n.k-toolbar .k-button:focus:active:not(.k-state-disabled):not([disabled]),\r\n.k-toolbar .k-button:active:hover,\r\n.k-toolbar .k-button.k-state-active:hover {\r\n  -webkit-box-shadow: none;\r\n          box-shadow: none;\r\n}\r\n.k-multiselect .k-button:active:hover {\r\n  color: #444444;\r\n  background-color: #dbdbdb;\r\n  border-color: #dbdbdb;\r\n}\r\n.k-multiselect-wrap > ul > .k-button {\r\n  -webkit-box-shadow: none;\r\n          box-shadow: none;\r\n}\r\n/* Editor */\r\ntable.k-editor {\r\n  border-color: #e6e6e6;\r\n}\r\n.k-editor.k-header,\r\n.editorToolbarWindow.k-header,\r\n.k-filebrowser .k-header {\r\n  background-color: #fafafa;\r\n  -webkit-box-shadow: none;\r\n          box-shadow: none;\r\n}\r\n.k-filebrowser .k-header {\r\n  color: #444444;\r\n}\r\n.k-editor-toolbar .k-tool,\r\n.k-group-start.k-group-end.k-tool {\r\n  border-color: #fafafa;\r\n}\r\n.k-treeview .k-state-selected,\r\n.k-treeview .k-state-focused,\r\n.k-editor-toolbar .k-dropdown,\r\n.k-panelbar > li > .k-state-focused {\r\n  -webkit-box-shadow: none;\r\n          box-shadow: none;\r\n}\r\n.k-treeview .k-state-focused,\r\n.k-panelbar > li > .k-state-focused {\r\n  background-color: #ebebeb;\r\n}\r\n.k-editor-toolbar .k-dropdown-wrap.k-state-default,\r\n.k-toolbar .k-dropdown-wrap.k-state-default {\r\n  border-color: #fafafa;\r\n}\r\n.k-editor-toolbar .k-tool.k-state-hover,\r\n.k-editor-toolbar .k-dropdown-wrap.k-state-hover,\r\n.k-toolbar .k-tool.k-state-hover,\r\n.k-toolbar .k-dropdown-wrap.k-state-hover {\r\n  color: #444444;\r\n  border-color: #ebebeb;\r\n  -webkit-box-shadow: none;\r\n          box-shadow: none;\r\n}\r\n.k-editor-toolbar .k-tool.k-state-selected,\r\n.k-toolbar .k-button-group .k-button.k-state-active {\r\n  -webkit-box-shadow: none;\r\n          box-shadow: none;\r\n  background-color: #dbdbdb;\r\n  border-color: #dbdbdb;\r\n}\r\n.k-editor-toolbar .k-tool.k-state-hover,\r\n.k-toolbar .k-button-group .k-button:hover {\r\n  background-color: #ebebeb;\r\n  border-color: #ebebeb;\r\n}\r\n/* Progressbar */\r\n.k-progressbar {\r\n  background-color: #fafafa;\r\n  border-color: #fafafa;\r\n}\r\n.k-progressbar .k-item,\r\n.k-progressbar .k-item.k-state-selected {\r\n  border-color: #fff;\r\n}\r\n.k-progressbar .k-state-selected {\r\n  background-color: #3f51b5;\r\n  border-color: #3f51b5;\r\n}\r\n.k-widget.k-tooltip-validation {\r\n  -webkit-box-shadow: none;\r\n          box-shadow: none;\r\n}\r\n/* Pivot Grid */\r\n.k-grid.k-alt {\r\n  background-color: #fafafa;\r\n}\r\n.k-gantt .k-treelist .k-alt,\r\n.k-gantt .k-header.k-nonwork-hour {\r\n  background-color: #fafafa;\r\n}\r\n.k-list > .k-state-hover,\r\n.k-list > .k-state-focused {\r\n  color: #444444;\r\n  background-color: #ebebeb;\r\n  border-color: #ebebeb;\r\n}\r\n/* Slider */\r\n.k-slider-track {\r\n  background-color: #cccccc;\r\n  -webkit-box-shadow: none;\r\n          box-shadow: none;\r\n}\r\n.k-slider-selection {\r\n  background-color: #3f51b5;\r\n  border-color: #3f51b5;\r\n}\r\n.k-slider .k-button,\r\n.k-slider .k-button.k-state-hover,\r\n.k-slider .k-button:active:hover,\r\n.k-slider .k-button:focus,\r\n.k-slider .k-button:active {\r\n  background: none;\r\n  border: none;\r\n  -webkit-box-shadow: none;\r\n          box-shadow: none;\r\n}\r\n.k-draghandle,\r\n.k-flatcolorpicker .k-slider-horizontal .k-slider-track {\r\n  -webkit-box-shadow: none;\r\n          box-shadow: none;\r\n}\r\n.k-flatcolorpicker .k-hue-slider .k-draghandle,\r\n.k-flatcolorpicker .k-transparency-slider .k-draghandle {\r\n  border-color: #3f51b5;\r\n  background-color: #3f51b5;\r\n  -webkit-box-shadow: none;\r\n          box-shadow: none;\r\n}\r\n.k-flatcolorpicker .k-hue-slider .k-draghandle:hover,\r\n.k-flatcolorpicker .k-transparency-slider .k-draghandle:hover {\r\n  border-color: #3f51b5;\r\n  background-color: #3f51b5;\r\n  -webkit-box-shadow: 0 0 0 8px rgba(63, 81, 181, 0.3);\r\n          box-shadow: 0 0 0 8px rgba(63, 81, 181, 0.3);\r\n}\r\n.k-draghandle.k-state-selected,\r\n.k-draghandle.k-state-selected:link,\r\n.k-draghandle.k-state-selected:hover,\r\n.k-flatcolorpicker .k-hue-slider .k-draghandle.k-state-selected,\r\n.k-flatcolorpicker .k-transparency-slider .k-draghandle.k-state-selected {\r\n  background-color: #cccccc;\r\n  border-color: #cccccc;\r\n}\r\n.k-draghandle.k-state-focused,\r\n.k-draghandle.k-state-focused:link,\r\n.k-flatcolorpicker .k-hue-slider .k-draghandle.k-state-focused,\r\n.k-flatcolorpicker .k-transparency-slider .k-draghandle.k-state-focused {\r\n  -webkit-box-shadow: none;\r\n          box-shadow: none;\r\n  border-color: #3f51b5;\r\n  background-color: #3f51b5;\r\n}\r\n.k-edit-form-container .k-edit-buttons {\r\n  background-color: #fafafa;\r\n}\r\n.k-popup .k-button,\r\n.k-popup .k-button:active:hover {\r\n  -webkit-box-shadow: none;\r\n          box-shadow: none;\r\n}\r\n.k-edit-form-container .k-button,\r\n.k-popup .k-button,\r\n.k-popup .k-primary:active,\r\n.k-popup .k-primary:active:hover,\r\n.k-edit-form-container .k-primary:active {\r\n  color: #444444;\r\n  background-color: #fafafa;\r\n  border-color: #fafafa;\r\n  -webkit-box-shadow: none;\r\n          box-shadow: none;\r\n}\r\n.k-popup .k-primary,\r\n.k-edit-form-container .k-primary {\r\n  color: #00b0ff;\r\n  background-color: #fafafa;\r\n  border-color: #fafafa;\r\n}\r\n.k-split-wrapper .k-button,\r\n.k-overflow-container .k-button,\r\n.k-filter-menu .k-button {\r\n  background: transparent;\r\n  border-color: transparent;\r\n}\r\n.k-split-wrapper .k-button,\r\n.k-overflow-container .k-button {\r\n  text-transform: none;\r\n}\r\n.k-split-wrapper .k-button:hover,\r\n.k-overflow-container .k-button:hover {\r\n  background-color: #ebebeb;\r\n  border-color: #ebebeb;\r\n}\r\n.k-split-wrapper .k-button:focus,\r\n.k-overflow-container .k-button:focus,\r\n.k-split-wrapper .k-button:focus:not(.k-state-disabled):not([disabled]),\r\n.k-overflow-container .k-button:focus:not(.k-state-disabled):not([disabled]) {\r\n  color: #3f51b5;\r\n  -webkit-box-shadow: none;\r\n          box-shadow: none;\r\n}\r\n.k-filter-menu .k-button {\r\n  background: transparent;\r\n  border-color: transparent;\r\n}\r\n.k-filter-menu .k-primary {\r\n  border-left-color: #f0f0f0;\r\n}\r\n.k-filter-menu > div > div:last-child {\r\n  border-color: #f0f0f0;\r\n}\r\n.k-popup .k-button:focus:active:not(.k-state-disabled):not([disabled]),\r\n.k-edit-form-container .k-button:focus:active:not(.k-state-disabled):not([disabled]) {\r\n  -webkit-box-shadow: none;\r\n          box-shadow: none;\r\n}\r\n.k-edit-form-container .k-scheduler-delete {\r\n  color: #00b0ff;\r\n}\r\ndiv.k-scheduler-marquee:before,\r\ndiv.k-scheduler-marquee:after {\r\n  border-color: #00b0ff;\r\n}\r\n.km-pane-wrapper > .km-pane > .km-view > .km-content {\r\n  color: #3f51b5;\r\n  background-color: #ffffff;\r\n}\r\n.km-pane-wrapper > .km-pane .km-content .k-mobile-list > ul > li > .k-link {\r\n  color: #3f51b5;\r\n}\r\n.k-popup.k-context-menu {\r\n  -webkit-box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.2);\r\n          box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.2);\r\n}\r\n.k-drag-clue {\r\n  color: #444444;\r\n  background-color: #ebebeb;\r\n  border-color: #ebebeb;\r\n  -webkit-box-shadow: inset 0 0 0 1px #808080;\r\n          box-shadow: inset 0 0 0 1px #808080;\r\n}\r\n.k-popup > .k-group-header,\r\n.k-popup > .k-virtual-wrap > .k-group-header {\r\n  color: #444444;\r\n}\r\n.k-popup .k-item > .k-group {\r\n  color: #444444;\r\n}\r\n/* Responsive styles */\r\n@media only screen and (max-width: 370px) {\r\n  .k-webkit .k-pager-refresh,\r\n  .k-ff .k-pager-refresh,\r\n  .k-ie11 .k-pager-refresh,\r\n  .k-safari .k-pager-refresh {\r\n    display: none;\r\n  }\r\n}\r\n@media only screen and (max-width: 590px) {\r\n  .k-webkit .k-pager-refresh,\r\n  .k-ff .k-pager-refresh,\r\n  .k-ie11 .k-pager-refresh,\r\n  .k-safari .k-pager-refresh {\r\n    margin-right: 0;\r\n  }\r\n}\r\n@media only screen and (max-width: 530px) {\r\n  .k-webkit .k-pager-sizes,\r\n  .k-ff .k-pager-sizes,\r\n  .k-ie11 .k-pager-sizes,\r\n  .k-safari .k-pager-sizes {\r\n    display: none;\r\n  }\r\n}\r\n@media only screen and (max-width: 687px) {\r\n  .k-webkit .k-pager-info,\r\n  .k-ff .k-pager-info,\r\n  .k-ie11 .k-pager-info,\r\n  .k-safari .k-pager-info {\r\n    display: none;\r\n  }\r\n}\r\n@media only screen and (max-width: 1024px) {\r\n  .k-scheduler-toolbar > ul.k-scheduler-views {\r\n    right: 13px;\r\n    top: 0;\r\n  }\r\n  .k-webkit,\r\n  .k-ff,\r\n  .k-ie11,\r\n  .k-safari {\r\n    /* Responsive Scheduler */\r\n    /* Responsive Pager */\r\n  }\r\n  .k-webkit .k-scheduler-toolbar > ul.k-scheduler-views,\r\n  .k-ff .k-scheduler-toolbar > ul.k-scheduler-views,\r\n  .k-ie11 .k-scheduler-toolbar > ul.k-scheduler-views,\r\n  .k-safari .k-scheduler-toolbar > ul.k-scheduler-views {\r\n    right: 13px;\r\n    top: 0;\r\n  }\r\n  .k-webkit .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view,\r\n  .k-ff .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view,\r\n  .k-ie11 .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view,\r\n  .k-safari .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view,\r\n  .k-webkit .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view:hover,\r\n  .k-ff .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view:hover,\r\n  .k-ie11 .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view:hover,\r\n  .k-safari .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view:hover {\r\n    background-image: none;\r\n    background-position: 50% 50%;\r\n    background-color: transparent;\r\n    border-color: transparent;\r\n    border-radius: 2px;\r\n    text-align: right;\r\n  }\r\n  .k-webkit .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded > li,\r\n  .k-ff .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded > li,\r\n  .k-ie11 .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded > li,\r\n  .k-safari .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded > li {\r\n    border-radius: 0;\r\n  }\r\n  .k-webkit .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded > li.k-current-view,\r\n  .k-ff .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded > li.k-current-view,\r\n  .k-ie11 .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded > li.k-current-view,\r\n  .k-safari .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded > li.k-current-view {\r\n    border-radius: 1px 1px 0 0;\r\n  }\r\n  .k-webkit .k-scheduler-toolbar > ul li:first-child,\r\n  .k-ff .k-scheduler-toolbar > ul li:first-child,\r\n  .k-ie11 .k-scheduler-toolbar > ul li:first-child,\r\n  .k-safari .k-scheduler-toolbar > ul li:first-child,\r\n  .k-webkit .k-scheduler-toolbar > ul li:first-child .k-link,\r\n  .k-ff .k-scheduler-toolbar > ul li:first-child .k-link,\r\n  .k-ie11 .k-scheduler-toolbar > ul li:first-child .k-link,\r\n  .k-safari .k-scheduler-toolbar > ul li:first-child .k-link,\r\n  .k-webkit .k-scheduler-toolbar > ul.k-scheduler-views li,\r\n  .k-ff .k-scheduler-toolbar > ul.k-scheduler-views li,\r\n  .k-ie11 .k-scheduler-toolbar > ul.k-scheduler-views li,\r\n  .k-safari .k-scheduler-toolbar > ul.k-scheduler-views li,\r\n  .k-webkit .k-scheduler-toolbar > ul.k-scheduler-views li .k-link,\r\n  .k-ff .k-scheduler-toolbar > ul.k-scheduler-views li .k-link,\r\n  .k-ie11 .k-scheduler-toolbar > ul.k-scheduler-views li .k-link,\r\n  .k-safari .k-scheduler-toolbar > ul.k-scheduler-views li .k-link {\r\n    border-radius: 0;\r\n  }\r\n  .k-webkit .k-scheduler-toolbar > ul.k-scheduler-views li:last-child,\r\n  .k-ff .k-scheduler-toolbar > ul.k-scheduler-views li:last-child,\r\n  .k-ie11 .k-scheduler-toolbar > ul.k-scheduler-views li:last-child,\r\n  .k-safari .k-scheduler-toolbar > ul.k-scheduler-views li:last-child,\r\n  .k-webkit .k-scheduler-toolbar > ul.k-scheduler-views li:last-child .k-link,\r\n  .k-ff .k-scheduler-toolbar > ul.k-scheduler-views li:last-child .k-link,\r\n  .k-ie11 .k-scheduler-toolbar > ul.k-scheduler-views li:last-child .k-link,\r\n  .k-safari .k-scheduler-toolbar > ul.k-scheduler-views li:last-child .k-link {\r\n    border-radius: 0 0 1px 1px;\r\n  }\r\n  .k-webkit .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view:hover,\r\n  .k-ff .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view:hover,\r\n  .k-ie11 .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view:hover,\r\n  .k-safari .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view:hover {\r\n    border-color: transparent;\r\n    background-image: none;\r\n    background-color: transparent;\r\n  }\r\n  .k-webkit .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view > .k-link,\r\n  .k-ff .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view > .k-link,\r\n  .k-ie11 .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view > .k-link,\r\n  .k-safari .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view > .k-link,\r\n  .k-webkit .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view:hover > .k-link,\r\n  .k-ff .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view:hover > .k-link,\r\n  .k-ie11 .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view:hover > .k-link,\r\n  .k-safari .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view:hover > .k-link {\r\n    color: #ffffff;\r\n    min-width: 20px;\r\n  }\r\n  .k-webkit .k-scheduler-views > li.k-state-selected > .k-link:after,\r\n  .k-ff .k-scheduler-views > li.k-state-selected > .k-link:after,\r\n  .k-ie11 .k-scheduler-views > li.k-state-selected > .k-link:after,\r\n  .k-safari .k-scheduler-views > li.k-state-selected > .k-link:after {\r\n    display: block;\r\n    content: \"\";\r\n    position: absolute;\r\n    top: 50%;\r\n    margin-top: -0.5em;\r\n    right: 0.333em;\r\n    width: 1.333em;\r\n    height: 1.333em;\r\n  }\r\n  .k-webkit .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded,\r\n  .k-ff .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded,\r\n  .k-ie11 .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded,\r\n  .k-safari .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded {\r\n    border-width: 1px;\r\n    border-style: solid;\r\n    border-color: transparent;\r\n    /*@secondary-border-color*/\r\n    background-image: none;\r\n    background-color: #3f51b5;\r\n    border-radius: 2px;\r\n    -webkit-box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.2);\r\n            box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.2);\r\n  }\r\n  .k-webkit .k-pager-wrap,\r\n  .k-ff .k-pager-wrap,\r\n  .k-ie11 .k-pager-wrap,\r\n  .k-safari .k-pager-wrap {\r\n    min-height: 2.56em;\r\n  }\r\n  .k-webkit .k-pager-wrap .k-pager-nav,\r\n  .k-ff .k-pager-wrap .k-pager-nav,\r\n  .k-ie11 .k-pager-wrap .k-pager-nav,\r\n  .k-safari .k-pager-wrap .k-pager-nav,\r\n  .k-webkit .k-pager-input,\r\n  .k-ff .k-pager-input,\r\n  .k-ie11 .k-pager-input,\r\n  .k-safari .k-pager-input {\r\n    display: inline-block;\r\n    vertical-align: top;\r\n  }\r\n  .k-webkit .k-pager-numbers,\r\n  .k-ff .k-pager-numbers,\r\n  .k-ie11 .k-pager-numbers,\r\n  .k-safari .k-pager-numbers,\r\n  .k-webkit .k-grid .k-pager-numbers,\r\n  .k-ff .k-grid .k-pager-numbers,\r\n  .k-ie11 .k-grid .k-pager-numbers,\r\n  .k-safari .k-grid .k-pager-numbers {\r\n    position: absolute;\r\n    left: 5.6em;\r\n    display: -webkit-inline-box;\r\n    display: -webkit-inline-flex;\r\n    display: -ms-inline-flexbox;\r\n    display: inline-flex;\r\n    -webkit-box-orient: vertical;\r\n    -webkit-box-direction: reverse;\r\n    -webkit-flex-direction: column-reverse;\r\n        -ms-flex-direction: column-reverse;\r\n            flex-direction: column-reverse;\r\n    overflow: visible;\r\n    height: auto;\r\n  }\r\n  .k-webkit .k-pager-numbers.k-state-expanded,\r\n  .k-ff .k-pager-numbers.k-state-expanded,\r\n  .k-ie11 .k-pager-numbers.k-state-expanded,\r\n  .k-safari .k-pager-numbers.k-state-expanded,\r\n  .k-webkit .k-grid .k-pager-numbers.k-state-expanded,\r\n  .k-ff .k-grid .k-pager-numbers.k-state-expanded,\r\n  .k-ie11 .k-grid .k-pager-numbers.k-state-expanded,\r\n  .k-safari .k-grid .k-pager-numbers.k-state-expanded {\r\n    -webkit-transform: translatey(-100%);\r\n        -ms-transform: translatey(-100%);\r\n            transform: translatey(-100%);\r\n  }\r\n  .k-webkit .km-pane-wrapper .k-pager-numbers,\r\n  .k-ff .km-pane-wrapper .k-pager-numbers,\r\n  .k-ie11 .km-pane-wrapper .k-pager-numbers,\r\n  .k-safari .km-pane-wrapper .k-pager-numbers,\r\n  .k-webkit .km-pane-wrapper .k-grid .k-pager-numbers,\r\n  .k-ff .km-pane-wrapper .k-grid .k-pager-numbers,\r\n  .k-ie11 .km-pane-wrapper .k-grid .k-pager-numbers,\r\n  .k-safari .km-pane-wrapper .k-grid .k-pager-numbers {\r\n    position: relative;\r\n    left: 50%;\r\n    -ms-transform: translate(-50%, 0%);\r\n        transform: translate(-50%, 0%);\r\n    -webkit-transform: translate(-50%, 0%);\r\n  }\r\n  .k-webkit .km-pane-wrapper .k-pager-numbers.k-state-expanded,\r\n  .k-ff .km-pane-wrapper .k-pager-numbers.k-state-expanded,\r\n  .k-ie11 .km-pane-wrapper .k-pager-numbers.k-state-expanded,\r\n  .k-safari .km-pane-wrapper .k-pager-numbers.k-state-expanded,\r\n  .k-webkit .km-pane-wrapper .k-grid .k-pager-numbers.k-state-expanded,\r\n  .k-ff .km-pane-wrapper .k-grid .k-pager-numbers.k-state-expanded,\r\n  .k-ie11 .km-pane-wrapper .k-grid .k-pager-numbers.k-state-expanded,\r\n  .k-safari .km-pane-wrapper .k-grid .k-pager-numbers.k-state-expanded {\r\n    -webkit-transform: translate(-50%, -100%);\r\n    -ms-transform: translate(-50%, -100%);\r\n        transform: translate(-50%, -100%);\r\n  }\r\n  .k-webkit .km-pane-wrapper .k-pager-numbers .k-link,\r\n  .k-ff .km-pane-wrapper .k-pager-numbers .k-link,\r\n  .k-ie11 .km-pane-wrapper .k-pager-numbers .k-link,\r\n  .k-safari .km-pane-wrapper .k-pager-numbers .k-link,\r\n  .k-webkit .km-pane-wrapper .k-pager-numbers .k-state-selected,\r\n  .k-ff .km-pane-wrapper .k-pager-numbers .k-state-selected,\r\n  .k-ie11 .km-pane-wrapper .k-pager-numbers .k-state-selected,\r\n  .k-safari .km-pane-wrapper .k-pager-numbers .k-state-selected,\r\n  .k-webkit .km-pane-wrapper .k-pager-wrap > .k-link,\r\n  .k-ff .km-pane-wrapper .k-pager-wrap > .k-link,\r\n  .k-ie11 .km-pane-wrapper .k-pager-wrap > .k-link,\r\n  .k-safari .km-pane-wrapper .k-pager-wrap > .k-link,\r\n  .k-webkit .km-pane-wrapper .k-pager-wrap > .k-pager-info,\r\n  .k-ff .km-pane-wrapper .k-pager-wrap > .k-pager-info,\r\n  .k-ie11 .km-pane-wrapper .k-pager-wrap > .k-pager-info,\r\n  .k-safari .km-pane-wrapper .k-pager-wrap > .k-pager-info {\r\n    padding-top: 0;\r\n    padding-bottom: 0;\r\n  }\r\n  .k-webkit .k-rtl .k-pager-numbers,\r\n  .k-ff .k-rtl .k-pager-numbers,\r\n  .k-ie11 .k-rtl .k-pager-numbers,\r\n  .k-safari .k-rtl .k-pager-numbers,\r\n  .k-webkit .k-rtl .k-grid .k-pager-numbers,\r\n  .k-ff .k-rtl .k-grid .k-pager-numbers,\r\n  .k-ie11 .k-rtl .k-grid .k-pager-numbers,\r\n  .k-safari .k-rtl .k-grid .k-pager-numbers {\r\n    right: 5.6em;\r\n    width: 5.15em;\r\n  }\r\n  .k-webkit .k-pager-numbers .k-current-page,\r\n  .k-ff .k-pager-numbers .k-current-page,\r\n  .k-ie11 .k-pager-numbers .k-current-page,\r\n  .k-safari .k-pager-numbers .k-current-page,\r\n  .k-webkit .k-grid .k-pager-numbers .k-current-page,\r\n  .k-ff .k-grid .k-pager-numbers .k-current-page,\r\n  .k-ie11 .k-grid .k-pager-numbers .k-current-page,\r\n  .k-safari .k-grid .k-pager-numbers .k-current-page {\r\n    display: block;\r\n    border-left: 0;\r\n  }\r\n  .k-webkit .k-pager-numbers li:not(.k-current-page),\r\n  .k-ff .k-pager-numbers li:not(.k-current-page),\r\n  .k-ie11 .k-pager-numbers li:not(.k-current-page),\r\n  .k-safari .k-pager-numbers li:not(.k-current-page) {\r\n    display: none;\r\n  }\r\n  .k-webkit .k-pager-numbers .k-current-page .k-link,\r\n  .k-ff .k-pager-numbers .k-current-page .k-link,\r\n  .k-ie11 .k-pager-numbers .k-current-page .k-link,\r\n  .k-safari .k-pager-numbers .k-current-page .k-link {\r\n    width: 3.8em;\r\n    line-height: 2.564em;\r\n    padding: 0 .429em 0 0.8em;\r\n    border-radius: 2px;\r\n    background-image: none;\r\n    background-position: 50% 50%;\r\n    background-color: #fafafa;\r\n    border: 1px solid transparent;\r\n    border-top: 0;\r\n    -webkit-box-shadow: 0 2px 2px 0 #fafafa;\r\n            box-shadow: 0 2px 2px 0 #fafafa;\r\n  }\r\n  .k-webkit .k-pager-numbers .k-current-page:hover .k-link,\r\n  .k-ff .k-pager-numbers .k-current-page:hover .k-link,\r\n  .k-ie11 .k-pager-numbers .k-current-page:hover .k-link,\r\n  .k-safari .k-pager-numbers .k-current-page:hover .k-link {\r\n    border-radius: 2px;\r\n    background-color: #fff;\r\n    border: 1px solid #ebebeb;\r\n    border-top: 0;\r\n    -webkit-box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2), 0 2px 3px rgba(0, 0, 0, 0.05);\r\n            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2), 0 2px 3px rgba(0, 0, 0, 0.05);\r\n  }\r\n  .k-webkit .k-pager-numbers .k-current-page .k-link:after,\r\n  .k-ff .k-pager-numbers .k-current-page .k-link:after,\r\n  .k-ie11 .k-pager-numbers .k-current-page .k-link:after,\r\n  .k-safari .k-pager-numbers .k-current-page .k-link:after {\r\n    display: block;\r\n    content: \"\";\r\n    position: absolute;\r\n    top: 50%;\r\n    margin-top: -0.6em;\r\n    right: 0.6em;\r\n    width: 1.333em;\r\n    height: 1.333em;\r\n    background-position: 0 -30px;\r\n  }\r\n  .k-webkit .k-pager-numbers + .k-link,\r\n  .k-ff .k-pager-numbers + .k-link,\r\n  .k-ie11 .k-pager-numbers + .k-link,\r\n  .k-safari .k-pager-numbers + .k-link {\r\n    margin-left: 5.4em;\r\n  }\r\n  .k-webkit .k-rtl .k-pager-numbers + .k-link,\r\n  .k-ff .k-rtl .k-pager-numbers + .k-link,\r\n  .k-ie11 .k-rtl .k-pager-numbers + .k-link,\r\n  .k-safari .k-rtl .k-pager-numbers + .k-link {\r\n    margin-right: 5.4em;\r\n    margin-left: 0;\r\n  }\r\n  .k-webkit .k-pager-wrap .k-pager-numbers .k-state-selected,\r\n  .k-ff .k-pager-wrap .k-pager-numbers .k-state-selected,\r\n  .k-ie11 .k-pager-wrap .k-pager-numbers .k-state-selected,\r\n  .k-safari .k-pager-wrap .k-pager-numbers .k-state-selected,\r\n  .k-webkit .k-pager-wrap .k-pager-numbers .k-link,\r\n  .k-ff .k-pager-wrap .k-pager-numbers .k-link,\r\n  .k-ie11 .k-pager-wrap .k-pager-numbers .k-link,\r\n  .k-safari .k-pager-wrap .k-pager-numbers .k-link {\r\n    display: block;\r\n    margin-top: 0;\r\n    margin-right: 0;\r\n    padding: 1px 5px 1px .8em;\r\n    text-align: left;\r\n    border-top: 0;\r\n    border-radius: 1px;\r\n  }\r\n  .k-webkit .k-pager-wrap .k-pager-numbers li:not(.k-current-page) .k-link:hover,\r\n  .k-ff .k-pager-wrap .k-pager-numbers li:not(.k-current-page) .k-link:hover,\r\n  .k-ie11 .k-pager-wrap .k-pager-numbers li:not(.k-current-page) .k-link:hover,\r\n  .k-safari .k-pager-wrap .k-pager-numbers li:not(.k-current-page) .k-link:hover {\r\n    background-color: #ebebeb;\r\n  }\r\n  .k-webkit .k-pager-numbers.k-state-expanded,\r\n  .k-ff .k-pager-numbers.k-state-expanded,\r\n  .k-ie11 .k-pager-numbers.k-state-expanded,\r\n  .k-safari .k-pager-numbers.k-state-expanded {\r\n    -webkit-box-sizing: border-box;\r\n            box-sizing: border-box;\r\n    padding: 2px 0 0;\r\n    border-width: 1px 1px 0 1px;\r\n    border-style: solid;\r\n    border-color: #ebebeb;\r\n    /*@secondary-border-color*/\r\n    background-color: #fff;\r\n    border-radius: 2px 2px 0 0;\r\n    -webkit-box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.2);\r\n            box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.2);\r\n  }\r\n  .k-webkit .k-pager-numbers.k-state-expanded .k-current-page,\r\n  .k-ff .k-pager-numbers.k-state-expanded .k-current-page,\r\n  .k-ie11 .k-pager-numbers.k-state-expanded .k-current-page,\r\n  .k-safari .k-pager-numbers.k-state-expanded .k-current-page {\r\n    margin: -2.2em -1px 0;\r\n    padding: 0;\r\n  }\r\n  .k-webkit .k-pager-numbers.k-state-expanded .k-current-page .k-link,\r\n  .k-ff .k-pager-numbers.k-state-expanded .k-current-page .k-link,\r\n  .k-ie11 .k-pager-numbers.k-state-expanded .k-current-page .k-link,\r\n  .k-safari .k-pager-numbers.k-state-expanded .k-current-page .k-link {\r\n    border-radius: 0 0 2px 2px;\r\n    background-color: #fff;\r\n    border: 1px solid #ebebeb;\r\n    border-top: 0;\r\n    -webkit-box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.2);\r\n            box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.2);\r\n  }\r\n  .k-webkit .k-pager-numbers.k-state-expanded li,\r\n  .k-ff .k-pager-numbers.k-state-expanded li,\r\n  .k-ie11 .k-pager-numbers.k-state-expanded li,\r\n  .k-safari .k-pager-numbers.k-state-expanded li {\r\n    display: inline-block;\r\n  }\r\n  .k-webkit .k-gantt-toolbar > ul.k-gantt-views,\r\n  .k-ff .k-gantt-toolbar > ul.k-gantt-views,\r\n  .k-ie11 .k-gantt-toolbar > ul.k-gantt-views,\r\n  .k-safari .k-gantt-toolbar > ul.k-gantt-views {\r\n    top: 0;\r\n  }\r\n}\r\n@media only screen and (max-width: 755px) {\r\n  .k-webkit .k-pager-info,\r\n  .k-ff .k-pager-info,\r\n  .k-ie11 .k-pager-info,\r\n  .k-safari .k-pager-info {\r\n    display: none;\r\n  }\r\n}\r\n@media only screen and (max-width: 572px) {\r\n  .k-webkit .k-pager-sizes,\r\n  .k-ff .k-pager-sizes,\r\n  .k-ie11 .k-pager-sizes,\r\n  .k-safari .k-pager-sizes {\r\n    display: none;\r\n  }\r\n}\r\n/* Default Theme */\r\n.k-chart .k-mask {\r\n  background-color: #fff;\r\n  filter: alpha(opacity=68);\r\n  opacity: 0.68;\r\n}\r\n.k-chart .k-selection {\r\n  border-color: #e5e5e5;\r\n}\r\n.k-chart .k-handle {\r\n  width: 15px;\r\n  height: 15px;\r\n  background-color: #3f51b5;\r\n  border-radius: 10px;\r\n}\r\n.k-chart .k-leftHandle {\r\n  left: -8px;\r\n}\r\n.k-chart .k-rightHandle {\r\n  right: -8px;\r\n}\r\n.k-chart .k-handle:hover {\r\n  background-color: #00b0ff;\r\n  border-color: #00b0ff;\r\n}\r\n.k-chart .k-navigator-hint .k-tooltip {\r\n  border: 3px solid #ffffff;\r\n  -webkit-box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.2);\r\n          box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.2);\r\n  background: #ffffff;\r\n  color: #242424;\r\n}\r\n.k-chart .k-navigator-hint .k-scroll {\r\n  background: #3f51b5;\r\n  height: 4px;\r\n}\r\n.k-chart-tooltip {\r\n  background-image: none;\r\n}\r\n/* Map */\r\n.k-map .k-marker {\r\n  background-image: url(\"Material/markers.png\");\r\n}\r\n@media only screen and (-webkit-min-device-pixel-ratio: 1.2), only screen and (min-device-pixel-ratio: 1.2) {\r\n  .k-map .k-marker {\r\n    background-image: url(\"Material/markers_2x.png\");\r\n  }\r\n}\r\n.k-map .k-attribution {\r\n  color: #666666;\r\n}\r\n.k-map .k-shadow {\r\n  background-color: #f9f9f9;\r\n  border-color: #f9f9f9;\r\n}\r\n.k-map .k-zoom-control {\r\n  border-color: #fff;\r\n  -webkit-box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.2);\r\n          box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.2);\r\n  border-radius: 2px;\r\n}\r\n.k-map .k-map-controls .k-button {\r\n  -webkit-box-shadow: none;\r\n          box-shadow: none;\r\n}\r\n.k-map .k-map-controls .k-button:focus,\r\n.k-map .k-map-controls .k-button:active,\r\n.k-map .k-map-controls .k-button:focus:active {\r\n  background-color: #d6d6d6;\r\n  border-color: #d6d6d6;\r\n  -webkit-box-shadow: none;\r\n          box-shadow: none;\r\n}\r\n.k-buttons-horizontal .k-zoom-out {\r\n  border-radius: 0 2px 2px 0;\r\n}\r\n.k-buttons-horizontal :first-child {\r\n  border-radius: 2px 0 0 2px;\r\n}\r\n.k-rtl .k-buttons-horizontal .k-zoom-out {\r\n  border-radius: 2px 0 0 2px;\r\n}\r\n.k-rtl .k-buttons-horizontal :first-child {\r\n  border-radius: 0 2px 2px 0;\r\n}\r\n.k-button-wrap .k-button {\r\n  font-size: 21px;\r\n  padding: 7px 13px;\r\n}\r\n.k-spreadsheet-row-header,\r\n.k-spreadsheet-column-header {\r\n  background-color: #fff;\r\n}\r\n.k-spreadsheet-top-corner,\r\n.k-spreadsheet-row-header,\r\n.k-spreadsheet-column-header {\r\n  background-color: #fff;\r\n  background-image: none;\r\n  color: #000000;\r\n  border-color: #cccccc;\r\n}\r\n.k-spreadsheet-top-corner {\r\n  border-color: #cccccc;\r\n}\r\n.k-spreadsheet-top-corner:after {\r\n  border-color: transparent #cccccc #cccccc transparent;\r\n}\r\n.k-spreadsheet-pane {\r\n  border-color: #cccccc;\r\n}\r\n.k-spreadsheet-pane .k-spreadsheet-vaxis,\r\n.k-spreadsheet-pane .k-spreadsheet-haxis {\r\n  border-color: #e6e6e6;\r\n}\r\n.k-spreadsheet-pane .k-spreadsheet-column-header,\r\n.k-spreadsheet-pane .k-spreadsheet-row-header {\r\n  border-color: #cccccc;\r\n}\r\n.k-spreadsheet-pane .k-spreadsheet-merged-cell {\r\n  background-color: #fff;\r\n}\r\n.k-spreadsheet-pane .k-selection-partial,\r\n.k-spreadsheet-pane .k-selection-full {\r\n  border-color: rgba(0, 176, 255, 0.2);\r\n  background-color: rgba(0, 176, 255, 0.2);\r\n}\r\n.k-spreadsheet-pane .k-filter-range {\r\n  border-color: #00b0ff;\r\n}\r\n.k-spreadsheet-pane .k-spreadsheet-column-header .k-selection-partial,\r\n.k-spreadsheet-pane .k-spreadsheet-column-header .k-selection-full {\r\n  border-bottom-color: #00b0ff;\r\n}\r\n.k-spreadsheet-pane .k-spreadsheet-row-header .k-selection-partial,\r\n.k-spreadsheet-pane .k-spreadsheet-row-header .k-selection-full {\r\n  border-right-color: #00b0ff;\r\n}\r\n.k-auto-fill,\r\n.k-spreadsheet-selection {\r\n  border-color: #00b0ff;\r\n  -webkit-box-shadow: inset 0 0 0 1px #fff, 0 0 0 1px #00b0ff;\r\n          box-shadow: inset 0 0 0 1px #fff, 0 0 0 1px #00b0ff;\r\n}\r\n.k-spreadsheet-selection {\r\n  background-color: rgba(0, 176, 255, 0.2);\r\n}\r\n.k-spreadsheet-active-cell {\r\n  border-color: #00b0ff !important;\r\n  background-color: #fff;\r\n}\r\n.k-spreadsheet-active-cell.k-single {\r\n  background-color: #fff;\r\n}\r\n.k-spreadsheet > .k-spreadsheet-formula-bar {\r\n  background-color: #fff;\r\n  border-color: #fff #fff #cccccc;\r\n}\r\n.k-spreadsheet > .k-spreadsheet-formula-bar:before {\r\n  border-color: #cccccc;\r\n}\r\n.k-spreadsheet > .k-spreadsheet-formula-bar:after {\r\n  border-color: #fff;\r\n}\r\n.k-spreadsheet .k-spreadsheet-formula-input {\r\n  background-color: #fff;\r\n  color: #444444;\r\n}\r\n.k-spreadsheet .k-resize-handle,\r\n.k-spreadsheet .k-resize-hint-handle,\r\n.k-spreadsheet .k-resize-hint-marker {\r\n  background-color: #00b0ff;\r\n}\r\n.k-spreadsheet .k-resize-hint-vertical .k-resize-hint-handle,\r\n.k-spreadsheet .k-resize-hint-vertical .k-resize-hint-marker {\r\n  background-color: #00b0ff;\r\n}\r\n.k-spreadsheet .k-single-selection::after {\r\n  background-color: #00b0ff;\r\n  border-color: #fff;\r\n}\r\n.k-spreadsheet .k-auto-fill-punch {\r\n  background-color: rgba(255, 255, 255, 0.5);\r\n}\r\n.k-spreadsheet .k-single-selection.k-dim-auto-fill-handle::after {\r\n  background-color: rgba(0, 176, 255, 0.5);\r\n}\r\n.k-spreadsheet-format-cells .k-spreadsheet-preview {\r\n  border-color: #e6e6e6;\r\n}\r\n.k-spreadsheet-filter {\r\n  border-radius: 2px;\r\n  background-color: #fff;\r\n  -webkit-box-shadow: inset 0 0 0 1px #e6e6e6;\r\n          box-shadow: inset 0 0 0 1px #e6e6e6;\r\n}\r\n.k-spreadsheet-filter.k-state-active {\r\n  color: #3f51b5;\r\n  background-color: #00b0ff;\r\n}\r\n.k-spreadsheet-filter:hover {\r\n  color: #444444;\r\n  background: #ebebeb;\r\n  border-color: #d7d7d7;\r\n}\r\n.k-action-window .k-action-buttons {\r\n  border-color: #e6e6e6;\r\n}\r\n.k-spreadsheet-sample {\r\n  color: #919191;\r\n}\r\n.k-state-selected .k-spreadsheet-sample {\r\n  color: inherit;\r\n}\r\n.k-spreadsheet-window .k-list-wrapper,\r\n.k-spreadsheet-window .k-list {\r\n  border-color: #e6e6e6;\r\n  border-radius: 2px;\r\n}\r\n.k-spreadsheet-window .export-config,\r\n.k-spreadsheet-window .k-edit-field > .k-orientation-label {\r\n  border-color: #e6e6e6;\r\n}\r\n.k-spreadsheet-window .k-edit-field > input[type=\"radio\"]:checked + .k-orientation-label {\r\n  background-image: none;\r\n  background-color: #3f51b5;\r\n  color: #6776ca;\r\n}\r\n.k-spreadsheet-window .k-page-orientation {\r\n  border-color: #e6e6e6;\r\n  -webkit-box-shadow: 0 5px 5px 0 rgba(0, 0, 0, 0.1);\r\n          box-shadow: 0 5px 5px 0 rgba(0, 0, 0, 0.1);\r\n}\r\n.k-spreadsheet-window .k-page-orientation:before {\r\n  background: #fff;\r\n  border-color: transparent;\r\n  border-bottom-color: #e6e6e6;\r\n  border-left-color: #e6e6e6;\r\n}\r\n.k-spreadsheet-window .k-margins-horizontal,\r\n.k-spreadsheet-window .k-margins-vertical {\r\n  background: transparent;\r\n  border-color: #e6e6e6;\r\n}\r\n.k-spreadsheet-toolbar.k-toolbar .k-button-group .k-button {\r\n  border-radius: 2px;\r\n}\r\n.k-spreadsheet-toolbar > .k-widget,\r\n.k-spreadsheet-toolbar > .k-button,\r\n.k-spreadsheet-toolbar > .k-button-group {\r\n  border-radius: 2px;\r\n}\r\n.k-spreadsheet-toolbar > .k-separator {\r\n  border-color: #e6e6e6;\r\n}\r\n.k-spreadsheet-toolbar .k-overflow-anchor {\r\n  border-radius: 0;\r\n}\r\n.k-spreadsheet-popup {\r\n  border-radius: 2px;\r\n}\r\n.k-spreadsheet-popup .k-separator {\r\n  background-color: #e6e6e6;\r\n}\r\n.k-spreadsheet-popup .k-button {\r\n  background-color: transparent;\r\n}\r\n.k-spreadsheet-popup .k-button:hover {\r\n  background-color: #ebebeb;\r\n}\r\n.k-spreadsheet-popup .k-state-active {\r\n  background-color: #00b0ff;\r\n  color: #ffffff;\r\n}\r\n.k-spreadsheet-popup .k-state-active:hover {\r\n  background-color: #008dcc;\r\n}\r\n.k-spreadsheet-filter-menu .k-details {\r\n  border-color: #e6e6e6;\r\n}\r\n.k-spreadsheet-filter-menu .k-details-content .k-space-right {\r\n  background-color: #fff;\r\n}\r\n.k-spreadsheet-filter-menu .k-spreadsheet-value-treeview-wrapper {\r\n  background-color: #fff;\r\n  border-color: #e6e6e6;\r\n  border-radius: 2px 0 0 2px;\r\n}\r\n.k-syntax-ref {\r\n  color: #ff8822;\r\n}\r\n.k-syntax-num {\r\n  color: #0099ff;\r\n}\r\n.k-syntax-func {\r\n  font-weight: bold;\r\n}\r\n.k-syntax-str {\r\n  color: #38b714;\r\n}\r\n.k-syntax-error {\r\n  color: red;\r\n}\r\n.k-syntax-bool {\r\n  color: #a9169c;\r\n}\r\n.k-syntax-startexp {\r\n  font-weight: bold;\r\n}\r\n.k-syntax-paren-match {\r\n  background-color: #caf200;\r\n}\r\n.k-series-a {\r\n  border-color: #3f51b5;\r\n  background-color: rgba(63, 81, 181, 0.15);\r\n}\r\n.k-series-b {\r\n  border-color: #03a9f4;\r\n  background-color: rgba(3, 169, 244, 0.15);\r\n}\r\n.k-series-c {\r\n  border-color: #4caf50;\r\n  background-color: rgba(76, 175, 80, 0.15);\r\n}\r\n.k-series-d {\r\n  border-color: #f9ce1d;\r\n  background-color: rgba(249, 206, 29, 0.15);\r\n}\r\n.k-series-e {\r\n  border-color: #ff9800;\r\n  background-color: rgba(255, 152, 0, 0.15);\r\n}\r\n.k-series-f {\r\n  border-color: #ff5722;\r\n  background-color: rgba(255, 87, 34, 0.15);\r\n}\r\n.k-spreadsheet-sheets-remove:hover .k-icon {\r\n  color: #cc2222;\r\n}\r\n.k-spreadsheet-formula-list .k-state-focused {\r\n  background-color: #00b0ff;\r\n  color: #3f51b5;\r\n}\r\n@media only screen and (-webkit-min-device-pixel-ratio: 2) {\r\n  .k-icon.k-font-icon {\r\n    background-image: none;\r\n  }\r\n}\r\n.k-spreadsheet .k-spreadsheet-quick-access-toolbar .k-button,\r\n.k-spreadsheet .k-spreadsheet-sheets-bar .k-button {\r\n  -webkit-box-shadow: none;\r\n          box-shadow: none;\r\n  color: #ffffff;\r\n  border-radius: 0;\r\n  line-height: 2.6em;\r\n  width: 3em;\r\n}\r\n.k-spreadsheet .k-spreadsheet-quick-access-toolbar .k-button:hover,\r\n.k-spreadsheet .k-spreadsheet-sheets-bar .k-button:hover {\r\n  background-color: #324191;\r\n  border-color: #324191;\r\n}\r\n.k-spreadsheet .k-spreadsheet-sheets-bar .k-button {\r\n  left: 0;\r\n  bottom: 0;\r\n  padding-top: .5em;\r\n  padding-bottom: .5em;\r\n  line-height: 2.2em;\r\n}\r\n.k-spreadsheet .k-spreadsheet-sheets-remove {\r\n  margin: 0 0 0 -1em;\r\n}\r\n.k-spreadsheet-sheets-items .k-state-default .k-link,\r\n.k-spreadsheet-tabstrip .k-state-default .k-link {\r\n  color: #9fa8da;\r\n}\r\n.k-spreadsheet-sheets-items .k-item.k-state-hover,\r\n.k-spreadsheet-tabstrip .k-item.k-state-hover,\r\n.k-spreadsheet-sheets-items .k-item.k-state-active,\r\n.k-spreadsheet-tabstrip .k-item.k-state-active,\r\n.k-spreadsheet-sheets-items .k-item.k-state-focused,\r\n.k-spreadsheet-tabstrip .k-item.k-state-focused {\r\n  background-color: transparent;\r\n}\r\n.k-spreadsheet-sheets-items .k-item.k-state-hover .k-link,\r\n.k-spreadsheet-tabstrip .k-item.k-state-hover .k-link,\r\n.k-spreadsheet-sheets-items .k-item.k-state-active .k-link,\r\n.k-spreadsheet-tabstrip .k-item.k-state-active .k-link,\r\n.k-spreadsheet-sheets-items .k-item.k-state-focused .k-link,\r\n.k-spreadsheet-tabstrip .k-item.k-state-focused .k-link {\r\n  color: #ffffff;\r\n}\r\n.k-spreadsheet-sheets-items .k-state-active .k-link,\r\n.k-spreadsheet-tabstrip .k-state-active .k-link {\r\n  color: #ffffff;\r\n}\r\n.k-spreadsheet-toolbar > .k-button:not(.k-overflow-anchor) {\r\n  line-height: 2em;\r\n}\r\n"]}