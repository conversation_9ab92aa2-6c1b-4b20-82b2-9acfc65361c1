
05ff1e8230ebe5cac4af017792f7d27a29bcb8b8	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.37.1754018536329.js\",\"contentHash\":\"f1eef00eedade78829834ded02e14915\"}","integrity":"sha512-ZgsxJjdYbhbxxM5oYAQ8EOEpFEIrF9RNIymSyAP81Q7iW/X2tTkAFxgoYliSWrMyYZjeC2XKRkX/hSXEyBe3gA==","time":1754018575958,"size":112520}