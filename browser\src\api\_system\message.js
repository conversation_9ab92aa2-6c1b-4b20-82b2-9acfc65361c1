import {request} from '@/api/index'

export default {
    /**
     * 查消息
     * @param data
     * @returns {*}
     */
    selectReceiverMessage(data) {
        return request({
            url: '/sys_message/selectReceiverMessage',
            method: 'post',
            data
        })
    },

    /**
     * mcp查消息
     * @param data
     * @returns {*}
     */
    selectMessage(data) {
        return request({
            url: '/sys/message/center/userReceiveMessage',
            method: 'post',
            data
        })
    },
    /**
     * mcp查消息
     * @param data
     * @returns {*}
     */
    selectNotice(handler,data) {
        return request({
            url: '/dynamicmd/'+handler+'/querycpx',
            method: 'post',
            data
        })
    },

    /**
     * mcp发送消息
     * @param data
     *        @param content               内容
     *        @param subject               标题
     *        @param receiverUserAccount   接收人账号（用来登录的用户名）
     *        ------------------------暂时无用---------------------------
     *        @param displayMode            显示方式：主页消息、消息中心（信封图标）、弹出框通知（实时）
     *        @param sendType               消息分类：系统消息、预警通知等
     * @returns {*}
     */
    sendUserMessage(data) {
        return request({
            url: '/sys/message/sendUserMessage',
            method: 'post',
            data
        })
    },
}