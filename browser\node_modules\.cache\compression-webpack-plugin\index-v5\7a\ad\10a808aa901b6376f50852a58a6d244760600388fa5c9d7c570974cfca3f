
42af99eb4f17ce6a2014508689cdaa11827250c7	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.379.1754018536329.js\",\"contentHash\":\"b9a20b0afb40cf162064666f51c7da5d\"}","integrity":"sha512-psBrbGBA6kLXmhi5wGizFRk8V6dWm7zqNXKeydcI05gBhGPGPBSxEAamvT3sjseg/O1cPwhuI+nAO25CGhwydw==","time":1754018575976,"size":101538}