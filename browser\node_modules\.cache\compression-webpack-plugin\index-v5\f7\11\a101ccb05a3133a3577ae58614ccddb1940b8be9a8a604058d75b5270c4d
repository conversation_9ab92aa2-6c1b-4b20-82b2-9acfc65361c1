
1cef0630eafd3eee8552b5f68d0206ea12e071ce	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.24.1754018536329.js\",\"contentHash\":\"0aa271ef1ef9fbe024f73e88498c82a6\"}","integrity":"sha512-i6653aRyQe6LZX9yBJhZ6PtWo8QwQ9zK1avEZeQJBKCCsoEiBhW9mAzmB2FAsQ8j2W8IeEk2UqusDadfKM8C6Q==","time":1754018575955,"size":53457}