
82731851dafefbd572f460af1f3b0f224d3a3411	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.161.1754018536329.js\",\"contentHash\":\"40eb3b38b7398be62db129024f9688dd\"}","integrity":"sha512-Et/VElOHjn2giN3xPFTYAf7Bt/zKSNC6o1pp471DNC/xDJCfWyvojoo3VV4/57QfRJ8h26DBGw2MhvISQDL0xQ==","time":1754018575960,"size":89818}