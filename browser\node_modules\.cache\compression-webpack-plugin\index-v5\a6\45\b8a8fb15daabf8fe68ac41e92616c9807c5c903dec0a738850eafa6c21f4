
a1ac3bce417ec16261f920b635044fc6f3841e8e	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.384.1754018536329.js\",\"contentHash\":\"b8c54269f77a561ddf3df8b20a4daf02\"}","integrity":"sha512-Uipxx2f7doWlSvR6EjEsBleLTwOAW3b2NE2IPX7dPQDDPPU2PkRjO3AcZ4tueXtYb41VjdKaNKmbDtC7Lwj1sw==","time":1754018576023,"size":156344}