import { request } from '@/api/index';

export default {
	query(data) {
		return request({
			url: '/special/query',
			method: 'post',
			data,
		});
	},
	save(data) {
		return request({
			url: '/special/save',
			method: 'post',
			data,
		});
	},
	queryById(data) {
		return request({
			url: '/special/queryById',
			method: 'post',
			data,
		});
	},
	delete(data) {
		return request({
			url: '/special/delete',
			method: 'post',
			data,
		});
	},
};
