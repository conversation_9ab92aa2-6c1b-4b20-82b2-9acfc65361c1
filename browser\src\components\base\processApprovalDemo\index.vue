<template>
    <div>
        <process-approval
            ref="tool"
            @processStarted="processStarted"
            @processAdreed="processAdreed"
            @getProcess="getProcess"
            @beforeAgree="beforeAgree"
            :ajax="axios"
            :formKey="formConfig.formKey"
            :formType="formConfig.formType"
            :processKey="processKey"
            :taskId="formConfig.id"
            :taskStatus="formConfig.taskStatus"
            @custom="custom"
            :variables="{assigneeList: ['10001']}"
            :customParams="formData"
            :assigneeType="formConfig.assigneeType"
        >
            <template v-slot:agreeContent>
                <el-form :model="formData" ref="formData" label-width="100px" class="demo-formData">
                    <el-form-item label="通过审批意见" prop="name">
                        <el-input v-model="formData.comment"></el-input>
                    </el-form-item>
                    <el-form-item label="人员" prop="user">
                        <el-select v-model="formData.assigneeList" multiple placeholder="请选择人员">
                            <el-option label="人员1" value="10001"></el-option>
                            <el-option label="人员2" value="10002"></el-option>
                        </el-select>
                    </el-form-item>
                </el-form>
            </template>
        </process-approval>
        <span>{{ formConfig.formKey }}业务页面  {{ formConfig.formType }}</span>
        <div class="w100 h100 flow-chart-svg-wrap mt10" v-if="formConfig.formType !== 'configuration'" v-html="embedDom"></div>
    </div>
</template>

<script>
import axios from '../../../js_public/axios'
import { mapState } from 'vuex'
import qs from 'qs'
import { getFlowChartData,getTaskById } from '../../../api/index'
export default {
    data(){
        return {
            axios: axios,
            formConfig: {},
            processKey: 'TEST_VALIDATE',
            headersConfig : {
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
                }
            },
            embedDom: null,
            formData: {
                comment:"",
                user:""
            }
        }
    },
    computed:{
        ajax: function(){
            return axios
        }
    },
    methods: {
        beforeAgree(){
            console.log(12312312)
        },
        processAdreed(){
            console.log(12312312)
        },
        custom(code){
            console.log(code)
        },
        processStarted(data){
            console.log(data)
        },
        processAdreed(data){
            console.log(processAdreed)
        },
        getProcess(data){
            console.log(data)
        },
        /**
         * 流程未启动时 获取流程 （获取初始页面）
         * 如为自定义类型页面 则业务页面查询后传入taskStatus 以供按钮显示
         */
        getProcessInstance(){
            let data = {
                processDefinitionKey: this.processKey,
            }
            this.ajax.post('/wfl/runtime/taskForm',qs.stringify(data),this.headersConfig).then(res => {
                if(res.data.code === 200 && res.data){
                    this.formConfig = res.data.data
                    this.$nextTick(() => {
                        this.$refs['tool'].refresh()
                    })
                }
            })
        },
        getWflImg(){
            getFlowChartData(this.formConfig.processInstanceId).then(res => {
                if(res.data){
                    this.embedDom = `<embed id="flow-chart-svg" class="h100 w100" style="display:none !important;" type="image/svg+xml">${res.data}</embed>`;
                }
            })
        }
    },
    created(){
        if(this.$route.query.taskId){
            getTaskById({
                taskId: this.$route.query.taskId,
                processInstanceId: this.$route.query.processInstanceId,
                businessKey: this.$route.query.businessKey
            }).then(res => {
                if(res.data.success){
                    this.formConfig = res.data.data
                    this.$nextTick(() => {
                        this.$refs['tool'].refresh()
                    })
                    // this.getWflImg()
                }
            })
        }else {
            this.getProcessInstance()
        }
    }
}
</script>

<style scoped lang="scss">
    ::v-deep .mcp-view-layout {
        padding-top: 5px 0px 0px 0px !important;
    }

</style>