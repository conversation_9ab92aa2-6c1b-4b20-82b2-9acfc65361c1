
5a753ebe3ad321dc612e27a791ad0f862f93a530	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.412.1754018536329.js\",\"contentHash\":\"dfed89a36616079507e3cb9ca1157ccc\"}","integrity":"sha512-Hi8vF5TYTLhEKifgV10kWbcOdj8QIQLL70M8l1ro96naAvljog4EXn495dMVXj8L8iimfteOGykpYXYArVKvLQ==","time":1754018575976,"size":87464}