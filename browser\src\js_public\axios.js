
import axios from 'axios'
import { MessageBox,Message } from 'element-ui';
import router from '../router'
import Vuex from '../store'
import rootPath from '../router/rootPath'
import cookie from 'js-cookie'
// axios 配置
axios.defaults.timeout = 600000;
axios.defaults.headers.post['Content-Type'] = 'application/json';
let baseUrl = localStorage.getItem('axiosBaseUrl')
/* 公用请求根路径 */
axios.defaults.baseURL = baseUrl

axios.defaults.withCredentials = true;

axios.interceptors.request.use((config) => {
    if(['post','get','put'].includes(config.method)){
        if(config.url !== `${baseUrl}/login`){
            config.headers['X-CSRF-TOKEN'] = localStorage.getItem('CSRF_TOKEN')
            config.headers['X-Requested-With'] = 'XMLHttpRequest'
        }
    }else if (config.method === 'delete'){
	    config.headers['X-CSRF-TOKEN'] = localStorage.getItem('CSRF_TOKEN')
    }
    return config;
},(error) =>{

    return 'request error :' + error
});

//返回状态判断
axios.interceptors.response.use((res) =>{
    if (res.data.code == "sys_session_timeout") {
    	if(res.config.url.split('/')[2] != 'logout' && res.config.url.split('/')[2] != 'loginInfo' && res.config.url.split('/')[2] != 'getToken'){
            Vuex.commit('C_TIMEOUT_TYPE')
            if(Vuex.state.timeOutType <= 1){
                MessageBox.alert('登录超时，请重新登陆。', '超时', {
                    confirmButtonText: '确定',
                    callback: action => {
                        localStorage.removeItem('CSRF_TOKEN')
                        router.push('login')
                    },
                    showClose : false
                });
            }
        }
    }else {
        Vuex.commit('C_TIMEOUT_TYPE',0)
    }
    if(res.data.code == 403){
        Message.error('账户无此权限')
    }
    return res

}, (error) => {
    let message = error?.response?.data?.message
    if(message === 'Forbidden'){
        return
    }
    if(error?.response?.data?.path&&error.response.data.path.includes('getToken')){//登录状态下在给提示
        return false
    }
    if(error.response.status === 404){
        Message.error(message||'请求地址错误. 错误码:404;')
    }
    if(error.response.status === 403){
        Message.error(message||'无权访问,请管理员授权')
    }
    if(error.response.status === 500){
        Message.error(message||'请求地址错误. 错误码:500;')
    }
    return error
});

export default axios