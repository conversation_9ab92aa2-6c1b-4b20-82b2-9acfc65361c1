<template>
  <el-dialog :id="id" :close-on-click-modal="false" title="学习列表查询" :visible.sync="dialogVisible" width="70%">
    <el-header>
      <el-form ref="queryForm" label-width="70px">
        <el-row>
          <el-col :span="16">
            <el-form-item label="模糊查询">
              <el-input v-model="tableQuery.fuzzyValue" placeholder="请输入..." style="width:100%" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-button-group style="float: right">
              <el-button type="primary" icon="el-icon-search" @click="search_">搜索</el-button>
              <el-button type="primary" icon="el-icon-refresh-left" @click="empty_">重置</el-button>
            </el-button-group>
          </el-col>
        </el-row>
      </el-form>
    </el-header>

    <div>
      <el-table :data="tableData" border style="table-layout: fixed;width: 99%;" :height="dialog_height" :fit="true"
        stripe highlight-current-row @current-change="currentChange">
        <el-table-column type="index" label="序号" />
        <el-table-column prop="createOrgName" label="所属组织" show-overflow-tooltip />
        <el-table-column prop="createPsnName" label="人员姓名" show-overflow-tooltip />
        <el-table-column prop="startTime" label="开始学习时间" show-overflow-tooltip />
        <el-table-column prop="endTime" label="结束学习时间" show-overflow-tooltip />
        <el-table-column prop="learningProgress" show-overflow-tooltip label="学习状态" min-width="100">
        </el-table-column>
      </el-table>
    </div>

    <div style="text-align: center">
      <el-footer>
        <pagination v-show="tableQuery.total > 0" :total="tableQuery.total" :page.sync="tableQuery.page"
          :limit.sync="tableQuery.limit" style="float: left;padding:20px 16px;" @pagination="refreshData" />

      </el-footer>
    </div>
  </el-dialog>
</template>

<script>
import pagination from "@/components/Pagination"
import textSpan from '@/view/components/TextSpan/TextSpan'
import { mapGetters } from 'vuex'
import AuthorApi from "@/api/authorization/authorization";
import ComplianceTrainingApi from '@/api/ComplianceTraining/ComplianceTraining.js';

export default {
  name: 'AuthorizationSelectDialog',
  components: { pagination, textSpan },
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    id: {
      type: String,
      default: null
    }
  },
  computed: {
    ...mapGetters(['orgContext']),
  },
  created() {
  },
  data() {
    return {
      myDialogVisible: this.dialogVisible,
      myId: this.id,
      tableQuery: {
        fuzzyValue: null, //模糊查询
        isQuery: true,
        page: 1,
        limit: 10,
        total: 0,
      },
      tableData: [], // 列表数据源
      dialog_height: "calc(-300px + 100vh)", // table高度
      tableTempData: null,
      selectData: [
        { code: '0', name: '普通授权' }, { code: '1', name: '普通转授权' }, { code: '2', name: '特别授权-合同' }
      ],
    }
  },
  watch: {
    dialogVisible(val) {
      this.myDialogVisible = val
      if (val) {
        this.refreshData()
      }
    },
    myDialogVisible(val) {
      this.$emit('update:dialogVisible', val)
    },
  },
  methods: {
    search_() { // 查询
      this.refreshData()
    },
    empty_() {
      this.tableQuery = {
        fuzzyValue: null, //模糊查询
        isQuery: true,
        page: 1,
        limit: 10,
        total: 0,
      }
      this.refreshData()
    },
    currentChange(currentRow) {
      this.tableTempData = currentRow
    },
    refreshData() {
      if (this.tableQuery.authorizationName === null || this.tableQuery.authorizationName === "") {
        this.$message.error("请选择授权类型")
        return;
      }
      console.log(this.dialogVisible)
      this.tableQuery.id = this.id;
      this.tableQuery.orgId = this.orgContext.currentOrgId
      this.tableQuery.legalUnitId = this.orgContext.currentLegalUnitId
      ComplianceTrainingApi.queryStudyData(this.tableQuery).then((res) => {
        this.tableData = res.data.data.records
        this.tableQuery.total = res.data.data.total
      })
    },
    cancel_() {
      this.myDialogVisible = false
    },
    sure_() {
      this.$emit('authorizationSure', this.tableTempData)
    },
  }
}
</script>

<style scoped>
/deep/ .el-dialog__body {
  padding: 0px 20px;
}
</style>