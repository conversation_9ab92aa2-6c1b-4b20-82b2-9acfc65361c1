
c1bbe9cc66943fee6edcc79cac69974448c4ca4c	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.36.1754018536329.js\",\"contentHash\":\"f32ef7b0b89b19e15f73daa6af8c9c4b\"}","integrity":"sha512-CLpG0yQ9pMUNy9DG2EvGXbFJrtdjR8sN/gwRpy8lRXTX9+SqfVc0Cuy4mPKZ/fYjZ2MncQILiNoX8B2gdQQ9ew==","time":1754018576085,"size":240055}