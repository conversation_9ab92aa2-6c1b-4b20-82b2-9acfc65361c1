package com.klaw.service.imp.complianceRiskServiceImp;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.klaw.entity.complianceRiskBean.ComplianceRiskWarning;
import com.klaw.entity.complianceRiskBean.RiskWarning;
import com.klaw.entity.systemBean.SysDict;
import com.klaw.service.complianceRiskService.ComplianceRiskWaringService;
import com.klaw.service.complianceRiskService.RiskWarningService;
import com.klaw.service.systemService.SysDictService;
import com.klaw.utils.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 风险事件大屏
 */
@Service
public class ComplianceRiskIndexServiceImpl {
    @Autowired
    private ComplianceRiskWaringService riskWaringService;
    @Autowired
    private RiskWarningService yjRiskWaringService;
    @Autowired
    private SysDictService sysDictService;

    /**
     * 风险数量统计
     * @param jsonObject
     * @return
     */
    public Map<String,Object> getRiskEeventCount(JSONObject jsonObject){
        Map<String,Object> dataMap = new HashMap<>();
        //风险事件
        QueryWrapper<ComplianceRiskWarning> wrapper = new QueryWrapper<>();
        getRiskWarningFilter(jsonObject,wrapper);
        List<ComplianceRiskWarning> riskWarningList = riskWaringService.list(wrapper);
        //风险事件总数
        dataMap.put("riskEventCount",riskWarningList.size());
        //上报中事件总数
        dataMap.put("sbzRiskEventCount", (int) riskWarningList.stream().filter(complianceRiskWarning -> "1".equals(complianceRiskWarning.getReportStatus())).count());
        //应对中事件总数
        dataMap.put("ydzRiskEventCount", (int) riskWarningList.stream().filter(complianceRiskWarning -> "3".equals(complianceRiskWarning.getReportStatus())).count());
        //已总结事件总数
        dataMap.put("zjRiskEventCount", (int) riskWarningList.stream().filter(complianceRiskWarning -> "7".equals(complianceRiskWarning.getReportStatus())).count());
        //风险预警
        QueryWrapper<RiskWarning> yjWrapper = new QueryWrapper<>();
        getYjWrapper(jsonObject,yjWrapper);
        List<RiskWarning> yjRiskWarningList = yjRiskWaringService.list(yjWrapper);
        //风险预警总数
        dataMap.put("yjEventCount",yjRiskWarningList.size());
        //预警准确率
        dataMap.put("yjAccuracyCount", yjRiskWarningList.isEmpty() ?0:riskWarningList.stream().filter(complianceRiskWarning -> complianceRiskWarning.getAssociatedRiskWarning()!=null).count()/yjRiskWarningList.size());
        return dataMap;
    }


    /**
     * 业务领域【最下方左1】
     * @param jsonObject
     * @return
     */
    public List<Map<String,Object>> getBusinessDomainData(JSONObject jsonObject){
        List<Map<String,Object>> dataList = new ArrayList<>();
        QueryWrapper<ComplianceRiskWarning> wrapper = new QueryWrapper<>();
        getRiskWarningFilter(jsonObject,wrapper);
        List<ComplianceRiskWarning> riskWarningList = riskWaringService.list(wrapper);
        Map<Object, Long> countMap = riskWarningList.stream()
                .map(ComplianceRiskWarning::getBusinessDomainName)
                .collect(Collectors.groupingBy(e -> e, Collectors.counting()));
        countMap.forEach((k,v)->{
            Map<String,Object> map = new HashMap<>();
            map.put("name",k);
            map.put("value",v);
            dataList.add(map);
        });
        return dataList;
    }

    /**
     * 预警风险等级占比【最下方中1】
     * @param jsonObject
     * @return
     */
    public List<Map<String,Object>> getYjRiskLevelData(JSONObject jsonObject){
        List<Map<String,Object>> dataList = new ArrayList<>();
        QueryWrapper<RiskWarning> wrapper = new QueryWrapper<>();
        getYjWrapper(jsonObject,wrapper);
        List<RiskWarning> riskWarningList = yjRiskWaringService.list(wrapper);
        List<SysDict> dictList = sysDictService.showSelect("HGYJ_FXDJ");
        Map<Object, Long> countMap = riskWarningList.stream()
                .map(RiskWarning::getExpectedRiskLevel)
                .collect(Collectors.groupingBy(e -> e, Collectors.counting()));
        countMap.forEach((k,v)->{
            Map<String,Object> map = new HashMap<>();
            map.put("name",dictList.stream().filter(e->e.getDicCode().equals(k.toString())).findFirst().orElse(new SysDict()).getDicName());
            map.put("value",v);
            dataList.add(map);
        });
        return dataList;
    }

    private void getYjWrapper(JSONObject jsonObject, QueryWrapper<RiskWarning> wrapper) {
        if(!StringUtil.emptyStr(jsonObject.getString("startDate")).isEmpty()){
            wrapper.ge("create_time",jsonObject.getString("startDate"));
        }
        if(!StringUtil.emptyStr(jsonObject.getString("endDate")).isEmpty()){
            wrapper.le("create_time",jsonObject.getString("endDate"));
        }
        //不设置开始结束日期时，默认查询当前年的数据
        if (StringUtil.emptyStr(jsonObject.getString("startDate")).isEmpty() && StringUtil.emptyStr(jsonObject.getString("endDate")).isEmpty()){
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy");
            wrapper.like("create_time",sdf.format(new Date()) );
        }
        if (jsonObject.containsKey("currentOgnId") && "Y".equals(jsonObject.getString("isContains"))){
            wrapper.like("create_Psn_Full_Id",jsonObject.getString("currentOgnId"));
        }else if(StringUtil.emptyStr(jsonObject.getString("selectUnitIds")).isEmpty()){
            wrapper.eq("create_ogn_id",jsonObject.getString("currentOgnId"));
        }
        if (jsonObject.containsKey("selectUnitIds") && !StringUtil.emptyStr(jsonObject.getString("selectUnitIds")).isEmpty()){
            StringBuilder sqlCon = new StringBuilder(" (");
            if ("Y".equals(jsonObject.getString("isContains"))){
                String selectUnitIds = jsonObject.getString("selectUnitIds");
                for (String unitId : selectUnitIds.split(",")) {
                    sqlCon.append(" create_Psn_Full_Id LIKE '%").append(unitId).append("%' OR");
                }

            }else{
                String selectUnitIds = jsonObject.getString("selectUnitIds");
                for (String unitId : selectUnitIds.split(",")) {
                    sqlCon.append(" create_ogn_id = '").append(unitId).append("' OR");
                }
            }
            if (sqlCon.toString().endsWith("OR")){
                wrapper.apply(sqlCon.substring(0,sqlCon.length()-2) +")");
            }
        }
    }

    /**
     * 风险事件占比统计【最下方最右侧】
     * @param jsonObject
     * @return
     */
    public List<Map<String,Object>> getRiskLevelData(JSONObject jsonObject){
        List<Map<String,Object>> dataList = new ArrayList<>();
        QueryWrapper<ComplianceRiskWarning> wrapper = new QueryWrapper<>();
        getRiskWarningFilter(jsonObject,wrapper);
        List<ComplianceRiskWarning> riskWarningList = riskWaringService.list(wrapper);
        Map<Object, Long> countMap = riskWarningList.stream()
                .map(ComplianceRiskWarning::getExpectedRiskLevelName)
                .collect(Collectors.groupingBy(e -> e, Collectors.counting()));
        countMap.forEach((k,v)->{
            Map<String,Object> map = new HashMap<>();
            map.put("name",k);
            map.put("value",v);
            dataList.add(map);
        });
        return dataList;
    }

    private void getRiskWarningFilter(JSONObject jsonObject,QueryWrapper<ComplianceRiskWarning> wrapper) {
        if(!StringUtil.emptyStr(jsonObject.getString("startDate")).isEmpty()){
            wrapper.ge("create_time",jsonObject.getString("startDate"));
        }
        if(!StringUtil.emptyStr(jsonObject.getString("endDate")).isEmpty()){
            wrapper.le("create_time",jsonObject.getString("endDate"));
        }
        if (jsonObject.containsKey("currentOgnId") && "Y".equals(jsonObject.getString("isContains"))){
            wrapper.like("create_Psn_Full_Id",jsonObject.getString("currentOgnId"));
        }else if(StringUtil.emptyStr(jsonObject.getString("selectUnitIds")).isEmpty()){
            wrapper.eq("create_ogn_id",jsonObject.getString("currentOgnId"));
        }
        //不设置开始结束日期时，默认查询当前年的数据
        if (StringUtil.emptyStr(jsonObject.getString("startDate")).isEmpty() && StringUtil.emptyStr(jsonObject.getString("endDate")).isEmpty()){
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy");
            wrapper.like("create_time",sdf.format(new Date()) );
        }
        if (jsonObject.containsKey("selectUnitIds") && !StringUtil.emptyStr(jsonObject.getString("selectUnitIds")).isEmpty()){
            StringBuilder sqlCon = new StringBuilder(" (");
            if ("Y".equals(jsonObject.getString("isContains"))){
                String selectUnitIds = jsonObject.getString("selectUnitIds");
                for (String unitId : selectUnitIds.split(",")) {
                    sqlCon.append(" create_Psn_Full_Id LIKE '%").append(unitId).append("%' OR");
                }
            }else{
                String selectUnitIds = jsonObject.getString("selectUnitIds");
                for (String unitId : selectUnitIds.split(",")) {
                    sqlCon.append(" create_ogn_id = '").append(unitId).append("' OR");
                }
            }
            if (sqlCon.toString().endsWith("OR")){
                wrapper.apply(sqlCon.substring(0,sqlCon.length()-2) + ")");
            }
        }
    }

    /**
     * 各企业风险事件数量
     * @param jsonObject
     * @return
     */
    public List<Map<String,Object>> getCompanyRiskData(JSONObject jsonObject){
        List<Map<String,Object>> dataList = new ArrayList<>();
        //风险事件
        QueryWrapper<ComplianceRiskWarning> wrapper = new QueryWrapper<>();
        getRiskWarningFilter(jsonObject,wrapper);
        List<ComplianceRiskWarning> riskWarningList = riskWaringService.list(wrapper);
        //预警信息
        //风险预警
        QueryWrapper<RiskWarning> yjWrapper = new QueryWrapper<>();
        getYjWrapper(jsonObject,yjWrapper);
        List<RiskWarning> yjRiskWarningList = yjRiskWaringService.list(yjWrapper);
        Map<String, Long> countMap = riskWarningList.stream()
                .map(ComplianceRiskWarning::getCreateOgnName)
                .collect(Collectors.groupingBy(e -> e, Collectors.counting()));
        // 取出现次数最多的 10 个元素
        Map<String, Long> top10Map = countMap.entrySet().stream()
                .sorted(Map.Entry.<String, Long>comparingByValue().reversed())
                .limit(10)
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (e1, e2) -> e1, LinkedHashMap::new));

        top10Map.forEach((k,v)->{
            Map<String,Object> map = new HashMap<>();
            map.put("product",k);
            map.put("风险事件数量",v);
            map.put("风险预警数量",yjRiskWarningList.stream().filter(e->e.getCreateOgnName().equals(k)).count());
            dataList.add(map);
        });
        return dataList;
    }
}
