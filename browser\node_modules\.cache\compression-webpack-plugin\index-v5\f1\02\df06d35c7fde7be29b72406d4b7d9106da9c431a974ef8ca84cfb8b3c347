
683948b554ebd808f465b91b19b5c65f83e0a1f5	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.91.1754018536329.js\",\"contentHash\":\"5a4a252a709ee7df3506c92a2d0b6fa0\"}","integrity":"sha512-eP22TTEGAgK6Q19wyYTaVtJGsNfAzoDWXTlAhxAlGaP7fW35Wl9eoDjV3g4GuWOoWKglYv0YkYY5UX9mJTWeag==","time":1754018576262,"size":536055}