
bb6a0378257593e67e10d2ec854592cb90bb9c16	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.51.1754018536329.js\",\"contentHash\":\"33be2c2b7b4409d4ea6cdf634aa23f9f\"}","integrity":"sha512-auTlUw/a3jVhM0IpLLYxUR7pcgS+Fftwb/+G4qKpge8MYoEDRsPAxzfgDC/aN1ghth7CvyIn3p5jlqUBlKqu8g==","time":1754018575955,"size":52696}