<template>
    <FormWindow ref="formWindow">
      <el-container v-loading="loading" style="height: calc(100vh - 150px);" :element-loading-text="loadingText">
        <el-main id="scrollbar">
          <el-card ref="printRef" style="height: auto;margin-left: 1%;margin-right: 2%;margin-top: 1%;">
            <el-scrollbar  style="height: 100%;overflow-y: hidden">
              <el-form ref="dataForm"
                       style="margin-left: 10px;margin-right: 10px;"
                       :model="mainData" :rules="rules" :class="className">
                <el-row style="margin-top: 20px;">
                  <span style="text-align: left;font-size: 23px;margin-left: 43%;font-weight: 900;">{{chackTypeMapping(mainData.chackInfo.checkType)}}</span>
                </el-row>
                <!--              <qs-base-info :data.sync="mainData" :data-state="dataState" :authorization-data="authorizationData" :view="view" :create="create"/>-->
                <SimpleBoardTitleApproval title="基本信息">
                  <table class="table_content">
                    <tbody>
                    <tr>
                      <th colspan="3" class="th_label_approval">检查名称</th>
                      <td colspan="9" class="td_value_approval">{{mainData.chackInfo.itemName}}</td>
                      <th colspan="3" class="th_label_approval">事项编码</th>
                      <td colspan="9" class="td_value_approval">{{mainData.chackInfo.itemCode}}</td>
                    </tr>
                    <tr>
                      <th colspan="3" class="th_label_approval">计划开始检查时间</th>
                      <td colspan="9" class="td_value_approval">{{mainData.chackInfo.plannedStartTime}}</td>
                      <th colspan="3" class="th_label_approval">计划结束检查时间</th>
                      <td colspan="9" class="td_value_approval">{{mainData.chackInfo.plannedEndTime}}</td>
                    </tr>
                    <tr>
                      <th colspan="3" class="th_label_approval">被检查单位</th>
                      <td colspan="9" class="td_value_approval">{{mainData.chackInfo.inspectedUnitName}}</td>
                      <th colspan="3" class="th_label_approval">参与部门</th>
                      <td colspan="9" class="td_value_approval">{{mainData.chackInfo.involvedDepartmentList}}</td>
                    </tr>
                    <tr>
                      <th colspan="3" class="th_label_approval_">检查通知</th>
                      <td colspan="21" class="td_value_approval_">
                        <UploadDoc :files.sync="mainData.chackInfo.inspectionNotice"
                                   doc-path="/case" :disabled="true" />
                      </td>
                    </tr>
                    </tbody>
                    <tbody>
                    <tr>
                      <th colspan="3" class="th_label_approval">附件材料</th>
                      <td colspan="21" class="td_value_approval">
                        <div v-if="mainData.chackInfo.relatedAttachmentsCreater">
                          <UploadDoc :files.sync="mainData.chackInfo.relatedAttachmentsCreater" doc-path="/case" :disabled="true"/>
                        </div>
                        <div v-else style="font-size: 15px">无</div>
                      </td>
                    </tr>
                    </tbody>
                  </table>


                </SimpleBoardTitleApproval>
                <!--审批历史 -->
                <SimpleBoardTitleApproval style="margin-top: 10px;" title="审查意见" class="print-table-wrap">
                  <ProcessOpinion style="border: 1px solid #606266;" :proc-inst-id="this.obj.processInstanceId" :task-id="this.obj.taskId" type-code="1"/>
                  <div v-if="approvalIs && isParseElement">
                    <el-input
                        type="textarea"
                        :rows="2"
                        style="border-radius: 0 !important;"
                        placeholder="请输入审批意见"
                        v-model="approvalOpinion">
                    </el-input>
                  </div>
                </SimpleBoardTitleApproval>
  
                <SimpleBoardTitleApproval style="margin-top: 10px;" title="领导意见" class="leadership-opinions-section-wrap">
                  <div style="border: solid 1px #606266;overflow: hidden">
                    <ProcessOpinion :show-header="false" :proc-inst-id="this.obj.processInstanceId" :task-id="this.obj.taskId" type-code="2"/>
                    <div v-if="approvalIs && isParseElementlg">
                      <el-input
                          type="textarea"
                          :rows="2"
                          style="border-radius: 0 !important;"
                          placeholder="请输入审批意见"
                          v-model="approvalOpinion">
                      </el-input>
                    </div>
                  </div>
                </SimpleBoardTitleApproval>
  
              </el-form>
            </el-scrollbar>
          </el-card>
        </el-main>
  
        <oa-records-dialog :visible.sync="oarecordsDialog" :dataid.sync="oaid"/>
      </el-container>
    </FormWindow>
  </template>
  
  <script>
  // vuex缓存数据
  import {mapGetters} from 'vuex'
  
  // 接口api
  import ComplianceAccountabilityApi from '@/api/risk/ComplianceAccountability'
  import processApi from '@/api/_system/process'
  import taskApi from "@/api/_system/task"
  import noticeApi from "@/api/_system/notice";
  import orgApi from '@/api/_system/org'
  // 组件
  import FormWindow from '@/view/components/FormWindow/FormWindow'
  import OtherInfo from '@/view/litigation/caseManage/case/caseChild/OtherInfo'
  import OrgSingleDialogSelect from '@/view/components/OrgSingleDialogSelect/index'
  import ProsecutionDialog from './ProsecutionDialog'
  import QsBaseInfo from './qisubaseInfo'
  import Claim from '@/view/litigation/caseManage/case/caseChild/Claim.vue'
  import Litigant from '@/view/litigation/caseManage/case/caseChild/Litigant'
  import CaseData from '@/view/litigation/caseManage/case/caseChild/CaseData'
  import CaseEvidenceData from '@/view/litigation/caseManage/case/caseChild/CaseEvidenceData'
  import OaRecordsDialog from '@/view/litigation/caseManage/caseProsecution/OarecordsDialog'
  import Shortcut from '@/view/litigation/caseManage/caseExamine/Shortcut'
  import relations from '@/view/litigation/caseManage/caseProsecution/child/Relations'
  import SimpleBoardTitle from "@/view/components/SimpleBoard/SimpleBoardTitle"
  import Authorization from '@/view/litigation/authorizationManage/authorization/child/AuthorizationLitigation'
  import ProcessOpinion from "@/view/components/ProcessOpinion/ProcessOpinion";
  import SimpleBoardTitleApproval from "@/view/components/SimpleBoard/SimpleBoardTitleApproval";
  import seal from '@/view/litigation/contractManage/contractApproval/child/seal'
  import doc from "@/api/_system/doc";
  import UploadDoc from "@/view/components/UploadDoc/UploadDoc.vue";
  import ComplianceReviewApi from '@/api/ComplianceReview/complianceReview.js'
  import HgjcApi from '@/api/Hgjc/Hgjc.js';
  export default {
    name: 'HgzxjhMain',
    inject: ['layout', 'mcpLayout', 'mcpDesignPage'],
    components: {
      UploadDoc,
      SimpleBoardTitleApproval,
      CaseData, Litigant, Claim, QsBaseInfo, ProsecutionDialog, OrgSingleDialogSelect,
      FormWindow, OtherInfo, CaseEvidenceData, OaRecordsDialog, Shortcut, relations,
      SimpleBoardTitle, Authorization, ProcessOpinion, seal
    },
    computed: {
      ...mapGetters(['orgContext']),
      isView: function () {
        return this.dataState === this.utils.formState.VIEW
      },
      isNotice() {
        const isNotice = this.$route.query.isNotice
        return this.mainData.dataStateCode !== this.utils.dataState_BPM.FINISH.code &&
            this.mainData.createPsnFullId === this.orgContext.currentPsnFullId && isNotice
      },
      noticeShow() {
        return this.mainData.dataStateCode === this.utils.dataState_BPM.FINISH.code
      },
      detailShow() {
        return this.view === 'new'
      },
      approvalShow() {
        return this.view === 'old'
      },
      printShow() {
        return this.view === 'new'
      },
      // 如果是从oa打开，则需需要加上这两个参数
      originOa(){
        let {origin,fullScreen} = this.$route.query
        return origin==='oa'&&fullScreen
      },
      downloadShow(){
        if(this.view===undefined){
          return true
        }
        return this.view === 'new'||this.view === 'detail'
      },
      isParseElement(){
        return this.parseElement === '部门意见'
      },
      isParseElementlg(){
        return this.parseElement === '领导意见'
      }
    },
    data() {
      return {
        reviewCategory:null,
        approvalOpinion:'',
        parseElement:null,
        approvalIs:false,
        className:'',
        qpShow: false,
        object1: null,
        type: null,
        timer: false,
        tabId: null,
        oarecordsDialog: false,
        loading: false,
        dataState: null,
        functionId: null,//终止的时候要用，需要手动关闭
        dataId: null,
        taskId: null,
        oaid: null,
        authorizationData: {},//授权信息
        authorizationList: [],//授权受托人信息
        view: 'new',
        create: '',
        mainData: {
          chackInfo: {
					  checkType: null,
					  itemName: null,
					  itemCode: null,
					  plannedStartTime: new Date().toISOString().substring(0, 10),
					  plannedEndTime: null,
					  inspectedUnitName: null,
					  inspectedUnitCode: null,
					  involvedDepartmentList: null,
					  involvedDepartmentListCode: null,
					  inspectionNotice: null,
					  relatedAttachmentsCreater: null,
					  chackInfoIsView: true,
				  },
          isSubmit: true,
				  currentProcess: null,
				  currentHandler: null,
				  currentProcessType: null,
			  	currentProcessId: null,
				  currentCheckNode: null,
				  currentTaskType: null,
				  reviewResult: {
					  reviewResultIsView: true,
					  inspectionResult: null,
					  needsRectification: true,
					  latestRectificationTime: null,
					  relatedAttachmentsResult: null,
				  },
				  rectificationPlan: {
					  rectificationPlanIsView: true,
					  plans: [],
				  },
				  rectificationResult: {
					  rectificationResultPlanIsView: true,
					  rectificationSituation: null,
					  rectificationStatus: null,
					  rectificationReport: null,
				  },
				  id: null, //主键
				  reviewCategory:null,//审查类别
          createOgnId: null,            //当前机构ID
          createOgnName: null,     //当前机构名称
          createDeptId: null,           //当前部门ID
          createDeptName: null,   //当前部门名称
          createGroupId: null,           //当前部门ID
          createGroupName: null,   //当前部门名称
          createPsnId: null,            //当前人ID
          createPsnName: null,          //当前人名称
          createOrgId: null,          //当前组织ID
          createOrgName: null,          //当前组织名称
          createPsnFullId: null,            //当前人全路径ID
          createPsnFullName: null,//当前人全路径名称
          createTime: null,//创建时间
          dataState: this.utils.dataState_BPM.SAVE.name, //数据状态
          dataStateCode: this.utils.dataState_BPM.SAVE.code, //数据状态编码
          eventReport: null, //事项报告
        },
  
        orgTreeDialog: false,
        orgDialogTitle: '组织信息',
        isAssign: false,
  
        prosecutionDialog: false,
  
        rules: {
          caseName: [
            {required: true, message: '请填写事项名称', trigger: 'blur'}
          ],
          belongPlate: [
            {required: true, message: '请选择所属单位', trigger: 'blur'}
          ],
          application: [
            {required: true, message: '请选择申请事项', trigger: 'blur'}
          ],
          projectName: [
            {required: true, message: '请填写项目名称', trigger: 'blur'}
          ]
        },
  
        activity: null,//记录当前待办处于流程实例的哪个环节
        obj: {// 流程处理逻辑需要的各种参数
          taskId: null,
          processInstanceId: null,
          businessKey: null,
          title: null,
          functionName: '合规专项检查',
          functionCode: null,
          sid: null,
        },
  
        noticeParams: {},
        noticeData: {
          moduleName: '', // 模块名称
          dataId: '', // 数据ID
          url: '', // 地址
          title: '', // 地址
          params: {} // 其他参数
        },
  
        loadingText: '加载中...'
      }
    },
    mounted() {
      //挂载完毕后，设置回调函数
      this.loadData();
      this.$emit('setCallBack', {
        beforeCallBack: this.beforeApproval,//点击办理或提交前的回调，效验必填控制
        afterCallBack: this.afterApproval,//点击办理弹框中再点击确定后的回调，审批完成后处理业务逻辑
        setTaskNodeInfo: this.setTaskNodeInfo,//挂载完毕后执行的回调，用于页面已进入需要处理的业务逻辑
        filterBtn: this.utils.filterBtn,
        beforeConfirmCallBack: this.beforeConfirmCallBack,
        beforeApprovalCb: this.beforeApprovalCb
      })
    },
    provide() {
      return {
        parentCase: this
      }
    },
    created() {
      //因为是流程功能，知会、抄送，都需要按照流程抄送的配置打开，
      // 只是知会的需要更新消息表，抄送需要更新日志表
      //判断是知会还是抄送，可以根据param中的参数isNotice判断

      this.obj.functionName = '合规专项审批';
      const isNotice = this.$route.query.isNotice
      // isNotice在OA中打开会解析成Boolean，系统内会被转成字符"true"
      if (isNotice === true || isNotice === "true") {
        //知会
        if (this.$route.query.processInstanceId !== null && this.$route.query.processInstanceId !== undefined) {
          //保存的数据，获取不到实例ID，只有启动流程实例才能拿到
          this.obj.processInstanceId = this.$route.query.processInstanceId
          this.obj.taskId = this.$route.query.taskId
        }
        const read = this.$route.query.read
        const sid = this.$route.query.sid
        if (read === false || read === 'false') {
          noticeApi.read({sid: sid})
        }
      } else {
        //这里除了抄送会走，其他正常逻辑也会走，所以下面的参数判断了type === 'toRead'，即未读时，才会更新OA消息和日志记录
        this.obj.sid = this.$route.query.sid//消息表中的消息ID 日志表中的日志ID
        if (this.$route.query.processInstanceId !== null && this.$route.query.processInstanceId !== undefined) {
          //保存的数据，获取不到实例ID，只有启动流程实例才能拿到
          this.obj.processInstanceId = this.$route.query.processInstanceId
          this.obj.taskId = this.$route.query.taskId
        }
        const type = this.$route.query.type//获取消息状态类型，toRead-》未读，此时需要更新，haveRead-》已读，就不需要更新了
        if (type === 'toRead') {
          this.obj.pathname = window.location.pathname
          //返回url路径名（https://www.runoob.com/try/try.php?filename=tryjsref_loc_pathname，返回/try/try.php），判断是在法务系统打开还是在OA中打开
          //法务中打开会把相同流程实例的全部消息改为已读，所以是更新OA多条，OA中打开是只更新OA一条
          this.obj.title = "合规专项审批"
          //更新OA需要参数processInstanceId, title, functionName, oldTaskId,
          processApi.finishOATask(this.obj)
        }
      }
    },
    methods: {
      chackTypeMapping(data){
        if(data === "COMPLIANCE_TRANSITION_CHECK"){
          return "合规专项检查"
        } else if(data === "COMPLIANCE_EFFECTIVENESS_EVALUATION"){
          return "合规有效性评价"
        }
      },
      setTaskNodeInfo(event) {
        const customProperties = event.customProperties.find(item => item.key === 'FlowCustomPropertiesSettingsServiceImpl')
        if(customProperties !== null && customProperties !== undefined ){
          this.parseElement = JSON.parse(customProperties.value)['name']
        }
        console.log("部门意见："+this.parseElement)
        //（toDeal-待办处理，haveDealt-已办查看，toRead-未读，haveRead-已读）
        const type = this.$route.query.type
        // 业务ID
        const id = this.$route.query.businessKey
        //是否首环节（submitNode-首环节）
        this.activity = event.taskNodeType
      },
      loadData(dataState, dataId) {
        this.functionId = this.$route.query.functionId
        if (this.$route.query.view !== undefined && this.$route.query.view !== '')
          this.view = this.$route.query.view
        if (this.$route.query.type !== undefined && this.$route.query.type !== '')
          this.type = this.$route.query.type
        if (this.$route.query.create !== undefined && this.$route.query.create !== '')
          this.create = this.$route.query.create
        this.reviewCategory = this.$route.query.type;
        this.dataState = dataState;
        if(this.$route.query.businessKey){
          HgjcApi.queryById(
            this.$route.query.businessKey
          ).then((res) => {
            this.mainData = res.data.data;
          })
        }
      },
      //提交前 校验必填，resolve--校验成功或者失败需要将结果返回
      beforeApproval(code, resolve) {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.save().then(response => {
              this.mainData.title = this.mainData.chackInfo.itemName
              this.mainData.code = code === null ? '' : code
              this.mainData.functionName = '合规专项审批'
              this.$emit('submit-success', this.mainData, 'id')
              this.$emit('setCallBack', {
                variables: {formData: {...this.mainData}}
              })
              resolve({success: true, formData: {...this.mainData},approvalOpinion:this.approvalOpinion})
            })
          } else {
            resolve(false)
            this.$nextTick(function () {
              document.querySelector('.is-error').scrollIntoView(false)
            })
            return false
          }
        })
      },
      //撤回转办
    beforeApprovalCb(code) {
      debugger
      if (code.type === "cancelTransfer") {
        this.obj.processInstanceId = this.$route.query.processInstanceId
        this.obj.taskId = this.$route.query.taskId
        this.obj.title = this.mainData.chackInfo.itemName
        this.obj.code = code === null ? '' : code
        this.obj.functionName = '合规专项审批'
        let pathname = window.location.pathname
        this.obj.functionCode = 'hgzxjh_main'
        processApi.sendOATask(this.obj).then(res => {
          if (pathname === '/base/design_page') {
            this.mcpLayout.closeTab()
          } else {
            window.close()
          }
        })
      }

    },
      afterApproval(code, data) {
        // 回退到首环节，修改业务数据状态，修改任务标题
        console.log("code==" + code)
        console.log("data==" + data)
        this.obj.businessKey = this.mainData.id
        //获取参数，为后续操作准备，processInstanceId和taskId其实在created中赋值了，这里在赋值一次也行
        if (data != null && data.data != null) {
          this.obj.processInstanceId = data.data.id
        }
        if (this.obj.businessKey == null || this.obj.processInstanceId == null) {
          this.obj.processInstanceId = this.$route.query.processInstanceId
          this.obj.taskId = this.$route.query.taskId
        }
  
        //需要传参数到流程中，这里操作，不限于首环节传参数
        if (this.activity === 'submitNode') {
          ComplianceAccountabilityApi.setParam(this.obj).then(response => {
            console.log("传值成功")
          })
        }
        // 将部分参数传给OA
        this.obj.title = this.mainData.chackInfo.itemName
        this.obj.code = code === null ? '' : code
        this.obj.functionName = '合规专项审批'
        //不是动态节点，给OA传待办
        if (code !== 'dynamic') {
          let loading = this.$loading({
            target: document.querySelector('.sg-page-wrap'),
            lock: false,
            text: '请稍后...',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          })
          processApi.sendOATask(this.obj).then(res => {
            loading.close()
            if (this.originOa) {
              window.close()
            }
            /*let pathname = window.location.pathname
            if (pathname.indexOf('/design_pages') !== -1) {
              window.close()
            }*/
          })
        }
      },
      beforeConfirmCallBack(data,resolve,reject)
      {
        const customProperties = data.nodeInfo.customProperties.find(item => item.key === 'FlowCustomPropertiesSettingsServiceImpl')
        if(customProperties&&customProperties.value && JSON.parse(customProperties.value)['id']==="1"&&data.approvalFormData.comment=="")
        {
          // 消息按需求编辑 这只是个示例
          this.$confirm('未填写意见，请确认是否继续。', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            resolve()()
          }).catch(() => {
            reject()
          })
        }else {
          resolve()
        }
      },
      handleConditionBranch(branch, resolve) {
        console.log('大写：' + JSON.stringify(branch))
        for (const b of branch) {
          let el = b.sequenceFlows[0].conditionExpression
        }
        let aa = branch[0]
        resolve([aa])
      },
      submit() {
        this.save().then(response => {
          this.$emit('submit-success', this.mainData, 'id')
          this.$message.success('保存成功!')
        })
      },
      save() {
        return new Promise((resolve, reject) => {
          this.mainData.isSubmit = false
          HgjcApi.save(this.mainData).then(response => {
            resolve(response)
          }).catch(error => {
            reject(error)
          })
        })
      },
      stopClick() {
        this.$confirm('您确定要终止当前流程吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          let processInstanceId = this.$route.query.processInstanceId
          this.obj.processInstanceId = this.$route.query.processInstanceId
          this.obj.taskId = this.$route.query.taskId
          this.obj.title = this.mainData.chackInfo.itemName
          this.obj.functionName = '合规专项审批'
          this.obj.code = 'stop'
          let pathname = window.location.pathname
          new Promise((resolve, reject) => {
            let processInstanceId = this.$route.query.processInstanceId
            processApi.end(processInstanceId).then(res => {
              resolve(res)
            })
          }).then(val => {
            if (val.data.code === 200) {
              this.obj.functionCode = 'hgzxjh_main'
              processApi.sendOATask(this.obj).then(res => {
                if (pathname === '/base/design_page') {
                  this.mcpLayout.closeTab()
                } else {
                  window.close()
                }
              })
            }
          })
        }).catch(() => {
          this.$message.info('已取消删除!')
        })
      },
      finishClick() {
        this.$confirm('您确定要结束当前流程吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.obj.processInstanceId = this.$route.query.processInstanceId
          this.obj.taskId = this.$route.query.taskId
          this.obj.title = this.mainData.chackInfo.itemName
          this.obj.functionName = '合规专项审批'
          this.obj.code = 'pass'
          let pathname = window.location.pathname
          new Promise((resolve, reject) => {
            processApi.move({
              proInstId: this.$route.query.processInstanceId,
              taskId: this.$route.query.taskId
            }).then(res => {
              resolve(res)
            })
          }).then(val => {
            if (val.status === 200) {
              this.obj.functionCode = 'hgzxjh_main'
              processApi.sendOATask(this.obj).then(res => {
                if (pathname === '/base/design_page') {
                  this.mcpLayout.closeTab()
                } else {
                  window.close()
                }
              })
            }
          })
        }).catch(() => {
          this.$message.info('已取消删除!')
        })
      },
      // 发起知会
      noticeClick(cooperateFunc) {
        //必传参数
        this.noticeParams = {
          ...this.utils.routeState.VIEW(this.mainData.id),//主要作用是后台逻辑需要，其他作用各业务看情况使用
          processInstanceId: this.$route.query.processInstanceId,//流程实例
          taskId: this.$route.query.taskId,//任务ID
          businessKey: this.mainData.id, //业务数据ID
          entranceType: "FLOWABLE", //流程特定的标识
          type: "haveRead",//已处理
          whetherProcess: true,//系统内部打开的时候判断是否是流程，通过不同的方式打开查看
          isNotice: true//告知是知会消息打开的功能，有些按钮可以隐藏,只有知会消息，在created钩子函数中才执行相关，如果是流程抄送不需要传，可以根据上面created描述判断
        }
        this.noticeData.dataId = this.mainData.id
        this.noticeData.moduleName = '合规专项审批'
        this.noticeData.params = this.noticeParams
        this.noticeData.url = 'hgzxjh_main'//这个需要与功能维护中的值一样
        this.noticeData.title = this.mainData.chackInfo.itemName
        cooperateFunc(this.noticeData)
      },
      //选择模板
      templateClick(val) {
        if (val) {
          this.prosecutionDialog = true
        }
      },
      detailClick() {
        const me = this
        if (this.mainData.dataStateCode !== this.utils.dataState_BPM.SAVE.code) {
          taskApi.selectTaskId({businessKey: this.mainData.id, isView: 'true'}).then(res => {
            const functionId = res.data.data[0].ID
            const tabId = this.utils.createUUID()
  
            let urlParam = {
              processInstanceId: res.data.data[0].PID,//流程实例
              taskId: res.data.data[0].ID,//任务ID
              businessKey: this.mainData.id, //业务数据ID
              functionId: functionId, //不传也可以，只有首环节需要传，实例启动就可以不传了
              entranceType: "FLOWABLE",
              type: "haveDealt",
              channel: 'business', //查看渠道，默认就传这个值即可，表示是从业务功能传过来的，首环节提交后过滤掉撤回按钮
              view: 'old',
            }
  
            if (me.$route.query.origin !== undefined && me.$route.query.origin === 'oa')
            {
              this.$set(urlParam, "origin", "oa")
              this.$set(urlParam, "fullScreen", "true")
            }
  
            this.layout.openNewTab("详细信息",
                "design_page",
                "design_page",
                tabId,
                urlParam
            )
          })
        } else {
          taskApi.selectFunctionId({functionCode: 'case_risk_main'}).then(res => {
            const functionId = res.data.data[0].ID
            const tabId = this.utils.createUUID()
  
            let urlParam = {
              processDefinitionKey: this.mcpDesignPage.processKey,
              functionId: functionId,
              entranceType: "FLOWABLE",
              ...this.utils.routeState.VIEW(this.mainData.id),
              channel: 'business',
              view: 'old',
            }
  
            if (me.$route.query.origin !== undefined && me.$route.query.origin === 'oa')
            {
              this.$set(urlParam, "origin", "oa")
              this.$set(urlParam, "fullScreen", "true")
            }
  
            this.layout.openNewTab("详细信息",
                "design_page",
                "design_page",
                tabId,
                urlParam
            )
          })
        }
      },
      approvalClick() {
        const me = this
        if (this.mainData.dataStateCode !== this.utils.dataState_BPM.SAVE.code) {
          taskApi.selectTaskId({businessKey: this.mainData.id, isView: 'true'}).then(res => {
            const functionId = res.data.data[0].ID
            const tabId = this.utils.createUUID()
  
            let urlParam  = {
              processInstanceId: res.data.data[0].PID,//流程实例
              taskId: res.data.data[0].ID,//任务ID
              businessKey: this.mainData.id, //业务数据ID
              functionId: functionId, //不传也可以，只有首环节需要传，实例启动就可以不传了
              entranceType: "FLOWABLE",
              type: "haveDealt",
              channel: 'business', //查看渠道，默认就传这个值即可，表示是从业务功能传过来的，首环节提交后过滤掉撤回按钮
              view: 'new',
            }
  
            if (me.$route.query.origin !== undefined && me.$route.query.origin === 'oa')
            {
              me.$set(urlParam, "origin", "oa")
              me.$set(urlParam, "fullScreen", "true")
            }
  
            this.layout.openNewTab("审批信息",
                "design_page",
                "design_page",
                tabId,
                urlParam
            )
          })
        } else {
          taskApi.selectFunctionId({functionCode: 'hgzxjh_main'}).then(res => {
            const functionId = res.data.data[0].ID
            const tabId = this.utils.createUUID()
  
            let urlParam = {
              processDefinitionKey: this.mcpDesignPage.processKey,
              functionId: functionId,
              entranceType: "FLOWABLE",
              ...this.utils.routeState.VIEW(this.mainData.id),
              channel: 'business',
              view: 'new',
            }
  
            if (me.$route.query.origin !== undefined && me.$route.query.origin === 'oa')
            {
              this.$set(urlParam, "origin", "oa")
              this.$set(urlParam, "fullScreen", "true")
            }
  
            this.layout.openNewTab("审批信息",
                "design_page",
                "design_page",
                tabId,
                urlParam
            )
          })
        }
      },
      approvalOpinionIs(){
        orgApi.roleCheck({ orgId: this.orgContext.currentOrgId, roleName: 'LDYJ' }).then(res => {
          console.log('approvalOpinionIs：',res)
          this.approvalIs = !(this.type !== 'toDeal' || res.data.data === false)
        })
      },
      queryHistoricalNode() {
        let taskId = this.$route.query.taskId
        taskApi.queryHistoricalNode({taskId: taskId}).then(res => {
          return res.data.data
        })
      },
      printClick(){
        this.$print(this.$refs.dataForm)
      },
      downloadClick(){
        // 初始化一个空数组来存储合并后的结果
        let mergedArray = [];
        // 使用forEach()方法循环遍历列表并合并JSON数组
        if(this.mainData.files){
          mergedArray = mergedArray.concat(JSON.parse(this.mainData.files));
        }
        if(this.mainData.authorization && this.mainData.authorization.authorizedBook){
          mergedArray = mergedArray.concat(JSON.parse(this.mainData.authorization.authorizedBook));
        }
        if(this.mainData.eventReport){
          mergedArray = mergedArray.concat(JSON.parse(this.mainData.eventReport));
        }
        console.log("附件合并的结果")
        console.log(mergedArray)
        //下载全部的文件
        const loading = this.$loading({
          lock: true,
          text: "文件下载中...",
          spinner: "el-icon-loading",
          background: "rgba(0, 0, 0, 0.7)"
        });
        doc.fileDown(mergedArray).then(response => {
          if (response) {
            const blob = response.data;
            const fileName = this.mainData.chackInfo.itemName+".zip";
            if ("download" in document.createElement("a")) {
              // 非IE下载
              const elink = document.createElement("a");
              elink.download = fileName;
              elink.style.display = "none";
              elink.href = URL.createObjectURL(blob);
              document.body.appendChild(elink);
              elink.click();
              URL.revokeObjectURL(elink.href); // 释放URL 对象
              document.body.removeChild(elink);
            } else {
              // IE10+下载
              navigator.msSaveBlob(blob, fileName);
            }
          }
          loading.close();
        }).catch(res => {
          loading.close();
        });
      },
    }
  }
  </script>
  
  <style scoped>
  .row_ {
    margin-top: 10px;
  }
  
  .label_ {
    margin-top: 10px;
    text-align: right;
    padding-right: 6px;
  }
  
  .title {
    font-size: 16px;
    font-weight: bold;
  }
  
  html, body, el-container, el-main, el-form, el-card {
    width: 100%;
  }
  </style>
  