package com.klaw.service.imp.caseServiceImpl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.klaw.dao.caseDao.CaseLedgerMapper;
import com.klaw.entity.caseBean.CaseRecord;
import com.klaw.entity.caseBean.child.Agent;
import com.klaw.entity.caseBean.child.CaseReport;
import com.klaw.entity.caseBean.child.Parties;
import com.klaw.entity.caseBean.child.Tag;
import com.klaw.service.caseService.CaseLedgerService;
import com.klaw.service.caseService.childService.AgentService;
import com.klaw.service.caseService.childService.PartiesService;
import com.klaw.service.caseService.childService.SgCaseReportService;
import com.klaw.service.caseService.childService.TagService;
import com.klaw.utils.DataAuthUtils;
import com.klaw.utils.PageUtils;
import com.klaw.utils.Utils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.*;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class CaseLedgerServiceImpl extends ServiceImpl<CaseLedgerMapper, CaseRecord> implements CaseLedgerService
{
    @Resource
    private TagService tagService;
    @Resource
    private PartiesService partiesService;
    @Resource
    private CaseLedgerService caseLedgerService;
    @Resource
    private AgentService agentService;
    @Resource
    private CaseLedgerMapper caseLedgerMapper;
    @Resource
    private SgCaseReportService sgCaseReportService;

    @Override
    public PageUtils<CaseRecord> queryCaseLeger(JSONObject json)
    {
        // 查案件
        QueryWrapper<CaseRecord> wrapper = new QueryWrapper<>();
        getLedgerFilter(json, wrapper, true);
        PageUtils<CaseRecord> recordPage = page(new PageUtils<>(json), wrapper);

        // 查标签
        List<String> ids = recordPage.getRecords().stream().map(CaseRecord::getId).collect(Collectors.toList());
        Map<String, List<Tag>> maps = new HashMap<>();
        if (ids.size() > 0) {
            List<Tag> tags = tagService.list(new QueryWrapper<Tag>().in("data_id", ids));
            tags.forEach(item -> {
                if (maps.containsKey(item.getDataId())) {
                    maps.get(item.getDataId()).add(item);
                } else {
                    List<Tag> temp = new ArrayList<>();
                    temp.add(item);
                    maps.put(item.getDataId(), temp);
                }
            });
        }

        // 匹配
        recordPage.getRecords().forEach(item -> {
            if (maps.containsKey(item.getId())) {
                item.setTagList(maps.get(item.getId()));
            } else {
                item.setTagList(new ArrayList<>());
            }
        });

        return recordPage;
    }

    @Override
    public List<Map<String, Object>> queryLedgerFastQueryCase(JSONObject json)
    {
        QueryWrapper queryParams = new QueryWrapper<>();
        queryParams.eq("1", "1");
        getLedgerFilter(json, queryParams, false);

        List<Map<String, Object>> maps1 = caseLedgerMapper.queryLedgerFastQueryCase1(queryParams);  //业务单元
        List<Map<String, Object>> maps2 = caseLedgerMapper.queryLedgerFastQueryCase2(queryParams);  //管理单位
        List<Map<String, Object>> maps3 = caseLedgerMapper.queryLedgerFastQueryCase3(queryParams);  //案件种类
        List<Map<String, Object>> maps4 = caseLedgerMapper.queryLedgerFastQueryCase4(queryParams);  //我方地位
        List<Map<String, Object>> maps5 = caseLedgerMapper.queryLedgerFastQueryCase5(queryParams);  //案由
        List<Map<String, Object>> maps6 = caseLedgerMapper.queryLedgerFastQueryCase6(queryParams);  //案发区域
        List<Map<String, Object>> maps7 = caseLedgerMapper.queryLedgerFastQueryCase7(queryParams);  //管辖机构
        List<Map<String, Object>> maps8 = caseLedgerMapper.queryLedgerFastQueryCase8(queryParams);  //案件年度
        List<Map<String, Object>> maps9 = caseLedgerMapper.queryLedgerFastQueryCase9(queryParams);  //风险等级

        List<Map<String, Object>> maps = new ArrayList<>();
        maps.addAll(maps1);
        maps.addAll(maps2);
        maps.addAll(maps3);
        maps.addAll(maps4);
        maps.addAll(maps5);
        maps.addAll(maps6);
        maps.addAll(maps7);
        maps.addAll(maps8);
        maps.addAll(maps9);

        return maps;
    }

    /**
     * 案件台账导出
     */
    @Override
    public void exportCaseLedger(JSONObject json, HttpServletResponse response) {
        QueryWrapper<CaseRecord> queryWrapper = new QueryWrapper<>();
        getLedgerFilter(json, queryWrapper, true);
        List<CaseRecord> caseRecordList = caseLedgerService.list(queryWrapper);

        ClassPathResource resource = new ClassPathResource("template/case/06caseReport.xlsx");
        InputStream inputStream = null;
        ByteArrayOutputStream out = null;
        try {
            inputStream = resource.getInputStream();
            //创建一个工作薄
            XSSFWorkbook workbook = new XSSFWorkbook(inputStream);
            //通过工作薄 创建一个sheet页,并命名为【第一个sheet】
            XSSFSheet sheet = workbook.getSheetAt(0);

            XSSFCellStyle style = createCaseLedgerCellStyle(workbook);
            XSSFCellStyle style2 = createCaseLedgerCellStyle(workbook);
            short format = workbook.createDataFormat().getFormat("yyyy-MM-dd");
            style2.setDataFormat(format);
            XSSFCellStyle style3 = createCaseLedgerCellStyle(workbook);
            short format3 = workbook.createDataFormat().getFormat("0.00");
            style3.setDataFormat(format3);

            XSSFCellStyle styleCopy = createCaseLedgerCellStyle(workbook);
            XSSFCellStyle styleCopy2 = createCaseLedgerCellStyle(workbook);
            short format5 = workbook.createDataFormat().getFormat("yyyy-MM-dd");
            styleCopy2.setDataFormat(format5);
            XSSFCellStyle styleCopy3 = createCaseLedgerCellStyle(workbook);
            short format6 = workbook.createDataFormat().getFormat("0.00");
            styleCopy3.setDataFormat(format6);

            styleCopy.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            styleCopy.setFillForegroundColor(IndexedColors.SEA_GREEN.getIndex());
            styleCopy2.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            styleCopy2.setFillForegroundColor(IndexedColors.SEA_GREEN.getIndex());
            styleCopy3.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            styleCopy3.setFillForegroundColor(IndexedColors.SEA_GREEN.getIndex());

            List<String> caseIdList = caseRecordList.stream().flatMap(i -> {
                List<String> list = new ArrayList<>();
                list.add(i.getId());
                return list.stream();
            }).collect(Collectors.toList());

            //收立案阶段-当事人
            List<Parties> partiesList = partiesService.list(new QueryWrapper<Parties>().in("master_id", caseIdList));
            //收立案代理信息
            List<Agent> agentList = agentService.list(new QueryWrapper<Agent>().in("parent_id", caseIdList));
            //标签
            List<Tag> tagList = tagService.list(new QueryWrapper<Tag>().in("data_id", caseIdList));
            //子数据
            QueryWrapper<CaseRecord> wrapper = new QueryWrapper<>();
            wrapper.in("parent_id", caseIdList).and(i->i.eq("case_process_type", "一审")
                    .or().eq("case_process_type", "再审一审")
                    .or().eq("case_process_type", "再审二审")
                    .or().eq("case_process_type", "执行")
                    .or().eq("case_process_type", "结案"));
            List<CaseRecord> childList = caseLedgerService.list(wrapper);
            //二审
            QueryWrapper<CaseRecord> wrapper2 = new QueryWrapper<>();
            wrapper2.in("parent_id", caseIdList).and(i->i.eq("case_process_type", "二审"));
            List<CaseRecord> ershenList = caseLedgerService.list(wrapper2);
            List<String> ershenIdList = ershenList.stream().flatMap(i -> {
                List<String> list = new ArrayList<>();
                list.add(i.getId());
                return list.stream();
            }).collect(Collectors.toList());
            //二审-当事人
            List<Parties> partiesList2 = new ArrayList<>();
            if(CollectionUtils.isNotEmpty(ershenIdList)){
                partiesList2 = partiesService.list(new QueryWrapper<Parties>().in("master_id", ershenIdList));
            }

            Set<String> numbers = new HashSet<>();
            for (int i = 0; i < caseRecordList.size(); i++) {
                CaseRecord caseRecord = caseRecordList.get(i);
                String caseNumber = caseRecord.getCaseNumber();

                XSSFRow row = sheet.createRow(i+2);
                if (StringUtils.isNotEmpty(caseNumber) && numbers.contains(caseNumber))
                {
                    setCellsByCase(row, caseRecord, i+1, styleCopy, styleCopy2, styleCopy3, partiesList, agentList, tagList, childList, ershenList, partiesList2);
                }
                else
                {
                    setCellsByCase(row, caseRecord, i+1, style, style2, style3, partiesList, agentList, tagList, childList, ershenList, partiesList2);
                    numbers.add(caseNumber);
                }

                row.setHeightInPoints(35);
            }

            String fileName = "案件.xlsx";
            response.setHeader("Access-Control-Expose-Headers", "Response-Type");
            response.setHeader("Response-Type", "doc");
            response.setContentType("application/octet-stream");
            // 设置Content-Disposition
            response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(fileName, "UTF-8"));
            response.setCharacterEncoding("UTF-8");

            out = new ByteArrayOutputStream();

            OutputStream os = response.getOutputStream();
            workbook.write(os);
            os.flush();
            os.close();
        } catch (Exception e) {
            log.info("printStackTrace异常");
        } finally {
            if (out != null) {
                try {
                    out.close();
                } catch (IOException e) {
                    log.info("printStackTrace异常");
                }
            }
            if(inputStream!=null){
                try {
                    inputStream.close();
                } catch (IOException e) {
                    log.info("printStackTrace异常");
                }
            }
        }
    }

    /**
     * 案件台账导出-重大案件一案一表
     */
    @Override
    public void exportGreatCaseZip(JSONObject json, HttpServletResponse response) throws IOException {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        QueryWrapper<CaseRecord> wrapper = new QueryWrapper<>();
        getLedgerFilter(json, wrapper, true);
        List<CaseRecord> caseRecords = caseLedgerService.list(wrapper);
        ClassPathResource resource = new ClassPathResource("template/case/07caseReportGreat.xlsx");

        String fileName = "批量支付文件" + ".zip";
        response.setContentType("application/octet-stream");
        response.setHeader("Content-Disposition",
                           "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
        response.setCharacterEncoding("UTF-8");

        OutputStream os = response.getOutputStream();
        ZipOutputStream zipOutputStream = new ZipOutputStream(os);

        int index = 1;

        for (CaseRecord caseRecord : caseRecords)
        {
            InputStream inputStream = null;
            List<CaseReport> reportList = sgCaseReportService.list(
                    new QueryWrapper<CaseReport>().eq("parent_id", caseRecord.getId()).orderByAsc("create_time"));
            try {
                inputStream = resource.getInputStream();
                XSSFWorkbook workbook = new XSSFWorkbook(inputStream);
                XSSFSheet sheet = workbook.getSheetAt(0);

                XSSFCellStyle style = createGreatCaseZipCellStyle(workbook);
                XSSFCellStyle style2 = createGreatCaseZipCellStyle(workbook);
                short format = workbook.createDataFormat().getFormat("yyyy-MM-dd");
                style2.setDataFormat(format);
                XSSFCellStyle style3 = createGreatCaseZipCellStyle(workbook);
                short format3 = workbook.createDataFormat().getFormat("0.00");
                style3.setDataFormat(format3);
                XSSFCellStyle style4 = createGreatCaseZipCellStyle4(workbook);
                XSSFCellStyle style5 = createGreatCaseZipCellStyle2(workbook);
                XSSFCellStyle style5_1 = createGreatCaseZipCellStyle2_1(workbook);
                style5_1.setDataFormat(format);
                XSSFCellStyle style6 = createGreatCaseZipCellStyle3(workbook);
                for (int i = 2; i < 13; i++) {
                    XSSFRow row = sheet.getRow(i);
                    String s = String.valueOf(i);
                    XSSFCell cell_1 = null;
                    XSSFCell cell_2 = null;
                    switch (s) {
                        case "2"://2，填报单位2-3-4，填报日期6
                            cell_1 = row.getCell(2);
                            cell_1.setCellStyle(style5);
                            cell_1.setCellValue(caseRecord.getCreateOgnName());

                            cell_2 = row.getCell(6);
                            cell_2.setCellStyle(style5_1);
                            cell_2.setCellValue(getDateByValue(sdf, caseRecord.getCreateTime()));
                            break;
                        case "3"://3，案件名称2-底
                            if(StringUtils.isNotBlank(caseRecord.getCaseName())){
                                cell_1 = row.getCell(2);
                                cell_1.setCellStyle(style);
                                cell_1.setCellValue(caseRecord.getCaseName());
                            }
                            break;
                        case "4"://4、风险类别2-底
                            if(StringUtils.isNotBlank(caseRecord.getRiskLevel())){
                                cell_1 = row.getCell(2);
                                cell_1.setCellStyle(style);
                                cell_1.setCellValue(caseRecord.getRiskLevel());
                            }
                            break;
                        case "5"://5、发案单位2，职务4-5-6
                            if(StringUtils.isNotBlank(caseRecord.getOgnPackage())){
                                cell_1 = row.getCell(2);
                                cell_1.setCellStyle(style);
                                cell_1.setCellValue(caseRecord.getOgnPackage());
                            }
                            break;
                        case "6"://6、集团2，职务4-5-6
                            if(StringUtils.isNotBlank(caseRecord.getGroupPackage())){
                                cell_1 = row.getCell(2);
                                cell_1.setCellStyle(style);
                                cell_1.setCellValue(caseRecord.getGroupPackage());
                            }
                            break;
                        case "7"://7、案发时间2，涉案金额4-5-6
                            if(caseRecord.getCaseTime()!= null){
                                cell_1 = row.getCell(2);
                                cell_1.setCellStyle(style2);
                                cell_1.setCellValue(getDateByValue(sdf, caseRecord.getCaseTime()));
                            }

                            if(caseRecord.getCaseMoney()!= null){
                                cell_2 = row.getCell(4);
                                cell_2.setCellStyle(style3);
                                cell_2.setCellValue(caseRecord.getCaseMoney().doubleValue());
                            }
                            break;
                        case "8": //8、案件概要2-底
                            if(StringUtils.isNotBlank(caseRecord.getCaseDetails())){
                                cell_1 = row.getCell(2);
                                cell_1.setCellStyle(style);
                                cell_1.setCellValue(caseRecord.getCaseDetails());
                            }
                            break;
                        case "9"://9、案发原因分析2-底
                            if(StringUtils.isNotBlank(caseRecord.getGreatReason())){
                                cell_1 = row.getCell(2);
                                cell_1.setCellStyle(style);
                                cell_1.setCellValue(caseRecord.getGreatReason());
                            }
                            break;
                        case "10"://10、下一步工作措施2-底
                            if(StringUtils.isNotBlank(caseRecord.getGreatPlan())){
                                cell_1 = row.getCell(2);
                                cell_1.setCellStyle(style);
                                cell_1.setCellValue(caseRecord.getGreatPlan());
                            }
                            break;
                        case "12"://12、解决时限2-3，任务分解4-5-6
                            if(StringUtils.isNotBlank(caseRecord.getTimeLimit())){
                                cell_1 = row.getCell(2);
                                cell_1.setCellStyle(style);
                                cell_1.setCellValue(caseRecord.getTimeLimit());
                            }
                            if(StringUtils.isNotBlank(caseRecord.getTaskDecomposition())){
                                cell_1 = row.getCell(4);
                                cell_1.setCellStyle(style);
                                cell_1.setCellValue(caseRecord.getTaskDecomposition());
                            }
                            break;
                        default:
                            log.info("没有符合的数据");
                    }
                }

                int n = 0;
                if(CollectionUtils.isNotEmpty(reportList)){
                    n = reportList.size();
                    if(reportList.size()>1){
                        sheet.shiftRows(14, sheet.getLastRowNum(), reportList.size()-1);
                        sheet.addMergedRegion(new CellRangeAddress(13, 12+reportList.size(), 0, 0));
                        for (int k = 14; k < 13+reportList.size(); k++) {
                            sheet.addMergedRegion(new CellRangeAddress(k, k, 2, 6));
                        }
                    }
                    for (int i = 0; i < reportList.size(); i++) {
                        int j = i + 13;
                        XSSFRow row = null;
                        row = sheet.getRow(j);
                        if (row == null) {
                            row = sheet.createRow(j);
                        }
                        XSSFCell cell_1 = row.getCell(1)!=null?row.getCell(1):row.createCell(1);
                        XSSFCell cell_2 = row.getCell(2)!=null?row.getCell(2):row.createCell(2);
                        XSSFCell cell_3 = row.getCell(3)!=null?row.getCell(3):row.createCell(3);
                        XSSFCell cell_4 = row.getCell(4)!=null?row.getCell(4):row.createCell(4);
                        XSSFCell cell_5 = row.getCell(5)!=null?row.getCell(5):row.createCell(5);
                        XSSFCell cell_6 = row.getCell(6)!=null?row.getCell(6):row.createCell(6);
                        cell_3.setCellStyle(style);
                        cell_4.setCellStyle(style);
                        cell_5.setCellStyle(style);
                        cell_6.setCellStyle(style);

                        CaseReport caseReport = reportList.get(i);
                        cell_1.setCellStyle(style4);
                        String aa = StringUtils.isNotBlank(caseReport.getReportQuarter())?caseReport.getReportQuarter():"";
                        String bb = StringUtils.isNotBlank(caseReport.getReportYear())?caseReport.getReportYear():"";
                        cell_1.setCellValue(aa + bb);
                        if(StringUtils.isNotBlank(caseReport.getUpdateDescription())){
                            cell_2.setCellStyle(style);
                            cell_2.setCellValue(caseReport.getUpdateDescription());
                        }
                    }
                }
                //16、填报人-2，联系电话-6
                XSSFRow row = sheet.getRow((n==0?1:n)+12+3);
                XSSFCell cell_1 = row.getCell(2);;
                cell_1.setCellStyle(style6);
                cell_1.setCellValue(caseRecord.getCreatePsnName());


                if (caseRecord.getCaseName() == null)
                    continue;

                ZipEntry entry = new ZipEntry(index + "." + caseRecord.getCaseName() + ".xlsx");
                zipOutputStream.putNextEntry(entry);
                workbook.write(zipOutputStream);
                index++;
            } finally {
                if(inputStream!=null){
                    inputStream.close();
                }
            }
        }

        zipOutputStream.flush();
        zipOutputStream.close();
        response.flushBuffer();
    }

    /**
     * 案件台账查询条件
     *
     * @param json  高级检索字段单列，模糊查询字段放数组
     * @param condition 是否排序
     *                      快速分类检索，不适用排序
     * @param wrapper 案件 wrapper
     */
    void getLedgerFilter(JSONObject json, QueryWrapper<CaseRecord> wrapper, Boolean condition) {
        /*
         * =============高级检索字段=================
         */
        // 统计年度
        String statisticsYear = json.containsKey("statisticsYear") ? json.getString("statisticsYear") : null;
        // 当事单位
        String party = json.containsKey("party") ? json.getString("party") : null;
        // 集团案号
        String ognCaseCode = json.containsKey("ognCaseCode") ? json.getString("ognCaseCode") : null;
        // 立案案号
        String caseNumber = json.containsKey("caseNumber") ? json.getString("caseNumber") : null;
        // 重大案件
        Boolean whetherMajor = json.containsKey("whetherMajor") ? json.getBoolean("whetherMajor") : null;
        // 风险类别
        String riskLevel = json.containsKey("riskLevel") ? json.getString("riskLevel") : null;
        // 发案时间(最小值)
        Date caseStartTimeMin = json.containsKey("caseStartTimeMin") ? json.getDate("caseStartTimeMin") : null;
        // 发案时间(最大值)
        Date caseStartTimeMax = json.containsKey("caseStartTimeMax") ? json.getDate("caseStartTimeMax") : null;
        // 结案时间(最小值)
        Date caseEndTimeMin = json.containsKey("caseEndTimeMin") ? json.getDate("caseEndTimeMin") : null;
        // 结案时间(最大值)
        Date caseEndTimeMax = json.containsKey("caseEndTimeMax") ? json.getDate("caseEndTimeMax") : null;
        // 我方地位
        String ourPosition = json.containsKey("ourPosition") ? json.getString("ourPosition") : null;
        // 标的额(最小值)
        BigDecimal caseMoneyMin = json.containsKey("caseMoneyMin") ? json.getBigDecimal("caseMoneyMin") : null;
        // 标的额(最大值)
        BigDecimal caseMoneyMax = json.containsKey("caseMoneyMax") ? json.getBigDecimal("caseMoneyMax") : null;
        // 一审结果
        String firstResult = json.containsKey("firstResult") ? json.getString("firstResult") : null;
        // 案件阶段
        String caseCurrentProcess = json.containsKey("caseCurrentProcess") ? json.getString("caseCurrentProcess") : null;
        // 再审结果
        String retrialResult = json.containsKey("retrialResult") ? json.getString("retrialResult") : null;
        // 二审结果
        String secondResult = json.containsKey("secondResult") ? json.getString("secondResult") : null;
        // 是否结案
        String whetherEnd = json.containsKey("whetherEnd") ? json.getString("whetherEnd") : null;
        // 仲裁结果
        String arbitrationResult = json.containsKey("arbitrationResult") ? json.getString("arbitrationResult") : null;
        // 是否聘请律师
        String whetherAgent = json.containsKey("whetherAgent") ? json.getString("whetherAgent") : null;
        // 案件标签
        String[] tagArr = json.containsKey("tag") ? json.getString("tag") == null ? null : json.getString("tag").split(",") : null;
        // 管辖机构
        String court = json.containsKey("court") ? json.getString("court") : null;
        // 案由
        String causeName = json.containsKey("causeName") ? json.getString("causeName") : null;
        //模糊搜索值
        String fuzzyValue = json.containsKey("fuzzyValue") ? json.getString("fuzzyValue") : null;
        //排序字段
        String orderCol = json.containsKey("orderCol") ? json.getString("orderCol") : null;
        //排序字段是否升序
        boolean orderColValue = json.containsKey("orderColValue") ? json.getBoolean("orderColValue") : false;
        String orgId = json.containsKey("orgId") ? json.getString("orgId") : "";
        Long functionId = DataAuthUtils.getFunctionIdByCode("case_ledger_index");

        /*
         * =============快速检索字段=================
         */
        // 单位类型（业务单元）
        String unitType = json.containsKey("unitType") ? json.getString("unitType") : null;
        // 管理单位
        String managementUnit = json.containsKey("managementUnit") ? json.getString("managementUnit") : null;
        // 案件种类（案件类型）
        String caseKind = json.containsKey("caseKind") ? json.getString("caseKind") : null;
        // 案发地区
        String venueProvince = json.containsKey("venueProvince") ? json.getString("venueProvince") : null;
        // 案件年度
        String caseTimeTag = json.containsKey("caseTimeTag") ? json.getString("caseTimeTag") : null;
        // 案由
        String causeNameTag = json.containsKey("causeNameTag") ? json.getString("causeNameTag") : null;

        /*
         * =============模糊搜索匹配字段=================
         * 案件名称、案由、标的额、案件种类、案件当前阶段、创建人、立案案号
         */
        String[] cols = {"case_name", "cause_name", "case_money", "case_kind", "case_current_process", "create_psn_name", "case_number"};
        Utils.fuzzyValueQuery(wrapper, cols, fuzzyValue);

        if(StringUtils.isNotBlank(statisticsYear))
        {
            String date = statisticsYear+"-01-01 00:00:01";
            String date1 = statisticsYear+"-12-31 23:59:59";
            wrapper.and(i->i.like("date_format(case_time,'%Y-%m-%d')", statisticsYear)
                    .or(j->j.ne("case_current_process", "结案完成").le("case_time",date))
                    .or(k->k.le("case_time",date).eq("case_current_process", "结案完成")
                            .like("date_format(case_end,'%Y-%m-%d')", statisticsYear))
                    .or(l->l.le("case_time",date).eq("case_current_process", "结案完成")
                            .ge("case_end", date1))
            );
        }

        if (StringUtils.isNotBlank(party))
        {
            wrapper.and(i -> i.like("current_Unit", party));

//            QueryWrapper<Parties> partiesQueryWrapper = new QueryWrapper<>();
//            partiesQueryWrapper.like("party", party);
//            List<Parties> parties = partiesService.list(partiesQueryWrapper);
//            ArrayList<String> caseIds = new ArrayList<>();
//            for (Parties party1 : parties)
//            {
//                caseIds.add(party1.getMasterId());
//            }
//
//            if (caseIds.size() > 0)
//            {
//                wrapper.and(i->i.in("ID", caseIds));
//            }
        }

        if (StringUtils.isNotBlank(ognCaseCode))
        {
            wrapper.and(i -> i.like("ogn_case_code", ognCaseCode));
        }

        if (StringUtils.isNotBlank(caseNumber))
        {
            wrapper.and(i -> i.like("case_number", caseNumber));
        }

        if (whetherMajor != null)
        {
            wrapper.and(i -> i.eq("whether_Major", whetherMajor ? 1 : 0));
        }

        if (StringUtils.isNotBlank(riskLevel))
        {
            wrapper.and(i -> i.eq("risk_Level", riskLevel));
        }

        if (caseStartTimeMin != null)
        {
            wrapper.and(i -> i.ge("case_time", caseStartTimeMin));
        }

        if (caseStartTimeMax != null)
        {
            wrapper.and(i -> i.le("case_time", caseStartTimeMax));
        }

        if (caseEndTimeMin != null)
        {
            wrapper.and(i -> i.ge("case_end", caseEndTimeMin));
        }

        if (caseEndTimeMax != null)
        {
            wrapper.and(i -> i.le("case_end", caseEndTimeMax));
        }

        if (StringUtils.isNotBlank(ourPosition))
        {
            wrapper.and(i -> i.eq("OUR_POSITION", ourPosition));
        }

        if (caseMoneyMin != null && caseMoneyMin.doubleValue() > 0)
        {
            wrapper.and(i -> i.ge("case_money", caseMoneyMin));
        }

        if (caseMoneyMax != null && caseMoneyMax.doubleValue() > 0)
        {
            wrapper.and(i -> i.le("case_money", caseMoneyMax));
        }

        if (StringUtils.isNotBlank(firstResult))
        {
            QueryWrapper<CaseRecord> resultWrapper = new QueryWrapper<>();
            resultWrapper.eq("handle_situation", firstResult);
            resultWrapper.eq("case_process_type", "一审");
            resultWrapper.isNotNull("parent_id");
            List<CaseRecord> first = caseLedgerService.list(resultWrapper);
            ArrayList<String> caseIds = new ArrayList<>();

            for (CaseRecord caseRecord : first)
            {
                caseIds.add(caseRecord.getParentId());
            }

            if (caseIds.size() > 0)
                wrapper.and(i->i.in("ID", caseIds));
        }

        if (StringUtils.isNotBlank(caseCurrentProcess))
        {
            wrapper.and(i -> i.like("case_current_process", caseCurrentProcess));

            if (caseCurrentProcess.equals("未结案"))
            {
                wrapper.and(i -> i.ne("case_current_process", "结案完成"));
            }
            else
            {
                if (caseCurrentProcess.equals("一审阶段"))
                {
                    List<String> list = new ArrayList<>();
                    list.add("收立案中");
                    list.add("收立案完成");
                    list.add("管辖一审中");
                    list.add("管辖一审完成");
                    list.add("管辖二审中");
                    list.add("管辖二审完成");
                    list.add("一审中");
                    list.add("一审完成");

                    wrapper.and(i -> i.in("case_current_process", list));
                }
                else
                {
                    wrapper.and(i -> i.like("case_current_process", caseCurrentProcess));
                }
            }
        }

        if (StringUtils.isNotBlank(retrialResult))
        {
            QueryWrapper<CaseRecord> resultWrapper = new QueryWrapper<>();
            resultWrapper.eq("handle_situation", retrialResult);
            resultWrapper.and(i -> i.eq("case_process_type", "再审一审").or().eq("case_process_type", "再审二审"));
            resultWrapper.isNotNull("parent_id");
            List<CaseRecord> retrial = caseLedgerService.list(resultWrapper);
            ArrayList<String> caseIds = new ArrayList<>();

            for (CaseRecord caseRecord : retrial)
            {
                caseIds.add(caseRecord.getParentId());
            }

            if (caseIds.size() > 0)
            {
                wrapper.and(i->i.in("ID", caseIds));
            }
        }

        if (StringUtils.isNotBlank(secondResult))
        {
            QueryWrapper<CaseRecord> resultWrapper = new QueryWrapper<>();
            resultWrapper.eq("handle_situation", secondResult);
            resultWrapper.eq("case_process_type", "二审");
            resultWrapper.isNotNull("parent_id");
            List<CaseRecord> retrial = caseLedgerService.list(resultWrapper);
            ArrayList<String> caseIds = new ArrayList<>();

            for (CaseRecord caseRecord : retrial)
            {
                caseIds.add(caseRecord.getParentId());
            }

            if (caseIds.size() > 0)
            {
                wrapper.and(i->i.in("ID", caseIds));
            }
        }

        if (StringUtils.isNotBlank(arbitrationResult))
        {
            QueryWrapper<CaseRecord> resultWrapper = new QueryWrapper<>();
            resultWrapper.eq("handle_situation", arbitrationResult);
            resultWrapper.eq("case_process_type", "仲裁");
            resultWrapper.isNotNull("parent_id");
            List<CaseRecord> arbitration = caseLedgerService.list(resultWrapper);
            ArrayList<String> caseIds = new ArrayList<>();

            for (CaseRecord caseRecord : arbitration)
            {
                caseIds.add(caseRecord.getParentId());
            }

            if (caseIds.size() > 0)
            {
                wrapper.and(i->i.in("ID", caseIds));
            }
        }

        if (StringUtils.isNotBlank(whetherAgent))
        {
            QueryWrapper<Agent> agentQueryWrapper = new QueryWrapper<>();
            agentQueryWrapper.eq("whether_lawyer", whetherAgent);
            List<Agent> agentList = agentService.list(agentQueryWrapper);
            ArrayList<String> caseIds = new ArrayList<>();
            for (Agent agent : agentList)
            {
                caseIds.add(agent.getParentId());
            }
            if (caseIds.size() > 0)
                wrapper.and(i->i.in("ID", caseIds));
        }

        if (StringUtils.isNotBlank(whetherEnd))
        {
            if ("1".equals(whetherEnd))
            {
                wrapper.and(i->i.eq("WHETHER_END", whetherEnd));
            }
            else
            {
                wrapper.and(i->i.eq("WHETHER_END", whetherEnd).or().isNull("WHETHER_END"));
            }
        }

        if (tagArr != null && tagArr.length > 0)
        {
            QueryWrapper<Tag> tagWrapper = new QueryWrapper<>();
            for (String s : tagArr)
            {
                tagWrapper.or().like("name", s);
            }
            tagWrapper.eq("is_delete", 0);
            List<Tag> name = tagService.list(tagWrapper);
            if(CollectionUtils.isNotEmpty(name)){
                List<String> strings = new ArrayList<>();
                for (Tag tag1 : name) {
                    String dataId = tag1.getDataId();
                    strings.add(dataId);
                }
                wrapper.and(i -> i.in("id", strings));
            }else{
                wrapper.ne("1", "1");
            }
        }

        if (StringUtils.isNotBlank(court))
        {
            wrapper.and(i -> i.like("court", court));
        }

        if (StringUtils.isNotBlank(causeName))
        {
            wrapper.and(i -> i.like("cause_name", causeName));
        }

        if (StringUtils.isNotBlank(unitType))
        {
            wrapper.and( i -> i.like("UNIT_TYPE", unitType));
        }

        if (StringUtils.isNotBlank(managementUnit))
        {
            String[] arr = managementUnit.split("、");

            if (arr.length > 1)
            {
                wrapper.and( i -> i.in("MANAGEMENT_UNIT", arr));
            }
            else
            {
                wrapper.and( i -> i.eq("MANAGEMENT_UNIT", managementUnit));
            }
        }

        if (StringUtils.isNotBlank(caseKind))
        {
            wrapper.and(i -> i.eq("CASE_KIND", caseKind));
        }

        if (StringUtils.isNotBlank(venueProvince))
        {
            wrapper.and(i -> i.eq("VENUE_PROVINCE", venueProvince));
        }

        if (StringUtils.isNotBlank(caseTimeTag))
        {
            wrapper.and(i -> i.eq("to_char(case_time, 'yyyy')", caseTimeTag));
        }

        if (StringUtils.isNotBlank(causeNameTag))
        {
            wrapper.and(i -> i.eq("cause_name", causeNameTag));
        }

        wrapper.and(i -> i.isNull("parent_id"));

        wrapper.and(i -> (i.isNotNull("case_kind")).getCustomSqlSegment());

        wrapper.and(i -> i.ne("case_current_process", "收立案中"));

        DataAuthUtils.dataPermSql(wrapper, null, functionId, orgId,"", "case");

        if (condition)
        {
            if (StringUtils.isNotBlank(orderCol))
            {
                if (orderCol.equals("caseCurrentProcess"))
                {
                    wrapper.orderBy(true, orderColValue, caseProcessOrder());
                } else
                {
                    wrapper.orderBy(true, orderColValue, Utils.humpToLine2(orderCol));
                }
            }
            else
            {
                wrapper.orderBy(true, false, "create_time");
            }
        }

    }

    /**
     * 案件阶段排序(根据定好的阶段顺序)
     */
    private String caseProcessOrder() {
        return " case case_current_process "
                + "when '收立案中'                      then 1 "
                + "when '收立案完成'                  then 2 "
                + "when '诉前调解中'                  then 2.1 "
                + "when '诉前调解完成'                  then 2.2 "
                + "when '管辖一审中'                  then 3 "
                + "when '管辖一审完成'              then 4 "
                + "when '管辖二审中'              then 5 "
                + "when '管辖二审完成'          then 6 "
                + "when '一审中'                     then 7 "
                + "when '一审完成'                 then 8 "
                + "when '仲裁中'                     then 8.1 "
                + "when '仲裁完成'                 then 8.2 "
                + "when '撤销仲裁中'                     then 8.3 "
                + "when '撤销仲裁完成'                 then 8.4 "
                + "when '重审中'             then 8.5 "
                + "when '重审完成'          then 8.6 "
                + "when '重审一审中'             then 9 "
                + "when '重审一审完成'          then 10 "
                + "when '再审一审中'              then 11 "
                + "when '再审一审完成'          then 12 "
                + "when '二审中'                     then 13 "
                + "when '二审完成'                 then 14 "
                + "when '重审二审中'             then 15 "
                + "when '重审二审完成'         then 16 "
                + "when '再审二审中'             then 17 "
                + "when '再审二审完成'         then 18 "
                + "when '再审申请中'             then 19 "
                + "when '再审申请完成'         then 20 "
                + "when '执行中'                    then 21 "
                + "when '执行完成'                then 22 "
                + "when '结案中'                    then 23 "
                + "when '结案完成'                then 24 "
                + "when '不起诉'                then 25 "
                + " else 99 end ";
    }

    private XSSFCellStyle createCaseLedgerCellStyle(XSSFWorkbook workbook) {
        XSSFFont font = workbook.createFont();
        font.setFontName("宋体");
        font.setFontHeightInPoints((short) 10);
        XSSFCellStyle borderStyle = workbook.createCellStyle();
        borderStyle.setFont(font);
        borderStyle.setVerticalAlignment(VerticalAlignment.CENTER);//水平居中且垂直居中
        borderStyle.setAlignment(HorizontalAlignment.CENTER);
        borderStyle.setWrapText(true);
        borderStyle.setBorderBottom(BorderStyle.THIN);
        borderStyle.setBorderLeft(BorderStyle.THIN);
        borderStyle.setBorderTop(BorderStyle.THIN);
        borderStyle.setBorderRight(BorderStyle.THIN);
        return borderStyle;
    }

    /**
     * 设置案件数据
     * @param row 当前行
     * @param caseRecord 当前案件
     * @param num 模板中的函行数
     * @param style 通用样式
     * @param style2 日期样式
     * @param partiesList2 当前案件的当事人信息
     * @param agentList2 当前案件的代理人信息
     * @param tagList2 当前案件的标签信息
     * @param childList 当前案件的子节点信息（不包含）
     * @param list2 当前案件的二审信息
     * @param ershenPartiesList 当前案件的所有二审案件的当事人信息
     */
    private void setCellsByCase(XSSFRow row, CaseRecord caseRecord, int num, XSSFCellStyle style, XSSFCellStyle style2, XSSFCellStyle style3,List<Parties> partiesList2,List<Agent> agentList2, List<Tag> tagList2, List<CaseRecord> childList, List<CaseRecord> list2, List<Parties> ershenPartiesList) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        //收立案阶段-当事人
        List<Parties> partiesList = partiesList2.stream().filter(i->i.getMasterId().equals(caseRecord.getId())).collect(Collectors.toList());
        String yuangao = "";
        StringBuilder yuangaos = new StringBuilder();
        StringBuilder beigaos = new StringBuilder();
        StringBuilder shenqingrens = new StringBuilder();
        StringBuilder beishenqingrens = new StringBuilder();
        StringBuilder disanrens = new StringBuilder();
        StringBuilder woxingzhi = new StringBuilder();
        StringBuilder taxingzhi = new StringBuilder();
        String position = caseRecord.getOurPosition();
        if (CollectionUtils.isNotEmpty(partiesList)) {
            for (Parties parties : partiesList) {
                if (StringUtils.isNotBlank(position) && position.equals(parties.getPartyType()) && "system".equals(parties.getIsSystemCreate())) {
                    yuangao = parties.getParty();
                    break;
                }
            }
            if(StringUtils.isBlank(yuangao)){
                for (Parties parties : partiesList) {
                    if (StringUtils.isNotBlank(position) && position.equals(parties.getPartyType())) {
                        yuangao = parties.getParty();
                        break;
                    }
                }
            }
            for (Parties parties : partiesList)
            {
                String partyType = parties.getPartyType();
                String party = parties.getParty();
                String partyNature = parties.getPartyNature();

                if (StringUtils.isNotBlank(party) && StringUtils.isNotBlank(partyType))
                {
                    switch (partyType)
                    {
                        case "原告":
                            yuangaos.append(party).append(";");
                            break;
                        case "被告":
                            beigaos.append(party).append(";");
                            break;
                        case "申请人":
                            shenqingrens.append(party).append(";");
                            break;
                        case "被申请人":
                            beishenqingrens.append(party).append(";");
                            break;
                        default:
                            disanrens.append(party).append(";");
                            break;
                    }
                }

                if (StringUtils.isNotBlank(position) && StringUtils.isNotBlank(partyType) && StringUtils.isNotBlank(partyNature))
                {
                    if (parties.getPartyType().equals(position))
                        woxingzhi.append(partyNature).append(",");
                    else
                        taxingzhi.append(partyNature).append(",");
                }
            }
        }
        //收立案代理信息
        List<Agent> agentList = agentList2.stream().filter(i->i.getParentId().equals(caseRecord.getId())).collect(Collectors.toList());
        boolean whetherLawyer = false;
        String lawyers = "";
        if(CollectionUtils.isNotEmpty(agentList)){
            whetherLawyer = agentList.stream().anyMatch(Agent::isWhetherLawyer);
            for (Agent agent : agentList) {
                if(StringUtils.isNotBlank(agent.getLawyerName())){
                    if("".equals(lawyers)){
                        lawyers = agent.getLawyerName();
                    }else{
                        lawyers += agent.getLawyerName();
                    }
                }
            }
        }
        //一审
        List<CaseRecord> yishenList = getCase(childList, "一审", caseRecord.getId());
        CaseRecord yishen = getCase2(yishenList);
        //二审
        List<CaseRecord> ershenList = getCase(list2, "二审", caseRecord.getId());
        CaseRecord ershen = getCase2(ershenList);
        String ershenssrs = "";
        String ershenbssrs = "";
        if (CollectionUtils.isNotEmpty(ershenList)) {
            if (ershen != null) {
                //二审当事人
                List<Parties> list3 = new ArrayList<>();
                if(CollectionUtils.isNotEmpty(ershenPartiesList)){
                    for (Parties parties : ershenPartiesList)
                    {
                        if (ershen.getId().equals(parties.getMasterId()))
                        {
                            list3.add(parties);
                        }
                    }
                }
                if (CollectionUtils.isNotEmpty(list3)) {
                    for (Parties parties : list3) {
                        if (parties.getPartyType()!=null&& parties.getPartyType().contains("上诉人")) {
                            if ("".equals(ershenssrs)) {
                                ershenssrs = parties.getParty();
                            } else {
                                ershenssrs += "、" + parties.getParty();
                            }
                        }
                        if (parties.getPartyType()!=null&& parties.getPartyType().contains("被上诉人")) {
                            if ("".equals(ershenbssrs)) {
                                ershenbssrs = parties.getParty();
                            } else {
                                ershenbssrs += "、" + parties.getParty();
                            }
                        }
                    }
                }
            }
        }
        //再审一审
        List<CaseRecord> zaishenyishenList = getCase(childList, "再审一审", caseRecord.getId());
        CaseRecord zaishenyishen = getCase2(zaishenyishenList);
        //再审二审
        List<CaseRecord> zaishenershenList = getCase(childList, "再审二审", caseRecord.getId());
        CaseRecord zaishenershen = getCase2(zaishenershenList);
        //执行
        List<CaseRecord> zhixingList = getCase(childList, "执行", caseRecord.getId());
        CaseRecord zhixing = getCase2(zhixingList);
        //结案
        List<CaseRecord> jieanList = getCase(childList, "结案", caseRecord.getId());
        CaseRecord jiean = getCase2(jieanList);
        //标签
        List<Tag> tagList = tagList2.stream().filter(i->i.getDataId().equals(caseRecord.getId())).collect(Collectors.toList());
        StringBuilder tags = new StringBuilder();
        if(CollectionUtils.isNotEmpty(tagList))
        {
            for (Tag tag : tagList)
            {
                tags.append(tag.getName()).append(",");
            }
            tags = new StringBuilder(tags.substring(0, tags.length() - 1));
        }
        //包钢项目管理单位、单位类型删除，重新调整部分字段
        for (int i = 0; i < 85; i++)
        {
            XSSFCell cell = row.createCell(i);
            if(i == 12||i==27||i==50||i==55||i==60||i==64||i==69)
            {
                //时间格式
                cell.setCellStyle(style2);
            }
            else
            {
                if(i==13||i==49||i==62||i==71||i==72||i==73||i==74||i==75||i==76)
                {
                    //金额格式
                    cell.setCellStyle(style3);
                    cell.setCellType(CellType.NUMERIC);
                }
                else
                {
                    cell.setCellStyle(style);
                }
            }

            String s = String.valueOf(i);
            switch (s) {
                case "0"://序号
                    cell.setCellValue(num);
                    break;
                /*case "1"://单位类型
                    if (StringUtils.isNotBlank(caseRecord.getUnitType())) {
                        cell.setCellValue(caseRecord.getUnitType());
                    }
                    break;
                case "2"://管理单位
                    if(StringUtils.isNotBlank(caseRecord.getManagementUnit())){
                        cell.setCellValue(caseRecord.getManagementUnit());
                    }
                    break;*/
                case "1"://当事单位《--》三级
                    cell.setCellValue(yuangao);
                    break;
                case "2"://是否并表
                    cell.setCellValue(caseRecord.getWhetherMerge());
                    break;
                case "3"://股权类型
                    if (caseRecord.getEquityType()!=null && StringUtils.isNotBlank(caseRecord.getEquityType())) {
                        cell.setCellValue(caseRecord.getEquityType());
                    }
                    break;
                case "4"://标签
                    cell.setCellValue(tags.toString());
                    break;
                case "5"://案件名称
                    if (caseRecord.getCaseName()!=null && StringUtils.isNotBlank(caseRecord.getCaseName())) {
                        cell.setCellValue(caseRecord.getCaseName());
                    }
                    break;
                case "6"://是否重大
                    if (caseRecord.getWhetherMajor() != null) {
                        cell.setCellValue(caseRecord.getWhetherMajor() ? "重大" : "一般");
                    }
                    break;
                case "7"://案件风险类型
                    if (caseRecord.getRiskLevel() != null && StringUtils.isNotBlank(caseRecord.getRiskLevel())) {
                        cell.setCellValue(caseRecord.getRiskLevel());
                    }
                    break;
                case "8"://一审案号《--》
                    if (yishen!=null && StringUtils.isNotBlank(yishen.getCaseNumber())) {
                        cell.setCellValue(caseRecord.getCaseNumber());
                    }
                    break;
                case "9"://收立案号《--》
                    if (StringUtils.isNotBlank(caseRecord.getCaseNumber())) {
                        cell.setCellValue(caseRecord.getCaseNumber());
                    }
                    break;
                /**
                 * 案件解决方式，导出为仲裁、诉讼、刑事、境外；其中劳动仲裁、商事仲裁统一为仲裁，民事诉讼、行政诉讼统一为诉讼
                 */
                case "10":
                    if (StringUtils.isNotBlank(caseRecord.getCaseKind()))
                    {
                        switch (caseRecord.getCaseKind())
                        {
                            case "劳动仲裁":
                            case "商事仲裁":
                                cell.setCellValue("仲裁");
                                break;
                            case "民事诉讼":
                            case "行政诉讼":
                                cell.setCellValue("诉讼");
                                break;
                            case "刑事案件":
                                cell.setCellValue("刑事");
                                break;
                            case "境外案件":
                                cell.setCellValue("境外");
                                break;
                            default:
                                cell.setCellValue(caseRecord.getCaseKind());
                        }
                    }
                    break;
                case "11"://涉案单位管理层级
                    if (caseRecord.getInvolvedLevel() != null && StringUtils.isNotBlank(caseRecord.getInvolvedLevel())) {
                        cell.setCellValue(caseRecord.getInvolvedLevel());
                    }
                    break;
                case "12"://案发时间
                    if (caseRecord.getCaseTime() != null) {
                        cell.setCellValue(getDateByValue(sdf, caseRecord.getCaseTime()));
                    }
                    break;
                case "13"://涉案金额
                    if (caseRecord.getCaseMoney() != null) {
                        cell.setCellValue(caseRecord.getCaseMoney().doubleValue());
                    }
                    break;
                /*case "14"://是否为小微案件
                    if(caseRecord.getWhether2()!=null){
                        cell.setCellValue(caseRecord.getWhether2() ? "是" : "否");
                    }
                    break;*/
                case "14"://我方地位
                    if (caseRecord.getOurPosition()!=null && StringUtils.isNotBlank(caseRecord.getOurPosition())) {
                        cell.setCellValue(caseRecord.getOurPosition());
                    }
                    break;
                case "15"://原告
                    String substring1 =StringUtils.isNotBlank(yuangaos)?yuangaos.substring(0, yuangaos.length() - 1):"";
                    cell.setCellValue(substring1);
                    break;
                case "16"://被告
                    String substring2 =StringUtils.isNotBlank(beigaos)?beigaos.substring(0, beigaos.length() - 1):"";
                    cell.setCellValue(substring2);
                    break;
                case "17"://第三人
                    String substring3 =StringUtils.isNotBlank(disanrens)?disanrens.substring(0, disanrens.length() - 1):"";
                    cell.setCellValue(substring3);
                    break;
                case "18"://申请人
                    String substring4 =StringUtils.isNotBlank(shenqingrens)?shenqingrens.substring(0, shenqingrens.length() - 1):"";
                    cell.setCellValue(substring4);
                    break;
                case "19"://被申请人
                    String substring5 =StringUtils.isNotBlank(beishenqingrens)?beishenqingrens.substring(0, beishenqingrens.length() - 1):"";
                    cell.setCellValue(substring5);
                    break;
                case "20"://案由名称
                    if(StringUtils.isNotBlank(caseRecord.getCauseName())){
                        cell.setCellValue(caseRecord.getCauseName());
                    }
                    break;
                case "21"://案件当前所处阶段
                    if (StringUtils.isNotBlank(caseRecord.getCaseCurrentProcess())) {
                        cell.setCellValue(caseRecord.getCaseCurrentProcess());
                    }
                    break;
                case "22"://是否存在二审
                    if (caseRecord.getCaseProcess()!=null && StringUtils.isNotBlank(caseRecord.getCaseProcess())) {
                        String[] split = caseRecord.getCaseProcess().split(",");
                        int binarySearch = Arrays.binarySearch(split, "二审");
                        cell.setCellValue(binarySearch > 0 ? "是" : "否");
                    }
                    break;
                case "23"://是否存在再审-再审、再审一审、再审二审有一个就行
                    if (caseRecord.getCaseProcess()!=null && StringUtils.isNotBlank(caseRecord.getCaseProcess())) {
                        cell.setCellValue(caseRecord.getCaseProcess().contains("再审") ? "是" : "否");
                    }
                    break;
                case "24"://结案情况
                    if (caseRecord.getWhetherEnd() != null)
                    {
                        cell.setCellValue(caseRecord.getWhetherEnd() ? "已结案" : "未结案");
                    }
                    break;
                case "25"://案件结果
                    if (jiean != null && jiean.getHandleSituation()!=null && StringUtils.isNotBlank(jiean.getHandleSituation())) {
                        cell.setCellValue(jiean.getHandleSituation());
                    }
                    break;
                case "26"://结案时间
                    if(jiean != null && jiean.getCaseTime()!=null){
                        cell.setCellValue(getDateByValue(sdf,jiean.getCaseTime()));
                    }
                    break;
                case "27"://我方当事人单位性质
                    String substring6 =StringUtils.isNotBlank(woxingzhi)?woxingzhi.substring(0, woxingzhi.length() - 1):"";
                    cell.setCellValue(substring6);
                    break;
                case "28"://他方当事人单位性质
                    String substring7 =StringUtils.isNotBlank(taxingzhi)?taxingzhi.substring(0, taxingzhi.length() - 1):"";
                    cell.setCellValue(substring7);
                    break;

                case "29"://重大案件基本情况
                    if (caseRecord.getCaseDetails()!=null && StringUtils.isNotBlank(caseRecord.getCaseDetails())) {
                        cell.setCellValue(caseRecord.getCaseDetails());
                    }
                    break;
                case "30"://重大案件     下一步工作计划
                    if (caseRecord.getGreatPlan() != null && StringUtils.isNotBlank(caseRecord.getGreatPlan())) {
                        cell.setCellValue(caseRecord.getGreatPlan());
                    }
                    break;
                case "31"://重大案件   案发原因分析
                    if (caseRecord.getGreatReason()!=null && StringUtils.isNotBlank(caseRecord.getGreatReason())) {
                        cell.setCellValue(caseRecord.getGreatReason());
                    }
                    break;
                case "32"://收立案机构《--》
                    if (StringUtils.isNotBlank(caseRecord.getCourt())) {
                        cell.setCellValue(caseRecord.getCourt());
                    }
                    break;
                case "33"://一审法院
                    if (yishen != null && yishen.getCourt()!=null) {
                        cell.setCellValue(yishen.getCourt());
                    }
                    break;
                case "34"://一审判决金额
                    if (yishen != null && yishen.getCaipan() != null) {
                        cell.setCellValue(yishen.getCaipan().doubleValue());
                    }
                    break;
                case "35"://一审判断时间
                    if (yishen != null && yishen.getCaseTime() != null) {
                        cell.setCellValue(getDateByValue(sdf,yishen.getCaseTime()));
                    }
                    break;
                case "36"://一审案件结果
                    if (yishen != null && StringUtils.isNotBlank(yishen.getHandleSituation())) {
                        cell.setCellValue(yishen.getHandleSituation());
                    }
                    break;
                case "37"://二审法院
                    if (ershen != null && StringUtils.isNotBlank(ershen.getCourt())) {
                        cell.setCellValue(ershen.getCourt());
                    }
                    break;
                case "38"://二审上诉人
                    cell.setCellValue(ershenssrs);
                    break;
                case "39"://二审被上诉人
                    cell.setCellValue(ershenbssrs);
                    break;
                case "40"://二审判决决时间
                    if (ershen != null && ershen.getCaseTime() != null) {
                        cell.setCellValue(getDateByValue(sdf,ershen.getCaseTime()));
                    }
                    break;
                case "41"://二审判决金额(元)
                    if (ershen != null && ershen.getCaipan() != null && ershen.getCaipan().compareTo(BigDecimal.ZERO)!=0) {
                        cell.setCellValue(ershen.getCaipan().doubleValue());
                    } else {
//                        cell.setCellValue(0);
                    }
                    break;
                case "42"://二审案件结果
                    if (ershen != null && StringUtils.isNotBlank(ershen.getHandleSituation())) {
                        cell.setCellValue(ershen.getHandleSituation());
                    }
                    break;
                case "43"://二审是否撤诉
                    if (ershen != null && StringUtils.isNotBlank(ershen.getHandleSituation()) && ershen.getHandleSituation().contains("裁定：上诉人撤回上诉")) {
                        cell.setCellValue("是");
                    }
                    else
                    {
                        cell.setCellValue("否");
                    }
                    break;
                case "44"://再审--审-法院
                    if (zaishenyishen != null && StringUtils.isNotBlank(zaishenyishen.getCourt())) {
                        cell.setCellValue(zaishenyishen.getCourt());
                    }
                    break;
                case "45"://再审--审-判决时间
                    if (zaishenyishen != null && zaishenyishen.getCaseTime() != null) {
                        cell.setCellValue(getDateByValue(sdf,zaishenyishen.getCaseTime()));
                    }
                    break;
                case "46"://再审--审-案件结果
                    if (zaishenyishen != null && StringUtils.isNotBlank(zaishenyishen.getHandleSituation())) {
                        cell.setCellValue(zaishenyishen.getHandleSituation());
                    }
                    break;
                case "47"://再审--审-判决金额(元)
                    if (zaishenyishen != null && zaishenyishen.getCaipan() != null) {
                        cell.setCellValue(zaishenyishen.getCaipan().doubleValue());
                    }
                    break;
                case "48"://再审二审法院
                    if (zaishenershen != null && StringUtils.isNotBlank(zaishenershen.getCourt())) {
                        cell.setCellValue(zaishenershen.getCourt());
                    }
                    break;
                case "49"://再审二审判决时间
                    if (zaishenershen != null && zaishenershen.getCaseTime() != null) {
                        cell.setCellValue(getDateByValue(sdf,zaishenershen.getCaseTime()));
                    }
                    break;
                case "50"://再审二审是否撤诉
                    if (zaishenershen != null && StringUtils.isNotBlank(zaishenershen.getHandleSituation()) && zaishenershen.getHandleSituation().contains("裁定：上诉人撤回上诉")) {
                        cell.setCellValue("是");
                    }
                    break;
                case "51"://再审二审-是否发回重审
                    if (zaishenershen != null && zaishenershen.getWhetherContinue() != null) {
                        cell.setCellValue(zaishenershen.getWhetherContinue() ? "是" : "否");
                    }
                    break;
                case "52"://仲裁机构--收立案阶段的数据
                    if (caseRecord.getCaseKind().contains("仲裁") && StringUtils.isNotBlank(caseRecord.getCourt())) {
                        cell.setCellValue(caseRecord.getCourt());
                    }
                    break;
                case "53"://仲裁类型--取商事仲裁或劳动仲裁
                    if (caseRecord.getCaseKind().contains("仲裁")) {
                        cell.setCellValue(caseRecord.getCaseKind());
                    }
                    break;
                case "54"://仲裁裁决时间
                    if (caseRecord.getCaseKind().contains("仲裁") && jiean != null && jiean.getCaseTime() != null) {
                        cell.setCellValue(getDateByValue(sdf,jiean.getCaseTime()));
                    }
                    break;
                case "55"://仲裁案件结果
                    if (caseRecord.getCaseKind().contains("仲裁") && jiean != null && StringUtils.isNotBlank(jiean.getHandleSituation())) {
                        cell.setCellValue(jiean.getHandleSituation());
                    }
                    break;
                case "56"://结案-执行收回金额(元)
                    if (jiean != null && jiean.getActualReceiveMoney() != null) {
                        cell.setCellValue(jiean.getActualReceiveMoney().doubleValue());
                    }
                    break;
                case "57"://结案-执行收回其他财产(元)
                    if (jiean != null && jiean.getActualReceiveOther() != null) {
                        cell.setCellValue(jiean.getActualReceiveOther().doubleValue());
                    }
                    break;
                case "58"://结案-执行支出金额(元)
                    if (jiean != null && jiean.getActualSendMoney() != null) {
                        cell.setCellValue(jiean.getActualSendMoney().doubleValue());
                    }
                    break;
                case "59"://结案-执行支出其他财产(元)
                    if (jiean != null && jiean.getActualSendOther() != null) {
                        cell.setCellValue(jiean.getActualSendOther().doubleValue());
                    }
                    break;
                case "60"://结案-挽回经济损失(元)
                    if (jiean != null && jiean.getAvoidEconomicLosses() != null) {
                        cell.setCellValue(jiean.getAvoidEconomicLosses().doubleValue());
                    }
                    break;
                case "61"://结案-造成经济损失(元)
                    if (jiean != null && jiean.getCauseEconomicLosses() != null) {
                        cell.setCellValue(jiean.getCauseEconomicLosses().doubleValue());
                    }
                    break;
                case "62"://执行-是否强制执行或被强制执行
                    if (zhixing != null && zhixing.getWhetherForceExecute() != null) {
                        cell.setCellValue(zhixing.getWhetherForceExecute() ? "是" : "否");
                    }
                    break;
                case "63"://执行法院
                    if (zhixing != null && zhixing.getCourt() != null) {
                        cell.setCellValue(zhixing.getCourt());
                    }
                    break;
                case "64"://收立案-重大案件包案领导（公司级领导）
                    if (StringUtils.isNotBlank(caseRecord.getOgnPackage())) {
                        cell.setCellValue(caseRecord.getOgnPackage());
                    }
                    break;
                case "65"://收立案-重大案件包案领导（集团领导）
                    if (StringUtils.isNotBlank(caseRecord.getGroupPackage())) {
                        cell.setCellValue(caseRecord.getGroupPackage());
                    }
                    break;
                case "66"://是否聘请律师--收立案，循环，有一条是就行
                    cell.setCellValue(whetherLawyer?"是":"否");
                    break;
                case "67"://律师事务所及律师情况
                    //收立案-代理信息中，是外聘律师，需要去律师库中选，然后带过来律师、律所信息，然后将信息拼接在一起
                    cell.setCellValue(lawyers);
                    break;
                case "68"://备注-是给客户自定义提供的
                    cell.setCellValue("");
                    break;
                default:
                    log.info("没有符合的数据");
            }
        }
    }

    private List<CaseRecord> getCase(List<CaseRecord> childList, String caseProcessType, String parentId) {
        List<CaseRecord> list = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(childList)){
            for (CaseRecord caseRecord : childList)
            {
                if (StringUtils.isNotBlank(caseProcessType) && StringUtils.isNotBlank(parentId) && caseProcessType.equals(caseRecord.getCaseProcessType()) && parentId.equals(caseRecord.getParentId()))
                {
                    list.add(caseRecord);
                }
            }
        }
        return list;
    }

    private CaseRecord getCase2(List<CaseRecord> childList) {
        CaseRecord caseRecord = null;
        if (CollectionUtils.isNotEmpty(childList)) {
            Collections.sort(childList, new Comparator<CaseRecord>() {
                @Override
                //定义一个比较器
                public int compare(CaseRecord o1, CaseRecord o2) {
                    try {
                        if (o1.getCreateTime().getTime() > o2.getCreateTime().getTime()) {
                            return -1;
                        } else if (o1.getCreateTime().getTime() < o2.getCreateTime().getTime()) {
                            return 1;
                        } else {
                            return 0;
                        }
                    } catch (Exception e) {
                        log.info("printStackTrace异常");
                    }
                    return 0;
                }
            });
            caseRecord = childList.get(0);
        }
        return caseRecord;
    }

    private Date getDateByValue(SimpleDateFormat sdf, Date dd) {
        String format = sdf.format(dd);
        Date date = null;
        try {
            date=sdf.parse(format);
        } catch (ParseException e) {
            log.info("printStackTrace异常");
        }
        return date;
    }

    private XSSFCellStyle createGreatCaseZipCellStyle(XSSFWorkbook workbook) {
        XSSFFont font = workbook.createFont();
        font.setFontName("楷体_GB2312");
        font.setFontHeightInPoints((short) 10);
        XSSFCellStyle borderStyle = workbook.createCellStyle();
        borderStyle.setFont(font);
        borderStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        borderStyle.setAlignment(HorizontalAlignment.LEFT);
        borderStyle.setWrapText(true);
        borderStyle.setBorderBottom(BorderStyle.THIN);
        borderStyle.setBorderLeft(BorderStyle.THIN);
        borderStyle.setBorderTop(BorderStyle.THIN);
        borderStyle.setBorderRight(BorderStyle.THIN);
        return borderStyle;
    }
    private XSSFCellStyle createGreatCaseZipCellStyle2(XSSFWorkbook workbook) {
        XSSFFont font = workbook.createFont();
        font.setFontName("楷体_GB2312");
        font.setFontHeightInPoints((short) 10);
        XSSFCellStyle borderStyle = workbook.createCellStyle();
        borderStyle.setFont(font);
        borderStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        borderStyle.setAlignment(HorizontalAlignment.LEFT);
        borderStyle.setWrapText(true);
        borderStyle.setBorderBottom(BorderStyle.THIN);
        return borderStyle;
    }
    private XSSFCellStyle createGreatCaseZipCellStyle2_1(XSSFWorkbook workbook) {
        XSSFFont font = workbook.createFont();
        font.setFontName("楷体_GB2312");
        font.setFontHeightInPoints((short) 10);
        XSSFCellStyle borderStyle = workbook.createCellStyle();
        borderStyle.setFont(font);
        borderStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        borderStyle.setAlignment(HorizontalAlignment.LEFT);
        borderStyle.setWrapText(true);
        borderStyle.setBorderBottom(BorderStyle.THIN);
        return borderStyle;
    }
    private XSSFCellStyle createGreatCaseZipCellStyle3(XSSFWorkbook workbook) {
        XSSFFont font = workbook.createFont();
        font.setFontName("楷体_GB2312");
        font.setFontHeightInPoints((short) 10);
        XSSFCellStyle borderStyle = workbook.createCellStyle();
        borderStyle.setFont(font);
        borderStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        borderStyle.setAlignment(HorizontalAlignment.LEFT);
        borderStyle.setWrapText(true);
        return borderStyle;
    }
    private XSSFCellStyle createGreatCaseZipCellStyle4(XSSFWorkbook workbook) {
        XSSFFont font = workbook.createFont();
        font.setFontName("楷体_GB2312");
        font.setFontHeightInPoints((short) 11);
        XSSFCellStyle borderStyle = workbook.createCellStyle();
        borderStyle.setFont(font);
        borderStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        borderStyle.setAlignment(HorizontalAlignment.CENTER);
        borderStyle.setBorderBottom(BorderStyle.THIN);
        borderStyle.setBorderLeft(BorderStyle.THIN);
        borderStyle.setBorderTop(BorderStyle.THIN);
        borderStyle.setBorderRight(BorderStyle.THIN);
        borderStyle.setWrapText(true);

        font.setBold(true);
        borderStyle.setFont(font);
        return borderStyle;
    }
}