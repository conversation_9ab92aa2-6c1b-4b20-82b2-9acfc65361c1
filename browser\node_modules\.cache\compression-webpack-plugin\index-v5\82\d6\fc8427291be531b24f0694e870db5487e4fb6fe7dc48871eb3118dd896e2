
2df88df9d2ca5d2d060c6cb9af6fb17cb8f27468	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.85.1754018536329.js\",\"contentHash\":\"9c224a7f4082214cd09ec070374b0119\"}","integrity":"sha512-0EVCx2GxvuLGiHmZfprJ9gnNE4JhjkyQIv6A7yWIcG2ugVUjGRm2EmvayAJCcvwld3krplVK91KXEsRtLS/23A==","time":1754018575979,"size":127290}