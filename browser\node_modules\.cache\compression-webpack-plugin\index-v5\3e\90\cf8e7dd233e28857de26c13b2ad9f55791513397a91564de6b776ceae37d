
03612847d59d92947e5de237a3348dd2c55b4c7f	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.17.1754018536329.js\",\"contentHash\":\"7586039114ed6c4e9f969938575ff793\"}","integrity":"sha512-ac67qMOHmRxX8cvoXHaxJJ85/JlzHQxBuZjlUssq0hfH7lrvtt4EsOiXM80u3u4rAZVJKYhi252iZQZn+zntcg==","time":1754018575954,"size":44710}