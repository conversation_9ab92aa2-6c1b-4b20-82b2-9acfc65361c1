<div class="modal" ng-controller="KisBpmConditionExpressionPopupCtrl">
    <div class="modal-dialog modal-wide">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true" ng-click="close()">&times;</button>
                <h2 translate>PROPERTY.SEQUENCEFLOW.CONDITION.TITLE</h2>
            </div>
            <div class="modal-body">
                <div class="detail-group clearfix">
                    <div class="form-group clearfix">
                        <div class="col-xs-12">
                            <label class="col-xs-3">表达式生成器</label>
                            <div class="col-xs-9">
                                <div class="btn-group"> <a class="btn btn-icon btn-lg" rel="tooltip" data-title="{{ACTION.ADD | translate}}" data-placement="bottom" data-original-title="" title="" ng-click="addGroupListener()">添加组</a>
                                    <a class="btn btn-icon btn-lg" rel="tooltip" data-title="{{ACTION.REMOVE | translate}}" data-placement="bottom" data-original-title="" title="" ng-click="removeGroupListener()">删除组</a>
                                    <a class="btn btn-icon btn-lg" rel="tooltip" data-title="{{ACTION.ADD | translate}}" data-placement="bottom" data-original-title="" title="" ng-click="addNewListener()">添加条件</a>
                                    <a class="btn btn-icon btn-lg" rel="tooltip" data-title="{{ACTION.REMOVE | translate}}" data-placement="bottom" data-original-title="" title="" ng-click="removeListener()">删除条件</a>
                                </div>
                            </div>
                        </div>
                        <div class="col-xs-12">
                            <label class="col-xs-3"></label>
                            <div class="col-xs-6">
                                <div ng-repeat="item in grids">
                                    <span ng-if="item > 0">或者<hr></span>
                                    <div class="kis-listener-grid" ng-grid="gridOptions[{{item}}]" ng-click="gridsClick(this)"></div>
                                </div>
                            </div>
                            <div class="col-xs-3">
                                <div ng-show="selectedListeners.length > 0">
                                    <div class="form-group">
                                        <label>{{columnDefs[1].displayName}}</label>
                                        <select id="name" class="form-control" ng-model="selectedListeners[0].name" ng-change="listenerDetailsChanged()">
                                            <option ng-repeat="x in businessVariables" value="{{x.name}}">{{x.name}}</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label>{{columnDefs[2].displayName}}</label>
                                        <select id="conditions" class="form-control" ng-model="selectedListeners[0].conditions" ng-change="listenerDetailsChanged()">
                                            <option ng-repeat="item in conditions">{{item}}</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label>{{columnDefs[3].displayName}}</label>
                                        <input type="text" id="inputValue" class="form-control" ng-model="selectedListeners[0].inputValue" ng-change="listenerDetailsChanged()" />
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-xs-12">
                            <label class="col-xs-3">注</label>
                            <div class="col-xs-9">
                                表达式可通过上面生成器生成，
                                最终结果以表达式为准。
                            </div>
                        </div>
                        <div class="col-xs-12">
                            <label class="col-xs-3">{{'PROPERTY.SEQUENCEFLOW.CONDITION.STATIC' | translate}}</label>
                            <div class="col-xs-9">
                                <textarea class="form-control" ng-model="conditionExpression.value" style="width:90%; height:100%; max-width: 100%; max-height: 100%; min-height: 100px" />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button ng-click="close()" class="btn btn-primary" translate>ACTION.CANCEL</button>
                <button ng-click="save()" class="btn btn-primary" translate>ACTION.SAVE</button>
            </div>
        </div>
    </div>
</div>