
bde6163a6d3f7634bd756aaff1bb8fc161f5d1ef	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.374.1754018536329.js\",\"contentHash\":\"ec5842ac2771b2dd1c89bd644df69d30\"}","integrity":"sha512-REARlcpL0ipphTAjr91MspOc6P1UPm4X9EoXXcv5A9oCl4O+XgneA+EmT0xcCZZ3bEFfv2PrSTcitj3kfCAsAQ==","time":1754018576071,"size":187027}