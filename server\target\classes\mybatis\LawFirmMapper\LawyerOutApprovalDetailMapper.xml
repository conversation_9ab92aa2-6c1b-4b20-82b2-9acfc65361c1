<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.klaw.dao.lawyerDao.LawyerOutApprovalDetailMapper">

    <resultMap id="BaseResultMap" type="com.klaw.entity.lawyerBean.LawyerOutApprovalDetail">
        <id property="id" column="id" jdbcType="VARCHAR"/>
        <result property="createOgnId" column="create_ogn_id" jdbcType="VARCHAR"/>
        <result property="createOgnName" column="create_ogn_name" jdbcType="VARCHAR"/>
        <result property="createDeptId" column="create_dept_id" jdbcType="VARCHAR"/>
        <result property="createDeptName" column="create_dept_name" jdbcType="VARCHAR"/>
        <result property="createGroupId" column="create_group_id" jdbcType="VARCHAR"/>
        <result property="createGroupName" column="create_group_name" jdbcType="VARCHAR"/>
        <result property="createPsnId" column="create_psn_id" jdbcType="VARCHAR"/>
        <result property="createPsnName" column="create_psn_name" jdbcType="VARCHAR"/>
        <result property="createOrgId" column="create_org_id" jdbcType="VARCHAR"/>
        <result property="createOrgName" column="create_org_name" jdbcType="VARCHAR"/>
        <result property="createPsnFullId" column="create_psn_full_id" jdbcType="VARCHAR"/>
        <result property="createPsnFullName" column="create_psn_full_name" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="dataState" column="data_state" jdbcType="VARCHAR"/>
        <result property="dataStateCode" column="data_state_code" jdbcType="DECIMAL"/>
        <result property="lawyerId" column="lawyer_id" jdbcType="VARCHAR"/>
        <result property="lawyerName" column="lawyer_name" jdbcType="VARCHAR"/>
        <result property="lawFirm" column="law_firm" jdbcType="VARCHAR"/>
        <result property="lawFirmId" column="law_firm_id" jdbcType="VARCHAR"/>
        <result property="beginDate" column="begin_date" jdbcType="TIMESTAMP"/>
        <result property="lawyerPhone" column="lawyer_phone" jdbcType="VARCHAR"/>
        <result property="lawyerEmail" column="lawyer_email" jdbcType="VARCHAR"/>
        <result property="lawyerTime" column="lawyer_time" jdbcType="TIMESTAMP"/>
        <result property="charteredNo" column="chartered_no" jdbcType="VARCHAR"/>
        <result property="beGoodAtDomain" column="be_good_at_domain" jdbcType="VARCHAR"/>
        <result property="beGoodAtDomainIds" column="be_good_at_domain_ids" jdbcType="VARCHAR"/>
        <result property="remarks" column="remarks" jdbcType="VARCHAR"/>
        <result property="parentId" column="parent_id" jdbcType="VARCHAR"/>
        <result property="isCounsel" column="is_counsel" jdbcType="VARCHAR"/>
        <result property="seq" column="seq" jdbcType="DECIMAL"/>
        <result property="whetherHostLawyer" column="whether_host_lawyer" jdbcType="DECIMAL"/>
        <result property="typeName" column="type_name" jdbcType="VARCHAR"/>
        <result property="typeCode" column="type_code" jdbcType="VARCHAR"/>
        <result property="attachment" column="attachment" jdbcType="VARCHAR"/>
        <result property="majorRegion" column="major_region" jdbcType="VARCHAR"/>
        <result property="lawFirmType" column="law_firm_type" jdbcType="VARCHAR"/>
        <result property="sex" column="sex" jdbcType="VARCHAR"/>
        <result property="avatar" column="avatar" jdbcType="VARCHAR"/>
        <result property="createLegalUnitId" column="create_legal_unit_id" jdbcType="VARCHAR"/>
        <result property="createLegalUnitName" column="create_legal_unit_name" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,create_ogn_id,create_ogn_name,
        create_dept_id,create_dept_name,create_group_id,
        create_group_name,create_psn_id,create_psn_name,
        create_org_id,create_org_name,create_psn_full_id,
        create_psn_full_name,create_time,update_time,
        data_state,data_state_code,lawyer_id,
        lawyer_name,law_firm,law_firm_id,
        begin_date,lawyer_phone,lawyer_email,
        lawyer_time,chartered_no,be_good_at_domain,
        be_good_at_domain_ids,remarks,parent_id,
        is_counsel,seq,whether_host_lawyer,
        type_name,type_code,attachment,
        major_region,law_firm_type,sex,
        avatar,create_legal_unit_id,create_legal_unit_name
    </sql>
    
    <select id="getApprovalList">

    </select>
</mapper>
