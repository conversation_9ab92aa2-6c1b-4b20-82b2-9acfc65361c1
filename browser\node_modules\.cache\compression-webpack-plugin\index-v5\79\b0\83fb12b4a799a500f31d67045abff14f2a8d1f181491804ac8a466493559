
9ba13a643391d8dd6a7b1bc9c661cd93b2ff833d	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.411.1754018536329.js\",\"contentHash\":\"a860acc13ccf91755360cdcadd54afe8\"}","integrity":"sha512-dJWa7Tsg7BcTucQW1cGKjlJlo7XK4r+62i4VobSV8WdWqDj0Kx38xXONGEhzhki6pQGgzFOR/N7jEOdu7fTJAw==","time":1754018575957,"size":29245}