
3097096830832362a8a08f9451dadfc5f7726987	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.303.1754018536329.js\",\"contentHash\":\"c296b61593c7882e6fde9acabfc11fd0\"}","integrity":"sha512-9F6+30roXkh7fHDV1KMLa3NugtZocM5EXvfeZjI6PdB/PzeowS95cfGH99+I288ju/50n6gXkGv3z32WOY7Udg==","time":1754018576013,"size":138240}