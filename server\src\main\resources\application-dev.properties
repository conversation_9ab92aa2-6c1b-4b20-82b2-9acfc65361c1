
activiti.mailServerHost=smtp.126.com
activiti.mailServerPort=465
activiti.mailServerUsername=<EMAIL>
activiti.mailServerPassword=mcp_dev126
#126/u90AE/u7BB1/u7684/u5BA2/u6237/u7AEF/u6388/u6743/u5BC6/u7801
activiti.mailServerAuthorizationPassword=mcpdev126

server.port=8080
server.servlet.context-path=/mcp

knife4j.enable=true

spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.url=********************************************************************************************************************
spring.datasource.username=root
spring.datasource.password=112Fw@2024

spring.redis.database=2
spring.redis.host=localhost
spring.redis.port=6379
spring.redis.password=111111

spring.redis.lettuce.pool.max-active=8
spring.redis.lettuce.pool.max-wait=-1
spring.redis.lettuce.pool.max-idle=8
spring.redis.lettuce.pool.min-idle=0

#license.licensePath=D:/work//u00E9/u00A6/u0096/u00E9/u0092/u00A2/40/u00E5/u008C/u0085/u00E9/u0092/u00A2/u00E6/u00B3/u0095/u00E5/u008A/u00A1/u00E9/u00A1/u00B9/u00E7/u009B/u00AE/baogang/server/mcplicense202410.lic
license.licensePath=D:/Desktop/XM/FW/baogang/server/mcplicense202410.lic

swagger.url.patterns=/**

mybatis-plus.global-config.enable-sql-runner=true

oa.sysCode=jtgk_fw
oa.path=http://**********:8080
oa.url=http://**********:8080/rest/ofs/ReceiveRequestInfoByJson
oa.client.client-id=1119
oa.client.client-secret=7364b2ce0e2b43488ae9f950e3c05410
oa.client.user-authorization-uri=https://amdev.shougang.com.cn/idp/oauth2/authorize
oa.client.access-token-uri=https://amdev.shougang.com.cn/idp/oauth2/getToken
oa.resource.user-info-uri=http://amdev.shougang.com.cn/idp/oauth2/getUserInfo
oa.delete.path=http://tcmp.btsteel.com/rest/ofs/deleteUserRequestInfoByJson

oa.resource.current-uri=http://localhost:8081/ssoLogin
oa.resource.transfer-uri=http://localhost:8081/transfer
oa.resource.uni-uri=http://fwtest.btsteel.com/fwh5/#/
oa.resource.contractDialogIndex-uri=http://localhost:8081/contractDialogIndex
oa.out.glo-uri=http://localhost:8081/logout

#oa.resource.current-uri=http://localhost:8081/ssoLogin
#oa.resource.transfer-uri=http://localhost:8081/transfer
#oa.resource.contractDialogIndex-uri=http://localhost:8081/contractDialogIndex
#oa.out.glo-uri=http://localhost:8081/logout


oa.out.glo=https://amdev.shougang.com.cn/idp/profile/OAUTH2/Redirect/GLO

#oa.task.CXF_URL=http://************:50000/XISOAPAdapter/MessageServlet?senderParty=&senderService=BC_WHIR_JTGK&receiverParty=&receiverService=&interface=SI_uwl_field_out&interfaceNamespace=http%3A%2F%2Fshougang.com.cn%2FOA%2Fwhir%2FCommon
oa.task.username=pi_ser
oa.task.password=init1234
#oa.task.key=DFFD512F3C274EC11AF53753FC82B483
#oa.task.cmd=unifiedDealFile
#oa.task.systemkey=020

oa.task.CXF_URL=https://moat.sgai.com.cn:1443/sgai/api/process/
oa.task.key=DFFD512F3C274EC11AF53753FC82B483
oa.task.cmd=unifiedDealFile
oa.task.systemkey=020
oa.task.application=fb26fdf5-19d9-4baf-9ed8-15759efbd74f
oa.task.authorization=Basic YTNkMzJjZmItY2MyMC00NmQ1LWFkMDgtMmMzYmUzN2ZjNzM5OjVjODM0Y2ZkLTg3NjgtNGFjZS1hNWE5LWRjMGFhN2Q5ZmUzYQ==
oa.task.sourceId=75915689-a8a9-412e-9044-3033f2588220



minio.accesskey=minioadmin
minio.secrekey=minioadmin
minio.endpoint=http://*************:23000
minio.bucket=bucket1

#/u00E6/u00B2/u00A1/u00E7/u0094/u00A8/u00E5/u0088/u00B0/u00E4/u00B8/u008B/u00E6/u0096/u00B9/u00E7/u009A/u0084/u00E5/u009C/u00B0/u00E5/u009D/u0080
process.file.url=http://*************:80/mcp/sys_doc/download/

edoc.url=https://moat.sgai.com.cn:8099
edoc.username=fwadmin
edoc.password=edoc2
edoc.clienttype=4
edoc.token=003450e624f24d5d44c4a9c4049a680983b1
edoc.parentFolderId=45
edoc.parentArchiveId=356


#edoc.url=https://iyd.shougang.com.cn
#edoc.username=fwadmin
#edoc.password=gzhzgmz0tk5
#edoc.clienttype=4
#edoc.token=00348de80efd6a464b2e9d0bb5e0f94b0267
#edoc.parentFolderId=1275

#edoc.url=http://*************
#edoc.username=fwadmin
#edoc.password=gzhzgmz0tk5
#edoc.clienttype=4
#edoc.token=003476154a79b6184726bf38c518349063ac
#edoc.parentFolderId=15

standardtext.url=http://*************:8768
standardtext.urlAdLogin=http://*************:8769
#standardtext.url=http://*************:8088
#standardtext.url=http://localhost:8088


knl.url=http://*************:9094

#/u8C03/u7528/u6CD5/u52A1/u7CFB/u7EDF/u540E/u53F0/u7684/u57DF/u540D/u5730/u5740
WEB_URL=http://*************:8080
#/u4E00/u8D77/u5199/uFF08/u6BD5/u5347/uFF09
EDITOR_URL=http://*************:88

tyc.url=http://************:80/integrationServices/SharedDataInterface/PublicInerfaceTycPost

#ESB/u5730/u5740
asset.url=http://************:50000

#/u660E/u6E90/u5730/u5740
purchase.url=http://test-mingyuan.shougang.com.cn:8060/Cbgl/Interface/HTDL/ReceiveFwEffecState.ashx

#OA/u6280/u672F/u7814/u7A76/u9662
oa.jyy.url=http://oa-test.shougang.com.cn:7001/defaultroot/xfservices/GeneralWeb

#/u9996/u94A2/u901A/u5355/u70B9/u5730/u5740
sgt.url=http://moat.sgai.com.cn:8046
sgt.appid=UBbFGoahRf0rB4t0sa-cZc
sgt.secretKey=B7CaimKKZWt0j
sgt.accessTokenUrl=http://moat.sgai.com.cn:8046/interface/sgjt/i1/oauth/getaccesstoken?
sgt.userinfoUrl=http://moat.sgai.com.cn:8046/interface/sgjt/i1/oauth/userinfo?

#/u00E6/u0089/u008B/u00E5/u0086/u0099/u00E7/u00AD/u00BE/u00E6/u0089/u00B9/u00E4/u00BB/u00A3/u00E7/u0090/u0086/u00E5/u009C/u00B0/u00E5/u009D/u0080
fw.url=https://fw-test.shougang.com.cn
hs.clientId=dynamic_cru
hs.grantType=client_credentials
hs.clientSecret=YTFiNDBkZDEtOTUxZi00ZmQwLTg3NDQtNjY4N2Q5NWVkYTNk
#/u00E7/u0094/u00B5/u00E5/u00AD/u0090/u00E7/u00AD/u00BE/u00E7/u00AB/u00A0ixjob/u00E5/u009C/u00B0/u00E5/u009D/u0080
ixjob.dzht.url= http://10.68.24.64:8089/Soap?wsdl
#ixjob.dzht.url= https://fw-test.shougang.com.cn/Soap?wsdl
#ixjob.dzht.url= http://*************/Soap?wsdl

#/u00E9/u00A6/u0096/u00E9/u0092/u00A2/u00E9/u0080/u009A/u00E8/u00BD/u00ACPDF
sgt.pdf=https://moat.sgai.com.cn:1443
sgt.secret=631EG8XRxswuRioRePerxTwJVhH9RalC

hwyun.obs.accessKey=B6TQQKBKPJPSLRPHDRBU
hwyun.obs.securityKey=96qvLRFQAfKmMYANBbmKTQRYiq1UlBvV6ENGplS3
#hwyun.obs.endPoint=obsv3.nm-bg-1.nmxlcloud.com
#hwyun.obs.endPoint=http://*************:8001
hwyun.obs.endPoint=http://10.202.19.229:8001
#hwyun.obs.endPoint=http://fwtest.btsteel.com:8001
hwyun.obs.bucketName=fwtest

wps.ak=ZXGWPOYDLNHQLUTV
wps.sk=SKkgcfxdpbawjdwd
wps.preview=http://************:8092/open/api/preview/v1/files/
wps.previewCallback=http://************:8092/open/api/preview/v1/files/
wps.edit=http://************:8092/open/api/edit/v1/files/
wps.convert=http://************:8092/open/api/cps/sync/v1/convert
wps.convertDown=http://************:8092/open/api/cps/v1/download
wps.type=test
wps.contentOperate=http://************:8092/open/api/cps/sync/v1/content/operate
sdk.fileDown=http://localhost:8088/sgaudit/sdkWpsFileDown

sftp.client.protocol=sftp
sftp.client.host=
sftp.client.port=
sftp.client.username=
sftp.client.password=
sftp.client.proxyUrl=
sftp.client.uploadUrl=doc/
sftp.client.privateKey=
sftp.client.passphrase=
sftp.client.sessionStrictHostKeyChecking=no
sftp.client.sessionConnectTimeout=15000
sftp.client.channelConnectedTimeout=15000
sftp.client.serviceUrl=

esb.url=https://************:9090

xxl.job.user-name=admin
xxl.job.password=123456
xxl.job.admin.addresses=http://*************:8088/xxl-job-admin
xxl.job.accessToken=eyJhbGciOiJIUzI1NiJ9
xxl.job.executor.appname=xxl-job-executor-sample
xxl.job.executor.address=
xxl.job.executor.ip=
xxl.job.executor.port=9999
xxl.job.executor.logpath=./logs/xxl-job/jobhandler
xxl.job.executor.logretentiondays=30