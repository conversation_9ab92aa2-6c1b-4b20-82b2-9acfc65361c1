<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.klaw.dao.lawyerDao.LawFirmSelectionDetailMapper">
    <resultMap id="BaseResultMap" type="com.klaw.entity.lawyerBean.LawFirmSelectionDetail">
        <id column="ID" jdbcType="VARCHAR" property="id" />
        <result column="CREATE_OGN_ID" jdbcType="VARCHAR" property="createOgnId" />
        <result column="CREATE_OGN_NAME" jdbcType="VARCHAR" property="createOgnName" />
        <result column="CREATE_DEPT_ID" jdbcType="VARCHAR" property="createDeptId" />
        <result column="CREATE_DEPT_NAME" jdbcType="VARCHAR" property="createDeptName" />
        <result column="CREATE_GROUP_ID" jdbcType="VARCHAR" property="createGroupId" />
        <result column="CREATE_GROUP_NAME" jdbcType="VARCHAR" property="createGroupName" />
        <result column="CREATE_PSN_ID" jdbcType="VARCHAR" property="createPsnId" />
        <result column="CREATE_PSN_NAME" jdbcType="VARCHAR" property="createPsnName" />
        <result column="CREATE_ORG_ID" jdbcType="VARCHAR" property="createOrgId" />
        <result column="CREATE_ORG_NAME" jdbcType="VARCHAR" property="createOrgName" />
        <result column="CREATE_PSN_FULL_ID" jdbcType="VARCHAR" property="createPsnFullId" />
        <result column="CREATE_PSN_FULL_NAME" jdbcType="VARCHAR" property="createPsnFullName" />
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
        <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="DATA_STATE" jdbcType="VARCHAR" property="dataState" />
        <result column="DATA_STATE_CODE" jdbcType="DECIMAL" property="dataStateCode" />
        <result column="PARENT_ID" jdbcType="VARCHAR" property="parentId" />
        <result column="LAW_FIRM" jdbcType="VARCHAR" property="lawFirm" />
        <result column="LAW_FIRM_ID" jdbcType="VARCHAR" property="lawFirmId" />
        <result column="LAWYER_NAME" jdbcType="VARCHAR" property="lawyerName" />
        <result column="LAWYER_ID" jdbcType="VARCHAR" property="lawyerId" />
        <result column="REASON" jdbcType="VARCHAR" property="reason" />
        <result column="WHETHER_REPLY" jdbcType="VARCHAR" property="whetherReply" />
        <result column="QUOTED_PRICE" jdbcType="DECIMAL" property="quotedPrice" />
        <result column="FRACTION" jdbcType="DECIMAL" property="fraction" />
        <result column="VOTERS" jdbcType="VARCHAR" property="voters" />
        <result column="WHETHER_WIN" jdbcType="VARCHAR" property="whetherWin" />
    </resultMap>
    <sql id="Base_Column_List">
        ID, CREATE_OGN_ID, CREATE_OGN_NAME, CREATE_DEPT_ID, CREATE_DEPT_NAME, CREATE_GROUP_ID,
        CREATE_GROUP_NAME, CREATE_PSN_ID, CREATE_PSN_NAME, CREATE_ORG_ID, CREATE_ORG_NAME,
        CREATE_PSN_FULL_ID, CREATE_PSN_FULL_NAME, CREATE_TIME, UPDATE_TIME, DATA_STATE, DATA_STATE_CODE,
        PARENT_ID, LAW_FIRM, LAW_FIRM_ID, LAWYER_NAME, LAWYER_ID, REASON, WHETHER_REPLY,
        QUOTED_PRICE, FRACTION, VOTERS, WHETHER_WIN
    </sql>
</mapper>