<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.klaw.dao.caseDao.CaseExamineMapper">

    <select id="queryCaseName" resultType="com.klaw.entity.caseBean.CaseChildExamine">
        select c.case_name caseName from sg_case_examine_manage e left join sg_case_child_examine c
        on e.id=c.relation_id where e.id = #{relationId}
    </select>

    <select id="queryById" resultType="com.klaw.entity.caseBean.CaseChildExamine">
        select c.id from sg_case_examine_manage e left join sg_case_child_examine c on e.id=c.relation_id
        where c.relation_id = #{id}
    </select>

<!--             select e.id,e.item_name name from sg_middle_relation m  join sg_case_examine_manage e
 on m.relation_id = e.id where m.associated_id = #{caseId}-->
    <select id="queryIdAndName" resultType="java.util.Map">
         select id,item_name name from sg_case_examine_manage where case_id =#{caseId} and data_state = '已完成'
    </select>

    <select id="queryInfoByCaseId" resultType="com.klaw.entity.caseBean.CaseExamine">
        select e.*  from sg_case_examine_manage e
        where e.relation_case_process_id = #{caseProcessId}
    </select>

    <update id="updateDataState" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index">
            update sg_case_examine_manage set data_state=#{item.state},data_state_code = #{item.stateCode}
            where   id =#{item.id};
        </foreach>
    </update>
</mapper>
