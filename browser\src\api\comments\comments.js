import {request} from '@/api/index'
export default {
    _debounce(fn, delay = 300) {
        var timer = null;
        return function () {
            var _this = this;
            var args = arguments;
            if (timer) clearTimeout(timer);
            timer = setTimeout(function () {
                fn.apply(_this, args);
            }, delay);
        };
    },
    save(data) {
        return request({
            url: '/comments/save',
            method: 'post',
            data
        })
    },
    query(data) {
        return request({
            url: '/comments/query',
            method: 'post',
            data
        })
    },
}
