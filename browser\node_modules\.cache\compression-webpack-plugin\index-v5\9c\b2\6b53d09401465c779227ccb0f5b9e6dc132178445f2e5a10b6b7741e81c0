
d7421fb5dffb552e04a0058284fb0cd387bebfd9	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.361.1754018536329.js\",\"contentHash\":\"41810e38f05d756f4526dd51ac2bd7eb\"}","integrity":"sha512-lWj3zMBMoAPdfNlJPyZ2s+u3Wx4cUBwfBC5gCBeHQ5lLvXnQzU/vZatANzDR+f4eVcNCUaaNOi4WWE2f7yPwIw==","time":1754018575975,"size":106339}