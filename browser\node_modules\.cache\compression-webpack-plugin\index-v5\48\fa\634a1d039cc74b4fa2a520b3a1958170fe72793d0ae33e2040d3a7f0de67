
30893ab50dea3e340c94650990d6ede21021f55e	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.440.1754018536329.js\",\"contentHash\":\"93e4fc4826d02924ae4fc30ee27a4917\"}","integrity":"sha512-PY+43sgvKlrqhFFB1NnvZOwXAek/Txs4EuElO5lTUJHH4M1CtvkbFmXQQh9o9KAQszzHu7h8/PmRppQ4q3DWSw==","time":1754018575977,"size":116125}