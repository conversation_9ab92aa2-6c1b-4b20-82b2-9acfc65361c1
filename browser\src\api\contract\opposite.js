import {request} from '@/api/index'

export default {
    query(data) {
        return request({
            url: '/opposite/query',
            method: 'post',
            data
        })
    },
    save(data) {
        return request({
            url: '/opposite/save',
            method: 'post',
            data
        })
    },
    stopSave(data) {
        return request({
            url: '/opposite/stopSave',
            method: 'post',
            data
        })
    },
    delete(data) {
        return request({
            url: '/opposite/delete',
            method: 'post',
            data
        })
    },
    queryById(data) {
        return request({
            url: '/opposite/queryById',
            method: 'post',
            data
        })
    },
    distinct(data) {
        return request({
            url: '/opposite/distinct',
            method: 'post',
            data
        })
    },
    queryDialog(data) {
        return request({
            url: '/opposite/queryDialog',
            method: 'post',
            data
        })
    },
    queryLicenseCode(data) {
        return request({
            url: '/opposite/queryLicenseCode',
            method: 'post',
            data
        })
    },
    queryDetail(data) {
        return request({
            url: '/opposite/queryDetail',
            method: 'post',
            data
        })
    },

    getIsDuplicates(data) {
        return request({
            url: '/opposite/getIsDuplicates',
            method: 'post',
            data
        })
    },
}