
64af21cff41d904cc4776a1f33ad4731f9c6ccd6	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.388.1754018536329.js\",\"contentHash\":\"e920a5109b6a4e0a6d18a950068957b7\"}","integrity":"sha512-wl8a68e4dcGL0e5vkwnz0O7TJOyN1yfTdQa2k8KkeWFLy48IWvBtYI7Nxs1QgDPyBSU2Whhg4cfupUnxHu3jVA==","time":1754018576023,"size":126155}