
3bc75438d7bbb9df65bdf25e992bf12c3da9dff7	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.357.1754018536329.js\",\"contentHash\":\"d81f827830a89d1ccc5748e4b2e3c9fc\"}","integrity":"sha512-oXywAQpeSmy1vscdKo7ed7EPJTd9xlg3kZ7KclIOc1vs6nlFxq1/DqEevkn0QfBHRbaxfwOUIziR77sTRNTIxw==","time":1754018575975,"size":79374}