
4fda22f3c1eaa2f8f940f8c84c2930142c79a400	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.83.1754018536329.js\",\"contentHash\":\"b512f1b1509c34a549b048031a6dd791\"}","integrity":"sha512-4R6tZyB/VKDNENSDeBOJ60x1CzsnFOSUJkDwnBXOu5wXl+TvfgONfrDtEBC/hBKQN8ezCxYJS6PV8XptT78kGw==","time":1754018575959,"size":89625}