
dc784614e5ad71b0f25dea7ae3e74db01cc8b739	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.278.1754018536329.js\",\"contentHash\":\"59334b14954d687796ec5d16c8ca4f36\"}","integrity":"sha512-uZWfoqqcJ9fEwpMqamSGp7Kacb99DCm05wgGsZKZNC61britI2N3ReTKvjpbZ4qYleLtxcjN3zdNNkxK6v+E3A==","time":1754018575962,"size":113082}