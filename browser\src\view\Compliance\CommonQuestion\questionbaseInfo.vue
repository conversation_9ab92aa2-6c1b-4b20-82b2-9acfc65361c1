<template xmlns:v-slot="http://www.w3.org/1999/XSL/Transform">

  <div style="margin-top: 10px" v-if="view === 'old'">
    <!--基础信息表单块-->
    <div v-if="dataState !== 'view'">
      <div style="padding-left: 10px;margin-bottom: 20px;font-size: 15px;color: red;font-weight: bolder">
       
      </div>
      <div style="margin: 10px">
        <span style="font-weight: bold;font-size: 16px;color: #5A5A5F;">基本信息</span>
      </div>
      <el-row style="margin-top: 10px">
        <el-col :span="8">
          <el-form-item label="所属大类" prop="category" required>
            <el-select v-model="mainData.category" clearable  placeholder="请选择" style="width: 100%" >
              <el-option
                  v-for="item in utils.common_question_type"
                  :key="item.dicName"
                  :label="item.dicName"
                  :value="item.dicName"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="所属子类" prop="subCategory" >
            <el-input v-if="!isView" v-model="mainData.subCategory" maxlength="100" show-word-limit
                      placeholder="请输入..." clearable/>
            <span v-else class="viewSpan">{{ mainData.subCategory }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="单据编号">
            <el-input placeholder="自动生成" v-model="mainData.documentNumber" show-word-limit :disabled="true"
                      style="width: 100%" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="关键字" prop="keyName" required>
            <el-input v-if="!isView" v-model="mainData.keyName" maxlength="100" show-word-limit
                      placeholder="请输入..." clearable/>
            <span v-else class="viewSpan">{{ mainData.keyName }}</span>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="问题描述" prop="questionDescription" required>
            <span slot="label">问题描述</span>
            <el-input v-if="!isView" v-model="mainData.questionDescription" :autosize="{ minRows: 5, maxRows: 20 }"
                      type="textarea" placeholder="请输入问题描述" maxlength="500"
                      show-word-limit/>
            <text-span v-else class="viewSpan" :text="mainData.questionDescription"/>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="发生原因" prop="reasonOfOccurrence" required>
            <span slot="label">发生原因</span>
            <el-input v-if="!isView" v-model="mainData.reasonOfOccurrence" :autosize="{ minRows: 5, maxRows: 20 }"
                      type="textarea" placeholder="请输入发生原因" maxlength="500"
                      show-word-limit/>
            <text-span v-else class="viewSpan" :text="mainData.reasonOfOccurrence"/>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="解决方案" prop="solution" required>
            <span slot="label">解决方案</span>
            <el-input v-if="!isView" v-model="mainData.solution" :autosize="{ minRows: 5, maxRows: 20 }"
                      type="textarea" placeholder="请输入解决方案" maxlength="500"
                      show-word-limit/>
            <text-span v-else class="viewSpan" :text="mainData.solution"/>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="附件材料">
            <UploadDoc :files.sync="mainData.uploadAttachment" doc-path="/case" :disabled="isView"/>
          </el-form-item>
        </el-col>
      </el-row>
    </el-row>
    </div>
<!-- 查看 -->
    <div v-else>
      <SimpleBoardTitle title="基本信息">
        <table class="table_content">
          <tbody>
            <tr>
              <th colspan="2" class="th_label">所属分类</th>
              <td colspan="14" class="td_value">{{ mainData.category }}</td>
              <th colspan="2" class="th_label">所属子类</th>
              <td colspan="6" class="td_value">{{ mainData.subCategory }}</td>
              <th colspan="2" class="th_label">单据编号</th>
              <td colspan="6" class="td_value">{{ mainData.documentNumber }}</td>
            </tr>
            <tr>
              <th colspan="2" class="th_label">关键字</th>
              <td colspan="10" class="td_value">{{ mainData.keyName }}</td>
            </tr>
            <tr>
              <th colspan="2" class="th_label">问题描述</th>
              <td colspan="30" class="td_value">{{ mainData.questionDescription }}</td>
            </tr>
            <tr>
              <th colspan="2" class="th_label">发生原因</th>
              <td colspan="30" class="td_value">{{ mainData.reasonOfOccurrence }}</td>
            </tr>
            <tr>
              <th colspan="2" class="th_label">解决方案</th>
              <td colspan="30" class="td_value">{{ mainData.solution }}</td>
            </tr>
            <tr>
              <th colspan="2" class="th_label">附件材料</th>
              <td colspan="30" class="td_value">
                <UploadDoc :files.sync="mainData.uploadAttachment" doc-path="/case" :disabled="isView"/>
              </td>
            </tr>
          </tbody>
          
        </table>

      </SimpleBoardTitle>
    </div>

  </div>
</template>

<script>
import AreaSelect from '@/view/components/AreaSelect/AreaSelect'
import OrgSingleDialogSelect from '@/view/components/OrgSingleDialogSelect/index.vue'
import UploadDoc from '@/view/components/UploadDoc/UploadDoc.vue'
import orgTree from '@/view/components/OrgTree/OrgTree'
import SimpleBoardTitle from "@/view/components/SimpleBoard/SimpleBoardTitle"
import SimpleBoardTitleApproval from "@/view/components/SimpleBoard/SimpleBoardTitleApproval"
import money from "@/view/components/Money/index"
import OrgLeader from '@/view/components/OrgLeader/OrgLeader'


export default {
  name: 'QsBaseInfo',
  components: {
    OrgSingleDialogSelect, AreaSelect, UploadDoc, orgTree, SimpleBoardTitleApproval, money,
    SimpleBoardTitle, OrgLeader
  },
  props: {
    data: {
      type: Object,
      default: function () {
        return {}
      }
    },
    dataState: {
      type: String,
      default: ''
    },
    view: {
      type: String,
      default: 'new'
    },
    create: {
      type: String,
      default: ''
    },
    authorizationData: {
      type: Object,
      default: function () {
        return {}
      }
    },
  },
  data() {
    return {
      orgDialogTitle: '组织信息',
      caseDialogVisible: false,
      mainData: this.data,
      dicTreeDialogVisible: false,
      orgTreeDialog: false,
      caseNatures: [],
      plateData: [],
      causeOfIns: [],
      applications: [],
      zxcheckedData: [],
      dialogVisible: false,
      orgVisible: false,
    }
  },
  computed: {
    isView: function () {
      return this.dataState === this.utils.formState.VIEW
    },
    causeOfInIds: {
      set: function (data) {
        this.mainData.causeOfInId = data.join(',')
      },
      get: function () {
        if (this.mainData.causeOfInId) {
          return this.mainData.causeOfInId.split(',')
        }
        return []
      }
    },
    isCreate: function () {
      return this.create === 'create'
    },
  },
  watch: {
    mainData: {
      handler(val, oldVal) {
        this.$emit('update:data', val)
      },
      deep: true
    },
    data(val) {
      this.mainData = Object.assign(this.mainData, val)
    },
    'mainData.involvedAmount': {
      handler(val, oldVal) {
        this.involvedAmountChange(val)
      },
      deep: true, immediate: true
    },
    'mainData.caseInterest': {
      handler(val, oldVal) {
        this.caseInterestChange(val)
      },
      deep: true, immediate: true
    }
  },
  created() {
    this.initDic()
    window.vm = this;
  },
  methods: {

    orgSelect(data) {
      this.mainData.currentUnitId = data.unitId
      this.mainData.currentUnit = data.name
      
      this.mainData.cultureCategory = data.name
    },

  }
}
</script>

<style lang="scss" scoped>
.hideContent {
  .el-input__inner, .el-radio.is-bordered, .el-textarea__inner, .el-input__count {
    background-color: #f9e8bb;
  }
}
.money-label-width .el-form-item__label {
  width: 150px; /* 指定宽度 */
}

</style>
