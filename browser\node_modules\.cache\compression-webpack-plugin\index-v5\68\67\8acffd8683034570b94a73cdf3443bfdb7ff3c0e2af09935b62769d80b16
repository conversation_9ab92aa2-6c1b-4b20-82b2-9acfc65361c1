
200e56c43f93e09984345db77beae272f4f789d7	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.327.1754018536329.js\",\"contentHash\":\"30c31b3b5031d21cfbe1ce4feefedb5b\"}","integrity":"sha512-aqNyPMeZUbkrb16wh1Jfl6LgNcjgWA7qrU2ZCW0Xp85Hko4gxuxYaRKQhekrTFGQ3d8T5LGQX20viAubJn/l/A==","time":1754018575974,"size":105645}