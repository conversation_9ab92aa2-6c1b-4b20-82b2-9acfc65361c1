
8634a08aaac8e7698cb3fcb838718b61af62c400	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.444.1754018536329.js\",\"contentHash\":\"be89a214b58b793bdf405f87402354c9\"}","integrity":"sha512-qKSB+/JV5runjyADqEA44rm+M3OokkBWRNOvJNUxzKYBrixikc8MWfgwFtc2zQsVrzvI9QfQWdMDdNlyluteKg==","time":1754018576041,"size":126603}