
834cf4b96dc5b3558d7848c4dcb39c651f04420d	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.216.1754018536329.js\",\"contentHash\":\"2d90b028d6b17d833b501d682efd8b9b\"}","integrity":"sha512-ZAg2s3P7aopwZ7AWMPha5yjpPYnyfwDmNEOgrr3aLctN95sjIKVwQAc6zhqE+KpNlpctwI5WEhf8NDMYrlvjSg==","time":1754018576056,"size":231764}