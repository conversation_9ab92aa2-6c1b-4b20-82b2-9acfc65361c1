
a4fee6764216d6d08f02916d5d7720abfb8d53bf	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.391.1754018536329.js\",\"contentHash\":\"67bacfed9748d46c010a6e51f5351412\"}","integrity":"sha512-wAoV5SFRdl/7j9UIInG2saR9IXkFXxGdYcjBLJjKwIR48Cmz3f6MJxG3j+95sOugGxBfMhTHuX8kaNgMbol0cA==","time":1754018575956,"size":34844}