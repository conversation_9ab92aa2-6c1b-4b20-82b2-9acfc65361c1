<template>
  <simple-board :title="'合规审查'" :hasAdd="false" :has-value="true" :data-state="dataState" style="margin-top: 10px">
    <el-table ref="table" v-loading="tableLoading" :data="tableData" border max-height="350" fit highlight-current-row>
      <el-table-column prop="fixedItemName" label="固定项名称" min-width="200" show-overflow-tooltip align="center" />
      <el-table-column prop="reviewResult" label="审查结果" min-width="120" show-overflow-tooltip align="center">
        <template slot-scope="scope">
          <el-radio-group v-if="!isView" v-model="scope.row.reviewResult" size="mini" @input="setInvolve(scope.row)">
            <el-radio border :label="true">是</el-radio>
            <el-radio border :label="false">否</el-radio>
          </el-radio-group>
          <span v-else>{{ scope.row.reviewResult ? '是' : '否' }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="fileName" label="文件名称" min-width="100" show-overflow-tooltip>
        <template slot-scope="scope" v-if="scope.row.reviewResult">
          <case-file :my-files="scope.row.fileName" />
        </template>
      </el-table-column>
      <el-table-column prop="responseMeasures" min-width="200" show-overflow-tooltip>
        <template slot="header">
          <span style="color: red;">*</span>
          <span>具体情况</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" min-width="100" align="center">
        <template slot-scope="scope">
          <el-button v-if="scope.row.reviewResult && !isView" size="mini" type="text" @click="handleEdit(scope.$index, scope.row)">编辑</el-button>
          <el-button v-if="scope.row.reviewResult" type="text" size="mini" @click="handleView(scope.$index, scope.row)">查看</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-dialog title="风险信息" :visible.sync="riskDialogVisible" :close-on-click-modal="false" width="70%" class="myDialog">
      <el-form ref="detailData" style="margin-top: 20px" :model="detailData" label-width="110px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="上传文件">
              <uploadDoc v-model="detailData.fileName" :files.sync="detailData.fileName" :disabled="isView||dataType==='view'" :doc-path="'/contract'" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="具体情况" prop="responseMeasures">
              <el-input v-if="dataType!=='view'" v-model="detailData.responseMeasures" :autosize="{ minRows: 3, maxRows: 6 }" type="textarea" placeholder="请输入内容" maxlength="500" show-word-limit />
              <span v-else class="viewSpan">{{ detailData.responseMeasures }}</span>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="riskDialogVisible = false">取消</el-button>
        <el-button size="mini" type="primary" @click="sealSure">确定</el-button>
      </div>
    </el-dialog>
  </simple-board>
</template>

<script>
import SimpleBoard from "../../../../components/SimpleBoard/SimpleBoardViewCase";
import { mapGetters } from "vuex";
import UploadDoc from "@/view/components/UploadDoc/UploadDoc.vue";
import caseFile from "@/view/litigation/disputeManage/disputeProcess/disputeChild/CaseFile.vue";

export default {
  name: "Party",
  inject: ['layout'],
  components: { caseFile, UploadDoc, SimpleBoard },
  props: {
    dataList: {
      type: Array,
      default: function () {
        return [];
      }
    },
    parentId: {
      type: String,
      default: ""
    },
    dataState: {
      type: String,
      default: ""
    },
    dataSourceCode: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      dataType: null,
      tableData: this.dataList,
      tableLoading: false,
      riskDialogVisible: false,
      detailData: {
        id: null,
        fixedItemName: null,
        fixedItemNameCode: null,
        reviewResult: false,
        fileName: null,
        responsibilityClause: null,
        responseMeasures: null,
        createPsnFullId: null,
        createPsnFullName: null,
        createTime: null,
      },
    };
  },
  computed: {
    ...mapGetters(['orgContext', 'currentFunctionId']),
    isView: function () {
      return this.dataState === this.utils.formState.VIEW;
    }
  },
  watch: {
    tableData: {
      handler(val, oldVal) {
        this.$emit("update:dataList", val);
      },
      deep: true
    },
    dataList: {
      handler(val, oldVal) {
        this.tableData = val === null ? [] : val;
      },
      deep: true
    },
  },
  methods: {
    handleEdit(index, row) {
      this.dataType = this.utils.formState.EDIT;
      this.dataIndex = index;
      this.detailData = JSON.parse(JSON.stringify(row));
      this.riskDialogVisible = true;
    },
    handleView(index, row) {
      this.dataType = this.utils.formState.VIEW;
      this.dataIndex = index;
      this.detailData = JSON.parse(JSON.stringify(row));
      this.riskDialogVisible = true;
    },
    sealSure() {
      this.$refs['detailData'].validate((valid) => {
        if (valid) {
          this.riskDialogVisible = false;
          this.tableData.splice(this.dataIndex, 1, this.detailData);
        }
      });
    },
    setInvolve(row) {
      if (row.reviewResult) {
        return; // 如果选择“是”，直接返回
      }
      if(row.fileName == null && row.responsibilityClause == null && row.responseMeasures == null){
        return
      }
      // 如果选择“否”，弹出确认对话框
      this.$confirm('取消后信息将清空，是否取消？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 用户确认取消，清空相关数据
        row.fileName = null;
        row.responsibilityClause = null;
        row.responseMeasures = null;
      }).catch(() => {
        // 用户取消操作，恢复选择“是”
        row.reviewResult = true;
      });
    }
  }
};
</script>

<style scoped>
.el-dialog {
  width: 100% !important;
  height: 70vh;
  overflow: auto;
  box-shadow: 0 0px 0px rgba(0, 0, 0, 0) !important;
  margin: 0 auto 0px !important;
}
</style>