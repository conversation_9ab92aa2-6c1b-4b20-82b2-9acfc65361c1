package com.klaw.service.imp.complianceRiskServiceImp;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import com.klaw.dao.complianceRiskDao.ComplianceEvaluationNotificationSlipMapper;
import com.klaw.entity.complianceRiskBean.ComplianceEvaluationNotificationSlipEntity;

import com.klaw.service.complianceRiskService.ComplianceEvaluationNotificationSlipService;

import com.klaw.utils.DataAuthUtils;
import com.klaw.utils.PageUtils;

import com.klaw.vo.Json;
import org.springframework.beans.factory.annotation.Autowired;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class ComplianceEvaluationNotificationSlipServiceImpl extends ServiceImpl<ComplianceEvaluationNotificationSlipMapper, ComplianceEvaluationNotificationSlipEntity> implements ComplianceEvaluationNotificationSlipService {

    @Autowired
    private ComplianceEvaluationNotificationSlipMapper complianceEvaluationNotificationSlipMapper;

    @Override
    public void saveData(ComplianceEvaluationNotificationSlipEntity complianceEvaluationNotificationSlipEntity) {
        //ComplianceEvaluationNotificationSlipEntity.setEvaluatorTime(String.valueOf(new Date()));
        // 插入数据到数据库
        complianceEvaluationNotificationSlipMapper.insert(complianceEvaluationNotificationSlipEntity);
    }


    @Override
    public Page<ComplianceEvaluationNotificationSlipEntity> queryPageData(JSONObject json) {
        QueryWrapper<ComplianceEvaluationNotificationSlipEntity> wrapper = new QueryWrapper<>();
        getFilter(json, wrapper);
        return page(new PageUtils<>(json), wrapper);
    }


    public void getFilter(JSONObject json, QueryWrapper<ComplianceEvaluationNotificationSlipEntity> wrapper) {
        //是否台账功能（登记只查自己的，台账和弹框查自己和数据权限的）
        boolean isQuery = json.containsKey("isQuery") ? json.getBoolean("isQuery") : false;
        String orgId = json.containsKey("orgId") ? json.getString("orgId") : "";
        // 评价对象类型
        String evaluationObjectType = json.containsKey("evaluationObjectType") ? json.getString("evaluationObjectType") : null;

        // 评价年度
        String evaluationYear = json.containsKey("evaluationYear") ? json.getString("evaluationYear") : null;

        // 评价对象
        String evaluationObject = json.containsKey("evaluationObject") ? json.getString("evaluationObject") : null;

        // 评价对象
        String dataState = json.containsKey("dataState") ? json.getString("dataState") : null;

        // 模糊搜索值
        String fuzzyValue = json.containsKey("fuzzyValue") ? json.getString("fuzzyValue") : null;

        // 排序字段
        String sortName = json.containsKey("sortName") ? json.getString("sortName") : null;

        // 排序字段是否升序
        boolean order = json.containsKey("order") ? json.getBoolean("order") : false;

        // 应用查询条件
        if (StringUtils.isNotBlank(evaluationObjectType)) {
            wrapper.and(i -> i.like("evaluation_object_type", evaluationObjectType));
        }

        if (StringUtils.isNotBlank(evaluationYear)) {
            wrapper.and(i -> i.like("evaluation_year", evaluationYear));
        }

        if (StringUtils.isNotBlank(evaluationObject)) {
            wrapper.and(i -> i.like("evaluation_object", evaluationObject));
        }
        if (StringUtils.isNotBlank(dataState)) {
            wrapper.and(i -> i.eq("data_state", dataState));
        }

        if (StringUtils.isNotBlank(fuzzyValue)) {
            wrapper.and(i -> i.like("evaluation_object_type", fuzzyValue)
                    .or().like("evaluation_year", fuzzyValue)
                    .or().like("evaluation_object", fuzzyValue));
        }

        if (StringUtils.isNotBlank(sortName) && order) {
            wrapper.orderByAsc(sortName);
        } else if (StringUtils.isNotBlank(sortName)) {
            wrapper.orderByDesc(sortName);
        }

        Long functionId = DataAuthUtils.getFunctionIdByCode("case_level_ledger");
        if(isQuery){
            DataAuthUtils.dataPermSql(wrapper, null, functionId, orgId);
        }else{
            wrapper.eq("create_org_id", orgId);
        }
        //按评价时间倒序显示
        wrapper.orderByDesc("evaluation_time");
    }


    @Override
    public ComplianceEvaluationNotificationSlipEntity queryDataById(String id) {
        ComplianceEvaluationNotificationSlipEntity complianceEvaluationNotificationSlipEntity = getById(id);
        return complianceEvaluationNotificationSlipEntity;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Json deleteDataById(String id) {
        boolean flag = removeById(id);
        if (!flag) {
            return Json.fail().msg("未找到需要删除的数据");
        }
        return Json.succ().msg("删除成功!");
    }
}



