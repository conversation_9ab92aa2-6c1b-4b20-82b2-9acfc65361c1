{"version": 3, "sources": ["web/kendo.uniform.css"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIA,oBACA,sBACE,QAAS,EAEX,gBACE,MAAO,KAET,cACE,MAAO,KAET,oBACE,MAAO,KAET,uBACE,cAAe,IAEjB,2BACE,MAAO,KAET,yBACE,iBAAkB,4BAClB,iBAAkB,KAAM,wFACxB,iBAAkB,KAAM,oEACxB,iBAAkB,KAAM,kEAE1B,2BACE,MAAO,QAET,0BACE,MAAO,KAET,wBACE,iBAAkB,4BAClB,iBAAkB,KAAM,wFACxB,iBAAkB,KAAM,oEACxB,iBAAkB,KAAM,kEAE1B,0BACE,MAAO,QAET,6BACE,MAAO,KAET,2BACE,iBAAkB,4BAClB,iBAAkB,KAAM,wFACxB,iBAAkB,KAAM,oEACxB,iBAAkB,KAAM,kEAE1B,6BACE,MAAO,QAET,eACE,MAAO,QAET,iBACE,MAAO,QAET,iBACE,MAAO,QAET,cACE,MAAO,QAET,kBACE,MAAO,QAET,kBACE,MAAO,QAET,kBACE,MAAO,QAET,kBACE,MAAO,QAET,kBACE,MAAO,QAET,kBACE,MAAO,QAET,2BACE,iBAAkB,KAClB,OAAQ,IAAI,MAAM,QAEpB,MACA,QACA,iBACE,aAAc,YAGhB,SACA,UACE,iBAAkB,KAcpB,gBAZA,SA8CA,wBAPA,eA7BA,4BALA,WAQA,iBA6BA,mBAlCA,iBADA,iBAUA,sBASA,WACA,4BAFA,uBATA,eAQA,sBAIA,oBAPA,eAEA,sBADA,oBAlBA,SAWA,mBAiBA,mBACA,sCA3BA,UAJA,SA6BA,iBAFA,cACA,sBAKA,yBAEA,uBADA,qBAFA,4BAzBA,aA+BA,gBACA,YAvBA,iBACA,2BACA,kBAjBA,WAQA,iBAgCA,SA7BA,WA+BA,WAPA,gBASA,gBA9CA,UA+CE,aAAc,QAUhB,eACA,oBAHA,sBADA,eALA,SAIA,mBAFA,mBACA,cAFA,WAMA,oBAGA,kBACE,iBAAkB,QAEpB,mBAEA,uBADA,gBAEE,iBAAkB,KAEpB,kBACE,aAAc,QACd,iBAAkB,KAEpB,WACA,iBAEA,mBADA,sBAEA,SACE,iBAAkB,KAEpB,OAGA,oDADA,kBADA,aAGE,iBAAkB,KAGpB,gBADA,kCAEE,iBAAkB,QAGpB,yBACA,gCAEA,+BADA,8BAHA,WAKE,aAAc,QACd,iBAAkB,KAGpB,yBAEA,yCADA,0BAEA,0CAEA,yCADA,wCALA,iBAOE,aAAc,QAMhB,iBAJA,gBAEA,sBADA,mBAEA,yBAEE,WAAY,IAEd,SAMA,oBADA,iBAJA,gBAEA,sBADA,mBAEA,yBAGE,iBAAkB,KAClB,MAAO,QAET,mBACE,iBAAkB,KAClB,MAAO,QAET,SAGA,WAEA,qBAHA,SAEA,WAHA,UAKE,MAAO,QAET,WACE,MAAO,KAET,SACE,MAAO,QAET,aACA,gBACA,qCACE,MAAO,QAGT,uBADA,0BAEE,MAAO,QAIT,iCAFA,UACA,iBAEE,MAAO,QAcT,gBAHA,UAEA,cARA,iBAFA,eAKA,mBANA,UAKA,gBAEA,cAQA,sCAXA,eAMA,eAGA,mBACA,0BANA,WANA,WAcA,+CACE,iBAAkB,4BAClB,iBAAkB,KAAM,wFACxB,iBAAkB,KAAM,oEACxB,iBAAkB,KAAM,kEACxB,oBAAqB,IAAI,IACzB,iBAAkB,KAEpB,SAMA,UACA,cALA,eAEA,mBAHA,UAIA,cAFA,WAKA,gBACA,gCACE,iBAAkB,KAQpB,yCADA,wCAJA,cAMA,qDACA,wFAJA,yBAFA,uBACA,0BAME,QAAS,EAIX,yBAFA,QAGA,+CACA,0EAHA,0BAIE,QAAS,GAEX,gCACA,qDACA,kDACE,QAAS,GAEX,QACE,aAAc,YAMhB,yBADA,aAMA,6CAHA,4CADA,6CAHA,qBAFA,QAOA,+CACA,0EAPA,aASE,iBAAkB,wBAClB,aAAc,YAGhB,gCACA,qDACA,kDACE,iBAAkB,wBAClB,aAAc,YAIhB,aAFA,WAGA,4BAFA,0BAGE,iBAAkB,yBAClB,oBAAqB,IAAI,IAE3B,iBACE,iBAAkB,+BAEpB,iBACE,iBAAkB,QAEpB,UACE,MAAO,QACP,aAAc,QACd,iBAAkB,KAEpB,cACE,aAAc,QACd,iBAAkB,KAClB,mBAAoB,KACZ,WAAY,KAEtB,oBACE,aAAc,QACd,iBAAkB,KAClB,mBAAoB,KACZ,WAAY,KAGtB,aACE,MAAO,QACP,iBAAkB,KAEpB,oBACE,MAAO,QAET,wBACA,yBACE,iBAAkB,KAClB,MAAO,QAKT,uBACA,yBAFA,sBAGA,mBAJA,sBADA,sBAME,aAAc,QAEhB,gBACE,iBAAkB,QAEpB,yBACE,iBAAkB,qBAEpB,kCACE,iBAAkB,sBAEpB,4BACA,iCACA,kCACE,iBAAkB,QAEpB,uBACE,kBAAmB,QAErB,sBACE,iBAAkB,QAEpB,SACA,iBACE,aAAc,QACd,WAAY,QAAQ,EAAE,OAAO,4BAA2B,SACxD,MAAO,QAET,iBACE,MAAO,KAET,0BACE,oBAAqB,EAAE,EACvB,mBAAoB,EAAE,EAAE,EAAE,IAAI,QACtB,WAAY,EAAE,EAAE,EAAE,IAAI,QAEhC,gCACA,sCACE,iBAAkB,QAGpB,2BADA,4BAEE,aAAc,KAEhB,uBAEA,oBADA,qBAEE,iBAAkB,KAClB,MAAO,QACP,aAAc,QAEhB,uBACE,MAAO,QAET,4BACE,aAAc,QAEhB,mBACE,iBAAkB,KAKpB,iBAFA,gBACA,sBAEA,4BACE,iBAAkB,KAClB,aAAc,QACd,MAAO,QAET,mCACE,iBAAkB,KAGpB,0BADA,gBAEE,aAAc,QAGhB,wBADA,gBAEE,MAAO,QACP,aAAc,KACd,iBAAkB,KAGpB,yBADA,iBAEE,MAAO,QACP,iBAAkB,KAClB,aAAc,QAGhB,+BADA,uBAEE,MAAO,QACP,aAAc,KACd,iBAAkB,QAGpB,2BAKA,kCAFA,iCAJA,oBAEA,4BAGA,mCAFA,kCAIE,MAAO,QACP,aAAc,QACd,iBAAkB,KAClB,iBAAkB,4BAClB,iBAAkB,KAAM,wFACxB,iBAAkB,KAAM,oEACxB,iBAAkB,KAAM,kEAE1B,uDACE,mBAAoB,MAAM,EAAE,EAAE,IAAI,IAAI,KAC9B,WAAY,MAAM,EAAE,EAAE,IAAI,IAAI,KAExC,8DACE,mBAAoB,MAAM,EAAE,EAAE,IAAI,IAAI,QAC9B,WAAY,MAAM,EAAE,EAAE,IAAI,IAAI,QAExC,uCACE,iBAAkB,YAEpB,kCACE,MAAO,QACP,iBAAkB,KAClB,aAAc,QACd,iBAAkB,4BAClB,iBAAkB,KAAM,wFACxB,iBAAkB,KAAM,oEACxB,iBAAkB,KAAM,kEAE1B,+BACE,iBAAkB,KAClB,aAAc,QACd,MAAO,QAET,+BACE,MAAO,QACP,iBAAkB,KAClB,aAAc,KACd,iBAAkB,4BAClB,iBAAkB,KAAM,wFACxB,iBAAkB,KAAM,oEACxB,iBAAkB,KAAM,kEAE1B,mBACE,WAAY,KACZ,MAAO,QAGT,iCADA,iBAEE,aAAc,QAEhB,oBACE,MAAO,QAET,sBACE,QAAS,EAEX,mCACE,MAAO,QACP,gBAAiB,KACjB,iBAAkB,KAGpB,iDADA,yCAEE,iBAAkB,KAClB,gBAAiB,UAEnB,0CACE,iBAAkB,KAEpB,+BACE,MAAO,QAET,sCACE,gBAAiB,KACjB,iBAAkB,KAClB,MAAO,QAET,kCACE,cAAe,IAEjB,qBACE,mBAAoB,MAAM,EAAE,EAAE,EAAE,IAAI,KAC5B,WAAY,MAAM,EAAE,EAAE,EAAE,IAAI,KAEtC,6BACE,MAAO,QACP,YAAa,IAEf,uCACA,qDACE,mBAAoB,MAAM,EAAE,EAAE,IAAI,IAAI,QAC9B,WAAY,MAAM,EAAE,EAAE,IAAI,IAAI,QAExC,qCACA,wCACE,mBAAoB,KACZ,WAAY,KAEtB,6CACE,MAAO,QAET,8BACE,cAAe,EAEjB,eACE,iBAAkB,QAEpB,8CACE,iBAAkB,QAClB,iBAAkB,KAEpB,wCACA,8DACE,MAAO,QAET,gDACA,+CAEA,qDADA,uCAEE,mBAAoB,MAAM,EAAE,EAAE,IAAI,IAAI,QAC9B,WAAY,MAAM,EAAE,EAAE,IAAI,IAAI,QAExC,2BACE,cAAe,IAEjB,8BACE,aAAc,QAUhB,qCADA,6BADA,2BAFA,2BADA,0BAQA,iBANA,2BAIA,oDACA,uCAVA,kBACA,uBACA,0BAUE,MAAO,QACP,iBAAkB,KAClB,aAAc,QAGhB,wCACA,yCAFA,wBAGE,iBAAkB,KAEpB,mDACE,iBAAkB,KAEpB,yBACA,yCACE,WAAY,KACZ,MAAO,QAET,kCACE,WAAY,KACZ,MAAO,QACP,0BAA2B,IAE7B,sCACE,WAAY,IACZ,MAAO,QAET,gBACE,MAAO,QAOT,0BAFA,kCAFA,yBACA,6BAFA,iBAIA,mBAEE,mBAAoB,MAAM,EAAE,EAAE,IAAI,IAAI,QAC9B,WAAY,MAAM,EAAE,EAAE,IAAI,IAAI,QAGxC,0CACA,8CAFA,kCAGA,oCACE,mBAAoB,MAAM,EAAE,EAAE,IAAI,IAAI,QAC9B,WAAY,MAAM,EAAE,EAAE,IAAI,IAAI,QASxC,iCADA,uBAHA,yCADA,oCADA,kCADA,wCAKA,6BADA,0BAIE,iBAAkB,KAGpB,qDADA,0CAEE,mBAAoB,KACZ,WAAY,KAItB,wDADA,iCADA,0BAGE,MAAO,QAST,6BACA,wBAJA,uBAOA,4CADA,uCADA,sCAGA,6CANA,4BADA,sDAHA,mCACA,iCAHA,eACA,qBAYE,MAAO,QACP,iBAAkB,KAClB,aAAc,KAGhB,wEACE,MAAO,QACP,iBAAkB,KAClB,aAAc,KAEhB,yCACE,aAAc,KAGhB,2BADA,yBAEE,aAAc,KAKhB,0BAFA,wBACA,gBAFA,gBAUA,oBAFA,qCADA,4BAFA,eACA,qBAFA,iBAKA,8BAEE,iBAAkB,4BAClB,iBAAkB,KAAM,wFACxB,iBAAkB,KAAM,oEACxB,iBAAkB,KAAM,kEAE1B,cACE,iBAAkB,KAClB,MAAO,QAET,+BAOA,iBAJA,gCADA,+BAMA,qCAPA,8BAGA,gBACA,sBACA,wBAGE,iBAAkB,KAIpB,yBADA,iBAEA,qCAHA,kBAIE,iBAAkB,4BAClB,iBAAkB,KAAM,wFACxB,iBAAkB,KAAM,oEACxB,iBAAkB,KAAM,kEAG1B,yBADA,iBAEA,qCACE,oBAAqB,IAAI,IAE3B,aACE,iBAAkB,wBAGpB,qCADA,uBAEA,8BACE,MAAO,QAGT,gCADA,8BAOA,iCADA,+BADA,gCADA,8BADA,+BADA,6BAME,iBAAkB,KAClB,iBAAkB,4BAClB,iBAAkB,KAAM,wFACxB,iBAAkB,KAAM,oEACxB,iBAAkB,KAAM,kEACxB,oBAAqB,IAAI,IACzB,aAAc,KAKhB,sCAHA,6EAEA,yCADA,gEAGE,MAAO,QAET,sEACE,iBAAkB,4BAClB,iBAAkB,KAAM,wFACxB,iBAAkB,KAAM,oEACxB,iBAAkB,KAAM,kEACxB,oBAAqB,IAAI,IACzB,WAAY,QACZ,aAAc,QAEhB,4EACE,WAAY,KACZ,aAAc,QAEhB,kFACE,mBAAoB,EAAE,EAAE,IAAI,EAAE,eACtB,WAAY,EAAE,EAAE,IAAI,EAAE,eAEhC,oCACE,MAAO,QAET,eACE,aAAc,QACd,iBAAkB,QAClB,MAAO,QAET,kBACE,QAAS,GAEX,yBACE,OAAQ,kBAGV,iCADA,+BAEE,aAAc,EACd,iBAAkB,KAClB,iBAAkB,YAOpB,eAFA,eACA,uBAGA,wBAPA,kBAEA,4BADA,0BAKA,qBAEE,MAAO,QAET,6BACE,MAAO,QAGT,6BACE,WAAY,+BAEd,qDACA,+CACE,QAAS,KAGX,gBACE,iBAAkB,QAEpB,oBACE,iBAAkB,KAEpB,6BACE,iBAAkB,0BAEpB,2BACE,iBAAkB,0BAGpB,oBACE,iBAAkB,4BAClB,iBAAkB,KAAM,wFACxB,iBAAkB,KAAM,oEACxB,iBAAkB,KAAM,kEACxB,oBAAqB,IAAI,IACzB,iBAAkB,KAClB,MAAO,QACP,aAAc,YACd,mBAAoB,EAAE,IAAI,IAAI,eACtB,WAAY,EAAE,IAAI,IAAI,eAEhC,+BACE,aAAc,QACd,iBAAkB,QAClB,MAAO,QAIT,oCADA,qCAEE,UAAW,KACX,SAAU,SACV,IAAK,IAEP,aACE,oBAAqB,KAEvB,aACE,mBAAoB,KAEtB,aACE,iBAAkB,KAEpB,aACE,kBAAmB,KAErB,mCACE,oBAAqB,QAEvB,mCACE,mBAAoB,QAEtB,mCACE,iBAAkB,QAEpB,mCACE,kBAAmB,QAGrB,YACE,iBAAkB,KAGpB,8BADA,4BAEE,iBAAkB,QAGpB,QACE,iBAAkB,KAClB,aAAc,QAEhB,iBACE,MAAO,QAET,6BACE,iBAAkB,QAEpB,gBACE,MAAO,QAET,4BACE,iBAAkB,QAEpB,cACE,MAAO,QAET,0BACE,iBAAkB,QAGpB,QACE,aAAc,KAEhB,iBACA,0BACE,aAAc,KAEhB,6BACE,aAAc,QAGhB,+BADA,iCAEE,iBAAkB,8BAClB,wBAAyB,KAAK,KACtB,gBAAiB,KAAK,KAGhC,QACA,4BACE,MAAO,KAET,kBACA,sCACE,MAAO,KAIT,UADA,UAEE,mBAAoB,KACZ,WAAY,KAEtB,UACA,YACA,UACE,mBAAoB,KACZ,WAAY,KAEtB,eACE,mBAAoB,KACZ,WAAY,KAGtB,gCACA,iCAEA,gCADA,+BAHA,iBAKE,mBAAoB,EAAE,EAAE,IAAI,EAAE,eACtB,WAAY,EAAE,EAAE,IAAI,EAAE,eAEhC,kBACE,mBAAoB,KACZ,WAAY,KAEtB,gBACE,mBAAoB,KACZ,WAAY,KAEtB,iBACE,iBAAkB,KAClB,iBAAkB,4BAClB,iBAAkB,KAAM,wFACxB,iBAAkB,KAAM,oEACxB,iBAAkB,KAAM,kEACxB,oBAAqB,IAAI,IAE3B,qCACE,WAAY,IAGd,kCADA,kCAEE,iBAAkB,QAClB,iBAAkB,KAOpB,oCACA,kCAFA,uBAGA,gCAGA,wBARA,0BADA,sBAQA,+BADA,8BARA,SAGA,cAQA,WACE,mBAAoB,EAAE,IAAI,IAAI,EAAE,eACxB,WAAY,EAAE,IAAI,IAAI,EAAE,eAElC,8BACE,mBAAoB,MAAM,EAAE,EAAE,EAAE,IAAI,QAC5B,WAAY,MAAM,EAAE,EAAE,EAAE,IAAI,QAGtC,UACE,aAAc,eACd,mBAAoB,IAAI,IAAI,IAAI,IAAI,qBAC5B,WAAY,IAAI,IAAI,IAAI,IAAI,qBACpC,iBAAkB,KAEpB,0BACE,aAAc,eACd,mBAAoB,IAAI,IAAI,IAAI,IAAI,eAC5B,WAAY,IAAI,IAAI,IAAI,IAAI,eAItC,sCADA,uCADA,6BAGE,cAAe,EAEjB,UACE,mBAAoB,EAAE,IAAI,IAAI,EAAE,eACxB,WAAY,EAAE,IAAI,IAAI,EAAE,eAElC,SACE,mBAAoB,MAAM,EAAE,IAAI,IAAI,eAC5B,WAAY,MAAM,EAAE,IAAI,IAAI,eAGtC,kCACE,iBAAkB,QAClB,YAAa,KACb,MAAO,KAET,6BACE,iBAAkB,QAClB,YAAa,KACb,MAAO,KAET,kCACE,iBAAkB,QAClB,YAAa,KACb,MAAO,KAGT,6CACE,iBAAkB,QAClB,MAAO,QACP,aAAc,QAEhB,gDACE,iBAAkB,QAClB,MAAO,QACP,aAAc,QAEhB,gDACE,iBAAkB,QAClB,MAAO,QACP,aAAc,QAEhB,8CACE,iBAAkB,QAClB,MAAO,QACP,aAAc,QAGhB,qBACE,WAAY,KAEd,4BACE,iBAAkB,QAEpB,8BACE,iBAAkB,4BAClB,iBAAkB,KAAM,wFACxB,iBAAkB,KAAM,oEACxB,iBAAkB,KAAM,kEACxB,iBAAkB,KAIpB,6CACA,gDAHA,uCACA,0CAGE,iBAAkB,KAClB,iBAAkB,4BAClB,iBAAkB,KAAM,wFACxB,iBAAkB,KAAM,oEACxB,iBAAkB,KAAM,kEAE1B,6CACA,gDACE,iBAAkB,QAClB,iBAAkB,KAEpB,kBACE,iBAAkB,QAClB,aAAc,QAEhB,wBACE,iBAAkB,KAEpB,gBACE,aAAc,QACd,WAAY,QAEd,kBACA,yBACE,aAAc,QACd,WAAY,QAEd,iCACE,aAAc,KACd,WAAY,KAGd,2CADA,mCAEE,aAAc,KACd,WAAY,KAEd,eACE,iBAAkB,KAClB,aAAc,QACd,MAAO,QAET,gCACE,aAAc,QAEhB,QACE,iBAAkB,QAClB,MAAO,QAET,yBACE,iBAAkB,KAClB,MAAO,KAET,YACE,iBAAkB,KAGpB,kBACA,gBACA,eACA,cACA,kBACA,cACE,iBAAkB,4BAcpB,gBAXA,SACA,UA6BA,oBADA,eADA,sBARA,eAPA,YAKA,cAGA,kBAlBA,aAWA,YACA,iBAiBA,iBAOA,+BA9BA,0BACA,sCAFA,gBAmBA,kBAXA,OAJA,eAUA,gBAGA,gBAFA,kBACA,eAYA,oBADA,gBAGA,+BApCA,WAgCA,QAfA,cAFA,UAgBA,WA7BA,mBA2BA,kBAMA,UAhCA,UAEA,iBADA,sCAkCE,cAAe,IAEjB,QACE,WAAY,OACZ,eAAgB,OAElB,sBAEA,0CADA,qCAEE,cAAe,IAAI,EAAE,EAAE,IAEzB,6BAEA,iDADA,4CAEE,cAAe,EAAE,IAAI,IAAI,EAE3B,wCACE,cAAe,IAEjB,oBACA,kDACA,iDACE,cAAe,EAAE,IAAI,IAAI,EAE3B,2BACA,+CACA,wDACE,cAAe,IAAI,EAAE,EAAE,IAEzB,kCACE,cAAe,IAIjB,kCAFA,wCAIA,mCAIA,eAPA,oCAEA,iCAGA,kCADA,iCAEA,kBAEE,cAAe,EAAE,EAAE,IAAI,IAEzB,2CACA,4CAGA,2CAFA,0CACA,mDAEE,cAAe,EAAE,EAAE,EAAE,IAEvB,qDACE,cAAe,EAAE,EAAE,IAAI,IASzB,oCANA,mBAIA,0CAIA,qCAGA,gCACA,gDAPA,sCAEA,mCAGA,oCARA,sCAOA,mCARA,0BAEA,0BAJA,mBAcE,cAAe,IAAI,IAAI,EAAE,EAE3B,8CACE,cAAe,IAAI,EAAE,EAAE,EAEzB,4CACE,cAAe,EAAE,EAAE,EAAE,IAEvB,0DACE,cAAe,EAAE,IAAI,EAAE,EAEzB,wDACE,cAAe,EAAE,EAAE,IAAI,EAEzB,0BAEA,yBADA,wBAEE,cAAe,IAAI,EAAE,EAAE,IAEzB,iCAEA,gCADA,+BAEE,cAAe,EAAE,IAAI,IAAI,EAE3B,wBACE,cAAe,EAAE,IAAI,EAAE,EAEzB,gCACE,cAAe,EAAE,EAAE,IAAI,EAEzB,iCACE,cAAe,IAAI,EAAE,EAAE,IAEzB,wCACE,cAAe,EAAE,IAAI,IAAI,EAE3B,6CACE,cAAe,IAAI,IAAI,EAAE,EAE3B,8CAGA,6CAFA,4CACA,qDAEE,cAAe,IAAI,EAAE,EAAE,EAEzB,yCACE,iBAAkB,KAEpB,uDACE,cAAe,IAAI,IAAI,EAAE,EAK3B,sCAHA,2BAIA,uCAFA,0BADA,yBAIE,cAAe,EAAE,IAAI,IAAI,EAK3B,6CAHA,kCAIA,8CAFA,iCADA,gCAIE,cAAe,IAAI,EAAE,EAAE,IAEzB,0CACE,cAAe,IAGjB,yBACA,oBAFA,iBAGE,cAAe,IAQjB,YAFA,iCAHA,yBACA,2BAFA,uBAGA,0BAEA,oBAEE,cAAe,IAGjB,4BADA,oBAEE,cAAe,KAEjB,cACE,cAAe,IAEjB,uCACA,+CACA,4DACA,oEACE,cAAe,IAAI,EAAE,EAAE,IAEzB,8CACA,sDACA,mEACA,2EACA,iEACA,yEACE,cAAe,EAAE,IAAI,IAAI,EAI3B,sCAFA,0DACA,kEAEE,cAAe,IAEjB,iCAEA,yCADA,yCAEA,iDACE,wBAAyB,IACzB,2BAA4B,IAE9B,wCAEA,gDADA,gDAEA,wDACE,cAAe,IAAI,EAAE,EAAE,IAGzB,4CADA,0CAEE,cAAe,IAGjB,SAGA,iBAJA,eAGA,iBADA,eAGE,cAAe,IAEjB,6BACE,cAAe,IAGjB,4CACE,kBAAmB,QAKrB,kCADA,gCAEA,iCACA,kEAJA,iCAOA,8CADA,8CADA,wCANA,iCASE,MAAO,QAET,0EACE,sBACE,MAAO,SAIX,8CADA,wCAEA,qEACE,iBAAkB,KAClB,WAAY,IAAI,MAAM,QAExB,uDACE,cAAe,EAEjB,yDACE,iBAAkB,KAClB,iBAAkB,QAEpB,iFACE,iBAAkB,YAEpB,mDACE,mBAAoB,MAAM,EAAE,KAAK,EAAE,QAC3B,WAAY,MAAM,EAAE,KAAK,EAAE,QAIrC,4EADA,yEADA,8CAGE,MAAO,QAET,8CACE,cAAe,IAAI,MAAM,QAE3B,8CACE,mBAAoB,EAAE,IAAI,IAAI,QACtB,WAAY,EAAE,IAAI,IAAI,QAEhC,+BACA,oCAEA,sDADA,qCAEE,MAAO,KACP,aAAc,QACd,iBAAkB,IAClB,iBAAkB,mGAClB,iBAAkB,wEAClB,iBAAkB,sEAEpB,sCACA,2CAEA,6DADA,4CAEE,iBAAkB,KAGpB,gCAGA,iCADA,gCADA,+BAGE,iBAAkB,4BAClB,iBAAkB,KAAM,wFACxB,iBAAkB,KAAM,oEACxB,iBAAkB,KAAM,kEACxB,oBAAqB,IAAI,IACzB,iBAAkB,QAClB,aAAc,QAEhB,8BAGA,+BADA,8BADA,6BAGE,iBAAkB,KAClB,iBAAkB,4BAClB,iBAAkB,KAAM,wFACxB,iBAAkB,KAAM,oEACxB,iBAAkB,KAAM,kEACxB,oBAAqB,IAAI,IACzB,aAAc,KAIhB,wBAFA,gBACA,mBAEE,aAAc,QAEhB,sCACE,aAAc,KAEhB,gCAGA,iCACA,wCAFA,gCADA,+BAIE,iBAAkB,KAClB,iBAAkB,4BAClB,iBAAkB,KAAM,wFACxB,iBAAkB,KAAM,oEACxB,iBAAkB,KAAM,kEACxB,oBAAqB,IAAI,IACzB,aAAc,QACd,mBAAoB,EAAE,EAAE,IAAI,EAAE,eACtB,WAAY,EAAE,EAAE,IAAI,EAAE,eAEhC,kBACE,MAAO,QAET,qBACA,sCACA,iBACE,MAAO,QAET,2BACE,aAAc,QAEhB,yBACE,aAAc,KAEhB,2BACE,aAAc,QAEhB,kBACE,mBAAoB,EAAE,EAAE,IAAI,EAAE,eACtB,WAAY,EAAE,EAAE,IAAI,EAAE,eAGhC,uCADA,2CAEE,MAAO,QAIT,qDADA,qCADA,yCAGE,MAAO,QAET,2CACE,WAAY,KACZ,mBAAoB,KACZ,WAAY,KAEtB,mCACE,aAAc,QAEhB,iCACE,aAAc,KAGhB,8CADA,kCAEE,iBAAkB,KAClB,iBAAkB,KAClB,aAAc,QAEhB,sCACE,iBAAkB,KAClB,MAAO,QAGT,gBADA,iBAEE,aAAc,QAEhB,eACA,uBACA,wCACE,aAAc,QAEhB,wCACE,mBAAoB,MAAM,EAAE,IAAI,EAAE,KAAS,EAAE,IAAI,EAAE,KAC3C,WAAY,MAAM,EAAE,IAAI,EAAE,KAAS,EAAE,IAAI,EAAE,KAGrD,0DADA,0CAEE,mBAAoB,EAAE,IAAI,EAAE,KACpB,WAAY,EAAE,IAAI,EAAE,KAE9B,yCACE,mBAAoB,MAAM,EAAE,IAAI,EAAE,KAC1B,WAAY,MAAM,EAAE,IAAI,EAAE,KAEpC,4BACE,aAAc,QACd,iBAAkB,YAEpB,iBACE,aAAc,QAEhB,8BACE,iBAAkB,KAIpB,kBADA,mBADA,mBAGE,MAAO,QACP,aAAc,QACd,YAAa,IAEf,mBACE,MAAO,QAET,2BACE,mBAAoB,MAAM,EAAE,EAAE,IAAI,IAAI,QAC9B,WAAY,MAAM,EAAE,EAAE,IAAI,IAAI,QAUxC,kCANA,2BACA,eAFA,oBAMA,sCAPA,UAIA,cAEA,sBADA,yBAIE,aAAc,QAGhB,iCADA,WAEE,MAAO,QACP,aAAc,QACd,iBAAkB,4BAClB,iBAAkB,KAAM,wFACxB,iBAAkB,KAAM,oEACxB,iBAAkB,KAAM,kEACxB,oBAAqB,IAAI,IACzB,iBAAkB,KAClB,mBAAoB,KACZ,WAAY,KAGtB,2BADA,iBAEE,MAAO,QACP,aAAc,QACd,iBAAkB,4BAClB,iBAAkB,KAAM,wFACxB,iBAAkB,KAAM,oEACxB,iBAAkB,KAAM,kEACxB,mBAAoB,EAAE,EAAE,IAAI,EAAE,KACtB,WAAY,EAAE,EAAE,IAAI,EAAE,KAEhC,iBACE,MAAO,QACP,aAAc,KACd,iBAAkB,4BAClB,iBAAkB,KAAM,wFACxB,iBAAkB,KAAM,oEACxB,iBAAkB,KAAM,kEACxB,iBAAkB,QAClB,mBAAoB,KACZ,WAAY,KAEtB,+DACA,wDACE,mBAAoB,EAAE,EAAE,IAAI,EAAE,KACtB,WAAY,EAAE,EAAE,IAAI,EAAE,KAEhC,kBACE,MAAO,QACP,aAAc,QACd,iBAAkB,4BAClB,iBAAkB,KAAM,wFACxB,iBAAkB,KAAM,oEACxB,iBAAkB,KAAM,kEACxB,iBAAkB,KAClB,mBAAoB,KACZ,WAAY,KAEtB,4BAMA,mCAJA,kCADA,6BAIA,oCAFA,mCAIE,MAAO,QACP,aAAc,QACd,iBAAkB,KAClB,iBAAkB,4BAClB,iBAAkB,KAAM,wFACxB,iBAAkB,KAAM,oEACxB,iBAAkB,KAAM,kEACxB,mBAAoB,KACZ,WAAY,KAEtB,yBACA,kBACE,aAAc,YAIhB,kCADA,2BADA,oBAGE,iBAAkB,YAClB,cAAe,IAEjB,0CACE,iBAAkB,YAEpB,gBACA,sBACE,QAAS,EAEX,sBACE,mBAAoB,MAAM,EAAE,EAAE,IAAI,IAAI,KAC9B,WAAY,MAAM,EAAE,EAAE,IAAI,IAAI,KAExC,gCACE,WAAY,IACZ,aAAc,QAEhB,wBACE,QAAS,EACT,aAAc,QACd,mBAAoB,MAAM,EAAE,EAAE,IAAI,IAAI,KAC9B,WAAY,MAAM,EAAE,EAAE,IAAI,IAAI;;;;;;;;;;;;;;;;;;;;;;;AAGxC,yBACE,aAAc,QACd,WAAY,KACZ,cAAe,IAEjB,+BACA,mDACE,aAAc,QACd,mBAAoB,KACZ,WAAY,KAEtB,6CACE,iBAAkB,KAClB,aAAc,QACd,MAAO,QAET,gCACE,mBAAoB,EAAE,EAAE,IAAI,EAAE,KACtB,WAAY,EAAE,EAAE,IAAI,EAAE,KAC9B,aAAc,QAEhB,oDACE,mBAAoB,EAAE,EAAE,IAAI,EAAE,KACtB,WAAY,EAAE,EAAE,IAAI,EAAE,KAC9B,aAAc,QAEhB,uCACE,MAAO,QAET,oDACE,mBAAoB,KACZ,WAAY,KAItB,6DADA,sDAEA,4DAHA,8CAIE,MAAO,QACP,WAAY,KACZ,aAAc,QACd,cAAe,IAEjB,2CACE,aAAc,QACd,mBAAoB,EAAE,EAAE,IAAI,EAAE,KACtB,WAAY,EAAE,EAAE,IAAI,EAAE,KAEhC,kDACE,iBAAkB,QAClB,iBAAkB,4BAClB,iBAAkB,KAAM,wFACxB,iBAAkB,KAAM,oEACxB,iBAAkB,KAAM,kEACxB,aAAc,QACd,cAAe,IAEjB,wDACE,aAAc,QACd,iBAAkB;;;;;;;;;;;;;;;;;;;;;;;AAGpB,sBACE,aAAc,QACd,cAAe,IACf,iBAAkB,KAClB,aAAc,IAEhB,4BACA,6CACE,aAAc,QACd,mBAAoB,KACZ,WAAY,KAEtB,sCACE,iBAAkB,QAClB,cAAe,IAEjB,6BACE,aAAc,QACd,mBAAoB,EAAE,EAAE,IAAI,EAAE,KACtB,WAAY,EAAE,EAAE,IAAI,EAAE,KAEhC,8CACE,mBAAoB,EAAE,EAAE,IAAI,EAAE,KACtB,WAAY,EAAE,EAAE,IAAI,EAAE,KAC9B,aAAc,QAEhB,iCACE,MAAO,QAGT,+CADA,wCAEA,6CACA,8CACE,WAAY,KACZ,aAAc,QACd,mBAAoB,KACZ,WAAY,KAEtB,+CACE,iBAAkB,QAClB,QAAS,GAEX,qCACE,aAAc,QACd,mBAAoB,EAAE,EAAE,IAAI,EAAE,KACtB,WAAY,EAAE,EAAE,IAAI,EAAE,KAEhC,yGAIE,WAOA,yBARA,aADA,qBADA,wBAWA,gCACA,qDACA,kDAPA,6BACA,2CAFA,4BAGA,+BACA,6CALA,aAUE,iBAAkB,2BAClB,wBAAyB,MAAM,MACvB,gBAAiB,MAAM,MAEjC,0BAEA,yBADA,wBAEE,cAAe,IAAI,EAAE,EAAE,IAEzB,kBACA,gBACA,eACA,cACA,kBACA,cACE,iBAAkB,+BAClB,wBAAyB,KAAK,KACtB,gBAAiB,KAAK,MAGlC,6CAEE,qDADA,wDAEE,aAAc,MAIlB,0CAME,+BAJA,uBAKA,iCAJA,yBAKA,mCAJA,2BACA,mCAJA,2BAQE,cAAe,kBACX,UAAW,kBACf,kBAAmB,kBAOrB,+CAJA,uCAKA,iDAJA,yCAKA,mDAJA,2CACA,mDAJA,2CAQE,cAAe,iBACX,UAAW,iBACf,kBAAmB,iBAOrB,+CAJA,kEAKA,iDAJA,oEAKA,mDAJA,sEACA,mDAJA,sEAQE,iBAAkB,4BAClB,iBAAkB,KAAM,wFACxB,iBAAkB,KAAM,oEACxB,iBAAkB,KAAM,kEACxB,oBAAqB,IAAI,IACzB,iBAAkB,QAClB,aAAc,QAGhB,+CACA,iDACA,mDAHA,mDAIE,aAAc,QAGhB,kEACA,oEACA,sEAHA,sEAIE,cAAe,IAGjB,oEACA,sEACA,wEAHA,wEAIE,cAAe,EAGjB,mFACA,qFACA,uFAHA,uFAIE,cAAe,IAAI,IAAI,EAAE,EAG3B,6CAIA,qDAIA,mDAIA,2DAXA,+CAIA,uDAIA,qDAIA,6DAXA,iDAIA,yDAIA,uDAIA,+DAfA,iDAIA,yDAIA,uDAIA,+DAIE,cAAe,EAGjB,8DAIA,sEAHA,gEAIA,wEAHA,kEAIA,0EAPA,kEAIA,0EAIE,cAAe,EAAE,EAAE,IAAI,IAGzB,qDAIA,wEAHA,uDAIA,0EAHA,yDAIA,4EAPA,yDAIA,4EAIE,aAAc,KACd,iBAAkB,4BAClB,iBAAkB,KAAM,wFACxB,iBAAkB,KAAM,oEACxB,iBAAkB,KAAM,kEACxB,iBAAkB,KAGpB,0EACA,4EACA,8EAHA,8EAIE,MAAO,QACP,UAAW,KAGb,gFACA,kFACA,oFAHA,oFAIE,MAAO,QAGT,qDAIA,2DAHA,uDAIA,6DAHA,yDAIA,+DAPA,yDAIA,+DAIE,QAAS,MACT,QAAS,GACT,SAAU,SACV,IAAK,IACL,WAAY,MACZ,MAAO,OACP,MAAO,QACP,OAAQ,QAGV,wCAIA,iEAHA,0CAIA,mEAHA,4CAIA,qEAPA,4CAIA,qEAIE,aAAc,IAAI,IAAI,EAAE,IACxB,aAAc,MACd,aAAc,QACd,iBAAkB,KAClB,cAAe,IAAI,IAAI,EAAE,EACzB,mBAAoB,EAAE,IAAI,IAAI,EAAE,eACxB,WAAY,EAAE,IAAI,IAAI,EAAE,eAGlC,iEACA,mEACA,qEAHA,qEAIE,aAAc,IACd,iBAAkB,KAClB,cAAe,IAOjB,+BAJA,yCAKA,iCAJA,2CAKA,mCAJA,6CACA,mCAJA,6CAQE,cAAe,IAGjB,qDACA,uDACA,yDAHA,yDAIE,SAAU,UAGd,iBACE,iBAAkB,KAClB,OAAQ,kBACR,QAAS,IAEX,sBACE,aAAc,eACd,mBAAoB,MAAM,EAAE,IAAI,IAAI,eAC5B,WAAY,MAAM,EAAE,IAAI,IAAI,eACpC,mBAAoB,aAAa,IAAK,OAAQ,mBAAmB,IAAK,OACtE,WAAY,aAAa,IAAK,OAAQ,mBAAmB,IAAK,OAC9D,WAAY,WAAW,IAAK,OAAQ,aAAa,IAAK,OACtD,WAAY,WAAW,IAAK,OAAQ,aAAa,IAAK,OAAQ,mBAAmB,IAAK,OAExF,4BACE,aAAc,eACd,mBAAoB,MAAM,EAAE,IAAI,IAAI,eAC5B,WAAY,MAAM,EAAE,IAAI,IAAI,eAEtC,mBACE,iBAAkB,QAClB,mBAAoB,EAAE,EAAE,EAAE,IAAI,eACtB,WAAY,EAAE,EAAE,EAAE,IAAI,eAEhC,yBACE,iBAAkB,KAClB,aAAc,QACd,mBAAoB,EAAE,EAAE,EAAE,IAAI,oBACtB,WAAY,EAAE,EAAE,EAAE,IAAI,oBAEhC,sCACE,OAAQ,IAAI,MAAM,KAClB,mBAAoB,EAAE,EAAE,EAAE,IAAI,eACtB,WAAY,EAAE,EAAE,EAAE,IAAI,eAC9B,WAAY,KACZ,MAAO,QAET,qCACE,WAAY,qBACZ,OAAQ,IAGV,iBACE,iBAAkB,yBAEpB,yGACE,iBACE,iBAAkB,6BAGtB,sBACE,MAAO;;;;;;;;;;;;;;;;;;;;;;;AAIT,6BADA,0BAEE,iBAAkB,KAIpB,6BADA,0BADA,0BAGE,iBAAkB,KAClB,iBAAkB,KAClB,MAAO,KACP,aAAc,KAEhB,0BACE,aAAc,KAEhB,gCACE,aAAc,YAAY,KAAQ,KAAQ,YAE5C,oBACE,aAAc,KAGhB,yCADA,yCAEE,aAAc,QAEhB,iDACA,8CACE,aAAc,KAEhB,+CACE,iBAAkB,KAGpB,sCADA,yCAEE,aAAc,qBACd,iBAAkB,qBAEpB,oCACE,aAAc,KAGhB,mEADA,sEAEE,oBAAqB,KAGvB,gEADA,mEAEE,mBAAoB,KAEtB,aACA,yBACE,aAAc,KACd,mBAAoB,MAAM,EAAE,EAAE,EAAE,IAAI,KAAM,EAAE,EAAE,EAAE,IAAI,KAC5C,WAAY,MAAM,EAAE,EAAE,EAAE,IAAI,KAAM,EAAE,EAAE,EAAE,IAAI,KAEtD,yBACE,iBAAkB,qBAEpB,2BACE,aAAc,eACd,iBAAkB,KAEpB,oCACE,MAAO,QACP,iBAAkB,KAEpB,yCACE,iBAAkB,KAClB,aAAc,QAEhB,oEACE,aAAc,KAEhB,4EACE,aAAc,KAEhB,4CACE,iBAAkB,KAClB,MAAO,QAET,gCACA,qCACA,qCACE,iBAAkB,KAEpB,6DACA,6DACE,iBAAkB,KAEpB,0CACE,iBAAkB,KAClB,aAAc,KAEhB,kCACE,iBAAkB,qBAEpB,iEACE,iBAAkB,qBAEpB,mDACE,aAAc,QAEhB,sBACE,cAAe,IACf,iBAAkB,KAClB,mBAAoB,MAAM,EAAE,EAAE,EAAE,IAAI,QAC5B,WAAY,MAAM,EAAE,EAAE,EAAE,IAAI,QAEtC,qCACE,MAAO,QACP,iBAAkB,KAEpB,4BACE,MAAO,QACP,WAAY,KACZ,aAAc,QAEhB,mCACE,aAAc,QAEhB,sBACE,MAAO,QAET,wCACE,MAAO,QAGT,8BADA,sCAEE,aAAc,QACd,cAAe,IAEjB,qCACA,yDACE,aAAc,QAEhB,mFACE,iBAAkB,4BAClB,iBAAkB,KAAM,wFACxB,iBAAkB,KAAM,oEACxB,iBAAkB,KAAM,kEACxB,iBAAkB,KAClB,MAAO,QAET,0CACE,aAAc,QACd,mBAAoB,EAAE,IAAI,IAAI,EAAE,eACxB,WAAY,EAAE,IAAI,IAAI,EAAE,eAElC,iDACE,WAAY,KACZ,aAAc,YACd,oBAAqB,QACrB,kBAAmB,QAErB,4CACA,0CACE,WAAY,IACZ,aAAc,QAEhB,2CACE,WAAY,QAEd,2DACE,cAAe,IAGjB,iCACA,uCAFA,iCAGE,cAAe,IAEjB,oCACE,aAAc,QAEhB,0CACE,cAAe,EAEjB,qBACE,cAAe,IAEjB,kCACE,iBAAkB,QAEpB,+BACE,iBAAkB,YAEpB,qCACE,iBAAkB,KAEpB,qCACE,iBAAkB,KAClB,MAAO,KAET,2CACE,iBAAkB,QAEpB,sCACE,aAAc,QAEhB,6DACE,iBAAkB,KAEpB,iEACE,iBAAkB,KAClB,aAAc,QACd,cAAe,IAAI,EAAE,EAAE,IAEzB,cACE,MAAO,KAET,cACE,MAAO,KAET,eACE,YAAa,IAEf,cACE,MAAO,QAET,gBACE,MAAO,IAET,eACE,MAAO,QAET,mBACE,YAAa,IAEf,sBACE,iBAAkB,QAEpB,YACE,aAAc,QACd,iBAAkB,qBAEpB,YACE,aAAc,QACd,iBAAkB,sBAEpB,YACE,aAAc,QACd,iBAAkB,sBAEpB,YACE,aAAc,QACd,iBAAkB,sBAEpB,YACE,aAAc,QACd,iBAAkB,sBAEpB,YACE,aAAc,QACd,iBAAkB,sBAEpB,2CACE,MAAO,KAET,6CACE,iBAAkB,KAClB,MAAO,QAET,0DACE,oBACE,iBAAkB,MAGtB,iDACE,MAAO,KAET,+CACE,MAAO,MAET,mDACE,MAAO,MAET,4CACE,MAAO,QAGT,4DACA,6DAFA,2DAGE,MAAO,QAGT,YACE,MAAO,KACP,OAAQ,KACR,WAAY,0BAAyB,UAAU,IAAI,OAErD,oBACE,oBAAqB,MAAM,OAE7B,6BACE,oBAAqB,MAAM,OAE7B,qBACE,oBAAqB,MAAM,OAE7B,kBACE,QAAS,EACT,mBAAoB,EAAE,EAAE,IAAI,EAAE,eACtB,WAAY,EAAE,EAAE,IAAI,EAAE,eAEhC,uCACE,oBAAqB,MAAM,OAE7B,2BAGA,wCACA,wCACA,4CAJA,mCAKA,8CAJA,sCAKE,QAAS,KACT,SAAU,SACV,MAAO,EACP,MAAO,eAET,2BACE,aAAc,IAEhB,4CACE,MAAO", "file": "web/kendo.uniform.min.css", "sourceRoot": "/source/", "sourcesContent": ["/** \r\n * Kendo UI v2016.2.714 (http://www.telerik.com/kendo-ui)                                                                                                                                               \r\n * Copyright 2016 Telerik AD. All rights reserved.                                                                                                                                                      \r\n *                                                                                                                                                                                                      \r\n * Kendo UI commercial licenses may be obtained at                                                                                                                                                      \r\n * http://www.telerik.com/purchase/license-agreement/kendo-ui-complete                                                                                                                                  \r\n * If you do not own a commercial license, this file shall be governed by the trial license terms.                                                                                                      \r\n                                                                                                                                                                                                       \r\n                                                                                                                                                                                                       \r\n                                                                                                                                                                                                       \r\n                                                                                                                                                                                                       \r\n                                                                                                                                                                                                       \r\n                                                                                                                                                                                                       \r\n                                                                                                                                                                                                       \r\n                                                                                                                                                                                                       \r\n                                                                                                                                                                                                       \r\n                                                                                                                                                                                                       \r\n                                                                                                                                                                                                       \r\n                                                                                                                                                                                                       \r\n                                                                                                                                                                                                       \r\n                                                                                                                                                                                                       \r\n                                                                                                                                                                                                       \r\n\r\n*/\r\n/* Kendo skin */\r\n.k-theme-test-class,\r\n.ktb-theme-id-default {\r\n  opacity: 0;\r\n}\r\n.ktb-var-accent {\r\n  color: #eee;\r\n}\r\n.ktb-var-base {\r\n  color: #fff;\r\n}\r\n.ktb-var-background {\r\n  color: #fff;\r\n}\r\n.ktb-var-border-radius {\r\n  border-radius: 6px;\r\n}\r\n.ktb-var-normal-background {\r\n  color: #fff;\r\n}\r\n.ktb-var-normal-gradient {\r\n  background-image: url('textures/highlight.png');\r\n  background-image: none, -webkit-gradient(linear, left top, left bottom, from(rgba(0,0,0,.01)), to(rgba(0,0,0,.08)));\r\n  background-image: none, -webkit-linear-gradient(top, rgba(0,0,0,.01) 0%, rgba(0,0,0,.08) 100%);\r\n  background-image: none, linear-gradient(to bottom, rgba(0,0,0,.01) 0%, rgba(0,0,0,.08) 100%);\r\n}\r\n.ktb-var-normal-text-color {\r\n  color: #676767;\r\n}\r\n.ktb-var-hover-background {\r\n  color: #fff;\r\n}\r\n.ktb-var-hover-gradient {\r\n  background-image: url('textures/highlight.png');\r\n  background-image: none, -webkit-gradient(linear, left top, left bottom, from(rgba(0,0,0,.01)), to(rgba(0,0,0,.06)));\r\n  background-image: none, -webkit-linear-gradient(top, rgba(0,0,0,.01) 0%, rgba(0,0,0,.06) 100%);\r\n  background-image: none, linear-gradient(to bottom, rgba(0,0,0,.01) 0%, rgba(0,0,0,.06) 100%);\r\n}\r\n.ktb-var-hover-text-color {\r\n  color: #676767;\r\n}\r\n.ktb-var-selected-background {\r\n  color: #eee;\r\n}\r\n.ktb-var-selected-gradient {\r\n  background-image: url('textures/highlight.png');\r\n  background-image: none, -webkit-gradient(linear, left top, left bottom, from(rgba(0,0,0,.13)), to(rgba(0,0,0,.08)));\r\n  background-image: none, -webkit-linear-gradient(top, rgba(0,0,0,.13) 0%, rgba(0,0,0,.08) 100%);\r\n  background-image: none, linear-gradient(to bottom, rgba(0,0,0,.13) 0%, rgba(0,0,0,.08) 100%);\r\n}\r\n.ktb-var-selected-text-color {\r\n  color: #454545;\r\n}\r\n.ktb-var-error {\r\n  color: #ffe0d9;\r\n}\r\n.ktb-var-warning {\r\n  color: #ffe9a8;\r\n}\r\n.ktb-var-success {\r\n  color: #eaf7ec;\r\n}\r\n.ktb-var-info {\r\n  color: #e5f5fa;\r\n}\r\n.ktb-var-series-a {\r\n  color: #527aa3;\r\n}\r\n.ktb-var-series-b {\r\n  color: #6f91b3;\r\n}\r\n.ktb-var-series-c {\r\n  color: #8ca7c2;\r\n}\r\n.ktb-var-series-d {\r\n  color: #a8bdd1;\r\n}\r\n.ktb-var-series-e {\r\n  color: #c5d3e0;\r\n}\r\n.ktb-var-series-f {\r\n  color: #e2e9f0;\r\n}\r\n.k-grid-norecords-template {\r\n  background-color: #fff;\r\n  border: 1px solid #ebebeb;\r\n}\r\n.k-in,\r\n.k-item,\r\n.k-window-action {\r\n  border-color: transparent;\r\n}\r\n/* main colors */\r\n.k-block,\r\n.k-widget {\r\n  background-color: #fff;\r\n}\r\n.k-block,\r\n.k-widget,\r\n.k-input,\r\n.k-textbox,\r\n.k-group,\r\n.k-content,\r\n.k-header,\r\n.k-filter-row > th,\r\n.k-editable-area,\r\n.k-separator,\r\n.k-colorpicker .k-i-arrow-s,\r\n.k-textbox > input,\r\n.k-autocomplete,\r\n.k-dropdown-wrap,\r\n.k-toolbar,\r\n.k-group-footer td,\r\n.k-grid-footer,\r\n.k-footer-template td,\r\n.k-state-default,\r\n.k-state-default .k-select,\r\n.k-state-disabled,\r\n.k-grid-header,\r\n.k-grid-header-wrap,\r\n.k-grid-header-locked,\r\n.k-grid-footer-locked,\r\n.k-grid-content-locked,\r\n.k-grid td,\r\n.k-grid td.k-state-selected,\r\n.k-grid-footer-wrap,\r\n.k-pager-wrap,\r\n.k-pager-wrap .k-link,\r\n.k-pager-refresh,\r\n.k-grouping-header,\r\n.k-grouping-header .k-group-indicator,\r\n.k-panelbar > .k-item > .k-link,\r\n.k-panel > .k-item > .k-link,\r\n.k-panelbar .k-panel,\r\n.k-panelbar .k-content,\r\n.k-treemap-tile,\r\n.k-calendar th,\r\n.k-slider-track,\r\n.k-splitbar,\r\n.k-dropzone-active,\r\n.k-tiles,\r\n.k-toolbar,\r\n.k-tooltip,\r\n.k-button-group .k-tool,\r\n.k-upload-files {\r\n  border-color: #ebebeb;\r\n}\r\n.k-group,\r\n.k-toolbar,\r\n.k-grouping-header,\r\n.k-pager-wrap,\r\n.k-group-footer td,\r\n.k-grid-footer,\r\n.k-footer-template td,\r\n.k-widget .k-status,\r\n.k-calendar th,\r\n.k-dropzone-hovered,\r\n.k-widget.k-popup {\r\n  background-color: #f5f5f5;\r\n}\r\n.k-grouping-row td,\r\ntd.k-group-cell,\r\n.k-resize-handle-inner {\r\n  background-color: #ffffff;\r\n}\r\n.k-list-container {\r\n  border-color: #dbdbdb;\r\n  background-color: #fff;\r\n}\r\n.k-content,\r\n.k-editable-area,\r\n.k-panelbar > li.k-item,\r\n.k-panel > li.k-item,\r\n.k-tiles {\r\n  background-color: #fff;\r\n}\r\n.k-alt,\r\n.k-separator,\r\n.k-resource.k-alt,\r\n.k-pivot-layout > tbody > tr:first-child > td:first-child {\r\n  background-color: #ffffff;\r\n}\r\n.k-pivot-rowheaders .k-alt .k-alt,\r\n.k-header.k-alt {\r\n  background-color: #ebebeb;\r\n}\r\n.k-textbox,\r\n.k-autocomplete.k-header,\r\n.k-dropdown-wrap.k-state-active,\r\n.k-picker-wrap.k-state-active,\r\n.k-numeric-wrap.k-state-active {\r\n  border-color: #ebebeb;\r\n  background-color: #fff;\r\n}\r\n.k-textbox > input,\r\n.k-autocomplete .k-input,\r\n.k-dropdown-wrap .k-input,\r\n.k-autocomplete.k-state-focused .k-input,\r\n.k-dropdown-wrap.k-state-focused .k-input,\r\n.k-picker-wrap.k-state-focused .k-input,\r\n.k-numeric-wrap.k-state-focused .k-input {\r\n  border-color: #ebebeb;\r\n}\r\ninput.k-textbox,\r\ntextarea.k-textbox,\r\ninput.k-textbox:hover,\r\ntextarea.k-textbox:hover,\r\n.k-textbox > input {\r\n  background: none;\r\n}\r\n.k-input,\r\ninput.k-textbox,\r\ntextarea.k-textbox,\r\ninput.k-textbox:hover,\r\ntextarea.k-textbox:hover,\r\n.k-textbox > input,\r\n.k-multiselect-wrap {\r\n  background-color: #fff;\r\n  color: #676767;\r\n}\r\n.k-input[readonly] {\r\n  background-color: #fff;\r\n  color: #676767;\r\n}\r\n.k-block,\r\n.k-widget,\r\n.k-popup,\r\n.k-content,\r\n.k-toolbar,\r\n.k-dropdown .k-input {\r\n  color: #676767;\r\n}\r\n.k-inverse {\r\n  color: #ffffff;\r\n}\r\n.k-block {\r\n  color: #464646;\r\n}\r\n.k-link:link,\r\n.k-link:visited,\r\n.k-nav-current.k-state-hover .k-link {\r\n  color: #686666;\r\n}\r\n.k-tabstrip-items .k-link,\r\n.k-panelbar > li > .k-link {\r\n  color: #676767;\r\n}\r\n.k-header,\r\n.k-treemap-title,\r\n.k-grid-header .k-header > .k-link {\r\n  color: #464646;\r\n}\r\n.k-header,\r\n.k-grid-header,\r\n.k-toolbar,\r\n.k-dropdown-wrap,\r\n.k-picker-wrap,\r\n.k-numeric-wrap,\r\n.k-grouping-header,\r\n.k-pager-wrap,\r\n.k-textbox,\r\n.k-button,\r\n.k-progressbar,\r\n.k-draghandle,\r\n.k-autocomplete,\r\n.k-state-highlight,\r\n.k-tabstrip-items .k-item,\r\n.k-panelbar .k-tabstrip-items .k-item,\r\n.km-pane-wrapper > .km-pane > .km-view > .km-content {\r\n  background-image: url('textures/highlight.png');\r\n  background-image: none, -webkit-gradient(linear, left top, left bottom, from(rgba(0,0,0,.01)), to(rgba(0,0,0,.08)));\r\n  background-image: none, -webkit-linear-gradient(top, rgba(0,0,0,.01) 0%, rgba(0,0,0,.08) 100%);\r\n  background-image: none, linear-gradient(to bottom, rgba(0,0,0,.01) 0%, rgba(0,0,0,.08) 100%);\r\n  background-position: 50% 50%;\r\n  background-color: #ffffff;\r\n}\r\n.k-block,\r\n.k-header,\r\n.k-grid-header,\r\n.k-toolbar,\r\n.k-grouping-header,\r\n.k-pager-wrap,\r\n.k-button,\r\n.k-draghandle,\r\n.k-treemap-tile,\r\nhtml .km-pane-wrapper .k-header {\r\n  background-color: #ffffff;\r\n}\r\n/* icons */\r\n.k-icon:hover,\r\n.k-state-hover .k-icon,\r\n.k-state-selected .k-icon,\r\n.k-state-focused .k-icon,\r\n.k-column-menu .k-state-hover .k-sprite,\r\n.k-column-menu .k-state-active .k-sprite,\r\n.k-pager-numbers .k-current-page .k-link:hover:after,\r\n.k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view.k-state-hover > .k-link:after {\r\n  opacity: 1;\r\n}\r\n.k-icon,\r\n.k-state-disabled .k-icon,\r\n.k-column-menu .k-sprite,\r\n.k-pager-numbers .k-current-page .k-link:after,\r\n.k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view > .k-link:after {\r\n  opacity: 0.9;\r\n}\r\n.k-mobile-list .k-check:checked,\r\n.k-mobile-list .k-edit-field [type=checkbox]:checked,\r\n.k-mobile-list .k-edit-field [type=radio]:checked {\r\n  opacity: 0.9;\r\n}\r\n.k-tool {\r\n  border-color: transparent;\r\n}\r\n.k-icon,\r\n.k-tool-icon,\r\n.k-grouping-dropclue,\r\n.k-drop-hint,\r\n.k-column-menu .k-sprite,\r\n.k-grid-mobile .k-resize-handle-inner:before,\r\n.k-grid-mobile .k-resize-handle-inner:after,\r\n.k-pager-numbers .k-current-page .k-link:after,\r\n.k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view > .k-link:after,\r\n.k-gantt-views > .k-current-view > .k-link:after {\r\n  background-image: url('Uniform/sprite.png');\r\n  border-color: transparent;\r\n}\r\n/* IE will ignore the above selectors if these are added too */\r\n.k-mobile-list .k-check:checked,\r\n.k-mobile-list .k-edit-field [type=checkbox]:checked,\r\n.k-mobile-list .k-edit-field [type=radio]:checked {\r\n  background-image: url('Uniform/sprite.png');\r\n  border-color: transparent;\r\n}\r\n.k-loading,\r\n.k-state-hover .k-loading {\r\n  background-image: url('Uniform/loading.gif');\r\n  background-position: 50% 50%;\r\n}\r\n.k-loading-image {\r\n  background-image: url('Uniform/loading-image.gif');\r\n}\r\n.k-loading-color {\r\n  background-color: #fcfcfc;\r\n}\r\n.k-button {\r\n  color: #676767;\r\n  border-color: #dbdbdb;\r\n  background-color: #ffffff;\r\n}\r\n.k-draghandle {\r\n  border-color: #a0dba9;\r\n  background-color: #ffffff;\r\n  -webkit-box-shadow: none;\r\n          box-shadow: none;\r\n}\r\n.k-draghandle:hover {\r\n  border-color: #95d79f;\r\n  background-color: #ffffff;\r\n  -webkit-box-shadow: none;\r\n          box-shadow: none;\r\n}\r\n/* Scheduler */\r\n.k-scheduler {\r\n  color: #646464;\r\n  background-color: #fff;\r\n}\r\n.k-scheduler-layout {\r\n  color: #676767;\r\n}\r\n.k-scheduler-datecolumn,\r\n.k-scheduler-groupcolumn {\r\n  background-color: #fff;\r\n  color: #676767;\r\n}\r\n.k-scheduler-times tr,\r\n.k-scheduler-times th,\r\n.k-scheduler-table td,\r\n.k-scheduler-header th,\r\n.k-scheduler-header-wrap,\r\n.k-scheduler-times {\r\n  border-color: #dbdbdb;\r\n}\r\n.k-nonwork-hour {\r\n  background-color: #f2f2f2;\r\n}\r\n.k-gantt .k-nonwork-hour {\r\n  background-color: rgba(255, 255, 255, 0.2);\r\n}\r\n.k-gantt .k-header.k-nonwork-hour {\r\n  background-color: rgba(255, 255, 255, 0.15);\r\n}\r\n.k-scheduler-table .k-today,\r\n.k-today > .k-scheduler-datecolumn,\r\n.k-today > .k-scheduler-groupcolumn {\r\n  background-color: #f7f7f7;\r\n}\r\n.k-scheduler-now-arrow {\r\n  border-left-color: #ff6745;\r\n}\r\n.k-scheduler-now-line {\r\n  background-color: #ff6745;\r\n}\r\n.k-event,\r\n.k-task-complete {\r\n  border-color: #dcdcdc;\r\n  background: #b6b6b6 0 -257px url('textures/highlight.png') repeat-x;\r\n  color: #646464;\r\n}\r\n.k-event-inverse {\r\n  color: #fff;\r\n}\r\n.k-event.k-state-selected {\r\n  background-position: 0 0;\r\n  -webkit-box-shadow: 0 0 0 2px #676767;\r\n          box-shadow: 0 0 0 2px #676767;\r\n}\r\n.k-event .k-resize-handle:after,\r\n.k-task-single .k-resize-handle:after {\r\n  background-color: #646464;\r\n}\r\n.k-scheduler-marquee:before,\r\n.k-scheduler-marquee:after {\r\n  border-color: #eee;\r\n}\r\n.k-panelbar .k-content,\r\n.k-panelbar .k-panel,\r\n.k-panelbar .k-item {\r\n  background-color: #fff;\r\n  color: #676767;\r\n  border-color: #dbdbdb;\r\n}\r\n.k-panelbar > li > .k-link {\r\n  color: #676767;\r\n}\r\n.k-panelbar > .k-item > .k-link {\r\n  border-color: #dbdbdb;\r\n}\r\n.k-panel > li.k-item {\r\n  background-color: #fff;\r\n}\r\n/* states */\r\n.k-state-active,\r\n.k-state-active:hover,\r\n.k-active-filter,\r\n.k-tabstrip .k-state-active {\r\n  background-color: #ffffff;\r\n  border-color: #b5b5b5;\r\n  color: #464646;\r\n}\r\n.k-fieldselector .k-list-container {\r\n  background-color: #ffffff;\r\n}\r\n.k-button:focus,\r\n.k-button.k-state-focused {\r\n  border-color: #dbdbdb;\r\n}\r\n.k-button:hover,\r\n.k-button.k-state-hover {\r\n  color: #676767;\r\n  border-color: #cccccc;\r\n  background-color: #fff;\r\n}\r\n.k-button:active,\r\n.k-button.k-state-active {\r\n  color: #454545;\r\n  background-color: #eee;\r\n  border-color: #cdcdcd;\r\n}\r\n.k-button:active:hover,\r\n.k-button.k-state-active:hover {\r\n  color: #646464;\r\n  border-color: #cccccc;\r\n  background-color: #f6f6f6;\r\n}\r\n.k-button[disabled],\r\n.k-button.k-state-disabled,\r\n.k-state-disabled .k-button,\r\n.k-state-disabled .k-button:hover,\r\n.k-button.k-state-disabled:hover,\r\n.k-state-disabled .k-button:active,\r\n.k-button.k-state-disabled:active {\r\n  color: #b5b5b5;\r\n  border-color: #dbdbdb;\r\n  background-color: #ffffff;\r\n  background-image: url('textures/highlight.png');\r\n  background-image: none, -webkit-gradient(linear, left top, left bottom, from(rgba(0,0,0,.01)), to(rgba(0,0,0,.08)));\r\n  background-image: none, -webkit-linear-gradient(top, rgba(0,0,0,.01) 0%, rgba(0,0,0,.08) 100%);\r\n  background-image: none, linear-gradient(to bottom, rgba(0,0,0,.01) 0%, rgba(0,0,0,.08) 100%);\r\n}\r\n.k-button:focus:not(.k-state-disabled):not([disabled]) {\r\n  -webkit-box-shadow: inset 0 0 3px 1px #cccccc;\r\n          box-shadow: inset 0 0 3px 1px #cccccc;\r\n}\r\n.k-button:focus:active:not(.k-state-disabled):not([disabled]) {\r\n  -webkit-box-shadow: inset 0 0 3px 1px #b3b3b3;\r\n          box-shadow: inset 0 0 3px 1px #b3b3b3;\r\n}\r\n.k-menu .k-state-hover > .k-state-active {\r\n  background-color: transparent;\r\n}\r\n.k-menu .k-state-selected > .k-link {\r\n  color: #454545;\r\n  background-color: #eee;\r\n  border-color: #cdcdcd;\r\n  background-image: url('textures/highlight.png');\r\n  background-image: none, -webkit-gradient(linear, left top, left bottom, from(rgba(0,0,0,.13)), to(rgba(0,0,0,.08)));\r\n  background-image: none, -webkit-linear-gradient(top, rgba(0,0,0,.13) 0%, rgba(0,0,0,.08) 100%);\r\n  background-image: none, linear-gradient(to bottom, rgba(0,0,0,.13) 0%, rgba(0,0,0,.08) 100%);\r\n}\r\n.k-menu .k-link.k-state-active {\r\n  background-color: #ffffff;\r\n  border-color: #b5b5b5;\r\n  color: #464646;\r\n}\r\n.k-menu .k-state-hover > .k-link {\r\n  color: #676767;\r\n  background-color: #fff;\r\n  border-color: #cccccc;\r\n  background-image: url('textures/highlight.png');\r\n  background-image: none, -webkit-gradient(linear, left top, left bottom, from(rgba(0,0,0,.01)), to(rgba(0,0,0,.06)));\r\n  background-image: none, -webkit-linear-gradient(top, rgba(0,0,0,.01) 0%, rgba(0,0,0,.06) 100%);\r\n  background-image: none, linear-gradient(to bottom, rgba(0,0,0,.01) 0%, rgba(0,0,0,.06) 100%);\r\n}\r\n.k-state-highlight {\r\n  background: #ffffff;\r\n  color: #464646;\r\n}\r\n.k-state-focused,\r\n.k-grouping-row .k-state-focused {\r\n  border-color: #ebebeb;\r\n}\r\n.k-calendar .k-link {\r\n  color: #686666;\r\n}\r\n.k-calendar .k-footer {\r\n  padding: 0;\r\n}\r\n.k-calendar .k-footer .k-nav-today {\r\n  color: #686666;\r\n  text-decoration: none;\r\n  background-color: #fff;\r\n}\r\n.k-calendar .k-footer .k-nav-today:hover,\r\n.k-calendar .k-footer .k-nav-today.k-state-hover {\r\n  background-color: #fff;\r\n  text-decoration: underline;\r\n}\r\n.k-calendar .k-footer .k-nav-today:active {\r\n  background-color: #fff;\r\n}\r\n.k-calendar .k-link.k-nav-fast {\r\n  color: #686666;\r\n}\r\n.k-calendar .k-nav-fast.k-state-hover {\r\n  text-decoration: none;\r\n  background-color: #fff;\r\n  color: #676767;\r\n}\r\n.k-calendar .k-link.k-state-hover {\r\n  border-radius: 4px;\r\n}\r\n.k-calendar .k-today {\r\n  -webkit-box-shadow: inset 0 0 0 1px #eee;\r\n          box-shadow: inset 0 0 0 1px #eee;\r\n}\r\n.k-calendar .k-today .k-link {\r\n  color: #454545;\r\n  font-weight: bold;\r\n}\r\n.k-calendar td.k-today.k-state-focused,\r\n.k-calendar td.k-today.k-state-focused.k-state-hover {\r\n  -webkit-box-shadow: inset 0 0 3px 1px #b3b3b3;\r\n          box-shadow: inset 0 0 3px 1px #b3b3b3;\r\n}\r\n.k-calendar td.k-today.k-state-hover,\r\n.k-calendar td.k-today.k-state-selected {\r\n  -webkit-box-shadow: none;\r\n          box-shadow: none;\r\n}\r\n.k-calendar td.k-today.k-state-hover .k-link {\r\n  color: #686666;\r\n}\r\n.k-calendar .k-footer .k-link {\r\n  border-radius: 0;\r\n}\r\n.k-calendar th {\r\n  background-color: #f5f5f5;\r\n}\r\n.k-calendar td.k-state-selected.k-state-hover {\r\n  background-color: #efeded;\r\n  background-image: none;\r\n}\r\n.k-calendar td.k-state-selected .k-link,\r\n.k-calendar td.k-today.k-state-selected.k-state-hover .k-link {\r\n  color: #454545;\r\n}\r\n.k-calendar td.k-state-focused.k-state-selected,\r\n.k-calendar td.k-state-selected.k-state-active,\r\n.k-calendar td.k-state-selected:active,\r\n.k-calendar td.k-state-selected.k-state-hover:active {\r\n  -webkit-box-shadow: inset 0 0 3px 1px #b3b3b3;\r\n          box-shadow: inset 0 0 3px 1px #b3b3b3;\r\n}\r\n.k-window-titlebar .k-link {\r\n  border-radius: 4px;\r\n}\r\n.k-calendar-container.k-group {\r\n  border-color: #dbdbdb;\r\n}\r\n.k-state-selected,\r\n.k-state-selected:link,\r\n.k-state-selected:visited,\r\n.k-list > .k-state-selected,\r\n.k-list > .k-state-highlight,\r\n.k-panel > .k-state-selected,\r\n.k-ghost-splitbar-vertical,\r\n.k-ghost-splitbar-horizontal,\r\n.k-draghandle.k-state-selected:hover,\r\n.k-scheduler .k-scheduler-toolbar .k-state-selected,\r\n.k-scheduler .k-today.k-state-selected,\r\n.k-marquee-color {\r\n  color: #454545;\r\n  background-color: #eee;\r\n  border-color: #cdcdcd;\r\n}\r\n.k-virtual-item.k-first,\r\n.k-group-header + .k-list > .k-item.k-first,\r\n.k-static-header + .k-list > .k-item.k-first {\r\n  border-top-color: #cccccc;\r\n}\r\n.k-group-header + div > .k-list > .k-item.k-first:before {\r\n  border-top-color: #cccccc;\r\n}\r\n.k-popup > .k-group-header,\r\n.k-popup > .k-virtual-wrap > .k-group-header {\r\n  background: #cccccc;\r\n  color: #454545;\r\n}\r\n.k-popup .k-list .k-item > .k-group {\r\n  background: #cccccc;\r\n  color: #676767;\r\n  border-bottom-left-radius: 5px;\r\n}\r\n.k-popup .k-treeview .k-item > .k-group {\r\n  background: transparent;\r\n  color: #676767;\r\n}\r\n.k-marquee-text {\r\n  color: #454545;\r\n}\r\n.k-state-focused,\r\n.k-list > .k-state-focused,\r\n.k-listview > .k-state-focused,\r\n.k-grid-header th.k-state-focused,\r\ntd.k-state-focused,\r\n.k-button.k-state-focused {\r\n  -webkit-box-shadow: inset 0 0 3px 1px #b3b3b3;\r\n          box-shadow: inset 0 0 3px 1px #b3b3b3;\r\n}\r\n.k-state-focused.k-state-selected,\r\n.k-list > .k-state-focused.k-state-selected,\r\n.k-listview > .k-state-focused.k-state-selected,\r\ntd.k-state-focused.k-state-selected {\r\n  -webkit-box-shadow: inset 0 0 3px 1px #b3b3b3;\r\n          box-shadow: inset 0 0 3px 1px #b3b3b3;\r\n}\r\n.k-ie8 .k-panelbar span.k-state-focused,\r\n.k-ie8 .k-menu li.k-state-focused,\r\n.k-ie8 .k-listview > .k-state-focused,\r\n.k-ie8 .k-grid-header th.k-state-focused,\r\n.k-ie8 td.k-state-focused,\r\n.k-ie8 .k-tool.k-state-hover,\r\n.k-ie8 .k-button:focus,\r\n.k-ie8 .k-button.k-state-focused {\r\n  background-color: #fff;\r\n}\r\n.k-list > .k-state-selected.k-state-focused,\r\n.k-list-optionlabel.k-state-selected.k-state-focused {\r\n  -webkit-box-shadow: none;\r\n          box-shadow: none;\r\n}\r\n.k-state-selected > .k-link,\r\n.k-panelbar > li > .k-state-selected,\r\n.k-panelbar > li.k-state-default > .k-link.k-state-selected {\r\n  color: #454545;\r\n}\r\n.k-state-hover,\r\n.k-state-hover:hover,\r\n.k-splitbar-horizontal-hover:hover,\r\n.k-splitbar-vertical-hover:hover,\r\n.k-list > .k-state-hover,\r\n.k-scheduler .k-scheduler-toolbar ul li.k-state-hover,\r\n.k-pager-wrap .k-link:hover,\r\n.k-dropdown .k-state-focused,\r\n.k-filebrowser-dropzone,\r\n.k-mobile-list .k-item > .k-link:active,\r\n.k-mobile-list .k-item > .k-label:active,\r\n.k-mobile-list .k-edit-label.k-check:active,\r\n.k-mobile-list .k-recur-view .k-check:active {\r\n  color: #676767;\r\n  background-color: #fff;\r\n  border-color: #cccccc;\r\n}\r\n/* this selector should be used separately, otherwise old IEs ignore the whole rule */\r\n.k-mobile-list .k-scheduler-timezones .k-edit-field:nth-child(2):active {\r\n  color: #676767;\r\n  background-color: #fff;\r\n  border-color: #cccccc;\r\n}\r\n.k-ie8 .k-window-titlebar .k-state-hover {\r\n  border-color: #cccccc;\r\n}\r\n.k-state-hover > .k-select,\r\n.k-state-focused > .k-select {\r\n  border-color: #cccccc;\r\n}\r\n.k-button:hover,\r\n.k-button.k-state-hover,\r\n.k-button:focus,\r\n.k-button.k-state-focused,\r\n.k-textbox:hover,\r\n.k-state-hover,\r\n.k-state-hover:hover,\r\n.k-pager-wrap .k-link:hover,\r\n.k-other-month.k-state-hover .k-link,\r\ndiv.k-filebrowser-dropzone em,\r\n.k-draghandle:hover {\r\n  background-image: url('textures/highlight.png');\r\n  background-image: none, -webkit-gradient(linear, left top, left bottom, from(rgba(0,0,0,.01)), to(rgba(0,0,0,.06)));\r\n  background-image: none, -webkit-linear-gradient(top, rgba(0,0,0,.01) 0%, rgba(0,0,0,.06) 100%);\r\n  background-image: none, linear-gradient(to bottom, rgba(0,0,0,.01) 0%, rgba(0,0,0,.06) 100%);\r\n}\r\n.k-pager-wrap {\r\n  background-color: #ffffff;\r\n  color: #464646;\r\n}\r\n.k-autocomplete.k-state-active,\r\n.k-picker-wrap.k-state-active,\r\n.k-numeric-wrap.k-state-active,\r\n.k-dropdown-wrap.k-state-active,\r\n.k-state-active,\r\n.k-state-active:hover,\r\n.k-state-active > .k-link,\r\n.k-button:active,\r\n.k-panelbar > .k-item > .k-state-focused {\r\n  background-image: none;\r\n}\r\n.k-state-selected,\r\n.k-button:active,\r\n.k-button.k-state-active,\r\n.k-draghandle.k-state-selected:hover {\r\n  background-image: url('textures/highlight.png');\r\n  background-image: none, -webkit-gradient(linear, left top, left bottom, from(rgba(0,0,0,.13)), to(rgba(0,0,0,.08)));\r\n  background-image: none, -webkit-linear-gradient(top, rgba(0,0,0,.13) 0%, rgba(0,0,0,.08) 100%);\r\n  background-image: none, linear-gradient(to bottom, rgba(0,0,0,.13) 0%, rgba(0,0,0,.08) 100%);\r\n}\r\n.k-button:active,\r\n.k-button.k-state-active,\r\n.k-draghandle.k-state-selected:hover {\r\n  background-position: 50% 50%;\r\n}\r\n.k-tool-icon {\r\n  background-image: url('Uniform/sprite.png');\r\n}\r\n.k-state-hover > .k-link,\r\n.k-other-month.k-state-hover .k-link,\r\ndiv.k-filebrowser-dropzone em {\r\n  color: #676767;\r\n}\r\n.k-autocomplete.k-state-hover,\r\n.k-autocomplete.k-state-focused,\r\n.k-picker-wrap.k-state-hover,\r\n.k-picker-wrap.k-state-focused,\r\n.k-numeric-wrap.k-state-hover,\r\n.k-numeric-wrap.k-state-focused,\r\n.k-dropdown-wrap.k-state-hover,\r\n.k-dropdown-wrap.k-state-focused {\r\n  background-color: #ffffff;\r\n  background-image: url('textures/highlight.png');\r\n  background-image: none, -webkit-gradient(linear, left top, left bottom, from(rgba(0,0,0,.01)), to(rgba(0,0,0,.06)));\r\n  background-image: none, -webkit-linear-gradient(top, rgba(0,0,0,.01) 0%, rgba(0,0,0,.06) 100%);\r\n  background-image: none, linear-gradient(to bottom, rgba(0,0,0,.01) 0%, rgba(0,0,0,.06) 100%);\r\n  background-position: 50% 50%;\r\n  border-color: #cccccc;\r\n}\r\n.km-pane-wrapper .k-mobile-list input:not([type=\"checkbox\"]):not([type=\"radio\"]),\r\n.km-pane-wrapper .km-pane .k-mobile-list select:not([multiple]),\r\n.km-pane-wrapper .k-mobile-list textarea,\r\n.k-dropdown .k-state-focused .k-input {\r\n  color: #676767;\r\n}\r\n.km-pane-wrapper .km-pane .k-mobile-list.k-filter-menu .k-space-right {\r\n  background-image: url('textures/highlight.png');\r\n  background-image: none, -webkit-gradient(linear, left top, left bottom, from(rgba(0,0,0,.01)), to(rgba(0,0,0,.08)));\r\n  background-image: none, -webkit-linear-gradient(top, rgba(0,0,0,.01) 0%, rgba(0,0,0,.08) 100%);\r\n  background-image: none, linear-gradient(to bottom, rgba(0,0,0,.01) 0%, rgba(0,0,0,.08) 100%);\r\n  background-position: 50% 50%;\r\n  background: #e8e8e8;\r\n  border-color: #dbdbdb;\r\n}\r\n.km-pane-wrapper .km-pane .k-mobile-list.k-filter-menu .k-space-right > input {\r\n  background: #fff;\r\n  border-color: #dbdbdb;\r\n}\r\n.km-pane-wrapper .km-pane .k-mobile-list.k-filter-menu .k-space-right > input:focus {\r\n  -webkit-box-shadow: 0 0 3px 0 rgba(0, 0, 0, 0.3);\r\n          box-shadow: 0 0 3px 0 rgba(0, 0, 0, 0.3);\r\n}\r\n.k-dropdown .k-state-hover .k-input {\r\n  color: #676767;\r\n}\r\n.k-state-error {\r\n  border-color: #ff6745;\r\n  background-color: #b5b5b5;\r\n  color: #e4e4e4;\r\n}\r\n.k-state-disabled {\r\n  opacity: .7;\r\n}\r\n.k-ie8 .k-state-disabled {\r\n  filter: alpha(opacity=70);\r\n}\r\n.k-tile-empty.k-state-selected,\r\n.k-loading-mask.k-state-selected {\r\n  border-width: 0;\r\n  background-image: none;\r\n  background-color: transparent;\r\n}\r\n.k-state-disabled,\r\n.k-state-disabled .k-link,\r\n.k-state-disabled .k-button,\r\n.k-other-month,\r\n.k-other-month .k-link,\r\n.k-dropzone em,\r\n.k-tile-empty strong,\r\n.k-slider .k-draghandle {\r\n  color: #b5b5b5;\r\n}\r\n.k-dropzone .k-upload-status {\r\n  color: #676767;\r\n}\r\n/* Progressbar */\r\n.k-progressbar-indeterminate {\r\n  background: url('Uniform/indeterminate.gif');\r\n}\r\n.k-progressbar-indeterminate .k-progress-status-wrap,\r\n.k-progressbar-indeterminate .k-state-selected {\r\n  display: none;\r\n}\r\n/* Slider */\r\n.k-slider-track {\r\n  background-color: #ebebeb;\r\n}\r\n.k-slider-selection {\r\n  background-color: #eee;\r\n}\r\n.k-slider-horizontal .k-tick {\r\n  background-image: url('Uniform/slider-h.gif');\r\n}\r\n.k-slider-vertical .k-tick {\r\n  background-image: url('Uniform/slider-v.gif');\r\n}\r\n/* Tooltip */\r\n.k-widget.k-tooltip {\r\n  background-image: url('textures/highlight.png');\r\n  background-image: none, -webkit-gradient(linear, left top, left bottom, from(rgba(0,0,0,.01)), to(rgba(0,0,0,.08)));\r\n  background-image: none, -webkit-linear-gradient(top, rgba(0,0,0,.01) 0%, rgba(0,0,0,.08) 100%);\r\n  background-image: none, linear-gradient(to bottom, rgba(0,0,0,.01) 0%, rgba(0,0,0,.08) 100%);\r\n  background-position: 50% 50%;\r\n  background-color: #ffffff;\r\n  color: #464646;\r\n  border-color: transparent;\r\n  -webkit-box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);\r\n          box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);\r\n}\r\n.k-widget.k-tooltip-validation {\r\n  border-color: #ffe9a8;\r\n  background-color: #ffe9a8;\r\n  color: #755700;\r\n}\r\n/* Bootstrap theme fix */\r\n.input-prepend .k-tooltip-validation,\r\n.input-append .k-tooltip-validation {\r\n  font-size: 12px;\r\n  position: relative;\r\n  top: 3px;\r\n}\r\n.k-callout-n {\r\n  border-bottom-color: #ffffff;\r\n}\r\n.k-callout-w {\r\n  border-right-color: #ffffff;\r\n}\r\n.k-callout-s {\r\n  border-top-color: #ffffff;\r\n}\r\n.k-callout-e {\r\n  border-left-color: #ffffff;\r\n}\r\n.k-tooltip-validation .k-callout-n {\r\n  border-bottom-color: #ffe9a8;\r\n}\r\n.k-tooltip-validation .k-callout-w {\r\n  border-right-color: #ffe9a8;\r\n}\r\n.k-tooltip-validation .k-callout-s {\r\n  border-top-color: #ffe9a8;\r\n}\r\n.k-tooltip-validation .k-callout-e {\r\n  border-left-color: #ffe9a8;\r\n}\r\n/* Splitter */\r\n.k-splitbar {\r\n  background-color: #ffffff;\r\n}\r\n.k-restricted-size-vertical,\r\n.k-restricted-size-horizontal {\r\n  background-color: #e4e4e4;\r\n}\r\n/* Upload */\r\n.k-file {\r\n  background-color: #ffffff;\r\n  border-color: #dbdbdb;\r\n}\r\n.k-file-progress {\r\n  color: #2498bc;\r\n}\r\n.k-file-progress .k-progress {\r\n  background-color: #e5f5fa;\r\n}\r\n.k-file-success {\r\n  color: #3ea44e;\r\n}\r\n.k-file-success .k-progress {\r\n  background-color: #eaf7ec;\r\n}\r\n.k-file-error {\r\n  color: #d92800;\r\n}\r\n.k-file-error .k-progress {\r\n  background-color: #ffe0d9;\r\n}\r\n/* ImageBrowser */\r\n.k-tile {\r\n  border-color: #fff;\r\n}\r\n.k-textbox:hover,\r\n.k-tiles li.k-state-hover {\r\n  border-color: #cccccc;\r\n}\r\n.k-tiles li.k-state-selected {\r\n  border-color: #cdcdcd;\r\n}\r\n.k-filebrowser .k-tile .k-folder,\r\n.k-filebrowser .k-tile .k-file {\r\n  background-image: url('Uniform/imagebrowser.png');\r\n  -webkit-background-size: auto auto;\r\n          background-size: auto auto;\r\n}\r\n/* TreeMap */\r\n.k-leaf,\r\n.k-leaf.k-state-hover:hover {\r\n  color: #fff;\r\n}\r\n.k-leaf.k-inverse,\r\n.k-leaf.k-inverse.k-state-hover:hover {\r\n  color: #000;\r\n}\r\n/* Shadows */\r\n.k-widget,\r\n.k-button {\r\n  -webkit-box-shadow: none;\r\n          box-shadow: none;\r\n}\r\n.k-slider,\r\n.k-treeview,\r\n.k-upload {\r\n  -webkit-box-shadow: none;\r\n          box-shadow: none;\r\n}\r\n.k-state-hover {\r\n  -webkit-box-shadow: none;\r\n          box-shadow: none;\r\n}\r\n.k-textbox:focus,\r\n.k-autocomplete.k-state-focused,\r\n.k-dropdown-wrap.k-state-focused,\r\n.k-picker-wrap.k-state-focused,\r\n.k-numeric-wrap.k-state-focused {\r\n  -webkit-box-shadow: 0 0 3px 0 rgba(0, 0, 0, 0.3);\r\n          box-shadow: 0 0 3px 0 rgba(0, 0, 0, 0.3);\r\n}\r\n.k-state-selected {\r\n  -webkit-box-shadow: none;\r\n          box-shadow: none;\r\n}\r\n.k-state-active {\r\n  -webkit-box-shadow: none;\r\n          box-shadow: none;\r\n}\r\n.k-grid tr:hover {\r\n  background-color: #ffffff;\r\n  background-image: url('textures/highlight.png');\r\n  background-image: none, -webkit-gradient(linear, left top, left bottom, from(rgba(0,0,0,.01)), to(rgba(0,0,0,.06)));\r\n  background-image: none, -webkit-linear-gradient(top, rgba(0,0,0,.01) 0%, rgba(0,0,0,.06) 100%);\r\n  background-image: none, linear-gradient(to bottom, rgba(0,0,0,.01) 0%, rgba(0,0,0,.06) 100%);\r\n  background-position: 50% 50%;\r\n}\r\n.k-pivot-rowheaders .k-grid tr:hover {\r\n  background: none;\r\n}\r\n.k-grid tr.k-state-selected:hover,\r\n.k-grid td.k-state-selected:hover {\r\n  background-color: #efeded;\r\n  background-image: none;\r\n}\r\n.k-popup,\r\n.k-menu .k-menu-group,\r\n.k-grid .k-filter-options,\r\n.k-time-popup,\r\n.k-datepicker-calendar,\r\n.k-autocomplete.k-state-border-down,\r\n.k-autocomplete.k-state-border-up,\r\n.k-dropdown-wrap.k-state-active,\r\n.k-picker-wrap.k-state-active,\r\n.k-multiselect.k-state-focused,\r\n.k-filebrowser .k-image,\r\n.k-tooltip {\r\n  -webkit-box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.3);\r\n          box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.3);\r\n}\r\n.k-treemap-tile.k-state-hover {\r\n  -webkit-box-shadow: inset 0 0 0 3px #ebebeb;\r\n          box-shadow: inset 0 0 0 3px #ebebeb;\r\n}\r\n/* Window */\r\n.k-window {\r\n  border-color: rgba(0, 0, 0, 0.3);\r\n  -webkit-box-shadow: 1px 1px 7px 1px rgba(128, 128, 128, 0.3);\r\n          box-shadow: 1px 1px 7px 1px rgba(128, 128, 128, 0.3);\r\n  background-color: #fff;\r\n}\r\n.k-window.k-state-focused {\r\n  border-color: rgba(0, 0, 0, 0.3);\r\n  -webkit-box-shadow: 1px 1px 7px 1px rgba(0, 0, 0, 0.3);\r\n          box-shadow: 1px 1px 7px 1px rgba(0, 0, 0, 0.3);\r\n}\r\n.k-window.k-window-maximized,\r\n.k-window-maximized .k-window-titlebar,\r\n.k-window-maximized .k-window-content {\r\n  border-radius: 0;\r\n}\r\n.k-shadow {\r\n  -webkit-box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);\r\n          box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);\r\n}\r\n.k-inset {\r\n  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.3);\r\n          box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.3);\r\n}\r\n/* Selection */\r\n.k-editor-inline ::-moz-selection {\r\n  background-color: #cdcdcd;\r\n  text-shadow: none;\r\n  color: #fff;\r\n}\r\n.k-editor-inline ::selection {\r\n  background-color: #cdcdcd;\r\n  text-shadow: none;\r\n  color: #fff;\r\n}\r\n.k-editor-inline ::-moz-selection {\r\n  background-color: #cdcdcd;\r\n  text-shadow: none;\r\n  color: #fff;\r\n}\r\n/* Notification */\r\n.k-widget.k-notification.k-notification-info {\r\n  background-color: #e5f5fa;\r\n  color: #2498bc;\r\n  border-color: #b6e3f1;\r\n}\r\n.k-widget.k-notification.k-notification-success {\r\n  background-color: #eaf7ec;\r\n  color: #3ea44e;\r\n  border-color: #c5e9cb;\r\n}\r\n.k-widget.k-notification.k-notification-warning {\r\n  background-color: #ffe9a8;\r\n  color: #a87e00;\r\n  border-color: #ffe599;\r\n}\r\n.k-widget.k-notification.k-notification-error {\r\n  background-color: #ffe0d9;\r\n  color: #d92800;\r\n  border-color: #ffb6a6;\r\n}\r\n/* Gantt */\r\n.k-gantt .k-treelist {\r\n  background: #ffffff;\r\n}\r\n.k-gantt .k-treelist .k-alt {\r\n  background-color: #f2f2f2;\r\n}\r\n.k-gantt .k-treelist tr:hover {\r\n  background-image: url('textures/highlight.png');\r\n  background-image: none, -webkit-gradient(linear, left top, left bottom, from(rgba(0,0,0,.01)), to(rgba(0,0,0,.08)));\r\n  background-image: none, -webkit-linear-gradient(top, rgba(0,0,0,.01) 0%, rgba(0,0,0,.08) 100%);\r\n  background-image: none, linear-gradient(to bottom, rgba(0,0,0,.01) 0%, rgba(0,0,0,.08) 100%);\r\n  background-color: #cccccc;\r\n}\r\n.k-gantt .k-treelist .k-state-selected,\r\n.k-gantt .k-treelist .k-state-selected td,\r\n.k-gantt .k-treelist .k-alt.k-state-selected,\r\n.k-gantt .k-treelist .k-alt.k-state-selected > td {\r\n  background-color: #eee;\r\n  background-image: url('textures/highlight.png');\r\n  background-image: none, -webkit-gradient(linear, left top, left bottom, from(rgba(0,0,0,.13)), to(rgba(0,0,0,.08)));\r\n  background-image: none, -webkit-linear-gradient(top, rgba(0,0,0,.13) 0%, rgba(0,0,0,.08) 100%);\r\n  background-image: none, linear-gradient(to bottom, rgba(0,0,0,.13) 0%, rgba(0,0,0,.08) 100%);\r\n}\r\n.k-gantt .k-treelist .k-state-selected:hover,\r\n.k-gantt .k-treelist .k-state-selected:hover td {\r\n  background-color: #efeded;\r\n  background-image: none;\r\n}\r\n.k-task-dot:after {\r\n  background-color: #676767;\r\n  border-color: #676767;\r\n}\r\n.k-task-dot:hover:after {\r\n  background-color: #ffffff;\r\n}\r\n.k-task-summary {\r\n  border-color: #a7a7a7;\r\n  background: #a7a7a7;\r\n}\r\n.k-task-milestone,\r\n.k-task-summary-complete {\r\n  border-color: #676767;\r\n  background: #676767;\r\n}\r\n.k-state-selected.k-task-summary {\r\n  border-color: #ffffff;\r\n  background: #ffffff;\r\n}\r\n.k-state-selected.k-task-milestone,\r\n.k-state-selected .k-task-summary-complete {\r\n  border-color: #eee;\r\n  background: #eee;\r\n}\r\n.k-task-single {\r\n  background-color: #eeeeee;\r\n  border-color: #dcdcdc;\r\n  color: #646464;\r\n}\r\n.k-state-selected.k-task-single {\r\n  border-color: #cdcdcd;\r\n}\r\n.k-line {\r\n  background-color: #676767;\r\n  color: #676767;\r\n}\r\n.k-state-selected.k-line {\r\n  background-color: #eee;\r\n  color: #eee;\r\n}\r\n.k-resource {\r\n  background-color: #fff;\r\n}\r\n/* PivotGrid */\r\n.k-i-kpi-decrease,\r\n.k-i-kpi-denied,\r\n.k-i-kpi-equal,\r\n.k-i-kpi-hold,\r\n.k-i-kpi-increase,\r\n.k-i-kpi-open {\r\n  background-image: url('Uniform/sprite_kpi.png');\r\n}\r\n/* Border radius */\r\n.k-block,\r\n.k-button,\r\n.k-textbox,\r\n.k-drag-clue,\r\n.k-touch-scrollbar,\r\n.k-window,\r\n.k-window-titleless .k-window-content,\r\n.k-window-action,\r\n.k-inline-block,\r\n.k-grid .k-filter-options,\r\n.k-grouping-header .k-group-indicator,\r\n.k-autocomplete,\r\n.k-multiselect,\r\n.k-combobox,\r\n.k-dropdown,\r\n.k-dropdown-wrap,\r\n.k-datepicker,\r\n.k-timepicker,\r\n.k-colorpicker,\r\n.k-datetimepicker,\r\n.k-notification,\r\n.k-numerictextbox,\r\n.k-picker-wrap,\r\n.k-numeric-wrap,\r\n.k-colorpicker,\r\n.k-list-container,\r\n.k-calendar-container,\r\n.k-calendar td,\r\n.k-calendar .k-link,\r\n.k-treeview .k-in,\r\n.k-editor-inline,\r\n.k-tooltip,\r\n.k-tile,\r\n.k-slider-track,\r\n.k-slider-selection,\r\n.k-upload,\r\n.k-split-button .k-gantt-views,\r\n.k-gantt-views > .k-current-view {\r\n  border-radius: 6px;\r\n}\r\n.k-tool {\r\n  text-align: center;\r\n  vertical-align: middle;\r\n}\r\n.k-tool.k-group-start,\r\n.k-toolbar .k-split-button .k-button,\r\n.k-toolbar .k-button-group .k-group-start {\r\n  border-radius: 6px 0 0 6px;\r\n}\r\n.k-rtl .k-tool.k-group-start,\r\n.k-rtl .k-toolbar .k-split-button .k-button,\r\n.k-rtl .k-toolbar .k-button-group .k-group-start {\r\n  border-radius: 0 6px 6px 0;\r\n}\r\n.k-toolbar .k-button-group > .k-group-end {\r\n  border-radius: 6px;\r\n}\r\n.k-tool.k-group-end,\r\n.k-toolbar .k-button-group .k-button + .k-group-end,\r\n.k-toolbar .k-split-button .k-split-button-arrow {\r\n  border-radius: 0 6px 6px 0;\r\n}\r\n.k-rtl .k-tool.k-group-end,\r\n.k-rtl .k-toolbar .k-button-group .k-group-end,\r\n.k-rtl .k-toolbar .k-split-button .k-split-button-arrow {\r\n  border-radius: 6px 0 0 6px;\r\n}\r\n.k-group-start.k-group-end.k-tool {\r\n  border-radius: 6px;\r\n}\r\n.k-calendar-container.k-state-border-up,\r\n.k-list-container.k-state-border-up,\r\n.k-autocomplete.k-state-border-up,\r\n.k-multiselect.k-state-border-up,\r\n.k-dropdown-wrap.k-state-border-up,\r\n.k-picker-wrap.k-state-border-up,\r\n.k-numeric-wrap.k-state-border-up,\r\n.k-window-content,\r\n.k-filter-menu {\r\n  border-radius: 0 0 6px 6px;\r\n}\r\n.k-autocomplete.k-state-border-up .k-input,\r\n.k-dropdown-wrap.k-state-border-up .k-input,\r\n.k-picker-wrap.k-state-border-up .k-input,\r\n.k-picker-wrap.k-state-border-up .k-selected-color,\r\n.k-numeric-wrap.k-state-border-up .k-input {\r\n  border-radius: 0 0 0 6px;\r\n}\r\n.k-multiselect.k-state-border-up .k-multiselect-wrap {\r\n  border-radius: 0 0 6px 6px;\r\n}\r\n.k-window-titlebar,\r\n.k-block > .k-header,\r\n.k-tabstrip-items .k-item,\r\n.k-panelbar .k-tabstrip-items .k-item,\r\n.k-tabstrip-items .k-link,\r\n.k-calendar-container.k-state-border-down,\r\n.k-list-container.k-state-border-down,\r\n.k-autocomplete.k-state-border-down,\r\n.k-multiselect.k-state-border-down,\r\n.k-dropdown-wrap.k-state-border-down,\r\n.k-picker-wrap.k-state-border-down,\r\n.k-numeric-wrap.k-state-border-down,\r\n.k-gantt-views.k-state-expanded,\r\n.k-gantt-views.k-state-expanded > .k-current-view {\r\n  border-radius: 6px 6px 0 0;\r\n}\r\n.k-split-button.k-state-border-down > .k-button {\r\n  border-radius: 6px 0 0 0;\r\n}\r\n.k-split-button.k-state-border-up > .k-button {\r\n  border-radius: 0 0 0 6px;\r\n}\r\n.k-split-button.k-state-border-down > .k-split-button-arrow {\r\n  border-radius: 0 6px 0 0;\r\n}\r\n.k-split-button.k-state-border-up > .k-split-button-arrow {\r\n  border-radius: 0 0 6px 0;\r\n}\r\n.k-dropdown-wrap .k-input,\r\n.k-picker-wrap .k-input,\r\n.k-numeric-wrap .k-input {\r\n  border-radius: 5px 0 0 5px;\r\n}\r\n.k-rtl .k-dropdown-wrap .k-input,\r\n.k-rtl .k-picker-wrap .k-input,\r\n.k-rtl .k-numeric-wrap .k-input {\r\n  border-radius: 0 5px 5px 0;\r\n}\r\n.k-numeric-wrap .k-link {\r\n  border-radius: 0 5px 0 0;\r\n}\r\n.k-numeric-wrap .k-link + .k-link {\r\n  border-radius: 0 0 5px 0;\r\n}\r\n.k-colorpicker .k-selected-color {\r\n  border-radius: 5px 0 0 5px;\r\n}\r\n.k-rtl .k-colorpicker .k-selected-color {\r\n  border-radius: 0 5px 5px 0;\r\n}\r\n.k-autocomplete.k-state-border-down .k-input {\r\n  border-radius: 6px 6px 0 0;\r\n}\r\n.k-dropdown-wrap.k-state-border-down .k-input,\r\n.k-picker-wrap.k-state-border-down .k-input,\r\n.k-picker-wrap.k-state-border-down .k-selected-color,\r\n.k-numeric-wrap.k-state-border-down .k-input {\r\n  border-radius: 6px 0 0 0;\r\n}\r\n.k-numeric-wrap .k-link.k-state-selected {\r\n  background-color: #eee;\r\n}\r\n.k-multiselect.k-state-border-down .k-multiselect-wrap {\r\n  border-radius: 5px 5px 0 0;\r\n}\r\n.k-dropdown-wrap .k-select,\r\n.k-picker-wrap .k-select,\r\n.k-numeric-wrap .k-select,\r\n.k-datetimepicker .k-select + .k-select,\r\n.k-list-container.k-state-border-right {\r\n  border-radius: 0 6px 6px 0;\r\n}\r\n.k-rtl .k-dropdown-wrap .k-select,\r\n.k-rtl .k-picker-wrap .k-select,\r\n.k-rtl .k-numeric-wrap .k-select,\r\n.k-rtl .k-datetimepicker .k-select + .k-select,\r\n.k-rtl .k-list-container.k-state-border-right {\r\n  border-radius: 6px 0 0 6px;\r\n}\r\n.k-numeric-wrap.k-expand-padding .k-input {\r\n  border-radius: 6px;\r\n}\r\n.k-textbox > input,\r\n.k-autocomplete .k-input,\r\n.k-multiselect-wrap {\r\n  border-radius: 5px;\r\n}\r\n.k-list .k-state-hover,\r\n.k-list .k-state-focused,\r\n.k-list .k-state-highlight,\r\n.k-list .k-state-selected,\r\n.k-fieldselector .k-list .k-item,\r\n.k-list-optionlabel,\r\n.k-dropzone {\r\n  border-radius: 5px;\r\n}\r\n.k-slider .k-button,\r\n.k-grid .k-slider .k-button {\r\n  border-radius: 13px;\r\n}\r\n.k-draghandle {\r\n  border-radius: 7px;\r\n}\r\n.k-scheduler-toolbar > ul li:first-child,\r\n.k-scheduler-toolbar > ul li:first-child .k-link,\r\n.k-scheduler-toolbar > ul.k-scheduler-views li:first-child + li,\r\n.k-scheduler-toolbar > ul.k-scheduler-views li:first-child + li .k-link {\r\n  border-radius: 6px 0 0 6px;\r\n}\r\n.k-rtl .k-scheduler-toolbar > ul li:first-child,\r\n.k-rtl .k-scheduler-toolbar > ul li:first-child .k-link,\r\n.k-rtl .k-scheduler-toolbar > ul.k-scheduler-views li:first-child + li,\r\n.k-rtl .k-scheduler-toolbar > ul.k-scheduler-views li:first-child + li .k-link,\r\n.km-view.k-popup-edit-form .k-scheduler-toolbar > ul li:last-child,\r\n.km-view.k-popup-edit-form .k-scheduler-toolbar > ul li:last-child .k-link {\r\n  border-radius: 0 6px 6px 0;\r\n}\r\n.k-scheduler-phone .k-scheduler-toolbar > ul li.k-nav-today,\r\n.k-scheduler-phone .k-scheduler-toolbar > ul li.k-nav-today .k-link,\r\n.k-edit-field > .k-scheduler-navigation {\r\n  border-radius: 6px;\r\n}\r\n.k-scheduler-toolbar .k-nav-next,\r\n.k-scheduler-toolbar ul + ul li:last-child,\r\n.k-scheduler-toolbar .k-nav-next .k-link,\r\n.k-scheduler-toolbar ul + ul li:last-child .k-link {\r\n  border-top-right-radius: 6px;\r\n  border-bottom-right-radius: 6px;\r\n}\r\n.k-rtl .k-scheduler-toolbar .k-nav-next,\r\n.k-rtl .k-scheduler-toolbar ul + ul li:last-child,\r\n.k-rtl .k-scheduler-toolbar .k-nav-next .k-link,\r\n.k-rtl .k-scheduler-toolbar ul + ul li:last-child .k-link {\r\n  border-radius: 6px 0 0 6px;\r\n}\r\n.k-scheduler div.k-scheduler-footer ul li,\r\n.k-scheduler div.k-scheduler-footer .k-link {\r\n  border-radius: 6px;\r\n}\r\n.k-more-events,\r\n.k-event,\r\n.k-task-single,\r\n.k-task-complete,\r\n.k-event .k-link {\r\n  border-radius: 5px;\r\n}\r\n.k-scheduler-mobile .k-event {\r\n  border-radius: 4px;\r\n}\r\n/* Adaptive Grid */\r\n.k-grid-mobile .k-column-active + th.k-header {\r\n  border-left-color: #676767;\r\n}\r\nhtml .km-pane-wrapper .km-widget,\r\n.k-ie .km-pane-wrapper .k-widget,\r\n.k-ie .km-pane-wrapper .k-group,\r\n.k-ie .km-pane-wrapper .k-content,\r\n.k-ie .km-pane-wrapper .k-header,\r\n.k-ie .km-pane-wrapper .k-popup-edit-form .k-edit-field .k-button,\r\n.km-pane-wrapper .k-mobile-list .k-item,\r\n.km-pane-wrapper .k-mobile-list .k-edit-label,\r\n.km-pane-wrapper .k-mobile-list .k-edit-field {\r\n  color: #676767;\r\n}\r\n@media screen and (-ms-high-contrast: active) and (-ms-high-contrast: none) {\r\n  div.km-pane-wrapper a {\r\n    color: #676767;\r\n  }\r\n}\r\n.km-pane-wrapper .k-mobile-list .k-item,\r\n.km-pane-wrapper .k-mobile-list .k-edit-field,\r\n.km-pane-wrapper .k-mobile-list .k-recur-view > .k-edit-field .k-check {\r\n  background-color: #fff;\r\n  border-top: 1px solid #dbdbdb;\r\n}\r\n.km-pane-wrapper .k-mobile-list .k-edit-field textarea {\r\n  outline-width: 0;\r\n}\r\n.km-pane-wrapper .k-mobile-list .k-item.k-state-selected {\r\n  background-color: #eee;\r\n  border-top-color: #cdcdcd;\r\n}\r\n.km-pane-wrapper .k-mobile-list .k-recur-view > .k-edit-field .k-check:first-child {\r\n  border-top-color: transparent;\r\n}\r\n.km-pane-wrapper .k-mobile-list .k-item:last-child {\r\n  -webkit-box-shadow: inset 0 -1px 0 #dbdbdb;\r\n          box-shadow: inset 0 -1px 0 #dbdbdb;\r\n}\r\n.km-pane-wrapper .k-mobile-list > ul > li > .k-link,\r\n.km-pane-wrapper .k-mobile-list .k-recur-view > .k-edit-label:nth-child(3),\r\n.km-pane-wrapper #recurrence .km-scroll-container > .k-edit-label:first-child {\r\n  color: #8f8f8f;\r\n}\r\n.km-pane-wrapper .k-mobile-list > ul > li > .k-link {\r\n  border-bottom: 1px solid #dbdbdb;\r\n}\r\n.km-pane-wrapper .k-mobile-list .k-edit-field {\r\n  -webkit-box-shadow: 0 1px 1px #dbdbdb;\r\n          box-shadow: 0 1px 1px #dbdbdb;\r\n}\r\n.km-actionsheet .k-grid-delete,\r\n.km-actionsheet .k-scheduler-delete,\r\n.km-pane-wrapper .k-scheduler-delete,\r\n.km-pane-wrapper .k-filter-menu .k-button[type=reset] {\r\n  color: #fff;\r\n  border-color: #ff6745;\r\n  background-color: red;\r\n  background-image: -webkit-gradient(linear, left top, left bottom, from(rgba(255, 255, 255, 0.3)), to(rgba(255, 255, 255, 0.15)));\r\n  background-image: -webkit-linear-gradient(top, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.15));\r\n  background-image: linear-gradient(to bottom, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.15));\r\n}\r\n.km-actionsheet .k-grid-delete:active,\r\n.km-actionsheet .k-scheduler-delete:active,\r\n.km-pane-wrapper .k-scheduler-delete:active,\r\n.km-pane-wrapper .k-filter-menu .k-button[type=reset]:active {\r\n  background-color: #990000;\r\n}\r\n/* /Column Menu */\r\n.k-autocomplete.k-state-default,\r\n.k-picker-wrap.k-state-default,\r\n.k-numeric-wrap.k-state-default,\r\n.k-dropdown-wrap.k-state-default {\r\n  background-image: url('textures/highlight.png');\r\n  background-image: none, -webkit-gradient(linear, left top, left bottom, from(rgba(0,0,0,.01)), to(rgba(0,0,0,.08)));\r\n  background-image: none, -webkit-linear-gradient(top, rgba(0,0,0,.01) 0%, rgba(0,0,0,.08) 100%);\r\n  background-image: none, linear-gradient(to bottom, rgba(0,0,0,.01) 0%, rgba(0,0,0,.08) 100%);\r\n  background-position: 50% 50%;\r\n  background-color: #e8e8e8;\r\n  border-color: #dbdbdb;\r\n}\r\n.k-autocomplete.k-state-hover,\r\n.k-picker-wrap.k-state-hover,\r\n.k-numeric-wrap.k-state-hover,\r\n.k-dropdown-wrap.k-state-hover {\r\n  background-color: #ffffff;\r\n  background-image: url('textures/highlight.png');\r\n  background-image: none, -webkit-gradient(linear, left top, left bottom, from(rgba(0,0,0,.01)), to(rgba(0,0,0,.06)));\r\n  background-image: none, -webkit-linear-gradient(top, rgba(0,0,0,.01) 0%, rgba(0,0,0,.06) 100%);\r\n  background-image: none, linear-gradient(to bottom, rgba(0,0,0,.01) 0%, rgba(0,0,0,.06) 100%);\r\n  background-position: 50% 50%;\r\n  border-color: #cccccc;\r\n}\r\ninput.k-textbox,\r\ntextarea.k-textbox,\r\n.k-multiselect.k-header {\r\n  border-color: #dbdbdb;\r\n}\r\n.k-multiselect.k-header.k-state-hover {\r\n  border-color: #cccccc;\r\n}\r\n.k-autocomplete.k-state-focused,\r\n.k-picker-wrap.k-state-focused,\r\n.k-numeric-wrap.k-state-focused,\r\n.k-dropdown-wrap.k-state-focused,\r\n.k-multiselect.k-header.k-state-focused {\r\n  background-color: #ffffff;\r\n  background-image: url('textures/highlight.png');\r\n  background-image: none, -webkit-gradient(linear, left top, left bottom, from(rgba(0,0,0,.01)), to(rgba(0,0,0,.06)));\r\n  background-image: none, -webkit-linear-gradient(top, rgba(0,0,0,.01) 0%, rgba(0,0,0,.06) 100%);\r\n  background-image: none, linear-gradient(to bottom, rgba(0,0,0,.01) 0%, rgba(0,0,0,.06) 100%);\r\n  background-position: 50% 50%;\r\n  border-color: #bdbdbd;\r\n  -webkit-box-shadow: 0 0 3px 0 rgba(0, 0, 0, 0.3);\r\n          box-shadow: 0 0 3px 0 rgba(0, 0, 0, 0.3);\r\n}\r\n.k-list-container {\r\n  color: #676767;\r\n}\r\n.k-dropdown .k-input,\r\n.k-dropdown .k-state-focused .k-input,\r\n.k-menu .k-popup {\r\n  color: #676767;\r\n}\r\n.k-state-default > .k-select {\r\n  border-color: #dbdbdb;\r\n}\r\n.k-state-hover > .k-select {\r\n  border-color: #cccccc;\r\n}\r\n.k-state-focused > .k-select {\r\n  border-color: #bdbdbd;\r\n}\r\n.k-tabstrip:focus {\r\n  -webkit-box-shadow: 0 0 3px 0 rgba(0, 0, 0, 0.3);\r\n          box-shadow: 0 0 3px 0 rgba(0, 0, 0, 0.3);\r\n}\r\n.k-tabstrip-items .k-state-default .k-link,\r\n.k-panelbar > li.k-state-default > .k-link {\r\n  color: #676767;\r\n}\r\n.k-tabstrip-items .k-state-hover .k-link,\r\n.k-panelbar > li.k-state-hover > .k-link,\r\n.k-panelbar > li.k-state-default > .k-link.k-state-hover {\r\n  color: #676767;\r\n}\r\n.k-panelbar > .k-state-focused.k-state-hover {\r\n  background: #fff;\r\n  -webkit-box-shadow: none;\r\n          box-shadow: none;\r\n}\r\n.k-tabstrip-items .k-state-default {\r\n  border-color: #dbdbdb;\r\n}\r\n.k-tabstrip-items .k-state-hover {\r\n  border-color: #cccccc;\r\n}\r\n.k-tabstrip-items .k-state-active,\r\n.k-panelbar .k-tabstrip-items .k-state-active {\r\n  background-color: #ffffff;\r\n  background-image: none;\r\n  border-color: #b5b5b5;\r\n}\r\n.k-tabstrip .k-content.k-state-active {\r\n  background-color: #ffffff;\r\n  color: #676767;\r\n}\r\n.k-menu.k-header,\r\n.k-menu .k-item {\r\n  border-color: #dbdbdb;\r\n}\r\n.k-column-menu,\r\n.k-column-menu .k-item,\r\n.k-overflow-container .k-overflow-group {\r\n  border-color: #dbdbdb;\r\n}\r\n.k-overflow-container .k-overflow-group {\r\n  -webkit-box-shadow: inset 0 1px 0 #ffffff, 0 1px 0 #ffffff;\r\n          box-shadow: inset 0 1px 0 #ffffff, 0 1px 0 #ffffff;\r\n}\r\n.k-toolbar-first-visible.k-overflow-group,\r\n.k-overflow-container .k-overflow-group + .k-overflow-group {\r\n  -webkit-box-shadow: 0 1px 0 #ffffff;\r\n          box-shadow: 0 1px 0 #ffffff;\r\n}\r\n.k-toolbar-last-visible.k-overflow-group {\r\n  -webkit-box-shadow: inset 0 1px 0 #ffffff;\r\n          box-shadow: inset 0 1px 0 #ffffff;\r\n}\r\n.k-column-menu .k-separator {\r\n  border-color: #dbdbdb;\r\n  background-color: transparent;\r\n}\r\n.k-menu .k-group {\r\n  border-color: #dbdbdb;\r\n}\r\n.k-grid-filter.k-state-active {\r\n  background-color: #ffffff;\r\n}\r\n.k-grouping-row td,\r\n.k-group-footer td,\r\n.k-grid-footer td {\r\n  color: #676767;\r\n  border-color: #dbdbdb;\r\n  font-weight: bold;\r\n}\r\n.k-grouping-header {\r\n  color: #676767;\r\n}\r\n.k-grid td.k-state-focused {\r\n  -webkit-box-shadow: inset 0 0 3px 1px #b3b3b3;\r\n          box-shadow: inset 0 0 3px 1px #b3b3b3;\r\n}\r\n.k-header,\r\n.k-grid-header-wrap,\r\n.k-grid .k-grouping-header,\r\n.k-grid-header,\r\n.k-pager-wrap,\r\n.k-pager-wrap .k-textbox,\r\n.k-pager-wrap .k-link,\r\n.k-grouping-header .k-group-indicator,\r\n.k-gantt-toolbar .k-state-default {\r\n  border-color: #dbdbdb;\r\n}\r\n.k-primary,\r\n.k-overflow-container .k-primary {\r\n  color: #646464;\r\n  border-color: #e6e6e6;\r\n  background-image: url('textures/highlight.png');\r\n  background-image: none, -webkit-gradient(linear, left top, left bottom, from(rgba(0,0,0,.13)), to(rgba(0,0,0,.08)));\r\n  background-image: none, -webkit-linear-gradient(top, rgba(0,0,0,.13) 0%, rgba(0,0,0,.08) 100%);\r\n  background-image: none, linear-gradient(to bottom, rgba(0,0,0,.13) 0%, rgba(0,0,0,.08) 100%);\r\n  background-position: 50% 50%;\r\n  background-color: #ffffff;\r\n  -webkit-box-shadow: none;\r\n          box-shadow: none;\r\n}\r\n.k-primary:focus,\r\n.k-primary.k-state-focused {\r\n  color: #646464;\r\n  border-color: #e6e6e6;\r\n  background-image: url('textures/highlight.png');\r\n  background-image: none, -webkit-gradient(linear, left top, left bottom, from(rgba(0,0,0,.01)), to(rgba(0,0,0,.06)));\r\n  background-image: none, -webkit-linear-gradient(top, rgba(0,0,0,.01) 0%, rgba(0,0,0,.06) 100%);\r\n  background-image: none, linear-gradient(to bottom, rgba(0,0,0,.01) 0%, rgba(0,0,0,.06) 100%);\r\n  -webkit-box-shadow: 0 0 3px 0 #eee;\r\n          box-shadow: 0 0 3px 0 #eee;\r\n}\r\n.k-primary:hover {\r\n  color: #646464;\r\n  border-color: #cccccc;\r\n  background-image: url('textures/highlight.png');\r\n  background-image: none, -webkit-gradient(linear, left top, left bottom, from(rgba(0,0,0,.01)), to(rgba(0,0,0,.06)));\r\n  background-image: none, -webkit-linear-gradient(top, rgba(0,0,0,.01) 0%, rgba(0,0,0,.06) 100%);\r\n  background-image: none, linear-gradient(to bottom, rgba(0,0,0,.01) 0%, rgba(0,0,0,.06) 100%);\r\n  background-color: #f6f6f6;\r\n  -webkit-box-shadow: none;\r\n          box-shadow: none;\r\n}\r\n.k-primary:focus:active:not(.k-state-disabled):not([disabled]),\r\n.k-primary:focus:not(.k-state-disabled):not([disabled]) {\r\n  -webkit-box-shadow: 0 0 3px 0 #eee;\r\n          box-shadow: 0 0 3px 0 #eee;\r\n}\r\n.k-primary:active {\r\n  color: #454545;\r\n  border-color: #cdcdcd;\r\n  background-image: url('textures/highlight.png');\r\n  background-image: none, -webkit-gradient(linear, left top, left bottom, from(rgba(0,0,0,.13)), to(rgba(0,0,0,.08)));\r\n  background-image: none, -webkit-linear-gradient(top, rgba(0,0,0,.13) 0%, rgba(0,0,0,.08) 100%);\r\n  background-image: none, linear-gradient(to bottom, rgba(0,0,0,.13) 0%, rgba(0,0,0,.08) 100%);\r\n  background-color: #eeeeee;\r\n  -webkit-box-shadow: none;\r\n          box-shadow: none;\r\n}\r\n.k-primary.k-state-disabled,\r\n.k-state-disabled .k-primary,\r\n.k-primary.k-state-disabled:hover,\r\n.k-state-disabled .k-primary:hover,\r\n.k-primary.k-state-disabled:hover,\r\n.k-state-disabled .k-primary:active,\r\n.k-primary.k-state-disabled:active {\r\n  color: #2b2b2b;\r\n  border-color: #2b2b2b;\r\n  background-color: #ffffff;\r\n  background-image: url('textures/highlight.png');\r\n  background-image: none, -webkit-gradient(linear, left top, left bottom, from(rgba(0,0,0,.13)), to(rgba(0,0,0,.08)));\r\n  background-image: none, -webkit-linear-gradient(top, rgba(0,0,0,.13) 0%, rgba(0,0,0,.08) 100%);\r\n  background-image: none, linear-gradient(to bottom, rgba(0,0,0,.13) 0%, rgba(0,0,0,.08) 100%);\r\n  -webkit-box-shadow: none;\r\n          box-shadow: none;\r\n}\r\n.k-pager-numbers .k-link,\r\n.k-treeview .k-in {\r\n  border-color: transparent;\r\n}\r\n.k-treeview .k-icon,\r\n.k-scheduler-table .k-icon,\r\n.k-grid .k-hierarchy-cell .k-icon {\r\n  background-color: transparent;\r\n  border-radius: 4px;\r\n}\r\n.k-scheduler-table .k-state-hover .k-icon {\r\n  background-color: transparent;\r\n}\r\n.k-button:focus,\r\n.k-split-button:focus {\r\n  outline: none;\r\n}\r\n.k-split-button:focus {\r\n  -webkit-box-shadow: inset 0 0 4px 2px #cccccc;\r\n          box-shadow: inset 0 0 4px 2px #cccccc;\r\n}\r\n.k-split-button:focus > .k-button {\r\n  background: transparent;\r\n  border-color: #dbdbdb;\r\n}\r\n.k-editor .k-tool:focus {\r\n  outline: 0;\r\n  border-color: #dbdbdb;\r\n  -webkit-box-shadow: inset 0 0 3px 1px #cccccc;\r\n          box-shadow: inset 0 0 3px 1px #cccccc;\r\n}\r\n.k-checkbox-label:before {\r\n  border-color: #dbdbdb;\r\n  background: #fff;\r\n  border-radius: 3px;\r\n}\r\n.k-checkbox-label:hover:before,\r\n.k-checkbox:checked + .k-checkbox-label:hover:before {\r\n  border-color: #c2c2c2;\r\n  -webkit-box-shadow: none;\r\n          box-shadow: none;\r\n}\r\n.k-checkbox:checked + .k-checkbox-label:before {\r\n  background-color: #ffffff;\r\n  border-color: #a0dba9;\r\n  color: #a4a4a4;\r\n}\r\n.k-checkbox-label:active:before {\r\n  -webkit-box-shadow: 0 0 3px 0 #eee;\r\n          box-shadow: 0 0 3px 0 #eee;\r\n  border-color: #a4a4a4;\r\n}\r\n.k-checkbox:checked + .k-checkbox-label:active:before {\r\n  -webkit-box-shadow: 0 0 3px 0 #eee;\r\n          box-shadow: 0 0 3px 0 #eee;\r\n  border-color: #a4a4a4;\r\n}\r\n.k-checkbox:disabled + .k-checkbox-label {\r\n  color: #b5b5b5;\r\n}\r\n.k-checkbox:disabled + .k-checkbox-label:hover:before {\r\n  -webkit-box-shadow: none;\r\n          box-shadow: none;\r\n}\r\n.k-checkbox:disabled + .k-checkbox-label:before,\r\n.k-checkbox:checked:disabled + .k-checkbox-label:before,\r\n.k-checkbox:checked:disabled + .k-checkbox-label:active:before,\r\n.k-checkbox:checked:disabled + .k-checkbox-label:hover:before {\r\n  color: #b5b5b5;\r\n  background: #ffffff;\r\n  border-color: #9cd9a6;\r\n  border-radius: 3px;\r\n}\r\n.k-checkbox:focus + .k-checkbox-label:before {\r\n  border-color: #a4a4a4;\r\n  -webkit-box-shadow: 0 0 3px 0 #eee;\r\n          box-shadow: 0 0 3px 0 #eee;\r\n}\r\n.k-checkbox:indeterminate + .k-checkbox-label:after {\r\n  background-color: #a4a4a4;\r\n  background-image: url('textures/highlight.png');\r\n  background-image: none, -webkit-gradient(linear, left top, left bottom, from(rgba(0,0,0,.13)), to(rgba(0,0,0,.08)));\r\n  background-image: none, -webkit-linear-gradient(top, rgba(0,0,0,.13) 0%, rgba(0,0,0,.08) 100%);\r\n  background-image: none, linear-gradient(to bottom, rgba(0,0,0,.13) 0%, rgba(0,0,0,.08) 100%);\r\n  border-color: #a4a4a4;\r\n  border-radius: 2px;\r\n}\r\n.k-checkbox:indeterminate:hover + .k-checkbox-label:after {\r\n  border-color: #a4a4a4;\r\n  background-color: #a4a4a4;\r\n}\r\n.k-radio-label:before {\r\n  border-color: #dbdbdb;\r\n  border-radius: 50%;\r\n  background-color: #fff;\r\n  border-width: 1px;\r\n}\r\n.k-radio-label:hover:before,\r\n.k-radio:checked + .k-radio-label:hover:before {\r\n  border-color: #c2c2c2;\r\n  -webkit-box-shadow: none;\r\n          box-shadow: none;\r\n}\r\n.k-radio:checked + .k-radio-label:after {\r\n  background-color: #a4a4a4;\r\n  border-radius: 50%;\r\n}\r\n.k-radio-label:active:before {\r\n  border-color: #a4a4a4;\r\n  -webkit-box-shadow: 0 0 3px 0 #eee;\r\n          box-shadow: 0 0 3px 0 #eee;\r\n}\r\n.k-radio:checked + .k-radio-label:active:before {\r\n  -webkit-box-shadow: 0 0 3px 0 #eee;\r\n          box-shadow: 0 0 3px 0 #eee;\r\n  border-color: #a4a4a4;\r\n}\r\n.k-radio:disabled + .k-radio-label {\r\n  color: #b5b5b5;\r\n}\r\n.k-radio:disabled + .k-radio-label:before,\r\n.k-radio:disabled + .k-radio-label:active:before,\r\n.k-radio:disabled + .k-radio-label:hover:after,\r\n.k-radio:disabled + .k-radio-label:hover:before {\r\n  background: #ffffff;\r\n  border-color: #bfbfbf;\r\n  -webkit-box-shadow: none;\r\n          box-shadow: none;\r\n}\r\n.k-radio:disabled:checked + .k-radio-label:after {\r\n  background-color: #a4a4a4;\r\n  opacity: .5;\r\n}\r\n.k-radio:focus + .k-radio-label:before {\r\n  border-color: #a4a4a4;\r\n  -webkit-box-shadow: 0 0 3px 0 #eee;\r\n          box-shadow: 0 0 3px 0 #eee;\r\n}\r\n@media only screen and (-webkit-min-device-pixel-ratio: 1.2), only screen and (min-device-pixel-ratio: 1.2) {\r\n  .k-icon:not(.k-loading),\r\n  .k-grouping-dropclue,\r\n  .k-drop-hint,\r\n  .k-callout,\r\n  .k-tool-icon,\r\n  .k-state-hover .k-tool-icon,\r\n  .k-state-active .k-tool-icon,\r\n  .k-state-active.k-state-hover .k-tool-icon,\r\n  .k-state-selected .k-tool-icon,\r\n  .k-state-selected.k-state-hover .k-tool-icon,\r\n  .k-column-menu .k-sprite,\r\n  .k-mobile-list .k-check:checked,\r\n  .k-mobile-list .k-edit-field [type=checkbox]:checked,\r\n  .k-mobile-list .k-edit-field [type=radio]:checked {\r\n    background-image: url('Uniform/sprite_2x.png');\r\n    -webkit-background-size: 340px 336px;\r\n            background-size: 340px 336px;\r\n  }\r\n  .k-dropdown-wrap .k-input,\r\n  .k-picker-wrap .k-input,\r\n  .k-numeric-wrap .k-input {\r\n    border-radius: 5px 0 0 5px;\r\n  }\r\n  .k-i-kpi-decrease,\r\n  .k-i-kpi-denied,\r\n  .k-i-kpi-equal,\r\n  .k-i-kpi-hold,\r\n  .k-i-kpi-increase,\r\n  .k-i-kpi-open {\r\n    background-image: url('Uniform/sprite_kpi_2x.png');\r\n    -webkit-background-size: 96px 16px;\r\n            background-size: 96px 16px;\r\n  }\r\n}\r\n@media screen and (-ms-high-contrast: active) {\r\n  .k-editor-toolbar-wrap .k-dropdown-wrap.k-state-focused,\r\n  .k-editor-toolbar-wrap .k-button-group .k-tool:focus {\r\n    border-color: #fff;\r\n  }\r\n}\r\n/* Responsive styles */\r\n@media only screen and (max-width: 1024px) {\r\n  .k-webkit .k-pager-numbers,\r\n  .k-ff .k-pager-numbers,\r\n  .k-ie11 .k-pager-numbers,\r\n  .k-safari .k-pager-numbers,\r\n  .k-webkit .k-grid .k-pager-numbers,\r\n  .k-ff .k-grid .k-pager-numbers,\r\n  .k-ie11 .k-grid .k-pager-numbers,\r\n  .k-safari .k-grid .k-pager-numbers {\r\n    -ms-transform: translatey(-100%);\r\n        transform: translatey(-100%);\r\n    -webkit-transform: translatey(-100%);\r\n  }\r\n  .k-webkit .k-pager-numbers .k-current-page,\r\n  .k-ff .k-pager-numbers .k-current-page,\r\n  .k-ie11 .k-pager-numbers .k-current-page,\r\n  .k-safari .k-pager-numbers .k-current-page,\r\n  .k-webkit .k-grid .k-pager-numbers .k-current-page,\r\n  .k-ff .k-grid .k-pager-numbers .k-current-page,\r\n  .k-ie11 .k-grid .k-pager-numbers .k-current-page,\r\n  .k-safari .k-grid .k-pager-numbers .k-current-page {\r\n    -ms-transform: translatey(100%);\r\n        transform: translatey(100%);\r\n    -webkit-transform: translatey(100%);\r\n  }\r\n  .k-webkit .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view,\r\n  .k-ff .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view,\r\n  .k-ie11 .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view,\r\n  .k-safari .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view,\r\n  .k-webkit .k-pager-numbers .k-current-page .k-link,\r\n  .k-ff .k-pager-numbers .k-current-page .k-link,\r\n  .k-ie11 .k-pager-numbers .k-current-page .k-link,\r\n  .k-safari .k-pager-numbers .k-current-page .k-link {\r\n    background-image: url('textures/highlight.png');\r\n    background-image: none, -webkit-gradient(linear, left top, left bottom, from(rgba(0,0,0,.01)), to(rgba(0,0,0,.08)));\r\n    background-image: none, -webkit-linear-gradient(top, rgba(0,0,0,.01) 0%, rgba(0,0,0,.08) 100%);\r\n    background-image: none, linear-gradient(to bottom, rgba(0,0,0,.01) 0%, rgba(0,0,0,.08) 100%);\r\n    background-position: 50% 50%;\r\n    background-color: #e8e8e8;\r\n    border-color: #ebebeb;\r\n  }\r\n  .k-webkit .k-pager-numbers .k-current-page .k-link,\r\n  .k-ff .k-pager-numbers .k-current-page .k-link,\r\n  .k-ie11 .k-pager-numbers .k-current-page .k-link,\r\n  .k-safari .k-pager-numbers .k-current-page .k-link {\r\n    border-color: #dbdbdb;\r\n  }\r\n  .k-webkit .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view,\r\n  .k-ff .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view,\r\n  .k-ie11 .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view,\r\n  .k-safari .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view {\r\n    border-radius: 6px;\r\n  }\r\n  .k-webkit .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded > li,\r\n  .k-ff .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded > li,\r\n  .k-ie11 .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded > li,\r\n  .k-safari .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded > li {\r\n    border-radius: 0;\r\n  }\r\n  .k-webkit .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded > li.k-current-view,\r\n  .k-ff .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded > li.k-current-view,\r\n  .k-ie11 .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded > li.k-current-view,\r\n  .k-safari .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded > li.k-current-view {\r\n    border-radius: 5px 5px 0 0;\r\n  }\r\n  .k-webkit .k-scheduler-toolbar > ul li:first-child,\r\n  .k-ff .k-scheduler-toolbar > ul li:first-child,\r\n  .k-ie11 .k-scheduler-toolbar > ul li:first-child,\r\n  .k-safari .k-scheduler-toolbar > ul li:first-child,\r\n  .k-webkit .k-scheduler-toolbar > ul li:first-child .k-link,\r\n  .k-ff .k-scheduler-toolbar > ul li:first-child .k-link,\r\n  .k-ie11 .k-scheduler-toolbar > ul li:first-child .k-link,\r\n  .k-safari .k-scheduler-toolbar > ul li:first-child .k-link,\r\n  .k-webkit .k-scheduler-toolbar > ul.k-scheduler-views li,\r\n  .k-ff .k-scheduler-toolbar > ul.k-scheduler-views li,\r\n  .k-ie11 .k-scheduler-toolbar > ul.k-scheduler-views li,\r\n  .k-safari .k-scheduler-toolbar > ul.k-scheduler-views li,\r\n  .k-webkit .k-scheduler-toolbar > ul.k-scheduler-views li .k-link,\r\n  .k-ff .k-scheduler-toolbar > ul.k-scheduler-views li .k-link,\r\n  .k-ie11 .k-scheduler-toolbar > ul.k-scheduler-views li .k-link,\r\n  .k-safari .k-scheduler-toolbar > ul.k-scheduler-views li .k-link {\r\n    border-radius: 0;\r\n  }\r\n  .k-webkit .k-scheduler-toolbar > ul.k-scheduler-views li:last-child,\r\n  .k-ff .k-scheduler-toolbar > ul.k-scheduler-views li:last-child,\r\n  .k-ie11 .k-scheduler-toolbar > ul.k-scheduler-views li:last-child,\r\n  .k-safari .k-scheduler-toolbar > ul.k-scheduler-views li:last-child,\r\n  .k-webkit .k-scheduler-toolbar > ul.k-scheduler-views li:last-child .k-link,\r\n  .k-ff .k-scheduler-toolbar > ul.k-scheduler-views li:last-child .k-link,\r\n  .k-ie11 .k-scheduler-toolbar > ul.k-scheduler-views li:last-child .k-link,\r\n  .k-safari .k-scheduler-toolbar > ul.k-scheduler-views li:last-child .k-link {\r\n    border-radius: 0 0 5px 5px;\r\n  }\r\n  .k-webkit .k-pager-numbers .k-current-page .k-link:hover,\r\n  .k-ff .k-pager-numbers .k-current-page .k-link:hover,\r\n  .k-ie11 .k-pager-numbers .k-current-page .k-link:hover,\r\n  .k-safari .k-pager-numbers .k-current-page .k-link:hover,\r\n  .k-webkit .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view:hover,\r\n  .k-ff .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view:hover,\r\n  .k-ie11 .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view:hover,\r\n  .k-safari .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view:hover {\r\n    border-color: #cccccc;\r\n    background-image: url('textures/highlight.png');\r\n    background-image: none, -webkit-gradient(linear, left top, left bottom, from(rgba(0,0,0,.01)), to(rgba(0,0,0,.06)));\r\n    background-image: none, -webkit-linear-gradient(top, rgba(0,0,0,.01) 0%, rgba(0,0,0,.06) 100%);\r\n    background-image: none, linear-gradient(to bottom, rgba(0,0,0,.01) 0%, rgba(0,0,0,.06) 100%);\r\n    background-color: #fff;\r\n  }\r\n  .k-webkit .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view > .k-link,\r\n  .k-ff .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view > .k-link,\r\n  .k-ie11 .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view > .k-link,\r\n  .k-safari .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view > .k-link {\r\n    color: #676767;\r\n    min-width: 75px;\r\n  }\r\n  .k-webkit .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view:hover > .k-link,\r\n  .k-ff .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view:hover > .k-link,\r\n  .k-ie11 .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view:hover > .k-link,\r\n  .k-safari .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view:hover > .k-link {\r\n    color: #676767;\r\n  }\r\n  .k-webkit .k-pager-numbers .k-current-page .k-link:after,\r\n  .k-ff .k-pager-numbers .k-current-page .k-link:after,\r\n  .k-ie11 .k-pager-numbers .k-current-page .k-link:after,\r\n  .k-safari .k-pager-numbers .k-current-page .k-link:after,\r\n  .k-webkit .k-scheduler-views > li.k-state-selected > .k-link:after,\r\n  .k-ff .k-scheduler-views > li.k-state-selected > .k-link:after,\r\n  .k-ie11 .k-scheduler-views > li.k-state-selected > .k-link:after,\r\n  .k-safari .k-scheduler-views > li.k-state-selected > .k-link:after {\r\n    display: block;\r\n    content: \"\";\r\n    position: absolute;\r\n    top: 50%;\r\n    margin-top: -0.5em;\r\n    right: 0.333em;\r\n    width: 1.333em;\r\n    height: 1.333em;\r\n  }\r\n  .k-webkit .k-pager-numbers.k-state-expanded,\r\n  .k-ff .k-pager-numbers.k-state-expanded,\r\n  .k-ie11 .k-pager-numbers.k-state-expanded,\r\n  .k-safari .k-pager-numbers.k-state-expanded,\r\n  .k-webkit .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded,\r\n  .k-ff .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded,\r\n  .k-ie11 .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded,\r\n  .k-safari .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded {\r\n    border-width: 1px 1px 0 1px;\r\n    border-style: solid;\r\n    border-color: #dbdbdb;\r\n    background-color: #ffffff;\r\n    border-radius: 6px 6px 0 0;\r\n    -webkit-box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.3);\r\n            box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.3);\r\n  }\r\n  .k-webkit .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded,\r\n  .k-ff .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded,\r\n  .k-ie11 .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded,\r\n  .k-safari .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded {\r\n    border-width: 1px;\r\n    background-image: none;\r\n    border-radius: 6px;\r\n  }\r\n  .k-webkit .k-pager-numbers .k-state-selected,\r\n  .k-ff .k-pager-numbers .k-state-selected,\r\n  .k-ie11 .k-pager-numbers .k-state-selected,\r\n  .k-safari .k-pager-numbers .k-state-selected,\r\n  .k-webkit .k-pager-numbers .k-link,\r\n  .k-ff .k-pager-numbers .k-link,\r\n  .k-ie11 .k-pager-numbers .k-link,\r\n  .k-safari .k-pager-numbers .k-link {\r\n    border-radius: 5px;\r\n  }\r\n  .k-webkit .k-widget.k-grid .k-pager-nav + .k-pager-numbers,\r\n  .k-ff .k-widget.k-grid .k-pager-nav + .k-pager-numbers,\r\n  .k-ie11 .k-widget.k-grid .k-pager-nav + .k-pager-numbers,\r\n  .k-safari .k-widget.k-grid .k-pager-nav + .k-pager-numbers {\r\n    position: absolute;\r\n  }\r\n}\r\n.k-chart .k-mask {\r\n  background-color: #fff;\r\n  filter: alpha(opacity=68);\r\n  opacity: 0.68;\r\n}\r\n.k-chart .k-selection {\r\n  border-color: rgba(0, 0, 0, 0.2);\r\n  -webkit-box-shadow: inset 0 1px 8px rgba(0, 0, 0, 0.1);\r\n          box-shadow: inset 0 1px 8px rgba(0, 0, 0, 0.1);\r\n  -webkit-transition: -webkit-box-shadow 0.2s linear, border-color 0.2s linear;\r\n          transition: box-shadow 0.2s linear, border-color 0.2s linear;\r\n}\r\n.k-chart .k-selection:hover {\r\n  border-color: rgba(0, 0, 0, 0.3);\r\n  -webkit-box-shadow: inset 0 3px 8px rgba(0, 0, 0, 0.2);\r\n          box-shadow: inset 0 3px 8px rgba(0, 0, 0, 0.2);\r\n}\r\n.k-chart .k-handle {\r\n  background-color: #fcfcfc;\r\n  -webkit-box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.1);\r\n          box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.1);\r\n}\r\n.k-chart .k-handle:hover {\r\n  background-color: #ffffff;\r\n  border-color: #b8b8b8;\r\n  -webkit-box-shadow: 0 0 0 2px rgba(111, 101, 96, 0.5);\r\n          box-shadow: 0 0 0 2px rgba(111, 101, 96, 0.5);\r\n}\r\n.k-chart .k-navigator-hint .k-tooltip {\r\n  border: 3px solid #ffffff;\r\n  -webkit-box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.2);\r\n          box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.2);\r\n  background: #ffffff;\r\n  color: #242424;\r\n}\r\n.k-chart .k-navigator-hint .k-scroll {\r\n  background: rgba(238, 238, 238, 0.7);\r\n  height: 4px;\r\n}\r\n/* Map */\r\n.k-map .k-marker {\r\n  background-image: url(\"Uniform/markers.png\");\r\n}\r\n@media only screen and (-webkit-min-device-pixel-ratio: 1.2), only screen and (min-device-pixel-ratio: 1.2) {\r\n  .k-map .k-marker {\r\n    background-image: url(\"Uniform/markers_2x.png\");\r\n  }\r\n}\r\n.k-map .k-attribution {\r\n  color: #666666;\r\n}\r\n.k-spreadsheet-row-header,\r\n.k-spreadsheet-column-header {\r\n  background-color: #fff;\r\n}\r\n.k-spreadsheet-top-corner,\r\n.k-spreadsheet-row-header,\r\n.k-spreadsheet-column-header {\r\n  background-color: #fff;\r\n  background-image: none;\r\n  color: #000000;\r\n  border-color: #cccccc;\r\n}\r\n.k-spreadsheet-top-corner {\r\n  border-color: #cccccc;\r\n}\r\n.k-spreadsheet-top-corner:after {\r\n  border-color: transparent #cccccc #cccccc transparent;\r\n}\r\n.k-spreadsheet-pane {\r\n  border-color: #cccccc;\r\n}\r\n.k-spreadsheet-pane .k-spreadsheet-vaxis,\r\n.k-spreadsheet-pane .k-spreadsheet-haxis {\r\n  border-color: #e6e6e6;\r\n}\r\n.k-spreadsheet-pane .k-spreadsheet-column-header,\r\n.k-spreadsheet-pane .k-spreadsheet-row-header {\r\n  border-color: #cccccc;\r\n}\r\n.k-spreadsheet-pane .k-spreadsheet-merged-cell {\r\n  background-color: #fff;\r\n}\r\n.k-spreadsheet-pane .k-selection-partial,\r\n.k-spreadsheet-pane .k-selection-full {\r\n  border-color: rgba(238, 238, 238, 0.2);\r\n  background-color: rgba(238, 238, 238, 0.2);\r\n}\r\n.k-spreadsheet-pane .k-filter-range {\r\n  border-color: #eee;\r\n}\r\n.k-spreadsheet-pane .k-spreadsheet-column-header .k-selection-partial,\r\n.k-spreadsheet-pane .k-spreadsheet-column-header .k-selection-full {\r\n  border-bottom-color: #eeeeee;\r\n}\r\n.k-spreadsheet-pane .k-spreadsheet-row-header .k-selection-partial,\r\n.k-spreadsheet-pane .k-spreadsheet-row-header .k-selection-full {\r\n  border-right-color: #eeeeee;\r\n}\r\n.k-auto-fill,\r\n.k-spreadsheet-selection {\r\n  border-color: #eee;\r\n  -webkit-box-shadow: inset 0 0 0 1px #fff, 0 0 0 1px #eee;\r\n          box-shadow: inset 0 0 0 1px #fff, 0 0 0 1px #eee;\r\n}\r\n.k-spreadsheet-selection {\r\n  background-color: rgba(238, 238, 238, 0.2);\r\n}\r\n.k-spreadsheet-active-cell {\r\n  border-color: #eee !important;\r\n  background-color: #fff;\r\n}\r\n.k-spreadsheet-active-cell.k-single {\r\n  background-color: #fff;\r\n}\r\n.k-spreadsheet > .k-spreadsheet-formula-bar {\r\n  background-color: #fff;\r\n  border-color: #fff #fff #cccccc;\r\n}\r\n.k-spreadsheet > .k-spreadsheet-formula-bar:before {\r\n  border-color: #cccccc;\r\n}\r\n.k-spreadsheet > .k-spreadsheet-formula-bar:after {\r\n  border-color: #fff;\r\n}\r\n.k-spreadsheet .k-spreadsheet-formula-input {\r\n  background-color: #fff;\r\n  color: #676767;\r\n}\r\n.k-spreadsheet .k-resize-handle,\r\n.k-spreadsheet .k-resize-hint-handle,\r\n.k-spreadsheet .k-resize-hint-marker {\r\n  background-color: #eee;\r\n}\r\n.k-spreadsheet .k-resize-hint-vertical .k-resize-hint-handle,\r\n.k-spreadsheet .k-resize-hint-vertical .k-resize-hint-marker {\r\n  background-color: #eee;\r\n}\r\n.k-spreadsheet .k-single-selection::after {\r\n  background-color: #eee;\r\n  border-color: #fff;\r\n}\r\n.k-spreadsheet .k-auto-fill-punch {\r\n  background-color: rgba(255, 255, 255, 0.5);\r\n}\r\n.k-spreadsheet .k-single-selection.k-dim-auto-fill-handle::after {\r\n  background-color: rgba(238, 238, 238, 0.5);\r\n}\r\n.k-spreadsheet-format-cells .k-spreadsheet-preview {\r\n  border-color: #ebebeb;\r\n}\r\n.k-spreadsheet-filter {\r\n  border-radius: 6px;\r\n  background-color: #fff;\r\n  -webkit-box-shadow: inset 0 0 0 1px #e6e6e6;\r\n          box-shadow: inset 0 0 0 1px #e6e6e6;\r\n}\r\n.k-spreadsheet-filter.k-state-active {\r\n  color: #454545;\r\n  background-color: #eee;\r\n}\r\n.k-spreadsheet-filter:hover {\r\n  color: #676767;\r\n  background: #fff;\r\n  border-color: #ebebeb;\r\n}\r\n.k-action-window .k-action-buttons {\r\n  border-color: #ebebeb;\r\n}\r\n.k-spreadsheet-sample {\r\n  color: #b3b3b3;\r\n}\r\n.k-state-selected .k-spreadsheet-sample {\r\n  color: inherit;\r\n}\r\n.k-spreadsheet-window .k-list-wrapper,\r\n.k-spreadsheet-window .k-list {\r\n  border-color: #ebebeb;\r\n  border-radius: 6px;\r\n}\r\n.k-spreadsheet-window .export-config,\r\n.k-spreadsheet-window .k-edit-field > .k-orientation-label {\r\n  border-color: #ebebeb;\r\n}\r\n.k-spreadsheet-window .k-edit-field > input[type=\"radio\"]:checked + .k-orientation-label {\r\n  background-image: url('textures/highlight.png');\r\n  background-image: none, -webkit-gradient(linear, left top, left bottom, from(rgba(0,0,0,.13)), to(rgba(0,0,0,.08)));\r\n  background-image: none, -webkit-linear-gradient(top, rgba(0,0,0,.13) 0%, rgba(0,0,0,.08) 100%);\r\n  background-image: none, linear-gradient(to bottom, rgba(0,0,0,.13) 0%, rgba(0,0,0,.08) 100%);\r\n  background-color: #eee;\r\n  color: #646464;\r\n}\r\n.k-spreadsheet-window .k-page-orientation {\r\n  border-color: #dbdbdb;\r\n  -webkit-box-shadow: 0 5px 5px 0 rgba(0, 0, 0, 0.1);\r\n          box-shadow: 0 5px 5px 0 rgba(0, 0, 0, 0.1);\r\n}\r\n.k-spreadsheet-window .k-page-orientation:before {\r\n  background: #fff;\r\n  border-color: transparent;\r\n  border-bottom-color: #dbdbdb;\r\n  border-left-color: #dbdbdb;\r\n}\r\n.k-spreadsheet-window .k-margins-horizontal,\r\n.k-spreadsheet-window .k-margins-vertical {\r\n  background: transparent;\r\n  border-color: #ebebeb;\r\n}\r\n.k-spreadsheet-toolbar.k-toolbar .k-button-group .k-button {\r\n  border-radius: 6px;\r\n}\r\n.k-spreadsheet-toolbar > .k-widget,\r\n.k-spreadsheet-toolbar > .k-button,\r\n.k-spreadsheet-toolbar > .k-button-group {\r\n  border-radius: 6px;\r\n}\r\n.k-spreadsheet-toolbar > .k-separator {\r\n  border-color: #ebebeb;\r\n}\r\n.k-spreadsheet-toolbar .k-overflow-anchor {\r\n  border-radius: 0;\r\n}\r\n.k-spreadsheet-popup {\r\n  border-radius: 6px;\r\n}\r\n.k-spreadsheet-popup .k-separator {\r\n  background-color: #ebebeb;\r\n}\r\n.k-spreadsheet-popup .k-button {\r\n  background-color: transparent;\r\n}\r\n.k-spreadsheet-popup .k-button:hover {\r\n  background-color: #fff;\r\n}\r\n.k-spreadsheet-popup .k-state-active {\r\n  background-color: #eee;\r\n  color: #ffffff;\r\n}\r\n.k-spreadsheet-popup .k-state-active:hover {\r\n  background-color: #d5d5d5;\r\n}\r\n.k-spreadsheet-filter-menu .k-details {\r\n  border-color: #ebebeb;\r\n}\r\n.k-spreadsheet-filter-menu .k-details-content .k-space-right {\r\n  background-color: #fff;\r\n}\r\n.k-spreadsheet-filter-menu .k-spreadsheet-value-treeview-wrapper {\r\n  background-color: #fff;\r\n  border-color: #ebebeb;\r\n  border-radius: 6px 0 0 6px;\r\n}\r\n.k-syntax-ref {\r\n  color: #ff8822;\r\n}\r\n.k-syntax-num {\r\n  color: #0099ff;\r\n}\r\n.k-syntax-func {\r\n  font-weight: bold;\r\n}\r\n.k-syntax-str {\r\n  color: #38b714;\r\n}\r\n.k-syntax-error {\r\n  color: red;\r\n}\r\n.k-syntax-bool {\r\n  color: #a9169c;\r\n}\r\n.k-syntax-startexp {\r\n  font-weight: bold;\r\n}\r\n.k-syntax-paren-match {\r\n  background-color: #caf200;\r\n}\r\n.k-series-a {\r\n  border-color: #527aa3;\r\n  background-color: rgba(82, 122, 163, 0.15);\r\n}\r\n.k-series-b {\r\n  border-color: #6f91b3;\r\n  background-color: rgba(111, 145, 179, 0.15);\r\n}\r\n.k-series-c {\r\n  border-color: #8ca7c2;\r\n  background-color: rgba(140, 167, 194, 0.15);\r\n}\r\n.k-series-d {\r\n  border-color: #a8bdd1;\r\n  background-color: rgba(168, 189, 209, 0.15);\r\n}\r\n.k-series-e {\r\n  border-color: #c5d3e0;\r\n  background-color: rgba(197, 211, 224, 0.15);\r\n}\r\n.k-series-f {\r\n  border-color: #e2e9f0;\r\n  background-color: rgba(226, 233, 240, 0.15);\r\n}\r\n.k-spreadsheet-sheets-remove:hover .k-icon {\r\n  color: #cc2222;\r\n}\r\n.k-spreadsheet-formula-list .k-state-focused {\r\n  background-color: #eee;\r\n  color: #454545;\r\n}\r\n@media only screen and (-webkit-min-device-pixel-ratio: 2) {\r\n  .k-icon.k-font-icon {\r\n    background-image: none;\r\n  }\r\n}\r\n.k-spreadsheet .k-widget[data-property='fontSize'] {\r\n  width: 60px;\r\n}\r\n.k-spreadsheet .k-widget[data-property='format'] {\r\n  width: 100px;\r\n}\r\n.k-spreadsheet .k-widget[data-property='fontFamily'] {\r\n  width: 130px;\r\n}\r\n.k-spreadsheet-toolbar .k-combobox .k-input {\r\n  color: #676767;\r\n}\r\n.k-spreadsheet-toolbar .k-combobox .k-state-hover .k-input,\r\n.k-spreadsheet-toolbar .k-combobox .k-state-active .k-input,\r\n.k-spreadsheet-toolbar .k-combobox .k-state-focused .k-input {\r\n  color: #676767;\r\n}\r\n"]}