<template>
  <div style="margin-top: 20px">

    <el-descriptions class="margin-top" :column="1" :contentStyle="content_style1" :label-style="LS1" border>
      <el-descriptions-item  :label="'审查标题'" >
        审查内容
      </el-descriptions-item>
    </el-descriptions>

    <el-descriptions v-for="item in tableData" :column="1" :contentStyle="content_style" :label-style="LS"
                     border class="margin-top">
      <el-descriptions-item :label="item.matter">
        <el-radio-group v-if="!isView" v-model="item.isMatter" size="mini" @input="setInvolve(item)">
          <el-radio border :label="true">是</el-radio>
          <el-radio border :label="false">否</el-radio>
        </el-radio-group>
        <span v-else>{{ item.isMatter ? '是' : '否' }}</span>
      </el-descriptions-item>
      <el-descriptions-item v-if="item.isMatter && item.clause !== null && item.clause !== undefined" :label="item.clause"
                            label-class-name="my-label">
        <uploadDoc
            v-model="item.clauseFile"
            :disabled="dataState === 'view'"
            :doc-path="'/clauseFile'"
            :files.sync="item.clauseFile"/>
        <el-input v-if="!isView" v-model="item.clauseName" :rows="2"
                  maxlength="500" placeholder="请输入内容" show-word-limit type="textarea"/>

        <span v-else>{{ item.clauseName }}</span>
      </el-descriptions-item>
      <el-descriptions-item v-if="item.isMatter && item.measures !== null && item.measures !== undefined"
                            :label="item.measures"
                            label-class-name="my-label">
        <el-input v-if="!isView" v-model="item.measuresName" :rows="2"
                  maxlength="500" placeholder="请输入内容" show-word-limit type="textarea"/>
        <span v-else>{{ item.measuresName }}</span>
      </el-descriptions-item>
      <el-descriptions-item v-if="item.isMatter && item.describe !== null && item.describe !== undefined"
                            :label="item.describe"
                            label-class-name="my-label">
        <el-input v-if="!isView" v-model="item.describeName" :rows="2"
                  maxlength="500" placeholder="请输入内容" show-word-limit type="textarea"/>
        <span v-else>{{ item.describeName }}</span>
      </el-descriptions-item>
    </el-descriptions>
  </div>
</template>
<script>
// 公共方法
import SimpleBoard from "../../../../../components/SimpleBoard/SimpleBoardViewCase";
import {mapGetters} from "vuex"
import UploadDoc from "@/view/components/UploadDoc/UploadDoc.vue";
import caseFile from "@/view/litigation/disputeManage/disputeProcess/disputeChild/CaseFile.vue";

export default {
  name: "Party",
  inject: ['layout'],

  components: {caseFile, UploadDoc, SimpleBoard},
  props: {
    dataList: {
      type: Array,
      default: function () {
        return [];
      }
    },
    parentId: {
      type: String,
      default: ""
    },
    dataState: {
      type: String,
      default: ""
    },
    dataSourceCode: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      content_style: {
        // 设置长度
        'width': "68%",
        // 排列第二行
        'word-break': 'break-all'
      },
      content_style1: {
        // 设置长度
        'width': "68%",
        // 排列第二行
        'word-break': 'break-all',
        'font-weight': 'bold',
        'font-size': '16px',
        'color' : 'rgb(90, 90, 95)',
        'background': '#FAFAFA'
      },
      CS: {
        'min-width': '250px',   //最小宽度
        'word-break': 'break-all'  //过长时自动换行
      },
      LS: {
        'color':'#606266',
        // 'text-indent': '2em',
        'max-width': '200px',
        'word-break': 'normal'
      },
      LS1: {
        // 'text-indent': '2em',
        'max-width': '200px',
        'word-break': 'normal',
        'font-weight': 'bold',
        'font-size': '16px',
        'color' : 'rgb(90, 90, 95)',
      },
      tableData: this.dataList,
      tableLoading: false,
      riskDialogVisible: false,
    };
  },
  computed: {
    ...mapGetters(['orgContext', 'currentFunctionId']),
    isView: function () {
      return this.dataState === this.utils.formState.VIEW;
    },
  },
  watch: {
    tableData: {
      handler(val, oldVal) {
        this.$emit("update: ", val);
      },
      deep: true
    },
    dataList: {
      handler(val, oldVal) {
        this.tableData = val === null ? [] : val;
      },
      deep: true
    },
  },
  methods: {

    setInvolve(row) {
      if (row.isMatter) {
        return
      }
      let level =row.sort
      if(level === 1 || level === 2 || level === 3 ||level === 4){
        if(row.clauseFile == null && row.clauseName == null && row.measuresName == null){
          return
        }
      }
      if(level === 5 ){
        if(row.measuresName == null){
          return
        }
      }
      if(level === 6 ){
        if(row.clauseFile == null && row.clauseName == null && row.measuresName == null && row.describeName == null){
          return
        }
      }

      this.$confirm('取消后信息将清空，是否取消？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        row.clauseFile = null
        row.measuresName = null
        row.clauseName = null
        row.describeName = null
        console.log('确定')
      }).catch(() => {
        row.isMatter = true
        console.log('取消了')
      })
    }
  }
}
</script>

<style scoped>
::v-deep table tbody:nth-of-type(2) th,
::v-deep table tbody:nth-of-type(3) th,
::v-deep table tbody:nth-of-type(4) th {
  text-indent: 2em;
}
.el-dialog {
  width: 100% !important;
  height: 70vh;
  overflow: auto;
  box-shadow: 0 0px 0px rgba(0, 0, 0, 0) !important;
  margin: 0 auto 0px !important;
}

::v-deep .my-label {
  border: 1px solid #E4E7ED !important;
}

::v-deep .my-content {
  background: #FDE2E2 !important;
}
</style>

