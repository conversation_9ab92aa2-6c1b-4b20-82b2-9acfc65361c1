<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.klaw.dao.caseDao.CaseLedgerMapper">

    <!-- 案件台账快速检索 -->

    <select id="queryLedgerFastQueryCase1" parameterType="java.util.Map" resultType="java.util.Map">
        select
        case name
        when '钢铁板块管理平台' then '钢铁平台'
        when '股权投资管理平台' then '股权平台'
        when '北京园区开发运营管理平台' then '北京园区'
        when '包钢集团有限公司' then '集团公司'
        when '曹妃甸园区开发管理平台' then '曹妃甸平台'
        else name
        end as "name",
        count as "count",
        type as "type"
        from (
        select AA.UNIT_TYPE as name,count(*) as count,'YWDY' as type from
        (select * from sg_case_records m
        <where>
            ${ew.sqlSegment}
        </where>
        and m.UNIT_TYPE is not null and m.parent_id is null and m.case_kind is not null
        )  AA
        group by AA.UNIT_TYPE order by count desc
        ) b LIMIT 6;
    </select>

    <select id="queryLedgerFastQueryCase2" parameterType="java.util.Map" resultType="java.util.Map">
        select sum(count) as "count",
        'GLDW' AS "type",
        NAME as "name"
        from (
        select a.name as management_unit,
        a.count as count,
        ( case when b.superior_code = '20000001' then '集团公司'  else b.abbreviation end) as NAME
        from (
        select * from (
        select management_unit as name,count(*) as count from
        (select * from sg_case_records m
        <where>
            ${ew.sqlSegment}
        </where>  and m.parent_id is null
        and m.management_unit is not null
        and m.case_kind is not null
        )  FF
        GROUP BY management_unit order by count desc
        ) b LIMIT 5
        ) a, SG_ORGANIZATION b

        where a.name = b.code_name
        )c  WHERE 1 = '1'
        GROUP BY NAME
    </select>

    <select id="queryLedgerFastQueryCase3" parameterType="java.util.Map" resultType="java.util.Map">
        select * from (
        select CASE_KIND as "name",count(*) as "count",'AJZL' as "type" from
        (select * from sg_case_records m
        <where>
            ${ew.sqlSegment}
        </where>
        and m.CASE_KIND is not null and m.parent_id is null
        )  BB
        group by CASE_KIND order by count desc
        )  b LIMIT 5;
    </select>

    <select id="queryLedgerFastQueryCase4" parameterType="java.util.Map" resultType="java.util.Map">
        select * from
        (
        select AA.OUR_POSITION as "name",count(*) as "count",'WFDW' as "type"
        from
        (
        select * from sg_case_records m
        <where>
            ${ew.sqlSegment}
        </where>
        and m.OUR_POSITION is not null and m.parent_id is null and m.case_kind is not null
        )  AA
        group by AA.OUR_POSITION order by count desc
        )  b LIMIT 5;
    </select>

    <select id="queryLedgerFastQueryCase5" parameterType="java.util.Map" resultType="java.util.Map">
        select * from (
        select cause_name as "name",count(*) as "count",'AY' as "type" from
        (select * from sg_case_records m
        <where>
            ${ew.sqlSegment}
        </where>  and m.parent_id is null
        and m.cause_name is not null
        and m.case_kind is not null
        )  CC
        GROUP BY cause_name order by count desc
        )  b LIMIT 5;
    </select>

    <select id="queryLedgerFastQueryCase6" parameterType="java.util.Map" resultType="java.util.Map">
        select * from (
        select VENUE_PROVINCE as "name",count(*) as "count",'AFDQ' as "type" from
        (select * from sg_case_records m
        <where>
            ${ew.sqlSegment}
        </where> and m.parent_id is null
        and m.VENUE_PROVINCE is not null
        and m.case_kind is not null
        )  DD
        GROUP BY VENUE_PROVINCE order by count desc
        )  b LIMIT 5;
    </select>

    <select id="queryLedgerFastQueryCase7" parameterType="java.util.Map" resultType="java.util.Map">
        select * from (
        select AA.COURT as "name",count(*) as "count",'GXJG' as "type" from
        (select * from sg_case_records m
        <where>
            ${ew.sqlSegment}
        </where>
        and m.COURT is not null and m.parent_id is null and m.case_kind is not null
        )  AA
        group by AA.COURT order by count desc
        )  b LIMIT 5;
    </select>

    <select id="queryLedgerFastQueryCase8" parameterType="java.util.Map" resultType="java.util.Map">
        select * from (
        select date_format(AA.case_time,'%Y') as "name",count(*) as "count",'AJND' as "type" from
        (select * from sg_case_records m
        <where>
            ${ew.sqlSegment}
        </where>
        and m.case_time is not null and m.parent_id is null and m.case_kind is not null
        )  AA
        group by date_format(AA.case_time,'%Y') order by count desc
        )  b LIMIT 5;
    </select>

    <select id="queryLedgerFastQueryCase9" parameterType="java.util.Map" resultType="java.util.Map">
        select * from (
        select AA.risk_level as "name",count(*) as "count",'FXDJ' as "type" from
        (select * from sg_case_records m
        <where>
            ${ew.sqlSegment}
        </where>
        and m.risk_level is not null and m.parent_id is null and m.case_kind is not null
        )  AA
        group by AA.risk_level order by count desc
        )  b LIMIT 5;
    </select>








    <select id="queryFastQueryCase" parameterType="java.util.Map" resultType="java.util.Map">
        select "NAME","COUNT","TYPE" from (
        select AA.OUR_POSITION as name,count(*) as count,'WFDW' as type from
        (select * from sg_case_records m
        <where>
            ${ew.sqlSegment}
        </where>
        and m.OUR_POSITION is not null and m.parent_id is null and m.case_kind is not null
        )  AA
        group by AA.OUR_POSITION

        UNION

        select CASE_KIND as name,count(*) as count,'AJZL' as type from
        (select * from sg_case_records m
        <where>
            ${ew.sqlSegment}
        </where>
        and m.CASE_KIND is not null and m.parent_id is null
        )  BB
        group by CASE_KIND

        UNION

        select cause_name as name,count(*) as count,'AY' as type from
        (select * from sg_case_records m
        <where>
            ${ew.sqlSegment}
        </where>  and m.parent_id is null
        and m.cause_name is not null
        and m.case_kind is not null
        )  CC
        GROUP BY cause_name

        union

        select VENUE_PROVINCE as name,count(*) as count,'AFDQ' as type from
        (select * from sg_case_records m
        <where>
            ${ew.sqlSegment}
        </where>  and m.parent_id is null
        and m.VENUE_PROVINCE is not null
        and m.case_kind is not null
        )  DD
        GROUP BY VENUE_PROVINCE

        UNION

        select belong_plate as name,count(*) as count,'SSDW' as type from
        (select * from sg_case_records m
        <where>
            ${ew.sqlSegment}
        </where>  and m.parent_id is null
        and m.belong_plate is not null
        and m.case_kind is not null
        )  FF
        GROUP BY belong_plate

        UNION

        select name as name,count(*) as count,'TAG' as type from
        (select * from TAG WHERE IS_DELETE = 0 AND DATA_ID IN
        (select ID from sg_case_records m
        <where>
            ${ew.sqlSegment}
        </where>  and m.parent_id is null
        and m.cause_name is not null
        and m.case_kind is not null
        )
        )  EE
        GROUP BY name

        ) A
    </select>

    <select id="queryFastQueryCase1" parameterType="java.util.Map" resultType="java.util.Map">
        select * from (
            select AA.OUR_POSITION as name,count(*) as count,'WFDW' as type from
            (select * from sg_case_records m
            <where>
                ${ew.sqlSegment}
            </where>
            and m.OUR_POSITION is not null and m.parent_id is null and m.case_kind is not null
            )  AA
            group by AA.OUR_POSITION order by count desc
        ) where rownum &lt; 6
    </select>

    <select id="queryFastQueryCase2" parameterType="java.util.Map" resultType="java.util.Map">
        select * from (
            select CASE_KIND as name,count(*) as count,'AJZL' as type from
            (select * from sg_case_records m
            <where>
                ${ew.sqlSegment}
            </where>
            and m.CASE_KIND is not null and m.parent_id is null
            )  BB
            group by CASE_KIND order by count desc
        )aa
        LIMIT 5;
--         where rownum &lt; 6
    </select>

    <select id="queryFastQueryCase3" parameterType="java.util.Map" resultType="java.util.Map">
        select * from (
            select cause_name as name,count(*) as count,'AY' as type from
            (select * from sg_case_records m
            <where>
                ${ew.sqlSegment}
            </where>  and m.parent_id is null
            and m.cause_name is not null
            and m.case_kind is not null
            )  CC
            GROUP BY cause_name order by count desc
        ) where rownum &lt; 6
    </select>

    <select id="queryFastQueryCase4" parameterType="java.util.Map" resultType="java.util.Map">
        select * from (
            select VENUE_PROVINCE as name,count(*) as count,'AFDQ' as type from
            (select * from sg_case_records m
            <where>
                ${ew.sqlSegment}
            </where>  and m.parent_id is null
            and m.VENUE_PROVINCE is not null
            and m.case_kind is not null
            )  DD
            GROUP BY VENUE_PROVINCE order by count desc
        ) where rownum &lt; 6
    </select>

    <select id="queryFastQueryCase5" parameterType="java.util.Map" resultType="java.util.Map">
        select name as name,count(*) as count,'TAG' as type from
        (select * from sg_TAG WHERE IS_DELETE = 0 AND DATA_ID IN
        (select ID from sg_case_records m
        <where>
            ${ew.sqlSegment}
        </where>  and m.parent_id is null
        and m.cause_name is not null
        and m.case_kind is not null
        )
        )  EE
        GROUP BY name

    </select>

    <select id="queryFastQueryCase6" parameterType="java.util.Map" resultType="java.util.Map">
        select belong_plate as name,count(*) as count,'SSDW' as type from
        (select * from sg_case_records m
        <where>
            ${ew.sqlSegment}
        </where>  and m.parent_id is null
        and m.belong_plate is not null
        and m.case_kind is not null
        )  FF
        GROUP BY belong_plate


    </select>

    <select id="queryFastQueryCase7" parameterType="java.util.Map" resultType="java.util.Map">
        select sum(count) as count,
                'GLDW' AS type,
                NAME
        from (
            select a.name as management_unit,
            a.count as count,
            ( case when b.superior_code = '20000001' then '集团公司'  else b.abbreviation end) as NAME
            from (
            select * from (
            select management_unit as name,count(*) as count from
            (select * from sg_case_records m
            <where>
                ${ew.sqlSegment}
            </where>  and m.parent_id is null
            and m.management_unit is not null
            and m.case_kind is not null
            )  FF
            GROUP BY management_unit order by count desc
            ) where rownum &lt; 6
            ) a, SG_ORGANIZATION b

            where a.name = b.code_name
        ) WHERE 1 = '1'
        GROUP BY NAME
    </select>

    <select id="queryFastQueryCase11" parameterType="java.util.Map" resultType="java.util.Map">
        select AA.OUR_POSITION as name,count(*) as count,'WFDW' as type from
        (select m.* from SG_CASE_REPORT_TASK t join SG_MIDDLE_RELATION r
        on t.ID = r.RELATION_ID join SG_CASE_RECORDS m on r.ASSOCIATED_ID = m.ID
        <where>
            ${ew.sqlSegment}
        </where>
        and m.OUR_POSITION is not null and m.parent_id is null and m.case_kind is not null
        )  AA
        group by AA.OUR_POSITION


    </select>

    <select id="queryFastQueryCase12" parameterType="java.util.Map" resultType="java.util.Map">
        select CASE_KIND as name,count(*) as count,'AJZL' as type from
        (select m.* from SG_CASE_REPORT_TASK t join SG_MIDDLE_RELATION r
        on t.ID = r.RELATION_ID join SG_CASE_RECORDS m on r.ASSOCIATED_ID = m.ID
        <where>
            ${ew.sqlSegment}
        </where>
        and m.CASE_KIND is not null and m.parent_id is null
        )  BB
        group by CASE_KIND


    </select>

    <select id="queryFastQueryCase13" parameterType="java.util.Map" resultType="java.util.Map">
        select cause_name as name,count(*) as count,'AY' as type from
        (select m.* from SG_CASE_REPORT_TASK t join SG_MIDDLE_RELATION r
        on t.ID = r.RELATION_ID join SG_CASE_RECORDS m on r.ASSOCIATED_ID = m.ID
        <where>
            ${ew.sqlSegment}
        </where>  and m.parent_id is null
        and m.cause_name is not null
        and m.case_kind is not null
        )  CC
        GROUP BY cause_name


    </select>

    <select id="queryFastQueryCase14" parameterType="java.util.Map" resultType="java.util.Map">
        select VENUE_PROVINCE as name,count(*) as count,'AFDQ' as type from
        (select m.* from SG_CASE_REPORT_TASK t join SG_MIDDLE_RELATION r
        on t.ID = r.RELATION_ID join SG_CASE_RECORDS m on r.ASSOCIATED_ID = m.ID
        <where>
            ${ew.sqlSegment}
        </where>  and m.parent_id is null
        and m.VENUE_PROVINCE is not null
        and m.case_kind is not null
        )  DD
        GROUP BY VENUE_PROVINCE


    </select>

    <select id="queryFastQueryCase15" parameterType="java.util.Map" resultType="java.util.Map">
        select name as name,count(*) as count,'TAG' as type from
        (select * from sg_TAG WHERE IS_DELETE = 0 AND DATA_ID IN
        (select m.ID from SG_CASE_REPORT_TASK t join SG_MIDDLE_RELATION r
        on t.ID = r.RELATION_ID join SG_CASE_RECORDS m on r.ASSOCIATED_ID = m.ID
        <where>
            ${ew.sqlSegment}
        </where>  and m.parent_id is null
        and m.cause_name is not null
        and m.case_kind is not null
        )
        )  EE
        GROUP BY name

    </select>

    <select id="queryFastQueryCase16" parameterType="java.util.Map" resultType="java.util.Map">
        select belong_plate as name,count(*) as count,'SSDW' as type from
        (select m.* from SG_CASE_REPORT_TASK t join SG_MIDDLE_RELATION r
        on t.ID = r.RELATION_ID join SG_CASE_RECORDS m on r.ASSOCIATED_ID = m.ID
        <where>
            ${ew.sqlSegment}
        </where>  and m.parent_id is null
        and m.belong_plate is not null
        and m.case_kind is not null
        )  FF
        GROUP BY belong_plate


    </select>

    <select id="queryFastQueryCase21" parameterType="java.util.Map" resultType="java.util.Map">
        select AA.OUR_POSITION as name,count(*) as count,'WFDW' as type from
        (select * from sg_case_records m
        <where>
            m.id not in (
            select id from (
            select m.id, m.CASE_NAME,
            case when d.name is null then '无' else d.name end as tagName
            from sg_case_records m left join sg_tag d on m.id = d.data_id
            <where>
                ${ew.sqlSegment}
            </where>
            )  A
            where tagName ='一审开庭前撤诉' or tagName = '并案处理' or tagName = '重复数据'
            )
            and
            ${ew.sqlSegment}
        </where>
        and m.OUR_POSITION is not null and m.parent_id is null and m.case_kind is not null
        )  AA
        group by AA.OUR_POSITION


    </select>

    <select id="queryFastQueryCase22" parameterType="java.util.Map" resultType="java.util.Map">
        select CASE_KIND as name,count(*) as count,'AJZL' as type from
        (select * from sg_case_records m
        <where>
            m.id not in (
            select id from (
            select m.id, m.CASE_NAME,
            case when d.name is null then '无' else d.name end as tagName
            from sg_case_records m left join sg_tag d on m.id = d.data_id
            <where>
                ${ew.sqlSegment}
            </where>
            )  A
            where tagName ='一审开庭前撤诉' or tagName = '并案处理' or tagName = '重复数据'
            )
            and
            ${ew.sqlSegment}
        </where>
        and m.CASE_KIND is not null and m.parent_id is null
        )  BB
        group by CASE_KIND


    </select>

    <select id="queryFastQueryCase23" parameterType="java.util.Map" resultType="java.util.Map">
        select cause_name as name,count(*) as count,'AY' as type from
        (select * from sg_case_records m
        <where>
            m.id not in (
            select id from (
            select m.id, m.CASE_NAME,
            case when d.name is null then '无' else d.name end as tagName
            from sg_case_records m left join sg_tag d on m.id = d.data_id
            <where>
                ${ew.sqlSegment}
            </where>
            )  A
            where tagName ='一审开庭前撤诉' or tagName = '并案处理' or tagName = '重复数据'
            )
            and
            ${ew.sqlSegment}
        </where>  and m.parent_id is null
        and m.cause_name is not null
        and m.case_kind is not null
        )  CC
        GROUP BY cause_name


    </select>

    <select id="queryFastQueryCase24" parameterType="java.util.Map" resultType="java.util.Map">
        select VENUE_PROVINCE as name,count(*) as count,'AFDQ' as type from
        (select * from sg_case_records m
        <where>
            m.id not in (
            select id from (
            select m.id, m.CASE_NAME,
            case when d.name is null then '无' else d.name end as tagName
            from sg_case_records m left join sg_tag d on m.id = d.data_id
            <where>
                ${ew.sqlSegment}
            </where>
            )  A
            where tagName ='一审开庭前撤诉' or tagName = '并案处理' or tagName = '重复数据'
            )
            and
            ${ew.sqlSegment}
        </where>  and m.parent_id is null
        and m.VENUE_PROVINCE is not null
        and m.case_kind is not null
        )  DD
        GROUP BY VENUE_PROVINCE


    </select>

    <select id="queryFastQueryCase25" parameterType="java.util.Map" resultType="java.util.Map">
        select name as name,count(*) as count,'TAG' as type from
        (select * from sg_TAG WHERE IS_DELETE = 0 AND DATA_ID IN
        (select ID from sg_case_records m
        <where>
            m.id not in (
            select id from (
            select m.id, m.CASE_NAME,
            case when d.name is null then '无' else d.name end as tagName
            from sg_case_records m left join sg_tag d on m.id = d.data_id
            <where>
                ${ew.sqlSegment}
            </where>
            )  A
            where tagName ='一审开庭前撤诉' or tagName = '并案处理' or tagName = '重复数据'
            )
            and
            ${ew.sqlSegment}
        </where>  and m.parent_id is null
        and m.cause_name is not null
        and m.case_kind is not null
        )
        )  EE
        GROUP BY name

    </select>

    <select id="queryFastQueryCase26" parameterType="java.util.Map" resultType="java.util.Map">
        select belong_plate as name,count(*) as count,'SSDW' as type from
        (select * from sg_case_records m
        <where>
            m.id not in (
            select id from (
            select m.id, m.CASE_NAME,
            case when d.name is null then '无' else d.name end as tagName
            from sg_case_records m left join sg_tag d on m.id = d.data_id
            <where>
                ${ew.sqlSegment}
            </where>
            )  A
            where tagName ='一审开庭前撤诉' or tagName = '并案处理' or tagName = '重复数据'
            )
            and
            ${ew.sqlSegment}
        </where>  and m.parent_id is null
        and m.belong_plate is not null
        and m.case_kind is not null
        )  FF
        GROUP BY belong_plate


    </select>

    <select id="queryFastQueryCase27" parameterType="java.util.Map" resultType="java.util.Map">
        select
               case name
                    when '钢铁板块管理平台' then '钢铁平台'
                    when '股权投资管理平台' then '股权平台'
                    when '北京园区开发运营管理平台' then '北京园区'
                    when '包钢集团有限公司' then '集团公司'
                    when '曹妃甸园区开发管理平台' then '曹妃甸平台'
                    else name
                    end as name,
                count,
                type
        from (
            select AA.UNIT_TYPE as name,count(*) as count,'YWDY' as type from
            (select * from sg_case_records m
            <where>
                ${ew.sqlSegment}
            </where>
            and m.UNIT_TYPE is not null and m.parent_id is null and m.case_kind is not null
            )  AA
            group by AA.UNIT_TYPE order by count desc
        ) where rownum &lt; 7
    </select>

    <select id="queryFastQueryCase28" parameterType="java.util.Map" resultType="java.util.Map">
        select * from (
            select AA.COURT as name,count(*) as count,'GXJG' as type from
            (select * from sg_case_records m
            <where>
                ${ew.sqlSegment}
            </where>
            and m.COURT is not null and m.parent_id is null and m.case_kind is not null
            )  AA
            group by AA.COURT order by count desc
        ) where rownum &lt; 6
    </select>

    <select id="queryFastQueryCase29" parameterType="java.util.Map" resultType="java.util.Map">
        select * from (
            select to_char(AA.case_time, 'yyyy' ) as name,count(*) as count,'AJND' as type from
            (select * from sg_case_records m
            <where>
                ${ew.sqlSegment}
            </where>
            and m.case_time is not null and m.parent_id is null and m.case_kind is not null
            )  AA
            group by to_char(AA.case_time, 'yyyy' ) order by count desc
        ) where rownum &lt; 6
    </select>

    <select id="queryFastQueryCase30" parameterType="java.util.Map" resultType="java.util.Map">
        select * from (
        select AA.risk_level as name,count(*) as count,'FXDJ' as type from
        (select * from sg_case_records m
        <where>
            ${ew.sqlSegment}
        </where>
        and m.risk_level is not null and m.parent_id is null and m.case_kind is not null
        )  AA
        group by AA.risk_level order by count desc
        ) where rownum &lt; 6
    </select>


    <select id="queryFastQueryCase8" parameterType="java.util.Map" resultType="java.util.Map">
        SELECT name,COUNT(*) AS count,'AJJC' AS type FROM
        (
        SELECT CASE m.case_current_process
        WHEN  '未收案' THEN '未收案'
        WHEN  '未收案中' THEN '未收案'
        WHEN  '收立案中' THEN '收立案'
        WHEN  '侦查中' THEN '侦查'
        WHEN  '审查起诉中' THEN '审查起诉'
        WHEN  '初裁中' THEN '初裁'
        WHEN  '终审中' THEN '终审'
        WHEN  '撤销仲裁中' THEN '撤销仲裁'
        WHEN  '侦查完成' THEN '侦查'
        WHEN  '审查起诉完成' THEN '审查起诉'
        WHEN  '初裁完成' THEN '初裁'
        WHEN  '终审完成' THEN '终审'
        WHEN  '撤销仲裁完成' THEN '撤销仲裁'
        WHEN  '收立案完成' THEN '收立案'
        WHEN  '管辖一审中' THEN '管辖一审'
        WHEN  '管辖一审完成' THEN '管辖一审'
        WHEN  '管辖二审中' THEN '管辖二审'
        WHEN  '管辖二审完成' THEN '管辖二审'
        WHEN  '诉前调解中' THEN '诉前调解'
        WHEN  '诉前调解完成' THEN '诉前调解'
        WHEN  '一审中' THEN '一审'
        WHEN  '一审完成' THEN '一审'
        WHEN  '二审中' THEN '二审'
        WHEN  '二审完成' THEN '二审'
        WHEN  '重审中' THEN '重审'
        WHEN  '重审完成' THEN '重审'
        WHEN  '再审一审中' THEN '再审一审'
        WHEN  '再审一审完成' THEN '再审一审'
        WHEN  '再审二审中' THEN '再审二审'
        WHEN  '再审二审完成' THEN '再审二审'
        WHEN  '执行中' THEN '执行'
        WHEN  '执行完成' THEN '执行'
        WHEN  '结案中' THEN '结案'
        WHEN  '结案完成' THEN '结案'
        WHEN  '仲裁中' THEN '仲裁'
        WHEN  '仲裁完成' THEN '仲裁'
        WHEN  '行政复议中' THEN '行政复议'
        WHEN  '行政复议完成' THEN '行政复议'
        WHEN  '不起诉' THEN '不起诉'
        ELSE
        '其他'
        END AS NAME FROM SG_case_RECORDS m
        <where>
            ${ew.sqlSegment}
        </where>
        and m.parent_id IS NULL AND m.case_current_process IS NOT NULL
        )  HH
        group by name
    </select>

    <select id="queryFastQueryCase9" parameterType="java.util.Map" resultType="java.util.Map">
        select case_time as name,count(*) as count,'AFSJ' as type from
        (
        SELECT DATE_FORMAT(m.case_time,'yyyy-mm') as case_time FROM SG_case_RECORDS m
        <where>
            ${ew.sqlSegment}
        </where>
        and m.parent_id IS NULL AND m.case_time IS NOT NULL
        )  JJ
        group by case_time
    </select>

    <resultMap id="caseRowMap" type="java.util.Map">
        <id property="id" column="id" />
        <result column="PARENT_ID" property="parentId" />
        <result column="CASE_PROCESS_TYPE" property="caseProcessType" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="MATTER" property="matter" />
        <result column="OCCUR_TIME" property="occurTime" />
        <result column="JUDGE_TIME" property="judgeTime" />
        <result column="CASE_END_TIME" property="caseEndTime" />
    </resultMap>
    <select id="queryTimeAxis" parameterType="java.util.Map" resultMap="caseRowMap">
        select m.CASE_PROCESS_TYPE,m.CREATE_TIME,d.MATTER,d.OCCUR_TIME,m.ID,m.PARENT_ID,m.JUDGE_TIME,m.CASE_END_TIME from sg_case_records m left join sg_CASE_PROCESS d
          on m.id=d.PARENT_ID
        <where>
            ${ew.sqlSegment}
        </where>
    </select>
</mapper>
