
f95869042d7b241e647851903f7a088684b3e8da	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.144.1754018536329.js\",\"contentHash\":\"deb30ebd39d13be2bac497feb284a327\"}","integrity":"sha512-fqspxGkvhcrjT1ZD2AKDZtIUEBJX3c4O5FR8uzYoGmQEJVF9lMsb1Oxc427mr64MANb7+VS4tsEUOTh1Kgy1nQ==","time":1754018575980,"size":148042}