<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.klaw.dao.contractDao.contract.BmContractPerformResultMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.klaw.entity.contractBean.contract.BmContractPerformResult">
        <id column="id" property="id" jdbcType="VARCHAR"/>
        <result column="perform_code" property="performCode" jdbcType="VARCHAR"/>
        <result column="our_party_name" property="ourPartyName" jdbcType="VARCHAR"/>
        <result column="other_party_name" property="otherPartyName" jdbcType="VARCHAR"/>
        <result column="contract_code" property="contractCode" jdbcType="VARCHAR"/>
        <result column="revenue_expenditure" property="revenueExpenditure" jdbcType="VARCHAR"/>
        <result column="settlement_method" property="settlementMethod" jdbcType="VARCHAR"/>
        <result column="project_code" property="projectCode" jdbcType="VARCHAR"/>
        <result column="currency" property="currency" jdbcType="VARCHAR"/>
        <result column="exchange_rate" property="exchangeRate" jdbcType="DECIMAL"/>
        <result column="actual_amount" property="actualAmount" jdbcType="DECIMAL"/>
        <result column="actual_perform_date" property="actualPerformDate" jdbcType="DATE"/>
        <result column="actual_amount_rmb" property="actualAmountRmb" jdbcType="DECIMAL"/>
        <result column="fund_document_code" property="fundDocumentCode" jdbcType="VARCHAR"/>
        <result column="cliam_flag" property="cliamFlag" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, perform_code, our_party_name, other_party_name, contract_code, revenue_expenditure,
        settlement_method, project_code, currency, exchange_rate, actual_amount, actual_perform_date,
        actual_amount_rmb, fund_document_code, cliam_flag
    </sql>



    <select id="queryPageList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from bm_contract_perform_result
        <where>
            ${ew.sqlSegment}
        </where>
    </select>

</mapper>
