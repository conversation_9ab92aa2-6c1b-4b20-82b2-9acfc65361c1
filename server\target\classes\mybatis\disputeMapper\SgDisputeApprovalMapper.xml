<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.klaw.dao.disputeDao.SgDisputeApprovalMapper">
  <resultMap id="BaseResultMap" type="com.klaw.entity.disputeBean.SgDisputeApproval">
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="NAME" jdbcType="VARCHAR" property="name" />
    <result column="TYPE_CODE" jdbcType="VARCHAR" property="typeCode" />
    <result column="TYPE_NAME" jdbcType="VARCHAR" property="typeName" />
    <result column="PURPOSE_CODE" jdbcType="VARCHAR" property="purposeCode" />
    <result column="PURPOSE_NAME" jdbcType="VARCHAR" property="purposeName" />
    <result column="DESCRIPTION" jdbcType="CLOB" property="description" />
    <result column="ATTACHMENT" jdbcType="CLOB" property="attachment" />
    <result column="SEAL_TYPE_CODE" jdbcType="VARCHAR" property="sealTypeCode" />
    <result column="SEAL_TYPE_NAME" jdbcType="VARCHAR" property="sealTypeName" />
    <result column="SEAL_COUNT" jdbcType="VARCHAR" property="sealCount" />

    <result column="DISPUTE_NAMES" jdbcType="CLOB" property="disputeNames" />
    <result column="DISPUTE_IDS" jdbcType="CLOB" property="disputeIds" />

    <result column="CREATE_OGN_ID" jdbcType="VARCHAR" property="createOgnId" />
    <result column="CREATE_OGN_NAME" jdbcType="VARCHAR" property="createOgnName" />
    <result column="CREATE_DEPT_ID" jdbcType="VARCHAR" property="createDeptId" />
    <result column="CREATE_DEPT_NAME" jdbcType="VARCHAR" property="createDeptName" />
    <result column="CREATE_GROUP_ID" jdbcType="VARCHAR" property="createGroupId" />
    <result column="CREATE_GROUP_NAME" jdbcType="VARCHAR" property="createGroupName" />
    <result column="CREATE_PSN_ID" jdbcType="VARCHAR" property="createPsnId" />
    <result column="CREATE_PSN_NAME" jdbcType="VARCHAR" property="createPsnName" />
    <result column="CREATE_ORG_ID" jdbcType="VARCHAR" property="createOrgId" />
    <result column="CREATE_ORG_NAME" jdbcType="VARCHAR" property="createOrgName" />
    <result column="CREATE_PSN_FULL_ID" jdbcType="VARCHAR" property="createPsnFullId" />
    <result column="CREATE_PSN_FULL_NAME" jdbcType="VARCHAR" property="createPsnFullName" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="DATA_STATE" jdbcType="VARCHAR" property="dataState" />
    <result column="DATA_STATE_CODE" jdbcType="DECIMAL" property="dataStateCode" />
    <collection property="disputeList" javaType="java.util.ArrayList" ofType="com.klaw.entity.disputeBean.SgDispute">
      <id column="DID" jdbcType="VARCHAR" property="id" />
      <result column="NAME" jdbcType="VARCHAR" property="name" />
      <result column="CODE" jdbcType="VARCHAR" property="code" />
      <result column="OCCUR_TIME" jdbcType="TIMESTAMP" property="occurTime" />
      <result column="DISPUTE_TYPE_CODE" jdbcType="VARCHAR" property="disputeTypeCode" />
      <result column="DISPUTE_TYPE_NAME" jdbcType="VARCHAR" property="disputeTypeName" />
    </collection>
  </resultMap>
  <sql id="Base_Column_List">
    m.ID, m.NAME, m.TYPE_CODE, m.TYPE_NAME, m.PURPOSE_CODE, m.PURPOSE_NAME, m.DESCRIPTION, m.ATTACHMENT,
    m.SEAL_TYPE_CODE, m.SEAL_TYPE_NAME, m.SEAL_COUNT, m.CREATE_OGN_ID, m.CREATE_OGN_NAME, m.CREATE_DEPT_ID,
     m.CREATE_DEPT_NAME, m.CREATE_GROUP_ID, m.CREATE_GROUP_NAME, m.CREATE_PSN_ID, m.CREATE_PSN_NAME, m.CREATE_ORG_ID,
      m.CREATE_ORG_NAME, m.CREATE_PSN_FULL_ID, m.CREATE_PSN_FULL_NAME, m.CREATE_TIME, m.UPDATE_TIME, m.DATA_STATE,
      m.DATA_STATE_CODE, d.id as DID, d.NAME as DNAME, d.CODE, d.OCCUR_TIME, d.DISPUTE_TYPE_CODE, d.DISPUTE_TYPE_NAME
  </sql>

  <select id="queryList" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/> from sg_dispute_approval m left join sg_dispute d on m.id = d.parent_id
    <where>
      ${ew.sqlSegment}
    </where>
  </select>
</mapper>