<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.klaw.dao.authorizationDao.AuthorizationEvaluationMapper">

    <resultMap id="BaseResultMap" type="com.klaw.entity.authorizationBean.AuthorizationEvaluation">
        <id property="id" column="id" jdbcType="VARCHAR"/>
        <result property="documentNumber" column="document_number" jdbcType="VARCHAR"/>
        <result property="evaluatedPersonName" column="evaluated_person_name" jdbcType="VARCHAR"/>
        <result property="evaluatedPersonEmployeeCode" column="evaluated_person_employee_code" jdbcType="VARCHAR"/>
        <result property="evaluatedPersonIdNumber" column="evaluated_person_id_number" jdbcType="VARCHAR"/>
        <result property="evaluatedPersonUnit" column="evaluated_person_unit" jdbcType="VARCHAR"/>
        <result property="evaluatedPersonDepartment" column="evaluated_person_department" jdbcType="VARCHAR"/>
        <result property="evaluationPassDate" column="evaluation_pass_date" jdbcType="TIMESTAMP"/>
        <result property="evaluationStatus" column="evaluation_status" jdbcType="VARCHAR"/>
        <result property="invalidationDate" column="invalidation_date" jdbcType="TIMESTAMP"/>
        <result property="status" column="status" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,document_number,evaluated_person_name,
        evaluated_person_employee_code,evaluated_person_id_number,evaluated_person_unit,
        evaluated_person_department,evaluation_pass_date,evaluation_status,
        invalidation_date,status
    </sql>
</mapper>
