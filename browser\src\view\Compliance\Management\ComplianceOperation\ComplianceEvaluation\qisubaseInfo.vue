<template xmlns:v-slot="http://www.w3.org/1999/XSL/Transform">
  <div style="margin-top: 10px" v-if="view === 'old'">
    <!--基础信息表单块-->
    <div v-if="dataState !== 'view'">
      <div style="padding-left: 10px;margin-bottom: 20px;font-size: 15px;color: red;font-weight: bolder">

      </div>
      <div style="margin: 10px">
        <span style="font-weight: bold;font-size: 16px;color: #5A5A5F;">基本信息</span>
        <el-divider></el-divider>
      </div>
      <el-row style="margin-top: 10px" v-if="mainData.evaluationObjectType != '合规管理员'">
        <el-col :span="8">
          <el-form-item :label="'关联评价\n通知编码'" class="fold_label">
            <el-input v-if="!isView" v-model.trim="mainData.complianceNotificationCode" show-word-limit
              style="width: 100%" @click.native="showNotificationDialog" readonly />
            <span v-else class="viewSpan">{{ mainData.complianceNotificationCode }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="编码">
            <el-input v-if="!isView" v-model.trim="mainData.code" disabled show-word-limit style="width: 100%" />
            <span v-else class="viewSpan">{{ mainData.code }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="评价对象类型" prop="evaluationObjectType">
            <el-input v-if="!isView" v-model.trim="mainData.evaluationObjectType" :disabled="true" show-word-limit
              style="width: 100%" placeholder="评价对象类型不可修改" />
          </el-form-item>
        </el-col>
        <el-col>
          <el-col :span="16">
            <el-form-item label="评价对象" prop="evaluationObject">
              <el-input v-if="!isView" v-model="mainData.evaluationObject" placeholder="请选择" class="input-with-select"
                disabled>
                <el-button slot="append" icon="el-icon-search" @click="showOrgTreeDialog" />
              </el-input>
              <span v-else class="viewSpan">{{ mainData.evaluationObject }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="报告年度" prop="evaluationYear">
              <el-input v-if="!isView" v-model.trim="mainData.evaluationYear" :disabled="true" show-word-limit
                style="width: 100%" placeholder="年份不可修改" />
              <span v-else class="viewSpan">{{ mainData.evaluationYear }}</span>
            </el-form-item>
          </el-col>
        </el-col>
      </el-row>

      <el-row style="margin-top: 10px" v-else>
        <el-col :span="8">
          <el-form-item label="编码">
            <el-input v-if="!isView" v-model.trim="mainData.code" disabled show-word-limit style="width: 100%" />
            <span v-else class="viewSpan">{{ mainData.code }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="评价对象类型" prop="evaluationObjectType">
            <el-input v-if="!isView" v-model.trim="mainData.evaluationObjectType" :disabled="true" show-word-limit
              style="width: 100%" placeholder="评价对象类型不可修改" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="报告年度" prop="evaluationYear">
            <el-input v-if="!isView" v-model.trim="mainData.evaluationYear" :disabled="true" show-word-limit
              style="width: 100%" placeholder="年份不可修改" />
            <span v-else class="viewSpan">{{ mainData.evaluationYear }}</span>
          </el-form-item>
        </el-col>
        <el-col>
          <el-col :span="16">
            <el-form-item label="评价对象" prop="evaluationObject">
              <el-input v-if="!isView" v-model="mainData.evaluationObject" placeholder="请选择" class="input-with-select"
                disabled>
                <el-button slot="append" icon="el-icon-search" @click="showOrgTreeDialog" />
              </el-input>
              <span v-else class="viewSpan">{{ mainData.evaluationObject }}</span>
            </el-form-item>
          </el-col>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="评价结果" prop="evaluationConclusion">
            <span slot="label">评价结果</span>
            <el-input v-if="!isView" v-model="mainData.evaluationConclusion" :autosize="{ minRows: 10, maxRows: 20 }"
              type="textarea" placeholder="请输入评价结果" maxlength="2000" show-word-limit />
            <text-span v-else class="viewSpan" :text="mainData.evaluationConclusion" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="附件材料">
              <UploadDoc :files.sync="mainData.uploadedAttachment" doc-path="/case" :disabled="isView" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-row>

      <!-- 弹窗组件 -->
      <el-dialog :close-on-click-modal="false" title="选择合规评价通知" :visible.sync="dialogVisible2" width="60%">
        <HgpjtzIndex @addBtn="handleAdd" @editBtn="handleEdit" @viewBtn="handleView" @deleteBtn="handleDelete"
          @rowDblclick="handleRowDblclick" :evaluation-object-type="mainData.evaluationObjectType" />
        <span slot="footer" class="">
          <el-button @click="dialogVisible2 = false">取消</el-button>
        </span>
      </el-dialog>

      <!-- el-dialog 组件 -->
      <el-dialog :close-on-click-modal="false" title="选择单位/部门" :visible.sync="dialogVisible" width="50%">
        <div class="el-dialog-div">
          <orgTree :accordion="false" :is-checked-user="false" :show-user="showUserValue" :is-check="false"
            :checked-data.sync="zxcheckedData" :is-not-cascade="true" :is-filter="true" />
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button icon="" class="negative-btn" @click="cancel">取消</el-button>
          <el-button type="primary" icon="" class="active-btn" @click="choiceDeptSure">确定</el-button>
        </span>
      </el-dialog>
    </div>
    <!-- 查看 -->
    <div v-else>
      <SimpleBoardTitle title="基本信息">
        <table class="table_content">
          <tbody>
            <tr>
              <th colspan="3" class="th_label">合规评价通知编码</th>
              <td colspan="10" class="td_value">{{ mainData.complianceNotificationCode }}</td>
              <th colspan="3" class="th_label">编码</th>
              <td colspan="10" class="td_value">{{ mainData.code }}</td>
              <th colspan="3" class="th_label">评价对象类型</th>
              <td colspan="10" class="td_value">{{ mainData.evaluationObjectType }}</td>
            </tr>
            <tr>
              <th colspan="3" class="th_label">评价对象</th>
              <td colspan="10" class="td_value">{{ mainData.evaluationObject }}</td>
              <th colspan="3" class="th_label">评价年度</th>
              <td colspan="23" class="td_value">{{ mainData.evaluationYear }}</td>
            </tr>
            <tr>
              <th colspan="3" class="th_label">评价结果</th>
              <td colspan="36" class="td_value">{{ mainData.evaluationConclusion }}</td>
            </tr>
            <tr>
              <th colspan="3" class="th_label">附件材料</th>
              <td colspan="36" class="td_value">
                <UploadDoc :files.sync="mainData.uploadedAttachment" doc-path="/case" :disabled="isView" />
              </td>
            </tr>
          </tbody>
        </table>
      </SimpleBoardTitle>
    </div>
  </div>
</template>

<script>
import dictApi from '@/api/_system/dict'
import AreaSelect from '@/view/components/AreaSelect/AreaSelect'
import OrgSingleDialogSelect from '@/view/components/OrgSingleDialogSelect/index.vue'
import UploadDoc from '@/view/components/UploadDoc/UploadDoc.vue'
import orgTree from '@/view/components/OrgTree/OrgTree'
import SimpleBoardTitle from "@/view/components/SimpleBoard/SimpleBoardTitle"
import SimpleBoardTitleApproval from "@/view/components/SimpleBoard/SimpleBoardTitleApproval"
import money from "@/view/components/Money/index"
import OrgLeader from '@/view/components/OrgLeader/OrgLeader'
import HgpjtzIndex from './child/HgpjtzIndex.vue'

export default {
  name: 'QsBaseInfo',
  components: {
    OrgSingleDialogSelect, AreaSelect, UploadDoc, orgTree, SimpleBoardTitleApproval, money,
    SimpleBoardTitle, OrgLeader, HgpjtzIndex
  },
  props: {
    data: {
      type: Object,
      default: function () {
        return {}
      }
    },
    dataState: {
      type: String,
      default: ''
    },
    view: {
      type: String,
      default: 'new'
    },
    create: {
      type: String,
      default: ''
    },
    authorizationData: {
      type: Object,
      default: function () {
        return {}
      }
    },
  },
  data() {
    return {
      orgDialogTitle: '组织信息',
      caseDialogVisible: false,
      mainData: this.data,
      dicTreeDialogVisible: false,
      dicTreeDialogVisible2: false,
      dicTreeDialogVisible3: false,
      orgTreeDialog: false,
      caseNatures: [],
      plateData: [],
      causeOfIns: [],
      applications: [],
      involvedLevelData: [],//涉案单位管理层级
      suedUnitTypeData: [], //被诉单位性质
      unitTypeData: [],//单位类型
      reportCategoryData: [],//报告类别
      zxcheckedData: [],
      dialogVisible: false,
      orgTreeDialog: false,
      orgVisible: false,
      tableQuery: {
        reportYear: '',
      },
      yearOptions: Array.from({ length: 10 }, (_, i) => new Date().getFullYear() - i),
      dialogVisible2: false, // 弹窗可见性
      selectedNotification: null, // 选中的合规评价通知数据
    }
  },
  computed: {
    isView: function () {
      return this.dataState === this.utils.formState.VIEW
    },
    causeOfInIds: {
      set: function (data) {
        this.mainData.causeOfInId = data.join(',')
      },
      get: function () {
        if (this.mainData.causeOfInId) {
          return this.mainData.causeOfInId.split(',')
        }
        return []
      }
    },
    isCreate: function () {
      return this.create === 'create'
    },
    showUserValue() {
      return this.mainData.evaluationObjectType === '合规管理员';
    }
  },
  watch: {
    mainData: {
      handler(val, oldVal) {
        this.$emit('update:data', val)
      },
      deep: true
    },
    data(val) {
      this.mainData = Object.assign(this.mainData, val)
    },
    'mainData.involvedAmount': {
      handler(val, oldVal) {
        this.involvedAmountChange(val)
      },
      deep: true, immediate: true
    },
    'mainData.caseInterest': {
      handler(val, oldVal) {
        this.caseInterestChange(val)
      },
      deep: true, immediate: true
    }
  },
  created() {
    this.initDic()
    window.vm = this;
  },
  methods: {

    reportCategoryChange(val) {
      this.mainData.reportCategory = this.utils.getDicName(this.reportCategoryData, val)
    },
    venueIsOutOnChange(val) {
      this.mainData.venueAddress = null
      this.mainData.venueProvince = null
      this.mainData.venueCity = null
      this.mainData.venueRegion = null
    },
    sureBtn(data) {
      this.mainData.caseUndertakerId = data.unitId
      this.mainData.caseUndertaker = data.name
    },
    sureBtn2(data) {
      this.mainData.ognPackageId = data.unitId
      this.mainData.ognPackage = data.name
    },
    sureBtn3(data) {
      this.mainData.groupPackageId = data.unitId
      this.mainData.groupPackage = data.name
    },
    orgSelect(data) {
      this.mainData.currentUnitId = data.unitId
      this.mainData.currentUnit = data.name

      this.mainData.riskDepartmentId = data.unitId
      this.mainData.riskDepartment = data.name
    },
    moneyFocus(event) {
      event.currentTarget.select()
    },
    chooseApprovalDeptClick() {
      this.isCheckedUser = false
      this.showUser = false
      this.orgVisible = true
      this.is_Check = false
    },

    queryOtherPartys() {
      let partys = this.mainData.partiesList
      let otherPartys = ""

      if (partys.length > 0) {
        for (let i = 0; i < partys.length; i++) {
          if (partys[i].partyType !== '原告' && partys[i].party !== null && partys[i].party !== undefined)
            otherPartys += partys[i].party + ","
        }

        if (otherPartys !== "")
          otherPartys = otherPartys.slice(0, -1)
      }

      return otherPartys
    },
    showOrgTreeDialog() {
      this.dialogVisible = true;
    },
    cancel() {
      this.dialogVisible = false
    },
    choiceDeptSure() {
      let selectedUnits = this.zxcheckedData.map(item => item.name).join(', ');
      this.mainData.evaluationObject = selectedUnits;
      this.dialogVisible = false;
    },
    showNotificationDialog() {
      this.dialogVisible2 = true;
    },
    handleRowDblclick(row) {
      this.mainData.complianceNotificationCode = row.code; // 假设 row.code 是合规评价通知的编码字段
      this.dialogVisible2 = false;
    },
    handleAdd() {
      // 处理添加事件
    },
    handleEdit() {
      // 处理编辑事件
    },
    handleView() {
      // 处理查看事件
    },
    handleDelete() {
      // 处理删除事件
    },
  }
}
</script>

<style lang="scss" scoped>
.hideContent {

  .el-input__inner,
  .el-radio.is-bordered,
  .el-textarea__inner,
  .el-input__count {
    background-color: #f9e8bb;
  }
}

.money-label-width .el-form-item__label {
  width: 150px;
  /* 指定宽度 */
}

/* 过于长的label分两行展示样式 */
/deep/.fold_label .el-form-item__label {
  white-space: pre-line;
  /* text-align-last: justify;
  text-align: justify;*/
  margin-top: -4px;
  line-height: 25px;
  text-justify: distribute-all-lines;
}
</style>
