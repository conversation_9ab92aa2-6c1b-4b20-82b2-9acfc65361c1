
ccec4c21494ef64231bb6f285f3c935048283788	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.215.1754018536329.js\",\"contentHash\":\"e701f014badb94a9113a31e236b9ea5a\"}","integrity":"sha512-vtbT/ASI8B4a5TQ0ArmNVEEPUDLzAgwdt7JNa2/s6BjS4nuLhd9tX83OuX0Tn75apy41q6zziL5Iet/b9yByhw==","time":1754018575990,"size":159568}