
8d7bb18fbc7060edf669642d83f637fe788c3977	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.73.1754018536329.js\",\"contentHash\":\"bc855bd793b2c7c70edbbf18651ae0ba\"}","integrity":"sha512-a2wNS7+ZBV2nFTeYSOzU5PBc23FuEt4Wq+a5opCMeB1Z1F/UVb0ZAOViPBMmeDBubiO8BjNUGXnWronHEkBwTA==","time":1754018575959,"size":94268}