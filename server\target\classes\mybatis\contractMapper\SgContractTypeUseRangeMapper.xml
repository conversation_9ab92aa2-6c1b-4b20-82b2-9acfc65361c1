<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.klaw.dao.contractDao.SgContractTypeUseRangeMapper">
  <resultMap id="BaseResultMap" type="com.klaw.entity.contractBean.SgContractTypeUseRange">
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="PARENT_ID" jdbcType="VARCHAR" property="parentId" />
    <result column="USE_RANGE_NAME" jdbcType="VARCHAR" property="useRangeName" />
    <result column="USE_RANGE_ID" jdbcType="VARCHAR" property="useRangeId" />
    <result column="USE_RANGE_FULL_ID" jdbcType="VARCHAR" property="useRangeFullId" />
    <result column="USE_RANGE_FULL_NAME" jdbcType="VARCHAR" property="useRangeFullName" />
    <result column="CREATE_OGN_ID" jdbcType="VARCHAR" property="createOgnId" />
    <result column="CREATE_OGN_NAME" jdbcType="VARCHAR" property="createOgnName" />
    <result column="CREATE_DEPT_ID" jdbcType="VARCHAR" property="createDeptId" />
    <result column="CREATE_DEPT_NAME" jdbcType="VARCHAR" property="createDeptName" />
    <result column="CREATE_GROUP_ID" jdbcType="VARCHAR" property="createGroupId" />
    <result column="CREATE_GROUP_NAME" jdbcType="VARCHAR" property="createGroupName" />
    <result column="CREATE_PSN_ID" jdbcType="VARCHAR" property="createPsnId" />
    <result column="CREATE_PSN_NAME" jdbcType="VARCHAR" property="createPsnName" />
    <result column="CREATE_ORG_ID" jdbcType="VARCHAR" property="createOrgId" />
    <result column="CREATE_ORG_NAME" jdbcType="VARCHAR" property="createOrgName" />
    <result column="CREATE_PSN_FULL_ID" jdbcType="VARCHAR" property="createPsnFullId" />
    <result column="CREATE_PSN_FULL_NAME" jdbcType="VARCHAR" property="createPsnFullName" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="DATA_STATE" jdbcType="VARCHAR" property="dataState" />
    <result column="DATA_STATE_CODE" jdbcType="DECIMAL" property="dataStateCode" />
  </resultMap>
  <sql id="Base_Column_List">
    ID, PARENT_ID, USE_RANGE_NAME, USE_RANGE_ID, USE_RANGE_FULL_ID, USE_RANGE_FULL_NAME, 
    CREATE_OGN_ID, CREATE_OGN_NAME, CREATE_DEPT_ID, CREATE_DEPT_NAME, CREATE_GROUP_ID, 
    CREATE_GROUP_NAME, CREATE_PSN_ID, CREATE_PSN_NAME, CREATE_ORG_ID, CREATE_ORG_NAME, 
    CREATE_PSN_FULL_ID, CREATE_PSN_FULL_NAME, CREATE_TIME, UPDATE_TIME, DATA_STATE, DATA_STATE_CODE
  </sql>

  <select id="queryUseRangeCount" resultType="java.util.Map">
    select parentId from (
      select m.id as parentId,count(d.parent_id) as num
      from SG_CONTRACT_TYPE m left join
      (select parent_id from sg_contract_type_use_range where use_range_id = #{useRangeId}) d
      on m.id = d.parent_id
      <where>
        ${ew.sqlSegment}
      </where>
      group by m.id
    ) A where num = 0
  </select>
</mapper>