
ef2aecad1bdc33849498ce22f6731ef9d73a7e95	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.133.1754018536329.js\",\"contentHash\":\"5bcc91fcb246facf154cc0c718f4f8c4\"}","integrity":"sha512-UKzfxIV5kzgO4TlBMq6YQFIY4G0q3oZ6eVYMx4oAqR6lt/3xeMsjQf/8/VanY007e1Uaxl1z00hBlYSUibVCFg==","time":1754018575980,"size":177045}