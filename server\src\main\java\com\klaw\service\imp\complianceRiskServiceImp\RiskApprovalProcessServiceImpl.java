package com.klaw.service.imp.complianceRiskServiceImp;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.klaw.dao.complianceRiskDao.RiskApprovalProcessMapper;
import com.klaw.entity.complianceRiskBean.RiskApprovalProcessEntity;
import com.klaw.service.complianceRiskService.RiskApprovalProcessService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.klaw.utils.PageUtils;
import com.klaw.utils.StringUtil;
import com.klaw.vo.Json;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

@Service
public class RiskApprovalProcessServiceImpl extends ServiceImpl<RiskApprovalProcessMapper, RiskApprovalProcessEntity> implements RiskApprovalProcessService {

    @Override
    public void saveData(RiskApprovalProcessEntity riskApprovalProcessEntity) {
        riskApprovalProcessEntity.setUpdateTime(new Date());
        saveOrUpdate(riskApprovalProcessEntity);
    }

    @Override
    public Page<RiskApprovalProcessEntity> queryPageData(JSONObject json) {
        QueryWrapper<RiskApprovalProcessEntity> wrapper = new QueryWrapper<>();
        getFilter(json, wrapper);
        return page(new PageUtils<>(json), wrapper);
    }



    public void getFilter(JSONObject json, QueryWrapper<RiskApprovalProcessEntity> wrapper) {
        //流程标题
        String processTitle = json.containsKey("processTitle") ? json.getString("processTitle") : null;

        // 模糊搜索值
        String fuzzyValue = json.containsKey("fuzzyValue") ? json.getString("fuzzyValue") : null;
        // 排序字段
        String sortName = json.containsKey("sortName") ? json.getString("sortName") : null;
        //当前人任职信息id
        String orgId = StringUtil.emptyStr(json.getString("orgId"));

        // 排序字段是否升序
        boolean order = json.containsKey("order") ? json.getBoolean("order") : false;

        if (StringUtils.isNotBlank(fuzzyValue)) {
            wrapper.and(i -> i.like("process_title", fuzzyValue));
        }
        // 应用查询条件
        if (StringUtils.isNotBlank(processTitle)) {
            wrapper.and(i -> i.like("process_title", processTitle));
        }
        if (!orgId.isEmpty()){
            wrapper.eq("create_org_id",orgId);
        }

        if (StringUtils.isNotBlank(sortName) && order) {
            wrapper.orderByAsc(sortName);
        } else if (StringUtils.isNotBlank(sortName)) {
            wrapper.orderByDesc(sortName);
        }
        //按创建时间倒序显示
        wrapper.orderByDesc("create_time");
    }


    @Override
    public RiskApprovalProcessEntity queryDataById(String id) {
        RiskApprovalProcessEntity riskApprovalProcessEntity = getById(id);
        return riskApprovalProcessEntity;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Json deleteDataById(String id) {
        boolean flag = removeById(id);
        if (!flag) {
            return Json.fail().msg("未找到需要删除的数据");
        }
        return Json.succ().msg("删除成功!");
    }
}

