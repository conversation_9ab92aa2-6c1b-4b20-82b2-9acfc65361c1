import {request} from '@/api/index'

export default {

    query(data) {
        return request({
            url: '/CommonQuestion/query',
            method: 'post',
            data
        })
    },

    save(data) {
        return request({
            url: '/CommonQuestion/save',
            method: 'post',
            data
        })
    },

    queryById(data) {
        return request({
            url: '/CommonQuestion/query_by_id',
            method: 'post',
            data: {
                id: data
            }
        })
    },
    deletebyid(data) {
        return request({
            url: '/CommonQuestion/deletebyid',
            method: 'post',
            data

        })
    },

    setParam(data) {
        return request({
            url: '/CommonQuestion/setParam',
            method: 'post',
            data
        })
    }
}

