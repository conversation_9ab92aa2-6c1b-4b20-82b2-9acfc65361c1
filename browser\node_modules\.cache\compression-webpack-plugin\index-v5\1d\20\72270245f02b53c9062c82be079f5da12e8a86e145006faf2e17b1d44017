
1171cac4f750f24d74e747e18024535bc492a3e3	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.4.1754018536329.js\",\"contentHash\":\"3918b1e945553574db966158e2032443\"}","integrity":"sha512-vpHKYKWChqoXIBPSZ8CgO3y8trqBAKpkpys9fWrCMTEtSuEb3pPuOkHaBxOO0loaL4u9rAADxH67K6dwFIoefw==","time":1754018575949,"size":42358}