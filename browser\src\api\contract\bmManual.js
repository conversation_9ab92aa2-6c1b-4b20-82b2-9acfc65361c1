import {request} from '@/api/index'

export default {
    query(data) {
        return request({
            url: '/bm-manual/query',
            method: 'post',
            data
        })
    },
    queryList(data) {
        return request({
            url: '/bm-manual/queryList',
            method: 'post'
        })
    },
    save(data) {
        return request({
            url: '/bm-manual/save',
            method: 'post',
            data
        })
    },
    stop(data) {
        return request({
            url: '/bm-manual/save',
            method: 'post',
            data
        })
    },
}