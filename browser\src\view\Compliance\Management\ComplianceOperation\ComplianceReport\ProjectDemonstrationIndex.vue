<template>
  <el-container direction="vertical" class="container-manage-sg">
    <el-header>
      <el-card>
        <div>
          <el-input v-model="tableQuery.fuzzyValue" class="filter_input" placeholder="检索字段（项目名称、项目编码）" clearable
            @keyup.enter.native="refreshData" @clear="refreshData">
            <el-popover disabled slot="prepend" placement="bottom-start" width="1000" trigger="click">
              <el-button slot="reference" size="small" type="primary">高级检索</el-button>
            </el-popover>

            <el-button slot="append" icon="el-icon-search" @click="search_" />
          </el-input>

        </div>
      </el-card>
    </el-header>
    <el-main>
      <SimpleBoardIndex :title="'重大项目初步论证列表'">
        <template slot="button">
          <el-button type="primary" class="normal-btn" size="mini" @click="add_">新增</el-button>
        </template>
        <el-table ref="table" v-loading="tableLoading" :data="tableData" size="mini" border :height="table_height"
          stripe highlight-current-row :show-overflow-tooltip="true" row-key="id"
          style="table-layout: fixed;width: 100%;" @row-dblclick="rowDblclick">
          <el-table-column type="index" width="50" label="序号" align="center" />
          <el-table-column prop="projectName" show-overflow-tooltip label="项目名称" min-width="130" />
          <el-table-column prop="projectNumber" show-overflow-tooltip label="项目编码" min-width="100" />
          <el-table-column prop="involvedAmount" show-overflow-tooltip label="涉及金额(元)" min-width="80" />
          <el-table-column prop="createTime" show-overflow-tooltip label="提交日期" min-width="150" />
          <el-table-column prop="createLegalUnitName" show-overflow-tooltip label="提交单位" min-width="180" />
          <el-table-column prop="createPsnName" show-overflow-tooltip label="提交人" min-width="80" />
          <el-table-column prop="examineType" show-overflow-tooltip label="审核状态" min-width="100" />
          <el-table-column label="操作" align="center" width="150" fixed="right">
            <template slot-scope="scope">
              <el-button type="text" v-if="isEdit(scope.row)" @click="edit_(scope.$index, scope.row)">编辑</el-button>
              <el-button type="text" @click="view_(scope.$index, scope.row)">查看</el-button>
              <el-button type="text" v-if="isDelete(scope.row)" @click="delete_(scope.$index, scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </SimpleBoardIndex>

    </el-main>
    <el-footer>
      <!--分页工具栏-->
      <pagination :total="tableQuery.total" :page.sync="tableQuery.page" :limit.sync="tableQuery.limit"
        @pagination="refreshData" />
    </el-footer>

  </el-container>
</template>

<script>
// 组件
import pagination from '@/view/components/Pagination/PaginationIndex'
import TableTools from '@/view/components/TableTools/index'
import SimpleBoardIndex from "@/view/components/SimpleBoard/SimpleBoardIndex";
import pd from '@/api/projectDemonstration/projectDemonstration.js'
import taskApi from '@/api/_system/task'
// vuex状态值
import { mapGetters } from 'vuex'
import orgTree from '@/view/components/OrgTree/OrgTree';

export default {
  name: 'ProjectDemonstrationIndex',
  inject: ["layout"],
  components: { pagination, TableTools, SimpleBoardIndex, orgTree },
  data() {
    return {
      tableLoading: false,
      tableData: [],
      table_height: '100%',
      tableQuery: {
        fuzzyValue: null,
        total: 0,
        page: 1,
        limit: 10,
      }
    }
  },
  computed: {
    ...mapGetters([
      'orgContext', 'currentFunctionId'
    ])
  },
  activated() {
    // 长连接页面第二次激活的时候,不会走created方法,会走此方法
    this.refreshData()
  },
  created() {
    this.refreshData()
  },

  mounted() {
    this.$nextTick(function () {
      this.table_height = window.innerHeight - this.$refs.table.$el.offsetTop - 84 - 45

      // 监听窗口大小变化
      const self = this
      window.onresize = function () {
        self.table_height = window.innerHeight - self.$refs.table.$el.offsetTop - 84 - 45
      }
    })
  },
  methods: {
    isEdit(row) {
      if (row.examineType === "" || row.examineType === undefined || row.examineType === null){
        return true
      } else {
        return false
      }
       
      },
      isDelete(row) {
        if(row.examineType === "" || row.examineType === undefined || row.examineType === null){
          return true
        } else {
          return false
        }
        
      },
    add_: function () {
      const tabId = this.utils.createUUID();
      //新页面添加
      this.layout.openNewTab(
        //此时页面没有，路径需要修改下面同理
        "重大项目初步审核",
        "PROJECT_DEMONSTRATION_DETAIL",
        "PROJECT_DEMONSTRATION_DETAIL",
        tabId,
        {
          functionId: "PROJECT_DEMONSTRATION_DETAIL," + tabId,
          ...this.utils.routeState.NEW(tabId)
        }
      );
    },
    //查看
    view_(index, row) {
      if (row.dataStateCode === this.utils.dataState_BPM.SAVE.code) {
        const tabId = row.id;
        this.layout.openNewTab(
          "重大项目初步审核",
          "PROJECT_DEMONSTRATION_DETAIL",
          "PROJECT_DEMONSTRATION_DETAIL",
          tabId,
          {
            functionId: "PROJECT_DEMONSTRATION_DETAIL," + tabId,
            ...this.utils.routeState.VIEW(tabId),
            view: 'old'
          }
        )
      } else {
        taskApi.selectTaskId({ businessKey: row.id, isView: 'true' }).then(res => {
          const functionId = res.data.data[0].ID
          const tabId = this.utils.createUUID()
          this.layout.openNewTab("重大项目初步论证",
            "design_page",
            "design_page",
            tabId,
            {
              processInstanceId: res.data.data[0].PID,//流程实例
              taskId: res.data.data[0].ID,//任务ID
              businessKey: row.id, //业务数据ID
              functionId: functionId, //不传也可以，只有首环节需要传，实例启动就可以不传了
              entranceType: "FLOWABLE",
              type: "haveDealt",
              channel: 'business', //查看渠道，默认就传这个值即可，表示是从业务功能传过来的，首环节提交后过滤掉撤回按钮
              view: 'new',
            }
          )
        })
      }
      
    },
    // 编辑
    edit_(index, row) {
      const tabId = this.utils.createUUID();
      this.layout.openNewTab(
        "重大项目初步审核",
        "PROJECT_DEMONSTRATION_DETAIL",
        "PROJECT_DEMONSTRATION_DETAIL",
        row.id,
        {
          functionId: "PROJECT_DEMONSTRATION_DETAIL," + row.id,
          ...this.utils.routeState.EDIT(row.id),
          view: 'old'
        }
      );
    },
    // 删除
    delete_(index, row) {
      this.$confirm('此操作将永久删除该数据及相关数据, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        new Promise((resolve, reject) => {
          pd.delete(row.id).then((response) => {
            resolve(response)
          })
        }).then(value => {
          this.tableData.splice(index, 1)
          this.$message.success('删除成功!')
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },
    search_: function () {
      this.tableQuery.page = 1
      this.refreshData()
    },
    rowDblclick(row) {
      view_(1,row);
    },
    refreshData() {
      if (this.tableData.length === 0 && this.tableQuery.page > 1) {
        this.tableQuery.page--
      }
      this.tableQuery.orgId = this.orgContext.currentOrgId
      pd.query(this.tableQuery).then(response => {
        let rows = response.data.data.records
        this.tableData = rows
        this.tableQuery.total = response.data.data.total
        this.tableLoading = false
      })
    }
  }
}
</script>

<style scoped>
.el-table__fixed-body-wrapper {
  top: 50px !important;
}
</style>