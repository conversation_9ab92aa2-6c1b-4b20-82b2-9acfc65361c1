import {request} from '@/api/index'

export default {
    contractType(data1,data2){
        return request({
            url:"/bm-contract/contractType/" + data1 + "/" + data2,
            method: 'get'
        })
    },
    countOtherSignNum(data1,data2){
        return request({
            url:"/bm-contract/countOtherSignNum/" + data1 + "/" + data2,
            method: 'get'
        })
    },
    standardSignNum(data1,data2){
        return request({
            url:"/bm-contract/standardSignNum/" + data1 + "/" + data2,
            method: 'get'
        })
    },
    countEffectNumber(data1,data2){
        return request({
            url:"/bm-contract/countEffectNum/" + data1 + "/" + data2,
            method: 'get'
        })
    },
    query(data) {
        return request({
            url: '/bm-contract/query',
            method: 'post',
            data
        })
    },
    queryLeft() {
        return request({
            url: '/bm-contract/queryLeft',
            method: 'post'
        })
    },
    queryRisk(data) {
        return request({
            url: '/bm-contract/queryRisk',
            method: 'post',
            data
        })
    },
    save(data) {
        return request({
            url: '/bm-contract/save',
            method: 'post',
            data
        })
    },
    saveComplianceFiles(data) {
        return request({
            url: '/bm-contract/saveComplianceFiles',
            method: 'post',
            data
        })
    },
    delete(data) {
        return request({
            url: '/bm-contract/delete',
            method: 'post',
            data
        })
    },
    queryById(data) {
        return request({
            url: '/bm-contract/queryById',
            method: 'post',
            data
        })
    },
    queryProjectById(data) {
        return request({
            url: '/bm-contract/queryProjectById',
            method: 'post',
            data
        })
    },
    queryPlaceById(data) {
        return request({
            url: '/bm-contract/queryPlaceById',
            method: 'post',
            data
        })
    },
    querySimpleById(data) {
        return request({
            url: '/bm-contract/querySimpleById',
            method: 'post',
            data
        })
    },
    queryTaskById(data) {
        return request({
            url: '/bm-contract/queryTaskById',
            method: 'post',
            data
        })
    },
    setParam(data) {
        return request({
            url: '/bm-contract/setParam',
            method: 'post',
            data
        })
    },
    queryDialog(data) {
        return request({
            url: '/bm-contract/queryDialog',
            method: 'post',
            data
        })
    },
    changeContractPerformState(data) {
        return request({
            url: '/bm-contract/changeContractPerformState',
            method: 'post',
            data
        })
    },

    takeEffectContract(data) {
        return request({
            url: '/contractAppSupple/takeEffectContract',
            method: 'post',
            data
        })
    },
    queryForSuppleDialog(data) {
        return request({
            url: '/contractAppSupple/queryForSuppleDialog',
            method: 'post',
            data
        })
    },
    exportImportSuppleData(data) {
        return request({
            url: '/contractAppSupple/exportImportSuppleData',
            method: 'post',
            data,
            responseType: 'blob'
        })
    },

    doGetWorkflowList(data) {
        return request({
            url: '/bm-contract/queryAssetList',
            method: 'post',
            data
        })
    },
    queryOwnerName(data) {
        return request({
            url: '/bm-contract/queryOwnerName',
            method: 'post',
            data
        })
    },
    queryRiskHisData(data) {
        return request({
            url: '/bm-contract/queryRiskHisData',
            method: 'post',
            data
        })
    },
    queryPlace(data) {
        return request({
            url: '/bm-contract/queryPlace',
            method: 'post',
            data
        })
    },
    getWinbidinfo(data) {
        return request({
            url: '/winbidinfo/query',
            method: 'post',
            data
        })
    },
    getEPDeListById(data) {
        return request({
            url: '/winbiddetailinfo/getEPDeListById',
            method: 'post',
            data
        })
    },
    saveContractFileMain(data) {
        return request({
            url: '/bm-contract/saveContractFileMain',
            method: 'post',
            data
        })
    },
    multipleSendContract(data) {
        return request({
            url: '/contractToMain/multipleSendContract',
            method: 'post',
            data
        })
    },
    exportContractLedger(data) {
        return request({
            url: '/bm-contract/exportContractLedger',
            method: 'post',
            responseType: 'blob',
            data
        })
    },
    exportRiskLedger(data) {
        return request({
            url: '/bm-contract/exportRiskLedger',
            method: 'post',
            responseType: 'blob',
            data
        })
    },
    cancelSubmit(data) {
        return request({
            url: '/bm-contract/cancelSubmit',
            method: 'post',
            data
        })
    },
}
