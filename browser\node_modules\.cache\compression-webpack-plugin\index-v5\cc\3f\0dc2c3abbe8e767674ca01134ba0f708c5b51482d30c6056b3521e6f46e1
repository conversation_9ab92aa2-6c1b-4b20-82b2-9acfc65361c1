
b529cf6696d01382a6a941081b32f7f96c51a37f	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.419.1754018536329.js\",\"contentHash\":\"e93020c5475dd35950893eb487d32143\"}","integrity":"sha512-WJAzqmO6xQ/PUBkKUQJJQRIOzUJdGFxGtwjlqFoVbcBBErBG+SM3xrybd+oXxfSLBN9g5PJGcR+kr6XFO+r5Iw==","time":1754018575957,"size":32305}