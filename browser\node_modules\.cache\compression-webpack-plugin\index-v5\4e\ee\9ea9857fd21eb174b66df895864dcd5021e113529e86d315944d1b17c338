
2c20d0da43c659499a8b0a2269873f18035bb51e	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.35.1754018536329.js\",\"contentHash\":\"1502bf71ee99fd40d96793bfb553dc90\"}","integrity":"sha512-yP7c+ionMIhGo7DqDTUWIgNVrYGVmMZU/LcrGzIF+zf+yOC2Q7VADiqhdjmtpmtCUrpCh861/A3FaKok+Rk1Cw==","time":1754018575955,"size":47692}