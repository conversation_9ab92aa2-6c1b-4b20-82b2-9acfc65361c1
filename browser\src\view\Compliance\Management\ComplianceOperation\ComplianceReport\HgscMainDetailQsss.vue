<template>
  <FormWindow ref="formWindow" @initData="initData" @loadData="loadData">
    <el-container v-loading="loading" :element-loading-text="loadingText" style="height: calc(100vh - 84px)">
      <el-main>
        <el-scrollbar style="height: 100%" wrap-style="overflow-x: hidden;">
          <!--数据表单块-->
          <el-form ref="dataForm" :model="mainData" :rules="!isView ? rules : {}" label-width="100px"
            style="margin-right: 10px;">
            <el-row
              style="padding: 10px 0 10px 0; position: fixed; width: 100%; overflow-y: auto; background-color: white; z-index: 999">
              <el-button
                v-if="(!isView) && (!(mainData.reviewCategory === 'HG-SCLX-HTHG' && mainData.relatedSignificantReview))"
                :disabled="mainData.reviewCategory === 'HG-SCLX-HTHG' && mainData.relatedSignificantReview"
                type="primary" size="mini" @click="save_"> 保存
              </el-button>
              <el-button v-if="!isView" size="mini" type="success" @click="approval_">生成审批单</el-button>
            </el-row>
            <div style="padding-top: 50px"></div>
            <span style="text-align: left; font-size: 23px; margin-left: 43%; font-weight: 900">{{
              this.title
            }}</span>
            <!--基础信息块-->
            <div v-if="dataState !== 'view' && this.mainData.reviewCategory === 'HG-SCLX-QSSX'">
              <div style="padding-left: 10px; margin-bottom: 20px; font-size: 15px; color: red; font-weight: bolder">
              </div>
              <div style="margin: 10px">
                <span style="font-weight: bold; font-size: 16px; color: #5a5a5f">基本信息</span>
                <el-divider></el-divider>
              </div>
              <el-row style="margin-top: 10px">
                <el-col :span="16">
                  <el-form-item label="事项题目" prop="reviewSubject" >
                    <el-input v-if="!isView" v-model="mainData.reviewSubject" clearable maxlength="100"
                      placeholder="请输入..." show-word-limit />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="事项编号" prop="reviewNumber">
                    <el-input v-if="!isView" v-model="mainData.reviewNumber" clearable disabled maxlength="100"
                      placeholder="后台自动生成" show-word-limit />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="16">
                  <el-form-item label="业务领域" prop="businessArea" >
                    <el-select v-model="mainData.businessArea" clearable placeholder="请选择" style="width: 100%">
                      <el-option v-for="item in businessAreaData" :key="item.dicName" :label="item.dicName"
                        :value="item.dicName" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col v-if="mainData.businessArea === '其他'" :span="8">
                  <el-form-item label="业务领域" prop="otherBusinessArea" >
                    <el-input v-model="mainData.otherBusinessArea" clearable placeholder="请输入业务领域..."></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="24">
                  <el-form-item label="事项简介" prop="matterDescription">
                    <span slot="label">事项简介</span>
                    <el-input v-if="!isView" v-model="mainData.matterDescription"
                      :autosize="{ minRows: 3, maxRows: 20 }" maxlength="500" placeholder="请输入事项简介" show-word-limit
                      type="textarea" />
                    <text-span v-else :text="mainData.matterDescription" class="viewSpan" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="24">
                  <el-form-item class="custom-word-break" label="请示正文" prop="requestText" >
                    <span slot="label">请示正文</span>
                    <UploadDoc :disabled="isView" :files.sync="mainData.requestText" doc-path="/case" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="24">
                  <el-form-item class="custom-word-break" label="相关附件" prop="reviewMaterials" >
                    <span slot="label">相关附件</span>
                    <UploadDoc :disabled="isView" :files.sync="mainData.reviewMaterials" doc-path="/case" />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
            <!-- 查看时的判断 -->
            <div v-if="dataState === 'view' && this.mainData.reviewCategory === 'HG-SCLX-QSSX'">
              <SimpleBoardTitle style="margin-top: 5px" title="基本信息">
                <table class="table_content" style="margin-top: 10px">
                  <tbody>
                    <tr>
                      <th class="th_label" colspan="3">事项题目</th>
                      <td class="td_value" colspan="13">{{ mainData.reviewSubject }}</td>
                      <th class="th_label" colspan="3">事项编号</th>
                      <td class="td_value" colspan="5">{{ mainData.reviewNumber }}</td>
                    </tr>
                    <tr>
                      <th class="th_label" colspan="3">业务领域</th>
                      <td v-if="mainData.businessArea !== '其他'" class="td_value" colspan="21">
                        {{ mainData.businessArea }}
                      </td>
                      <template v-else>
                      <td class="td_value" colspan="9">{{ mainData.businessArea }}</td>
                      <td class="td_value" colspan="9">{{ mainData.otherBusinessArea }}</td>
                    </template>

                    </tr>
                    <tr>
                      <th class="th_label" colspan="3">事项简介</th>
                      <td class="td_value" colspan="21">{{ mainData.matterDescription }}</td>
                    </tr>
                    <tr>
                      <th class="th_label" colspan="3">请示正文</th>
                      <td class="td_value" colspan="21">
                        <UploadDoc :disabled="isView" :files.sync="mainData.requestText" doc-path="/case" />
                      </td>
                    </tr>
                    <tr>
                      <th class="th_label" colspan="3">相关附件</th>
                      <td class="td_value" colspan="21">
                        <UploadDoc :disabled="isView" :files.sync="mainData.reviewMaterials" doc-path="/case" />
                      </td>
                    </tr>
                  </tbody>
                </table>
              </SimpleBoardTitle>
            </div>
            <div v-if="dataState === 'view' && this.mainData.reviewCategory === 'HG-SCLX-QSSX'">
              <SimpleBoardTitle style="margin-top: 5px" title="基本信息">
                <table class="table_content" style="margin-top: 10px">
                  <tbody>
                    <tr>
                      <th class="th_label" colspan="3">事项题目</th>
                      <td class="td_value" colspan="13">{{ mainData.reviewSubject }}</td>
                      <th class="th_label" colspan="3">事项编号</th>
                      <td class="td_value" colspan="5">{{ mainData.reviewNumber }}</td>
                    </tr>
                    <tr>
                      <th class="th_label" colspan="3">业务领域</th>
                      <td v-if="mainData.businessArea !== '其他'" class="td_value" colspan="21">
                        {{ mainData.businessArea }}
                      </td>
                      <td v-else class="td_value" colspan="21">{{ mainData.otherBusinessArea }}</td>
                    </tr>
                    <tr>
                      <th class="th_label" colspan="3">事项简介</th>
                      <td class="td_value" colspan="21">{{ mainData.matterDescription }}</td>
                    </tr>
                    <tr>
                      <th class="th_label" colspan="3">附件材料</th>
                      <td class="td_value" colspan="21">
                        <UploadDoc :disabled="isView" :files.sync="mainData.reviewMaterials" doc-path="/case" />
                      </td>
                    </tr>
                  </tbody>
                </table>
              </SimpleBoardTitle>
            </div>


            <!-- 选择重大事项 -->
            <SectionDataDialog :dialog-visible.sync="sectionVisible" @sectionSure="sectionSure" />
            <!--公共信息-->
            <OtherInfo :data-state="dataState" :data.sync="mainData" :main-id="mainData.id" />
          </el-form>
        </el-scrollbar>
      </el-main>
    </el-container>
  </FormWindow>
</template>

<script>
// vuex缓存数据
import { mapGetters } from 'vuex';

// 接口api
import complianceReviewApi from '@/api/ComplianceReview/complianceReview.js'
import taskApi from '@/api/_system/task';
import commonApi from '@/api/_system/common';
// 组件
import FormWindow from '@/view/components/FormWindow/FormWindow';
import SimpleBoardTitle from "../../../../components/SimpleBoard/SimpleBoardTitle"
import OtherInfo from '@/view/litigation/caseManage/case/caseChild/OtherInfo';
import OrgSingleDialogSelect from '@/view/components/OrgSingleDialogSelect/index';
import QsBaseInfo from './qisubaseInfo';
import CaseData from '@/view/litigation/caseManage/case/caseChild/CaseData';
import CaseEvidenceData from '@/view/litigation/caseManage/case/caseChild/CaseEvidenceData';
import Shortcut from '@/view/litigation/caseManage/caseExamine/Shortcut';
import SimpleBoardTitleApproval from '@/view/components/SimpleBoard/SimpleBoardTitleApproval';
import UploadDoc from '@/view/components/UploadDoc/UploadDoc.vue';
import SectionDataDialog from './dialog/SelectionDialog.vue';
import HgscGdx from "@/view/Compliance/Management/ComplianceOperation/ComplianceReport/HgscGdx.vue";
import examination from "./child/Examination.vue";
import AutEvaDialog from "@/view/litigation/authorizationManage/authorization/dialog/AutEvaluationDialog.vue";
import OriginalComplianceReviewDialog
  from "@/view/Compliance/Management/ComplianceOperation/ComplianceReport/dialog/OriginalComplianceReviewDialog.vue";
import SimpleBoard from "@/view/components/SimpleBoard/SimpleBoardViewCase.vue";

export default {
  name: 'HgscMainDetailQsss',
  inject: ['layout', 'mcpLayout'],
  components: {
    SimpleBoard,
    AutEvaDialog,
    SimpleBoardTitleApproval,
    CaseData,
    QsBaseInfo,
    OrgSingleDialogSelect,
    FormWindow,
    OtherInfo,
    CaseEvidenceData,
    Shortcut,
    UploadDoc,
    SectionDataDialog,
    OriginalComplianceReviewDialog,
    HgscGdx,
    SimpleBoardTitle,
    examination,
  },
  computed: {
    ...mapGetters(['orgContext']),
    isView: function () {
      return this.dataState === this.utils.formState.VIEW;
    },
  },
  data() {
    return {
      originalComplianceReviewVisible: false,
      sectionVisible: false,
      businessAreaData: [],
      institutionalTypeData: [],
      radio: true,
      radio1: true,
      title: null,
      type: null,
      tabId: null,
      oarecordsDialog: false,
      loading: false,
      dataState: null,
      functionId: null, //终止的时候要用，需要手动关闭
      dataId: null,
      taskId: null,
      selectedKey: '', // 选中界面key
      view: 'old',
      mainData: {
        id: null, //主键
        reviewCategory: null,//审查类别
        reviewCategoryName: null,//审查类别
        reviewSubject: null,//事项题目
        reviewNumber: null,//事项编号
        isComplianceReviewed: null,//是否已合规审查
        hasProposalChanged: null,//议案是否有变化
        businessArea: null,//业务领域
        institutionalType: null, //制度类型
        otherBusinessArea: null, // 拟开展业务模式
        otherArgumentClassification: null, //风险描述
        argumentationReport: null, // 论证报告
        reportFiles: null, // 论证材料
        proposedBusinessModel: null, // 当业务领域选择“其他”时需填写此项
        matterDescription: null,//事项简介
        reviewMaterials: null,//相关附件
        basisForSystemFormulation: null,//制度制定依据
        relatedSignificantReview: null,//关联重大事项审查
        requiresTripleOneMeeting: null,//三重一大
        argumentClassification: null,//论证分类
        argumentClassificationName: null,//论证分类
        submittingEntity: null,//送审对象
        submittingEntityName: null,//送审对象
        requirementDocument: null,//需求文件
        requirementDocumentName: null,//需求文件
        createOgnId: null, //当前机构ID
        createOgnName: null, //当前机构名称
        createDeptId: null, //当前部门ID
        createDeptName: null, //当前部门名称
        createGroupId: null, //当前部门ID
        createGroupName: null, //当前部门名称
        createPsnId: null, //当前人ID
        createPsnName: null, //当前人名称
        createOrgId: null, //当前组织ID
        createOrgName: null, //当前组织名称
        createPsnFullId: null, //当前人全路径ID
        createPsnFullName: null, //当前人全路径名称
        createPsnPhone: null, //经办人电话
        checkList: [],// 合规审查
        examinationLists: [],// 合规审查
        createTime: null, //创建时间
        dataState: this.utils.dataState_BPM.SAVE.name, //数据状态
        dataStateCode: this.utils.dataState_BPM.SAVE.code, //数据状态编码
        originalComplianceReview: null, // 原事项题目
        decisionContent: null, // 议案正文
        requestText: null //请示正文
      },
      orgTreeDialog: false,
      orgDialogTitle: '组织信息',
      isAssign: false,
      rules: {
        reviewSubject: [{required : true, message: '请输入事项题目', trigger: 'blur' }],
        isComplianceReviewed: [{required : true, message: '请选择是否已合规审查', trigger: 'blur' }],
        hasProposalChanged: [{required : true, message: '请选择议案是否有变化', trigger: 'blur' }],
        businessArea: [{required : true, message: '请选择业务领域', trigger: 'blur' }],
        matterDescription: [{ required: true, message: '请输入事项简介', trigger: 'blur' }],
        requiresTripleOneMeeting: [{required : true, message: '请输入是否提交重大决策', trigger: 'blur' }],
        submittingEntity: [{required : true, message: '请输入送审对象', trigger: 'blur' }],
        requirementDocument: [{required : true, message: '请输入需求文件', trigger: 'blur' }],
        argumentClassification: [{required : true, message: '请输入论证分类', trigger: 'blur' }],
        otherBusinessArea: [{required : true, message: '请输入业务领域', trigger: 'blur' }],
        otherArgumentClassification: [{required : true, message: '请输入风险描述', trigger: 'blur' }],
        proposedBusinessModel: [{required : true, message: '请输入拟开展业务模式', trigger: 'blur' }],
        originalComplianceReview: [{required : true, message: '请选择原事项题目', trigger: 'blur' }],
        basisForSystemFormulation: [{required : true, message: '请上传附件', trigger: 'blur' }],
        decisionContent: [{required : true, message: '请上传附件', trigger: 'blur' }],
        requestText: [{required : true, message: '请上传附件', trigger: 'blur' }],
        institutionalType: [{required : true, message: '请选择制度类型', trigger: 'blur' }],
        reviewMaterials: [{required : true, message: '请上传附件', trigger: 'blur' }],
      },
      fixedItemNames: [
        { value: '制度是否符合铸牢中华民族共同体意识', level: 1 },
        { value: '制度是否符合习近平总书记关于加强和改进民族工作重要思想', level: 2 },
        { value: '制度是否符合党的民族理论、民族政策', level: 3 },
        { value: '制度是否与国家法律、法规、规章、规范性文件一致', level: 4 },
        { value: '制度是否与行业主管部门规定一致', level: 5 },
        { value: '制度是否与国资监管制度文件一致', level: 6 },
      ],
      examinationData: [
        {
          matter: '本事项是否涉及被行政处罚的风险',
          clause: '涉及的行政处罚的文件名称及责任条款',
          measures: '行政处罚风险应对措施',
          level: 1
        },
        {
          matter: '本事项是否涉及单位被追究刑事责任的风险',
          clause: '涉及单位被追究刑事责任的文件名称及责任条款',
          measures: '刑事责任风险应对措施',
          level: 2
        },
        {
          matter: '本事项是否涉及被国际制裁或采取强制性措施的风险',
          clause: '涉及的国际制裁或采取强制性措施的文件名称及责任条款',
          measures: '国际制裁或采取强制性措施风险应对措施',
          level: 3
        },
        {
          matter: '本事项是否涉及违反国资监管要求的风险',
          clause: '涉及的国资监管的文件名称及具体要求',
          measures: '违反国资监管要求风险应对措施',
          level: 4
        },
        {
          matter: '是否存在产生社会重大舆情的风险',
          clause: '',
          measures: '合规风险应对措施',
          level: 5
        },
        {
          matter: '是否存在其他合规风险',
          clause: '文件名称及责任条款',
          measures: '合规风险应对措施',
          level: 6
        },
      ],
      activity: null, //记录当前待办处于流程实例的哪个环节
      obj: {
        // 流程处理逻辑需要的各种参数
        taskId: null,
        processInstanceId: null,
        businessKey: null,
        title: null,
        functionName: null,
        sid: null,
      },
      noticeParams: {},
      noticeData: {
        moduleName: '', // 模块名称
        dataId: '', // 数据ID
        url: '', // 地址
        title: '', // 地址
        params: {}, // 其他参数
      },
      loadingText: '加载中...',
    };
  },
  created() {
    this.initDic();
  },
  methods: {
    initDic() {
      const code = ['businessDomainDic','HG-HGZD'];
      this.utils.getDic(code).then((response) => {
        this.businessAreaData = response.data.data[code[0]];
        this.institutionalTypeData = response.data.data[code[1]]
      })
    },
    originalComplianceReviewSure(val) {
      this.mainData.originalComplianceReview = val.reviewSubject;
      this.originalComplianceReviewVisible = false;
    },
    chooseOriginalComplianceReview() {
      this.originalComplianceReviewVisible = true;
    },
    initData(temp, dataState) {
      this.dataState = dataState;
      Object.assign(this.mainData, temp);
      this.mainData.reviewCategory = this.$route.query.reviewCategory;
      this.mainData.reviewCategoryName = this.$route.query.reviewCategoryName;
      this.title = this.$route.query.reviewCategoryName;
      if (this.mainData.reviewCategory === 'HG-SCLX-QSSX') this.selectedKey = 'HGQS';
      commonApi.createKvsequence({
        key: this.selectedKey + new Date().getFullYear(),
        length: 6
      }).then((res) => {
        this.mainData.reviewNumber = res.data.kvsequence;
      })
      this.view = 'old';
      this.loading = false;
    },
    loadData(dataState, dataId) {
      this.functionId = this.$route.query.functionId;
      this.title = this.$route.query.reviewCategoryName;
      if (this.$route.query.view !== undefined && this.$route.query.view !== '') this.view = this.$route.query.view;
      if (this.$route.query.type !== undefined && this.$route.query.type !== '') this.type = this.$route.query.type;
      this.dataState = dataState;
      complianceReviewApi.queryById(dataId).then((response) => {
        this.mainData = response.data.data;
        this.loading = false;
      });
    },
    sectionSure(val) {
      this.mainData.relatedSignificantReview = val.reviewNumber;
      this.sectionVisible = false;
    },
    save() {
      return new Promise((resolve, reject) => {
        complianceReviewApi.save(this.mainData).then((response) => {
          resolve(response);
        }).catch((error) => {
          reject(error)
        })
      })
    },
    approval_() {
      this.$refs['dataForm'].validate((valid) => {
        let content = []
        /*if(this.mainData.checkList.length > 0){
            for(let i = 0; i < this.mainData.checkList.length; i++){
              if (this.mainData.checkList[i].reviewResult == true) {
                if (this.mainData.checkList[i].responseMeasures == undefined || this.mainData.checkList[i].responseMeasures == null || this.mainData.checkList[i].responseMeasures == '') {
                   content.push(this.mainData.checkList[i].fixedItemName)
                }
              }
            }
            if( content !== ""){
              this.$message.error("合规审查具体内容不允许为空！")
            }
          }*/
        if (valid) {

          this.save()
            .then(() => {
              const tabId = this.mainData.id;
              if (this.mainData.dataStateCode === this.utils.dataState_BPM.SAVE.code) {
                taskApi.selectFunctionId({ functionCode: 'hgsc_main_qsss' }).then((res) => {
                  const functionId = res.data.data[0].ID;
                  this.layout.openNewTab('合规审查审批', 'design_page', 'design_page', tabId, {
                    ...this.utils.routeState.NEW(tabId),
                    functionId: functionId,
                    businessKey: tabId,
                    entranceType: 'FLOWABLE',
                    create: 'create',
                    type: this.mainData.reviewCategory,
                    view: 'new',
                  });
                });

              } else {
                const tabId = this.mainData.id;
                taskApi.selectTaskId({ businessKey: tabId, isView: '' }).then((res) => {
                  const functionId = res.data.data[0].ID;
                  const uuid = this.utils.createUUID();
                  this.layout.openNewTab('案件风险告知审批信息', 'design_page', 'design_page', uuid, {
                    processInstanceId: res.data.data[0].PID, //流程实例
                    taskId: res.data.data[0].ID, //任务ID
                    businessKey: tabId, //业务数据ID
                    functionId: functionId, //不传也可以，只有首环节需要传，实例启动就可以不传了
                    entranceType: 'FLOWABLE',
                    type: 'toDeal',
                    view: 'new',
                    create: 'create',
                  });
                });
              }
            }).then(() => {
              this.mcpLayout.closeTab();
            });
        }
      });
    },
    save_() {
      this.save().then(() => {
        this.$message.success('保存成功!');
      });
    },
    asfChange(val) {
      this.mainData.argumentClassificationName = this.utils.getDicName(this.utils.classificationData, val);
    },
    entityChange(val) {
      this.mainData.submittingEntityName = this.utils.getDicName2(this.submittingEntityData, val);
    },
    documentChange(val) {
      this.mainData.requirementDocumentName = this.utils.getDicName2(this.requirementDocumentData, val);
    },
  },
};
</script>

<style scoped></style>
