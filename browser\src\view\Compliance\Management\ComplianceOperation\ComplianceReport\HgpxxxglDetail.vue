<template>
	<div>
		<FormWindow ref="formWindow" @initData="initData" @loadData="loadData">
			<el-container v-loading="loading" style="height: calc(100vh - 84px)" :element-loading-text="loadingText">
				<el-main>
					<el-scrollbar style="height: 100%" wrap-style="overflow-x: hidden;">
						<div style="background: green; color: white; position: absolute; right: 0">培训进行中</div>
						<!-- <div style="display: flex">
							<div style="font-size: 16px; font-weight: bolder">{{mainData.trainingTopic}}</div>
							<div class="up"></div>
							<div>{{mainData.likeCount}}</div>
							<div class="down"></div>
							<div>{{mainData.dislikeCount}}</div>
						</div> -->
						<div style="margin-top: 10px">培训主题：{{ mainData.trainingTopic }}</div>
						<div style="margin-top: 5px">培训分类：{{ mainData.trainingCategory == 'dwzxzzthgxx' ? '党委中心组专题合规学习'
							: mainData.trainingCategory == 'glcpxxx' ? '管理层（董监高）培训学习'
								: mainData.trainingCategory == 'hgglypxxx' ? '合规管理员培训学习'
									: mainData.trainingCategory == 'xygrzpxxx' ? '新员工入职培训学习'
										: mainData.trainingCategory == 'qypsxpxxg' ? '全员普适性培训宣贯'
											: mainData.trainingCategory == 'qt' ? '其他' : '' }}</div>
						<div style="margin-top: 5px">培训时间：{{ formatDate(mainData.trainingStartTime) + ' 至 ' +
							formatDate(mainData.trainingEndTime) }}</div>
						<div style="margin-top: 5px">培训讲师：{{ mainData.trainingDirector }}</div>
						<div style="margin-top: 5px">发布人：{{ mainData.createPsnFullName }}</div>
						<div style="margin-top: 5px; display: flex">
							<div>学习进度</div>
							<div style="color:#DC5443">未开始</div>
							<div>{{ mainData.notStartedCount + '人；' }}</div>
							<div style="color:#e6a23c">学习中</div>
							<div>{{ mainData.learningInProgressCount + '人；' }}</div>
							<div style="color:#7FB554">已完成</div>
							<div>{{ mainData.completedCount + '人；' }}</div>
							<span @click="openDialog" style="color: #409eff; cursor: pointer">查看详情</span>
						</div>
						<!-- 学习进度列表 -->
						<StudyDialog :dialog-visible.sync="dialogVisible" :id="mainData.id" @studySure="studySure" />
						<el-tabs type="border-card">
							<el-tab-pane label="课程信息">
								<el-table ref="table" v-loading="tableLoading" :data="mainData.courseDetailsList"
									size="mini" border :height="table_height" stripe fit highlight-current-row
									:show-overflow-tooltip="true" row-key="id" style="table-layout: fixed; width: 100%"
									@sort-change="tableSort" @row-dblclick="rowDblclick">
									<el-table-column type="index" width="50" label="序号" align="center" />
									<el-table-column prop="trainingCourse" show-overflow-tooltip label="培训课程">
									</el-table-column>
									<el-table-column prop="trainingMaterials" show-overflow-tooltip label="培训课件"
										sortable="custom">
										<template slot-scope="scope">
											<UploadDoc :files.sync="scope.row.trainingMaterials" doc-path="/case" v-if="scope.row.isVideo==null || scope.row.isVideo=='N'"
												:disabled="true" />
											<span v-if="scope.row.isVideo=='Y'" style="color:blue;cursor:pointer"
												  @click="loadVideo(scope.row)">{{parseVideoName(scope.row.trainingMaterials)}}</span>
										</template>
									</el-table-column>
									<el-table-column show-overflow-tooltip prop="courseDescription" label="课程说明" />
								</el-table>
							</el-tab-pane>
							<el-tab-pane label="课程评论">
								<div style="font-size: 16px;font-weight: bolder;">评论记录</div>
								<ul style='margin-top: 10px;'>
									<!-- <li>
                                        <div style="display: flex;">
                                            <div class="userAvatar"></div>
                                            <div style="margin-left: 5px;width: 50px;">测试</div>
                                            <div style="margin-left: 200px;">评价时间：2024-09-01 14：08：08</div>
                                        </div>
                                        <div style="display: flex;flex-wrap: wrap;margin-top: 20px;">
                                            在参加党委中心坦专验合规学习培训后，独深剧认识购合规管理2(67为限筑面要计。调过学写，我通确了合烟的内销纪要求，最兴作为一名党局，必须产信育守法律法规，坚守取业道制，以身作期，为营造良好的相织文化期合规环境员献力量。这次培训不仅握升了我的合规察识，也增强了现在工作中识说和防范风险的能力。
                                        </div>
                                        <div style="width: calc(100% - 10px);height: 1px;background: #000;margin:10px 0;"></div>
                                    </li>
                                    <li>
                                        <div style="display: flex;">
                                            <div class="userAvatar"></div>
                                            <div style="margin-left: 5px;width: 50px;">测试1</div>
                                            <div style="margin-left: 200px;">评价时间：2024-09-01 14：08：08</div>
                                        </div>
                                        <div style="display: flex;flex-wrap: wrap;margin-top: 20px;">
                                            在参加党委中心坦专验合规学习培训后，独深剧认识购合规管理2(67为限筑面要计。调过学写，我通确了合烟的内销纪要求，最兴作为一名党局，必须产信育守法律法规，坚守取业道制，以身作期，为营造良好的相织文化期合规环境员献力量。这次培训不仅握升了我的合规察识，也增强了现在工作中识说和防范风险的能力。
                                        </div>
                                    </li> -->
									<li v-for="(item, index) in mainData.reviewsList">
										<div style="display: flex;">
											<div class="userAvatar"></div>
											<div style="margin-left: 5px;width: 50px;">{{ item.createPsnName }}</div>
											<div style="margin-left: 200px;">{{ '评价时间：' + item.createTime }}</div>
										</div>
										<div style="display: flex;flex-wrap: wrap;margin-top: 20px;">
											{{ item.reviewDesc }}
										</div>
										<div
											style="width: calc(100% - 10px);height: 1px;background: #000;margin:10px 0;">
										</div>
									</li>
								</ul>
							</el-tab-pane>
							<el-tab-pane label="学习报告">
								<div style="display: flex;justify-content: space-between;">
									<div>{{ '已上传：' + mainData.uploadedCount + '人；' + '未上传' + mainData.notUploadedCount +
										'人；' }}</div>
									<el-button style="margin-bottom: 0;" type="primary"
										@click="exportReports">导出</el-button>
								</div>
								<el-table ref="table" v-loading="tableLoading"
									:data="mainData.complianceTrainingReports" size="mini" border :height="table_height"
									stripe fit highlight-current-row :show-overflow-tooltip="true" row-key="id"
									style="table-layout: fixed; width: 100%" @sort-change="tableSort"
									@row-dblclick="rowDblclick">
									<el-table-column type="index" label="序号" align="center" width="50" />
									<el-table-column prop="createPsnName" show-overflow-tooltip label="姓名"
										align="center" width="100" />
									<el-table-column prop="createOgnName" show-overflow-tooltip label="所属公司"
										sortable="custom" align="center" width="350" />
									<el-table-column show-overflow-tooltip prop="createDeptName" label="所属部门"
										align="center" width="300" />
									<el-table-column show-overflow-tooltip prop="reportFile" label="学习报告" width="400">
										<template slot-scope="scope">
											<UploadDoc :files.sync="scope.row.reportFile" doc-path="/case"
												:disabled="isView" />
										</template>
									</el-table-column>
									<el-table-column show-overflow-tooltip prop="createTime" label="上传时间" align="center"
										width="150" />
								</el-table>
							</el-tab-pane>
						</el-tabs>
						<!--案件审批表-->
						<prosecution-dialog :visible.sync="prosecutionDialog" :is-multiple="false"
							@onSure="prosecutionSelect" />
					</el-scrollbar>
				</el-main>
				<!-- 选择模版 -->
				<!-- <Shortcut :templateShow="templateShow" @templateClick="templateClick" /> -->
			</el-container>
			<!--视频播放组件-->
			<Video-Player-Popup :videoSrc="videoUrl" :visible="videoShow" :videoWatchDuration="0" @sendToParent="closeVideoDialog"></Video-Player-Popup>
		</FormWindow>
	</div>
</template>

<script>
// vuex缓存数据
import { mapGetters } from 'vuex';

// 接口api
import complianceReviewApi from '@/api/ComplianceReview/complianceReview.js';
import taskApi from '@/api/_system/task';
import commonApi from '@/api/_system/common';
// 组件
import FormWindow from '@/view/components/FormWindow/FormWindow';
import OtherInfo from '@/view/litigation/caseManage/case/caseChild/OtherInfo';
import OrgSingleDialogSelect from '@/view/components/OrgSingleDialogSelect/index';
import ProsecutionDialog from './ProsecutionDialog';
import QsBaseInfo from './qisubaseInfo';
import CaseData from '@/view/litigation/caseManage/case/caseChild/CaseData';
import CaseEvidenceData from '@/view/litigation/caseManage/case/caseChild/CaseEvidenceData';
import Shortcut from '@/view/litigation/caseManage/caseExamine/Shortcut';
import SimpleBoardTitleApproval from '@/view/components/SimpleBoard/SimpleBoardTitleApproval';
import UploadDoc from '@/view/components/UploadDoc/UploadDoc.vue';
import VideoPlayerPopup from '@/view/video/VideoPlayerPopup.vue';
import SectionDataDialog from './dialog/SelectionDialog.vue';
import StudyDialog from './dialog/StudyDialog.vue';
import ComplianceTrainingApi from '@/api/ComplianceTraining/ComplianceTraining.js';
// 接口api
import docApi from '@/api/_system/doc'
export default {
	name: 'HgpxxxglDetail',
	inject: ['layout', 'mcpLayout'],
	components: {
		SimpleBoardTitleApproval,
		CaseData,
		QsBaseInfo,
		ProsecutionDialog,
		OrgSingleDialogSelect,
		FormWindow,
		OtherInfo,
		CaseEvidenceData,
		Shortcut,
		UploadDoc,
		SectionDataDialog,
		StudyDialog,
		VideoPlayerPopup
	},
	computed: {
		...mapGetters(['orgContext']),
		isView: function () {
			return this.dataState === this.utils.formState.VIEW;
		},
		templateShow() {
			return this.mainData.dataStateCode === this.utils.dataState_BPM.SAVE.code && this.dataState !== this.utils.formState.VIEW;
		},
	},
	data() {
		return {
			videoUrl:'',
			videoShow:false,
			dialogVisible: false,//学习进度列表弹框
			tableData: [
				{
					trainingCourse: 'XXXXX',
					courseDescription: '测试说明123',
					trainingMaterials: 'XXXXX.docs'
				}
			], //课程信息列表
			tableLoading: false, //课程信息列表loading
			sectionVisible: false,
			businessAreaData: [],
			radio: true,
			radio1: true,
			title: null,
			type: null,
			tabId: null,
			oarecordsDialog: false,
			loading: false,
			dataState: null,
			functionId: null, //终止的时候要用，需要手动关闭
			dataId: null,
			taskId: null,

			view: 'old',
			mainData: {
				trainingCourseDetailsList: [], //培训内容
				uploadedCount: null,
				notUploadedCount: null,
				complianceTrainingReports: [],
				likeCount: null,
				dislikeCount: null,
				trainingDirector: null,
				responsibleDepartment: null,
				trainingTopic: null,
				trainingCategory: null,
				trainingStartTime: null,
				trainingEndTime: null,
				trainingLocation: null,
				trainingIntroduction: null,
				notStartedCount: null,
				learningInProgressCount: null,
				completedCount: null,
				mandatoryObjectId: null,
				electiveObjectId: null,
				mandatoryObjectName: null,
				electiveObjectName: null,
				reviewsList: [],
				courseDetailsList: [],
				id: null, //主键
				reviewCategory: null, //审查类别
				createOgnId: null, //当前机构ID
				createOgnName: null, //当前机构名称
				createDeptId: null, //当前部门ID
				createDeptName: null, //当前部门名称
				createGroupId: null, //当前部门ID
				createGroupName: null, //当前部门名称
				createPsnId: null, //当前人ID
				createPsnName: null, //当前人名称
				createOrgId: null, //当前组织ID
				createOrgName: null, //当前组织名称
				createPsnFullId: null, //当前人全路径ID
				createPsnFullName: null, //当前人全路径名称
				createPsnPhone: null, //经办人电话
				createTime: null, //创建时间
				// auditStatus: this.utils.dataState_BPM.SAVE.name, //状态
				dataState: this.utils.dataState_BPM.SAVE.name, //数据状态
				dataStateCode: this.utils.dataState_BPM.SAVE.code, //数据状态编码
			},
			orgTreeDialog: false,
			orgDialogTitle: '组织信息',
			isAssign: false,
			prosecutionDialog: false,
			rules: {
				reviewSubject: [{ required: true, message: '请输入审查主题', trigger: 'blur' }],
				isComplianceReviewed: [{ required: true, message: '请选择是否有合规审查', trigger: 'blur' }],
				hasProposalChanged: [{ required: true, message: '请选择议案是否有变化', trigger: 'blur' }],
				businessArea: [{ required: true, message: '请选择业务领域', trigger: 'blur' }],
			},
			activity: null, //记录当前待办处于流程实例的哪个环节
			obj: {
				// 流程处理逻辑需要的各种参数
				taskId: null,
				processInstanceId: null,
				businessKey: null,
				title: null,
				functionName: null,
				sid: null,
			},
			noticeParams: {},
			noticeData: {
				moduleName: '', // 模块名称
				dataId: '', // 数据ID
				url: '', // 地址
				title: '', // 地址
				params: {}, // 其他参数
			},
			loadingText: '加载中...',
		};
	},
	provide() {
		return {
			parentCase: this,
		};
	},
	methods: {
		initData(temp, dataState) {
			this.dataState = dataState;
			Object.assign(this.mainData, temp);
			this.mainData.reviewCategory = this.$route.query.reviewCategory;
			this.title = this.utils.getDicName(this.utils.compliance_review_type, this.mainData.reviewCategory);
			let obj = this.utils.getManagementUnit(this.mainData.createPsnFullName, this.mainData.createPsnFullId);
			this.mainData.createPsnFullName = obj.name;
			this.mainData.managementUnitId = obj.id;
			this.mainData.unitType = obj.unitType;
			this.mainData.unitTypeId = obj.unitTypeId;
			commonApi
				.createKvsequence({
					key: 'AJ' + new Date().getFullYear(),
					length: 5,
				})
				.then((res) => {
					this.mainData.reviewNumber = res.data.kvsequence;
				});
			this.view = 'old';
			const code = ['businessDomainDic'];
			this.utils.getDic(code).then((response) => {
				this.businessAreaData = response.data.data[code[0]];
			});
			let year = new Date().getFullYear();
			this.utils.createKvsequence('HGBG' + year, 6).then((value) => {
				this.mainData.caseCode = value.data.kvsequence;
			});
			// this.mainData.currentUnit = this.mainData.createOgnName;
			// this.mainData.currentUnitId = this.mainData.createOgnId;

			this.mainData.riskDepartment = this.mainData.createOgnName; //有点问题
			this.mainData.riskDepartmentId = this.mainData.createOgnId;

			const interCode = 'AJ_GC_CLMC_QS';
			const codes = [interCode];
			this.utils.getDic(codes).then((response) => {
				const datas = response.data.data[codes[0]].filter((item) => item.whetherSolidified === true);
				if (datas && datas.length > 0) {
					datas.forEach((item, index) => {
						const data = this.childData(index);
						data.name = item.dicName;
						data.whetherSys = true;
						this.mainData.otherDataList.push(data);
					});
				}
			});
			this.loading = false;
		},
		loadData(dataState, dataId) {
			this.functionId = this.$route.query.functionId;
			if (this.$route.query.view !== undefined && this.$route.query.view !== '') this.view = this.$route.query.view;
			if (this.$route.query.type !== undefined && this.$route.query.type !== '') this.type = this.$route.query.type;
			this.dataState = dataState;
			ComplianceTrainingApi.queryReport({
				complianceTrainingLearningTableId: this.$route.query.id
			}).then((res) => {
				console.log(res, 'res');
				this.mainData.complianceTrainingReports = res.data.data.complianceTrainingReports.records;
				this.mainData.uploadedCount = res.data.data.uploadedCount;
				this.mainData.notUploadedCount = res.data.data.notUploadedCount;
			})

			this.mainData.createPsnFullName = this.orgContext.currentPsnFullName;
			// this.mainData.trainingTopic = this.$route.query.trainingTopic;
			// this.mainData.dislikeCount = !this.$route.query.dislikeCount?'0':this.$route.query.dislikeCount;
			// this.mainData.likeCount = !this.$route.query.likeCount?'0':this.$route.query.likeCount;
			// this.mainData.trainingCategory = this.$route.query.trainingCategory;
			// this.mainData.trainingStartTime = this.$route.query.trainingStartTime;
			// this.mainData.trainingEndTime = this.$route.query.trainingEndTime;
			// this.mainData.trainingDirector = this.$route.query.trainingDirector;
			// this.mainData.notStartedCount = this.$route.query.notStartedCount;
			// this.mainData.learningInProgressCount = this.$route.query.learningInProgressCount;
			// this.mainData.completedCount = this.$route.query.completedCount;
			// this.mainData.id = this.$route.query.id;
			// this.mainData.courseDetailsList = this.mainData.courseDetailsList.length>0?JSON.parse(this.mainData.courseDetailsList):[];
			// ComplianceTrainingApi.queryReport({id:this.$route.query.id}).then((res) => {
			// 	this.mainData.uploadedCount = res.data.data.uploadedCount;
			// 	this.mainData.notUploadedCount = res.data.data.notUploadedCount;
			// 	this.mainData.complianceTrainingReports = res.data.data.complianceTrainingReports.records;
			// })
			// ComplianceTrainingApi.queryReview({id:this.$route.query.id}).then((res) => {
			// 	this.mainData.reviewsList = res.data.data.records;
			// })
			this.queryCourList();
		},
		// },
		sectionSure(val) {
			this.mainData.relatedSignificantReview = val.reviewNumber;
			this.sectionVisible = false;
		},
		queryCourList() {
			ComplianceTrainingApi.queryById({ id: this.$route.query.id }).then((res) => {
				this.mainData.trainingTopic = res.data.data.trainingTopic;
				this.mainData.trainingCategory = res.data.data.trainingCategory;
				this.mainData.trainingStartTime = res.data.data.trainingStartTime;
				this.mainData.trainingEndTime = res.data.data.trainingEndTime;
				this.mainData.trainingDirector = res.data.data.trainingDirector;
				this.mainData.notStartedCount = res.data.data.notStartedCount;
				this.mainData.learningInProgressCount = res.data.data.learningInProgressCount;
				this.mainData.completedCount = res.data.data.completedCount;
				this.mainData.dislikeCount = !res.data.data.dislikeCount ? '0' : res.data.data.dislikeCount;
				this.mainData.likeCount = !res.data.data.likeCount ? '0' : res.data.data.likeCount;
				this.mainData.courseDetailsList = res.data.data.trainingCourseDetailsList;
				this.mainData.reviewsList = res.data.data.reviewsList;
				this.mainData.learningProgress = res.data.data.learningProgress;
				this.mainData.id = this.$route.query.id;
				this.mainData.complianceTrainingLearningTableId = this.$route.query.id;
			})
		},
		save() {
			return new Promise((resolve, reject) => {
				complianceReviewApi
					.save(this.mainData)
					.then((response) => {
						resolve(response);
					})
					.catch((error) => {
						reject(error);
					});
			});
		},
		submit() {
			return new Promise((resolve, reject) => {
				//判断 reporter 和 reportingUnit 是否有空值
				if (!this.mainData.reporter || !this.mainData.reportingUnit) {
					this.mainData.reporter = this.mainData.createPsnName; // 上报人
					this.mainData.reportingUnit = this.mainData.createDeptName; // 上报单位
				} else {
					this.mainData.reporter = this.mainData.reporter; // 上报人
					this.mainData.reportingUnit = this.mainData.reportingUnit; // 上报单位
				}
				this.mainData.reviewStatus = '已提交';

				// 获取当前时间并格式化
				// const now = new Date();
				// const year = now.getFullYear();
				// const month = String(now.getMonth() + 1).padStart(2, '0');
				// const day = String(now.getDate()).padStart(2, '0');
				// const hours = String(now.getHours()).padStart(2, '0');
				// const minutes = String(now.getMinutes()).padStart(2, '0');
				// const seconds = String(now.getSeconds()).padStart(2, '0');

				// this.mainData.reportTime = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
				this.mainData.reportTime = new Date();
				complianceReportApi
					.save(this.mainData)
					.then((response) => {
						resolve(response);
					})
					.catch((error) => {
						reject(error);
					});
			});
		},

		//选择模板
		templateClick(val) {
			if (val) {
				this.prosecutionDialog = true;
			}
		},
		prosecutionSelect(data) {
			this.mainData.reportSubject = data.reportSubject;
			this.mainData.reportYear = data.reportYear;
			this.mainData.reportCategory = data.reportCategory;
			this.mainData.internalExternalRisk = data.internalExternalRisk;
			this.mainData.positiveNegativeImpact = data.positiveNegativeImpact;
			this.mainData.involvedAmount = data.involvedAmount;
			this.mainData.riskDescription = data.riskDescription;
			this.mainData.riskReason = data.riskReason;
			this.mainData.potentialConsequences = data.potentialConsequences;
			this.mainData.reportFile = data.reportFile;
			this.mainData.unitTypeId = data.unitTypeId; //单位类型id
			this.mainData.riskDepartment = data.riskDepartment; //风险部门
			this.mainData.riskDepartmentId = data.riskDepartmentId; //风险部门Id
			this.mainData.currentUnit = data.currentUnit; //当事单位
			this.mainData.currentUnitId = data.currentUnitId; //当事单位id
			this.mainData.reporter = data.createPsnName; //经办人
			this.mainData.reportingUnit = data.createDeptName; //经办单位
			this.mainData.createPsnPhone = data.createPsnPhone; //经办人电话
			this.mainData.caseInterest = data.caseInterest; //利息
			this.mainData.createOgnName = data.createOgnName; //经办单位
			this.mainData.createPsnName = data.createPsnName; //经办人
			this.mainData.createDeptName = data.createDeptName; //经办部门
			// this.mainData.auditStatus = data.dataState;//状态
			this.mainData.partiesList = data.partiesList;
			this.mainData.partiesList.forEach((item) => {
				item.id = this.utils.createUUID();
				item.masterId = this.mainData.id;
			});

			this.mainData.claimList = data.claimList;
			this.mainData.claimList.forEach((item) => {
				item.id = this.utils.createUUID();
				item.parentId = this.mainData.id;
			});

			this.mainData.otherDataList = data.otherDataList;
			this.mainData.otherDataList.forEach((item) => {
				item.id = this.utils.createUUID();
				item.parentId = this.mainData.id;
				item.files = null;
			});

			this.mainData.relations = data.relations;
			this.mainData.relations.forEach((item) => {
				item.id = this.utils.createUUID();
				item.relationId = this.mainData.id;
			});
		},
		approval_() {
			this.$refs['dataForm'].validate((valid) => {
				if (valid) {
					this.save()
						.then(() => {
							const tabId = this.mainData.id;
							if (this.mainData.dataStateCode == this.utils.dataState_BPM.SAVE.code) {
								taskApi.selectFunctionId({ functionCode: 'case_risk_main' }).then((res) => {
									const functionId = res.data.data[0].ID;
									this.layout.openNewTab('案件风险告知审批信息', 'design_page', functionId, tabId, {
										...this.utils.routeState.NEW(tabId),
										functionId: functionId,
										businessKey: tabId,
										entranceType: 'FLOWABLE',
										create: 'create',
										view: 'new',
									});
								});
							} else {
								const tabId = this.mainData.id;
								taskApi.selectTaskId({ businessKey: tabId, isView: '' }).then((res) => {
									const functionId = res.data.data[0].ID;
									const uuid = this.utils.createUUID();
									this.layout.openNewTab('案件风险告知审批信息', 'design_page', 'design_page', uuid, {
										processInstanceId: res.data.data[0].PID, //流程实例
										taskId: res.data.data[0].ID, //任务ID
										businessKey: tabId, //业务数据ID
										functionId: functionId, //不传也可以，只有首环节需要传，实例启动就可以不传了
										entranceType: 'FLOWABLE',
										type: 'toDeal',
										view: 'new',
										create: 'create',
									});
								});
							}
						})
						.then(() => {
							this.mcpLayout.closeTab();
						});
				}
			});
		},
		save_() {
			this.save().then(() => {
				this.$message.success('保存成功!');
			});
		},
		submit_() {
			this.$refs['dataForm'].validate((valid) => {
				if (valid) {
					this.submit().then(() => {
						this.mainData.reviewStatus = '已提交';
						this.$message.success('提交成功!');
					});
				} else {
					this.$message.error('表单填写不完整，请检查后重新提交!');
				}
			});
		},
		saveCurrentTime() {
			const now = new Date();
			const year = now.getFullYear();
			const month = String(now.getMonth() + 1).padStart(2, '0');
			const day = String(now.getDate()).padStart(2, '0');
			const hours = String(now.getHours()).padStart(2, '0');
			const minutes = String(now.getMinutes()).padStart(2, '0');
			const seconds = String(now.getSeconds()).padStart(2, '0');
		},
		//添加表格数据
		addTableData() {
			this.mainData.trainingCourseDetailsList.push({
				trainingCourse: '',
				courseDescription: '',
				trainingMaterials: [],
			});
		},
		openDialog() {
			this.dialogVisible = true;
		},
		formatDate(date) {
			if (!date) return '';
			const d = new Date(date);
			const year = d.getFullYear();
			const month = String(d.getMonth() + 1).padStart(2, '0');
			const day = String(d.getDate()).padStart(2, '0');
			return `${year}-${month}-${day}`;
		},
		//导出功能
		exportReports() {
			import('@/utils/export.js').then(({ FileSaver, XLSX }) => {
				// 创建工作簿
				const workbook = XLSX.utils.book_new();
				// 准备数据
				const data = this.mainData.complianceTrainingReports.map((report, index) => {
					let reportFileName = '';
					if (report.reportFile) {
						// 解析 reportFile 字符串为 JSON 对象数组
						const reportFiles = JSON.parse(report.reportFile);
						if (reportFiles.length > 0) {
							reportFileName = reportFiles[0].name || ''; // 获取第一个对象的 name 或空字符串
						}
					}
					return {
						序号: index + 1,
						姓名: report.createPsnName,
						所属公司: report.createOgnName,
						所属部门: report.createDeptName,
						学习报告: reportFileName,  // 使用解析后的 name 或空字符串
						上传时间: report.createTime
					};
				});
				// 创建工作表
				const worksheet = XLSX.utils.json_to_sheet(data);

				// 设置列的固定宽度
				const wscols = [
					{ wch: 10 },  // 序号
					{ wch: 20 },  // 姓名
					{ wch: 30 },  // 所属公司
					{ wch: 30 },  // 所属部门
					{ wch: 50 },  // 学习报告
					{ wch: 20 }   // 上传时间
				];
				worksheet['!cols'] = wscols;

				// 设置样式
				const headerStyle = {
					font: { bold: true, color: { rgb: 'FFFFFF' } },
					fill: { fgColor: { rgb: '0070C0' } },
					alignment: { horizontal: 'center' },
					border: {
						top: { style: 'thin' },
						bottom: { style: 'thin' },
						left: { style: 'thin' },
						right: { style: 'thin' }
					}
				};

				const cellStyle = {
					alignment: { horizontal: 'center' },
					border: {
						top: { style: 'thin' },
						bottom: { style: 'thin' },
						left: { style: 'thin' },
						right: { style: 'thin' }
					}
				};

				// 应用样式到工作表
				const range = XLSX.utils.decode_range(worksheet['!ref']);
				for (let R = range.s.r; R <= range.e.r; ++R) {
					for (let C = range.s.c; C <= range.e.c; ++C) {
						const cellRef = XLSX.utils.encode_cell({ r: R, c: C });
						const cell = worksheet[cellRef];
						if (cell && cell.t) {
							if (R === range.s.r) {
								// 头部样式
								worksheet[cellRef].s = headerStyle;
							} else {
								// 单元格样式
								worksheet[cellRef].s = cellStyle;
							}
						}
					}
				}

				// 添加工作表到工作簿
				XLSX.utils.book_append_sheet(workbook, worksheet, '学习报告');
				// 导出 Excel 文件
				XLSX.writeFile(workbook, '学习报告.xlsx');
			});
		},
		loadVideo(row){
			let jsonArr = JSON.parse(row.trainingMaterials);
			let docId = jsonArr[0].docId;
			docApi.downloadURL(docId).then(res=>{
				this.videoShow = true;
				this.videoUrl = res.data;
			})
		},
		closeVideoDialog(visible){
			this.videoShow = visible;
			this.videoUrl = '';
		},
		parseVideoName(videoJson){
			let jsonArr = JSON.parse(videoJson);
			return jsonArr[0].name;
		}
	}
};
</script>

<style scoped>
.userAvatar {
	background: url(../../../../../assets/img/home/<USER>
	background-size: 100% 100%;
	width: 20px;
	height: 20px;
	border-radius: 20px;
}

.up {
	background: url(../../../../../assets/img/home/<USER>
	background-size: 100% 100%;
	width: 20px;
	height: 20px;
	margin-left: 10px;
}

.down {
	background: url(../../../../../assets/img/home/<USER>
	background-size: 100% 100%;
	width: 20px;
	height: 20px;
	margin-left: 10px;
}
</style>
