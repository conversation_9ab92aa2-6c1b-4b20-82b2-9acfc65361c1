
eb52274293845284b5420614e1d31b1246be5020	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.464.1754018536329.js\",\"contentHash\":\"cfc018a92fb6b0d9191f743056cd494e\"}","integrity":"sha512-+/nSxkcT9hMa63LjB8h5g57UWTAcrY1ooR6lGNfT8TDMegCwGM2Mk7bPDTv+m4U0VD420uan4BD48/OFHBtdMQ==","time":1754018575957,"size":18027}