<template>
    <div>
        <div v-if="!isView">
            <!--风险上报-->
            <div style="margin: 10px">
                <span style="font-weight: bold;font-size: 16px;color: #5A5A5F;">基本信息</span>
            </div>
            <Baseinfo :mainData="mainData" :businessDomainData="businessDomainData"
                :expectedRiskLevelData="expectedRiskLevelData" :eventNatureData="eventNatureData"
                :riskClassificationData="riskClassificationData"></Baseinfo>
            <el-divider></el-divider>
            <div style="margin: 10px">
                <el-row>
                    <el-col :span="8">
                        <el-form-item >
                            <template #label>
                                <span style="color: #f56c6c;">* </span>应对过程状态
                            </template>
                            <el-select v-model="mainData.riskMitigationStatus" placeholder="请选择...">
                                <el-option label="未应对" value="2"></el-option>
                                <el-option label="应对中" value="3"> </el-option>
                                <el-option label="已完成" value="4"> </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="16">
                        <el-form-item label="应对过程相关附件" label-width="130px" prop="mainData.respondProcessFiles">
                            <uploadDoc :disabled="isView" :doc-path="docURL" :files.sync="mainData.respondProcessFiles"
                                v-model="mainData.respondProcessFiles" />
                        </el-form-item>
                    </el-col>

                </el-row>
                <!--<span style="font-weight: bold;font-size: 16px;color: #5A5A5F;">应对过程</span>
                <el-select v-model="mainData.riskMitigationStatus" placeholder="请选择...">
                    <el-option label="未应对" value="2"></el-option>
                    <el-option label="应对中" value="3"> </el-option>
                    <el-option label="已完成" value="4"> </el-option>
                </el-select>
                <span>应对过程相关附件</span>
                <uploadDoc
                        :disabled="isView"
                        :doc-path="docURL"
                        :files.sync="mainData.respondProcessFiles"
                        v-model="mainData.respondProcessFiles"
                />-->
                <el-divider></el-divider>
                <div style="display: inline;float: right;margin-top: 10px;margin-right: 30px">
                    <el-link type="primary" class="normal-btn" size="mini" @click="addRow">增行</el-link>
                    <el-link type="danger" class="normal-btn" size="mini" v-show="this.currentRow!=null"
                        @click="delRow">删行</el-link>
                    <el-link type="primary" class="normal-btn" size="mini" v-show="this.currentRow!=null"
                        @click="upRow">上移</el-link>
                    <el-link type="primary" class="normal-btn" size="mini" v-show="this.currentRow!=null"
                        @click="downRow">下移</el-link>
                </div>
            </div>


            <el-table ref="table" :data="mitigationList" border
                style="table-layout: fixed;width: 100%;overflow:auto;height:auto !important;" stripe fit
                highlight-current-row :show-overflow-tooltip="true" @row-click="selectRow">
                <el-table-column type="index" label="序号" align="center"></el-table-column>
                <el-table-column prop="mitigationMeasures" label="应对措施" min-width="300" show-overflow-tooltip>
                    <template slot-scope="{row, $index}">
                        <el-form-item :prop="`mitigationList.${$index}.mitigationMeasures`"
                            :rules="{ required: true, message: '请输入应对措施', trigger: 'blur' }"
                            :class="{ 'has-margin': !row.mitigationMeasures }">
                            <el-input placeholder="请输入" v-model="row.mitigationMeasures" maxlength="300"
                                show-word-limit></el-input>
                        </el-form-item>
                    </template>
                </el-table-column>
                <el-table-column prop="mitigationResults" label="应对结果" min-width="200" show-overflow-tooltip>
                    <template slot-scope="{row, $index}">
                        <el-form-item :prop="`mitigationList.${$index}.mitigationResults`"
                            :rules="{ required: true, message: '请输入应对结果', trigger: 'blur' }"
                            :class="{ 'has-margin': !row.mitigationResults }">
                            <el-input placeholder="请输入" v-model="row.mitigationResults" maxlength="300"
                                show-word-limit></el-input>
                        </el-form-item>
                    </template>
                </el-table-column>
                <el-table-column prop="executionTime" label="执行时间" min-width="120"
                    show-overflow-tooltip></el-table-column>
                <el-table-column prop="executionDepartment" label="执行部门" min-width="200"
                    show-overflow-tooltip></el-table-column>
                <el-table-column prop="executionPerson" label="执行人" min-width="100"
                    show-overflow-tooltip></el-table-column>
            </el-table>
        </div>
        <div v-if="isView">
            <SimpleBoard title="风险应对" style="margin-top: 10px;">
                <el-row>
                    <el-col :span="8">
                        <el-form-item label="应对过程状态">
                            <!--<el-select v-model="mainData.riskMitigationStatus" placeholder="请选择..." disabled>
                                <el-option label="未应对" value="2"></el-option>
                                <el-option label="应对中" value="3"> </el-option>
                                <el-option label="已完成" value="4"> </el-option>
                            </el-select>-->
                            <span v-if="mainData.riskMitigationStatus=='2'">未应对</span>
                            <span v-if="mainData.riskMitigationStatus=='3'">应对中</span>
                            <span v-if="mainData.riskMitigationStatus=='4'">已完成</span>
                        </el-form-item>
                    </el-col>
                    <el-col :span="16">
                        <el-form-item label="应对过程相关附件" label-width="130px">
                            <uploadDoc :disabled="isView" :doc-path="docURL" :files.sync="mainData.respondProcessFiles"
                                v-model="mainData.respondProcessFiles" />
                        </el-form-item>
                    </el-col>

                </el-row>
                <el-divider></el-divider>
                <el-table ref="table" :data="mitigationList" border
                    style="table-layout: fixed;width: 100%;overflow:auto;" stripe fit highlight-current-row
                    :show-overflow-tooltip="true">
                    <el-table-column type="index" label="序号" align="center"></el-table-column>
                    <el-table-column prop="mitigationMeasures" label="应对措施" min-width="300" align="center"
                        show-overflow-tooltip></el-table-column>
                    <el-table-column prop="mitigationResults" label="应对结果" min-width="200" align="center"
                        show-overflow-tooltip></el-table-column>
                    <el-table-column prop="executionTime" label="执行时间" min-width="150" align="center"
                        show-overflow-tooltip></el-table-column>
                    <el-table-column prop="executionDepartment" label="执行部门" min-width="200" align="center"
                        show-overflow-tooltip></el-table-column>
                    <el-table-column prop="executionPerson" label="执行人" min-width="150" align="center"
                        show-overflow-tooltip></el-table-column>
                </el-table>
            </SimpleBoard>
        </div>

    </div>
</template>
<script>
    import SimpleBoard from '@/view/components/SimpleBoard/SimpleBoardTitle'
    import Baseinfo from './Baseinfo'
    import uploadDoc from '@/view/components/UploadDoc/UploadDoc'

    import orgApi from "@/api/_system/org";
    export default{
        name: "FXYD",
        components: {
            SimpleBoard, uploadDoc,Baseinfo
        },
        /*activated() {
            // 长连接页面第二次激活的时候,不会走created方法,会走此方法
            this.baseDataLoad();
        },
        created() {
            // 长连接页面第二次激活的时候,不会走created方法,会走此方法
            this.baseDataLoad();
        },*/
        props: {
            dataState: {
                type: String,
                default: ""
            },
            isView: {
                type: Boolean,
                default: false
            },
            mainData: {
                type: Object,
                default:  function () {
                    return {}
                }
            },
            mitigationList: {
                type: Array,
                default:  function () {
                    return []
                }
            },
            businessDomainData:{
                type:Array,
                default : function () {
                    return [];
                }
            },
            expectedRiskLevelData:{
                type:Array,
                default : function () {
                    return [];
                }
            },
            eventNatureData:{
                type:Array,
                default : function () {
                    return [];
                }
            },
            riskClassificationData:{
                type:Array,
                default : function () {
                    return [];
                }
            },
        },
        data() {
            return {
                docURL: '/complianceRiskWaring',
                rules: [],
                title: '',
                hasAdd: true,
                isHas: true,
                tableData:[],
                currentRow:{},
            }
        },
        methods:{
            addRow(){
                let date = new Date();
                let month = (date.getMonth()+1);
                if (month<10){
                    month = "0"+month;
                }
                let days = date.getDate();
                if(days<10){
                    days = '0' + days;
                }
                let executionTime = date.getFullYear()+"-"+ month +"-"+ days;
                let obj = {'mitigationMeasures':'',"mitigationResults":'',"executionTime":executionTime,'executionDepartment':this.mainData.createDeptName,"executionPerson":this.mainData.createPsnName};
                this.mitigationList.push(obj);
            },
            delRow(){
                let index = this.mitigationList.indexOf(this.currentRow);
                this.mitigationList.splice(index,1);
                this.currentRow = {};
            },
            upRow(){
                if (this.currentRow) {
                    const index = this.mitigationList.indexOf(this.currentRow);
                    if (index > 0) {
                        this.mitigationList.splice(index, 1);
                        this.mitigationList.splice(index - 1, 0, this.currentRow);
                    }
                }
            },
            downRow(){
                if (this.currentRow) {
                    const index = this.mitigationList.indexOf(this.currentRow);
                    if (index < this.mitigationList.length - 1) {
                        this.mitigationList.splice(index, 1);
                        this.mitigationList.splice(index + 1, 0, this.currentRow);
                    }
                }
            },
            selectRow(row,column,event){
                let index = this.mitigationList.indexOf(row);
                this.currentRow = row;
            },

        }
    }
</script>
<style scoped>

    /* Element UI 错误状态保留默认边距 */
    ::v-deep .el-table .el-form-item.is-error {
        margin-bottom: 20px !important; /* Element 默认值 */
    }
    /* 针对 el-table 内的 el-form-item */
    ::v-deep .el-table .el-form-item {
        margin-left: 0 !important;
        margin-bottom: 0 !important;
    }
    /* 或者仅调整内容区域 */
    ::v-deep .el-table .el-form-item__content {
        margin-left: 0 !important;
    }
</style>