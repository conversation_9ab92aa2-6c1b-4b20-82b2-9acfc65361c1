
d66285edecfb5c88f0adc097d3abb59aa901b366	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.219.1754018536329.js\",\"contentHash\":\"b9c3bb3b5520fb0ca6b2d31e07a14a17\"}","integrity":"sha512-NFeUdTrESivSUeAmzD2Pqpd/ImqeqK6l1EI76Y4BJPU8BtoXy/h5jOf/06INtLN6Pt5XbbPruxn+RZ95mWwdoQ==","time":1754018575990,"size":132546}