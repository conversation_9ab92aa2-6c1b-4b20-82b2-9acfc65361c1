<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.klaw.dao.caseDao.SgCaseSuperviseMapper">
    <resultMap id="BaseResultMap" type="com.klaw.entity.caseBean.child.MiddleRelation">
        <id column="r_ID" jdbcType="VARCHAR" property="id"/>
        <result column="RELATION_ID" jdbcType="VARCHAR" property="relationId"/>
        <result column="RELATION_TYPE" jdbcType="VARCHAR" property="relationType"/>
        <result column="ASSOCIATED_ID" jdbcType="VARCHAR" property="associatedId"/>
        <result column="ASSOCIATED_TYPE" jdbcType="VARCHAR" property="associatedType"/>
        <association property="associatedData" javaType="com.klaw.entity.caseBean.CaseRecord">
            <id column="d_ID" jdbcType="VARCHAR" property="id" />
            <result column="PARENT_ID" jdbcType="VARCHAR" property="parentId" />
            <result column="CASE_NAME" jdbcType="VARCHAR" property="caseName" />
            <result column="CASE_PROCESS" jdbcType="LONGVARCHAR" property="caseProcess" />
            <result column="CASE_PROCESS_IDS" jdbcType="LONGVARCHAR" property="caseProcessIds" />
            <result column="CASE_KIND" jdbcType="VARCHAR" property="caseKind" />
            <result column="CASE_KIND_CODE" jdbcType="VARCHAR" property="caseKindCode" />
            <result column="CASE_CURRENT_PROCESS" jdbcType="VARCHAR" property="caseCurrentProcess" />
        </association>
        <association property="relationData" javaType="com.klaw.entity.caseBean.SgCaseSupervise">
            <id column="ID" jdbcType="VARCHAR" property="id"/>
            <result column="SUPERVISE_CODE" jdbcType="VARCHAR" property="superviseCode"/>
            <result column="SUPERVISE_LEADER" jdbcType="VARCHAR" property="superviseLeader"/>
            <result column="SUPERVISE_LEADER_ID" jdbcType="VARCHAR" property="superviseLeaderId"/>
            <result column="SUPERVISE_YEAR" jdbcType="VARCHAR" property="superviseYear"/>
            <result column="SUPERVISE_QUARTER" jdbcType="VARCHAR" property="superviseQuarter"/>
            <result column="DESCRIPTION" jdbcType="CLOB" property="description"/>
            <result column="ATTACHMENT" jdbcType="CLOB" property="attachment"/>
            <result column="CREATE_OGN_ID" jdbcType="VARCHAR" property="createOgnId"/>
            <result column="CREATE_OGN_NAME" jdbcType="VARCHAR" property="createOgnName"/>
            <result column="CREATE_DEPT_ID" jdbcType="VARCHAR" property="createDeptId"/>
            <result column="CREATE_DEPT_NAME" jdbcType="VARCHAR" property="createDeptName"/>
            <result column="CREATE_GROUP_ID" jdbcType="VARCHAR" property="createGroupId"/>
            <result column="CREATE_GROUP_NAME" jdbcType="VARCHAR" property="createGroupName"/>
            <result column="CREATE_PSN_ID" jdbcType="VARCHAR" property="createPsnId"/>
            <result column="CREATE_PSN_NAME" jdbcType="VARCHAR" property="createPsnName"/>
            <result column="CREATE_ORG_ID" jdbcType="VARCHAR" property="createOrgId"/>
            <result column="CREATE_ORG_NAME" jdbcType="VARCHAR" property="createOrgName"/>
            <result column="CREATE_PSN_FULL_ID" jdbcType="VARCHAR" property="createPsnFullId"/>
            <result column="CREATE_PSN_FULL_NAME" jdbcType="VARCHAR" property="createPsnFullName"/>
            <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
            <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime"/>
            <result column="DATA_STATE" jdbcType="VARCHAR" property="dataState"/>
            <result column="DATA_STATE_CODE" jdbcType="DECIMAL" property="dataStateCode"/>
        </association>
    </resultMap>
    <sql id="Base_Column_List">
    m.ID, m.SUPERVISE_CODE, m.SUPERVISE_LEADER, m.SUPERVISE_LEADER_ID, m.SUPERVISE_YEAR, m.SUPERVISE_QUARTER,
    m.DESCRIPTION, m.ATTACHMENT, m.CREATE_OGN_ID, m.CREATE_OGN_NAME, m.CREATE_DEPT_ID,
    m.CREATE_DEPT_NAME, m.CREATE_GROUP_ID, m.CREATE_GROUP_NAME, m.CREATE_PSN_ID, m.CREATE_PSN_NAME,
    m.CREATE_ORG_ID, m.CREATE_ORG_NAME, m.CREATE_PSN_FULL_ID, m.CREATE_PSN_FULL_NAME, m.CREATE_TIME,
    m.UPDATE_TIME, m.DATA_STATE, m.DATA_STATE_CODE,
    r.ID as r_ID, r.RELATION_ID, r.RELATION_TYPE, r.ASSOCIATED_ID, r.ASSOCIATED_TYPE,
    d.ID as d_id, d.PARENT_ID, d.CASE_NAME, d.CASE_PROCESS, d.CASE_PROCESS_IDS, d.CASE_KIND, d.CASE_KIND_CODE, d.CASE_CURRENT_PROCESS
  </sql>
    <select id="queryData" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from SG_CASE_SUPERVISE m left join SG_MIDDLE_RELATION r on m.id = r.RELATION_ID
        left join SG_CASE_RECORDS d on r.ASSOCIATED_ID = d.id
        <where>
            ${ew.sqlSegment}
        </where>
    </select>
</mapper>