<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.klaw.dao.contractDao.contract.BmContractPlaceMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.klaw.entity.contractBean.contract.BmContractPlace">
        <id column="id" property="id"/>
        <result column="parent_id" property="parentId"/>
        <result column="place_type" property="placeType"/>
        <result column="place_time" property="placeTime"/>
        <result column="place_num" property="placeNum"/>
        <result column="place_state_name" property="placeStateName"/>
        <result column="place_state_code" property="placeStateCode"/>
        <result column="place_remark" property="placeRemark"/>
        <result column="other_data" property="otherData"/>
        <result column="create_time" property="createTime"/>
        <result column="create_psn_full_id" property="createPsnFullId"/>
        <result column="create_psn_full_name" property="createPsnFullName"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, parent_id, place_type, place_time, place_num, place_state_name, place_state_code, place_remark, other_data, create_time, create_psn_full_id, create_psn_full_name
    </sql>

</mapper>
