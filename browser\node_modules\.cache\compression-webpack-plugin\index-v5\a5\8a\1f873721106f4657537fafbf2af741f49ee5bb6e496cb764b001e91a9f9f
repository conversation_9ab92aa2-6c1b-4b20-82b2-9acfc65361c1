
9fad3c3a6f1187be59678738dc364f2e3ce08429	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.333.1754018536329.js\",\"contentHash\":\"5d92a82098514a2f623ada4fca5f2783\"}","integrity":"sha512-p76CN4tuEAcqQ2GPUxvGLJa64CgnBtJOJ0NQRYIDI1i8RC4mI2ENeUA7AnI8yd7ODSHIwwTlS52q6l0n76JGkA==","time":1754018575975,"size":111388}