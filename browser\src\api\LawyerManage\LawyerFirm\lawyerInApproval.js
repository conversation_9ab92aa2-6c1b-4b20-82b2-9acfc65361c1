import {request} from '@/api/index'

export default {
  query(data) {
    return request({
      url: '/lawyerInApproval/query',
      method: 'post',
      data
    })
  },
  save(data) {
    return request({
      url: '/lawyerInApproval/save',
      method: 'post',
      data
    })
  },
  queryDataById(data) {
    return request({
      url: '/lawyerInApproval/queryDataById',
      method: 'post',
      data
    })
  },
  delete(data) {
    return request({
      url: '/lawyerInApproval/delete',
      method: 'post',
      data
    })
  },
  distinct(data) {
    return request({
      url: '/lawyerInApproval/distinct',
      method: 'post',
      data
    })
  },
  changeOut(data) {
    return request({
      url: '/lawyerInApproval/changeOut',
      method: 'post',
      data
    })
  }
}
