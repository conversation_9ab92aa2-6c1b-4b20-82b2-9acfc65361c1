<template>
  <FormWindow ref="formWindow" @loadData="loadData">
    <el-container v-loading="loading" style="height: calc(100vh - 84px);">
      <el-header style="padding-top: 10px;padding-bottom: 10px;border-bottom: solid 1px #f7f7f7;background-color: #FCFCFC">
        <span style="text-align: left;font-size: 24px;margin: 0;font-weight: 1000;">律师信息</span>
      </el-header>

      <el-main>
        <el-scrollbar style="height: 100%" wrap-style="overflow-x: hidden;">

          <el-form ref="dataForm" style="padding-right: 10px;" :model="detailData" label-width="100px">

            <SimpleBoardTitle title="基本信息" style="margin-top: 5px;">
              <table class="table_content" style="margin-top: 10px;">
                <tbody>
                <tr>
                  <th colspan="2" class="th_label">律师名称</th>
                  <td colspan="6" class="td_value">{{ detailData.lawyerName }}</td>
                  <th colspan="2" class="th_label">所属律所</th>
                  <td colspan="13" class="td_value">{{ detailData.lawFirm }}</td>
                  <th colspan="1" class="td_value"><img style="width: 100%;height: auto;" :src="detailData.avatar"  alt=""/></th>
                </tr>
                </tbody>

                <tbody>
                <tr>
                  <th colspan="2" class="th_label">性别</th>
                  <td colspan="6" class="td_value">{{ detailData.sex }}</td>
                  <th colspan="2" class="th_label">联系电话</th>
                  <td colspan="6" class="td_value">{{ detailData.lawyerPhone }}</td>
                  <th colspan="2" class="th_label">电子邮箱</th>
                  <td colspan="6" class="td_value">{{ detailData.lawyerEmail }}</td>
                </tr>
                </tbody>

                <tbody>
                <tr>
                  <th colspan="2" class="th_label">从事律师<br>执业时间</th>
                  <td colspan="6" class="td_value">{{ detailData.lawyerTime | parseTime }}</td>
                  <th colspan="2" class="th_label">执业证号</th>
                  <td colspan="14" class="td_value">{{ detailData.charteredNo }}</td>
                </tr>
                </tbody>

                <tbody>
                <tr>
                  <th colspan="2" class="th_label">擅长业务领域</th>
                  <td colspan="22" class="td_value">{{ detailData.beGoodAtDomain }}</td>
                </tr>
                </tbody>

                <tbody>
                <tr>
                  <th colspan="2" class="th_label">主要执业地域</th>
                  <td colspan="22" class="td_value">{{ detailData.majorRegion }}</td>
                </tr>
                </tbody>

                <tbody>
                <tr>
                  <th colspan="2" class="th_label_">备注</th>
                  <td colspan="22" class="td_value_">{{ detailData.remarks }}</td>
                </tr>
                </tbody>

              </table>
            </SimpleBoardTitle>

            <div style="overflow: auto">
              <el-row style="height: 300px;">
                <el-col :span="12">
                  <simple-board style="margin: 0;height: 300px;" title="评价信息" :has-add="hasAdd" :data-state="dataState" >
                    <el-table
                        style="width: 100%"
                        :data="detailData.evaluateList"
                        border
                        stripe
                        :show-overflow-tooltip="true"
                        fit
                        highlight-current-row
                    >
                      <el-table-column label="序号" type="index" align="center" width="60" show-overflow-tooltip />
                      <el-table-column label="评价项目/案件名称" align="center" prop="projectName" show-overflow-tooltip />
                      <el-table-column label="专业水平" align="center" prop="professionalLevel" show-overflow-tooltip />
                      <el-table-column label="服务质量" align="center" prop="serviceQuality" show-overflow-tooltip />
                      <el-table-column label="收费合理" align="center" prop="reasonableCharge" show-overflow-tooltip />
                      <el-table-column label="资源实力" align="center" prop="resourceStrength" show-overflow-tooltip />
                      <el-table-column label="评价分" align="center" prop="score" show-overflow-tooltip />
                    </el-table>
                  </simple-board>
                </el-col>
                <el-col :span="12">
                  <el-card>
                    <div id="myChart" style="width: 100%;height: 300px;overflow: initial" />
                  </el-card>
                </el-col>
              </el-row>
            </div>

            <simple-board style="margin-top: 20px;" title="助理律师" :has-add="hasAdd" :data-state="dataState" v-if="detailData.whetherHostLawyer">
              <el-table
                  style="width: 100%"
                  :data="detailData.lawyerList"
                  border
                  stripe
                  :show-overflow-tooltip="true"
                  fit
                  :height="200"
                  highlight-current-row
              >
                <el-table-column label="序号" type="index" align="center" width="60" show-overflow-tooltip />
                <el-table-column label="姓名" align="center" prop="lawyerName" show-overflow-tooltip />
                <el-table-column label="联系电话" align="center" prop="lawyerPhone" show-overflow-tooltip />
                <el-table-column label="擅长领域" align="center" prop="beGoodAtDomain" show-overflow-tooltip />
                <el-table-column label="备注" align="center" prop="remarks" show-overflow-tooltip />
                <el-table-column label="经办时间" align="center" prop="createTime" show-overflow-tooltip>
                  <template slot-scope="scope">
                    <span>{{ scope.row.createTime | parseTime }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="操作" align="center" width="200px">
                  <template slot-scope="scope">
                    <el-button v-if="dataState === 'view'" size="mini" type="text" @click="look(scope.$index,scope.row)">查看</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </simple-board>


            <simple-board style="margin-top: 20px;" title="代理信息" :has-add="hasAdd" :data-state="dataState" >
                <el-table
                    style="width: 100%"
                    :data="detailData.agentList"
                    border
                    stripe
                    :show-overflow-tooltip="true"
                    fit
                    :height="200"
                    highlight-current-row
                >
                  <el-table-column label="序号" type="index" align="center" width="60" show-overflow-tooltip />
                  <el-table-column label="类型" align="center" prop="selectionTypeName" show-overflow-tooltip />
                  <el-table-column label="名称" align="center" prop="name" show-overflow-tooltip />
                  <el-table-column label="状态" align="center" prop="type" show-overflow-tooltip />
                  <el-table-column label="经办时间" align="center" prop="createTime" show-overflow-tooltip>
                    <template slot-scope="scope">
                      <span>{{ scope.row.createTime | parseTime }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" align="center" width="200px">
                    <template slot-scope="scope">
                      <el-button
                          v-if="dataState === 'view'"
                          size="mini" type="text"
                          @click="lookAgent(scope.$index,scope.row)">查看</el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </simple-board>

            <el-row style="margin-top: 20px;">
              <el-col :span="18">
                <el-form-item label="经办组织">
                  <span class="viewSpan">{{ detailData.createPsnFullName }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="经办时间">
                  <span class="viewSpan">{{ detailData.createTime| parseTime }}</span>
                </el-form-item>
              </el-col>
            </el-row>

          </el-form>
        </el-scrollbar>
      </el-main>
      <el-form ref="lawyerRow" :model="lawyerRow" label-width="80px">
        <!--新增律师人员-->
        <el-dialog :close-on-click-modal="false" title="新增助理律师" :visible.sync="orgVisible" width="50%">
          <div class="el-dialog-div">
            <el-row>
              <el-col :span="24">
                <el-form-item label="律师姓名" prop="lawyerName">
                  <span class="viewSpan">{{ lawyerRow.lawyerName }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="联系电话">
                  <span class="viewSpan">{{ lawyerRow.lawyerPhone }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="擅长领域">
                  <span class="viewSpan">{{ lawyerRow.beGoodAtDomain }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="备注">
                  <text-span class="viewSpan" :text="lawyerRow.remarks" />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
          <span slot="footer" class="dialog-footer">
            <el-button icon="" class="negative-btn" @click="cancel_">取消</el-button>
          </span>
        </el-dialog>
      </el-form>

      <el-form ref="agentRow"
               :model="agentRow"
               >
        <!--新增代理信息-->
        <el-dialog :close-on-click-modal="false" :title="agentRowTitle" :visible.sync="agentDialogVisible" width="50%">
          <div class="el-dialog-div">
            <el-row>
              <el-col :span="24">
                <el-form-item label="类型" prop="selectionTypeId">
                  <el-select v-if="dataState !== 'view'" v-model="agentRow.selectionTypeId"
                             placeholder="请选择" style="width:100%" clearable
                             @change="selectionTypeChange" @clear="selectionTypeClear" >
                    <el-option
                        v-for="item in utils.selectionType_data"
                        :key="item.id"
                        :label="item.dicName"
                        :value="item.id"
                    />
                  </el-select>
                  <span v-else class="viewSpan">{{ agentRow.selectionTypeName }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="名称" prop="name">
                  <el-input v-if="dataState !== 'view'" v-model="agentRow.name"
                            maxlength="50" show-word-limit style="width: 100%"
                            placeholder="请输入..." class="input-with-select"
                            clearable  />
                  <span v-else class="viewSpan">{{ agentRow.relationCaseName }}</span>
                </el-form-item>
              </el-col>

              <el-col :span="12" v-if="agentRow.selectionTypeId === '2'">
                <el-form-item label="关联案件" prop="relationCaseName">
                  <el-input v-if="dataState !== 'view'" v-model="agentRow.relationCaseName" maxlength="20" show-word-limit style="width: 100%" placeholder="请输入..." disabled>
                    <el-button slot="append" icon="el-icon-search" @click="choiceCaseClick" />
                  </el-input>
                  <span v-else class="viewSpan">{{ agentRow.relationCaseName }}</span>
                </el-form-item>
              </el-col>

              <el-col :span="12" v-if="agentRow.selectionTypeId === '2'">
                <el-form-item label="代理阶段">
                  <el-select v-if="dataState !== 'view'" v-model="agentStageIds" multiple style="width: 100%;">
                    <el-option v-for="item in processData" :key="item.code" :label="item.name" :value="item.code" />
                  </el-select>
                  <span v-else class="viewSpan">{{ agentRow.agentStageName }}</span>
                </el-form-item>
              </el-col>

              <el-col :span="12" v-if="agentRow.selectionTypeId === '3'">
                <el-form-item label="开始时间" prop="beginTime">
                  <el-date-picker v-if="dataState !== 'view'"
                                  v-model="agentRow.beginTime"
                                  value-format="yyyy-MM-dd" type="date" style="width: 100%" clearable />
                  <span v-else class="viewSpan">{{ agentRow.beginTime | parseTime }}</span>
                </el-form-item>
              </el-col>

              <el-col :span="12" v-if="agentRow.selectionTypeId === '3'">
                <el-form-item label="结束时间" prop="endTime">
                  <el-date-picker v-if="dataState !== 'view'"
                                  v-model="agentRow.endTime"
                                  value-format="yyyy-MM-dd" type="date" style="width: 100%" clearable />
                  <span v-else class="viewSpan">{{ agentRow.endTime | parseTime }}</span>
                </el-form-item>
              </el-col>

              <el-col :span="24">
                <el-form-item label="详细描述" prop="description">
                  <el-input v-if="dataState !== 'view'" v-model="agentRow.description"
                            type="textarea" placeholder="请输入..."
                            :autosize="{ minRows: 3, maxRows: 6}" maxlength="1000"
                            show-word-limit style="width: 100%" clearable />
                  <text-span v-else class="viewSpan" :text=" agentRow.description" />
                </el-form-item>
              </el-col>

<!--              <el-col :span="24">-->
<!--                <el-form-item label="状态" prop="type">-->
<!--                  <el-select v-if="dataState !== 'view'" v-model="agentRow.type"-->
<!--                             placeholder="请选择" style="width:100%" clearable >-->
<!--                    <el-option-->
<!--                        v-for="item in this.typeData"-->
<!--                        :key="item.id"-->
<!--                        :label="item.name"-->
<!--                        :value="item.name"-->
<!--                    />-->
<!--                  </el-select>-->
<!--                  <span v-else class="viewSpan">{{ agentRow.type }}</span>-->
<!--                </el-form-item>-->
<!--              </el-col>-->

              <el-col :span="24" prop="attachment">
                <el-form-item label="附件"  prop="attachment">
                  <uploadDoc
                      v-model="agentRow.attachment"
                      :files.sync="agentRow.attachment"
                      :disabled="dataState === 'view'"
                      :doc-path="docURL"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
          <span slot="footer" class="dialog-footer">
            <el-button icon="" class="negative-btn" @click="cancelAgent">取消</el-button>
            <el-button v-if="dataState !== 'view'"
                       type="primary" icon="" class="active-btn"
                       @click="agentSure">确定</el-button>
          </span>
        </el-dialog>
      </el-form>

    </el-container>
  </FormWindow>
</template>

<script>
import FormWindow from '@/view/components/FormWindow/FormWindow'
import SimpleBoard from "@/view/components/SimpleBoard/SimpleBoardViewCase"
import UploadDoc from '@/view/components/UploadDoc/UploadDoc'
import dictApi from '@/api/_system/dict'
import lawyerApi from '@/api/LawyerManage/LawyerFirm/lawyer'
import {isZC} from "@/view/utils/constants"
require('echarts/theme/macarons')
import * as echarts from 'echarts'
import resize from '@/view/components/Charts/mixins/resize'
import SimpleBoardTitle from "../../../components/SimpleBoard/SimpleBoardTitle"
import ProcessOpinion from '@/view/components/ProcessOpinion/ProcessOpinion'

export default {
  name: 'FirmLedgerLawyer',
  mixins: [resize],
  inject: ['layout'],
  components: { FormWindow, SimpleBoard, UploadDoc, SimpleBoardTitle, ProcessOpinion },
  computed: {
    agentRowTitle() {
      return this.dataState !== 'view' ? '新增代理信息' : '查看代理信息'
    },
    agentStageIds: {
      set: function(data) {
        this.agentRow.agentStageId = data.join(',')
      },
      get: function() {
        if (this.agentRow.agentStageId) {
          return this.agentRow.agentStageId.split(',')
        }
        return []
      }
    },
    isZc: function() {
      return isZC(this.processName)
    },
    processData: function() {
      if (this.isZc) return [{ code: 'all', name: '全过程' }, ...this.utils.caseProcessData.ZC]
      return [{ code: 'all', name: '全过程' }, ...this.utils.caseProcessData.MSSS]
    },
  },
  data() {
    return {
      chart: null,
      loading: true,
      detailData: {
        id: this.utils.createUUID(),
        lawyerName: null, // 律师姓名
        lawFirm: null, // 律所名称
        lawFirmId: null, // 律所ID
        beginDate: null, // 服务开始时间
        lawyerPhone: null, // 联系电话
        lawyerEmail: null, // 电子邮箱
        lawyerTime: null, // 从事律师执业时间
        charteredNo: null, // 执业证号
        beGoodAtDomain: null, // 擅长领域
        beGoodAtDomainIds: null, // 擅长领域
        majorRegion: null, //主要执业地域
        remarks: null, // 简介/备注
        whetherHostLawyer: true, // 是否主办律师（是-主律师；否-助理律师）
        createOgnName: null, //  经办单位
        createDeptName: null, // 经办部门
        createPsnName: null, // 经办人员
        createTime: null, // 经办时间
        createOgnId: null,
        createDeptId: null,
        createPsnId: null,
        createPsnFullId: null,
        createPsnFullName: null,
        createGroupId: null,
        createGroupName: null,
        createOrgId: null,
        createOrgName: null,
        parentId: null,
        dataSource: 'new',
        sourceId: null,
        whetherMy: 'no',
        updateTime: null, // 更新时间
        lawyerList: [], // 弹框的表格数据源
        agentList: [],  // 代理信息数据源
        evaluateList: [], // 评价信息数据源
        sex:null, //性别
        avatar:null, //头像
      },
      dataState: null,
      docURL: '/firmIn',
      SCLYData: [],
      hasAdd: false,
      orgVisible: false,
      lawyerRow: {
        id: null,
        lawyerName: null, // 律师姓名
        lawFirm: null, // 律所名称
        lawFirmId: null, // 律所ID
        beginDate: null, // 服务开始时间
        lawyerPhone: null, // 联系电话
        lawyerEmail: null, // 电子邮箱
        lawyerTime: null, // 从事律师执业时间
        charteredNo: null, // 执业证号
        beGoodAtDomain: null, // 擅长领域
        beGoodAtDomainIds: null, // 擅长领域
        majorRegion: null, // 主要执业地域
        remarks: null, // 简介/备注
        whetherHostLawyer: true, // 是否主办律师（是-主律师；否-助理律师）
        createOgnName: null, //  经办单位
        createDeptName: null, // 经办部门
        createPsnName: null, // 经办人员
        createTime: null, // 经办时间
        createOgnId: null,
        createDeptId: null,
        createPsnId: null,
        createPsnFullId: null,
        createPsnFullName: null,
        createGroupId: null,
        createGroupName: null,
        createOrgId: null,
        createOrgName: null,
        dataSource: 'new',
        sourceId: null,
        whetherMy: 'no',
        updateTime: null, // 更新时间
        parentId: null
      },
      agentRow: {
        id: null,
        lawFirm: null, // 律所名称
        lawFirmId: null, // 律所ID
        lawyerName: null, // 律师姓名
        lawyerId: null, // 律师id
        charteredNo: null, // 执业证号
        parentId: null,
        selectionTypeId: null, // 类型id
        selectionTypeName: null, // 类型名称
        beginTime: null, // 开始时间
        endTime: null, // 结束时间
        name: null,    // 名称
        type: null, // 状态
        attachment: null, // 附件
        relationCaseName: null, // 关联案件
        relationCaseId: null, // 关联案件ID
        agentStageName: null, // 代理阶段
        agentStageId: null, // 代理阶段ID
        description: null, // 详细描述
        dataSource: 'new',
        createOgnName: null, //  经办单位
        createDeptName: null, // 经办部门
        createPsnName: null, // 经办人员
        createTime: null, // 经办时间
        updateTime: null, // 更新时间
        createOgnId: null,
        createDeptId: null,
        createPsnId: null,
        createPsnFullId: null,
        createPsnFullName: null,
        createGroupId: null,
        createGroupName: null,
        createOrgId: null,
        createOrgName: null,
      },
      index: null,
      lawyerDialogVisible: false,
      agentDialogVisible: false,
      lawyerDialogVisibleSource: null,
      typeData : [
        {id: 1, name: '未完成'},
        {id: 2, name: '已完成'},
        {id: 3, name: '不适用'},
      ],
      evaluateData: null,
    }
  },
  created() {
    this.baseDataLoad()
  },
  mounted() {
  },
  methods:{
    loadData(dataState, dataId) {
      this.dataState = dataState
      lawyerApi.queryDataById({ id: dataId }).then(res => {
        this.detailData = res.data.data
        this.loading = false
        console.log(this.detailData)
      })
      this.getData(dataId)
    },
    baseDataLoad() {
      dictApi.showSelect({
        dicCode: 'LS-SCLY'
      }).then(response => {
        this.SCLYData = response.data.data
      })
    },
    look(index, row) {
      this.lawyerRow = {}
      this.lawyerRow = Object.assign({}, row)
      this.orgVisible = true
    },
    lookAgent(index, row) {
      this.agentRow = {}
      this.agentRow = Object.assign({}, row)
      this.agentDialogVisible = true
    },
    cancel_() {
      this.orgVisible = false
    },
    selectionTypeChange(newVal) {
      this.agentRow.selectionTypeName = this.utils.getDicName(this.utils.selectionType_data, newVal)
      this.agentRow.lawFirmSelectCaseList = []
    },
    selectionTypeClear() {
      this.agentRow.selectionTypeId = null
    },
    choiceCaseClick() {
      this.caseDialogVisible = true
    },
    cancelAgent() {
      this.agentDialogVisible = false
    },
    agentSure() {
      this.$refs['agentRow'].validate((valid) => {
        if (valid) {
          this.agentRow.updateTime = new Date()
          if (this.agentRow.state === 'edit') {
            Object.assign(this.detailData.agentList[this.index], JSON.parse(JSON.stringify(this.agentRow)))
          } else {
            this.detailData.agentList.push(JSON.parse(JSON.stringify(this.agentRow)))
          }
          this.agentDialogVisible = false
        } else {
          return false
        }
      })
    },
    getData(dataId){
      lawyerApi.queryEvaluateData({id: dataId}).then(res => {
        if (res.data.data === undefined || res.data.data === null)
        {
          this.evaluateData = [0, 0, 0, 0]
        }else
        {
          this.evaluateData = res.data.data
        }
        this.initChart()
        console.log(this.detailData)
      })
    },
    initChart() {
      this.chart = echarts.init(document.getElementById('myChart'))

      this.chart.setOption({
        title: {
          text: '律师评分'
        },
        tooltip: {
          trigger: 'axis'
        },
        radar: {
          indicator: [
            { name: '专业水平', max: 100 },
            { name: '服务质量', max: 100 },
            { name: '收费合理', max: 100 },
            { name: '资源实力', max: 100 },
          ],
        },
        series: [
          {
            type: 'radar',
            tooltip: {
              trigger: 'item'
            },
            areaStyle: {},
            data: [
              {
                value: this.evaluateData,
                name: ' '
              },
            ]
          }
        ]
      })
    },
  }
}
</script>


<style>
.el-dialog-div {
  height: 40vh;
  overflow: auto;
}
</style>