
3f9dbc0645097d63330b5a3f28495d407eb653e5	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.78.1754018536329.js\",\"contentHash\":\"a1af0aa0837c4ef0424406b3e37f0130\"}","integrity":"sha512-fc2rDLYDW/Fgqpw6thvcChvV8SX59yE+eRrF1tUzbmj2tS4yzsNyxRtgs0+X84G3X18IM+nVBhlXPmoc+PrJ+w==","time":1754018575955,"size":58149}