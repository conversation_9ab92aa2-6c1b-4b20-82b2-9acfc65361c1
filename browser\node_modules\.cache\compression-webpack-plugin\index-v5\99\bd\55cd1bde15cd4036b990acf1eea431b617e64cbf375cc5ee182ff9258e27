
25bdf9169b5fcc3defde6d4cf10c9ab31ef05983	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.58.1754018536329.js\",\"contentHash\":\"68fee90dbacd52a13bb89a36d41d309a\"}","integrity":"sha512-LHmMFWey0qA77pniYP4+N8n53pJlCEvohaNffV3zhg6pKfu6rVYgCUm+XHWb6OyvyXZ6i02OhcuA/Gmh2AENAQ==","time":1754018576262,"size":555400}