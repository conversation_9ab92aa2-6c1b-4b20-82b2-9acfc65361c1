import {request} from '@/api/index'

export default {
  query(data) {
    return request({
      url: '/bm-contract-close/query',
      method: 'post',
      data
    })
  },
  save(data) {
    return request({
      url: '/bm-contract-close/save',
      method: 'post',
      data
    })
  },
  delete(data) {
    return request({
      url: '/bm-contract-close/delete',
      method: 'post',
      data
    })
  },
  queryById(data) {
    return request({
      url: '/bm-contract-close/queryById',
      method: 'post',
      data
    })
  },
  queryCloseById(data) {
    return request({
      url: '/bm-contract-close/queryCloseById',
      method: 'post',
      data
    })
  },
  queryContractByCode(data) {
    return request({
      url: '/bm-contract-close/queryContractByCode',
      method: 'post',
      data
    })
  },
  setParam(data) {
    return request({
      url: '/bm-contract-close/setParam',
      method: 'post',
      data
    })
  },
  queryDialog(data) {
    return request({
      url: '/bm-contract-close/queryDialog',
      method: 'post',
      data
    })
  },
}