<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.klaw.dao.lawyerDao.LawFirmOutApprovalMainMapper">
    <resultMap id="BaseResultMap" type="com.klaw.entity.lawyerBean.LawFirmOutApprovalMain">
        <id column="ID" jdbcType="VARCHAR" property="id"/>
        <result column="CREATE_OGN_ID" jdbcType="VARCHAR" property="createOgnId"/>
        <result column="CREATE_OGN_NAME" jdbcType="VARCHAR" property="createOgnName"/>
        <result column="CREATE_DEPT_ID" jdbcType="VARCHAR" property="createDeptId"/>
        <result column="CREATE_DEPT_NAME" jdbcType="VARCHAR" property="createDeptName"/>
        <result column="CREATE_GROUP_ID" jdbcType="VARCHAR" property="createGroupId"/>
        <result column="CREATE_GROUP_NAME" jdbcType="VARCHAR" property="createGroupName"/>
        <result column="CREATE_PSN_ID" jdbcType="VARCHAR" property="createPsnId"/>
        <result column="CREATE_PSN_NAME" jdbcType="VARCHAR" property="createPsnName"/>
        <result column="CREATE_ORG_ID" jdbcType="VARCHAR" property="createOrgId"/>
        <result column="CREATE_ORG_NAME" jdbcType="VARCHAR" property="createOrgName"/>
        <result column="CREATE_PSN_FULL_ID" jdbcType="VARCHAR" property="createPsnFullId"/>
        <result column="CREATE_PSN_FULL_NAME" jdbcType="VARCHAR" property="createPsnFullName"/>
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="DATA_STATE" jdbcType="VARCHAR" property="dataState"/>
        <result column="DATA_STATE_CODE" jdbcType="DECIMAL" property="dataStateCode"/>
        <result column="CREATE_OGN_IDOA" jdbcType="VARCHAR" property="createOgnIdoa"/>
        <result column="CREATE_OGN_NAMEOA" jdbcType="VARCHAR" property="createOgnNameoa"/>
        <result column="CREATE_DEPT_IDOA" jdbcType="VARCHAR" property="createDeptIdoa"/>
        <result column="CREATE_DEPT_NAMEOA" jdbcType="VARCHAR" property="createDeptNameoa"/>
        <result column="CREATE_GROUP_IDOA" jdbcType="VARCHAR" property="createGroupIdoa"/>
        <result column="CREATE_GROUP_NAMEOA" jdbcType="VARCHAR" property="createGroupNameoa"/>
        <result column="CREATE_PSN_IDOA" jdbcType="VARCHAR" property="createPsnIdoa"/>
        <result column="CREATE_PSN_NAMEOA" jdbcType="VARCHAR" property="createPsnNameoa"/>
        <result column="CREATE_ORG_IDOA" jdbcType="VARCHAR" property="createOrgIdoa"/>
        <result column="CREATE_ORG_NAMEOA" jdbcType="VARCHAR" property="createOrgNameoa"/>
        <result column="CREATE_PSN_FULL_IDOA" jdbcType="VARCHAR" property="createPsnFullIdoa"/>
        <result column="CREATE_PSN_FULL_NAMEOA" jdbcType="VARCHAR" property="createPsnFullNameoa"/>
        <result column="OUT_TYPE_NAME" jdbcType="VARCHAR" property="outTypeName"/>
        <result column="OUT_TYPE_ID" jdbcType="VARCHAR" property="outTypeId"/>
        <result column="FAIL_NAME" jdbcType="VARCHAR" property="failName"/>
        <result column="FAIL_ID" jdbcType="VARCHAR" property="failId"/>
        <result column="DESCRIPTION" jdbcType="CLOB" property="description"/>
        <result column="ATTACHMENT" jdbcType="CLOB" property="attachment"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        ID, CREATE_OGN_ID, CREATE_OGN_NAME, CREATE_DEPT_ID, CREATE_DEPT_NAME, CREATE_GROUP_ID,
        CREATE_GROUP_NAME, CREATE_PSN_ID, CREATE_PSN_NAME, CREATE_ORG_ID, CREATE_ORG_NAME,
        CREATE_PSN_FULL_ID, CREATE_PSN_FULL_NAME, CREATE_TIME, DATA_STATE, DATA_STATE_CODE,
        CREATE_OGN_IDOA, CREATE_OGN_NAMEOA, CREATE_DEPT_IDOA, CREATE_DEPT_NAMEOA, CREATE_GROUP_IDOA,
        CREATE_GROUP_NAMEOA, CREATE_PSN_IDOA, CREATE_PSN_NAMEOA, CREATE_ORG_IDOA, CREATE_ORG_NAMEOA,
        CREATE_PSN_FULL_IDOA, CREATE_PSN_FULL_NAMEOA, OUT_TYPE_NAME, OUT_TYPE_ID, FAIL_NAME,
        FAIL_ID, DESCRIPTION, ATTACHMENT
    </sql>


    <resultMap id="queryResultMap" type="com.klaw.entity.lawyerBean.LawFirmOutApprovalDetail">
        <id column="ID" jdbcType="VARCHAR" property="id"/>
        <result column="CREATE_OGN_ID" jdbcType="VARCHAR" property="createOgnId"/>
        <result column="CREATE_OGN_NAME" jdbcType="VARCHAR" property="createOgnName"/>
        <result column="CREATE_DEPT_ID" jdbcType="VARCHAR" property="createDeptId"/>
        <result column="CREATE_DEPT_NAME" jdbcType="VARCHAR" property="createDeptName"/>
        <result column="CREATE_GROUP_ID" jdbcType="VARCHAR" property="createGroupId"/>
        <result column="CREATE_GROUP_NAME" jdbcType="VARCHAR" property="createGroupName"/>
        <result column="CREATE_PSN_ID" jdbcType="VARCHAR" property="createPsnId"/>
        <result column="CREATE_PSN_NAME" jdbcType="VARCHAR" property="createPsnName"/>
        <result column="CREATE_ORG_ID" jdbcType="VARCHAR" property="createOrgId"/>
        <result column="CREATE_ORG_NAME" jdbcType="VARCHAR" property="createOrgName"/>
        <result column="CREATE_PSN_FULL_ID" jdbcType="VARCHAR" property="createPsnFullId"/>
        <result column="CREATE_PSN_FULL_NAME" jdbcType="VARCHAR" property="createPsnFullName"/>
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="DATA_STATE" jdbcType="VARCHAR" property="dataState"/>
        <result column="DATA_STATE_CODE" jdbcType="DECIMAL" property="dataStateCode"/>
        <result column="CREATE_OGN_IDOA" jdbcType="VARCHAR" property="createOgnIdoa"/>
        <result column="CREATE_OGN_NAMEOA" jdbcType="VARCHAR" property="createOgnNameoa"/>
        <result column="CREATE_DEPT_IDOA" jdbcType="VARCHAR" property="createDeptIdoa"/>
        <result column="CREATE_DEPT_NAMEOA" jdbcType="VARCHAR" property="createDeptNameoa"/>
        <result column="CREATE_GROUP_IDOA" jdbcType="VARCHAR" property="createGroupIdoa"/>
        <result column="CREATE_GROUP_NAMEOA" jdbcType="VARCHAR" property="createGroupNameoa"/>
        <result column="CREATE_PSN_IDOA" jdbcType="VARCHAR" property="createPsnIdoa"/>
        <result column="CREATE_PSN_NAMEOA" jdbcType="VARCHAR" property="createPsnNameoa"/>
        <result column="CREATE_ORG_IDOA" jdbcType="VARCHAR" property="createOrgIdoa"/>
        <result column="CREATE_ORG_NAMEOA" jdbcType="VARCHAR" property="createOrgNameoa"/>
        <result column="CREATE_PSN_FULL_IDOA" jdbcType="VARCHAR" property="createPsnFullIdoa"/>
        <result column="CREATE_PSN_FULL_NAMEOA" jdbcType="VARCHAR" property="createPsnFullNameoa"/>
        <result column="LAWYER_FIRM" jdbcType="VARCHAR" property="lawyerFirm"/>
        <result column="LAWYER_FIRM_ID" jdbcType="VARCHAR" property="lawyerFirmId"/>
        <result column="REGISTER_ADDRESS" jdbcType="VARCHAR" property="registerAddress"/>
        <result column="FUNCTIONARY" jdbcType="VARCHAR" property="functionary"/>
        <result column="POSTAL_CODE" jdbcType="VARCHAR" property="postalCode"/>
        <result column="ISSUING_AUTHORITY" jdbcType="VARCHAR" property="issuingAuthority"/>
        <result column="LICENSE_CODE" jdbcType="VARCHAR" property="licenseCode"/>
        <result column="LICENSE_NUMBER" jdbcType="VARCHAR" property="licenseNumber"/>
        <result column="REGISTERED_CAPITAL" jdbcType="VARCHAR" property="registeredCapital"/>
        <result column="FOUND_TIME" jdbcType="TIMESTAMP" property="foundTime"/>
        <result column="WORK_NUMBER" jdbcType="DECIMAL" property="workNumber"/>
        <result column="APPLY_DEPT_NAME" jdbcType="VARCHAR" property="applyDeptName"/>
        <result column="APPLY_DEPT_ID" jdbcType="VARCHAR" property="applyDeptId"/>
        <result column="ANNUAL_INSPECTION_NAME" jdbcType="VARCHAR" property="annualInspectionName"/>
        <result column="ANNUAL_INSPECTION_ID" jdbcType="VARCHAR" property="annualInspectionId"/>
        <result column="NO_RESPONSE_TIMES" jdbcType="DECIMAL" property="noResponseTimes"/>
        <result column="WHETHER_MY" jdbcType="VARCHAR" property="whetherMy"/>
        <association property="lawFirmOutApprovalMain" javaType="com.klaw.entity.lawyerBean.LawFirmOutApprovalMain">
            <id column="ID_" jdbcType="VARCHAR" property="id"/>
            <result column="CREATE_OGN_ID_" jdbcType="VARCHAR" property="createOgnId"/>
            <result column="CREATE_OGN_NAME_" jdbcType="VARCHAR" property="createOgnName"/>
            <result column="CREATE_DEPT_ID_" jdbcType="VARCHAR" property="createDeptId"/>
            <result column="CREATE_DEPT_NAME_" jdbcType="VARCHAR" property="createDeptName"/>
            <result column="CREATE_GROUP_ID_" jdbcType="VARCHAR" property="createGroupId"/>
            <result column="CREATE_GROUP_NAME_" jdbcType="VARCHAR" property="createGroupName"/>
            <result column="CREATE_PSN_ID_" jdbcType="VARCHAR" property="createPsnId"/>
            <result column="CREATE_PSN_NAME_" jdbcType="VARCHAR" property="createPsnName"/>
            <result column="CREATE_ORG_ID_" jdbcType="VARCHAR" property="createOrgId"/>
            <result column="CREATE_ORG_NAME_" jdbcType="VARCHAR" property="createOrgName"/>
            <result column="CREATE_PSN_FULL_ID_" jdbcType="VARCHAR" property="createPsnFullId"/>
            <result column="CREATE_PSN_FULL_NAME_" jdbcType="VARCHAR" property="createPsnFullName"/>
            <result column="CREATE_TIME_" jdbcType="TIMESTAMP" property="createTime"/>
            <result column="DATA_STATE_" jdbcType="VARCHAR" property="dataState"/>
            <result column="DATA_STATE_CODE_" jdbcType="DECIMAL" property="dataStateCode"/>
            <result column="CREATE_OGN_IDOA_" jdbcType="VARCHAR" property="createOgnIdoa"/>
            <result column="CREATE_OGN_NAMEOA_" jdbcType="VARCHAR" property="createOgnNameoa"/>
            <result column="CREATE_DEPT_IDOA_" jdbcType="VARCHAR" property="createDeptIdoa"/>
            <result column="CREATE_DEPT_NAMEOA_" jdbcType="VARCHAR" property="createDeptNameoa"/>
            <result column="CREATE_GROUP_IDOA_" jdbcType="VARCHAR" property="createGroupIdoa"/>
            <result column="CREATE_GROUP_NAMEOA_" jdbcType="VARCHAR" property="createGroupNameoa"/>
            <result column="CREATE_PSN_IDOA_" jdbcType="VARCHAR" property="createPsnIdoa"/>
            <result column="CREATE_PSN_NAMEOA_" jdbcType="VARCHAR" property="createPsnNameoa"/>
            <result column="CREATE_ORG_IDOA_" jdbcType="VARCHAR" property="createOrgIdoa"/>
            <result column="CREATE_ORG_NAMEOA_" jdbcType="VARCHAR" property="createOrgNameoa"/>
            <result column="CREATE_PSN_FULL_IDOA_" jdbcType="VARCHAR" property="createPsnFullIdoa"/>
            <result column="CREATE_PSN_FULL_NAMEOA_" jdbcType="VARCHAR" property="createPsnFullNameoa"/>
            <result column="OUT_TYPE_NAME_" jdbcType="VARCHAR" property="outTypeName"/>
            <result column="OUT_TYPE_ID_" jdbcType="VARCHAR" property="outTypeId"/>
            <result column="FAIL_NAME_" jdbcType="VARCHAR" property="failName"/>
            <result column="FAIL_ID_" jdbcType="VARCHAR" property="failId"/>
            <result column="DESCRIPTION_" jdbcType="CLOB" property="description"/>
            <result column="ATTACHMENT_" jdbcType="CLOB" property="attachment"/>
        </association>
    </resultMap>

    <select id="queryPageData" resultMap="queryResultMap">
        select
        d.ID,d.CREATE_OGN_ID,d.CREATE_OGN_NAME,d.CREATE_DEPT_ID,d.CREATE_DEPT_NAME,d.CREATE_GROUP_ID,d.CREATE_GROUP_NAME,
        d.CREATE_PSN_ID,d.CREATE_PSN_NAME,d.CREATE_ORG_ID,d.CREATE_ORG_NAME,d.CREATE_PSN_FULL_ID,d.CREATE_PSN_FULL_NAME,
        d.CREATE_TIME,d.DATA_STATE,d.DATA_STATE_CODE,d.CREATE_OGN_IDOA,d.CREATE_OGN_NAMEOA,d.CREATE_DEPT_IDOA,d.CREATE_DEPT_NAMEOA,
        d.CREATE_GROUP_IDOA,d.CREATE_GROUP_NAMEOA,d.CREATE_PSN_IDOA,d.CREATE_PSN_NAMEOA,d.CREATE_ORG_IDOA,d.CREATE_ORG_NAMEOA,
        d.CREATE_PSN_FULL_IDOA,d.CREATE_PSN_FULL_NAMEOA,
        d.LAWYER_FIRM,d.LAWYER_FIRM_ID,d.REGISTER_ADDRESS,d.FUNCTIONARY,d.POSTAL_CODE,d.ISSUING_AUTHORITY,
        d.LICENSE_CODE,d.LICENSE_NUMBER,d.REGISTERED_CAPITAL,d.FOUND_TIME,d.WORK_NUMBER,d.APPLY_DEPT_NAME,
        d.APPLY_DEPT_ID,d.ANNUAL_INSPECTION_NAME,d.ANNUAL_INSPECTION_ID,d.NO_RESPONSE_TIMES,d.WHETHER_MY,
        d.ANNUAL_INSPECTION_DESC, d.LAW_FIRM_PHONE, d.LAW_FIRM_EMAIL, d.LAW_FIRM_LEVEL, d.LAW_FIRM_TYPE, d.INTRODUCTION, d.REMARK,
        m.ID ID_,m.CREATE_OGN_ID CREATE_OGN_ID_,m.CREATE_OGN_NAME CREATE_OGN_NAME_,m.CREATE_DEPT_ID
        CREATE_DEPT_ID_,m.CREATE_DEPT_NAME CREATE_DEPT_NAME_,m.CREATE_GROUP_ID CREATE_GROUP_ID_,m.CREATE_GROUP_NAME
        CREATE_GROUP_NAME_,m.CREATE_PSN_ID CREATE_PSN_ID_,m.CREATE_PSN_NAME CREATE_PSN_NAME_,m.CREATE_ORG_ID
        CREATE_ORG_ID_,m.CREATE_ORG_NAME CREATE_ORG_NAME_,m.CREATE_PSN_FULL_ID
        CREATE_PSN_FULL_ID_,m.CREATE_PSN_FULL_NAME CREATE_PSN_FULL_NAME_,m.CREATE_TIME CREATE_TIME_,m.DATA_STATE
        DATA_STATE_,m.DATA_STATE_CODE DATA_STATE_CODE_,m.CREATE_OGN_IDOA CREATE_OGN_IDOA_,m.CREATE_OGN_NAMEOA
        CREATE_OGN_NAMEOA_,m.CREATE_DEPT_IDOA CREATE_DEPT_IDOA_,m.CREATE_DEPT_NAMEOA
        CREATE_DEPT_NAMEOA_,m.CREATE_GROUP_IDOA CREATE_GROUP_IDOA_,m.CREATE_GROUP_NAMEOA
        CREATE_GROUP_NAMEOA_,m.CREATE_PSN_IDOA CREATE_PSN_IDOA_,m.CREATE_PSN_NAMEOA CREATE_PSN_NAMEOA_,m.CREATE_ORG_IDOA
        CREATE_ORG_IDOA_,m.CREATE_ORG_NAMEOA CREATE_ORG_NAMEOA_,m.CREATE_PSN_FULL_IDOA
        CREATE_PSN_FULL_IDOA_,m.CREATE_PSN_FULL_NAMEOA CREATE_PSN_FULL_NAMEOA_,m.OUT_TYPE_NAME
        OUT_TYPE_NAME_,m.OUT_TYPE_ID OUT_TYPE_ID_,m.FAIL_NAME FAIL_NAME_,m.FAIL_ID FAIL_ID_,m.DESCRIPTION
        DESCRIPTION_,m.ATTACHMENT ATTACHMENT_, m.TYPE_NAME TYPE_NAME_, m.TYPE_CODE TYPE_CODE_, m.SEQUENCE_CODE SEQUENCE_CODE_

        from SG_LAW_FIRM_OUT_APPROVAL_DETAIL d right join SG_LAW_FIRM_OUT_APPROVAL_MAIN m
        on m.id=d.parent_id
        <where>
            ${ew.sqlSegment}
        </where>
    </select>
</mapper>