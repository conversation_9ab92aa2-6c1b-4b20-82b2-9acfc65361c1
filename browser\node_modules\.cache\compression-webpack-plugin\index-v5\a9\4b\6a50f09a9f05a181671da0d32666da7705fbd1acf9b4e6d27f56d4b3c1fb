
8ffc4f7bd15c35ee3daf2275b7fe23c6b5f42276	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.395.1754018536329.js\",\"contentHash\":\"aaf6a5fac84f27e3c20275ae1f93f40d\"}","integrity":"sha512-dV12ljgwuqkuAFJh/MMzNdmpOcEFYdUv1DI+nB4UWnxa43PGtO3whh3Z7QXL29gRcUZMfBbfMwVvAHYTRv+a/g==","time":1754018576022,"size":131546}