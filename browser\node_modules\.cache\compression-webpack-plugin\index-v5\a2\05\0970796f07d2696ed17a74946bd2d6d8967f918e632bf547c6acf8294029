
4049a08eebc79b2caaf6ce9958ad593925d77294	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.312.1754018536329.js\",\"contentHash\":\"6632e4a70370ca8c650ae0c617281131\"}","integrity":"sha512-6RSKgLJKZy/ex3GyGgwaYs6wckX/0LRFP7tjH/qxNuaSl+1l3L0D8/jt2C/w3us07vUJc18oOPaoKuYrhlJ44w==","time":1754018575974,"size":108540}