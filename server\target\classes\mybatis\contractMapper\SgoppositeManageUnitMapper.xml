<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.klaw.dao.contractDao.SgOppositeManageUnitMapper">

    <select id="queryPageList" resultType="java.util.Map">
        select
        a.*,
        b.*
        from
        (
        select
        o.id ,
        k.listed_Type as "listedType" ,
        k.listed_Reason as "listedReason" ,
        k.data_state as "blackdataState",
        k.data_state_code as "blackdataStateCode",
        k.create_time as "createTime"
        from
        SG_OPPOSITE_MANAGE_OGN o,
        Sg_Opposite_Manage_Black k
        where
        o.id = k.parent_id
        and k.data_state_code = 2
        and k.create_ogn_name = #{createOgnName}
        )
        a
        left join
        (
        select
        o.id as oid ,
        o.code as "code" ,
        o.business_name as "businessName" ,
        o.data_state as "dataState" ,
        o.data_state_code as "dataStateCode",
        o.create_ogn_name as "createOgnName",
        o.create_psn_name as "createPsnName",
        count(o.id) as num
        from
        SG_OPPOSITE_MANAGE_OGN o
        left join Sg_Opposite_Manage_Black k
        on
        k.parent_id = o.id
        where
        o.create_ogn_name = #{createOgnName}
        group by
        o.id ,
        o.code ,
        o.business_name ,
        o.data_state ,
        o.data_state_code,
        o.create_ogn_name,
        o.create_psn_name
        )
        b on a.id = b.oid
        <where>
            ${ew.sqlSegment}
        </where>
    </select>
</mapper>
