
dfefbdb10ae386650b40fef8090264d29a802897	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.8.1754018536329.js\",\"contentHash\":\"a1ccfdcec66adff094479899b63f94df\"}","integrity":"sha512-bQ/T/U51srDnSrU+uPXx49Ht/pSjsyQVkgn90vcqQ0bwBftvBWzOpJDcSx4QtbXpeN1K7Lr2fGEY1lz4JCF9ug==","time":1754018576470,"size":3772926}