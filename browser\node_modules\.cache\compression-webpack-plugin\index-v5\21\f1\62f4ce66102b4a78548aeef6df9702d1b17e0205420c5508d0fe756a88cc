
8af9f2c911a41d4e3f616b6907ec6a88352bd867	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.473.1754018536329.js\",\"contentHash\":\"5de2891b21461e829fc92bf314589f67\"}","integrity":"sha512-yflkp8bGDP6kcRlfwKDYiecG4vrAEhLWdIcA0BoRgVwBzTMVnp7BBHSCkrTu6f5W98qYowoychRxZ+oo1YTj2Q==","time":1754018575957,"size":13534}