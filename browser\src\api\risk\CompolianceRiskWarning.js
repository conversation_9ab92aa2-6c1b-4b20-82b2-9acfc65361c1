import { request } from '@/api/index'

export default {

    updateView(tableId, psnId) {
        return request({
            url: '/riskWarning/updateView/' + tableId + "/" + psnId,
            method: 'get'
        })
    },
    updateHandle(tableId, psnId) {
        return request({
            url: '/riskWarning/updateHandle/' + tableId + "/" + psnId,
            method: 'get'
        })
    },
    exportViewTypeList(data) {
        return request({
            url: '/riskWarning/exportViewTypeList/' + data,
            method: 'post',
            responseType: 'blob'
        })
    },
    exportHandleTypeList(data) {
        return request({
            url: '/riskWarning/exportHandleTypeList/' + data,
            method: 'post',
            responseType: 'blob'
        })
    },
    //查看风险预警信息的接收人查看信息
    queryViewList(data) {
        return request({
            url: '/riskWarning/queryViewList',
            method: 'post',
            data
        })
    },

    queryHandleList(data) {
        return request({
            url: '/riskWarning/queryHandleList',
            method: 'post',
            data
        })
    },
    query(data) {
        return request({
            url: '/riskWarning/query',
            method: 'post',
            data
        })
    },

    queryDatabase(data) {
        return request({
            url: '/riskWarning/queryDatabase',
            method: 'post',
            data
        })
    },
    query2(data) {
        return request({
            url: '/riskWarning/query2',
            method: 'post',
            data
        })
    },

    getOrgList() {
        return request({
            url: '/sys_org/queryAllOrg',
            method: 'get'
        })
    },

    getOrgPersonList(data) {
        return request({
            url: '/sys_org/queryPersonByOrg/' + data,
            method: 'get'
        })
    },

    queryByStandingBook(data) {
        return request({
            url: '/riskWarning/standingBook/query',
            method: 'post',
            data
        })
    },

    export(data) {
        return request({
            url: '/riskWarning/export',
            method: 'post',
            data,
            responseType: 'blob'
        })
    },

    deletebyid(data) {
        return request({
            url: '/riskWarning/delete/' + data,
            method: 'delete'
        })
    },

    add(data) {
        return request({
            url: '/riskWarning/add',
            method: 'post',
            data
        })
    },

    queryById(data) {
        return request({
            url: '/riskWarning/queryById/' + data,
            method: 'get'
        })
    },
    queryBaseById(data) {
        return request({
            url: '/riskWarning/queryBaseById/' + data,
            method: 'get'
        })
    },

    update(data) {
        return request({
            url: '/riskWarning/update',
            method: 'post',
            data
        })
    }
}