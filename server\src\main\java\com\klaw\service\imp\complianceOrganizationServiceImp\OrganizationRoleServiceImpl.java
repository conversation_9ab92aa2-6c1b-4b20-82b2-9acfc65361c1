package com.klaw.service.imp.complianceOrganizationServiceImp;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.klaw.dao.complianceOrganizationDao.OrganizationRoleMapper;
import com.klaw.entity.complianceOrganizationBean.OrganizationMember;
import com.klaw.entity.complianceOrganizationBean.OrganizationRole;
import com.klaw.service.complianceOrganizationService.OrganizationMemberService;
import com.klaw.service.complianceOrganizationService.OrganizationRoleService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【organization_role(组织角色表)】的数据库操作Service实现
 * @createDate 2024-12-05 16:06:00
 */
@Service
public class OrganizationRoleServiceImpl extends ServiceImpl<OrganizationRoleMapper, OrganizationRole>
        implements OrganizationRoleService {

    @Resource
    private OrganizationMemberService organizationMemberService;

    @Override
    public void deleteRole(String id) {
        // 首先删除所有子节点并删除节点下的人员信息
        deleteChildren(id);
        removeById(id);
        deleteRoleMember(id);
    }

    private void deleteRoleMember(String id) {
        QueryWrapper<OrganizationMember> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("role_id", id);
        organizationMemberService.remove(queryWrapper);
    }

    private void deleteChildren(String id) {
        List<OrganizationRole> children = listChildren(id);
        for (OrganizationRole child : children) {
            deleteRoleMember(child.getId());
        }
    }

    public List<OrganizationRole> listChildren(String parentId) {
        return list(new QueryWrapper<OrganizationRole>().eq("parent_id", parentId));
    }
}




