package com.klaw.service.imp.complianceRiskServiceImp;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.klaw.dao.complianceRiskDao.ComplianceRegulationsLibraryMapper;
import com.klaw.dao.complianceRiskDao.CulturePropagandaMapper;
import com.klaw.entity.complianceRiskBean.ComplianceRegulationsLibraryEntity;
import com.klaw.entity.complianceRiskBean.CulturePropagandaEntity;
import com.klaw.service.complianceRiskService.ComplianceRegulationsLibraryService;
import com.klaw.service.complianceRiskService.CulturePropagandaService;
import com.klaw.utils.DataAuthUtils;
import com.klaw.utils.PageUtils;
import com.klaw.vo.Json;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class ComplianceRegulationsLibraryServiceImpl extends ServiceImpl<ComplianceRegulationsLibraryMapper, ComplianceRegulationsLibraryEntity> implements ComplianceRegulationsLibraryService {


    @Override
    public Page<ComplianceRegulationsLibraryEntity> queryPageData(JSONObject json) {
        QueryWrapper<ComplianceRegulationsLibraryEntity> wrapper = new QueryWrapper<>();
        getFilter(json, wrapper);
        return page(new PageUtils<>(json), wrapper);
    }



    public void getFilter(JSONObject json, QueryWrapper<ComplianceRegulationsLibraryEntity> wrapper) {
        boolean isQuery = json.containsKey("isQuery") ? json.getBoolean("isQuery") : false;
        String orgId = json.containsKey("orgId") ? json.getString("orgId") : null;
        String reviewStatus = json.containsKey("reviewStatus") ? json.getString("reviewStatus") : null;
        // 政策法规名称
        String regulationName = json.containsKey("regulationName") ? json.getString("regulationName") : null;

        // 模糊搜索值
        String fuzzyValue = json.containsKey("fuzzyValue") ? json.getString("fuzzyValue") : null;
        // 排序字段
        String sortName = json.containsKey("sortName") ? json.getString("sortName") : null;

        // 排序字段是否升序
        boolean order = json.containsKey("order") ? json.getBoolean("order") : false;

        if (StringUtils.isNotBlank(fuzzyValue)) {
            wrapper.and(i -> i.like("regulation_name", fuzzyValue));
        }
        // 应用查询条件
        if (StringUtils.isNotBlank(regulationName)) {
            wrapper.and(i -> i.like("regulation_name", regulationName));
        }

        if (StringUtils.isNotBlank(reviewStatus)) {
            wrapper.and(i -> i.eq("review_status", reviewStatus));
        }

        //台账权限
        if (isQuery){
            Long functionId = DataAuthUtils.getFunctionIdByCode("zcfgk_tz");
            DataAuthUtils.dataPermSql(wrapper, null, functionId, orgId);
        }else {
            //经办人权限隔离
            wrapper.eq("create_org_id", orgId);
        }

        if (StringUtils.isNotBlank(sortName) && order) {
            wrapper.orderByAsc(sortName);
        } else if (StringUtils.isNotBlank(sortName)) {
            wrapper.orderByDesc(sortName);
        }
        //按创建时间倒序显示
        wrapper.orderByDesc("effective_date");
    }


    @Override
    public ComplianceRegulationsLibraryEntity queryDataById(String id) {
        ComplianceRegulationsLibraryEntity complianceRegulationsLibraryEntity = getById(id);
        return complianceRegulationsLibraryEntity;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Json deleteDataById(String id) {
        boolean flag = removeById(id);
        if (!flag) {
            return Json.fail().msg("未找到需要删除的数据");
        }
        return Json.succ().msg("删除成功!");
    }
}



