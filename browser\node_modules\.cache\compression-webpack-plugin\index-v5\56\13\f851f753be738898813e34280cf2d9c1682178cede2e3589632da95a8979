
302a023823c83d75ef6028c2ec76e7fea52734d6	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.128.1754018536329.js\",\"contentHash\":\"2c59ec27d965a65f56a3215c9c554bb9\"}","integrity":"sha512-Im8/amBOEzR7xvHKDF+mOVm+JgdvoGMY9ljVVO52JtOWNCEHAQQkQNh+KyS79gk0MBkx6r7L7XTD71AQwzUjig==","time":1754018575980,"size":168150}