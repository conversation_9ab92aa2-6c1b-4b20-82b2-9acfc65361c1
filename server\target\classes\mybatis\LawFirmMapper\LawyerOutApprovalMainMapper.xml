<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.klaw.dao.lawyerDao.LawyerOutApprovalMainMapper">

    <resultMap id="BaseResultMap" type="com.klaw.entity.lawyerBean.LawyerOutApprovalMain">
        <id property="id" column="id" jdbcType="VARCHAR"/>
        <result property="createOgnId" column="create_ogn_id" jdbcType="VARCHAR"/>
        <result property="createOgnName" column="create_ogn_name" jdbcType="VARCHAR"/>
        <result property="createDeptId" column="create_dept_id" jdbcType="VARCHAR"/>
        <result property="createDeptName" column="create_dept_name" jdbcType="VARCHAR"/>
        <result property="createGroupId" column="create_group_id" jdbcType="VARCHAR"/>
        <result property="createGroupName" column="create_group_name" jdbcType="VARCHAR"/>
        <result property="createPsnId" column="create_psn_id" jdbcType="VARCHAR"/>
        <result property="createPsnName" column="create_psn_name" jdbcType="VARCHAR"/>
        <result property="createOrgId" column="create_org_id" jdbcType="VARCHAR"/>
        <result property="createOrgName" column="create_org_name" jdbcType="VARCHAR"/>
        <result property="createPsnFullId" column="create_psn_full_id" jdbcType="VARCHAR"/>
        <result property="createPsnFullName" column="create_psn_full_name" jdbcType="VARCHAR"/>
        <result property="createLegalUnitId" column="create_legal_unit_id" jdbcType="VARCHAR"/>
        <result property="createLegalUnitName" column="create_legal_unit_name" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="dataState" column="data_state" jdbcType="VARCHAR"/>
        <result property="dataStateCode" column="data_state_code" jdbcType="INTEGER"/>
        <result property="outTypeName" column="out_type_name" jdbcType="VARCHAR"/>
        <result property="outTypeId" column="out_type_id" jdbcType="VARCHAR"/>
        <result property="failName" column="fail_name" jdbcType="VARCHAR"/>
        <result property="failId" column="fail_id" jdbcType="VARCHAR"/>
        <result property="description" column="description" jdbcType="VARCHAR"/>
        <result property="attachment" column="attachment" jdbcType="VARCHAR"/>
        <result property="typeName" column="type_name" jdbcType="VARCHAR"/>
        <result property="typeCode" column="type_code" jdbcType="VARCHAR"/>
        <result property="sequenceCode" column="sequence_code" jdbcType="VARCHAR"/>
        <result property="fileId" column="file_id" jdbcType="VARCHAR"/>
        <result property="eventReport" column="event_report" jdbcType="VARCHAR"/>
        <result property="itemName" column="item_name" jdbcType="VARCHAR"/>
        <result property="noticeDeptId" column="notice_dept_id" jdbcType="VARCHAR"/>
        <result property="noticeDeptName" column="notice_dept_name" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,create_ogn_id,create_ogn_name,
        create_dept_id,create_dept_name,create_group_id,
        create_group_name,create_psn_id,create_psn_name,
        create_org_id,create_org_name,create_psn_full_id,
        create_psn_full_name,create_legal_unit_id,create_legal_unit_name,
        create_time,update_time,data_state,
        data_state_code,out_type_name,out_type_id,
        fail_name,fail_id,description,
        attachment,type_name,type_code,
        sequence_code,file_id,event_report,
        item_name,notice_dept_id,notice_dept_name
    </sql>

    <resultMap id="queryResultMap" type="com.klaw.entity.lawyerBean.LawyerOutApprovalDetail">
        <id column="ID" jdbcType="VARCHAR" property="id"/>
        <result column="CREATE_OGN_ID" jdbcType="VARCHAR" property="createOgnId"/>
        <result column="CREATE_OGN_NAME" jdbcType="VARCHAR" property="createOgnName"/>
        <result column="CREATE_DEPT_ID" jdbcType="VARCHAR" property="createDeptId"/>
        <result column="CREATE_DEPT_NAME" jdbcType="VARCHAR" property="createDeptName"/>
        <result column="CREATE_GROUP_ID" jdbcType="VARCHAR" property="createGroupId"/>
        <result column="CREATE_GROUP_NAME" jdbcType="VARCHAR" property="createGroupName"/>
        <result column="CREATE_PSN_ID" jdbcType="VARCHAR" property="createPsnId"/>
        <result column="CREATE_PSN_NAME" jdbcType="VARCHAR" property="createPsnName"/>
        <result column="CREATE_ORG_ID" jdbcType="VARCHAR" property="createOrgId"/>
        <result column="CREATE_ORG_NAME" jdbcType="VARCHAR" property="createOrgName"/>
        <result column="CREATE_PSN_FULL_ID" jdbcType="VARCHAR" property="createPsnFullId"/>
        <result column="CREATE_PSN_FULL_NAME" jdbcType="VARCHAR" property="createPsnFullName"/>
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="DATA_STATE" jdbcType="VARCHAR" property="dataState"/>
        <result column="DATA_STATE_CODE" jdbcType="DECIMAL" property="dataStateCode"/>
        <result column="DATA_STATE_CODE" jdbcType="DECIMAL" property="dataStateCode"/>
        <result column="LAWYER_ID" jdbcType="DECIMAL" property="lawyerId"/>
        <result column="LAWYER_NAME" jdbcType="DECIMAL" property="lawyerName"/>
        <result column="LAW_FIRM" jdbcType="DECIMAL" property="lawFirm"/>
        <result column="LAW_FIRM_ID" jdbcType="DECIMAL" property="lawFirmId"/>
        <result column="BEGIN_DATE" jdbcType="DECIMAL" property="beginDate"/>
        <result column="LAWYER_PHONE" jdbcType="DECIMAL" property="lawyerPhone"/>
        <result column="LAWYER_EMAIL" jdbcType="DECIMAL" property="lawyerEmail"/>
        <result column="LAWYER_TIME" jdbcType="DECIMAL" property="lawyerTime"/>
        <result column="CHARTERED_NO" jdbcType="DECIMAL" property="charteredNo"/>
        <result column="BE_GOOD_AT_DOMAIN" jdbcType="DECIMAL" property="beGoodAtDomain"/>
        <result column="BE_GOOD_AT_DOMAIN_IDS" jdbcType="DECIMAL" property="beGoodAtDomainIds"/>
        <result column="REMARKS" jdbcType="DECIMAL" property="remarks"/>
        <result column="PARENT_ID" jdbcType="DECIMAL" property="parentId"/>
        <result column="IS_COUNSEL" jdbcType="DECIMAL" property="isCounsel"/>
        <result column="SEQ" jdbcType="DECIMAL" property="seq"/>
        <result column="WHETHER_HOST_LAWYER" jdbcType="DECIMAL" property="whetherHostLawyer"/>
        <result column="TYPE_NAME" jdbcType="DECIMAL" property="typeName"/>
        <result column="TYPE_CODE" jdbcType="DECIMAL" property="typeCode"/>
        <result column="ATTACHMENT" jdbcType="DECIMAL" property="attachment"/>
        <result column="MAJOR_REGION" jdbcType="DECIMAL" property="majorRegion"/>
        <result column="LAW_FIRM_TYPE" jdbcType="DECIMAL" property="lawFirmType"/>
        <result column="SEX" jdbcType="DECIMAL" property="sex"/>
        <result column="AVATAR" jdbcType="DECIMAL" property="avatar"/>
        <association property="lawyerOutApprovalMain" javaType="com.klaw.entity.lawyerBean.LawyerOutApprovalMain">
            <id column="ID_" jdbcType="VARCHAR" property="id"/>
            <result column="CREATE_OGN_ID_" jdbcType="VARCHAR" property="createOgnId"/>
            <result column="CREATE_OGN_NAME_" jdbcType="VARCHAR" property="createOgnName"/>
            <result column="CREATE_DEPT_ID_" jdbcType="VARCHAR" property="createDeptId"/>
            <result column="CREATE_DEPT_NAME_" jdbcType="VARCHAR" property="createDeptName"/>
            <result column="CREATE_GROUP_ID_" jdbcType="VARCHAR" property="createGroupId"/>
            <result column="CREATE_GROUP_NAME_" jdbcType="VARCHAR" property="createGroupName"/>
            <result column="CREATE_PSN_ID_" jdbcType="VARCHAR" property="createPsnId"/>
            <result column="CREATE_PSN_NAME_" jdbcType="VARCHAR" property="createPsnName"/>
            <result column="CREATE_ORG_ID_" jdbcType="VARCHAR" property="createOrgId"/>
            <result column="CREATE_ORG_NAME_" jdbcType="VARCHAR" property="createOrgName"/>
            <result column="CREATE_PSN_FULL_ID_" jdbcType="VARCHAR" property="createPsnFullId"/>
            <result column="CREATE_PSN_FULL_NAME_" jdbcType="VARCHAR" property="createPsnFullName"/>
            <result column="CREATE_TIME_" jdbcType="TIMESTAMP" property="createTime"/>
            <result column="DATA_STATE_" jdbcType="VARCHAR" property="dataState"/>
            <result column="DATA_STATE_CODE_" jdbcType="DECIMAL" property="dataStateCode"/>
            <result column="OUT_TYPE_NAME_" jdbcType="VARCHAR" property="outTypeName"/>
            <result column="OUT_TYPE_ID_" jdbcType="VARCHAR" property="outTypeId"/>
            <result column="FAIL_NAME_" jdbcType="VARCHAR" property="failName"/>
            <result column="FAIL_ID_" jdbcType="VARCHAR" property="failId"/>
            <result column="DESCRIPTION_" jdbcType="CLOB" property="description"/>
            <result column="ATTACHMENT_" jdbcType="CLOB" property="attachment"/>
        </association>
    </resultMap>

    <select id="queryPageData" resultMap="queryResultMap">
        select
        d.ID,d.CREATE_OGN_ID,d.CREATE_OGN_NAME,d.CREATE_DEPT_ID,d.CREATE_DEPT_NAME,d.CREATE_GROUP_ID,d.CREATE_GROUP_NAME,
        d.CREATE_PSN_ID,d.CREATE_PSN_NAME,d.CREATE_ORG_ID,d.CREATE_ORG_NAME,d.CREATE_PSN_FULL_ID,d.CREATE_PSN_FULL_NAME,
        d.CREATE_TIME,d.DATA_STATE,d.DATA_STATE_CODE,
        d.lawyer_name,d.lawyer_id,d.law_firm,d.law_firm_id,d.begin_date,d.lawyer_phone,
        d.lawyer_email,d.lawyer_time,d.chartered_no,d.be_good_at_domain,d.be_good_at_domain_ids,d.remarks,
        d.parent_id,d.is_counsel,d.seq,d.whether_host_lawyer,d.type_name,
        d.type_code, d.attachment, d.major_region, d.law_firm_type, d.sex, d.avatar,
        d.create_legal_unit_id,d.create_legal_unit_name,
        m.ID ID_,m.CREATE_OGN_ID CREATE_OGN_ID_,m.CREATE_OGN_NAME CREATE_OGN_NAME_,m.CREATE_DEPT_ID
        CREATE_DEPT_ID_,m.CREATE_DEPT_NAME CREATE_DEPT_NAME_,m.CREATE_GROUP_ID CREATE_GROUP_ID_,m.CREATE_GROUP_NAME
        CREATE_GROUP_NAME_,m.CREATE_PSN_ID CREATE_PSN_ID_,m.CREATE_PSN_NAME CREATE_PSN_NAME_,m.CREATE_ORG_ID
        CREATE_ORG_ID_,m.CREATE_ORG_NAME CREATE_ORG_NAME_,m.CREATE_PSN_FULL_ID
        CREATE_PSN_FULL_ID_,m.CREATE_PSN_FULL_NAME CREATE_PSN_FULL_NAME_,m.CREATE_TIME CREATE_TIME_,m.DATA_STATE
        DATA_STATE_,m.DATA_STATE_CODE DATA_STATE_CODE_,m.OUT_TYPE_NAME
        OUT_TYPE_NAME_,m.OUT_TYPE_ID OUT_TYPE_ID_,m.FAIL_NAME FAIL_NAME_,m.FAIL_ID FAIL_ID_,m.DESCRIPTION
        DESCRIPTION_,m.ATTACHMENT ATTACHMENT_, m.TYPE_NAME TYPE_NAME_, m.TYPE_CODE TYPE_CODE_, m.SEQUENCE_CODE
        SEQUENCE_CODE_

        from sg_lawyer_out_approval_detail d right join sg_lawyer_out_approval_main m
        on m.id=d.parent_id
        <where>
            <if test="ew != null and ew.sqlSegment != null and ew.sqlSegment != ''">
                ${ew.sqlSegment}
            </if>
        </where>
        order by m.create_time desc
    </select>
</mapper>
