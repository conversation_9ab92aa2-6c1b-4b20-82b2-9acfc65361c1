<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.klaw.dao.authorizationDao.AuthorizationMapper">
    <select id="queryByAuthorizationAffairId" resultType="com.klaw.entity.authorizationBean.AuthorizationAffair">
        select c.id
        from AUTHORIZATION m
                 join AUTHORIZATION_AFFAIR c on m.id = c.relation_id
        where c.relation_id = #{id}
    </select>
    <select id="queryByAuthorizationContractId" resultType="com.klaw.entity.authorizationBean.AuthorizationContract">
        select c.id
        from AUTHORIZATION m
                 join AUTHORIZATION_CONTRACT c on m.id = c.relation_id
        where c.relation_id = #{id}
    </select>
    <select id="queryByAuthorizationLitigationId"
            resultType="com.klaw.entity.authorizationBean.AuthorizationLitigation">
        select c.id
        from AUTHORIZATION m
                 join AUTHORIZATION_LITIGATION c on m.id = c.relation_id
        where c.relation_id = #{id}
    </select>
    <select id="queryByAuthorizationLitigationAssId"
            resultType="com.klaw.entity.authorizationBean.AuthorizationLitigationAss">
        select c.id
        from AUTHORIZATION m
                 join AUTHORIZATION_LITIGATION_ASS c on m.id = c.relation_id
        where c.relation_id = #{id}
    </select>
    <select id="queryByAuthorizationShareholderId"
            resultType="com.klaw.entity.authorizationBean.AuthorizationShareholder">
        select c.id
        from AUTHORIZATION m
                 join AUTHORIZATION_SHAREHOLDER c on m.id = c.relation_id
        where c.relation_id = #{id}
    </select>
    <select id="queryByAuthorizationRoutineId" resultType="com.klaw.entity.authorizationBean.AuthorizationRoutine">
        select c.id
        from AUTHORIZATION m
                 join AUTHORIZATION_ROUTINE c on m.id = c.relation_id
        where c.relation_id = #{id}
    </select>

    <resultMap id="GroupByBaseResultMap" type="java.util.Map">
        <result column="AUTHORIZATION_TYPE" property="name"/>
        <result column="NUM" property="num"/>
    </resultMap>
    <select id="queryAllGroupBy" resultMap="GroupByBaseResultMap" parameterType="java.util.Map">
        select
        ${ew.sqlSelect}
        from AUTHORIZATION
        <where>
            ${ew.sqlSegment}
        </where>
    </select>

    <resultMap id="GroupByBaseResultMap1" type="java.util.Map">
        <result column="CREATE_TIME" property="name"/>
        <result column="NUM" property="num"/>
    </resultMap>
    <select id="queryAllGroupBy1" resultMap="GroupByBaseResultMap1" parameterType="java.util.Map">
        select
        ${ew.sqlSelect}
        from AUTHORIZATION
        <where>
            ${ew.sqlSegment}
        </where>
    </select>
    <update id="updateStatus">
        update AUTHORIZATION
        set AUTH_STATUS=#{status},
            SUBLI_STATUS=#{subliStatus}
        where id = #{id}
    </update>

    <resultMap id="PageResultMap" type="com.klaw.vo.contractVo.Authorization">
        <id column="ID" jdbcType="VARCHAR" property="id"/>
        <result column="AUTHORIZED_NAME" jdbcType="VARCHAR" property="authorizedName"/>
        <result column="AUTHORIZED_BOOK_NUMBER" jdbcType="VARCHAR" property="authorizedBookNumber"/>
        <result column="AUTHORIZATION_TYPE" jdbcType="VARCHAR" property="authorizationType"/>
        <result column="AUTHORIZER" jdbcType="VARCHAR" property="authorizer"/>
        <result column="AUTHORIZER_ID" jdbcType="VARCHAR" property="authorizerId"/>
        <result column="TRUSTEE" jdbcType="VARCHAR" property="trustee"/>
        <result column="TRUSTEE_CODE" jdbcType="VARCHAR" property="trusteeCode"/>
        <result column="ENTRUSTED_RANGE" jdbcType="VARCHAR" property="entrustedRange"/>
    </resultMap>
    <sql id="Page_Column_List">
        ID, AUTHORIZED_NAME, AUTHORIZED_BOOK_NUMBER, AUTHORIZATION_TYPE
        , AUTHORIZER, AUTHORIZER_ID, TRUSTEE, TRUSTEE_CODE, ENTRUSTED_RANGE
    </sql>

    <select id="queryAuthorizationPage" resultMap="PageResultMap">
        select
        <include refid="Page_Column_List"/>
        from SG_AUTHORIZATION
        <where>
            ${ew.sqlSegment}
        </where>
    </select>
</mapper>
