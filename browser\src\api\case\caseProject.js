import {request} from '@/api/index'

export default {

  queryCaseProject(data) {
    return request({
      url: '/case_project/query',
      method: 'post',
      data
    })
  },

  save(data) {
    return request({
      url: '/case_project/save',
      method: 'post',
      data
    })
  },

  queryById(data) {
    return request({
      url: '/case_project/query_by_id',
      method: 'post',
      data: {
        id: data
      }
    })
  },
  deleteCaseProject(data) {
    return request({
      url: '/case_project',
      method: 'delete',
      data
    })
  }

}
