
28a560461f0e620e0f208273685ab2a5543a86b0	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.6.1754018536329.js\",\"contentHash\":\"0fc58cf6a7d889ff134d363d42a84815\"}","integrity":"sha512-UZ4Km9483nfuhW8BIIpmiUqjD0xed6hHzrbjzPJ4m948DfurH4IzYMiMOmVsm6DxrH97XaqCVfM0HffXE+UIOg==","time":1754018575958,"size":70737}