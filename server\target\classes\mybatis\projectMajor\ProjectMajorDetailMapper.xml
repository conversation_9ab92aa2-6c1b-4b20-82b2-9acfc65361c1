<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.klaw.dao.projectDao.ProjectMajorDetailMapper">


    <select id="queryList" resultType="java.util.Map">
        select b.*,a.*
        from project_major_reasoning a
        INNER join (
            select * from project_major_reasoning_detail
            where
            1 = 1
            AND is_main = 'Y'
            <if test="isResult == true">
                AND executor_id = #{userId}
            </if>
            <if test="isResult == false">
                AND create_psn_id = #{userId}
            </if>
        ) b on a.id = b.parent_id
        where
        (a.probject_status = 'pro_2'
        or a.probject_status = 'pro_3'
        or a.probject_status = 'pro_4'   ) and a.change_status !='change_1'
        <if test="fuzzyValue != null and fuzzyValue != ''">
            AND (a.probject_number like '%${fuzzyValue}%' or a.probject_name like '%${fuzzyValue}%')
        </if>
        limit #{page},#{limit}
    </select>

    <select id="queryListCount" resultType="java.lang.Integer">
        select count(a.id)
        from project_major_reasoning a
        INNER join (
        select * from project_major_reasoning_detail
        where
        1 = 1
        AND is_main = 'Y'
        <if test="isResult == true">
           AND executor_id = #{userId}
        </if>
        <if test="isResult == false">
           AND create_psn_id = #{userId}
        </if>
        ) b on a.id = b.parent_id
        where
        a.probject_status = 'pro_2'
        or a.probject_status = 'pro_3'
        or a.probject_status = 'pro_4'
        <if test="fuzzyValue != null and fuzzyValue != ''">
            AND (a.probject_number like '%${fuzzyValue}%' or a.probject_name like '%${fuzzyValue}%')
        </if>
    </select>

    <select id="queryById" resultType="java.util.Map">
        select b.*,a.*
        from project_major_reasoning a
        INNER join (
        select * from project_major_reasoning_detail
        where
        1 = 1
        AND is_main = 'Y'
        <if test="id != null and id != ''">
           AND task_number = #{id}
        </if>
        ) b on a.id = b.parent_id
    </select>
</mapper>
