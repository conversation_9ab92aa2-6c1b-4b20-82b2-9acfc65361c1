import {request} from '@/api/index'

export default {
    query(data) {
        return request({
            url: '/contractapproval/query',
            method: 'post',
            data
        })
    },
    queryPlace(data) {
        return request({
            url: '/contractapproval/queryPlace',
            method: 'post',
            data
        })
    },
    queryContractPerform(data) {
        return request({
            url: '/contractapproval/queryContractPerform',
            method: 'post',
            data
        })
    },
    queryContractApprovalById(data) {
        return request({
            url: '/contractapproval/queryContractApprovalById',
            method: 'post',
            data
        })
    },
    save(data) {
        return request({
            url: '/contractapproval/save',
            method: 'post',
            data
        })
    },
    saveContractFileMain(data) {
        return request({
            url: '/contractapproval/saveContractFileMain',
            method: 'post',
            data
        })
    },
    saveById(data) {
        return request({
            url: '/contractapproval/saveById',
            method: 'post',
            data
        })
    },
    delete(data) {
        return request({
            url: '/contractapproval/delete',
            method: 'post',
            data
        })
    },
    queryById(data) {
        return request({
            url: '/contractapproval/queryById',
            method: 'post',
            data
        })
    },
    querySimpleById(data) {
        return request({
            url: '/contractapproval/querySimpleById',
            method: 'post',
            data
        })
    },
    setParam(data) {
        return request({
            url: '/contractapproval/setParam',
            method: 'post',
            data
        })
    },
    queryDialog(data) {
        return request({
            url: '/contractapproval/queryDialog',
            method: 'post',
            data
        })
    },
    queryContractByIndex(data) {
        return request({
            url: '/contractapproval/queryContractByIndex',
            method: 'post',
            data
        })
    },
    updateContractPerformState(data) {
        return request({
            url: '/contractapproval/updateContractPerformState',
            method: 'post',
            data
        })
    },
    queryContractLeftOrgTree1(data) {
        return request({
            url: '/contractApproval/queryContractLeftOrgTree3',
            method: 'post',
            data
        })
    },
    queryContractLeftOrgTree(data) {
        return request({
            url: '/contractapproval/queryContractLeftOrgTree',
            method: 'post',
            data
        })
    },
    queryContractApproval(data) {
        return request({
            url: '/contractApproval/queryContractApproval',
            method: 'post',
            data
        })
    },
    queryLeft(data) {
        return request({
            url: '/contractapproval/queryLeft',
            method: 'post',
            data
        })
    },
    queryCase(data) {
        return request({
            url: '/contractapproval/queryCase',
            method: 'post',
            data
        })
    },
    queryByUnion(data) {
        return request({
            url: '/contractapproval/queryByUnion',
            method: 'post',
            data
        })
    },
    queryContractTowType(data) {
        return request({
            url: '/contractapproval/queryContractTowType',
            method: 'post',
            data
        })
    },
    doGetWorkflowList(data) {
        return request({
            url: '/contractapproval/queryAssetList',
            method: 'post',
            data
        })
    },
    queryOwnerName(data) {
        return request({
            url: '/contractapproval/queryOwnerName',
            method: 'post',
            data
        })
    },
    queryProcessOpinion(data) {
        return request({
            url: '/contractapproval/queryContractProcessOpinion',
            method: 'post',
            data
        })
    },
    queryContractDialog(data) {
        return request({
            url: '/contractapproval/queryContractDialog',
            method: 'post',
            data
        })
    },
    pushContractDialogInfo(data) {
        return request({
            url: '/contractapproval/pushContractDialogInfo',
            method: 'post',
            data
        })
    },
    queryByAll(data) {
        return request({
            url: '/contractApproval/queryByAll',
            method: 'post',
            data
        })
    },

}