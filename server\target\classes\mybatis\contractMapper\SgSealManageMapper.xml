<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.klaw.dao.contractDao.SgSealManageMapper">
  <resultMap id="BaseResultMap" type="com.klaw.entity.contractBean.SgSealManage">
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="SEAL_NAME" jdbcType="VARCHAR" property="sealName" />
    <result column="SEAL_CODE" jdbcType="VARCHAR" property="sealCode" />
    <result column="SEAL_TYPE" jdbcType="VARCHAR" property="sealType" />
    <result column="SEAL_TYPE_ID" jdbcType="VARCHAR" property="sealTypeId" />
    <result column="COMPANY_ID" jdbcType="VARCHAR" property="companyId" />
    <result column="COMPANY_NAME" jdbcType="VARCHAR" property="companyName" />
    <result column="SEAL_ADMIN" jdbcType="VARCHAR" property="sealAdmin" />
    <result column="SEAL_ADMIN_ID" jdbcType="VARCHAR" property="sealAdminId" />
  </resultMap>
  <sql id="Base_Column_List">
    m.id,m.SEAL_NAME,m.SEAL_CODE,m.SEAL_TYPE,m.SEAL_TYPE_ID,m.COMPANY_ID,m.COMPANY_NAME,m.SEAL_ADMIN,m.SEAL_ADMIN_ID
  </sql>

  <select id="querySealList" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/> from sg_seal_manage m join SG_SEAL_USE_RANGE u on m.id = u.PARENT_ID
    <where>
      ${ew.sqlSegment}
    </where>
    order by m.create_time desc
  </select>

  <resultMap id="PageResultMap" type="com.klaw.vo.contractVo.SgSealManage">
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="SEAL_NAME" jdbcType="VARCHAR" property="sealName" />
    <result column="SEAL_CODE" jdbcType="VARCHAR" property="sealCode" />
    <result column="SEAL_TYPE" jdbcType="VARCHAR" property="sealType" />
    <result column="SEAL_TYPE_CODE" jdbcType="VARCHAR" property="sealTypeCode" />
    <result column="SEAL_ADMIN" jdbcType="VARCHAR" property="sealAdmin" />
    <result column="SEAL_ADMIN_ID" jdbcType="VARCHAR" property="sealAdminId" />
    <result column="USE_ORG_NAME" jdbcType="VARCHAR" property="sealUnit" />
      <result column="USE_ORG_ID" jdbcType="VARCHAR" property="sealUnitCode" />

  </resultMap>
  <sql id="Page_Column_List">
    m.id,m.SEAL_NAME,m.SEAL_CODE,m.SEAL_TYPE,m.SEAL_TYPE_CODE,m.SEAL_ADMIN,m.SEAL_ADMIN_ID
    ,u.USE_ORG_NAME,u.USE_ORG_ID
  </sql>

  <select id="querySealPage" resultMap="PageResultMap">
    select <include refid="Page_Column_List"/> from sg_seal_manage m join SG_SEAL_USE_RANGE u on m.id = u.PARENT_ID
    <where>
      ${ew.sqlSegment}
    </where>
  </select>
</mapper>