
c20501cf5c0e489ea5e9ff6279381f025eec65c2	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.417.1754018536329.js\",\"contentHash\":\"0ab24ec712300845d69066335760e3f6\"}","integrity":"sha512-Wb9tHteBMeCwbjWx4ypsEMhyIVV9Q7j1+2W0yeJxFYOR2N0WaDVbQ9EPnOKBHckIbOuB4bttS/PsHhNPooI6JA==","time":1754018575956,"size":34893}