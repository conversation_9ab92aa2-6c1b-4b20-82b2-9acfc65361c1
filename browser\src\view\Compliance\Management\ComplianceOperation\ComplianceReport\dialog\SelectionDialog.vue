<template>
    <el-dialog :close-on-click-modal="false" title="标段信息" :visible.sync="dialogVisible" width="70%">
      <el-header>
        <el-form ref="queryForm" label-width="70px">
          <el-row>
            <el-col :span="10">
              <el-form-item label="模糊查询">
                <el-input v-model="tableQuery.fuzzyValue" placeholder="请输入"
                          style="width:100%" clearable/>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-button-group style="float: right">
                <el-button type="primary" icon="el-icon-search" @click="search_">搜索</el-button>
                <el-button type="primary" icon="el-icon-refresh-left" @click="empty_">重置</el-button>
              </el-button-group>
            </el-col>
          </el-row>
        </el-form>
      </el-header>
  
      <div>
        <el-table
            :data="tableData"
            border
            style="table-layout: fixed;width: 99%;"
            :height="dialog_height"
            :fit="true"
            stripe
            highlight-current-row
            @current-change="currentChange">
          <el-table-column type="index" label="序号"/>
          <el-table-column prop="reviewSubject" label="审查主题" min-width="150" show-overflow-tooltip/>
          <el-table-column prop="reviewNumber" label="审查编号" min-width="200" show-overflow-tooltip/>
          <el-table-column prop="submissionDate" label="送审时间" min-width="200" show-overflow-tooltip/>
  
        </el-table>
      </div>
  
      <div style="text-align: center">
        <el-footer>
          <pagination
              v-show="tableQuery.total>0"
              :total="tableQuery.total"
              :page.sync="tableQuery.page"
              :limit.sync="tableQuery.limit"
              style="float: left;padding:20px 16px;"
              @pagination="refreshData"/>
  
        </el-footer>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button icon="" class="negative-btn" @click="cancel_">取消</el-button>
        <el-button type="primary" icon="" class="active-btn" @click="sure_">确定</el-button>
      </span>
    </el-dialog>
  </template>
  
  <script>
  import pagination from "@/components/Pagination/index.vue"
  import {mapGetters} from 'vuex'
  import winbidinfoApi from "@/api/contract/bmContract";
  import complianceReviewApi from '@/api/ComplianceReview/complianceReview.js'
  
  export default {
    name: 'sectionDataDialog',
    components: {pagination},
    props: {
      dialogVisible: {
        type: Boolean,
        default: false
      },
    },
    computed: {
      ...mapGetters(['orgContext']),
    },
    created() {
      // this.orgId = ;
    },
    data() {
      return {
        myDialogVisible: this.dialogVisible,
        orgName:null,
        tableQuery: {
          total:0
        },
        tableData: [], // 列表数据源
        dialog_height: "calc(-300px + 100vh)", // table高度
        tableTempData: null,
      }
    },
    watch: {
      dialogVisible(val) {
        this.myDialogVisible = val
        if (val) {
          this.refreshData()
        }
      },
      myDialogVisible(val) {
        this.$emit('update:dialogVisible', val)
      },
    },
    methods: {
      search_() { // 查询
        this.refreshData()
      },
      empty_() {
        this.tableQuery = {
          fuzzyValue: null, //模糊查询
          isQuery: true,
          page: 1,
          limit: 10,
          total: 0,
        }
        this.refreshData()
      },
      currentChange(currentRow) {
        this.tableTempData = currentRow;
      },
      refreshData() {
        // this.tableQuery.orgId = this.orgContext.currentOrgId
        complianceReviewApi.queryTableData({
          fuzzyValue: null, //模糊查询
          page: 1,
          limit: 10,
          total: 0,
          orgId:this.orgContext.currentOrgId
        }).then((res) => {
          this.tableData = res.data.data.records
          this.tableQuery.total = res.data.data.total
        })
      },
      cancel_() {
        this.myDialogVisible = false
      },
      sure_() {
        this.$emit('sectionSure', this.tableTempData)
      },
    }
  }
  </script>
  
  <style scoped>
  
  /deep/ .el-dialog__body {
    padding: 0px 20px;
  }
  </style>
  