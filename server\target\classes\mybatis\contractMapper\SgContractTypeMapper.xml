<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.klaw.dao.contractDao.SgContractTypeMapper">
  <resultMap id="BaseResultMap" type="com.klaw.entity.contractBean.SgContractType">
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="TYPE_NAME" jdbcType="VARCHAR" property="typeName" />
    <result column="TYPE_CODE" jdbcType="VARCHAR" property="typeCode" />
    <result column="USE_RANGE_NAME" jdbcType="VARCHAR" property="useRangeName" />
    <result column="USE_RANGE_ID" jdbcType="VARCHAR" property="useRangeId" />
    <result column="PARENT_ID" jdbcType="VARCHAR" property="parentId" />
    <result column="FULL_ID" jdbcType="VARCHAR" property="fullId" />
    <result column="FULL_NAME" jdbcType="VARCHAR" property="fullName" />
    <result column="IS_LEAF" jdbcType="VARCHAR" property="isLeaf" />
    <result column="NODE_LEVEL" jdbcType="DECIMAL" property="nodeLevel" />
    <result column="SORT" jdbcType="DECIMAL" property="sort" />
    <result column="CREATE_OGN_ID" jdbcType="VARCHAR" property="createOgnId" />
    <result column="CREATE_OGN_NAME" jdbcType="VARCHAR" property="createOgnName" />
    <result column="CREATE_DEPT_ID" jdbcType="VARCHAR" property="createDeptId" />
    <result column="CREATE_DEPT_NAME" jdbcType="VARCHAR" property="createDeptName" />
    <result column="CREATE_GROUP_ID" jdbcType="VARCHAR" property="createGroupId" />
    <result column="CREATE_GROUP_NAME" jdbcType="VARCHAR" property="createGroupName" />
    <result column="CREATE_PSN_ID" jdbcType="VARCHAR" property="createPsnId" />
    <result column="CREATE_PSN_NAME" jdbcType="VARCHAR" property="createPsnName" />
    <result column="CREATE_ORG_ID" jdbcType="VARCHAR" property="createOrgId" />
    <result column="CREATE_ORG_NAME" jdbcType="VARCHAR" property="createOrgName" />
    <result column="CREATE_PSN_FULL_ID" jdbcType="VARCHAR" property="createPsnFullId" />
    <result column="CREATE_PSN_FULL_NAME" jdbcType="VARCHAR" property="createPsnFullName" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="DATA_STATE" jdbcType="VARCHAR" property="dataState" />
    <result column="DATA_STATE_CODE" jdbcType="DECIMAL" property="dataStateCode" />
    <result column="PARENT_NAME" jdbcType="DECIMAL" property="parentName" />
    <result column="PARENT_CODE" jdbcType="DECIMAL" property="parentCode" />
    <result column="FULL_CODE" jdbcType="DECIMAL" property="fullCode" />
  </resultMap>
  <sql id="Base_Column_List">
    m.ID,  m.TYPE_NAME,  m.TYPE_CODE,  m.USE_RANGE_NAME,  m.USE_RANGE_ID,  m.PARENT_ID,  m.FULL_ID,  m.FULL_NAME,  m.IS_LEAF,  m.NODE_LEVEL,
      m.SORT,  m.CREATE_OGN_ID,  m.CREATE_OGN_NAME,  m.CREATE_DEPT_ID,  m.CREATE_DEPT_NAME,  m.CREATE_GROUP_ID,  m.CREATE_GROUP_NAME,
      m.CREATE_PSN_ID,  m.CREATE_PSN_NAME,  m.CREATE_ORG_ID,  m.CREATE_ORG_NAME,  m.CREATE_PSN_FULL_ID,  m.CREATE_PSN_FULL_NAME,  m.CREATE_TIME,
       m.UPDATE_TIME,  m.DATA_STATE,  m.DATA_STATE_CODE, m.PARENT_NAME, m.PARENT_CODE, m.FULL_CODE
  </sql>

  <select id="queryOwnData" resultMap="BaseResultMap">
    select distinct <include refid="Base_Column_List"/> from SG_CONTRACT_TYPE m join sg_contract_type_use_range d on m.id=d.parent_id
    <where>
      ${ew.sqlSegment}
    </where>
  </select>
</mapper>