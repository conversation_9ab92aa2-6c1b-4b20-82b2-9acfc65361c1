
003ed74146ad84fa29d4117534cfdf4e7dfa104d	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.437.1754018536329.js\",\"contentHash\":\"fe0b1ad3ecda3b53d8ad5f4e7cf77232\"}","integrity":"sha512-2auqOXD/lFx3ZT/xbI1N7fV8jizx2wzuaz0n3tXjzoaZQdtN4vUk5FZ5w12PSY4Y5djXtOGOg74CAPnx6NToPg==","time":1754018575977,"size":71696}