<template>
	<el-container direction="vertical" class="container-manage-sg">
		<el-header>
			<el-card>
				<div>
					<el-input v-model="tableQuery.fuzzyValue" class="filter_input" placeholder="检索字段（人员编码、人员姓名）"
						clearable @keyup.enter.native="refreshData" @clear="refreshData">
						<el-popover slot="prepend" placement="bottom-start" width="1000" trigger="click">
							<el-form ref="queryForm" label-width="100px" size="mini">
								<el-row>
									<el-col :span="12">
										<el-form-item label="人员编码">
											<el-input v-model="tableQuery.memberCode" clearable placeholder="请输入..." />
										</el-form-item>
									</el-col>
									<el-col :span="12">
										<el-form-item label="人员姓名">
											<el-input v-model="tableQuery.memberName" clearable placeholder="请输入..." />
										</el-form-item>
									</el-col>
								</el-row>
								<el-row>
									<el-col :span="12">
										<el-form-item label="人员状态">
											<el-select style="width: 100%" v-model="tableQuery.dataState">
												<el-option key="0" value="0" label="待启用"></el-option>
												<el-option key="1" value="1" label="启用"></el-option>
												<el-option key="2" value="2" label="停用"></el-option>
											</el-select>
										</el-form-item>
									</el-col>
								</el-row>
								<el-button-group style="float: right">
									<el-button type="primary" size="mini" icon="el-icon-search"
										@click="search_">搜索</el-button>
									<el-button type="primary" size="mini" icon="el-icon-refresh-left"
										@click="empty_">重置</el-button>
								</el-button-group>
							</el-form>

							<el-button slot="reference" size="small" type="primary">高级检索</el-button>
						</el-popover>

						<el-button slot="append" icon="el-icon-search" @click="search_" />
					</el-input>
				</div>
			</el-card>
		</el-header>
		<el-main>
			<SimpleBoardIndex :title="'合规人员库'">
				<template slot="button">
					<div>
						<el-checkbox @change="changeChecked" style="position: absolute; left: 130px; top: 87px"
							:value="checked">显示历史人员库</el-checkbox>
						<el-button type="primary" class="normal-btn" size="mini"
							@click="openPersonDialog">选择人员</el-button>
					</div>
				</template>
				<el-dialog title="选择人员" :visible.sync="dialogVisible">
					<el-form ref="dataForm" :model="dataForm" :rules="rules">
						<el-row>
							<el-col :span="12">
								<el-form-item label="启用时间" prop="enableTime" :label-width="formLabelWidth">
									<el-date-picker v-model="dataForm.enableTime" value-format="yyyy-MM-dd" type="date"
										style="width: 100%;" />
								</el-form-item>
							</el-col>
							<el-col :span="12">
								<el-form-item label="停用时间" prop="disableTime" :label-width="formLabelWidth">
									<el-date-picker v-model="dataForm.disableTime" value-format="yyyy-MM-dd" type="date"
										:disabled="!dataForm.enableTime" :picker-options="{
											disabledDate: time => {
												return this.dataForm.enableTime &&
													new Date(time) < new Date(this.dataForm.enableTime)
											}
										}" style="width: 100%;" />
								</el-form-item>
							</el-col>
						</el-row>
						<el-row>
							<el-col :span="24">
								<el-form-item label="所属人员" prop="idNames" :label-width="formLabelWidth">
									<el-input v-model="dataForm.idNames" clearable placeholder="请选择"
										style="width: 100%;" disabled>
										<el-button slot="append" icon="el-icon-search" @click="showDialog" />
									</el-input>
								</el-form-item>
							</el-col>
						</el-row>
					</el-form>

					<span slot="footer" class="dialog-footer">
						<el-button @click="dialogVisible = false">取 消</el-button>
						<el-button type="primary" @click="roleClick">确 定</el-button>
					</span>
				</el-dialog>
				<el-dialog title="请选择人员" :visible.sync="dialogRoleVisible" width="70%">
					<el-form ref="queryForm" label-width="70px">
						<el-row>
							<el-col :span="6">
								<el-form-item label="人员姓名">
									<el-input v-model="tableQuery.memberName" placeholder="请输入..." style="width: 100%"
										clearable />
								</el-form-item>
							</el-col>
							<el-col :span="12">
								<el-form-item label="所属组织">
									<!-- <el-input v-model="tableQuery.organization" placeholder="请输入..."
										style="width: 100%" clearable /> -->
									<el-input style="width: 100%" v-model="tableQuery.organizationName"
										class="input-with-select" disabled placeholder="请选择">
										<el-button slot="append" icon="el-icon-search"
											@click="orgTreeCurrentDeptDialog = true" />
									</el-input>
								</el-form-item>
							</el-col>
							<el-col :span="6">
								<el-button-group style="float: right">
									<el-button type="primary" icon="el-icon-search" @click="showDialog">搜索</el-button>
									<el-button type="primary" icon="el-icon-refresh-left" @click="empty_">重置</el-button>
								</el-button-group>
							</el-col>
						</el-row>
					</el-form>
					<div>
						<el-table :data="tableDataOne" border style="table-layout: fixed; width: 99%" max-height="250"
							:fit="true" stripe @selection-change="personSelectChange" highlight-current-row>
							<el-table-column type="selection" width="55">
							</el-table-column>
							<el-table-column prop="memberCode" label="人员编码" show-overflow-tooltip />
							<el-table-column prop="memberName" label="人员姓名" show-overflow-tooltip />
							<el-table-column prop="memberGender" label="性别" show-overflow-tooltip />
							<el-table-column prop="organizationName" label="现工作单位(部室)" show-overflow-tooltip />
							<el-table-column prop="position" label="岗位/职务" show-overflow-tooltip />
							<el-table-column prop="phone" label="本人手机号" show-overflow-tooltip />
						</el-table>

						<pagination v-show="tableQuery.total > 0" :total="tableQuery.total" :page.sync="tableQuery.page"
							:limit.sync="tableQuery.limit" style="float: center; padding: 8px 0px"
							@pagination="refreshData1" />
					</div>


					<span slot="footer" class="dialog-footer">
						<el-button @click="dialogRoleVisible = false">取 消</el-button>
						<el-button type="primary" @click="sureDialog">确 定</el-button>
					</span>
				</el-dialog>
				<selectCurrentUnit :dialog-visible.sync="orgTreeCurrentDeptDialog" :is-checked-user="false"
					:isCheck="true" :selectDept="true" :show-group="false" :show-user="false" :title="'部门信息'"
					:isRadioChecked="true" :isNotCascade="true" @sureDeptBtn="deptSelect"></selectCurrentUnit>
				<el-table ref="table" v-loading="tableLoading" :data="tableData" size="mini" border
					:height="table_height" stripe fit highlight-current-row :show-overflow-tooltip="true"
					style="table-layout: fixed; width: 100%">
					<el-table-column type="index" width="50" label="序号" align="center" />
					<el-table-column prop="memberCode" width="100" label="人员编码" show-overflow-tooltip />
					<el-table-column prop="memberName" width="100" label="人员姓名" show-overflow-tooltip />
					<el-table-column prop="memberGender" width="50" label="性别" show-overflow-tooltip />
					<el-table-column prop="organizationName" width="250" label="现工作单位(部室)" show-overflow-tooltip />
					<el-table-column prop="position" width="250" label="岗位/职务" show-overflow-tooltip />
					<el-table-column prop="birthDate" width="130" label="出生年月" show-overflow-tooltip />
					<el-table-column prop="attendWorkerDate" width="150" label="参加包钢工作时间" show-overflow-tooltip />
					<el-table-column prop="plate" width="130" label="所属板块" show-overflow-tooltip />
					<el-table-column prop="professionNow" width="150" label="现从事专业" show-overflow-tooltip />
					<el-table-column prop="technicalQualification" width="150" label="现有专业技术资格" show-overflow-tooltip />
					<el-table-column prop="phone" width="130" label="本人手机号" show-overflow-tooltip />
					<el-table-column prop="dataState" width="80" show-overflow-tooltip label="人员状态">
						<template slot-scope="scope">
							<el-tag
								:type="scope.row.dataState == '待启用' ? 'info' : scope.row.dataState == '启用' ? 'success' : 'danger'">
								{{ scope.row.dataState }}
							</el-tag>
						</template>
					</el-table-column>
					<el-table-column label="操作" align="center" width="120" fixed="right">
						<template slot-scope="scope">
							<!-- v-if="scope.row.dataState == '待启用' && scope.row.parentId == null" -->
							<el-button type="text"
							@click="view_(scope.$index, scope.row)">查看</el-button>
							<el-button type="text" v-if="scope.row.dataState == '待启用' && scope.row.parentId == null"
								@click="edit_(scope.$index, scope.row)">编辑</el-button>
							<el-button type="text" v-if="scope.row.dataState == '启用' && scope.row.parentId == null"
								@click="stop(scope.$index, scope.row)">停用</el-button>
							<el-button v-if="scope.row.parentId == null" type="text" style="color: #f56c6c"
								@click="delete_(scope.$index, scope.row)">删除</el-button>
						</template>
					</el-table-column>
				</el-table>
			</SimpleBoardIndex>
		</el-main>
		<el-footer>
			<!--分页工具栏-->
			<pagination :total="tableQuery.total" :page.sync="tableQuery.page" :limit.sync="tableQuery.limit"
				@pagination="refreshData" />
		</el-footer>
	</el-container>
</template>

<script>
// 组件
import pagination from '@/view/components/Pagination/PaginationIndex';
import TableTools from '@/view/components/TableTools/index';
import SimpleBoardIndex from '@/view/components/SimpleBoard/SimpleBoardIndex';
// vuex状态值
import { mapGetters } from 'vuex';

// 接口api
import taskApi from '@/api/_system/task';
import ComplianceOrgApi from '@/api/ComplianceOrg/ComplianceOrg.js';
import selectCurrentUnit from "@/view/components/OrgSingleDialogSelect/selectCurrentUnit";
export default {
	name: 'HgrykIndexRegister',
	inject: ['layout'],
	components: { pagination, TableTools, SimpleBoardIndex, selectCurrentUnit },
	data() {
		return {
			orgTreeCurrentDeptDialog: false,
			checked: false,
			personData: [],
			dataForm: {
				enableTime: new Date().toISOString().substring(0, 10),
				disableTime: null,
				items: [],
			},
			rules: {
				enableTime: [
					{ required: true, message: '请选择启用时间', trigger: 'change' }
				],
				disableTime: [
					{
						validator: (rule, value, callback) => {
							if (value && this.dataForm.enableTime) {
								const enable = new Date(this.dataForm.enableTime)
								const disable = new Date(value)
								if (disable < enable) {
									callback(new Error('停用时间不能早于启用时间'))
								} else {
									callback()
								}
							}
							callback()
						},
						trigger: 'change'
					}
				]
			},
			tableDataOne: [],
			dialogVisible: false,
			dialogRoleVisible: false,
			tableQuery: {
				memberCode: null,//用户编码
				memberName: null,//用户名称
				memberGender: null,//用户性别
				organizationName: null,//先工作单位（部室）
				organization: null,//组织编码
				position: null,//岗位
				birthDate: null,//出生年月
				attendWorkerDate: null,//参加包钢日期
				plate: null,//板块
				professionNow: null,//现从事专业
				technicalQualification: null,//现有专业技术资格
				currentOgnId: null,//当前组织ID
				phone: null,//当前手机号
				page: 1, // 当前页码
				limit: 10, // 每页显示的记录数
				total: 0, // 总记录数
				fuzzyValue: null,
				dataState: null, // 数据状态
				dataStateCode: null, // 数据状态编码
				orderCol: null, // 排序字段
				orderColValue: false, // 排序字段是否升序
				functionCode: null, // 功能代码
				orgId: null, // 组织ID
				isQuery: false, // 是否查询
				currentOgnId: null, // 当前组织ID
			},
			table_height: '100%',
			tableData: [],
			tableLoading: false,
			formLabelWidth: "120px",
			ss: {
				data: this.tableData,
			},
		};
	},
	computed: {
		...mapGetters(['orgContext', 'currentFunctionId']),
	},
	watch: {
		'dataForm.enableTime'(newVal) {
			if (newVal && this.dataForm.disableTime) {
				this.$refs.dataForm.validateField('disableTime')
			}
		}
	},
	activated() {
		// 长连接页面第二次激活的时候,不会走created方法,会走此方法
		this.refreshData();
	},
	created() {
		this.refreshData();
	},

	mounted() {
		this.$nextTick(function () {
			this.table_height = window.innerHeight - this.$refs.table.$el.offsetTop - 84 - 45;

			// 监听窗口大小变化
			const self = this;
			window.onresize = function () {
				self.table_height = window.innerHeight - self.$refs.table.$el.offsetTop - 84 - 45;
			};
		});
	},
	methods: {
		isEdit(row) {
			return row.dataStateCode === this.utils.dataState_BPM.SAVE.code || row.dataStateCode === this.utils.dataState_BPM.BACK.code;
		},
		isDelete(row) {
			return row.dataStateCode === this.utils.dataState_BPM.SAVE.code;
		},
		changeChecked() {
			this.checked = !this.checked;
			this.refreshData();
		},
		showDialog() {
			console.log(this.orgContext)
			this.dialogRoleVisible = true;
			this.tableQuery.functionCode = this.currentFunctionId.functionCode;
			this.tableQuery.orgId = this.orgContext.currentOrgId;
			this.tableQuery.currentOgnId = this.orgContext.currentOgnId;
			this.tableQuery.currentPsnFullId = this.orgContext.currentPsnFullId;
			// 主要用于删除,当前页只有一条数据的时候,删除需要回到上一页
			if (this.tableDataOne.length === 0 && this.tableQuery.page > 1) {
				this.tableQuery.page--;
			}
			ComplianceOrgApi.queryDia(this.tableQuery).then((response) => {
				let rows = response.data.data.records;
				this.tableDataOne = rows;
				this.ss.data = rows;
				this.tableQuery.total = response.data.data.total;
				this.tableLoading = false;
			});
		},
		refreshData1() {
			this.tableQuery.functionCode = this.currentFunctionId.functionCode;
			this.tableQuery.orgId = this.orgContext.currentOrgId;
			this.tableQuery.currentOgnId = this.orgContext.currentOgnId;
			this.tableQuery.currentPsnFullId = this.orgContext.currentPsnFullId;
			// 主要用于删除,当前页只有一条数据的时候,删除需要回到上一页
			if (this.tableDataOne.length === 0 && this.tableQuery.page > 1) {
				this.tableQuery.page--;
			}
			ComplianceOrgApi.queryDia(this.tableQuery).then((response) => {
				let rows = response.data.data.records;
				this.tableDataOne = rows;
				this.ss.data = rows;
				this.tableQuery.total = response.data.data.total;
				this.tableLoading = false;
			});
		},
		// 刷新数据
		refreshData() {
			if (!this.checked) {
				// 赋值当前人组织全路径
				this.tableQuery.functionCode = this.currentFunctionId.functionCode;
				this.tableQuery.orgId = this.orgContext.currentOrgId;
				this.tableQuery.currentOgnId = this.orgContext.currentOgnId;
				this.tableQuery.currentPsnFullId = this.orgContext.currentPsnFullId;
				// 主要用于删除,当前页只有一条数据的时候,删除需要回到上一页
				if (this.tableData == null) {
					this.tableData = [];
				}
				if (this.tableData.length === 0 && this.tableQuery.page > 1) {
					this.tableQuery.page--;
				}
				ComplianceOrgApi.query(this.tableQuery)
					.then((response) => {
						let rows = response.data.data.records;
						this.tableData = rows;
						this.ss.data = rows;
						this.tableQuery.total = response.data.data.total;
						this.tableLoading = false;
					})
					.catch({});
			} else {
				this.tableQuery.functionCode = this.currentFunctionId.functionCode;
				this.tableQuery.orgId = this.orgContext.currentOrgId;
				this.tableQuery.currentOgnId = this.orgContext.currentOgnId;
				this.tableQuery.currentPsnFullId = this.orgContext.currentPsnFullId;
				if (this.tableData == null) {
					this.tableData = [];
				}
				// 主要用于删除,当前页只有一条数据的时候,删除需要回到上一页
				if (this.tableData.length === 0 && this.tableQuery.page > 1) {
					this.tableQuery.page--;
				}
				ComplianceOrgApi.queryHistory(this.tableQuery)
					.then((response) => {
						debugger
						let rows = response.data.data.records;
						this.tableData = rows;
						this.ss.data = rows;
						this.tableQuery.total = response.data.data.total;
						this.tableLoading = false;
					})
					.catch({});
			}
		},
		openPersonDialog() {
			this.dialogVisible = true;
		},
		// 启用
		start(index, row) {
			this.$confirm('是否启用此员工?', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning',
			})
				.then(() => {
					new Promise((resolve, reject) => {
						ComplianceOrgApi.update({
							id: row.id,
							dataState: row.dataState,
							dataStateCode: row.dataStateCode
						}).then((response) => {
							resolve(response);
						});
					}).then((value) => {
						this.$message.success('启用成功!');
						this.refreshData();
					});
				})
				.catch(() => {
					this.$message({
						type: 'info',
						message: '已取消停用',
					});
				});
		},
		// 停用
		stop(index, row) {
			this.$confirm('是否停用此员工?', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning',
			})
				.then(() => {
					new Promise((resolve, reject) => {
						ComplianceOrgApi.update({
							id: row.id,
							dataState: row.dataState,
							dataStateCode: row.dataStateCode
						}).then((response) => {
							resolve(response);
						});
					}).then((value) => {
						this.$message.success('停用成功!');
						this.refreshData();
					});
				})
				.catch(() => {
					this.$message({
						type: 'info',
						message: '已取消停用',
					});
				});
		},
		// 删除
		delete_(index, row) {
			this.$confirm('此操作将永久删除该数据及相关数据, 是否继续?', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning',
			})
				.then(() => {
					new Promise((resolve, reject) => {
						ComplianceOrgApi.delete({
							id: row.id,
						}).then((response) => {
							resolve(response);
						});
					}).then((value) => {
						this.tableData.splice(index, 1);
						this.$message.success('删除成功!');
					});
				})
				.catch(() => {
					this.$message({
						type: 'info',
						message: '已取消删除',
					});
				});
		},
		tableSort(column, prop, order) {
			this.tableQuery.sortName = column.prop;
			this.tableQuery.order = column.order === 'ascending';
			this.refreshData();
		},
		// 点击搜索按钮事件,回到第一页,重新刷新数据
		search_: function () {
			this.tableQuery.page = 1;
			this.refreshData();
		},
		// 点击清空按钮事件,清空查询条件属性,回到第一页,重新刷新数据
		empty_() {
			this.tableQuery.memberName = null;
			this.tableQuery.organizationName = null;
			this.tableQuery.organization = null;
			this.tableQuery.dataState = null;
			this.tableQuery.dataStateCode = null;
			this.tableQuery.memberCode = null;
			this.refreshData();
		},
		// 点击刷新按钮事件
		refresh_() {
			this.tableQuery.sortName = null;
			this.tableQuery.order = null;
			this.empty_();
		},
		handleClose() { },
		handleRoleClose() { },
		personSelectChange(selection) {
			this.personData = selection;
		},
		roleClick() {

			this.$refs.dataForm.validate(valid => {
				if (valid) {
					
					this.dataForm.currentOgnId = null;
					// 通过校验后的提交逻辑
					// this.dataForm.items[0].createOrgId = this.dataForm.items[0].currentOgnId;
					for (let i = 0; i < this.dataForm.items.length; i++){
						this.dataForm.items[i].createOgnId = this.orgContext.currentOgnId
						this.dataForm.items[i].createOgnName = this.orgContext.currentOgnName
						this.dataForm.items[i].createDeptId = this.orgContext.currentDeptId
						this.dataForm.items[i].createDeptName = this.orgContext.currentDeptName
						this.dataForm.items[i].createPsnId = this.orgContext.currentPsnId
						this.dataForm.items[i].createPsnName = this.orgContext.currentPsnName
						this.dataForm.items[i].createPsnFullId = this.orgContext.currentPsnFullId
						this.dataForm.items[i].createPsnFullName = this.orgContext.currentPsnFullName
						this.dataForm.items[i].createPsnPhone = this.orgContext.currentPsnPhone
						this.dataForm.items[i].createGroupId = this.orgContext.currentGroupId
						this.dataForm.items[i].createGroupName = this.orgContext.currentGroupName
						this.dataForm.items[i].createOrgId = this.orgContext.currentOrgId
						this.dataForm.items[i].createOrgName = this.orgContext.currentOrgName
						this.dataForm.items[i].createLegalUnitId = this.orgContext.currentLegalUnitId
						this.dataForm.items[i].createLegalUnitName = this.orgContext.currentLegalUnitName
						this.dataForm.items[i].createTime = new Date()
					}
					ComplianceOrgApi.save(this.dataForm).then(res => {
						this.$message.success('操作成功');
						this.refreshData();
						this.dialogVisible = false;
						this.dataForm.idNames = null;
						this.dataForm.ids = null;
						this.dataForm.items = [];
					});
				} else {
					this.$message.warning('请完善表单信息');
				}
			})
		},
		sureDialog() {
			var c = '';
			var cid = '';
			debugger;
			if (this.personData !== undefined && this.personData != null && this.personData.length > 0) {
				this.personData.forEach((item) => {
					if (c.length === 0) {
						c = c + item.memberName;
						cid = cid + item.memberCode;
					} else {
						c = c + ',' + item.memberName;
						cid = cid + ',' + item.memberCode;
					}
				});
			}
			this.dataForm.idNames = c;
			this.dataForm.ids = cid;
			this.dataForm.items = this.personData;
			this.dialogRoleVisible = false;
		},
		deptSelect(data) {
			let str = '';
			let id = '';
			data.map(obj => {
				str += obj.name;
				id += obj.unitId;
			})
			this.tableQuery.organization = id;
			this.tableQuery.organizationName = str;
		},
		view_(index,row){
			const tabId = row.id;
			this.layout.openNewTab(
				"合规人员库",
				"hgry_detail",
				"hgry_detail",
				tabId,
				{
					functionId: "hgry_detail," + tabId,
					...this.utils.routeState.VIEW(tabId),
					view: 'old'
				}
			);
		},
		edit_(index, row) {
			const tabId = row.id;
			this.layout.openNewTab(
				"合规人员库",
				"hgry_detail",
				"hgry_detail",
				tabId,
				{
					functionId: "hgry_detail," + tabId,
					...this.utils.routeState.EDIT(tabId),
					view: 'old'
				}
			);
		}
	},
};
</script>

<style scoped>
.el-table__fixed-body-wrapper {
	top: 50px !important;
}

.jobNoOk {
	display: flex;
	color: #f56c6c;
}

.jobOk {
	display: flex;
	color: #67c23a;
}

.form-minwidth-style {
	width: 1740px;
	height: 30px;
}

/deep/.el-pagination {
	text-align: center;
}
</style>