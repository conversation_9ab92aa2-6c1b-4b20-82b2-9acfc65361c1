
dcc070295ff9baf0c2a80021610a2cb3dd01deeb	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.394.1754018536329.js\",\"contentHash\":\"e5fa52634106789c82ce0ff0a2492d49\"}","integrity":"sha512-XO71BR92fCf0an7SRu2tUn1DSESkhXDEIZT9pNU7EekEnQjEGO7grg0zFBUZUzXdFiwlVIa7P3CqjyhMk9/RlQ==","time":1754018575976,"size":71791}