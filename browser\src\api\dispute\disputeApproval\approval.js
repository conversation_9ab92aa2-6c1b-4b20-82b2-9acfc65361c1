import {request} from '@/api/index'
export default {
    query(data) {
        return request({
            url: '/disputeapproval/query',
            method: 'post',
            data
        })
    },
    save(data) {
        return request({
            url: '/disputeapproval/save',
            method: 'post',
            data
        })
    },
    delete(data) {
        return request({
            url: '/disputeapproval/delete',
            method: 'post',
            data
        })
    },
    queryById(data) {
        return request({
            url: '/disputeapproval/queryById',
            method: 'post',
            data
        })
    },
    setParam(data) {
        return request({
            url: '/disputeapproval/setParam',
            method: 'post',
            data
        })
    }
}
