<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.klaw.dao.contractDao.SgSealApprovalMapper">
    <resultMap id="BaseResultMap" type="com.klaw.entity.contractBean.SgSealApproval">
        <id column="ID" jdbcType="VARCHAR" property="id"/>
        <result column="CONTRACT_NAME" jdbcType="VARCHAR" property="contractName"/>
        <result column="CONTRACT_ID" jdbcType="VARCHAR" property="contractId"/>
        <result column="SEAL_PURPOSE" jdbcType="VARCHAR" property="sealPurpose"/>
        <result column="SEAL_PURPOSE_ID" jdbcType="VARCHAR" property="sealPurposeId"/>
        <result column="SEAL_NAMES" jdbcType="VARCHAR" property="sealNames"/>
        <result column="SEAL_IDS" jdbcType="VARCHAR" property="sealIds"/>
        <result column="SEAL_TYPES" jdbcType="VARCHAR" property="sealTypes"/>
        <result column="SEAL_TYPE_IDS" jdbcType="VARCHAR" property="sealTypeIds"/>
        <result column="OTHER_SEAL_TIME" jdbcType="TIMESTAMP" property="otherSealTime"/>
        <result column="EFFECTIVE_TIME" jdbcType="TIMESTAMP" property="effectiveTime"/>
        <result column="SIGNING_ADDRESS" jdbcType="VARCHAR" property="signingAddress"/>
        <result column="ORIGINAL_ATTACHMENT" jdbcType="VARCHAR" property="originalAttachment"/>
        <result column="EFFECTIVE_DESCRIPTION" jdbcType="CLOB" property="effectiveDescription"/>
        <result column="TAKE_EFFECT_NAME" jdbcType="VARCHAR" property="takeEffectName"/>
        <result column="TAKE_EFFECT_CODE" jdbcType="CLOB" property="takeEffectCode"/>
        <result column="APPROVAL_CODE" jdbcType="VARCHAR" property="approvalCode"/>
        <result column="OTHER_PARTYS" jdbcType="CLOB" property="otherPartys"/>
        <result column="OUR_PARTYS" jdbcType="CLOB" property="ourPartys"/>
        <result column="CREATE_OGN_ID" jdbcType="VARCHAR" property="createOgnId"/>
        <result column="CREATE_OGN_NAME" jdbcType="VARCHAR" property="createOgnName"/>
        <result column="CREATE_DEPT_ID" jdbcType="VARCHAR" property="createDeptId"/>
        <result column="CREATE_DEPT_NAME" jdbcType="VARCHAR" property="createDeptName"/>
        <result column="CREATE_PSN_ID" jdbcType="VARCHAR" property="createPsnId"/>
        <result column="CREATE_PSN_NAME" jdbcType="VARCHAR" property="createPsnName"/>
        <result column="CREATE_ORG_ID" jdbcType="VARCHAR" property="createOrgId"/>
        <result column="CREATE_ORG_NAME" jdbcType="VARCHAR" property="createOrgName"/>
        <result column="CREATE_PSN_FULL_ID" jdbcType="VARCHAR" property="createPsnFullId"/>
        <result column="CREATE_PSN_FULL_NAME" jdbcType="VARCHAR" property="createPsnFullName"/>
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="DATA_STATE" jdbcType="VARCHAR" property="dataState"/>
        <result column="DATA_STATE_CODE" jdbcType="DECIMAL" property="dataStateCode"/>

        <result column="SEND_STATUS" jdbcType="VARCHAR" property="sendStatus"/>
        <result column="SEND_STATUS_NAME" jdbcType="VARCHAR" property="sendStatusName"/>
        <result column="FEEDBACK_CODE" jdbcType="VARCHAR" property="feedbackCode"/>
        <result column="FEEDBACK_NAME" jdbcType="VARCHAR" property="feedbackName"/>
        <result column="SIGNATURE_NUM" jdbcType="VARCHAR" property="signatureNum"/>
        <result column="SIGNATURE_URL" jdbcType="VARCHAR" property="signatureUrl"/>
        <result column="FEEDBACK_CONTENT" jdbcType="VARCHAR" property="feedbackContent"/>
        <result column="IS_SEAM" jdbcType="VARCHAR" property="isSeam"/>
    </resultMap>

    <sql id="Seal_Column_List">
        SSA.ID,
        SSA.CONTRACT_NAME,
        SSA.CONTRACT_ID,
        SSA.SEAL_PURPOSE,
        SSA.SEAL_PURPOSE_ID,
        SSA.OTHER_SEAL_TIME,
        SSA.EFFECTIVE_TIME,
        SSA.SIGNING_ADDRESS,
        SSA.ORIGINAL_ATTACHMENT,
        SSA.EFFECTIVE_DESCRIPTION,
        SSA.CREATE_OGN_ID,
        SSA.CREATE_OGN_NAME,
        SSA.CREATE_DEPT_ID,
        SSA.CREATE_DEPT_NAME,
        SSA.CREATE_GROUP_ID,
        SSA.CREATE_GROUP_NAME,
        SSA.CREATE_PSN_ID,
        SSA.CREATE_PSN_NAME,
        SSA.CREATE_ORG_ID,
        SSA.CREATE_ORG_NAME,
        SSA.CREATE_PSN_FULL_ID,
        SSA.CREATE_PSN_FULL_NAME,
        SSA.CREATE_TIME,
        SSA.UPDATE_TIME,
        SSA.DATA_STATE,
        SSA.DATA_STATE_CODE,
        SSA.SEAL_NAMES,
        SSA.SEAL_IDS,
        SSA.SEAL_TYPES,
        SSA.SEAL_TYPE_IDS,
        SSA.SEAL_ADMINS,
        SSA.SEAL_ADMIN_IDS,
        SSA.TAKE_EFFECT_NAME,
        SSA.TAKE_EFFECT_CODE,
        SSA.BOTH_SEALS_ATTACHMENT,
        SSA.CREATE_LEGAL_UNIT_ID,
        SSA.CREATE_LEGAL_UNIT_NAME,
        SSA.SEND_STATUS,
        SSA.SEND_STATUS_NAME,
        SSA.FEEDBACK_CODE,
        SSA.FEEDBACK_NAME,
        SSA.SIGNATURE_NUM,
        SSA.SIGNATURE_URL,
        SSA.FEEDBACK_CONTENT,
        SSA.IS_SEAM,
        SCA.CONTRACT_CODE,
        SSA.PDF_FILE

    </sql>

    <sql id="Base_Column_List">
        m
        .
        ID
        ,  m.CONTRACT_NAME,  m.CONTRACT_ID,  m.SEAL_PURPOSE,  m.SEAL_PURPOSE_ID, m.SEAL_NAMES, m.SEAL_IDS, m.SEAL_TYPES, m.SEAL_TYPE_IDS, m.OUR_SEAL_TIME,
    m.OTHER_SEAL_TIME,  m.EFFECTIVE_TIME,  m.SIGNING_ADDRESS,  m.ORIGINAL_ATTACHMENT,  m.EFFECTIVE_DESCRIPTION,
    m.CREATE_OGN_ID,  m.CREATE_OGN_NAME,  m.CREATE_DEPT_ID,  m.CREATE_DEPT_NAME,  m.CREATE_GROUP_ID,
    m.CREATE_GROUP_NAME,  m.CREATE_PSN_ID,  m.CREATE_PSN_NAME,  m.CREATE_ORG_ID,  m.CREATE_ORG_NAME,
    m.CREATE_PSN_FULL_ID,  m.CREATE_PSN_FULL_NAME,  m.CREATE_TIME,  m.UPDATE_TIME,  m.DATA_STATE,  m.DATA_STATE_CODE,
     d.ID as DID,  d.PARENT_ID,  d.SEAL_NUMBER_OLD,  d.PRINTS_NUMBER_OLD,  d.SEAL_ADMIN_OLD,  d.SEAL_ADMIN_ID_OLD,  d.SEAL_NAME,
    d.SEAL_ID,  d.SEAL_TYPE,  d.SEAL_TYPE_ID,  d.SEAL_NUMBER,  d.PRINTS_NUMBER,  d.OUR_SEAL_TIME,
    d.SEAL_ADMIN,  d.SEAL_ADMIN_ID
    </sql>
    <select id="queryPageData" resultType="com.klaw.entity.contractBean.SgSealApproval">
        select
        m.id          as "id"        ,
        m.contract_name          as "contractName"        ,
        m.contract_code          as "contractCode"        ,
        d.data_source_code       as "dataSourceCode"      ,
        m.contract_id            as "contractId"          ,
        m.seal_purpose           as "sealPurpose"         ,
        m.seal_purpose_id        as "sealPurposeId"       ,
        m.seal_names             as "sealNames"           ,
        m.seal_ids               as "sealIds"             ,
        m.seal_types             as "sealTypes"           ,
        m.seal_type_ids          as "sealTypeIds"         ,
        m.seal_admins            as "sealAdmins"          ,
        m.seal_admin_ids         as "sealAdminIds"        ,
        m.other_seal_time        as "otherSealTime"       ,
        m.effective_time         as "effectiveTime"       ,
        m.signing_address        as "signingAddress"      ,
        m.original_attachment    as "originalAttachment"  ,
        m.both_seals_attachment  as "bothSealsAttachment" ,
        m.take_effect_name       as "takeEffectName"      ,
        m.take_effect_code       as "takeEffectCode"      ,
        d.approval_Code          as "approvalCode"        ,
        d.other_party_name           as "otherPartys"         ,
        d.our_party_name             as "ourPartys"           ,
        m.create_ogn_id          as "createOgnId"         ,
        m.create_ogn_name        as "createOgnName"       ,
        m.create_dept_id         as "createDeptId"        ,
        m.create_dept_name       as "createDeptName"      ,
        m.create_group_id        as "createGroupId"       ,
        m.create_group_name      as "createGroupName"     ,
        m.create_psn_id          as "createPsnId"         ,
        m.create_psn_name        as "createPsnName"       ,
        m.create_org_id          as "createOrgId"         ,
        m.create_org_name        as "createOrgName"       ,
        m.create_psn_full_id     as "createPsnFullId"     ,
        m.create_psn_full_name   as "createPsnFullName"   ,
        m.create_legal_unit_id   as "createLegalUnitId"   ,
        m.create_legal_unit_name as "createLegalUnitName" ,
        m.create_time            as "createTime"          ,
        m.update_time            as "updateTime"          ,
        m.data_state             as "dataState"           ,
        m.data_state_code        as "dataStateCode"       ,
        m.send_status            as "sendStatus"          ,
        m.send_status_name       as "sendStatusName"      ,
        m.feedback_code          as "feedbackCode"        ,
        m.feedback_name          as "feedbackName"        ,
        m.signature_num          as "signatureNum"        ,
        m.signature_url          as "signatureUrl"        ,
        m.feedback_content       as "feedbackContent"     ,
        m.is_seam                as "isSeam"
        from
        SG_SEAL_APPROVAL m
        left join bm_contract d
        on
        m.contract_Id = d.ID
        <where>
            ${ew.sqlSegment}
        </where>
    </select>
    <select id="querySealDetailList" resultType="java.util.Map">
        select
        s.id as "id",
        s.contract_name as "contractName",
        s.contract_code as "contractCode",
        s.contract_id as "contractId",
        s.seal_Purpose as "sealPurpose",
        s.seal_Purpose_id as "sealPurposeId",
        s.seal_names as "sealNames",
        s.seal_ids as "sealIds",
        s.seal_types as "sealTypes",
        s.seal_type_ids as "sealTypeIds",
        s.seal_admins as "sealAdmins",
        s.seal_admin_ids as "sealAdminIds",
        s.create_psn_name as "createPsnName",
        s.create_dept_name as "createDeptName",
        d.seal_Name as "sealName",
        d.seal_Type as "sealType",
        d.seal_Admin_Old as "sealAdminOld",
        d.our_seal_time as "ourSealTime",
        d.seal_number as "sealNumber",
        d.prints_number as "printsNumber",
        d.seal_number_old as "sealNumberOld",
        d.prints_number_old as "printsNumberOld"
        from sg_seal_approval s
                 join sg_seal_approval_detail d
                      on s.id = d.parent_Id
        <where>
            ${ew.sqlSegment}
        </where>
        order by s.create_time DESC
    </select>

    <select id="getSealApprovalByContractNum" resultMap="BaseResultMap">
        SELECT <include refid="Seal_Column_List"/>
        FROM SG_SEAL_APPROVAL SSA
        INNER JOIN SG_CONTRACT_APPROVAL SCA
            ON SSA.CONTRACT_ID = SCA.ID
        WHERE SSA.SEAL_PURPOSE ='合同用印'
        AND SCA.CONTRACT_CODE = #{contractNum}
    </select>

    <select id="getSealApprovalByIds" resultMap="BaseResultMap">
        SELECT <include refid="Seal_Column_List"/>
        FROM SG_SEAL_APPROVAL SSA
        INNER JOIN SG_CONTRACT_APPROVAL SCA
        ON SSA.CONTRACT_ID = SCA.ID
        WHERE SSA.SEAL_PURPOSE ='合同用印'
        AND SSA.ID IN
        <foreach collection="ids" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
    </select>
</mapper>