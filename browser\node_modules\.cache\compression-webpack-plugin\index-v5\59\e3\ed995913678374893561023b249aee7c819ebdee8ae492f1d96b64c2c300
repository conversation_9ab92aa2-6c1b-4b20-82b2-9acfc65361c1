
ffd67c256ecdc15277cab218fcdf8590dafc9349	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.398.1754018536329.js\",\"contentHash\":\"83a1ad32655ba367ef96abf9460ff823\"}","integrity":"sha512-+HHVi9+Dp8kf3WeCEb7OSMIggTwXUEnYBo7xjdKZNwH7Ou+UU0nnzrp6Az8kkyphiNWKnUVTGG4FnxaZULDUQQ==","time":1754018576026,"size":163053}