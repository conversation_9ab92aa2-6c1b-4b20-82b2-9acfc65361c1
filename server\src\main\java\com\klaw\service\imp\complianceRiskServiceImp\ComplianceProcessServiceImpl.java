package com.klaw.service.imp.complianceRiskServiceImp;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.klaw.dao.complianceRiskDao.ComplianceProcessMapper;
import com.klaw.entity.complianceRiskBean.ComplianceProcessEntity;
import com.klaw.service.complianceRiskService.ComplianceProcessService;
import com.klaw.utils.PageUtils;
import com.klaw.vo.Json;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class ComplianceProcessServiceImpl extends ServiceImpl<ComplianceProcessMapper, ComplianceProcessEntity> implements ComplianceProcessService {


    @Override
    public Page<ComplianceProcessEntity> queryPageData(JSONObject json) {
        QueryWrapper<ComplianceProcessEntity> wrapper = new QueryWrapper<>();
        getFilter(json, wrapper);
        return page(new PageUtils<>(json), wrapper);
    }



    public void getFilter(JSONObject json, QueryWrapper<ComplianceProcessEntity> wrapper) {
        //事项名称
        String itemName = json.containsKey("itemName") ? json.getString("itemName") : null;
        //事项名称
        String dataStateCode = json.containsKey("dataStateCode") ? json.getString("dataStateCode") : null;

        // 模糊搜索值
        String fuzzyValue = json.containsKey("fuzzyValue") ? json.getString("fuzzyValue") : null;
        // 排序字段
        String sortName = json.containsKey("sortName") ? json.getString("sortName") : null;

        String orgId = json.containsKey("orgId")? json.getString( "orgId"):"";
        // 排序字段是否升序
        boolean order = json.containsKey("order") ? json.getBoolean("order") : false;

        if(StringUtils.isNotBlank(orgId)){
            wrapper.eq("create_org_id", orgId);
        }

        if (StringUtils.isNotBlank(fuzzyValue)) {
            wrapper.and(i -> i.like("item_name", fuzzyValue))
                    .or().like("data_state", fuzzyValue);
        }
        // 应用查询条件
        if (StringUtils.isNotBlank(itemName)) {
            wrapper.and(i -> i.like("item_name", itemName));
        }

        // 应用查询条件
        if (StringUtils.isNotBlank(dataStateCode)) {
            wrapper.and(i -> i.eq("data_state_code", dataStateCode));
        }

        if (StringUtils.isNotBlank(sortName) && order) {
            wrapper.orderByAsc(sortName);
        } else if (StringUtils.isNotBlank(sortName)) {
            wrapper.orderByDesc(sortName);
        }
        //按创建时间倒序显示
        wrapper.orderByDesc("create_time");
    }


    @Override
    public ComplianceProcessEntity queryDataById(String id) {
        ComplianceProcessEntity complianceProcessEntity = getById(id);
        return complianceProcessEntity;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Json deleteDataById(String id) {
        boolean flag = removeById(id);
        if (!flag) {
            return Json.fail().msg("未找到需要删除的数据");
        }
        return Json.succ().msg("删除成功!");
    }
}




