
5336bd09e22fbb15269f36430eb1be2279d5cb9f	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.11.1754018536329.js\",\"contentHash\":\"c4a6f40bd536062f363cb5c689ca96fb\"}","integrity":"sha512-kPe02TaFaBx5gnF4rIKRsQMjxXMOTy52eoveE9lDRDNAh5Em1cpLJuxNTYs0qxd1fW5kZtLTiLTjdL7NrfBnRQ==","time":1754018575955,"size":54152}