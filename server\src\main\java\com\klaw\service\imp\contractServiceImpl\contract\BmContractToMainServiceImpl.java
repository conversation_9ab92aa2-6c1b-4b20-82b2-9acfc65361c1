package com.klaw.service.imp.contractServiceImpl.contract;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.klaw.constant.DataStateBPM;
import com.klaw.dao.contractDao.contract.BmContractMapper;
import com.klaw.entity.authorizationBean.Authorization;
import com.klaw.entity.contractBean.SgContractType;
import com.klaw.entity.contractBean.SgSealApproval;
import com.klaw.entity.contractBean.SgSealApprovalDetail;
import com.klaw.entity.contractBean.contract.*;
import com.klaw.entity.mainDataBean.MainMidStaff;
import com.klaw.entity.systemBean.SgSysDoc;
import com.klaw.service.authorizationService.AuthorizationService;
import com.klaw.service.contractService.SgContractTypeService;
import com.klaw.service.contractService.SgSealApprovalDetailService;
import com.klaw.service.contractService.SgSealApprovalService;
import com.klaw.service.contractService.contract.*;
import com.klaw.service.imp.contractServiceImpl.SgProjectManageServiceImpl;
import com.klaw.service.systemService.SgHrOrgUnitBService;
import com.klaw.service.systemService.SgKvsequenceService;
import com.klaw.service.systemService.SysDocService;
import com.klaw.utils.*;
import kong.unirest.HttpResponse;
import kong.unirest.Unirest;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.*;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 服务实现类
 * 合同实现类
 *
 * <AUTHOR>
 * @since 2024-10-14
 */
@Service
public class BmContractToMainServiceImpl extends ServiceImpl<BmContractMapper, BmContract> implements BmContractToMainService {
    @Autowired
    private BmContractService bmContractService;
    @Autowired
    private BmContractProjectService bmContractProjectService;
    @Autowired
    private BmContractPerformService bmContractPerformService;
    @Autowired
    private BmContractPerformResultService bmContractPerformResultService;
    @Value("${esb.url}")
    private String esbUrl;
    @Autowired
    private SgSealApprovalService sgSealApprovalService;
    @Autowired
    private BmContractLogService bmContractLogService;
    @Autowired
    private SysDocService sysDocService;

    private static final Map<String,String> contractKeyMap = new HashMap<>();
    private static final Map<String,String> projectKeyMap = new HashMap<>();
    private static final Map<String,String> performKeyMap = new HashMap<>();
    private static final Map<String,String> resultKeyMap = new HashMap<>();
    private static final Map<String,String> ourPositionMap = new HashMap<>();



    private static SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");


    static {
        loadContractKeyMap();
        loadProjectKeyMap();
        loadPerformKeyMap();
        loadResultKeyMap();
        loadOurPositionMap();
    }

    private static void loadOurPositionMap() {
        // 将数据推入HashMap中
        ourPositionMap.put("买受人", "01");
        ourPositionMap.put("出卖人", "02");
        ourPositionMap.put("供应人", "03");
        ourPositionMap.put("赠与人", "04");
        ourPositionMap.put("融入方", "05");
        ourPositionMap.put("债权人", "06");
        ourPositionMap.put("出租人", "07");
        ourPositionMap.put("转让人", "08");
        ourPositionMap.put("购买人", "09");
        ourPositionMap.put("不填", "10");
        ourPositionMap.put("承揽人", "11");
        ourPositionMap.put("发包人", "12");
        ourPositionMap.put("委托人", "13");
        ourPositionMap.put("承包人", "14");
        ourPositionMap.put("托运人", "15");
        ourPositionMap.put("合作开发人", "16");
        ourPositionMap.put("许可人", "17");
        ourPositionMap.put("让与人", "18");
        ourPositionMap.put("共同申请人", "19");
        ourPositionMap.put("寄存人", "20");
        ourPositionMap.put("存货人", "21");
        ourPositionMap.put("普通合伙人", "22");
        ourPositionMap.put("出资人", "23");
        ourPositionMap.put("受让人", "24");
        ourPositionMap.put("增资人", "25");
        ourPositionMap.put("用人单位", "26");
        ourPositionMap.put("用工单位", "27");
        ourPositionMap.put("节能服务方", "28");
        ourPositionMap.put("服务方", "29");
        ourPositionMap.put("提供方", "30");
        ourPositionMap.put("赞助人", "31");
        ourPositionMap.put("投资人", "32");
        ourPositionMap.put("委托方", "33");
        ourPositionMap.put("使用人", "34");
        ourPositionMap.put("受赠人", "35");
        ourPositionMap.put("融出方", "36");
        ourPositionMap.put("担保人", "37");
        ourPositionMap.put("承租人", "38");
        ourPositionMap.put("债务人", "39");
        ourPositionMap.put("定作人", "40");
        ourPositionMap.put("设计人", "41");
        ourPositionMap.put("勘察人", "42");
        ourPositionMap.put("监理人", "43");
        ourPositionMap.put("咨询人", "44");
        ourPositionMap.put("分包人", "45");
        ourPositionMap.put("承运人", "46");
        ourPositionMap.put("受托人", "47");
        ourPositionMap.put("被许可人", "48");
        ourPositionMap.put("开发人", "49");
        ourPositionMap.put("保管人", "50");
        ourPositionMap.put("服务人", "51");
        ourPositionMap.put("有限合伙人", "52");
        ourPositionMap.put("被增资人", "53");
        ourPositionMap.put("被服务方", "54");
        ourPositionMap.put("中介人", "55");
        ourPositionMap.put("行纪人", "56");
        ourPositionMap.put("接收方", "57");
        ourPositionMap.put("被赞助人", "58");
        ourPositionMap.put("用户", "59");
        ourPositionMap.put("运营方", "60");
        ourPositionMap.put("治理方", "61");
        ourPositionMap.put("保函申请人", "62");
        ourPositionMap.put("其他", "63");
    }

    /**
     * 封装主数据所需内容
     * @param bmContract
     * @return
     */
    @Override
    public JSONObject formatteContract(BmContract bmContract) {
        JSONObject jsonObject = new JSONObject(JSONObject.parseObject(JSONObject.toJSONString(bmContract)));
        System.out.println(jsonObject.toJSONString());
        jsonObject = JSONUtil.removeKey(jsonObject, contractKeyMap);
        JSONObject jsonObj = JSONUtil.changeJsonObj(jsonObject, contractKeyMap);
        /*QueryWrapper<SgSealApproval> query = new QueryWrapper<>();
        query.eq("contract_id",bmContract.getId());
        SgSealApproval seal = sgSealApprovalService.getOne(query);
        String originalAttachment = seal.getOriginalAttachment();*/
//        JSONArray jsonArray = JSONObject.parseArray(originalAttachment);
//        JSONObject obj = (JSONObject) jsonArray.get(0);

        for (Map.Entry<String, String> entry : contractKeyMap.entrySet()) {
            if (!jsonObj.containsKey(entry.getValue()) || jsonObj.get(entry.getValue())==null){
                jsonObj.put(entry.getValue(),"");
            }
        }
        jsonObj.put("desc50","100016");
//        jsonObj.put("desc50_desc","主数据管理系统");
        jsonObj.put("desc53",sdf.format(bmContract.getUpdateTime()));
        jsonObj.put("desc54",sdf.format(bmContract.getCreateTime()));

        jsonObj.put("desc7", (bmContract.getCustom()== null || !bmContract.getCustom())?"2":"1");
        jsonObj.put("desc17", (bmContract.getIsTax()== null || !bmContract.getIsTax())?"2":"1");
        jsonObj.put("desc33",bmContract.getAgreedStartTime()!=null?sdf.format(bmContract.getAgreedStartTime()): "");
        jsonObj.put("desc34",bmContract.getAgreedEndTime()!=null?sdf.format(bmContract.getAgreedEndTime()):"");
        jsonObj.put("desc35", (bmContract.getWhetherGroupMajor()== null || !bmContract.getWhetherGroupMajor())?"2":"1");
        jsonObj.put("desc36", (bmContract.getWhetherUnitMajor()== null || !bmContract.getWhetherUnitMajor())?"2":"1");
        //jsonObj.put("desc44",bmContract.getContractTakeEffectFile()==null?"":bmContract.getContractTakeEffectFile());
        if (bmContract.getContractTakeEffectFile()!=null){
            JSONArray jsonArray = JSONObject.parseArray(bmContract.getContractTakeEffectFile());
            StringBuilder fileIds = new StringBuilder();
            for (Object o : jsonArray) {
                JSONObject jsono = (JSONObject) o;
                SgSysDoc doc = sysDocService.getById(jsono.getString("docId"));
                if (doc==null) continue;
                fileIds.append(doc.getFileId()).append(",");
            }
            jsonObj.put("desc44", fileIds.substring(0,fileIds.length()-1));
        }else{
            jsonObj.put("desc44","");
        }

        jsonObj.put("desc46",bmContract.getContractTakeEffectDate()==null?"":sdf.format(bmContract.getContractTakeEffectDate()));
        jsonObj.put("desc52",ourPositionMap.get(bmContract.getOurPosition())==null?"":ourPositionMap.get(bmContract.getOurPosition()));
        jsonObj.put("desc37","关联授权".equals(bmContract.getAuthorizedSource())?"1":"0");

        jsonObj.put("state","1");
//        jsonObj.put("desc15",settlementMethodMap.get(bmContract.getSettlementMethodCode().trim())==null?"":settlementMethodMap.get(bmContract.getSettlementMethodCode().trim()));
        //valueAddedTaxRateCodeMap.get(bmContract.getValueAddedTaxRateCode().trim())
//        jsonObj.put("desc18", Arrays.stream(bmContract.getValueAddedTaxRateCode().split(",")).map(valueAddedTaxRateCodeMap::get).collect(Collectors.joining(",")));
        jsonObj.replaceAll((k, v) -> jsonObj.get(k) + "");
        //查询该合同对应的项目
        List<BmContractProject> projectList = bmContractProjectService.getContractProjectByContractId(bmContract.getId());
        JSONArray projectArr = new JSONArray();
        projectList.forEach(pro->{
            JSONObject proJson = new JSONObject(JSONObject.parseObject(JSONObject.toJSONString(pro, SerializerFeature.WriteMapNullValue)));
            proJson = JSONUtil.removeKey(proJson, projectKeyMap);
            proJson = JSONUtil.changeJsonObj(proJson, projectKeyMap);
            for (Map.Entry<String, String> entry : projectKeyMap.entrySet()) {
                if (!proJson.containsKey(entry.getValue()) || proJson.get(entry.getValue())==null){
                    proJson.put(entry.getValue(),"");
                }
            }
            proJson.put("desc12",sdf.format(bmContract.getUpdateTime()));
            proJson.put("desc13",sdf.format(pro.getCreateTime()));
            proJson.put("desc14",bmContract.getCreatePsnId());
            proJson.put("desc15",bmContract.getCreatePsnName());
            proJson.put("desc16",bmContract.getCreateDeptId());
            proJson.put("desc18",bmContract.getCreateDeptName());
            proJson.put("desc19",bmContract.getCreateOgnId());
            proJson.put("desc20",bmContract.getCreateOgnName());
            proJson.put("desc4", (pro.getWhether() == null || !pro.getWhether())?"2":"1");
//            proJson.put("desc4_desc",pro.getWhether()==null?"否":"是");
            proJson.put("mdmId","1000000270");
            proJson.put("state","1");
//            proJson.put("state_desc","正常");
            JSONObject finalProJson = proJson;
            proJson.replaceAll((k, v) -> finalProJson.get(k) + "");
            projectArr.add(proJson);
        });
        jsonObj.put("info_1",projectArr);
        //查询该合同对应的履行计划
        List<BmContractPerform> performList = bmContractPerformService.queryByContractId(bmContract.getId());
        JSONArray performArr = new JSONArray();
        String[] ourList = bmContract.getOurPartyList().split(",");
        String[] otherList = bmContract.getOtherPartyList().split(",");
        String[] ourNameList = bmContract.getOurPartyName().split(",");
        String[] otherNameList = bmContract.getOtherPartyName().split(",");
        performList.forEach(perform->{
            Integer ourIndex = Arrays.stream(ourNameList)
                    .filter(s -> s.equals(perform.getOurPartyName()))
                    .mapToInt(s->Arrays.asList(ourNameList).indexOf(s))
                    .findFirst()
                    .orElse(-1);
            Integer otherIndex = Arrays.stream(otherNameList)
                    .filter(s -> s.equals(perform.getOtherPartyName()))
                    .mapToInt(s->Arrays.asList(otherNameList).indexOf(s))
                    .findFirst()
                    .orElse(-1);

            JSONObject performJson = new JSONObject(JSONObject.parseObject(JSONObject.toJSONString(perform,SerializerFeature.WriteMapNullValue)));
            performJson = JSONUtil.removeKey(performJson, performKeyMap);
            performJson = JSONUtil.changeJsonObj(performJson, performKeyMap);
            for (Map.Entry<String, String> entry : performKeyMap.entrySet()) {
                if (!performJson.containsKey(entry.getValue()) || performJson.get(entry.getValue())==null){
                    performJson.put(entry.getValue(),"");
                }
            }
            performJson.put("desc6",bmContract.getRevenueExpenditureCode());
            performJson.put("desc7",bmContract.getSettlementMethodCode());
            performJson.put("desc23",ourList[ourIndex]);
            performJson.put("desc24",otherList[otherIndex]);
            performJson.put("desc12",bmContract.getCurrencyCode());
            performJson.put("desc13",bmContract.getExchangeRateMethodCode() == null ? "" : bmContract.getExchangeRateMethodCode());
            performJson.put("desc14",bmContract.getExchangeRate());
            performJson.put("desc18",sdf.format(perform.getUpdateTime()));
            performJson.put("desc19",sdf.format(perform.getCreateTime()));
            performJson.put("desc8",sdf.format(perform.getPlanDate()));
            performJson.put("desc16",sdf.format(perform.getPlanInvoiceDate()));
            performJson.put("desc28",perform.getPeriodDay());
            performJson.put("desc29",bmContract.getSettlementMethodCode());
            performJson.put("desc30",bmContract.getSettlementMethod());
            performJson.put("mdmId","1000000329");
            performJson.put("state","1");
            performJson.put("desc17",performJson.getString("desc17").replaceAll("\\n","newline"));
//            performJson.put("state_desc","正常");
            JSONObject finalProJson = performJson;
            performJson.replaceAll((k, v) -> finalProJson.get(k) + "");
            performArr.add(performJson);
        });
        jsonObj.put("info_2",performArr);
        //查询该合同对应的履行完成记录
        List<BmContractPerformResult> resultList = bmContractPerformResultService.queryByContractCode(bmContract.getContractCode()).stream().filter(result-> result.getPerformCode()!=null).collect(Collectors.toList());
        JSONArray resultArr = new JSONArray();
        resultList.forEach(result->{
            Integer ourIndex = Arrays.stream(ourNameList)
                    .filter(s -> s.equals(result.getOurPartyName()))
                    .mapToInt(s->Arrays.asList(ourNameList).indexOf(s))
                    .findFirst()
                    .orElse(-1);
            Integer otherIndex = Arrays.stream(otherNameList)
                    .filter(s -> s.equals(result.getOtherPartyName()))
                    .mapToInt(s->Arrays.asList(otherNameList).indexOf(s))
                    .findFirst()
                    .orElse(-1);
            JSONObject resultJson = new JSONObject(JSONObject.parseObject(JSONObject.toJSONString(result, SerializerFeature.WriteMapNullValue)));
            resultJson = JSONUtil.removeKey(resultJson, resultKeyMap);
            resultJson = JSONUtil.changeJsonObj(resultJson, resultKeyMap);
            for (Map.Entry<String, String> entry : resultKeyMap.entrySet()) {
                if (!resultJson.containsKey(entry.getValue()) || resultJson.get(entry.getValue())==null){
                    resultJson.put(entry.getValue(),"");
                }
            }

            resultJson.put("desc2",ourList[ourIndex]);
            resultJson.put("desc3",ourList[otherIndex]);
            resultJson.put("desc18",sdf.format(result.getActualPerformDate()));
            resultJson.put("desc21",sdf.format(result.getCreateTime()));
            resultJson.put("desc22",sdf.format(result.getCreateTime()));
            resultJson.put("desc31",bmContract.getSettlementMethodCode());
            resultJson.put("desc32",bmContract.getSettlementMethod());
            resultJson.put("mdmId","1000000337");
            resultJson.put("state","1");
//            resultJson.put("state_desc","正常");
            JSONObject finalResultJson = resultJson;
            resultJson.replaceAll((k, v) -> finalResultJson.get(k) + "");
            resultArr.add(resultJson);
        });
        jsonObj.put("info_3",resultArr);
        //主数据要求，数据格式中不得出现\n 字符，将换行父替换为 newline
        jsonObj.put("desc42",jsonObj.getString("desc42").replaceAll("\\n","newline"));
        jsonObj.put("desc43",jsonObj.getString("desc43").replaceAll("\\n","newline"));
        JSONObject result = new JSONObject();
        result.put("mdmId","1000000341");
        result.put("BusinessSystem","法务管理系统--合同明细");
        result.put("user","FWGLXT");
        result.put("psw","Mdm123456");
        JSONArray fileArr = new JSONArray();
        fileArr.add(jsonObj);
        result.put("Field",fileArr);
        return result;
    }


    private static void loadContractKeyMap(){
        // 添加键值对到 Map 中
        contractKeyMap.put("dataStateCode", "state");
//        contractKeyMap.put("dataState", "state_desc");
        contractKeyMap.put("contractCodeVersion", "code");
        contractKeyMap.put("contractName", "desc1");
        contractKeyMap.put("ourPartyName", "desc10");
        contractKeyMap.put("otherPartyList", "desc11");
        contractKeyMap.put("otherPartyName", "desc12");
        contractKeyMap.put("moneyTypeCode", "desc13");
        contractKeyMap.put("revenueExpenditureCode", "desc14");

        contractKeyMap.put("contractMoney", "desc16");
//        contractKeyMap.put("valueAddedTaxRate", "desc17");  是否含税
//        contractKeyMap.put("valueAddedTaxRateCode", "desc18");
        contractKeyMap.put("valueAddedTaxAmount", "desc19");
        contractKeyMap.put("contractTypeCode", "desc2");
        contractKeyMap.put("currencyCode", "desc20");
        contractKeyMap.put("exchangeRateMethodCode", "desc21");
        contractKeyMap.put("exchangeRate", "desc22");
        contractKeyMap.put("contractMoneyRmb", "desc23");
        contractKeyMap.put("afterChangeMoney", "desc24");
        contractKeyMap.put("changeMoneyTypeCode", "desc26");
        contractKeyMap.put("thisChangeMoney", "desc27");
//        contractKeyMap.put("thisChangeValueAddedTaxAmount", "desc28");
//        contractKeyMap.put("afterChangeValueAddedTaxAmount", "desc29");
        contractKeyMap.put("dataTypeCode", "desc3");
        contractKeyMap.put("contractExecutoryMoney", "desc30");
        contractKeyMap.put("contractExecutedMoney", "desc31");
        contractKeyMap.put("explain", "desc32");
//        contractKeyMap.put("agreedStartTime", "desc33");
//        contractKeyMap.put("agreedEndTime", "desc34");
//        contractKeyMap.put("是否集团重大合同", "desc35");
//        contractKeyMap.put("是否本单位重大合同", "desc36");
        contractKeyMap.put("authorizedSource", "desc37");
        contractKeyMap.put("authorizedCode", "desc38");
        contractKeyMap.put("authorizedName", "desc39");
        contractKeyMap.put("approvalCode", "desc4");
        contractKeyMap.put("authorizedPerson", "desc40");
        contractKeyMap.put("authorizedRange", "desc41");
        contractKeyMap.put("summaryNote", "desc42");
        contractKeyMap.put("contractSubjectMatter", "desc43");
        //contractKeyMap.put("", "desc44");
        contractKeyMap.put("takeEffectCode", "desc45");
//        contractKeyMap.put("contractTakeEffectDate", "desc46");
        contractKeyMap.put("closeStateCode", "desc47");
        contractKeyMap.put("performStateCode", "desc48");
        contractKeyMap.put("dataSourceCode", "desc49");
        contractKeyMap.put("projectDecisionCode", "desc5");
        contractKeyMap.put("assetDocNo", "desc51");
//        contractKeyMap.put("ourPosition", "desc52");
        contractKeyMap.put("createPsnId", "desc55");
        contractKeyMap.put("createPsnName", "desc56");
        contractKeyMap.put("createDeptId", "desc57");
        contractKeyMap.put("createDeptName", "desc58");
        contractKeyMap.put("createOgnId", "desc59");
        contractKeyMap.put("relativeMethodCode", "desc6");
        contractKeyMap.put("createOgnName", "desc60");
        contractKeyMap.put("excludingTaxAmount", "desc64");
        contractKeyMap.put("contractCode", "desc65");
        contractKeyMap.put("version", "desc66");
        contractKeyMap.put("valueAddedTaxRateCode", "desc67");
        contractKeyMap.put("valueAddedTaxRate", "desc68");
        contractKeyMap.put("sectionCode", "desc69");
//        contractKeyMap.put("custom", "desc7");
        contractKeyMap.put("customCode", "desc8");
        contractKeyMap.put("settlementMethodCode", "desc87");
        contractKeyMap.put("settlementMethod", "desc88");
        contractKeyMap.put("ourPartyList", "desc9");

    }
    private static void loadProjectKeyMap() {
        projectKeyMap.put("id", "code");
        projectKeyMap.put("projectName", "desc1");
        projectKeyMap.put("changeAssignMoney", "desc10");
        projectKeyMap.put("projectNoPerformMoney", "desc11");
//        projectKeyMap.put("updateTime", "desc12");
//        projectKeyMap.put("createTime", "desc13");
//        projectKeyMap.put("createPsnId", "desc14");
//        projectKeyMap.put("createPsnName", "desc15");
//        projectKeyMap.put("createDeptId", "desc16");
//        projectKeyMap.put("projectCode", "desc17");
//        projectKeyMap.put("createDeptName", "desc18");
//        projectKeyMap.put("createOrgId", "desc19");
        projectKeyMap.put("childProjectCode", "desc2");
//        projectKeyMap.put("createOrgName", "desc20");
        projectKeyMap.put("projectTypeCode","desc3");
//        projectKeyMap.put("whether","desc4");
        projectKeyMap.put("totalMoney", "desc5");
        projectKeyMap.put("surplusMoney", "desc6");
        projectKeyMap.put("assignMoney", "desc7");
        projectKeyMap.put("projectChangeMoneyTypeCode", "desc8");
//        projectKeyMap.put("projectChangeMoneyType", "desc8_desc");
        projectKeyMap.put("projectThisChangeMoney", "desc9");
        projectKeyMap.put("dataStateCode", "state");



    }

    private static void loadPerformKeyMap(){
        // 添加键值对到 Map 中
        performKeyMap.put("performCode", "code");
        performKeyMap.put("contractCode", "desc1");
        performKeyMap.put("planRatio", "desc10");
        performKeyMap.put("projectCode", "desc11");
//        performKeyMap.put("currencyCode", "desc12");
//        performKeyMap.put("exchangeRateMethodCode", "desc13");
//        performKeyMap.put("exchangeRate", "desc14");
//        performKeyMap.put("planInvoiceDate", "desc16");
        performKeyMap.put("performExplain", "desc17");
//        performKeyMap.put("updateTime", "desc18");
//        performKeyMap.put("createTime", "desc19");

        performKeyMap.put("ourPartyName", "desc2");
        performKeyMap.put("createPsnName", "desc20" );
        performKeyMap.put("createDeptName", "desc21");
        performKeyMap.put("createOgnName", "desc22");
        performKeyMap.put("ourPartyCode", "desc23");
        performKeyMap.put("otherPartyCode", "desc24");
        performKeyMap.put("createPsnId", "desc25");
        performKeyMap.put("createDeptId", "desc26");
        performKeyMap.put("createOgnId", "desc27");
        performKeyMap.put("otherPartyName", "desc3");
        performKeyMap.put("performText", "desc4");
        performKeyMap.put("contractTerms", "desc5");
//        performKeyMap.put("revenueExpenditureCode", "desc6");
//        performKeyMap.put("settlementMethodCode", "desc7");
//        performKeyMap.put("planDate", "desc8");
        performKeyMap.put("planAmount", "desc9");

    }
    private static void loadResultKeyMap(){
        resultKeyMap.put("performCode", "code");
        resultKeyMap.put("contractCode", "desc1");
        resultKeyMap.put("planRatio", "desc10");
        resultKeyMap.put("projectCode", "desc11");
        resultKeyMap.put("currencyCode", "desc12");
        resultKeyMap.put("exchangeRateMethodCode", "desc13");
        resultKeyMap.put("exchangeRate", "desc14");
        resultKeyMap.put("planAmountRmb", "desc15");
        resultKeyMap.put("planInvoiceDate", "desc16");
        resultKeyMap.put("actualAmount", "desc17");
        resultKeyMap.put("fundDocumentCode", "desc19");
        resultKeyMap.put("ourPartyCode", "desc2");
        resultKeyMap.put("performExplain", "desc20");
        resultKeyMap.put("createPsnId", "desc23");
        resultKeyMap.put("createDeptId", "desc24");
        resultKeyMap.put("createOgnId", "desc25");
        resultKeyMap.put("ourPartyName", "desc26");
        resultKeyMap.put("otherPartyName", "desc27");
        resultKeyMap.put("createPsnName", "desc28");
        resultKeyMap.put("createDeptName", "desc29");
        resultKeyMap.put("otherPartyCode", "desc3");
        resultKeyMap.put("createOgnName", "desc30");
        resultKeyMap.put("performText", "desc4");
        resultKeyMap.put("contractTerms", "desc5");
        resultKeyMap.put("revenueExpenditureCode", "desc6");
        resultKeyMap.put("settlementMethodCode", "desc7");
//        resultKeyMap.put("planDate", "desc8");
        resultKeyMap.put("planAmount", "desc9");

    }


    @Override
    public String sendToMainMethod(JSONObject jsonObject) {
       /* HttpResponse<String> response = Unirest.post("http://10.202.19.83:8080/bgjt/mdm/get/getContractDetails")
                .header("OnlineToken", "rPlSk46zpCBaH5vPK68hq5xN0XBhVODnEJzMmPBR3Wa2VR2xL1kAOpP7bW5LRIfaYenoRte2ye4=")
                .header("User-Agent", "Apifox/1.0.0 (https://apifox.com)")
                .header("Content-Type", "application/json")
                .body(jsonObject.toJSONString())
                .asString();

        return response.getBody();*/
        String returnObject;
        returnObject = OkHttpUtils.builder().url(esbUrl + "/bgjt/mdm/get/getContractDetails")
                .addHeader("Content-Type", "application/json; charset=utf-8")
                .addHeader("OnlineToken", "rPlSk46zpCBaH5vPK68hq5xN0XBhVODnEJzMmPBR3Wa2VR2xL1kAOpP7bW5LRIfaYenoRte2ye4=")
                // 如果是true的话，会类似于postman中post提交方式的raw，用json的方式提交，不是表单
                // 如果是false的话传统的表单提交
                .post(true, jsonObject.toJSONString())
                .sync();
        return returnObject;
    }

    @Override
    public void sendToMainContractDetail(String contractId) {
        try {
            BmContract bmContract = bmContractService.getById(contractId);
            JSONObject jsonObject = this.formatteContract(bmContract);
            String responseBody = this.sendToMainMethod(jsonObject);
            this.saveSendContractLog(bmContract,jsonObject.toJSONString(),responseBody);
        }catch (Exception e){
            log.error("发送合同详情失败：{}",e);
        }

    }

    /**
     * 记录推送合同明细日志
     * @param bmContract
     * @param requestBody
     * @param responseBody
     */
    @Override
    public void saveSendContractLog(BmContract bmContract,String requestBody,String responseBody){
        BmContractLog bmContractLog = new BmContractLog();
        bmContractLog.setBusinessId(bmContract.getId());
        bmContractLog.setContractCode(bmContract.getContractCode());
        bmContractLog.setRequestBody(requestBody);
        bmContractLog.setRequestTime(new Date());
        bmContractLog.setResponseBody(responseBody);
        bmContractLog.setResponseTime(new Date());
        bmContractLogService.save(bmContractLog);
    }
}


