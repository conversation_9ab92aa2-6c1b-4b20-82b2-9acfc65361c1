<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.klaw.dao.contractDao.SgContractApprovalMapper">
    <resultMap id="BaseResultMap" type="com.klaw.entity.contractBean.SgContractApproval">
        <id property="id" column="ID" jdbcType="VARCHAR"/>
        <result property="takeEffectName" column="TAKE_EFFECT_NAME" jdbcType="VARCHAR"/>
        <result property="takeEffectCode" column="TAKE_EFFECT_CODE" jdbcType="DECIMAL"/>
        <result property="processState" column="PROCESS_STATE" jdbcType="VARCHAR"/>
        <result property="processStateCode" column="PROCESS_STATE_CODE" jdbcType="DECIMAL"/>
        <result property="changeState" column="CHANGE_STATE" jdbcType="VARCHAR"/>
        <result property="changeStateCode" column="CHANGE_STATE_CODE" jdbcType="DECIMAL"/>
        <result property="dataTypeName" column="DATA_TYPE_NAME" jdbcType="VARCHAR"/>
        <result property="dataTypeCode" column="DATA_TYPE_CODE" jdbcType="DECIMAL"/>
        <result property="approvalCode" column="APPROVAL_CODE" jdbcType="VARCHAR"/>
        <result property="projectDecision" column="PROJECT_DECISION" jdbcType="VARCHAR"/>
        <result property="projectDecisionId" column="PROJECT_DECISION_ID" jdbcType="VARCHAR"/>
        <result property="relatedOaDocuments" column="RELATED_OA_DOCUMENTS" jdbcType="VARCHAR"/>
        <result property="relatedOaDocumentsId" column="RELATED_OA_DOCUMENTS_ID" jdbcType="VARCHAR"/>
        <result property="oppositePartyWay" column="OPPOSITE_PARTY_WAY" jdbcType="VARCHAR"/>
        <result property="oppositePartyWayId" column="OPPOSITE_PARTY_WAY_ID" jdbcType="VARCHAR"/>
        <result property="contractName" column="CONTRACT_NAME" jdbcType="VARCHAR"/>
        <result property="contractCode" column="CONTRACT_CODE" jdbcType="VARCHAR"/>
        <result property="oneTypeName" column="ONE_TYPE_NAME" jdbcType="VARCHAR"/>
        <result property="oneTypeCode" column="ONE_TYPE_CODE" jdbcType="VARCHAR"/>
        <result property="twoTypeName" column="TWO_TYPE_NAME" jdbcType="VARCHAR"/>
        <result property="twoTypeCode" column="TWO_TYPE_CODE" jdbcType="VARCHAR"/>
        <result property="settlementMethod" column="SETTLEMENT_METHOD" jdbcType="VARCHAR"/>
        <result property="settlementMethodId" column="SETTLEMENT_METHOD_ID" jdbcType="VARCHAR"/>
        <result property="revenueExpenditure" column="REVENUE_EXPENDITURE" jdbcType="VARCHAR"/>
        <result property="revenueExpenditureId" column="REVENUE_EXPENDITURE_ID" jdbcType="VARCHAR"/>
        <result property="moneyType" column="MONEY_TYPE" jdbcType="VARCHAR"/>
        <result property="moneyTypeId" column="MONEY_TYPE_ID" jdbcType="VARCHAR"/>
        <result property="includingTax" column="INCLUDING_TAX" jdbcType="DECIMAL"/>
        <result property="noIncludingTax" column="NO_INCLUDING_TAX" jdbcType="DECIMAL"/>
        <result property="exchangeRate" column="EXCHANGE_RATE" jdbcType="DECIMAL"/>
        <result property="includingTaxRmb" column="INCLUDING_TAX_RMB" jdbcType="DECIMAL"/>
        <result property="contractDate" column="CONTRACT_DATE" jdbcType="TIMESTAMP"/>
        <result property="estimatedTotal" column="ESTIMATED_TOTAL" jdbcType="VARCHAR"/>
        <result property="whetherMajorOgn" column="WHETHER_MAJOR_OGN" jdbcType="DECIMAL"/>
        <result property="whetherMajorDept" column="WHETHER_MAJOR_DEPT" jdbcType="DECIMAL"/>
        <result property="disputeResolution" column="DISPUTE_RESOLUTION" jdbcType="VARCHAR"/>
        <result property="disputeResolutionId" column="DISPUTE_RESOLUTION_ID" jdbcType="VARCHAR"/>
        <result property="createOgnId" column="CREATE_OGN_ID" jdbcType="VARCHAR"/>
        <result property="createOgnName" column="CREATE_OGN_NAME" jdbcType="VARCHAR"/>
        <result property="createDeptId" column="CREATE_DEPT_ID" jdbcType="VARCHAR"/>
        <result property="createDeptName" column="CREATE_DEPT_NAME" jdbcType="VARCHAR"/>
        <result property="createGroupId" column="CREATE_GROUP_ID" jdbcType="VARCHAR"/>
        <result property="createGroupName" column="CREATE_GROUP_NAME" jdbcType="VARCHAR"/>
        <result property="createPsnId" column="CREATE_PSN_ID" jdbcType="VARCHAR"/>
        <result property="createPsnName" column="CREATE_PSN_NAME" jdbcType="VARCHAR"/>
        <result property="createOrgId" column="CREATE_ORG_ID" jdbcType="VARCHAR"/>
        <result property="createOrgName" column="CREATE_ORG_NAME" jdbcType="VARCHAR"/>
        <result property="createPsnFullId" column="CREATE_PSN_FULL_ID" jdbcType="VARCHAR"/>
        <result property="createPsnFullName" column="CREATE_PSN_FULL_NAME" jdbcType="VARCHAR"/>
        <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="UPDATE_TIME" jdbcType="TIMESTAMP"/>
        <result property="dataState" column="DATA_STATE" jdbcType="VARCHAR"/>
        <result property="dataStateCode" column="DATA_STATE_CODE" jdbcType="DECIMAL"/>
        <result property="whetherOgn" column="WHETHER_OGN" jdbcType="DECIMAL"/>
        <result property="whetherEvaluated" column="WHETHER_EVALUATED" jdbcType="DECIMAL"/>
        <result property="projectNames" column="PROJECT_NAMES" jdbcType="VARCHAR"/>
        <result property="otherPartys" column="OTHER_PARTYS" jdbcType="VARCHAR"/>
        <result property="ourPartys" column="OUR_PARTYS" jdbcType="VARCHAR"/>
        <result property="fileTypes" column="FILE_TYPES" jdbcType="VARCHAR"/>
        <result property="currency" column="CURRENCY" jdbcType="VARCHAR"/>
        <result property="currencyId" column="CURRENCY_ID" jdbcType="VARCHAR"/>
        <result property="receiveDate" column="RECEIVE_DATE" jdbcType="TIMESTAMP"/>
        <result property="paymentDate" column="PAYMENT_DATE" jdbcType="TIMESTAMP"/>
        <result property="profitCenter" column="PROFIT_CENTER" jdbcType="VARCHAR"/>
        <result property="costCenter" column="COST_CENTER" jdbcType="VARCHAR"/>
        <result property="projectName" column="PROJECT_NAME" jdbcType="VARCHAR"/>
        <result property="billingDate" column="BILLING_DATE" jdbcType="TIMESTAMP"/>
        <result property="contractNameOld" column="CONTRACT_NAME_OLD" jdbcType="VARCHAR"/>
        <result property="contractCodeOld" column="CONTRACT_CODE_OLD" jdbcType="VARCHAR"/>
        <result property="contractIdOld" column="CONTRACT_ID_OLD" jdbcType="VARCHAR"/>
        <result property="moneyTypeOld" column="MONEY_TYPE_OLD" jdbcType="VARCHAR"/>
        <result property="moneyTypeIdOld" column="MONEY_TYPE_ID_OLD" jdbcType="VARCHAR"/>
        <result property="revenueExpenditureOld" column="REVENUE_EXPENDITURE_OLD" jdbcType="VARCHAR"/>
        <result property="revenueExpenditureIdOld" column="REVENUE_EXPENDITURE_ID_OLD" jdbcType="VARCHAR"/>
        <result property="includingTaxOld" column="INCLUDING_TAX_OLD" jdbcType="DECIMAL"/>
        <result property="currencyOld" column="CURRENCY_OLD" jdbcType="VARCHAR"/>
        <result property="currencyIdOld" column="CURRENCY_ID_OLD" jdbcType="VARCHAR"/>
        <result property="exchangeRateOld" column="EXCHANGE_RATE_OLD" jdbcType="DECIMAL"/>
        <result property="includingTaxRmbOld" column="INCLUDING_TAX_RMB_OLD" jdbcType="DECIMAL"/>
        <result property="noIncludingTaxOld" column="NO_INCLUDING_TAX_OLD" jdbcType="DECIMAL"/>
        <result property="estimatedTotalOld" column="ESTIMATED_TOTAL_OLD" jdbcType="DECIMAL"/>
        <result property="changeMoney" column="CHANGE_MONEY" jdbcType="DECIMAL"/>
        <result property="parentId" column="PARENT_ID" jdbcType="VARCHAR"/>
        <result property="mergeState" column="MERGE_STATE" jdbcType="VARCHAR"/>
        <result property="mergeStateCode" column="MERGE_STATE_CODE" jdbcType="DECIMAL"/>
        <result property="closeId" column="CLOSE_ID" jdbcType="VARCHAR"/>
        <result property="effectId" column="EFFECT_ID" jdbcType="VARCHAR"/>
        <result property="dataSource" column="DATA_SOURCE" jdbcType="VARCHAR"/>
        <result property="dataSourceCode" column="DATA_SOURCE_CODE" jdbcType="DECIMAL"/>
        <result property="standardAttachmentVersion" column="STANDARD_ATTACHMENT_VERSION" jdbcType="DECIMAL"/>
        <result property="authorizationSource" column="AUTHORIZATION_SOURCE" jdbcType="VARCHAR"/>
        <result property="authorizationId" column="AUTHORIZATION_ID" jdbcType="VARCHAR"/>
        <result property="effectTime" column="EFFECT_TIME" jdbcType="TIMESTAMP"/>
        <result property="performState" column="PERFORM_STATE" jdbcType="VARCHAR"/>
        <result property="alreadyPerformMoney" column="ALREADY_PERFORM_MONEY" jdbcType="DECIMAL"/>
        <result property="notPerformMoney" column="NOT_PERFORM_MONEY" jdbcType="DECIMAL"/>
        <result property="performProgress" column="PERFORM_PROGRESS" jdbcType="VARCHAR"/>
        <result property="nextPerformMoney" column="NEXT_PERFORM_MONEY" jdbcType="DECIMAL"/>
        <result property="nextPerformTime" column="NEXT_PERFORM_TIME" jdbcType="TIMESTAMP"/>
        <result property="symbol" column="SYMBOL" jdbcType="VARCHAR"/>
        <result property="tradeTerms" column="TRADE_TERMS" jdbcType="VARCHAR"/>
        <result property="tradeTermsCode" column="TRADE_TERMS_CODE" jdbcType="VARCHAR"/>
        <result property="placeDelivery" column="PLACE_DELIVERY" jdbcType="VARCHAR"/>
        <result property="countryOrigin" column="COUNTRY_ORIGIN" jdbcType="VARCHAR"/>
        <result property="tradeCountry" column="TRADE_COUNTRY" jdbcType="VARCHAR"/>
        <result property="tradeCountryCode" column="TRADE_COUNTRY_CODE" jdbcType="VARCHAR"/>
        <result property="positionInfo" column="POSITION_INFO" jdbcType="VARCHAR"/>
        <result property="warrant" column="WARRANT" jdbcType="VARCHAR"/>
        <result property="area" column="AREA" jdbcType="VARCHAR"/>
        <result property="areaCode" column="AREA_CODE" jdbcType="VARCHAR"/>
        <result property="nature" column="NATURE" jdbcType="VARCHAR"/>
        <result property="natureCode" column="NATURE_CODE" jdbcType="VARCHAR"/>
        <result property="transactionPrice" column="TRANSACTION_PRICE" jdbcType="VARCHAR"/>
        <result property="loanInformation" column="LOAN_INFORMATION" jdbcType="VARCHAR"/>
        <result property="original" column="ORIGINAL" jdbcType="VARCHAR"/>
        <result property="netWorth" column="NET_WORTH" jdbcType="VARCHAR"/>
        <result property="evaluation" column="EVALUATION" jdbcType="VARCHAR"/>
        <result property="trading" column="TRADING" jdbcType="DECIMAL"/>
        <result property="quantity" column="QUANTITY" jdbcType="VARCHAR"/>
        <result property="supplyAddress" column="SUPPLY_ADDRESS" jdbcType="VARCHAR"/>
        <result property="quality" column="QUALITY" jdbcType="VARCHAR"/>
        <result property="measurement" column="MEASUREMENT" jdbcType="VARCHAR"/>
        <result property="settlement" column="SETTLEMENT" jdbcType="VARCHAR"/>
        <result property="contractPeriod" column="CONTRACT_PERIOD" jdbcType="TIMESTAMP"/>
        <result property="propertyPoint" column="PROPERTY_POINT" jdbcType="VARCHAR"/>
        <result property="loanType" column="LOAN_TYPE" jdbcType="VARCHAR"/>
        <result property="loanUsage" column="LOAN_USAGE" jdbcType="VARCHAR"/>
        <result property="loanTerm" column="LOAN_TERM" jdbcType="TIMESTAMP"/>
        <result property="ourPosition" column="OUR_POSITION" jdbcType="VARCHAR"/>
        <result property="ourPositionCode" column="OUR_POSITION_CODE" jdbcType="VARCHAR"/>
        <result property="ringFrameMode" column="RING_FRAME_MODE" jdbcType="VARCHAR"/>
        <result property="guaranteeExternal" column="GUARANTEE_EXTERNAL" jdbcType="VARCHAR"/>
        <result property="guaranteeExternalCode" column="GUARANTEE_EXTERNAL_CODE" jdbcType="VARCHAR"/>
        <result property="creditor" column="CREDITOR" jdbcType="VARCHAR"/>
        <result property="guaranteeAmount" column="GUARANTEE_AMOUNT" jdbcType="DECIMAL"/>
        <result property="currType" column="CURR_TYPE" jdbcType="VARCHAR"/>
        <result property="currTypeCode" column="CURR_TYPE_CODE" jdbcType="VARCHAR"/>
        <result property="guaranteeMethod" column="GUARANTEE_METHOD" jdbcType="VARCHAR"/>
        <result property="guaranteeMethodCode" column="GUARANTEE_METHOD_CODE" jdbcType="VARCHAR"/>
        <result property="guaranteeScope" column="GUARANTEE_SCOPE" jdbcType="VARCHAR"/>
        <result property="guaranteeScopeCode" column="GUARANTEE_SCOPE_CODE" jdbcType="VARCHAR"/>
        <result property="warrantyPeriod" column="WARRANTY_PERIOD" jdbcType="VARCHAR"/>
        <result property="leaseTerm" column="LEASE_TERM" jdbcType="VARCHAR"/>
        <result property="maintenance" column="MAINTENANCE" jdbcType="VARCHAR"/>
        <result property="rentPaymentMethod" column="RENT_PAYMENT_METHOD" jdbcType="VARCHAR"/>
        <result property="rentPaymentMethodCode" column="RENT_PAYMENT_METHOD_CODE" jdbcType="VARCHAR"/>
        <result property="allowSublease" column="ALLOW_SUBLEASE" jdbcType="VARCHAR"/>
        <result property="allowSubleaseCode" column="ALLOW_SUBLEASE_CODE" jdbcType="VARCHAR"/>
        <result property="lessorLessee" column="LESSOR_LESSEE" jdbcType="VARCHAR"/>
        <result property="lessorLesseeCode" column="LESSOR_LESSEE_CODE" jdbcType="VARCHAR"/>
        <result property="shipInformation" column="SHIP_INFORMATION" jdbcType="VARCHAR"/>
        <result property="maintenanceCode" column="MAINTENANCE_CODE" jdbcType="VARCHAR"/>
        <result property="disposalMethod" column="DISPOSAL_METHOD" jdbcType="VARCHAR"/>
        <result property="bankAccountInformation" column="BANK_ACCOUNT_INFORMATION" jdbcType="VARCHAR"/>
        <result property="shippingType" column="SHIPPING_TYPE" jdbcType="VARCHAR"/>
        <result property="shippingTypeCode" column="SHIPPING_TYPE_CODE" jdbcType="VARCHAR"/>
        <result property="relevantDocuments" column="RELEVANT_DOCUMENTS" jdbcType="VARCHAR"/>
        <result property="isRequired" column="IS_REQUIRED" jdbcType="DECIMAL"/>
        <result property="patentApplicationNo" column="PATENT_APPLICATION_NO" jdbcType="VARCHAR"/>
        <result property="patentProtectionPeriod" column="PATENT_PROTECTION_PERIOD" jdbcType="TIMESTAMP"/>
        <result property="businessClassification" column="BUSINESS_CLASSIFICATION" jdbcType="VARCHAR"/>
        <result property="businessClassificationCode" column="BUSINESS_CLASSIFICATION_CODE" jdbcType="VARCHAR"/>
        <result property="itemsList" column="ITEMS_LIST" jdbcType="VARCHAR"/>
        <result property="deliveryTime" column="DELIVERY_TIME" jdbcType="TIMESTAMP"/>
        <result property="storagePlace" column="STORAGE_PLACE" jdbcType="VARCHAR"/>
        <result property="permanentTransfer" column="PERMANENT_TRANSFER" jdbcType="DECIMAL"/>
        <result property="permanentStartDate" column="PERMANENT_START_DATE" jdbcType="TIMESTAMP"/>
        <result property="permanentEndDate" column="PERMANENT_END_DATE" jdbcType="TIMESTAMP"/>
        <result property="donationScope" column="DONATION_SCOPE" jdbcType="VARCHAR"/>
        <result property="donationScopeCode" column="DONATION_SCOPE_CODE" jdbcType="VARCHAR"/>
        <result property="sponsorshipDonation" column="SPONSORSHIP_DONATION" jdbcType="VARCHAR"/>
        <result property="sponsorshipDonationCode" column="SPONSORSHIP_DONATION_CODE" jdbcType="VARCHAR"/>
        <result property="contractCommencementDate" column="CONTRACT_COMMENCEMENT_DATE" jdbcType="TIMESTAMP"/>
        <result property="contractPeriodType" column="CONTRACT_PERIOD_TYPE" jdbcType="VARCHAR"/>
        <result property="contractPeriodTypeCode" column="CONTRACT_PERIOD_TYPE_CODE" jdbcType="VARCHAR"/>
        <result property="principalUnit" column="PRINCIPAL_UNIT" jdbcType="VARCHAR"/>
        <result property="principalUnitCode" column="PRINCIPAL_UNIT_CODE" jdbcType="VARCHAR"/>
        <result property="participatingUnits" column="PARTICIPATING_UNITS" jdbcType="VARCHAR"/>
        <result property="participatingUnitsCode" column="PARTICIPATING_UNITS_CODE" jdbcType="VARCHAR"/>
        <result property="placeCode" column="PLACE_CODE" jdbcType="VARCHAR"/>
        <result property="contractOgnCode" column="CONTRACT_OGN_CODE" jdbcType="VARCHAR"/>
        <result property="threeTypeName" column="THREE_TYPE_NAME" jdbcType="VARCHAR"/>
        <result property="threeTypeCode" column="THREE_TYPE_CODE" jdbcType="VARCHAR"/>
        <result property="leaseTermEnd" column="LEASE_TERM_END" jdbcType="TIMESTAMP"/>
        <result property="loanTermEnd" column="LOAN_TERM_END" jdbcType="TIMESTAMP"/>
        <result property="contractPeriodEnd" column="CONTRACT_PERIOD_END" jdbcType="TIMESTAMP"/>
        <result property="currencyCode" column="CURRENCY_CODE" jdbcType="VARCHAR"/>
        <result property="settlementMethodCode" column="SETTLEMENT_METHOD_CODE" jdbcType="VARCHAR"/>
        <result property="businessType" column="BUSINESS_TYPE" jdbcType="VARCHAR"/>
        <result property="businessTypeCode" column="BUSINESS_TYPE_CODE" jdbcType="VARCHAR"/>
        <result property="assetDocNo" column="ASSET_DOC_NO" jdbcType="VARCHAR"/>
        <result property="assetContractNumber" column="ASSET_CONTRACT_NUMBER" jdbcType="VARCHAR"/>
        <result property="assetDescription" column="ASSET_DESCRIPTION" jdbcType="VARCHAR"/>
        <result property="assetOwner" column="ASSET_OWNER" jdbcType="VARCHAR"/>
        <result property="assetRemarks" column="ASSET_REMARKS" jdbcType="VARCHAR"/>
        <result property="signedAmountTax" column="SIGNED_AMOUNT_TAX" jdbcType="DECIMAL"/>
        <result property="thirdContractNo" column="THIRD_CONTRACT_NO" jdbcType="VARCHAR"/>
        <result property="thirdVersion" column="THIRD_VERSION" jdbcType="VARCHAR"/>
        <result property="isCheckSuccess" column="IS_CHECK_SUCCESS" jdbcType="DECIMAL"/>
        <result property="systemId" column="SYSTEM_ID" jdbcType="VARCHAR"/>
        <result property="messageTypeId" column="MESSAGE_TYPE_ID" jdbcType="VARCHAR"/>
        <result property="svcName" column="SVC_NAME" jdbcType="VARCHAR"/>
        <result property="pushSystem" column="PUSH_SYSTEM" jdbcType="VARCHAR"/>
        <result property="pushSystemCode" column="PUSH_SYSTEM_CODE" jdbcType="VARCHAR"/>
        <result property="dataSourceOtherCode" column="DATA_SOURCE_OTHER_CODE" jdbcType="VARCHAR"/>
        <result property="isContractOgnCode" column="IS_CONTRACT_OGN_CODE" jdbcType="DECIMAL"/>
        <result property="createLegalUnitId" column="CREATE_LEGAL_UNIT_ID" jdbcType="VARCHAR"/>
        <result property="createLegalUnitName" column="CREATE_LEGAL_UNIT_NAME" jdbcType="VARCHAR"/>
        <result property="whetherPerform" column="WHETHER_PERFORM" jdbcType="VARCHAR"/>
        <result property="fileXml" column="FILE_XML" jdbcType="VARCHAR"/>
        <result property="taxable" column="TAXABLE" jdbcType="VARCHAR"/>
        <result property="taxableCode" column="TAXABLE_CODE" jdbcType="VARCHAR"/>
        <result property="lawFirmSelectName" column="LAW_FIRM_SELECT_NAME" jdbcType="VARCHAR"/>
        <result property="lawFirmSelectId" column="LAW_FIRM_SELECT_ID" jdbcType="VARCHAR"/>
        <result property="lawFirmWinNames" column="LAW_FIRM_WIN_NAMES" jdbcType="VARCHAR"/>
        <result property="lawFirmWinIds" column="LAW_FIRM_WIN_IDS" jdbcType="VARCHAR"/>
        <result property="isFle" column="IS_FLE" jdbcType="DECIMAL"/>
        <result property="isWithinOgn" column="IS_WITHIN_OGN" jdbcType="DECIMAL"/>
        <result property="closeState" column="CLOSE_STATE" jdbcType="VARCHAR"/>
        <result property="closeStateCode" column="CLOSE_STATE_CODE" jdbcType="DECIMAL"/>
        <result property="interestRate" column="INTEREST_RATE" jdbcType="VARCHAR"/>
        <result property="whetherCounterGuarantee" column="WHETHER_COUNTER_GUARANTEE" jdbcType="VARCHAR"/>
        <result property="mortgageTime" column="MORTGAGE_TIME" jdbcType="TIMESTAMP"/>
        <result property="pledgeTime" column="PLEDGE_TIME" jdbcType="TIMESTAMP"/>
        <result property="createTransferId" column="CREATE_TRANSFER_ID" jdbcType="VARCHAR"/>
        <result property="createTransferName" column="CREATE_TRANSFER_NAME" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="contract_Column_List">
        ID,
	TAKE_EFFECT_NAME,
	TAKE_EFFECT_CODE,
	PROCESS_STATE,
	PROCESS_STATE_CODE,
	CHANGE_STATE,
	CHANGE_STATE_CODE,
	DATA_TYPE_NAME,
	DATA_TYPE_CODE,
	APPROVAL_CODE,
	PROJECT_DECISION,
	PROJECT_DECISION_ID,
	RELATED_OA_DOCUMENTS,
	RELATED_OA_DOCUMENTS_ID,
	MEETING_ATTACHMENT,
	OTHER_ATTACHMENT,
	OPPOSITE_PARTY_WAY,
	OPPOSITE_PARTY_WAY_ID,
	WAY_ATTACHMENT,
	CONTRACT_NAME,
	CONTRACT_CODE,
	ONE_TYPE_NAME,
	ONE_TYPE_CODE,
	TWO_TYPE_NAME,
	TWO_TYPE_CODE,
	SETTLEMENT_METHOD,
	SETTLEMENT_METHOD_ID,
	REVENUE_EXPENDITURE,
	REVENUE_EXPENDITURE_ID,
	MONEY_TYPE,
	MONEY_TYPE_ID,
	INCLUDING_TAX,
	NO_INCLUDING_TAX,
	EXCHANGE_RATE,
	INCLUDING_TAX_RMB,
	CONTRACT_DATE,
	ESTIMATED_TOTAL,
	CONTRACT_SUBJECT,
	NOTICE_DEPT,
	NOTICE_DEPT_ID,
	WHETHER_MAJOR_OGN,
	WHETHER_MAJOR_DEPT,
	DISPUTE_RESOLUTION,
	DISPUTE_RESOLUTION_ID,
	SUMMARY_NOTE,
	CREATE_OGN_ID,
	CREATE_OGN_NAME,
	CREATE_DEPT_ID,
	CREATE_DEPT_NAME,
	CREATE_GROUP_ID,
	CREATE_GROUP_NAME,
	CREATE_PSN_ID,
	CREATE_PSN_NAME,
	CREATE_ORG_ID,
	CREATE_ORG_NAME,
	CREATE_PSN_FULL_ID,
	CREATE_PSN_FULL_NAME,
	CREATE_TIME,
	UPDATE_TIME,
	DATA_STATE,
	DATA_STATE_CODE,
	WHETHER_OGN,
	WHETHER_EVALUATED,
	PROJECT_NAMES,
	OTHER_PARTYS,
	OUR_PARTYS,
	FILE_TYPES,
	CURRENCY,
	CURRENCY_ID,
	RECEIVE_DATE,
	PAYMENT_DATE,
	PROFIT_CENTER,
	COST_CENTER,
	PROJECT_NAME,
	BILLING_DATE,
	CONTRACT_NAME_OLD,
	CONTRACT_CODE_OLD,
	CONTRACT_ID_OLD,
	CHANGE_DESCRIPTION,
	MONEY_TYPE_OLD,
	MONEY_TYPE_ID_OLD,
	REVENUE_EXPENDITURE_OLD,
	REVENUE_EXPENDITURE_ID_OLD,
	INCLUDING_TAX_OLD,
	CURRENCY_OLD,
	CURRENCY_ID_OLD,
	EXCHANGE_RATE_OLD,
	INCLUDING_TAX_RMB_OLD,
	NO_INCLUDING_TAX_OLD,
	ESTIMATED_TOTAL_OLD,
	CHANGE_MONEY,
	CONTRACT_ATTACHMENT,
	PARENT_ID,
	MERGE_STATE,
	MERGE_STATE_CODE,
	CLOSE_ID,
	EFFECT_ID,
	DATA_SOURCE,
	DATA_SOURCE_CODE,
	STANDARD_ATTACHMENT_VERSION,
	AUTHORIZATION_SOURCE,
	AUTHORIZATION_ID,
	EFFECT_TIME,
	PERFORM_STATE,
	ALREADY_PERFORM_MONEY,
	NOT_PERFORM_MONEY,
	PERFORM_PROGRESS,
	NEXT_PERFORM_MONEY,
	NEXT_PERFORM_TIME,
	SYMBOL,
	TRADE_TERMS,
	TRADE_TERMS_CODE,
	PLACE_DELIVERY,
	COUNTRY_ORIGIN,
	TRADE_COUNTRY,
	TRADE_COUNTRY_CODE,
	POSITION_INFO,
	WARRANT,
	AREA,
	AREA_CODE,
	NATURE,
	NATURE_CODE,
	TRANSACTION_PRICE,
	LOAN_INFORMATION,
	ORIGINAL,
	NET_WORTH,
	EVALUATION,
	TRADING,
	QUANTITY,
	SUPPLY_ADDRESS,
	QUALITY,
	MEASUREMENT,
	SETTLEMENT,
	CONTRACT_PERIOD,
	PROPERTY_POINT,
	LOAN_TYPE,
	LOAN_USAGE,
	LOAN_TERM,
	OUR_POSITION,
	OUR_POSITION_CODE,
	RING_FRAME_MODE,
	GUARANTEE_EXTERNAL,
	GUARANTEE_EXTERNAL_CODE,
	CREDITOR,
	GUARANTEE_AMOUNT,
	CURR_TYPE,
	CURR_TYPE_CODE,
	GUARANTEE_METHOD,
	GUARANTEE_METHOD_CODE,
	GUARANTEE_SCOPE,
	GUARANTEE_SCOPE_CODE,
	WARRANTY_PERIOD,
	LEASE_TERM,
	MAINTENANCE,
	RENT_PAYMENT_METHOD,
	RENT_PAYMENT_METHOD_CODE,
	ALLOW_SUBLEASE,
	ALLOW_SUBLEASE_CODE,
	LESSOR_LESSEE,
	LESSOR_LESSEE_CODE,
	SHIP_INFORMATION,
	MAINTENANCE_CODE,
	DISPOSAL_METHOD,
	BANK_ACCOUNT_INFORMATION,
	SHIPPING_TYPE,
	SHIPPING_TYPE_CODE,
	RELEVANT_DOCUMENTS,
	IS_REQUIRED,
	PATENT_APPLICATION_NO,
	PATENT_PROTECTION_PERIOD,
	BUSINESS_CLASSIFICATION,
	BUSINESS_CLASSIFICATION_CODE,
	ITEMS_LIST,
	DELIVERY_TIME,
	STORAGE_PLACE,
	PERMANENT_TRANSFER,
	PERMANENT_START_DATE,
	PERMANENT_END_DATE,
	DONATION_SCOPE,
	DONATION_SCOPE_CODE,
	SPONSORSHIP_DONATION,
	SPONSORSHIP_DONATION_CODE,
	CONTRACT_COMMENCEMENT_DATE,
	CONTRACT_PERIOD_TYPE,
	CONTRACT_PERIOD_TYPE_CODE,
	PRINCIPAL_UNIT,
	PRINCIPAL_UNIT_CODE,
	PARTICIPATING_UNITS,
	PARTICIPATING_UNITS_CODE,
	PLACE_CODE,
	CONTRACT_OGN_CODE,
	THREE_TYPE_NAME,
	THREE_TYPE_CODE,
	LEASE_TERM_END,
	LOAN_TERM_END,
	CONTRACT_PERIOD_END,
	CURRENCY_CODE,
	SETTLEMENT_METHOD_CODE,
	BUSINESS_TYPE,
	BUSINESS_TYPE_CODE,
	ASSET_DOC_NO,
	ASSET_CONTRACT_NUMBER,
	ASSET_DESCRIPTION,
	ASSET_OWNER,
	ASSET_REMARKS,
	SIGNED_AMOUNT_TAX,
	THIRD_CONTRACT_NO,
	THIRD_VERSION,
	IS_CHECK_SUCCESS,
	SYSTEM_ID,
	MESSAGE_TYPE_ID,
	SVC_NAME,
	PUSH_SYSTEM,
	PUSH_SYSTEM_CODE,
	DATA_SOURCE_OTHER_CODE,
	FILE_PLACE,
	IS_CONTRACT_OGN_CODE,
	EVENT_REPORT,
	CREATE_LEGAL_UNIT_ID,
	CREATE_LEGAL_UNIT_NAME,
	WHETHER_PERFORM,
	FILE_XML,
	TAXABLE,
	TAXABLE_CODE,
	LAW_FIRM_SELECT_NAME,
	LAW_FIRM_SELECT_ID,
	LAW_FIRM_WIN_NAMES,
	LAW_FIRM_WIN_IDS,
	IS_FLE,
	IS_WITHIN_OGN,
	FROUP_CONTRACT_CODE,
	CLOSE_STATE,
	CLOSE_STATE_CODE,
	INTEREST_RATE,
	WHETHER_COUNTER_GUARANTEE,
	MORTGAGE_TIME,
	PLEDGE_TIME,
	CREATE_TRANSFER_ID,
	CREATE_TRANSFER_NAME,
	FILE_APPROVAL,
	PLAN_ARCHIVE_TIME
    </sql>

    <sql id="Base_Column_List">
        ID,TAKE_EFFECT_NAME,TAKE_EFFECT_CODE,
        PROCESS_STATE,PROCESS_STATE_CODE,CHANGE_STATE,
        CHANGE_STATE_CODE,DATA_TYPE_NAME,DATA_TYPE_CODE,
        APPROVAL_CODE,PROJECT_DECISION,PROJECT_DECISION_ID,
        RELATED_OA_DOCUMENTS,RELATED_OA_DOCUMENTS_ID,OPPOSITE_PARTY_WAY,
        OPPOSITE_PARTY_WAY_ID,CONTRACT_NAME,CONTRACT_CODE,
        ONE_TYPE_NAME,ONE_TYPE_CODE,TWO_TYPE_NAME,
        TWO_TYPE_CODE,SETTLEMENT_METHOD,SETTLEMENT_METHOD_ID,
        REVENUE_EXPENDITURE,REVENUE_EXPENDITURE_ID,MONEY_TYPE,
        MONEY_TYPE_ID,INCLUDING_TAX,NO_INCLUDING_TAX,
        EXCHANGE_RATE,INCLUDING_TAX_RMB,CONTRACT_DATE,
        ESTIMATED_TOTAL,WHETHER_MAJOR_OGN,WHETHER_MAJOR_DEPT,
        DISPUTE_RESOLUTION,DISPUTE_RESOLUTION_ID,CREATE_OGN_ID,
        CREATE_OGN_NAME,CREATE_DEPT_ID,CREATE_DEPT_NAME,
        CREATE_GROUP_ID,CREATE_GROUP_NAME,CREATE_PSN_ID,
        CREATE_PSN_NAME,CREATE_ORG_ID,CREATE_ORG_NAME,
        CREATE_PSN_FULL_ID,CREATE_PSN_FULL_NAME,CREATE_TIME,
        UPDATE_TIME,DATA_STATE,DATA_STATE_CODE,
        WHETHER_OGN,WHETHER_EVALUATED,PROJECT_NAMES,
        OTHER_PARTYS,OUR_PARTYS,FILE_TYPES,
        CURRENCY,CURRENCY_ID,RECEIVE_DATE,
        PAYMENT_DATE,PROFIT_CENTER,COST_CENTER,
        PROJECT_NAME,BILLING_DATE,CONTRACT_NAME_OLD,
        CONTRACT_CODE_OLD,CONTRACT_ID_OLD,MONEY_TYPE_OLD,
        MONEY_TYPE_ID_OLD,REVENUE_EXPENDITURE_OLD,REVENUE_EXPENDITURE_ID_OLD,
        INCLUDING_TAX_OLD,CURRENCY_OLD,CURRENCY_ID_OLD,
        EXCHANGE_RATE_OLD,INCLUDING_TAX_RMB_OLD,NO_INCLUDING_TAX_OLD,
        ESTIMATED_TOTAL_OLD,CHANGE_MONEY,PARENT_ID,
        MERGE_STATE,MERGE_STATE_CODE,CLOSE_ID,
        EFFECT_ID,DATA_SOURCE,DATA_SOURCE_CODE,
        STANDARD_ATTACHMENT_VERSION,AUTHORIZATION_SOURCE,AUTHORIZATION_ID,
        EFFECT_TIME,PERFORM_STATE,ALREADY_PERFORM_MONEY,
        NOT_PERFORM_MONEY,PERFORM_PROGRESS,NEXT_PERFORM_MONEY,
        NEXT_PERFORM_TIME,SYMBOL,TRADE_TERMS,
        TRADE_TERMS_CODE,PLACE_DELIVERY,COUNTRY_ORIGIN,
        TRADE_COUNTRY,TRADE_COUNTRY_CODE,POSITION_INFO,
        WARRANT,AREA,AREA_CODE,
        NATURE,NATURE_CODE,TRANSACTION_PRICE,
        LOAN_INFORMATION,ORIGINAL,NET_WORTH,
        EVALUATION,TRADING,QUANTITY,
        SUPPLY_ADDRESS,QUALITY,MEASUREMENT,
        SETTLEMENT,CONTRACT_PERIOD,PROPERTY_POINT,
        LOAN_TYPE,LOAN_USAGE,LOAN_TERM,
        OUR_POSITION,OUR_POSITION_CODE,RING_FRAME_MODE,
        GUARANTEE_EXTERNAL,GUARANTEE_EXTERNAL_CODE,CREDITOR,
        GUARANTEE_AMOUNT,CURR_TYPE,CURR_TYPE_CODE,
        GUARANTEE_METHOD,GUARANTEE_METHOD_CODE,GUARANTEE_SCOPE,
        GUARANTEE_SCOPE_CODE,WARRANTY_PERIOD,LEASE_TERM,
        MAINTENANCE,RENT_PAYMENT_METHOD,RENT_PAYMENT_METHOD_CODE,
        ALLOW_SUBLEASE,ALLOW_SUBLEASE_CODE,LESSOR_LESSEE,
        LESSOR_LESSEE_CODE,SHIP_INFORMATION,MAINTENANCE_CODE,
        DISPOSAL_METHOD,BANK_ACCOUNT_INFORMATION,SHIPPING_TYPE,
        SHIPPING_TYPE_CODE,RELEVANT_DOCUMENTS,IS_REQUIRED,
        PATENT_APPLICATION_NO,PATENT_PROTECTION_PERIOD,BUSINESS_CLASSIFICATION,
        BUSINESS_CLASSIFICATION_CODE,ITEMS_LIST,DELIVERY_TIME,
        STORAGE_PLACE,PERMANENT_TRANSFER,PERMANENT_START_DATE,
        PERMANENT_END_DATE,DONATION_SCOPE,DONATION_SCOPE_CODE,
        SPONSORSHIP_DONATION,SPONSORSHIP_DONATION_CODE,CONTRACT_COMMENCEMENT_DATE,
        CONTRACT_PERIOD_TYPE,CONTRACT_PERIOD_TYPE_CODE,PRINCIPAL_UNIT,
        PRINCIPAL_UNIT_CODE,PARTICIPATING_UNITS,PARTICIPATING_UNITS_CODE,
        PLACE_CODE,CONTRACT_OGN_CODE,THREE_TYPE_NAME,
        THREE_TYPE_CODE,LEASE_TERM_END,LOAN_TERM_END,
        CONTRACT_PERIOD_END,CURRENCY_CODE,SETTLEMENT_METHOD_CODE,
        BUSINESS_TYPE,BUSINESS_TYPE_CODE,ASSET_DOC_NO,
        ASSET_CONTRACT_NUMBER,ASSET_DESCRIPTION,ASSET_OWNER,
        ASSET_REMARKS,SIGNED_AMOUNT_TAX,THIRD_CONTRACT_NO,
        THIRD_VERSION,IS_CHECK_SUCCESS,SYSTEM_ID,
        MESSAGE_TYPE_ID,SVC_NAME,PUSH_SYSTEM,
        PUSH_SYSTEM_CODE,DATA_SOURCE_OTHER_CODE,
        IS_CONTRACT_OGN_CODE,CREATE_LEGAL_UNIT_ID,CREATE_LEGAL_UNIT_NAME,
        WHETHER_PERFORM,FILE_XML,TAXABLE,
        TAXABLE_CODE,LAW_FIRM_SELECT_NAME,LAW_FIRM_SELECT_ID,
        LAW_FIRM_WIN_NAMES,LAW_FIRM_WIN_IDS,IS_FLE,
        IS_WITHIN_OGN,CLOSE_STATE,
        CLOSE_STATE_CODE,INTEREST_RATE,WHETHER_COUNTER_GUARANTEE,
        MORTGAGE_TIME,PLEDGE_TIME,CREATE_TRANSFER_ID,
        CREATE_TRANSFER_NAME,MEETING_ATTACHMENT,OTHER_ATTACHMENT,
        WAY_ATTACHMENT,CONTRACT_SUBJECT,NOTICE_DEPT,
        NOTICE_DEPT_ID,SUMMARY_NOTE,CHANGE_DESCRIPTION,
        CONTRACT_ATTACHMENT,EVENT_REPORT
    </sql>
    <sql id="Base_Column_List1">
                    ID,
            APPROVAL_CODE,
            CONTRACT_CODE,
            CONTRACT_OGN_CODE,
            CONTRACT_NAME,
            TWO_TYPE_NAME,
            INCLUDING_TAX_RMB,
            ESTIMATED_TOTAL,
            DATA_TYPE_NAME,
            DATA_STATE,
            OUR_PARTYS,
            OTHER_PARTYS,
            DATA_SOURCE,
            CREATE_PSN_NAME,
            CREATE_DEPT_NAME,
            CREATE_OGN_NAME,
            CREATE_TIME,
            EFFECT_TIME,
            TAKE_EFFECT_NAME,
            PROCESS_STATE,
            PERFORM_STATE,
            CLOSE_STATE,
            CHANGE_STATE,
            WHETHER_EVALUATED,
            DATA_SOURCE_CODE,
            MERGE_STATE_CODE,
            DATA_TYPE_CODE,
            DATA_STATE_CODE,
            PARENT_ID
    </sql>

    <select id="queryPageData" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from SG_CONTRACT_APPROVAL m
        left join SG_CONTRACT_PROJECT pt on m.id=pt.PARENT_ID
        left join SG_CONTRACT_PARTY py on m.id=py.PARENT_ID
        left join SG_CONTRACT_TEXT tt on m.id=tt.PARENT_ID
        <where>
            ${ew.sqlSegment}
        </where>
    </select>
    <!--  <select id="queryPageList" resultType="java.util.Map">-->

    <select id="queryPageList" resultMap="BaseResultMap">
        select <include refid="Base_Column_List1"/>
        from SG_CONTRACT_APPROVAL
        <where>
            ${ew.sqlSegment}
        </where>
    </select>


    <select id="queryChangeCount" resultType="java.util.Map">
        select count(*) as num
        from SG_CONTRACT_APPROVAL
        where contract_code is not null
        start with id = #{id}
        connect by prior id = contract_id_old
    </select>

    <select id="queryFiledsValue" parameterType="java.util.Map" resultType="java.util.Map">
        select ${selectParams}
        from SG_CONTRACT_APPROVAL
        where id = #{id}
    </select>

    <update id="updatePerformState">
        update SG_CONTRACT_APPROVAL
        set perform_State=#{state},
            update_time  = sysdate
        where id = #{id}
    </update>


    <resultMap id="SendResultMap" type="com.klaw.entity.contractBean.SgContractApproval">
        <result column="DATA_TYPE_NAME" jdbcType="VARCHAR" property="dataTypeName"/>
        <result column="DATA_TYPE_CODE" jdbcType="VARCHAR" property="dataTypeCode"/>
        <result column="DATA_STATE" jdbcType="VARCHAR" property="dataState"/>
        <result column="DATA_STATE_CODE" jdbcType="VARCHAR" property="dataStateCode"/>
        <result column="CHANGE_STATE" jdbcType="VARCHAR" property="changeState"/>
        <result column="CHANGE_STATE_CODE" jdbcType="VARCHAR" property="changeStateCode"/>
        <result column="PERFORM_STATE" jdbcType="VARCHAR" property="performState"/>
        <result column="CREATE_ORG_ID" jdbcType="VARCHAR" property="createOrgId"/>
        <result column="CREATE_ORG_NAME" jdbcType="VARCHAR" property="createOrgName"/>
        <result column="PROJECT_DECISION" jdbcType="VARCHAR" property="projectDecision"/>
        <result column="PROJECT_DECISION_ID" jdbcType="VARCHAR" property="projectDecisionId"/>
        <result column="OPPOSITE_PARTY_WAY" jdbcType="VARCHAR" property="oppositePartyWay"/>
        <result column="OPPOSITE_PARTY_WAY_ID" jdbcType="VARCHAR" property="oppositePartyWayId"/>
        <result column="APPROVAL_CODE" jdbcType="VARCHAR" property="approvalCode"/>
        <result column="CONTRACT_NAME" jdbcType="VARCHAR" property="contractName"/>
        <result column="CONTRACT_CODE" jdbcType="VARCHAR" property="contractCode"/>
        <result column="TWO_TYPE_NAME" jdbcType="VARCHAR" property="twoTypeName"/>
        <result column="TWO_TYPE_CODE" jdbcType="VARCHAR" property="twoTypeCode"/>
        <result column="SETTLEMENT_METHOD" jdbcType="VARCHAR" property="settlementMethod"/>
        <result column="SETTLEMENT_METHOD_ID" jdbcType="VARCHAR" property="settlementMethodId"/>
        <result column="REVENUE_EXPENDITURE" jdbcType="VARCHAR" property="revenueExpenditure"/>
        <result column="REVENUE_EXPENDITURE_ID" jdbcType="VARCHAR" property="revenueExpenditureId"/>
        <result column="MONEY_TYPE" jdbcType="VARCHAR" property="moneyType"/>
        <result column="MONEY_TYPE_ID" jdbcType="VARCHAR" property="moneyTypeId"/>
        <result column="INCLUDING_TAX" jdbcType="VARCHAR" property="includingTax"/>
        <result column="CURRENCY" jdbcType="VARCHAR" property="currency"/>
        <result column="CURRENCY_ID" jdbcType="VARCHAR" property="currencyId"/>
        <result column="NO_INCLUDING_TAX" jdbcType="VARCHAR" property="noIncludingTax"/>
        <result column="EXCHANGE_RATE" jdbcType="VARCHAR" property="exchangeRate"/>
        <result column="INCLUDING_TAX_RMB" jdbcType="VARCHAR" property="includingTaxRmb"/>
        <result column="CONTRACT_DATE" jdbcType="VARCHAR" property="contractDate"/>
        <result column="ESTIMATED_TOTAL" jdbcType="VARCHAR" property="estimatedTotal"/>
        <result column="CONTRACT_SUBJECT" jdbcType="CLOB" property="contractSubject"/>
        <result column="WHETHER_MAJOR_OGN" jdbcType="VARCHAR" property="whetherMajorOgn"/>
        <result column="WHETHER_MAJOR_DEPT" jdbcType="VARCHAR" property="whetherMajorDept"/>
        <result column="SUMMARY_NOTE" jdbcType="CLOB" property="summaryNote"/>
        <result column="RECEIVE_DATE" jdbcType="TIMESTAMP" property="receiveDate"/>
        <result column="PAYMENT_DATE" jdbcType="TIMESTAMP" property="paymentDate"/>
        <result column="BILLING_DATE" jdbcType="TIMESTAMP" property="billingDate"/>
        <result column="PROFIT_CENTER" jdbcType="VARCHAR" property="profitCenter"/>
        <result column="PROJECT_NAME" jdbcType="VARCHAR" property="projectName"/>
        <result column="AUTHORIZATION_ID" jdbcType="VARCHAR" property="authorizationId"/>
        <result column="CONTRACT_NAME_OLD" jdbcType="VARCHAR" property="contractNameOld"/>
        <result column="CONTRACT_CODE_OLD" jdbcType="VARCHAR" property="contractCodeOld"/>
        <result column="CHANGE_DESCRIPTION" jdbcType="VARCHAR" property="changeDescription"/>
        <result column="CHANGE_MONEY" jdbcType="VARCHAR" property="changeMoney"/>
        <result column="symbol" jdbcType="VARCHAR" property="symbol"/>
        <collection property="projectList" javaType="java.util.ArrayList"
                    ofType="com.klaw.vo.contractVo.SgContractProject">
            <result column="PROJECT_TYPE" jdbcType="VARCHAR" property="projectType"/>
            <result column="PROJECT_NAME2" jdbcType="VARCHAR" property="projectName"/>
            <result column="PROJECT_CODE" jdbcType="VARCHAR" property="projectCode"/>
            <result column="CHILD_PROJECT_CODE" jdbcType="VARCHAR" property="childProjectCode"/>
            <result column="WHETHER" jdbcType="VARCHAR" property="whether"/>
            <result column="TOTAL_MONEY" jdbcType="VARCHAR" property="totalMoney"/>
            <result column="SURPLUS_MONEY" jdbcType="VARCHAR" property="surplusMoney"/>
            <result column="ASSIGN_MONEY" jdbcType="VARCHAR" property="assignMoney"/>
        </collection>
        <collection property="partyList" javaType="java.util.ArrayList" ofType="com.klaw.vo.contractVo.SgContractParty">
            <result column="PARTY_NAME" jdbcType="VARCHAR" property="partyName"/>
            <result column="OTHER_NAME" jdbcType="VARCHAR" property="otherName"/>
            <result column="SUBJECT_STATUS_NAME" jdbcType="VARCHAR" property="subjectStatusName"/>
            <result column="SUBJECT_STATUS_ID" jdbcType="VARCHAR" property="subjectStatusId"/>
            <result column="CONTECT_NAME" jdbcType="VARCHAR" property="contectName"/>
            <result column="CONTECT_TEL" jdbcType="VARCHAR" property="contectTel"/>
            <result column="NATURE" jdbcType="VARCHAR" property="nature"/>
        </collection>
        <collection property="taxAddList" javaType="java.util.ArrayList"
                    ofType="com.klaw.vo.contractVo.SgContractTaxAdd">
            <result column="TAXABLE_AMOUNT" jdbcType="VARCHAR" property="taxableAmount"/>
            <result column="BILLING_TYPE" jdbcType="VARCHAR" property="billingType"/>
            <result column="BILLING_TYPE_CODE" jdbcType="VARCHAR" property="billingTypeCode"/>
            <result column="PRODUCT_NAME" jdbcType="VARCHAR" property="productName"/>
            <result column="VAT_RATE" jdbcType="VARCHAR" property="vatRate"/>
            <result column="VAT_RATE_CODE" jdbcType="VARCHAR" property="vatRateCode"/>
            <result column="VAT_AMOUNT" jdbcType="VARCHAR" property="vatAmount"/>
        </collection>
        <collection property="taxStampList" javaType="java.util.ArrayList"
                    ofType="com.klaw.vo.contractVo.SgContractTaxStamp">
            <result column="TAXABLE_AMOUNT2" jdbcType="VARCHAR" property="taxableAmount"/>
            <result column="TAX_ITEM" jdbcType="VARCHAR" property="taxItem"/>
        </collection>
        <collection property="sealList" javaType="java.util.ArrayList" ofType="com.klaw.entity.contractBean.contract.BmContractSeal">
            <result column="SEAL_NAME" jdbcType="VARCHAR" property="sealName"/>
            <result column="SEAL_ID" jdbcType="VARCHAR" property="sealId"/>
            <result column="SEAL_TYPE" jdbcType="VARCHAR" property="sealType"/>
            <result column="SEAL_TYPE_ID" jdbcType="VARCHAR" property="sealTypeId"/>
            <result column="SEAL_ADMIN" jdbcType="VARCHAR" property="sealAdmin"/>
            <result column="SEAL_ADMIN_ID" jdbcType="VARCHAR" property="sealAdminId"/>
            <result column="SEAL_NUMBER" jdbcType="VARCHAR" property="sealNumber"/>
            <result column="PRINTS_NUMBER" jdbcType="VARCHAR" property="printsNumber"/>
        </collection>
    </resultMap>
    <select id="querySendData" parameterType="java.util.Map" resultMap="SendResultMap">
        select a.DATA_TYPE_NAME,
               a.DATA_TYPE_CODE,
               a.DATA_STATE,
               a.DATA_STATE_CODE,
               a.PERFORM_STATE,
               a.CHANGE_STATE,
               a.CHANGE_STATE_CODE,
               a.CREATE_ORG_ID,
               a.CREATE_OGN_NAME,
               a.PROJECT_DECISION,
               a.PROJECT_DECISION_ID,
               a.OPPOSITE_PARTY_WAY,
               a.OPPOSITE_PARTY_WAY_ID,
               a.APPROVAL_CODE,
               a.CONTRACT_NAME,
               a.CONTRACT_CODE,
               a.TWO_TYPE_CODE,
               a.TWO_TYPE_NAME,
               a.SETTLEMENT_METHOD,
               a.SETTLEMENT_METHOD_ID,
               a.REVENUE_EXPENDITURE,
               a.REVENUE_EXPENDITURE_ID,
               a.MONEY_TYPE,
               a.MONEY_TYPE_ID,
               a.INCLUDING_TAX,
               a.CURRENCY,
               a.CURRENCY_ID,
               a.NO_INCLUDING_TAX,
               a.EXCHANGE_RATE,
               a.INCLUDING_TAX_RMB,
               a.CONTRACT_DATE,
               a.ESTIMATED_TOTAL,
               a.CONTRACT_SUBJECT,
               a.WHETHER_MAJOR_OGN,
               a.WHETHER_MAJOR_DEPT,
               a.SUMMARY_NOTE,
               a.RECEIVE_DATE,
               a.PAYMENT_DATE,
               a.BILLING_DATE,
               a.PROFIT_CENTER,
               a.PROJECT_NAME,
               a.AUTHORIZATION_ID,
               a.CONTRACT_NAME_OLD,
               a.CONTRACT_CODE_OLD,
               a.CHANGE_DESCRIPTION,
               a.CHANGE_MONEY,
               a.symbol,
               b.PROJECT_TYPE,
               b.PROJECT_NAME   as PROJECT_NAME2,
               b.PROJECT_CODE,
               b.CHILD_PROJECT_CODE,
               b.WHETHER,
               b.TOTAL_MONEY,
               b.SURPLUS_MONEY,
               b.ASSIGN_MONEY,
               c.PARTY_NAME,
               c.OTHER_NAME,
               c.SUBJECT_STATUS_ID,
               c.SUBJECT_STATUS_NAME,
               c.CONTECT_NAME,
               c.CONTECT_TEL,
               c.NATURE,
               d.TAXABLE_AMOUNT,
               d.BILLING_TYPE,
               d.BILLING_TYPE_CODE,
               d.PRODUCT_NAME,
               d.VAT_RATE,
               d.VAT_RATE_CODE,
               d.VAT_AMOUNT,
               e.TAXABLE_AMOUNT as TAXABLE_AMOUNT2,
               e.TAX_ITEM,
               f.SEAL_ID,
               f.SEAL_NAME,
               f.SEAL_TYPE,
               f.SEAL_TYPE_ID,
               f.SEAL_ADMIN,
               f.SEAL_ADMIN_ID,
               f.SEAL_NUMBER,
               f.PRINTS_NUMBER
        from sg_contract_approval a
                 left join SG_CONTRACT_PROJECT b on a.id = b.parent_id
                 left join sg_contract_party c on a.id = c.parent_id
                 left join SG_CONTRACT_TAX_ADD d on a.id = d.parent_id
                 left join SG_CONTRACT_TAX_STAMP e on a.id = e.parent_id
                 left join SG_CONTRACT_SEAL f on a.id = f.parent_id
        where a.id = #{id}
    </select>

    <select id="queryLeftByUnit" resultType="java.util.Map">
        SELECT
        count( one_Type_Name ) AS num,
        one_Type_Name AS name_
        FROM
        ( SELECT case when one_Type_Name is null or one_Type_Name = '' then '无' else one_Type_Name end as one_Type_Name
        FROM sg_contract_approval
        <where>
            ${ew.sqlSegment}
        </where>
        ) A
        GROUP BY
        one_Type_Name
        ORDER BY one_Type_Name
    </select>

    <select id="queryLeftByYear" resultType="java.util.Map">
        SELECT
        count( time_ ) AS num,
        time_ AS name_
        FROM
        (
        SELECT
        case when create_time is null or create_time = '' then '无' else
        to_char(create_time, 'yyyy') end AS time_
        FROM
        sg_contract_approval
        <where>
            ${ew.sqlSegment}
        </where>
        ) A
        GROUP BY
        time_
        ORDER BY time_
    </select>

    <select id="queryContractTowType" resultType="java.util.Map">
        SELECT TOP 8 count(*) AS value, TWO_TYPE_NAME AS name
        FROM SG_CONTRACT_APPROVAL
        WHERE TWO_TYPE_NAME IS NOT NULL
        GROUP BY TWO_TYPE_NAME
        ORDER BY value DESC
    </select>

    <select id="getContractByContractIds" resultMap="BaseResultMap">
        SELECT <include refid="contract_Column_List"/>
        FROM SG_CONTRACT_APPROVAL SCA
        <where>
            SCA.DATA_SOURCE_CODE = 0 AND
            EXISTS (
            SELECT SCT.ID
            FROM SG_CONTRACT_TEXT SCT
            INNER JOIN SG_CONTRACT_TEMPLATE SCTT
                ON SCTT.STANDARD_ATTACHMENT_CODE  = SCT.STANDARD_ATTACHMENT_CODE
            WHERE SCA.ID = SCT.PARENT_ID
                AND SCT.FILE_TYPE_CODE  ='0' AND SCT.FILE_FROM_CODE = '1'
                AND SCTT.DATA_STATE_CODE = 1
            )
            <if test="ids != null and ids.size()>0">
                AND SCA.ID IN
                <foreach collection="ids" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>
</mapper>
