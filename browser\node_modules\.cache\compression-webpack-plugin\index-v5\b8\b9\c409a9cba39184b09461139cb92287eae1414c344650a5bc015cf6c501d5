
f8cc145a0a3a7657c6fc09335e3627e89e51b0f2	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.261.1754018536329.js\",\"contentHash\":\"537d28f828bfb06f56008855ac13979c\"}","integrity":"sha512-oB8CCA3dRusuO28mhZN7NqnmADps4HMccNQr/6ajansiLHiDugbOEitCgeUB3H9MhVMfGQyPCwtaVazFTZxUSg==","time":1754018575962,"size":106634}