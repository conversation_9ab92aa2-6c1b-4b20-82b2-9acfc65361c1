
2f0b75ca62cc47abf291cc4c9191378d9547cc11	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.44.1754018536329.js\",\"contentHash\":\"7b858b86b7362a51ab29adc262d080e7\"}","integrity":"sha512-3augBkCHEgBvnayYIE6Ety/fzImYJtycWEMrK1HCtLn39RlPIkoUy06EtkCgXdqPhWVOeEX+FZLKHUGYQLnMog==","time":1754018575958,"size":103659}