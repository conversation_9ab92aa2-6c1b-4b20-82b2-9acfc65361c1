/** 
 * Kendo UI v2016.3.1118 (http://www.telerik.com/kendo-ui)                                                                                                                                              
 * Copyright 2016 Telerik AD. All rights reserved.                                                                                                                                                      
 *                                                                                                                                                                                                      
 * Kendo UI commercial licenses may be obtained at                                                                                                                                                      
 * http://www.telerik.com/purchase/license-agreement/kendo-ui-complete                                                                                                                                  
 * If you do not own a commercial license, this file shall be governed by the trial license terms.                                                                                                      
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       

*/
!function(e,define){define("kendo.color.min",["kendo.core.min"],e)}(function(){!function(e,t,n){function i(e,o){var r,s;if(null==e||"none"==e)return null;if(e instanceof l)return e;if(e=e.toLowerCase(),r=a.exec(e))return e="transparent"==r[1]?new d(1,1,1,0):i(p.namedColors[r[1]],o),e.match=[r[1]],e;if((r=/^#?([0-9a-f]{2})([0-9a-f]{2})([0-9a-f]{2})\b/i.exec(e))?s=new c(n(r[1],16),n(r[2],16),n(r[3],16),1):(r=/^#?([0-9a-f])([0-9a-f])([0-9a-f])\b/i.exec(e))?s=new c(n(r[1]+r[1],16),n(r[2]+r[2],16),n(r[3]+r[3],16),1):(r=/^rgb\(\s*([0-9]+)\s*,\s*([0-9]+)\s*,\s*([0-9]+)\s*\)/.exec(e))?s=new c(n(r[1],10),n(r[2],10),n(r[3],10),1):(r=/^rgba\(\s*([0-9]+)\s*,\s*([0-9]+)\s*,\s*([0-9]+)\s*,\s*([0-9.]+)\s*\)/.exec(e))?s=new c(n(r[1],10),n(r[2],10),n(r[3],10),t(r[4])):(r=/^rgb\(\s*([0-9]*\.?[0-9]+)%\s*,\s*([0-9]*\.?[0-9]+)%\s*,\s*([0-9]*\.?[0-9]+)%\s*\)/.exec(e))?s=new d(t(r[1])/100,t(r[2])/100,t(r[3])/100,1):(r=/^rgba\(\s*([0-9]*\.?[0-9]+)%\s*,\s*([0-9]*\.?[0-9]+)%\s*,\s*([0-9]*\.?[0-9]+)%\s*,\s*([0-9.]+)\s*\)/.exec(e))&&(s=new d(t(r[1])/100,t(r[2])/100,t(r[3])/100,t(r[4]))),s)s.match=r;else if(!o)throw Error("Cannot parse color: "+e);return s}function o(e,t,n){for(n||(n="0"),e=e.toString(16);t>e.length;)e="0"+e;return e}function r(e,t,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?e+6*(t-e)*n:n<.5?t:n<2/3?e+(t-e)*(2/3-n)*6:e}var a,s,l,d,c,u,f,p=function(e){var t,n,i,o,r,a=this,s=p.formats;if(1===arguments.length)for(e=a.resolveColor(e),o=0;o<s.length;o++)t=s[o].re,n=s[o].process,i=t.exec(e),i&&(r=n(i),a.r=r[0],a.g=r[1],a.b=r[2]);else a.r=arguments[0],a.g=arguments[1],a.b=arguments[2];a.r=a.normalizeByte(a.r),a.g=a.normalizeByte(a.g),a.b=a.normalizeByte(a.b)};p.prototype={toHex:function(){var e=this,t=e.padDigit,n=e.r.toString(16),i=e.g.toString(16),o=e.b.toString(16);return"#"+t(n)+t(i)+t(o)},resolveColor:function(e){return e=e||"black","#"==e.charAt(0)&&(e=e.substr(1,6)),e=e.replace(/ /g,""),e=e.toLowerCase(),e=p.namedColors[e]||e},normalizeByte:function(e){return e<0||isNaN(e)?0:e>255?255:e},padDigit:function(e){return 1===e.length?"0"+e:e},brightness:function(e){var t=this,n=Math.round;return t.r=n(t.normalizeByte(t.r*e)),t.g=n(t.normalizeByte(t.g*e)),t.b=n(t.normalizeByte(t.b*e)),t},percBrightness:function(){var e=this;return Math.sqrt(.241*e.r*e.r+.691*e.g*e.g+.068*e.b*e.b)}},p.formats=[{re:/^rgb\((\d{1,3}),\s*(\d{1,3}),\s*(\d{1,3})\)$/,process:function(e){return[n(e[1],10),n(e[2],10),n(e[3],10)]}},{re:/^(\w{2})(\w{2})(\w{2})$/,process:function(e){return[n(e[1],16),n(e[2],16),n(e[3],16)]}},{re:/^(\w{1})(\w{1})(\w{1})$/,process:function(e){return[n(e[1]+e[1],16),n(e[2]+e[2],16),n(e[3]+e[3],16)]}}],p.namedColors={aliceblue:"f0f8ff",antiquewhite:"faebd7",aqua:"00ffff",aquamarine:"7fffd4",azure:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"000000",blanchedalmond:"ffebcd",blue:"0000ff",blueviolet:"8a2be2",brown:"a52a2a",burlywood:"deb887",cadetblue:"5f9ea0",chartreuse:"7fff00",chocolate:"d2691e",coral:"ff7f50",cornflowerblue:"6495ed",cornsilk:"fff8dc",crimson:"dc143c",cyan:"00ffff",darkblue:"00008b",darkcyan:"008b8b",darkgoldenrod:"b8860b",darkgray:"a9a9a9",darkgrey:"a9a9a9",darkgreen:"006400",darkkhaki:"bdb76b",darkmagenta:"8b008b",darkolivegreen:"556b2f",darkorange:"ff8c00",darkorchid:"9932cc",darkred:"8b0000",darksalmon:"e9967a",darkseagreen:"8fbc8f",darkslateblue:"483d8b",darkslategray:"2f4f4f",darkslategrey:"2f4f4f",darkturquoise:"00ced1",darkviolet:"9400d3",deeppink:"ff1493",deepskyblue:"00bfff",dimgray:"696969",dimgrey:"696969",dodgerblue:"1e90ff",firebrick:"b22222",floralwhite:"fffaf0",forestgreen:"228b22",fuchsia:"ff00ff",gainsboro:"dcdcdc",ghostwhite:"f8f8ff",gold:"ffd700",goldenrod:"daa520",gray:"808080",grey:"808080",green:"008000",greenyellow:"adff2f",honeydew:"f0fff0",hotpink:"ff69b4",indianred:"cd5c5c",indigo:"4b0082",ivory:"fffff0",khaki:"f0e68c",lavender:"e6e6fa",lavenderblush:"fff0f5",lawngreen:"7cfc00",lemonchiffon:"fffacd",lightblue:"add8e6",lightcoral:"f08080",lightcyan:"e0ffff",lightgoldenrodyellow:"fafad2",lightgray:"d3d3d3",lightgrey:"d3d3d3",lightgreen:"90ee90",lightpink:"ffb6c1",lightsalmon:"ffa07a",lightseagreen:"20b2aa",lightskyblue:"87cefa",lightslategray:"778899",lightslategrey:"778899",lightsteelblue:"b0c4de",lightyellow:"ffffe0",lime:"00ff00",limegreen:"32cd32",linen:"faf0e6",magenta:"ff00ff",maroon:"800000",mediumaquamarine:"66cdaa",mediumblue:"0000cd",mediumorchid:"ba55d3",mediumpurple:"9370d8",mediumseagreen:"3cb371",mediumslateblue:"7b68ee",mediumspringgreen:"00fa9a",mediumturquoise:"48d1cc",mediumvioletred:"c71585",midnightblue:"191970",mintcream:"f5fffa",mistyrose:"ffe4e1",moccasin:"ffe4b5",navajowhite:"ffdead",navy:"000080",oldlace:"fdf5e6",olive:"808000",olivedrab:"6b8e23",orange:"ffa500",orangered:"ff4500",orchid:"da70d6",palegoldenrod:"eee8aa",palegreen:"98fb98",paleturquoise:"afeeee",palevioletred:"d87093",papayawhip:"ffefd5",peachpuff:"ffdab9",peru:"cd853f",pink:"ffc0cb",plum:"dda0dd",powderblue:"b0e0e6",purple:"800080",red:"ff0000",rosybrown:"bc8f8f",royalblue:"4169e1",saddlebrown:"8b4513",salmon:"fa8072",sandybrown:"f4a460",seagreen:"2e8b57",seashell:"fff5ee",sienna:"a0522d",silver:"c0c0c0",skyblue:"87ceeb",slateblue:"6a5acd",slategray:"708090",slategrey:"708090",snow:"fffafa",springgreen:"00ff7f",steelblue:"4682b4",tan:"d2b48c",teal:"008080",thistle:"d8bfd8",tomato:"ff6347",turquoise:"40e0d0",violet:"ee82ee",wheat:"f5deb3",white:"ffffff",whitesmoke:"f5f5f5",yellow:"ffff00",yellowgreen:"9acd32"},a=["transparent"];for(s in p.namedColors)p.namedColors.hasOwnProperty(s)&&a.push(s);a=RegExp("^("+a.join("|")+")(\\W|$)","i"),l=kendo.Class.extend({toHSV:function(){return this},toRGB:function(){return this},toHex:function(){return this.toBytes().toHex()},toBytes:function(){return this},toCss:function(){return"#"+this.toHex()},toCssRgba:function(){var e=this.toBytes();return"rgba("+e.r+", "+e.g+", "+e.b+", "+t((+this.a).toFixed(3))+")"},toDisplay:function(){return kendo.support.browser.msie&&kendo.support.browser.version<9?this.toCss():this.toCssRgba()},equals:function(e){return e===this||null!==e&&this.toCssRgba()==i(e).toCssRgba()},diff:function(e){if(null==e)return NaN;var t=this.toBytes();return e=e.toBytes(),Math.sqrt(Math.pow(.3*(t.r-e.r),2)+Math.pow(.59*(t.g-e.g),2)+Math.pow(.11*(t.b-e.b),2))},clone:function(){var e=this.toBytes();return e===this&&(e=new c(e.r,e.g,e.b,e.a)),e}}),d=l.extend({init:function(e,t,n,i){this.r=e,this.g=t,this.b=n,this.a=i},toHSV:function(){var e,t,n=this.r,i=this.g,o=this.b,r=Math.min(n,i,o),a=Math.max(n,i,o),s=a,l=a-r;return 0===l?new u(0,0,s,this.a):(0!==a?(t=l/a,e=n==a?(i-o)/l:i==a?2+(o-n)/l:4+(n-i)/l,e*=60,e<0&&(e+=360)):(t=0,e=-1),new u(e,t,s,this.a))},toHSL:function(){var e,t,n,i=this.r,o=this.g,r=this.b,a=Math.max(i,o,r),s=Math.min(i,o,r),l=(a+s)/2;if(a==s)e=t=0;else{switch(n=a-s,t=l>.5?n/(2-a-s):n/(a+s),a){case i:e=(o-r)/n+(o<r?6:0);break;case o:e=(r-i)/n+2;break;case r:e=(i-o)/n+4}e*=60,t*=100,l*=100}return new f(e,t,l,this.a)},toBytes:function(){return new c(255*this.r,255*this.g,255*this.b,this.a)}}),c=d.extend({init:function(e,t,n,i){this.r=Math.round(e),this.g=Math.round(t),this.b=Math.round(n),this.a=i},toRGB:function(){return new d(this.r/255,this.g/255,this.b/255,this.a)},toHSV:function(){return this.toRGB().toHSV()},toHSL:function(){return this.toRGB().toHSL()},toHex:function(){return o(this.r,2)+o(this.g,2)+o(this.b,2)},toBytes:function(){return this}}),u=l.extend({init:function(e,t,n,i){this.h=e,this.s=t,this.v=n,this.a=i},toRGB:function(){var e,t,n,i,o,r,a,s,l=this.h,c=this.s,u=this.v;if(0===c)t=n=i=u;else switch(l/=60,e=Math.floor(l),o=l-e,r=u*(1-c),a=u*(1-c*o),s=u*(1-c*(1-o)),e){case 0:t=u,n=s,i=r;break;case 1:t=a,n=u,i=r;break;case 2:t=r,n=u,i=s;break;case 3:t=r,n=a,i=u;break;case 4:t=s,n=r,i=u;break;default:t=u,n=r,i=a}return new d(t,n,i,this.a)},toHSL:function(){return this.toRGB().toHSL()},toBytes:function(){return this.toRGB().toBytes()}}),f=l.extend({init:function(e,t,n,i){this.h=e,this.s=t,this.l=n,this.a=i},toRGB:function(){var e,t,n,i,o,a=this.h,s=this.s,l=this.l;return 0===s?e=t=n=l:(a/=360,s/=100,l/=100,i=l<.5?l*(1+s):l+s-l*s,o=2*l-i,e=r(o,i,a+1/3),t=r(o,i,a),n=r(o,i,a-1/3)),new d(e,t,n,this.a)},toHSV:function(){return this.toRGB().toHSV()},toBytes:function(){return this.toRGB().toBytes()}}),p.fromBytes=function(e,t,n,i){return new c(e,t,n,null!=i?i:1)},p.fromRGB=function(e,t,n,i){return new d(e,t,n,null!=i?i:1)},p.fromHSV=function(e,t,n,i){return new u(e,t,n,null!=i?i:1)},p.fromHSL=function(e,t,n,i){return new f(e,t,n,null!=i?i:1)},kendo.Color=p,kendo.parseColor=i}(window.kendo.jQuery,parseFloat,parseInt)},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),/** 
 * Kendo UI v2016.3.1118 (http://www.telerik.com/kendo-ui)                                                                                                                                              
 * Copyright 2016 Telerik AD. All rights reserved.                                                                                                                                                      
 *                                                                                                                                                                                                      
 * Kendo UI commercial licenses may be obtained at                                                                                                                                                      
 * http://www.telerik.com/purchase/license-agreement/kendo-ui-complete                                                                                                                                  
 * If you do not own a commercial license, this file shall be governed by the trial license terms.                                                                                                      
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       

*/
function(e,define){define("kendo.colorpicker.min",["kendo.core.min","kendo.color.min","kendo.popup.min","kendo.slider.min","kendo.userevents.min"],e)}(function(){return function(e,t,n){function i(e,t,n){n=c(n),n&&!n.equals(e.color())&&("change"==t&&(e._value=n),n=1!=n.a?n.toCssRgba():n.toCss(),e.trigger(t,{value:n}))}function o(e,t,n){var i,o;return e=Array.prototype.slice.call(e),i=e.length,o=e.indexOf(t),o<0?n<0?e[i-1]:e[0]:(o+=n,o<0?o+=i:o%=i,e[o])}function r(e){e.preventDefault()}function a(e,t){return function(){return e.apply(t,arguments)}}var s=window.kendo,l=s.ui,d=l.Widget,c=s.parseColor,u=s.Color,f=s.keys,p="background-color",h="k-state-selected",m="000000,7f7f7f,880015,ed1c24,ff7f27,fff200,22b14c,00a2e8,3f48cc,a349a4,ffffff,c3c3c3,b97a57,ffaec9,ffc90e,efe4b0,b5e61d,99d9ea,7092be,c8bfe7",g="FFFFFF,FFCCFF,FF99FF,FF66FF,FF33FF,FF00FF,CCFFFF,CCCCFF,CC99FF,CC66FF,CC33FF,CC00FF,99FFFF,99CCFF,9999FF,9966FF,9933FF,9900FF,FFFFCC,FFCCCC,FF99CC,FF66CC,FF33CC,FF00CC,CCFFCC,CCCCCC,CC99CC,CC66CC,CC33CC,CC00CC,99FFCC,99CCCC,9999CC,9966CC,9933CC,9900CC,FFFF99,FFCC99,FF9999,FF6699,FF3399,FF0099,CCFF99,CCCC99,CC9999,CC6699,CC3399,CC0099,99FF99,99CC99,999999,996699,993399,990099,FFFF66,FFCC66,FF9966,FF6666,FF3366,FF0066,CCFF66,CCCC66,CC9966,CC6666,CC3366,CC0066,99FF66,99CC66,999966,996666,993366,990066,FFFF33,FFCC33,FF9933,FF6633,FF3333,FF0033,CCFF33,CCCC33,CC9933,CC6633,CC3333,CC0033,99FF33,99CC33,999933,996633,993333,990033,FFFF00,FFCC00,FF9900,FF6600,FF3300,FF0000,CCFF00,CCCC00,CC9900,CC6600,CC3300,CC0000,99FF00,99CC00,999900,996600,993300,990000,66FFFF,66CCFF,6699FF,6666FF,6633FF,6600FF,33FFFF,33CCFF,3399FF,3366FF,3333FF,3300FF,00FFFF,00CCFF,0099FF,0066FF,0033FF,0000FF,66FFCC,66CCCC,6699CC,6666CC,6633CC,6600CC,33FFCC,33CCCC,3399CC,3366CC,3333CC,3300CC,00FFCC,00CCCC,0099CC,0066CC,0033CC,0000CC,66FF99,66CC99,669999,666699,663399,660099,33FF99,33CC99,339999,336699,333399,330099,00FF99,00CC99,009999,006699,003399,000099,66FF66,66CC66,669966,666666,663366,660066,33FF66,33CC66,339966,336666,333366,330066,00FF66,00CC66,009966,006666,003366,000066,66FF33,66CC33,669933,666633,663333,660033,33FF33,33CC33,339933,336633,333333,330033,00FF33,00CC33,009933,006633,003333,000033,66FF00,66CC00,669900,666600,663300,660000,33FF00,33CC00,339900,336600,333300,330000,00FF00,00CC00,009900,006600,003300,000000",b={apply:"Apply",cancel:"Cancel"},v=".kendoColorTools",k="click"+v,y="keydown"+v,C=s.support.browser,w=C.msie&&C.version<9,x=d.extend({init:function(e,t){var n,i=this;d.fn.init.call(i,e,t),e=i.element,t=i.options,i._value=t.value=c(t.value),i._tabIndex=e.attr("tabIndex")||0,n=i._ariaId=t.ariaId,n&&e.attr("aria-labelledby",n),t._standalone&&(i._triggerSelect=i._triggerChange)},options:{name:"ColorSelector",value:null,_standalone:!0},events:["change","select","cancel"],color:function(e){return e!==n&&(this._value=c(e),this._updateUI(this._value)),this._value},value:function(e){return e=this.color(e),e&&(e=this.options.opacity?e.toCssRgba():e.toCss()),e||null},enable:function(t){0===arguments.length&&(t=!0),e(".k-disabled-overlay",this.wrapper).remove(),t||this.wrapper.append("<div class='k-disabled-overlay'></div>"),this._onEnable(t)},_select:function(e,t){var n=this._value;e=this.color(e),t||(this.element.trigger("change"),e.equals(n)?this._standalone||this.trigger("cancel"):this.trigger("change",{value:this.value()}))},_triggerSelect:function(e){i(this,"select",e)},_triggerChange:function(e){i(this,"change",e)},destroy:function(){this.element&&this.element.off(v),this.wrapper&&this.wrapper.off(v).find("*").off(v),this.wrapper=null,d.fn.destroy.call(this)},_updateUI:e.noop,_selectOnHide:function(){return null},_cancel:function(){this.trigger("cancel")}}),T=x.extend({init:function(t,n){var i,o,r,l,d=this;if(x.fn.init.call(d,t,n),t=d.wrapper=d.element,n=d.options,i=n.palette,"websafe"==i?(i=g,n.columns=18):"basic"==i&&(i=m),"string"==typeof i&&(i=i.split(",")),e.isArray(i)&&(i=e.map(i,function(e){return c(e)})),d._selectedID=(n.ariaId||s.guid())+"_selected",t.addClass("k-widget k-colorpalette").attr("role","grid").attr("aria-readonly","true").append(e(d._template({colors:i,columns:n.columns,tileSize:n.tileSize,value:d._value,id:n.ariaId}))).on(k,".k-item",function(t){d._select(e(t.currentTarget).css(p))}).attr("tabIndex",d._tabIndex).on(y,a(d._keydown,d)),o=n.tileSize){if(/number|string/.test(typeof o))r=l=parseFloat(o);else{if("object"!=typeof o)throw Error("Unsupported value for the 'tileSize' argument");r=parseFloat(o.width),l=parseFloat(o.height)}t.find(".k-item").css({width:r,height:l})}},focus:function(){this.wrapper.focus()},options:{name:"ColorPalette",columns:10,tileSize:null,palette:"basic"},_onEnable:function(e){e?this.wrapper.attr("tabIndex",this._tabIndex):this.wrapper.removeAttr("tabIndex")},_keydown:function(t){var n,i,a=this.wrapper,s=a.find(".k-item"),l=s.filter("."+h).get(0),d=t.keyCode;if(d==f.LEFT?n=o(s,l,-1):d==f.RIGHT?n=o(s,l,1):d==f.DOWN?n=o(s,l,this.options.columns):d==f.UP?n=o(s,l,-this.options.columns):d==f.ENTER?(r(t),l&&this._select(e(l).css(p))):d==f.ESC&&this._cancel(),n){r(t),this._current(n);try{i=c(n.css(p)),this._triggerSelect(i)}catch(u){}}},_current:function(t){this.wrapper.find("."+h).removeClass(h).attr("aria-selected",!1).removeAttr("id"),e(t).addClass(h).attr("aria-selected",!0).attr("id",this._selectedID),this.element.removeAttr("aria-activedescendant").attr("aria-activedescendant",this._selectedID)},_updateUI:function(t){var n=null;this.wrapper.find(".k-item").each(function(){var i=c(e(this).css(p));if(i&&i.equals(t))return n=this,!1}),this._current(n)},_template:s.template('<table class="k-palette k-reset" role="presentation"><tr role="row"># for (var i = 0; i < colors.length; ++i) { ## var selected = colors[i].equals(value); ## if (i && i % columns == 0) { # </tr><tr role="row"> # } #<td role="gridcell" unselectable="on" style="background-color:#= colors[i].toCss() #"#= selected ? " aria-selected=true" : "" # #=(id && i === 0) ? "id=\\""+id+"\\" " : "" # class="k-item#= selected ? " '+h+'" : "" #" aria-label="#= colors[i].toCss() #"></td># } #</tr></table>')}),_=x.extend({init:function(t,n){var i=this;x.fn.init.call(i,t,n),n=i.options,t=i.element,i.wrapper=t.addClass("k-widget k-flatcolorpicker").append(i._template(n)),i._hueElements=e(".k-hsv-rectangle, .k-transparency-slider .k-slider-track",t),i._selectedColor=e(".k-selected-color-display",t),i._colorAsText=e("input.k-color-value",t),i._sliders(),i._hsvArea(),i._updateUI(i._value||c("#f00")),t.find("input.k-color-value").on(y,function(t){var n,o,r=this;if(t.keyCode==f.ENTER)try{n=c(r.value),o=i.color(),i._select(n,n.equals(o))}catch(a){e(r).addClass("k-state-error")}else i.options.autoupdate&&setTimeout(function(){var e=c(r.value,!0);e&&i._updateUI(e,!0)},10)}).end().on(k,".k-controls button.apply",function(){i._select(i._getHSV())}).on(k,".k-controls button.cancel",function(){i._updateUI(i.color()),i._cancel()}),w&&i._applyIEFilter()},destroy:function(){this._hueSlider.destroy(),this._opacitySlider&&this._opacitySlider.destroy(),this._hueSlider=this._opacitySlider=this._hsvRect=this._hsvHandle=this._hueElements=this._selectedColor=this._colorAsText=null,x.fn.destroy.call(this)},options:{name:"FlatColorPicker",opacity:!1,buttons:!1,input:!0,preview:!0,autoupdate:!0,messages:b},_applyIEFilter:function(){var e=this.element.find(".k-hue-slider .k-slider-track")[0],t=e.currentStyle.backgroundImage;t=t.replace(/^url\([\'\"]?|[\'\"]?\)$/g,""),e.style.filter="progid:DXImageTransform.Microsoft.AlphaImageLoader(src='"+t+"', sizingMethod='scale')"},_sliders:function(){function e(e){n._updateUI(n._getHSV(e.value,null,null,null))}function t(e){n._updateUI(n._getHSV(null,null,null,e.value/100))}var n=this,i=n.element;n._hueSlider=i.find(".k-hue-slider").kendoSlider({min:0,max:360,tickPlacement:"none",showButtons:!1,slide:e,change:e}).data("kendoSlider"),n._opacitySlider=i.find(".k-transparency-slider").kendoSlider({min:0,max:100,tickPlacement:"none",showButtons:!1,slide:t,change:t}).data("kendoSlider")},_hsvArea:function(){function e(e,n){var i=this.offset,o=e-i.left,r=n-i.top,a=this.width,s=this.height;o=o<0?0:o>a?a:o,r=r<0?0:r>s?s:r,t._svChange(o/a,1-r/s)}var t=this,n=t.element,i=n.find(".k-hsv-rectangle"),o=i.find(".k-draghandle").attr("tabIndex",0).on(y,a(t._keydown,t));t._hsvEvents=new s.UserEvents(i,{global:!0,press:function(t){this.offset=s.getOffset(i),this.width=i.width(),this.height=i.height(),o.focus(),e.call(this,t.x.location,t.y.location)},start:function(){i.addClass("k-dragging"),o.focus()},move:function(t){t.preventDefault(),e.call(this,t.x.location,t.y.location)},end:function(){i.removeClass("k-dragging")}}),t._hsvRect=i,t._hsvHandle=o},_onEnable:function(e){this._hueSlider.enable(e),this._opacitySlider&&this._opacitySlider.enable(e),this.wrapper.find("input").attr("disabled",!e);var t=this._hsvRect.find(".k-draghandle");e?t.attr("tabIndex",this._tabIndex):t.removeAttr("tabIndex")},_keydown:function(e){function t(t,n){var o=i._getHSV();o[t]+=n*(e.shiftKey?.01:.05),o[t]<0&&(o[t]=0),o[t]>1&&(o[t]=1),i._updateUI(o),r(e)}function n(t){var n=i._getHSV();n.h+=t*(e.shiftKey?1:5),n.h<0&&(n.h=0),n.h>359&&(n.h=359),i._updateUI(n),r(e)}var i=this;switch(e.keyCode){case f.LEFT:e.ctrlKey?n(-1):t("s",-1);break;case f.RIGHT:e.ctrlKey?n(1):t("s",1);break;case f.UP:t(e.ctrlKey&&i._opacitySlider?"a":"v",1);break;case f.DOWN:t(e.ctrlKey&&i._opacitySlider?"a":"v",-1);break;case f.ENTER:i._select(i._getHSV());break;case f.F2:i.wrapper.find("input.k-color-value").focus().select();break;case f.ESC:i._cancel()}},focus:function(){this._hsvHandle.focus()},_getHSV:function(e,t,n,i){var o=this._hsvRect,r=o.width(),a=o.height(),s=this._hsvHandle.position();return null==e&&(e=this._hueSlider.value()),null==t&&(t=s.left/r),null==n&&(n=1-s.top/a),null==i&&(i=this._opacitySlider?this._opacitySlider.value()/100:1),u.fromHSV(e,t,n,i)},_svChange:function(e,t){var n=this._getHSV(null,e,t,null);this._updateUI(n)},_updateUI:function(e,t){var n=this,i=n._hsvRect;e&&(this._colorAsText.removeClass("k-state-error"),n._selectedColor.css(p,e.toDisplay()),t||n._colorAsText.val(n._opacitySlider?e.toCssRgba():e.toCss()),n._triggerSelect(e),e=e.toHSV(),n._hsvHandle.css({left:e.s*i.width()+"px",top:(1-e.v)*i.height()+"px"}),n._hueElements.css(p,u.fromHSV(e.h,1,1,1).toCss()),n._hueSlider.value(e.h),n._opacitySlider&&n._opacitySlider.value(100*e.a))},_selectOnHide:function(){return this.options.buttons?null:this._getHSV()},_template:s.template('# if (preview) { #<div class="k-selected-color"><div class="k-selected-color-display"><input class="k-color-value" #= !data.input ? \'style="visibility: hidden;"\' : "" #></div></div># } #<div class="k-hsv-rectangle"><div class="k-hsv-gradient"></div><div class="k-draghandle"></div></div><input class="k-hue-slider" /># if (opacity) { #<input class="k-transparency-slider" /># } ## if (buttons) { #<div unselectable="on" class="k-controls"><button class="k-button k-primary apply">#: messages.apply #</button> <button class="k-button cancel">#: messages.cancel #</button></div># } #')}),F=d.extend({init:function(t,n){var i,o,r,a,s,l=this;d.fn.init.call(l,t,n),n=l.options,t=l.element,i=t.attr("value")||t.val(),i=i?c(i,!0):c(n.value,!0),l._value=n.value=i,o=l.wrapper=e(l._template(n)),t.hide().after(o),t.is("input")&&(t.appendTo(o),r=t.closest("label"),a=t.attr("id"),a&&(r=r.add('label[for="'+a+'"]')),r.click(function(e){l.open(),e.preventDefault()})),l._tabIndex=t.attr("tabIndex")||0,l.enable(!t.attr("disabled")),s=t.attr("accesskey"),s&&(t.attr("accesskey",null),o.attr("accesskey",s)),l.bind("activate",function(e){e.isDefaultPrevented()||l.toggle()}),l._updateUI(i)},destroy:function(){this.wrapper.off(v).find("*").off(v),this._popup&&(this._selector.destroy(),this._popup.destroy()),this._selector=this._popup=this.wrapper=null,d.fn.destroy.call(this)},enable:function(e){var t=this,n=t.wrapper,i=n.children(".k-picker-wrap"),o=i.find(".k-select");0===arguments.length&&(e=!0),t.element.attr("disabled",!e),n.attr("aria-disabled",!e),o.off(v).on("mousedown"+v,r),n.addClass("k-state-disabled").removeAttr("tabIndex").add("*",n).off(v),e?n.removeClass("k-state-disabled").attr("tabIndex",t._tabIndex).on("mouseenter"+v,function(){i.addClass("k-state-hover")}).on("mouseleave"+v,function(){i.removeClass("k-state-hover")}).on("focus"+v,function(){i.addClass("k-state-focused")}).on("blur"+v,function(){i.removeClass("k-state-focused")}).on(y,a(t._keydown,t)).on(k,".k-select",a(t.toggle,t)).on(k,t.options.toolIcon?".k-tool-icon":".k-selected-color",function(){t.trigger("activate")}):t.close()},_template:s.template('<span role="textbox" aria-haspopup="true" class="k-widget k-colorpicker k-header"><span class="k-picker-wrap k-state-default"># if (toolIcon) { #<span class="k-tool-icon #= toolIcon #"><span class="k-selected-color"></span></span># } else { #<span class="k-selected-color"></span># } #<span class="k-select" unselectable="on" aria-label="select"><span class="k-icon k-i-arrow-s"></span></span></span></span>'),options:{name:"ColorPicker",palette:null,columns:10,toolIcon:null,value:null,messages:b,opacity:!1,buttons:!0,preview:!0,ARIATemplate:'Current selected color is #=data || ""#'},events:["activate","change","select","open","close"],open:function(){this.element.prop("disabled")||this._getPopup().open()},close:function(){this._getPopup().close()},toggle:function(){this.element.prop("disabled")||this._getPopup().toggle()},color:x.fn.color,value:x.fn.value,_select:x.fn._select,_triggerSelect:x.fn._triggerSelect,_isInputTypeColor:function(){var e=this.element[0];return/^input$/i.test(e.tagName)&&/^color$/i.test(e.type)},_updateUI:function(e){var t="";e&&(t=this._isInputTypeColor()||1==e.a?e.toCss():e.toCssRgba(),this.element.val(t)),this._ariaTemplate||(this._ariaTemplate=s.template(this.options.ARIATemplate)),this.wrapper.attr("aria-label",this._ariaTemplate(t)),this._triggerSelect(e),this.wrapper.find(".k-selected-color").css(p,e?e.toDisplay():"transparent")},_keydown:function(e){var t=e.keyCode;this._getPopup().visible()?(t==f.ESC?this._selector._cancel():this._selector._keydown(e),r(e)):t!=f.ENTER&&t!=f.DOWN||(this.open(),r(e))},_getPopup:function(){var t,i,o,r,a=this,l=a._popup;return l||(t=a.options,i=t.palette?T:_,t._standalone=!1,delete t.select,delete t.change,delete t.cancel,o=s.guid(),r=a._selector=new i(e('<div id="'+o+'"/>').appendTo(document.body),t),a.wrapper.attr("aria-owns",o),a._popup=l=r.wrapper.kendoPopup({anchor:a.wrapper,adjustSize:{width:5,height:0}}).data("kendoPopup"),r.bind({select:function(e){a._updateUI(c(e.value))},change:function(){a._select(r.color()),a.close()},cancel:function(){a.close()}}),l.bind({close:function(e){if(a.trigger("close"))return e.preventDefault(),n;a.wrapper.children(".k-picker-wrap").removeClass("k-state-focused");var t=r._selectOnHide();t?a._select(t):(setTimeout(function(){a.wrapper&&a.wrapper.focus()}),a._updateUI(a.color()))},open:function(e){a.trigger("open")?e.preventDefault():a.wrapper.children(".k-picker-wrap").addClass("k-state-focused")},activate:function(){r._select(a.color(),!0),r.focus(),a.wrapper.children(".k-picker-wrap").addClass("k-state-focused")}})),l}});l.plugin(T),l.plugin(_),l.plugin(F)}(jQuery,parseInt),window.kendo},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),/** 
 * Kendo UI v2016.2.714 (http://www.telerik.com/kendo-ui)                                                                                                                                               
 * Copyright 2016 Telerik AD. All rights reserved.                                                                                                                                                      
 *                                                                                                                                                                                                      
 * Kendo UI commercial licenses may be obtained at                                                                                                                                                      
 * http://www.telerik.com/purchase/license-agreement/kendo-ui-complete                                                                                                                                  
 * If you do not own a commercial license, this file shall be governed by the trial license terms.                                                                                                      
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       

*/
function(e,define){define("util/undoredostack.min",["kendo.core.min"],e)}(function(){!function(e){var t=e.Observable.extend({init:function(t){e.Observable.fn.init.call(this,t),this.clear()},events:["undo","redo"],push:function(e){this.stack=this.stack.slice(0,this.currentCommandIndex+1),this.currentCommandIndex=this.stack.push(e)-1},undo:function(){if(this.canUndo()){var e=this.stack[this.currentCommandIndex--];e.undo(),this.trigger("undo",{command:e})}},redo:function(){if(this.canRedo()){var e=this.stack[++this.currentCommandIndex];e.redo(),this.trigger("redo",{command:e})}},clear:function(){this.stack=[],this.currentCommandIndex=-1},canUndo:function(){return this.currentCommandIndex>=0},canRedo:function(){return this.currentCommandIndex!=this.stack.length-1}});e.deepExtend(e,{util:{UndoRedoStack:t}})}(kendo)},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("editor/main.min",["util/undoredostack.min","kendo.combobox.min","kendo.dropdownlist.min","kendo.window.min","kendo.colorpicker.min"],e)}(function(){!function(e,t){var n,i,o,r,a=window.kendo,s=a.Class,l=a.ui.Widget,d=a.support.mobileOS,c=a.support.browser,u=e.extend,f=e.proxy,p=a.deepExtend,h=a.keys,m=s.extend({init:function(e){this.options=e},getHtml:function(){var e=this.options;return a.template(e.template,{useWithBlock:!1})(e)}}),g={editorWrapperTemplate:'<table cellspacing="4" cellpadding="0" class="k-widget k-editor k-header" role="presentation"><tbody><tr role="presentation"><td class="k-editor-toolbar-wrap" role="presentation"><ul class="k-editor-toolbar" role="toolbar" /></td></tr><tr><td class="k-editable-area" /></tr></tbody></table>',buttonTemplate:'<a href="" role="button" class="k-tool"#= data.popup ? " data-popup" : "" # unselectable="on" title="#= data.title #"><span unselectable="on" class="k-tool-icon #= data.cssClass #"></span><span class="k-tool-text">#= data.title #</span></a>',colorPickerTemplate:'<div class="k-colorpicker #= data.cssClass #" />',comboBoxTemplate:'<select title="#= data.title #" class="#= data.cssClass #" />',dropDownListTemplate:'<span class="k-editor-dropdown"><select title="#= data.title #" class="#= data.cssClass #" /></span>',separatorTemplate:'<span class="k-separator" />',overflowAnchorTemplate:'<a href="" role="button" class="k-tool k-overflow-anchor" data-popup unselectable="on"><span unselectable="on" class="k-icon k-i-more"></span></a>',formatByName:function(t,n){for(var i=0;i<n.length;i++)if(e.inArray(t,n[i].tags)>=0)return n[i]},registerTool:function(e,t){var n=t.options;n&&n.template&&(n.template.options.cssClass="k-"+e),t.name||(t.options.name=e,t.name=e.toLowerCase()),y.defaultTools[e]=t},registerFormat:function(e,t){y.fn.options.formats[e]=t}},b={bold:"Bold",italic:"Italic",underline:"Underline",strikethrough:"Strikethrough",superscript:"Superscript",subscript:"Subscript",justifyCenter:"Center text",justifyLeft:"Align text left",justifyRight:"Align text right",justifyFull:"Justify",insertUnorderedList:"Insert unordered list",insertOrderedList:"Insert ordered list",indent:"Indent",outdent:"Outdent",createLink:"Insert hyperlink",unlink:"Remove hyperlink",insertImage:"Insert image",insertFile:"Insert file",insertHtml:"Insert HTML",viewHtml:"View HTML",fontName:"Select font family",fontNameInherit:"(inherited font)",fontSize:"Select font size",fontSizeInherit:"(inherited size)",formatBlock:"Format",formatting:"Format",foreColor:"Color",backColor:"Background color",style:"Styles",emptyFolder:"Empty Folder",editAreaTitle:"Editable area. Press F10 for toolbar.",uploadFile:"Upload",orderBy:"Arrange by:",orderBySize:"Size",orderByName:"Name",invalidFileType:'The selected file "{0}" is not valid. Supported file types are {1}.',deleteFile:'Are you sure you want to delete "{0}"?',overwriteFile:'A file with name "{0}" already exists in the current directory. Do you want to overwrite it?',directoryNotFound:"A directory with this name was not found.",imageWebAddress:"Web address",imageAltText:"Alternate text",imageWidth:"Width (px)",imageHeight:"Height (px)",fileWebAddress:"Web address",fileTitle:"Title",linkWebAddress:"Web address",linkText:"Text",linkToolTip:"ToolTip",linkOpenInNewWindow:"Open link in new window",dialogUpdate:"Update",dialogInsert:"Insert",dialogCancel:"Cancel",createTable:"Create table",createTableHint:"Create a {0} x {1} table",addColumnLeft:"Add column on the left",addColumnRight:"Add column on the right",addRowAbove:"Add row above",addRowBelow:"Add row below",deleteRow:"Delete row",deleteColumn:"Delete column"},v=!d||d.ios&&d.flatVersion>=500||!d.ios&&t!==document.documentElement.contentEditable,k={basic:["bold","italic","underline"],alignment:["justifyLeft","justifyCenter","justifyRight"],lists:["insertUnorderedList","insertOrderedList"],indenting:["indent","outdent"],links:["createLink","unlink"],tables:["createTable","addColumnLeft","addColumnRight","addRowAbove","addRowBelow","deleteRow","deleteColumn"]},y=l.extend({init:function(n,i){var o,r,s,d,c,h=this,m=a.ui.editor,g=m.Dom;v&&(l.fn.init.call(h,n,i),h.options=p({},h.options,i),h.options.tools=h.options.tools.slice(),n=h.element,c=n[0],d=g.name(c),this._registerHandler(n.closest("form"),"submit",f(h.update,h,t)),s=u({},h.options),s.editor=h,"textarea"==d?(h._wrapTextarea(),r=h.wrapper.find(".k-editor-toolbar"),c.id&&r.attr("aria-controls",c.id)):(h.element.attr("contenteditable",!0).addClass("k-widget k-editor k-editor-inline"),s.popup=!0,r=e('<ul class="k-editor-toolbar" role="toolbar" />').insertBefore(n)),h.toolbar=new m.Toolbar(r[0],s),h.toolbar.bindTo(h),"textarea"==d&&setTimeout(function(){var e=h.wrapper[0].style.height,t=parseInt(e,10),n=h.wrapper.height();e.indexOf("px")>0&&!isNaN(t)&&n>t&&h.wrapper.height(t-(n-t))}),h._resizable(),h._initializeContentElement(h),h.keyboard=new m.Keyboard([new m.BackspaceHandler(h),new m.TypingHandler(h),new m.SystemHandler(h)]),h.clipboard=new m.Clipboard(this),h.undoRedoStack=new a.util.UndoRedoStack,i&&i.value?o=i.value:h.textarea?(o=c.value,h.options.encoded&&e.trim(c.defaultValue).length&&(o=c.defaultValue),o=o.replace(/[\r\n\v\f\t ]+/gi," ")):o=c.innerHTML,h.value(o||a.ui.editor.emptyElementContent),this._registerHandler(document,{mousedown:function(){h._endTyping()},mouseup:function(){h._mouseup()}}),h.toolbar.resize(),a.notify(h))},setOptions:function(e){var t=this;l.fn.setOptions.call(t,e),e.tools&&t.toolbar.bindTo(t)},_endTyping:function(){var e=this.keyboard;try{e.isTypingInProgress()&&(e.endTyping(!0),this.saveSelection())}catch(t){}},_selectionChange:function(){this._selectionStarted=!1,this.saveSelection(),this.trigger("select",{})},_resizable:function(){var n=this.options.resizable,i=e.isPlainObject(n)?n.content===t||n.content===!0:n;i&&this.textarea&&(e("<div class='k-resize-handle'><span class='k-icon k-resize-se' /></div>").insertAfter(this.textarea),this.wrapper.kendoResizable(u({},this.options.resizable,{start:function(t){var n=this.editor=e(t.currentTarget).closest(".k-editor");this.initialSize=n.height(),n.find("td:last").append("<div class='k-overlay' />")},resize:function(e){var t=e.y.initialDelta,n=this.initialSize+t,i=this.options.min||0,o=this.options.max||1/0;n=Math.min(o,Math.max(i,n)),this.editor.height(n)},resizeend:function(){this.editor.find(".k-overlay").remove(),this.editor=null}})))},_wrapTextarea:function(){var t=this,n=t.element,i=n[0].style.width,o=n[0].style.height,r=g.editorWrapperTemplate,a=e(r).insertBefore(n).width(i).height(o),s=a.find(".k-editable-area");n.attr("autocomplete","off").appendTo(s).addClass("k-content k-raw-content").css("display","none"),t.textarea=n,t.wrapper=a},_createContentElement:function(t){var n,i,o,r=this,s=r.textarea,l=r.options.domain,d=l||document.domain,c="",u='javascript:""';return(l||d!=location.hostname)&&(c='<script>document.domain="'+d+'"</script>',u="javascript:document.write('"+c+"')"),s.hide(),n=e("<iframe />",{title:r.options.messages.editAreaTitle,frameBorder:"0"})[0],e(n).css("display","").addClass("k-content").attr("tabindex",s[0].tabIndex).insertBefore(s),n.src=u,i=n.contentWindow||n,o=i.document||n.contentDocument,e(n).one("load",function(){r.toolbar.decorateFrom(o.body)}),o.open(),o.write("<!DOCTYPE html><html><head><meta charset='utf-8' /><style>html,body{padding:0;margin:0;height:100%;min-height:100%;}body{font-size:12px;font-family:Verdana,Geneva,sans-serif;margin-top:-1px;padding:1px .2em 0;word-wrap: break-word;-webkit-nbsp-mode: space;-webkit-line-break: after-white-space;"+(a.support.isRtl(s)?"direction:rtl;":"")+"}h1{font-size:2em;margin:.67em 0}h2{font-size:1.5em}h3{font-size:1.16em}h4{font-size:1em}h5{font-size:.83em}h6{font-size:.7em}p{margin:0 0 1em;}.k-marker{display:none;}.k-paste-container,.Apple-style-span{position:absolute;left:-10000px;width:1px;height:1px;overflow:hidden}ul,ol{padding-left:2.5em}span{-ms-high-contrast-adjust:none;}a{color:#00a}code{font-size:1.23em}telerik\\3Ascript{display: none;}.k-table{table-layout:fixed;width:100%;border-spacing:0;margin: 0 0 1em;}.k-table td{min-width:1px;padding:.2em .3em;}.k-table,.k-table td{outline:0;border: 1px dotted #ccc;}.k-table p{margin:0;padding:0;}k\\:script{display:none;}</style>"+c+"<script>(function(d,c){d[c]('header'),d[c]('article'),d[c]('nav'),d[c]('section'),d[c]('footer');})(document, 'createElement');</script>"+e.map(t,function(e){return"<link rel='stylesheet' href='"+e+"'>"}).join("")+"</head><body autocorrect='off' contenteditable='true'></body></html>"),o.close(),i},_blur:function(){var e=this.textarea,t=e?e.val():this._oldValue,n=this.options.encoded?this.encodedValue():this.value();this.update(),e&&e.trigger("blur"),n!=t&&this.trigger("change")},_spellCorrect:function(e){var n,i=!1;this._registerHandler(e.body,{contextmenu:function(){e.one("select",function(){n=null}),e._spellCorrectTimeout=setTimeout(function(){n=new a.ui.editor.RestorePoint(e.getRange()),i=!1},10)},input:function(){if(n)return a.support.browser.mozilla&&!i?(i=!0,t):(a.ui.editor._finishUpdate(e,n),t)}})},_registerHandler:function(t,n,i){var o,r=".kendoEditor";if(t=e(t),this._handlers||(this._handlers=[]),t.length)if(e.isPlainObject(n))for(o in n)n.hasOwnProperty(o)&&this._registerHandler(t,o,n[o]);else n=n.split(" ").join(r+" ")+r,this._handlers.push({element:t,type:n,handler:i}),t.on(n,i)},_deregisterHandlers:function(){var e,t,n=this._handlers;for(e=0;e<n.length;e++)t=n[e],t.element.off(t.type,t.handler);this._handlers=[]},_initializeContentElement:function(){var n,i,o=this;o.textarea?(o.window=o._createContentElement(o.options.stylesheets),n=o.document=o.window.contentDocument||o.window.document,o.body=n.body,i=o.window,this._registerHandler(n,"mouseup",f(this._mouseup,this))):(o.window=window,n=o.document=document,o.body=o.element[0],i=o.body,o.toolbar.decorateFrom(o.body)),this._registerHandler(i,"blur",f(this._blur,this));try{n.execCommand("enableInlineTableEditing",null,!1)}catch(r){}a.support.touch&&this._registerHandler(n,{selectionchange:f(this._selectionChange,this),keydown:function(){a._activeElement()!=n.body&&o.window.focus()}}),this._spellCorrect(o),this._registerHandler(o.body,{dragstart:function(e){e.preventDefault()},keydown:function(e){var n,i,r,a,s,l,d,c;return(e.keyCode!==h.BACKSPACE&&e.keyCode!==h.DELETE||"true"===o.body.getAttribute("contenteditable"))&&(e.keyCode===h.F10?(setTimeout(f(o.toolbar.focus,o.toolbar),100),e.preventDefault(),t):(e.keyCode!=h.LEFT&&e.keyCode!=h.RIGHT||(n=o.getRange(),i=e.keyCode==h.LEFT,r=n[i?"startContainer":"endContainer"],a=n[i?"startOffset":"endOffset"],s=i?-1:1,i&&(a-=1),a+s>0&&3==r.nodeType&&"\ufeff"==r.nodeValue[a]&&(n.setStart(r,a+s),n.collapse(!0),o.selectRange(n))),l=o.toolbar.tools,d=o.keyboard.toolFromShortcut(l,e),c=d?l[d].options:{},d&&!c.keyPressCommand?(e.preventDefault(),/^(undo|redo)$/.test(d)||o.keyboard.endTyping(!0),o.trigger("keydown",e),o.exec(d),o._runPostContentKeyCommands(e),!1):(o.keyboard.clearTimeout(),o.keyboard.keydown(e),t)))},keypress:function(e){setTimeout(function(){o._runPostContentKeyCommands(e)},0)},keyup:function(t){var n=[8,9,33,34,35,36,37,38,39,40,40,45,46];(e.inArray(t.keyCode,n)>-1||65==t.keyCode&&t.ctrlKey&&!t.altKey&&!t.shiftKey)&&o._selectionChange(),o.keyboard.keyup(t)},mousedown:function(t){if(o._selectionStarted=!0,!c.gecko){var n=e(t.target);(2==t.which||1==t.which&&t.ctrlKey)&&n.is("a[href]")&&window.open(n.attr("href"),"_new")}},click:function(e){var t,n=a.ui.editor.Dom;"img"===n.name(e.target)&&(t=o.createRange(),t.selectNode(e.target),o.selectRange(t))},"cut copy paste":function(e){o.clipboard["on"+e.type](e)},focusin:function(){o.body.hasAttribute("contenteditable")&&(e(this).addClass("k-state-active"),o.toolbar.show())},focusout:function(){setTimeout(function(){var t=a._activeElement(),n=o.body,i=o.toolbar;t==n||e.contains(n,t)||e(t).is(".k-editortoolbar-dragHandle")||i.focused()||(e(n).removeClass("k-state-active"),i.hide())},10)}})},_mouseup:function(){var e=this;e._selectionStarted&&setTimeout(function(){e._selectionChange()},1)},_runPostContentKeyCommands:function(e){var t,n,i,o,r=this.getRange(),a=this.keyboard.toolsFromShortcut(this.toolbar.tools,e);for(t=0;t<a.length;t++)n=a[t],i=n.options,i.keyPressCommand&&(o=new i.command({range:r}),o.changesContent()&&(this.keyboard.endTyping(!0),this.exec(n.name)))},refresh:function(){var e=this;e.textarea&&(e.textarea.val(e.value()),e.wrapper.find("iframe").remove(),e._initializeContentElement(e),e.value(e.textarea.val()))},events:["select","change","execute","error","paste","keydown","keyup"],options:{name:"Editor",messages:b,formats:{},encoded:!0,domain:null,resizable:!1,deserialization:{custom:null},serialization:{entities:!0,semantic:!0,scripts:!1},pasteCleanup:{all:!1,css:!1,custom:null,keepNewLines:!1,msAllFormatting:!1,msConvertLists:!0,msTags:!0,none:!1,span:!1},stylesheets:[],dialogOptions:{modal:!0,resizable:!1,draggable:!0,animation:!1},imageBrowser:null,fileBrowser:null,fontName:[{text:"Arial",value:"Arial,Helvetica,sans-serif"},{text:"Courier New",value:"'Courier New',Courier,monospace"},{text:"Georgia",value:"Georgia,serif"},{text:"Impact",value:"Impact,Charcoal,sans-serif"},{text:"Lucida Console",value:"'Lucida Console',Monaco,monospace"},{text:"Tahoma",value:"Tahoma,Geneva,sans-serif"},{text:"Times New Roman",value:"'Times New Roman',Times,serif"},{text:"Trebuchet MS",value:"'Trebuchet MS',Helvetica,sans-serif"},{text:"Verdana",value:"Verdana,Geneva,sans-serif"}],fontSize:[{text:"1 (8pt)",value:"xx-small"},{text:"2 (10pt)",value:"x-small"},{text:"3 (12pt)",value:"small"},{text:"4 (14pt)",value:"medium"},{text:"5 (18pt)",value:"large"},{text:"6 (24pt)",value:"x-large"},{text:"7 (36pt)",value:"xx-large"}],formatBlock:[{text:"Paragraph",value:"p"},{text:"Quotation",value:"blockquote"},{text:"Heading 1",value:"h1"},{text:"Heading 2",value:"h2"},{text:"Heading 3",value:"h3"},{text:"Heading 4",value:"h4"},{text:"Heading 5",value:"h5"},{text:"Heading 6",value:"h6"}],tools:[].concat.call(["formatting"],k.basic,k.alignment,k.lists,k.indenting,k.links,["insertImage"],k.tables)},destroy:function(){l.fn.destroy.call(this),this._endTyping(!0),this._deregisterHandlers(),clearTimeout(this._spellCorrectTimeout),this._focusOutside(),this.toolbar.destroy(),a.destroy(this.wrapper)},_focusOutside:function(){if(a.support.browser.msie&&this.textarea){var t=e("<input style='position:fixed;left:1px;top:1px;width:1px;height:1px;font-size:0;border:0;opacity:0' />").appendTo(document.body).focus();t.blur().remove()}},state:function(e){var t,n,i=y.defaultTools[e],o=i&&(i.options.finder||i.finder),r=a.ui.editor.RangeUtils;return!!o&&(t=this.getRange(),n=r.textNodes(t),!n.length&&t.collapsed&&(n=[t.startContainer]),o.getFormat?o.getFormat(n):o.isFormatted(n))},value:function(e){var n=this.body,i=a.ui.editor,o=this.options,r=i.Serializer.domToXhtml(n,o.serialization);return e===t?r:(e!=r&&(i.Serializer.htmlToDom(e,n,o.deserialization),this.selectionRestorePoint=null,this.update(),this.toolbar.refreshTools()),t)},saveSelection:function(t){t=t||this.getRange();var n=t.commonAncestorContainer,i=this.body;(n==i||e.contains(i,n))&&(this.selectionRestorePoint=new a.ui.editor.RestorePoint(t))},_focusBody:function(){var e,t=this.body,n=this.wrapper&&this.wrapper.find("iframe")[0],i=this.document.documentElement,o=a._activeElement();o!=t&&o!=n&&(e=i.scrollTop,t.focus(),i.scrollTop=e)},restoreSelection:function(){this._focusBody(),this.selectionRestorePoint&&this.selectRange(this.selectionRestorePoint.toRange())},focus:function(){this.restoreSelection()},update:function(e){e=e||this.options.encoded?this.encodedValue():this.value(),this.textarea?this.textarea.val(e):this._oldValue=e},encodedValue:function(){return a.ui.editor.Dom.encode(this.value())},createRange:function(e){return a.ui.editor.RangeUtils.createRange(e||this.document)},getSelection:function(){return a.ui.editor.SelectionUtils.selectionFromDocument(this.document)},selectRange:function(e){this._focusBody();var t=this.getSelection();t.removeAllRanges(),t.addRange(e),this.saveSelection(e)},getRange:function(){var e=this.getSelection(),t=e&&e.rangeCount>0?e.getRangeAt(0):this.createRange(),n=this.document;return t.startContainer!=n||t.endContainer!=n||t.startOffset||t.endOffset||(t.setStart(this.body,0),t.collapse(!0)),t},selectedHtml:function(){return a.ui.editor.Serializer.domToXhtml(this.getRange().cloneContents())},paste:function(t,n){this.focus();var i=new a.ui.editor.InsertHtmlCommand(e.extend({range:this.getRange(),html:t},n));i.editor=this,i.exec()},exec:function(e,n){var i,o,r,a,s=this,l=null;if(!e)throw Error("kendoEditor.exec(): `name` parameter cannot be empty");if("true"!==s.body.getAttribute("contenteditable")&&"print"!==e&&"pdf"!==e)return!1;if(e=e.toLowerCase(),s.keyboard.isTypingInProgress()||s.restoreSelection(),o=s.toolbar.toolById(e),!o)for(a in y.defaultTools)if(a.toLowerCase()==e){o=y.defaultTools[a];break}if(o){if(i=s.getRange(),o.command&&(l=o.command(u({range:i},n))),r=s.trigger("execute",{name:e,command:l}))return;if(/^(undo|redo)$/i.test(e))s.undoRedoStack[e]();else if(l&&(l.managesUndoRedo||s.undoRedoStack.push(l),l.editor=s,l.exec(),l.async))return l.change=f(s._selectionChange,s),t;s._selectionChange()}}});y.defaultTools={undo:{options:{key:"Z",ctrl:!0}},redo:{options:{key:"Y",ctrl:!0}}},a.ui.plugin(y),n=s.extend({init:function(e){this.options=e},initialize:function(e,t){e.attr({unselectable:"on",title:t.title}),e.children(".k-tool-text").html(t.title)},command:function(e){return new this.options.command(e)},update:e.noop}),n.exec=function(e,t,n){e.exec(t,{value:n})},i=n.extend({init:function(e){n.fn.init.call(this,e)},command:function(e){var t=this;return new a.ui.editor.FormatCommand(u(e,{formatter:t.options.formatter}))},update:function(e,t){var n=this.options.finder.isFormatted(t);e.toggleClass("k-state-selected",n),e.attr("aria-pressed",n)}}),g.registerTool("separator",new n({template:new m({template:g.separatorTemplate})})),o=c.msie&&c.version<9?"\ufeff":"",r="\ufeff",c.msie&&10==c.version&&(r=" "),u(a.ui,{editor:{ToolTemplate:m,EditorUtils:g,Tool:n,FormatTool:i,_bomFill:o,emptyElementContent:r}}),a.PDFMixin&&(a.PDFMixin.extend(y.prototype),y.prototype._drawPDF=function(){return a.drawing.drawDOM(this.body,this.options.pdf)},y.prototype.saveAsPDF=function(){var t,n=new e.Deferred,i=n.promise(),o={promise:i};if(!this.trigger("pdfExport",o))return t=this.options.pdf,this._drawPDF(n).then(function(e){return a.drawing.exportPDF(e,t)}).done(function(e){a.saveAs({dataURI:e,fileName:t.fileName,proxyURL:t.proxyURL,forceProxy:t.forceProxy}),n.resolve()}).fail(function(e){n.reject(e)}),i})}(window.jQuery||window.kendo.jQuery)},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("editor/dom.min",["editor/main.min"],e)}(function(){!function(e){function t(e){var t,n,i={};for(t=0,n=e.length;t<n;t++)i[e[t]]=!0;return i}var n,i,o,r,a,s,l,d,c,u,f,p=window.kendo,h=e.map,m=e.extend,g=p.support.browser,b="style",v="float",k="cssFloat",y="styleFloat",C="class",w="k-marker",x=t("area,base,basefont,br,col,frame,hr,img,input,isindex,link,meta,param,embed".split(",")),T="div,p,h1,h2,h3,h4,h5,h6,address,applet,blockquote,button,center,dd,dir,dl,dt,fieldset,form,frameset,hr,iframe,isindex,map,menu,noframes,noscript,object,pre,script,table,tbody,td,tfoot,th,thead,tr,header,article,nav,footer,section,aside,main,figure,figcaption".split(","),_=T.concat(["ul","ol","li"]),F=t(_),S="span,em,a,abbr,acronym,applet,b,basefont,bdo,big,br,button,cite,code,del,dfn,font,i,iframe,img,input,ins,kbd,label,map,object,q,s,samp,script,select,small,strike,strong,sub,sup,textarea,tt,u,var,data,time,mark,ruby".split(","),N=t(S),A=t("checked,compact,declare,defer,disabled,ismap,multiple,nohref,noresize,noshade,nowrap,readonly,selected".split(",")),R=function(e){1==e.nodeType&&e.normalize()};g.msie&&g.version>=8&&(R=function(e){if(1==e.nodeType&&e.firstChild)for(var t=e.firstChild,n=t;;){if(n=n.nextSibling,!n)break;3==n.nodeType&&3==t.nodeType&&(n.nodeValue=t.nodeValue+n.nodeValue,f.remove(t)),t=n}}),n=/^\s+$/,i=/^[\n\r\t]+$/,o=/rgb\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*\)/i,r=/\ufeff/g,a=/^(\s+|\ufeff)$/,l="color,padding-left,padding-right,padding-top,padding-bottom,background-color,background-attachment,background-image,background-position,background-repeat,border-top-style,border-top-width,border-top-color,border-bottom-style,border-bottom-width,border-bottom-color,border-left-style,border-left-width,border-left-color,border-right-style,border-right-width,border-right-color,font-family,font-size,font-style,font-variant,font-weight,line-height".split(","),d=/[<>\&]/g,c=/[\u00A0-\u2666<>\&]/g,u={34:"quot",38:"amp",39:"apos",60:"lt",62:"gt",160:"nbsp",161:"iexcl",162:"cent",163:"pound",164:"curren",165:"yen",166:"brvbar",167:"sect",168:"uml",169:"copy",170:"ordf",171:"laquo",172:"not",173:"shy",174:"reg",175:"macr",176:"deg",177:"plusmn",178:"sup2",179:"sup3",180:"acute",181:"micro",182:"para",183:"middot",184:"cedil",185:"sup1",186:"ordm",187:"raquo",188:"frac14",189:"frac12",190:"frac34",191:"iquest",192:"Agrave",193:"Aacute",194:"Acirc",195:"Atilde",196:"Auml",197:"Aring",198:"AElig",199:"Ccedil",200:"Egrave",201:"Eacute",202:"Ecirc",203:"Euml",204:"Igrave",205:"Iacute",206:"Icirc",207:"Iuml",208:"ETH",209:"Ntilde",210:"Ograve",211:"Oacute",212:"Ocirc",213:"Otilde",214:"Ouml",215:"times",216:"Oslash",217:"Ugrave",218:"Uacute",219:"Ucirc",220:"Uuml",221:"Yacute",222:"THORN",223:"szlig",224:"agrave",225:"aacute",226:"acirc",227:"atilde",228:"auml",229:"aring",230:"aelig",231:"ccedil",232:"egrave",233:"eacute",234:"ecirc",235:"euml",236:"igrave",237:"iacute",238:"icirc",239:"iuml",240:"eth",241:"ntilde",242:"ograve",243:"oacute",244:"ocirc",245:"otilde",246:"ouml",247:"divide",248:"oslash",249:"ugrave",250:"uacute",251:"ucirc",252:"uuml",253:"yacute",254:"thorn",255:"yuml",402:"fnof",913:"Alpha",914:"Beta",915:"Gamma",916:"Delta",917:"Epsilon",918:"Zeta",919:"Eta",920:"Theta",921:"Iota",922:"Kappa",923:"Lambda",924:"Mu",925:"Nu",926:"Xi",927:"Omicron",928:"Pi",929:"Rho",931:"Sigma",932:"Tau",933:"Upsilon",934:"Phi",935:"Chi",936:"Psi",937:"Omega",945:"alpha",946:"beta",947:"gamma",948:"delta",949:"epsilon",950:"zeta",951:"eta",952:"theta",953:"iota",954:"kappa",955:"lambda",956:"mu",957:"nu",958:"xi",959:"omicron",960:"pi",961:"rho",962:"sigmaf",963:"sigma",964:"tau",965:"upsilon",966:"phi",967:"chi",968:"psi",969:"omega",977:"thetasym",978:"upsih",982:"piv",8226:"bull",8230:"hellip",8242:"prime",8243:"Prime",8254:"oline",8260:"frasl",8472:"weierp",8465:"image",8476:"real",8482:"trade",8501:"alefsym",8592:"larr",8593:"uarr",8594:"rarr",8595:"darr",8596:"harr",8629:"crarr",8656:"lArr",8657:"uArr",8658:"rArr",8659:"dArr",8660:"hArr",8704:"forall",8706:"part",8707:"exist",8709:"empty",8711:"nabla",8712:"isin",8713:"notin",8715:"ni",8719:"prod",8721:"sum",8722:"minus",8727:"lowast",8730:"radic",8733:"prop",8734:"infin",8736:"ang",8743:"and",8744:"or",8745:"cap",8746:"cup",8747:"int",8756:"there4",8764:"sim",8773:"cong",8776:"asymp",8800:"ne",8801:"equiv",8804:"le",8805:"ge",8834:"sub",8835:"sup",8836:"nsub",8838:"sube",8839:"supe",8853:"oplus",8855:"otimes",8869:"perp",8901:"sdot",8968:"lceil",8969:"rceil",8970:"lfloor",8971:"rfloor",9001:"lang",9002:"rang",9674:"loz",9824:"spades",9827:"clubs",9829:"hearts",9830:"diams",338:"OElig",339:"oelig",352:"Scaron",353:"scaron",376:"Yuml",710:"circ",732:"tilde",8194:"ensp",8195:"emsp",8201:"thinsp",8204:"zwnj",8205:"zwj",8206:"lrm",8207:"rlm",8211:"ndash",8212:"mdash",8216:"lsquo",8217:"rsquo",8218:"sbquo",8220:"ldquo",8221:"rdquo",8222:"bdquo",8224:"dagger",8225:"Dagger",8240:"permil",8249:"lsaquo",8250:"rsaquo",8364:"euro"},f={block:F,inline:N,findNodeIndex:function(e,t){var n=0;if(!e)return-1;for(;;){if(e=e.previousSibling,!e)break;t&&3==e.nodeType||n++}return n},isDataNode:function(e){return e&&null!==e.nodeValue&&null!==e.data},isAncestorOf:function(t,n){try{return!f.isDataNode(t)&&(e.contains(t,f.isDataNode(n)?n.parentNode:n)||n.parentNode==t)}catch(i){return!1}},isAncestorOrSelf:function(e,t){return f.isAncestorOf(e,t)||e==t},findClosestAncestor:function(e,t){if(f.isAncestorOf(e,t))for(;t&&t.parentNode!=e;)t=t.parentNode;return t},getNodeLength:function(e){return f.isDataNode(e)?e.length:e.childNodes.length},splitDataNode:function(e,t){for(var n,i=e.cloneNode(!1),o="",r=e.nextSibling;r&&3==r.nodeType&&r.nodeValue;)o+=r.nodeValue,n=r,r=r.nextSibling,f.remove(n);e.deleteData(t,e.length),i.deleteData(0,t),i.nodeValue+=o,f.insertAfter(i,e)},attrEquals:function(e,t){var n,i;for(n in t)if(i=e[n],n==v&&(i=e[p.support.cssFloat?k:y]),"object"==typeof i){if(!f.attrEquals(i,t[n]))return!1}else if(i!=t[n])return!1;return!0},blockParentOrBody:function(e){return f.parentOfType(e,_)||e.ownerDocument.body},blockParents:function(t){var n,i,o,r=[];for(n=0,i=t.length;n<i;n++)o=f.parentOfType(t[n],f.blockElements),o&&e.inArray(o,r)<0&&r.push(o);return r},windowFromDocument:function(e){return e.defaultView||e.parentWindow},normalize:R,blockElements:_,nonListBlockElements:T,inlineElements:S,empty:x,fillAttrs:A,toHex:function(e){var t=o.exec(e);return t?"#"+h(t.slice(1),function(e){return e=parseInt(e,10).toString(16),e.length>1?e:"0"+e}).join(""):e},encode:function(e,t){var n=!t||t.entities?c:d;return e.replace(n,function(e){var t=e.charCodeAt(0),n=u[t];return n?"&"+n+";":e})},stripBom:function(e){return(e||"").replace(r,"")},stripBomNode:function(e){e&&3===e.nodeType&&"\ufeff"===e.nodeValue&&e.parentNode.removeChild(e)},insignificant:function(e){var t=e.attributes;return"k-marker"==e.className||f.is(e,"br")&&("k-br"==e.className||t._moz_dirty||t._moz_editor_bogus_node)},significantNodes:function(t){return e.grep(t,function(e){var t=f.name(e);return"br"!=t&&(!f.insignificant(e)&&((3!=e.nodeType||!a.test(e.nodeValue))&&!(1==e.nodeType&&!x[t]&&f.emptyNode(e))))})},emptyNode:function(e){return 1==e.nodeType&&!f.significantNodes(e.childNodes).length},name:function(e){return e.nodeName.toLowerCase()},significantChildNodes:function(t){return e.grep(t.childNodes,function(e){return 3!=e.nodeType||!f.isWhitespace(e)})},lastTextNode:function(e){var t,n=null;if(3==e.nodeType)return e;for(t=e.lastChild;t;t=t.previousSibling)if(n=f.lastTextNode(t))return n;return n},is:function(e,t){return f.name(e)==t},isMarker:function(e){return e.className==w},isWhitespace:function(e){return n.test(e.nodeValue)},isEmptyspace:function(e){return i.test(e.nodeValue)},isBlock:function(e){return F[f.name(e)]},isEmpty:function(e){return x[f.name(e)]},isInline:function(e){return N[f.name(e)]},scrollContainer:function(e){var t=f.windowFromDocument(e),n=(t.contentWindow||t).document||t.ownerDocument||t;return n=p.support.browser.webkit||"BackCompat"==n.compatMode?n.body:n.documentElement},scrollTo:function(t){var n,i,o=e(f.isDataNode(t)?t.parentNode:t),r=f.windowFromDocument(t.ownerDocument),a=r.innerHeight,s=f.scrollContainer(t.ownerDocument);n=o.offset().top,i=o[0].offsetHeight,i||(i=parseInt(o.css("line-height"),10)||Math.ceil(1.2*parseInt(o.css("font-size"),10))||15),i+n>s.scrollTop+a&&(s.scrollTop=i+n-a)},persistScrollTop:function(e){s=f.scrollContainer(e).scrollTop},restoreScrollTop:function(e){f.scrollContainer(e).scrollTop=s},insertAt:function(e,t,n){e.insertBefore(t,e.childNodes[n]||null)},insertBefore:function(e,t){return t.parentNode?t.parentNode.insertBefore(e,t):t},insertAfter:function(e,t){return t.parentNode.insertBefore(e,t.nextSibling)},remove:function(e){e.parentNode.removeChild(e)},removeTextSiblings:function(e){for(var t=e.parentNode;e.nextSibling&&3==e.nextSibling.nodeType;)t.removeChild(e.nextSibling);for(;e.previousSibling&&3==e.previousSibling.nodeType;)t.removeChild(e.previousSibling)},trim:function(e){var t,n;for(t=e.childNodes.length-1;t>=0;t--)n=e.childNodes[t],f.isDataNode(n)?(f.stripBom(n.nodeValue).length||f.remove(n),f.isWhitespace(n)&&f.insertBefore(n,e)):n.className!=w&&(f.trim(n),n.childNodes.length||f.isEmpty(n)||f.remove(n));return e},closest:function(e,t){for(;e&&f.name(e)!=t;)e=e.parentNode;return e},sibling:function(e,t){do e=e[t];while(e&&1!=e.nodeType);return e},next:function(e){return f.sibling(e,"nextSibling")},prev:function(e){return f.sibling(e,"previousSibling")},parentOfType:function(e,t){do e=e.parentNode;while(e&&!f.ofType(e,t));return e},ofType:function(t,n){return e.inArray(f.name(t),n)>=0},changeTag:function(e,t,n){var i,o,r,a,s,l=f.create(e.ownerDocument,t),d=e.attributes;if(!n)for(i=0,o=d.length;i<o;i++)s=d[i],s.specified&&(r=s.nodeName,a=s.nodeValue,r==C?l.className=a:r==b?l.style.cssText=e.style.cssText:l.setAttribute(r,a));for(;e.firstChild;)l.appendChild(e.firstChild);return f.insertBefore(l,e),f.remove(e),l},editableParent:function(e){for(;e&&(3==e.nodeType||"true"!==e.contentEditable);)e=e.parentNode;return e},wrap:function(e,t){return f.insertBefore(t,e),t.appendChild(e),t},unwrap:function(e){for(var t=e.parentNode;e.firstChild;)t.insertBefore(e.firstChild,e);t.removeChild(e)},create:function(e,t,n){return f.attr(e.createElement(t),n)},attr:function(e,t){t=m({},t),t&&b in t&&(f.style(e,t.style),delete t.style);for(var n in t)null===t[n]?(e.removeAttribute(n),delete t[n]):"className"==n&&(e[n]=t[n]);return m(e,t)},style:function(t,n){e(t).css(n||{})},unstyle:function(e,t){for(var n in t)n==v&&(n=p.support.cssFloat?k:y),e.style[n]="";""===e.style.cssText&&e.removeAttribute(b)},inlineStyle:function(t,n,i){var o,r=e(f.create(t.ownerDocument,n,i));return t.appendChild(r[0]),o=h(l,function(e){return g.msie&&"line-height"==e&&"1px"==r.css(e)?"line-height:1.5":e+":"+r.css(e)}).join(";"),r.remove(),o},getEffectiveBackground:function(e){var t=e.css("background-color");return t.indexOf("rgba(0, 0, 0, 0")<0&&"transparent"!==t?t:"html"===e[0].tagName.toLowerCase()?"Window":f.getEffectiveBackground(e.parent())},innerText:function(e){var t=e.innerHTML;return t=t.replace(/<!--(.|\s)*?-->/gi,""),t=t.replace(/<\/?[^>]+?\/?>/gm,"")},removeClass:function(t,n){var i,o,r=" "+t.className+" ",a=n.split(" ");for(i=0,o=a.length;i<o;i++)r=r.replace(" "+a[i]+" "," ");r=e.trim(r),r.length?t.className=r:t.removeAttribute(C)},commonAncestor:function(){var e,t,n,i,o,r=arguments.length,a=[],s=1/0,l=null;if(!r)return null;if(1==r)return arguments[0];for(e=0;e<r;e++){for(t=[],n=arguments[e];n;)t.push(n),n=n.parentNode;a.push(t.reverse()),s=Math.min(s,t.length)}if(1==r)return a[0][0];for(e=0;e<s;e++){for(i=a[0][e],o=1;o<r;o++)if(i!=a[o][e])return l;l=i}return l},closestSplittableParent:function(t){var n,i,o;return n=1==t.length?f.parentOfType(t[0],["ul","ol"]):f.commonAncestor.apply(null,t),n||(n=f.parentOfType(t[0],["p","td"])||t[0].ownerDocument.body),f.isInline(n)&&(n=f.blockParentOrBody(n)),i=h(t,f.editableParent),o=f.commonAncestor(i)[0],e.contains(n,o)&&(n=o),n},closestEditable:function(t,n){var i,o=f.editableParent(t);return i=f.ofType(t,n)?t:f.parentOfType(t,n),i&&o&&e.contains(i,o)?i=o:!i&&o&&(i=o),i},closestEditableOfType:function(e,t){var n=f.closestEditable(e,t);if(n&&f.ofType(n,t))return n},filter:function(e,t,n){for(var i,o=0,r=t.length,a=[];o<r;o++)i=f.name(t[o]),(!n&&i==e||n&&i!=e)&&a.push(t[o]);
return a},ensureTrailingBreaks:function(t){var n=e(t).find("p,td,th"),i=n.length,o=0;if(i)for(;o<i;o++)f.ensureTrailingBreak(n[o]);else f.ensureTrailingBreak(t)},removeTrailingBreak:function(t){e(t).find("br[type=_moz],.k-br").remove()},ensureTrailingBreak:function(e){var t,n,i;f.removeTrailingBreak(e),t=e.lastChild,n=t&&f.name(t),(!n||"br"!=n&&"img"!=n||"br"==n&&"k-br"!=t.className)&&(i=e.ownerDocument.createElement("br"),i.className="k-br",e.appendChild(i))}},p.ui.editor.Dom=f}(window.kendo.jQuery)},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("editor/serializer.min",["editor/dom.min"],e)}(function(){!function(e,t){var n,i,o,r=window.kendo,a=r.ui.editor,s=a.Dom,l=e.extend,d="xx-small,x-small,small,medium,large,x-large,xx-large".split(","),c=/"/g,u=/<br[^>]*>/i,f=/^\d+(\.\d*)?(px)?$/i,p=/<p>(?:&nbsp;)?<\/p>/i,h=/(\*?[-#\/\*\\\w]+(?:\[[0-9a-z_-]+\])?)\s*:\s*((?:'(?:\\'|.)*?'|"(?:\\"|.)*?"|\([^\)]*?\)|[^};])+)/g,m=/^sizzle-\d+/i,g=/^k-script-/i,b=/\s*onerror\s*=\s*(?:'|")?([^'">\s]*)(?:'|")?/i,v=document.createElement("div");v.innerHTML=" <hr>",n=3===v.firstChild.nodeType,v=null,i=e.isFunction,o={toEditableHtml:function(e){var t='<br class="k-br">';return e=e||"",e.replace(/<!\[CDATA\[(.*)?\]\]>/g,"<!--[CDATA[$1]]-->").replace(/<(\/?)script([^>]*)>/gi,"<$1k:script$2>").replace(/<img([^>]*)>/gi,function(e){return e.replace(b,"")}).replace(/(<\/?img[^>]*>)[\r\n\v\f\t ]+/gi,"$1").replace(/^<(table|blockquote)/i,t+"<$1").replace(/^[\s]*(&nbsp;|\u00a0)/i,"$1").replace(/<\/(table|blockquote)>$/i,"</$1>"+t)},_fillEmptyElements:function(t){e(t).find("p,td").each(function(){var t,n=e(this);if(/^\s*$/g.test(n.text())&&!n.find("img,input").length){for(t=this;t.firstChild&&3!=t.firstChild.nodeType;)t=t.firstChild;1!=t.nodeType||s.empty[s.name(t)]||(t.innerHTML=r.ui.editor.emptyElementContent)}})},_removeSystemElements:function(t){e(".k-paste-container",t).remove()},_resetOrderedLists:function(e){var t,n,i,o=e.getElementsByTagName("ol");for(t=0;t<o.length;t++)n=o[t],i=n.getAttribute("start"),n.setAttribute("start",1),i?n.setAttribute("start",i):n.removeAttribute(i)},_preventScriptExecution:function(t){e(t).find("*").each(function(){var e,t,n,i,o=this.attributes;for(t=0,n=o.length;t<n;t++)e=o[t],i=e.nodeName,e.specified&&/^on/i.test(i)&&(this.setAttribute("k-script-"+i,e.value),this.removeAttribute(i))})},htmlToDom:function(t,n,a){var l=r.support.browser,d=l.msie,c=d&&l.version<9,u="originalsrc",f="originalhref",p=a||{};return t=o.toEditableHtml(t),c&&(t="<br/>"+t,t=t.replace(/href\s*=\s*(?:'|")?([^'">\s]*)(?:'|")?/,f+'="$1"'),t=t.replace(/src\s*=\s*(?:'|")?([^'">\s]*)(?:'|")?/,u+'="$1"')),i(p.custom)&&(t=p.custom(t)||t),n.innerHTML=t,c?(s.remove(n.firstChild),e(n).find("k\\:script,script,link,img,a").each(function(){var e=this;e[f]&&(e.setAttribute("href",e[f]),e.removeAttribute(f)),e[u]&&(e.setAttribute("src",e[u]),e.removeAttribute(u))})):d&&(s.normalize(n),o._resetOrderedLists(n)),o._preventScriptExecution(n),o._fillEmptyElements(n),o._removeSystemElements(n),e("table",n).addClass("k-table"),n},domToXhtml:function(i,o){function a(t){return e.grep(t,function(e){return"style"!=e.name})}function l(t){var n,i,o,r=e.trim,a=r(t),l=[];for(h.lastIndex=0;;){if(n=h.exec(a),!n)break;i=r(n[1].toLowerCase()),o=r(n[2]),"font-size-adjust"!=i&&"font-stretch"!=i&&(i.indexOf("color")>=0?o=s.toHex(o):i.indexOf("font")>=0?o=o.replace(c,"'"):/\burl\(/g.test(o)&&(o=o.replace(c,"")),l.push({property:i,value:o}))}return l}function b(e){var t,n=l(e);for(t=0;t<n.length;t++)F.push(n[t].property),F.push(":"),F.push(n[t].value),F.push(";")}function v(e){var t,n,i,r,a,l,d=[],c=e.attributes;for(n=0,i=c.length;n<i;n++)t=c[n],r=t.nodeName,a=t.value,l=t.specified,"value"==r&&"value"in e&&e.value?l=!0:"type"==r&&"text"==a?l=!0:"class"!=r||a?m.test(r)?l=!1:"complete"==r?l=!1:"altHtml"==r?l=!1:"start"==r&&s.is(e,"ul")?l=!1:"start"==r&&s.is(e,"ol")&&"1"==a?l=!1:r.indexOf("_moz")>=0?l=!1:g.test(r)&&(l=!!o.scripts):l=!1,l&&d.push(t);return d}function k(n,i){var o,a,l,d,c,u,p,h;if(i=i||v(n),s.is(n,"img")&&(u=n.style.width,p=n.style.height,h=e(n),u&&f.test(u)&&(h.attr("width",parseInt(u,10)),s.unstyle(n,{width:t})),p&&f.test(p)&&(h.attr("height",parseInt(p,10)),s.unstyle(n,{height:t}))),i.length)for(i.sort(function(e,t){return e.nodeName>t.nodeName?1:e.nodeName<t.nodeName?-1:0}),o=0,a=i.length;o<a;o++)l=i[o],d=l.nodeName,c=l.value,"class"==d&&"k-table"==c||(d=d.replace(g,""),F.push(" "),F.push(d),F.push('="'),"style"==d?b(c||n.style.cssText):F.push("src"==d||"href"==d?r.htmlEncode(n.getAttribute(d,2)):s.fillAttrs[d]?d:c),F.push('"'))}function y(e,t,n){for(var i=e.firstChild;i;i=i.nextSibling)x(i,t,n)}function C(e){return e.nodeValue.replace(/\ufeff/g,"")}function w(e){if("\ufeff"===e.nodeValue){do if(e=e.parentNode,s.is(e,"td")||1!==e.childNodes.length)return!1;while(!s.isBlock(e));return!0}return!1}function x(e,i,r){var a,l,d,c,u,f=e.nodeType;if(1==f){if(a=s.name(e),!a||s.insignificant(e))return;if(!o.scripts&&("script"==a||"k:script"==a))return;if(l=S[a],l&&(t===l.semantic||o.semantic^l.semantic))return l.start(e),y(e,!1,l.skipEncoding),l.end(e),t;F.push("<"),F.push(a),k(e),s.empty[a]?F.push(" />"):(F.push(">"),y(e,i||s.is(e,"pre")),F.push("</"),F.push(a),F.push(">"))}else if(3==f){if(w(e))return F.push("&nbsp;"),t;c=C(e),!i&&n&&(d=e.parentNode,u=e.previousSibling,u||(u=(s.isInline(d)?d:e).previousSibling),u&&""!==u.innerHTML&&!s.isBlock(u)||(c=c.replace(/^[\r\n\v\f\t ]+/,"")),c=c.replace(/ +/," ")),F.push(r?c:s.encode(c,o))}else 4==f?(F.push("<![CDATA["),F.push(e.data),F.push("]]>")):8==f&&(e.data.indexOf("[CDATA[")<0?(F.push("<!--"),F.push(e.data),F.push("-->")):(F.push("<!"),F.push(e.data),F.push(">")))}function T(e){var t=e.childNodes.length,n=t&&3==e.firstChild.nodeType;return n&&(1==t||2==t&&s.insignificant(e.lastChild))}function _(){e.isFunction(o.custom)&&(F=o.custom(F)||F)}var F=[],S={iframe:{start:function(e){F.push("<iframe"),k(e),F.push(">")},end:function(){F.push("</iframe>")}},"k:script":{start:function(e){F.push("<script"),k(e),F.push(">")},end:function(){F.push("</script>")},skipEncoding:!0},span:{semantic:!0,start:function(t){var n,i,o=t.style,r=v(t),l=a(r);l.length&&(F.push("<span"),k(t,l),F.push(">")),"underline"==o.textDecoration&&F.push("<u>"),n=[],o.color&&n.push('color="'+s.toHex(o.color)+'"'),o.fontFamily&&n.push('face="'+o.fontFamily+'"'),o.fontSize&&(i=e.inArray(o.fontSize,d),n.push('size="'+i+'"')),n.length&&F.push("<font "+n.join(" ")+">")},end:function(e){var t=e.style;(t.color||t.fontFamily||t.fontSize)&&F.push("</font>"),"underline"==t.textDecoration&&F.push("</u>"),a(v(e)).length&&F.push("</span>")}},strong:{semantic:!0,start:function(){F.push("<b>")},end:function(){F.push("</b>")}},em:{semantic:!0,start:function(){F.push("<i>")},end:function(){F.push("</i>")}},b:{semantic:!1,start:function(){F.push("<strong>")},end:function(){F.push("</strong>")}},i:{semantic:!1,start:function(){F.push("<em>")},end:function(){F.push("</em>")}},u:{semantic:!1,start:function(){F.push('<span style="text-decoration:underline;">')},end:function(){F.push("</span>")}},font:{semantic:!1,start:function(e){var t,n,i;F.push('<span style="'),t=e.getAttribute("color"),n=d[e.getAttribute("size")],i=e.getAttribute("face"),t&&(F.push("color:"),F.push(s.toHex(t)),F.push(";")),i&&(F.push("font-family:"),F.push(i),F.push(";")),n&&(F.push("font-size:"),F.push(n),F.push(";")),F.push('">')},end:function(){F.push("</span>")}}};return S.script=S["k:script"],o=o||{},t===o.semantic&&(o.semantic=!0),T(i)?(F=s.encode(C(i.firstChild).replace(/[\r\n\v\f\t ]+/," "),o),_(),F):(y(i),F=F.join(""),_(),""===F.replace(u,"").replace(p,"")?"":F)}},l(a,{Serializer:o})}(window.kendo.jQuery)},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("editor/range.min",["editor/serializer.min"],e)}(function(){!function(e){function t(e,t,n,i){var o,r,a,s;if(e==t)return i-n;for(o=t;o&&o.parentNode!=e;)o=o.parentNode;if(o)return y(o)-n;for(o=e;o&&o.parentNode!=t;)o=o.parentNode;if(o)return i-y(o)-1;for(r=k.commonAncestor(e,t),a=e;a&&a.parentNode!=r;)a=a.parentNode;for(a||(a=r),s=t;s&&s.parentNode!=r;)s=s.parentNode;return s||(s=r),a==s?0:y(s)-y(a)}function n(e,n){function i(e){try{return t(e.startContainer,e.endContainer,e.startOffset,e.endOffset)<0}catch(n){return!0}}i(e)&&(n?(e.commonAncestorContainer=e.endContainer=e.startContainer,e.endOffset=e.startOffset):(e.commonAncestorContainer=e.startContainer=e.endContainer,e.startOffset=e.endOffset),e.collapsed=!0)}function i(e){e.collapsed=e.startContainer==e.endContainer&&e.startOffset==e.endOffset;for(var t=e.startContainer;t&&t!=e.endContainer&&!k.isAncestorOf(t,e.endContainer);)t=t.parentNode;e.commonAncestorContainer=t}function o(e){var t=e.duplicate(),n=e.duplicate();return t.collapse(!0),n.collapse(!1),k.commonAncestor(e.parentElement(),t.parentElement(),n.parentElement())}function r(e,t,n){var i,o=t[n?"startContainer":"endContainer"],r=t[n?"startOffset":"endOffset"],a=0,s=C(o),l=s?o:o.childNodes[r]||null,d=s?o.parentNode:o,c=t.ownerDocument,u=c.body.createTextRange();3!=o.nodeType&&4!=o.nodeType||(a=r),d||(d=c.body),"img"==d.nodeName.toLowerCase()?(u.moveToElementText(d),u.collapse(!1),e.setEndPoint(n?"StartToStart":"EndToStart",u)):(i=d.insertBefore(k.create(c,"a"),l),u.moveToElementText(i),k.remove(i),u[n?"moveStart":"moveEnd"]("character",a),u.collapse(!1),e.setEndPoint(n?"StartToStart":"EndToStart",u))}function a(e,t,n,i){var o,r,a,s,l,d,c,u=k.create(t.ownerDocument,"a"),f=e.duplicate(),p=i?"StartToStart":"StartToEnd",h=!1;u.innerHTML="\ufeff",f.collapse(i),r=f.parentElement(),k.isAncestorOrSelf(n,r)||(r=n);do h?r.insertBefore(u,u.previousSibling):(r.appendChild(u),h=!0),f.moveToElementText(u);while((o=f.compareEndPoints(p,e))>0&&u.previousSibling);a=u.nextSibling,o==-1&&C(a)?(f.setEndPoint(i?"EndToStart":"EndToEnd",e),k.remove(u),d=[a,f.text.length]):(s=!i&&u.previousSibling,l=i&&u.nextSibling,C(l)?d=[l,0]:C(s)?d=[s,s.length]:(c=y(u),d=r.nextSibling&&c==r.childNodes.length-1?[r.nextSibling,0]:[r,c]),k.remove(u)),t[i?"setStart":"setEnd"].apply(t,d)}var s,l,d,c,u,f,p,h=window.kendo,m=h.Class,g=e.extend,b=h.ui.editor,v=h.support.browser,k=b.Dom,y=k.findNodeIndex,C=k.isDataNode,w=k.findClosestAncestor,x=k.getNodeLength,T=k.normalize,_={selectionFromWindow:function(e){return"getSelection"in e?e.getSelection():new l(e.document)},selectionFromRange:function(e){var t=p.documentFromRange(e);return _.selectionFromDocument(t)},selectionFromDocument:function(e){return _.selectionFromWindow(k.windowFromDocument(e))}},F=m.extend({init:function(t){e.extend(this,{ownerDocument:t,startContainer:t,endContainer:t,commonAncestorContainer:t,startOffset:0,endOffset:0,collapsed:!0})},setStart:function(e,t){this.startContainer=e,this.startOffset=t,i(this),n(this,!0)},setEnd:function(e,t){this.endContainer=e,this.endOffset=t,i(this),n(this,!1)},setStartBefore:function(e){this.setStart(e.parentNode,y(e))},setStartAfter:function(e){this.setStart(e.parentNode,y(e)+1)},setEndBefore:function(e){this.setEnd(e.parentNode,y(e))},setEndAfter:function(e){this.setEnd(e.parentNode,y(e)+1)},selectNode:function(e){this.setStartBefore(e),this.setEndAfter(e)},selectNodeContents:function(e){this.setStart(e,0),this.setEnd(e,e[1===e.nodeType?"childNodes":"nodeValue"].length)},collapse:function(e){var t=this;e?t.setEnd(t.startContainer,t.startOffset):t.setStart(t.endContainer,t.endOffset)},deleteContents:function(){var e=this,t=e.cloneRange();e.startContainer!=e.commonAncestorContainer&&e.setStartAfter(w(e.commonAncestorContainer,e.startContainer)),e.collapse(!0),function n(e){for(;e.next();)e.hasPartialSubtree()?n(e.getSubtreeIterator()):e.remove()}(new s(t))},cloneContents:function(){var e=p.documentFromRange(this);return function t(n){for(var i,o=e.createDocumentFragment();i=n.next();)i=i.cloneNode(!n.hasPartialSubtree()),n.hasPartialSubtree()&&i.appendChild(t(n.getSubtreeIterator())),o.appendChild(i);return o}(new s(this))},extractContents:function(){var e,t=this,n=t.cloneRange();return t.startContainer!=t.commonAncestorContainer&&t.setStartAfter(w(t.commonAncestorContainer,t.startContainer)),t.collapse(!0),e=p.documentFromRange(t),function i(n){for(var o,r=e.createDocumentFragment();o=n.next();)n.hasPartialSubtree()?(o=o.cloneNode(!1),o.appendChild(i(n.getSubtreeIterator()))):n.remove(t.originalRange),r.appendChild(o);return r}(new s(n))},insertNode:function(e){var t=this;C(t.startContainer)?(t.startOffset!=t.startContainer.nodeValue.length&&k.splitDataNode(t.startContainer,t.startOffset),k.insertAfter(e,t.startContainer)):k.insertAt(t.startContainer,e,t.startOffset),t.setStart(t.startContainer,t.startOffset)},cloneRange:function(){return e.extend(new F(this.ownerDocument),{startContainer:this.startContainer,endContainer:this.endContainer,commonAncestorContainer:this.commonAncestorContainer,startOffset:this.startOffset,endOffset:this.endOffset,collapsed:this.collapsed,originalRange:this})},toString:function(){var e=this.startContainer.nodeName,t=this.endContainer.nodeName;return("#text"==e?this.startContainer.nodeValue:e)+"("+this.startOffset+") : "+("#text"==t?this.endContainer.nodeValue:t)+"("+this.endOffset+")"}});F.fromNode=function(e){return new F(e.ownerDocument)},s=m.extend({init:function(t){if(e.extend(this,{range:t,_current:null,_next:null,_end:null}),!t.collapsed){var n=t.commonAncestorContainer;this._next=t.startContainer!=n||C(t.startContainer)?w(n,t.startContainer):t.startContainer.childNodes[t.startOffset],this._end=t.endContainer!=n||C(t.endContainer)?w(n,t.endContainer).nextSibling:t.endContainer.childNodes[t.endOffset]}},hasNext:function(){return!!this._next},next:function(){var e=this,t=e._current=e._next;return e._next=e._current&&e._current.nextSibling!=e._end?e._current.nextSibling:null,C(e._current)&&(e.range.endContainer==e._current&&(t=t.cloneNode(!0),t.deleteData(e.range.endOffset,t.length-e.range.endOffset)),e.range.startContainer==e._current&&(t=t.cloneNode(!0),t.deleteData(0,e.range.startOffset))),t},traverse:function(e){function t(){return i._current=i._next,i._next=i._current&&i._current.nextSibling!=i._end?i._current.nextSibling:null,i._current}for(var n,i=this;n=t();)i.hasPartialSubtree()?i.getSubtreeIterator().traverse(e):e(n);return n},remove:function(e){var t,n,i,o,r,a=this,s=a.range.startContainer==a._current,l=a.range.endContainer==a._current;C(a._current)&&(s||l)?(t=s?a.range.startOffset:0,n=l?a.range.endOffset:a._current.length,i=n-t,e&&(s||l)&&(a._current==e.startContainer&&t<=e.startOffset&&(e.startOffset-=i),a._current==e.endContainer&&n<=e.endOffset&&(e.endOffset-=i)),a._current.deleteData(t,i)):(o=a._current.parentNode,!e||a.range.startContainer!=o&&a.range.endContainer!=o||(r=y(a._current),o==e.startContainer&&r<=e.startOffset&&(e.startOffset-=1),o==e.endContainer&&r<e.endOffset&&(e.endOffset-=1)),k.remove(a._current))},hasPartialSubtree:function(){return!C(this._current)&&(k.isAncestorOrSelf(this._current,this.range.startContainer)||k.isAncestorOrSelf(this._current,this.range.endContainer))},getSubtreeIterator:function(){var e=this,t=e.range.cloneRange();return t.selectNodeContents(e._current),k.isAncestorOrSelf(e._current,e.range.startContainer)&&t.setStart(e.range.startContainer,e.range.startOffset),k.isAncestorOrSelf(e._current,e.range.endContainer)&&t.setEnd(e.range.endContainer,e.range.endOffset),new s(t)}}),l=m.extend({init:function(e){this.ownerDocument=e,this.rangeCount=1},addRange:function(e){var t=this.ownerDocument.body.createTextRange();r(t,e,!1),r(t,e,!0),t.select()},removeAllRanges:function(){var e=this.ownerDocument.selection;"None"!=e.type&&e.empty()},getRangeAt:function(){var e,t,n,i,r,s,l,d,c=new F(this.ownerDocument),u=this.ownerDocument.selection;try{if(e=u.createRange(),t=e.item?e.item(0):e.parentElement(),t.ownerDocument!=this.ownerDocument)return c}catch(f){return c}if("Control"==u.type)c.selectNode(e.item(0));else if(n=o(e),a(e,c,n,!0),a(e,c,n,!1),9==c.startContainer.nodeType&&c.setStart(c.endContainer,c.startOffset),9==c.endContainer.nodeType&&c.setEnd(c.startContainer,c.endOffset),0===e.compareEndPoints("StartToEnd",e)&&c.collapse(!1),i=c.startContainer,r=c.endContainer,s=this.ownerDocument.body,!(c.collapsed||0!==c.startOffset||c.endOffset!=x(c.endContainer)||i==r&&C(i)&&i.parentNode==s)){for(l=!1,d=!1;0===y(i)&&i==i.parentNode.firstChild&&i!=s;)i=i.parentNode,l=!0;for(;y(r)==x(r.parentNode)-1&&r==r.parentNode.lastChild&&r!=s;)r=r.parentNode,d=!0;i==s&&r==s&&l&&d&&(c.setStart(i,0),c.setEnd(r,x(s)))}return c}}),d=m.extend({init:function(e){this.enumerate=function(){function t(e){if(k.is(e,"img")||3==e.nodeType&&(!k.isEmptyspace(e)||"\ufeff"==e.nodeValue))n.push(e);else for(e=e.firstChild;e;)t(e),e=e.nextSibling}var n=[];return new s(e).traverse(t),n}}}),c=m.extend({init:function(e,t){var n=this;n.range=e,n.rootNode=p.documentFromRange(e),n.body=t||n.getEditable(e),"body"!=k.name(n.body)&&(n.rootNode=n.body),n.html=n.body.innerHTML,n.startContainer=n.nodeToPath(e.startContainer),n.endContainer=n.nodeToPath(e.endContainer),n.startOffset=n.offset(e.startContainer,e.startOffset),n.endOffset=n.offset(e.endContainer,e.endOffset)},index:function(e){for(var t,n=0,i=e.nodeType;e=e.previousSibling;)t=e.nodeType,3==t&&i==t||n++,i=t;return n},getEditable:function(e){for(var t=e.commonAncestorContainer;t&&(3==t.nodeType||t.attributes&&!t.attributes.contentEditable);)t=t.parentNode;return t},restoreHtml:function(){this.body.innerHTML=this.html},offset:function(e,t){if(3==e.nodeType)for(;(e=e.previousSibling)&&3==e.nodeType;)t+=e.nodeValue.length;return t},nodeToPath:function(e){for(var t=[];e!=this.rootNode;)t.push(this.index(e)),e=e.parentNode;return t},toRangePoint:function(e,t,n,i){for(var o=this.rootNode,r=n.length,a=i;r--;)o=o.childNodes[n[r]];for(;o&&3==o.nodeType&&o.nodeValue.length<a;)a-=o.nodeValue.length,o=o.nextSibling;o&&a>=0&&e[t?"setStart":"setEnd"](o,a)},toRange:function(){var e=this,t=e.range.cloneRange();return e.toRangePoint(t,!0,e.startContainer,e.startOffset),e.toRangePoint(t,!1,e.endContainer,e.endOffset),t}}),u=m.extend({init:function(){this.caret=null},addCaret:function(e){var t=this;return t.caret=k.create(p.documentFromRange(e),"span",{className:"k-marker"}),e.insertNode(t.caret),e.selectNode(t.caret),t.caret},removeCaret:function(e){var t,n,i,o,r=this,a=r.caret.previousSibling,s=0;a&&(s=C(a)?a.nodeValue.length:y(a)),t=r.caret.parentNode,n=a?y(a):0,k.remove(r.caret),T(t),i=t.childNodes[n],C(i)?e.setStart(i,s):i?(o=k.lastTextNode(i),o?e.setStart(o,o.nodeValue.length):e[a?"setStartAfter":"setStartBefore"](i)):(v.msie||t.innerHTML||(t.innerHTML='<br _moz_dirty="" />'),e.selectNodeContents(t)),e.collapse(!0)},add:function(e,t){var n,i,o=this,r=e.collapsed&&!p.isExpandable(e),a=p.documentFromRange(e);return t&&e.collapsed&&(o.addCaret(e),e=p.expand(e)),n=e.cloneRange(),n.collapse(!1),o.end=k.create(a,"span",{className:"k-marker"}),n.insertNode(o.end),n=e.cloneRange(),n.collapse(!0),o.start=o.end.cloneNode(!0),n.insertNode(o.start),o._removeDeadMarkers(o.start,o.end),r&&(i=a.createTextNode("\ufeff"),k.insertAfter(i.cloneNode(),o.start),k.insertBefore(i,o.end)),T(e.commonAncestorContainer),e.setStartBefore(o.start),e.setEndAfter(o.end),e},_removeDeadMarkers:function(e,t){e.previousSibling&&"\ufeff"==e.previousSibling.nodeValue&&k.remove(e.previousSibling),t.nextSibling&&"\ufeff"==t.nextSibling.nodeValue&&k.remove(t.nextSibling)},_normalizedIndex:function(e){for(var t=y(e),n=e;n.previousSibling;)3==n.nodeType&&3==n.previousSibling.nodeType&&t--,n=n.previousSibling;return t},remove:function(e){var t,n,i,o,r,a,s,l,d,c,u,f=this,p=f.start,h=f.end;for(T(e.commonAncestorContainer);!p.nextSibling&&p.parentNode;)p=p.parentNode;for(;!h.previousSibling&&h.parentNode;)h=h.parentNode;if(t=p.previousSibling&&3==p.previousSibling.nodeType&&p.nextSibling&&3==p.nextSibling.nodeType,n=h.previousSibling&&3==h.previousSibling.nodeType&&h.nextSibling&&3==h.nextSibling.nodeType,i=t&&n,p=p.nextSibling,h=h.previousSibling,o=!1,r=!1,p==f.end&&(r=!!f.start.previousSibling,p=h=f.start.previousSibling||f.end.nextSibling,o=!0),k.remove(f.start),k.remove(f.end),!p||!h)return e.selectNodeContents(e.commonAncestorContainer),void e.collapse(!0);if(a=o?C(p)?p.nodeValue.length:p.childNodes.length:0,s=C(h)?h.nodeValue.length:h.childNodes.length,3==p.nodeType)for(;p.previousSibling&&3==p.previousSibling.nodeType;)p=p.previousSibling,a+=p.nodeValue.length;if(3==h.nodeType)for(;h.previousSibling&&3==h.previousSibling.nodeType;)h=h.previousSibling,s+=h.nodeValue.length;l=p.parentNode,d=h.parentNode,c=this._normalizedIndex(p),u=this._normalizedIndex(h),T(l),3==p.nodeType&&(p=l.childNodes[c]),T(d),3==h.nodeType&&(h=d.childNodes[u]),o?(3==p.nodeType?e.setStart(p,a):e[r?"setStartAfter":"setStartBefore"](p),e.collapse(!0)):(3==p.nodeType?e.setStart(p,a):e.setStartBefore(p),3==h.nodeType?e.setEnd(h,s):e.setEndAfter(h)),f.caret&&f.removeCaret(e)}}),f=/[\u0009-\u000d]|\u0020|\u00a0|\ufeff|\.|,|;|:|!|\(|\)|\?/,p={nodes:function(e){var t=p.textNodes(e);return t.length||(e.selectNodeContents(e.commonAncestorContainer),t=p.textNodes(e),t.length||(t=k.significantChildNodes(e.commonAncestorContainer))),t},textNodes:function(e){return new d(e).enumerate()},documentFromRange:function(e){var t=e.startContainer;return 9==t.nodeType?t:t.ownerDocument},createRange:function(e){return v.msie&&v.version<9?new F(e):e.createRange()},selectRange:function(e){var t,n=p.image(e);n&&(e.setStartAfter(n),e.setEndAfter(n)),t=_.selectionFromRange(e),t.removeAllRanges(),t.addRange(e)},stringify:function(e){return h.format("{0}:{1} - {2}:{3}",k.name(e.startContainer),e.startOffset,k.name(e.endContainer),e.endOffset)},split:function(e,t,n){function i(i){var o,r=e.cloneRange();r.collapse(i),r[i?"setStartBefore":"setEndAfter"](t),o=r.extractContents(),n&&(o=k.trim(o)),k[i?"insertBefore":"insertAfter"](o,t)}i(!0),i(!1)},mapAll:function(t,n){var i=[];return new s(t).traverse(function(t){var o=n(t);o&&e.inArray(o,i)<0&&i.push(o)}),i},getAll:function(e,t){var n=t;return"string"==typeof t&&(t=function(e){return k.is(e,n)}),p.mapAll(e,function(e){if(t(e))return e})},getMarkers:function(e){return p.getAll(e,function(e){return"k-marker"==e.className})},image:function(e){var t=p.getAll(e,"img");if(1==t.length)return t[0]},isStartOf:function(e,t){var n,i,o;if(0!==e.startOffset)return!1;for(n=e.cloneRange();0===n.startOffset&&n.startContainer!=t;){for(i=k.findNodeIndex(n.startContainer),o=n.startContainer.parentNode;i>0&&o[i-1]&&k.insignificant(o[i-1]);)i--;n.setStart(o,i)}return 0===n.startOffset&&n.startContainer==t},isEndOf:function(e,t){function n(e){k.insignificant(e)||o.push(e)}var i,o,r=e.cloneRange();return r.collapse(!1),i=r.startContainer,k.isDataNode(i)&&r.startOffset==k.getNodeLength(i)&&(r.setStart(i.parentNode,k.findNodeIndex(i)+1),r.collapse(!0)),r.setEnd(t,k.getNodeLength(t)),o=[],new s(r).traverse(n),!o.length},wrapSelectedElements:function(e){function t(e,t){var n,i=k.getNodeLength(t);if(e==i)return!0;for(n=e;n<i;n++)if(!k.insignificant(t.childNodes[n]))return!1;return!0}for(var n=k.editableParent(e.startContainer),i=k.editableParent(e.endContainer);0===e.startOffset&&e.startContainer!=n;)e.setStart(e.startContainer.parentNode,k.findNodeIndex(e.startContainer));for(;t(e.endOffset,e.endContainer)&&e.endContainer!=i;)e.setEnd(e.endContainer.parentNode,k.findNodeIndex(e.endContainer)+1);return e},expand:function(e){var t,n,i,o,r=e.cloneRange(),a=r.startContainer.childNodes[0===r.startOffset?0:r.startOffset-1],s=r.endContainer.childNodes[r.endOffset];return C(a)&&C(s)?(t=a.nodeValue,n=s.nodeValue,t&&n?(i=t.split("").reverse().join("").search(f),o=n.search(f),i&&o?(o=o==-1?n.length:o,i=i==-1?0:t.length-i,r.setStart(a,i),r.setEnd(s,o),r):r):r):r},isExpandable:function(e){var t,n,i,o,r,a,s=e.startContainer,l=p.documentFromRange(e);return s!=l&&s!=l.body&&(t=e.cloneRange(),!!(n=s.nodeValue)&&(i=n.substring(0,t.startOffset),o=n.substring(t.startOffset),r=0,a=0,i&&(r=i.split("").reverse().join("").search(f)),o&&(a=o.search(f)),r&&a))}},g(b,{SelectionUtils:_,W3CRange:F,RangeIterator:s,W3CSelection:l,RangeEnumerator:d,RestorePoint:c,Marker:u,RangeUtils:p})}(window.kendo.jQuery)},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("editor/systemBean.min",["editor/range.min"],e)}(function(){!function(e){function t(e,t){var n=e.selectionRestorePoint=new c(e.getRange()),i=new h(t,n);return i.editor=e,e.undoRedoStack.push(i),n}var n=window.kendo,i=n.Class,o=n.ui.editor,r=o.EditorUtils,a=r.registerTool,s=o.Dom,l=o.Tool,d=o.ToolTemplate,c=o.RestorePoint,u=o.Marker,f=e.extend,p=i.extend({init:function(e){this.options=e,this.restorePoint=new c(e.range),this.marker=new u,this.formatter=e.formatter},getRange:function(){return this.restorePoint.toRange()},lockRange:function(e){return this.marker.add(this.getRange(),e)},releaseRange:function(e){this.marker.remove(e),this.editor.selectRange(e)},undo:function(){var e=this.restorePoint;e.restoreHtml(),this.editor.selectRange(e.toRange())},redo:function(){this.exec()},createDialog:function(t,i){var o=this.editor;return e(t).appendTo(document.body).kendoWindow(f({},o.options.dialogOptions,i)).closest(".k-window").toggleClass("k-rtl",n.support.isRtl(o.wrapper)).end()},exec:function(){var e=this.lockRange(!0);this.formatter.editor=this.editor,this.formatter.toggle(e),this.releaseRange(e)}}),h=i.extend({init:function(e,t){this.body=e.body,this.startRestorePoint=e,this.endRestorePoint=t},redo:function(){this.body.innerHTML=this.endRestorePoint.html,this.editor.selectRange(this.endRestorePoint.toRange())},undo:function(){this.body.innerHTML=this.startRestorePoint.html,this.editor.selectRange(this.startRestorePoint.toRange())}}),m=p.extend({init:function(e){p.fn.init.call(this,e),this.managesUndoRedo=!0},exec:function(){var e,t=this.editor,n=this.options,i=n.range,o=t.body,r=new c(i,o),a=n.html||n.value||"";t.selectRange(i),t.clipboard.paste(a,n),n.postProcess&&n.postProcess(t,t.getRange()),e=new h(r,new c(t.getRange(),o)),e.editor=t,t.undoRedoStack.push(e),t.focus()}}),g=l.extend({initialize:function(e,t){var n=t.editor,i=this.options,r=i.items?i.items:n.options.insertHtml;this._selectBox=new o.SelectBox(e,{dataSource:r,dataTextField:"text",dataValueField:"value",change:function(){l.exec(n,"insertHtml",this.value())},title:n.options.messages.insertHtml,highlightFirst:!1})},command:function(e){return new m(e)},update:function(e){var t=e.data("kendoSelectBox")||e.find("select").data("kendoSelectBox");t.close(),t.value(t.options.title)}}),b=i.extend({init:function(e){this.editor=e},keydown:function(n){var i,o=this,r=o.editor,a=r.keyboard,s=a.isTypingKey(n),l=f(e.Event(),n);return o.editor.trigger("keydown",l),l.isDefaultPrevented()?(n.preventDefault(),!0):!(l.isDefaultPrevented()||!s||a.isTypingInProgress())&&(i=r.getRange(),o.startRestorePoint=new c(i),a.startTyping(function(){o.endRestorePoint=t(r,o.startRestorePoint)}),!0)},keyup:function(e){var t=this.editor.keyboard;return this.editor.trigger("keyup",e),!!t.isTypingInProgress()&&(t.endTyping(),!0)}}),v=i.extend({init:function(e){this.editor=e},_addCaret:function(e){var t=s.create(this.editor.document,"a");return s.insertAt(e,t,0),t},_restoreCaret:function(e){var t=this.editor.createRange();t.setStartAfter(e),t.collapse(!0),this.editor.selectRange(t),s.remove(e)},_handleDelete:function(e){var t,n,i=e.endContainer,r=s.closestEditableOfType(i,s.blockElements);return!(!r||!o.RangeUtils.isEndOf(e,r))&&(t=s.next(r),!(!t||"p"!=s.name(t))&&(n=this._addCaret(t),this._merge(r,t),this._restoreCaret(n),!0))},_cleanBomBefore:function(e){for(var t=e.startOffset,n=e.startContainer,i=n.nodeValue,o=0;t-o>=0&&"\ufeff"==i[t-o-1];)o++;o>0&&(n.deleteData(t-o,o),e.setStart(n,Math.max(0,t-o)),e.collapse(!0),this.editor.selectRange(e))},_handleBackspace:function(e){var t,n,i,r,a=e.startContainer,l=s.closestEditableOfType(a,["li"]),d=s.closestEditableOfType(a,"p,h1,h2,h3,h4,h5,h6".split(","));return s.isDataNode(a)&&this._cleanBomBefore(e),d&&d.previousSibling&&o.RangeUtils.isStartOf(e,d)?(t=d.previousSibling,n=this._addCaret(d),this._merge(t,d),this._restoreCaret(n),!0):!(!l||!o.RangeUtils.isStartOf(e,l))&&(i=l.firstChild,i||(l.innerHTML=o.emptyElementContent,i=l.firstChild),r=new o.ListFormatter(s.name(l.parentNode),"p"),e.selectNodeContents(l),r.toggle(e),s.insignificant(i)?e.setStartBefore(i):e.setStart(i,0),this.editor.selectRange(e),!0)},_handleSelection:function(t){var n,i,r,a=t.commonAncestorContainer,l=s.closest(a,"table"),d=o.emptyElementContent;return/t(able|body)/i.test(s.name(a))&&t.selectNode(l),n=new u,n.add(t,!1),t.setStartAfter(n.start),t.setEndBefore(n.end),i=t.startContainer,r=t.endContainer,t.deleteContents(),l&&""===e(l).text()&&(t.selectNode(l),t.deleteContents()),a=t.commonAncestorContainer,"p"===s.name(a)&&""===a.innerHTML&&(a.innerHTML=d,t.setStart(a,0)),this._join(i,r),s.insertAfter(this.editor.document.createTextNode("\ufeff"),n.start),n.remove(t),i=t.startContainer,"tr"==s.name(i)&&(i=i.childNodes[Math.max(0,t.startOffset-1)],t.setStart(i,s.getNodeLength(i))),t.collapse(!0),this.editor.selectRange(t),!0},_root:function(e){for(;e&&e.parentNode&&"body"!=s.name(e.parentNode);)e=e.parentNode;return e},_join:function(e,t){e=this._root(e),t=this._root(t),e!=t&&s.is(t,"p")&&this._merge(e,t)},_merge:function(e,t){for(s.removeTrailingBreak(e);t.firstChild;)1==e.nodeType?e.appendChild(t.firstChild):e.parentNode.appendChild(t.firstChild);s.remove(t)},keydown:function(e){var i,o,r=this.editor.getRange(),a=e.keyCode,s=n.keys,l=a===s.BACKSPACE,d=a==s.DELETE;!l&&!d||r.collapsed?l?i="_handleBackspace":d&&(i="_handleDelete"):i="_handleSelection",i&&(o=new c(r),this[i](r)&&(e.preventDefault(),t(this.editor,o)))},keyup:e.noop}),k=i.extend({init:function(e){this.editor=e,this.systemCommandIsInProgress=!1},createUndoCommand:function(){this.startRestorePoint=this.endRestorePoint=t(this.editor,this.startRestorePoint)},changed:function(){return!!this.startRestorePoint&&this.startRestorePoint.html!=this.editor.body.innerHTML},keydown:function(e){var t=this,n=t.editor,i=n.keyboard;return i.isModifierKey(e)?(i.isTypingInProgress()&&i.endTyping(!0),t.startRestorePoint=new c(n.getRange()),!0):!!i.isSystem(e)&&(t.systemCommandIsInProgress=!0,t.changed()&&(t.systemCommandIsInProgress=!1,t.createUndoCommand()),!0)},keyup:function(){var e=this;return!(!e.systemCommandIsInProgress||!e.changed())&&(e.systemCommandIsInProgress=!1,e.createUndoCommand(),!0)}}),y=i.extend({init:function(e){this.handlers=e,this.typingInProgress=!1},isCharacter:function(e){return e>=48&&e<=90||e>=96&&e<=111||e>=186&&e<=192||e>=219&&e<=222||229==e},toolFromShortcut:function(t,n){var i,o,r=String.fromCharCode(n.keyCode);for(i in t)if(o=e.extend({ctrl:!1,alt:!1,shift:!1},t[i].options),(o.key==r||o.key==n.keyCode)&&o.ctrl==n.ctrlKey&&o.alt==n.altKey&&o.shift==n.shiftKey)return i},toolsFromShortcut:function(t,n){var i,o,r,a=String.fromCharCode(n.keyCode),s=[],l=function(e){return e==a||e==n.keyCode};for(i in t)o=e.extend({ctrl:!1,alt:!1,shift:!1},t[i].options),r=e.isArray(o.key)?e.grep(o.key,l).length>0:l(o.key),r&&o.ctrl==n.ctrlKey&&o.alt==n.altKey&&o.shift==n.shiftKey&&s.push(t[i]);return s},isTypingKey:function(e){var t=e.keyCode;return this.isCharacter(t)&&!e.ctrlKey&&!e.altKey||32==t||13==t||8==t||46==t&&!e.shiftKey&&!e.ctrlKey&&!e.altKey},isModifierKey:function(e){var t=e.keyCode;return 17==t&&!e.shiftKey&&!e.altKey||16==t&&!e.ctrlKey&&!e.altKey||18==t&&!e.ctrlKey&&!e.shiftKey},isSystem:function(e){return 46==e.keyCode&&e.ctrlKey&&!e.altKey&&!e.shiftKey},startTyping:function(e){this.onEndTyping=e,this.typingInProgress=!0},stopTyping:function(){this.typingInProgress&&this.onEndTyping&&this.onEndTyping(),this.typingInProgress=!1},endTyping:function(t){var n=this;n.clearTimeout(),t?n.stopTyping():n.timeout=window.setTimeout(e.proxy(n.stopTyping,n),1e3)},isTypingInProgress:function(){return this.typingInProgress},clearTimeout:function(){window.clearTimeout(this.timeout)},notify:function(e,t){var n,i=this.handlers;for(n=0;n<i.length&&!i[n][t](e);n++);
},keydown:function(e){this.notify(e,"keydown")},keyup:function(e){this.notify(e,"keyup")}}),C=i.extend({init:function(e){this.editor=e;var t=e.options.pasteCleanup;this.cleaners=[new x(t),new T(t),new _(t),new F(t),new N(t),new A(t),new B(t),new D(t)]},htmlToFragment:function(e){var t=this.editor,n=t.document,i=s.create(n,"div"),o=n.createDocumentFragment();for(i.innerHTML=e;i.firstChild;)o.appendChild(i.firstChild);return o},isBlock:function(e){return/<(div|p|ul|ol|table|h[1-6])/i.test(e)},_startModification:function(){var e,t,n=this.editor;if(!this._inProgress)return this._inProgress=!0,e=n.getRange(),t=new c(e),s.persistScrollTop(n.document),{range:e,restorePoint:t}},_endModification:function(e){t(this.editor,e.restorePoint),this.editor._selectionChange(),this._inProgress=!1},_contentModification:function(e,t){var n=this,i=n.editor,o=n._startModification();o&&(e.call(n,i,o.range),setTimeout(function(){t.call(n,i,o.range),n._endModification(o)}))},_removeBomNodes:function(e){var t,n=o.RangeUtils.textNodes(e);for(t=0;t<n.length;t++)n[t].nodeValue=s.stripBom(n[t].nodeValue)},_onBeforeCopy:function(e){var t=new u;t.add(e),this._removeBomNodes(e),t.remove(e),this.editor.selectRange(e)},oncopy:function(){this._onBeforeCopy(this.editor.getRange())},oncut:function(){this._onBeforeCopy(this.editor.getRange()),this._contentModification(e.noop,e.noop)},_fileToDataURL:function(t){var n=e.Deferred(),i=new FileReader;return t instanceof window.File||!t.getAsFile||(t=t.getAsFile()),i.onload=e.proxy(n.resolve,n),i.readAsDataURL(t),n.promise()},_triggerPaste:function(e,t){var n={html:e||""};n.html=n.html.replace(/\ufeff/g,""),this.editor.trigger("paste",n),this.paste(n.html,t||{})},_handleImagePaste:function(t){var n,i,o,r,a;if("FileReader"in window&&(n=t.clipboardData||t.originalEvent.clipboardData||window.clipboardData||{},i=n.items||n.files,i&&(o=e.grep(i,function(e){return/^image\//i.test(e.type)}),r=e.grep(i,function(e){return/^text\/html/i.test(e.type)}),!r.length&&o.length&&(a=this._startModification()))))return e.when.apply(e,e.map(o,this._fileToDataURL)).done(e.proxy(function(){var t=Array.prototype.slice.call(arguments),n=e.map(t,function(e){return'<img src="'+e.target.result+'" />'}).join("");this._triggerPaste(n),this._endModification(a)},this)),!0},onpaste:function(t){return this._handleImagePaste(t)?void t.preventDefault():void this._contentModification(function(i,o){var r,a,l,d=s.create(i.document,"div",{className:"k-paste-container",innerHTML:"\ufeff"}),c=n.support.browser;i.body.appendChild(d),c.msie&&c.version<11?(t.preventDefault(),r=i.createRange(),r.selectNodeContents(d),i.selectRange(r),a=i.document.body.createTextRange(),a.moveToElementText(d),e(i.body).unbind("paste"),a.execCommand("Paste"),e(i.body).bind("paste",e.proxy(this.onpaste,this))):(l=i.createRange(),l.selectNodeContents(d),i.selectRange(l)),o.deleteContents()},function(t,n){var i,o="";t.selectRange(n),i=e(t.body).children(".k-paste-container"),i.each(function(){var e=this.lastChild;e&&s.is(e,"br")&&s.remove(e),o+=this.innerHTML}),i.remove(),this._triggerPaste(o,{clean:!0})})},splittableParent:function(e,t){var n,i;if(e)return s.closestEditableOfType(t,["p","ul","ol"])||t.parentNode;if(n=t.parentNode,i=t.ownerDocument.body,s.isInline(n))for(;n.parentNode!=i&&!s.isBlock(n.parentNode);)n=n.parentNode;return n},paste:function(t,n){var i,r,a,l,d,c,p,h,m,g,b,v,k=this.editor;for(n=f({clean:!1,split:!0},n),i=0,r=this.cleaners.length;i<r;i++)this.cleaners[i].applicable(t)&&(t=this.cleaners[i].clean(t));if(n.clean&&(t=t.replace(/(<br>(\s|&nbsp;)*)+(<\/?(div|p|li|col|t))/gi,"$3"),t=t.replace(/<(a|span)[^>]*><\/\1>/gi,"")),t=t.replace(/^<li/i,"<ul><li").replace(/li>$/g,"li></ul>"),a=this.isBlock(t),k.focus(),l=k.getRange(),l.deleteContents(),l.startContainer==k.document&&l.selectNodeContents(k.body),d=new u,c=d.addCaret(l),p=this.splittableParent(a,c),h=!1,m=p!=k.body&&!s.is(p,"td"),n.split&&m&&(a||s.isInline(p))&&(l.selectNode(c),o.RangeUtils.split(l,p,!0),h=!0),g=this.htmlToFragment(t),g.firstChild&&"k-paste-container"===g.firstChild.className){for(b=[],i=0,r=g.childNodes.length;i<r;i++)b.push(g.childNodes[i].innerHTML);g=this.htmlToFragment(b.join("<br />"))}if(e(g.childNodes).filter("table").addClass("k-table").end().find("table").addClass("k-table"),l.insertNode(g),p=this.splittableParent(a,c),h){for(;c.parentNode!=p;)s.unwrap(c.parentNode);s.unwrap(c.parentNode)}s.normalize(l.commonAncestorContainer),c.style.display="inline",s.restoreScrollTop(k.document),s.scrollTo(c),d.removeCaret(l),v=l.commonAncestorContainer.parentNode,l.collapsed&&"tbody"==s.name(v)&&(l.setStartAfter(e(v).closest("table")[0]),l.collapse(!0)),k.selectRange(l)}}),w=i.extend({init:function(e){this.options=e||{},this.replacements=[]},clean:function(e,t){var n,i,o=this,r=t||o.replacements;for(n=0,i=r.length;n<i;n+=2)e=e.replace(r[n],r[n+1]);return e}}),x=w.extend({init:function(e){w.fn.init.call(this,e),this.replacements=[/<(\/?)script([^>]*)>/i,"<$1telerik:script$2>"]},applicable:function(e){return!this.options.none&&/<script[^>]*>/i.test(e)}}),T=w.extend({init:function(e){w.fn.init.call(this,e);var t=" ";this.replacements=[/<span\s+class="Apple-tab-span"[^>]*>\s*<\/span>/gi,t,/\t/gi,t,/&nbsp;&nbsp; &nbsp;/gi,t]},applicable:function(e){return/&nbsp;&nbsp; &nbsp;|class="?Apple-tab-span/i.test(e)}}),_=w.extend({init:function(e){w.fn.init.call(this,e),this.junkReplacements=[/<\?xml[^>]*>/gi,"",/<!--(.|\n)*?-->/g,"",/&quot;/g,"'",/<o:p>&nbsp;<\/o:p>/gi,"&nbsp;",/<\/?(meta|link|style|o:|v:|x:)[^>]*>((?:.|\n)*?<\/(meta|link|style|o:|v:|x:)[^>]*>)?/gi,"",/<\/o>/g,""],this.replacements=this.junkReplacements.concat([/(?:<br>&nbsp;[\s\r\n]+|<br>)*(<\/?(h[1-6]|hr|p|div|table|tbody|thead|tfoot|th|tr|td|li|ol|ul|caption|address|pre|form|blockquote|dl|dt|dd|dir|fieldset)[^>]*>)(?:<br>&nbsp;[\s\r\n]+|<br>)*/g,"$1",/<br><br>/g,"<BR><BR>",/<br>(?!\n)/g," ",/<table([^>]*)>(\s|&nbsp;)+<t/gi,"<table$1><t",/<tr[^>]*>(\s|&nbsp;)*<\/tr>/gi,"",/<tbody[^>]*>(\s|&nbsp;)*<\/tbody>/gi,"",/<table[^>]*>(\s|&nbsp;)*<\/table>/gi,"",/<BR><BR>/g,"<br>",/^\s*(&nbsp;)+/gi,"",/(&nbsp;|<br[^>]*>)+\s*$/gi,"",/mso-[^;"]*;?/gi,"",/<(\/?)b(\s[^>]*)?>/gi,"<$1strong$2>",/<(\/?)font(\s[^>]*)?>/gi,this.convertFontMatch,/<(\/?)i(\s[^>]*)?>/gi,"<$1em$2>",/style=(["|'])\s*\1/g,"",/(<br[^>]*>)?\n/g,function(e,t){return t?e:" "}])},convertFontMatch:function(e,t,n){var i=/face=['"]([^'"]+)['"]/i,o=i.exec(n),r=n&&o&&o[1];return t?"</span>":r?'<span style="font-family:'+r+'">':"<span>"},applicable:function(e){return/class="?Mso/i.test(e)||/style="[^"]*mso-/i.test(e)||/urn:schemas-microsoft-com:office/.test(e)},stripEmptyAnchors:function(e){return e.replace(/<a([^>]*)>\s*<\/a>/gi,function(e,t){return!t||t.indexOf("href")<0?"":e})},listType:function(e,t){var n,i=e.innerHTML,o=s.innerText(e),r=i.match(/^(?:<span [^>]*texhtml[^>]*>)?<span [^>]*(?:Symbol|Wingdings)[^>]*>([^<]+)/i),a=r&&r[1],l=/^[a-z\d]/i.test(a),d=function(e){return e.replace(/^(?:&nbsp;|[\u00a0\n\r\s])+/,"")};return r&&(n=!0),i=i.replace(/<\/?\w+[^>]*>/g,"").replace(/&nbsp;/g," "),!n&&/^[\u2022\u00b7\u00a7\u00d8o]\u00a0+/.test(i)||n&&/^.\u00a0+/.test(i)||a&&!l&&t?{tag:"ul",style:this._guessUnorderedListStyle(d(o))}:/^\s*\w+[\.\)][\u00a0 ]{2,}/.test(i)?{tag:"ol",style:this._guessOrderedListStyle(d(o))}:void 0},_convertToLi:function(e){var t;return 1==e.childNodes.length?t=e.firstChild.innerHTML.replace(/^\w+[\.\)](&nbsp;)+ /,""):(s.remove(e.firstChild),3==e.firstChild.nodeType&&/^[ivxlcdm]+\.$/i.test(e.firstChild.nodeValue)&&s.remove(e.firstChild),/^(&nbsp;|\s)+$/i.test(e.firstChild.innerHTML)&&s.remove(e.firstChild),t=e.innerHTML),s.remove(e),s.create(document,"li",{innerHTML:t})},_guessUnorderedListStyle:function(e){return/^[\u2022\u00b7\u00FC\u00D8\u002dv-]/.test(e)?null:/^o/.test(e)?"circle":"square"},_guessOrderedListStyle:function(e){var t=null;return/^\d/.test(e)||(t=(/^[a-z]/.test(e)?"lower-":"upper-")+(/^[ivxlcdm]/i.test(e)?"roman":"alpha")),t},extractListLevels:function(e){var t=/style=['"]?[^'"]*?mso-list:\s?[a-zA-Z]+(\d+)\s[a-zA-Z]+(\d+)\s(\w+)/gi;return e=e.replace(t,function(e,t,i){return n.format('data-list="{0}" data-level="{1}" {2}',t,i,e)})},lists:function(t){var n,i,o,r,a,l,d,c,u,f,p,h,m=e(t).find(s.blockElements.join(",")),g=-1,b={},v=t;for(r=0;r<m.length;r++)a=m[r],u=e(a).data(),f=u.list,n=s.name(a),"td"!=n&&(p=this.listType(a,u),l=p&&p.tag,l&&"p"==n?(d=parseFloat(a.style.marginLeft||0),void 0===i&&(i=d),h=l+f,b[d]||(b[d]={}),c=b[d][h],(d>g||!c)&&(c=s.create(document,l,{style:{listStyleType:p.style}}),v==t||d<=g?(o&&i!==d?o.appendChild(c):s.insertBefore(c,a),b[d]={}):(o=v,v.appendChild(c)),b[d][h]=c),v=this._convertToLi(a),c.appendChild(v),g=d):a.innerHTML?(g=-1,v=t):s.remove(a))},removeAttributes:function(e){for(var t=e.attributes,n=t.length;n--;)"colspan"!=s.name(t[n])&&e.removeAttributeNode(t[n])},createColGroup:function(t){var i=t.cells,o=e(t).closest("table"),r=o.children("colgroup");i.length<2||(r.length&&(i=r.children(),r[0].parentNode.removeChild(r[0])),r=e(e.map(i,function(e){var t=e.width;return t&&0!==parseInt(t,10)?n.format('<col style="width:{0}px;"/>',t):"<col />"}).join("")),r.is("colgroup")||(r=e("<colgroup/>").append(r)),r.prependTo(o))},convertHeaders:function(t){var n,i=t.cells,o=e.map(i,function(t){var n=e(t).children("p").children("strong")[0];if(n&&"strong"==s.name(n))return n});if(o.length==i.length){for(n=0;n<o.length;n++)s.unwrap(o[n]);for(e(t).closest("table").find("colgroup").after("<thead></thead>").end().find("thead").append(t),n=0;n<i.length;n++)s.changeTag(i[n],"th")}},removeParagraphs:function(t){var n,i,o,r,a;for(n=0;n<t.length;n++)for(this.removeAttributes(t[n]),r=e(t[n]),a=r.children("p"),i=0,o=a.length;i<o;i++)i<o-1&&s.insertAfter(s.create(document,"br"),a[i]),s.unwrap(a[i])},removeDefaultColors:function(e){for(var t=0;t<e.length;t++)/^\s*color:\s*[^;]*;?$/i.test(e[t].style.cssText)&&s.unwrap(e[t])},tables:function(t){var n,i,o,r,a,s=e(t).find("table"),l=this;for(r=0;r<s.length;r++){for(n=s[r].rows,o=i=n[0],a=1;a<n.length;a++)n[a].cells.length>o.cells.length&&(o=n[a]);l.createColGroup(o),l.convertHeaders(i),l.removeAttributes(s[r]),l.removeParagraphs(s.eq(r).find("td,th")),l.removeDefaultColors(s.eq(r).find("span"))}},headers:function(t){var n,i=e(t).find("p.MsoTitle");for(n=0;n<i.length;n++)s.changeTag(i[n],"h1")},removeFormatting:function(t){e(t).find("*").each(function(){e(this).css({fontSize:"",fontFamily:""}),this.getAttribute("style")||this.style.cssText||this.removeAttribute("style")})},clean:function(e){var t,n=this,i=this.options;return i.none?(e=w.fn.clean.call(n,e,this.junkReplacements),e=n.stripEmptyAnchors(e)):(e=this.extractListLevels(e),e=w.fn.clean.call(n,e),e=n.stripEmptyAnchors(e),t=s.create(document,"div",{innerHTML:e}),n.headers(t),i.msConvertLists&&n.lists(t),n.tables(t),i.msAllFormatting&&n.removeFormatting(t),e=t.innerHTML.replace(/(<[^>]*)\s+class="?[^"\s>]*"?/gi,"$1")),e}}),F=w.extend({init:function(e){w.fn.init.call(this,e),this.replacements=[/\s+class="Apple-style-span[^"]*"/gi,"",/<(div|p|h[1-6])\s+style="[^"]*"/gi,"<$1",/^<div>(.*)<\/div>$/,"$1"]},applicable:function(e){return/class="?Apple-style-span|style="[^"]*-webkit-nbsp-mode/i.test(e)}}),S=w.extend({clean:function(e){var t=s.create(document,"div",{innerHTML:e});return t=this.cleanDom(t),t.innerHTML},cleanDom:function(e){return e}}),N=S.extend({cleanDom:function(t){var n=this.collectTags();return e(t).find(n).each(function(){s.unwrap(this)}),t},collectTags:function(){if(this.options.span)return"span"},applicable:function(){return this.options.span}}),A=S.extend({cleanDom:function(t){var n=this.collectAttr(),i=e(t).find("["+n.join("],[")+"]");return i.removeAttr(n.join(" ")),t},collectAttr:function(){return this.options.css?["class","style"]:[]},applicable:function(){return this.options.css}}),R=function(){this.text="",this.add=function(e){this.text+=e}},E=i.extend({init:function(e){this.separators=e||{text:" ",line:"<br/>"},this.lines=[],this.inlineBlockText=[],this.resetLine()},appendText:function(e){3===e.nodeType&&(e=e.nodeValue),this.textContainer.add(e)},appendInlineBlockText:function(e){this.inlineBlockText.push(e)},flashInlineBlockText:function(){this.inlineBlockText.length&&(this.appendText(this.inlineBlockText.join(" ")),this.inlineBlockText=[])},endLine:function(){this.flashInlineBlockText(),this.resetLine()},html:function(){var e,t,n,i,o,r,a,s,l=this.separators,d="",c=this.lines;for(this.flashInlineBlockText(),e=0,t=c.length,n=t-1;e<t;e++){for(i=c[e],o=0,r=i.length,a=r-1;o<r;o++)s=i[o].text,d+=s,o!==a&&(d+=l.text);e!==n&&(d+=l.line)}return d},resetLine:function(){this.textContainer=new R,this.line=[],this.line.push(this.textContainer),this.lines.push(this.line)}}),I=i.extend({init:function(e){this.callback=e},enumerate:function(e){var t,n;e&&(t=this.callback(e),n=e.firstChild,!t&&n&&this.enumerate(n),this.enumerate(e.nextSibling))}}),B=w.extend({init:function(t){w.fn.init.call(this,t),this.hasText=!1,this.enumerator=new I(e.proxy(this.buildText,this))},clean:function(e){var t=s.create(document,"div",{innerHTML:e});return this.cleanDom(t)},cleanDom:function(e){return this.separators=this.getDefaultSeparators(),this.htmlLines=new E(this.separators),this.enumerator.enumerate(e.firstChild),this.hasText=!1,this.htmlLines.html()},buildText:function(e){if(s.isDataNode(e)){if(s.isEmptyspace(e))return;this.htmlLines.appendText(e.nodeValue.replace("\n",this.separators.line)),this.hasText=!0}else if(s.isBlock(e)&&this.hasText){var t=this.actions[s.name(e)]||this.actions.block;return t(this,e)}},applicable:function(){var e=this.options;return e.all||e.keepNewLines},getDefaultSeparators:function(){return this.options.all?{text:" ",line:" "}:{text:" ",line:"<br/>"}},actions:{ul:e.noop,ol:e.noop,table:e.noop,thead:e.noop,tbody:e.noop,td:function(e,t){var n=new B({all:!0}),i=n.cleanDom(t);return e.htmlLines.appendInlineBlockText(i),!0},block:function(e){e.htmlLines.endLine()}}}),D=w.extend({clean:function(e){return this.options.custom(e)},applicable:function(){return"function"==typeof this.options.custom}}),O=p.extend({init:function(e){p.fn.init.call(this,e),this.managesUndoRedo=!0},exec:function(){var e=this.editor;n.support.browser.msie?e.document.execCommand("print",!1,null):e.window.print&&e.window.print()}}),P=p.extend({init:function(e){this.async=!0,p.fn.init.call(this,e)},exec:function(){var e=this,t=this.lockRange(!0);this.editor.saveAsPDF().then(function(){e.releaseRange(t)})}});f(o,{_finishUpdate:t,Command:p,GenericCommand:h,InsertHtmlCommand:m,InsertHtmlTool:g,TypingHandler:b,SystemHandler:k,BackspaceHandler:v,Keyboard:y,Clipboard:C,Cleaner:w,ScriptCleaner:x,TabCleaner:T,MSWordFormatCleaner:_,WebkitFormatCleaner:F,HtmlTagsCleaner:N,HtmlAttrCleaner:A,HtmlContentCleaner:B,HtmlTextLines:E,CustomCleaner:D,PrintCommand:O,ExportPdfCommand:P}),a("insertHtml",new g({template:new d({template:r.dropDownListTemplate,title:"Insert HTML",initialValue:"Insert HTML"})})),a("print",new l({command:O,template:new d({template:r.buttonTemplate,title:"Print"})})),a("pdf",new l({command:P,template:new d({template:r.buttonTemplate,title:"Export PDF"})}))}(window.kendo.jQuery)},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("editor/inlineformat.min",["editor/systemBean.min"],e)}(function(){!function(e){var t=window.kendo,n=t.Class,i=t.ui.editor,o=t.ui.Editor.fn.options.formats,r=i.EditorUtils,a=i.Tool,s=i.ToolTemplate,l=i.FormatTool,d=i.Dom,c=i.RangeUtils,u=e.extend,f=i.EditorUtils.registerTool,p=i.EditorUtils.registerFormat,h="k-marker",m=n.extend({init:function(e){this.format=e},numberOfSiblings:function(e){var t,n=0,i=0,o=0,r=e.parentNode;for(t=r.firstChild;t;t=t.nextSibling)t!=e&&(t.className==h?o++:3==t.nodeType?n++:i++);return o>1&&r.firstChild.className==h&&r.lastChild.className==h?0:i+n},findSuitable:function(e,t){var n,i;if(!t&&this.numberOfSiblings(e)>0)return null;for(n=e.parentNode,i=this.format[0].tags;!d.ofType(n,i);){if(this.numberOfSiblings(n)>0)return null;n=n.parentNode}return n},findFormat:function(e){var t,n,i,o,r,a=this.format,s=d.attrEquals;for(t=0,n=a.length;t<n;t++){if(i=e,o=a[t].tags,r=a[t].attr,i&&d.ofType(i,o)&&s(i,r))return i;for(;i;)if(i=d.parentOfType(i,o),i&&s(i,r))return i}return null},isFormatted:function(e){var t,n;for(t=0,n=e.length;t<n;t++)if(this.findFormat(e[t]))return!0;return!1}}),g=n.extend({init:function(e,t){this.finder=new m(e),this.attributes=u({},e[0].attr,t),this.tag=e[0].tags[0]},wrap:function(e){return d.wrap(e,d.create(e.ownerDocument,this.tag,this.attributes))},activate:function(e,t){this.finder.isFormatted(t)?(this.split(e),this.remove(t)):this.apply(t)},toggle:function(e){var t=c.textNodes(e);t.length>0&&this.activate(e,t)},apply:function(e){var t,n,i,o,r=[];for(t=0,n=e.length;t<n;t++){if(i=e[t],o=this.finder.findSuitable(i))d.attr(o,this.attributes);else{for(;!d.isBlock(i.parentNode)&&1==i.parentNode.childNodes.length&&"true"!==i.parentNode.contentEditable;)i=i.parentNode;o=this.wrap(i)}r.push(o)}this.consolidate(r)},remove:function(e){var t,n,i;for(t=0,n=e.length;t<n;t++)i=this.finder.findFormat(e[t]),i&&(this.attributes&&this.attributes.style?(d.unstyle(i,this.attributes.style),i.style.cssText||i.attributes["class"]||d.unwrap(i)):d.unwrap(i))},split:function(e){var t,n,i=c.textNodes(e),o=i.length;if(o>0)for(t=0;t<o;t++)n=this.finder.findFormat(i[t]),n&&c.split(e,n,!0)},consolidate:function(e){for(var t,n;e.length>1;)if(t=e.pop(),n=e[e.length-1],t.previousSibling&&t.previousSibling.className==h&&n.appendChild(t.previousSibling),t.tagName==n.tagName&&t.previousSibling==n&&t.style.cssText==n.style.cssText){for(;t.firstChild;)n.appendChild(t.firstChild);d.remove(t)}}}),b=m.extend({init:function(e,t){this.format=e,this.greedyProperty=t,m.fn.init.call(this,e)},getInlineCssValue:function(t){var n,i,o,r,a,s,l,c,u,f,p,h,m=t.attributes,g=e.trim;if(m)for(n=0,i=m.length;n<i;n++)if(o=m[n],r=o.nodeName,a=o.nodeValue,o.specified&&"style"==r)for(s=g(a||t.style.cssText).split(";"),c=0,u=s.length;c<u;c++)if(l=s[c],l.length){if(f=l.split(":"),p=g(f[0].toLowerCase()),h=g(f[1]),p!=this.greedyProperty)continue;return p.indexOf("color")>=0?d.toHex(h):h}},getFormatInner:function(t){var n,i,o,r=e(d.isDataNode(t)?t.parentNode:t),a=r.parentsUntil("[contentEditable]").addBack().toArray().reverse();for(n=0,i=a.length;n<i;n++)if(o="className"==this.greedyProperty?a[n].className:this.getInlineCssValue(a[n]))return o;return"inherit"},getFormat:function(e){var t,n,i=this.getFormatInner(e[0]);for(t=1,n=e.length;t<n;t++)if(i!=this.getFormatInner(e[t]))return"";return i},isFormatted:function(e){return""!==this.getFormat(e)}}),v=g.extend({init:function(e,n,i){g.fn.init.call(this,e,n),this.values=n,this.finder=new b(e,i),i&&(this.greedyProperty=t.toCamelCase(i))},activate:function(e,t){var n=this.greedyProperty,i="apply";this.split(e),n&&"inherit"==this.values.style[n]&&(i="remove"),this[i](t)}}),k=l.extend({init:function(e){l.fn.init.call(this,u(e,{finder:new m(e.format),formatter:function(){return new g(e.format)}}))}}),y=a.extend({update:function(e,t){var n=e.data(this.type);n.close(),n.value(this.finder.getFormat(t))}}),C=y.extend({init:function(e){a.fn.init.call(this,e),this.type=t.support.browser.msie||t.support.touch?"kendoDropDownList":"kendoComboBox",this.format=[{tags:["span"]}],this.finder=new b(this.format,e.cssAttr)},command:function(e){var t=this.options,n=this.format,o={};return new i.FormatCommand(u(e,{formatter:function(){return o[t.domAttr]=e.value,new v(n,{style:o},t.cssAttr)}}))},initialize:function(e,t){var n,i=t.editor,o=this.options,r=o.name,s=[];o.defaultValue&&(s=[{text:i.options.messages[o.defaultValue[0].text],value:o.defaultValue[0].value}]),n=s.concat(o.items?o.items:i.options[r]||[]),e.attr({title:t.title}),e[this.type]({dataTextField:"text",dataValueField:"value",dataSource:n,change:function(){a.exec(i,r,this.value())},highlightFirst:!1}),e.closest(".k-widget").removeClass("k-"+r).find("*").addBack().attr("unselectable","on"),e.data(this.type).value("inherit")}}),w=a.extend({init:function(e){a.fn.init.call(this,e),this.format=[{tags:["span"]}],this.finder=new b(this.format,e.cssAttr)},options:{palette:"websafe"},update:function(){this._widget.close()},command:function(e){var t=this.options,n=this.format,o={};return new i.FormatCommand(u(e,{formatter:function(){return o[t.domAttr]=e.value,new v(n,{style:o},t.cssAttr)}}))},initialize:function(e,n){var i=n.editor,o=this.name,r=u({},w.fn.options,this.options),s=r.palette;e=this._widget=new t.ui.ColorPicker(e,{toolIcon:"k-"+r.name,palette:s,change:function(){var t=e.value();t&&a.exec(i,o,t),i.focus()},activate:function(t){t.preventDefault(),e.trigger("change")}}),e.wrapper.attr({title:n.title,unselectable:"on"}).find("*").attr("unselectable","on")}});u(i,{InlineFormatFinder:m,InlineFormatter:g,DelayedExecutionTool:y,GreedyInlineFormatFinder:b,GreedyInlineFormatter:v,InlineFormatTool:k,FontTool:C,ColorTool:w}),p("bold",[{tags:["strong","b"]},{tags:["span"],attr:{style:{fontWeight:"bold"}}}]),f("bold",new k({key:"B",ctrl:!0,format:o.bold,template:new s({template:r.buttonTemplate,title:"Bold"})})),p("italic",[{tags:["em","i"]},{tags:["span"],attr:{style:{fontStyle:"italic"}}}]),f("italic",new k({key:"I",ctrl:!0,format:o.italic,template:new s({template:r.buttonTemplate,title:"Italic"})})),p("underline",[{tags:["span"],attr:{style:{textDecoration:"underline"}}},{tags:["u"]}]),f("underline",new k({key:"U",ctrl:!0,format:o.underline,template:new s({template:r.buttonTemplate,title:"Underline"})})),p("strikethrough",[{tags:["del","strike"]},{tags:["span"],attr:{style:{textDecoration:"line-through"}}}]),f("strikethrough",new k({format:o.strikethrough,template:new s({template:r.buttonTemplate,title:"Strikethrough"})})),p("superscript",[{tags:["sup"]}]),f("superscript",new k({format:o.superscript,template:new s({template:r.buttonTemplate,title:"Superscript"})})),p("subscript",[{tags:["sub"]}]),f("subscript",new k({format:o.subscript,template:new s({template:r.buttonTemplate,title:"Subscript"})})),f("foreColor",new w({cssAttr:"color",domAttr:"color",name:"foreColor",template:new s({template:r.colorPickerTemplate,title:"Color"})})),f("backColor",new w({cssAttr:"background-color",domAttr:"backgroundColor",name:"backColor",template:new s({template:r.colorPickerTemplate,title:"Background Color"})})),f("fontName",new C({cssAttr:"font-family",domAttr:"fontFamily",name:"fontName",defaultValue:[{text:"fontNameInherit",value:"inherit"}],template:new s({template:r.comboBoxTemplate,title:"Font Name"})})),f("fontSize",new C({cssAttr:"font-size",domAttr:"fontSize",name:"fontSize",defaultValue:[{text:"fontSizeInherit",value:"inherit"}],template:new s({template:r.comboBoxTemplate,title:"Font Size"})}))}(window.kendo.jQuery)},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("editor/formatblock.min",["editor/inlineformat.min"],e)}(function(){!function(e){var t,n=window.kendo,i=n.Class,o=e.extend,r=n.ui.editor,a=n.ui.Editor.fn.options.formats,s=r.Dom,l=r.Command,d=r.ToolTemplate,c=r.FormatTool,u=r.EditorUtils,f=u.registerTool,p=u.registerFormat,h=r.RangeUtils,m=i.extend({init:function(e){this.format=e},contains:function(e,t){var n,i,o;for(n=0,i=t.length;n<i;n++)if(o=t[n],!o||!s.isAncestorOrSelf(e,o))return!1;return!0},findSuitable:function(t){var n,i,o,r,a=this.format,l=[];for(n=0,i=t.length;n<i;n++){for(r=a.length-1;r>=0&&!(o=s.ofType(t[n],a[r].tags)?t[n]:s.closestEditableOfType(t[n],a[r].tags));r--);if(!o||"true"===o.contentEditable)return[];e.inArray(o,l)<0&&l.push(o)}for(n=0,i=l.length;n<i;n++)if(this.contains(l[n],l))return[l[n]];return l},findFormat:function(e){var t,n,i,o,r,a=this.format,l=s.editableParent(e);for(t=0,n=a.length;t<n;t++)for(i=e,o=a[t].tags,r=a[t].attr;i&&s.isAncestorOf(l,i);){if(s.ofType(i,o)&&s.attrEquals(i,r))return i;i=i.parentNode}return null},getFormat:function(e){var t,n,i=this,o=function(e){return i.findFormat(s.isDataNode(e)?e.parentNode:e)},r=o(e[0]);if(!r)return"";for(t=1,n=e.length;t<n;t++)if(r!=o(e[t]))return"";return r.nodeName.toLowerCase()},isFormatted:function(e){for(var t=0,n=e.length;t<n;t++)if(!this.findFormat(e[t]))return!1;return!0}}),g=i.extend({init:function(e,t){this.format=e,this.values=t,this.finder=new m(e)},wrap:function(e,t,n){var i,o,r,a,l,d=1==n.length?s.blockParentOrBody(n[0]):s.commonAncestor.apply(null,n);for(s.isInline(d)&&(d=s.blockParentOrBody(d)),i=s.significantChildNodes(d),o=s.findNodeIndex(i[0]),r=s.create(d.ownerDocument,e,t),a=0;a<i.length;a++)l=i[a],s.isBlock(l)?(s.attr(l,t),r.childNodes.length&&(s.insertBefore(r,l),r=r.cloneNode(!1)),o=s.findNodeIndex(l)+1):r.appendChild(l);r.firstChild&&s.insertAt(d,r,o)},apply:function(t){function n(e){return o({},e&&e.attr,c)}var i,r,a,l,d,c=this.values,f=s.filter("img",t),p=u.formatByName("img",this.format),h=n(p);if(e.each(f,function(){s.attr(this,h)}),f.length!=t.length)if(r=s.filter("img",t,!0),a=this.finder.findSuitable(r),a.length)for(l=0,d=a.length;l<d;l++)i=u.formatByName(s.name(a[l]),this.format),s.attr(a[l],n(i));else i=this.format[0],this.wrap(i.tags[0],n(i),r)},remove:function(e){var t,n,i,o,r;for(t=0,n=e.length;t<n;t++)i=this.finder.findFormat(e[t]),i&&(r=s.name(i),"div"!=r||i.getAttribute("class")?(o=u.formatByName(r,this.format),o.attr.style&&s.unstyle(i,o.attr.style),o.attr.className&&s.removeClass(i,o.attr.className)):s.unwrap(i))},toggle:function(e){var t=this,n=h.nodes(e);t.finder.isFormatted(n)?t.remove(n):t.apply(n)}}),b=i.extend({init:function(e,t){var n=this;n.format=e,n.values=t,n.finder=new m(e)},apply:function(e){var t,n,i,o,a,l,d,c=this.format,u=s.blockParents(e),f=c[0].tags[0];if(u.length)for(t=0,n=u.length;t<n;t++)d=s.name(u[t]),"li"==d?(i=u[t].parentNode,o=new r.ListFormatter(i.nodeName.toLowerCase(),f),a=this.editor.createRange(),a.selectNode(u[t]),o.toggle(a)):f&&("td"==d||u[t].attributes.contentEditable)?new g(c,this.values).apply(u[t].childNodes):(l=s.changeTag(u[t],f),s.attr(l,c[0].attr));else new g(c,this.values).apply(e)},toggle:function(e){var t=h.textNodes(e);t.length||(e.selectNodeContents(e.commonAncestorContainer),t=h.textNodes(e),t.length||(t=s.significantChildNodes(e.commonAncestorContainer))),this.apply(t)}}),v=l.extend({init:function(e){e.formatter=e.formatter(),l.fn.init.call(this,e)}}),k=c.extend({init:function(e){c.fn.init.call(this,o(e,{finder:new m(e.format),formatter:function(){return new g(e.format)}}))}});o(r,{BlockFormatFinder:m,BlockFormatter:g,GreedyBlockFormatter:b,FormatCommand:v,BlockFormatTool:k}),t=["ul","ol","li"],p("justifyLeft",[{tags:s.nonListBlockElements,attr:{style:{textAlign:"left"}}},{tags:["img"],attr:{style:{"float":"left",display:"",marginLeft:"",marginRight:""}}},{tags:t,attr:{style:{textAlign:"left",listStylePosition:""}}}]),f("justifyLeft",new k({format:a.justifyLeft,template:new d({template:u.buttonTemplate,title:"Justify Left"})})),p("justifyCenter",[{tags:s.nonListBlockElements,attr:{style:{textAlign:"center"}}},{tags:["img"],attr:{style:{display:"block",marginLeft:"auto",marginRight:"auto","float":""}}},{tags:t,attr:{style:{textAlign:"center",listStylePosition:"inside"}}}]),f("justifyCenter",new k({format:a.justifyCenter,template:new d({template:u.buttonTemplate,title:"Justify Center"})})),p("justifyRight",[{tags:s.nonListBlockElements,attr:{style:{textAlign:"right"}}},{tags:["img"],attr:{style:{"float":"right",display:"",marginLeft:"",marginRight:""}}},{tags:t,attr:{style:{textAlign:"right",listStylePosition:"inside"}}}]),f("justifyRight",new k({format:a.justifyRight,template:new d({template:u.buttonTemplate,title:"Justify Right"})})),p("justifyFull",[{tags:s.nonListBlockElements,attr:{style:{textAlign:"justify"}}},{tags:["img"],attr:{style:{display:"block",marginLeft:"auto",marginRight:"auto","float":""}}},{tags:t,attr:{style:{textAlign:"justify",listStylePosition:""}}}]),f("justifyFull",new k({format:a.justifyFull,template:new d({template:u.buttonTemplate,title:"Justify Full"})}))}(window.kendo.jQuery)},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("editor/linebreak.min",["editor/formatblock.min"],e)}(function(){!function(e){var t=window.kendo,n=e.extend,i=t.ui.editor,o=i.Dom,r=i.Command,a=i.Tool,s=i.BlockFormatter,l=o.normalize,d=i.RangeUtils,c=i.EditorUtils.registerTool,u=r.extend({init:function(e){this.options=e,r.fn.init.call(this,e)},_insertMarker:function(e,t){var n,i=o.create(e,"a");return i.className="k-marker",t.insertNode(i),i.parentNode||(n=t.commonAncestorContainer,n.innerHTML="",n.appendChild(i)),l(i.parentNode),i},_moveFocus:function(e,t){if(o.isEmpty(t))e.setStartBefore(t);else{e.selectNodeContents(t);var n=d.textNodes(e)[0];if(!n){for(;t.childNodes.length&&!o.is(t.firstChild,"br");)t=t.firstChild;n=t}o.isEmpty(n)?e.setStartBefore(n):(o.emptyNode(n)&&(n.innerHTML="\ufeff"),e.setStartBefore(n.firstChild||n))}},shouldTrim:function(e){var t="p,h1,h2,h3,h4,h5,h6".split(","),n=o.parentOfType(e.startContainer,t),i=o.parentOfType(e.endContainer,t);return n&&!i||!n&&i},_blankAfter:function(e){for(;e&&(o.isMarker(e)||""===o.stripBom(e.nodeValue));)e=e.nextSibling;return!e},exec:function(){var e,t,n,r,a,c,u,f,p=this.getRange(),h=d.documentFromRange(p),m=i.emptyElementContent,g=this.shouldTrim(p);p.deleteContents(),a=this._insertMarker(h,p),o.stripBomNode(a.previousSibling),o.stripBomNode(a.nextSibling),c=o.closestEditableOfType(a,["li"]),u=o.closestEditableOfType(a,"h1,h2,h3,h4,h5,h6".split(",")),c?o.emptyNode(c)&&(r=o.create(h,"p"),c.nextSibling&&(f=p.cloneRange(),f.selectNode(c),d.split(f,c.parentNode)),o.insertAfter(r,c.parentNode),o.remove(1==c.parentNode.childNodes.length?c.parentNode:c),r.innerHTML=m,n=r):u&&this._blankAfter(a)&&(r=o.create(h,"p"),o.insertAfter(r,u),r.innerHTML=m,o.remove(a),n=r),n||(c||u||new s([{tags:["p"]}]).apply([a]),p.selectNode(a),e=o.parentOfType(a,[c?"li":u?o.name(u):"p"]),d.split(p,e,g),t=e.previousSibling,o.is(t,"li")&&t.firstChild&&!o.is(t.firstChild,"br")&&(t=t.firstChild),n=e.nextSibling,this.clean(t),this.clean(n,{links:!0}),o.is(n,"li")&&n.firstChild&&!o.is(n.firstChild,"br")&&(n=n.firstChild),o.remove(e),l(t)),l(n),this._moveFocus(p,n),p.collapse(!0),o.scrollTo(n),d.selectRange(p)},clean:function(t,n){var r,a=t;if(t.firstChild&&o.is(t.firstChild,"br")&&o.remove(t.firstChild),o.isDataNode(t)&&!t.nodeValue&&(t=t.parentNode),t){for(r=!1;t.firstChild&&1==t.firstChild.nodeType;)r=r||o.significantNodes(t.childNodes).length>1,t=t.firstChild;if(o.isEmpty(t)||!/^\s*$/.test(t.innerHTML)||r||(e(a).find(".k-br").remove(),t.innerHTML=i.emptyElementContent),n&&n.links)for(;t!=a;){if(o.is(t,"a")&&o.emptyNode(t)){o.unwrap(t);break}t=t.parentNode}}}}),f=r.extend({init:function(e){this.options=e,r.fn.init.call(this,e)},exec:function(){var e,n=this.getRange(),i=o.create(d.documentFromRange(n),"br"),r=t.support.browser,a=r.msie&&r.version<11;n.deleteContents(),n.insertNode(i),l(i.parentNode),a||i.nextSibling&&!o.isWhitespace(i.nextSibling)||(e=i.cloneNode(!0),e.className="k-br",o.insertAfter(e,i)),n.setStartAfter(i),n.collapse(!0),o.scrollTo(i.nextSibling||i),d.selectRange(n)}});n(i,{ParagraphCommand:u,NewLineCommand:f}),c("insertLineBreak",new a({key:13,shift:!0,command:f})),c("insertParagraph",new a({key:13,command:u}))}(window.kendo.jQuery)},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("editor/lists.min",["editor/linebreak.min"],e)}(function(){!function(e){var t=window.kendo,n=t.Class,i=e.extend,o=t.ui.editor,r=o.Dom,a=o.RangeUtils,s=o.EditorUtils,l=o.Command,d=o.ToolTemplate,c=o.FormatTool,u=o.BlockFormatFinder,f=a.textNodes,p=o.EditorUtils.registerTool,h=u.extend({init:function(e){this.tag=e;var t=this.tags=["ul"==e?"ol":"ul",e];u.fn.init.call(this,[{tags:t}])},isFormatted:function(e){var t,n,i=[];for(n=0;n<e.length;n++)t=this.findFormat(e[n]),t&&r.name(t)==this.tag&&i.push(t);if(i.length<1)return!1;if(i.length!=e.length)return!1;for(n=0;n<i.length&&i[n].parentNode==t.parentNode;n++)if(i[n]!=t)return!1;return!0},findSuitable:function(e){var t=this.findFormat(e[0]);return t&&r.name(t)==this.tag?t:null;
}}),m=n.extend({init:function(e,t){var n=this;n.finder=new h(e),n.tag=e,n.unwrapTag=t},isList:function(e){var t=r.name(e);return"ul"==t||"ol"==t||"dl"==t},wrap:function(e,t){var n,i,o=r.create(e.ownerDocument,"li");for(n=0;n<t.length;n++)if(i=t[n],r.is(i,"li"))e.appendChild(i);else if(this.isList(i))for(;i.firstChild;)e.appendChild(i.firstChild);else if(r.is(i,"td")){for(;i.firstChild;)o.appendChild(i.firstChild);e.appendChild(o),i.appendChild(e),e=e.cloneNode(!1),o=o.cloneNode(!1)}else o.appendChild(i),r.isBlock(i)&&(e.appendChild(o),r.unwrap(i),o=o.cloneNode(!1));o.firstChild&&e.appendChild(o)},containsAny:function(e,t){for(var n=0;n<t.length;n++)if(r.isAncestorOrSelf(e,t[n]))return!0;return!1},suitable:function(e,t){if("k-marker"==e.className){var n=e.nextSibling;if(n&&r.isBlock(n))return!1;if(n=e.previousSibling,n&&r.isBlock(n))return!1}return this.containsAny(e,t)||r.isInline(e)||3==e.nodeType},_parentLists:function(t){var n=r.closestEditable(t);return e(t).parentsUntil(n,"ul,ol")},split:function(e){var t,n,i,o,s,l,d=f(e);if(d.length)for(t=r.parentOfType(d[0],["li"]),n=r.parentOfType(d[d.length-1],["li"]),e.setStartBefore(t),e.setEndAfter(n),o=0,s=d.length;o<s;o++)l=this.finder.findFormat(d[o]),l&&(i=this._parentLists(l),i.length?a.split(e,i.last()[0],!0):a.split(e,l,!0))},merge:function(e,t){for(var n,i=t.previousSibling;i&&("k-marker"==i.className||3==i.nodeType&&r.isWhitespace(i));)i=i.previousSibling;if(i&&r.name(i)==e){for(;t.firstChild;)i.appendChild(t.firstChild);r.remove(t),t=i}for(n=t.nextSibling;n&&("k-marker"==n.className||3==n.nodeType&&r.isWhitespace(n));)n=n.nextSibling;if(n&&r.name(n)==e){for(;t.lastChild;)n.insertBefore(t.lastChild,n.firstChild);r.remove(t)}},breakable:function(e){return e!=e.ownerDocument.body&&!/table|tbody|tr|td/.test(r.name(e))&&!e.attributes.contentEditable},applyOnSection:function(t,n){function i(){u.push(this)}var o,a,s,l,d=this.tag,c=r.closestSplittableParent(n),u=[],f=this.finder.findSuitable(n);for(f||(f=new h("ul"==d?"ol":"ul").findSuitable(n)),/table|tbody/.test(r.name(c))?o=e.map(n,function(e){return r.parentOfType(e,["td"])}):(o=r.significantChildNodes(c),e.grep(o,r.isBlock).length&&(o=e.grep(o,e.proxy(function(e){return this.containsAny(e,n)},this))),o.length||(o=n)),a=0;a<o.length;a++)s=o[a],l=(!f||!r.isAncestorOrSelf(f,s))&&this.suitable(s,n),l&&(f&&this.isList(s)?(e.each(s.childNodes,i),r.remove(s)):u.push(s));u.length==o.length&&this.breakable(c)&&(u=[c]),f||(f=r.create(c.ownerDocument,d),r.insertBefore(f,u[0])),this.wrap(f,u),r.is(f,d)||r.changeTag(f,d),this.merge(d,f)},apply:function(e){var t,n,i,o=0,a=[];do i=r.closestEditable(e[o],["td","body"]),t&&i==t?n.push(e[o]):(t&&a.push({section:t,nodes:n}),n=[e[o]],t=i),o++;while(o<e.length);for(a.push({section:t,nodes:n}),o=0;o<a.length;o++)this.applyOnSection(a[o].section,a[o].nodes)},unwrap:function(e){var t,n,i,o,a=e.ownerDocument.createDocumentFragment(),s=this.unwrapTag;for(n=e.firstChild;n;n=n.nextSibling){for(i=r.create(e.ownerDocument,s||"p");n.firstChild;)o=n.firstChild,r.isBlock(o)?(i.firstChild&&(a.appendChild(i),i=r.create(e.ownerDocument,s||"p")),a.appendChild(o)):i.appendChild(o);i.firstChild&&a.appendChild(i)}t=this._parentLists(e),t[0]?(r.insertAfter(a,t.last()[0]),t.last().remove()):r.insertAfter(a,e),r.remove(e)},remove:function(e){var t,n,i;for(n=0,i=e.length;n<i;n++)t=this.finder.findFormat(e[n]),t&&this.unwrap(t)},toggle:function(e){var t,n=this,i=f(e),o=e.commonAncestorContainer;i.length||(e.selectNodeContents(o),i=f(e),i.length||(t=o.ownerDocument.createTextNode(""),e.startContainer.appendChild(t),i=[t],e.selectNode(t.parentNode))),n.finder.isFormatted(i)?(n.split(e),n.remove(i)):n.apply(i)}}),g=l.extend({init:function(e){e.formatter=new m(e.tag),l.fn.init.call(this,e)}}),b=c.extend({init:function(e){this.options=e,c.fn.init.call(this,i(e,{finder:new h(e.tag)}))},command:function(e){return new g(i(e,{tag:this.options.tag}))}});i(o,{ListFormatFinder:h,ListFormatter:m,ListCommand:g,ListTool:b}),p("insertUnorderedList",new b({tag:"ul",template:new d({template:s.buttonTemplate,title:"Insert unordered list"})})),p("insertOrderedList",new b({tag:"ol",template:new d({template:s.buttonTemplate,title:"Insert ordered list"})}))}(window.kendo.jQuery)},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("editor/link.min",["editor/lists.min"],e)}(function(){!function(e,t){function n(e,t){for(var n=e.length;n--&&!t.test(e[n]););return n}function i(e,t){var n=t.exec(e);return n?n.index:-1}var o=window.kendo,r=o.Class,a=e.extend,s=e.proxy,l=o.ui.editor,d=l.Dom,c=l.RangeUtils,u=l.EditorUtils,f=l.Command,p=l.Tool,h=l.ToolTemplate,m=l.InlineFormatter,g=l.InlineFormatFinder,b=c.textNodes,v=l.EditorUtils.registerTool,k=o.keys,y="http://",C=/^\w*:\/\//,w=/[\w\/\$\-_\*\?]/i,x=r.extend({findSuitable:function(e){return d.parentOfType(e,["a"])}}),T=r.extend({init:function(){this.finder=new x},apply:function(e,t){var n,i,o,r,a,s,l,u=b(e);if(t.innerHTML){for(i=c.documentFromRange(e),n=c.getMarkers(e),e.deleteContents(),r=d.create(i,"a",t),e.insertNode(r),a=r.parentNode,"a"==d.name(a)&&d.insertAfter(r,a),d.emptyNode(a)&&d.remove(a),s=r,l=0;l<n.length;l++)d.insertAfter(n[l],s),s=n[l];n.length&&(d.insertBefore(i.createTextNode("\ufeff"),n[1]),d.insertAfter(i.createTextNode("\ufeff"),n[1]),e.setStartBefore(n[0]),e.setEndAfter(n[n.length-1]))}else o=new m([{tags:["a"]}],t),o.finder=this.finder,o.apply(u)}}),_=f.extend({init:function(e){e.formatter={toggle:function(e){new m([{tags:["a"]}]).remove(b(e))}},this.options=e,f.fn.init.call(this,e)}}),F=f.extend({init:function(e){this.options=e,f.fn.init.call(this,e),this.formatter=new T,e.url?this.exec=function(){this.formatter.apply(e.range,{href:e.url,innerHTML:e.text||e.url,target:e.target})}:(this.attributes=null,this.async=!0)},_dialogTemplate:function(){return o.template("<div class=\"k-editor-dialog k-popup-edit-form k-edit-form-container\"><div class='k-edit-label'><label for='k-editor-link-url'>#: messages.linkWebAddress #</label></div><div class='k-edit-field'><input type='text' class='k-input k-textbox' id='k-editor-link-url'></div><div class='k-edit-label k-editor-link-text-row'><label for='k-editor-link-text'>#: messages.linkText #</label></div><div class='k-edit-field k-editor-link-text-row'><input type='text' class='k-input k-textbox' id='k-editor-link-text'></div><div class='k-edit-label'><label for='k-editor-link-title'>#: messages.linkToolTip #</label></div><div class='k-edit-field'><input type='text' class='k-input k-textbox' id='k-editor-link-title'></div><div class='k-edit-label'></div><div class='k-edit-field'><input type='checkbox' class='k-checkbox' id='k-editor-link-target'><label for='k-editor-link-target' class='k-checkbox-label'>#: messages.linkOpenInNewWindow #</label></div><div class='k-edit-buttons k-state-default'><button class=\"k-dialog-insert k-button k-primary\">#: messages.dialogInsert #</button><button class=\"k-dialog-close k-button\">#: messages.dialogCancel #</button></div></div>")({messages:this.editor.options.messages})},exec:function(){var t,n,i,o,r=this.editor.options.messages;this._initialText="",this._range=this.lockRange(!0),t=b(this._range),n=t.length?this.formatter.finder.findSuitable(t[0]):null,i=t.length&&"img"==d.name(t[0]),o=this.createDialog(this._dialogTemplate(),{title:r.createLink,close:s(this._close,this),visible:!1}),n&&(this._range.selectNodeContents(n),t=b(this._range)),this._initialText=this.linkText(t),o.find(".k-dialog-insert").click(s(this._apply,this)).end().find(".k-dialog-close").click(s(this._close,this)).end().find(".k-edit-field input").keydown(s(this._keydown,this)).end().find("#k-editor-link-url").val(this.linkUrl(n)).end().find("#k-editor-link-text").val(this._initialText).end().find("#k-editor-link-title").val(n?n.title:"").end().find("#k-editor-link-target").attr("checked",!!n&&"_blank"==n.target).end().find(".k-editor-link-text-row").toggle(!i),this._dialog=o.data("kendoWindow").center().open(),e("#k-editor-link-url",o).focus().select()},_keydown:function(e){var t=o.keys;e.keyCode==t.ENTER?this._apply(e):e.keyCode==t.ESC&&this._close(e)},_apply:function(t){var n,i,o,r=this._dialog.element,a=e("#k-editor-link-url",r).val(),s=e("#k-editor-link-text",r);a&&a!=y&&(a.indexOf("@")>0&&!/^(\w+:)|(\/\/)/i.test(a)&&(a="mailto:"+a),this.attributes={href:a},n=e("#k-editor-link-title",r).val(),n&&(this.attributes.title=n),s.is(":visible")&&(i=s.val(),i||this._initialText?i&&i!==this._initialText&&(this.attributes.innerHTML=d.stripBom(i)):this.attributes.innerHTML=a),o=e("#k-editor-link-target",r).is(":checked"),this.attributes.target=o?"_blank":null,this.formatter.apply(this._range,this.attributes)),this._close(t),this.change&&this.change()},_close:function(e){e.preventDefault(),this._dialog.destroy(),d.windowFromDocument(c.documentFromRange(this._range)).focus(),this.releaseRange(this._range)},linkUrl:function(e){return e?e.getAttribute("href",2):y},linkText:function(e){var t,n="";for(t=0;t<e.length;t++)n+=e[t].nodeValue;return d.stripBom(n||"")},redo:function(){var e=this.lockRange(!0);this.formatter.apply(e,this.attributes),this.releaseRange(e)}}),S=f.extend({init:function(e){f.fn.init.call(this,e),this.formatter=new T},exec:function(){var e,t,n,i=this.detectLink();i&&(e=this.getRange(),t=new o.ui.editor.Marker,n=e.cloneRange(),n.setStart(i.start.node,i.start.offset),n.setEnd(i.end.node,i.end.offset),e=this.lockRange(),t.add(n),this.formatter.apply(n,{href:this._ensureWebProtocol(i.text)}),t.remove(n),this.releaseRange(e))},detectLink:function(){var e=this.getRange(),t=new I({node:e.startContainer,offset:e.startOffset,cancelAtNode:function(e){return e&&"a"===d.name(e)}}),n=new A(t);return n.detectLink()},changesContent:function(){return!!this.detectLink()},_ensureWebProtocol:function(e){var t=this._hasProtocolPrefix(e);return t?e:this._prefixWithWebProtocol(e)},_hasProtocolPrefix:function(e){return C.test(e)},_prefixWithWebProtocol:function(e){return y+e}}),N=p.extend({init:function(t){this.options=t,this.finder=new g([{tags:["a"]}]),p.fn.init.call(this,e.extend(t,{command:_}))},initialize:function(e,t){p.fn.initialize.call(this,e,t),e.addClass("k-state-disabled")},update:function(e,t){e.toggleClass("k-state-disabled",!this.finder.isFormatted(t)).removeClass("k-state-hover")}}),A=r.extend({init:function(e){this.traverser=e,this.start=R(),this.end=R(),this.text=""},detectLink:function(){var t,n,i,o,r=this.traverser.node,a=this.traverser.offset;if(d.isDataNode(r)){if(t=r.data.substring(0,a),/\s{2}$/.test(d.stripBom(t)))return}else 0===a&&(n=d.closestEditableOfType(r,d.blockElements),n&&n.previousSibling&&this.traverser.init({node:n.previousSibling}));return this.traverser.traverse(e.proxy(this._detectEnd,this)),this.end.blank()||(this.traverser=this.traverser.clone(this.end),this.traverser.traverse(e.proxy(this._detectStart,this)),this._isLinkDetected()||(i=this.traverser.extendOptions(this.start),o=new B(i),o.traverse(e.proxy(this._skipStartPuntuation,this)),this._isLinkDetected()||(this.start=R()))),this.start.blank()?null:{start:this.start,end:this.end,text:this.text}},_isLinkDetected:function(){return C.test(this.text)||/^w{3}\./i.test(this.text)},_detectEnd:function(e,t){var i=n(e,w);if(i>-1)return this.end.node=t,this.end.offset=i+1,!1},_detectStart:function(e,t){var i=n(e,/\s/),o=i+1;if(this.text=e.substring(o)+this.text,this.start.node=t,this.start.offset=o,i>-1)return!1},_skipStartPuntuation:function(e,t,n){var o=i(e,/\w/),r=o;if(o===-1&&(r=e.length),this.text=this.text.substring(r),this.start.node=t,this.start.offset=r+(0|n),o>-1)return!1}}),R=function(){return{node:null,offset:null,blank:function(){return null===this.node&&null===this.offset}}},E=r.extend({init:function(n){this.node=n.node,this.offset=n.offset===t?d.isDataNode(this.node)&&this.node.length||0:n.offset,this.cancelAtNode=n.cancelAtNode||this.cancelAtNode||e.noop},traverse:function(e){e&&(this.cancel=!1,this._traverse(e,this.node,this.offset))},_traverse:function(e,n,i){var o,r,a,s;if(n&&!this.cancel){if(3!==n.nodeType)return r=this.edgeNode(n),this.cancel=this.cancel||this.cancelAtNode(r),this._traverse(e,r);if(o=n.data,i!==t&&(o=this.subText(o,i)),this.cancel=e(o,n,i)===!1,a=this.next(n),!a)for(s=n.parentNode;!a&&d.isInline(s);)a=this.next(s),s=s.parentNode;this.cancel=this.cancel||this.cancelAtNode(a),this._traverse(e,a)}},extendOptions:function(t){return e.extend({node:this.node,offset:this.offset,cancelAtNode:this.cancelAtNode},t||{})},edgeNode:function(e){},next:function(e){},subText:function(e,t){}}),I=E.extend({subText:function(e,t){return e.substring(0,t)},next:function(e){return e.previousSibling},edgeNode:function(e){return e.lastChild},clone:function(e){var t=this.extendOptions(e);return new I(t)}}),B=E.extend({subText:function(e,t){return e.substring(t)},next:function(e){return e.nextSibling},edgeNode:function(e){return e.firstChild},clone:function(e){var t=this.extendOptions(e);return new B(t)}});a(o.ui.editor,{LinkFormatFinder:x,LinkFormatter:T,UnlinkCommand:_,LinkCommand:F,AutoLinkCommand:S,UnlinkTool:N,DomTextLinkDetection:A,LeftDomTextTraverser:I,RightDomTextTraverser:B}),v("createLink",new p({key:"K",ctrl:!0,command:F,template:new h({template:u.buttonTemplate,title:"Create Link"})})),v("unlink",new N({key:"K",ctrl:!0,shift:!0,template:new h({template:u.buttonTemplate,title:"Remove Link"})})),v("autoLink",new p({key:[k.ENTER,k.SPACEBAR],keyPressCommand:!0,command:S}))}(window.kendo.jQuery)},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("editor/file.min",["kendo.filebrowser.min","editor/link.min"],e)}(function(){!function(e,t){var n=window.kendo,i=e.extend,o=n.ui.editor,r=o.EditorUtils,a=o.Dom,s=r.registerTool,l=o.ToolTemplate,d=o.RangeUtils,c=o.Command,u=o.LinkFormatter,f=d.textNodes,p=n.keys,h="#k-editor-file-url",m="#k-editor-file-title",g=c.extend({init:function(e){var t=this;c.fn.init.call(t,e),t.formatter=new u,t.async=!0,t.attributes={}},insertFile:function(e,t){var n=this.attributes,i=d.documentFromRange(t);if(n.href&&"http://"!=n.href){if(!e)return e=a.create(i,"a",{href:n.href}),e.innerHTML=n.innerHTML,t.deleteContents(),t.insertNode(e),e.nextSibling||a.insertAfter(i.createTextNode("\ufeff"),e),t.setStartAfter(e),t.setEndAfter(e),d.selectRange(t),!0;a.attr(e,n)}return!1},_dialogTemplate:function(e){return n.template('<div class="k-editor-dialog k-popup-edit-form k-edit-form-container"># if (showBrowser) { #<div class="k-filebrowser"></div># } #<div class=\'k-edit-label\'><label for="k-editor-file-url">#: messages.fileWebAddress #</label></div><div class=\'k-edit-field\'><input type="text" class="k-input k-textbox" id="k-editor-file-url"></div><div class=\'k-edit-label\'><label for="k-editor-file-title">#: messages.fileTitle #</label></div><div class=\'k-edit-field\'><input type="text" class="k-input k-textbox" id="k-editor-file-title"></div><div class="k-edit-buttons k-state-default"><button class="k-dialog-insert k-button k-primary">#: messages.dialogInsert #</button><button class="k-dialog-close k-button">#: messages.dialogCancel #</button></div></div>')({messages:this.editor.options.messages,showBrowser:e})},redo:function(){var e=this,t=e.lockRange();this.formatter.apply(t,this.attributes),e.releaseRange(t)},exec:function(){function e(e){var t=s.element,n=t.find(h).val().replace(/ /g,"%20"),i=t.find(m).val();l.attributes={href:n,innerHTML:""!==i?i:n},g=l.insertFile(b,c),o(e),l.change&&l.change()}function o(e){e.preventDefault(),s.destroy(),a.windowFromDocument(d.documentFromRange(c)).focus(),g||l.releaseRange(c)}function r(t){t.keyCode==p.ENTER?e(t):t.keyCode==p.ESC&&o(t)}var s,l=this,c=l.lockRange(),u=f(c),g=!1,b=u.length?this.formatter.finder.findSuitable(u[0]):null,v=l.editor.options,k=v.messages,y=v.fileBrowser,C=!!(n.ui.FileBrowser&&y&&y.transport&&y.transport.read!==t),w={title:k.insertFile,visible:!1,resizable:C};w.close=o,C&&(w.width=750),s=this.createDialog(l._dialogTemplate(C),w).toggleClass("k-filebrowser-dialog",C).find(".k-dialog-insert").click(e).end().find(".k-dialog-close").click(o).end().find(".k-edit-field input").keydown(r).end().find(h).val(b?b.getAttribute("href",2):"http://").end().find(m).val(b?b.title:"").end().data("kendoWindow"),C&&(l._fileBrowser=new n.ui.FileBrowser(s.element.find(".k-filebrowser"),i({},y,{change:function(){s.element.find(h).val(this.value())},apply:e}))),s.center().open(),s.element.find(h).focus().select()}});n.ui.editor.FileCommand=g,s("insertFile",new o.Tool({command:g,template:new l({template:r.buttonTemplate,title:"Insert File"})}))}(window.kendo.jQuery)},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("editor/image.min",["kendo.imagebrowser.min","editor/link.min"],e)}(function(){!function(e,t){var n=window.kendo,i=e.extend,o=n.ui.editor,r=o.EditorUtils,a=o.Dom,s=r.registerTool,l=o.ToolTemplate,d=o.RangeUtils,c=o.Command,u=n.keys,f="#k-editor-image-url",p="#k-editor-image-title",h="#k-editor-image-width",m="#k-editor-image-height",g=c.extend({init:function(e){var t=this;c.fn.init.call(t,e),t.async=!0,t.attributes={}},insertImage:function(e,t){var n,i=this.attributes,o=d.documentFromRange(t);if(i.src&&"http://"!=i.src){if(n=function(){setTimeout(function(){i.width||e.removeAttribute("width"),i.height||e.removeAttribute("height"),e.removeAttribute("complete")})},!e)return e=a.create(o,"img",i),e.onload=e.onerror=n,t.deleteContents(),t.insertNode(e),e.nextSibling||a.insertAfter(o.createTextNode("\ufeff"),e),n(),t.setStartAfter(e),t.setEndAfter(e),d.selectRange(t),!0;e.onload=e.onerror=n,a.attr(e,i),n()}return!1},_dialogTemplate:function(e){return n.template('<div class="k-editor-dialog k-popup-edit-form k-edit-form-container"># if (showBrowser) { #<div class="k-filebrowser k-imagebrowser"></div># } #<div class=\'k-edit-label\'><label for="k-editor-image-url">#: messages.imageWebAddress #</label></div><div class=\'k-edit-field\'><input type="text" class="k-input k-textbox" id="k-editor-image-url"></div><div class=\'k-edit-label\'><label for="k-editor-image-title">#: messages.imageAltText #</label></div><div class=\'k-edit-field\'><input type="text" class="k-input k-textbox" id="k-editor-image-title"></div><div class=\'k-edit-label\'><label for="k-editor-image-width">#: messages.imageWidth #</label></div><div class=\'k-edit-field\'><input type="text" class="k-input k-textbox" id="k-editor-image-width"></div><div class=\'k-edit-label\'><label for="k-editor-image-height">#: messages.imageHeight #</label></div><div class=\'k-edit-field\'><input type="text" class="k-input k-textbox" id="k-editor-image-height"></div><div class="k-edit-buttons k-state-default"><button class="k-dialog-insert k-button k-primary">#: messages.dialogInsert #</button><button class="k-dialog-close k-button">#: messages.dialogCancel #</button></div></div>')({messages:this.editor.options.messages,showBrowser:e})},redo:function(){var e=this,t=e.lockRange();e.insertImage(d.image(t),t)||e.releaseRange(t)},exec:function(){function e(e){var t=s.element,n=parseInt(t.find(h).val(),10),i=parseInt(t.find(m).val(),10);l.attributes={src:t.find(f).val().replace(/ /g,"%20"),alt:t.find(p).val()},l.attributes.width=null,l.attributes.height=null,!isNaN(n)&&n>0&&(l.attributes.width=n),!isNaN(i)&&i>0&&(l.attributes.height=i),g=l.insertImage(b,c),o(e),l.change&&l.change()}function o(e){e.preventDefault(),s.destroy(),a.windowFromDocument(d.documentFromRange(c)).focus(),g||l.releaseRange(c)}function r(t){t.keyCode==u.ENTER?e(t):t.keyCode==u.ESC&&o(t)}var s,l=this,c=l.lockRange(),g=!1,b=d.image(c),v=b&&b.getAttribute("width")||"",k=b&&b.getAttribute("height")||"",y=l.editor.options,C=y.messages,w=y.imageBrowser,x=!!(n.ui.ImageBrowser&&w&&w.transport&&w.transport.read!==t),T={title:C.insertImage,visible:!1,resizable:x};T.close=o,x&&(T.width=750),s=this.createDialog(l._dialogTemplate(x),T).toggleClass("k-filebrowser-dialog",x).find(".k-dialog-insert").click(e).end().find(".k-dialog-close").click(o).end().find(".k-edit-field input").keydown(r).end().find(f).val(b?b.getAttribute("src",2):"http://").end().find(p).val(b?b.alt:"").end().find(h).val(v).end().find(m).val(k).end().data("kendoWindow"),x&&(this._imageBrowser=new n.ui.ImageBrowser(s.element.find(".k-imagebrowser"),i({},w,{change:function(){s.element.find(f).val(this.value())},apply:e}))),s.center().open(),s.element.find(f).focus().select()}});n.ui.editor.ImageCommand=g,s("insertImage",new o.Tool({command:g,template:new l({template:r.buttonTemplate,title:"Insert Image"})}))}(window.kendo.jQuery)},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("editor/components.min",["editor/image.min"],e)}(function(){!function(e,t){var n=window.kendo,i=n.ui.DropDownList,o=n.ui.editor.Dom,r=i.extend({init:function(t,o){var r=this;i.fn.init.call(r,t,o),n.support.mobileOS.ios&&(this._initSelectOverlay(),this.bind("dataBound",e.proxy(this._initSelectOverlay,this))),r.text(r.options.title),r.bind("open",function(){if(r.options.autoSize){var e,t=r.list;t.css({whiteSpace:"nowrap",width:"auto"}),e=t.width(),e?e+=20:e=r._listWidth,t.css("width",e+n.support.scrollbar()),r._listWidth=e}})},options:{name:"SelectBox",index:-1},_initSelectOverlay:function(){var t,i,o,r,a=this,s=a.value(),l=this.dataSource.view(),d="",c=n.htmlEncode;for(i=0;i<l.length;i++)t=l[i],d+="<option value='"+c(t.value)+"'",t.value==s&&(d+=" selected"),d+=">"+c(t.text)+"</option>";o=e("<select class='k-select-overlay'>"+d+"</select>"),r=e(this.element).closest(".k-widget"),r.next(".k-select-overlay").remove(),o.insertAfter(r),o.on("change",function(){a.value(this.value),a.trigger("change")})},value:function(e){var n=this,o=i.fn.value.call(n,e);return e===t?o:(i.fn.value.call(n)||n.text(n.options.title),t)},decorate:function(t){var n,i,r,a,s=this,l=s.dataSource,d=l.data();for(t&&s.list.css("background-color",o.getEffectiveBackground(e(t))),n=0;n<d.length;n++)i=d[n].tag||"span",r=d[n].className,a=o.inlineStyle(t,i,{className:r}),a=a.replace(/"/g,"'"),d[n].style=a+";display:inline-block";l.trigger("change")}});n.ui.plugin(r),n.ui.editor.SelectBox=r}(window.kendo.jQuery)},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("editor/indent.min",["editor/components.min"],e)}(function(){!function(e,t){function n(n,i){var o="rtl"==e(n).css("direction"),r=o?"Right":"Left",a="td"!=s.name(n)?"margin"+r:"padding"+r;return i===t?n.style[a]||0:(i>0?n.style[a]=i+"px":(n.style[a]="",n.style.cssText||n.removeAttribute("style")),t)}var i=window.kendo,o=i.Class,r=e.extend,a=i.ui.editor,s=a.Dom,l=a.EditorUtils,d=l.registerTool,c=a.Command,u=a.Tool,f=a.ToolTemplate,p=a.RangeUtils,h=s.blockElements,m=a.BlockFormatFinder,g=a.BlockFormatter,b=o.extend({init:function(){this.finder=new m([{tags:s.blockElements}])},apply:function(t){var i,o,r,a,l,d,c,u,f,p,h=this.finder.findSuitable(t),m=[];if(h.length){for(i=0,o=h.length;i<o;i++)s.is(h[i],"li")?e(h[i]).index()?e.inArray(h[i].parentNode,m)<0&&m.push(h[i]):m.push(h[i].parentNode):m.push(h[i]);for(;m.length;)if(r=m.shift(),s.is(r,"li"))if(a=r.parentNode,l=e(r).prev("li"),d=l.find("ul,ol").last(),c=e(r).children("ul,ol")[0],c&&l[0])d[0]?(d.append(r),d.append(e(c).children()),s.remove(c)):(l.append(c),c.insertBefore(r,c.firstChild));else for(c=l.children("ul,ol")[0],c||(c=s.create(r.ownerDocument,s.name(a)),l.append(c));r&&r.parentNode==a;)c.appendChild(r),r=m.shift();else for(u=parseInt(n(r),10)+30,n(r,u),f=0;f<m.length;f++)e.contains(r,m[f])&&m.splice(f,1)}else p=new g([{tags:["p"]}],{style:{marginLeft:30}}),p.apply(t)},remove:function(t){var i,o,r,a,s,l,d,c,u=this.finder.findSuitable(t);for(o=0,r=u.length;o<r;o++){if(d=e(u[o]),d.is("li")){if(a=d.parent(),s=a.parent(),s.is("li,ul,ol")&&!n(a[0])){if(i&&e.contains(i,s[0]))continue;l=d.nextAll("li"),l.length&&e(a[0].cloneNode(!1)).appendTo(d).append(l),s.is("li")?d.insertAfter(s):d.appendTo(s),a.children("li").length||a.remove();continue}if(i==a[0])continue;i=a[0]}else i=u[o];c=parseInt(n(i),10)-30,n(i,c)}}}),v=c.extend({init:function(e){e.formatter={toggle:function(e){(new b).apply(p.nodes(e))}},c.fn.init.call(this,e)}}),k=c.extend({init:function(e){e.formatter={toggle:function(e){(new b).remove(p.nodes(e))}},c.fn.init.call(this,e)}}),y=u.extend({init:function(e){u.fn.init.call(this,e),this.finder=new m([{tags:h}])},initialize:function(e,t){u.fn.initialize.call(this,e,t),e.addClass("k-state-disabled")},update:function(i,o){var r,a,l,d,c=this.finder.findSuitable(o);for(l=0,d=c.length;l<d;l++)if(r=n(c[l]),r||(a=e(c[l]).parents("ul,ol").length,r=s.is(c[l],"li")&&(a>1||n(c[l].parentNode))||s.ofType(c[l],["ul","ol"])&&a>0),r)return i.removeClass("k-state-disabled"),t;i.addClass("k-state-disabled").removeClass("k-state-hover")}});r(a,{IndentFormatter:b,IndentCommand:v,OutdentCommand:k,OutdentTool:y}),d("indent",new u({command:v,template:new f({template:l.buttonTemplate,title:"Indent"})})),d("outdent",new y({command:k,template:new f({template:l.buttonTemplate,title:"Outdent"})}))}(window.kendo.jQuery)},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("editor/viewhtml.min",["editor/indent.min"],e)}(function(){!function(e,t){var n=window.kendo,i=e.extend,o=n.ui.editor,r=o.EditorUtils,a=o.Command,s=o.Tool,l=o.ToolTemplate,d=a.extend({init:function(e){var t=this;t.options=e,a.fn.init.call(t,e),t.attributes=null,t.async=!0},exec:function(){function t(e){r.value(s.find(c).val()),i(e),o.change&&o.change(),r.trigger("change")}function i(e){e.preventDefault(),s.data("kendoWindow").destroy(),r.focus()}var o=this,r=o.editor,a=r.options.messages,s=e(n.template(d.template)(a)).appendTo(document.body),l=d.indent(r.value()),c=".k-editor-textarea";this.createDialog(s,{title:a.viewHtml,close:i,visible:!1}).find(c).val(l).end().find(".k-dialog-update").click(t).end().find(".k-dialog-close").click(i).end().data("kendoWindow").center().open(),s.find(c).focus()}});i(d,{template:"<div class='k-editor-dialog k-popup-edit-form k-edit-form-container k-viewhtml-dialog'><textarea class='k-editor-textarea k-input'></textarea><div class='k-edit-buttons k-state-default'><button class='k-dialog-update k-button k-primary'>#: dialogUpdate #</button><button class='k-dialog-close k-button'>#: dialogCancel #</button></div></div>",indent:function(e){return e.replace(/<\/(p|li|ul|ol|h[1-6]|table|tr|td|th)>/gi,"</$1>\n").replace(/<(ul|ol)([^>]*)><li/gi,"<$1$2>\n<li").replace(/<br \/>/gi,"<br />\n").replace(/\n$/,"")}}),n.ui.editor.ViewHtmlCommand=d,o.EditorUtils.registerTool("viewHtml",new s({command:d,template:new l({template:r.buttonTemplate,title:"View HTML"})}))}(window.kendo.jQuery)},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("editor/formatting.min",["editor/viewhtml.min"],e)}(function(){!function(e){function t(e){var t,o,r=l.closestEditableOfType(e,["li"]);r&&(t=new i.ListFormatter(l.name(r.parentNode)),o=n.ui.editor.W3CRange.fromNode(e),o.selectNode(r),t.toggle(o))}var n=window.kendo,i=n.ui.editor,o=i.Tool,r=i.ToolTemplate,a=i.DelayedExecutionTool,s=i.Command,l=i.Dom,d=i.EditorUtils,c=i.RangeUtils,u=d.registerTool,f=a.extend({init:function(e){var t=this;o.fn.init.call(t,n.deepExtend({},t.options,e)),t.type="kendoSelectBox",t.finder={getFormat:function(){return""}}},options:{items:[{text:"Paragraph",value:"p"},{text:"Quotation",value:"blockquote"},{text:"Heading 1",value:"h1"},{text:"Heading 2",value:"h2"},{text:"Heading 3",value:"h3"},{text:"Heading 4",value:"h4"},{text:"Heading 5",value:"h5"},{text:"Heading 6",value:"h6"}],width:110},toFormattingItem:function(e){var t,n=e.value;return n?e.tag||e.className?e:(t=n.indexOf("."),0===t?e.className=n.substring(1):t==-1?e.tag=n:(e.tag=n.substring(0,t),e.className=n.substring(t+1)),e):e},command:function(t){var n=t.value;return n=this.toFormattingItem(n),new i.FormatCommand({range:t.range,formatter:function(){var t,o=(n.tag||n.context||"span").split(","),r=[{tags:o,attr:{className:n.className||""}}];return t=e.inArray(o[0],l.inlineElements)>=0?new i.GreedyInlineFormatter(r):new i.GreedyBlockFormatter(r)}})},initialize:function(e,t){var i=t.editor,r=this.options,a=r.name,s=this;e.width(r.width),e.kendoSelectBox({dataTextField:"text",dataValueField:"value",dataSource:r.items||i.options[a],title:i.options.messages[a],autoSize:!0,change:function(){var e=this.dataItem();e&&o.exec(i,a,e.toJSON())},dataBound:function(){var e,t=this.dataSource.data();for(e=0;e<t.length;e++)t[e]=s.toFormattingItem(t[e])},highlightFirst:!1,template:n.template('<span unselectable="on" style="display:block;#=(data.style||"")#">#:data.text#</span>')}),e.addClass("k-decorated").closest(".k-widget").removeClass("k-"+a).find("*").addBack().attr("unselectable","on")},getFormattingValue:function(t,n){var i,o,r,a,s,l,d;for(i=0;i<t.length;i++)if(o=t[i],r=o.tag||o.context||"",a=o.className?"."+o.className:"",s=r+a,l=e(n[0]).closest(s)[0]){if(1==n.length)return o.value;for(d=1;d<n.length&&e(n[d]).closest(s)[0];d++)if(d==n.length-1)return o.value}return""},update:function(t,n){var i,o,r,s,d,c=e(t).data(this.type);if(c&&(i=c.dataSource,o=i.data(),d=l.commonAncestor.apply(null,n),d==l.closestEditable(d)||this._ancestor!=d)){for(this._ancestor=d,r=0;r<o.length;r++)s=o[r].context,o[r].visible=!s||!!e(d).closest(s).length;i.filter([{field:"visible",operator:"eq",value:!0}]),a.fn.update.call(this,t,n),c.value(this.getFormattingValue(i.view(),n)),c.wrapper.toggleClass("k-state-disabled",!i.view().length)}},destroy:function(){this._ancestor=null}}),p=s.extend({exec:function(){var e,t,n=this.lockRange(!0);for(this.tagsToClean=this.options.remove||"strong,em,span,sup,sub,del,b,i,u,font".split(","),c.wrapSelectedElements(n),e=c.mapAll(n,function(e){return e}),t=e.length-1;t>=0;t--)this.clean(e[t]);this.releaseRange(n)},clean:function(n){var o,r,a,s,d;if(n&&!l.isMarker(n)){if(o=l.name(n),"ul"==o||"ol"==o)for(r=new i.ListFormatter(o),a=n.previousSibling,s=n.nextSibling,r.unwrap(n);a&&a!=s;a=a.nextSibling)this.clean(a);else if("blockquote"==o)l.changeTag(n,"p");else if(1!=n.nodeType||l.insignificant(n))t(n);else{for(d=n.childNodes.length-1;d>=0;d--)this.clean(n.childNodes[d]);n.removeAttribute("style"),n.removeAttribute("class")}e.inArray(o,this.tagsToClean)>-1&&l.unwrap(n)}}});e.extend(i,{FormattingTool:f,CleanFormatCommand:p}),u("formatting",new f({template:new r({template:d.dropDownListTemplate,title:"Format"})})),u("cleanFormatting",new o({command:p,template:new r({template:d.buttonTemplate,title:"Clean formatting"})}))}(window.kendo.jQuery)},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("editor/toolbar.min",["editor/formatting.min"],e)}(function(){!function(e,t){var n,i=window.kendo,o=i.ui,r=o.editor,a=o.Widget,s=e.extend,l=e.proxy,d=i.keys,c=".kendoEditor",u=i.ui.editor.EditorUtils,f=i.ui.editor.ToolTemplate,p=i.ui.editor.Tool,h="overflowAnchor",m=".k-tool-group:visible a.k-tool:not(.k-state-disabled),.k-tool.k-overflow-anchor,.k-tool-group:visible .k-widget.k-colorpicker,.k-tool-group:visible .k-selectbox,.k-tool-group:visible .k-dropdown,.k-tool-group:visible .k-combobox .k-input",g=p.extend({initialize:function(t,n){t.attr({unselectable:"on"});var i=n.editor.toolbar;t.on("click",e.proxy(function(){this.overflowPopup.toggle()},i))},options:{name:h},command:e.noop,update:e.noop,destroy:e.noop});u.registerTool(h,new g({key:"",ctrl:!0,template:new f({template:u.overflowAnchorTemplate})})),n=a.extend({init:function(e,t){var n=this;t=s({},t,{name:"EditorToolbar"}),a.fn.init.call(n,e,t),t.popup&&n._initPopup(),t.resizable&&t.resizable.toolbar&&(n._resizeHandler=i.onResize(function(){n.resize()}),n.element.addClass("k-toolbar-resizable"))},events:["execute"],groups:{basic:["bold","italic","underline","strikethrough"],scripts:["subscript","superscript"],alignment:["justifyLeft","justifyCenter","justifyRight","justifyFull"],links:["insertImage","insertFile","createLink","unlink"],lists:["insertUnorderedList","insertOrderedList","indent","outdent"],
tables:["createTable","addColumnLeft","addColumnRight","addRowAbove","addRowBelow","deleteRow","deleteColumn"],advanced:["viewHtml","cleanFormatting","print","pdf"],fonts:["fontName","fontSize"],colors:["foreColor","backColor"]},overflowFlaseTools:["formatting","fontName","fontSize","foreColor","backColor","insertHtml"],_initPopup:function(){this.window=e(this.element).wrap("<div class='editorToolbarWindow k-header' />").parent().prepend("<button class='k-button k-button-bare k-editortoolbar-dragHandle'><span class='k-icon k-i-move' /></button>").kendoWindow({title:!1,resizable:!1,draggable:{dragHandle:".k-editortoolbar-dragHandle"},animation:{open:{effects:"fade:in"},close:{effects:"fade:out"}},minHeight:42,visible:!1,autoFocus:!1,actions:[],dragend:function(){this._moved=!0}}).on("mousedown",function(t){e(t.target).is(".k-icon")||t.preventDefault()}).data("kendoWindow")},_toggleOverflowStyles:function(e,t){e.find("li").toggleClass("k-item k-state-default",t).find(".k-tool:not(.k-state-disabled),.k-overflow-button").toggleClass("k-overflow-button k-button",t)},_initOverflowPopup:function(t){var n=this,i="<ul class='k-editor-overflow-popup k-overflow-container k-list-container'></ul>";n.overflowPopup=e(i).appendTo("body").kendoPopup({anchor:t,origin:"bottom right",position:"top right",copyAnchorStyles:!1,open:function(e){this.element.is(":empty")&&e.preventDefault(),n._toggleOverflowStyles(this.element,!0)},activate:l(n.focusOverflowPopup,n)}).data("kendoPopup")},items:function(){var e,t,n=this.options.resizable&&this.options.resizable.toolbar;return t=this.element.children().find("> *, select"),n&&(e=this.overflowPopup,t=t.add(e.element.children().find("> *"))),t},focused:function(){return this.element.find(".k-state-focused").length>0},toolById:function(e){var t,n=this.tools;for(t in n)if(t.toLowerCase()==e)return n[t]},toolGroupFor:function(t){var n,i=this.groups;if(this.isCustomTool(t))return"custom";for(n in i)if(e.inArray(t,i[n])>=0)return n},bindTo:function(t){var n=this,i=n.window;n._editor&&n._editor.unbind("select",l(n.resize,n)),n._editor=t,n.options.resizable&&n.options.resizable.toolbar&&t.options.tools.push(h),n.tools=n.expandTools(t.options.tools),n.render(),n.element.find(".k-combobox .k-input").keydown(function(t){var n=e(this).closest(".k-combobox").data("kendoComboBox"),i=t.keyCode;i==d.RIGHT||i==d.LEFT?n.close():i==d.DOWN&&(n.dropDown.isOpened()||(t.stopImmediatePropagation(),n.open()))}),n._attachEvents(),n.items().each(function(){var i,o=n._toolName(this),r="more"!==o?n.tools[o]:n.tools.overflowAnchor,a=r&&r.options,s=t.options.messages,l=a&&a.tooltip||s[o],d=e(this);r&&r.initialize&&("fontSize"!=o&&"fontName"!=o||(i=s[o+"Inherit"],d.find("input").val(i).end().find("span.k-input").text(i).end()),r.initialize(d,{title:n._appendShortcutSequence(l,r),editor:n._editor}),d.closest(".k-widget",n.element).addClass("k-editor-widget"),d.closest(".k-colorpicker",n.element).next(".k-colorpicker").addClass("k-editor-widget"))}),t.bind("select",l(n.resize,n)),n.update(),i&&i.wrapper.css({top:"",left:"",width:""})},show:function(){var e,t,n,i=this,o=i.window,r=i.options.editor;o&&(e=o.wrapper,t=r.element,e.is(":visible")&&i.window.options.visible||(e[0].style.width||e.width(t.outerWidth()-parseInt(e.css("border-left-width"),10)-parseInt(e.css("border-right-width"),10)),o._moved||(n=t.offset(),e.css({top:Math.max(0,parseInt(n.top,10)-e.outerHeight()-parseInt(i.window.element.css("padding-bottom"),10)),left:Math.max(0,parseInt(n.left,10))})),o.open()))},hide:function(){this.window&&this.window.close()},focus:function(){var e="tabIndex",t=this.element,n=this._editor.element.attr(e);t.attr(e,n||0).focus().find(m).first().focus(),n||0===n||t.removeAttr(e)},focusOverflowPopup:function(){var e="tabIndex",t=this.overflowPopup.element,n=this._editor.element.attr(e);t.closest(".k-animation-container").addClass("k-overflow-wrapper"),t.attr(e,n||0).find(m).first().focus(),n||0===n||t.removeAttr(e)},_appendShortcutSequence:function(e,t){if(!t.key)return e;var n=e+" (";return t.ctrl&&(n+="Ctrl + "),t.shift&&(n+="Shift + "),t.alt&&(n+="Alt + "),n+=t.key+")"},_nativeTools:["insertLineBreak","insertParagraph","redo","undo","autoLink"],tools:{},isCustomTool:function(e){return!(e in i.ui.Editor.defaultTools)},expandTools:function(t){var n,o,a,l,d=this._nativeTools,c=i.deepExtend({},i.ui.Editor.defaultTools),u={};for(o=0;o<t.length;o++)n=t[o],l=n.name,e.isPlainObject(n)?l&&c[l]?(u[l]=s({},c[l]),s(u[l].options,n)):(a=s({cssClass:"k-i-custom",type:"button",title:""},n),a.name||(a.name="custom"),a.cssClass="k-"+("custom"==a.name?"i-custom":a.name),a.template||"button"!=a.type||(a.template=r.EditorUtils.buttonTemplate,a.title=a.title||a.tooltip),u[l]={options:a}):c[n]&&(u[n]=c[n]);for(o=0;o<d.length;o++)u[d[o]]||(u[d[o]]=c[d[o]]);return u},render:function(){function t(t){var n;return t.getHtml?n=t.getHtml():(e.isFunction(t)||(t=i.template(t)),n=t(r)),e.trim(n)}function n(){f.children().length&&(w&&(f.data("position",C),C++),f.appendTo(v))}function o(t){t!==h?(f=e("<li class='k-tool-group' role='presentation' />"),f.data("overflow",e.inArray(t,x)===-1)):f=e("<li class='k-overflow-tools' />")}var r,a,s,d,c,u,f,p,m=this,g=m.tools,b=m._editor.element,v=m.element.empty(),k=m._editor.options.tools,y=i.support.browser,C=0,w=m.options.resizable&&m.options.resizable.toolbar,x=this.overflowFlaseTools;for(v.empty(),k.length&&(d=k[0].name||k[0]),o(d,x),p=0;p<k.length;p++)d=k[p].name||k[p],r=g[d]&&g[d].options,!r&&e.isPlainObject(d)&&(r=d),a=r&&r.template,"break"==d&&(n(),e("<li class='k-row-break' />").appendTo(m.element),o(d,x)),a&&(u=m.toolGroupFor(d),c==u&&d!=h||(n(),o(d,x),c=u),a=t(a),s=e(a).appendTo(f),"custom"==u&&(n(),o(d,x)),r.exec&&s.hasClass("k-tool")&&s.click(l(r.exec,b[0])));n(),e(m.element).children(":has(> .k-tool)").addClass("k-button-group"),m.options.popup&&y.msie&&y.version<9&&m.window.wrapper.find("*").attr("unselectable","on"),m.updateGroups(),w&&m._initOverflowPopup(m.element.find(".k-overflow-anchor")),m.angular("compile",function(){return{elements:m.element}})},updateGroups:function(){e(this.element).children().each(function(){e(this).children().filter(function(){return!e(this).hasClass("k-state-disabled")}).removeClass("k-group-end").first().addClass("k-group-start").end().last().addClass("k-group-end").end()})},decorateFrom:function(t){this.items().filter(".k-decorated").each(function(){var n=e(this).data("kendoSelectBox");n&&n.decorate(t)})},destroy:function(){a.fn.destroy.call(this);var e,t=this.tools;for(e in t)t[e].destroy&&t[e].destroy();this.window&&this.window.destroy(),this._resizeHandler&&i.unbindResize(this._resizeHandler),this.overflowPopup&&this.overflowPopup.destroy()},_attachEvents:function(){var t=this,n="[role=button].k-tool",i=n+":not(.k-state-disabled)",o=n+".k-state-disabled",r=t.overflowPopup?t.overflowPopup.element:e([]);t.element.add(r).off(c).on("mouseenter"+c,i,function(){e(this).addClass("k-state-hover")}).on("mouseleave"+c,i,function(){e(this).removeClass("k-state-hover")}).on("mousedown"+c,n,function(e){e.preventDefault()}).on("keydown"+c,m,function(n){function i(e,t,n){var i=t.find(m),o=i.index(a)+e;return n&&(o=Math.max(0,Math.min(i.length-1,o))),i[o]}var o,r,a=this,s=t.options.resizable&&t.options.resizable.toolbar,l=n.keyCode;l==d.RIGHT||l==d.LEFT?e(a).hasClass(".k-dropdown")||(o=i(l==d.RIGHT?1:-1,t.element,!0)):!s||l!=d.UP&&l!=d.DOWN?l==d.ESC?(t.overflowPopup.visible()&&t.overflowPopup.close(),o=t._editor):l!=d.TAB||n.ctrlKey||n.altKey||(r=s&&e(a.parentElement).hasClass("k-overflow-tool-group")?t.overflowPopup.element:t.element,n.shiftKey?o=i(-1,r):(o=i(1,r),o||(o=t._editor))):o=i(l==d.DOWN?1:-1,t.overflowPopup.element,!0),o&&(n.preventDefault(),o.focus())}).on("click"+c,i,function(n){var i=e(this);n.preventDefault(),n.stopPropagation(),i.removeClass("k-state-hover"),i.is("[data-popup]")||t._editor.exec(t._toolName(this))}).on("click"+c,o,function(e){e.preventDefault()})},_toolName:function(t){var n,i;if(t)return n=t.className,/k-tool\b/i.test(n)&&(n=t.firstChild.className),i=e.grep(n.split(" "),function(e){return!/^k-(widget|tool|tool-icon|icon|state-hover|header|combobox|dropdown|selectbox|colorpicker)$/i.test(e)}),i[0]?i[0].substring(i[0].lastIndexOf("-")+1):"custom"},refreshTools:function(){var t=this,n=t._editor,o=n.getRange(),r=i.ui.editor.RangeUtils.textNodes(o);r.length||(r=[o.startContainer]),t.items().each(function(){var n=t.tools[t._toolName(this)];n&&n.update&&n.update(e(this),r)}),this.update()},update:function(){this.updateGroups()},_resize:function(e){var t=e.width,n=this.options.resizable&&this.options.resizable.toolbar,i=this.overflowPopup;this.refreshTools(),n&&(i.visible()&&i.close(!0),this._refreshWidths(),this._shrink(t),this._stretch(t),this._toggleOverflowStyles(this.element,!1),this._toggleOverflowStyles(this.overflowPopup.element,!0),this.element.children("li.k-overflow-tools").css("visibility",i.element.is(":empty")?"hidden":"visible"))},_refreshWidths:function(){this.element.children("li").each(function(t,n){var i=e(n);i.data("outerWidth",i.outerWidth(!0))})},_shrink:function(e){var t,n,i;if(e<this._groupsWidth())for(n=this._visibleGroups().filter(":not(.k-overflow-tools)"),i=n.length-1;i>=0&&(t=n.eq(i),!(e>this._groupsWidth()));i--)this._hideGroup(t)},_stretch:function(e){var t,n,i;if(e>this._groupsWidth())for(n=this._hiddenGroups(),i=0;i<n.length&&(t=n.eq(i),!(e<this._groupsWidth())&&this._showGroup(t,e));i++);},_hiddenGroups:function(){var t=this.overflowPopup,n=this.element.children("li.k-tool-group").filter(":hidden");return n=n.add(t.element.children("li")),n.sort(function(t,n){return e(t).data("position")>e(n).data("position")?1:-1}),n},_visibleGroups:function(){return this.element.children("li.k-tool-group, li.k-overflow-tools").filter(":visible")},_groupsWidth:function(){var t=0;return this._visibleGroups().each(function(){t+=e(this).data("outerWidth")}),Math.ceil(t)},_hideGroup:function(e){if(e.data("overflow")){var t=this.overflowPopup;e.detach().prependTo(t.element).addClass("k-overflow-tool-group")}else e.hide()},_showGroup:function(t,n){var i,o;return!!(t.length&&n>this._groupsWidth()+t.data("outerWidth"))&&(t.hasClass("k-overflow-tool-group")?(i=t.data("position"),0===i?t.detach().prependTo(this.element):(o=this.element.children().filter(function(t,n){return e(n).data("position")===i-1}),t.detach().insertAfter(o)),t.removeClass("k-overflow-tool-group")):t.show(),!0)}}),e.extend(r,{Toolbar:n})}(window.jQuery||window.kendo.jQuery)},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("editor/tables.min",["editor/toolbar.min"],e)}(function(){!function(e,t){var n=window.kendo,i=e.extend,o=e.proxy,r=n.ui.editor,a=r.Dom,s=r.EditorUtils,l=r.RangeUtils,d=r.Command,c=".kendoEditor",u="k-state-active",f="k-state-selected",p=r.Tool,h=r.ToolTemplate,m=r.InsertHtmlCommand,g=r.BlockFormatFinder,b=r.EditorUtils.registerTool,v="<td>"+r.emptyElementContent+"</td>",k=new g([{tags:["table"]}]),y=m.extend({_tableHtml:function(e,t){return e=e||1,t=t||1,"<table class='k-table' data-last>"+Array(e+1).join("<tr>"+Array(t+1).join(v)+"</tr>")+"</table>"},postProcess:function(t,n){var i=e("table[data-last]",t.document).removeAttr("data-last");n.setStart(i.find("td")[0],0),n.collapse(!0),t.selectRange(n)},exec:function(){var e=this.options;e.html=this._tableHtml(e.rows,e.columns),e.postProcess=this.postProcess,m.fn.exec.call(this)}}),C=p.extend({initialize:function(t,n){p.fn.initialize.call(this,t,n);var i=e(this.options.popupTemplate).appendTo("body").kendoPopup({anchor:t,copyAnchorStyles:!1,open:o(this._open,this),activate:o(this._activate,this),close:o(this._close,this)}).data("kendoPopup");t.click(o(this._toggle,this)).keydown(o(this._keydown,this)),this._editor=n.editor,this._popup=i},popup:function(){return this._popup},_activate:e.noop,_open:function(){this._popup.options.anchor.addClass(u)},_close:function(){this._popup.options.anchor.removeClass(u)},_keydown:function(e){var t=n.keys,i=e.keyCode;i==t.DOWN&&e.altKey?this._popup.open():i==t.ESC&&this._popup.close()},_toggle:function(t){var n=e(t.target).closest(".k-tool");n.hasClass("k-state-disabled")||this.popup().toggle()},update:function(e){var t=this.popup();t.wrapper&&"block"==t.wrapper.css("display")&&t.close(),e.removeClass("k-state-hover")},destroy:function(){this._popup.destroy()}}),w=C.extend({init:function(t){this.cols=8,this.rows=6,C.fn.init.call(this,e.extend(t,{command:y,popupTemplate:"<div class='k-ct-popup'>"+Array(this.cols*this.rows+1).join("<span class='k-ct-cell k-state-disabled' />")+"<div class='k-status'></div></div>"}))},_activate:function(){function t(t){var n=e(window);return{row:Math.floor((t.clientY+n.scrollTop()-u.top)/o)+1,col:Math.floor((t.clientX+n.scrollLeft()-u.left)/i)+1}}var i,o,r=this,a=r._popup.element,s=a.find(".k-ct-cell"),l=s.eq(0),d=s.eq(s.length-1),u=n.getOffset(l),f=n.getOffset(d),p=r.cols,h=r.rows;a.find("*").addBack().attr("unselectable","on"),f.left+=d[0].offsetWidth,f.top+=d[0].offsetHeight,i=(f.left-u.left)/p,o=(f.top-u.top)/h,a.on("mousemove"+c,function(e){r._setTableSize(t(e))}).on("mouseleave"+c,function(){r._setTableSize()}).on("mouseup"+c,function(e){r._exec(t(e))})},_valid:function(e){return e&&e.row>0&&e.col>0&&e.row<=this.rows&&e.col<=this.cols},_exec:function(e){this._valid(e)&&(this._editor.exec("createTable",{rows:e.row,columns:e.col}),this._popup.close())},_setTableSize:function(t){var i=this._popup.element,o=i.find(".k-status"),r=i.find(".k-ct-cell"),a=this.cols,s=this._editor.options.messages;this._valid(t)?(o.text(n.format(s.createTableHint,t.row,t.col)),r.each(function(n){e(this).toggleClass(f,n%a<t.col&&n/a<t.row)})):(o.text(s.dialogCancel),r.removeClass(f))},_keydown:function(e){var t,i,o,r,a,s,l,d;C.fn._keydown.call(this,e),this._popup.visible()&&(t=n.keys,i=e.keyCode,o=this._popup.element.find(".k-ct-cell"),r=Math.max(o.filter(".k-state-selected").last().index(),0),a=Math.floor(r/this.cols),s=r%this.cols,l=!1,i!=t.DOWN||e.altKey?i==t.UP?(l=!0,a--):i==t.RIGHT?(l=!0,s++):i==t.LEFT&&(l=!0,s--):(l=!0,a++),d={row:Math.max(1,Math.min(this.rows,a+1)),col:Math.max(1,Math.min(this.cols,s+1))},i==t.ENTER?this._exec(d):this._setTableSize(d),l&&(e.preventDefault(),e.stopImmediatePropagation()))},_open:function(){var e=this._editor.options.messages;C.fn._open.call(this),this.popup().element.find(".k-status").text(e.dialogCancel).end().find(".k-ct-cell").removeClass(f)},_close:function(){C.fn._close.call(this),this.popup().element.off(c)},update:function(e,t){var n;C.fn.update.call(this,e),n=k.isFormatted(t),e.toggleClass("k-state-disabled",n)}}),x=d.extend({exec:function(){for(var e,t,n,i,o=this.lockRange(!0),s=o.endContainer;"td"!=a.name(s);)s=s.parentNode;for(t=s.parentNode,e=t.children.length,n=t.cloneNode(!0),i=0;i<t.cells.length;i++)n.cells[i].innerHTML=r.emptyElementContent;"before"==this.options.position?a.insertBefore(n,t):a.insertAfter(n,t),this.releaseRange(o)}}),T=d.extend({exec:function(){var e,t,n,i,o=this.lockRange(!0),s=a.closest(o.endContainer,"td"),l=a.closest(s,"table"),d=l.rows,c=this.options.position;for(e=a.findNodeIndex(s,!0),t=0;t<d.length;t++)n=d[t].cells[e],i=n.cloneNode(),i.innerHTML=r.emptyElementContent,"before"==c?a.insertBefore(i,n):a.insertAfter(i,n);this.releaseRange(o)}}),_=d.extend({exec:function(){var t,n,i,o=this.lockRange(),r=l.mapAll(o,function(t){return e(t).closest("tr")[0]}),s=a.closest(r[0],"table");if(s.rows.length<=r.length)t=a.next(s),t&&!a.insignificant(t)||(t=a.prev(s)),a.remove(s);else for(n=0;n<r.length;n++)i=r[n],a.removeTextSiblings(i),t=a.next(i)||a.prev(i),t=t.cells[0],a.remove(i);t&&(o.setStart(t,0),o.collapse(!0),this.editor.selectRange(o))}}),F=d.extend({exec:function(){var e,t,n=this.lockRange(),i=a.closest(n.endContainer,"td"),o=a.closest(i,"table"),r=o.rows,s=a.findNodeIndex(i,!0),l=r[0].cells.length;if(1==l)e=a.next(o),e&&!a.insignificant(e)||(e=a.prev(o)),a.remove(o);else for(a.removeTextSiblings(i),e=a.next(i)||a.prev(i),t=0;t<r.length;t++)a.remove(r[t].cells[s]);e&&(n.setStart(e,0),n.collapse(!0),this.editor.selectRange(n))}}),S=p.extend({command:function(e){return e=i(e,this.options),"delete"==e.action?"row"==e.type?new _(e):new F(e):"row"==e.type?new x(e):new T(e)},initialize:function(e,t){p.fn.initialize.call(this,e,t),e.addClass("k-state-disabled")},update:function(e,t){var n=!k.isFormatted(t);e.toggleClass("k-state-disabled",n)}});i(n.ui.editor,{PopupTool:C,TableCommand:y,InsertTableTool:w,TableModificationTool:S,InsertRowCommand:x,InsertColumnCommand:T,DeleteRowCommand:_,DeleteColumnCommand:F}),b("createTable",new w({template:new h({template:s.buttonTemplate,popup:!0,title:"Create table"})})),b("addColumnLeft",new S({type:"column",position:"before",template:new h({template:s.buttonTemplate,title:"Add column on the left"})})),b("addColumnRight",new S({type:"column",template:new h({template:s.buttonTemplate,title:"Add column on the right"})})),b("addRowAbove",new S({type:"row",position:"before",template:new h({template:s.buttonTemplate,title:"Add row above"})})),b("addRowBelow",new S({type:"row",template:new h({template:s.buttonTemplate,title:"Add row below"})})),b("deleteRow",new S({type:"row",action:"delete",template:new h({template:s.buttonTemplate,title:"Delete row"})})),b("deleteColumn",new S({type:"column",action:"delete",template:new h({template:s.buttonTemplate,title:"Delete column"})}))}(window.kendo.jQuery)},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()}),function(e,define){define("kendo.editor.min",["kendo.combobox.min","kendo.dropdownlist.min","kendo.resizable.min","kendo.window.min","kendo.colorpicker.min","kendo.imagebrowser.min","util/undoredostack.min","editor/main.min","editor/dom.min","editor/serializer.min","editor/range.min","editor/systemBean.min","editor/inlineformat.min","editor/formatblock.min","editor/linebreak.min","editor/lists.min","editor/link.min","editor/file.min","editor/image.min","editor/components.min","editor/indent.min","editor/viewhtml.min","editor/formatting.min","editor/toolbar.min","editor/tables.min"],e)}(function(){},"function"==typeof define&&define.amd?define:function(e,t,n){(n||t)()});
//# sourceMappingURL=kendo.editor.min.js.map
