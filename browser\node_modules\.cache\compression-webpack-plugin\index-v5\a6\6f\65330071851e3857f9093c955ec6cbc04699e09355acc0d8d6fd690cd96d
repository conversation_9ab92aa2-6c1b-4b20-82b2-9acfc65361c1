
03717ed6a88dc3c50bb5967d5b18bc4e23992747	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.115.1754018536329.js\",\"contentHash\":\"8017e4dc34b7a2936b853b228dec6149\"}","integrity":"sha512-2jqeViGYje/dYflYKL856JGULS9bflJDlf/muMNWEODX8t/6ZPPVbiT5/YhulFVyIVJNZw88+wyq4GhPazjw3Q==","time":1754018575979,"size":164461}