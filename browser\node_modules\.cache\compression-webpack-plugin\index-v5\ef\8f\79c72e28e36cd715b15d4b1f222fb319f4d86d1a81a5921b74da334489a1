
6a97afbe7d00a6bd7ee6f09b1c32d5ddc11be03a	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.366.1754018536329.js\",\"contentHash\":\"c6afa94b624f927e106e59c6a84d3510\"}","integrity":"sha512-V0jAx5u6IyeDMp4Rz5Anjpq527PrDpRxQWu6+5FoElhdno9RQTCkDDFvFr7f5JKUgN0Wd8xL5JPt4iUNY9IuFw==","time":1754018575976,"size":102781}