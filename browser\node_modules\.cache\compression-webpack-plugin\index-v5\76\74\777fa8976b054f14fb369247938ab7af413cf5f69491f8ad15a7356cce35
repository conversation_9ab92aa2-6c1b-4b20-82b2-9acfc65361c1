
1de6ce4bf9057bac95b19c555a3d42ad07b2d08b	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.375.1754018536329.js\",\"contentHash\":\"99ee2ff132b32497a6bb3097fe718f1a\"}","integrity":"sha512-Xkp+JgxWFFVeSMLKeOVB2q58pWeTVLbk5W0LPE18gSTlVZwFSGCvfg6Kv3Kf0lXne5eH20Ph+l5vjCeJ9C7r7g==","time":1754018576020,"size":157704}