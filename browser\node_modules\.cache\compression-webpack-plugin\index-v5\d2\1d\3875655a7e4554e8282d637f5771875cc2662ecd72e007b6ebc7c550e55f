
3117e3245582b42a400aad7ccd5c602d6384e159	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.381.1754018536329.js\",\"contentHash\":\"3e2946677a7673cde0dba573baed52a5\"}","integrity":"sha512-s8Lis+km7US8xyxeGC/C5YW2Wl5zTVoF1wySZnD18vWszFmKtcpZXALqm7lPcT882nbz6SQMUnaAvoTxexZowQ==","time":1754018575956,"size":40079}