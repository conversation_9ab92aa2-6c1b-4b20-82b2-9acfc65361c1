
e714d1ebc52b06a14d6e142451af38369aea1949	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.61.1754018536329.js\",\"contentHash\":\"5e5d665f175630d671b0cef3b5977a8b\"}","integrity":"sha512-IpfaLUBP0aB7+5WpMA6p2U97Z840w6TWd0F8acCX2HGfIf/VPvcKB65NemVU0BHdY4bW0hhs+r2bLvWtPf9PTQ==","time":1754018575979,"size":173379}