package com.klaw.service.imp.caseServiceImpl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.klaw.constant.CaseKind;
import com.klaw.constant.DataState;
import com.klaw.constant.NoticeType;
import com.klaw.dao.caseDao.CaseMapper;
import com.klaw.dao.caseDao.SgCaseReportTaskMapper;
import com.klaw.dao.systemDao.SgHrOrgUnitBMapper;
import com.klaw.entity.basicBean.SgAdminConfig;
import com.klaw.entity.caseBean.*;
import com.klaw.entity.caseBean.child.*;
import com.klaw.entity.contractBean.SgContractProject;
import com.klaw.entity.systemBean.SgHrOrgUnitB;
import com.klaw.entity.systemBean.SgSysDoc;
import com.klaw.service.basicService.SgAdminConfigService;
import com.klaw.service.caseService.CaseHistoryService;
import com.klaw.service.caseService.CaseService;
import com.klaw.service.caseService.childService.*;
import com.klaw.service.systemService.SgSysNoticeService;
import com.klaw.utils.*;
import com.klaw.vo.Json;
import com.klaw.vo.OrgContextVo;
import freemarker.template.Template;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.*;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestParam;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.lang.reflect.InvocationTargetException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

@Slf4j
@Service
public class CaseServiceImpl extends ServiceImpl<CaseMapper, CaseRecord> implements CaseService {
    @Resource
    private PartiesService partiesService;
    @Resource
    private AgentService agentService;
    @Resource
    private IndictmentService indictmentService;
    @Resource
    private ClaimService claimService;
    @Resource
    private EvidenceService evidenceService;
    @Resource
    private PreservationService preservationService;
    @Resource
    private MiddleRelationService middleRelationService;
    @Resource
    private JudgeContentService judgeContentService;
    @Resource
    private JudgeFilesService judgeFilesService;
    @Resource
    private CaseProcessService caseProcessService;
    @Resource
    private AppraisalService appraisalService;
    @Resource
    private CaseHistoryService caseHistoryService;
    @Resource
    private FildocService fildocService;
    @Resource
    private OtherDataService otherDataService;
    @Resource
    private CaseEvidenceDataService caseEvidenceDataService;
    @Resource
    private SgCaseTeamService caseTeamService;
    @Resource
    private SgCaseReportService caseReportService;
    @Resource
    private SgCaseExecuteService sgCaseExecuteService;
    @Resource
    private HearingService hearingService;
    @Resource
    private SgAdminConfigService sgAdminConfigService;
    @Resource
    private SgCaseReportService sgCaseReportService;
    @Resource
    private CaseService caseService;
    @Resource
    private AgentContractService agentContractService;
    @Resource
    private CaseEasyModelService caseEasyModelService;
    @Resource
    private TagService tagService;
    @Resource
    private CaseMapper caseMapper;
    @Resource
    private SgSysNoticeService sgSysNoticeService;
    @Resource
    private SgHrOrgUnitBMapper sgHrOrgUnitBMapper;

    @Override
    public Page<CaseRecord> queryPageData(JSONObject json) {
        QueryWrapper<CaseRecord> wrapper = new QueryWrapper<>();
        getFilter(json, wrapper);
        return page(new PageUtils<>(json), wrapper);
    }


    @Override
    public Page<CaseRecord> queryDialog(JSONObject json) {
        QueryWrapper<CaseRecord> wrapper = new QueryWrapper<>();

        //模糊搜索值
        String fuzzyValue = json.containsKey("fuzzyValue") ? json.getString("fuzzyValue") : null;

        String orgName = json.containsKey("orgName") ? json.getString("orgName") : "";

        String caseCurrentProcess = json.containsKey("caseCurrentProcess") ? json.getString("caseCurrentProcess") : "";

        if (StringUtils.isNotBlank(fuzzyValue)) {
            wrapper.and(i -> i.like("case_name", fuzzyValue).or()
                    .like("case_number", fuzzyValue).or()
                    .like("cause_name", fuzzyValue).or()
                    .like("case_money", fuzzyValue).or()
                    .like("case_type", fuzzyValue).or()
                    .like("case_current_process", fuzzyValue).or()
                    .like("create_psn_name", fuzzyValue).or()
                    .like("CASE_KIND", fuzzyValue).or()
                    .like("belong_plate", fuzzyValue));
        }
        if (json.containsKey("orgName")) {
            wrapper.like("case_team_name", orgName);
        }
        if (StringUtils.isNotBlank(caseCurrentProcess)) {
            wrapper.eq("case_current_process", caseCurrentProcess);
        }
        PageUtils<CaseRecord> page = page(new PageUtils<>(json), wrapper);
        List<CaseRecord> caseRecords = page.getRecords();
        for (CaseRecord caseRecord : caseRecords) {
            QueryWrapper<Parties> partiesQueryWrapper = new QueryWrapper<Parties>();
            partiesQueryWrapper.eq("master_id", caseRecord.getId());
            List<Parties> partiesList = partiesService.list(partiesQueryWrapper);
            QueryWrapper<Claim> claimQueryWrapper = new QueryWrapper<>();
            claimQueryWrapper.eq("parent_id", caseRecord.getId());
            List<Claim> claims = claimService.list(claimQueryWrapper);
            if (partiesList != null) {
                caseRecord.setPartiesList(partiesList);
            }
            if (claims != null) {
                caseRecord.setClaimList(claims);
            }
        }
        return page.setRecords(caseRecords);
    }
    /**
     * 案件保存方法
     *
     * @param caseBean 案件数据
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveData(CaseRecord caseBean) {
        List<CaseTeam> teamList = caseBean.getTeamList();
        String caseTeamName = "";
        if (!org.springframework.util.CollectionUtils.isEmpty(teamList)) {
            for (CaseTeam caseTeam : teamList) {
                if ("".equals(caseTeamName)) {
                    caseTeamName = caseTeam.getPersonName();
                } else {
                    caseTeamName += "、" + caseTeam.getPersonName();
                }
            }
            caseBean.setCaseTeamName(caseTeamName);
        } else {
            caseBean.setCaseTeamName(null);
        }
        // 查询旧数据
        CaseRecord caseOn = getById(caseBean.getId());

        // 更新当前数据状态和主案件阶段
        updateCaseProcess(caseBean, caseOn);

        //更新当前案件数据表
        saveOrUpdate(caseBean);

        //保存子表
        saveChildData(caseBean);

        // 如果旧数据已经是提交的情况,记录历史
        if (caseOn != null && caseOn.getDataStateCode().equals(DataState.SUBMIT.getKey())) {
            insertHistory(caseOn, caseBean);
        }
    }

    @Override
    public void updateExecuteByEnd(CaseRecord caseRecord) {
        UpdateWrapper<CaseRecord> updateWrapper = new UpdateWrapper<CaseRecord>();
        updateWrapper.set("actual_receive_money", caseRecord.getActualReceiveMoney());
        updateWrapper.set("actual_receive_other", caseRecord.getActualReceiveOther());
        updateWrapper.set("actual_send_money", caseRecord.getActualSendMoney());
        updateWrapper.set("actual_send_other", caseRecord.getActualSendOther());

        CaseRecord record = queryExecuteById(caseRecord.getParentId(), CaseKind.ZX.getKey());

        if (record != null) {
            updateWrapper.eq("id", record.getId());
            boolean update = update(updateWrapper);
            log.info("更新完成：" + update);
        }
    }

    @Override
    public Page<CaseRecord> queryPageDataAndTags(JSONObject json) {
        QueryWrapper<CaseRecord> wrapper = new QueryWrapper<CaseRecord>();
        getFilter(json, wrapper);
        Page<CaseRecord> recordPage = page(new PageUtils<CaseRecord>(json), wrapper);
        List<String> ids = recordPage.getRecords().stream().map(CaseRecord::getId).collect(Collectors.toList());
        Map<String, List<Tag>> maps = new HashMap<>();
        if (ids.size() > 0) {
            List<Tag> tags = tagService.list(new QueryWrapper<Tag>().in("data_id", ids));
            tags.stream().forEach(item -> {
                if (maps.containsKey(item.getDataId())) {
                    maps.get(item.getDataId()).add(item);
                } else {
                    List<Tag> temp = new ArrayList<>();
                    temp.add(item);
                    maps.put(item.getDataId(), temp);
                }
            });
        }
        recordPage.getRecords().stream().forEach(item -> {
            if (maps.containsKey(item.getId())) {
                item.setTagList(maps.get(item.getId()));
            } else {
                item.setTagList(new ArrayList<>());
            }
        });
        return recordPage;
    }

    @Override
    public CaseRecord queryDataById(String id) {

        CaseRecord caseBean = getById(id);

        if (caseBean == null) {
            caseBean = caseHistoryService.getById(id);
        }

        //首环节的时候查询
        if (StringUtils.isBlank(caseBean.getParentId())) {

            caseBean.setAgentList(agentService.list(new QueryWrapper<Agent>().eq("parent_id", caseBean.getId()).orderBy(false, true, "seq")));
            caseBean.setAgentContractList(agentContractService.queryDataById(caseBean.getId()));

            Map<String, List<MiddleRelation>> map = middleRelationService.queryData(id, "case", new String[]{"caseProject", "dispute", "case", "contract"});
            List<MiddleRelation> relations = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(map.get("dispute"))) relations.addAll(map.get("dispute"));
            if (CollectionUtils.isNotEmpty(map.get("caseProject")))
                relations.addAll(map.get("caseProject"));
            if (CollectionUtils.isNotEmpty(map.get("case")))
                relations.addAll(map.get("case"));
            if (CollectionUtils.isNotEmpty(map.get("contract")))
                relations.addAll(map.get("contract"));
            caseBean.setRelations(relations);
            caseBean.setCaseEasyModel(caseEasyModelService.queryDataByCaseId(caseBean.getId()));

            caseBean.setPreservationList(preservationService.list(new QueryWrapper<Preservation>().eq("parent_id", caseBean.getId()).orderBy(false, true, "seq")));
        }

        List<String> childs = getHasChilds(caseBean.getCaseProcessTypeCode());

        if (childs.isEmpty()) {
            return caseBean;
        }

        caseBean.setTeamList(caseTeamService.list(new QueryWrapper<CaseTeam>().eq("parent_id", caseBean.getId()).orderBy(false, true, "seq")));
        caseBean.setReportList(caseReportService.list(new QueryWrapper<CaseReport>().eq("parent_id", caseBean.getId()).orderBy(false, true, "seq")));

        if (childs.contains("parties"))
            caseBean.setPartiesList(partiesService.list(new QueryWrapper<Parties>().eq("master_id", caseBean.getId()).orderBy(false, true, "seq")));
        if (childs.contains("claim"))
            caseBean.setClaimList(claimService.list(new QueryWrapper<Claim>().eq("parent_id", caseBean.getId()).orderBy(false, true, "seq")));
        if (childs.contains("caseProcess"))
            caseBean.setCaseProcessList(caseProcessService.list(new QueryWrapper<CaseProcess>().eq("parent_id", caseBean.getId()).orderBy(false, true, "seq")));
        if (childs.contains("appraisal"))
            caseBean.setAppraisalList(appraisalService.list(new QueryWrapper<Appraisal>().eq("parent_id", caseBean.getId()).orderBy(false, true, "seq")));
        if (childs.contains("judgeContent"))
            caseBean.setJudgeContentList(judgeContentService.list(new QueryWrapper<JudgeContent>().eq("parent_id", caseBean.getId()).orderBy(false, true, "seq")));
        if (childs.contains("otherData"))
            caseBean.setOtherDataList(otherDataService.list(new QueryWrapper<OtherData>().eq("parent_id", caseBean.getId()).orderBy(false, true, "seq")));
        if (childs.contains("caseEvidenceData"))
            caseBean.setCaseEvidenceDataList(caseEvidenceDataService.list(new QueryWrapper<CaseEvidenceData>().eq("parent_id", caseBean.getId()).orderBy(false, true, "seq")));
        if (childs.contains("execute")) {
            caseBean.setExecuteList(sgCaseExecuteService.list(new QueryWrapper<CaseExecute>().eq("parent_id", caseBean.getId()).orderBy(false, true, "seq")));
        }
        if (childs.contains("cc")) {
            caseBean.setHearingList(hearingService.list(new QueryWrapper<Hearing>().eq("parent_id", caseBean.getId()).orderBy(false, true, "seq")));
        }
        if (childs.contains("agent")) {
            caseBean.setAgentList(agentService.list(new QueryWrapper<Agent>().eq("parent_id", caseBean.getId()).orderBy(false, true, "seq")));
        }
        if (childs.contains("contract")) {
            caseBean.setAgentContractList(agentContractService.queryDataById(caseBean.getId()));
        }

        return caseBean;
    }

    @Override
    public CaseRecord queryExecuteById(String id, String code) {
        QueryWrapper<CaseRecord> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("parent_id", id);
        queryWrapper.eq("CASE_PROCESS_TYPE_CODE", code);
        queryWrapper.orderByDesc("create_time");
        List<CaseRecord> list = list(queryWrapper);
        if (CollectionUtils.isNotEmpty(list)) {
            CaseRecord caseRecord = list.get(0);
            caseRecord.setPartiesList(partiesService.list(new QueryWrapper<Parties>().eq("master_id", caseRecord.getId()).orderBy(false, true, "seq")));
            caseRecord.setClaimList(claimService.list(new QueryWrapper<Claim>().eq("parent_id", caseRecord.getId()).orderBy(false, true, "seq")));
            return caseRecord;
        }
        return null;
    }

    /**
     * 案件阶段排序(根据定好的阶段顺序)
     *
     * @return
     */
    String caseProcessOrder() {
        StringBuffer sb = new StringBuffer();
        sb.append(" case case_current_process ")

                .append("when '收立案中'                      then 1 ")
                .append("when '收立案完成'                  then 2 ")
                .append("when '诉前调解中'                  then 2.1 ")
                .append("when '诉前调解完成'                  then 2.2 ")
                .append("when '管辖一审中'                  then 3 ")
                .append("when '管辖一审完成'              then 4 ")
                .append("when '管辖二审中'              then 5 ")
                .append("when '管辖二审完成'          then 6 ")
                .append("when '一审中'                     then 7 ")
                .append("when '一审完成'                 then 8 ")
                .append("when '仲裁中'                     then 8.1 ")
                .append("when '仲裁完成'                 then 8.2 ")
                .append("when '撤销仲裁中'                     then 8.3 ")
                .append("when '撤销仲裁完成'                 then 8.4 ")
                .append("when '重审中'             then 8.5 ")
                .append("when '重审完成'          then 8.6 ")
                .append("when '重审一审中'             then 9 ")
                .append("when '重审一审完成'          then 10 ")
                .append("when '再审一审中'              then 11 ")
                .append("when '再审一审完成'          then 12 ")
                .append("when '二审中'                     then 13 ")
                .append("when '二审完成'                 then 14 ")
                .append("when '重审二审中'             then 15 ")
                .append("when '重审二审完成'         then 16 ")
                .append("when '再审二审中'             then 17 ")
                .append("when '再审二审完成'         then 18 ")
                .append("when '再审申请中'             then 19 ")
                .append("when '再审申请完成'         then 20 ")
                .append("when '执行中'                    then 21 ")
                .append("when '执行完成'                then 22 ")
                .append("when '结案中'                    then 23 ")
                .append("when '结案完成'                then 24 ")
                .append("when '不起诉'                then 25 ")
                .append(" else 99 end ");
        return sb.toString();
    }

    /**
     * 获取查询条件
     *
     * @param json
     * @param wrapper
     */
    void getFilter(JSONObject json, QueryWrapper<CaseRecord> wrapper) {
        Boolean isReport = json.containsKey("isReport") ? json.getBoolean("isReport") : false;
        String other = json.containsKey("other") ? json.getString("other") : null;
        //案件名称
        String caseName = json.containsKey("caseName") ? json.getString("caseName") : null;
        //案号
        String caseNumber = json.containsKey("caseNumber") ? json.getString("caseNumber") : null;
        //是否结案
        String whetherEnd = json.containsKey("whetherEnd") ? json.getString("whetherEnd") : null;
        //当事单位
        String party = json.containsKey("party") ? json.getString("party") : null;
        //案件阶段
        String caseCurrentProcess = json.containsKey("caseCurrentProcess") ? json.getString("caseCurrentProcess") : null;
        //案件类型
        String caseTypeId = json.containsKey("caseTypeId") ? json.getString("caseTypeId") : null;
        //案件类型
        String caseKindCode = json.containsKey("caseKindCode") ? json.getString("caseKindCode") : null;
        //案由
        String causeName = json.containsKey("causeName") ? json.getString("causeName") : null;
        //发案事由
        String causeOfIn = json.containsKey("causeOfIn") ? json.getString("causeOfIn") : null;
        //重大案件
        Boolean whetherMajor = json.containsKey("whetherMajor") ? json.getBoolean("whetherMajor") : null;
        //所属单位
        String belongPlateIds = json.containsKey("belongPlateId") ? json.getString("belongPlateId") : null;
        //创建人
        String createPsnName = json.containsKey("createPsnName") ? json.getString("createPsnName") : null;
        // 经办单位
        String createOgnName = json.containsKey("createOgnName") ? json.getString("createOgnName") : null;
        // 经办部门
        String createDeptName = json.containsKey("createDeptName") ? json.getString("createDeptName") : null;
        /*陈杰加的4个*/
        //案发地区
        String venueProvince = json.containsKey("venueProvince") ? json.getString("venueProvince") : null;
        //我方地位
        String ourPosition = json.containsKey("ourPosition") ? json.getString("ourPosition") : null;
        //案件种类
        String caseKind = json.containsKey("caseKind") ? json.getString("caseKind") : null;
        //管辖机构
        String court = json.containsKey("court") ? json.getString("court") : null;
        //所属单位
        String belongPlateTag = json.containsKey("belongPlateTag") ? json.getString("belongPlateTag") : null;
        //案件年度
        String caseTimeTag = json.containsKey("caseTimeTag") ? json.getString("caseTimeTag") : null;
        //风险等级
        String riskLevel = json.containsKey("riskLevel") ? json.getString("riskLevel") : null;
        //集团案号
        String ognCaseCode = json.containsKey("ognCaseCode") ? json.getString("ognCaseCode") : null;
        //案件结果(处理情形)
        String handleSituation = json.containsKey("handleSituation") ? json.getString("handleSituation") : null;
        //他方当事人性质
        String partyNature = json.containsKey("partyNature") ? json.getString("partyNature") : null;
        //是否聘请律师
        String whetherAgent = json.containsKey("whetherAgent") ? json.getString("whetherAgent") : null;
        //是否涉外
        String venueIsOut = json.containsKey("venueIsOut") ? json.getString("venueIsOut") : null;
        //是否反诉
        String whether18 = json.containsKey("whether18") ? json.getString("whether18") : null;
        //标签
        String[] tagArr = json.containsKey("tag") ? json.getString("tag") == null ? null : json.getString("tag").split(",") : null;
        //是否案例库
        Boolean hasTag = json.containsKey("hasTag") ? json.getBoolean("hasTag") : false;
        //是否移交
        Boolean isTransfer = json.containsKey("isTransfer") ? json.getBoolean("isTransfer") : false;
        //当前人ID
        String transferId = json.containsKey("transferId") ? json.getString("transferId") : null;

        Boolean isAdmin = json.containsKey("isAdmin") ? json.getBoolean("isAdmin") : false;

        //标的额(最小值)
        BigDecimal caseMoneyMin = json.containsKey("caseMoneyMin") ? json.getBigDecimal("caseMoneyMin") : null;
        //标的额(最大值)
        BigDecimal caseMoneyMax = json.containsKey("caseMoneyMax") ? json.getBigDecimal("caseMoneyMax") : null;
        //案发时间(最小值)
        Date caseStartTimeMin = json.containsKey("caseStartTimeMin") ? json.getDate("caseStartTimeMin") : null;
        //案发时间(最大值)
        Date caseStartTimeMax = json.containsKey("caseStartTimeMax") ? json.getDate("caseStartTimeMax") : null;
        //模糊搜索值
        String fuzzyValue = json.containsKey("fuzzyValue") ? json.getString("fuzzyValue") : null;

        String functionCode = json.containsKey("functionCode") ? json.getString("functionCode") : null;

        boolean relation = json.containsKey("relation") ? json.getBoolean("relation") : false;

        String AJJC = json.containsKey("AJJC") ? json.getString("AJJC") : null;

        String AFSJ = json.containsKey("AFSJ") ? json.getString("AFSJ") : null;

        String orgId = json.containsKey("orgId") ? json.getString("orgId") : "";

        String groupPackageId = json.containsKey("groupPackageId") ? json.getString("groupPackageId") : "";

        String ognPackageId = json.containsKey("ognPackageId") ? json.getString("ognPackageId") : "";

        Boolean sfgzw = json.containsKey("sfgzw") ? json.getBoolean("sfgzw") == null ? false : json.getBoolean("sfgzw") : false;

        String orgFullId = json.containsKey("orgFullId") ? json.getString("orgFullId") : "";
        //统计年度
        String statisticsYear = json.containsKey("statisticsYear") ? json.getString("statisticsYear") : null;
        //是否要素单位
        Boolean whetherElement = json.containsKey("whetherElement") ? json.getBoolean("whetherElement") : null;
        //管理单位
        String managementUnit = json.containsKey("managementUnit") ? json.getString("managementUnit") : null;
        //单位类型
        String unitType = json.containsKey("unitType") ? json.getString("unitType") : null;
        //是否并表单位
        String whetherMerge = json.containsKey("whetherMerge") ? json.getString("whetherMerge") : null;
        //单位股权类型
        String equityType = json.containsKey("equityType") ? json.getString("equityType") : null;
        // 一审结果
        String firstResult = json.containsKey("firstResult") ? json.getString("firstResult") : null;
        // 再审结果
        String retrialResult = json.containsKey("retrialResult") ? json.getString("retrialResult") : null;
        // 二审结果
        String secondResult = json.containsKey("secondResult") ? json.getString("secondResult") : null;
        // 仲裁结果
        String arbitrationResult = json.containsKey("arbitrationResult") ? json.getString("arbitrationResult") : null;
        // 结案时间(最小值)
        Date caseEndTimeMin = json.containsKey("caseEndTimeMin") ? json.getDate("caseEndTimeMin") : null;
        // 结案时间(最大值)
        Date caseEndTimeMax = json.containsKey("caseEndTimeMax") ? json.getDate("caseEndTimeMax") : null;
        //模糊搜索匹配字段
        String[] cols = {"case_name", "case_number", "cause_name", "case_money", "case_type", "case_current_process", "create_psn_name", "CASE_KIND", "belong_plate"};
        // 办案小组
        String caseTeamName = json.containsKey("caseTeamName") ? json.getString("caseTeamName") : null;

        //排序字段
        String orderCol = json.containsKey("orderCol") ? json.getString("orderCol") : null;
        //排序字段是否升序
        boolean orderColValue = json.containsKey("orderColValue") ? json.getBoolean("orderColValue") : false;

        boolean isQuery = json.containsKey("isQuery") ? json.getBoolean("isQuery") : false;

        if (isQuery) {
            wrapper.and(i -> (i.isNotNull("case_kind")).getCustomSqlSegment());
        }

        if (relation) {
            wrapper.and(i -> (i.isNotNull("case_kind")).getCustomSqlSegment());
        }

        if (StringUtils.isNotBlank(caseName)) {
            wrapper.and(i -> (i.like("case_name", caseName)).getCustomSqlSegment());
        }
        if (StringUtils.isNotBlank(caseTeamName)) {
            wrapper.and(i -> i.like("case_team_name", caseTeamName));
        }
        if (StringUtils.isNotBlank(caseCurrentProcess)) {
            wrapper.and(i -> i.like("case_current_process", caseCurrentProcess));
        }
        if (StringUtils.isNotBlank(caseCurrentProcess)) {
            if (caseCurrentProcess.equals("未结案")) {
                wrapper.and(i -> i.ne("case_current_process", "结案完成"));
            } else if (caseCurrentProcess.equals("一审阶段")) {
                List<String> list = new ArrayList<>();
                list.add("收立案中");
                list.add("收立案完成");
                list.add("管辖一审中");
                list.add("管辖一审完成");
                list.add("管辖二审中");
                list.add("管辖二审完成");
                list.add("一审中");
                list.add("一审完成");

                wrapper.and(i -> i.in("case_current_process", list));
            } else {
                wrapper.and(i -> i.like("case_current_process", caseCurrentProcess));
            }
        }
        if (StringUtils.isNotBlank(caseTypeId)) {
            wrapper.and(i -> i.like("case_type_id", caseTypeId));
        }
        if (StringUtils.isNotBlank(caseKindCode)) {
            wrapper.and(i -> i.like("case_kind_code", caseKindCode));
        }
        if (StringUtils.isNotBlank(causeName)) {
            wrapper.and(i -> i.like("cause_name", causeName));
        }
        if (StringUtils.isNotBlank(causeOfIn)) {
            wrapper.and(i -> i.like("cause_of_in", causeOfIn));
        }
        if (whetherMajor != null) {
            wrapper.and(i -> i.eq("whether_Major", whetherMajor ? 1 : 0));
        }
        if (StringUtils.isNotBlank(caseNumber)) {
            wrapper.and(i -> i.like("case_number", caseNumber));
        }
        if (StringUtils.isNotBlank(whetherMerge)) {
            wrapper.and(i -> i.like("WHETHER_MERGE", whetherMerge));
        }
        if (StringUtils.isNotBlank(whetherEnd)) {
            if ("1".equals(whetherEnd)) {
                wrapper.and(i -> i.eq("WHETHER_END", whetherEnd));
            } else {
                wrapper.and(i -> i.eq("WHETHER_END", whetherEnd).or().isNull("WHETHER_END"));
            }
        }
        if (StringUtils.isNotBlank(equityType)) {
            wrapper.and(i -> i.like("EQUITY_TYPE", equityType));
        }
        if (StringUtils.isNotBlank(party)) {
            QueryWrapper<Parties> partiesQueryWrapper = new QueryWrapper<>();
            partiesQueryWrapper.like("party", party);
            List<Parties> parties = partiesService.list(partiesQueryWrapper);
            ArrayList<String> caseIds = new ArrayList<>();
            for (Parties party1 : parties) {
                caseIds.add(party1.getMasterId());
            }
            if (caseIds.size() > 0)
                wrapper.and(i -> i.in("ID", caseIds));
        }

        if (StringUtils.isNotBlank(firstResult)) {
            QueryWrapper<CaseRecord> resultWrapper = new QueryWrapper<>();
            resultWrapper.eq("handle_situation", firstResult);
            resultWrapper.eq("case_process_type", "一审");
            resultWrapper.isNotNull("parent_id");
            List<CaseRecord> first = caseService.list(resultWrapper);
            ArrayList<String> caseIds = new ArrayList<>();

            for (CaseRecord caseRecord : first) {
                caseIds.add(caseRecord.getParentId());
            }

            if (caseIds.size() > 0)
                wrapper.and(i -> i.in("ID", caseIds));
        }

        if (StringUtils.isNotBlank(retrialResult)) {
            QueryWrapper<CaseRecord> resultWrapper = new QueryWrapper<>();
            resultWrapper.eq("handle_situation", retrialResult);
            resultWrapper.and(i -> i.eq("case_process_type", "再审一审").or().eq("case_process_type", "再审二审"));
            resultWrapper.isNotNull("parent_id");
            List<CaseRecord> retrial = caseService.list(resultWrapper);
            ArrayList<String> caseIds = new ArrayList<>();

            for (CaseRecord caseRecord : retrial) {
                caseIds.add(caseRecord.getParentId());
            }

            if (caseIds.size() > 0) {
                wrapper.and(i -> i.in("ID", caseIds));
            }
        }

        if (StringUtils.isNotBlank(secondResult)) {
            QueryWrapper<CaseRecord> resultWrapper = new QueryWrapper<>();
            resultWrapper.eq("handle_situation", secondResult);
            resultWrapper.eq("case_process_type", "二审");
            resultWrapper.isNotNull("parent_id");
            List<CaseRecord> retrial = caseService.list(resultWrapper);
            ArrayList<String> caseIds = new ArrayList<>();

            for (CaseRecord caseRecord : retrial) {
                caseIds.add(caseRecord.getParentId());
            }

            if (caseIds.size() > 0) {
                wrapper.and(i -> i.in("ID", caseIds));
            }
        }

        if (StringUtils.isNotBlank(arbitrationResult)) {
            QueryWrapper<CaseRecord> resultWrapper = new QueryWrapper<>();
            resultWrapper.eq("handle_situation", arbitrationResult);
            resultWrapper.eq("case_process_type", "仲裁");
            resultWrapper.isNotNull("parent_id");
            List<CaseRecord> arbitration = caseService.list(resultWrapper);
            ArrayList<String> caseIds = new ArrayList<>();

            for (CaseRecord caseRecord : arbitration) {
                caseIds.add(caseRecord.getParentId());
            }

            if (caseIds.size() > 0) {
                wrapper.and(i -> i.in("ID", caseIds));
            }
        }

        if (StringUtils.isNotBlank(handleSituation)) {
            wrapper.and(i -> i.eq("handle_situation", handleSituation));
        }

        if (StringUtils.isNotBlank(partyNature)) {
            QueryWrapper<Parties> partiesQueryWrapper = new QueryWrapper<>();
            partiesQueryWrapper.eq("party_nature", partyNature);
            List<Parties> parties = partiesService.list(partiesQueryWrapper);
            ArrayList<String> caseIds = new ArrayList<>();
            for (Parties party1 : parties) {
                caseIds.add(party1.getMasterId());
            }
            if (caseIds.size() > 0)
                wrapper.and(i -> i.in("ID", caseIds));
        }

        if (StringUtils.isNotBlank(whetherAgent)) {
            QueryWrapper<Agent> agentQueryWrapper = new QueryWrapper<>();
            agentQueryWrapper.eq("whether_lawyer", whetherAgent);
            List<Agent> agentList = agentService.list(agentQueryWrapper);
            ArrayList<String> caseIds = new ArrayList<>();
            for (Agent agent : agentList) {
                caseIds.add(agent.getParentId());
            }
            if (caseIds.size() > 0)
                wrapper.and(i -> i.in("ID", caseIds));
        }
        if (StringUtils.isNotBlank(venueIsOut)) {
            wrapper.and(i -> i.eq("venue_is_out", venueIsOut));
        }
        if (StringUtils.isNotBlank(whether18)) {
            wrapper.and(i -> i.eq("whether18", whether18));
        }
        //所属单位
        if (StringUtils.isNotBlank(belongPlateIds)) {
            String[] split = belongPlateIds.split(",");
            for (int i = 0; i < split.length; i++) {
                if (i == 0) {
                    int finalI = i;
                    wrapper.and(wapperI -> wapperI.like("belong_Plate_Full_Id", split[finalI]));
                } else {
                    wrapper.or().like("belong_Plate_Full_Id", split[i]);
                }
            }
        }
        if (StringUtils.isNotBlank(createPsnName)) {
            wrapper.and(i -> i.like("create_psn_name", createPsnName));
        }
        if (StringUtils.isNotBlank(createOgnName)) {
            wrapper.and(i -> i.eq("create_Ogn_Name", createOgnName));
        }
        if (StringUtils.isNotBlank(createDeptName)) {
            wrapper.and(i -> i.like("create_Dept_Name", createDeptName));
        }
        /*陈杰加的4个*/
        if (StringUtils.isNotBlank(venueProvince)) {
            wrapper.and(i -> i.eq("VENUE_PROVINCE", venueProvince));
        }
        if (StringUtils.isNotBlank(ourPosition)) {
            wrapper.and(i -> i.eq("OUR_POSITION", ourPosition));
        }
        if (StringUtils.isNotBlank(caseKind)) {
            wrapper.and(i -> i.eq("CASE_KIND", caseKind));
        }
        if (StringUtils.isNotBlank(court)) {
            wrapper.and(i -> i.eq("court", court));
        }
        if (StringUtils.isNotBlank(belongPlateTag)) {
            wrapper.and(i -> i.eq("belong_plate", belongPlateTag));
        }
        if (StringUtils.isNotBlank(caseTimeTag)) {
            wrapper.and(i -> i.eq("to_char(case_time, 'yyyy')", caseTimeTag));
        }
        if (StringUtils.isNotBlank(riskLevel)) {
            wrapper.and(i -> i.eq("risk_Level", riskLevel));
        }
        if (StringUtils.isNotBlank(ognCaseCode)) {
            wrapper.and(i -> i.like("ogn_case_code", ognCaseCode));
        }


        if (StringUtils.isNotBlank(AJJC)) {
            wrapper.and(i -> i.like("case_current_process", AJJC));
        }
        if (StringUtils.isNotBlank(AFSJ)) {
            wrapper.and(i -> i.like("DATE_FORMAT(case_time,'yyyy-mm')", AFSJ));
        }

        if (StringUtils.isNotBlank(statisticsYear)) {
            String date = statisticsYear + "-01-01 00:00:01";
            String date1 = statisticsYear + "-12-31 23:59:59";
            wrapper.and(i -> i.like("to_char(case_time,'yyyy-MM-dd')", statisticsYear)
                    .or(j -> j.ne("case_current_process", "结案完成").le("case_time", date))
                    .or(k -> k.le("case_time", date).eq("case_current_process", "结案完成").like("to_char(case_end,'yyyy-MM-dd')", statisticsYear))
                    .or(l -> l.le("case_time", date).eq("case_current_process", "结案完成").ge("case_end", date1))
            );
        }

        if (hasTag && (tagArr == null || tagArr.length <= 0)) {
            List<Tag> name = tagService.list(new QueryWrapper<Tag>().eq("is_delete", 0));
            List<String> strings = new ArrayList<>();
            for (Tag tag1 : name) {
                String dataId = tag1.getDataId();
                strings.add(dataId);
            }
            wrapper.and(i -> i.in("id", strings));
        }

        if (tagArr != null && tagArr.length > 0) {
            QueryWrapper<Tag> tagWrapper = new QueryWrapper<>();
            for (String s : tagArr) {
                tagWrapper.or().like("name", s);
            }
            tagWrapper.eq("is_delete", 0);
            List<Tag> name = tagService.list(tagWrapper);
            if (CollectionUtils.isNotEmpty(name)) {
                List<String> strings = new ArrayList<>();
                for (Tag tag1 : name) {
                    String dataId = tag1.getDataId();
                    strings.add(dataId);
                }
                wrapper.and(i -> i.in("id", strings));
            } else {
                wrapper.ne("1", "1");
            }
        }

        if (caseMoneyMin != null && caseMoneyMin.doubleValue() > 0) {
            wrapper.and(i -> i.ge("case_money", caseMoneyMin));
        }
        if (caseMoneyMax != null && caseMoneyMax.doubleValue() > 0) {
            wrapper.and(i -> i.le("case_money", caseMoneyMax));
        }
        if (caseStartTimeMin != null) {
            wrapper.and(i -> i.ge("case_time", caseStartTimeMin));
        }
        if (caseStartTimeMax != null) {
            wrapper.and(i -> i.le("case_time", caseStartTimeMax));
        }

        if (caseEndTimeMin != null) {
            wrapper.and(i -> i.ge("case_time", caseEndTimeMin));
        }
        if (caseEndTimeMax != null) {
            wrapper.and(i -> i.le("case_time", caseEndTimeMax));
        }

        if (sfgzw != null && sfgzw) {
            wrapper.inSql("id", "SELECT a.id FROM sg_case_records a JOIN sg_tag b ON a.id = b.data_id AND b.name ='国资委管理案件'");
        }
        if (whetherElement != null) {
            wrapper.and(i -> i.eq("WHETHER_ELEMENT", whetherElement ? 1 : 0));
        }
        if (StringUtils.isNotBlank(managementUnit)) {
            String[] arr = managementUnit.split("、");

            if (arr.length > 1) {
                wrapper.and(i -> i.in("MANAGEMENT_UNIT", arr));
            } else {
                wrapper.and(i -> i.like("MANAGEMENT_UNIT", managementUnit));
            }
        }
        if (StringUtils.isNotBlank(unitType)) {
            wrapper.and(i -> i.like("UNIT_TYPE", unitType));
        }
        if (StringUtils.isNotBlank(ognPackageId) && StringUtils.isNotBlank(groupPackageId)) {
            wrapper.and(item -> {
                item.eq("OGN_PACKAGE_ID", ognPackageId);
                item.eq("GROUP_PACKAGE_ID", groupPackageId);
                item.or(i -> {
                    i.eq("CREATE_ORG_ID", orgId);
                });
            });
        }
        if (StringUtils.isNotBlank(riskLevel)) {
            wrapper.and(item -> {
                item.eq("risk_Level", riskLevel);
                item.or(i -> {
                    i.eq("CREATE_ORG_ID", orgId);
                });
            });
        }


        wrapper.and(i -> i.isNull("parent_id"));

        if (isQuery) {
            wrapper.and(i -> i.ne("case_current_process", "收立案中"));
        }

//        String[] cols = {"case_name", "case_number", "cause_name", "case_money", "case_type", "case_current_process", "create_psn_name", "CASE_KIND", "belong_plate"};

        if (StringUtils.isNotBlank(fuzzyValue)) {
            wrapper.and(i -> i.like("case_name", fuzzyValue).or()
                    .like("case_number", fuzzyValue).or()
                    .like("cause_name", fuzzyValue).or()
                    .like("case_money", fuzzyValue).or()
                    .like("case_type", fuzzyValue).or()
                    .like("case_current_process", fuzzyValue).or()
                    .like("create_psn_name", fuzzyValue).or()
                    .like("CASE_KIND", fuzzyValue).or()
                    .like("belong_plate", fuzzyValue));
//                                    wrapper.and(i-> i);
//            for (int i = 0; i < cols.length; i++) {
//                if (i == 0) {
//                    wrapper.like(cols[i], fuzzyValue);
//                } else {
//                    wrapper.or().like(cols[i], fuzzyValue);
//                }
//            }
//            wrapper.or().eq("our_position", fuzzyValue);
        }
        if (isAdmin) {
//                                    SysUser user = SysUtils.getCurrentUser();
//                                    List<SysRole> roleList = user.getRoleList();
//                                    for (SysRole sysRole : roleList) {
//                                             if (sysRole.getVal().equals("turnover")){
//                                                      isTransfer = "2";
//                                             }
//                                    }

        }

        if (isReport) {
            if (isQuery) {
                wrapper.isNotNull("case_kind_code");
                if (StringUtils.isNotBlank(other)) {
                    if ("本年新发".equals(other)) {
                        Calendar cal = Calendar.getInstance();
                        int year = cal.get(Calendar.YEAR);
                        wrapper.like("to_char(case_time,'yyyy-MM-dd')", String.valueOf(year));
                    }
                    if ("在手案件".equals(other)) {
                    }
                    if ("刑事类".equals(other)) {
                        wrapper.eq("case_kind_code", "xsaj");
                    }
                }
            } else {
                List<SgAdminConfig> sgAdminConfigList = sgAdminConfigService.getConfigOrg(orgId);
                if (CollectionUtils.isNotEmpty(sgAdminConfigList)) {
                    wrapper.and(i -> {
                        boolean bool = false;
                        for (SgAdminConfig sgAdminConfig : sgAdminConfigList) {
                            if (bool) {
                                i.or().like("create_psn_full_id", sgAdminConfig.getOrgFullId());
                            } else {
                                i.like("create_psn_full_id", sgAdminConfig.getOrgFullId());
                                bool = true;
                            }
                        }
                    });
                    //未结案  或者  结案  结案时间是本季度
                    wrapper.and(item -> {
                        item.notLike("case_process", "结案");
                        item.or(item2 -> {
                            item2.like("case_process", "结案");
                            Calendar cal = Calendar.getInstance();
                            int year = cal.get(Calendar.YEAR);
                            int intMonth = cal.get(Calendar.MONTH) + 1;
                            int i = intMonth % 3 == 0 ? intMonth / 3 : intMonth / 3 + 1;
                            SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                            Date start = null;
                            Date end = null;
                            try {
                                if (i == 1) {
                                    start = sf.parse(year + "-01-01 00:00:01");
                                    end = sf.parse(year + "-03-31 24:00:00");
                                }
                                if (i == 2) {
                                    start = sf.parse(year + "-04-01 00:00:01");
                                    end = sf.parse(year + "-06-30 24:00:00");
                                }
                                if (i == 3) {
                                    start = sf.parse(year + "-07-01 00:00:01");
                                    end = sf.parse(year + "-09-30 24:00:00");
                                }
                                if (i == 4) {
                                    start = sf.parse(year + "-10-01 00:00:01");
                                    end = sf.parse(year + "-12-31 24:00:00");
                                }
                            } catch (ParseException e) {
                                log.info("printStackTrace异常");
                            }
                            item2.ge("case_end", start);
                            item2.le("case_end", end);
                        });
                    });
                } else {
                    wrapper.ne("1", "1");
                }
            }
        } else {
            if (isTransfer) {
                wrapper.like("create_psn_full_id", orgId);
            } else {
                Long functionId = DataAuthUtils.getFunctionIdByCode("case_ledger_index");
                if (isQuery) {
                    DataAuthUtils.dataPermSql(wrapper, null, functionId, orgId, "", "case");
                } else {
                    DataAuthUtils.currentDataPermSql(wrapper, null, orgId, "", "case");
                }
            }
        }

        if (StringUtils.isNotBlank(orderCol)) {
            if (orderCol.equals("caseCurrentProcess")) {
                wrapper.orderBy(true, orderColValue, caseProcessOrder());
            } else {
                wrapper.orderBy(true, orderColValue, Utils.humpToLine2(orderCol));
            }
        } else {
            wrapper.orderBy(true, false, "create_time");
        }

    }

    void getFilter2(JSONObject json, QueryWrapper<CaseRecord> wrapper) {
        Boolean isReport = json.containsKey("isReport") ? json.getBoolean("isReport") : false;
        String other = json.containsKey("other") ? json.getString("other") : null;
        //案件名称
        String caseName = json.containsKey("caseName") ? json.getString("caseName") : null;
        //案件阶段
        String caseCurrentProcess = json.containsKey("caseCurrentProcess") ? json.getString("caseCurrentProcess") : null;
        //案件类型
        String caseTypeId = json.containsKey("caseTypeId") ? json.getString("caseTypeId") : null;
        //案件类型
        String caseKindCode = json.containsKey("caseKindCode") ? json.getString("caseKindCode") : null;
        //案由
        String causeName = json.containsKey("causeName") ? json.getString("causeName") : null;
        //发案事由
        String causeOfIn = json.containsKey("causeOfIn") ? json.getString("causeOfIn") : null;
        //重大案件
        Boolean whetherMajor = json.containsKey("whetherMajor") ? json.getBoolean("whetherMajor") : null;
        //案号
        String caseNumber = json.containsKey("caseNumber") ? json.getString("caseNumber") : null;
        //所属单位
//        String belongPlate = json.containsKey("belongPlate") ? json.getString("belongPlate") : null;
        //所属单位
        String belongPlateIds = json.containsKey("belongPlateId") ? json.getString("belongPlateId") : null;
        //创建人
        String createPsnName = json.containsKey("createPsnName") ? json.getString("createPsnName") : null;
        /*陈杰加的4个*/
        //案发地区
        String venueProvince = json.containsKey("venueProvince") ? json.getString("venueProvince") : null;
        //我方地位
        String ourPosition = json.containsKey("ourPosition") ? json.getString("ourPosition") : null;
        //案件种类
        String caseKind = json.containsKey("caseKind") ? json.getString("caseKind") : null;
        //管辖机构
        String court = json.containsKey("court") ? json.getString("court") : null;
        //所属单位
        String belongPlateTag = json.containsKey("belongPlateTag") ? json.getString("belongPlateTag") : null;
        //标签
        String[] tagArr = json.containsKey("tag") ? json.getString("tag") == null ? null : json.getString("tag").split(",") : null;
        //是否案例库
        Boolean hasTag = json.containsKey("hasTag") ? json.getBoolean("hasTag") : false;
        //是否移交
        String isTransfer = json.containsKey("isTransfer") ? json.getString("isTransfer") : "2";
        //当前人ID
        String transferId = json.containsKey("transferId") ? json.getString("transferId") : null;

        Boolean isAdmin = json.containsKey("isAdmin") ? json.getBoolean("isAdmin") : false;

        //标的额(最小值)
        BigDecimal caseMoneyMin = json.containsKey("caseMoneyMin") ? json.getBigDecimal("caseMoneyMin") : null;
        //标的额(最大值)
        BigDecimal caseMoneyMax = json.containsKey("caseMoneyMax") ? json.getBigDecimal("caseMoneyMax") : null;
        //案发时间(最小值)
        Date caseStartTimeMin = json.containsKey("caseStartTimeMin") ? json.getDate("caseStartTimeMin") : null;
        //案发时间(最大值)
        Date caseStartTimeMax = json.containsKey("caseStartTimeMax") ? json.getDate("caseStartTimeMax") : null;
        //模糊搜索值
        String fuzzyValue = json.containsKey("fuzzyValue") ? json.getString("fuzzyValue") : null;

        String functionCode = json.containsKey("functionCode") ? json.getString("functionCode") : null;

        boolean relation = json.containsKey("relation") ? json.getBoolean("relation") : false;

        String AJJC = json.containsKey("AJJC") ? json.getString("AJJC") : null;

        String AFSJ = json.containsKey("AFSJ") ? json.getString("AFSJ") : null;

        String orgId = json.containsKey("orgId") ? json.getString("orgId") : "";

        String groupPackageId = json.containsKey("groupPackageId") ? json.getString("groupPackageId") : "";

        String ognPackageId = json.containsKey("ognPackageId") ? json.getString("ognPackageId") : "";

        String orgFullId = json.containsKey("orgFullId") ? json.getString("orgFullId") : "";
        //模糊搜索匹配字段
        String[] cols = {"case_name", "case_number", "cause_name", "case_money", "case_type", "case_current_process", "create_psn_name", "CASE_KIND", "belong_plate"};

        //排序字段
        String orderCol = json.containsKey("orderCol") ? json.getString("orderCol") : null;
        //排序字段是否升序
        boolean orderColValue = json.containsKey("orderColValue") ? json.getBoolean("orderColValue") : false;

        boolean isQuery = json.containsKey("isQuery") ? json.getBoolean("isQuery") : false;

        if (isQuery) {
//                           wrapper.and(i-> i).;
            wrapper.and(i -> (i.isNotNull("m.case_kind")).getCustomSqlSegment());
        }

        if (relation) {
            wrapper.and(i -> (i.isNotNull("m.case_kind")).getCustomSqlSegment());
        }

        if (StringUtils.isNotBlank(caseName)) {
            wrapper.and(i -> (i.like("m.case_name", caseName)).getCustomSqlSegment());
        }
        if (StringUtils.isNotBlank(caseCurrentProcess)) {
//                     wrapper.andNew("case_current_process like '" + caseCurrentProcess + "%'");
            wrapper.and(i -> i.like("m.case_current_process", caseCurrentProcess));

            //wrapper.and(i-> i).like("case_current_process", caseCurrentProcess);
        }
        if (StringUtils.isNotBlank(caseCurrentProcess)) {
            if (caseCurrentProcess.equals("未结案")) {

//                                             wrapper.and(i-> i).ne("case_current_process", "结案完成");
                wrapper.and(i -> i.ne("m.case_current_process", "结案完成"));
            } else if (caseCurrentProcess.equals("一审阶段")) {
                List<String> list = new ArrayList<>();
                list.add("收立案中");
                list.add("收立案完成");
                list.add("管辖一审中");
                list.add("管辖一审完成");
                list.add("管辖二审中");
                list.add("管辖二审完成");
                list.add("一审中");
                list.add("一审完成");

                wrapper.and(i -> i.in("m.case_current_process", list));
            } else {
                wrapper.and(i -> i.like("m.case_current_process", caseCurrentProcess));
            }
        }
        if (StringUtils.isNotBlank(caseTypeId)) {
            wrapper.and(i -> i.like("m.case_type_id", caseTypeId));
        }
        if (StringUtils.isNotBlank(caseKindCode)) {
            wrapper.and(i -> i.like("m.case_kind_code", caseKindCode));
        }
        if (StringUtils.isNotBlank(causeName)) {
            wrapper.and(i -> i.like("m.cause_name", causeName));
        }
        if (StringUtils.isNotBlank(causeOfIn)) {
            wrapper.and(i -> i.like("m.cause_of_in", causeOfIn));
        }
        if (whetherMajor != null) {
            wrapper.and(i -> i.eq("m.whether_Major", whetherMajor ? 1 : 0));
        }
        if (StringUtils.isNotBlank(caseNumber)) {
            wrapper.and(i -> i.like("m.case_number", caseNumber));
        }
        //所属单位
        if (StringUtils.isNotBlank(belongPlateIds)) {
            String[] split = belongPlateIds.split(",");
            for (int i = 0; i < split.length; i++) {
                if (i == 0) {
                    int finalI = i;
                    wrapper.and(wapperI -> wapperI.like("m.belong_Plate_Full_Id", split[finalI]));
                } else {
                    wrapper.or().like("m.belong_Plate_Full_Id", split[i]);
                }
            }
        }
        if (StringUtils.isNotBlank(createPsnName)) {
            wrapper.and(i -> i.like("m.create_psn_name", createPsnName));
        }
        /*陈杰加的4个*/
        if (StringUtils.isNotBlank(venueProvince)) {
            wrapper.and(i -> i.eq("m.VENUE_PROVINCE", venueProvince));
        }
        if (StringUtils.isNotBlank(ourPosition)) {
            wrapper.and(i -> i.eq("m.OUR_POSITION", ourPosition));
        }
        if (StringUtils.isNotBlank(caseKind)) {
            wrapper.and(i -> i.eq("m.CASE_KIND", caseKind));
        }
        if (StringUtils.isNotBlank(court)) {
            wrapper.and(i -> i.eq("m.court", court));
        }
        if (StringUtils.isNotBlank(belongPlateTag)) {
            wrapper.and(i -> i.eq("m.belong_plate", belongPlateTag));
        }

        if (StringUtils.isNotBlank(AJJC)) {
            wrapper.and(i -> i.like("m.case_current_process", AJJC));
        }
        if (StringUtils.isNotBlank(AFSJ)) {
            wrapper.and(i -> i.like("m.DATE_FORMAT(case_time,'yyyy-mm')", AFSJ));
        }

        if (hasTag && (tagArr == null || tagArr.length <= 0)) {
            List<Tag> name = tagService.list(new QueryWrapper<Tag>().eq("is_delete", 0));
            List<String> strings = new ArrayList<>();
            for (Tag tag1 : name) {
                String dataId = tag1.getDataId();
                strings.add(dataId);
            }
            wrapper.and(i -> i.in("m.id", strings));
        }

        if (tagArr != null && tagArr.length > 0) {
            QueryWrapper<Tag> tagWrapper = new QueryWrapper<>();
            for (String s : tagArr) {
                tagWrapper.or().like("name", s);
            }
            tagWrapper.eq("is_delete", 0);
            List<Tag> name = tagService.list(tagWrapper);
            List<String> strings = new ArrayList<>();
            for (Tag tag1 : name) {
                String dataId = tag1.getDataId();
                strings.add(dataId);
            }
            wrapper.and(i -> i.in("m.id", strings));
        }

        if (caseMoneyMin != null && caseMoneyMin.doubleValue() > 0) {
            wrapper.and(i -> i.ge("m.case_money", caseMoneyMin));
        }
        if (caseMoneyMax != null && caseMoneyMax.doubleValue() > 0) {
            wrapper.and(i -> i.le("m.case_money", caseMoneyMax));
        }
        if (caseStartTimeMin != null) {
            wrapper.and(i -> i.ge("m.case_time", caseStartTimeMin));
        }
        if (caseStartTimeMax != null) {
            wrapper.and(i -> i.le("m.case_time", caseStartTimeMax));
        }

        if (StringUtils.isNotBlank(ognPackageId) && StringUtils.isNotBlank(groupPackageId)) {
            wrapper.and(item -> {
                item.eq("m.OGN_PACKAGE_ID", ognPackageId);
                item.eq("m.GROUP_PACKAGE_ID", groupPackageId);
                item.or(i -> {
                    i.eq("m.CREATE_ORG_ID", orgId);
                });
            });
        }


        wrapper.and(i -> i.isNull("m.parent_id"));

//        String[] cols = {"case_name", "case_number", "cause_name", "case_money", "case_type", "case_current_process", "create_psn_name", "CASE_KIND", "belong_plate"};

        if (StringUtils.isNotBlank(fuzzyValue)) {
            wrapper.and(i -> i.like("m.case_name", fuzzyValue).or()
                    .like("m.case_number", fuzzyValue).or()
                    .like("m.cause_name", fuzzyValue).or()
                    .like("m.case_money", fuzzyValue).or()
                    .like("m.case_type", fuzzyValue).or()
                    .like("m.case_current_process", fuzzyValue).or()
                    .like("m.create_psn_name", fuzzyValue).or()
                    .like("m.CASE_KIND", fuzzyValue).or()
                    .like("m.belong_plate", fuzzyValue));
//                                    wrapper.and(i-> i);
//            for (int i = 0; i < cols.length; i++) {
//                if (i == 0) {
//                    wrapper.like(cols[i], fuzzyValue);
//                } else {
//                    wrapper.or().like(cols[i], fuzzyValue);
//                }
//            }
//            wrapper.or().eq("our_position", fuzzyValue);
        }
        if (isAdmin) {
//                                    SysUser user = SysUtils.getCurrentUser();
//                                    List<SysRole> roleList = user.getRoleList();
//                                    for (SysRole sysRole : roleList) {
//                                             if (sysRole.getVal().equals("turnover")){
//                                                      isTransfer = "2";
//                                             }
//                                    }

        }

        if (isReport) {
            if (isQuery) {
                wrapper.isNotNull("m.case_kind_code");
                if (StringUtils.isNotBlank(other)) {
                    if ("本年新发".equals(other)) {
                        Calendar cal = Calendar.getInstance();
                        int year = cal.get(Calendar.YEAR);
                        wrapper.like("to_char(m.case_time,'yyyy-MM-dd')", String.valueOf(year));
                    }
                    if ("在手案件".equals(other)) {
                    }
                    if ("刑事类".equals(other)) {
                        wrapper.eq("m.case_kind_code", "xsaj");
                    }
                }
            } else {
                List<SgAdminConfig> sgAdminConfigList = sgAdminConfigService.getConfigOrg(orgId);
                if (CollectionUtils.isNotEmpty(sgAdminConfigList)) {
                    wrapper.and(i -> {
                        boolean bool = false;
                        for (SgAdminConfig sgAdminConfig : sgAdminConfigList) {
                            if (bool) {
                                i.or().like("m.create_psn_full_id", sgAdminConfig.getOrgFullId());
                            } else {
                                i.like("m.create_psn_full_id", sgAdminConfig.getOrgFullId());
                                bool = true;
                            }
                        }
                    });
                    //未结案  或者  结案  结案时间是本季度
                    wrapper.and(item -> {
                        item.notLike("m.case_process", "结案");
                        item.or(item2 -> {
                            item2.like("m.case_process", "结案");
                            Calendar cal = Calendar.getInstance();
                            int year = cal.get(Calendar.YEAR);
                            int intMonth = cal.get(Calendar.MONTH) + 1;
                            int i = intMonth % 3 == 0 ? intMonth / 3 : intMonth / 3 + 1;
                            SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                            Date start = null;
                            Date end = null;
                            try {
                                if (i == 1) {
                                    start = sf.parse(year + "-01-01 00:00:01");
                                    end = sf.parse(year + "-03-31 24:00:00");
                                }
                                if (i == 2) {
                                    start = sf.parse(year + "-04-01 00:00:01");
                                    end = sf.parse(year + "-06-30 24:00:00");
                                }
                                if (i == 3) {
                                    start = sf.parse(year + "-07-01 00:00:01");
                                    end = sf.parse(year + "-09-30 24:00:00");
                                }
                                if (i == 4) {
                                    start = sf.parse(year + "-10-01 00:00:01");
                                    end = sf.parse(year + "-12-31 24:00:00");
                                }
                            } catch (ParseException e) {
                                log.info("printStackTrace异常");
                            }
                            item2.ge("m.case_end", start);
                            item2.le("m.case_end", end);
                        });
                    });
                } else {
                    wrapper.ne("1", "1");
                }
            }
        } else {
            Long functionId = DataAuthUtils.getFunctionIdByCode("case_ledger_index");
            //陈杰加的移交判断逻辑
            if (isQuery) {
                DataAuthUtils.dataPermSql(wrapper, null, functionId, orgId, "m.");
            } else {
                wrapper.like("m.create_psn_full_id", orgId);
            }
        }

        if (StringUtils.isNotBlank(orderCol)) {
            if (orderCol.equals("caseCurrentProcess")) {
                wrapper.orderBy(true, orderColValue, caseProcessOrder());
            } else {
                wrapper.orderBy(true, orderColValue, "m." + Utils.humpToLine2(orderCol));
            }
        } else {
            wrapper.orderBy(true, false, "m.create_time");
        }

    }

    /**
     * 获取诉讼条件信息
     *
     * @param code
     * @return
     */
    private List<String> getHasChilds(String code) {
        String o1 = "parties"; //当事人
        String o2 = "claim"; //诉讼请求
        String o3 = "caseProcess"; //过程记录
        String o7 = "appraisal"; //鉴定
        String o9 = "judgeContent"; //裁判内容
        String o10 = "otherData"; //其他信息
        String o11 = "cf"; //查封信息
        String o12 = "caseEvidenceData"; //查封信息
        String o13 = "team"; //办案小组
        String o14 = "report"; //重大上报
        String o15 = "execute"; //执行明细
        String o16 = "cc"; //
        String o17 = "agent"; //
        String o18 = "contract"; //
        if (StringUtils.isBlank(code)) {
            return Arrays.asList(o1, o2, o3, o10, o12);
        }
        if (code.equals(CaseKind.LA.getKey())) {
            return Arrays.asList(o1, o2, o3, o10, o12);
        }
        if (code.equals(CaseKind.SQLT.getKey())) {
            return Arrays.asList(o3, o7, o9, o10);
        }
        if (code.equals(CaseKind.CXS.getKey())) {
            return Arrays.asList(o3, o10);
        }
        if (code.equals(CaseKind.CXES.getKey())) {
            return Arrays.asList(o3, o10);
        }
        if (code.equals(CaseKind.YS.getKey()) || code.equals(CaseKind.CSYS.getKey()) || code.equals(CaseKind.ZSYS.getKey())
                || code.equals(CaseKind.ZC.getKey())) {
            return Arrays.asList(o1, o2, o3, o7, o9, o10, o12);
        }
        if (code.equals(CaseKind.ES.getKey()) || code.equals(CaseKind.CSES.getKey()) || code.equals(CaseKind.ZSES.getKey())) {
            return Arrays.asList(o1, o2, o3, o7, o9, o10);
        }
        if (code.equals(CaseKind.ZS.getKey())) {
            return Arrays.asList(o3, o9, o10);
        }
        if (code.equals(CaseKind.ZX.getKey())) {
            return Arrays.asList(o3, o7, o10, o11, o15);
        }
        if (code.equals(CaseKind.JA.getKey())) {
            return new ArrayList<>();
        }
        if (code.equals(CaseKind.CXZC.getKey())) {
            return Arrays.asList(o3, o7, o10);
        }
        if (code.equals(CaseKind.WSA.getKey())) {
            return Arrays.asList(o1, o2, o10);
        }
        if (code.equals(CaseKind.CC.getKey())) {
            return Arrays.asList(o1, o2, o3, o9, o10, o16);
        }
        if (code.equals(CaseKind.ZHCH.getKey())) {
            return Arrays.asList(o1, o13, o3, o17, o10, o18);
        }
        if (code.equals(CaseKind.ZHSH.getKey())) {
            return Arrays.asList(o1, o2, o3, o9, o10);
        }
        if (code.equals(CaseKind.SCQS.getKey())) {
            return Arrays.asList(o3, o10);
        }
        return new ArrayList<>();
    }

    /**
     * 更新当前数据状态和主案件阶段
     *
     * @param caseBean
     */
    private void updateCaseProcess(CaseRecord caseBean, CaseRecord caseOn) {

        //判断当前保存数据是过程信息还是主信息,父ID为空为主信息
        if (StringUtils.isBlank(caseBean.getParentId())) {
            //判断当前案件是否只包含主信息
            if (StringUtils.isBlank(caseBean.getCaseCurrentProcessId()) || caseBean.getCaseCurrentProcessId().equals(caseBean.getId())) {
                //历史数据为空表示此次为新增数据
                if (caseOn == null) {
                    // 根据保存或者提交判断当前阶段状态
                    if (caseBean.getCaseKindCode().equals("xzfy")) {
                        caseBean.setCaseCurrentProcess(caseBean.getDataStateCode().equals(DataState.SUBMIT.getKey()) ? "行政复议完成" : "行政复议中");
                    } else {
                        caseBean.setCaseCurrentProcess(caseBean.getCaseProcessType() + (caseBean.getDataStateCode().equals(DataState.SUBMIT.getKey()) ? "完成" : "中"));
                    }

                    // 新增数据，是劳动仲裁转诉讼的，给劳动仲裁打标签
                    if (caseBean.getCaseKindCode().equals("msss") && StringUtils.isNotBlank(caseBean.getRelationId())) {
                        createTag(caseBean, caseBean.getRelationId(), "仲裁转诉讼");
                    }

                    caseBean.setCaseCurrentProcessId(caseBean.getId());
                    caseBean.setCaseProcess(caseBean.getCaseProcessType());
                    caseBean.setCaseProcessIds(caseBean.getId());
                } else {
                    //历史数据为保存状态时,根据此次保存或者提交更新案件阶段信息
                    if (caseOn.getDataStateCode().equals(DataState.SAVE.getKey()) || caseOn.getDataStateCode().equals(DataState.NEED.getKey())) {
                        if (caseBean.getCaseKindCode().equals("xzfy")) {
                            caseBean.setCaseCurrentProcess(caseBean.getDataStateCode().equals(DataState.SUBMIT.getKey()) ? "行政复议完成" : "行政复议中");
                        } else {
                            caseBean.setCaseCurrentProcess(caseBean.getCaseProcessType() + (caseBean.getDataStateCode().equals(DataState.SUBMIT.getKey()) ? "完成" : "中"));
                        }
                    } else {
                        //如果当前案件已经提交过,状态只能为提交
                        caseBean.setDataStateCode(DataState.SUBMIT.getKey());
                        caseBean.setDataState(DataState.SUBMIT.getValue());
                    }
                }

                // 如果是更新收立案，是收立案完成的version+1，后续用于判断是否首次收立案完成
                if (caseBean.getCaseCurrentProcess().equals("收立案完成"))
                    caseBean.setVersion(caseBean.getVersion() + 1);
            } else {
                //如果当前案件已经包含过程信息后,状态只能为提交
                caseBean.setDataStateCode(DataState.SUBMIT.getKey());
                caseBean.setDataState(DataState.SUBMIT.getValue());
            }

            String[] ids = caseBean.getCaseProcessIds().split(",");
            if (ids[ids.length - 1].equals(caseBean.getId())) {
                if (caseBean.getCaseKindCode().equals("xzfy")) {
                    caseBean.setCaseCurrentProcess(caseBean.getDataStateCode().equals(DataState.SUBMIT.getKey()) ? "行政复议完成" : "行政复议中");
                } else {
                    caseBean.setCaseCurrentProcess(caseBean.getCaseProcessType() + (caseBean.getDataStateCode().equals(DataState.SUBMIT.getKey()) ? "完成" : "中"));
                }
            } else {
                CaseRecord cr = getById(ids[ids.length - 1]);
                if (caseBean.getCaseKindCode().equals("xzfy")) {
                    caseBean.setCaseCurrentProcess(cr.getDataStateCode().equals(DataState.SUBMIT.getKey()) ? "行政复议完成" : "行政复议中");
                } else {
                    caseBean.setCaseCurrentProcess(cr.getCaseProcessType() + (cr.getDataStateCode().equals(DataState.SUBMIT.getKey()) ? "完成" : "中"));
                }
            }

        } else {
            // 判断含有历史信息并且历史信息中的状态为提交则更新当前数据未提交状态
            if (caseOn != null && caseOn.getDataStateCode().equals(DataState.SUBMIT.getKey())) {
                caseBean.setDataStateCode(DataState.SUBMIT.getKey());
                caseBean.setDataState(DataState.SUBMIT.getValue());
            }

            //先根据父ID查询出案件主信息
            CaseRecord caseMain = getById(caseBean.getParentId());
            // 记录更新前的案件阶段
            String oldCaseCurrentProcess = caseMain.getCaseCurrentProcess();
            if (caseBean.getCaseProcessTypeCode().equals("end") && DataState.SUBMIT.getKey().equals(caseBean.getDataStateCode())) {
                caseMain.setCaseEnd(caseBean.getCaseTime());
                saveOrUpdate(caseMain);
            }
            //此进程包含在案件进程中
            if (caseBean.getWhetherInclude()) {
                //获取案件进程IDs
                String processIds = caseMain.getCaseProcessIds();

                //判读当前阶段数据已经包含在进程内并且它是当前进程的时候
                if (processIds.contains(caseBean.getId()) && processIds.endsWith(caseBean.getId())) {
                    //根据本次状态更新主信息当前进程状态(不是简易模式的是时候）
                    if (!caseMain.getWhetherEasy()) {
                        if (caseMain.getCaseKindCode().equals("xzfy")) {
                            caseMain.setCaseCurrentProcess(caseBean.getDataStateCode().equals(DataState.SUBMIT.getKey()) ? "行政复议完成" : "行政复议中");
                        } else {
                            caseMain.setCaseCurrentProcess(caseBean.getCaseProcessType() + (caseBean.getDataStateCode().equals(DataState.SUBMIT.getKey()) ? "完成" : "中"));
                        }
                        //更新主案件信息
                        updateById(caseMain);
                    }
                }

                //判断阶段不处于主信息阶段中,即为本次新增阶段
                if (!processIds.contains(caseBean.getId())) {
                    //更新主信息的当前阶段和进程记录
                    if (!caseMain.getWhetherEasy()) {
                        if (caseMain.getCaseKindCode().equals("xzfy")) {
                            caseMain.setCaseCurrentProcess(caseBean.getDataStateCode().equals(DataState.SUBMIT.getKey()) ? "行政复议完成" : "行政复议中");
                        } else {
                            caseMain.setCaseCurrentProcess(caseBean.getCaseProcessType() + (caseBean.getDataStateCode().equals(DataState.SUBMIT.getKey()) ? "完成" : "中"));
                        }
                    }
                    caseMain.setCaseCurrentProcessId(caseBean.getId());
                    caseMain.setCaseProcess(caseMain.getCaseProcess() + "," + caseBean.getCaseProcessType());
                    caseMain.setCaseProcessIds(caseMain.getCaseProcessIds() + "," + caseBean.getId());
                    caseMain.setHasChildren(true);
                    //更新主案件信息
                    updateById(caseMain);
                }
            } else {
                if (caseMain.getHasChildren() == null || !caseMain.getHasChildren()) {
                    caseMain.setHasChildren(true);
                    //更新主案件信息
                    updateById(caseMain);
                }
            }

            // 新旧案件当前阶段不一致，说明案件状态有更新
            if (!oldCaseCurrentProcess.equals(caseMain.getCaseCurrentProcess())) {
                if ("一审完成".equals(caseMain.getCaseCurrentProcess())) {
                    if ("一审开庭前撤诉".equals(caseBean.getHandleSituation())) {
                        createTag(caseBean, caseBean.getParentId(), "一审开庭前撤诉");
                    }
                }
            }
        }
    }

    /**
     * 给案件创建标签
     *
     * @param caseBean 用来获取创建人等相关信息
     * @param dataId   需要打标签的案件ID
     * @param tagName  标签名称
     */
    private void createTag(CaseRecord caseBean, String dataId, String tagName) {
        List<Tag> list = tagService.list(new QueryWrapper<Tag>().eq("data_id", dataId).eq("name", tagName));
        if (CollectionUtils.isEmpty(list)) {
            Tag tag = new Tag();
            tag.setId(StringUtil.makeUUID());
            tag.setDataId(dataId);
            tag.setName(tagName);
            tag.setIsDelete(0);
            tag.setCreateOgnId(caseBean.getCreateOgnId());
            tag.setCreateOgnName(caseBean.getCreateOgnName());
            tag.setCreateDeptId(caseBean.getCreateDeptId());
            tag.setCreateDeptName(caseBean.getCreateDeptName());
            tag.setCreatePsnId(caseBean.getCreatePsnId());
            tag.setCreatePsnName(caseBean.getCreatePsnName());
            tag.setCreatePsnFullId(caseBean.getCreatePsnFullId());
            // 用来表示为系统创建
            tag.setCreatePsnFullName("system");
            tag.setCreateOrgId(caseBean.getCreateOrgId());
            tag.setCreateOrgName(caseBean.getCreateOrgName());
            tag.setCreateTime(new Date());
            tag.setUpdateTime(new Date());
            tag.setShow("ogn");
            tagService.save(tag);
        }
    }

    /**
     * 保存子表
     *
     * @param caseBean
     */
    private void saveChildData(CaseRecord caseBean) {

        if (StringUtils.isBlank(caseBean.getParentId())) {

            //保存代理人
            Utils.saveChilds(caseBean.getAgentList(), "parent_id", caseBean.getId(), agentService, true);

            //保存代理合同
            agentContractService.saveData(caseBean.getAgentContractList(), caseBean.getId());

            //保存关联纠纷、关联项目、关联合同
            middleRelationService.saveData(caseBean.getId(), new String[]{"caseProject", "dispute", "case", "contract"}, caseBean.getRelations());

            //保存保全
            Utils.saveChilds(caseBean.getPreservationList(), "parent_id", caseBean.getId(), preservationService, true);
            //办案小组
            Utils.saveChilds(caseBean.getTeamList(), "parent_id", caseBean.getId(), caseTeamService, true);
            //重大上报
            Utils.saveChilds(caseBean.getReportList(), "parent_id", caseBean.getId(), caseReportService, true);
            //执行明细
            Utils.saveChilds(caseBean.getExecuteList(), "parent_id", caseBean.getId(), sgCaseExecuteService, true);
            //听证信息
            Utils.saveChilds(caseBean.getHearingList(), "parent_id", caseBean.getId(), hearingService, true);

            //保存简易模式,当不是简易模式时,将简易模式数据清空
            if (!caseBean.getWhetherEasy()) caseBean.setCaseEasyModel(null);
            caseEasyModelService.saveData(caseBean.getCaseEasyModel(), caseBean.getId());
        }

        List<String> childs = getHasChilds(caseBean.getCaseProcessTypeCode());

        if (!childs.isEmpty()) {

            //保存当事人子表
            if (childs.contains("parties"))
                Utils.saveChilds(caseBean.getPartiesList(), "master_id", caseBean.getId(), partiesService, true);

            //保存诉讼请求
            if (childs.contains("claim")) {
                Utils.saveChilds(caseBean.getClaimList(), "parent_id", caseBean.getId(), claimService, true);
            }

            //保存其他资料
            if (childs.contains("otherData"))
                Utils.saveChilds(caseBean.getOtherDataList(), "parent_id", caseBean.getId(), otherDataService, true);

            //保存其他资料
            if (childs.contains("caseEvidenceData"))
                Utils.saveChilds(caseBean.getCaseEvidenceDataList(), "parent_id", caseBean.getId(), caseEvidenceDataService, true);

            //保存过程记录
            if (childs.contains("caseProcess"))
                Utils.saveChilds(caseBean.getCaseProcessList(), "parent_id", caseBean.getId(), caseProcessService, true);

            //保存裁判内容子表
            if (childs.contains("judgeContent"))
                Utils.saveChilds(caseBean.getJudgeContentList(), "parent_id", caseBean.getId(), judgeContentService, true);

            //保存鉴定
            if (childs.contains("appraisal"))
                Utils.saveChilds(caseBean.getAppraisalList(), "parent_id", caseBean.getId(), appraisalService, true);

            if (childs.contains("execute")) {
                Utils.saveChilds(caseBean.getExecuteList(), "parent_id", caseBean.getId(), sgCaseExecuteService, true);
            }
            if (childs.contains("team")) {
                Utils.saveChilds(caseBean.getTeamList(), "parent_id", caseBean.getId(), caseTeamService, true);
            }
            if (childs.contains("agent")) {
                Utils.saveChilds(caseBean.getAgentList(), "parent_id", caseBean.getId(), agentService, true);
            }
            if (childs.contains("contract")) {
                agentContractService.saveData(caseBean.getAgentContractList(), caseBean.getId());
            }
        }


    }

    /**
     * 增加更新记录
     *
     * @param caseOn
     */
    private void insertHistory(CaseRecord caseOn, CaseRecord caseBean) {
        //先查看是否有更新记录

        int num = caseHistoryService.count(new QueryWrapper<CaseRecordHistory>().eq("history_id", caseOn.getId()));

        //不含有更新记录,需要先插入一条原数据
        if (num == 0) {
            caseOn = queryDataById(caseOn.getId());
            CaseRecordHistory crh = new CaseRecordHistory(caseOn);
            caseHistoryService.save(crh);
            insertChildData(crh);

        }
        //增加最新的记录为更新记录
        CaseRecordHistory crh = new CaseRecordHistory(caseBean);
//            crh.copyCurrentInfo();
//            caseHistoryService.insert(crh);
        caseHistoryService.save(crh);
        insertChildData(crh);
    }

    /**
     * 历史表插入子表数据
     *
     * @param crh
     */
    private void insertChildData(CaseRecordHistory crh) {

        //插入当事人子表
        insertChilds(crh.getPartiesList(), "master_id", crh.getId(), partiesService);

        //插入代理人
        insertChilds(crh.getAgentList(), "parent_id", crh.getId(), agentService);

        agentContractService.insertHistory(crh.getAgentContractList(), crh.getId());

        //插入关联纠纷、关联项目、关联合同
        middleRelationService.insertData(crh.getId(), crh.getRelations());

        //插入保全
        insertChilds(crh.getPreservationList(), "parent_id", crh.getId(), preservationService);

        caseEasyModelService.insertHistory(crh.getCaseEasyModel(), crh.getId());

        //插入诉讼请求
        insertChilds(crh.getClaimList(), "parent_id", crh.getId(), claimService);

        //插入裁判内容子表
        insertChilds(crh.getJudgeContentList(), "parent_id", crh.getId(), judgeContentService);

        //插入过程记录
        insertChilds(crh.getCaseProcessList(), "parent_id", crh.getId(), caseProcessService);

        //插入鉴定
        insertChilds(crh.getAppraisalList(), "parent_id", crh.getId(), appraisalService);

        //插入其他资料
        insertChilds(crh.getOtherDataList(), "parent_id", crh.getId(), otherDataService);
    }

    /**
     * 插入子表数据
     *
     * @param childList 子表集合
     * @param col       子表关联主键字段
     * @param caseId    主表主键
     * @param service   子表接口类
     * @param <T>       子表类
     */
    private <T> void insertChilds(List<T> childList, String col, String caseId, IService service) {
        if (childList != null && !childList.isEmpty()) {
            for (T t : childList) {
                try {
                    t.getClass().getMethod("setId", String.class).invoke(t, Utils.createUUID());
                    t.getClass().getMethod("set" + Utils.upperFirstLatter(Utils.lineToHump(col)), String.class).invoke(t, caseId);
                } catch (NoSuchMethodException e) {
                    log.info("printStackTrace异常");
                } catch (IllegalAccessException e) {
                    log.info("printStackTrace异常");
                } catch (InvocationTargetException e) {
                    log.info("printStackTrace异常");
                }
            }
            if (!childList.isEmpty()) {
                service.saveBatch(childList);
            }
        }
    }

    @Override
    public List<CaseRecord> queryTimeAxis(JSONObject json) {
        QueryWrapper<CaseRecord> wrapper = new QueryWrapper<>();
        String parentId = json.containsKey("parentId") ? json.getString("parentId") : null;
        if (StringUtils.isNotBlank(parentId)) {
            CaseRecord caseRecord = getById(parentId);
            if (caseRecord == null) {
                return new ArrayList<>();
            }

//            if (caseRecord.getWhetherEasy()) {
//                List<CaseRecord> caseBeans = new ArrayList<>();
//                caseBeans.add(caseRecord);
//                CaseEasyModel caseEasyModel = caseEasyModelService.queryDataByCaseId(caseRecord.getId());
//                if (caseEasyModel != null) {
//                    List<CaseProcess> process = caseEasyModel.getCaseProcessList();
//                    if (CollectionUtils.isNotEmpty(process)) {
//                        CaseRecord record = new CaseRecord();
//                        record.setCaseProcessList(process);
//                        record.setCaseProcessTypeCode("jygc");
//                        record.setCaseProcessType("简易过程");
//                        caseBeans.add(record);
//                    }
//                    if (caseEasyModel.getHasJa() != null && caseEasyModel.getHasJa()) {
//                        CaseRecord record = new CaseRecord();
//                        record.setCaseProcessTypeCode(CaseKind.JA.getKey());
//                        record.setCaseProcessType(CaseKind.JA.getValue());
//                        record.setCaseTime(caseEasyModel.getCloseTime());
//                        caseBeans.add(record);
//                    }
//                }
//                return caseBeans;
//
//            } else {
            wrapper.eq("parent_id", parentId).or().eq("id", parentId);
            wrapper.orderBy(true, true, "create_time");
            List<CaseRecord> caseBeans = list(wrapper);
            List<String> ids = caseBeans.stream().map(bean -> bean.getId()).collect(Collectors.toList());
            List<CaseProcess> process = caseProcessService.list(new QueryWrapper<CaseProcess>().in("parent_id", ids).orderBy(true, true, "occur_time"));
            List<CaseEvidenceData> evidence = caseEvidenceDataService.list(new QueryWrapper<CaseEvidenceData>().eq("whether_Timeline", true).in("parent_id", ids).orderBy(true, true, "occur_time"));
            caseBeans.stream().forEach(bean -> bean.setCaseProcessList(process.stream().filter(item -> item.getParentId().equals(bean.getId())).collect(Collectors.toList())));
            caseBeans.stream().forEach(bean -> bean.setCaseEvidenceDataList(evidence.stream().filter(item -> item.getParentId().equals(bean.getId())).collect(Collectors.toList())));
            return caseBeans;
//            }
        }

        return new ArrayList<>();

    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void copyCase(String id, Integer num) {
        CaseRecord caseRecord = queryDataById(id);
        String key = "AJ" + Calendar.getInstance().get(Calendar.YEAR);
        Integer length = 6;
        List<CaseRecord> caseRecords = new ArrayList<>();

        CaseRecord childsBean = new CaseRecord();
        List<CaseEasyModel> caseEasyModels = new ArrayList<>();

        for (int i = 0; i < num; i++) {
            CaseRecord caseRecordCopy = new CaseRecord();

            BeanUtils.copyProperties(caseRecord, caseRecordCopy);
            caseRecordCopy.setId(Utils.createUUID());
//                  caseRecordCopy.setCaseCode(kvsequenceService.createNextSequenceString(key, length));
            caseRecordCopy.setCaseName(caseRecord.getCaseName() + "-副本" + (i + 1));
            caseRecordCopy.setDataState(DataState.SAVE.getValue());
            caseRecordCopy.setDataStateCode(DataState.SAVE.getKey());
            caseRecordCopy.setHasChildren(false);
            if (caseRecord.getWhetherEasy()) {
                String caseStage = caseRecord.getCaseEasyModel().getCaseStage();
                String caseState = caseRecord.getCaseEasyModel().getCaseState();
                if (StringUtils.isNotBlank(caseStage) && StringUtils.isNotBlank(caseState)) {
                    caseRecordCopy.setCaseCurrentProcess(caseStage + caseState);
                } else {
                    caseRecordCopy.setCaseCurrentProcess(null);
                }
                //复制简易模式数据
                CaseEasyModel caseEasyModel = new CaseEasyModel();
                BeanUtils.copyProperties(caseRecord.getCaseEasyModel(), caseEasyModel);
                caseEasyModel.setId(Utils.createUUID());
                caseEasyModel.setParentId(caseRecordCopy.getId());
                if (CollectionUtils.isEmpty(caseRecord.getCaseEasyModel().getEasyDataList())) {
                    caseEasyModel.setEasyDataList(new ArrayList<>());
                } else {
                    caseEasyModel.setEasyDataList(getChildList(Utils.copyList(caseRecord.getCaseEasyModel().getEasyDataList(), EasyData.class), "parent_id", caseEasyModel.getId()));
                }
                if (CollectionUtils.isEmpty(caseRecord.getCaseEasyModel().getCaseProcessList())) {
                    caseEasyModel.setCaseProcessList(new ArrayList<>());
                } else {
                    caseEasyModel.setCaseProcessList(getChildList(Utils.copyList(caseRecord.getCaseEasyModel().getCaseProcessList(), CaseProcess.class), "parent_id", caseEasyModel.getId()));
                }
                caseEasyModels.add(caseEasyModel);
            } else {
                caseRecordCopy.setCaseCurrentProcess("收立案中");
            }
            caseRecordCopy.setCaseCurrentProcessId(caseRecordCopy.getId());
            caseRecordCopy.setInterfile(0);
            caseRecordCopy.setCaseProcess(caseRecordCopy.getCaseProcessType());
            caseRecordCopy.setCaseProcessIds(caseRecordCopy.getId());
//                  caseRecordCopy.copyCurrentInfo();
            caseRecords.add(caseRecordCopy);

            getChildsData(caseRecord, caseRecordCopy.getId(), childsBean);

        }

        saveBatch(caseRecords);
        addChilds(childsBean);

        if (caseRecord.getWhetherEasy()) {
            caseEasyModelService.insertMore(caseEasyModels);
        }


    }


    /**
     * 获取一个临时数据,数据里面存储所有子表的集合
     *
     * @param caseRecord
     * @param id
     * @param childsBean
     */
    private void getChildsData(CaseRecord caseRecord, String id, CaseRecord childsBean) {

        //保存当事人子表
        if (CollectionUtils.isNotEmpty(caseRecord.getPartiesList())) {
            if (CollectionUtils.isEmpty(childsBean.getPartiesList())) childsBean.setPartiesList(new ArrayList<>());
            childsBean.getPartiesList().addAll(getChildList(Utils.copyList(caseRecord.getPartiesList(), Parties.class), "master_id", id));
        }

        //保存代理人
        if (CollectionUtils.isNotEmpty(caseRecord.getAgentList())) {
            if (CollectionUtils.isEmpty(childsBean.getAgentList())) childsBean.setAgentList(new ArrayList<>());
            childsBean.getAgentList().addAll(getChildList(Utils.copyList(caseRecord.getAgentList(), Agent.class), "parent_id", id));
        }

        //保存代理合同
        if (CollectionUtils.isNotEmpty(caseRecord.getAgentContractList())) {
            if (CollectionUtils.isEmpty(childsBean.getAgentContractList()))
                childsBean.setAgentContractList(new ArrayList<>());
            List<AgentContract> agentContractList = new ArrayList<>();
            caseRecord.getAgentContractList().forEach(i -> {
                AgentContract agentContract = new AgentContract();
                BeanUtils.copyProperties(i, agentContract);
                agentContract.setId(Utils.createUUID());
                agentContract.setParentId(id);
                agentContractList.add(agentContract);
                if (CollectionUtils.isNotEmpty(i.getChilds())) {
                    i.getChilds().forEach(k -> {
                        AgentContract child = new AgentContract();
                        BeanUtils.copyProperties(k, child);
                        child.setId(Utils.createUUID());
                        child.setParentId(agentContract.getId());
                        agentContractList.add(child);
                    });
                }
            });
            childsBean.getAgentContractList().addAll(agentContractList);
        }

        //保存关联纠纷、关联项目、关联合同
        if (CollectionUtils.isNotEmpty(caseRecord.getRelations())) {
            if (CollectionUtils.isEmpty(childsBean.getRelations()))
                childsBean.setRelations(new ArrayList<>());
            childsBean.getRelations().addAll(getChildList(Utils.copyList(caseRecord.getRelations() != null ? caseRecord.getRelations().stream().filter(item -> item.getRelationId().equals(caseRecord.getId())).collect(Collectors.toList()) : null, MiddleRelation.class), "relation_id", id));
        }

        Map<String, String> map = new HashMap<>();


        //保存诉讼请求
        if (CollectionUtils.isNotEmpty(caseRecord.getClaimList())) {
            if (CollectionUtils.isEmpty(childsBean.getClaimList())) childsBean.setClaimList(new ArrayList<>());
            childsBean.getClaimList().addAll(getChildList(Utils.copyList(caseRecord.getClaimList(), Claim.class), "parent_id", id));
        }

        //保存保全
        if (CollectionUtils.isNotEmpty(caseRecord.getPreservationList())) {
            if (CollectionUtils.isEmpty(childsBean.getPreservationList()))
                childsBean.setPreservationList(new ArrayList<>());
            childsBean.getPreservationList().addAll(getChildList(Utils.copyList(caseRecord.getPreservationList(), Preservation.class), "parent_id", id));
        }

        //保存裁判内容子表
        if (CollectionUtils.isNotEmpty(caseRecord.getJudgeContentList())) {
            if (CollectionUtils.isEmpty(childsBean.getJudgeContentList()))
                childsBean.setJudgeContentList(new ArrayList<>());
            childsBean.getJudgeContentList().addAll(getChildList(Utils.copyList(caseRecord.getJudgeContentList(), JudgeContent.class), "parent_id", id));

        }

        //保存鉴定
        if (CollectionUtils.isNotEmpty(caseRecord.getAppraisalList())) {
            if (CollectionUtils.isEmpty(childsBean.getAppraisalList())) childsBean.setAppraisalList(new ArrayList<>());
            childsBean.getAppraisalList().addAll(getChildListMaps(Utils.copyList(caseRecord.getAppraisalList(), Appraisal.class), "parent_id", id, map));
        }

        //保存过程记录
        if (CollectionUtils.isNotEmpty(caseRecord.getCaseProcessList())) {
            if (CollectionUtils.isEmpty(childsBean.getCaseProcessList()))
                childsBean.setCaseProcessList(new ArrayList<>());
            childsBean.getCaseProcessList().addAll(getChildListMaps(Utils.copyList(caseRecord.getCaseProcessList(), CaseProcess.class), "parent_id", id, map));
        }

        //保存其他资料
        if (CollectionUtils.isNotEmpty(caseRecord.getOtherDataList())) {
            if (CollectionUtils.isEmpty(childsBean.getOtherDataList())) childsBean.setOtherDataList(new ArrayList<>());
            childsBean.getOtherDataList().addAll(getChildListMaps(Utils.copyList(caseRecord.getOtherDataList(), OtherData.class), "parent_id", id, map));
        }

        //保存证据材料
        if (CollectionUtils.isNotEmpty(caseRecord.getCaseEvidenceDataList())) {
            if (CollectionUtils.isEmpty(childsBean.getCaseEvidenceDataList()))
                childsBean.setCaseEvidenceDataList(new ArrayList<>());
            childsBean.getCaseEvidenceDataList().addAll(getChildListMaps(Utils.copyList(caseRecord.getCaseEvidenceDataList(), CaseEvidenceData.class), "parent_id", id, map));
        }

    }

    /**
     * 插入子表数据
     *
     * @param childList 子表集合
     * @param col       子表关联主键字段
     * @param caseId    主表主键
     * @param <T>       子表类
     */
    private <T> List<T> getChildList(List<T> childList, String col, String caseId) {
        List<T> result = new ArrayList<>();
        if (childList != null && !childList.isEmpty()) {
            for (T t : childList) {
                try {
                    t.getClass().getMethod("setId", String.class).invoke(t, Utils.createUUID());
                    t.getClass().getMethod("set" + Utils.upperFirstLatter(Utils.lineToHump(col)), String.class).invoke(t, caseId);
                    result.add(t);
                } catch (NoSuchMethodException e) {
                    log.info("printStackTrace异常");
                } catch (IllegalAccessException e) {
                    log.info("printStackTrace异常");
                } catch (InvocationTargetException e) {
                    log.info("printStackTrace异常");
                }
            }
        }
        return result;
    }

    /**
     * 插入子表数据(获取前后id集合）
     *
     * @param childList
     * @param col
     * @param caseId
     * @param map（原ID，新ID）
     * @param <T>
     * @return
     */
    private <T> List<T> getChildListMaps(List<T> childList, String col, String caseId, Map<String, String> map) {
        List<T> result = new ArrayList<>();
        if (childList != null && !childList.isEmpty()) {
            for (T t : childList) {
                try {
                    String oldId = t.getClass().getMethod("getId").invoke(t).toString();
                    String newId = map.containsKey(oldId) ? (String) map.get(oldId) : Utils.createUUID();
                    t.getClass().getMethod("setId", String.class).invoke(t, newId);
                    map.put(oldId, newId);
                    t.getClass().getMethod("set" + Utils.upperFirstLatter(Utils.lineToHump(col)), String.class).invoke(t, caseId);
                    result.add(t);
                } catch (NoSuchMethodException e) {
                    log.info("printStackTrace异常");
                } catch (IllegalAccessException e) {
                    log.info("printStackTrace异常");
                } catch (InvocationTargetException e) {
                    log.info("printStackTrace异常");
                }
            }
        }
        return result;
    }

    /**
     * 对所有子表的新增
     *
     * @param childsBean
     */
    private void addChilds(CaseRecord childsBean) {
        if (!CollectionUtils.isEmpty(childsBean.getAgentList()))
            agentService.saveBatch(childsBean.getAgentList());
        if (!CollectionUtils.isEmpty(childsBean.getAgentContractList()))
            agentContractService.saveBatch(childsBean.getAgentContractList());
        if (!CollectionUtils.isEmpty(childsBean.getPartiesList()))
            partiesService.saveBatch(childsBean.getPartiesList());
        if (!CollectionUtils.isEmpty(childsBean.getRelations()))
            middleRelationService.saveBatch(childsBean.getRelations());
        if (!CollectionUtils.isEmpty(childsBean.getClaimList()))
            claimService.saveBatch(childsBean.getClaimList());
        if (!CollectionUtils.isEmpty(childsBean.getPreservationList()))
            preservationService.saveBatch(childsBean.getPreservationList());
        if (!CollectionUtils.isEmpty(childsBean.getJudgeContentList()))
            judgeContentService.saveBatch(childsBean.getJudgeContentList());
        if (!CollectionUtils.isEmpty(childsBean.getAppraisalList()))
            appraisalService.saveBatch(childsBean.getAppraisalList());
        if (!CollectionUtils.isEmpty(childsBean.getCaseProcessList()))
            caseProcessService.saveBatch(childsBean.getCaseProcessList());
        if (!CollectionUtils.isEmpty(childsBean.getOtherDataList()))
            otherDataService.saveBatch(childsBean.getOtherDataList());
    }


    @Override
    public boolean cancel(String id) {
//        CaseRecord cr = new CaseRecord();
//        cr.setId(id);
//        cr.setDataStateCode(DataState.CANCEL.getKey());
//        cr.setDataState(DataState.CANCEL.getValue());
//        cr.setCaseCurrentProcess(CaseKind.BQS.getValue());
//        return updateById(cr);
        CaseRecord caseRecord = getById(id);
        caseRecord.setDataStateCode(DataState.CANCEL.getKey());
        caseRecord.setDataState(DataState.CANCEL.getValue());
        caseRecord.setCaseCurrentProcess(CaseKind.BQS.getValue());
        caseRecord.setCaseCurrentProcessId(CaseKind.BQS.getKey());
        return updateById(caseRecord);
    }

    @Override
    public Page<CaseRecordHistory> queryHistory(String id) {
        QueryWrapper<CaseRecordHistory> wrapper = new QueryWrapper<>();
        wrapper.eq("history_id", id);
        wrapper.orderBy(true, false, "create_time");
        Page<CaseRecordHistory> data = caseHistoryService.page(new Page<>(), wrapper);
        return data;
    }

    @Override
    public int queryPageDataByCaseId(JSONObject json) {
        QueryWrapper<CaseRecord> wrapper = new QueryWrapper<>();
        getFilter(json, wrapper);
        CaseRecord caseBean = getById(json.getString("caseId"));
        wrapper.and(i -> i.ge("create_time", caseBean.getCreateTime()));
        int count = count(wrapper);
        int size = json.getInteger("limit");
        int page = (int) Math.ceil((float) count / size);
        return page;
    }

    public Json createReportWord(HttpServletRequest request, HttpServletResponse response, @RequestParam Map<String, Object> json) throws Exception {
//        JSONObject json = JSONObject.parseObject(body);
        String id = ComUtil.getString(json.get("id"));//id
        QueryWrapper<CaseRecord> wrapper = new QueryWrapper<CaseRecord>();
        QueryWrapper<Parties> partiesWrapper = new QueryWrapper<>();
        QueryWrapper<Agent> agentWrapper = new QueryWrapper<>();
        wrapper.eq("id", id);
        partiesWrapper.eq("master_id", id);
        agentWrapper.eq("parent_id", id);
        CaseRecord caseRecord = getOne(wrapper);
        if (caseRecord.getCaseName() == null) {
            caseRecord.setCaseName("");
        }
        if (caseRecord.getCaseNumber() == null) {
            caseRecord.setCaseNumber("");
        }
        if (caseRecord.getCaseDetails() == null) {
            caseRecord.setCaseDetails("");
        }
        if (caseRecord.getCourt() == null) {
            caseRecord.setCourt("");
        }
        if (caseRecord.getCaseType() == null) {
            caseRecord.setCaseType("");
        }
        if (caseRecord.getCauseName() == null) {
            caseRecord.setCauseName("");
        }
        List<Parties> parties = partiesService.list(partiesWrapper);
        List<Agent> agents = agentService.list(agentWrapper);
        String accuser = "";
        String accuserCode = "";
        String defendant = "";
        String defendantCode = "";
        String agent = "";
        String chargingStandard = "";
        for (Parties party : parties) {
            if (StringUtils.isNotBlank(party.getPartyType())) {
                if (party.getPartyType().equals("原告") || party.getPartyType().equals("申请人")) {
                    if (StringUtils.isNotBlank(party.getParty())) {
                        accuser = accuser + party.getParty() + "、";
                    }
                    accuserCode = party.getPartyType();
                }
                if (party.getPartyType().equals("被告") || party.getPartyType().equals("被申请人")) {
                    if (StringUtils.isNotBlank(party.getParty())) {
                        defendant = defendant + party.getParty() + "、";
                    }
                    defendantCode = party.getPartyType();
                }
            }
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        JSONObject object = new JSONObject();
        object.put("parentId", id);
        List<CaseProcess> caseProcesses = new ArrayList<>();
        List<CaseRecord> caseRecords = queryTimeAxis(object);
        if (null != caseRecords) {
            for (CaseRecord record : caseRecords) {
                CaseProcess caseProcess = new CaseProcess();
                caseProcess.setMatterTypeCode(record.getCaseProcessType());
                caseProcesses.add(caseProcess);
                String processStr = "";
                if (record.getCaseProcessType().equals("收立案")) {
                    if (record.getCaseTime() != null) {
                        processStr += sdf.format(record.getCaseTime()) + " ";
                    }
                    processStr += StringUtils.isNotBlank(record.getOurPosition()) ? ":我方成为" + record.getOurPosition() : "";
                }
                if (record.getCaseTime() != null && StringUtils.isNotBlank(record.getHandleSituation())) {
                    if (record.getCaseTime() != null) {
                        processStr += sdf.format(record.getCaseTime()) + " ";
                    }
                    processStr += StringUtils.isNotBlank(record.getHandleSituation()) ? ":" + record.getHandleSituation() : "";
                }
                caseProcess.setMatterType(processStr);
                if (record.getCaseProcessList() != null && !record.getCaseProcessTypeCode().equals("start")) {
                    for (CaseProcess process : record.getCaseProcessList()) {
                        String processesStr = "";
                        process.setMatterTypeCode("");
                        if (process.getOccurTime() != null) {
                            processesStr += sdf.format(process.getOccurTime()) + " ";
                        }
                        if (process.getMatter() != null) {
                            String matter = process.getMatter();
                            if (process.getMatterDes() != null) {
                                matter += ":" + process.getMatterDes();
                            }
                            processesStr += matter;
                        }
                        process.setMatterType(processesStr);

                    }
                    caseProcesses.addAll(record.getCaseProcessList());
                }
                if (record.getCaseEvidenceDataList() != null) {
                    for (CaseEvidenceData caseEvidenceData : record.getCaseEvidenceDataList()) {
                        if (caseEvidenceData.isWhetherTimeline()) {
                            CaseProcess cp = new CaseProcess();
                            String processesStr = "";
                            cp.setMatterTypeCode("");
                            String str = StringUtils.isNotBlank(caseEvidenceData.getProveObj()) ? caseEvidenceData.getProveObj() : "无";
                            if (caseEvidenceData.getOccurTime() != null) {
                                processesStr += sdf.format(caseEvidenceData.getOccurTime()) + " ";
                            }
                            processesStr += "证明对象:" + str + caseEvidenceData.getProveRemark();
                            cp.setMatterType(processesStr);
                            caseProcesses.add(cp);
                        }
                    }
                }
            }
        }

        for (Agent agent1 : agents) {
            if (StringUtils.isNotBlank(agent1.getLawyerName())) {
                agent = agent + agent1.getLawyerName() + "、";
            }
            if (StringUtils.isNotBlank(agent1.getChargingStandard())) {
                chargingStandard = chargingStandard + agent1.getChargingStandard() + ";";
            }
        }
        if (agent.length() > 0) {
            agent = agent.substring(0, agent.length() - 1);
        }
        if (chargingStandard.length() > 0) {
            chargingStandard = chargingStandard.substring(0, chargingStandard.length() - 1);
        }
        if (accuser.length() > 0) {
            accuser = accuser.substring(0, accuser.length() - 1);
        }
        if (defendant.length() > 0) {
            defendant = defendant.substring(0, defendant.length() - 1);
        }
        DecimalFormat df = new DecimalFormat("0.00");
        String caseMoney = df.format(caseRecord.getCaseMoney());
        String caseTime = sdf.format(caseRecord.getCaseTime());
        String caseName = caseRecord.getCaseName();
        if (caseName.length() > 0) {
            caseRecord.setCaseName(caseName.substring(0, caseName.length() - 1));
        }
        Map<String, Object> dataMap = new HashMap<String, Object>();
        dataMap.put("case", caseRecord);
        dataMap.put("accuser", accuser.equals("null") ? "" : accuser);
        dataMap.put("caseTime", caseTime);
        dataMap.put("defendantCode", defendantCode);
        dataMap.put("accuserCode", accuserCode);
        dataMap.put("caseMoney", caseMoney);
        dataMap.put("agent", agent.equals("null") ? "" : agent);
        dataMap.put("chargingStandard", chargingStandard.equals("null") ? "" : chargingStandard);
        dataMap.put("defendant", defendant.equals("null") ? "" : defendant);
        dataMap.put("list", caseProcesses);
        ByteArrayOutputStream out = null;
        OutputStreamWriter writer = null;
        try {
            String filename = caseRecord.getCaseName();
            response.setCharacterEncoding("UTF-8");
            response.setHeader("Access-Control-Expose-Headers", "Response-Type");
            response.setHeader("Response-Type", "doc");
            response.setContentType("application/octet-stream");
            // 设置Content-Disposition
            response.setHeader("Content-Disposition",
                    "attachment; filename=" + URLEncoder.encode(filename, "UTF-8"));
            response.setCharacterEncoding("UTF-8");
            out = new ByteArrayOutputStream();
            writer = new OutputStreamWriter(out, "utf-8");

            Template template = new CreateWordUtil().createWord("A类诉讼案件进程表.ftl");
            template.process(dataMap, writer);
            byte[] byts = out.toByteArray();
            response.getOutputStream().write(byts);
            //关闭流
            response.flushBuffer();
        } finally {
            if (writer != null)
                writer.close();
            if (out != null)
                out.close();
        }
        return Json.succ("createWord").data("status", "200");
    }

    @Override
    public CaseRecord queryProcessByIdAndStage(String id, String stage) {
        if (StringUtils.isBlank(stage)) {
            return queryDataById(id);
        }
        QueryWrapper<CaseRecord> wrapper = new QueryWrapper<CaseRecord>().eq("parent_id", id).eq("case_process_type_code", stage).orderBy(true, false, "create_time");
        List<CaseRecord> list = list(wrapper);
        if (list.isEmpty()) {
            return null;
        }
        String cid = list.get(0).getId();
        return queryDataById(cid);
    }

    @Override
    public List<Map<String, Object>> selectStatistics(JSONObject jsonObject, JSONArray jsonArray) {

        JSONArray belongPlate = jsonObject.containsKey("belongPlate") ? jsonObject.getJSONArray("belongPlate") : null;

        JSONArray caseKing = jsonObject.containsKey("caseKing") ? jsonObject.getJSONArray("caseKing") : null;

        //案发时间(最小值)
        Date startTime = jsonObject.containsKey("startTime") ? jsonObject.getDate("startTime") : null;
        //案发时间(stopTime)
        Date stopTime = jsonObject.containsKey("stopTime") ? jsonObject.getDate("stopTime") : null;

        QueryWrapper wrapper = new QueryWrapper();
        wrapper.apply("c.belong_Plate_Full_Id like concat( '%', b.unit_code, '%' )");

        wrapper.isNull("c.parent_id");
        if (belongPlate != null && belongPlate.size() > 0) {
            wrapper.in("b.unit_code", belongPlate);
        } else {
            wrapper.in("b.unit_code", jsonArray);
        }
        if (caseKing != null && caseKing.size() != 0) {
            wrapper.in("c.case_Kind_code", caseKing);
        }
        if (startTime != null) {
            wrapper.ge("c.case_time", startTime);
        }
        if (stopTime != null) {
            wrapper.le("c.case_time", stopTime);
        }
//        Utils.dataPermSql(wrapper, "c.belong_plate_full_id", "2", "case");
        List<Map<String, Object>> maps = caseMapper.selectStatistics(wrapper);
        return maps;
    }

    @Override
    public List<Map<String, Object>> selectStatisticsTWO(JSONObject jsonObject) {

        JSONArray caseKing = jsonObject.containsKey("caseKing") ? jsonObject.getJSONArray("caseKing") : null;
        //案发时间(最小值)
        Date startTime = jsonObject.containsKey("startTime") ? jsonObject.getDate("startTime") : null;
        //案发时间(stopTime)
        Date stopTime = jsonObject.containsKey("stopTime") ? jsonObject.getDate("stopTime") : null;

        QueryWrapper wrapper = new QueryWrapper();
        wrapper.isNull("parent_id");
        if (caseKing != null && caseKing.size() != 0) {
            wrapper.in("case_Kind_code", caseKing);
        }
        if (startTime != null) {
            wrapper.ge("case_time", startTime);
        }
        if (stopTime != null) {
            wrapper.le("case_time", stopTime);
        }
//        DataAuthUtils.dataPermSql(wrapper, "belong_plate_full_id", "2", "case");
        List<Map<String, Object>> maps = caseMapper.selectStatisticsTWO(wrapper);
        return maps;
    }

    @Resource
    private SgCaseReportTaskMapper sgCaseReportTaskMapper;

    @Override
    public Map<String, Object> queryPageDataAndTags2(JSONObject json) {
        QueryWrapper<CaseRecord> wrapper = new QueryWrapper<>();
        getFilter(json, wrapper);

        PageUtils<CaseRecord> recordPage = page(new PageUtils<>(json), wrapper);
        List<String> ids = recordPage.getRecords().stream().map(CaseRecord::getId).collect(Collectors.toList());
        Map<String, List<Tag>> maps = new HashMap<>();

        if (ids.size() > 0) {
            List<Tag> tags = tagService.list(new QueryWrapper<Tag>().in("data_id", ids));
            tags.stream().forEach(item -> {
                if (maps.containsKey(item.getDataId())) {
                    maps.get(item.getDataId()).add(item);
                } else {
                    List<Tag> temp = new ArrayList<>();
                    temp.add(item);
                    maps.put(item.getDataId(), temp);
                }
            });
        }

        recordPage.getRecords().stream().forEach(item -> {
            if (maps.containsKey(item.getId())) {
                item.setTagList(maps.get(item.getId()));
            } else {
                item.setTagList(new ArrayList<>());
            }
        });
        //标的额合计
        QueryWrapper<CaseRecord> wrapper2 = new QueryWrapper<CaseRecord>();
        getFilter(json, wrapper2);
        wrapper2.select("sum(case_money) as moneytotal");
        Map<String, Object> result = new HashMap<>();
        Map<String, Object> result2 = getMap(wrapper2);
        if (result2 != null) {
            result.put("MONEYTOTAL", result2.get("MONEYTOTAL"));
        }
        result.put("recordPage", recordPage);
        return result;
    }

    @Override
    public Map<String, Object> queryPageDataAndTags3(JSONObject json) {
        QueryWrapper<CaseRecord> wrapper = new QueryWrapper<CaseRecord>();
        getFilter2(json, wrapper);

        PageUtils<CaseRecord> recordPage = new PageUtils<>(json);
        recordPage.setRecords(sgCaseReportTaskMapper.queryCaseList2(recordPage, wrapper));
        List<String> ids = recordPage.getRecords().stream().map(CaseRecord::getId).collect(Collectors.toList());
        Map<String, List<Tag>> maps = new HashMap<>();

        if (ids.size() > 0) {
            List<Tag> tags = tagService.list(new QueryWrapper<Tag>().in("data_id", ids));
            tags.stream().forEach(item -> {
                if (maps.containsKey(item.getDataId())) {
                    maps.get(item.getDataId()).add(item);
                } else {
                    List<Tag> temp = new ArrayList<>();
                    temp.add(item);
                    maps.put(item.getDataId(), temp);
                }
            });
        }

        recordPage.getRecords().stream().forEach(item -> {
            if (maps.containsKey(item.getId())) {
                item.setTagList(maps.get(item.getId()));
            } else {
                item.setTagList(new ArrayList<>());
            }
        });
        //标的额合计
        QueryWrapper<CaseRecord> wrapper2 = new QueryWrapper<CaseRecord>();
        getFilter(json, wrapper2);
        wrapper2.select("sum(case_money) as moneytotal");
        Map<String, Object> result = new HashMap<>();
        Map<String, Object> result2 = getMap(wrapper2);
        if (result2 != null) {
            result.put("MONEYTOTAL", result2.get("MONEYTOTAL"));
        }
        result.put("recordPage", recordPage);
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Json deleteDataById(String id) {

        CaseRecord caseBean = getById(id);
        if (StringUtils.isNotBlank(caseBean.getParentId())) {
            CaseRecord caseMain = getById(caseBean.getParentId());

            if (caseBean.getWhetherInclude()) {
                caseMain.setCaseProcess(caseMain.getCaseProcess().substring(0, caseMain.getCaseProcess().length() - caseBean.getCaseProcessType().length() - 1));
                caseMain.setCaseProcessIds(caseMain.getCaseProcessIds().substring(0, caseMain.getCaseProcessIds().length() - caseBean.getId().length() - 1));
                if (!caseMain.getWhetherEasy()) {
                    caseMain.setCaseCurrentProcess(caseMain.getCaseProcess().substring(caseMain.getCaseProcess().lastIndexOf(",") + 1) + "完成");
                }
                caseMain.setCaseCurrentProcessId(caseMain.getCaseProcessIds().substring(caseMain.getCaseProcessIds().lastIndexOf(",") + 1));
            }
            //判断是否有子
            int sum = count(new QueryWrapper<CaseRecord>().eq("parent_id", caseMain.getId()).ne("id", caseBean.getId()));
            caseMain.setHasChildren(sum >= 0);
            updateById(caseMain);
        }

        boolean flag = removeById(id);
        if (!flag) {
            return Json.fail().msg("未找到需要删除的数据");
        }

        //首环节的时候删除
        if (StringUtils.isBlank(caseBean.getParentId())) {
            agentService.remove(new QueryWrapper<Agent>().eq("parent_id", caseBean.getId()));
            agentContractService.deleteData(caseBean.getId());
            middleRelationService.remove(new QueryWrapper<MiddleRelation>().eq("relation_id", id).or().eq("associated_id", id));
            caseEasyModelService.deleteData(caseBean.getId());
            preservationService.remove(new QueryWrapper<Preservation>().eq("parent_id", caseBean.getId()));
        }

        List<String> childs = getHasChilds(caseBean.getCaseProcessTypeCode());

        if (childs.isEmpty()) {
            return Json.succ().msg("删除成功!");
        }

        if (childs.contains("parties")) partiesService.remove(new QueryWrapper<Parties>().eq("master_id", id));
        if (childs.contains("claim") || childs.contains("changeClaim") || childs.contains("oppositeClaim"))
            claimService.remove(new QueryWrapper<Claim>().eq("parent_id", id));
        if (childs.contains("caseProcess"))
            caseProcessService.remove(new QueryWrapper<CaseProcess>().eq("parent_id", id));
        if (childs.contains("fildoc")) fildocService.remove(new QueryWrapper<Fildoc>().eq("parent_id", id));
        if (childs.contains("indictment"))
            indictmentService.remove(new QueryWrapper<Indictment>().eq("parent_id", id));
        if (childs.contains("evidence")) evidenceService.remove(new QueryWrapper<Evidence>().eq("parent_id", id));
        if (childs.contains("appraisal")) appraisalService.remove(new QueryWrapper<Appraisal>().eq("parent_id", id));
        if (childs.contains("judgeFiles"))
            judgeFilesService.remove(new QueryWrapper<JudgeFiles>().eq("parent_id", id));
        if (childs.contains("judgeContent"))
            judgeContentService.remove(new QueryWrapper<JudgeContent>().eq("parent_id", id));
        if (childs.contains("otherData")) otherDataService.remove(new QueryWrapper<OtherData>().eq("parent_id", id));
        if (childs.contains("caseEvidenceData"))
            caseEvidenceDataService.remove(new QueryWrapper<CaseEvidenceData>().eq("parent_id", id));

        return Json.succ().msg("删除成功!");
    }

    @Override
    public void fileDownUtil(List<CaseRecord> caseRecords, OutputStream outputStream) {

        //循环收立案、 一审 、二审 、其他进程
        List<SgSysDoc> sysDocs = new ArrayList<>();
        for (CaseRecord caseRecord : caseRecords) {
            if (!caseRecord.getCaseProcessType().equals("结案")) {
                //循环每个进程的资料存档
                for (OtherData otherData : caseRecord.getOtherDataList()) {
                    if (StringUtils.isNotBlank(otherData.getFiles())) {
                        //取出每个资料存档的文件信息
                        List<JSONObject> jsonObjects = JSON.parseArray(otherData.getFiles(), JSONObject.class);
                        //循环文件信息放到files
                        for (JSONObject jsonObject : jsonObjects) {
                            String docId = jsonObject.getString("docId");
                            try {
                                SgSysDoc sysDoc = DocUtils.downloadFile(docId);
                                sysDocs.add(sysDoc);
                            } catch (Exception e) {
                                System.out.println("文件下载异常");
                            }
                        }
                    }
                }
            }
        }
        try {
            ZipUtil.toZip(sysDocs, outputStream);
        } catch (Exception e) {
            System.out.println("转换失败");
        }
    }

    @Override
    public Page<Preservation> queryPreservation(JSONObject json) {
        QueryWrapper<Preservation> wrapper = new QueryWrapper<>();
        wrapper.eq("1", 1);
        PageUtils page = new PageUtils(json);
        String fuzzyValue = json.containsKey("fuzzyValue") ? json.getString("fuzzyValue") : null;
        String preservationCourt = json.containsKey("preservationCourt") ? json.getString("preservationCourt") : null;
        String preservationPhase = json.containsKey("preservationPhase") ? json.getString("preservationPhase") : null;
        String preservationNumber = json.containsKey("preservationNumber") ? json.getString("preservationNumber") : null;
        String caseName = json.containsKey("caseName") ? json.getString("caseName") : null;
        if (StringUtils.isNotBlank(preservationCourt)) wrapper.like("p.preservation_court", preservationCourt);
        if (StringUtils.isNotBlank(preservationPhase)) wrapper.like("p.preservation_phase", preservationPhase);
        if (StringUtils.isNotBlank(preservationNumber)) wrapper.like("p.preservation_number", preservationNumber);
        if (StringUtils.isNotBlank(caseName)) wrapper.like("r.case_name", preservationCourt);
        if (StringUtils.isNotBlank(fuzzyValue))
            wrapper.like("r.case_name", fuzzyValue).or().like("p.preservation_phase", fuzzyValue).or().like("p.preservation_court", fuzzyValue).or().like("p.preservation_number", fuzzyValue);
//        wrapper = (Wrapper) SqlHelper.fillWrapper(page, wrapper);
        List<Preservation> list = caseMapper.queryPreservation(page, wrapper);
        list.stream().forEach((Preservation item) -> {
            item.setId(UUID.randomUUID().toString().replace("-", ""));
            item.setHasChildren(true);
        });
        return page.setRecords(list);
    }

    @Override
    public boolean updateRemind(CaseRecord caseRecord, String createOrgId, String preCaseProcessType) {
        //1.获取案件关键信息，查出消息接收人信息
        String dataId = caseRecord.getId();
        Boolean whetherMajor = caseRecord.getWhetherMajor();
        if (StringUtils.isNotBlank(caseRecord.getParentId())) {
            dataId = caseRecord.getParentId();
            CaseRecord record = getById(caseRecord.getParentId());
            whetherMajor = record.getWhetherMajor();
        }
        String url = "case_ledger_view";
        List<String> receiveOrgIds = new ArrayList<>();//消息接收人（经办人、办案小组成员、重大案件推送集团法务部）
        receiveOrgIds.add(createOrgId);

        if (whetherMajor) {
            List<SgHrOrgUnitB> orgUnitBS = sgHrOrgUnitBMapper.queryOrgByRole("case_remind_admin");
            if (CollectionUtils.isNotEmpty(orgUnitBS)) {
                for (SgHrOrgUnitB orgUnitB : orgUnitBS) {
                    receiveOrgIds.add(String.valueOf(orgUnitB.getUnitId()));
                }
            }
        }

        JSONObject json = new JSONObject();
        json.put("dataState", "view");
        json.put("dataId", dataId);
        json.put("functionId", "case_ledger_view," + dataId);
        json.put("isNotice", true);

        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");

        //2.创建系统内部的消息待办
        boolean notices = sgSysNoticeService.createNotices(
                caseRecord.getCaseName() + "-" + preCaseProcessType + "-" + simpleDateFormat.format(caseRecord.getCreateTime()),
                "案件进程", NoticeType.AJGX_NOTICE, dataId, url, json.toJSONString(), Long.parseLong(createOrgId),
                receiveOrgIds, false);
        return notices;
    }

    @Override
    public boolean restartById(String id) {
        //查出结案子数据
        QueryWrapper<CaseRecord> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("case_process_type_code", "end");
        queryWrapper.eq("parent_id", id);
        List<CaseRecord> list = list(queryWrapper);
        if (CollectionUtils.isNotEmpty(list)) {
            CaseRecord childCase = list.get(0);
            UpdateWrapper<CaseRecord> updateWrapper = new UpdateWrapper<>();
            updateWrapper.set("CASE_CURRENT_PROCESS", "重启中");
            updateWrapper.set("case_current_process_id", childCase.getId());
            updateWrapper.set("whether_end", false);
            updateWrapper.eq("id", id);
            boolean update = update(updateWrapper);
            if (update) {
                UpdateWrapper<CaseRecord> updateWrapper2 = new UpdateWrapper<>();
                updateWrapper2.set("data_state", "已保存");
                updateWrapper2.set("data_state_code", 1);
                updateWrapper2.set("case_time", null);
                updateWrapper2.set("Handle_situation", null);
                updateWrapper2.set("Handle_situation_id", null);
                updateWrapper2.eq("id", childCase.getId());
                return update(updateWrapper2);
            }
        }
        return false;
    }

    @Override
    public void exportGreatCase(String id, HttpServletResponse response) throws IOException {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        CaseRecord caseRecord = getById(id);
        List<CaseReport> reportList = sgCaseReportService.list(new QueryWrapper<CaseReport>().eq("parent_id", id).orderByAsc("create_time"));
        ClassPathResource resource = new ClassPathResource("template/case/07caseReportGreat.xlsx");
        InputStream inputStream = null;
        ByteArrayOutputStream out = null;
        try {
            inputStream = resource.getInputStream();
            XSSFWorkbook workbook = new XSSFWorkbook(inputStream);
            XSSFSheet sheet = workbook.getSheetAt(0);

            XSSFCellStyle style = createCellStyle(workbook);
            XSSFCellStyle style2 = createCellStyle(workbook);
            short format = workbook.createDataFormat().getFormat("yyyy-MM-dd");
            style2.setDataFormat(format);
            XSSFCellStyle style3 = createCellStyle(workbook);
            short format3 = workbook.createDataFormat().getFormat("0.00");
            style3.setDataFormat(format3);
            XSSFCellStyle style4 = createCellStyle4(workbook);
            XSSFCellStyle style5 = createCellStyle2(workbook);
            XSSFCellStyle style5_1 = createCellStyle2_1(workbook);
            style5_1.setDataFormat(format);
            XSSFCellStyle style6 = createCellStyle3(workbook);
            for (int i = 2; i < 13; i++) {
                XSSFRow row = sheet.getRow(i);
                String s = String.valueOf(i);
                XSSFCell cell_1 = null;
                XSSFCell cell_2 = null;
                switch (s) {
                    case "2"://2，填报单位2-3-4，填报日期6
                        cell_1 = row.getCell(2);
                        cell_1.setCellStyle(style5);
                        cell_1.setCellValue(caseRecord.getCreateOgnName());

                        cell_2 = row.getCell(6);
                        cell_2.setCellStyle(style5_1);
                        cell_2.setCellValue(getDateByValue(sdf, caseRecord.getCreateTime()));
                        break;
                    case "3"://3，案件名称2-底
                        if (StringUtils.isNotBlank(caseRecord.getCaseName())) {
                            cell_1 = row.getCell(2);
                            cell_1.setCellStyle(style);
                            cell_1.setCellValue(caseRecord.getCaseName());
                        }
                        break;
                    case "4"://4、风险类别2-底
                        if (StringUtils.isNotBlank(caseRecord.getRiskLevel())) {
                            cell_1 = row.getCell(2);
                            cell_1.setCellStyle(style);
                            cell_1.setCellValue(caseRecord.getRiskLevel());
                        }
                        break;
                    case "5"://5、发案单位2，职务4-5-6
                        if (StringUtils.isNotBlank(caseRecord.getOgnPackage())) {
                            cell_1 = row.getCell(2);
                            cell_1.setCellStyle(style);
                            cell_1.setCellValue(caseRecord.getOgnPackage());
                        }
                        break;
                    case "6"://6、集团2，职务4-5-6
                        if (StringUtils.isNotBlank(caseRecord.getGroupPackage())) {
                            cell_1 = row.getCell(2);
                            cell_1.setCellStyle(style);
                            cell_1.setCellValue(caseRecord.getGroupPackage());
                        }
                        break;
                    case "7"://7、案发时间2，涉案金额4-5-6
                        if (caseRecord.getCaseTime() != null) {
                            cell_1 = row.getCell(2);
                            cell_1.setCellStyle(style2);
                            cell_1.setCellValue(getDateByValue(sdf, caseRecord.getCaseTime()));
                        }

                        if (caseRecord.getCaseMoney() != null) {
                            cell_2 = row.getCell(4);
                            cell_2.setCellStyle(style3);
                            cell_2.setCellValue(caseRecord.getCaseMoney().doubleValue());
                        }
                        break;
                    case "8": //8、案件概要2-底
                        if (StringUtils.isNotBlank(caseRecord.getCaseDetails())) {
                            cell_1 = row.getCell(2);
                            cell_1.setCellStyle(style);
                            cell_1.setCellValue(caseRecord.getCaseDetails());
                        }
                        break;
                    case "9"://9、案发原因分析2-底
                        if (StringUtils.isNotBlank(caseRecord.getGreatReason())) {
                            cell_1 = row.getCell(2);
                            cell_1.setCellStyle(style);
                            cell_1.setCellValue(caseRecord.getGreatReason());
                        }
                        break;
                    case "10"://10、下一步工作措施2-底
                        if (StringUtils.isNotBlank(caseRecord.getGreatPlan())) {
                            cell_1 = row.getCell(2);
                            cell_1.setCellStyle(style);
                            cell_1.setCellValue(caseRecord.getGreatPlan());
                        }
                        break;
                    case "12"://12、解决时限2-3，任务分解4-5-6
                        if (StringUtils.isNotBlank(caseRecord.getTimeLimit())) {
                            cell_1 = row.getCell(2);
                            cell_1.setCellStyle(style);
                            cell_1.setCellValue(caseRecord.getTimeLimit());
                        }
                        if (StringUtils.isNotBlank(caseRecord.getTaskDecomposition())) {
                            cell_1 = row.getCell(4);
                            cell_1.setCellStyle(style);
                            cell_1.setCellValue(caseRecord.getTaskDecomposition());
                        }
                        break;
                    default:
                        log.info("没有符合的数据");
                }
            }

            int n = 0;
            if (CollectionUtils.isNotEmpty(reportList)) {
                n = reportList.size();
                if (reportList.size() > 1) {
                    sheet.shiftRows(14, sheet.getLastRowNum(), reportList.size() - 1);
                    sheet.addMergedRegion(new CellRangeAddress(13, 12 + reportList.size(), 0, 0));
                    for (int k = 14; k < 13 + reportList.size(); k++) {
                        sheet.addMergedRegion(new CellRangeAddress(k, k, 2, 6));
                    }
                }
                for (int i = 0; i < reportList.size(); i++) {
                    int j = i + 13;
                    XSSFRow row = null;
                    row = sheet.getRow(j);
                    if (row == null) {
                        row = sheet.createRow(j);
                    }
                    XSSFCell cell_1 = row.getCell(1) != null ? row.getCell(1) : row.createCell(1);
                    XSSFCell cell_2 = row.getCell(2) != null ? row.getCell(2) : row.createCell(2);
                    XSSFCell cell_3 = row.getCell(3) != null ? row.getCell(3) : row.createCell(3);
                    XSSFCell cell_4 = row.getCell(4) != null ? row.getCell(4) : row.createCell(4);
                    XSSFCell cell_5 = row.getCell(5) != null ? row.getCell(5) : row.createCell(5);
                    XSSFCell cell_6 = row.getCell(6) != null ? row.getCell(6) : row.createCell(6);
                    cell_3.setCellStyle(style);
                    cell_4.setCellStyle(style);
                    cell_5.setCellStyle(style);
                    cell_6.setCellStyle(style);

                    CaseReport caseReport = reportList.get(i);
                    cell_1.setCellStyle(style4);
                    String aa = StringUtils.isNotBlank(caseReport.getReportQuarter()) ? caseReport.getReportQuarter() : "";
                    String bb = StringUtils.isNotBlank(caseReport.getReportYear()) ? caseReport.getReportYear() : "";
                    cell_1.setCellValue(aa + bb);
                    if (StringUtils.isNotBlank(caseReport.getUpdateDescription())) {
                        cell_2.setCellStyle(style);
                        cell_2.setCellValue(caseReport.getUpdateDescription());
                    }
                }
            }
            //16、填报人-2，联系电话-6
            XSSFRow row = sheet.getRow((n == 0 ? 1 : n) + 12 + 3);
            XSSFCell cell_1 = row.getCell(2);
            ;
            cell_1.setCellStyle(style6);
            cell_1.setCellValue(caseRecord.getCreatePsnName());

            String fileName = "下载一案一表.xlsx";
            response.setHeader("content-Type", "application/vnd.ms-excel");
            response.setCharacterEncoding("UTF-8");
            response.setHeader("Content-Disposition",
                    "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
            response.addHeader("Access-Control-Expose-Headers", "*");
            out = new ByteArrayOutputStream();
            OutputStream os = response.getOutputStream();
            workbook.write(os);
            os.flush();
            os.close();
        } finally {
            if (out != null) {
                out.close();
            }
            if (inputStream != null) {
                inputStream.close();
            }
        }
    }

    @Override
    public List<CaseRecord> queryCaseByIndex(OrgContextVo vo) {

        int year = LocalDate.now().getYear();
        QueryWrapper<CaseRecord> wrapper = new QueryWrapper<CaseRecord>();
        wrapper.isNull("parent_id").like("to_char(create_time,'yyyy-MM-dd')", year + "%");
        wrapper.eq("create_psn_id", vo.getCurrentPsnId());
        List<CaseRecord> list = list(wrapper);
        return list;
    }

    private XSSFCellStyle createCellStyle(XSSFWorkbook workbook) {
        XSSFFont font = workbook.createFont();
        font.setFontName("楷体_GB2312");
        font.setFontHeightInPoints((short) 10);
        XSSFCellStyle borderStyle = workbook.createCellStyle();
        borderStyle.setFont(font);
        borderStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        borderStyle.setAlignment(HorizontalAlignment.LEFT);
        borderStyle.setWrapText(true);
        borderStyle.setBorderBottom(BorderStyle.THIN);
        borderStyle.setBorderLeft(BorderStyle.THIN);
        borderStyle.setBorderTop(BorderStyle.THIN);
        borderStyle.setBorderRight(BorderStyle.THIN);
        return borderStyle;
    }

    private XSSFCellStyle createCellStyle2(XSSFWorkbook workbook) {
        XSSFFont font = workbook.createFont();
        font.setFontName("楷体_GB2312");
        font.setFontHeightInPoints((short) 10);
        XSSFCellStyle borderStyle = workbook.createCellStyle();
        borderStyle.setFont(font);
        borderStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        borderStyle.setAlignment(HorizontalAlignment.LEFT);
        borderStyle.setWrapText(true);
        borderStyle.setBorderBottom(BorderStyle.THIN);
        return borderStyle;
    }

    private XSSFCellStyle createCellStyle2_1(XSSFWorkbook workbook) {
        XSSFFont font = workbook.createFont();
        font.setFontName("楷体_GB2312");
        font.setFontHeightInPoints((short) 10);
        XSSFCellStyle borderStyle = workbook.createCellStyle();
        borderStyle.setFont(font);
        borderStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        borderStyle.setAlignment(HorizontalAlignment.LEFT);
        borderStyle.setWrapText(true);
        borderStyle.setBorderBottom(BorderStyle.THIN);
        return borderStyle;
    }

    private XSSFCellStyle createCellStyle3(XSSFWorkbook workbook) {
        XSSFFont font = workbook.createFont();
        font.setFontName("楷体_GB2312");
        font.setFontHeightInPoints((short) 10);
        XSSFCellStyle borderStyle = workbook.createCellStyle();
        borderStyle.setFont(font);
        borderStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        borderStyle.setAlignment(HorizontalAlignment.LEFT);
        borderStyle.setWrapText(true);
        return borderStyle;
    }

    private XSSFCellStyle createCellStyle4(XSSFWorkbook workbook) {
        XSSFFont font = workbook.createFont();
        font.setFontName("楷体_GB2312");
        font.setFontHeightInPoints((short) 11);
        XSSFCellStyle borderStyle = workbook.createCellStyle();
        borderStyle.setFont(font);
        borderStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        borderStyle.setAlignment(HorizontalAlignment.CENTER);
        borderStyle.setBorderBottom(BorderStyle.THIN);
        borderStyle.setBorderLeft(BorderStyle.THIN);
        borderStyle.setBorderTop(BorderStyle.THIN);
        borderStyle.setBorderRight(BorderStyle.THIN);
        borderStyle.setWrapText(true);

        font.setBold(true);
        borderStyle.setFont(font);
        return borderStyle;
    }

    private Date getDateByValue(SimpleDateFormat sdf, Date dd) {
        String format = sdf.format(dd);
        Date date = null;
        try {
            date = sdf.parse(format);
        } catch (ParseException e) {
            log.info("printStackTrace异常");
        }
        return date;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteProcess(String id) {
        QueryWrapper<CaseRecord> wrapper = new QueryWrapper<>();
        wrapper.eq("id", id).or().eq("parent_id", id).eq("data_state_code", DataState.SUBMIT.getKey()).orderBy(true, false, "sort");
        List<CaseRecord> caseRecords = list(wrapper);
        CaseRecord caseMain = getById(id);
        boolean result = false;
        if (caseRecords.size() > 0) {
            CaseRecord caseRecord = caseRecords.get(0);
            String caseProcess = caseMain.getCaseProcess();
            if (StringUtils.isNotBlank(caseProcess) && "收立案".equals(caseProcess)) {
                result = removeById(caseRecord.getId());

                List<String> childs = getHasChilds(caseRecord.getCaseProcessTypeCode());

                if (childs.isEmpty()) {
                    result = true;
                }

                if (childs.contains("parties")) partiesService.remove(new QueryWrapper<Parties>().eq("master_id", id));
                if (childs.contains("claim") || childs.contains("changeClaim") || childs.contains("oppositeClaim"))
                    claimService.remove(new QueryWrapper<Claim>().eq("parent_id", id));
                if (childs.contains("caseProcess"))
                    caseProcessService.remove(new QueryWrapper<CaseProcess>().eq("parent_id", id));
                if (childs.contains("fildoc")) fildocService.remove(new QueryWrapper<Fildoc>().eq("parent_id", id));
                if (childs.contains("indictment"))
                    indictmentService.remove(new QueryWrapper<Indictment>().eq("parent_id", id));
                if (childs.contains("evidence"))
                    evidenceService.remove(new QueryWrapper<Evidence>().eq("parent_id", id));
                if (childs.contains("appraisal"))
                    appraisalService.remove(new QueryWrapper<Appraisal>().eq("parent_id", id));
                if (childs.contains("judgeFiles"))
                    judgeFilesService.remove(new QueryWrapper<JudgeFiles>().eq("parent_id", id));
                if (childs.contains("judgeContent"))
                    judgeContentService.remove(new QueryWrapper<JudgeContent>().eq("parent_id", id));
                if (childs.contains("otherData"))
                    otherDataService.remove(new QueryWrapper<OtherData>().eq("parent_id", id));
                if (childs.contains("caseEvidenceData"))
                    caseEvidenceDataService.remove(new QueryWrapper<CaseEvidenceData>().eq("parent_id", id));
            } else {
                caseMain.setCaseProcess(caseMain.getCaseProcess().substring(0, caseMain.getCaseProcess().length() - caseRecord.getCaseProcessType().length() - 1));
                caseMain.setCaseProcessIds(caseMain.getCaseProcessIds().substring(0, caseMain.getCaseProcessIds().length() - caseRecord.getId().length() - 1));
                if (!caseMain.getWhetherEasy()) {
                    caseMain.setCaseCurrentProcess(caseMain.getCaseProcess().substring(caseMain.getCaseProcess().lastIndexOf(",") + 1) + "完成");
                }
                caseMain.setCaseCurrentProcessId(caseMain.getCaseProcessIds().substring(caseMain.getCaseProcessIds().lastIndexOf(",") + 1));
                result = removeById(caseRecord.getId());
                updateById(caseMain);
            }
        }
        return result;
    }

    @Override
    public List<Map<String, Object>> queryAnnualCaseReport(JSONObject json) {
        String year = json.getString("year");

//        List<Map<String, Object>> list = this.initQueryCaseReport();

        // 新发案件统计
        List<Map<String, Object>> newCaseList = caseMapper.queryNewCase("%" + year + "%");
        // 新发重大案件统计
        List<Map<String, Object>> newMajorCaseList = caseMapper.queryNewMajorCase("%" + year + "%");

        //年度上报统计
        List<Map<String, Object>> caseReportYear = caseMapper.queryYearCaseReport(year);
        //年度新发案件统计
        List<Map<String, Object>> newCaseReportYear = caseMapper.queryYearNewCase(year);
        //年度更新结案统计
        List<Map<String, Object>> endCaseYear = caseMapper.queryYearEndCase(year);
        //年度重大案件统计
        List<Map<String, Object>> majorCaseReportYear = caseMapper.queryYearMajorCaseReport(year);

//        mergeList(list, newCaseList);
//        mergeList(list, newMajorCaseList);
//        mergeList(list, caseReportYear);
//        mergeList(list, newCaseReportYear);
//        mergeList(list, endCaseYear);
//        mergeList(list, majorCaseReportYear);

        return null;
    }

    @Override
    public List<Map<String, Object>> queryQuarterCaseReport(JSONObject json) {
        String year = json.getString("year");
        String quarter = json.getString("quarter");
//        List<Map<String, Object>> list = this.initQueryCaseReport();

        //季度上报统计
        List<Map<String, Object>> caseReportQuarter = caseMapper.queryCaseReportQuarter(year, quarter);
        //季度新发案件统计
        List<Map<String, Object>> newCaseReportQuarter = caseMapper.queryNewCaseQuarter(year, quarter);
        //季度更新结案统计
        List<Map<String, Object>> endCaseQuarter = caseMapper.queryEndCaseQuarter(year, quarter);
        //季度重大案件统计
        List<Map<String, Object>> majorCaseReportQuarter = caseMapper.queryMajorCaseReportQuarter(year, quarter);

//        mergeList(list, caseReportQuarter);
//        mergeList(list, newCaseReportQuarter);
//        mergeList(list, endCaseQuarter);
//        mergeList(list, majorCaseReportQuarter);

        return null;
    }



    /**
     * 需要统计数据的单位
     *
     * @return
     */
    private List<Map<String, Object>> initQueryCaseReport() {
        String[] ognName = {
        };

        String[] ognType = {
        };

        List<Map<String, Object>> list = new ArrayList<>();

        for (int i = 0; i < ognName.length; i++) {
            HashMap<String, Object> map = new HashMap<>();
            map.put("ognName", ognName[i]);
            map.put("ognType", ognType[i]);
            list.add(map);
        }

        return list;
    }

    /**
     * 拼接List<map> 用来拼接sql结果
     */
    private void mergeList(List<Map<String, Object>> list1, List<Map<String, Object>> list2) {
        for (Map<String, Object> map : list1) {
            Object key = map.get("ognName");

            for (Map<String, Object> m : list2) {
                boolean exist = m.containsValue(key);

                if (exist) {
                    m.forEach((k, v) -> {
                        if (!v.equals(key)) {
                            map.put(k, v);
                        }
                    });
                }
            }
        }

    }
}