
91e6e5ecb420892c99de8dc3b2efbc41d849877a	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.27.1754018536329.js\",\"contentHash\":\"5e88417093905ce52117067ec60bead8\"}","integrity":"sha512-w3mBLqbXrBnljLv9UO5OFKFm8HpJFkLtJMIR62oAWwCBfju5uXekw8sOBigGwYAVdO6RiYJTSy+dg3G0/qDbOA==","time":1754018575958,"size":79114}