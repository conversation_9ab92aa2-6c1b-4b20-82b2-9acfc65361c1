
2c93a3e4ebdd6470fba1e816020e10cc389c0e69	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.68.1754018536329.js\",\"contentHash\":\"c5eb078cbe1bfc8a488767c69b1dc19a\"}","integrity":"sha512-ALNlwCW6JLGaVqllAgSKBjMBsGLsz4N+NwZf4y9bMXbJw/p4wHjpwgBFMxqFS4e1MXRTafCLj1Kn0x5RFwzddw==","time":1754018575956,"size":56001}