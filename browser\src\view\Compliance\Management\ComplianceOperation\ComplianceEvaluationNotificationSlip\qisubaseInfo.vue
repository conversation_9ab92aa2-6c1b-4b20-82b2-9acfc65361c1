<template xmlns:v-slot="http://www.w3.org/1999/XSL/Transform">

  <div style="margin-top: 10px" v-if="view === 'old'">
    <!--基础信息表单块-->
    <div v-if="dataState !== 'view'">
      <div style="padding-left: 10px;margin-bottom: 20px;font-size: 15px;color: red;font-weight: bolder">

      </div>
      <div style="margin: 10px">
        <span style="font-weight: bold;font-size: 16px;color: #5A5A5F;">基本信息</span>
        <el-divider></el-divider>
      </div>
      <el-row style="margin-top: 10px">
        <el-col :span="8">
          <el-form-item label="编码">
            <el-input v-if="!isView" v-model.trim="mainData.code" disabled show-word-limit style="width: 100%" />
            <span v-else class="viewSpan">{{ mainData.code }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="报告年度" prop="evaluationYear">
            <el-input v-if="!isView" v-model.trim="mainData.evaluationYear" :disabled="true" show-word-limit
              style="width: 100%" placeholder="年份不可修改" />
            <span v-else class="viewSpan">{{ mainData.evaluationYear }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="评价对象类型" prop="evaluationObjectType">
            <el-input v-if="!isView" v-model.trim="mainData.evaluationObjectType" :disabled="true" show-word-limit
              style="width: 100%" placeholder="评价对象类型不可修改" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="'计划开始\n检查时间'" class="fold_label" prop="plannedStartTime">
            <el-date-picker v-model="mainData.plannedStartTime" value-format="yyyy-MM-dd" type="date"
              style="width: 100%" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item :label="'计划结束\n检查时间'" class="fold_label" prop="plannedEndTime">
            <el-date-picker v-model="mainData.plannedEndTime" value-format="yyyy-MM-dd" type="date"
              style="width: 100%" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="评价对象" prop="evaluationObject">
            <el-input v-if="!isView" v-model="mainData.evaluationObject" placeholder="请选择" class="input-with-select"
              disabled>
              <el-button slot="append" icon="el-icon-search" @click="showOrgTreeDialog" />
            </el-input>
            <span v-else class="viewSpan">{{ mainData.evaluationObject }}</span>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="评价内容" prop="evaluationConclusion">
            <span slot="label">评价内容</span>
            <el-input v-if="!isView" v-model="mainData.evaluationConclusion" :autosize="{ minRows: 5, maxRows: 20 }"
              type="textarea" placeholder="请输入评价内容" maxlength="2000" show-word-limit />
            <text-span v-else class="viewSpan" :text="mainData.evaluationConclusion" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="相关事项" prop="relatedMatters">
            <span slot="label">相关事项</span>
            <el-input v-if="!isView" v-model="mainData.relatedMatters" :autosize="{ minRows: 5, maxRows: 20 }"
              type="textarea" placeholder="请输入相关事项" maxlength="2000" show-word-limit />
            <text-span v-else class="viewSpan" :text="mainData.relatedMatters" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="附件材料">
              <UploadDoc :files.sync="mainData.uploadedAttachment" doc-path="/case" :disabled="isView" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-row>

      <!-- el-dialog 组件 -->
      <el-dialog :close-on-click-modal="false" title="选择单位/部门" :visible.sync="dialogVisible" width="50%">
        <div class="el-dialog-div">
          <orgTree :accordion="false" :is-checked-user="false" :show-user="showUserValue" :is-check="false"
            :checked-data.sync="zxcheckedData" :is-not-cascade="true" :is-filter="true" />
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button icon="" class="negative-btn" @click="cancel">取消</el-button>
          <el-button type="primary" icon="" class="active-btn" @click="choiceDeptSure">确定</el-button>
        </span>
      </el-dialog>
    </div>
    <!-- 查看 -->
    <div v-else>
      <SimpleBoardTitle title="基本信息">
        <table class="table_content">
          <tbody>
            <tr>
              <th colspan="3" class="th_label">编码</th>
              <td colspan="9" class="td_value">{{ mainData.code }}</td>
              <th colspan="3" class="th_label">评价年度</th>
              <td colspan="9" class="td_value">{{ mainData.evaluationYear }}</td>
              <!-- <th colspan="3" class="th_label">评价对象类型</th>
              <td colspan="9" class="td_value">{{ mainData.evaluationObjectType }}</td> -->
            </tr>
            <tr>
              <th colspan="3" class="th_label">计划开始时间</th>
              <td colspan="9" class="td_value">{{ formattedPlannedStartTime }}</td>
              <th colspan="3" class="th_label">计划结束时间</th>
              <td colspan="10" class="td_value">{{ formattedPlannedEndTime }}</td>
            </tr>
            <tr>
              <th colspan="3" class="th_label">评价对象</th>
              <td colspan="21" class="td_value">{{ mainData.evaluationObject }}</td>

            </tr>
            <tr>
              <th colspan="3" class="th_label">评价结果</th>
              <td colspan="21" class="td_value">{{ mainData.evaluationConclusion }}</td>
            </tr>
            <tr>
              <th colspan="3" class="th_label">相关事项</th>
              <td colspan="21" class="td_value">{{ mainData.relatedMatters }}</td>
            </tr>
            <tr>
              <th colspan="3" class="th_label">附件材料</th>
              <td colspan="21" class="td_value">
                <div v-if="mainData.uploadedAttachment">
                  <UploadDoc :files.sync="mainData.uploadedAttachment" doc-path="/case" :disabled="true" />
                </div>
                <div v-else style="font-size: 15px">无</div>
              </td>
            </tr>
          </tbody>

        </table>

      </SimpleBoardTitle>
    </div>

  </div>

  <!-- <div v-else>
    <SimpleBoardTitleApproval title="基本信息">
      <table class="table_content">
        <tbody>
          <tr>
            <th colspan="3" class="th_label_approval">风险名称</th>
            <td colspan="9" class="td_value_approval">{{ mainData.caseName }}</td>
            <th colspan="3" class="th_label_approval">审批单号</th>
            <td colspan="9" class="td_value_approval">{{ mainData.caseCode }}</td>
          </tr>
          <tr>
            <th colspan="3" class="th_label_approval">经办人</th>
            <td colspan="9" class="td_value_approval">{{ mainData.createPsnFullName }}</td>
            <th colspan="3" class="th_label_approval">经办时间</th>
            <td colspan="9" class="td_value_approval">{{ mainData.createTime | parseTime }}</td>
          </tr>
          <tr>
            <th colspan="3" class="th_label_approval">经办人电话</th>
            <td colspan="9" class="td_value_approval">{{ mainData.createPsnPhone }}</td>
            <th colspan="3" class="th_label_approval">风险发生部门</th>
            <td colspan="9" class="td_value_approval">{{ queryOtherPartys() }}</td>

          </tr>
          <tr>
            <th colspan="3" class="th_label_approval">涉及金额(元)</th>
            <td colspan="9" class="td_value_approval">{{ mainData.caseMoney | toThousandFilter }}</td>
          </tr>

          <tr>
            <th colspan="3" class="th_label_approval_">风险描述</th>
            <td colspan="21" class="td_value_approval_">{{ mainData.riskDescription }}</td>
          </tr>
        </tbody>

        <tbody>
        <tr>
          <th colspan="3" class="th_label_approval">事项报告</th>
          <td colspan="21" class="td_value_approval" style="height: 100%">
            <div v-if="mainData.eventReport || isCreate">
              <uploadDoc
                  v-model="mainData.eventReport"
                  :files.sync="mainData.eventReport"
                  :disabled="!isCreate"
                  doc-path="/case"
              />
            </div>
          </td>
        </tr>
        </tbody>

        <tbody>
        <tr>
          <th colspan="3" class="th_label_approval">附件材料</th>
          <td colspan="21" class="td_value_approval">
            <div v-if="mainData.relatedAttachments">
              <UploadDoc :files.sync="mainData.relatedAttachments" doc-path="/case" :disabled="true"/>
            </div>
            <div v-else style="font-size: 15px">无</div>
          </td>
        </tr>
        </tbody>
      </table>
    </SimpleBoardTitleApproval>
  </div> -->
</template>

<script>
import dictApi from '@/api/_system/dict'
import AreaSelect from '@/view/components/AreaSelect/AreaSelect'
import OrgSingleDialogSelect from '@/view/components/OrgSingleDialogSelect/index.vue'
import UploadDoc from '@/view/components/UploadDoc/UploadDoc.vue'
import orgTree from '@/view/components/OrgTree/OrgTree'
import SimpleBoardTitle from "@/view/components/SimpleBoard/SimpleBoardTitle"
import SimpleBoardTitleApproval from "@/view/components/SimpleBoard/SimpleBoardTitleApproval"
import money from "@/view/components/Money/index"
import OrgLeader from '@/view/components/OrgLeader/OrgLeader'


export default {
  name: 'QsBaseInfo',
  components: {
    OrgSingleDialogSelect, AreaSelect, UploadDoc, orgTree, SimpleBoardTitleApproval, money,
    SimpleBoardTitle, OrgLeader
  },
  props: {
    data: {
      type: Object,
      default: function () {
        return {}
      }
    },
    dataState: {
      type: String,
      default: ''
    },
    view: {
      type: String,
      default: 'new'
    },
    create: {
      type: String,
      default: ''
    },
    authorizationData: {
      type: Object,
      default: function () {
        return {}
      }
    },
  },
  data() {
    return {
      orgDialogTitle: '组织信息',
      caseDialogVisible: false,
      mainData: this.data,
      dicTreeDialogVisible: false,
      dicTreeDialogVisible2: false,
      dicTreeDialogVisible3: false,
      orgTreeDialog: false,
      caseNatures: [],
      plateData: [],
      causeOfIns: [],
      applications: [],
      involvedLevelData: [],//涉案单位管理层级
      suedUnitTypeData: [], //被诉单位性质
      unitTypeData: [],//单位类型
      reportCategoryData: [],//报告类别
      zxcheckedData: [],
      dialogVisible: false,
      orgTreeDialog: false,
      orgVisible: false,
      tableQuery: {
        reportYear: '',
      },
      yearOptions: Array.from({ length: 10 }, (_, i) => new Date().getFullYear() - i)
    }
  },
  computed: {
    formattedPlannedStartTime() {
      return this.formatDate(this.mainData.plannedStartTime);
    },
    formattedPlannedEndTime() {
      return this.formatDate(this.mainData.plannedEndTime);
    },
    isView: function () {
      return this.dataState === this.utils.formState.VIEW
    },
    causeOfInIds: {
      set: function (data) {
        this.mainData.causeOfInId = data.join(',')
      },
      get: function () {
        if (this.mainData.causeOfInId) {
          return this.mainData.causeOfInId.split(',')
        }
        return []
      }
    },
    isCreate: function () {
      return this.create === 'create'
    },
    showUserValue() {
      return this.mainData.evaluationObjectType === '合规管理员';
    }
  },
  watch: {
    mainData: {
      handler(val, oldVal) {
        this.$emit('update:data', val)
      },
      deep: true
    },
    data(val) {
      this.mainData = Object.assign(this.mainData, val)
    },
    'mainData.involvedAmount': {
      handler(val, oldVal) {
        this.involvedAmountChange(val)
      },
      deep: true, immediate: true
    },
    'mainData.caseInterest': {
      handler(val, oldVal) {
        this.caseInterestChange(val)
      },
      deep: true, immediate: true
    }
  },
  created() {
    this.initDic()
    window.vm = this;
  },
  methods: {
    formatDate(dateString) {
      if (!dateString) return '';
      const date = new Date(dateString);
      return date.toISOString().split('T')[0];
    },
    reportCategoryChange(val) {
      this.mainData.reportCategory = this.utils.getDicName(this.reportCategoryData, val)
    },
    venueIsOutOnChange(val) {
      this.mainData.venueAddress = null
      this.mainData.venueProvince = null
      this.mainData.venueCity = null
      this.mainData.venueRegion = null
    },
    sureBtn(data) {
      this.mainData.caseUndertakerId = data.unitId
      this.mainData.caseUndertaker = data.name
    },
    sureBtn2(data) {
      this.mainData.ognPackageId = data.unitId
      this.mainData.ognPackage = data.name
    },
    sureBtn3(data) {
      this.mainData.groupPackageId = data.unitId
      this.mainData.groupPackage = data.name
    },
    orgSelect(data) {
      this.mainData.currentUnitId = data.unitId
      this.mainData.currentUnit = data.name

      this.mainData.riskDepartmentId = data.unitId
      this.mainData.riskDepartment = data.name
    },
    moneyFocus(event) {
      event.currentTarget.select()
    },
    chooseApprovalDeptClick() {
      this.isCheckedUser = false
      this.showUser = false
      this.orgVisible = true
      this.is_Check = false
    },

    queryOtherPartys() {
      let partys = this.mainData.partiesList
      let otherPartys = ""

      if (partys.length > 0) {
        for (let i = 0; i < partys.length; i++) {
          if (partys[i].partyType !== '原告' && partys[i].party !== null && partys[i].party !== undefined)
            otherPartys += partys[i].party + ","
        }

        if (otherPartys !== "")
          otherPartys = otherPartys.slice(0, -1)
      }

      return otherPartys
    },
    showOrgTreeDialog() {
      this.dialogVisible = true;
    },
    cancel() {
      this.dialogVisible = false
    },
    choiceDeptSure() {
      let selectedUnits = this.zxcheckedData.map(item => item.name).join(', ');
      this.mainData.evaluationObject = selectedUnits;
      this.dialogVisible = false;
    }
  }
}
</script>

<style lang="scss" scoped>
.hideContent {

  .el-input__inner,
  .el-radio.is-bordered,
  .el-textarea__inner,
  .el-input__count {
    background-color: #f9e8bb;
  }
}

.money-label-width .el-form-item__label {
  width: 150px;
  /* 指定宽度 */
}

/* 过于长的label分两行展示样式 */
/deep/.fold_label .el-form-item__label {
  white-space: pre-line;
  /* text-align-last: justify; 
  text-align: justify;*/
  margin-top: -4px;
  line-height: 23px;
  text-justify: distribute-all-lines;
}
</style>
