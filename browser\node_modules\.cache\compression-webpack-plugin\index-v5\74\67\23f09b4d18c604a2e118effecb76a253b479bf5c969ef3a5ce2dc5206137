
96ec9abb57f06908922e1be8d8a07d6cdc823f38	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.342.1754018536329.js\",\"contentHash\":\"d8d07fdb18e07a0b7de5d5430dd22263\"}","integrity":"sha512-9Z15jfvz6IVMIKSjCdN8oAmUyb5v85TbnZ9mIb2lYQjS+BOXJzPN68Sr6LZyY/bvxOI+xBIO9Y0+uUTyG30vgw==","time":1754018575975,"size":95921}