
bf4e04be152482071adf5cbe394ddf61e769c406	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.10.1754018536329.js\",\"contentHash\":\"d9d7b5d3202afafea75ada817ce61f09\"}","integrity":"sha512-a3FmbIa1Wt1d84oOk24IWh+a5evaNHzu4sUeATjTbaYYjl2ysTxbSouNAqjF3lqH1NseYmDCKCbNiC7e7AJfSw==","time":1754018575954,"size":38137}