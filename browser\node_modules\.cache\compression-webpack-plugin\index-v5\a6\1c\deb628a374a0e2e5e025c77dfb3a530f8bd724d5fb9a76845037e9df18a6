
12ac63da293353244070dc8452ac47dd6c3d0c8d	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.328.1754018536329.js\",\"contentHash\":\"b17a543cf684b0c62eb56462c6dc3c4a\"}","integrity":"sha512-DaLfJ0ljt35iMRth6OdVvXEGGmFlXzOCMybf1eIMQtYkRzU+yj2wioeytHFV8dnz3ojjWttORPBx+uV9HBGbGw==","time":1754018575974,"size":105436}