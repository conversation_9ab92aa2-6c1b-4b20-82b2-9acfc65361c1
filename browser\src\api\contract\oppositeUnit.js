import {request} from '@/api/index'

export default {
  query(data) {
    return request({
      url: '/oppositeogn/query',
      method: 'post',
      data
    })
  },
  queryDialog(data) {
    return request({
      url: '/oppositeogn/queryDialog',
      method: 'post',
      data
    })
  },
  save(data) {
    return request({
      url: '/oppositeogn/save',
      method: 'post',
      data
    })
  },
  delete(data) {
    return request({
      url: '/oppositeogn/delete',
      method: 'post',
      data
    })
  },
  queryById(data) {
    return request({
      url: '/oppositeogn/queryById',
      method: 'post',
      data
    })
  },
  stopSave(data) {
    return request({
      url: '/oppositeogn/stopSave',
      method: 'post',
      data
    })
  }
}