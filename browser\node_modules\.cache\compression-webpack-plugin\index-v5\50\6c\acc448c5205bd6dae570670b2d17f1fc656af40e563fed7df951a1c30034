
27f4e525d60c08f495aee134db83c90517ec806d	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.355.1754018536329.js\",\"contentHash\":\"9a6e8d7f40511f3c9f113f845e760fea\"}","integrity":"sha512-A5WdX20O3AeyIxROwMi2wrjA3qxjyk1/wY7YwC0m3dHIoe68fqMnaJurkqaHDS6t01FXQw/wwA8vg28RlOty6w==","time":1754018575975,"size":103657}