
940f68ec31533f6302cb1d2e48ad662ce23b3588	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.319.1754018536329.js\",\"contentHash\":\"5f6aaa2fde70d46da11b5c36859a798e\"}","integrity":"sha512-geT9yQcJ8SSRm9otL6bAn/x8m15ajMCrXVlp5eu+3ca2dvazF5jXTuoFdaxwaBmXB0D4nAlKLXmxxa03SB4/IA==","time":1754018575974,"size":113364}