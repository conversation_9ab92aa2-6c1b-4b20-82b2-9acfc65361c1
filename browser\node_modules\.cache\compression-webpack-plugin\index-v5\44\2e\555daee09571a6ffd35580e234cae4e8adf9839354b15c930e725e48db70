
da4ed3907e15930fe529f4a83dba569d8a843180	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.54.1754018536329.js\",\"contentHash\":\"f1ec7da48867f0d7fc500fb316c4d59c\"}","integrity":"sha512-o7800uW/PG1DJDaVWihtkjwsq4RiQhqvVB2xiq0zfsiyoZPduiH+ZD1dPQgj38GfuWXZa4vCEBOXf5wKL0HShA==","time":1754018575958,"size":92439}