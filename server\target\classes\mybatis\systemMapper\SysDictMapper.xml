<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.klaw.dao.systemDao.SysDictMapper">

    <select id="queryDic" resultType="com.klaw.entity.systemBean.SysDict">
        SELECT *   FROM SG_SYS_DICT
        <where>
            <if test="parentid == null ">
                parent_id is  null
            </if>
            <if test="parentid != null and parentid != ''">
                and  parent_id = #{parentid}
            </if>
        </where>
        order by sort
    </select>

    <select id="showSelect" resultType="com.klaw.entity.systemBean.SysDict">
        select * from SG_SYS_DICT where parent_id in (select id from SG_SYS_DICT where dic_code=#{dicCode}) and is_leaf='isLeaf' and dic_status=0 order by sort
    </select>


    <select id="queryDicSort" resultType="com.klaw.entity.systemBean.SysDict">
        SELECT *   FROM SG_SYS_DICT
        <where>
            <if test="parentid == null ">
                parent_id is  null
            </if>
            <if test="parentid != null and parentid != ''">
                and  parent_id = #{parentid}
            </if>
        </where>
        order by sort
    </select>


    <select id="showAllSelect" resultType="com.klaw.entity.systemBean.SysDict">
        select * from SG_SYS_DICT where parent_id in (select id from SG_SYS_DICT where dic_code=#{dicCode})  and dic_status=0 order by sort
    </select>

    <select id="queryDicCode" resultType="java.lang.Integer">
        SELECT COUNT(0) FROM SG_SYS_DICT WHERE dic_code=#{dicCode}
    </select>

    <update id="updateIsLeaf">
        UPDATE SG_SYS_DICT SET is_leaf=NULL WHERE id=#{id}
    </update>

    <select id="showTable" resultType="com.klaw.entity.systemBean.SysDict">
        select * from SG_SYS_DICT where PARENT_ID = #{parentId}
    </select>

    <update id="updateStatus">
        UPDATE SG_SYS_DICT SET dic_status=#{dicStatus} WHERE id=#{id}
    </update>

    <update id="updateDict">
        UPDATE SG_SYS_DICT SET dic_name=#{dicName},dic_code=#{dicCode},remark=#{remark},url_name=#{urlName},whether_Solidified=#{whetherSolidified} where id=#{id}
    </update>

    <select id="searchDic" resultType="com.klaw.entity.systemBean.SysDict">
        SELECT * FROM SG_SYS_DICT
        <where>
            <if test="parentId == null  ||  parentId == ''">
                and  parent_id = '1'
            </if>
            <if test="parentId != null and parentId != ''">
                and  parent_id = #{parentId}
            </if>
            <if test="dicName!=null and dicName!=''">
                and dic_name like concat('%', #{dicName}, '%')
            </if>
            <if test="dicCode !=null and dicCode != ''">
                and dic_code like concat('%', #{dicCode}, '%')
            </if>
            <if test="dicStatus != null and dicStatus != ''">
                and dic_status=#{dicStatus}
            </if>
        </where>
    </select>
    <select id="showregion" resultType="java.util.Map">
        select * from sys_org s1 inner join sys_org s2 on s1.parent_id=s2.id where s2.org_name='时代集团'
    </select>
    <select id="showAllSelectbyid" resultType="com.klaw.entity.systemBean.SysDict">
        select * from SG_SYS_DICT where parent_id =#{id}  and dic_status=0 order by sort
    </select>

</mapper>
