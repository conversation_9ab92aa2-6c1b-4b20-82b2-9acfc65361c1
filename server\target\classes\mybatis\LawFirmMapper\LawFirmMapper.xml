<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.klaw.dao.lawyerDao.LawFirmMapper">
    <resultMap id="BaseResultMap" type="com.klaw.entity.lawyerBean.LawFirm">
        <id column="ID" jdbcType="VARCHAR" property="id" />
        <result column="CREATE_OGN_ID" jdbcType="VARCHAR" property="createOgnId" />
        <result column="CREATE_OGN_NAME" jdbcType="VARCHAR" property="createOgnName" />
        <result column="CREATE_DEPT_ID" jdbcType="VARCHAR" property="createDeptId" />
        <result column="CREATE_DEPT_NAME" jdbcType="VARCHAR" property="createDeptName" />
        <result column="CREATE_GROUP_ID" jdbcType="VARCHAR" property="createGroupId" />
        <result column="CREATE_GROUP_NAME" jdbcType="VARCHAR" property="createGroupName" />
        <result column="CREATE_PSN_ID" jdbcType="VARCHAR" property="createPsnId" />
        <result column="CREATE_PSN_NAME" jdbcType="VARCHAR" property="createPsnName" />
        <result column="CREATE_ORG_ID" jdbcType="VARCHAR" property="createOrgId" />
        <result column="CREATE_ORG_NAME" jdbcType="VARCHAR" property="createOrgName" />
        <result column="CREATE_PSN_FULL_ID" jdbcType="VARCHAR" property="createPsnFullId" />
        <result column="CREATE_PSN_FULL_NAME" jdbcType="VARCHAR" property="createPsnFullName" />
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
        <result column="DATA_STATE" jdbcType="VARCHAR" property="dataState" />
        <result column="DATA_STATE_CODE" jdbcType="DECIMAL" property="dataStateCode" />
        <result column="LAWYER_FIRM" jdbcType="VARCHAR" property="lawyerFirm" />
        <result column="REGISTER_ADDRESS" jdbcType="VARCHAR" property="registerAddress" />
        <result column="FUNCTIONARY" jdbcType="VARCHAR" property="functionary" />
        <result column="POSTAL_CODE" jdbcType="VARCHAR" property="postalCode" />
        <result column="ISSUING_AUTHORITY" jdbcType="VARCHAR" property="issuingAuthority" />
        <result column="LICENSE_CODE" jdbcType="VARCHAR" property="licenseCode" />
        <result column="LICENSE_NUMBER" jdbcType="VARCHAR" property="licenseNumber" />
        <result column="REGISTERED_CAPITAL" jdbcType="VARCHAR" property="registeredCapital" />
        <result column="FOUND_TIME" jdbcType="TIMESTAMP" property="foundTime" />
        <result column="WORK_NUMBER" jdbcType="DECIMAL" property="workNumber" />
        <result column="APPLY_DEPT_NAME" jdbcType="VARCHAR" property="applyDeptName" />
        <result column="APPLY_DEPT_ID" jdbcType="VARCHAR" property="applyDeptId" />
        <result column="ANNUAL_INSPECTION_NAME" jdbcType="VARCHAR" property="annualInspectionName" />
        <result column="ANNUAL_INSPECTION_ID" jdbcType="VARCHAR" property="annualInspectionId" />
        <result column="RECOMMENDED_INSTRUCTIONS_NAME" jdbcType="VARCHAR" property="recommendedInstructionsName" />
        <result column="RECOMMENDED_INSTRUCTIONS_ID" jdbcType="VARCHAR" property="recommendedInstructionsId" />
        <result column="INTRODUCTION" jdbcType="CLOB" property="introduction" />
        <result column="REMARK" jdbcType="CLOB" property="remark" />
        <result column="BUSINESS_LICENSE" jdbcType="CLOB" property="businessLicense" />
        <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="BE_GOOD_AT_DOMAIN" jdbcType="VARCHAR" property="beGoodAtDomain" />
        <result column="BE_GOOD_AT_DOMAIN_IDS" jdbcType="VARCHAR" property="beGoodAtDomainIds" />
        <result column="RECOMMENDED" jdbcType="VARCHAR" property="recommended" />
        <result column="LAW_FIRM_TYPE" jdbcType="VARCHAR" property="lawFirmType" />
        <result column="LAW_FIRM_SELECTED" jdbcType="VARCHAR" property="lawFirmSelected" />
        <result column="LAW_FIRM_PHONE" jdbcType="VARCHAR" property="lawFirmPhone" />
        <collection property="lawyerList" javaType="java.util.ArrayList" ofType="com.klaw.entity.lawyerBean.Lawyer">
            <id column="DID" jdbcType="VARCHAR" property="id" />
            <result column="DLAWYER_NAME" jdbcType="VARCHAR" property="lawyerName" />
            <result column="DBE_GOOD_AT_DOMAIN" jdbcType="VARCHAR" property="beGoodAtDomain" />
        </collection>
    </resultMap>
    <sql id="Base_Column_List">
        m.ID,m.CREATE_OGN_ID,m.CREATE_OGN_NAME,m.CREATE_DEPT_ID,m.CREATE_DEPT_NAME,m.CREATE_GROUP_ID,m.CREATE_GROUP_NAME,
        m.CREATE_PSN_ID,m.CREATE_PSN_NAME,m.CREATE_ORG_ID,m.CREATE_ORG_NAME,m.CREATE_PSN_FULL_ID,m.CREATE_PSN_FULL_NAME,
        m.CREATE_TIME,m.DATA_STATE,m.DATA_STATE_CODE,m.LAWYER_FIRM,m.REGISTER_ADDRESS,m.FUNCTIONARY,
        m.POSTAL_CODE,m.ISSUING_AUTHORITY,m.LICENSE_CODE,m.LICENSE_NUMBER,m.REGISTERED_CAPITAL,
        m.FOUND_TIME,m.WORK_NUMBER,m.APPLY_DEPT_NAME,m.APPLY_DEPT_ID,m.ANNUAL_INSPECTION_NAME,m.ANNUAL_INSPECTION_ID,
        m.NO_RESPONSE_TIMES,m.RECOMMENDED_INSTRUCTIONS_NAME,m.RECOMMENDED_INSTRUCTIONS_ID,m.INTRODUCTION,m.REMARK,
        m.BUSINESS_LICENSE,m.UPDATE_TIME,m.BE_GOOD_AT_DOMAIN,m.BE_GOOD_AT_DOMAIN_IDS,m.RECOMMENDED,
        m.LAW_FIRM_TYPE,m.LAW_FIRM_SELECTED,m.LAW_FIRM_PHONE,m.LAW_FIRM_FAX,
        d.ID AS DID,d.LAWYER_NAME AS DLAWYER_NAME,d.BE_GOOD_AT_DOMAIN AS DBE_GOOD_AT_DOMAIN
    </sql>

    <update id="updateStateByIds">
        update SG_LAW_FIRM
        <set>
            <if test="statusCode !=  null ">
                data_state_code=#{statusCode},
            </if>
            <if test="statusName !=  null  and statusName != '' ">
                data_state=#{statusName},UPDATE_TIME = SYSDATE()
            </if>
        </set>
        WHERE
        id in
        <foreach item="dataId" index="index" collection="list"
                 open="(" separator="," close=")"> #{dataId}</foreach>
    </update>


    <select id="queryExportData" parameterType="java.util.Map" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from SG_LAW_FIRM m LEFT JOIN SG_LAWYER d on d.law_firm_id = m.id and d.whether_host_lawyer = 1
        <where>
            ${ew.sqlSegment}
        </where>
    </select>

    <select id="queryLawFirmAxis" resultType="java.util.Map">
        SELECT *  FROM (
        SELECT
        a.id as dataId,
        SOURCE_ID AS id,
        LAWYER_FIRM AS name,
        CREATE_TIME AS time,
        '入库' AS operating
        FROM
        SG_LAW_FIRM_IN_APPROVAL a
        WHERE
        OPERATING_TYPE = 'new'
        and data_state_code in (3,4,5,6) UNION ALL
        SELECT
        b.id as dataId,
        SOURCE_ID AS id,
        LAWYER_FIRM AS name,
        CREATE_TIME AS time,
        '变更' AS operating
        FROM
        SG_LAW_FIRM_IN_APPROVAL b
        WHERE
        OPERATING_TYPE = 'change'
        and data_state_code in (3,4,5,6) UNION ALL
        SELECT
        e.id as dataId,
        SOURCE_ID AS id,
        LAWYER_FIRM AS name,
        CREATE_TIME AS time,
        '推荐入库' AS operating
        FROM
        SG_LAW_FIRM_IN_APPROVAL e
        WHERE
        OPERATING_TYPE = 'recommend'
        and data_state_code in (3,4,5,6) UNION ALL
        SELECT
        m.id AS dataId,
        d.LAWYER_FIRM_ID AS id,
        d.LAWYER_FIRM AS name,
        m.CREATE_TIME AS time,
        '出库' AS operating
        FROM
        SG_LAW_FIRM_OUT_APPROVAL_DETAIL d join SG_LAW_FIRM_OUT_APPROVAL_MAIN m on m.id=d.PARENT_ID
        WHERE
        m.data_state_code IN ( 3, 4, 5, 6 )  UNION ALL
        SELECT
        id as dataId,
        SOURCE_ID AS id,
        LAWYER_FIRM AS name,
        CREATE_TIME AS time,
        '黑名单' AS operating
        FROM
        SG_LAW_FIRM_BLACKLIST where DATA_SOURCE = 'old'
        ) A
        <where>
            A.id = #{id}
        </where>
        order by A.time
    </select>
</mapper>