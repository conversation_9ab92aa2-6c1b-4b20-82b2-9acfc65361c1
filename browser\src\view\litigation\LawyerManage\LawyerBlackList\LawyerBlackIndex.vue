<template>
  <el-container direction="vertical" class="container-manage-sg">
    <el-header>
      <el-card>
        <el-input
            v-model="temp.fuzzyValue"
            class="filter_input"
            placeholder="检索条件（律师名称、所属律所、执业证号）"
            clearable
            @keyup.enter.native="refreshData"
            @clear="refreshData"
        >
          <el-popover slot="prepend" placement="bottom-start" width="1000" trigger="click">
            <el-form ref="queryForm" label-width="100px" size="mini">
              <el-row>
                <el-col :span="12">
                  <el-form-item label="律师名称">
                    <el-input v-model="temp.lawyerName" placeholder="请输入..." style="width:100%"
                              clearable/>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="所属律所">
                    <el-input v-model="temp.lawFirm" placeholder="请输入..." style="width:100%" clearable/>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="12" class="myLabel">
                  <el-form-item label="执业证号" class="custom-word-break">
                    <span slot="label">执业证号</span>
                    <el-input v-model="temp.charteredNo" placeholder="请输入..." style="width:100%" clearable/>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-button-group style="float: right">
                <el-button type="primary" icon="el-icon-search" @click="search_">搜索</el-button>
                <el-button type="primary" icon="el-icon-refresh-left" @click="empty_">重置</el-button>
              </el-button-group>
            </el-form>
            <el-button slot="reference" type="primary">高级检索</el-button>
          </el-popover>
          <el-button slot="append" icon="el-icon-search" @click="search_"/>
        </el-input>
      </el-card>
    </el-header>

    <el-main>
      <SimpleBoard2 :title="'律师黑名单列表'" :isButton="isLegal" @addBtn="add_">
        <el-table
            ref="table"
            :data="tableData"
            border
            :show-overflow-tooltip="true"
            style="width: 100%"
            :height="table_height"
            stripe
            fit
            highlight-current-row
            @sort-change="tableSort"
            @row-dblclick="rowDblclick"
        >
          <el-table-column label="序号" type="index" align="center" width="50"/>
          <el-table-column v-if="ss.tableColumns.find(item => item.key === 'lawyerName').visible" label="律师名称"
                           prop="lawyerName" align="center" min-width="200" show-overflow-tooltip sortable="custom"/>
          <el-table-column v-if="ss.tableColumns.find(item => item.key === 'lawFirm').visible" label="所属律所"
                           prop="lawFirm" min-width="100" align="center" show-overflow-tooltip sortable="custom"/>
          <el-table-column v-if="ss.tableColumns.find(item => item.key === 'lawyerTime').visible"
                           label="从事律师执业时间"
                           prop="lawyerTime" align="center" min-width="100" show-overflow-tooltip sortable="custom"/>
          <el-table-column v-if="ss.tableColumns.find(item => item.key === 'beGoodAtDomain').visible"
                           label="擅长业务领域" prop="beGoodAtDomain"
                           min-width="100" align="center" show-overflow-tooltip sortable="custom"/>
          <el-table-column v-if="ss.tableColumns.find(item => item.key === 'dataState').visible" label="状态"
                           min-width="100" prop="dataState" align="center" show-overflow-tooltip sortable="custom"/>
          <el-table-column label="操作" align="center" min-width="100">
            <template slot-scope="scope">
              <el-button type="text" v-if="isEdit(scope.row)" @click="edit_(scope.$index,scope.row)">编辑</el-button>
              <el-button type="text" @click="view_(scope.$index,scope.row)">查看</el-button>
              <el-button type="text" v-if="isDelete(scope.row)" @click="delete_(scope.$index,scope.row)">删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </SimpleBoard2>
    </el-main>

    <el-footer>
      <pagination
          align="center"
          :total="temp.total"
          :page.sync="temp.page"
          :limit.sync="temp.limit"
          @pagination="refreshData"
      />
    </el-footer>

  </el-container>
</template>

<script>
import lawyerApi from '@/api/LawyerManage/LawyerBlackList/lawyerBlackList'
import pagination from '@/components/Pagination'
import SimpleBoard2 from "@/view/components/SimpleBoard/SimpleBoardIndex"
import {mapGetters} from "vuex"
import taskApi from "@/api/_system/task"
import orgApi from '@/api/_system/org'

export default {
  name: 'LawyerBlackListIndex',
  inject: ['layout'],
  components: {pagination, SimpleBoard2},
  computed: {
    ...mapGetters(["orgContext", "currentFunctionId"])
  },
  data() {
    return {
      isLegal: false,
      table_height: '100%', // 定义表格高度
      tableData: null, // 定义表格数据源
      typeName: null,   // 库类别名称
      typeCode: null,  // 库类别代码
      temp: {
        lawyerName: null, // 律师名称
        lawFirm: null, // 所属律所
        charteredNo: null, // 执业证号
        dataState: null, // 状态
        fuzzyValue: null, // 模糊字段
        sortName: null, // 排序
        order: false,
        page: 1,
        limit: 10,
        total: 0,
        functionId: null,
        orgId: null,
      },
      ss: {
        data: this.tableData,
        tableColumns: [
          {key: 'lawyerName', label: '律师名称', visible: true},
          {key: 'lawFirm', label: '所属律所', visible: true},
          {key: 'lawyerTime', label: '从事律师执业时间', visible: true},
          {key: 'beGoodAtDomain', label: '擅长业务领域', visible: true},
          {key: 'dataState', label: '审批状态', visible: true}
        ]
      }
    }
  },
  activated() {
    // 长连接页面第二次激活的时候,不会走created方法,会走此方法
    this.refreshData()
  },
  created() {
    this.refreshData()
  },
  mounted() {
    this.$nextTick(function () {
      this.table_height = window.innerHeight - this.$refs.table.$el.offsetTop - 84 - 65
      // 监听窗口大小变化
      const self = this
      window.onresize = function () {
        self.table_height = window.innerHeight - self.$refs.table.$el.offsetTop - 84 - 65
      }
    })
  },
  methods: {
    refreshData() { //  查询方法
      this.temp.functionId = this.currentFunctionId.id
      this.temp.orgId = this.orgContext.currentOrgId
      lawyerApi.query(this.temp).then((res) => {
        this.tableData = res.data.page.records
        this.ss.data = this.tableData
        this.temp.total = res.data.page.total
      })
      this.isLegal_()
    },
    isLegal_() {
      orgApi.roleCheck({orgId: this.orgContext.currentOrgId, roleName: 'FLGWFWBQX'}).then(res => {
        console.log('isLegal：', res)
        this.isLegal = res.data.data
      })
    },
    search_() { // 查询
      this.refreshData()
    },
    empty_() {
      this.temp = {
        lawyerFirm: null, // 律师名称
        functionary: null, // 负责人
        licenseCode: null, // 统一社会信用代码
        lawFirmFax: null, // 律所传真
        dataState: null, // 状态
        fuzzyValue: null, // 模糊字段
        sortName: null, // 排序
        order: false,
        page: 1,
        limit: 10,
        total: 0,
        isQuery: true,
        functionId: null,
        orgId: null,
      }
      this.refreshData()
    },
    add_() {
      const uuid = this.utils.createUUID();
      this.layout.openNewTab(
          "黑名单信息",
          "lawyer_black_main_detail",
          "lawyer_black_main_detail",
          uuid,
          {
            functionId: "lawyer_black_main_detail," + uuid,
            ...this.utils.routeState.NEW(uuid)
          }
      );
    },
    tableSort(column, prop, order) {
      this.temp.sortName = column.prop
      this.temp.order = column.order === 'ascending'
      this.refreshData()
    },
    rowDblclick(row) {
      taskApi.selectTaskId({businessKey: row.id, isView: 'true'}).then(res => {
        if (res.data.data.length > 0) {
          const functionId = res.data.data[0].ID
          const tabId = this.utils.createUUID()
          this.layout.openNewTab("黑名单信息",
              "design_page",
              "design_page",
              tabId,
              {
                processInstanceId: res.data.data[0].PID,//流程实例
                taskId: res.data.data[0].ID,//任务ID
                businessKey: row.id, //业务数据ID
                functionId: functionId, //不传也可以，只有首环节需要传，实例启动就可以不传了
                entranceType: "FLOWABLE",
                type: "haveDealt",
                channel: 'business', //查看渠道，默认就传这个值即可，表示是从业务功能传过来的，首环节提交后过滤掉撤回按钮
                view: 'new',
              }
          )
        } else {
          const tabId = this.utils.createUUID();
          this.layout.openNewTab(
              "黑名单信息",
              'lawyer_black_main_detail',
              'lawyer_black_main_detail',
              tabId,
              {
                functionId: 'lawyer_black_main_detail,' + tabId,
                ...this.utils.routeState.VIEW(row.id)
              }
          )
        }
      })
    },
    isEdit(row) {
      return row.dataStateCode === this.utils.dataState_BPM.SAVE.code || row.dataStateCode === this.utils.dataState_BPM.BACK.code
    },
    isDelete(row) {
      return row.dataStateCode === this.utils.dataState_BPM.SAVE.code
    },
    view_(index, row) {
      taskApi.selectTaskId({businessKey: row.id, isView: 'true'}).then(res => {
        if (res.data.data.length > 0) {
          const functionId = res.data.data[0].ID
          const tabId = this.utils.createUUID()
          this.layout.openNewTab("黑名单信息",
              "design_page",
              "design_page",
              tabId,
              {
                processInstanceId: res.data.data[0].PID,//流程实例
                taskId: res.data.data[0].ID,//任务ID
                businessKey: row.id, //业务数据ID
                functionId: functionId, //不传也可以，只有首环节需要传，实例启动就可以不传了
                entranceType: "FLOWABLE",
                type: "haveDealt",
                channel: 'business', //查看渠道，默认就传这个值即可，表示是从业务功能传过来的，首环节提交后过滤掉撤回按钮
                view: 'new',
              }
          )
        } else {
          const tabId = this.utils.createUUID();
          this.layout.openNewTab(
              "黑名单信息",
              'lawyer_black_main_detail',
              'lawyer_black_main_detail',
              tabId,
              {
                functionId: 'lawyer_black_main_detail,' + tabId,
                ...this.utils.routeState.VIEW(row.id)
              }
          )
        }
      })
    },
    edit_(index, row) {
      const uuid = this.utils.createUUID();
      this.layout.openNewTab(
          "黑名单信息",
          "lawyer_black_main_detail",
          "lawyer_black_main_detail",
          uuid,
          {
            functionId: "lawyer_black_main_detail," + uuid,
            ...this.utils.routeState.EDIT(row.id)
          }
      );
    },
    delete_(index, row) {
      this.$confirm('您确定要删除吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        new Promise((resolve, reject) => {
          lawyerApi.delete({id: row.id}).then((response) => {
            resolve(response)
          })
        }).then(value => {
          this.tableData.splice(index, 1)
          this.$message.success('删除成功!')
        })
      }).catch(() => {
        this.$message.info('已取消删除!')
      })
    },
  }
}

</script>

<style>
.mylabel .el-form-item--mini .el-form-item__label {
  line-height: 15px;
}

</style>