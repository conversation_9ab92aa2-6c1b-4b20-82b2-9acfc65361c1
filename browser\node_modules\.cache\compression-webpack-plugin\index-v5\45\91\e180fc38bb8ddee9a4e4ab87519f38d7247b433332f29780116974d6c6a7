
c8960f0b8a66fac8887eb90282c9c6e41c75a1d2	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.240.1754018536329.js\",\"contentHash\":\"ed6eaa84b248ff096be7a1a16e105280\"}","integrity":"sha512-mSjtqcDRU6eHxL7r/0Dac1OLjOKSA7kiDnSZ6PaWjc7cZO3JuWqjSOGnuKiuKnIlxO+oKYzS8DuZHlEqRvcqiw==","time":1754018575996,"size":132545}