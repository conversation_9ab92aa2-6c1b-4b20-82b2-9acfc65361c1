package com.klaw.service.imp.complianceRiskServiceImp;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.klaw.dao.complianceRiskDao.JobResponsibilitiesListMapper;
import com.klaw.entity.complianceRiskBean.JobResponsibilitiesList;
import com.klaw.service.complianceRiskService.JobResponsibilitiesListService;
import com.klaw.utils.DataAuthUtils;
import com.klaw.utils.PageUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.klaw.vo.Json;
import java.util.Date;
@Service
public class JobResponsibilitiesListServiceImpl extends ServiceImpl<JobResponsibilitiesListMapper, JobResponsibilitiesList> implements JobResponsibilitiesListService {

    @Override
    public void saveData(JobResponsibilitiesList jobResponsibilitiesList) {
        jobResponsibilitiesList.setUpdateTime(new Date());
        saveOrUpdate(jobResponsibilitiesList);
    }


    @Override
    public Page<JobResponsibilitiesList> queryPageData(JSONObject json) {
        QueryWrapper<JobResponsibilitiesList> wrapper = new QueryWrapper<>();
        getFilter(json, wrapper);
        return page(new PageUtils<>(json), wrapper);
    }


    public void getFilter(JSONObject json, QueryWrapper<JobResponsibilitiesList> wrapper){
        //是否台账功能（登记只查自己的，台账和弹框查自己和数据权限的）
        boolean isQuery = json.containsKey("isQuery") ? json.getBoolean("isQuery") : false;
        String orgId = json.containsKey("orgId") ? json.getString("orgId") : "";
        String unit = json.containsKey("unit") ? json.getString("unit") : null;
        String responsiblePosition = json.containsKey("responsiblePosition") ? json.getString("responsiblePosition") : null;
        String primaryJobClassification = json.containsKey("primaryJobClassification") ? json.getString("primaryJobClassification") : null;
        String fuzzyValue = json.containsKey("fuzzyValue") ? json.getString("fuzzyValue") : null;

        if (StringUtils.isNotBlank(unit)) {
            wrapper.like("unit", unit);
        }
        if (StringUtils.isNotBlank(responsiblePosition)) {
            wrapper.like("responsible_position", responsiblePosition);
        }
        if (StringUtils.isNotBlank(primaryJobClassification)) {
            wrapper.like("primary_job_classification", primaryJobClassification);
        }
        if (StringUtils.isNotBlank(fuzzyValue)) {
            wrapper.and(i -> i.like("unit", fuzzyValue)
                    .or().like("responsible_position", fuzzyValue)
                    .or().like("primary_job_classification", fuzzyValue))
            ;
        }
        if(isQuery){
            Long functionId = DataAuthUtils.getFunctionIdByCode("gwzzqd_ledger_index");
            DataAuthUtils.dataPermSql(wrapper, null, functionId, orgId);
            wrapper.eq("data_state", "已提交");
        }else{
            wrapper.eq("create_org_id", orgId);
        }
        wrapper.orderByDesc("create_time");
    }

    @Override
    public JobResponsibilitiesList queryDataById(String id) {
        JobResponsibilitiesList jobResponsibilitiesList = getById(id);
        return jobResponsibilitiesList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Json deleteDataById(String id) {
        boolean flag = removeById(id);
        if (!flag) {
            return Json.fail().msg("未找到需要删除的数据");
        }
        return Json.succ().msg("删除成功!");
    }

}
