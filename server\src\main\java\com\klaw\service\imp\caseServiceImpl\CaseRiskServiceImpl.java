package com.klaw.service.imp.caseServiceImpl;


import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.klaw.dao.caseDao.CaseRiskMapper;
import com.klaw.entity.caseBean.CaseRisk;
import com.klaw.service.caseService.CaseRiskService;
import com.klaw.utils.PageUtils;
import com.klaw.vo.Json;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;

@Service
public class CaseRiskServiceImpl extends ServiceImpl<CaseRiskMapper, CaseRisk> implements CaseRiskService {

    @Autowired
    private CaseRiskMapper caseRiskMapper;

    @Override
    public void saveData(CaseRisk caseRisk) {
        caseRisk.setReportingDate(new Date());
        //caseRiskMapper.insert(caseRisk);
        saveOrUpdate(caseRisk);
    }


    @Override
    public Page<CaseRisk> queryPageData(JSONObject json) {
        QueryWrapper<CaseRisk> wrapper = new QueryWrapper<>();
        getFilter(json, wrapper);
        return page(new PageUtils<>(json), wrapper);
    }


    public void getFilter(JSONObject json, QueryWrapper<CaseRisk> wrapper) {

        // 风险名称
        String riskName = json.containsKey("riskName") ? json.getString("riskName") : null;

        // 风险编号
        String riskCode = json.containsKey("riskCode") ? json.getString("riskCode") : null;

        // 事件性质
        String eventNature = json.containsKey("eventNature") ? json.getString("eventNature") : null;

        // 预计风险等级
        String expectedRiskLevel = json.containsKey("expectedRiskLevel") ? json.getString("expectedRiskLevel") : null;

        // 商业领域
        String businessField = json.containsKey("businessField") ? json.getString("businessField") : null;

        // 内部/外部风险
        String internalExternalRisk = json.containsKey("internalExternalRisk") ? json.getString("internalExternalRisk") : null;

        // 正面/负面影响
        String positiveNegativeImpact = json.containsKey("positiveNegativeImpact") ? json.getString("positiveNegativeImpact") : null;

        // 涉及金额
        BigDecimal involvedAmount = json.containsKey("involvedAmount") ? json.getBigDecimal("involvedAmount") : null;

        // 风险来源
        String riskSource = json.containsKey("riskSource") ? json.getString("riskSource") : null;

        // 风险部门
        String riskDepartment = json.containsKey("riskDepartment") ? json.getString("riskDepartment") : null;

        // 风险描述
        String riskDescription = json.containsKey("riskDescription") ? json.getString("riskDescription") : null;

        // 风险原因
        String riskReason = json.containsKey("riskReason") ? json.getString("riskReason") : null;

        // 潜在后果
        String potentialConsequences = json.containsKey("potentialConsequences") ? json.getString("potentialConsequences") : null;

        // 法律意见
        String legalOpinion = json.containsKey("legalOpinion") ? json.getString("legalOpinion") : null;

        // 相关附件
        String relatedAttachments = json.containsKey("relatedAttachments") ? json.getString("relatedAttachments") : null;

        // 报告单位
        String reportingUnit = json.containsKey("reportingUnit") ? json.getString("reportingUnit") : null;

        // 报告人
        String reportingPerson = json.containsKey("reportingPerson") ? json.getString("reportingPerson") : null;

        // 上报日期(最小值)
        Date reportingDateMin = json.containsKey("reportingDateMin") ? json.getDate("reportingDateMin") : null;
        // 上报日期(最大值)
        Date reportingDateMax = json.containsKey("reportingDateMax") ? json.getDate("reportingDateMax") : null;

        // 审核状态
        String auditStatus = json.containsKey("auditStatus") ? json.getString("auditStatus") : null;

        // 模糊搜索值
        String fuzzyValue = json.containsKey("fuzzyValue") ? json.getString("fuzzyValue") : null;

        // 排序字段
        String sortName = json.containsKey("sortName") ? json.getString("sortName") : null;
        // 排序字段是否升序
        boolean order = json.containsKey("order") ? json.getBoolean("order") : false;

        // 应用查询条件
        if (StringUtils.isNotBlank(riskName)) {
            wrapper.and(i -> i.like("risk_name", riskName));
        }

        if (StringUtils.isNotBlank(riskCode)) {
            wrapper.and(i -> i.like("risk_code", riskCode));
        }

        if (StringUtils.isNotBlank(eventNature)) {
            wrapper.and(i -> i.like("event_nature", eventNature));
        }

        if (StringUtils.isNotBlank(expectedRiskLevel)) {
            wrapper.and(i -> i.like("expected_risk_level", expectedRiskLevel));
        }

        if (StringUtils.isNotBlank(businessField)) {
            wrapper.and(i -> i.eq("business_field", businessField));
        }

        if (StringUtils.isNotBlank(internalExternalRisk)) {
            wrapper.and(i -> i.eq("internal_external_risk", internalExternalRisk));
        }

        if (StringUtils.isNotBlank(positiveNegativeImpact)) {
            wrapper.and(i -> i.eq("positive_negative_impact", positiveNegativeImpact));
        }

        if (involvedAmount != null) {
            // 使用eq方法比较BigDecimal类型的字段
            wrapper.eq("involved_amount", involvedAmount);
        }

        if (StringUtils.isNotBlank(riskSource)) {
            wrapper.and(i -> i.eq("risk_source", riskSource));
        }

        if (StringUtils.isNotBlank(riskDepartment)) {
            wrapper.and(i -> i.like("risk_department", riskDepartment));
        }

        if (StringUtils.isNotBlank(riskDescription)) {
            wrapper.and(i -> i.like("risk_description", riskDescription));
        }

        if (StringUtils.isNotBlank(riskReason)) {
            wrapper.and(i -> i.like("risk_reason", riskReason));
        }

        if (StringUtils.isNotBlank(potentialConsequences)) {
            wrapper.and(i -> i.like("potential_consequences", potentialConsequences));
        }

        if (StringUtils.isNotBlank(legalOpinion)) {
            wrapper.and(i -> i.like("legal_opinion", legalOpinion));
        }

        if (StringUtils.isNotBlank(relatedAttachments)) {
            wrapper.and(i -> i.like("related_attachments", relatedAttachments));
        }

        if (StringUtils.isNotBlank(reportingUnit)) {
            wrapper.and(i -> i.like("reporting_unit", reportingUnit));
        }

        if (StringUtils.isNotBlank(reportingPerson)) {
            wrapper.and(i -> i.like("reporting_person", reportingPerson));
        }

        if (reportingDateMin != null) {
            wrapper.and(i -> i.ge("reporting_date", reportingDateMin));
        }
        if (reportingDateMax != null) {
            wrapper.and(i -> i.le("reporting_date", reportingDateMax));
        }

        if (StringUtils.isNotBlank(auditStatus)) {
            wrapper.and(i -> i.eq("audit_status", auditStatus));
        }

        if (StringUtils.isNotBlank(fuzzyValue)) {
            wrapper.and(i -> i.like("risk_name", fuzzyValue)
                    .or().like("risk_code", fuzzyValue)
                    .or().like("expected_risk_level", fuzzyValue));
        }

        if (StringUtils.isNotBlank(sortName) && order) {
            wrapper.orderByAsc(sortName);
        } else if (StringUtils.isNotBlank(sortName)) {
            wrapper.orderByDesc(sortName);
        }
    }

    @Override
    public CaseRisk queryDataById(String id) {
        CaseRisk caseRisk = getById(id);
        return caseRisk;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Json deleteDataById(String id) {
        boolean flag = removeById(id);
        if (!flag) {
            return Json.fail().msg("未找到需要删除的数据");
        }
        return Json.succ().msg("删除成功!");
    }
}
