<template>
    <FormWindow ref="formWindow" obj-id="main"  @loadData="loadData">
        <el-container v-loading="loading" style="height: calc(100vh - 84px);">
            <el-header
                style="padding-top: 10px;padding-bottom: 10px;border-bottom: solid 1px #f7f7f7;background-color: #FCFCFC">
                <div
                    style="padding: 10px 0 10px 0; position: fixed; width: 100%; overflow-y: auto; background-color: white; z-index: 999">
                    <el-button v-if="!isView" type="primary" size="mini" @click="submit_()">提交</el-button>
                </div>
            </el-header>
            <el-main>
                <el-scrollbar style="height: 100%" wrap-style="overflow-x: hidden;">
                    <el-form :label-position="right" ref="dataForm" :style="'padding-right: 60px;'" :model="mainData"
                        :rules="rules" label-width="110px">
                        <div>
                            <div style="margin: 10px">
                                <span
                                    style="text-align: left; font-size: 23px; margin-left: 43%; font-weight: 900">合规人员库</span>
                            </div>
                            <div v-if="!isView">
                                <span style="font-weight: bold;font-size: 15px;color: #5A5A5F;">基本信息</span>
                                <el-divider />
                                <div>
                                    <br />
                                    <el-row style="margin-top: 10px">
                                        <el-col :span="8">
                                            <el-form-item label="姓名" prop="memberName">
                                                <el-input v-model="mainData.memberName" :disabled="true" show-word-limit
                                                    style="width: 100%" placeholder="请输入..." clearable />
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="8">
                                            <el-form-item label="人员编码" prop="memberCode">
                                                <el-input v-model="mainData.memberCode" :disabled="true" show-word-limit
                                                    style="width: 100%" placeholder="请输入..." clearable />
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="8">
                                            <el-form-item label="性别" prop="memberGender">
                                                <el-input v-model="mainData.memberGender" :disabled="true"
                                                    show-word-limit style="width: 100%" placeholder="请输入..."
                                                    clearable />
                                            </el-form-item>
                                        </el-col>
                                    </el-row>

                                    <el-row style="margin-top: 15px">
                                        <el-col :span="8">
                                            <el-form-item class="el-form-label" label="现工作单位" prop="organizationName">
                                                <el-input v-model="mainData.organizationName" :disabled="true"
                                                    show-word-limit style="width: 100%" placeholder="请输入..."
                                                    clearable />
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="8">
                                            <el-form-item label="岗位/职务" prop="position">
                                                <el-input v-model="mainData.position" :disabled="true" show-word-limit
                                                    style="width: 100%" placeholder="请输入..." clearable />
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="8">
                                            <el-form-item label="所属板块" prop="plate">
                                                <el-input v-model="mainData.plate" maxlength="50" show-word-limit
                                                    style="width: 100%" placeholder="请输入..." clearable />
                                            </el-form-item>
                                        </el-col>
                                    </el-row>

                                    <el-row style="margin-top: 15px">
                                        <el-col :span="8">
                                            <el-form-item label="出生年月" prop="birthDate">
                                                <el-date-picker style="width: 100%;" :disabled="true"
                                                    v-model="mainData.birthDate" value-format="yyyy-MM-dd"
                                                    type="date" />
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="8">
                                            <el-form-item label="参加包钢时间" prop="attendWorkerDate">
                                                <el-date-picker style="width: 100%;" :disabled="true"
                                                    v-model="mainData.attendWorkerDate" value-format="yyyy-MM-dd"
                                                    type="date" />
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="8">
                                            <el-form-item label="现从事专业" prop="professionNow">
                                                <el-input v-model="mainData.professionNow" maxlength="50"
                                                    show-word-limit style="width: 100%" placeholder="请输入..."
                                                    clearable />
                                            </el-form-item>
                                        </el-col>
                                    </el-row>

                                    <el-row style="margin-top: 15px">
                                        <el-col :span="8">
                                            <el-form-item class="el-form-label" label="现有技术资格"
                                                prop="technicalQualification">
                                                <el-input v-model="mainData.technicalQualification" maxlength="50"
                                                    show-word-limit style="width: 100%" placeholder="请输入..."
                                                    clearable />
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="8">
                                            <el-form-item label="启用时间" prop="enableTime">
                                                <el-date-picker style="width: 100%;" v-model="mainData.enableTime"
                                                    value-format="yyyy-MM-dd" type="date" />
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="8">
                                            <el-form-item label="停用时间" prop="disableTime">
                                                <el-date-picker style="width: 100%;" v-model="mainData.disableTime"
                                                    value-format="yyyy-MM-dd" type="date" />
                                            </el-form-item>
                                        </el-col>

                                    </el-row>
                                    <el-row style="margin-top: 15px">
                                        <el-col :span="8">
                                            <el-form-item label="本人手机号" prop="phone">
                                                <el-input v-model="mainData.phone" :disabled="true" show-word-limit
                                                    style="width: 100%" placeholder="请输入..." clearable />
                                            </el-form-item>
                                        </el-col>
                                    </el-row>
                                </div>
                            </div>

                            <div v-else>
                                <SimpleBoardTitle style="margin-top: 5px" title="基本信息">
                                    <table class="table_content" style="margin-top: 10px">
                                        <tbody>
                                            <tr>
                                                <th class="th_label" colspan="3">姓名</th>
                                                <td class="td_value" colspan="6">{{ mainData.memberName }}</td>
                                                <th class="th_label" colspan="3">人员编码</th>
                                                <td class="td_value" colspan="6">{{ mainData.memberCode }}</td>
                                                <th class="th_label" colspan="3">性别</th>
                                                <td class="td_value" colspan="6">{{ mainData.memberGender }}</td>
                                            </tr>
                                            <tr>
                                                <th class="th_label" colspan="3">现工作单位</th>
                                                <td class="td_value" colspan="6">{{ mainData.organizationName }}</td>
                                                <th class="th_label" colspan="3">岗位/职务</th>
                                                <td class="td_value" colspan="6">{{ mainData.position }}</td>
                                                <th class="th_label" colspan="3">所属板块</th>
                                                <td class="td_value" colspan="6">{{ mainData.plate }}</td>
                                            </tr>
                                            <tr>
                                                <th class="th_label" colspan="3">出生年月</th>
                                                <td class="td_value" colspan="6">{{ mainData.birthDate }}</td>
                                                <th class="th_label" colspan="3">参加包钢时间</th>
                                                <td class="td_value" colspan="6">{{ mainData.attendWorkerDate }}</td>
                                                <th class="th_label" colspan="3">现从事专业</th>
                                                <td class="td_value" colspan="6">{{ mainData.professionNow }}</td>
                                            </tr>
                                            <tr>
                                                <th class="th_label" colspan="3">现有技术资格</th>
                                                <td class="td_value" colspan="6">{{ mainData.technicalQualification }}</td>
                                                <th class="th_label" colspan="3">启用时间</th>
                                                <td class="td_value" colspan="6">{{ mainData.enableTime }}</td>
                                                <th class="th_label" colspan="3">停用时间</th>
                                                <td class="td_value" colspan="6">{{ mainData.disableTime }}</td>
                                            </tr>
                                            <tr>
                                                <th class="th_label" colspan="3">本人手机号</th>
                                                <td class="td_value" colspan="24">{{ mainData.phone }}</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </SimpleBoardTitle>
                            </div>
                            <el-row class="rowCol1" style="margin-top: 20px">

                                <el-col :span="12">
                                    <el-form-item label="经办单位">
                                        <span class="viewSpan">{{ mainData.createOgnName }}</span>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="6">
                                    <el-form-item label="经办人">
                                        <span class="viewSpan">{{ mainData.createPsnName }}</span>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="经办部门">
                                        <span class="viewSpan">{{ mainData.createDeptName }}</span>
                                    </el-form-item>
                                </el-col>

                                <el-col :span="6">
                                    <el-form-item label="经办时间">
                                        <span class="viewSpan">{{ mainData.createTime | parseTime }}</span>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                        </div>
                    </el-form>
                </el-scrollbar>
            </el-main>

        </el-container>
    </FormWindow>
</template>
<script>
import FormWindow from '@/view/components/FormWindow/FormWindow'
import ComplianceOrgApi from '@/api/ComplianceOrg/ComplianceOrg.js';
import SimpleBoardTitle from '@/view/components/SimpleBoard/SimpleBoardTitle';
export default {
    name: "HgryDetail",
    inject: ['layout'],
    components: {
        FormWindow, SimpleBoardTitle
    },
    data() {
        return {
            mainData: {
                id: null,
                memberCode: null,//用户编码
                memberName: null,//用户名称
                memberGender: null,//用户性别
                organizationName: null,//先工作单位（部室）
                organization: null,//组织编码
                position: null,//岗位
                birthDate: null,//出生年月
                attendWorkerDate: null,//参加包钢日期
                plate: null,//板块
                professionNow: null,//现从事专业
                technicalQualification: null,//现有专业技术资格
                phone: null,//当前手机号
                enableTime: null,//启用时间
                disableTime: null,//停用时间
                createOgnId: null,
                createOgnName: null,
                createDeptId: null,
                createDeptName: null,
                createPsnId: null,
                createPsnName: null,
                createPsnFullId: null,
                createPsnFullName: null,
                createPsnPhone: null,
                createGroupId: null,
                createGroupName: null,
                createOrgId: null,
                createOrgName: null,
                createLegalUnitId: null,
                createLegalUnitName: null,
                createTime: null,
            },
            dataState: null,
            functionId: null,
        }
    },
    computed: {
        isView: function () {
            return this.dataState === this.utils.formState.VIEW;
        },
    },

    methods: {
        // initData(temp, dataState) {
        //     Object.assign(this.mainData, temp);
        // },
        loadData(dataState, dataId) {
            this.functionId = this.$route.query.functionId
            this.dataState = dataState
            ComplianceOrgApi.queryById(this.$route.query.dataId).then(res => {
                this.mainData = res.data.data
                console.log(this.mainData)
            })
        },
        submit_() {
            this.$refs['dataForm'].validate((valid) => {
                if (valid) {
                    this.$confirm('您确定要提交该数据吗？', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        const that = this;
                        this.mainData.isSubmit = true;
                        ComplianceOrgApi.updateById(this.mainData).then(response => {
                            debugger
                            that.layout.handleTabsEdit(that.functionId, 'remove')
                        })
                    }).catch(() => {
                        this.$message.info('已取消提交')
                    })
                } else {
                    this.$nextTick(function () {
                        document.querySelector('.is-error').scrollIntoView(false)
                    })
                    return false
                }
            })
        },
    }
}
</script>
<style scoped>
.viewSpan {
    font-size: 12px;
    color: #1890ff;
    padding: 0 5px;
    word-wrap: break-word;
}

angle-input {
    border-radius: 0;
    /* 去除默认的圆角 */
    border: 1px solid #ccc;
    /* 设置边框颜色和宽度 */
    padding: 5px;
    /* 设置内边距 */
    /* 其他样式... */
}

.el-form-label {
    text-align: center;
    /* 文本居中 */
    white-space: normal;
    /* 允许换行 */
    word-wrap: break-word;
    /* 允许长单词或URL地址换行 */
}
</style>