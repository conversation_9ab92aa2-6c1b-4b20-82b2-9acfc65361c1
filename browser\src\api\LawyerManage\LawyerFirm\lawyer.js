import {request} from '@/api/index'

export default {
    query(data) {
        return request({
            url: '/lawyer/query',
            method: 'post',
            data
        })
    },
    getDataByFirmId(data) {
        return request({
            url: '/lawyer/getDataByFirmId',
            method: 'post',
            data
        })
    },
    queryOutLawyer(data) {
        return request({
            url: '/lawyer/queryOutLawyer',
            method: 'post',
            data
        })
    },
    queryLedger(data) {
        return request({
            url: '/lawyer/queryLedger',
            method: 'post',
            data
        })
    },
    save(data) {
        return request({
            url: '/lawyer/save',
            method: 'post',
            data
        })
    },
    distinct(data) {
        return request({
            url: '/lawyer/distinct',
            method: 'post',
            data
        })
    },
    delete(data) {
        return request({
            url: '/lawyer',
            method: 'delete',
            data
        })
    },
    queryDataById(data) {
        return request({
            url: '/lawyer/queryDataById',
            method: 'post',
            data
        })
    },
    querySelection(data) {
        return request({
            url: '/lawyer/querySelection',
            method: 'post',
            data
        })
    },
    updateState(data) {
        return request({
            url: '/lawyer/updateState',
            method: 'post',
            data
        })
    },
    deleteById(data) {
        return request({
            url: '/lawyer',
            method: 'delete',
            data
        })
    },
    queryMind(data) {
        return request({
            url: '/lawyer/queryMind',
            method: 'post',
            data
        })
    },
    queryLawyerAxis(data) {
        return request({
            url: '/lawyer/queryLawyerAxis',
            method: 'post',
            data
        })
    },
    queryEvaluateData(data) {
        return request({
            url: '/lawyer/queryEvaluateData',
            method: 'post',
            data
        })
    }
}

