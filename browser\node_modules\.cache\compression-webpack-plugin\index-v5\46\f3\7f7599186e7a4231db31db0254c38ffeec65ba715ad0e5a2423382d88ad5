
30492ef0a139deae67b7d3d527c80251b8658d08	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.234.1754018536329.js\",\"contentHash\":\"ca77709521f688fce7e7f09489573bcd\"}","integrity":"sha512-wXrTaCSzOC9vA9DAeJR4ULQcnQxp+XkVlKkpbEKs7AxLEoHrpOcdyjnR/jYPv1xBs+t9AwsM60OpgQ3OUJ6PEw==","time":1754018576105,"size":241014}