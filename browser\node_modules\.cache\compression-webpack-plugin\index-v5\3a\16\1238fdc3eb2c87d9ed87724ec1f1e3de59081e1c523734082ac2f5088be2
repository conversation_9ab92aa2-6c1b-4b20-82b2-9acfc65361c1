
50e35466bea63b75fb46b0b8c56aed6b07126592	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.242.1754018536329.js\",\"contentHash\":\"f569684bebea4924510c4450e96002fe\"}","integrity":"sha512-ZhbggKDJf1g3gea8KLwUnii70MXFeDd5I9apamEpX9OouJczwmosEk08IKlVoLJ/zNZCr9OblQGNKVA9F9e0xQ==","time":1754018575998,"size":145161}