
08e562e6980d2c108706735eb2abe5790cb101d5	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.243.1754018536329.js\",\"contentHash\":\"4c04c8e36d2f283672a8a13c83d4edef\"}","integrity":"sha512-7mzYSA2eNrjn6LxUNlDa5Wnxg6U+ZP8QE0MKDCpjFL4ILDU+zV8SIe08kEy7mNdIHYx5niW6Nk3qZL4MgNbzeg==","time":1754018575997,"size":143501}