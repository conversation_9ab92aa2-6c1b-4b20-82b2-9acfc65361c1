<template>
  <el-container direction="vertical" class="container-manage-sg">
    <el-header>
      <el-card>
        <div>
          <el-input v-model="tableQuery.fuzzyValue" class="filter_input" placeholder="检索字段（评价对象类型、评价对象、评价年度）" clearable
            @keyup.enter.native="refreshData" @clear="refreshData">
            <el-popover slot="prepend" placement="bottom-start" width="1000" trigger="click">
              <el-form ref="queryForm" label-width="100px" size="mini">
                <el-row>
                  <el-col :span="12">
                    <el-form-item label="评价对象类型">
                      <el-select v-model="tableQuery.evaluationObjectType" clearable placeholder="请选择"
                        style="width: 100%">
                        <el-option v-for="item in utils.compliance_evaluation_type" :key="item.dicName"
                          :label="item.dicName" :value="item.dicName" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="评价对象">
                      <el-input v-model="tableQuery.evaluationObject" clearable placeholder="请输入..." />
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="12">
                    <el-form-item label="评价年度">
                      <el-select v-model="tableQuery.evaluationYear" clearable placeholder="请选择年份" style="width: 100%">
                        <el-option v-for="year in yearOptions" :key="year" :label="year" :value="year" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-button-group style="float: right">
                  <el-button type="primary" size="mini" icon="el-icon-search" @click="search_">搜索</el-button>
                  <el-button type="primary" size="mini" icon="el-icon-refresh-left" @click="empty_">重置</el-button>
                </el-button-group>
              </el-form>
              <el-button slot="reference" size="small" type="primary">高级检索</el-button>
            </el-popover>
            <el-button slot="append" icon="el-icon-search" @click="search_" />
          </el-input>
        </div>

      </el-card>

    </el-header>

    <el-main>
      <SimpleBoardIndex :title="'合规评价通知'" :isDropdown="true" :dropdowns="utils.compliance_evaluation_type"
        @addBtn="add_">
        <el-table ref="table" v-loading="tableLoading" :data="tableData" size="mini" border :height="table_height"
          stripe fit highlight-current-row :show-overflow-tooltip="true" row-key="id"
          style="table-layout: fixed;width: 100%;" @sort-change="tableSort" @row-dblclick="rowDblclick">
          <el-table-column type="index" width="50" label="序号" align="center" />
          <!-- <el-table-column v-if="ss.tableColumns.find(item => item.key === 'code').visible" prop="code"
            show-overflow-tooltip label="编码" min-width="150" /> -->
          <el-table-column v-if="ss.tableColumns.find(item => item.key === 'evaluationObjectType').visible"
            prop="evaluationObjectType" show-overflow-tooltip label="评价对象类型" min-width="150" />
          <el-table-column v-if="ss.tableColumns.find(item => item.key === 'evaluationObject').visible"
            prop="evaluationObject" show-overflow-tooltip label="评价对象" min-width="250" />

          <el-table-column v-if="ss.tableColumns.find(item => item.key === 'plannedStartTime').visible"
            prop="plannedStartTime" show-overflow-tooltip label="计划开始时间" min-width="130" sortable="custom">
            <template slot-scope="scope">
              <span>{{ scope.row.plannedStartTime | parseTime }}</span>
            </template>
          </el-table-column>
          <el-table-column v-if="ss.tableColumns.find(item => item.key === 'plannedEndTime').visible"
            prop="plannedEndTime" show-overflow-tooltip label="计划结束时间" min-width="130" sortable="custom">
            <template slot-scope="scope">
              <span>{{ scope.row.plannedEndTime | parseTime }}</span>
            </template>
          </el-table-column>
          <el-table-column v-if="ss.tableColumns.find(item => item.key === 'evaluatorTime').visible"
            prop="evaluatorTime" show-overflow-tooltip label="评价日期" min-width="100" sortable="custom">
            <template slot-scope="scope">
              <span>{{ scope.row.evaluatorTime | parseTime }}</span>
            </template>
          </el-table-column>
          <el-table-column v-if="ss.tableColumns.find(item => item.key === 'evaluator').visible" show-overflow-tooltip
            prop="evaluator" label="提交人" min-width="80" />
          <el-table-column v-if="ss.tableColumns.find(item => item.key === 'createDeptName').visible"
            show-overflow-tooltip prop="createDeptName" label="提交单位" min-width="250" />
          <el-table-column v-if="ss.tableColumns.find(item => item.key === 'dataState').visible" prop="dataState"
            show-overflow-tooltip label="状态" min-width="100" sortable="custom" />
          <el-table-column label="操作" align="center" width="150" fixed="right">
            <template slot-scope="scope">
              <el-button type="text" v-if="isEdit(scope.row)" @click="edit_(scope.$index, scope.row)">编辑</el-button>
              <el-button type="text" @click="view_(scope.$index, scope.row)">查看</el-button>
              <el-button type="text" v-if="isDelete(scope.row)" @click="delete_(scope.$index, scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </SimpleBoardIndex>

    </el-main>
    <el-footer>
      <!--分页工具栏-->
      <pagination :total="tableQuery.total" :page.sync="tableQuery.page" :limit.sync="tableQuery.limit"
        @pagination="refreshData" />
    </el-footer>

  </el-container>
</template>

<script>
// 组件
import pagination from '@/view/components/Pagination/PaginationIndex'
import TableTools from '@/view/components/TableTools/index'
import SimpleBoardIndex from "@/view/components/SimpleBoard/SimpleBoardIndex";
import SimpleBoard2 from "@/view/components/SimpleBoard/SimpleBoardIndex";
import orgTree from '@/view/components/OrgTree/OrgTree'
// vuex状态值
import { mapGetters } from 'vuex'

// 接口api
import ComplianceEvaluationNotificationSlipApi from '@/api/risk/ComplianceEvaluationNotificationSlip.js'
import taskApi from '@/api/_system/task'

export default {
  name: 'HgpjIndex',
  inject: ["layout"],
  components: { pagination, TableTools, SimpleBoardIndex, orgTree, SimpleBoard2 },
  data() {
    return {
      tableQuery: {
        page: 1,
        limit: 10,
        total: 0,
        evaluationObjectType: null, // 评价对象类型
        evaluationObject: null, // 评价对象
        evaluationYear: null, // 评价年度
        fuzzyValue: null, // 模糊搜索值
        orgId: null, // 组织机构id
      },
      yearOptions: Array.from({ length: 10 }, (_, i) => new Date().getFullYear() - i),
      table_height: '100%',
      tableData: [],
      dialogVisible: false,
      orgTreeDialog: false,
      zxcheckedData: [],
      orgVisible: false,
      tableLoading: false,
      ss: {
        data: this.tableData,
        tableColumns: [
          { key: 'code', label: '编码', visible: true },
          { key: 'evaluationObjectType', label: '评价对象类型', visible: true },
          { key: 'evaluationObject', label: '评价对象', visible: true },
          { key: 'evaluationYear', label: '评价年度', visible: true },
          { key: 'evaluationConclusion', label: '评价结论', visible: true },
          { key: 'evaluatorTime', label: '评价时间', visible: true },
          { key: 'evaluator', label: '提交人', visible: true },
          { key: 'createDeptName', label: '提交单位', visible: true },
          { key: 'plannedStartTime', label: '计划开始时间', visible: true },
          { key: 'plannedEndTime', label: '计划结束时间', visible: true },
          { key: 'dataState', label: '状态', visible: true },
        ]
      },
    }
  },
  computed: {
    ...mapGetters([
      'orgContext', 'currentFunctionId'
    ])
  },
  activated() {
    // 长连接页面第二次激活的时候,不会走created方法,会走此方法
    this.refreshData()
  },
  created() {
    this.refreshData()
  },

  mounted() {
    this.$nextTick(function () {
      this.table_height = window.innerHeight - this.$refs.table.$el.offsetTop - 84 - 45

      // 监听窗口大小变化
      const self = this
      window.onresize = function () {
        self.table_height = window.innerHeight - self.$refs.table.$el.offsetTop - 84 - 45
      }
    })
  },
  methods: {
    isEdit(row) {
      return row.dataStateCode === this.utils.dataState_BPM.SAVE.code || row.dataStateCode === this.utils.dataState_BPM.BACK.code
    },
    isDelete(row) {
      return row.dataStateCode === this.utils.dataState_BPM.SAVE.code
    },
    // 刷新数据
    refreshData() {
      // 赋值当前人组织全路径
      this.tableQuery.functionCode = this.currentFunctionId.functionCode
      this.tableQuery.orgId = this.orgContext.currentOrgId
      this.tableQuery.currentPsnFullId = this.orgContext.currentPsnFullId
      // 主要用于删除,当前页只有一条数据的时候,删除需要回到上一页
      if (this.tableData.length === 0 && this.tableQuery.page > 1) {
        this.tableQuery.page--
      }
      ComplianceEvaluationNotificationSlipApi.query(this.tableQuery).then(response => {
        let rows = response.data.data.records
        this.tableData = rows
        this.ss.data = rows
        this.tableQuery.total = response.data.data.total
        this.tableLoading = false
      }).catch({})
    },
    add_(event) {
      const name = this.utils.getDicName(this.utils.compliance_evaluation_type, event)
      const tabId = this.utils.createUUID();
      this.layout.openNewTab(
        "合规评价通知详情单",
        "hgpjtz_main_detail",
        "hgpjtz_main_detail",
        tabId,
        {
          functionId: "hgpjtz_main_detail," + tabId,
          ...this.utils.routeState.NEW(tabId),
          evaluationObjectType: name
        }
      );
    },
    // 编辑
    edit_(index, row) {

      this.layout.openNewTab(
        "合规评价通知详情单",
        "hgpjtz_main_detail",
        "hgpjtz_main_detail",
        row.id,
        {
          functionId: "hgpjtz_main_detail," + row.id,
          ...this.utils.routeState.EDIT(row.id),
          view: 'old'
        }
      );
    },
    // 删除
    delete_(index, row) {
      this.$confirm('此操作将永久删除该数据及相关数据, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        new Promise((resolve, reject) => {
          ComplianceEvaluationNotificationSlipApi.deletebyid({
            id: row.id
          }).then((response) => {
            resolve(response)
          })
        }).then(value => {
          this.tableData.splice(index, 1)
          this.$message.success('删除成功!')
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },
    // 查看
    view_(index, row) {
      if (row.dataStateCode == this.utils.dataState_BPM.SAVE.code) {
        const tabId = this.utils.createUUID();
        this.layout.openNewTab(
          "合规评价详情单",
          "hgpjtz_main_detail",
          "hgpjtz_main_detail",
          tabId,
          {
            functionId: "hgpjtz_main_detail," + tabId,
            ...this.utils.routeState.VIEW(row.id)
          }
        )
      }
      else {
        taskApi.selectTaskId({ businessKey: row.id, isView: 'true' }).then(res => {
          const functionId = res.data.data[0].ID
          const tabId = this.utils.createUUID()
          this.layout.openNewTab("合规评价审批信息",
            "design_page",
            "design_page",
            tabId,
            {
              processInstanceId: res.data.data[0].PID,//流程实例
              taskId: res.data.data[0].ID,//任务ID
              businessKey: row.id, //业务数据ID
              functionId: functionId, //不传也可以，只有首环节需要传，实例启动就可以不传了
              entranceType: "FLOWABLE",
              type: "haveDealt",
              channel: 'business', //查看渠道，默认就传这个值即可，表示是从业务功能传过来的，首环节提交后过滤掉撤回按钮
              view: 'new',
            }
          )
        })
      }
    },
    rowDblclick(row, column, event) {
      this.$emit('rowDblclick', row);
      if (row.dataStateCode === this.utils.dataState_BPM.SAVE.code) {
        const tabId = this.utils.createUUID();
        this.layout.openNewTab(
          "合规评价审批信息",
          "hgpjtz_main_detail",
          "hgpjtz_main_detail",
          tabId,
          {
            functionId: "contract_approval_main_detail," + tabId,
            ...this.utils.routeState.VIEW(row.id)
          }
        )
      }
      else {
        taskApi.selectTaskId({ businessKey: row.id, isView: 'true' }).then(res => {
          const functionId = res.data.data[0].ID
          const tabId = this.utils.createUUID()
          this.layout.openNewTab("合规评价审批信息",
            "design_page",
            "design_page",
            tabId,
            {
              processInstanceId: res.data.data[0].PID,//流程实例
              taskId: res.data.data[0].ID,//任务ID
              businessKey: row.id, //业务数据ID
              functionId: functionId, //不传也可以，只有首环节需要传，实例启动就可以不传了
              entranceType: "FLOWABLE",
              type: "haveDealt",
              channel: 'business', //查看渠道，默认就传这个值即可，表示是从业务功能传过来的，首环节提交后过滤掉撤回按钮
              view: 'new',
            }
          )
        })
      }
    },
    tableSort(column, prop, order) {
      this.tableQuery.sortName = column.prop
      this.tableQuery.order = column.order === "ascending"
      this.refreshData()
    },
    // 点击搜索按钮事件,回到第一页,重新刷新数据
    search_: function () {
      this.tableQuery.page = 1
      this.refreshData()
    },
    // 点击清空按钮事件,清空查询条件属性,回到第一页,重新刷新数据
    empty_() {
      // 清空搜索条件
      this.tableQuery = {
        page: 1,
        limit: 10,
        total: 0,
        evaluationObjectType: '',
        evaluationObject: '',
        evaluationYear: '',
      };
      this.refreshData();
    },
    // 点击刷新按钮事件
    refresh_() {
      this.tableQuery.sortName = null
      this.tableQuery.order = null
      this.empty_()
    },
    showOrgTreeDialog() {
      this.dialogVisible = true;
    },
    cancel() {
      this.dialogVisible = false
    },
    choiceDeptSure() {
      let selectedUnits = this.zxcheckedData.map(item => item.name).join(', ');
      this.tableQuery.reportingUnit = selectedUnits;
      this.dialogVisible = false;
    },
  }
}
</script>

<style scoped>
.el-table__fixed-body-wrapper {
  top: 50px !important;
}
</style>
