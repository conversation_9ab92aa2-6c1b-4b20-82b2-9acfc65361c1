
cea29fd68fa21e957b510c11058e864e1bd3fcff	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.387.1754018536329.js\",\"contentHash\":\"728dec50fa36222fed4976b7c61ba006\"}","integrity":"sha512-63u4g9gvLB/63I7dPA/TzN0wK+MKd6GLMZd4/UZUEucysh+xUyYKr1x9AuXdOGXRSz5ghsYwdSau7m/u0MNK8A==","time":1754018576026,"size":169605}