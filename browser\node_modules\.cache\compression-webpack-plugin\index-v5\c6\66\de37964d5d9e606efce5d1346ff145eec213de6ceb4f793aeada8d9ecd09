
ca350a7a6ecdf6b821b6f69161254b51157b41aa	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.249.1754018536329.js\",\"contentHash\":\"4c0b902904ea0ea14ae5fa15e1e15901\"}","integrity":"sha512-ZCd9gMqXNsfOfVEm7KPE5rZZg1mK+o3r8JNZ0JQyOfHHCMf3rZGM5kI0peP67U7FJ0QJiyu5q6DEXxyT5/8OYg==","time":1754018576000,"size":129054}