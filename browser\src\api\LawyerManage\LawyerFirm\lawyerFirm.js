import {request} from '@/api/index'

export default {
    query(data) {
        return request({
            url: '/lawFirm/query',
            method: 'post',
            data
        })
    },
    queryPrivate(data) {
        return request({
            url: '/lawFirm/queryPrivate',
            method: 'post',
            data
        })
    },
    queryPublic(data) {
        return request({
            url: '/lawFirm/queryPublic',
            method: 'post',
            data
        })
    },
    querySub(data) {
        return request({
            url: '/lawFirm/querySub',
            method: 'post',
            data
        })
    },
    save(data) {
        return request({
            url: '/lawFirm/save',
            method: 'post',
            data
        })
    },
    queryDataById(data) {
        return request({
            url: '/lawFirm/queryDataById',
            method: 'post',
            data
        })
    },
    deleteData(data) {
        return request({
            url: '/lawFirm',
            method: 'delete',
            data
        })
    },
    queryLawFirmAxis(data) {
        return request({
            url: '/lawFirm/queryLawFirmAxis',
            method: 'post',
            data
        })
    },
    queryHistory(data) {
        return request({
            url: '/lawFirm/query_history',
            method: 'post',
            data
        })
    },

    exportLawFirmData(data) {
        return request({
            url: '/lawFirm/exportLawFirmData',
            method: 'post',
            data,
            responseType: 'blob'
        })
    },

    isBlackList(data) {
        return request({
            url: '/lawFirm/isBlackList',
            method: 'post',
            data
        })
    },
    distinct(data) {
        return request({
            url: '/lawFirm/distinct',
            method: 'post',
            data
        })
    },
    isRecommend(data) {
        return request({
            url: '/lawFirm/isRecommend',
            method: 'post',
            data
        })
    },
    getExistLawyerList(data) {
        return request({
            url: '/lawFirm/getExistLawyerList',
            method: 'post',
            data
        })
    },
    setParam(data) {
        return request({
            url: '/lawFirm/setParam',
            method: 'post',
            data
        })
    },
    queryForSelect(data) {
        return request({
            url: '/lawFirm/queryForSelect',
            method: 'post',
            data
        })
    },
    queryForEvaluate(data) {
        return request({
            url: '/lawFirm/queryForEvaluate',
            method: 'post',
            data
        })
    },
    queryOutFirmDialog(data) {
        return request({
            url: '/lawFirm/queryOutFirmDialog',
            method: 'post',
            data
        })
    },
    queryBlackFirmDialog(data) {
        return request({
            url: '/lawFirm/queryBlackFirmDialog',
            method: 'post',
            data
        })
    },
    queryLedgerFirmDialog(data) {
        return request({
            url: '/lawFirm/queryLedgerFirmDialog',
            method: 'post',
            data
        })
    },
}
