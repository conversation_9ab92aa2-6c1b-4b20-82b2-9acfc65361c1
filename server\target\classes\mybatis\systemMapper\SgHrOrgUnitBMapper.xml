<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.klaw.dao.systemDao.SgHrOrgUnitBMapper">
    <resultMap id="BaseResultMap" type="com.klaw.entity.systemBean.SgHrOrgUnitB">
        <id column="UNIT_ID" jdbcType="DECIMAL" property="unitId"/>
        <result column="PARENT_ID" jdbcType="DECIMAL" property="parentId"/>
        <result column="UNIT_CODE" jdbcType="VARCHAR" property="unitCode"/>
        <result column="NAME" jdbcType="VARCHAR" property="name"/>
        <result column="DESCRIPTION" jdbcType="VARCHAR" property="description"/>
        <result column="MANAGER_POSITION" jdbcType="DECIMAL" property="managerPosition"/>
        <result column="COMPANY_ID" jdbcType="DECIMAL" property="companyId"/>
        <result column="ENABLED_FLAG" jdbcType="VARCHAR" property="enabledFlag"/>
        <result column="UNIT_CATEGORY" jdbcType="VARCHAR" property="unitCategory"/>
        <result column="UNIT_TYPE" jdbcType="VARCHAR" property="unitType"/>
        <result column="UNIT_LEVEL" jdbcType="DECIMAL" property="unitLevel"/>
        <result column="UNITSEQ" jdbcType="CLOB" property="unitseq"/>
        <result column="ISLEAF" jdbcType="VARCHAR" property="isleaf"/>
        <result column="CREATED_BY" jdbcType="DECIMAL" property="createdBy"/>
        <result column="CREATION_DATE" jdbcType="TIMESTAMP" property="creationDate"/>
        <result column="LAST_UPDATED_BY" jdbcType="DECIMAL" property="lastUpdatedBy"/>
        <result column="LAST_UPDATE_DATE" jdbcType="TIMESTAMP" property="lastUpdateDate"/>
        <result column="OBJECT_VERSION_NUMBER" jdbcType="DECIMAL" property="objectVersionNumber"/>
    </resultMap>
    <sql id="Base_Column_List">
        UNIT_ID, PARENT_ID, UNIT_CODE, NAME, DESCRIPTION, MANAGER_POSITION, COMPANY_ID,
    ENABLED_FLAG, UNIT_CATEGORY, UNIT_TYPE, UNIT_LEVEL, UNITSEQ, ISLEAF, CREATED_BY, 
    CREATION_DATE, LAST_UPDATED_BY, LAST_UPDATE_DATE, OBJECT_VERSION_NUMBER
    </sql>

    <select id="queryOrg" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        FROM HR_ORG_UNIT_B
        <where>
            <if test="parentId != null">
                ${parentId}
            </if>
        </where>
    </select>

    <resultMap id="UserResultMap" type="com.klaw.entity.systemBean.SysUsers">
        <id column="USER_ID" jdbcType="DECIMAL" property="userId"/>
        <result column="USER_TYPE" jdbcType="VARCHAR" property="userType"/>
        <result column="USER_NAME" jdbcType="VARCHAR" property="userName"/>
        <result column="PASSWORD_ENCRYPTED" jdbcType="VARCHAR" property="passwordEncrypted"/>
        <result column="EMAIL" jdbcType="VARCHAR" property="email"/>
        <result column="PHONE" jdbcType="VARCHAR" property="phone"/>
        <result column="START_ACTIVE_DATE" jdbcType="TIMESTAMP" property="startActiveDate"/>
        <result column="END_ACTIVE_DATE" jdbcType="TIMESTAMP" property="endActiveDate"/>
        <result column="STATUS" jdbcType="VARCHAR" property="status"/>
        <result column="LAST_LOGIN_DATE" jdbcType="TIMESTAMP" property="lastLoginDate"/>
        <result column="LAST_PASSWORD_UPDATE_DATE" jdbcType="TIMESTAMP" property="lastPasswordUpdateDate"/>
        <result column="FIRST_LOGIN" jdbcType="VARCHAR" property="firstLogin"/>
        <result column="DESCRIPTION" jdbcType="VARCHAR" property="description"/>
        <result column="CREATED_BY" jdbcType="DECIMAL" property="createdBy"/>
        <result column="CREATION_DATE" jdbcType="TIMESTAMP" property="creationDate"/>
        <result column="LAST_UPDATED_BY" jdbcType="DECIMAL" property="lastUpdatedBy"/>
        <result column="LAST_UPDATE_DATE" jdbcType="TIMESTAMP" property="lastUpdateDate"/>
        <result column="OBJECT_VERSION_NUMBER" jdbcType="DECIMAL" property="objectVersionNumber"/>
        <result column="FULL_NAME" jdbcType="VARCHAR" property="fullName"/>
        <result column="POST_ID" jdbcType="DECIMAL" property="postId"/>
        <result column="ENTRY_TIME" jdbcType="TIMESTAMP" property="entryTime"/>
        <result column="BIRTH_DATE" jdbcType="TIMESTAMP" property="birthDate"/>
        <result column="LEAVE_DATE" jdbcType="TIMESTAMP" property="leaveDate"/>
        <result column="CERTIFICATE_NUMBER" jdbcType="VARCHAR" property="certificateNumber"/>
        <result column="CERTIFICATE_TYPE" jdbcType="VARCHAR" property="certificateType"/>
        <result column="EMPLOYEE_STATUS" jdbcType="VARCHAR" property="employeeStatus"/>
        <result column="EMPLOYEE_SEX" jdbcType="VARCHAR" property="employeeSex"/>
        <result column="EMPLOYEE_NUMBER" jdbcType="VARCHAR" property="employeeNumber"/>
    </resultMap>
    <sql id="user_Column_List">
        u.USER_ID,u.USER_TYPE,u.USER_NAME,u.PASSWORD_ENCRYPTED,u.EMAIL,u.PHONE,u.START_ACTIVE_DATE,
    u.END_ACTIVE_DATE,u.`STATUS`,u.LAST_LOGIN_DATE,u.LAST_PASSWORD_UPDATE_DATE,u.FIRST_LOGIN,
    u.DESCRIPTION,u.CREATED_BY,u.CREATION_DATE,u.LAST_UPDATED_BY,u.LAST_UPDATE_DATE,u.OBJECT_VERSION_NUMBER,
    u.FULL_NAME,u.POST_ID,u.ENTRY_TIME,u.BIRTH_DATE,u.LEAVE_DATE,u.CERTIFICATE_NUMBER,u.CERTIFICATE_TYPE,
    u.EMPLOYEE_STATUS,u.EMPLOYEE_SEX,u.EMPLOYEE_NUMBER
    </sql>

    <select id="queryUserByOrg" resultMap="UserResultMap">
        select
        <include refid="user_Column_List"/>
        from sys_user u join hr_user_unit h
        on u.USER_ID = h.USER_ID
        join HR_ORG_UNIT_B o
        on h.UNIT_ID = o.UNIT_ID
        and o.UNIT_ID=#{unitId}
    </select>

    <select id="selectOrgUnitsByCodes" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        hr_org_unit_b b join main_mid_staff_unit_org munit on b.UNIT_CODE = munit.code
        where munit.code in
        <foreach item="code" collection="codes"
                 open="(" separator="," close=")">
            #{code}
        </foreach>
        and munit.CODE_TYPE = '1' and ENABLED_FLAG = 'Y'
    </select>

    <select id="queryOrgByRole" resultMap="BaseResultMap">
        select h.*
        from HR_ORG_UNIT_B h
                 join HR_USER_UNIT hu on h.UNIT_ID = hu.UNIT_ID
                 join SYS_USER u on u.USER_ID = hu.USER_ID
                 join SYS_USER_ROLE m on u.USER_ID = m.USER_ID
                 join SYS_ROLE_B r on m.ROLE_ID = r.ROLE_ID
        where r.ROLE_CODE = #{roleCode}
    </select>

    <select id="queryLeaderList" resultMap="BaseResultMap">
        select
        A.*
        from
        HR_ORG_UNIT_B A
        join HR_USER_UNIT B
        on
        A.UNIT_ID =B.UNIT_ID
        and A.PARENT_ID = 20001385
        and A.UNIT_TYPE = 'psn'
        join SYS_USER C
        on
        C.USER_ID = B.USER_ID
        <where>
            ${ew.sqlSegment}
        </where>
        order by
        A.SORT_CODE
    </select>

    <select id="getOrgUnitByUnitIds" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM HR_ORG_UNIT_B HOUB
        WHERE 1=1
        <if test="ids != null and ids.size()>0">
            AND HOUB.UNIT_ID IN
            <foreach collection="ids" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>
</mapper>