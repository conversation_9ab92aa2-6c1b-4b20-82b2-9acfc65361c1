
85c55542d45818fef114b43162258b1f2346280a	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.454.1754018536329.js\",\"contentHash\":\"03d9def6b1f0d9e5edd6fc3c5501a0d9\"}","integrity":"sha512-uYxcPjEQIqWcLFY27Dy5ZbnkFOz82Syykz3jHq6NXNYcgZmuBNadhmMbKCudhgyg0eIhfQkbwvQBuQCEfNwccg==","time":1754018575977,"size":101851}