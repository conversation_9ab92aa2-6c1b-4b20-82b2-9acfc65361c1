
a2aae1f24e34a2570190f35cebfdb4a40850084e	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.90.1754018536329.js\",\"contentHash\":\"4ee5e3bd571619b8d522bc6e4144e090\"}","integrity":"sha512-OIBnGjJ8Axv7PLXO5cup03cqhJio5AUNTHxyX1sbbO/Ve0s8weOjhK+qeKiiJoPNMAkujEhPTYFfHEi9HKemCw==","time":1754018575956,"size":44635}