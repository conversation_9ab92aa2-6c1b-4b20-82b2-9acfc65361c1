package com.klaw.service.imp.caseServiceImpl;



import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.klaw.dao.caseDao.CaseBigdataMapper;
import com.klaw.entity.caseBean.CaseBigdata;
import com.klaw.service.caseService.CaseBigdataService;
import org.springframework.stereotype.Service;


@Service
public class CaseBigdataServiceImpl extends ServiceImpl<CaseBigdataMapper, CaseBigdata> implements CaseBigdataService {
    /**
     * 保存数据
     * @param caseBigdata
     */
    @Override
    public boolean saveData(CaseBigdata caseBigdata) {
        //插入或更新群诉案件数据
        return saveOrUpdate(caseBigdata);
    }

    /**
     * 根据id查询是否存在对应的数据
     */
    @Override
    public CaseBigdata selectReport(String id) {
        return getById(id);
    }
}
