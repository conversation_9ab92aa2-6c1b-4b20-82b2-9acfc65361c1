
f74778699ca3c4ce8b78211f5f2eb6f476767270	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.80.1754018536329.js\",\"contentHash\":\"160ea819e4bb165db8f3230ea39a3c42\"}","integrity":"sha512-a7V/qy9oj3h2Fs7Owumzos8o5LeiV0GR8GDMb4axyeJocBxcKukr0mqZbc+SXpmASWUTJoh8bUGNhHAKNqO8bg==","time":1754018575959,"size":68956}