
2dfb8d9477eaf3b5dcc276096724d2a2107c2b6e	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.354.1754018536329.js\",\"contentHash\":\"99835ee965f989720f49f6e6b47ecbfa\"}","integrity":"sha512-tW/rH0/pI4wN7m8P0ZO1DKK7RNFArzaa4JGHOIES1oCVHjKfobCuY53Zuh+A8FZYMmy93c/GoVBs6TmRf1beWA==","time":1754018575975,"size":104861}