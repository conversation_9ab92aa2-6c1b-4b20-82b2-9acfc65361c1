
6187cd5b3076bc1dc4b0054ce1f1c8b614f3c56e	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.48.1754018536329.js\",\"contentHash\":\"463d15812f57a7753edbc5b34f6180a2\"}","integrity":"sha512-R/DeB1v6DHliQmCcbyOftdYX/8McZ9zALLXzdPuam+hVIP5ReLDRSg6Hg7Jc5c408YxEZWRcyVEpnAHgEw2tYA==","time":1754018575958,"size":110365}