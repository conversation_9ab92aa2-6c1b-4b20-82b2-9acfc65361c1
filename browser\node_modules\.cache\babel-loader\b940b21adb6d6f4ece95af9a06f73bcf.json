{"remainingRequest": "D:\\Desktop\\XM\\FW\\baogang\\browser\\node_modules\\babel-loader\\lib\\index.js!D:\\Desktop\\XM\\FW\\baogang\\browser\\src\\js_public\\axios.js", "dependencies": [{"path": "D:\\Desktop\\XM\\FW\\baogang\\browser\\src\\js_public\\axios.js", "mtime": 1754018825288}, {"path": "D:\\Desktop\\XM\\FW\\baogang\\browser\\babel.config.js", "mtime": 1744958013967}, {"path": "D:\\Desktop\\XM\\FW\\baogang\\browser\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Desktop\\XM\\FW\\baogang\\browser\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}], "contextDependencies": [], "result": ["import axios from 'axios';\nimport { MessageBox, Message } from 'element-ui';\nimport router from '../router';\nimport Vuex from '../store';\nimport rootPath from '../router/rootPath';\nimport cookie from 'js-cookie';\n// axios 配置\naxios.defaults.timeout = 600000;\naxios.defaults.headers.post['Content-Type'] = 'application/json';\nvar baseUrl = localStorage.getItem('axiosBaseUrl');\n/* 公用请求根路径 */\naxios.defaults.baseURL = baseUrl;\naxios.defaults.withCredentials = true;\naxios.interceptors.request.use(function (config) {\n  if (['post', 'get', 'put'].includes(config.method)) {\n    if (config.url !== \"\".concat(baseUrl, \"/login\")) {\n      config.headers['X-CSRF-TOKEN'] = localStorage.getItem('CSRF_TOKEN');\n      config.headers['X-Requested-With'] = 'XMLHttpRequest';\n    }\n  } else if (config.method === 'delete') {\n    config.headers['X-CSRF-TOKEN'] = localStorage.getItem('CSRF_TOKEN');\n  }\n  return config;\n}, function (error) {\n  return 'request error :' + error;\n});\n\n//返回状态判断\naxios.interceptors.response.use(function (res) {\n  if (res.data.code == \"sys_session_timeout\") {\n    if (res.config.url.split('/')[2] != 'logout' && res.config.url.split('/')[2] != 'loginInfo' && res.config.url.split('/')[2] != 'getToken') {\n      Vuex.commit('C_TIMEOUT_TYPE');\n      if (Vuex.state.timeOutType <= 1) {\n        MessageBox.alert('登录超时，请重新登陆。', '超时', {\n          confirmButtonText: '确定',\n          callback: function callback(action) {\n            localStorage.removeItem('CSRF_TOKEN');\n            router.push('login');\n          },\n          showClose: false\n        });\n      }\n    }\n  } else {\n    Vuex.commit('C_TIMEOUT_TYPE', 0);\n  }\n  if (res.data.code == 403) {\n    Message.error('账户无此权限');\n  }\n  return res;\n}, function (error) {\n  var _error$response, _error$response$data, _error$response2, _error$response2$data;\n  var message = error === null || error === void 0 ? void 0 : (_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message;\n  if (message === 'Forbidden') {\n    return;\n  }\n  if (error !== null && error !== void 0 && (_error$response2 = error.response) !== null && _error$response2 !== void 0 && (_error$response2$data = _error$response2.data) !== null && _error$response2$data !== void 0 && _error$response2$data.path && error.response.data.path.includes('getToken')) {\n    //登录状态下在给提示\n    return false;\n  }\n  if (error.response.status === 404) {\n    Message.error(message || '请求地址错误. 错误码:404;');\n  }\n  if (error.response.status === 403) {\n    Message.error(message || '无权访问,请管理员授权');\n  }\n  if (error.response.status === 500) {\n    Message.error(message || '请求地址错误. 错误码:500;');\n  }\n  return error;\n});\nexport default axios;", {"version": 3, "names": ["axios", "MessageBox", "Message", "router", "Vuex", "rootPath", "cookie", "defaults", "timeout", "headers", "post", "baseUrl", "localStorage", "getItem", "baseURL", "withCredentials", "interceptors", "request", "use", "config", "includes", "method", "url", "concat", "error", "response", "res", "data", "code", "split", "commit", "state", "timeOutType", "alert", "confirmButtonText", "callback", "action", "removeItem", "push", "showClose", "_error$response", "_error$response$data", "_error$response2", "_error$response2$data", "message", "path", "status"], "sources": ["D:/Desktop/XM/FW/baogang/browser/src/js_public/axios.js"], "sourcesContent": ["\nimport axios from 'axios'\nimport { MessageBox,Message } from 'element-ui';\nimport router from '../router'\nimport Vuex from '../store'\nimport rootPath from '../router/rootPath'\nimport cookie from 'js-cookie'\n// axios 配置\naxios.defaults.timeout = 600000;\naxios.defaults.headers.post['Content-Type'] = 'application/json';\nlet baseUrl = localStorage.getItem('axiosBaseUrl')\n/* 公用请求根路径 */\naxios.defaults.baseURL = baseUrl\n\naxios.defaults.withCredentials = true;\n\naxios.interceptors.request.use((config) => {\n    if(['post','get','put'].includes(config.method)){\n        if(config.url !== `${baseUrl}/login`){\n            config.headers['X-CSRF-TOKEN'] = localStorage.getItem('CSRF_TOKEN')\n            config.headers['X-Requested-With'] = 'XMLHttpRequest'\n        }\n    }else if (config.method === 'delete'){\n\t    config.headers['X-CSRF-TOKEN'] = localStorage.getItem('CSRF_TOKEN')\n    }\n    return config;\n},(error) =>{\n\n    return 'request error :' + error\n});\n\n//返回状态判断\naxios.interceptors.response.use((res) =>{\n    if (res.data.code == \"sys_session_timeout\") {\n    \tif(res.config.url.split('/')[2] != 'logout' && res.config.url.split('/')[2] != 'loginInfo' && res.config.url.split('/')[2] != 'getToken'){\n            Vuex.commit('C_TIMEOUT_TYPE')\n            if(Vuex.state.timeOutType <= 1){\n                MessageBox.alert('登录超时，请重新登陆。', '超时', {\n                    confirmButtonText: '确定',\n                    callback: action => {\n                        localStorage.removeItem('CSRF_TOKEN')\n                        router.push('login')\n                    },\n                    showClose : false\n                });\n            }\n        }\n    }else {\n        Vuex.commit('C_TIMEOUT_TYPE',0)\n    }\n    if(res.data.code == 403){\n        Message.error('账户无此权限')\n    }\n    return res\n\n}, (error) => {\n    let message = error?.response?.data?.message\n    if(message === 'Forbidden'){\n        return\n    }\n    if(error?.response?.data?.path&&error.response.data.path.includes('getToken')){//登录状态下在给提示\n        return false\n    }\n    if(error.response.status === 404){\n        Message.error(message||'请求地址错误. 错误码:404;')\n    }\n    if(error.response.status === 403){\n        Message.error(message||'无权访问,请管理员授权')\n    }\n    if(error.response.status === 500){\n        Message.error(message||'请求地址错误. 错误码:500;')\n    }\n    return error\n});\n\nexport default axios"], "mappings": "AACA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,UAAU,EAACC,OAAO,QAAQ,YAAY;AAC/C,OAAOC,MAAM,MAAM,WAAW;AAC9B,OAAOC,IAAI,MAAM,UAAU;AAC3B,OAAOC,QAAQ,MAAM,oBAAoB;AACzC,OAAOC,MAAM,MAAM,WAAW;AAC9B;AACAN,KAAK,CAACO,QAAQ,CAACC,OAAO,GAAG,MAAM;AAC/BR,KAAK,CAACO,QAAQ,CAACE,OAAO,CAACC,IAAI,CAAC,cAAc,CAAC,GAAG,kBAAkB;AAChE,IAAIC,OAAO,GAAGC,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC;AAClD;AACAb,KAAK,CAACO,QAAQ,CAACO,OAAO,GAAGH,OAAO;AAEhCX,KAAK,CAACO,QAAQ,CAACQ,eAAe,GAAG,IAAI;AAErCf,KAAK,CAACgB,YAAY,CAACC,OAAO,CAACC,GAAG,CAAC,UAACC,MAAM,EAAK;EACvC,IAAG,CAAC,MAAM,EAAC,KAAK,EAAC,KAAK,CAAC,CAACC,QAAQ,CAACD,MAAM,CAACE,MAAM,CAAC,EAAC;IAC5C,IAAGF,MAAM,CAACG,GAAG,QAAAC,MAAA,CAAQZ,OAAO,WAAQ,EAAC;MACjCQ,MAAM,CAACV,OAAO,CAAC,cAAc,CAAC,GAAGG,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;MACnEM,MAAM,CAACV,OAAO,CAAC,kBAAkB,CAAC,GAAG,gBAAgB;IACzD;EACJ,CAAC,MAAK,IAAIU,MAAM,CAACE,MAAM,KAAK,QAAQ,EAAC;IACpCF,MAAM,CAACV,OAAO,CAAC,cAAc,CAAC,GAAGG,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;EACpE;EACA,OAAOM,MAAM;AACjB,CAAC,EAAC,UAACK,KAAK,EAAI;EAER,OAAO,iBAAiB,GAAGA,KAAK;AACpC,CAAC,CAAC;;AAEF;AACAxB,KAAK,CAACgB,YAAY,CAACS,QAAQ,CAACP,GAAG,CAAC,UAACQ,GAAG,EAAI;EACpC,IAAIA,GAAG,CAACC,IAAI,CAACC,IAAI,IAAI,qBAAqB,EAAE;IAC3C,IAAGF,GAAG,CAACP,MAAM,CAACG,GAAG,CAACO,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,QAAQ,IAAIH,GAAG,CAACP,MAAM,CAACG,GAAG,CAACO,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,WAAW,IAAIH,GAAG,CAACP,MAAM,CAACG,GAAG,CAACO,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,UAAU,EAAC;MAClIzB,IAAI,CAAC0B,MAAM,CAAC,gBAAgB,CAAC;MAC7B,IAAG1B,IAAI,CAAC2B,KAAK,CAACC,WAAW,IAAI,CAAC,EAAC;QAC3B/B,UAAU,CAACgC,KAAK,CAAC,aAAa,EAAE,IAAI,EAAE;UAClCC,iBAAiB,EAAE,IAAI;UACvBC,QAAQ,EAAE,SAAAA,SAAAC,MAAM,EAAI;YAChBxB,YAAY,CAACyB,UAAU,CAAC,YAAY,CAAC;YACrClC,MAAM,CAACmC,IAAI,CAAC,OAAO,CAAC;UACxB,CAAC;UACDC,SAAS,EAAG;QAChB,CAAC,CAAC;MACN;IACJ;EACJ,CAAC,MAAK;IACFnC,IAAI,CAAC0B,MAAM,CAAC,gBAAgB,EAAC,CAAC,CAAC;EACnC;EACA,IAAGJ,GAAG,CAACC,IAAI,CAACC,IAAI,IAAI,GAAG,EAAC;IACpB1B,OAAO,CAACsB,KAAK,CAAC,QAAQ,CAAC;EAC3B;EACA,OAAOE,GAAG;AAEd,CAAC,EAAE,UAACF,KAAK,EAAK;EAAA,IAAAgB,eAAA,EAAAC,oBAAA,EAAAC,gBAAA,EAAAC,qBAAA;EACV,IAAIC,OAAO,GAAGpB,KAAK,aAALA,KAAK,wBAAAgB,eAAA,GAALhB,KAAK,CAAEC,QAAQ,cAAAe,eAAA,wBAAAC,oBAAA,GAAfD,eAAA,CAAiBb,IAAI,cAAAc,oBAAA,uBAArBA,oBAAA,CAAuBG,OAAO;EAC5C,IAAGA,OAAO,KAAK,WAAW,EAAC;IACvB;EACJ;EACA,IAAGpB,KAAK,aAALA,KAAK,gBAAAkB,gBAAA,GAALlB,KAAK,CAAEC,QAAQ,cAAAiB,gBAAA,gBAAAC,qBAAA,GAAfD,gBAAA,CAAiBf,IAAI,cAAAgB,qBAAA,eAArBA,qBAAA,CAAuBE,IAAI,IAAErB,KAAK,CAACC,QAAQ,CAACE,IAAI,CAACkB,IAAI,CAACzB,QAAQ,CAAC,UAAU,CAAC,EAAC;IAAC;IAC3E,OAAO,KAAK;EAChB;EACA,IAAGI,KAAK,CAACC,QAAQ,CAACqB,MAAM,KAAK,GAAG,EAAC;IAC7B5C,OAAO,CAACsB,KAAK,CAACoB,OAAO,IAAE,kBAAkB,CAAC;EAC9C;EACA,IAAGpB,KAAK,CAACC,QAAQ,CAACqB,MAAM,KAAK,GAAG,EAAC;IAC7B5C,OAAO,CAACsB,KAAK,CAACoB,OAAO,IAAE,aAAa,CAAC;EACzC;EACA,IAAGpB,KAAK,CAACC,QAAQ,CAACqB,MAAM,KAAK,GAAG,EAAC;IAC7B5C,OAAO,CAACsB,KAAK,CAACoB,OAAO,IAAE,kBAAkB,CAAC;EAC9C;EACA,OAAOpB,KAAK;AAChB,CAAC,CAAC;AAEF,eAAexB,KAAK"}]}