
3b7785b865ede1bc0f7154ca60c39ae067a3487c	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.462.1754018536329.js\",\"contentHash\":\"1fb431095db17a7c04a7554ac3d4ab70\"}","integrity":"sha512-YWwnAxt9hJOS3Yv6LwPEXFAvIYW1P5FHAhh1GT/OeGq9u3OeQTejsgy2OWn6PTxk83DMJQeZSdsgBDYHfQhmow==","time":1754018575957,"size":18274}