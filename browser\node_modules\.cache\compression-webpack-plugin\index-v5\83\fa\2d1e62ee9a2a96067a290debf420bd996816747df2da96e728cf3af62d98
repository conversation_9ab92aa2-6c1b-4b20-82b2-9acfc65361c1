
a1601d95f5046f6876b2a552f449e9c4d385c886	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.157.1754018536329.js\",\"contentHash\":\"01de77ebd50d206274c43b3750e07223\"}","integrity":"sha512-q01HxhKZ6PAVCSh3PWdz+1Cv+2qcNUy3/scGoChThzFCJkSxPeeeUFmdW15uZVnCJJkf5y256quEfqcP7wenAw==","time":1754018575980,"size":136117}