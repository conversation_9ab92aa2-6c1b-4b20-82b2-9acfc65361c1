
4a25ce3026937a0966c30ac6a904b40ab3d02202	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.97.1754018536329.js\",\"contentHash\":\"70bed06eeffa26e4acadbc1e9f3bd715\"}","integrity":"sha512-u+nTIGPpn3nhNp8KzKww2GRM4v3DJRVaYB/hb9E8yjUicTH3HyBaXP3INpap923EufSZb2Vhy/WKgbgmY4ZLRw==","time":1754018576044,"size":202357}