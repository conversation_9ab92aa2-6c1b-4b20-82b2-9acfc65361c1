import {request} from '@/api/index'

export default {
    query(data) {
        return request({
            url: '/ContractSealUseManagerController/query',
            method: 'post',
            data
        })
    },
    queryContract(data) {
        return request({
            url: '/ContractSealRegisterController/queryContract',
            method: 'post',
            data
        })
    },

    queryContractNotStatus(data) {
        return request({
            url: '/ContractSealRegisterController/queryContractNotStatus',
            method: 'post',
            data
        })
    },

    queryContractId(data) {
        return request({
            url: '/ContractSealRegisterController/queryContractId',
            method: 'post',
            data
        })
    },
    selectNumber(data) {
        return request({
            url: '/ContractSealUseManagerController/selectNumber',
            method: 'post',
            data
        })
    },

    queryRegister(data) {
        return request({
            url: '/ContractSealRegisterController/listRegister',
            method: 'post',
            data
        })
    },

    queryContracts(data) {
        return request({
            url: '/ContractSealRegisterController/queryContracts',
            method: 'post',
            data
        })
    },


    getSeal(data) {
        return request({
            url: '/ContractSealUseManagerController/getSeal',
            method: 'post',
            data
        })
    },

    getContract(data) {
        return request({
            url: '/ContractSealUseManagerController/getContract',
            method: 'post',
            data
        })
    },
    // addSealRegister
    save(data) {
        return request({
            url: '/ContractSealRegisterController/addSealRegister',
            method: 'post',
            data
        })
    },
    queryById(data) {
        return request({
            url: '/ContractSealRegisterController/queryById',
            method: 'post',
            data
        })
    },

    setAA(data) {
        return request({
            url: '/ContractSealRegisterController/setAA',
            method: 'post',
            data
        })
    },

    getNumber(data) {
        return request({
            url: '/ContractSealRegisterController/getNumber',
            method: 'post',
            data
        })
    },






}
