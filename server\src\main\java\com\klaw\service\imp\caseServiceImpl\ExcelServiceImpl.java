package com.klaw.service.imp.caseServiceImpl;

import com.klaw.dao.caseDao.ExcelMapper;
import com.klaw.entity.caseBean.CaseExcelData;
import com.klaw.service.caseService.ExcelService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Service
public class ExcelServiceImpl implements ExcelService {


    @Resource
    private ExcelMapper excelMapper;
    @Override
    public List<CaseExcelData> getCaseExcelData(Map<String, Object> param) {
        return null;
    }

    @Override
    public List<Map<String, Object>> getbigcaseData(Map<String, Object> param) {
        return excelMapper.getbigcaseData(param);
    }

    @Override
    public List<Map<String, Object>> getbenyueyijieData(Map<String, Object> param) {
        return excelMapper.getbenyueyijieData(param);
    }

    @Override
    public List<Map<String, Object>> getbigcaseofficer(Map<String, Object> param) {
        return excelMapper.getbigcaseofficer(param);
    }

    @Override
    public List<Map<String, Object>> weijiecase(Map<String, Object> param) {
        return excelMapper.weijiecase(param);
    }

    @Override
    public List<Map<String, Object>> weijiecasejine(Map<String, Object> param) {
        return excelMapper.weijiecasejine(param);
    }

    @Override
    public List<Map<String, Object>> xinfacase(Map<String, Object> param) {
        return excelMapper.xinfacase(param);
    }

    @Override
    public List<Map<String, Object>> xinfacasejine(Map<String, Object> param) {
        return excelMapper.xinfacasejine(param);
    }

    @Override
    public List<Map<String, Object>> xinzengleixingjine(Map<String, Object> param) {
        return excelMapper.xinzengleixingjine(param);
    }

    @Override
    public List<Map<String, Object>> yijieleixingjine(Map<String, Object> param) {
        return excelMapper.yijieleixingjine(param);
    }

    @Override
    public List<Map<String, Object>> weijieleixingshuliang(Map<String, Object> param) {

        return excelMapper.weijieleixingshuliang(param);
    }

    @Override
    public List<Map<String, Object>> xinzengleixingshuliang(Map<String, Object> param) {

        return excelMapper.xinzengleixingshuliang(param);
    }

    @Override
    public List<Map<String, Object>> weijieleixingjine(Map<String, Object> param) {
        return excelMapper.weijieleixingjine(param);
    }

    @Override
    public List<Map<String, Object>> yijieleixingshuliang(Map<String, Object> param) {
        return excelMapper.yijieleixingshuliang(param);
    }
}
