
582ea19ae5dd0c2b2bef85f8b7280d4de9263f8b	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.372.1754018536329.js\",\"contentHash\":\"fdeb9d590681071b2039cc65b34cf47a\"}","integrity":"sha512-cD+LeHFeGFrwc9o6l5E04zNJxL81DOiCWr59Mt66elc7d0lvD/znMFn4QsuJ7YLy0FixWMCrXwIVZA9W4/fBng==","time":1754018576020,"size":155826}