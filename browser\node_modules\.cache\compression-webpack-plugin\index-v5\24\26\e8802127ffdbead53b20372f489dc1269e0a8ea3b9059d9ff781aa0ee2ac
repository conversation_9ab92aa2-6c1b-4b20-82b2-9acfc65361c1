
3bda053dd645cd9b863696f51b77ae73dc212745	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.453.1754018536329.js\",\"contentHash\":\"b4c66c03bafb60a2690ac84f38e4a606\"}","integrity":"sha512-6YqLGalBPWBZ18R0cdUQ2lck/LeX+fONEDwpHcAmnWPG6ex4EwxulaHdAaqbAmUPy8g0U+1YTx3IjwVEbmeG9A==","time":1754018575977,"size":98655}