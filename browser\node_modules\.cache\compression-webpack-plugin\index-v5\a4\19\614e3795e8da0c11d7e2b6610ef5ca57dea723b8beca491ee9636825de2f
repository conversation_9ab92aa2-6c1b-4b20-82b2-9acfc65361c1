
24ddba05ff887874350061ad6808a69ede7256fd	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.203.1754018536329.js\",\"contentHash\":\"c0d715435f405c55fa880153409920c8\"}","integrity":"sha512-kfrTlZJhkxGA1oSmkMUwfgMh8ncaVAiVl9kjLY8hePvvB5n/GeE+NypHRlAspRT7bHNsqF4n/p7jE87HSywo9w==","time":1754018575988,"size":162184}