
1f19ea5231a51a8ae182e68b9b53488c116ddcc8	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.99.1754018536329.js\",\"contentHash\":\"385f0f73bd474d39bc4744df2dc87969\"}","integrity":"sha512-NXI8KVrwx548t1HYBAdFEMcEtAzt9KbnRiajoASECaNzt27bTCBpIudFqVg9gJpKpDU5UGUT8MyIPsttzDYTtA==","time":1754018575979,"size":168222}