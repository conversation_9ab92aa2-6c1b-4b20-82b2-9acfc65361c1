<template xmlns:v-slot="http://www.w3.org/1999/XSL/Transform">

  <div style="margin-top: 10px" v-if="view === 'old'">
    <!--基础信息表单块-->
    <div v-if="dataState !== 'view'">
      <div style="padding-left: 10px;margin-bottom: 20px;font-size: 15px;color: red;font-weight: bolder">

      </div>
      <div style="margin: 10px">
        <span style="font-weight: bold;font-size: 16px;color: #5A5A5F;">基本信息</span>
        <el-divider></el-divider>
      </div>
      <el-row style="margin-top: 10px">
        <el-col :span="8">
          <el-form-item label="标题" prop="title" required>
            <el-input v-if="!isView" v-model="mainData.title" maxlength="100" show-word-limit placeholder="请输入..."
              clearable />
            <span v-else class="viewSpan">{{ mainData.title }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="文化分类" required>
            <el-select v-model="mainData.cultureCategory" clearable placeholder="请选择" style="width: 100%">
              <el-option v-for="item in utils.culture_propaganda_type" :key="item.dicName" :label="item.dicName"
                :value="item.dicName" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="编号">
            <el-input v-if="!isView" v-model.trim="mainData.cultureId" disabled show-word-limit style="width: 100%" />
            <span v-else class="viewSpan">{{ mainData.cultureId }}</span>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item prop="content" label="摘要">
            <rich-text ref="richText" :content.sync="mainData.summary" :disabled="isView"></rich-text>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="附件材料">
              <UploadDoc :files.sync="mainData.uploadAttachment" :isMultiple=false :showPreview=false doc-path="/case" :disabled="isView" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-row> 
    </div>
    <!-- 查看 -->
    <div v-else>
      <SimpleBoardTitle title="基本信息">
        <table class="table_content">
          <tbody>
            <tr>
              <th colspan="2" class="th_label">标题</th>
              <td colspan="6" class="td_value">{{ mainData.title }}</td>
              <th colspan="2" class="th_label">文化分类</th>
              <td colspan="6" class="td_value">{{ mainData.cultureCategory }}</td>
              <th colspan="2" class="th_label">编码</th>
              <td colspan="6" class="td_value">{{ mainData.cultureId }}</td>
            </tr>
            <tr>
              <th colspan="2" class="th_label">摘要</th>
              <!-- <td colspan="22"><rich-text :content.sync="mainData.summary" :disabled="ture" /> </td> -->
              <td colspan="22" class="td_value">
                <div class="richText" v-html="mainData.summary"></div>
              </td>
            </tr>
            <tr>
              <th colspan="2" class="th_label">附件材料</th>
              <td colspan="22" class="td_value">
                <UploadDoc :files.sync="mainData.uploadAttachment"  doc-path="/case" :disabled="isView" />
              </td>
            </tr>
          </tbody>

        </table>

      </SimpleBoardTitle>
    </div>

  </div>
</template>

<script>
import AreaSelect from '@/view/components/AreaSelect/AreaSelect'
import OrgSingleDialogSelect from '@/view/components/OrgSingleDialogSelect/index.vue'
import UploadDoc from '@/view/components/UploadDoc/UploadDoc.vue'
import orgTree from '@/view/components/OrgTree/OrgTree'
import SimpleBoardTitle from "@/view/components/SimpleBoard/SimpleBoardTitle"
import SimpleBoardTitleApproval from "@/view/components/SimpleBoard/SimpleBoardTitleApproval"
import money from "@/view/components/Money/index"
import OrgLeader from '@/view/components/OrgLeader/OrgLeader'

//富文本
import RichText from "@/view/components/Editor/RichText";

export default {
  name: 'QsBaseInfo',
  components: {
    OrgSingleDialogSelect, AreaSelect, UploadDoc, orgTree, SimpleBoardTitleApproval, money,
    SimpleBoardTitle, OrgLeader, RichText
  },
  props: {
    data: {
      type: Object,
      default: function () {
        return {}
      }
    },
    dataState: {
      type: String,
      default: ''
    },
    view: {
      type: String,
      default: 'new'
    },
    create: {
      type: String,
      default: ''
    },
    authorizationData: {
      type: Object,
      default: function () {
        return {}
      }
    },
  },
  data() {
    return {
      videoUrl:'',
			videoShow:false,
      orgDialogTitle: '组织信息',
      caseDialogVisible: false,
      mainData: this.data,
      dicTreeDialogVisible: false,
      orgTreeDialog: false,
      caseNatures: [],
      plateData: [],
      causeOfIns: [],
      applications: [],
      zxcheckedData: [],
      dialogVisible: false,
      orgTreeDialog: false,
      orgVisible: false,
    }
  },
  computed: {
    isView: function () {
      return this.dataState === this.utils.formState.VIEW
    },
    causeOfInIds: {
      set: function (data) {
        this.mainData.causeOfInId = data.join(',')
      },
      get: function () {
        if (this.mainData.causeOfInId) {
          return this.mainData.causeOfInId.split(',')
        }
        return []
      }
    },
    isCreate: function () {
      return this.create === 'create'
    },
  },
  watch: {
    mainData: {
      handler(val, oldVal) {
        this.$emit('update:data', val)
      },
      deep: true
    },
    data(val) {
      this.mainData = Object.assign(this.mainData, val)
    },
    'mainData.involvedAmount': {
      handler(val, oldVal) {
        this.involvedAmountChange(val)
      },
      deep: true, immediate: true
    },
    'mainData.caseInterest': {
      handler(val, oldVal) {
        this.caseInterestChange(val)
      },
      deep: true, immediate: true
    }
  },
  created() {
    this.initDic()
  },
  methods: {

    orgSelect(data) {
      this.mainData.currentUnitId = data.unitId
      this.mainData.currentUnit = data.name

      this.mainData.cultureCategory = data.name
    },

  }
}
</script>

<style lang="scss" scoped>
.hideContent {

  .el-input__inner,
  .el-radio.is-bordered,
  .el-textarea__inner,
  .el-input__count {
    background-color: #f9e8bb;
  }
}

.money-label-width .el-form-item__label {
  width: 150px;
  /* 指定宽度 */
}

.richText {
  width: 100%;
}
</style>
