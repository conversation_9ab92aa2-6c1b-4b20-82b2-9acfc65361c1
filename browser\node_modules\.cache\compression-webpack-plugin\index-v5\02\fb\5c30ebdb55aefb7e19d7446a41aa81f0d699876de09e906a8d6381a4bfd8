
05560440f6c50551ba317dce37ee5bbf2c83e876	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.318.1754018536329.js\",\"contentHash\":\"9f2ad0f3194f144492ca43eaa821f7e7\"}","integrity":"sha512-VOmvxHhsfg+qHhQkFf3VKrkuJFlw9quA2q9iOER0/y52fFqNbTOrZ3KvnotxBcb+cqjOQ188Ra+vhVOc3vSDVQ==","time":1754018576016,"size":121961}