
3aaa0c68cf27055d50ff923811a34829059ef26d	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.198.1754018536329.js\",\"contentHash\":\"7dcf690d89ae1c220c40b4a26f79d686\"}","integrity":"sha512-5zSQy0eoXDgrhIwZxHsgKFrprM5U1zfRUvpG5NCidxfQTyt6NstjgbsYM26y4E0fKxgjgHzcLVv2AaTlCBUuEg==","time":1754018575984,"size":170812}