<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.klaw.dao.systemDao.SgFlowableMapper">
    <resultMap id="BaseResultMap" type="com.klaw.entity.systemBean.Comment">
        <id column="ID_" jdbcType="VARCHAR" property="id" />
        <result column="TYPE_" jdbcType="VARCHAR" property="type" />
        <result column="TIME_" jdbcType="TIMESTAMP" property="time" />
        <result column="USER_ID_" jdbcType="TIMESTAMP" property="userId" />
        <result column="TASK_ID_" jdbcType="VARCHAR" property="taskId" />
        <result column="PROC_INST_ID_" jdbcType="VARCHAR" property="procInstId" />
        <result column="ACTION_" jdbcType="VARCHAR" property="action" />
        <result column="MESSAGE_" jdbcType="VARCHAR" property="message" />
        <result column="FULL_MSG_" jdbcType="VARCHAR" property="fullMessage" />
        <result column="TASK_DEF_KEY_" jdbcType="VARCHAR" property="taskDefKey" />
    </resultMap>

    <resultMap id="SimpleResultMap" type="com.klaw.entity.systemBean.Comment">
        <id column="ID_" jdbcType="VARCHAR" property="id" />
        <result column="TYPE_" jdbcType="VARCHAR" property="type" />
        <result column="TIME_" jdbcType="TIMESTAMP" property="time" />
        <result column="USER_ID_" jdbcType="TIMESTAMP" property="userId" />
        <result column="TASK_ID_" jdbcType="VARCHAR" property="taskId" />
        <result column="PROC_INST_ID_" jdbcType="VARCHAR" property="procInstId" />
        <result column="ACTION_" jdbcType="VARCHAR" property="action" />
        <result column="MESSAGE_" jdbcType="VARCHAR" property="message" />
    </resultMap>

    <sql id="Base_Column_List">
        ID_, TYPE_, TIME_, USER_ID_, TASK_ID_, PROC_INST_ID_, ACTION_, MESSAGE_, FULL_MSG_
    </sql>
    <select id="selectHiComments" resultMap="BaseResultMap">
        SELECT
            C.ID_,
            C.TIME_,
            C.USER_ID_,
            C.TASK_ID_,
            C.PROC_INST_ID_,
            C.ACTION_,
            extTo.AUTO_RETURN_STATUS toReturn,
            C.TYPE_,
            C.MESSAGE_,
            C.FULL_MSG_,
            TAS.TASK_DEF_KEY_,
            U.FULL_NAME,
            P.UNIT_NAME  as DEPT_NAME
        FROM ACT_HI_COMMENT C
                 INNER JOIN ACT_HI_TASKINST TAS ON C.TASK_ID_ = TAS.ID_

                 LEFT JOIN  (
            select DISTINCT EXECUTION_ID_,PROC_INST_ID_,TASK_ID_,FROM_CURRENT_USER_ID,TO_TARGET_USER_ID,AUTO_RETURN_STATUS   FROM   FLOW_VARIABLE_EXT
            where  PROC_INST_ID_ = #{processInstanceId} and AUTO_RETURN_STATUS = 2  ) extTo
                            on  extTo.TASK_ID_ = C.TASK_ID_  and extTo.TO_TARGET_USER_ID =  C.USER_ID_

                 LEFT JOIN SYS_USER U ON U.USER_ID = C.USER_ID_
                 LEFT JOIN FLOW_USER_PATH P ON P.COMMENT_ID = C.ID_
                 LEFT JOIN HR_ORG_UNIT_B UN ON UN.UNIT_ID = P.UNIT_ID
                 LEFT JOIN HR_ORG_UNIT_B UNIT ON UNIT.UNIT_ID = UN.PARENT_ID
        WHERE
            C.PROC_INST_ID_ = #{processInstanceId}
          AND C.TYPE_ != 'taskMessage'
        ORDER BY C.TIME_ DESC,C.TASK_ID_ DESC
    </select>

    <select id="selectHiCommentsByProcessInstanceId" resultMap="SimpleResultMap">
        SELECT
            C.ID_,
            C.TIME_,
            C.USER_ID_,
            C.TASK_ID_,
            C.PROC_INST_ID_,
            C.ACTION_,
            C.TYPE_,
            C.MESSAGE_,
            U.FULL_NAME,
            P.UNIT_NAME  as DEPT_NAME
        FROM ACT_HI_COMMENT C
                 LEFT JOIN SYS_USER U ON U.USER_ID = C.USER_ID_
                 LEFT JOIN FLOW_USER_PATH P ON P.COMMENT_ID = C.ID_
                 LEFT JOIN HR_ORG_UNIT_B UN ON UN.UNIT_ID = P.UNIT_ID
                 LEFT JOIN HR_ORG_UNIT_B UNIT ON UNIT.UNIT_ID = UN.PARENT_ID
        WHERE
            C.PROC_INST_ID_ = #{processInstanceId}
          AND C.TYPE_ = 'pass'
        ORDER BY C.TIME_ DESC,C.TASK_ID_ DESC
    </select>
</mapper>
