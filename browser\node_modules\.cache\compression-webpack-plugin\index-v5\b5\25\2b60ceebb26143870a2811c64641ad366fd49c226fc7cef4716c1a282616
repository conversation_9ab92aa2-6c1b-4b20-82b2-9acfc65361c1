
49e2cac6dc806ea7d73e1876bb5b98c69e3662ef	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.160.1754018536329.js\",\"contentHash\":\"20a6c86993e264fc638d16218d78b127\"}","integrity":"sha512-U2IZQS1KUjB97uOfd+G6E7sdTs2yWGjZ+EVxCVveOuhS3TBJDZ7XPBIrmd3HgKbpYKYX6un67EE9OF4jD4o+qg==","time":1754018576050,"size":178258}