
4602df19f184264bf52472210252c4231f689c37	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.335.1754018536329.js\",\"contentHash\":\"c01add674d4e0ee806fedd69b064a305\"}","integrity":"sha512-YxP2Q8MiS2gVH7QYgQcyHF4frLNOrWhv08rjn1E18xzMm4Cyt465XGDr8vliiYGxkqMC+byZdf+eupGwUOFAGg==","time":1754018576069,"size":207980}