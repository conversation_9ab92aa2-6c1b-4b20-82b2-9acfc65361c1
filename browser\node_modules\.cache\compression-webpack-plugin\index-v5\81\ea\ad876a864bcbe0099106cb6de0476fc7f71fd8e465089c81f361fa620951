
6a192f3ba4f8fc17362efd4549715fcd677afb67	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.287.1754018536329.js\",\"contentHash\":\"55af824320f85e0faa0279b068fb044a\"}","integrity":"sha512-+oAmeVN7i8opqEdk81zO+wzRKCVNUuSAfRunQt+5OWnkv/FxYPUSYgZIyBIbHXZ7yn7EPMR/4oUxLJtqICAQxw==","time":1754018576008,"size":117977}