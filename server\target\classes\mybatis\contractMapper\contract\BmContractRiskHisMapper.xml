<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.klaw.dao.contractDao.contract.BmContractRiskHisMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.klaw.entity.contractBean.contract.BmContractRiskHis">
        <id column="id" property="id"/>
        <result column="parent_id" property="parentId"/>
        <result column="risk_type" property="riskType"/>
        <result column="risk_description" property="riskDescription"/>
        <result column="risk_occurrence_time" property="riskOccurrenceTime"/>
        <result column="resp_unit" property="respUnit"/>
        <result column="resp_unit_code" property="respUnitCode"/>
        <result column="resp_dept" property="respDept"/>
        <result column="resp_dept_code" property="respDeptCode"/>
        <result column="resp_psn" property="respPsn"/>
        <result column="resp_psn_code" property="respPsnCode"/>
        <result column="report_psn" property="reportPsn"/>
        <result column="report_psn_code" property="reportPsnCode"/>
        <result column="report_psn_tel" property="reportPsnTel"/>
        <result column="add_time" property="addTime"/>
        <result column="system_flag" property="systemFlag"/>
        <result column="create_ogn_id" property="createOgnId"/>
        <result column="create_ogn_name" property="createOgnName"/>
        <result column="create_dept_id" property="createDeptId"/>
        <result column="create_dept_name" property="createDeptName"/>
        <result column="create_group_id" property="createGroupId"/>
        <result column="create_group_name" property="createGroupName"/>
        <result column="create_psn_id" property="createPsnId"/>
        <result column="create_psn_name" property="createPsnName"/>
        <result column="create_org_id" property="createOrgId"/>
        <result column="create_org_name" property="createOrgName"/>
        <result column="create_psn_full_id" property="createPsnFullId"/>
        <result column="create_psn_full_name" property="createPsnFullName"/>
        <result column="create_legal_unit_id" property="createLegalUnitId"/>
        <result column="create_legal_unit_name" property="createLegalUnitName"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="data_state" property="dataState"/>
        <result column="data_state_code" property="dataStateCode"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        parent_id,
        contract_name,
        contract_code,
        risk_type,
        risk_description,
        risk_occurrence_time,
        resp_unit,
        resp_unit_code,
        resp_dept,
        resp_dept_code,
        resp_psn,
        resp_psn_code,
        report_psn,
        report_psn_code,
        report_psn_tel,
        add_time,
        system_flag,
        create_ogn_id,
        create_ogn_name,
        create_dept_id,
        create_dept_name,
        create_group_id,
        create_group_name,
        create_psn_id,
        create_psn_name,
        create_org_id,
        create_org_name,
        create_psn_full_id,
        create_psn_full_name,
        create_legal_unit_id,
        create_legal_unit_name,
        create_time,
        update_time,
        data_state,
        data_state_code

    </sql>
    <sql id="query_Page_List">
        id,
        parent_id,
        contract_name,
        contract_code,
        risk_type,
        risk_description,
        risk_occurrence_time,
        resp_unit,
        resp_unit_code,
        resp_dept,
        resp_dept_code,
        resp_psn,
        resp_psn_code,
        report_psn,
        report_psn_code,
        report_psn_tel,
        add_time,
        system_flag,
        create_ogn_id,
        create_ogn_name,
        create_dept_id,
        create_dept_name,
        create_group_id,
        create_group_name,
        create_psn_id,
        create_psn_name,
        create_org_id,
        create_org_name,
        create_psn_full_id,
        create_psn_full_name,
        create_legal_unit_id,
        create_legal_unit_name,
        create_time,
        update_time,
        data_state,
        data_state_code
    </sql>


    <select id="queryPageList" resultMap="BaseResultMap">
        select
        <include refid="query_Page_List"/>
        from bm_contract_risk_his
        <where>
            ${ew.sqlSegment}
        </where>
    </select>

</mapper>
