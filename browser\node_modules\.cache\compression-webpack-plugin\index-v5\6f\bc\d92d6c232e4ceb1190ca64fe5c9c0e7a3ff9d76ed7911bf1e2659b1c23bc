
fc8d42853aa53633ead75c0268bc5ecea2c19a69	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.218.1754018536329.js\",\"contentHash\":\"cf04c868eb6ee8687a0ca04f91bffd1f\"}","integrity":"sha512-YDuOdqmm5sOzzZP/nhSjLCbS8en4vXKUhs3GDR4YgMDTL/tG6yq/FE3Wv1fwKwrleKHZ7H8r8ncGMFGQApgStA==","time":1754018575992,"size":147173}