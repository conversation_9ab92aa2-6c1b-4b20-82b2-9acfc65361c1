
37b2966ca950e3494cced4807f9b6631226e9b9b	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.382.1754018536329.js\",\"contentHash\":\"25f0dacea8367b79f47b4dd0a68c6509\"}","integrity":"sha512-rEqMA1IHKvuSOUCvv11Rjvf3RvNiP7uS6xVO1gfjaCIkE+GMSIz4qVFJ23fWpHXOHb1v7tSVL3VH+sv2dGKJWw==","time":1754018576072,"size":179240}