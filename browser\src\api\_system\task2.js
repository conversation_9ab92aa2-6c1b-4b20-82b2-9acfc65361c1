import {request} from '@/api/index'

export default {
    query(data) {
        return request({
            url: '/sys_task2/query',
            method: 'post',
            data
        })
    },
    queryApprovalRecord(data) {
        return request({
            url: '/sys_task2/queryApprovalRecord',
            method: 'post',
            data
        })
    },
    queryById(data) {
        return request({
            url: '/sys_task2/queryById',
            method: 'post',
            data
        })
    },
}