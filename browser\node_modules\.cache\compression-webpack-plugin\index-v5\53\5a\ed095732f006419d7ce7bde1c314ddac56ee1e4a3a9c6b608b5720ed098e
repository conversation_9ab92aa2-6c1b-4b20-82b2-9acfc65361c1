
2e0ed8b5adf3410e90327d3b899937434a9bc222	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.282.1754018536329.js\",\"contentHash\":\"9159dead365e05018b1e71230abdb57a\"}","integrity":"sha512-ILGqC4W/RaWZPeELNXYUVTSqiltPcHZ20n5noyBV9Bw3W8IF+mla6lTHqU3YKr95WKC61t7+yZJiU+FZinjaPg==","time":1754018575963,"size":93426}