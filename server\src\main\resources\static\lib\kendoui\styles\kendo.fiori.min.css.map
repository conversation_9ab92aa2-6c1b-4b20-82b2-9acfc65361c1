{"version": 3, "sources": ["web/kendo.fiori.css"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAKA,oBACA,oBACE,QAAS,EAEX,gBACE,MAAO,QAET,cACE,MAAO,QAET,oBACE,MAAO,KAET,uBACE,cAAe,EAEjB,2BACE,MAAO,KAET,yBACE,iBAAkB,KAEpB,2BACE,MAAO,KAET,0BACE,MAAO,QAET,wBACE,iBAAkB,KAEpB,0BACE,MAAO,KAET,6BACE,MAAO,QAET,2BACE,iBAAkB,KAEpB,6BACE,MAAO,KAET,eACE,MAAO,QAET,iBACE,MAAO,QAET,iBACE,MAAO,QAET,cACE,MAAO,QAET,kBACE,MAAO,QAET,kBACE,MAAO,QAET,kBACE,MAAO,QAET,kBACE,MAAO,QAET,kBACE,MAAO,QAET,kBACE,MAAO,QAET,2BACE,iBAAkB,KAClB,OAAQ,IAAI,MAAM,QAEpB,MACA,QACA,iBACE,aAAc,YAGhB,SACA,UACE,iBAAkB,KAcpB,gBAZA,SA8CA,wBAPA,eA7BA,4BALA,WAQA,iBA6BA,mBAlCA,iBADA,iBAUA,sBASA,WACA,4BAFA,uBATA,eAQA,sBAIA,oBAPA,eAEA,sBADA,oBAlBA,SAWA,mBAiBA,mBACA,sCA3BA,UAJA,SA6BA,iBAFA,cACA,sBAKA,yBAEA,uBADA,qBAFA,4BAzBA,aA+BA,gBACA,YAvBA,iBACA,2BACA,kBAjBA,WAQA,iBAgCA,SA7BA,WA+BA,WAPA,gBASA,gBA9CA,UA+CE,aAAc,QAUhB,eACA,oBAHA,sBADA,eALA,SAIA,mBAFA,mBACA,cAFA,WAMA,oBAGA,kBACE,iBAAkB,KAEpB,mBAEA,uBADA,gBAEE,iBAAkB,QAEpB,kBACE,aAAc,eACd,iBAAkB,KAEpB,WACA,iBAEA,mBADA,sBAEA,SACE,iBAAkB,KAEpB,OAGA,oDADA,kBADA,aAGE,iBAAkB,YAGpB,gBADA,kCAEE,iBAAkB,cAGpB,yBACA,gCAEA,+BADA,8BAHA,WAKE,aAAc,QACd,iBAAkB,QAGpB,yBAEA,yCADA,0BAEA,0CAEA,yCADA,wCALA,iBAOE,aAAc,QAMhB,iBAJA,gBAEA,sBADA,mBAEA,yBAEE,WAAY,IAEd,SAMA,oBADA,iBAJA,gBAEA,sBADA,mBAEA,yBAGE,iBAAkB,KAClB,MAAO,KAET,mBACE,iBAAkB,KAClB,MAAO,KAET,SAGA,WAEA,qBAHA,SAEA,WAHA,UAKE,MAAO,KAET,WACE,MAAO,KAET,SACE,MAAO,KAET,aACA,gBACA,qCACE,MAAO,QAGT,uBADA,0BAEE,MAAO,KAIT,iCAFA,UACA,iBAEE,MAAO,KAcT,gBAHA,UAEA,cARA,iBAFA,eAKA,mBANA,UAKA,gBAEA,cAQA,sCAXA,eAMA,eAGA,mBACA,0BANA,WANA,WAcA,+CACE,iBAAkB,KAClB,oBAAqB,IAAI,IACzB,iBAAkB,QAEpB,oBACE,iBAAkB,KAEpB,SAMA,UACA,cALA,eAEA,mBAHA,UAIA,cAFA,WAKA,gBACA,gCACE,iBAAkB,QAQpB,yCADA,wCAJA,cAMA,qDACA,wFAJA,yBAFA,uBACA,0BAME,QAAS,EAIX,yBAFA,QAGA,+CACA,0EAHA,0BAIE,QAAS,GAEX,gCACA,qDACA,kDACE,QAAS,GAEX,QACE,aAAc,YAMhB,yBADA,aAMA,6CAHA,4CADA,6CAHA,qBAFA,QAOA,+CACA,0EAPA,aASE,iBAAkB,sBAClB,aAAc,YAGhB,gCACA,qDACA,kDACE,iBAAkB,sBAClB,aAAc,YAEhB,WACA,0BACE,iBAAkB,uBAClB,oBAAqB,IAAI,IAE3B,iBACE,iBAAkB,6BAEpB,iBACE,iBAAkB,KAEpB,UACE,MAAO,KACP,aAAc,QACd,iBAAkB,QAEpB,cACE,aAAc,QACd,iBAAkB,QAClB,mBAAoB,KACZ,WAAY,KAEtB,oBACE,aAAc,QACd,iBAAkB,QAClB,mBAAoB,KACZ,WAAY,KAGtB,aACE,MAAO,KACP,iBAAkB,QAEpB,oBACE,MAAO,KAET,wBACA,yBACE,iBAAkB,QAClB,MAAO,KAKT,uBACA,yBAFA,sBAGA,mBAJA,sBADA,sBAME,aAAc,QAEhB,gBACE,iBAAkB,QAEpB,yBACE,iBAAkB,gBAEpB,kCACE,iBAAkB,eAEpB,4BACA,iCACA,kCACE,iBAAkB,QAEpB,uBACE,kBAAmB,KAErB,sBACE,iBAAkB,KAEpB,SACA,iBACE,aAAc,QACd,WAAY,QAAQ,EAAE,OAAO,KAAK,SAClC,MAAO,KAET,iBACE,MAAO,KAET,0BACE,oBAAqB,EAAE,EACvB,mBAAoB,EAAE,EAAE,EAAE,IAAI,KACtB,WAAY,EAAE,EAAE,EAAE,IAAI,KAEhC,gCACA,sCACE,iBAAkB,KAGpB,2BADA,4BAEE,aAAc,QAEhB,uBAEA,oBADA,qBAEE,iBAAkB,KAClB,MAAO,KACP,aAAc,QAEhB,uBACE,MAAO,KAET,4BACE,aAAc,QAEhB,mBACE,iBAAkB,KAKpB,iBAFA,gBACA,sBAEA,4BACE,iBAAkB,KAClB,aAAc,QACd,MAAO,KAET,mCACE,iBAAkB,KAGpB,0BADA,gBAEE,aAAc,QAGhB,wBADA,gBAEE,MAAO,KACP,aAAc,QACd,iBAAkB,QAGpB,yBADA,iBAEE,MAAO,KACP,iBAAkB,QAClB,aAAc,QAGhB,+BADA,uBAEE,MAAO,KACP,aAAc,QACd,iBAAkB,QAGpB,2BAKA,kCAFA,iCAJA,oBAEA,4BAGA,mCAFA,kCAIE,MAAO,QACP,aAAc,QACd,iBAAkB,QAClB,iBAAkB,KAEpB,uDACE,mBAAoB,KACZ,WAAY,KAEtB,8DACE,mBAAoB,KACZ,WAAY,KAEtB,uCACE,iBAAkB,YAEpB,kCACE,MAAO,KACP,iBAAkB,QAEpB,+BACE,iBAAkB,QAClB,MAAO,KAET,+BACE,MAAO,KACP,iBAAkB,QAClB,aAAc,QACd,iBAAkB,KAEpB,mBACE,WAAY,KACZ,MAAO,KAGT,iCADA,iBAEE,aAAc,QAEhB,oBACE,MAAO,KAET,sBACE,QAAS,EAEX,mCACE,MAAO,KACP,gBAAiB,KACjB,iBAAkB,KAGpB,iDADA,yCAEE,iBAAkB,KAClB,gBAAiB,UAEnB,0CACE,iBAAkB,KAEpB,+BACE,MAAO,KAET,sCACE,gBAAiB,KACjB,iBAAkB,QAClB,MAAO,KAET,kCACA,2BACE,cAAe,EAEjB,8BACE,cAAe,EAEjB,eACE,iBAAkB,KAEpB,8BACE,aAAc,eAUhB,qCADA,6BADA,2BAFA,2BADA,0BAQA,iBANA,2BAIA,oDACA,uCAVA,kBACA,uBACA,0BAUE,MAAO,KACP,iBAAkB,QAClB,aAAc,QAGhB,wCACA,yCAFA,wBAGE,iBAAkB,QAEpB,mDACE,iBAAkB,QAEpB,yBACA,yCACE,WAAY,QACZ,MAAO,KAET,kCACE,WAAY,QACZ,MAAO,KACP,0BAA2B,EAE7B,gBACE,MAAO,KAMT,0BAFA,kCADA,yBADA,iBAGA,mBAEE,mBAAoB,KACZ,WAAY,KAEtB,6BACA,8CACE,mBAAoB,MAAM,EAAE,EAAE,EAAE,IAAI,QAC5B,WAAY,MAAM,EAAE,EAAE,EAAE,IAAI,QAEtC,8BACE,MAAO,KACP,iBAAkB,QAGpB,0CADA,kCAEA,oCACE,mBAAoB,KACZ,WAAY,KAStB,iCADA,uBAHA,yCADA,oCADA,kCADA,wCAKA,6BADA,0BAKA,qDADA,0CAEE,mBAAoB,KACZ,WAAY,KAItB,wDADA,iCADA,0BAGE,MAAO,KAST,6BACA,wBAJA,uBAOA,4CADA,uCADA,sCAGA,6CANA,4BADA,sDAHA,mCACA,iCAHA,eACA,qBAYE,MAAO,KACP,iBAAkB,QAClB,aAAc,QAGhB,wEACE,MAAO,KACP,iBAAkB,QAClB,aAAc,QAEhB,yCACE,aAAc,QAGhB,2BADA,yBAEE,aAAc,QAKhB,0BAFA,wBACA,gBAFA,gBAUA,oBAFA,qCADA,4BAFA,eACA,qBAFA,iBAKA,8BAEE,iBAAkB,KAEpB,cACE,iBAAkB,QAClB,MAAO,KAET,+BAOA,iBAJA,gCADA,+BAMA,qCAPA,8BAGA,gBACA,sBACA,wBAGE,iBAAkB,KAIpB,yBADA,iBAEA,qCAHA,kBAIE,iBAAkB,KAGpB,yBADA,iBAEA,qCACE,oBAAqB,IAAI,IAE3B,aACE,iBAAkB,sBAGpB,qCADA,uBAEA,8BACE,MAAO,KAGT,gCADA,8BAOA,iCADA,+BADA,gCADA,8BADA,+BADA,6BAME,iBAAkB,KAClB,iBAAkB,KAClB,oBAAqB,IAAI,IACzB,aAAc,QAKhB,sCAHA,6EAEA,yCADA,gEAGE,MAAO,KAET,sEACE,WAAY,KACZ,aAAc,QAEhB,4EACE,iBAAkB,KAClB,aAAc,QAEhB,kFACE,aAAc,QAEhB,oCACE,MAAO,KAET,eACE,aAAc,KACd,iBAAkB,QAClB,MAAO,KAET,kBACE,QAAS,GAEX,yBACE,OAAQ,kBAGV,iCADA,+BAEE,aAAc,EACd,iBAAkB,KAClB,iBAAkB,YAQpB,6BADA,eAFA,eACA,uBAIA,wBARA,kBAEA,4BADA,0BAMA,qBAEE,MAAO,QAGT,6BACE,WAAY,6BAEd,qDACA,+CACE,QAAS,KAGX,gBACE,iBAAkB,QAEpB,oBACE,iBAAkB,QAEpB,6BACE,iBAAkB,wBAEpB,2BACE,iBAAkB,wBAGpB,oBACE,aAAc,QACd,iBAAkB,KAClB,MAAO,KAET,+BACE,aAAc,QACd,iBAAkB,QAClB,MAAO,QAIT,oCADA,qCAEE,UAAW,KACX,SAAU,SACV,IAAK,IAEP,aACE,oBAAqB,QAEvB,aACE,mBAAoB,QAEtB,aACE,iBAAkB,QAEpB,aACE,kBAAmB,QAErB,mCACE,oBAAqB,QAEvB,mCACE,mBAAoB,QAEtB,mCACE,iBAAkB,QAEpB,mCACE,kBAAmB,QAGrB,YACE,iBAAkB,QAGpB,8BADA,4BAEE,iBAAkB,KAGpB,QACE,iBAAkB,KAClB,aAAc,QAEhB,iBACE,MAAO,QAET,6BACE,iBAAkB,QAEpB,gBACE,MAAO,QAET,4BACE,iBAAkB,QAEpB,cACE,MAAO,QAET,0BACE,iBAAkB,QAGpB,QACE,aAAc,KAEhB,iBACA,0BACE,aAAc,QAEhB,6BACE,aAAc,QAGhB,+BADA,iCAEE,iBAAkB,4BAClB,wBAAyB,KAAK,KACtB,gBAAiB,KAAK,KAGhC,QACA,4BACE,MAAO,KAET,kBACA,sCACE,MAAO,KAIT,UADA,UAEE,mBAAoB,KACZ,WAAY,KAEtB,UACA,YACA,UACE,mBAAoB,KACZ,WAAY,KAEtB,eACE,mBAAoB,KACZ,WAAY,KAGtB,gCACA,iCAEA,gCADA,+BAHA,iBAKE,mBAAoB,KACZ,WAAY,KAEtB,kBACE,mBAAoB,KACZ,WAAY,KAEtB,gBACE,mBAAoB,KACZ,WAAY,KAOtB,oCACA,kCAFA,uBAGA,gCAGA,wBARA,0BADA,sBAQA,+BADA,8BARA,SAGA,cAQA,WACE,mBAAoB,EAAE,IAAI,IAAI,EAAE,eACxB,WAAY,EAAE,IAAI,IAAI,EAAE,eAElC,8BACE,mBAAoB,MAAM,EAAE,EAAE,EAAE,IAAI,QAC5B,WAAY,MAAM,EAAE,EAAE,EAAE,IAAI,QAGtC,UACE,aAAc,eACd,mBAAoB,IAAI,IAAI,KAAK,IAAI,qBAC7B,WAAY,IAAI,IAAI,KAAK,IAAI,qBACrC,iBAAkB,KAEpB,0BACE,aAAc,eACd,mBAAoB,IAAI,IAAI,KAAK,IAAI,eAC7B,WAAY,IAAI,IAAI,KAAK,IAAI,eAIvC,sCADA,uCADA,6BAGE,cAAe,EAEjB,UACE,mBAAoB,EAAE,IAAI,IAAI,EAAE,eACxB,WAAY,EAAE,IAAI,IAAI,EAAE,eAElC,SACE,mBAAoB,MAAM,EAAE,IAAI,IAAI,eAC5B,WAAY,MAAM,EAAE,IAAI,IAAI,eAGtC,kCACE,iBAAkB,QAClB,YAAa,KACb,MAAO,KAET,6BACE,iBAAkB,QAClB,YAAa,KACb,MAAO,KAET,kCACE,iBAAkB,QAClB,YAAa,KACb,MAAO,KAGT,6CACE,iBAAkB,QAClB,MAAO,KACP,aAAc,QAEhB,gDACE,iBAAkB,QAClB,MAAO,KACP,aAAc,QAEhB,gDACE,iBAAkB,QAClB,MAAO,KACP,aAAc,QAEhB,8CACE,iBAAkB,QAClB,MAAO,KACP,aAAc,KAGhB,qBACE,WAAY,IAEd,4BACE,iBAAkB,cAIpB,6CACA,gDAHA,uCACA,0CAGE,iBAAkB,QAEpB,kBACE,iBAAkB,KAClB,aAAc,KAEhB,wBACE,iBAAkB,KAEpB,gBACE,aAAc,KACd,WAAY,KAEd,kBACA,yBACE,aAAc,KACd,WAAY,KAEd,iCACE,aAAc,QACd,WAAY,QAGd,2CADA,mCAEE,aAAc,QACd,WAAY,QAEd,eACE,iBAAkB,QAClB,aAAc,QACd,MAAO,KAET,gCACE,aAAc,QAEhB,QACE,iBAAkB,KAClB,MAAO,KAET,yBACE,iBAAkB,QAClB,MAAO,QAET,YACE,iBAAkB,KAGpB,kBACA,gBACA,eACA,cACA,kBACA,cACE,iBAAkB,0BAcpB,gBAXA,SACA,UA2BA,oBADA,eADA,sBARA,eALA,YAGA,cAGA,kBAhBA,aAWA,YACA,iBAeA,iBArBA,0BACA,sCAFA,gBAiBA,kBAbA,eAQA,gBAGA,gBAFA,kBACA,eAYA,oBADA,gBA/BA,WA8BA,QAfA,cAcA,WA3BA,mBAyBA,kBAMA,UA9BA,UAEA,iBADA,sCA8BE,cAAe,EAEjB,QACE,WAAY,OACZ,eAAgB,OAElB,sBACA,0CACE,cAAe,EAEjB,6BAEA,iDADA,4CAEE,cAAe,EAEjB,oBACA,wCACA,iDACE,cAAe,EAEjB,2BACA,+CACA,wDACE,cAAe,EAEjB,kCACE,cAAe,EAIjB,kCAFA,wCAIA,mCAIA,eAPA,oCAEA,iCAGA,kCADA,iCAEA,kBAEE,cAAe,EAEjB,2CACA,4CAGA,2CAFA,0CACA,mDAEE,cAAe,EAEjB,qDACE,cAAe,EASjB,oCANA,mBAIA,0CAIA,qCAHA,sCAEA,mCAGA,oCARA,sCAOA,mCARA,0BAEA,0BAJA,mBAYE,cAAe,EAEjB,8CACE,cAAe,EAEjB,4CACE,cAAe,EAEjB,0DACE,cAAe,EAEjB,wDACE,cAAe,EAEjB,0BAEA,yBADA,wBAEE,cAAe,EAEjB,iCAEA,gCADA,+BAEE,cAAe,EAEjB,wBACE,cAAe,EAEjB,gCACE,cAAe,EAEjB,iCACE,cAAe,EAEjB,wCACE,cAAe,EAEjB,6CACE,cAAe,EAEjB,8CAGA,6CAFA,4CACA,qDAEE,cAAe,EAEjB,yCACE,iBAAkB,QAEpB,uDACE,cAAe,EAKjB,sCAHA,2BAIA,uCAFA,0BADA,yBAIE,cAAe,EAKjB,6CAHA,kCAIA,8CAFA,iCADA,gCAIE,cAAe,EAEjB,0CACE,cAAe,EAGjB,yBACA,oBAFA,iBAGE,cAAe,EAQjB,YAFA,iCAHA,yBACA,2BAFA,uBAGA,0BAEA,oBAEE,cAAe,EAGjB,4BADA,oBAEE,cAAe,EAEjB,cACE,cAAe,EAEjB,uCACA,+CACA,4DACA,oEACE,cAAe,EAEjB,8CACA,sDACA,mEACA,2EACA,iEACA,yEACE,cAAe,EAIjB,sCAFA,0DACA,kEAEE,cAAe,EAEjB,iCAEA,yCADA,yCAEA,iDACE,wBAAyB,EACzB,2BAA4B,EAE9B,wCAEA,gDADA,gDAEA,wDACE,cAAe,EAGjB,4CADA,0CAEE,cAAe,EAGjB,SAGA,iBAJA,eAGA,iBADA,eAGE,cAAe,EAEjB,6BACE,cAAe,GAGjB,yDACE,aAAc,EAEhB,8CACE,mBAAoB,EAEtB,4CACA,oDACE,aAAc,EAAE,EAAE,IAAI,EACtB,aAAc,YAGhB,4CACE,kBAAmB,KAKrB,kCADA,gCAEA,iCACA,kEAJA,iCAOA,8CADA,8CADA,wCANA,iCASE,MAAO,KAET,0EACE,sBACE,MAAO,MAIX,8CADA,wCAEA,qEACE,iBAAkB,KAClB,WAAY,IAAI,MAAM,QAExB,uDACE,cAAe,EAEjB,yDACE,iBAAkB,QAClB,iBAAkB,QAEpB,iFACE,iBAAkB,YAEpB,mDACE,mBAAoB,MAAM,EAAE,KAAK,EAAE,QAC3B,WAAY,MAAM,EAAE,KAAK,EAAE,QAIrC,4EADA,yEADA,8CAGE,MAAO,QAET,8CACE,cAAe,IAAI,MAAM,QAE3B,8CACE,mBAAoB,EAAE,IAAI,IAAI,QACtB,WAAY,EAAE,IAAI,IAAI,QAEhC,+BACA,oCAEA,sDADA,qCAEE,MAAO,KACP,aAAc,KACd,iBAAkB,IAClB,iBAAkB,mGAClB,iBAAkB,wEAClB,iBAAkB,sEAEpB,sCACA,2CAEA,6DADA,4CAEE,iBAAkB,KAGpB,gCAGA,iCADA,gCADA,+BAGE,iBAAkB,KAClB,oBAAqB,IAAI,IACzB,iBAAkB,KAClB,aAAc,QAEhB,8BAGA,+BADA,8BADA,6BAGE,iBAAkB,KAClB,iBAAkB,KAClB,oBAAqB,IAAI,IACzB,aAAc,QAEhB,wBACE,aAAc,QAEhB,sCACE,aAAc,QAEhB,gCAGA,iCACA,wCAFA,gCADA,+BAIE,iBAAkB,KAClB,iBAAkB,KAClB,oBAAqB,IAAI,IACzB,aAAc,QACd,mBAAoB,KACZ,WAAY,KAEtB,kBACE,MAAO,KAET,qBACA,sCACA,iBACE,MAAO,KAET,2BACE,aAAc,QAEhB,yBACE,aAAc,QAEhB,2BACE,aAAc,QAEhB,kBACE,mBAAoB,KACZ,WAAY,KAGtB,uCADA,2CAEE,MAAO,KAIT,qDADA,qCADA,yCAGE,MAAO,KAET,2CACE,WAAY,QACZ,mBAAoB,KACZ,WAAY,KAEtB,mCACE,aAAc,YAEhB,iCACE,aAAc,QAGhB,8CADA,kCAEE,iBAAkB,KAClB,iBAAkB,KAClB,aAAc,QAEhB,sCACE,iBAAkB,KAClB,MAAO,KAGT,gBADA,iBAEE,aAAc,YAEhB,eACA,uBACA,wCACE,aAAc,QAEhB,wCACE,mBAAoB,MAAM,EAAE,IAAI,EAAE,KAAS,EAAE,IAAI,EAAE,KAC3C,WAAY,MAAM,EAAE,IAAI,EAAE,KAAS,EAAE,IAAI,EAAE,KAGrD,0DADA,0CAEE,mBAAoB,EAAE,IAAI,EAAE,KACpB,WAAY,EAAE,IAAI,EAAE,KAE9B,yCACE,mBAAoB,MAAM,EAAE,IAAI,EAAE,KAC1B,WAAY,MAAM,EAAE,IAAI,EAAE,KAEpC,4BACE,aAAc,QACd,iBAAkB,YAEpB,iBACE,aAAc,eAEhB,8BACE,iBAAkB,KAIpB,kBADA,mBADA,mBAGE,MAAO,KACP,aAAc,QACd,YAAa,IAEf,mBACE,MAAO,KAET,2BACE,mBAAoB,MAAM,EAAE,EAAE,EAAE,IAAI,KAC5B,WAAY,MAAM,EAAE,EAAE,EAAE,IAAI,KAUtC,kCANA,2BACA,eAFA,oBAMA,sCAPA,UAIA,cAEA,sBADA,yBAIE,aAAc,QAGhB,iCADA,WAEE,MAAO,KACP,aAAc,QACd,iBAAkB,KAClB,oBAAqB,IAAI,IACzB,iBAAkB,QAClB,mBAAoB,KACZ,WAAY,KAGtB,2BADA,iBAEE,MAAO,KACP,aAAc,QACd,iBAAkB,KAClB,mBAAoB,KACZ,WAAY,KAEtB,iBACE,MAAO,KACP,aAAc,QACd,iBAAkB,KAClB,iBAAkB,QAClB,mBAAoB,KACZ,WAAY,KAEtB,+DACA,wDACE,mBAAoB,KACZ,WAAY,KAEtB,kBACE,MAAO,KACP,aAAc,QACd,iBAAkB,KAClB,iBAAkB,QAClB,mBAAoB,KACZ,WAAY,KAEtB,4BAMA,mCAJA,kCADA,6BAIA,oCAFA,mCAIE,MAAO,KACP,aAAc,QACd,iBAAkB,QAClB,iBAAkB,KAClB,mBAAoB,KACZ,WAAY,KAEtB,yBACA,kBACE,aAAc,YAIhB,kCADA,2BADA,oBAGE,iBAAkB,YAClB,cAAe,EAEjB,0CACE,iBAAkB,YAEpB,gBACA,sBACE,QAAS,EAEX,sBACE,mBAAoB,EAAE,EAAE,EAAI,IAAI,QACxB,WAAY,EAAE,EAAE,EAAI,IAAI,QAElC,gCACE,WAAY,IACZ,aAAc,YAEhB,qDACE,kBAAmB,QAErB,wBACE,QAAS,EACT,aAAc,QACd,mBAAoB,KACZ,WAAY;;;;;;;;;;;;;;;;;;;;;;;AAGtB,yBACE,aAAc,QACd,WAAY,KACZ,cAAe,IAEjB,+BACA,mDACE,aAAc,QACd,mBAAoB,KACZ,WAAY,KAEtB,6CACE,iBAAkB,KAClB,aAAc,QACd,MAAO,QAET,gCACE,mBAAoB,KACZ,WAAY,KACpB,aAAc,QAEhB,oDACE,mBAAoB,KACZ,WAAY,KACpB,aAAc,QAEhB,uCACE,MAAO,QAET,oDACE,mBAAoB,KACZ,WAAY,KAItB,6DADA,sDAEA,4DAHA,8CAIE,MAAO,QACP,WAAY,QACZ,aAAc,QACd,cAAe,IAEjB,2CACE,aAAc,QACd,mBAAoB,KACZ,WAAY,KAEtB,kDACE,iBAAkB,QAClB,iBAAkB,KAClB,aAAc,QACd,cAAe,EAEjB,wDACE,aAAc,QACd,iBAAkB;;;;;;;;;;;;;;;;;;;;;;;AAGpB,sBACE,aAAc,QACd,cAAe,IACf,iBAAkB,KAClB,aAAc,IAEhB,4BACA,6CACE,aAAc,QACd,mBAAoB,KACZ,WAAY,KAEtB,sCACE,iBAAkB,QAClB,cAAe,IAEjB,6BACE,aAAc,QACd,mBAAoB,KACZ,WAAY,KAEtB,8CACE,mBAAoB,KACZ,WAAY,KACpB,aAAc,QAEhB,iCACE,MAAO,QAGT,+CADA,wCAEA,6CACA,8CACE,WAAY,KACZ,aAAc,QACd,mBAAoB,KACZ,WAAY,KAEtB,+CACE,iBAAkB,QAClB,QAAS,GAEX,qCACE,aAAc,QACd,mBAAoB,KACZ,WAAY,KAEtB,8CACE,aAAc,QAEhB,uDACE,aAAc,QAEhB,yGAIE,WAOA,yBARA,aADA,qBADA,wBAWA,gCACA,qDACA,kDAPA,6BACA,2CAFA,4BAGA,+BACA,6CALA,aAUE,iBAAkB,yBAClB,wBAAyB,MAAM,MACvB,gBAAiB,MAAM,MAEjC,0BAEA,yBADA,wBAEE,cAAe,EAEjB,kBACA,gBACA,eACA,cACA,kBACA,cACE,iBAAkB,6BAClB,wBAAyB,KAAK,KACtB,gBAAiB,KAAK,MAGlC,6CAEE,qDADA,wDAEE,aAAc,MAIlB,0CAME,+CAJA,kEAKA,iDAJA,oEAKA,mDAJA,sEACA,mDAJA,sEAQE,iBAAkB,KAClB,oBAAqB,IAAI,IACzB,iBAAkB,KAClB,aAAc,QAGhB,+CACA,iDACA,mDAHA,mDAIE,aAAc,QAGhB,kEACA,oEACA,sEAHA,sEAIE,cAAe,EAGjB,oEACA,sEACA,wEAHA,wEAIE,cAAe,EAGjB,mFACA,qFACA,uFAHA,uFAIE,cAAe,EAGjB,6CAIA,qDAIA,mDAIA,2DAXA,+CAIA,uDAIA,qDAIA,6DAXA,iDAIA,yDAIA,uDAIA,+DAfA,iDAIA,yDAIA,uDAIA,+DAIE,cAAe,EAGjB,8DAIA,sEAHA,gEAIA,wEAHA,kEAIA,0EAPA,kEAIA,0EAIE,cAAe,EAGjB,qDAIA,wEAHA,uDAIA,0EAHA,yDAIA,4EAPA,yDAIA,4EAIE,aAAc,QACd,iBAAkB,KAClB,iBAAkB,QAGpB,0EACA,4EACA,8EAHA,8EAIE,MAAO,KACP,UAAW,KAGb,gFACA,kFACA,oFAHA,oFAIE,MAAO,KAGT,qDAIA,2DAHA,uDAIA,6DAHA,yDAIA,+DAPA,yDAIA,+DAIE,QAAS,MACT,QAAS,GACT,SAAU,SACV,IAAK,IACL,WAAY,MACZ,MAAO,OACP,MAAO,QACP,OAAQ,QAGV,wCAIA,iEAHA,0CAIA,mEAHA,4CAIA,qEAPA,4CAIA,qEAIE,aAAc,IAAI,IAAI,EAAE,IACxB,aAAc,MACd,aAAc,QACd,iBAAkB,QAClB,cAAe,EACf,mBAAoB,EAAE,IAAI,IAAI,EAAE,eACxB,WAAY,EAAE,IAAI,IAAI,EAAE,eAGlC,iEACA,mEACA,qEAHA,qEAIE,aAAc,IACd,iBAAkB,KAClB,cAAe,EAOjB,+BAJA,yCAKA,iCAJA,2CAKA,mCAJA,6CACA,mCAJA,6CAQE,cAAe,EAGjB,qDACA,uDACA,yDAHA,yDAIE,SAAU,UAGd,UACA,gBACE,cAAe,IAEjB,qCACE,cAAe,IAAI,EAAE,EAAE,IAEzB,yBACE,aAAc,QAGhB,+BADA,yBAEE,iBAAkB,QAGpB,iBADA,iBAEE,aAAc,QAEhB,YACE,aAAc,YAIhB,qDAFA,qDAGA,8DAFA,8DAGE,aAAc,QAEhB,sBAIA,8BAHA,iBAIA,8BAFA,sCADA,qBAIE,iBAAkB,KASpB,8BANA,uBACA,2BAEA,qBAEA,wBANA,4BAGA,uBAEA,kCAGE,iBAAkB,YAIpB,eAFA,uBACA,sBAEE,MAAO,KACP,YAAa,IACb,UAAW,KACX,eAAgB,UAElB,6CACE,aAAc,YAEhB,qDACE,MAAO,QAET,8CACE,aAAc,QAEhB,sDACE,MAAO,QACP,aAAc,QAEhB,aACA,6BACE,aAAc,KAEhB,4BACE,WAAY,IACZ,MAAO,KAMT,6CACA,gDAHA,uCACA,0CAFA,mCADA,0BAME,iBAAkB,QAClB,MAAO,KAET,iBACE,iBAAkB,QAEpB,qCACE,WAAY,IAGd,2BADA,kCAEE,mBAAoB,MAAM,EAAE,EAAE,EAAE,IAAI,QAC5B,WAAY,MAAM,EAAE,EAAE,EAAE,IAAI,QAGtC,4BACA,4CAFA,4BAGE,MAAO,KACP,iBAAkB,QAEpB,kCACA,qCACE,iBAAkB,QAEpB,kBACE,WAAY,QAEd,cACA,cACE,kBAAmB,YAGrB,yBACA,+BACA,mCAHA,sBAIE,WAAY,IACZ,cAAe,EAKjB,+BAHA,sBACA,6CACA,4BAEE,cAAe,EACf,aAAc,YAEhB,4BACE,aAAc,QAEhB,yBACE,aAAc,YAGhB,gBADA,mBAEE,QAAS,OAEX,0BACE,WAAY,KAGd,0CADA,kDAEE,WAAY,IAEd,2BACE,cAAe,EAEjB,0CACE,aAAc,QAEhB,yBACE,aAAc,QACd,WAAY,QAEd,2CACE,aAAc,QACd,WAAY,QAEd,kBACE,aAAc,QACd,WAAY,QAEd,kBACE,iBAAkB,QAClB,aAAc,QAEhB,wBACE,iBAAkB,KAIpB,4BACA,+BAEA,2BACA,yBAFA,6CAJA,gCACA,mCAME,MAAO,KAET,mCACA,+BAEA,kDACA,mCAFA,0DAGE,MAAO,QAGT,8CADA,sDAEE,WAAY,IACZ,aAAc,YAGhB,iDADA,yDAEE,aAAc,QAGhB,iDADA,yDAEE,MAAO,QAET,eACE,WAAY,QACZ,aAAc,KAEhB,+BACE,mBAAoB,MAAM,EAAE,EAAE,EAAI,IAAI,QAC9B,WAAY,MAAM,EAAE,EAAE,EAAI,IAAI,QAExC,yBACE,WAAY,QAEd,uBACE,WAAY,QAEd,6BACE,MAAO,KAET,mCACE,iBAAkB,QAEpB,qCAEA,6CADA,4BAEE,mBAAoB,MAAM,EAAE,EAAE,EAAI,IAAI,QAC9B,WAAY,MAAM,EAAE,EAAE,EAAI,IAAI,QAExC,6BACE,WAAY,IAEd,gCACE,WAAY,QAEd,wCACE,MAAO,KAET,6BACE,WAAY,QACZ,aAAc,KAGhB,8CADA,sBAEE,iBAAkB,QAGpB,uBADA,kCAEE,WAAY,KAKd,uBACA,yBAFA,sBAGA,mBAJA,sBADA,sBAME,aAAc,KAEhB,6BACE,iBAAkB,QAEpB,uBACE,OAAQ,QAEV,qCACA,qCACE,MAAO,MACP,aAAc,IACd,cAAe,IAEjB,kBACE,OAAQ,kBACR,QAAS,GAEX,6BACE,iBAAkB,KAEpB,yBACA,yCACE,MAAO,KAET,0BACE,MAAO,KAGT,0CASE,gDACA,kDACA,oDAHA,oDAIE,MAAO,EACP,IAAK,EAGP,uDACA,yDACA,2DAHA,2DAIE,MAAO,KACP,KAAM,EAGR,kEAIA,wEAHA,oEAIA,0EAHA,sEAIA,4EAPA,sEAIA,4EAIE,iBAAkB,KAClB,oBAAqB,IAAI,IACzB,iBAAkB,YAClB,aAAc,YACd,cAAe,EACf,WAAY,MAGd,yEACA,2EACA,6EAHA,6EAIE,WAAY,KACZ,aAAc,EAGhB,oEACA,sEACA,wEAHA,wEAIE,cAAe,EAGjB,mFACA,qFACA,uFAHA,uFAIE,cAAe,EAGjB,6CAIA,qDAIA,mDAIA,2DAXA,+CAIA,uDAIA,qDAIA,6DAXA,iDAIA,yDAIA,uDAIA,+DAfA,iDAIA,yDAIA,uDAIA,+DAIE,cAAe,EACf,YAAa,EAGf,8DAIA,sEAHA,gEAIA,wEAHA,kEAIA,0EAPA,kEAIA,0EAIE,cAAe,EAGjB,wEACA,0EACA,4EAHA,4EAIE,aAAc,YACd,iBAAkB,KAClB,iBAAkB,YAOpB,gFAJA,0EAKA,kFAJA,4EAKA,oFAJA,8EACA,oFAJA,8EAQE,MAAO,KACP,UAAW,KAGb,2DACA,6DACA,+DAHA,+DAIE,QAAS,MACT,QAAS,GACT,SAAU,SACV,IAAK,IACL,WAAY,MACZ,MAAO,OACP,MAAO,KACP,OAAQ,KAGV,iEACA,mEACA,qEAHA,qEAIE,aAAc,IAAI,IAAI,EAAE,IACxB,aAAc,MACd,aAAc,YAEd,iBAA80yB,QAC90yB,cAAe,EACf,aAAc,IACd,iBAAkB,KAClB,mBAAoB,EAAE,IAAI,IAAI,EAAE,eACxB,WAAY,EAAE,IAAI,IAAI,EAAE,eAGlC,wEACA,0EACA,4EAHA,4EAIE,WAAY,KAGd,oBACA,sBACA,wBAHA,wBAIE,WAAY,MAOd,qBAJA,iCAKA,uBAJA,mCAKA,yBAJA,qCACA,yBAJA,qCAQE,QAAS,aACT,eAAgB,IAOlB,+BAJA,uBAKA,iCAJA,yBAKA,mCAJA,2BACA,mCAJA,2BAQE,SAAU,SACV,KAAM,IACN,QAAS,mBACT,QAAS,oBACT,QAAS,mBACT,QAAS,YACT,mBAAoB,SACpB,sBAAuB,QACvB,uBAAwB,eACpB,mBAAoB,eAChB,eAAgB,eACxB,WAAY,IACZ,SAAU,QACV,OAAQ,KACR,kBAAmB,kBACf,cAAe,kBACX,UAAW,kBAOrB,2CAJA,mCAKA,6CAJA,qCAKA,+CAJA,uCACA,+CAJA,uCAQE,KAAM,KAGR,gDAIA,0DAIA,6CAIA,mDAXA,kDAIA,4DAIA,+CAIA,qDAXA,oDAIA,8DAIA,iDAIA,uDAfA,oDAIA,8DAIA,iDAIA,uDAIE,YAAa,EACb,eAAgB,EAOlB,sCAJA,8BAKA,wCAJA,gCAKA,0CAJA,kCACA,0CAJA,kCAQE,MAAO,IACP,MAAO,OAOT,kDAJA,0CAKA,oDAJA,4CAKA,sDAJA,8CACA,sDAJA,8CAQE,MAAO,KAOT,+CAJA,uCAKA,iDAJA,yCAKA,mDAJA,2CACA,mDAJA,2CAQE,QAAS,MACT,YAAa,EACb,kBAAmB,iBACf,cAAe,iBACX,UAAW,iBAGrB,+CACA,iDACA,mDAHA,mDAIE,QAAS,KAGX,6DACA,+DACA,iEAHA,iEAIE,MAAO,MACP,YAAa,QACb,QAAS,OAAQ,OAAO,OAAQ,KAChC,cAAe,EACf,iBAAkB,KAClB,oBAAqB,IAAI,IACzB,iBAAkB,KAClB,OAAQ,IAAI,MAAM,QAGpB,qDACA,uDACA,yDAHA,yDAIE,cAAe,EACf,iBAAkB,KAClB,OAAQ,IAAI,MAAM,QAGpB,qDACA,uDACA,yDAHA,yDAIE,QAAS,MACT,QAAS,GACT,SAAU,SACV,IAAK,IACL,WAAY,MACZ,MAAO,KACP,MAAO,KACP,OAAQ,KAGV,+BACA,iCACA,mCAHA,mCAIE,YAAa,MAGf,sCACA,wCACA,0CAHA,0CAIE,aAAc,MACd,YAAa,EAOf,6CAJA,uDAKA,+CAJA,yDAKA,iDAJA,2DACA,iDAJA,2DAQE,QAAS,MACT,WAAY,EACZ,aAAc,EACd,QAAS,IAAI,IAAI,IAAI,KACrB,WAAY,KACZ,aAAc,IAGhB,uDACA,yDACA,2DAHA,2DAIE,iBAAkB,QAGpB,2EACA,6EACA,+EAHA,+EAIE,iBAAkB,QAGpB,wCACA,0CACA,4CAHA,4CAIE,mBAAoB,WACZ,WAAY,WACpB,QAAS,IAAI,EAAE,EACf,aAAc,MACd,aAAc,QACd,iBAAkB,KAClB,cAAe,EACf,mBAAoB,EAAE,IAAI,IAAI,EAAE,eACxB,WAAY,EAAE,IAAI,IAAI,EAAE,eAGlC,wDACA,0DACA,4DAHA,4DAIE,OAAQ,OAAO,KAAK,EACpB,QAAS,EAGX,gEACA,kEACA,oEAHA,oEAIE,cAAe,EACf,iBAAkB,KAClB,OAAQ,IAAI,MAAM,QAClB,iBAAkB,QAGpB,sEACA,wEACA,0EAHA,0EAIE,oBAAqB,EAAE,EACvB,QAAS,EAGX,2CACA,6CACA,+CAHA,+CAIE,QAAS,cAGb,yCAEE,oBACA,sBACA,wBAHA,wBAIE,QAAS,MAGb,yCAEE,qBACA,uBACA,yBAHA,yBAIE,QAAS,MAGb,iBACE,iBAAkB,KAClB,OAAQ,kBACR,aAAc,IACd,QAAS,IAEX,sBACE,aAAc,QAEhB,mBACE,MAAO,KACP,OAAQ,KACR,iBAAkB,QAClB,cAAe,KAEjB,uBACE,KAAM,KAER,wBACE,MAAO,KAET,yBACE,iBAAkB,QAClB,aAAc,QAEhB,sCACE,OAAQ,IAAI,MAAM,KAClB,mBAAoB,EAAE,EAAE,EAAE,IAAI,eACtB,WAAY,EAAE,EAAE,EAAE,IAAI,eAC9B,WAAY,KACZ,MAAO,QAET,qCACE,WAAY,QACZ,OAAQ,IAEV,iBACE,iBAAkB,KAGpB,iBACE,iBAAkB,uBAEpB,yGACE,iBACE,iBAAkB,2BAGtB,sBACE,MAAO;;;;;;;;;;;;;;;;;;;;;;;AAIT,6BADA,0BAEE,iBAAkB,KAIpB,6BADA,0BADA,0BAGE,iBAAkB,QAClB,iBAAkB,KAClB,MAAO,KACP,aAAc,QAEhB,0BACE,aAAc,QAEhB,gCACE,aAAc,YAAY,QAAQ,QAAQ,YAE5C,oBACE,aAAc,QAGhB,yCADA,yCAEE,aAAc,QAEhB,iDACA,8CACE,aAAc,QAEhB,+CACE,iBAAkB,KAGpB,sCADA,yCAEE,aAAc,mBACd,iBAAkB,mBAEpB,oCACE,aAAc,QAGhB,mEADA,sEAEE,oBAAqB,QAGvB,gEADA,mEAEE,mBAAoB,QAEtB,aACA,yBACE,aAAc,QACd,mBAAoB,MAAM,EAAE,EAAE,EAAE,IAAI,KAAM,EAAE,EAAE,EAAE,IAAI,QAC5C,WAAY,MAAM,EAAE,EAAE,EAAE,IAAI,KAAM,EAAE,EAAE,EAAE,IAAI,QAEtD,yBACE,iBAAkB,mBAEpB,2BACE,aAAc,kBACd,iBAAkB,KAEpB,oCACE,MAAO,KACP,iBAAkB,KAEpB,yCACE,iBAAkB,KAClB,aAAc,QAEhB,oEACE,aAAc,QAEhB,4EACE,aAAc,QAEhB,4CACE,iBAAkB,KAClB,MAAO,KAET,gCACA,qCACA,qCACE,iBAAkB,QAEpB,6DACA,6DACE,iBAAkB,QAEpB,0CACE,iBAAkB,QAClB,aAAc,KAEhB,kCACE,iBAAkB,qBAEpB,iEACE,iBAAkB,mBAEpB,mDACE,aAAc,QAEhB,sBACE,cAAe,EACf,iBAAkB,KAClB,mBAAoB,MAAM,EAAE,EAAE,EAAE,IAAI,QAC5B,WAAY,MAAM,EAAE,EAAE,EAAE,IAAI,QAEtC,qCACE,MAAO,KACP,iBAAkB,QAEpB,4BACE,MAAO,KACP,WAAY,QACZ,aAAc,QAEhB,mCACE,aAAc,QAEhB,sBACE,MAAO,KAET,wCACE,MAAO,QAGT,8BADA,sCAEE,aAAc,QACd,cAAe,EAEjB,qCACA,yDACE,aAAc,QAEhB,mFACE,iBAAkB,KAClB,iBAAkB,QAClB,MAAO,KAET,0CACE,aAAc,QACd,mBAAoB,EAAE,IAAI,IAAI,EAAE,eACxB,WAAY,EAAE,IAAI,IAAI,EAAE,eAElC,iDACE,WAAY,KACZ,aAAc,YACd,oBAAqB,QACrB,kBAAmB,QAErB,4CACA,0CACE,WAAY,IACZ,aAAc,QAEhB,2CACE,WAAY,QAEd,2DACE,cAAe,EAGjB,iCACA,uCAFA,iCAGE,cAAe,EAEjB,oCACE,aAAc,QAEhB,0CACE,cAAe,EAEjB,qBACE,cAAe,EAEjB,kCACE,iBAAkB,QAEpB,+BACE,iBAAkB,YAEpB,qCACE,iBAAkB,QAEpB,qCACE,iBAAkB,QAClB,MAAO,KAET,2CACE,iBAAkB,QAEpB,sCACE,aAAc,QAEhB,6DACE,iBAAkB,KAEpB,iEACE,iBAAkB,KAClB,aAAc,QACd,cAAe,EAEjB,cACE,MAAO,KAET,cACE,MAAO,KAET,eACE,YAAa,IAEf,cACE,MAAO,QAET,gBACE,MAAO,IAET,eACE,MAAO,QAET,mBACE,YAAa,IAEf,sBACE,iBAAkB,QAEpB,YACE,aAAc,QACd,iBAAkB,oBAEpB,YACE,aAAc,QACd,iBAAkB,oBAEpB,YACE,aAAc,QACd,iBAAkB,oBAEpB,YACE,aAAc,QACd,iBAAkB,oBAEpB,YACE,aAAc,QACd,iBAAkB,oBAEpB,YACE,aAAc,QACd,iBAAkB,sBAEpB,2CACE,MAAO,KAET,6CACE,iBAAkB,QAClB,MAAO,KAET,0DACE,oBACE,iBAAkB,MAGtB,2BAGA,wCACA,wCACA,4CAJA,mCAKA,8CAJA,sCAKE,QAAS,KACT,SAAU,SACV,MAAO,gBAET,2BACE,aAAc,IAEhB,4CACE,MAAO", "file": "web/kendo.fiori.min.css", "sourceRoot": "/source/", "sourcesContent": ["/** \r\n * Kendo UI v2016.2.714 (http://www.telerik.com/kendo-ui)                                                                                                                                               \r\n * Copyright 2016 Telerik AD. All rights reserved.                                                                                                                                                      \r\n *                                                                                                                                                                                                      \r\n * Kendo UI commercial licenses may be obtained at                                                                                                                                                      \r\n * http://www.telerik.com/purchase/license-agreement/kendo-ui-complete                                                                                                                                  \r\n * If you do not own a commercial license, this file shall be governed by the trial license terms.                                                                                                      \r\n                                                                                                                                                                                                       \r\n                                                                                                                                                                                                       \r\n                                                                                                                                                                                                       \r\n                                                                                                                                                                                                       \r\n                                                                                                                                                                                                       \r\n                                                                                                                                                                                                       \r\n                                                                                                                                                                                                       \r\n                                                                                                                                                                                                       \r\n                                                                                                                                                                                                       \r\n                                                                                                                                                                                                       \r\n                                                                                                                                                                                                       \r\n                                                                                                                                                                                                       \r\n                                                                                                                                                                                                       \r\n                                                                                                                                                                                                       \r\n                                                                                                                                                                                                       \r\n\r\n*/\r\n/* Kendo skin */\r\n.k-theme-test-class,\r\n.ktb-theme-id-fiori {\r\n  opacity: 0;\r\n}\r\n.ktb-var-accent {\r\n  color: #007cc0;\r\n}\r\n.ktb-var-base {\r\n  color: #f2f2f2;\r\n}\r\n.ktb-var-background {\r\n  color: #fff;\r\n}\r\n.ktb-var-border-radius {\r\n  border-radius: 0;\r\n}\r\n.ktb-var-normal-background {\r\n  color: #fff;\r\n}\r\n.ktb-var-normal-gradient {\r\n  background-image: none;\r\n}\r\n.ktb-var-normal-text-color {\r\n  color: #333;\r\n}\r\n.ktb-var-hover-background {\r\n  color: #eaeaea;\r\n}\r\n.ktb-var-hover-gradient {\r\n  background-image: none;\r\n}\r\n.ktb-var-hover-text-color {\r\n  color: #333;\r\n}\r\n.ktb-var-selected-background {\r\n  color: #007cc0;\r\n}\r\n.ktb-var-selected-gradient {\r\n  background-image: none;\r\n}\r\n.ktb-var-selected-text-color {\r\n  color: #fff;\r\n}\r\n.ktb-var-error {\r\n  color: #ffd7d7;\r\n}\r\n.ktb-var-warning {\r\n  color: #fef1d0;\r\n}\r\n.ktb-var-success {\r\n  color: #cce8d8;\r\n}\r\n.ktb-var-info {\r\n  color: #d1eaff;\r\n}\r\n.ktb-var-series-a {\r\n  color: #008fd3;\r\n}\r\n.ktb-var-series-b {\r\n  color: #99d101;\r\n}\r\n.ktb-var-series-c {\r\n  color: #f39b02;\r\n}\r\n.ktb-var-series-d {\r\n  color: #f05662;\r\n}\r\n.ktb-var-series-e {\r\n  color: #c03c53;\r\n}\r\n.ktb-var-series-f {\r\n  color: #acacac;\r\n}\r\n.k-grid-norecords-template {\r\n  background-color: #fff;\r\n  border: 1px solid #bfbfbf;\r\n}\r\n.k-in,\r\n.k-item,\r\n.k-window-action {\r\n  border-color: transparent;\r\n}\r\n/* main colors */\r\n.k-block,\r\n.k-widget {\r\n  background-color: #fff;\r\n}\r\n.k-block,\r\n.k-widget,\r\n.k-input,\r\n.k-textbox,\r\n.k-group,\r\n.k-content,\r\n.k-header,\r\n.k-filter-row > th,\r\n.k-editable-area,\r\n.k-separator,\r\n.k-colorpicker .k-i-arrow-s,\r\n.k-textbox > input,\r\n.k-autocomplete,\r\n.k-dropdown-wrap,\r\n.k-toolbar,\r\n.k-group-footer td,\r\n.k-grid-footer,\r\n.k-footer-template td,\r\n.k-state-default,\r\n.k-state-default .k-select,\r\n.k-state-disabled,\r\n.k-grid-header,\r\n.k-grid-header-wrap,\r\n.k-grid-header-locked,\r\n.k-grid-footer-locked,\r\n.k-grid-content-locked,\r\n.k-grid td,\r\n.k-grid td.k-state-selected,\r\n.k-grid-footer-wrap,\r\n.k-pager-wrap,\r\n.k-pager-wrap .k-link,\r\n.k-pager-refresh,\r\n.k-grouping-header,\r\n.k-grouping-header .k-group-indicator,\r\n.k-panelbar > .k-item > .k-link,\r\n.k-panel > .k-item > .k-link,\r\n.k-panelbar .k-panel,\r\n.k-panelbar .k-content,\r\n.k-treemap-tile,\r\n.k-calendar th,\r\n.k-slider-track,\r\n.k-splitbar,\r\n.k-dropzone-active,\r\n.k-tiles,\r\n.k-toolbar,\r\n.k-tooltip,\r\n.k-button-group .k-tool,\r\n.k-upload-files {\r\n  border-color: #bfbfbf;\r\n}\r\n.k-group,\r\n.k-toolbar,\r\n.k-grouping-header,\r\n.k-pager-wrap,\r\n.k-group-footer td,\r\n.k-grid-footer,\r\n.k-footer-template td,\r\n.k-widget .k-status,\r\n.k-calendar th,\r\n.k-dropzone-hovered,\r\n.k-widget.k-popup {\r\n  background-color: #fff;\r\n}\r\n.k-grouping-row td,\r\ntd.k-group-cell,\r\n.k-resize-handle-inner {\r\n  background-color: #f2f2f2;\r\n}\r\n.k-list-container {\r\n  border-color: rgba(0, 0, 0, 0.2);\r\n  background-color: #fff;\r\n}\r\n.k-content,\r\n.k-editable-area,\r\n.k-panelbar > li.k-item,\r\n.k-panel > li.k-item,\r\n.k-tiles {\r\n  background-color: #fff;\r\n}\r\n.k-alt,\r\n.k-separator,\r\n.k-resource.k-alt,\r\n.k-pivot-layout > tbody > tr:first-child > td:first-child {\r\n  background-color: transparent;\r\n}\r\n.k-pivot-rowheaders .k-alt .k-alt,\r\n.k-header.k-alt {\r\n  background-color: rgba(0, 0, 0, 0);\r\n}\r\n.k-textbox,\r\n.k-autocomplete.k-header,\r\n.k-dropdown-wrap.k-state-active,\r\n.k-picker-wrap.k-state-active,\r\n.k-numeric-wrap.k-state-active {\r\n  border-color: #bfbfbf;\r\n  background-color: #007cc0;\r\n}\r\n.k-textbox > input,\r\n.k-autocomplete .k-input,\r\n.k-dropdown-wrap .k-input,\r\n.k-autocomplete.k-state-focused .k-input,\r\n.k-dropdown-wrap.k-state-focused .k-input,\r\n.k-picker-wrap.k-state-focused .k-input,\r\n.k-numeric-wrap.k-state-focused .k-input {\r\n  border-color: #bfbfbf;\r\n}\r\ninput.k-textbox,\r\ntextarea.k-textbox,\r\ninput.k-textbox:hover,\r\ntextarea.k-textbox:hover,\r\n.k-textbox > input {\r\n  background: none;\r\n}\r\n.k-input,\r\ninput.k-textbox,\r\ntextarea.k-textbox,\r\ninput.k-textbox:hover,\r\ntextarea.k-textbox:hover,\r\n.k-textbox > input,\r\n.k-multiselect-wrap {\r\n  background-color: #fff;\r\n  color: #333;\r\n}\r\n.k-input[readonly] {\r\n  background-color: #fff;\r\n  color: #333;\r\n}\r\n.k-block,\r\n.k-widget,\r\n.k-popup,\r\n.k-content,\r\n.k-toolbar,\r\n.k-dropdown .k-input {\r\n  color: #333;\r\n}\r\n.k-inverse {\r\n  color: #fff;\r\n}\r\n.k-block {\r\n  color: #333;\r\n}\r\n.k-link:link,\r\n.k-link:visited,\r\n.k-nav-current.k-state-hover .k-link {\r\n  color: #007cc0;\r\n}\r\n.k-tabstrip-items .k-link,\r\n.k-panelbar > li > .k-link {\r\n  color: #333;\r\n}\r\n.k-header,\r\n.k-treemap-title,\r\n.k-grid-header .k-header > .k-link {\r\n  color: #333;\r\n}\r\n.k-header,\r\n.k-grid-header,\r\n.k-toolbar,\r\n.k-dropdown-wrap,\r\n.k-picker-wrap,\r\n.k-numeric-wrap,\r\n.k-grouping-header,\r\n.k-pager-wrap,\r\n.k-textbox,\r\n.k-button,\r\n.k-progressbar,\r\n.k-draghandle,\r\n.k-autocomplete,\r\n.k-state-highlight,\r\n.k-tabstrip-items .k-item,\r\n.k-panelbar .k-tabstrip-items .k-item,\r\n.km-pane-wrapper > .km-pane > .km-view > .km-content {\r\n  background-image: none;\r\n  background-position: 50% 50%;\r\n  background-color: #f2f2f2;\r\n}\r\n.k-widget.k-tooltip {\r\n  background-image: none;\r\n}\r\n.k-block,\r\n.k-header,\r\n.k-grid-header,\r\n.k-toolbar,\r\n.k-grouping-header,\r\n.k-pager-wrap,\r\n.k-button,\r\n.k-draghandle,\r\n.k-treemap-tile,\r\nhtml .km-pane-wrapper .k-header {\r\n  background-color: #f2f2f2;\r\n}\r\n/* icons */\r\n.k-icon:hover,\r\n.k-state-hover .k-icon,\r\n.k-state-selected .k-icon,\r\n.k-state-focused .k-icon,\r\n.k-column-menu .k-state-hover .k-sprite,\r\n.k-column-menu .k-state-active .k-sprite,\r\n.k-pager-numbers .k-current-page .k-link:hover:after,\r\n.k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view.k-state-hover > .k-link:after {\r\n  opacity: 1;\r\n}\r\n.k-icon,\r\n.k-state-disabled .k-icon,\r\n.k-column-menu .k-sprite,\r\n.k-pager-numbers .k-current-page .k-link:after,\r\n.k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view > .k-link:after {\r\n  opacity: 0.5;\r\n}\r\n.k-mobile-list .k-check:checked,\r\n.k-mobile-list .k-edit-field [type=checkbox]:checked,\r\n.k-mobile-list .k-edit-field [type=radio]:checked {\r\n  opacity: 0.5;\r\n}\r\n.k-tool {\r\n  border-color: transparent;\r\n}\r\n.k-icon,\r\n.k-tool-icon,\r\n.k-grouping-dropclue,\r\n.k-drop-hint,\r\n.k-column-menu .k-sprite,\r\n.k-grid-mobile .k-resize-handle-inner:before,\r\n.k-grid-mobile .k-resize-handle-inner:after,\r\n.k-pager-numbers .k-current-page .k-link:after,\r\n.k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view > .k-link:after,\r\n.k-gantt-views > .k-current-view > .k-link:after {\r\n  background-image: url('Fiori/sprite.png');\r\n  border-color: transparent;\r\n}\r\n/* IE will ignore the above selectors if these are added too */\r\n.k-mobile-list .k-check:checked,\r\n.k-mobile-list .k-edit-field [type=checkbox]:checked,\r\n.k-mobile-list .k-edit-field [type=radio]:checked {\r\n  background-image: url('Fiori/sprite.png');\r\n  border-color: transparent;\r\n}\r\n.k-loading,\r\n.k-state-hover .k-loading {\r\n  background-image: url('Fiori/loading.gif');\r\n  background-position: 50% 50%;\r\n}\r\n.k-loading-image {\r\n  background-image: url('Fiori/loading-image.gif');\r\n}\r\n.k-loading-color {\r\n  background-color: #fff;\r\n}\r\n.k-button {\r\n  color: #333;\r\n  border-color: #bfbfbf;\r\n  background-color: #f2f2f2;\r\n}\r\n.k-draghandle {\r\n  border-color: #707070;\r\n  background-color: #f2f2f2;\r\n  -webkit-box-shadow: none;\r\n          box-shadow: none;\r\n}\r\n.k-draghandle:hover {\r\n  border-color: #707070;\r\n  background-color: #787878;\r\n  -webkit-box-shadow: none;\r\n          box-shadow: none;\r\n}\r\n/* Scheduler */\r\n.k-scheduler {\r\n  color: #fff;\r\n  background-color: #f2f2f2;\r\n}\r\n.k-scheduler-layout {\r\n  color: #333;\r\n}\r\n.k-scheduler-datecolumn,\r\n.k-scheduler-groupcolumn {\r\n  background-color: #f2f2f2;\r\n  color: #333;\r\n}\r\n.k-scheduler-times tr,\r\n.k-scheduler-times th,\r\n.k-scheduler-table td,\r\n.k-scheduler-header th,\r\n.k-scheduler-header-wrap,\r\n.k-scheduler-times {\r\n  border-color: #c9c9c9;\r\n}\r\n.k-nonwork-hour {\r\n  background-color: #e5e5e5;\r\n}\r\n.k-gantt .k-nonwork-hour {\r\n  background-color: rgba(0, 0, 0, 0.02);\r\n}\r\n.k-gantt .k-header.k-nonwork-hour {\r\n  background-color: rgba(0, 0, 0, 0.2);\r\n}\r\n.k-scheduler-table .k-today,\r\n.k-today > .k-scheduler-datecolumn,\r\n.k-today > .k-scheduler-groupcolumn {\r\n  background-color: #e5e5e5;\r\n}\r\n.k-scheduler-now-arrow {\r\n  border-left-color: #666666;\r\n}\r\n.k-scheduler-now-line {\r\n  background-color: #666666;\r\n}\r\n.k-event,\r\n.k-task-complete {\r\n  border-color: #007cc0;\r\n  background: #007cc0 0 -257px none repeat-x;\r\n  color: #fff;\r\n}\r\n.k-event-inverse {\r\n  color: #333;\r\n}\r\n.k-event.k-state-selected {\r\n  background-position: 0 0;\r\n  -webkit-box-shadow: 0 0 0 2px #333;\r\n          box-shadow: 0 0 0 2px #333;\r\n}\r\n.k-event .k-resize-handle:after,\r\n.k-task-single .k-resize-handle:after {\r\n  background-color: #fff;\r\n}\r\n.k-scheduler-marquee:before,\r\n.k-scheduler-marquee:after {\r\n  border-color: #007cc0;\r\n}\r\n.k-panelbar .k-content,\r\n.k-panelbar .k-panel,\r\n.k-panelbar .k-item {\r\n  background-color: #fff;\r\n  color: #333;\r\n  border-color: #c9c9c9;\r\n}\r\n.k-panelbar > li > .k-link {\r\n  color: #333;\r\n}\r\n.k-panelbar > .k-item > .k-link {\r\n  border-color: #c9c9c9;\r\n}\r\n.k-panel > li.k-item {\r\n  background-color: #fff;\r\n}\r\n/* states */\r\n.k-state-active,\r\n.k-state-active:hover,\r\n.k-active-filter,\r\n.k-tabstrip .k-state-active {\r\n  background-color: #fff;\r\n  border-color: #c9c9c9;\r\n  color: #333;\r\n}\r\n.k-fieldselector .k-list-container {\r\n  background-color: #fff;\r\n}\r\n.k-button:focus,\r\n.k-button.k-state-focused {\r\n  border-color: #007cc0;\r\n}\r\n.k-button:hover,\r\n.k-button.k-state-hover {\r\n  color: #333;\r\n  border-color: #bfbfbf;\r\n  background-color: #eaeaea;\r\n}\r\n.k-button:active,\r\n.k-button.k-state-active {\r\n  color: #fff;\r\n  background-color: #007cc0;\r\n  border-color: #005483;\r\n}\r\n.k-button:active:hover,\r\n.k-button.k-state-active:hover {\r\n  color: #fff;\r\n  border-color: #0089d4;\r\n  background-color: #0089d4;\r\n}\r\n.k-button[disabled],\r\n.k-button.k-state-disabled,\r\n.k-state-disabled .k-button,\r\n.k-state-disabled .k-button:hover,\r\n.k-button.k-state-disabled:hover,\r\n.k-state-disabled .k-button:active,\r\n.k-button.k-state-disabled:active {\r\n  color: #7d7d7d;\r\n  border-color: #f2f2f2;\r\n  background-color: #f2f2f2;\r\n  background-image: none;\r\n}\r\n.k-button:focus:not(.k-state-disabled):not([disabled]) {\r\n  -webkit-box-shadow: none;\r\n          box-shadow: none;\r\n}\r\n.k-button:focus:active:not(.k-state-disabled):not([disabled]) {\r\n  -webkit-box-shadow: none;\r\n          box-shadow: none;\r\n}\r\n.k-menu .k-state-hover > .k-state-active {\r\n  background-color: transparent;\r\n}\r\n.k-menu .k-state-selected > .k-link {\r\n  color: #333;\r\n  background-color: #e5f3ff;\r\n}\r\n.k-menu .k-link.k-state-active {\r\n  background-color: #007cc0;\r\n  color: #fff;\r\n}\r\n.k-menu .k-state-hover > .k-link {\r\n  color: #333;\r\n  background-color: #eaeaea;\r\n  border-color: #eaeaea;\r\n  background-image: none;\r\n}\r\n.k-state-highlight {\r\n  background: #fff;\r\n  color: #333;\r\n}\r\n.k-state-focused,\r\n.k-grouping-row .k-state-focused {\r\n  border-color: #eaeaea;\r\n}\r\n.k-calendar .k-link {\r\n  color: #333;\r\n}\r\n.k-calendar .k-footer {\r\n  padding: 0;\r\n}\r\n.k-calendar .k-footer .k-nav-today {\r\n  color: #333;\r\n  text-decoration: none;\r\n  background-color: #fff;\r\n}\r\n.k-calendar .k-footer .k-nav-today:hover,\r\n.k-calendar .k-footer .k-nav-today.k-state-hover {\r\n  background-color: #fff;\r\n  text-decoration: underline;\r\n}\r\n.k-calendar .k-footer .k-nav-today:active {\r\n  background-color: #fff;\r\n}\r\n.k-calendar .k-link.k-nav-fast {\r\n  color: #333;\r\n}\r\n.k-calendar .k-nav-fast.k-state-hover {\r\n  text-decoration: none;\r\n  background-color: #eaeaea;\r\n  color: #333;\r\n}\r\n.k-calendar .k-link.k-state-hover,\r\n.k-window-titlebar .k-link {\r\n  border-radius: 0;\r\n}\r\n.k-calendar .k-footer .k-link {\r\n  border-radius: 0;\r\n}\r\n.k-calendar th {\r\n  background-color: #fff;\r\n}\r\n.k-calendar-container.k-group {\r\n  border-color: rgba(0, 0, 0, 0.2);\r\n}\r\n.k-state-selected,\r\n.k-state-selected:link,\r\n.k-state-selected:visited,\r\n.k-list > .k-state-selected,\r\n.k-list > .k-state-highlight,\r\n.k-panel > .k-state-selected,\r\n.k-ghost-splitbar-vertical,\r\n.k-ghost-splitbar-horizontal,\r\n.k-draghandle.k-state-selected:hover,\r\n.k-scheduler .k-scheduler-toolbar .k-state-selected,\r\n.k-scheduler .k-today.k-state-selected,\r\n.k-marquee-color {\r\n  color: #fff;\r\n  background-color: #007cc0;\r\n  border-color: #007cc0;\r\n}\r\n.k-virtual-item.k-first,\r\n.k-group-header + .k-list > .k-item.k-first,\r\n.k-static-header + .k-list > .k-item.k-first {\r\n  border-top-color: #eaeaea;\r\n}\r\n.k-group-header + div > .k-list > .k-item.k-first:before {\r\n  border-top-color: #eaeaea;\r\n}\r\n.k-popup > .k-group-header,\r\n.k-popup > .k-virtual-wrap > .k-group-header {\r\n  background: #eaeaea;\r\n  color: #fff;\r\n}\r\n.k-popup .k-list .k-item > .k-group {\r\n  background: #eaeaea;\r\n  color: #fff;\r\n  border-bottom-left-radius: 0;\r\n}\r\n.k-marquee-text {\r\n  color: #fff;\r\n}\r\n.k-state-focused,\r\n.k-list > .k-state-focused,\r\n.k-grid-header th.k-state-focused,\r\ntd.k-state-focused,\r\n.k-button.k-state-focused {\r\n  -webkit-box-shadow: none;\r\n          box-shadow: none;\r\n}\r\n.k-listview > .k-state-focused,\r\n.k-listview > .k-state-focused.k-state-selected {\r\n  -webkit-box-shadow: inset 0 0 0 1px #007cc0;\r\n          box-shadow: inset 0 0 0 1px #007cc0;\r\n}\r\n.k-listview > .k-state-selected {\r\n  color: #333;\r\n  background-color: #e6f3fe;\r\n}\r\n.k-state-focused.k-state-selected,\r\n.k-list > .k-state-focused.k-state-selected,\r\ntd.k-state-focused.k-state-selected {\r\n  -webkit-box-shadow: none;\r\n          box-shadow: none;\r\n}\r\n.k-ie8 .k-panelbar span.k-state-focused,\r\n.k-ie8 .k-menu li.k-state-focused,\r\n.k-ie8 .k-listview > .k-state-focused,\r\n.k-ie8 .k-grid-header th.k-state-focused,\r\n.k-ie8 td.k-state-focused,\r\n.k-ie8 .k-tool.k-state-hover,\r\n.k-ie8 .k-button:focus,\r\n.k-ie8 .k-button.k-state-focused,\r\n.k-list > .k-state-selected.k-state-focused,\r\n.k-list-optionlabel.k-state-selected.k-state-focused {\r\n  -webkit-box-shadow: none;\r\n          box-shadow: none;\r\n}\r\n.k-state-selected > .k-link,\r\n.k-panelbar > li > .k-state-selected,\r\n.k-panelbar > li.k-state-default > .k-link.k-state-selected {\r\n  color: #fff;\r\n}\r\n.k-state-hover,\r\n.k-state-hover:hover,\r\n.k-splitbar-horizontal-hover:hover,\r\n.k-splitbar-vertical-hover:hover,\r\n.k-list > .k-state-hover,\r\n.k-scheduler .k-scheduler-toolbar ul li.k-state-hover,\r\n.k-pager-wrap .k-link:hover,\r\n.k-dropdown .k-state-focused,\r\n.k-filebrowser-dropzone,\r\n.k-mobile-list .k-item > .k-link:active,\r\n.k-mobile-list .k-item > .k-label:active,\r\n.k-mobile-list .k-edit-label.k-check:active,\r\n.k-mobile-list .k-recur-view .k-check:active {\r\n  color: #333;\r\n  background-color: #eaeaea;\r\n  border-color: #eaeaea;\r\n}\r\n/* this selector should be used separately, otherwise old IEs ignore the whole rule */\r\n.k-mobile-list .k-scheduler-timezones .k-edit-field:nth-child(2):active {\r\n  color: #333;\r\n  background-color: #eaeaea;\r\n  border-color: #eaeaea;\r\n}\r\n.k-ie8 .k-window-titlebar .k-state-hover {\r\n  border-color: #eaeaea;\r\n}\r\n.k-state-hover > .k-select,\r\n.k-state-focused > .k-select {\r\n  border-color: #eaeaea;\r\n}\r\n.k-button:hover,\r\n.k-button.k-state-hover,\r\n.k-button:focus,\r\n.k-button.k-state-focused,\r\n.k-textbox:hover,\r\n.k-state-hover,\r\n.k-state-hover:hover,\r\n.k-pager-wrap .k-link:hover,\r\n.k-other-month.k-state-hover .k-link,\r\ndiv.k-filebrowser-dropzone em,\r\n.k-draghandle:hover {\r\n  background-image: none;\r\n}\r\n.k-pager-wrap {\r\n  background-color: #f2f2f2;\r\n  color: #333;\r\n}\r\n.k-autocomplete.k-state-active,\r\n.k-picker-wrap.k-state-active,\r\n.k-numeric-wrap.k-state-active,\r\n.k-dropdown-wrap.k-state-active,\r\n.k-state-active,\r\n.k-state-active:hover,\r\n.k-state-active > .k-link,\r\n.k-button:active,\r\n.k-panelbar > .k-item > .k-state-focused {\r\n  background-image: none;\r\n}\r\n.k-state-selected,\r\n.k-button:active,\r\n.k-button.k-state-active,\r\n.k-draghandle.k-state-selected:hover {\r\n  background-image: none;\r\n}\r\n.k-button:active,\r\n.k-button.k-state-active,\r\n.k-draghandle.k-state-selected:hover {\r\n  background-position: 50% 50%;\r\n}\r\n.k-tool-icon {\r\n  background-image: url('Fiori/sprite.png');\r\n}\r\n.k-state-hover > .k-link,\r\n.k-other-month.k-state-hover .k-link,\r\ndiv.k-filebrowser-dropzone em {\r\n  color: #333;\r\n}\r\n.k-autocomplete.k-state-hover,\r\n.k-autocomplete.k-state-focused,\r\n.k-picker-wrap.k-state-hover,\r\n.k-picker-wrap.k-state-focused,\r\n.k-numeric-wrap.k-state-hover,\r\n.k-numeric-wrap.k-state-focused,\r\n.k-dropdown-wrap.k-state-hover,\r\n.k-dropdown-wrap.k-state-focused {\r\n  background-color: #fff;\r\n  background-image: none;\r\n  background-position: 50% 50%;\r\n  border-color: #007cc0;\r\n}\r\n.km-pane-wrapper .k-mobile-list input:not([type=\"checkbox\"]):not([type=\"radio\"]),\r\n.km-pane-wrapper .km-pane .k-mobile-list select:not([multiple]),\r\n.km-pane-wrapper .k-mobile-list textarea,\r\n.k-dropdown .k-state-focused .k-input {\r\n  color: #333;\r\n}\r\n.km-pane-wrapper .km-pane .k-mobile-list.k-filter-menu .k-space-right {\r\n  background: #fff;\r\n  border-color: #bfbfbf;\r\n}\r\n.km-pane-wrapper .km-pane .k-mobile-list.k-filter-menu .k-space-right > input {\r\n  background-color: #fff;\r\n  border-color: #bfbfbf;\r\n}\r\n.km-pane-wrapper .km-pane .k-mobile-list.k-filter-menu .k-space-right > input:focus {\r\n  border-color: #007cc0;\r\n}\r\n.k-dropdown .k-state-hover .k-input {\r\n  color: #333;\r\n}\r\n.k-state-error {\r\n  border-color: #666666;\r\n  background-color: #ffd7d7;\r\n  color: #666666;\r\n}\r\n.k-state-disabled {\r\n  opacity: .7;\r\n}\r\n.k-ie8 .k-state-disabled {\r\n  filter: alpha(opacity=70);\r\n}\r\n.k-tile-empty.k-state-selected,\r\n.k-loading-mask.k-state-selected {\r\n  border-width: 0;\r\n  background-image: none;\r\n  background-color: transparent;\r\n}\r\n.k-state-disabled,\r\n.k-state-disabled .k-link,\r\n.k-state-disabled .k-button,\r\n.k-other-month,\r\n.k-other-month .k-link,\r\n.k-dropzone em,\r\n.k-dropzone .k-upload-status,\r\n.k-tile-empty strong,\r\n.k-slider .k-draghandle {\r\n  color: #7d7d7d;\r\n}\r\n/* Progressbar */\r\n.k-progressbar-indeterminate {\r\n  background: url('Fiori/indeterminate.gif');\r\n}\r\n.k-progressbar-indeterminate .k-progress-status-wrap,\r\n.k-progressbar-indeterminate .k-state-selected {\r\n  display: none;\r\n}\r\n/* Slider */\r\n.k-slider-track {\r\n  background-color: #bfbfbf;\r\n}\r\n.k-slider-selection {\r\n  background-color: #007cc0;\r\n}\r\n.k-slider-horizontal .k-tick {\r\n  background-image: url('Fiori/slider-h.gif');\r\n}\r\n.k-slider-vertical .k-tick {\r\n  background-image: url('Fiori/slider-v.gif');\r\n}\r\n/* Tooltip */\r\n.k-widget.k-tooltip {\r\n  border-color: #bfbfbf;\r\n  background-color: #fff;\r\n  color: #333;\r\n}\r\n.k-widget.k-tooltip-validation {\r\n  border-color: #fef1d0;\r\n  background-color: #fef1d0;\r\n  color: #996e03;\r\n}\r\n/* Bootstrap theme fix */\r\n.input-prepend .k-tooltip-validation,\r\n.input-append .k-tooltip-validation {\r\n  font-size: 12px;\r\n  position: relative;\r\n  top: 3px;\r\n}\r\n.k-callout-n {\r\n  border-bottom-color: #bfbfbf;\r\n}\r\n.k-callout-w {\r\n  border-right-color: #bfbfbf;\r\n}\r\n.k-callout-s {\r\n  border-top-color: #bfbfbf;\r\n}\r\n.k-callout-e {\r\n  border-left-color: #bfbfbf;\r\n}\r\n.k-tooltip-validation .k-callout-n {\r\n  border-bottom-color: #fef1d0;\r\n}\r\n.k-tooltip-validation .k-callout-w {\r\n  border-right-color: #fef1d0;\r\n}\r\n.k-tooltip-validation .k-callout-s {\r\n  border-top-color: #fef1d0;\r\n}\r\n.k-tooltip-validation .k-callout-e {\r\n  border-left-color: #fef1d0;\r\n}\r\n/* Splitter */\r\n.k-splitbar {\r\n  background-color: #f2f2f2;\r\n}\r\n.k-restricted-size-vertical,\r\n.k-restricted-size-horizontal {\r\n  background-color: #666666;\r\n}\r\n/* Upload */\r\n.k-file {\r\n  background-color: #fff;\r\n  border-color: #c9c9c9;\r\n}\r\n.k-file-progress {\r\n  color: #0072d1;\r\n}\r\n.k-file-progress .k-progress {\r\n  background-color: #d1eaff;\r\n}\r\n.k-file-success {\r\n  color: #387d56;\r\n}\r\n.k-file-success .k-progress {\r\n  background-color: #cce8d8;\r\n}\r\n.k-file-error {\r\n  color: #d70000;\r\n}\r\n.k-file-error .k-progress {\r\n  background-color: #ffd7d7;\r\n}\r\n/* ImageBrowser */\r\n.k-tile {\r\n  border-color: #fff;\r\n}\r\n.k-textbox:hover,\r\n.k-tiles li.k-state-hover {\r\n  border-color: #eaeaea;\r\n}\r\n.k-tiles li.k-state-selected {\r\n  border-color: #007cc0;\r\n}\r\n.k-filebrowser .k-tile .k-folder,\r\n.k-filebrowser .k-tile .k-file {\r\n  background-image: url('Fiori/imagebrowser.png');\r\n  -webkit-background-size: auto auto;\r\n          background-size: auto auto;\r\n}\r\n/* TreeMap */\r\n.k-leaf,\r\n.k-leaf.k-state-hover:hover {\r\n  color: #fff;\r\n}\r\n.k-leaf.k-inverse,\r\n.k-leaf.k-inverse.k-state-hover:hover {\r\n  color: #000;\r\n}\r\n/* Shadows */\r\n.k-widget,\r\n.k-button {\r\n  -webkit-box-shadow: none;\r\n          box-shadow: none;\r\n}\r\n.k-slider,\r\n.k-treeview,\r\n.k-upload {\r\n  -webkit-box-shadow: none;\r\n          box-shadow: none;\r\n}\r\n.k-state-hover {\r\n  -webkit-box-shadow: none;\r\n          box-shadow: none;\r\n}\r\n.k-textbox:focus,\r\n.k-autocomplete.k-state-focused,\r\n.k-dropdown-wrap.k-state-focused,\r\n.k-picker-wrap.k-state-focused,\r\n.k-numeric-wrap.k-state-focused {\r\n  -webkit-box-shadow: none;\r\n          box-shadow: none;\r\n}\r\n.k-state-selected {\r\n  -webkit-box-shadow: none;\r\n          box-shadow: none;\r\n}\r\n.k-state-active {\r\n  -webkit-box-shadow: none;\r\n          box-shadow: none;\r\n}\r\n.k-popup,\r\n.k-menu .k-menu-group,\r\n.k-grid .k-filter-options,\r\n.k-time-popup,\r\n.k-datepicker-calendar,\r\n.k-autocomplete.k-state-border-down,\r\n.k-autocomplete.k-state-border-up,\r\n.k-dropdown-wrap.k-state-active,\r\n.k-picker-wrap.k-state-active,\r\n.k-multiselect.k-state-focused,\r\n.k-filebrowser .k-image,\r\n.k-tooltip {\r\n  -webkit-box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.2);\r\n          box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.2);\r\n}\r\n.k-treemap-tile.k-state-hover {\r\n  -webkit-box-shadow: inset 0 0 0 3px #bfbfbf;\r\n          box-shadow: inset 0 0 0 3px #bfbfbf;\r\n}\r\n/* Window */\r\n.k-window {\r\n  border-color: rgba(0, 0, 0, 0.2);\r\n  -webkit-box-shadow: 1px 1px 13px 1px rgba(128, 128, 128, 0.2);\r\n          box-shadow: 1px 1px 13px 1px rgba(128, 128, 128, 0.2);\r\n  background-color: #fff;\r\n}\r\n.k-window.k-state-focused {\r\n  border-color: rgba(0, 0, 0, 0.2);\r\n  -webkit-box-shadow: 1px 1px 13px 1px rgba(0, 0, 0, 0.2);\r\n          box-shadow: 1px 1px 13px 1px rgba(0, 0, 0, 0.2);\r\n}\r\n.k-window.k-window-maximized,\r\n.k-window-maximized .k-window-titlebar,\r\n.k-window-maximized .k-window-content {\r\n  border-radius: 0;\r\n}\r\n.k-shadow {\r\n  -webkit-box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.2);\r\n          box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.2);\r\n}\r\n.k-inset {\r\n  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.2);\r\n          box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.2);\r\n}\r\n/* Selection */\r\n.k-editor-inline ::-moz-selection {\r\n  background-color: #007cc0;\r\n  text-shadow: none;\r\n  color: #fff;\r\n}\r\n.k-editor-inline ::selection {\r\n  background-color: #007cc0;\r\n  text-shadow: none;\r\n  color: #fff;\r\n}\r\n.k-editor-inline ::-moz-selection {\r\n  background-color: #007cc0;\r\n  text-shadow: none;\r\n  color: #fff;\r\n}\r\n/* Notification */\r\n.k-widget.k-notification.k-notification-info {\r\n  background-color: #d1eaff;\r\n  color: #333;\r\n  border-color: #007cc0;\r\n}\r\n.k-widget.k-notification.k-notification-success {\r\n  background-color: #cce8d8;\r\n  color: #333;\r\n  border-color: #595959;\r\n}\r\n.k-widget.k-notification.k-notification-warning {\r\n  background-color: #fef1d0;\r\n  color: #333;\r\n  border-color: #ff8b8b;\r\n}\r\n.k-widget.k-notification.k-notification-error {\r\n  background-color: #ffd7d7;\r\n  color: #333;\r\n  border-color: #666666;\r\n}\r\n/* Gantt */\r\n.k-gantt .k-treelist {\r\n  background: transparent;\r\n}\r\n.k-gantt .k-treelist .k-alt {\r\n  background-color: rgba(0, 0, 0, 0);\r\n}\r\n.k-gantt .k-treelist .k-state-selected,\r\n.k-gantt .k-treelist .k-state-selected td,\r\n.k-gantt .k-treelist .k-alt.k-state-selected,\r\n.k-gantt .k-treelist .k-alt.k-state-selected > td {\r\n  background-color: #007cc0;\r\n}\r\n.k-task-dot:after {\r\n  background-color: #333;\r\n  border-color: #333;\r\n}\r\n.k-task-dot:hover:after {\r\n  background-color: #fff;\r\n}\r\n.k-task-summary {\r\n  border-color: #cccccc;\r\n  background: #cccccc;\r\n}\r\n.k-task-milestone,\r\n.k-task-summary-complete {\r\n  border-color: #333;\r\n  background: #333;\r\n}\r\n.k-state-selected.k-task-summary {\r\n  border-color: #e5f3ff;\r\n  background: #e5f3ff;\r\n}\r\n.k-state-selected.k-task-milestone,\r\n.k-state-selected .k-task-summary-complete {\r\n  border-color: #007cc0;\r\n  background: #007cc0;\r\n}\r\n.k-task-single {\r\n  background-color: #0093e4;\r\n  border-color: #007cc0;\r\n  color: #fff;\r\n}\r\n.k-state-selected.k-task-single {\r\n  border-color: #007cc0;\r\n}\r\n.k-line {\r\n  background-color: #333;\r\n  color: #333;\r\n}\r\n.k-state-selected.k-line {\r\n  background-color: #007cc0;\r\n  color: #007cc0;\r\n}\r\n.k-resource {\r\n  background-color: #fff;\r\n}\r\n/* PivotGrid */\r\n.k-i-kpi-decrease,\r\n.k-i-kpi-denied,\r\n.k-i-kpi-equal,\r\n.k-i-kpi-hold,\r\n.k-i-kpi-increase,\r\n.k-i-kpi-open {\r\n  background-image: url('Fiori/sprite_kpi.png');\r\n}\r\n/* Border radius */\r\n.k-block,\r\n.k-button,\r\n.k-textbox,\r\n.k-drag-clue,\r\n.k-touch-scrollbar,\r\n.k-window,\r\n.k-window-titleless .k-window-content,\r\n.k-window-action,\r\n.k-inline-block,\r\n.k-grid .k-filter-options,\r\n.k-grouping-header .k-group-indicator,\r\n.k-autocomplete,\r\n.k-multiselect,\r\n.k-combobox,\r\n.k-dropdown,\r\n.k-dropdown-wrap,\r\n.k-datepicker,\r\n.k-timepicker,\r\n.k-colorpicker,\r\n.k-datetimepicker,\r\n.k-notification,\r\n.k-numerictextbox,\r\n.k-picker-wrap,\r\n.k-numeric-wrap,\r\n.k-colorpicker,\r\n.k-list-container,\r\n.k-calendar-container,\r\n.k-calendar td,\r\n.k-calendar .k-link,\r\n.k-treeview .k-in,\r\n.k-editor-inline,\r\n.k-tooltip,\r\n.k-tile,\r\n.k-slider-track,\r\n.k-slider-selection,\r\n.k-upload {\r\n  border-radius: 0;\r\n}\r\n.k-tool {\r\n  text-align: center;\r\n  vertical-align: middle;\r\n}\r\n.k-tool.k-group-start,\r\n.k-toolbar .k-button-group .k-group-start {\r\n  border-radius: 0 0 0 0;\r\n}\r\n.k-rtl .k-tool.k-group-start,\r\n.k-rtl .k-toolbar .k-split-button .k-button,\r\n.k-rtl .k-toolbar .k-button-group .k-group-start {\r\n  border-radius: 0 0 0 0;\r\n}\r\n.k-tool.k-group-end,\r\n.k-toolbar .k-button-group .k-group-end,\r\n.k-toolbar .k-split-button .k-split-button-arrow {\r\n  border-radius: 0 0 0 0;\r\n}\r\n.k-rtl .k-tool.k-group-end,\r\n.k-rtl .k-toolbar .k-button-group .k-group-end,\r\n.k-rtl .k-toolbar .k-split-button .k-split-button-arrow {\r\n  border-radius: 0 0 0 0;\r\n}\r\n.k-group-start.k-group-end.k-tool {\r\n  border-radius: 0;\r\n}\r\n.k-calendar-container.k-state-border-up,\r\n.k-list-container.k-state-border-up,\r\n.k-autocomplete.k-state-border-up,\r\n.k-multiselect.k-state-border-up,\r\n.k-dropdown-wrap.k-state-border-up,\r\n.k-picker-wrap.k-state-border-up,\r\n.k-numeric-wrap.k-state-border-up,\r\n.k-window-content,\r\n.k-filter-menu {\r\n  border-radius: 0 0 0 0;\r\n}\r\n.k-autocomplete.k-state-border-up .k-input,\r\n.k-dropdown-wrap.k-state-border-up .k-input,\r\n.k-picker-wrap.k-state-border-up .k-input,\r\n.k-picker-wrap.k-state-border-up .k-selected-color,\r\n.k-numeric-wrap.k-state-border-up .k-input {\r\n  border-radius: 0 0 0 0;\r\n}\r\n.k-multiselect.k-state-border-up .k-multiselect-wrap {\r\n  border-radius: 0 0 0 0;\r\n}\r\n.k-window-titlebar,\r\n.k-block > .k-header,\r\n.k-tabstrip-items .k-item,\r\n.k-panelbar .k-tabstrip-items .k-item,\r\n.k-tabstrip-items .k-link,\r\n.k-calendar-container.k-state-border-down,\r\n.k-list-container.k-state-border-down,\r\n.k-autocomplete.k-state-border-down,\r\n.k-multiselect.k-state-border-down,\r\n.k-dropdown-wrap.k-state-border-down,\r\n.k-picker-wrap.k-state-border-down,\r\n.k-numeric-wrap.k-state-border-down {\r\n  border-radius: 0 0 0 0;\r\n}\r\n.k-split-button.k-state-border-down > .k-button {\r\n  border-radius: 0 0 0 0;\r\n}\r\n.k-split-button.k-state-border-up > .k-button {\r\n  border-radius: 0 0 0 0;\r\n}\r\n.k-split-button.k-state-border-down > .k-split-button-arrow {\r\n  border-radius: 0 0 0 0;\r\n}\r\n.k-split-button.k-state-border-up > .k-split-button-arrow {\r\n  border-radius: 0 0 0 0;\r\n}\r\n.k-dropdown-wrap .k-input,\r\n.k-picker-wrap .k-input,\r\n.k-numeric-wrap .k-input {\r\n  border-radius: 0 0 0 0;\r\n}\r\n.k-rtl .k-dropdown-wrap .k-input,\r\n.k-rtl .k-picker-wrap .k-input,\r\n.k-rtl .k-numeric-wrap .k-input {\r\n  border-radius: 0 0 0 0;\r\n}\r\n.k-numeric-wrap .k-link {\r\n  border-radius: 0 0 0 0;\r\n}\r\n.k-numeric-wrap .k-link + .k-link {\r\n  border-radius: 0 0 0 0;\r\n}\r\n.k-colorpicker .k-selected-color {\r\n  border-radius: 0 0 0 0;\r\n}\r\n.k-rtl .k-colorpicker .k-selected-color {\r\n  border-radius: 0 0 0 0;\r\n}\r\n.k-autocomplete.k-state-border-down .k-input {\r\n  border-radius: 0 0 0 0;\r\n}\r\n.k-dropdown-wrap.k-state-border-down .k-input,\r\n.k-picker-wrap.k-state-border-down .k-input,\r\n.k-picker-wrap.k-state-border-down .k-selected-color,\r\n.k-numeric-wrap.k-state-border-down .k-input {\r\n  border-radius: 0 0 0 0;\r\n}\r\n.k-numeric-wrap .k-link.k-state-selected {\r\n  background-color: #eaeaea;\r\n}\r\n.k-multiselect.k-state-border-down .k-multiselect-wrap {\r\n  border-radius: 0 0 0 0;\r\n}\r\n.k-dropdown-wrap .k-select,\r\n.k-picker-wrap .k-select,\r\n.k-numeric-wrap .k-select,\r\n.k-datetimepicker .k-select + .k-select,\r\n.k-list-container.k-state-border-right {\r\n  border-radius: 0 0 0 0;\r\n}\r\n.k-rtl .k-dropdown-wrap .k-select,\r\n.k-rtl .k-picker-wrap .k-select,\r\n.k-rtl .k-numeric-wrap .k-select,\r\n.k-rtl .k-datetimepicker .k-select + .k-select,\r\n.k-rtl .k-list-container.k-state-border-right {\r\n  border-radius: 0 0 0 0;\r\n}\r\n.k-numeric-wrap.k-expand-padding .k-input {\r\n  border-radius: 0;\r\n}\r\n.k-textbox > input,\r\n.k-autocomplete .k-input,\r\n.k-multiselect-wrap {\r\n  border-radius: 0;\r\n}\r\n.k-list .k-state-hover,\r\n.k-list .k-state-focused,\r\n.k-list .k-state-highlight,\r\n.k-list .k-state-selected,\r\n.k-fieldselector .k-list .k-item,\r\n.k-list-optionlabel,\r\n.k-dropzone {\r\n  border-radius: 0;\r\n}\r\n.k-slider .k-button,\r\n.k-grid .k-slider .k-button {\r\n  border-radius: 0;\r\n}\r\n.k-draghandle {\r\n  border-radius: 0;\r\n}\r\n.k-scheduler-toolbar > ul li:first-child,\r\n.k-scheduler-toolbar > ul li:first-child .k-link,\r\n.k-scheduler-toolbar > ul.k-scheduler-views li:first-child + li,\r\n.k-scheduler-toolbar > ul.k-scheduler-views li:first-child + li .k-link {\r\n  border-radius: 0 0 0 0;\r\n}\r\n.k-rtl .k-scheduler-toolbar > ul li:first-child,\r\n.k-rtl .k-scheduler-toolbar > ul li:first-child .k-link,\r\n.k-rtl .k-scheduler-toolbar > ul.k-scheduler-views li:first-child + li,\r\n.k-rtl .k-scheduler-toolbar > ul.k-scheduler-views li:first-child + li .k-link,\r\n.km-view.k-popup-edit-form .k-scheduler-toolbar > ul li:last-child,\r\n.km-view.k-popup-edit-form .k-scheduler-toolbar > ul li:last-child .k-link {\r\n  border-radius: 0 0 0 0;\r\n}\r\n.k-scheduler-phone .k-scheduler-toolbar > ul li.k-nav-today,\r\n.k-scheduler-phone .k-scheduler-toolbar > ul li.k-nav-today .k-link,\r\n.k-edit-field > .k-scheduler-navigation {\r\n  border-radius: 0;\r\n}\r\n.k-scheduler-toolbar .k-nav-next,\r\n.k-scheduler-toolbar ul + ul li:last-child,\r\n.k-scheduler-toolbar .k-nav-next .k-link,\r\n.k-scheduler-toolbar ul + ul li:last-child .k-link {\r\n  border-top-right-radius: 0;\r\n  border-bottom-right-radius: 0;\r\n}\r\n.k-rtl .k-scheduler-toolbar .k-nav-next,\r\n.k-rtl .k-scheduler-toolbar ul + ul li:last-child,\r\n.k-rtl .k-scheduler-toolbar .k-nav-next .k-link,\r\n.k-rtl .k-scheduler-toolbar ul + ul li:last-child .k-link {\r\n  border-radius: 0 0 0 0;\r\n}\r\n.k-scheduler div.k-scheduler-footer ul li,\r\n.k-scheduler div.k-scheduler-footer .k-link {\r\n  border-radius: 0;\r\n}\r\n.k-more-events,\r\n.k-event,\r\n.k-task-single,\r\n.k-task-complete,\r\n.k-event .k-link {\r\n  border-radius: 0;\r\n}\r\n.k-scheduler-mobile .k-event {\r\n  border-radius: -1;\r\n}\r\n/* RTL Scheduler & Gantt */\r\n.k-rtl .k-scheduler-toolbar > ul.k-scheduler-navigation > li {\r\n  border-width: 0;\r\n}\r\n.k-rtl .k-scheduler-toolbar > ul > li:first-child {\r\n  border-right-width: 0;\r\n}\r\n.k-rtl .k-gantt-toolbar > ul.k-gantt-views > li,\r\n.k-rtl .k-scheduler-toolbar > ul.k-scheduler-views > li {\r\n  border-width: 0 0 3px 0;\r\n  border-color: transparent;\r\n}\r\n/* Adaptive Grid */\r\n.k-grid-mobile .k-column-active + th.k-header {\r\n  border-left-color: #333;\r\n}\r\nhtml .km-pane-wrapper .km-widget,\r\n.k-ie .km-pane-wrapper .k-widget,\r\n.k-ie .km-pane-wrapper .k-group,\r\n.k-ie .km-pane-wrapper .k-content,\r\n.k-ie .km-pane-wrapper .k-header,\r\n.k-ie .km-pane-wrapper .k-popup-edit-form .k-edit-field .k-button,\r\n.km-pane-wrapper .k-mobile-list .k-item,\r\n.km-pane-wrapper .k-mobile-list .k-edit-label,\r\n.km-pane-wrapper .k-mobile-list .k-edit-field {\r\n  color: #333;\r\n}\r\n@media screen and (-ms-high-contrast: active) and (-ms-high-contrast: none) {\r\n  div.km-pane-wrapper a {\r\n    color: #333;\r\n  }\r\n}\r\n.km-pane-wrapper .k-mobile-list .k-item,\r\n.km-pane-wrapper .k-mobile-list .k-edit-field,\r\n.km-pane-wrapper .k-mobile-list .k-recur-view > .k-edit-field .k-check {\r\n  background-color: #fff;\r\n  border-top: 1px solid #c9c9c9;\r\n}\r\n.km-pane-wrapper .k-mobile-list .k-edit-field textarea {\r\n  outline-width: 0;\r\n}\r\n.km-pane-wrapper .k-mobile-list .k-item.k-state-selected {\r\n  background-color: #007cc0;\r\n  border-top-color: #007cc0;\r\n}\r\n.km-pane-wrapper .k-mobile-list .k-recur-view > .k-edit-field .k-check:first-child {\r\n  border-top-color: transparent;\r\n}\r\n.km-pane-wrapper .k-mobile-list .k-item:last-child {\r\n  -webkit-box-shadow: inset 0 -1px 0 #c9c9c9;\r\n          box-shadow: inset 0 -1px 0 #c9c9c9;\r\n}\r\n.km-pane-wrapper .k-mobile-list > ul > li > .k-link,\r\n.km-pane-wrapper .k-mobile-list .k-recur-view > .k-edit-label:nth-child(3),\r\n.km-pane-wrapper #recurrence .km-scroll-container > .k-edit-label:first-child {\r\n  color: #7d7d7d;\r\n}\r\n.km-pane-wrapper .k-mobile-list > ul > li > .k-link {\r\n  border-bottom: 1px solid #c9c9c9;\r\n}\r\n.km-pane-wrapper .k-mobile-list .k-edit-field {\r\n  -webkit-box-shadow: 0 1px 1px #c9c9c9;\r\n          box-shadow: 0 1px 1px #c9c9c9;\r\n}\r\n.km-actionsheet .k-grid-delete,\r\n.km-actionsheet .k-scheduler-delete,\r\n.km-pane-wrapper .k-scheduler-delete,\r\n.km-pane-wrapper .k-filter-menu .k-button[type=reset] {\r\n  color: #fff;\r\n  border-color: #666666;\r\n  background-color: red;\r\n  background-image: -webkit-gradient(linear, left top, left bottom, from(rgba(255, 255, 255, 0.3)), to(rgba(255, 255, 255, 0.15)));\r\n  background-image: -webkit-linear-gradient(top, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.15));\r\n  background-image: linear-gradient(to bottom, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.15));\r\n}\r\n.km-actionsheet .k-grid-delete:active,\r\n.km-actionsheet .k-scheduler-delete:active,\r\n.km-pane-wrapper .k-scheduler-delete:active,\r\n.km-pane-wrapper .k-filter-menu .k-button[type=reset]:active {\r\n  background-color: #990000;\r\n}\r\n/* /Column Menu */\r\n.k-autocomplete.k-state-default,\r\n.k-picker-wrap.k-state-default,\r\n.k-numeric-wrap.k-state-default,\r\n.k-dropdown-wrap.k-state-default {\r\n  background-image: none;\r\n  background-position: 50% 50%;\r\n  background-color: #fff;\r\n  border-color: #bfbfbf;\r\n}\r\n.k-autocomplete.k-state-hover,\r\n.k-picker-wrap.k-state-hover,\r\n.k-numeric-wrap.k-state-hover,\r\n.k-dropdown-wrap.k-state-hover {\r\n  background-color: #fff;\r\n  background-image: none;\r\n  background-position: 50% 50%;\r\n  border-color: #007cc0;\r\n}\r\n.k-multiselect.k-header {\r\n  border-color: #bfbfbf;\r\n}\r\n.k-multiselect.k-header.k-state-hover {\r\n  border-color: #007cc0;\r\n}\r\n.k-autocomplete.k-state-focused,\r\n.k-picker-wrap.k-state-focused,\r\n.k-numeric-wrap.k-state-focused,\r\n.k-dropdown-wrap.k-state-focused,\r\n.k-multiselect.k-header.k-state-focused {\r\n  background-color: #fff;\r\n  background-image: none;\r\n  background-position: 50% 50%;\r\n  border-color: #007cc0;\r\n  -webkit-box-shadow: none;\r\n          box-shadow: none;\r\n}\r\n.k-list-container {\r\n  color: #333;\r\n}\r\n.k-dropdown .k-input,\r\n.k-dropdown .k-state-focused .k-input,\r\n.k-menu .k-popup {\r\n  color: #333;\r\n}\r\n.k-state-default > .k-select {\r\n  border-color: #bfbfbf;\r\n}\r\n.k-state-hover > .k-select {\r\n  border-color: #007cc0;\r\n}\r\n.k-state-focused > .k-select {\r\n  border-color: #007cc0;\r\n}\r\n.k-tabstrip:focus {\r\n  -webkit-box-shadow: none;\r\n          box-shadow: none;\r\n}\r\n.k-tabstrip-items .k-state-default .k-link,\r\n.k-panelbar > li.k-state-default > .k-link {\r\n  color: #333;\r\n}\r\n.k-tabstrip-items .k-state-hover .k-link,\r\n.k-panelbar > li.k-state-hover > .k-link,\r\n.k-panelbar > li.k-state-default > .k-link.k-state-hover {\r\n  color: #333;\r\n}\r\n.k-panelbar > .k-state-focused.k-state-hover {\r\n  background: #eaeaea;\r\n  -webkit-box-shadow: none;\r\n          box-shadow: none;\r\n}\r\n.k-tabstrip-items .k-state-default {\r\n  border-color: transparent;\r\n}\r\n.k-tabstrip-items .k-state-hover {\r\n  border-color: #eaeaea;\r\n}\r\n.k-tabstrip-items .k-state-active,\r\n.k-panelbar .k-tabstrip-items .k-state-active {\r\n  background-color: #fff;\r\n  background-image: none;\r\n  border-color: #c9c9c9;\r\n}\r\n.k-tabstrip .k-content.k-state-active {\r\n  background-color: #fff;\r\n  color: #333;\r\n}\r\n.k-menu.k-header,\r\n.k-menu .k-item {\r\n  border-color: transparent;\r\n}\r\n.k-column-menu,\r\n.k-column-menu .k-item,\r\n.k-overflow-container .k-overflow-group {\r\n  border-color: #c9c9c9;\r\n}\r\n.k-overflow-container .k-overflow-group {\r\n  -webkit-box-shadow: inset 0 1px 0 #ffffff, 0 1px 0 #ffffff;\r\n          box-shadow: inset 0 1px 0 #ffffff, 0 1px 0 #ffffff;\r\n}\r\n.k-toolbar-first-visible.k-overflow-group,\r\n.k-overflow-container .k-overflow-group + .k-overflow-group {\r\n  -webkit-box-shadow: 0 1px 0 #ffffff;\r\n          box-shadow: 0 1px 0 #ffffff;\r\n}\r\n.k-toolbar-last-visible.k-overflow-group {\r\n  -webkit-box-shadow: inset 0 1px 0 #ffffff;\r\n          box-shadow: inset 0 1px 0 #ffffff;\r\n}\r\n.k-column-menu .k-separator {\r\n  border-color: #c9c9c9;\r\n  background-color: transparent;\r\n}\r\n.k-menu .k-group {\r\n  border-color: rgba(0, 0, 0, 0.2);\r\n}\r\n.k-grid-filter.k-state-active {\r\n  background-color: #fff;\r\n}\r\n.k-grouping-row td,\r\n.k-group-footer td,\r\n.k-grid-footer td {\r\n  color: #333;\r\n  border-color: #c9c9c9;\r\n  font-weight: bold;\r\n}\r\n.k-grouping-header {\r\n  color: #333;\r\n}\r\n.k-grid td.k-state-focused {\r\n  -webkit-box-shadow: inset 0 0 0 1px none;\r\n          box-shadow: inset 0 0 0 1px none;\r\n}\r\n.k-header,\r\n.k-grid-header-wrap,\r\n.k-grid .k-grouping-header,\r\n.k-grid-header,\r\n.k-pager-wrap,\r\n.k-pager-wrap .k-textbox,\r\n.k-pager-wrap .k-link,\r\n.k-grouping-header .k-group-indicator,\r\n.k-gantt-toolbar .k-state-default {\r\n  border-color: #c9c9c9;\r\n}\r\n.k-primary,\r\n.k-overflow-container .k-primary {\r\n  color: #fff;\r\n  border-color: #0089d4;\r\n  background-image: none;\r\n  background-position: 50% 50%;\r\n  background-color: #009aee;\r\n  -webkit-box-shadow: none;\r\n          box-shadow: none;\r\n}\r\n.k-primary:focus,\r\n.k-primary.k-state-focused {\r\n  color: #fff;\r\n  border-color: #005483;\r\n  background-image: none;\r\n  -webkit-box-shadow: none;\r\n          box-shadow: none;\r\n}\r\n.k-primary:hover {\r\n  color: #fff;\r\n  border-color: #0089d4;\r\n  background-image: none;\r\n  background-color: #0089d4;\r\n  -webkit-box-shadow: none;\r\n          box-shadow: none;\r\n}\r\n.k-primary:focus:active:not(.k-state-disabled):not([disabled]),\r\n.k-primary:focus:not(.k-state-disabled):not([disabled]) {\r\n  -webkit-box-shadow: none;\r\n          box-shadow: none;\r\n}\r\n.k-primary:active {\r\n  color: #fff;\r\n  border-color: #0075b6;\r\n  background-image: none;\r\n  background-color: #0075b6;\r\n  -webkit-box-shadow: none;\r\n          box-shadow: none;\r\n}\r\n.k-primary.k-state-disabled,\r\n.k-state-disabled .k-primary,\r\n.k-primary.k-state-disabled:hover,\r\n.k-state-disabled .k-primary:hover,\r\n.k-primary.k-state-disabled:hover,\r\n.k-state-disabled .k-primary:active,\r\n.k-primary.k-state-disabled:active {\r\n  color: #fff;\r\n  border-color: #7ac2ff;\r\n  background-color: #84c7ff;\r\n  background-image: none;\r\n  -webkit-box-shadow: none;\r\n          box-shadow: none;\r\n}\r\n.k-pager-numbers .k-link,\r\n.k-treeview .k-in {\r\n  border-color: transparent;\r\n}\r\n.k-treeview .k-icon,\r\n.k-scheduler-table .k-icon,\r\n.k-grid .k-hierarchy-cell .k-icon {\r\n  background-color: transparent;\r\n  border-radius: 0;\r\n}\r\n.k-scheduler-table .k-state-hover .k-icon {\r\n  background-color: transparent;\r\n}\r\n.k-button:focus,\r\n.k-split-button:focus {\r\n  outline: none;\r\n}\r\n.k-split-button:focus {\r\n  -webkit-box-shadow: 0 0 0px 1px #007cc0;\r\n          box-shadow: 0 0 0px 1px #007cc0;\r\n}\r\n.k-split-button:focus > .k-button {\r\n  background: transparent;\r\n  border-color: transparent;\r\n}\r\n.k-split-button:focus > .k-button.k-split-button-arrow {\r\n  border-left-color: #007cc0;\r\n}\r\n.k-editor .k-tool:focus {\r\n  outline: 0;\r\n  border-color: #007cc0;\r\n  -webkit-box-shadow: none;\r\n          box-shadow: none;\r\n}\r\n.k-checkbox-label:before {\r\n  border-color: #bfbfbf;\r\n  background: #fff;\r\n  border-radius: 1px;\r\n}\r\n.k-checkbox-label:hover:before,\r\n.k-checkbox:checked + .k-checkbox-label:hover:before {\r\n  border-color: #007cc0;\r\n  -webkit-box-shadow: none;\r\n          box-shadow: none;\r\n}\r\n.k-checkbox:checked + .k-checkbox-label:before {\r\n  background-color: #fff;\r\n  border-color: #bfbfbf;\r\n  color: #007cc0;\r\n}\r\n.k-checkbox-label:active:before {\r\n  -webkit-box-shadow: none;\r\n          box-shadow: none;\r\n  border-color: #007cc0;\r\n}\r\n.k-checkbox:checked + .k-checkbox-label:active:before {\r\n  -webkit-box-shadow: none;\r\n          box-shadow: none;\r\n  border-color: #007cc0;\r\n}\r\n.k-checkbox:disabled + .k-checkbox-label {\r\n  color: #bababa;\r\n}\r\n.k-checkbox:disabled + .k-checkbox-label:hover:before {\r\n  -webkit-box-shadow: none;\r\n          box-shadow: none;\r\n}\r\n.k-checkbox:disabled + .k-checkbox-label:before,\r\n.k-checkbox:checked:disabled + .k-checkbox-label:before,\r\n.k-checkbox:checked:disabled + .k-checkbox-label:active:before,\r\n.k-checkbox:checked:disabled + .k-checkbox-label:hover:before {\r\n  color: #bababa;\r\n  background: #f4f4f4;\r\n  border-color: #bfbfbf;\r\n  border-radius: 1px;\r\n}\r\n.k-checkbox:focus + .k-checkbox-label:before {\r\n  border-color: #007cc0;\r\n  -webkit-box-shadow: none;\r\n          box-shadow: none;\r\n}\r\n.k-checkbox:indeterminate + .k-checkbox-label:after {\r\n  background-color: #007cc0;\r\n  background-image: none;\r\n  border-color: #007cc0;\r\n  border-radius: 0px;\r\n}\r\n.k-checkbox:indeterminate:hover + .k-checkbox-label:after {\r\n  border-color: #007cc0;\r\n  background-color: #007cc0;\r\n}\r\n.k-radio-label:before {\r\n  border-color: #bfbfbf;\r\n  border-radius: 50%;\r\n  background-color: #fff;\r\n  border-width: 2px;\r\n}\r\n.k-radio-label:hover:before,\r\n.k-radio:checked + .k-radio-label:hover:before {\r\n  border-color: #007cc0;\r\n  -webkit-box-shadow: none;\r\n          box-shadow: none;\r\n}\r\n.k-radio:checked + .k-radio-label:after {\r\n  background-color: #007cc0;\r\n  border-radius: 50%;\r\n}\r\n.k-radio-label:active:before {\r\n  border-color: #bfbfbf;\r\n  -webkit-box-shadow: none;\r\n          box-shadow: none;\r\n}\r\n.k-radio:checked + .k-radio-label:active:before {\r\n  -webkit-box-shadow: none;\r\n          box-shadow: none;\r\n  border-color: #bfbfbf;\r\n}\r\n.k-radio:disabled + .k-radio-label {\r\n  color: #bfbfbf;\r\n}\r\n.k-radio:disabled + .k-radio-label:before,\r\n.k-radio:disabled + .k-radio-label:active:before,\r\n.k-radio:disabled + .k-radio-label:hover:after,\r\n.k-radio:disabled + .k-radio-label:hover:before {\r\n  background: #fff;\r\n  border-color: #bfbfbf;\r\n  -webkit-box-shadow: none;\r\n          box-shadow: none;\r\n}\r\n.k-radio:disabled:checked + .k-radio-label:after {\r\n  background-color: #007cc0;\r\n  opacity: .5;\r\n}\r\n.k-radio:focus + .k-radio-label:before {\r\n  border-color: #bfbfbf;\r\n  -webkit-box-shadow: none;\r\n          box-shadow: none;\r\n}\r\n.k-radio:checked + .k-radio-label:active:before {\r\n  border-color: #007cc0;\r\n}\r\n.k-radio:disabled:checked + .k-radio-label:active:before {\r\n  border-color: #bfbfbf;\r\n}\r\n@media only screen and (-webkit-min-device-pixel-ratio: 1.2), only screen and (min-device-pixel-ratio: 1.2) {\r\n  .k-icon:not(.k-loading),\r\n  .k-grouping-dropclue,\r\n  .k-drop-hint,\r\n  .k-callout,\r\n  .k-tool-icon,\r\n  .k-state-hover .k-tool-icon,\r\n  .k-state-active .k-tool-icon,\r\n  .k-state-active.k-state-hover .k-tool-icon,\r\n  .k-state-selected .k-tool-icon,\r\n  .k-state-selected.k-state-hover .k-tool-icon,\r\n  .k-column-menu .k-sprite,\r\n  .k-mobile-list .k-check:checked,\r\n  .k-mobile-list .k-edit-field [type=checkbox]:checked,\r\n  .k-mobile-list .k-edit-field [type=radio]:checked {\r\n    background-image: url('Fiori/sprite_2x.png');\r\n    -webkit-background-size: 340px 336px;\r\n            background-size: 340px 336px;\r\n  }\r\n  .k-dropdown-wrap .k-input,\r\n  .k-picker-wrap .k-input,\r\n  .k-numeric-wrap .k-input {\r\n    border-radius: 0 0 0 0;\r\n  }\r\n  .k-i-kpi-decrease,\r\n  .k-i-kpi-denied,\r\n  .k-i-kpi-equal,\r\n  .k-i-kpi-hold,\r\n  .k-i-kpi-increase,\r\n  .k-i-kpi-open {\r\n    background-image: url('Fiori/sprite_kpi_2x.png');\r\n    -webkit-background-size: 96px 16px;\r\n            background-size: 96px 16px;\r\n  }\r\n}\r\n@media screen and (-ms-high-contrast: active) {\r\n  .k-editor-toolbar-wrap .k-dropdown-wrap.k-state-focused,\r\n  .k-editor-toolbar-wrap .k-button-group .k-tool:focus {\r\n    border-color: #fff;\r\n  }\r\n}\r\n/* Responsive styles */\r\n@media only screen and (max-width: 1024px) {\r\n  .k-webkit .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view,\r\n  .k-ff .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view,\r\n  .k-ie11 .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view,\r\n  .k-safari .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view,\r\n  .k-webkit .k-pager-numbers .k-current-page .k-link,\r\n  .k-ff .k-pager-numbers .k-current-page .k-link,\r\n  .k-ie11 .k-pager-numbers .k-current-page .k-link,\r\n  .k-safari .k-pager-numbers .k-current-page .k-link {\r\n    background-image: none;\r\n    background-position: 50% 50%;\r\n    background-color: #fff;\r\n    border-color: #bfbfbf;\r\n  }\r\n  .k-webkit .k-pager-numbers .k-current-page .k-link,\r\n  .k-ff .k-pager-numbers .k-current-page .k-link,\r\n  .k-ie11 .k-pager-numbers .k-current-page .k-link,\r\n  .k-safari .k-pager-numbers .k-current-page .k-link {\r\n    border-color: #c9c9c9;\r\n  }\r\n  .k-webkit .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view,\r\n  .k-ff .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view,\r\n  .k-ie11 .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view,\r\n  .k-safari .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view {\r\n    border-radius: 0;\r\n  }\r\n  .k-webkit .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded > li,\r\n  .k-ff .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded > li,\r\n  .k-ie11 .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded > li,\r\n  .k-safari .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded > li {\r\n    border-radius: 0;\r\n  }\r\n  .k-webkit .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded > li.k-current-view,\r\n  .k-ff .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded > li.k-current-view,\r\n  .k-ie11 .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded > li.k-current-view,\r\n  .k-safari .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded > li.k-current-view {\r\n    border-radius: 0 0 0 0;\r\n  }\r\n  .k-webkit .k-scheduler-toolbar > ul li:first-child,\r\n  .k-ff .k-scheduler-toolbar > ul li:first-child,\r\n  .k-ie11 .k-scheduler-toolbar > ul li:first-child,\r\n  .k-safari .k-scheduler-toolbar > ul li:first-child,\r\n  .k-webkit .k-scheduler-toolbar > ul li:first-child .k-link,\r\n  .k-ff .k-scheduler-toolbar > ul li:first-child .k-link,\r\n  .k-ie11 .k-scheduler-toolbar > ul li:first-child .k-link,\r\n  .k-safari .k-scheduler-toolbar > ul li:first-child .k-link,\r\n  .k-webkit .k-scheduler-toolbar > ul.k-scheduler-views li,\r\n  .k-ff .k-scheduler-toolbar > ul.k-scheduler-views li,\r\n  .k-ie11 .k-scheduler-toolbar > ul.k-scheduler-views li,\r\n  .k-safari .k-scheduler-toolbar > ul.k-scheduler-views li,\r\n  .k-webkit .k-scheduler-toolbar > ul.k-scheduler-views li .k-link,\r\n  .k-ff .k-scheduler-toolbar > ul.k-scheduler-views li .k-link,\r\n  .k-ie11 .k-scheduler-toolbar > ul.k-scheduler-views li .k-link,\r\n  .k-safari .k-scheduler-toolbar > ul.k-scheduler-views li .k-link {\r\n    border-radius: 0;\r\n  }\r\n  .k-webkit .k-scheduler-toolbar > ul.k-scheduler-views li:last-child,\r\n  .k-ff .k-scheduler-toolbar > ul.k-scheduler-views li:last-child,\r\n  .k-ie11 .k-scheduler-toolbar > ul.k-scheduler-views li:last-child,\r\n  .k-safari .k-scheduler-toolbar > ul.k-scheduler-views li:last-child,\r\n  .k-webkit .k-scheduler-toolbar > ul.k-scheduler-views li:last-child .k-link,\r\n  .k-ff .k-scheduler-toolbar > ul.k-scheduler-views li:last-child .k-link,\r\n  .k-ie11 .k-scheduler-toolbar > ul.k-scheduler-views li:last-child .k-link,\r\n  .k-safari .k-scheduler-toolbar > ul.k-scheduler-views li:last-child .k-link {\r\n    border-radius: 0 0 0 0;\r\n  }\r\n  .k-webkit .k-pager-numbers .k-current-page .k-link:hover,\r\n  .k-ff .k-pager-numbers .k-current-page .k-link:hover,\r\n  .k-ie11 .k-pager-numbers .k-current-page .k-link:hover,\r\n  .k-safari .k-pager-numbers .k-current-page .k-link:hover,\r\n  .k-webkit .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view:hover,\r\n  .k-ff .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view:hover,\r\n  .k-ie11 .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view:hover,\r\n  .k-safari .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view:hover {\r\n    border-color: #eaeaea;\r\n    background-image: none;\r\n    background-color: #eaeaea;\r\n  }\r\n  .k-webkit .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view > .k-link,\r\n  .k-ff .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view > .k-link,\r\n  .k-ie11 .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view > .k-link,\r\n  .k-safari .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view > .k-link {\r\n    color: #333;\r\n    min-width: 75px;\r\n  }\r\n  .k-webkit .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view:hover > .k-link,\r\n  .k-ff .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view:hover > .k-link,\r\n  .k-ie11 .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view:hover > .k-link,\r\n  .k-safari .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view:hover > .k-link {\r\n    color: #333;\r\n  }\r\n  .k-webkit .k-pager-numbers .k-current-page .k-link:after,\r\n  .k-ff .k-pager-numbers .k-current-page .k-link:after,\r\n  .k-ie11 .k-pager-numbers .k-current-page .k-link:after,\r\n  .k-safari .k-pager-numbers .k-current-page .k-link:after,\r\n  .k-webkit .k-scheduler-views > li.k-state-selected > .k-link:after,\r\n  .k-ff .k-scheduler-views > li.k-state-selected > .k-link:after,\r\n  .k-ie11 .k-scheduler-views > li.k-state-selected > .k-link:after,\r\n  .k-safari .k-scheduler-views > li.k-state-selected > .k-link:after {\r\n    display: block;\r\n    content: \"\";\r\n    position: absolute;\r\n    top: 50%;\r\n    margin-top: -0.5em;\r\n    right: 0.333em;\r\n    width: 1.333em;\r\n    height: 1.333em;\r\n  }\r\n  .k-webkit .k-pager-numbers.k-state-expanded,\r\n  .k-ff .k-pager-numbers.k-state-expanded,\r\n  .k-ie11 .k-pager-numbers.k-state-expanded,\r\n  .k-safari .k-pager-numbers.k-state-expanded,\r\n  .k-webkit .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded,\r\n  .k-ff .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded,\r\n  .k-ie11 .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded,\r\n  .k-safari .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded {\r\n    border-width: 1px 1px 0 1px;\r\n    border-style: solid;\r\n    border-color: #c9c9c9;\r\n    background-color: #f2f2f2;\r\n    border-radius: 0 0 0 0;\r\n    -webkit-box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.2);\r\n            box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.2);\r\n  }\r\n  .k-webkit .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded,\r\n  .k-ff .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded,\r\n  .k-ie11 .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded,\r\n  .k-safari .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded {\r\n    border-width: 1px;\r\n    background-image: none;\r\n    border-radius: 0;\r\n  }\r\n  .k-webkit .k-pager-numbers .k-state-selected,\r\n  .k-ff .k-pager-numbers .k-state-selected,\r\n  .k-ie11 .k-pager-numbers .k-state-selected,\r\n  .k-safari .k-pager-numbers .k-state-selected,\r\n  .k-webkit .k-pager-numbers .k-link,\r\n  .k-ff .k-pager-numbers .k-link,\r\n  .k-ie11 .k-pager-numbers .k-link,\r\n  .k-safari .k-pager-numbers .k-link {\r\n    border-radius: 0;\r\n  }\r\n  .k-webkit .k-widget.k-grid .k-pager-nav + .k-pager-numbers,\r\n  .k-ff .k-widget.k-grid .k-pager-nav + .k-pager-numbers,\r\n  .k-ie11 .k-widget.k-grid .k-pager-nav + .k-pager-numbers,\r\n  .k-safari .k-widget.k-grid .k-pager-nav + .k-pager-numbers {\r\n    position: absolute;\r\n  }\r\n}\r\n.k-button,\r\n.k-split-button {\r\n  border-radius: 2px;\r\n}\r\n.k-toolbar .k-split-button .k-button {\r\n  border-radius: 2px 0 0 2px;\r\n}\r\n.k-list > .k-state-focused {\r\n  border-color: #007cc0;\r\n}\r\n.k-menu > .k-state-focused,\r\n.k-menu-group > .k-state-focused {\r\n  background-color: #eaeaea;\r\n}\r\n.k-textbox:hover,\r\n.k-textbox:focus {\r\n  border-color: #007cc0;\r\n}\r\n.k-splitbar {\r\n  border-color: transparent;\r\n}\r\n.k-flatcolorpicker .k-hue-slider .k-draghandle:hover,\r\n.k-flatcolorpicker .k-transparency-slider .k-draghandle:hover,\r\n.k-flatcolorpicker .k-hue-slider .k-draghandle:focus,\r\n.k-flatcolorpicker .k-transparency-slider .k-draghandle:focus {\r\n  border-color: #eaeaea;\r\n}\r\n.k-calendar .k-header,\r\n.k-menu.k-header,\r\n.k-tabstrip.k-header,\r\n.k-tabstrip .k-tabstrip-items .k-item,\r\n.k-header.k-scheduler-toolbar,\r\n.k-scheduler-footer > .k-header {\r\n  background-color: #fff;\r\n}\r\n.k-header.k-scheduler-views,\r\n.k-header.k-datepicker,\r\n.k-header.k-datetimepicker,\r\n.k-header.k-timepicker,\r\n.k-header.k-dropdown,\r\n.k-scheduler-navigation > .k-header,\r\n.k-header.k-gantt-views,\r\n.k-gantt-navigation > .k-header {\r\n  background-color: transparent;\r\n}\r\n.k-scheduler-header th,\r\n.k-scheduler-times th,\r\n.k-calendar th {\r\n  color: #666;\r\n  font-weight: normal;\r\n  font-size: .8em;\r\n  text-transform: uppercase;\r\n}\r\n.k-tabstrip .k-tabstrip-items .k-state-hover {\r\n  border-color: transparent;\r\n}\r\n.k-tabstrip .k-tabstrip-items .k-state-hover .k-link {\r\n  color: #007cc0;\r\n}\r\n.k-tabstrip .k-tabstrip-items .k-state-active {\r\n  border-color: #007cc0;\r\n}\r\n.k-tabstrip .k-tabstrip-items .k-state-active .k-link {\r\n  color: #007cc0;\r\n  border-color: #007cc0;\r\n}\r\ndiv.k-window,\r\ndiv.k-window.k-state-focused {\r\n  border-color: #666;\r\n}\r\n.k-window-titlebar.k-header {\r\n  background: none;\r\n  color: #666;\r\n}\r\n.k-list > .k-state-selected,\r\n.k-group > .k-item > .k-state-selected,\r\n.k-gantt .k-treelist .k-state-selected,\r\n.k-gantt .k-treelist .k-state-selected td,\r\n.k-gantt .k-treelist .k-alt.k-state-selected,\r\n.k-gantt .k-treelist .k-alt.k-state-selected > td {\r\n  background-color: #e5f3ff;\r\n  color: #333;\r\n}\r\n.k-grid tr:hover {\r\n  background-color: #eaeaea;\r\n}\r\n.k-pivot-rowheaders .k-grid tr:hover {\r\n  background: none;\r\n}\r\n.k-grid-header th.k-state-focused,\r\n.k-grid td.k-state-focused {\r\n  -webkit-box-shadow: inset 0 0 0 1px #007cc0;\r\n          box-shadow: inset 0 0 0 1px #007cc0;\r\n}\r\n.k-grid tr.k-state-selected,\r\n.k-grid td.k-state-selected,\r\n.k-grid td.k-state-selected.k-state-focused {\r\n  color: #333;\r\n  background-color: #e6f3fe;\r\n}\r\n.k-grid td.k-state-selected:hover,\r\n.k-grid tr.k-state-selected:hover td {\r\n  background-color: #d6ecff;\r\n}\r\n.k-gantt-treelist {\r\n  background: #f2f2f2;\r\n}\r\n.k-grid tr td,\r\n.k-grid tr th {\r\n  border-left-color: transparent;\r\n}\r\n.k-pager-wrap > .k-link,\r\n.k-pager-numbers .k-link,\r\n.k-pager-numbers .k-link:hover,\r\n.k-pager-numbers .k-state-selected {\r\n  background: none;\r\n  border-radius: 0;\r\n}\r\n.k-pager-wrap > .k-link,\r\n.k-pager-wrap > .k-link.k-state-disabled:hover,\r\n.k-pager-wrap > .k-link:hover,\r\n.k-pager-numbers .k-link:hover {\r\n  border-radius: 0;\r\n  border-color: transparent;\r\n}\r\n.k-pager-wrap > .k-link:hover {\r\n  border-color: #bfbfbf;\r\n}\r\n.k-pager-numbers .k-link {\r\n  border-color: transparent;\r\n}\r\n.k-grouping-header,\r\n.k-grid-toolbar {\r\n  padding: 0.714em;\r\n}\r\n.k-window .k-edit-buttons {\r\n  background: #000;\r\n}\r\n.k-scheduler .k-scheduler-views .k-state-selected,\r\n.k-gantt .k-gantt-views .k-state-selected {\r\n  background: none;\r\n}\r\n.k-gantt .k-gantt-views > li {\r\n  border-radius: 0;\r\n}\r\n.k-gantt .k-gantt-views .k-state-selected {\r\n  border-color: #007cc0;\r\n}\r\n.k-task-summary-complete {\r\n  border-color: #007cc0;\r\n  background: #007cc0;\r\n}\r\n.k-state-selected .k-task-summary-complete {\r\n  border-color: #0075b6;\r\n  background: #0075b6;\r\n}\r\n.k-task-milestone {\r\n  border-color: #99d101;\r\n  background: #99d101;\r\n}\r\n.k-task-dot:after {\r\n  background-color: #7d7d7d;\r\n  border-color: #7d7d7d;\r\n}\r\n.k-task-dot:hover:after {\r\n  background-color: #444;\r\n}\r\n.k-scheduler-views .k-link:link,\r\n.k-scheduler-views .k-link:visited,\r\n.k-gantt-views .k-link:link,\r\n.k-gantt-views .k-link:visited,\r\n.k-scheduler-navigation .k-nav-today .k-link,\r\n.k-group-indicator .k-link,\r\n.k-pager-numbers .k-link {\r\n  color: #333;\r\n}\r\n.k-calendar .k-footer .k-nav-today,\r\n.k-calendar .k-link.k-nav-fast,\r\n.k-scheduler .k-scheduler-views > .k-state-selected > .k-link,\r\n.k-gantt .k-gantt-views > .k-state-selected > .k-link,\r\n.k-pager-numbers .k-state-selected {\r\n  color: #007cc0;\r\n}\r\n.k-scheduler .k-scheduler-toolbar ul li.k-state-hover,\r\n.k-gantt .k-gantt-toolbar ul li.k-state-hover {\r\n  background: none;\r\n  border-color: transparent;\r\n}\r\n.k-scheduler .k-scheduler-toolbar ul li.k-state-selected,\r\n.k-gantt .k-gantt-toolbar ul li.k-state-selected {\r\n  border-color: #007cc0;\r\n}\r\n.k-scheduler .k-scheduler-toolbar .k-state-hover .k-link,\r\n.k-gantt .k-gantt-toolbar .k-state-hover .k-link {\r\n  color: #007cc0;\r\n}\r\n.k-calendar td {\r\n  background: #f2f2f2;\r\n  border-color: #fff;\r\n}\r\n.k-calendar td.k-state-focused {\r\n  -webkit-box-shadow: inset 0 0 0px 1px #007cc0;\r\n          box-shadow: inset 0 0 0px 1px #007cc0;\r\n}\r\n.k-calendar td.k-weekend {\r\n  background: #e5e5e5;\r\n}\r\n.k-calendar td.k-today {\r\n  background: #007cc0;\r\n}\r\n.k-calendar .k-today .k-link {\r\n  color: #fff;\r\n}\r\n.k-calendar .k-today.k-state-hover {\r\n  background-color: #0086c0;\r\n}\r\n.k-calendar .k-today.k-state-focused,\r\n.k-calendar .k-today:active,\r\n.k-calendar .k-today.k-state-selected:active {\r\n  -webkit-box-shadow: inset 0 0 0px 1px #005483;\r\n          box-shadow: inset 0 0 0px 1px #005483;\r\n}\r\n.k-calendar td.k-other-month {\r\n  background: transparent;\r\n}\r\n.k-calendar td.k-state-selected {\r\n  background: #e9f7ff;\r\n}\r\n.k-calendar td.k-state-selected .k-link {\r\n  color: #333;\r\n}\r\n.k-calendar td.k-state-hover {\r\n  background: #eaeaea;\r\n  border-color: #fff;\r\n}\r\n.k-calendar td:active,\r\n.k-calendar td.k-state-selected.k-state-hover {\r\n  background-color: #cfe6f3;\r\n}\r\n.k-scheduler-table .k-other-month,\r\n.k-scheduler-header th {\r\n  background: #fff;\r\n}\r\n.k-scheduler-times tr,\r\n.k-scheduler-times th,\r\n.k-scheduler-table td,\r\n.k-scheduler-header th,\r\n.k-scheduler-header-wrap,\r\n.k-scheduler-times {\r\n  border-color: #fff;\r\n}\r\n.k-scheduler-header tr + tr > th {\r\n  border-top-color: #c9c9c9;\r\n}\r\n.k-scheduler-header th {\r\n  height: 2.714em;\r\n}\r\n.k-scheduler .k-scheduler-datecolumn,\r\n.k-scheduler .k-scheduler-timecolumn {\r\n  width: 150px;\r\n  padding-left: 7px;\r\n  padding-right: 7px;\r\n}\r\n.k-state-disabled {\r\n  filter: alpha(opacity=50);\r\n  opacity: .5;\r\n}\r\n.k-dropdown .k-dropdown-wrap {\r\n  background-color: #fff;\r\n}\r\n.k-popup > .k-group-header,\r\n.k-popup > .k-virtual-wrap > .k-group-header {\r\n  color: #333;\r\n}\r\n.k-popup .k-item > .k-group {\r\n  color: #333;\r\n}\r\n/* Responsive styles */\r\n@media only screen and (max-width: 1024px) {\r\n  .k-webkit,\r\n  .k-ff,\r\n  .k-ie11,\r\n  .k-safari {\r\n    /* Responsive Scheduler */\r\n    /* Responsive Pager */\r\n  }\r\n  .k-webkit .k-scheduler-toolbar > ul.k-scheduler-views,\r\n  .k-ff .k-scheduler-toolbar > ul.k-scheduler-views,\r\n  .k-ie11 .k-scheduler-toolbar > ul.k-scheduler-views,\r\n  .k-safari .k-scheduler-toolbar > ul.k-scheduler-views {\r\n    right: 0;\r\n    top: 0;\r\n  }\r\n  .k-webkit .k-rtl .k-scheduler-toolbar > ul.k-scheduler-views,\r\n  .k-ff .k-rtl .k-scheduler-toolbar > ul.k-scheduler-views,\r\n  .k-ie11 .k-rtl .k-scheduler-toolbar > ul.k-scheduler-views,\r\n  .k-safari .k-rtl .k-scheduler-toolbar > ul.k-scheduler-views {\r\n    right: auto;\r\n    left: 0;\r\n  }\r\n  .k-webkit .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view,\r\n  .k-ff .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view,\r\n  .k-ie11 .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view,\r\n  .k-safari .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view,\r\n  .k-webkit .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view:hover,\r\n  .k-ff .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view:hover,\r\n  .k-ie11 .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view:hover,\r\n  .k-safari .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view:hover {\r\n    background-image: none;\r\n    background-position: 50% 50%;\r\n    background-color: transparent;\r\n    border-color: transparent;\r\n    border-radius: 0;\r\n    text-align: right;\r\n  }\r\n  .k-webkit .k-rtl .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view,\r\n  .k-ff .k-rtl .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view,\r\n  .k-ie11 .k-rtl .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view,\r\n  .k-safari .k-rtl .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view {\r\n    text-align: left;\r\n    padding-left: 0;\r\n  }\r\n  .k-webkit .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded > li,\r\n  .k-ff .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded > li,\r\n  .k-ie11 .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded > li,\r\n  .k-safari .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded > li {\r\n    border-radius: 0;\r\n  }\r\n  .k-webkit .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded > li.k-current-view,\r\n  .k-ff .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded > li.k-current-view,\r\n  .k-ie11 .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded > li.k-current-view,\r\n  .k-safari .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded > li.k-current-view {\r\n    border-radius: 0 0 0 0;\r\n  }\r\n  .k-webkit .k-scheduler-toolbar > ul li:first-child,\r\n  .k-ff .k-scheduler-toolbar > ul li:first-child,\r\n  .k-ie11 .k-scheduler-toolbar > ul li:first-child,\r\n  .k-safari .k-scheduler-toolbar > ul li:first-child,\r\n  .k-webkit .k-scheduler-toolbar > ul li:first-child .k-link,\r\n  .k-ff .k-scheduler-toolbar > ul li:first-child .k-link,\r\n  .k-ie11 .k-scheduler-toolbar > ul li:first-child .k-link,\r\n  .k-safari .k-scheduler-toolbar > ul li:first-child .k-link,\r\n  .k-webkit .k-scheduler-toolbar > ul.k-scheduler-views li,\r\n  .k-ff .k-scheduler-toolbar > ul.k-scheduler-views li,\r\n  .k-ie11 .k-scheduler-toolbar > ul.k-scheduler-views li,\r\n  .k-safari .k-scheduler-toolbar > ul.k-scheduler-views li,\r\n  .k-webkit .k-scheduler-toolbar > ul.k-scheduler-views li .k-link,\r\n  .k-ff .k-scheduler-toolbar > ul.k-scheduler-views li .k-link,\r\n  .k-ie11 .k-scheduler-toolbar > ul.k-scheduler-views li .k-link,\r\n  .k-safari .k-scheduler-toolbar > ul.k-scheduler-views li .k-link {\r\n    border-radius: 0;\r\n    padding-top: 0;\r\n  }\r\n  .k-webkit .k-scheduler-toolbar > ul.k-scheduler-views li:last-child,\r\n  .k-ff .k-scheduler-toolbar > ul.k-scheduler-views li:last-child,\r\n  .k-ie11 .k-scheduler-toolbar > ul.k-scheduler-views li:last-child,\r\n  .k-safari .k-scheduler-toolbar > ul.k-scheduler-views li:last-child,\r\n  .k-webkit .k-scheduler-toolbar > ul.k-scheduler-views li:last-child .k-link,\r\n  .k-ff .k-scheduler-toolbar > ul.k-scheduler-views li:last-child .k-link,\r\n  .k-ie11 .k-scheduler-toolbar > ul.k-scheduler-views li:last-child .k-link,\r\n  .k-safari .k-scheduler-toolbar > ul.k-scheduler-views li:last-child .k-link {\r\n    border-radius: 0 0 0 0;\r\n  }\r\n  .k-webkit .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view:hover,\r\n  .k-ff .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view:hover,\r\n  .k-ie11 .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view:hover,\r\n  .k-safari .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view:hover {\r\n    border-color: transparent;\r\n    background-image: none;\r\n    background-color: transparent;\r\n  }\r\n  .k-webkit .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view > .k-link,\r\n  .k-ff .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view > .k-link,\r\n  .k-ie11 .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view > .k-link,\r\n  .k-safari .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view > .k-link,\r\n  .k-webkit .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view:hover > .k-link,\r\n  .k-ff .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view:hover > .k-link,\r\n  .k-ie11 .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view:hover > .k-link,\r\n  .k-safari .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view:hover > .k-link {\r\n    color: #333;\r\n    min-width: 20px;\r\n  }\r\n  .k-webkit .k-scheduler-views > li.k-state-selected > .k-link:after,\r\n  .k-ff .k-scheduler-views > li.k-state-selected > .k-link:after,\r\n  .k-ie11 .k-scheduler-views > li.k-state-selected > .k-link:after,\r\n  .k-safari .k-scheduler-views > li.k-state-selected > .k-link:after {\r\n    display: block;\r\n    content: \"\";\r\n    position: absolute;\r\n    top: 50%;\r\n    margin-top: -0.5em;\r\n    right: 0.333em;\r\n    width: 16px;\r\n    height: 16px;\r\n  }\r\n  .k-webkit .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded,\r\n  .k-ff .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded,\r\n  .k-ie11 .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded,\r\n  .k-safari .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded {\r\n    border-width: 1px 1px 0 1px;\r\n    border-style: solid;\r\n    border-color: transparent;\r\n    /*@secondary-border-color*/\r\n    background-color: #f2f2f2;\r\n    border-radius: 0;\r\n    border-width: 1px;\r\n    background-image: none;\r\n    -webkit-box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.2);\r\n            box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.2);\r\n  }\r\n  .k-webkit .k-rtl .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded,\r\n  .k-ff .k-rtl .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded,\r\n  .k-ie11 .k-rtl .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded,\r\n  .k-safari .k-rtl .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded {\r\n    text-align: left;\r\n  }\r\n  .k-webkit .k-pager-wrap,\r\n  .k-ff .k-pager-wrap,\r\n  .k-ie11 .k-pager-wrap,\r\n  .k-safari .k-pager-wrap {\r\n    min-height: 3.8em;\r\n  }\r\n  .k-webkit .k-pager-wrap .k-pager-nav,\r\n  .k-ff .k-pager-wrap .k-pager-nav,\r\n  .k-ie11 .k-pager-wrap .k-pager-nav,\r\n  .k-safari .k-pager-wrap .k-pager-nav,\r\n  .k-webkit .k-pager-input,\r\n  .k-ff .k-pager-input,\r\n  .k-ie11 .k-pager-input,\r\n  .k-safari .k-pager-input {\r\n    display: inline-block;\r\n    vertical-align: top;\r\n  }\r\n  .k-webkit .k-pager-numbers,\r\n  .k-ff .k-pager-numbers,\r\n  .k-ie11 .k-pager-numbers,\r\n  .k-safari .k-pager-numbers,\r\n  .k-webkit .k-grid .k-pager-numbers,\r\n  .k-ff .k-grid .k-pager-numbers,\r\n  .k-ie11 .k-grid .k-pager-numbers,\r\n  .k-safari .k-grid .k-pager-numbers {\r\n    position: absolute;\r\n    left: 5em;\r\n    display: -webkit-inline-box;\r\n    display: -webkit-inline-flex;\r\n    display: -ms-inline-flexbox;\r\n    display: inline-flex;\r\n    -webkit-box-orient: vertical;\r\n    -webkit-box-direction: reverse;\r\n    -webkit-flex-direction: column-reverse;\r\n        -ms-flex-direction: column-reverse;\r\n            flex-direction: column-reverse;\r\n    margin-top: 6px;\r\n    overflow: visible;\r\n    height: auto;\r\n    -webkit-transform: translatey(-100%);\r\n        -ms-transform: translatey(-100%);\r\n            transform: translatey(-100%);\r\n  }\r\n  .k-webkit .k-pager-numbers:first-child,\r\n  .k-ff .k-pager-numbers:first-child,\r\n  .k-ie11 .k-pager-numbers:first-child,\r\n  .k-safari .k-pager-numbers:first-child,\r\n  .k-webkit .k-grid .k-pager-numbers:first-child,\r\n  .k-ff .k-grid .k-pager-numbers:first-child,\r\n  .k-ie11 .k-grid .k-pager-numbers:first-child,\r\n  .k-safari .k-grid .k-pager-numbers:first-child {\r\n    left: .3em;\r\n  }\r\n  .k-webkit .km-pane-wrapper .k-pager-numbers .k-link,\r\n  .k-ff .km-pane-wrapper .k-pager-numbers .k-link,\r\n  .k-ie11 .km-pane-wrapper .k-pager-numbers .k-link,\r\n  .k-safari .km-pane-wrapper .k-pager-numbers .k-link,\r\n  .k-webkit .km-pane-wrapper .k-pager-numbers .k-state-selected,\r\n  .k-ff .km-pane-wrapper .k-pager-numbers .k-state-selected,\r\n  .k-ie11 .km-pane-wrapper .k-pager-numbers .k-state-selected,\r\n  .k-safari .km-pane-wrapper .k-pager-numbers .k-state-selected,\r\n  .k-webkit .km-pane-wrapper .k-pager-wrap > .k-link,\r\n  .k-ff .km-pane-wrapper .k-pager-wrap > .k-link,\r\n  .k-ie11 .km-pane-wrapper .k-pager-wrap > .k-link,\r\n  .k-safari .km-pane-wrapper .k-pager-wrap > .k-link,\r\n  .k-webkit .km-pane-wrapper .k-pager-wrap > .k-pager-info,\r\n  .k-ff .km-pane-wrapper .k-pager-wrap > .k-pager-info,\r\n  .k-ie11 .km-pane-wrapper .k-pager-wrap > .k-pager-info,\r\n  .k-safari .km-pane-wrapper .k-pager-wrap > .k-pager-info {\r\n    padding-top: 0;\r\n    padding-bottom: 0;\r\n  }\r\n  .k-webkit .k-rtl .k-pager-numbers,\r\n  .k-ff .k-rtl .k-pager-numbers,\r\n  .k-ie11 .k-rtl .k-pager-numbers,\r\n  .k-safari .k-rtl .k-pager-numbers,\r\n  .k-webkit .k-rtl .k-grid .k-pager-numbers,\r\n  .k-ff .k-rtl .k-grid .k-pager-numbers,\r\n  .k-ie11 .k-rtl .k-grid .k-pager-numbers,\r\n  .k-safari .k-rtl .k-grid .k-pager-numbers {\r\n    right: 5em;\r\n    width: 5.15em;\r\n  }\r\n  .k-webkit .k-rtl .k-pager-numbers:first-child,\r\n  .k-ff .k-rtl .k-pager-numbers:first-child,\r\n  .k-ie11 .k-rtl .k-pager-numbers:first-child,\r\n  .k-safari .k-rtl .k-pager-numbers:first-child,\r\n  .k-webkit .k-rtl .k-grid .k-pager-numbers:first-child,\r\n  .k-ff .k-rtl .k-grid .k-pager-numbers:first-child,\r\n  .k-ie11 .k-rtl .k-grid .k-pager-numbers:first-child,\r\n  .k-safari .k-rtl .k-grid .k-pager-numbers:first-child {\r\n    right: .3em;\r\n  }\r\n  .k-webkit .k-pager-numbers .k-current-page,\r\n  .k-ff .k-pager-numbers .k-current-page,\r\n  .k-ie11 .k-pager-numbers .k-current-page,\r\n  .k-safari .k-pager-numbers .k-current-page,\r\n  .k-webkit .k-grid .k-pager-numbers .k-current-page,\r\n  .k-ff .k-grid .k-pager-numbers .k-current-page,\r\n  .k-ie11 .k-grid .k-pager-numbers .k-current-page,\r\n  .k-safari .k-grid .k-pager-numbers .k-current-page {\r\n    display: block;\r\n    border-left: 0;\r\n    -webkit-transform: translatey(100%);\r\n        -ms-transform: translatey(100%);\r\n            transform: translatey(100%);\r\n  }\r\n  .k-webkit .k-pager-numbers li:not(.k-current-page),\r\n  .k-ff .k-pager-numbers li:not(.k-current-page),\r\n  .k-ie11 .k-pager-numbers li:not(.k-current-page),\r\n  .k-safari .k-pager-numbers li:not(.k-current-page) {\r\n    display: none;\r\n  }\r\n  .k-webkit .k-pager-wrap .k-pager-numbers .k-current-page .k-link,\r\n  .k-ff .k-pager-wrap .k-pager-numbers .k-current-page .k-link,\r\n  .k-ie11 .k-pager-wrap .k-pager-numbers .k-current-page .k-link,\r\n  .k-safari .k-pager-wrap .k-pager-numbers .k-current-page .k-link {\r\n    width: 3.8em;\r\n    line-height: 1.429em;\r\n    padding: 0.643em .429em 0.643em 0.8em;\r\n    border-radius: 0;\r\n    background-image: none;\r\n    background-position: 50% 50%;\r\n    background-color: #fff;\r\n    border: 1px solid #bfbfbf;\r\n  }\r\n  .k-webkit .k-pager-numbers .k-current-page:hover .k-link,\r\n  .k-ff .k-pager-numbers .k-current-page:hover .k-link,\r\n  .k-ie11 .k-pager-numbers .k-current-page:hover .k-link,\r\n  .k-safari .k-pager-numbers .k-current-page:hover .k-link {\r\n    border-radius: 0;\r\n    background-color: #fff;\r\n    border: 1px solid #007cc0;\r\n  }\r\n  .k-webkit .k-pager-numbers .k-current-page .k-link:after,\r\n  .k-ff .k-pager-numbers .k-current-page .k-link:after,\r\n  .k-ie11 .k-pager-numbers .k-current-page .k-link:after,\r\n  .k-safari .k-pager-numbers .k-current-page .k-link:after {\r\n    display: block;\r\n    content: \"\";\r\n    position: absolute;\r\n    top: 50%;\r\n    margin-top: -0.6em;\r\n    right: 0.8em;\r\n    width: 16px;\r\n    height: 16px;\r\n  }\r\n  .k-webkit .k-pager-numbers + .k-link,\r\n  .k-ff .k-pager-numbers + .k-link,\r\n  .k-ie11 .k-pager-numbers + .k-link,\r\n  .k-safari .k-pager-numbers + .k-link {\r\n    margin-left: 5.4em;\r\n  }\r\n  .k-webkit .k-rtl .k-pager-numbers + .k-link,\r\n  .k-ff .k-rtl .k-pager-numbers + .k-link,\r\n  .k-ie11 .k-rtl .k-pager-numbers + .k-link,\r\n  .k-safari .k-rtl .k-pager-numbers + .k-link {\r\n    margin-right: 5.2em;\r\n    margin-left: 0;\r\n  }\r\n  .k-webkit .k-pager-wrap .k-pager-numbers .k-state-selected,\r\n  .k-ff .k-pager-wrap .k-pager-numbers .k-state-selected,\r\n  .k-ie11 .k-pager-wrap .k-pager-numbers .k-state-selected,\r\n  .k-safari .k-pager-wrap .k-pager-numbers .k-state-selected,\r\n  .k-webkit .k-pager-wrap .k-pager-numbers .k-link,\r\n  .k-ff .k-pager-wrap .k-pager-numbers .k-link,\r\n  .k-ie11 .k-pager-wrap .k-pager-numbers .k-link,\r\n  .k-safari .k-pager-wrap .k-pager-numbers .k-link {\r\n    display: block;\r\n    margin-top: 0;\r\n    margin-right: 0;\r\n    padding: 1px 5px 1px .8em;\r\n    text-align: left;\r\n    border-width: 1px;\r\n  }\r\n  .k-webkit .k-pager-wrap .k-pager-numbers .k-state-selected,\r\n  .k-ff .k-pager-wrap .k-pager-numbers .k-state-selected,\r\n  .k-ie11 .k-pager-wrap .k-pager-numbers .k-state-selected,\r\n  .k-safari .k-pager-wrap .k-pager-numbers .k-state-selected {\r\n    background-color: #e5f3ff;\r\n  }\r\n  .k-webkit .k-pager-wrap .k-pager-numbers li:not(.k-current-page) .k-link:hover,\r\n  .k-ff .k-pager-wrap .k-pager-numbers li:not(.k-current-page) .k-link:hover,\r\n  .k-ie11 .k-pager-wrap .k-pager-numbers li:not(.k-current-page) .k-link:hover,\r\n  .k-safari .k-pager-wrap .k-pager-numbers li:not(.k-current-page) .k-link:hover {\r\n    background-color: #eaeaea;\r\n  }\r\n  .k-webkit .k-pager-numbers.k-state-expanded,\r\n  .k-ff .k-pager-numbers.k-state-expanded,\r\n  .k-ie11 .k-pager-numbers.k-state-expanded,\r\n  .k-safari .k-pager-numbers.k-state-expanded {\r\n    -webkit-box-sizing: border-box;\r\n            box-sizing: border-box;\r\n    padding: 5px 0 0;\r\n    border-style: solid;\r\n    border-color: #bfbfbf;\r\n    background-color: #fff;\r\n    border-radius: 0 0 0 0;\r\n    -webkit-box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.2);\r\n            box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.2);\r\n  }\r\n  .k-webkit .k-pager-numbers.k-state-expanded .k-current-page,\r\n  .k-ff .k-pager-numbers.k-state-expanded .k-current-page,\r\n  .k-ie11 .k-pager-numbers.k-state-expanded .k-current-page,\r\n  .k-safari .k-pager-numbers.k-state-expanded .k-current-page {\r\n    margin: -2.5em -1px 0;\r\n    padding: 0;\r\n  }\r\n  .k-webkit .k-pager-numbers.k-state-expanded .k-current-page .k-link,\r\n  .k-ff .k-pager-numbers.k-state-expanded .k-current-page .k-link,\r\n  .k-ie11 .k-pager-numbers.k-state-expanded .k-current-page .k-link,\r\n  .k-safari .k-pager-numbers.k-state-expanded .k-current-page .k-link {\r\n    border-radius: 0 0 0 0;\r\n    background-color: #fff;\r\n    border: 1px solid #007cc0;\r\n    border-top-color: #bfbfbf;\r\n  }\r\n  .k-webkit .k-pager-numbers.k-state-expanded .k-current-page .k-link:after,\r\n  .k-ff .k-pager-numbers.k-state-expanded .k-current-page .k-link:after,\r\n  .k-ie11 .k-pager-numbers.k-state-expanded .k-current-page .k-link:after,\r\n  .k-safari .k-pager-numbers.k-state-expanded .k-current-page .k-link:after {\r\n    background-position: 0 0;\r\n    opacity: 1;\r\n  }\r\n  .k-webkit .k-pager-numbers.k-state-expanded li,\r\n  .k-ff .k-pager-numbers.k-state-expanded li,\r\n  .k-ie11 .k-pager-numbers.k-state-expanded li,\r\n  .k-safari .k-pager-numbers.k-state-expanded li {\r\n    display: inline-block;\r\n  }\r\n}\r\n@media only screen and (max-width: 640px) {\r\n  .k-webkit .k-pager-info,\r\n  .k-ff .k-pager-info,\r\n  .k-ie11 .k-pager-info,\r\n  .k-safari .k-pager-info {\r\n    display: none;\r\n  }\r\n}\r\n@media only screen and (max-width: 480px) {\r\n  .k-webkit .k-pager-sizes,\r\n  .k-ff .k-pager-sizes,\r\n  .k-ie11 .k-pager-sizes,\r\n  .k-safari .k-pager-sizes {\r\n    display: none;\r\n  }\r\n}\r\n.k-chart .k-mask {\r\n  background-color: #fff;\r\n  filter: alpha(opacity=68);\r\n  -moz-opacity: 0.68;\r\n  opacity: 0.68;\r\n}\r\n.k-chart .k-selection {\r\n  border-color: #e5e5e5;\r\n}\r\n.k-chart .k-handle {\r\n  width: 15px;\r\n  height: 15px;\r\n  background-color: #00b0ff;\r\n  border-radius: 10px;\r\n}\r\n.k-chart .k-leftHandle {\r\n  left: -8px;\r\n}\r\n.k-chart .k-rightHandle {\r\n  right: -8px;\r\n}\r\n.k-chart .k-handle:hover {\r\n  background-color: #00b0ff;\r\n  border-color: #00b0ff;\r\n}\r\n.k-chart .k-navigator-hint .k-tooltip {\r\n  border: 3px solid #ffffff;\r\n  -webkit-box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.2);\r\n          box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.2);\r\n  background: #ffffff;\r\n  color: #242424;\r\n}\r\n.k-chart .k-navigator-hint .k-scroll {\r\n  background: #3f51b5;\r\n  height: 4px;\r\n}\r\n.k-chart-tooltip {\r\n  background-image: none;\r\n}\r\n/* Map */\r\n.k-map .k-marker {\r\n  background-image: url(\"Fiori/markers.png\");\r\n}\r\n@media only screen and (-webkit-min-device-pixel-ratio: 1.2), only screen and (min-device-pixel-ratio: 1.2) {\r\n  .k-map .k-marker {\r\n    background-image: url(\"Fiori/markers_2x.png\");\r\n  }\r\n}\r\n.k-map .k-attribution {\r\n  color: #666666;\r\n}\r\n.k-spreadsheet-row-header,\r\n.k-spreadsheet-column-header {\r\n  background-color: #fff;\r\n}\r\n.k-spreadsheet-top-corner,\r\n.k-spreadsheet-row-header,\r\n.k-spreadsheet-column-header {\r\n  background-color: #f2f2f2;\r\n  background-image: none;\r\n  color: #000000;\r\n  border-color: #bfbfbf;\r\n}\r\n.k-spreadsheet-top-corner {\r\n  border-color: #bfbfbf;\r\n}\r\n.k-spreadsheet-top-corner:after {\r\n  border-color: transparent #bfbfbf #bfbfbf transparent;\r\n}\r\n.k-spreadsheet-pane {\r\n  border-color: #bfbfbf;\r\n}\r\n.k-spreadsheet-pane .k-spreadsheet-vaxis,\r\n.k-spreadsheet-pane .k-spreadsheet-haxis {\r\n  border-color: #dedede;\r\n}\r\n.k-spreadsheet-pane .k-spreadsheet-column-header,\r\n.k-spreadsheet-pane .k-spreadsheet-row-header {\r\n  border-color: #bfbfbf;\r\n}\r\n.k-spreadsheet-pane .k-spreadsheet-merged-cell {\r\n  background-color: #fff;\r\n}\r\n.k-spreadsheet-pane .k-selection-partial,\r\n.k-spreadsheet-pane .k-selection-full {\r\n  border-color: rgba(0, 124, 192, 0.2);\r\n  background-color: rgba(0, 124, 192, 0.2);\r\n}\r\n.k-spreadsheet-pane .k-filter-range {\r\n  border-color: #007cc0;\r\n}\r\n.k-spreadsheet-pane .k-spreadsheet-column-header .k-selection-partial,\r\n.k-spreadsheet-pane .k-spreadsheet-column-header .k-selection-full {\r\n  border-bottom-color: #007cc0;\r\n}\r\n.k-spreadsheet-pane .k-spreadsheet-row-header .k-selection-partial,\r\n.k-spreadsheet-pane .k-spreadsheet-row-header .k-selection-full {\r\n  border-right-color: #007cc0;\r\n}\r\n.k-auto-fill,\r\n.k-spreadsheet-selection {\r\n  border-color: #007cc0;\r\n  -webkit-box-shadow: inset 0 0 0 1px #fff, 0 0 0 1px #007cc0;\r\n          box-shadow: inset 0 0 0 1px #fff, 0 0 0 1px #007cc0;\r\n}\r\n.k-spreadsheet-selection {\r\n  background-color: rgba(0, 124, 192, 0.2);\r\n}\r\n.k-spreadsheet-active-cell {\r\n  border-color: #007cc0 !important;\r\n  background-color: #fff;\r\n}\r\n.k-spreadsheet-active-cell.k-single {\r\n  background-color: #fff;\r\n}\r\n.k-spreadsheet > .k-spreadsheet-formula-bar {\r\n  background-color: #fff;\r\n  border-color: #f2f2f2 #f2f2f2 #bfbfbf;\r\n}\r\n.k-spreadsheet > .k-spreadsheet-formula-bar:before {\r\n  border-color: #bfbfbf;\r\n}\r\n.k-spreadsheet > .k-spreadsheet-formula-bar:after {\r\n  border-color: #f2f2f2;\r\n}\r\n.k-spreadsheet .k-spreadsheet-formula-input {\r\n  background-color: #fff;\r\n  color: #333;\r\n}\r\n.k-spreadsheet .k-resize-handle,\r\n.k-spreadsheet .k-resize-hint-handle,\r\n.k-spreadsheet .k-resize-hint-marker {\r\n  background-color: #007cc0;\r\n}\r\n.k-spreadsheet .k-resize-hint-vertical .k-resize-hint-handle,\r\n.k-spreadsheet .k-resize-hint-vertical .k-resize-hint-marker {\r\n  background-color: #007cc0;\r\n}\r\n.k-spreadsheet .k-single-selection::after {\r\n  background-color: #007cc0;\r\n  border-color: #fff;\r\n}\r\n.k-spreadsheet .k-auto-fill-punch {\r\n  background-color: rgba(255, 255, 255, 0.5);\r\n}\r\n.k-spreadsheet .k-single-selection.k-dim-auto-fill-handle::after {\r\n  background-color: rgba(0, 124, 192, 0.5);\r\n}\r\n.k-spreadsheet-format-cells .k-spreadsheet-preview {\r\n  border-color: #bfbfbf;\r\n}\r\n.k-spreadsheet-filter {\r\n  border-radius: 0;\r\n  background-color: #fff;\r\n  -webkit-box-shadow: inset 0 0 0 1px #dedede;\r\n          box-shadow: inset 0 0 0 1px #dedede;\r\n}\r\n.k-spreadsheet-filter.k-state-active {\r\n  color: #fff;\r\n  background-color: #007cc0;\r\n}\r\n.k-spreadsheet-filter:hover {\r\n  color: #333;\r\n  background: #eaeaea;\r\n  border-color: #d6d6d6;\r\n}\r\n.k-action-window .k-action-buttons {\r\n  border-color: #bfbfbf;\r\n}\r\n.k-spreadsheet-sample {\r\n  color: #808080;\r\n}\r\n.k-state-selected .k-spreadsheet-sample {\r\n  color: inherit;\r\n}\r\n.k-spreadsheet-window .k-list-wrapper,\r\n.k-spreadsheet-window .k-list {\r\n  border-color: #bfbfbf;\r\n  border-radius: 0;\r\n}\r\n.k-spreadsheet-window .export-config,\r\n.k-spreadsheet-window .k-edit-field > .k-orientation-label {\r\n  border-color: #bfbfbf;\r\n}\r\n.k-spreadsheet-window .k-edit-field > input[type=\"radio\"]:checked + .k-orientation-label {\r\n  background-image: none;\r\n  background-color: #007cc0;\r\n  color: #ffffff;\r\n}\r\n.k-spreadsheet-window .k-page-orientation {\r\n  border-color: #cecece;\r\n  -webkit-box-shadow: 0 5px 5px 0 rgba(0, 0, 0, 0.1);\r\n          box-shadow: 0 5px 5px 0 rgba(0, 0, 0, 0.1);\r\n}\r\n.k-spreadsheet-window .k-page-orientation:before {\r\n  background: #fff;\r\n  border-color: transparent;\r\n  border-bottom-color: #cecece;\r\n  border-left-color: #cecece;\r\n}\r\n.k-spreadsheet-window .k-margins-horizontal,\r\n.k-spreadsheet-window .k-margins-vertical {\r\n  background: transparent;\r\n  border-color: #bfbfbf;\r\n}\r\n.k-spreadsheet-toolbar.k-toolbar .k-button-group .k-button {\r\n  border-radius: 0;\r\n}\r\n.k-spreadsheet-toolbar > .k-widget,\r\n.k-spreadsheet-toolbar > .k-button,\r\n.k-spreadsheet-toolbar > .k-button-group {\r\n  border-radius: 0;\r\n}\r\n.k-spreadsheet-toolbar > .k-separator {\r\n  border-color: #bfbfbf;\r\n}\r\n.k-spreadsheet-toolbar .k-overflow-anchor {\r\n  border-radius: 0;\r\n}\r\n.k-spreadsheet-popup {\r\n  border-radius: 0;\r\n}\r\n.k-spreadsheet-popup .k-separator {\r\n  background-color: #bfbfbf;\r\n}\r\n.k-spreadsheet-popup .k-button {\r\n  background-color: transparent;\r\n}\r\n.k-spreadsheet-popup .k-button:hover {\r\n  background-color: #eaeaea;\r\n}\r\n.k-spreadsheet-popup .k-state-active {\r\n  background-color: #007cc0;\r\n  color: #000000;\r\n}\r\n.k-spreadsheet-popup .k-state-active:hover {\r\n  background-color: #005b8d;\r\n}\r\n.k-spreadsheet-filter-menu .k-details {\r\n  border-color: #bfbfbf;\r\n}\r\n.k-spreadsheet-filter-menu .k-details-content .k-space-right {\r\n  background-color: #fff;\r\n}\r\n.k-spreadsheet-filter-menu .k-spreadsheet-value-treeview-wrapper {\r\n  background-color: #fff;\r\n  border-color: #bfbfbf;\r\n  border-radius: 0 0 0 0;\r\n}\r\n.k-syntax-ref {\r\n  color: #ff8822;\r\n}\r\n.k-syntax-num {\r\n  color: #0099ff;\r\n}\r\n.k-syntax-func {\r\n  font-weight: bold;\r\n}\r\n.k-syntax-str {\r\n  color: #38b714;\r\n}\r\n.k-syntax-error {\r\n  color: red;\r\n}\r\n.k-syntax-bool {\r\n  color: #a9169c;\r\n}\r\n.k-syntax-startexp {\r\n  font-weight: bold;\r\n}\r\n.k-syntax-paren-match {\r\n  background-color: #caf200;\r\n}\r\n.k-series-a {\r\n  border-color: #008fd3;\r\n  background-color: rgba(0, 143, 211, 0.15);\r\n}\r\n.k-series-b {\r\n  border-color: #99d101;\r\n  background-color: rgba(153, 209, 1, 0.15);\r\n}\r\n.k-series-c {\r\n  border-color: #f39b02;\r\n  background-color: rgba(243, 155, 2, 0.15);\r\n}\r\n.k-series-d {\r\n  border-color: #f05662;\r\n  background-color: rgba(240, 86, 98, 0.15);\r\n}\r\n.k-series-e {\r\n  border-color: #c03c53;\r\n  background-color: rgba(192, 60, 83, 0.15);\r\n}\r\n.k-series-f {\r\n  border-color: #acacac;\r\n  background-color: rgba(172, 172, 172, 0.15);\r\n}\r\n.k-spreadsheet-sheets-remove:hover .k-icon {\r\n  color: #cc2222;\r\n}\r\n.k-spreadsheet-formula-list .k-state-focused {\r\n  background-color: #007cc0;\r\n  color: #fff;\r\n}\r\n@media only screen and (-webkit-min-device-pixel-ratio: 2) {\r\n  .k-icon.k-font-icon {\r\n    background-image: none;\r\n  }\r\n}\r\n"]}