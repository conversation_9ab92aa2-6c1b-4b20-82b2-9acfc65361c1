
45958c63a3358f3cab9fc54cf4c3a0441ba5672d	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.148.1754018536329.js\",\"contentHash\":\"322fdb60242857d4ac0467e347a15937\"}","integrity":"sha512-Zsxb6mpBaZ0nnIZEtoS1MBkMgJ/MW1d3caRL+kK6DrDcenh5kQOiVUGB0gu0J8r6qDbgEyVdm93br4x9uc+tug==","time":1754018575960,"size":108799}