
f3e45e4efddad191903ffa9c97ce3fcdb68c369f	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.64.1754018536329.js\",\"contentHash\":\"487d48da27bbdc4458fab7fcb59276ba\"}","integrity":"sha512-z9GQSjXe82wxSWi9UICD42GR6ONrIje7pUOMcO8WgAYtp5vaYk7WGWZI1lOR14ngkRYzABq1SVPXypxkQ4fRHQ==","time":1754018575959,"size":108518}