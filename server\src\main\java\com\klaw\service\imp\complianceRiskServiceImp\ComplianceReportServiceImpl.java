package com.klaw.service.imp.complianceRiskServiceImp;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import com.klaw.dao.complianceRiskDao.ComplianceReportMapper;

import com.klaw.entity.complianceRiskBean.ComplianceReportEntity;


import com.klaw.service.complianceRiskService.ComplianceReportService;

import com.klaw.utils.DataAuthUtils;
import com.klaw.utils.PageUtils;

import com.klaw.utils.Utils;
import com.klaw.vo.Json;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;




@Service
public class ComplianceReportServiceImpl extends ServiceImpl<ComplianceReportMapper, ComplianceReportEntity> implements ComplianceReportService {

    @Autowired
    private ComplianceReportMapper complianceReportMapper;

//    @Override
//    public boolean saveOrUpdate(ComplianceReportEntity entity) {
//        // 如果id为空，表示是新记录，需要设置createTime
//        if (entity.getId() == null || entity.getId().isEmpty()) {
//            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//            String formattedDate = formatter.format(new Date()); // 当前时间格式化为字符串
//            entity.setCreateTime(formattedDate); // 设置createTime
//        } else {
//            // 更新记录时，设置updateTime
//            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//            String formattedDate = formatter.format(new Date()); // 当前时间格式化为字符串
//            entity.setUpdateTime(formattedDate); // 设置updateTime
//        }
//        return super.saveOrUpdate(entity);
//    }

    @Override
    public void saveData(ComplianceReportEntity complianceReportEntity) {
        //complianceReportEntity.setReportTime(String.valueOf(new Date()));
        // 插入数据到数据库
        complianceReportMapper.insert(complianceReportEntity);
    }


    @Override
    public Page<ComplianceReportEntity> queryPageData(JSONObject json) {
        QueryWrapper<ComplianceReportEntity> wrapper = new QueryWrapper<>();
        getFilter(json, wrapper);
        return page(new PageUtils<>(json), wrapper);
    }


    public void getFilter(JSONObject json, QueryWrapper<ComplianceReportEntity> wrapper) {
        //是否台账功能（登记只查自己的，台账和弹框查自己和数据权限的）
        boolean isQuery = json.containsKey("isQuery") ? json.getBoolean("isQuery") : false;
        String orgId = json.containsKey("orgId") ? json.getString("orgId") : "";
        // 报告主题
        String reportSubject = json.containsKey("reportSubject") ? json.getString("reportSubject") : null;

        // 报告类别
        String reportCategory = json.containsKey("reportCategory") ? json.getString("reportCategory") : null;

        // 上报人
        String reporter = json.containsKey("reporter") ? json.getString("reporter") : null;

        // 上报日期范围
        // 上报日期(最小值)
        Date reportingDateMin = json.containsKey("reportingDateMin") ? json.getDate("reportingDateMin") : null;
        // 上报日期(最大值)
        Date reportingDateMax = json.containsKey("reportingDateMax") ? json.getDate("reportingDateMax") : null;
        // 上报单位
        String reportingUnit = json.containsKey("reportingUnit") ? json.getString("reportingUnit") : null;

        // 审核状态
        String reviewStatus = json.containsKey("reviewStatus") ? json.getString("reviewStatus") : null;

        // 当前节点
        String currentNode = json.containsKey("currentNode") ? json.getString("currentNode") : null;

        // 节点操作人
        String nodeOperator = json.containsKey("nodeOperator") ? json.getString("nodeOperator") : null;

        // 报告内容
        String reportExplanation = json.containsKey("reportExplanation") ? json.getString("reportExplanation") : null;

        // 上传附件
        String reportFile = json.containsKey("reportFile") ? json.getString("reportFile") : null;

        // 数据创建单位ID
        String createOgnId = json.containsKey("createOgnId") ? json.getString("createOgnId") : null;

        // 数据创建单位
        String createOgnName = json.containsKey("createOgnName") ? json.getString("createOgnName") : null;

        // 数据创建部门ID
        String createDeptId = json.containsKey("createDeptId") ? json.getString("createDeptId") : null;

        // 数据创建部门
        String createDeptName = json.containsKey("createDeptName") ? json.getString("createDeptName") : null;

        // 数据创建班组id
        String createGroupId = json.containsKey("createGroupId") ? json.getString("createGroupId") : null;

        // 数据创建班组
        String createGroupName = json.containsKey("createGroupName") ? json.getString("createGroupName") : null;

        // 数据创建人id
        String createPsnId = json.containsKey("createPsnId") ? json.getString("createPsnId") : null;

        // 数据创建人
        String createPsnName = json.containsKey("createPsnName") ? json.getString("createPsnName") : null;

        // 数据创建组织id
        String createOrgId = json.containsKey("createOrgId") ? json.getString("createOrgId") : null;

        // 数据创建组织
        String createOrgName = json.containsKey("createOrgName") ? json.getString("createOrgName") : null;

        // 数据创建人ID全路径
        String createPsnFullId = json.containsKey("createPsnFullId") ? json.getString("createPsnFullId") : null;

        // 数据创建人全路径
        String createPsnFullName = json.containsKey("createPsnFullName") ? json.getString("createPsnFullName") : null;

        // 数据创建时间
        String createTime = json.containsKey("createTime") ? json.getString("createTime") : null;

        // 数据更新时间
        String updateTime = json.containsKey("updateTime") ? json.getString("updateTime") : null;

        // 数据状态
        String dataState = json.containsKey("dataState") ? json.getString("dataState") : null;

        // 数据状态代码
        Integer dataStateCode = json.containsKey("dataStateCode") ? json.getInteger("dataStateCode") : null;

        // 模糊搜索值
        String fuzzyValue = json.containsKey("fuzzyValue") ? json.getString("fuzzyValue") : null;

        // 排序字段
        String sortName = json.containsKey("sortName") ? json.getString("sortName") : null;
        // 排序字段是否升序
        boolean order = json.containsKey("order") ? json.getBoolean("order") : false;

        // 应用查询条件
        if (StringUtils.isNotBlank(reportSubject)) {
            wrapper.and(i -> i.like("report_subject", reportSubject));
        }

        if (StringUtils.isNotBlank(reportCategory)) {
            wrapper.and(i -> i.like("report_category", reportCategory));
        }

        if (StringUtils.isNotBlank(reporter)) {
            wrapper.and(i -> i.like("reporter", reporter));
        }

        if (reportingDateMin != null) {
            wrapper.and(i -> i.ge("report_time", reportingDateMin));
        }
        if (reportingDateMax != null) {
            wrapper.and(i -> i.le("report_time", reportingDateMax));
        }

        if (StringUtils.isNotBlank(reportingUnit)) {
            wrapper.and(i -> i.like("reporting_unit", reportingUnit));
        }

        if (StringUtils.isNotBlank(reviewStatus)) {
            wrapper.and(i -> i.eq("review_status", reviewStatus));
        }

        if (StringUtils.isNotBlank(currentNode)) {
            wrapper.and(i -> i.like("current_node", currentNode));
        }

        if (StringUtils.isNotBlank(nodeOperator)) {
            wrapper.and(i -> i.like("node_operator", nodeOperator));
        }

        if (StringUtils.isNotBlank(reportExplanation)) {
            wrapper.and(i -> i.like("report_explanation", reportExplanation));
        }

        if (StringUtils.isNotBlank(reportFile)) {
            wrapper.and(i -> i.like("report_file", reportFile));
        }

        if (StringUtils.isNotBlank(createOgnId)) {
            wrapper.and(i -> i.like("create_ogn_id", createOgnId));
        }

        if (StringUtils.isNotBlank(createOgnName)) {
            wrapper.and(i -> i.like("create_ogn_name", createOgnName));
        }

        if (StringUtils.isNotBlank(createDeptId)) {
            wrapper.and(i -> i.like("create_dept_id", createDeptId));
        }

        if (StringUtils.isNotBlank(createDeptName)) {
            wrapper.and(i -> i.like("create_dept_name", createDeptName));
        }

        if (StringUtils.isNotBlank(createGroupId)) {
            wrapper.and(i -> i.like("create_group_id", createGroupId));
        }

        if (StringUtils.isNotBlank(createGroupName)) {
            wrapper.and(i -> i.like("create_group_name", createGroupName));
        }

        if (StringUtils.isNotBlank(createPsnId)) {
            wrapper.and(i -> i.like("create_psn_id", createPsnId));
        }

        if (StringUtils.isNotBlank(createPsnName)) {
            wrapper.and(i -> i.like("create_psn_name", createPsnName));
        }

        if (StringUtils.isNotBlank(createOrgId)) {
            wrapper.and(i -> i.like("create_org_id", createOrgId));
        }

        if (StringUtils.isNotBlank(createOrgName)) {
            wrapper.and(i -> i.like("create_org_name", createOrgName));
        }

        if (StringUtils.isNotBlank(createPsnFullId)) {
            wrapper.and(i -> i.like("create_psn_full_id", createPsnFullId));
        }

        if (StringUtils.isNotBlank(createPsnFullName)) {
            wrapper.and(i -> i.like("create_psn_full_name", createPsnFullName));
        }

        if (createTime != null) {
            wrapper.and(i -> i.ge("create_time", createTime));
        }

        if (updateTime != null) {
            wrapper.and(i -> i.eq("update_time", updateTime));
        }

        if (StringUtils.isNotBlank(dataState)) {
            wrapper.and(i -> i.like("data_state", dataState));
        }

        if (dataStateCode != null) {
            wrapper.and(i -> i.eq("data_state_code", dataStateCode));
        }

        if (StringUtils.isNotBlank(fuzzyValue)) {
            wrapper.and(i -> i.like("report_subject", fuzzyValue)
                    .or().like("reporting_unit", fuzzyValue)
                    .or().like("report_category", fuzzyValue)
                    .or().like("report_year", fuzzyValue));
        }
        if (StringUtils.isNotBlank(sortName)) {
            wrapper.orderBy(true, order, Utils.humpToLine2(sortName));
        } else {
            wrapper.orderBy(true, order, "create_time");
        }
        Long functionId = DataAuthUtils.getFunctionIdByCode("case_level_ledger");
        if(isQuery){
            DataAuthUtils.dataPermSql(wrapper, null, functionId, orgId);
        }else{
            wrapper.eq("create_org_id", orgId);
        }
//        if (StringUtils.isNotBlank(sortName) && order) {
//            wrapper.orderByAsc(sortName);
//        } else if (StringUtils.isNotBlank(sortName)) {
//            wrapper.orderByDesc(sortName);
//        }
//        wrapper.orderByDesc("create_time");
    }


    @Override
    public ComplianceReportEntity queryDataById(String id) {
        ComplianceReportEntity complianceReportEntity = getById(id);
        return complianceReportEntity;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Json deleteDataById(String id) {
        boolean flag = removeById(id);
        if (!flag) {
            return Json.fail().msg("未找到需要删除的数据");
        }
        return Json.succ().msg("删除成功!");
    }
}


