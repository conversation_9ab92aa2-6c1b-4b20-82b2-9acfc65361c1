
901297d9a884e3979e8c5b33f904a3699986490e	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.180.1754018536329.js\",\"contentHash\":\"396b2c4a8f0bf44dd1f7a135e2323eb9\"}","integrity":"sha512-lCpGeJa4g7CKGk8kLOSmiGVviXSvxXpu+VtWhepnMZkRispwXIGNYlWG1uDrRpfnEkoe9K258QCL1jZaA8WAKg==","time":1754018576216,"size":388434}