import {request} from '@/api/index';

export default {
    // 提交订单
    submitOrder(data) {
        return request({
            url: '/api/order/submit',
            method: 'post',
            data
        })
    },

    // 根据订单ID查询订单
    getOrderById(orderId) {
        return request({
            url: '/api/order/getById/' + orderId,
            method: 'get'
        })
    },

    // 根据商品ID查询订单列表
    getOrdersByProductId(productId) {
        return request({
            url: '/api/order/getByProductId/' + productId,
            method: 'get'
        })
    },

    // 根据订单状态查询订单列表
    getOrdersByStatus(orderStatus, params) {
        return request({
            url: '/api/order/getByStatus/' + orderStatus,
            method: 'get',
            params
        })
    },

    // 更新订单状态
    updateOrderStatus(data) {
        return request({
            url: '/api/order/updateStatus',
            method: 'put',
            params: data
        })
    },

    // 确认订单
    confirmOrder(orderId) {
        return request({
            url: '/api/order/confirm/' + orderId,
            method: 'put'
        })
    },

    // 删除订单
    deleteOrder(orderId) {
        return request({
            url: '/api/order/delete/' + orderId,
            method: 'delete'
        })
    },

    // 分页查询订单
    queryOrders(data) {
        return request({
            url: '/api/order/query',
            method: 'post',
            data
        })
    },

    // 批量确认订单
    batchConfirmOrders(data) {
        return request({
            url: '/api/order/batchConfirm',
            method: 'post',
            data
        })
    },

    // 导出订单数据
    exportOrders(data) {
        return request({
            url: '/api/order/export',
            method: 'post',
            data,
            responseType: 'blob'
        })
    }
} 