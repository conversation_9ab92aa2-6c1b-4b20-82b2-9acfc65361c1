@import "../../style/mixins";

$sizes-list: 16px 13px 10px;
$sizes-value-list: 'large' 'normal' 'small';

@for $i from 1 to length($sizes-list)+1 {
  .font-size-#{nth($sizes-value-list,$i)} {
    *,.el-submenu__title,
    .el-menu-item,
    .el-tabs__item,
    .el-button--medium,
    .el-form-item__label,
    .el-form-item__content,
    .el-input--medium,
    .el-table--small,
    .el-input--small,
    .el-select-dropdown__item,
    .el-submenu__icon-arrow,
    .el-table,
    .el-checkbox__label,
    .el-button--mini, .el-button--small,
    .el-dialog__title,.navbar .tit{
      font-size: nth($sizes-list,$i);
    }

    .fa{
      font-size: nth($sizes-list,$i) - 1;
    }
    .popoverList {
      li {
        font-size: nth($sizes-list,$i);
      .fa {
          font-size: nth($sizes-list,$i) - 1;
        }
      }
    }

    .sg-fix-main{
      position: absolute;
      overflow-y:scroll;
      width: 100%;
      height: 100%;
    }
  }
}

.font-size-small {
  .navbar .left .icon_bar{
    margin-left: 10px;
    line-height: 45px;
  }
}
.font-size-normal{
  .navbar .left .icon_bar{
    margin-left: 12px;
    line-height: 42px;
  }
}
.font-size-large{
  .navbar .left .icon_bar{
    margin-left: 15px;
    line-height: 44px;
  }
}