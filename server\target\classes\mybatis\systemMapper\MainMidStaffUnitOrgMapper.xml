<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.klaw.dao.mainDataDao.MainMidStaffUnitOrgMapper">
    <resultMap id="BaseResultMap" type="com.klaw.entity.mainDataBean.MainMidStaffUnitOrg">
        <id property="staffCode" column="STAFF_CODE" jdbcType="VARCHAR"/>
        <result property="staffName" column="STAFF_NAME" jdbcType="VARCHAR"/>
        <result property="state" column="STATE" jdbcType="VARCHAR"/>
        <result property="stateDesc" column="STATE_DESC" jdbcType="VARCHAR"/>
        <result property="codeType" column="CODE_TYPE" jdbcType="VARCHAR"/>
        <result property="codeDesc" column="CODE_DESC" jdbcType="VARCHAR"/>
        <result property="orgCode" column="ORG_CODE" jdbcType="VARCHAR"/>
        <result property="orgName" column="ORG_NAME" jdbcType="VARCHAR"/>
        <result property="deptCode" column="DEPT_CODE" jdbcType="VARCHAR"/>
        <result property="deptName" column="DEPT_NAME" jdbcType="VARCHAR"/>
        <result property="jobCode" column="JOB_CODE" jdbcType="VARCHAR"/>
        <result property="jobName" column="JOB_NAME" jdbcType="VARCHAR"/>
        <result property="postCode" column="POST_CODE" jdbcType="VARCHAR"/>
        <result property="postName" column="POST_NAME" jdbcType="VARCHAR"/>
        <result property="jobStartDate" column="JOB_START_DATE" jdbcType="VARCHAR"/>
        <result property="jobEndDate" column="JOB_END_DATE" jdbcType="VARCHAR"/>
        <result property="code" column="CODE" jdbcType="VARCHAR"/>


    </resultMap>


    <!--<select id="queryPageData" resultType="java.util.Map">
        select
            s.code as "code",
            s.name as "name",
            u.user_account as "account",
            s.organization as "organization"
        from sg_mid_staff s join SG_USER u on s.code = u.code
        <where>
            ${ew.sqlSegment}
        </where>
    </select>-->
    <sql id="Base_Column_List1">
        o.code,
        o.staff_code,
        u.name as staff_name,
        o.state,
        o.state_desc,
        o.code_type,
        o.code_desc,
        o.org_code,
        o.org_name,
        o.dept_code,
        o.dept_name,
        o.job_code,
        o.job_name,
        o.post_code,
        o.post_name,
        o.job_start_date,
        o.job_end_date
    </sql>

    <select id="queryOrgUser" resultMap="BaseResultMap">
        select <include refid="Base_Column_List1"/>
        from Main_Mid_Staff u join  main_mid_staff_unit_org o on u.code = o.staff_code
        <where>
            ${ew.sqlSegment}
        </where>
    </select>
</mapper>