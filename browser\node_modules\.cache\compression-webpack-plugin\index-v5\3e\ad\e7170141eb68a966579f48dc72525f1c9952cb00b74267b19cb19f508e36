
88bde11b36221f8c0a341f4edd442e6b50f5f10f	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.232.1754018536329.js\",\"contentHash\":\"8bb713f780a5cdf60408007c0691e732\"}","integrity":"sha512-u85ZNDcd5PNpg3xUVEtGRdE/p73H2UMoIl8S6vt4Mjh0JsZKU/5LG7lTEgWCaJfBrxwJgVBNzhGY8YJg7Hk+WQ==","time":1754018575994,"size":131448}