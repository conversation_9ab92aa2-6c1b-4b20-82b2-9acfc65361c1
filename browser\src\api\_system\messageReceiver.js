import {request} from '@/api/index'

export default {
    /**
     * 消息未读变已读
     * @param data
     *          @param id       messageID
     * @returns {*}
     */
    updateByPrimaryKeySelective(data) {
        return request({
            url: '/sys_message_receiver/updateByPrimaryKeySelective',
            method: 'post',
            data
        })
    },

    //
    // /**
    //  * 消息未读变已读
    //  * @param data
    //  *          @param id       messageID
    //  * @returns {*}
    //  */
    // messageAttachment(data) {
    //     return request({
    //         url: '/sys_message_receiver/updateByPrimaryKeySelective',
    //         method: 'post',
    //         data
    //     })
    // },
}