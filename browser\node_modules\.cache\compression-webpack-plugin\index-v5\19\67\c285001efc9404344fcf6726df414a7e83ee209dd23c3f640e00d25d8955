
56fefec1207e8d7a7f473758d310bf3222dd5c6e	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.191.1754018536329.js\",\"contentHash\":\"e9ae670abb70094be169eb7baf3c615e\"}","integrity":"sha512-2+4o7BD6k+rz6txyI7JvXYFGwT6G2/6F3mh0f0HPbnYPOJS9GtdfQQ/OVEYNby94yXWTGe/kBSREwPUrbnchRA==","time":1754018576053,"size":187232}