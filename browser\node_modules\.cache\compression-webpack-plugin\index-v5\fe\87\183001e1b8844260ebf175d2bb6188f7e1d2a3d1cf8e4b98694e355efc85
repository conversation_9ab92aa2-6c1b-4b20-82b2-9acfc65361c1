
97f7f965d973630d90c7a152a51edc3c15a1f954	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.74.1754018536329.js\",\"contentHash\":\"ed6ddc7c29f03fef60e8aed684e3e991\"}","integrity":"sha512-B1F1w0IGwX4y6W1pCyUn6kEIAdxeIa/kS4EOL9pfG/NSng8RlZBD/JDYZe6IwrgD1fE6N4HkgeuH4eHenQ5Iow==","time":1754018575979,"size":157507}