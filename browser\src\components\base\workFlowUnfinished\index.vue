<template>
    <layoutView :data="configData" :routeJump="routeJump" :ajax="ajax"></layoutView>
</template>

<script>
import config from './config'
import axios from '../../../js_public/axios'
import { mapMutations } from 'vuex'
export default {
    name: 'BusinessHandler',
    inject: ['layout'],
    data(){
        return {
            configData: config
        }
    },
    methods: {
        routeJump(row){
            this.layout.select('process_approval_demo,流程审批测试,process_approval_demo',{
                taskId:row.id,
                processInstanceId: row.processInstanceId,
                businessKey: row.businessKey
            })
        }
    },
    computed: {
        ajax: function(){
            return axios
        }
    }
}
</script>