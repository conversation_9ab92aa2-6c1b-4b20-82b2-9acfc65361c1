<template>
	<div>
		<FormWindow ref="formWindow" @initData="initData" @loadData="loadData">
			<el-container v-loading="loading" style="height: calc(100vh - 84px)" :element-loading-text="loadingText">
				<el-main>
					<el-scrollbar style="height: 100%" wrap-style="overflow-x: hidden;">
						<div style="background: green; color: white; position: absolute; right: 0">培训进行中</div>
						<!-- <div style="display: flex">
							<div style="font-size: 16px; font-weight: bolder">{{mainData.trainingTopic}}</div>
							<div class="up"></div>
							<div>{{mainData.likeCount}}</div>
							<div class="down"></div>
							<div>{{mainData.dislikeCount}}</div>
						</div> -->
						<div style="margin-top: 10px">培训主题：{{ mainData.trainingTopic }}</div>
						<div style="margin-top: 5px">
							培训分类：{{
							mainData.trainingCategory == 'dwzxzzthgxx'
							? '党委中心组专题合规学习'
							: mainData.trainingCategory == 'glcpxxx'
							? '管理层（董监高）培训学习'
							: mainData.trainingCategory == 'hgglypxxx'
							? '合规管理员培训学习'
							: mainData.trainingCategory == 'xygrzpxxx'
							? '新员工入职培训学习'
							: mainData.trainingCategory == 'qypsxpxxg'
							? '全员普适性培训宣贯'
							: mainData.trainingCategory == 'qt'
							? '其他'
							: ''
							}}
						</div>
						<div style="margin-top: 5px">培训时间：{{ formatDate(mainData.trainingStartTime) + ' 至 ' +
							formatDate(mainData.trainingEndTime) }}</div>
						<div style="margin-top: 5px">培训讲师：{{ mainData.trainingDirector }}</div>
						<div style="margin-top: 5px">发布人：{{ mainData.createPsnFullName }}</div>
						<el-button type="primary" v-if="this.mainData.learningProgress == '未开始'"
							@click="showBtnAndTabs">开始学习</el-button>
						<el-button type="warning"
							v-if="this.mainData.learningProgress == '学习中' || showBtn == true">学习中</el-button>
						<el-button type="success" v-if="this.mainData.learningProgress == '已完成'">已完成</el-button>
						<el-tabs type="border-card">
							<el-tab-pane label="课程信息">
								<el-table ref="table" v-loading="tableLoading" :data="mainData.courseDetailsList"
									size="mini" border  stripe fit :show-overflow-tooltip="true"
									row-key="id" style="table-layout: fixed; width: 100%">
									<el-table-column type="index" width="50" label="序号" align="center" />
									<el-table-column prop="trainingCourse" show-overflow-tooltip label="培训课程"
										width="400">
									</el-table-column>
									<el-table-column prop="trainingMaterials" show-overflow-tooltip label="培训课件"
										width="600" sortable="custom">
										<template slot-scope="scope">
											<UploadDoc :files.sync="scope.row.trainingMaterials" doc-path="/case"
													   v-if="scope.row.isVideo==null || scope.row.isVideo=='N'"
												:disabled="true" />
											<span v-if="scope.row.isVideo=='Y'" style="color:blue;cursor:pointer"
												@click="loadVideo(scope.row)">{{parseVideoName(scope.row.trainingMaterials)}}</span>
										</template>
									</el-table-column>
									<el-table-column show-overflow-tooltip prop="courseDescription" label="课程说明"
										width="600" />
									<el-table-column prop="finished" label="操作" width="100">
										<template slot-scope="scope">
											<el-button type="primary" size="mini" v-if="!scope.row.finished && (scope.row.isVideo==null || scope.row.isVideo=='N')"
												@click="sureStudy(scope.$index, scope.row)">完成学习</el-button>
											<div v-else-if="!scope.row.finished && (scope.row.isVideo=='Y')">未完成</div>
											<div style="color:#409EFF" v-else>已学习</div>
										</template>
									</el-table-column>
								</el-table>
							</el-tab-pane>
							<el-tab-pane label="课程评论">
								<div style="font-size: 16px; font-weight: bolder">发表评论</div>
								<el-form ref="dataForm" :model="mainData"
									:style="mainData.dataStateCode === utils.dataState_BPM.FINISH.code ? 'margin-right: 50px;' : ' margin-right: 10px;'">
									<el-row>
										<el-col :span="24">
											<el-form-item label="" prop="reviewDesc">
												<el-input v-model="mainData.reviewDesc"
													:autosize="{ minRows: 3, maxRows: 20 }" type="textarea"
													placeholder="请输入评论" maxlength="500" show-word-limit />
											</el-form-item>
										</el-col>
									</el-row>
								</el-form>
								<el-button type="primary" @click="saveReview">发表评论</el-button>
								<div style="font-size: 16px; font-weight: bolder">评论记录</div>
								<ul style="margin-top: 10px">
									<li v-for="(item, index) in mainData.reviewsList">
										<div style="display: flex">
											<div class="userAvatar"></div>
											<div style="margin-left: 5px; width: 50px">{{ item.createPsnName }}</div>
											<div style="margin-left: 200px">{{ '评价时间：' + item.createTime }}</div>
										</div>
										<div style="display: flex; flex-wrap: wrap; margin-top: 20px">
											{{ item.reviewDesc }}
										</div>
										<div
											style="width: calc(100% - 10px); height: 1px; background: #000; margin: 10px 0">
										</div>
									</li>
								</ul>
							</el-tab-pane>
							<el-tab-pane label="学习报告">
								<UploadDoc :files.sync="mainData.reportFile" doc-path="/case" />
								<el-button type="primary" @click="sureUpload">确认</el-button>
							</el-tab-pane>
						</el-tabs>
						<!--案件审批表-->
						<prosecution-dialog :visible.sync="prosecutionDialog" :is-multiple="false"
							@onSure="prosecutionSelect" />
					</el-scrollbar>
					<div>

				</div>
				</el-main>
				<!-- 选择模版 -->
				<!-- <Shortcut :templateShow="templateShow" @templateClick="templateClick" /> -->
				<!--视频播放组件-->
				<Video-Player-Popup :videoSrc="videoUrl" :visible="videoShow" :videoWatchDuration="videoWatchDuration" @sendToParent="closeVideoDialog"></Video-Player-Popup>

			</el-container>
		</FormWindow>
	</div>
</template>

<script>
// vuex缓存数据
import { mapGetters } from 'vuex';
// 接口api
import complianceReviewApi from '@/api/ComplianceReview/complianceReview.js';
import taskApi from '@/api/_system/task';
import commonApi from '@/api/_system/common';
// 组件
import FormWindow from '@/view/components/FormWindow/FormWindow';
import OtherInfo from '@/view/litigation/caseManage/case/caseChild/OtherInfo';
import OrgSingleDialogSelect from '@/view/components/OrgSingleDialogSelect/index';
import ProsecutionDialog from './ProsecutionDialog';
import QsBaseInfo from './qisubaseInfo';
import CaseData from '@/view/litigation/caseManage/case/caseChild/CaseData';
import CaseEvidenceData from '@/view/litigation/caseManage/case/caseChild/CaseEvidenceData';
import Shortcut from '@/view/litigation/caseManage/caseExamine/Shortcut';
import SimpleBoardTitleApproval from '@/view/components/SimpleBoard/SimpleBoardTitleApproval';
import UploadDoc from '@/view/components/UploadDoc/UploadDoc.vue';
import VideoPlayerPopup from '@/view/video/VideoPlayerPopup.vue';
import SectionDataDialog from './dialog/SelectionDialog.vue';
import StudyDialog from './dialog/StudyDialog.vue';
import ComplianceTrainingApi from '@/api/ComplianceTraining/ComplianceTraining.js';
// 接口api
import docApi from '@/api/_system/doc'
export default {
	name: 'HgpxxxyhDetail',
	inject: ['layout', 'mcpLayout'],
	components: {
		SimpleBoardTitleApproval,
		CaseData,
		QsBaseInfo,
		ProsecutionDialog,
		OrgSingleDialogSelect,
		FormWindow,
		OtherInfo,
		CaseEvidenceData,
		Shortcut,
		UploadDoc,
		SectionDataDialog,
		StudyDialog,
		VideoPlayerPopup
	},
	computed: {
		...mapGetters(['orgContext']),
		isView: function () {
			return this.dataState === this.utils.formState.VIEW;
		},
		templateShow() {
			return this.mainData.dataStateCode === this.utils.dataState_BPM.SAVE.code && this.dataState !== this.utils.formState.VIEW;
		},
	},
	mounted(){
		window.vm = this;
	},
	data() {
		return {
			selectRow:{},//选中行
			videoWatchDuration:0,
			videoUrl:'',
			videoShow:false,
			showBtn: false,
			showDown: true,
			showUp: true,
			upNum: false,
			downNum: false,
			dialogVisible: false, //学习进度列表弹框
			tableData: [
				{
					trainingCourse: 'XXXXX',
					courseDescription: '测试说明123',
					trainingMaterials: 'XXXXX.docs',
				},
			], //课程信息列表
			tableLoading: false, //课程信息列表loading
			sectionVisible: false,
			businessAreaData: [],
			radio: true,
			radio1: true,
			title: null,
			type: null,
			tabId: null,
			oarecordsDialog: false,
			loading: false,
			dataState: null,
			functionId: null, //终止的时候要用，需要手动关闭
			dataId: null,
			taskId: null,

			view: 'old',
			mainData: {
				startTime: null, // 新增开始时间字段
				endTime: null,   // 新增结束时间字段
				trainingCourseDetailsList: [], //培训内容
				uploadedCount: null,
				dislikeCount: null,
				likeCount: null,
				reviewDesc: null,
				reportFile: null,
				notUploadedCount: null,
				complianceTrainingReports: [],
				trainingDirector: null,
				responsibleDepartment: null,
				trainingTopic: null,
				trainingCategory: null,
				trainingStartTime: null,
				trainingEndTime: null,
				trainingLocation: null,
				trainingIntroduction: null,
				notStartedCount: null,
				learningInProgressCount: null,
				learningProgress: null,
				completedCount: null,
				mandatoryObjectId: null,
				electiveObjectId: null,
				mandatoryObjectName: null,
				electiveObjectName: null,
				reviewsList: [],
				reviewDesc: null,
				complianceTrainingLearningTableId: null,
				courseDetailsList: [],
				id: null, //主键
				id: null, //主键
				reviewCategory: null, //审查类别
				createOgnId: null, //当前机构ID
				createOgnName: null, //当前机构名称
				createDeptId: null, //当前部门ID
				createDeptName: null, //当前部门名称
				createGroupId: null, //当前部门ID
				createGroupName: null, //当前部门名称
				createPsnId: null, //当前人ID
				createPsnName: null, //当前人名称
				createOrgId: null, //当前组织ID
				createOrgName: null, //当前组织名称
				createPsnFullId: null, //当前人全路径ID
				createPsnFullName: null, //当前人全路径名称
				createPsnPhone: null, //经办人电话
				createTime: null, //创建时间
				// auditStatus: this.utils.dataState_BPM.SAVE.name, //状态
				dataState: this.utils.dataState_BPM.SAVE.name, //数据状态
				dataStateCode: this.utils.dataState_BPM.SAVE.code, //数据状态编码
			},
			orgTreeDialog: false,
			orgDialogTitle: '组织信息',
			isAssign: false,
			prosecutionDialog: false,
			rules: {
				reviewSubject: [{ required: true, message: '请输入审查主题', trigger: 'blur' }],
				isComplianceReviewed: [{ required: true, message: '请选择是否有合规审查', trigger: 'blur' }],
				hasProposalChanged: [{ required: true, message: '请选择议案是否有变化', trigger: 'blur' }],
				businessArea: [{ required: true, message: '请选择业务领域', trigger: 'blur' }],
			},
			activity: null, //记录当前待办处于流程实例的哪个环节
			obj: {
				// 流程处理逻辑需要的各种参数
				taskId: null,
				processInstanceId: null,
				businessKey: null,
				title: null,
				functionName: null,
				sid: null,
			},
			noticeParams: {},
			noticeData: {
				moduleName: '', // 模块名称
				dataId: '', // 数据ID
				url: '', // 地址
				title: '', // 地址
				params: {}, // 其他参数
			},
			loadingText: '加载中...',
      		videoPoster: null, // 封面图片路径
			maxWatchedTime:0
		};
	},
	provide() {
		return {
			parentCase: this,
		};
	},
	methods: {
		initData(temp, dataState) {
			this.dataState = dataState;
			Object.assign(this.mainData, temp);
			this.mainData.reviewCategory = this.$route.query.reviewCategory;
			this.title = this.utils.getDicName(this.utils.compliance_review_type, this.mainData.reviewCategory);
			let obj = this.utils.getManagementUnit(this.mainData.createPsnFullName, this.mainData.createPsnFullId);
			this.mainData.createPsnFullName = obj.name;
			this.mainData.managementUnitId = obj.id;
			this.mainData.unitType = obj.unitType;
			this.mainData.unitTypeId = obj.unitTypeId;
			commonApi
				.createKvsequence({
					key: 'AJ' + new Date().getFullYear(),
					length: 5,
				})
				.then((res) => {
					this.mainData.reviewNumber = res.data.kvsequence;
				});
			this.view = 'old';
			const code = ['businessDomainDic'];
			this.utils.getDic(code).then((response) => {
				this.businessAreaData = response.data.data[code[0]];
			});
			let year = new Date().getFullYear();
			this.utils.createKvsequence('HGBG' + year, 6).then((value) => {
				this.mainData.caseCode = value.data.kvsequence;
			});
			// this.mainData.currentUnit = this.mainData.createOgnName;
			// this.mainData.currentUnitId = this.mainData.createOgnId;

			this.mainData.riskDepartment = this.mainData.createOgnName; //有点问题
			this.mainData.riskDepartmentId = this.mainData.createOgnId;

			const interCode = 'AJ_GC_CLMC_QS';
			const codes = [interCode];
			this.utils.getDic(codes).then((response) => {
				const datas = response.data.data[codes[0]].filter((item) => item.whetherSolidified === true);
				if (datas && datas.length > 0) {
					datas.forEach((item, index) => {
						const data = this.childData(index);
						data.name = item.dicName;
						data.whetherSys = true;
						this.mainData.otherDataList.push(data);
					});
				}
			});
			this.loading = false;
		},
		loadData(dataState, dataId) {
			this.functionId = this.$route.query.functionId;
			if (this.$route.query.view !== undefined && this.$route.query.view !== '') this.view = this.$route.query.view;
			if (this.$route.query.type !== undefined && this.$route.query.type !== '') this.type = this.$route.query.type;
			this.dataState = dataState;
			this.queryCourList();

			this.mainData.createOgnId = this.orgContext.currentOgnId;
			this.mainData.createOgnName = this.orgContext.currentOgnName;
			this.mainData.createDeptId = this.orgContext.currentDeptId;
			this.mainData.createDeptName = this.orgContext.currentDeptName;
			this.mainData.createPsnId = this.orgContext.currentPsnId;
			this.mainData.createPsnName = this.orgContext.currentPsnName;
			this.mainData.createPsnFullId = this.orgContext.currentPsnFullId;
			this.mainData.createPsnFullName = this.orgContext.currentPsnFullName;
			this.mainData.createPsnPhone = this.orgContext.currentPsnPhone;
			this.mainData.createGroupId = this.orgContext.currentGroupId;
			this.mainData.createGroupName = this.orgContext.currentGroupName;
			this.mainData.createOrgId = this.orgContext.currentOrgId;
			this.mainData.createOrgName = this.orgContext.currentOrgName;
			this.mainData.createLegalUnitId = this.orgContext.currentLegalUnitId;
			this.mainData.createLegalUnitName = this.orgContext.currentLegalUnitName;
			// this.mainData.courseDetailsList = JSON.parse(this.mainData.courseDetailsList);
			// ComplianceTrainingApi.queryReview({ id: this.$route.query.id }).then((res) => {
			// 	this.mainData.reviewsList = res.data.data.records;
			// });
		},
		// },
		sectionSure(val) {
			this.mainData.relatedSignificantReview = val.reviewNumber;
			this.sectionVisible = false;
		},
		queryCourList() {
			ComplianceTrainingApi.queryDetail({ id: this.$route.query.id }).then((res) => {
				this.mainData.trainingTopic = res.data.data.trainingTopic;
				this.mainData.trainingCategory = res.data.data.trainingCategory;
				this.mainData.trainingStartTime = res.data.data.trainingStartTime;
				this.mainData.trainingEndTime = res.data.data.trainingEndTime;
				this.mainData.trainingDirector = res.data.data.trainingDirector;
				this.mainData.notStartedCount = res.data.data.notStartedCount;
				this.mainData.learningInProgressCount = res.data.data.learningInProgressCount;
				this.mainData.completedCount = res.data.data.completedCount;
				this.mainData.dislikeCount = !res.data.data.dislikeCount ? '0' : res.data.data.dislikeCount;
				this.mainData.likeCount = !res.data.data.likeCount ? '0' : res.data.data.likeCount;
				this.mainData.courseDetailsList = res.data.data.trainingCourseDetailsList;
				this.mainData.reviewsList = res.data.data.reviewsList;
				this.mainData.learningProgress = res.data.data.learningProgress;
				this.mainData.id = this.$route.query.id;
				this.mainData.complianceTrainingLearningTableId = this.$route.query.complianceTrainingLearningTableId;
				if(this.mainData.learningProgress == '未开始'){
					this.showBtnAndTabs();
				}
			})
		},
		save() {
			return new Promise((resolve, reject) => {
				complianceReviewApi
					.save(this.mainData)
					.then((response) => {
						resolve(response);
					})
					.catch((error) => {
						reject(error);
					});
			});
		},
		submit() {
			return new Promise((resolve, reject) => {
				//判断 reporter 和 reportingUnit 是否有空值
				if (!this.mainData.reporter || !this.mainData.reportingUnit) {
					this.mainData.reporter = this.mainData.createPsnName; // 上报人
					this.mainData.reportingUnit = this.mainData.createDeptName; // 上报单位
				} else {
					this.mainData.reporter = this.mainData.reporter; // 上报人
					this.mainData.reportingUnit = this.mainData.reportingUnit; // 上报单位
				}
				this.mainData.reviewStatus = '已提交';

				// 获取当前时间并格式化
				// const now = new Date();
				// const year = now.getFullYear();
				// const month = String(now.getMonth() + 1).padStart(2, '0');
				// const day = String(now.getDate()).padStart(2, '0');
				// const hours = String(now.getHours()).padStart(2, '0');
				// const minutes = String(now.getMinutes()).padStart(2, '0');
				// const seconds = String(now.getSeconds()).padStart(2, '0');

				// this.mainData.reportTime = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
				this.mainData.reportTime = new Date();
				complianceReportApi
					.save(this.mainData)
					.then((response) => {
						resolve(response);
					})
					.catch((error) => {
						reject(error);
					});
			});
		},

		//选择模板
		templateClick(val) {
			if (val) {
				this.prosecutionDialog = true;
			}
		},
		prosecutionSelect(data) {
			this.mainData.reportSubject = data.reportSubject;
			this.mainData.reportYear = data.reportYear;
			this.mainData.reportCategory = data.reportCategory;
			this.mainData.internalExternalRisk = data.internalExternalRisk;
			this.mainData.positiveNegativeImpact = data.positiveNegativeImpact;
			this.mainData.involvedAmount = data.involvedAmount;
			this.mainData.riskDescription = data.riskDescription;
			this.mainData.riskReason = data.riskReason;
			this.mainData.potentialConsequences = data.potentialConsequences;
			this.mainData.reportFile = data.reportFile;
			this.mainData.unitTypeId = data.unitTypeId; //单位类型id
			this.mainData.riskDepartment = data.riskDepartment; //风险部门
			this.mainData.riskDepartmentId = data.riskDepartmentId; //风险部门Id
			this.mainData.currentUnit = data.currentUnit; //当事单位
			this.mainData.currentUnitId = data.currentUnitId; //当事单位id
			this.mainData.reporter = data.createPsnName; //经办人
			this.mainData.reportingUnit = data.createDeptName; //经办单位
			this.mainData.createPsnPhone = data.createPsnPhone; //经办人电话
			this.mainData.caseInterest = data.caseInterest; //利息
			this.mainData.createOgnName = data.createOgnName; //经办单位
			this.mainData.createPsnName = data.createPsnName; //经办人
			this.mainData.createDeptName = data.createDeptName; //经办部门
			// this.mainData.auditStatus = data.dataState;//状态
			this.mainData.partiesList = data.partiesList;
			this.mainData.partiesList.forEach((item) => {
				item.id = this.utils.createUUID();
				item.masterId = this.mainData.id;
			});

			this.mainData.claimList = data.claimList;
			this.mainData.claimList.forEach((item) => {
				item.id = this.utils.createUUID();
				item.parentId = this.mainData.id;
			});

			this.mainData.otherDataList = data.otherDataList;
			this.mainData.otherDataList.forEach((item) => {
				item.id = this.utils.createUUID();
				item.parentId = this.mainData.id;
				item.files = null;
			});

			this.mainData.relations = data.relations;
			this.mainData.relations.forEach((item) => {
				item.id = this.utils.createUUID();
				item.relationId = this.mainData.id;
			});
		},
		approval_() {
			this.$refs['dataForm'].validate((valid) => {
				if (valid) {
					this.save()
						.then(() => {
							const tabId = this.mainData.id;
							if (this.mainData.dataStateCode == this.utils.dataState_BPM.SAVE.code) {
								taskApi.selectFunctionId({ functionCode: 'case_risk_main' }).then((res) => {
									const functionId = res.data.data[0].ID;
									this.layout.openNewTab('案件风险告知审批信息', 'design_page', functionId, tabId, {
										...this.utils.routeState.NEW(tabId),
										functionId: functionId,
										businessKey: tabId,
										entranceType: 'FLOWABLE',
										create: 'create',
										view: 'new',
									});
								});
							} else {
								const tabId = this.mainData.id;
								taskApi.selectTaskId({ businessKey: tabId, isView: '' }).then((res) => {
									const functionId = res.data.data[0].ID;
									const uuid = this.utils.createUUID();
									this.layout.openNewTab('案件风险告知审批信息', 'design_page', 'design_page', uuid, {
										processInstanceId: res.data.data[0].PID, //流程实例
										taskId: res.data.data[0].ID, //任务ID
										businessKey: tabId, //业务数据ID
										functionId: functionId, //不传也可以，只有首环节需要传，实例启动就可以不传了
										entranceType: 'FLOWABLE',
										type: 'toDeal',
										view: 'new',
										create: 'create',
									});
								});
							}
						})
						.then(() => {
							this.mcpLayout.closeTab();
						});
				}
			});
		},
		save_() {
			this.save().then(() => {
				this.$message.success('保存成功!');
			});
		},
		submit_() {
			this.$refs['dataForm'].validate((valid) => {
				if (valid) {
					this.submit().then(() => {
						this.mainData.reviewStatus = '已提交';
						this.$message.success('提交成功!');
					});
				} else {
					this.$message.error('表单填写不完整，请检查后重新提交!');
				}
			});
		},
		saveCurrentTime() {
			const now = new Date();
			const year = now.getFullYear();
			const month = String(now.getMonth() + 1).padStart(2, '0');
			const day = String(now.getDate()).padStart(2, '0');
			const hours = String(now.getHours()).padStart(2, '0');
			const minutes = String(now.getMinutes()).padStart(2, '0');
			const seconds = String(now.getSeconds()).padStart(2, '0');
		},
		//添加表格数据
		addTableData() {
			this.mainData.trainingCourseDetailsList.push({
				trainingCourse: '',
				courseDescription: '',
				trainingMaterials: [],
			});
		},
		openDialog() {
			this.dialogVisible = true;
		},
		showBtnAndTabs() {
			ComplianceTrainingApi.studyBegin({
				id: this.$route.query.id,
				learningProgress: '学习中'
			}).then((res) => {
				this.showBtn = true;
				this.queryCourList();
			})
		},
		sureUpload() {
			ComplianceTrainingApi.sureUpload({
				reportFile: this.mainData.reportFile,
				complianceTrainingLearningTableId: this.mainData.complianceTrainingLearningTableId,
				createPsnId: this.orgContext.currentPsnId,
				createPsnName: this.orgContext.currentPsnName,
				createOgnName: this.orgContext.currentOgnName,
				createDeptName: this.orgContext.currentDeptName,
				createTime: new Date()
			}).then((res) => {
				this.$message.success('上传成功!');
			});
		},
		sureUp() {
			ComplianceTrainingApi.upAndDown({
				id: this.mainData.id,
				like: !this.upNum,
			}).then((res) => {
				this.$message.success('成功');
				this.upNum = !this.upNum;
				this.showDown = false;
				ComplianceTrainingApi.queryById({ id: this.mainData.id }).then((response) => {
					this.mainData.likeCount = response.data.data.likeCount;
				});
			});
		},
		sureDown() {
			ComplianceTrainingApi.upAndDown({
				id: this.mainData.id,
				dislike: !this.downNum,
			}).then((res) => {
				this.$message.success('成功');
				this.downNum = !this.downNum;
				this.showUp = false;
				ComplianceTrainingApi.queryById({ id: this.mainData.id }).then((response) => {
					this.mainData.dislikeCount = response.data.data.dislikeCount;
				});
			});
		},
		sureStudy(index, row) {
			this.$confirm('是否确定已学习完成?', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			}).then(() => {
				new Promise((resolve, reject) => {
					ComplianceTrainingApi.changeStudy({
						courseId: row.courseId,
						houseHoldId: this.mainData.id
					}).then((response) => {
						resolve(response)
					})
				}).then(value => {
					this.queryCourList();
					this.$message.success('学习完成!')
				})
			}).catch(() => {
				this.$message({
					type: 'info',
					message: '已取消'
				})
			})
		},
		saveReview() {
			// this.mainData.createTime = new Date()
			let stamp = new Date();
			let sign2 = ':';
			let year = stamp.getFullYear(); // 年
			let month = stamp.getMonth() + 1; // 月
			let day = stamp.getDate(); // 日
			let hour = stamp.getHours(); // 时
			let minutes = stamp.getMinutes(); // 分
			let seconds = stamp.getSeconds(); //秒
			if (month >= 1 && month <= 9) {
				month = '0' + month;
			}
			if (day >= 0 && day <= 9) {
				day = '0' + day;
			}
			if (hour >= 0 && hour <= 9) {
				hour = '0' + hour;
			}
			if (minutes >= 0 && minutes <= 9) {
				minutes = '0' + minutes;
			}
			if (seconds >= 0 && seconds <= 9) {
				seconds = '0' + seconds;
			}
			this.mainData.createTime = year + '-' + month + '-' + day + ' ' + hour + sign2 + minutes + sign2 + seconds;
			ComplianceTrainingApi.saveReview(this.mainData).then((res) => {
				this.$message.success('评论成功');
				this.mainData.reviewDesc = null;
				ComplianceTrainingApi.queryReview({ id: this.mainData.complianceTrainingLearningTableId }).then((res) => {
					this.mainData.reviewsList = res.data.data.records;
				});
			});
		},
		formatDate(date) {
			if (!date) return '';
			const d = new Date(date);
			const year = d.getFullYear();
			const month = String(d.getMonth() + 1).padStart(2, '0');
			const day = String(d.getDate()).padStart(2, '0');
			return `${year}-${month}-${day}`;
		},
		loadVideo(row){
			let jsonArr = JSON.parse(row.trainingMaterials);
			let docId = jsonArr[0].docId;
			docApi.downloadURL(docId).then(res=>{
				this.videoShow = true;
				this.videoUrl = res.data;
				this.videoWatchDuration = row.videoWatchDuration;
				this.selectRow = row;
			})
		},
		closeVideoDialog(visible,result){
			this.videoShow = visible;
			this.selectRow.videoDuration = result.duration;
			this.selectRow.videoWatchDuration = result.currentTime;
			if(!this.selectRow.finished){
				ComplianceTrainingApi.updateVideoProgress(this.selectRow).then((response) => {
					console.log(result);
					if(result.duration == result.currentTime){
						new Promise((resolve, reject) => {
							ComplianceTrainingApi.changeStudy({
								courseId: this.selectRow.courseId,
								houseHoldId: this.mainData.id
							}).then((response) => {
								resolve(response)
							})
						}).then(value => {
							this.queryCourList();
							this.$message.success('学习完成!')
							this.selectRow = {};
						})
					}else{
						this.selectRow = {};
					}
				})
			}

		},
		parseVideoName(videoJson){
			let jsonArr = JSON.parse(videoJson);
			return jsonArr[0].name;
		}
	},
};
</script>

<style scoped>
.userAvatar {
	background: url(../../../../../assets/img/home/<USER>
	background-size: 100% 100%;
	width: 20px;
	height: 20px;
	border-radius: 20px;
}

.up {
	background: url(../../../../../assets/img/home/<USER>
	background-size: 100% 100%;
	width: 20px;
	height: 20px;
	border-radius: 20px;
	margin-left: 10px;
	cursor: pointer;
}

.upcheck {
	background: url(../../../../../assets/img/home/<USER>
	background-size: 100% 100%;
	width: 20px;
	height: 20px;
	border-radius: 20px;
	margin-left: 10px;
	cursor: pointer;
}

.down {
	background: url(../../../../../assets/img/home/<USER>
	background-size: 100% 100%;
	width: 20px;
	height: 20px;
	border-radius: 20px;
	margin-left: 10px;
	cursor: pointer;
}

.downcheck {
	background: url(../../../../../assets/img/home/<USER>
	background-size: 100% 100%;
	width: 20px;
	height: 20px;
	border-radius: 20px;
	margin-left: 10px;
	cursor: pointer;
}
</style>
