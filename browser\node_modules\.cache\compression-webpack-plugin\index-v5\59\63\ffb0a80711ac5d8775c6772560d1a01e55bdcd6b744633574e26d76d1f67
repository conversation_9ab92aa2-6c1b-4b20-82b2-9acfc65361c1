
2e413e3440a322a939135466c6eb8b8f74ab9302	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.441.1754018536329.js\",\"contentHash\":\"4c191364807319208159f7ccb1c50bfb\"}","integrity":"sha512-xTB5ls54NYUsy4mVsmObqiwWDI8pxjHNQDMqXCkpbB+0JghDOQ2NmHqA+5wrWNJs+e1sLAtEir0Xu0j6HrgjNg==","time":1754018575957,"size":48138}