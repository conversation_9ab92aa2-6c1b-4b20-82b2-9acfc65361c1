
1f96d6b0af76bb3da86b56e5b024f286e14b7bf0	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.139.1754018536329.js\",\"contentHash\":\"8ff3a23c785bbaa8ecff00320f90c87d\"}","integrity":"sha512-OhM9r2rARBrrlGDoDappjLvoXuHOvZdcQ9fNCeFQyIwoVDWOO9EEfmhOzWRNMWi3YV9FKCVwzpzamEK/TSEFGA==","time":1754018575956,"size":37507}