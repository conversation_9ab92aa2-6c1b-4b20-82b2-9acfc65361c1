<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.klaw.dao.contractDao.SgContractSealMapper">

    <resultMap id="BaseResultMap" type="com.klaw.entity.contractBean.SgContractSeal">
        <id column="ID" jdbcType="VARCHAR" property="id" />
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
        <result column="CREATE_PSN_FULL_ID" jdbcType="VARCHAR" property="createPsnFullId" />
        <result column="CREATE_PSN_FULL_NAME" jdbcType="VARCHAR" property="createPsnFullName" />
        <result column="PARENT_ID" jdbcType="VARCHAR" property="parentId" />
        <result column="SEAL_NAME" jdbcType="VARCHAR" property="sealName" />
        <result column="SEAL_ID" jdbcType="VARCHAR" property="sealId" />
        <result column="SEAL_TYPE" jdbcType="VARCHAR" property="sealType" />
        <result column="SEAL_TYPE_ID" jdbcType="VARCHAR" property="sealTypeId" />
        <result column="SEAL_ADMIN" jdbcType="VARCHAR" property="sealAdmin" />
        <result column="SEAL_ADMIN_ID" jdbcType="VARCHAR" property="sealAdminId" />
        <result column="SEAL_NUMBER" jdbcType="VARCHAR" property="sealNumber" />
        <result column="EFFECTIVE_CONDITIONS" jdbcType="VARCHAR" property="effectiveConditions" />
        <result column="PRINTS_NUMBER" jdbcType="VARCHAR" property="printsNumber" />
        <result column="REMARK" jdbcType="VARCHAR" property="remark" />
        <result column="EFFECTIVE_ATTACHMENT" jdbcType="VARCHAR" property="effectiveAttachment" />
        <result column="SEAL_CODE" jdbcType="VARCHAR" property="sealCode" />
    </resultMap>

    <sql id="Base_Column_List">
        ID, SEAL_NAME, SEAL_ID, SEAL_TYPE, SEAL_TYPE_ID, SEAL_NUMBER, EFFECTIVE_CONDITIONS, PRINTS_NUMBER, REMARK, EFFECTIVE_ATTACHMENT, CREATE_TIME, CREATE_PSN_FULL_ID, CREATE_PSN_FULL_NAME, SEAL_ADMIN, SEAL_ADMIN_ID, PARENT_ID
    </sql>

    <sql id="Map_Column_List">
        SCS.ID, SCS.SEAL_NAME, SCS.SEAL_ID, SCS.SEAL_TYPE, SCS.SEAL_TYPE_ID, SCS.SEAL_NUMBER,
        SCS.EFFECTIVE_CONDITIONS, SCS.PRINTS_NUMBER, SCS.REMARK, SCS.EFFECTIVE_ATTACHMENT,
        SCS.CREATE_TIME, SCS.CREATE_PSN_FULL_ID, SCS.CREATE_PSN_FULL_NAME,
        SCS.SEAL_ADMIN, SCS.SEAL_ADMIN_ID, SCS.PARENT_ID,
        SSM.SEAL_CODE
    </sql>

    <select id="getContractSealByContractId" resultMap="BaseResultMap">
        SELECT <include refid="Map_Column_List"/>
        FROM SG_CONTRACT_SEAL SCS
        INNER JOIN SG_SEAL_MANAGE SSM
            ON SCS.SEAL_ID  = SSM.ID
        WHERE SCS.PARENT_ID = #{contractId}
    </select>
</mapper>