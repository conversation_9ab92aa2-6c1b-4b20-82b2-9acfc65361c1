import {request} from '@/api/index'

export default {
  query(data) {
    return request({
      url: '/userange/query',
      method: 'post',
      data
    })
  },
  save(data) {
    return request({
      url: '/userange/save',
      method: 'post',
      data
    })
  },
  delete(data) {
    return request({
      url: '/userange/delete',
      method: 'post',
      data
    })
  },
  queryById(data) {
    return request({
      url: '/userange/queryById',
      method: 'post',
      data
    })
  }
}