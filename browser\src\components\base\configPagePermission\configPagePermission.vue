<template>
    <div class="root">
        <el-button @click="Test">测试11112222</el-button>
        <el-button @click="test2">测试111122229999</el-button>
        <!-- <el-card class="box-card" >
            <div slot="header" class="clearfix">
                <span>卡片名称</span>
                <el-button style="float: right; padding: 3px 0" type="text">操作按钮</el-button>
            </div>
        </el-card> -->
    </div>
</template>

<script>
// import { tvModelQuery } from '../../../api/index'
export default {
    name: 'configPagePermission',
    data(){
        return {
            sourceData: {}
        }
    },
    props: {
        processKey: {
            type: String,
            default: ''
        },
        formKey: {
            type: String,
            default: ''
        },
        nodeId: {

        },
        modeler: {

        },
        element: {

        },
        permissionJson: {}
    },
    methods: {
        onUpdated(element){
            console.log('保存完毕',element)
        },
        Test(){
            this.$emit('customPermission','[{Test:Test}]')
        },
        test2(){
            this.$emit('closeDialog')
        },
        dialogOpen(){
            console.log(this.nodeId,this.modeler,this.element,'open')
        },
        dialogClose(){
            console.log(this.nodeId,this.modeler,this.element,'close')
        },
        submit(){
            this.$emit('customPermission','测试JSON')
        },
        getFormConfig(){
            if(this.formKey){
                const queryData = {
                    tableName: "EASY_PAGE",
                    page: 1,
                    pageSize: 10,
                    "params":[{"andOr":"and","params":[{"andOr":"and","fieldName":'PAGE_NAME',"cpString": '=',"cpValue":this.formKey}]}]
                }
                // tvModelQuery(queryData).then(res => {
                //     if(res.data.success && res.data.rows && res.data.rows.length){
                //         this.sourceData = JSON.parse(res.data.rows[2].PAGE_JSON).dataSources
                //     }
                // })
            }
        }
    },
    mounted(){
        console.log(this.nodeId,this.modeler,this.element,this.processKey,this.formKey)
        this.getFormConfig()
    }
}
</script>

<style scoped lang="scss">
    .root {
        padding-top: 5px;
    }
</style>