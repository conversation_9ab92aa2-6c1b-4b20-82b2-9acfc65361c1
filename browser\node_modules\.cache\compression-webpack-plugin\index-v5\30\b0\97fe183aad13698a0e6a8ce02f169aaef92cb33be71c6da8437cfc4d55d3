
363283980ca3b692f32a254c73d3e3b419bd1413	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.206.1754018536329.js\",\"contentHash\":\"784f791aa03b84954573d19a1dd0e703\"}","integrity":"sha512-Ql00Kux1qPOzI9hgyA1mtWR8E2DpexJ3slqk5qRnCHgDPTmYCReXvsTRDw6L4Y/RJ14TdwHBZJLowWR8QPBcAw==","time":1754018575985,"size":139260}