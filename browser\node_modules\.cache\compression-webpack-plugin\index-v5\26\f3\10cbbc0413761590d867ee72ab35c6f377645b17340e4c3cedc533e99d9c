
96e08ecb7bc797a6a69eb2c4da718d66016e9458	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.368.1754018536329.js\",\"contentHash\":\"d6e3d682853d1082e8742550b57f8f6a\"}","integrity":"sha512-mQuIvNcZMzIG/8ZbhllH4nAUL2NIx7n4UBrh4jZx23OLN542lfk18P69XE4zz/GdegxZ0FskquLY04PzzdOBfg==","time":1754018575975,"size":69188}