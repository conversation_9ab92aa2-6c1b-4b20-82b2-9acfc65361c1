
f83397864e864e41251ece018aa0754f12f2bdab	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.130.1754018536329.js\",\"contentHash\":\"ae925a8b96b4234a2db1409fe05db83d\"}","integrity":"sha512-xA4iwePLTG0DPkUlgrpKZIkRD6yGjgmvn5VaBqdjja+aT/oRxAORfpPWMvbQXCYAGJv3uaYy0Y1UugyTE/l9lA==","time":1754018576047,"size":180125}