package com.klaw.service.imp.contractServiceImpl.contract;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.klaw.dao.contractDao.contract.BmManualMapper;
import com.klaw.entity.contractBean.contract.BmManual;
import com.klaw.service.contractService.contract.BmManualService;
import com.klaw.utils.PageUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * 服务实现类
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
@Service
public class BmManualServiceImpl extends ServiceImpl<BmManualMapper, BmManual> implements BmManualService {

    @Autowired
    BmManualMapper bmManualMapper;


    @Override
    public BmManual getBmManual(Integer id) {

        return bmManualMapper.selectById(id);
    }

    @Override
    public List<BmManual> getAllBmManual() {
        return bmManualMapper.selectList(null);

    }

    @Override
    public void add(BmManual bmManual) {
        bmManualMapper.insert(bmManual);
    }

    @Override
    public int modify(BmManual bmManual) {
        //乐观锁更新
        BmManual currentBmManual = bmManualMapper.selectById(bmManual.getId());
        return bmManualMapper.updateById(bmManual);
    }

    @Override
    public void remove(String ids) {

        if (StringUtils.isNotEmpty(ids)) {
            String[] array = ids.split(",");
            if (!CollectionUtils.isEmpty(Arrays.asList(array))) {
                bmManualMapper.deleteBatchIds(Collections.singletonList(array));
            }
        }

    }

    @Override
    public PageUtils<BmManual> queryPageData(JSONObject jsonObject) {
        //模糊搜索
        String fuzzyValue = jsonObject.getString("fuzzyValue");
        QueryWrapper<BmManual> queryWrapper = new QueryWrapper<>();
        if (StringUtils.isNotBlank(fuzzyValue)) {
            queryWrapper.and(i -> i.like("manual_file", fuzzyValue)
                    .or().like("manual_psn_name", fuzzyValue));
        }
        String isVideo = jsonObject.getString("isVideo");
        if (StringUtils.isNotBlank(isVideo)) {
            queryWrapper.and(i -> i.like("is_video", isVideo));
        }
        queryWrapper.orderByAsc("sort");
        return page(new PageUtils<>(jsonObject), queryWrapper);
    }

    @Override
    public List<BmManual> queryList() {
        //模糊搜索
        QueryWrapper<BmManual> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("data_state_code", "01");
        queryWrapper.orderByAsc("sort");
        return list(queryWrapper);
    }
}


