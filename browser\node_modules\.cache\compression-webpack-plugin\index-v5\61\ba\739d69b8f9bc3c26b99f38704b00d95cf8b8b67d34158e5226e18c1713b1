
349459ee6ed4e19b33449292e8166a0c31c3d086	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.415.1754018536329.js\",\"contentHash\":\"fb91ac235b1fcd6a2e0d69ae4a5facf5\"}","integrity":"sha512-1snsYvpocKx07nqHXn/aPtlTY5A7UkBx8QvwJpTiv3MexQGIQn0GAOwSvNA0SQLkpW6oT7Bb0n8Jqso/pdYfHQ==","time":1754018575976,"size":68105}