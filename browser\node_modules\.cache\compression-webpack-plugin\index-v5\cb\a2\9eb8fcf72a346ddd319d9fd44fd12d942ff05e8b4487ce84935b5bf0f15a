
af744b6e36ce40401fdba7c35764e8ed3f47bda6	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.472.1754018536329.js\",\"contentHash\":\"bad7d81519e6049acb951dbd884dee4f\"}","integrity":"sha512-8b1FWIbWarrONR8Fkjd5M9+wzAKuoG+N5bhUe9mscBJsH0S87O6/vdMsUfxJixfJ6vJr3ZKFABY6rMzFevODjw==","time":1754018575957,"size":34607}