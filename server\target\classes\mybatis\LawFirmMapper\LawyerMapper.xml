<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.klaw.dao.lawyerDao.LawyerMapper">

    <update id="updateStateByIds">
        update SG_LAWYER
        <set>
            <if test="statusCode !=  null ">
                data_state_code=#{statusCode},
            </if>
            <if test="statusName !=  null  and statusName != '' ">
                data_state=#{statusName},UPDATE_TIME = SYSDATE
            </if>
            <if test="lawFirmId !=  null  and lawFirmId != '' ">
                ,LAW_FIRM_ID=#{lawFirmId}
            </if>
            <if test="lawFirmName !=  null  and lawFirmName != '' ">
                ,LAW_FIRM=#{lawFirmName}
            </if>
        </set>
        WHERE
        id in
        <foreach item="dataId" index="index" collection="list"
                 open="(" separator="," close=")"> #{dataId}</foreach>
    </update>
</mapper>