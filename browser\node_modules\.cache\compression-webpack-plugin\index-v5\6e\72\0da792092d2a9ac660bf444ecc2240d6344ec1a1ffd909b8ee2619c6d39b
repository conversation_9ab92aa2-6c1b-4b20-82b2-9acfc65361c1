
e075740a6cafe7f1616ca48a6cd844aa2a1eb07c	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.94.1754018536329.js\",\"contentHash\":\"4f341d45d4ff2a53b88c11e2784ac7d2\"}","integrity":"sha512-cLsa5pMFk8/z/Pqj2B8rQmIWVjl6++QP0TPfP972HLvaJe0OMBbbCRwcpC4ecoqH6B7T2nabBbZelNx3D9l3mg==","time":1754018576088,"size":238138}