<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.klaw.dao.lawyerDao.LawyerBlackListMapper">

    <resultMap id="BaseResultMap" type="com.klaw.entity.lawyerBean.LawyerBlackList">
            <result property="createOgnId" column="CREATE_OGN_ID" jdbcType="VARCHAR"/>
            <result property="createOgnName" column="CREATE_OGN_NAME" jdbcType="VARCHAR"/>
            <result property="createDeptId" column="CREATE_DEPT_ID" jdbcType="VARCHAR"/>
            <result property="createDeptName" column="CREATE_DEPT_NAME" jdbcType="VARCHAR"/>
            <result property="createGroupId" column="CREATE_GROUP_ID" jdbcType="VARCHAR"/>
            <result property="createGroupName" column="CREATE_GROUP_NAME" jdbcType="VARCHAR"/>
            <result property="createPsnId" column="CREATE_PSN_ID" jdbcType="VARCHAR"/>
            <result property="createPsnName" column="CREATE_PSN_NAME" jdbcType="VARCHAR"/>
            <result property="createOrgId" column="CREATE_ORG_ID" jdbcType="VARCHAR"/>
            <result property="createOrgName" column="CREATE_ORG_NAME" jdbcType="VARCHAR"/>
            <result property="createPsnFullId" column="CREATE_PSN_FULL_ID" jdbcType="VARCHAR"/>
            <result property="createPsnFullName" column="CREATE_PSN_FULL_NAME" jdbcType="VARCHAR"/>
            <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="UPDATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="dataState" column="DATA_STATE" jdbcType="VARCHAR"/>
            <result property="dataStateCode" column="DATA_STATE_CODE" jdbcType="DECIMAL"/>
            <result property="id" column="ID" jdbcType="VARCHAR"/>
            <result property="lawyerName" column="LAWYER_NAME" jdbcType="VARCHAR"/>
            <result property="lawFirm" column="LAW_FIRM" jdbcType="VARCHAR"/>
            <result property="lawFirmId" column="LAW_FIRM_ID" jdbcType="VARCHAR"/>
            <result property="beginDate" column="BEGIN_DATE" jdbcType="TIMESTAMP"/>
            <result property="lawyerPhone" column="LAWYER_PHONE" jdbcType="VARCHAR"/>
            <result property="lawyerEmail" column="LAWYER_EMAIL" jdbcType="VARCHAR"/>
            <result property="lawyerTime" column="LAWYER_TIME" jdbcType="TIMESTAMP"/>
            <result property="charteredNo" column="CHARTERED_NO" jdbcType="VARCHAR"/>
            <result property="beGoodAtDomain" column="BE_GOOD_AT_DOMAIN" jdbcType="VARCHAR"/>
            <result property="beGoodAtDomainIds" column="BE_GOOD_AT_DOMAIN_IDS" jdbcType="VARCHAR"/>
            <result property="remarks" column="REMARKS" jdbcType="VARCHAR"/>
            <result property="parentId" column="PARENT_ID" jdbcType="VARCHAR"/>
            <result property="typeName" column="TYPE_NAME" jdbcType="VARCHAR"/>
            <result property="typeCode" column="TYPE_CODE" jdbcType="VARCHAR"/>
            <result property="createLegalUnitId" column="CREATE_LEGAL_UNIT_ID" jdbcType="VARCHAR"/>
            <result property="createLegalUnitName" column="CREATE_LEGAL_UNIT_NAME" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        CREATE_OGN_ID,CREATE_OGN_NAME,CREATE_DEPT_ID,
        CREATE_DEPT_NAME,CREATE_GROUP_ID,CREATE_GROUP_NAME,
        CREATE_PSN_ID,CREATE_PSN_NAME,CREATE_ORG_ID,
        CREATE_ORG_NAME,CREATE_PSN_FULL_ID,CREATE_PSN_FULL_NAME,
        CREATE_TIME,UPDATE_TIME,DATA_STATE,
        DATA_STATE_CODE,ID,LAWYER_NAME,
        LAW_FIRM,LAW_FIRM_ID,BEGIN_DATE,
        LAWYER_PHONE,LAWYER_EMAIL,LAWYER_TIME,
        CHARTERED_NO,BE_GOOD_AT_DOMAIN,BE_GOOD_AT_DOMAIN_IDS,
        JUDICIAL_RESOURCES,CASES_EXPERIENCE,REMARKS,
        IS_COUNSEL,SEQ,WHETHER_HOST_LAWYER,
        PARENT_ID,DATA_SOURCE,SOURCE_ID,
        INVOLVE_LAW_COURT,TYPE_NAME,TYPE_CODE,
        CREATE_LEGAL_UNIT_ID,CREATE_LEGAL_UNIT_NAME
    </sql>
</mapper>
