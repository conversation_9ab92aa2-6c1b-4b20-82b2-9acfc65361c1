<template>
  <el-dialog :close-on-click-modal="false" title="律所信息" :visible.sync="dialogVisible" width="70%">
    <div style="margin-bottom: 5px">
      <el-row>
        <el-col :span="4">
          <el-input v-model="tableQuery.content" clearable placeholder="模糊搜索" @clear="empty_" @keyup.enter.native="select_" />
        </el-col>
        <el-col :span="2">
          <el-button type="primary" @click="select_">查询</el-button>
        </el-col>
      </el-row>
    </div>

    <div>
      <el-table
          v-loading="tableLoading"
          :data="tableData"
          border
          style="table-layout: fixed;width: 99%;"
          :height="dialog_height"
          :fit="true"
          stripe
          highlight-current-row
          @current-change="lawFirmCurrentChange"
          @selection-change="lawFirmSelectionChange"
      >
        <el-table-column type="index" width="50" label="序号" />
        <el-table-column prop="lawyerFirm" label="律所名称" min-width="100" show-overflow-tooltip />
<!--        <el-table-column prop="typeName" label="所属库" min-width="100" show-overflow-tooltip />-->
        <el-table-column prop="beGoodAtDomain" label="擅长领域" min-width="200" show-overflow-tooltip />
      </el-table>
    </div>

    <div style="text-align: center">
      <el-footer>
        <pagination
            v-show="tableQuery.total>0"
            :total="tableQuery.total"
            :page.sync="tableQuery.page"
            :limit.sync="tableQuery.limit"
            style="float: left;padding:20px 16px;"
            @pagination="refreshData"
        />
      </el-footer>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button icon="" class="negative-btn" @click="cancel_">取消</el-button>
      <el-button type="primary" icon="" class="active-btn" @click="sure_">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import lawFirmApi from '@/api/LawyerManage/LawyerFirm/lawyerFirm'
import pagination from "@/components/Pagination"
import {mapGetters} from 'vuex'

export default {
  name: 'FirmLedgerDialog',
  components: { pagination },
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
  },
  computed: {
    ...mapGetters(['orgContext']),
  },
  created() {
  },
  data() {
    return {
      myDialogVisible: this.dialogVisible,
      tableLoading: true,
      typeCode: null,
      typeName: null,
      tableQuery: {
        content: null,
        page: 1,
        limit: 10,
        total: 0,
      },
      tableData: [], // 列表数据源
      dialog_height: 'calc(100vh - 450px)', // table高度
      tableTempData: null,
    }
  },
  watch: {
    dialogVisible(val) {
      this.myDialogVisible = val
      if (val) {
        this.refreshData()
      }
    },
    myDialogVisible(val) {
      this.$emit('update:dialogVisible', val)
    },
  },
  methods: {
    select_() {
      this.refreshData()
    },
    empty_() {
      this.tableQuery = {
        page: 1,
        limit: 10,
        total: 0,
        content: null,
      }
      this.refreshData()
    },
    lawFirmCurrentChange(currentRow) {
      this.tableTempData = currentRow
    },
    lawFirmSelectionChange(selection) {
      this.tableTempData = selection
    },
    refreshData() {
      lawFirmApi.queryLedgerFirmDialog(this.tableQuery).then(res => {
        this.tableData = res.data.page.records
        this.tableQuery.total = res.data.page.total
        this.tableLoading = false
      })
    },
    cancel_() {
      this.myDialogVisible = false
    },
    sure_() {
      this.$emit('lawFirmSure', this.tableTempData)
    },
  }
}
</script>

<style scoped>

</style>
