
fbbababdada792dc111bd1df0aabc5e93e41608a	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.254.1754018536329.js\",\"contentHash\":\"267949582586e963bd1c06d34b05cbc6\"}","integrity":"sha512-4Bn4wBs/cKt4U8U01Vv7i3R8YWWYJxnE51kXeu4T9VUQnuIUXZJ2v+e9L20Gn4iiCTG9acEspV/vuYvBCBN3qA==","time":1754018576004,"size":154080}