<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.klaw.dao.caseDao.CaseMapper">

    <select id="selectStatistics" resultType="java.util.Map">
        SELECT
            b.name AS belongPlate,
            COUNT( 1 ) AS allCaseNum,
            CASE_KIND AS caseKing,
            SUM( CASE_MONEY ) AS allCaseMoney,
            SUM( CASE c.CASE_CURRENT_PROCESS WHEN '结案完成' THEN 0 ELSE 1 END ) AS openCaseNum,
            SUM( CASE c.CASE_CURRENT_PROCESS WHEN '结案完成' THEN 0 ELSE c.CASE_MONEY END ) AS openCaseMoney,
            SUM( CASE c.OUR_POSITION WHEN '原告' THEN 1 WHEN '申请人' THEN 1 ELSE 0 END ) AS plaintiffCaseNum,
            SUM( CASE c.OUR_POSITION WHEN '原告' THEN CASE_MONEY WHEN '申请人' THEN CASE_MONEY ELSE 0 END ) AS plaintiffCaseMoney,
            SUM( CASE c.OUR_POSITION WHEN '被告' THEN 1 WHEN '被申请人' THEN 1 ELSE 0 END ) AS defendantCaseNum,
            SUM( CASE c.OUR_POSITION WHEN '被告' THEN CASE_MONEY WHEN '被申请人' THEN CASE_MONEY ELSE 0 END ) AS defendantCaseMoney
        FROM
            sg_case_records c,hr_org_unit_b b
        <where>
            ${ew.sqlSegment}
        </where>
        GROUP BY
            b.name,
            C.CASE_KIND
        ORDER BY
            b.name
    </select>

    <select id="selectStatisticsTWO" resultType="java.util.Map">
        SELECT
        COUNT( 1 ) AS allCaseNum,
        CASE_KIND AS caseKing,
        SUM( CASE_MONEY ) AS allCaseMoney,
        SUM( CASE c.CASE_CURRENT_PROCESS WHEN '结案完成' THEN 0 ELSE 1 END ) AS openCaseNum,
        SUM( CASE c.CASE_CURRENT_PROCESS WHEN '结案完成' THEN 0 ELSE c.CASE_MONEY END ) AS openCaseMoney,
        SUM( CASE c.OUR_POSITION WHEN '原告' THEN 1 WHEN '申请人' THEN 1 ELSE 0 END ) AS plaintiffCaseNum,
        SUM( CASE c.OUR_POSITION WHEN '原告' THEN CASE_MONEY WHEN '申请人' THEN CASE_MONEY ELSE 0 END ) AS
        plaintiffCaseMoney,
        SUM( CASE c.OUR_POSITION WHEN '被告' THEN 1 WHEN '被申请人' THEN 1 ELSE 0 END ) AS defendantCaseNum,
        SUM( CASE c.OUR_POSITION WHEN '被告' THEN CASE_MONEY WHEN '被申请人' THEN CASE_MONEY ELSE 0 END ) AS
        defendantCaseMoney
        FROM
        sg_case_records c
        <where>
            ${ew.sqlSegment}
        </where>
        GROUP BY
        CASE_KIND
        ORDER BY
        CASE_KIND
    </select>

    <select id="selectEChartsList" resultType="java.util.Map">
        SELECT
            c.BELONG_PLATE as 'name',
            c.belong_Plate_Full_Id,
            c.management_unit,
            count(1) as 'Total',
            count( CASE WHEN OUR_POSITION = '原告' OR OUR_POSITION = '申请人' THEN 1 ELSE NULL END ) AS 'PTotal',
            count( CASE WHEN OUR_POSITION = '被告' OR OUR_POSITION = '被申请人' THEN 1 ELSE NULL END ) AS 'DTotal',
            count( CASE WHEN OUR_POSITION = '第三人' THEN 1 ELSE NULL END ) AS 'TTotal' ,
        truncate(sum( CASE c.OUR_POSITION WHEN '原告' THEN CASE_MONEY WHEN '申请人' THEN CASE_MONEY ELSE 0 END)/10000,2) as 'PMoney',
        truncate(sum( CASE c.OUR_POSITION WHEN '被告' THEN CASE_MONEY WHEN '被申请人' THEN CASE_MONEY ELSE 0 END)/10000,2) as 'DMoney',
        truncate(sum( CASE c.OUR_POSITION WHEN '第三人' THEN CASE_MONEY ELSE 0 END)/10000,2) as 'TMoney'
        FROM
            sg_case_records c
        <where>
            ${ew.sqlSegment}
        </where>
        GROUP BY
            c.belong_Plate_Full_Id
    </select>

    <select id="selectNightingale" resultType="java.util.Map">
        SELECT
            count( 1 ) AS 'value',
                case_type AS 'name'
        FROM
            sg_case_records
        <where>
            ${ew.sqlSegment}
        </where>
        GROUP BY
            case_type
        ORDER BY count( 1 ) desc
    </select>

    <update id="updateSort">
        <foreach item="item" index="index" collection="list" separator=";">
            UPDATE SG_CASE_RECORDS SET SORT = #{item.sort} WHERE id = #{item.id}
        </foreach>
    </update>

    <select id="queryPreservation" resultType="com.klaw.entity.caseBean.child.Preservation">
        SELECT p.preservation_number AS preservationNumber,p.preservation_phase AS preservationPhase,p.preservation_court AS preservationCourt,r.case_name AS caseName,r.id AS parentId
        FROM sg_case_preservation p LEFT JOIN sg_case_records r ON r.id = p.parent_id
        <where>
            ${ew.sqlSegment}
        </where>
        and p.preservation_number IS NOT NULL GROUP BY preservation_number,preservation_phase,preservation_court,case_name,r.id
    </select>


    <select id="queryCaseKind" resultType="java.util.Map">
        SELECT
            CASE_KIND as name,
            count(*) AS value
        FROM
            SG_CASE_RECORDS
        WHERE
            PARENT_ID IS NULL
          AND CASE_KIND IS NOT NULL
        GROUP BY
            CASE_KIND
    </select>

    <select id="queryNewCase" resultType="java.util.Map">
        SELECT
            management_unit as "ognName",
            sum(case_Money) as "newCaseMoney",
            count(*) as "newCaseNum"
        FROM
            SG_CASE_RECORDS
        WHERE
            parent_id IS NULL
            AND case_kind IS NOT NULL
            AND to_char ( case_time, 'yyyy-MM-dd' ) LIKE #{year}
        GROUP BY
            management_unit
    </select>

    <select id="queryNewMajorCase" resultType="java.util.Map">
        SELECT
            management_unit as "ognName",
            sum(case_Money) as "majorCaseMoney",
            count(*) as "majorCaseNum"
        FROM
            SG_CASE_RECORDS
        WHERE
            parent_id IS NULL
          AND case_kind IS NOT NULL
          AND whether_major = 1
          AND to_char ( case_time, 'yyyy-MM-dd' ) LIKE #{year}
        GROUP BY
            management_unit
    </select>

    <select id="queryCaseReportQuarter" resultType="java.util.Map">
        SELECT
            "ognName",
            "caseReportTime",
            "caseReportCreateName" AS "caseReportCreateName",
            SUM("caseReportMoney") AS "caseReportMoney",
            SUM("caseReportNum") AS "caseReportNum"
        FROM
            (
                SELECT
                    CASE
                        WHEN COALESCE(c.management_unit, t.report_ogn_names) = '北京包钢建设投资有限公司' THEN '北京园区开发运营管理平台'
                        WHEN COALESCE(c.management_unit, t.report_ogn_names) = '北京包钢特殊钢有限公司' THEN '北京园区开发运营管理平台'
                        WHEN COALESCE(c.management_unit, t.report_ogn_names) = '包钢集团有限公司园区管理部' THEN '北京园区开发运营管理平台'
                        WHEN COALESCE(c.management_unit, t.report_ogn_names) = '北京包钢园区综合服务有限公司' THEN '北京园区开发运营管理平台'
                        WHEN COALESCE(c.management_unit, t.report_ogn_names) = '战略管控部门' THEN '包钢集团有限公司'
                        WHEN COALESCE(c.management_unit, t.report_ogn_names) = '战略支撑部门' THEN '包钢集团有限公司'
                        WHEN COALESCE(c.management_unit, t.report_ogn_names) = '业务支持服务部门' THEN '包钢集团有限公司'
                        ELSE COALESCE(c.management_unit, t.report_ogn_names)
                    END AS "ognName",
                    t.create_time AS "caseReportTime",
                    t.create_psn_name AS "caseReportCreateName",
                    COALESCE(SUM(c.case_money), 0) AS "caseReportMoney",
                    COUNT(c.management_unit) AS "caseReportNum"
                FROM
                    SG_CASE_REPORT_TASK t
                    LEFT JOIN SG_MIDDLE_RELATION m ON t.ID = m.RELATION_ID
                    LEFT JOIN SG_CASE_RECORDS c ON m.ASSOCIATED_ID = c.ID
                WHERE
                    t.YEAR = #{year}
                    AND t.QUARTER = #{quarter}
                    AND t.data_state != '已回退'
                    AND c.parent_id IS NULL
                GROUP BY COALESCE(c.management_unit, t.report_ogn_names), t.create_time, t.create_psn_name
            )   AS subquery
        GROUP BY
            "ognName", "caseReportTime", "caseReportCreateName"
    </select>

    <select id="queryYearCaseReport" resultType="java.util.Map">
        SELECT
            c.management_unit as "ognName",
            t.create_time as "caseReportTimeYear",
            t.create_psn_name as "caseReportCreateNameYear",
            sum(c.case_money) as "caseReportMoneyYear",
            count(*) as "caseReportNumYear"
        FROM
            SG_CASE_REPORT_TASK t
                JOIN SG_MIDDLE_RELATION m ON t.ID = m.RELATION_ID
                JOIN SG_CASE_RECORDS c ON m.ASSOCIATED_ID = c.ID
        WHERE
            t.YEAR = #{year}
          AND t.data_state != '已回退'
          AND t.report_time_type = 'year'
        GROUP BY c.management_unit, t.create_time, t.create_psn_name
    </select>

    <select id="queryNewCaseQuarter" resultType="java.util.Map">
        SELECT
            "ognName",
            SUM("newCaseReportMoney") AS "newCaseReportMoney",
            SUM("newCaseReportNum") as "newCaseReportNum"
        FROM
            (
                SELECT
                    CASE
                        WHEN COALESCE(c.management_unit, t.report_ogn_names) = '北京包钢建设投资有限公司' THEN '北京园区开发运营管理平台'
                        WHEN COALESCE(c.management_unit, t.report_ogn_names) = '北京包钢特殊钢有限公司' THEN '北京园区开发运营管理平台'
                        WHEN COALESCE(c.management_unit, t.report_ogn_names) = '包钢集团有限公司园区管理部' THEN '北京园区开发运营管理平台'
                        WHEN COALESCE(c.management_unit, t.report_ogn_names) = '北京包钢园区综合服务有限公司' THEN '北京园区开发运营管理平台'
                        WHEN COALESCE(c.management_unit, t.report_ogn_names) = '战略管控部门' THEN '包钢集团有限公司'
                        WHEN COALESCE(c.management_unit, t.report_ogn_names) = '战略支撑部门' THEN '包钢集团有限公司'
                        WHEN COALESCE(c.management_unit, t.report_ogn_names) = '业务支持服务部门' THEN '包钢集团有限公司'
                        ELSE COALESCE(c.management_unit, t.report_ogn_names)
                        END AS "ognName",
                    SUM(c.case_money) as "newCaseReportMoney",
                    count(*) as "newCaseReportNum"
                FROM
                    SG_CASE_REPORT_TASK t
                    LEFT JOIN SG_MIDDLE_RELATION m ON t.ID = m.RELATION_ID
                    LEFT JOIN SG_CASE_RECORDS c ON m.ASSOCIATED_ID = c.ID
                WHERE
                    t.YEAR = #{year}
                    AND t.QUARTER = #{quarter}
                    AND t.data_state != '已回退'
                    AND c.case_kind IS NOT NULL
                    AND c.parent_id IS NULL
                    AND to_char ( c.case_time, 'yyyy-MM-dd' ) LIKE concat(concat('%',#{year}),'%')
                GROUP BY COALESCE(c.management_unit, t.report_ogn_names)
            )   AS subquery
        GROUP BY
            "ognName"
    </select>

    <select id="queryYearNewCase" resultType="java.util.Map">
        SELECT
            c.management_unit as "ognName",
            sum(c.case_money) as "newCaseReportMoneyYear",
            count(*) as "newCaseReportNumYear"
        FROM
            SG_CASE_REPORT_TASK t
                JOIN SG_MIDDLE_RELATION m ON t.ID = m.RELATION_ID
                JOIN SG_CASE_RECORDS c ON m.ASSOCIATED_ID = c.ID
        WHERE
            t.YEAR = #{year}
            AND t.data_state != '已回退'
            AND t.report_time_type = 'year'
            AND to_char ( c.case_time, 'yyyy-MM-dd' ) LIKE concat(concat('%',#{year}),'%')
        GROUP BY c.management_unit
    </select>

    <select id="queryEndCaseQuarter" resultType="java.util.Map">
        SELECT
            "ognName",
            SUM("endCaseReportNum") as "endCaseReportNum"
        FROM
            (
                SELECT
                    CASE
                        WHEN COALESCE(c.management_unit, t.report_ogn_names) = '北京包钢建设投资有限公司' THEN '北京园区开发运营管理平台'
                        WHEN COALESCE(c.management_unit, t.report_ogn_names) = '北京包钢特殊钢有限公司' THEN '北京园区开发运营管理平台'
                        WHEN COALESCE(c.management_unit, t.report_ogn_names) = '包钢集团有限公司园区管理部' THEN '北京园区开发运营管理平台'
                        WHEN COALESCE(c.management_unit, t.report_ogn_names) = '北京包钢园区综合服务有限公司' THEN '北京园区开发运营管理平台'
                        WHEN COALESCE(c.management_unit, t.report_ogn_names) = '战略管控部门' THEN '包钢集团有限公司'
                        WHEN COALESCE(c.management_unit, t.report_ogn_names) = '战略支撑部门' THEN '包钢集团有限公司'
                        WHEN COALESCE(c.management_unit, t.report_ogn_names) = '业务支持服务部门' THEN '包钢集团有限公司'
                        ELSE COALESCE(c.management_unit, t.report_ogn_names)
                        END AS "ognName",
                    COUNT(*) AS "endCaseReportNum"
                FROM
                    SG_CASE_REPORT_TASK t
                    LEFT JOIN SG_MIDDLE_RELATION m ON t.ID = m.RELATION_ID
                    LEFT JOIN SG_CASE_RECORDS c ON m.ASSOCIATED_ID = c.ID
                WHERE
                    t.YEAR = #{year}
                    AND t.QUARTER = #{quarter}
                    AND t.data_state != '已回退'
                    AND c.whether_end = 1
                    AND c.case_kind IS NOT NULL
                    AND c.parent_id IS NULL
                GROUP BY COALESCE(c.management_unit, t.report_ogn_names)
            )   AS subquery
        GROUP BY
            "ognName"
    </select>

    <select id="queryYearEndCase" resultType="java.util.Map">
        SELECT
            c.management_unit as "ognName",
            count(*) as "endCaseReportNumYear"
        FROM
            SG_CASE_REPORT_TASK t
                JOIN SG_MIDDLE_RELATION m ON t.ID = m.RELATION_ID
                JOIN SG_CASE_RECORDS c ON m.ASSOCIATED_ID = c.ID
        WHERE
            t.YEAR = #{year}
            AND t.data_state != '已回退'
            AND c.whether_end = 1
            AND t.report_time_type = 'year'
        GROUP BY c.management_unit
    </select>

    <select id="queryMajorCaseReportQuarter" resultType="java.util.Map">
        SELECT
            "ognName",
            SUM("majorCaseReportNum") as "majorCaseReportNum"
        FROM
            (
                SELECT
                    CASE
                        WHEN COALESCE(c.management_unit, t.report_ogn_names) = '北京包钢建设投资有限公司' THEN '北京园区开发运营管理平台'
                        WHEN COALESCE(c.management_unit, t.report_ogn_names) = '北京包钢特殊钢有限公司' THEN '北京园区开发运营管理平台'
                        WHEN COALESCE(c.management_unit, t.report_ogn_names) = '包钢集团有限公司园区管理部' THEN '北京园区开发运营管理平台'
                        WHEN COALESCE(c.management_unit, t.report_ogn_names) = '北京包钢园区综合服务有限公司' THEN '北京园区开发运营管理平台'
                        WHEN COALESCE(c.management_unit, t.report_ogn_names) = '战略管控部门' THEN '包钢集团有限公司'
                        WHEN COALESCE(c.management_unit, t.report_ogn_names) = '战略支撑部门' THEN '包钢集团有限公司'
                        WHEN COALESCE(c.management_unit, t.report_ogn_names) = '业务支持服务部门' THEN '包钢集团有限公司'
                        ELSE COALESCE(c.management_unit, t.report_ogn_names)
                        END AS "ognName",
                    COUNT(*) AS "majorCaseReportNum"
                FROM
                    SG_CASE_REPORT_TASK t
                        LEFT JOIN SG_MIDDLE_RELATION m ON t.ID = m.RELATION_ID
                        LEFT JOIN SG_CASE_RECORDS c ON m.ASSOCIATED_ID = c.ID
                WHERE
                    t.YEAR = #{year}
                    AND t.QUARTER = #{quarter}
                    AND t.data_state != '已回退'
                    AND c.whether_major = 1
                    AND c.case_kind IS NOT NULL
                    AND c.parent_id IS NULL
                GROUP BY COALESCE(c.management_unit, t.report_ogn_names)
            )   AS subquery
        GROUP BY
            "ognName"
    </select>

    <select id="queryYearMajorCaseReport" resultType="java.util.Map">
        SELECT
            c.management_unit as "ognName",
            count(*) as "majorCaseReportNumYear"
        FROM
            SG_CASE_REPORT_TASK t
                JOIN SG_MIDDLE_RELATION m ON t.ID = m.RELATION_ID
                JOIN SG_CASE_RECORDS c ON m.ASSOCIATED_ID = c.ID
        WHERE
            t.YEAR = #{year}
          AND t.data_state != '已回退'
          AND c.whether_major = 1
          AND t.report_time_type = 'year'
        GROUP BY c.management_unit
    </select>

</mapper>
