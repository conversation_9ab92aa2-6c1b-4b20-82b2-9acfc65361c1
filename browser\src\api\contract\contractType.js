import {request} from '@/api/index'

export default {
  uploadFile(orgId){
    return request({
      url:'/mcp/contractAppSupple/importContractSuppleData?orgId='+ orgId,
      method:'post',
    })
  },
  querySearch(data) {
    return request({
      url: '/contracttype/querySearch',
      method: 'post',
      data
    })
  },
  query(data) {
    return request({
      url: '/contracttype/query',
      method: 'post',
      data
    })
  },
  queryChild(data) {
    return request({
      url: '/contracttype/queryChild',
      method: 'post',
      data
    })
  },
  save(data) {
    return request({
      url: '/contracttype/save',
      method: 'post',
      data
    })
  },

  queryOneType() {
    return request({
      url: '/contracttype/queryOneType',
      method: 'post'
    })
  },
  queryTwoType(data) {
    return request({
      url: '/contracttype/queryTwoType',
      method: 'post',
      data
    })
  },
  checkOnly(data) {
    return request({
      url: '/contracttype/checkOnly',
      method: 'post',
      data
    })
  },
  queryOwnData(data) {
    return request({
      url: '/contracttype/queryOwnData',
      method: 'post',
      data
    })
  },
  queryAll(){
    return request({
      url: '/contracttype/queryAll',
      method: 'post'
    })
  }
}