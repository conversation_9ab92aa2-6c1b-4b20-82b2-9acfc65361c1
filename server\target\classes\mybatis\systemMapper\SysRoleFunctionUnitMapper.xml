<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.klaw.dao.systemDao.SysRoleFunctionUnitMapper2">
  <resultMap id="BaseResultMap" type="com.klaw.entity.systemBean.SysRoleFunctionUnit">
    <!--@mbg.generated-->
    <result column="ROLE_ID" jdbcType="DECIMAL" property="roleId" />
    <result column="FUNCTION_ID" jdbcType="DECIMAL" property="functionId" />
    <result column="UNIT_ID" jdbcType="DECIMAL" property="unitId" />
    <result column="SID" jdbcType="DECIMAL" property="sid" />
    <result column="CREATED_BY" jdbcType="DECIMAL" property="createdBy" />
    <result column="CREATION_DATE" jdbcType="TIMESTAMP" property="creationDate" />
    <result column="LAST_UPDATED_BY" jdbcType="DECIMAL" property="lastUpdatedBy" />
    <result column="LAST_UPDATE_DATE" jdbcType="TIMESTAMP" property="lastUpdateDate" />
    <result column="OBJECT_VERSION_NUMBER" jdbcType="DECIMAL" property="objectVersionNumber" />
  </resultMap>
  <insert id="insert" parameterType="com.klaw.entity.systemBean.SysRoleFunctionUnit">
    <!--@mbg.generated-->
    insert into SYS_ROLE_FUNCTION_UNIT (ROLE_ID, FUNCTION_ID, UNIT_ID, 
      SID, CREATED_BY, CREATION_DATE, 
      LAST_UPDATED_BY, LAST_UPDATE_DATE, OBJECT_VERSION_NUMBER
      )
    values (#{roleId,jdbcType=DECIMAL}, #{functionId,jdbcType=DECIMAL}, #{unitId,jdbcType=DECIMAL}, 
      #{sid,jdbcType=DECIMAL}, #{createdBy,jdbcType=DECIMAL}, #{creationDate,jdbcType=TIMESTAMP}, 
      #{lastUpdatedBy,jdbcType=DECIMAL}, #{lastUpdateDate,jdbcType=TIMESTAMP}, #{objectVersionNumber,jdbcType=DECIMAL}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.klaw.entity.systemBean.SysRoleFunctionUnit">
    <!--@mbg.generated-->
    insert into SYS_ROLE_FUNCTION_UNIT
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="roleId != null">
        ROLE_ID,
      </if>
      <if test="functionId != null">
        FUNCTION_ID,
      </if>
      <if test="unitId != null">
        UNIT_ID,
      </if>
      <if test="sid != null">
        SID,
      </if>
      <if test="createdBy != null">
        CREATED_BY,
      </if>
      <if test="creationDate != null">
        CREATION_DATE,
      </if>
      <if test="lastUpdatedBy != null">
        LAST_UPDATED_BY,
      </if>
      <if test="lastUpdateDate != null">
        LAST_UPDATE_DATE,
      </if>
      <if test="objectVersionNumber != null">
        OBJECT_VERSION_NUMBER,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="roleId != null">
        #{roleId,jdbcType=DECIMAL},
      </if>
      <if test="functionId != null">
        #{functionId,jdbcType=DECIMAL},
      </if>
      <if test="unitId != null">
        #{unitId,jdbcType=DECIMAL},
      </if>
      <if test="sid != null">
        #{sid,jdbcType=DECIMAL},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=DECIMAL},
      </if>
      <if test="creationDate != null">
        #{creationDate,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdatedBy != null">
        #{lastUpdatedBy,jdbcType=DECIMAL},
      </if>
      <if test="lastUpdateDate != null">
        #{lastUpdateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="objectVersionNumber != null">
        #{objectVersionNumber,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>
</mapper>