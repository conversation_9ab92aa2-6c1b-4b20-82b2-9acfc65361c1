
738c27c940e2e7bc15ef6c4a6d2a6204e1a29b4d	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.178.1754018536329.js\",\"contentHash\":\"3a0ca97cbc966009550ff2aa783454b4\"}","integrity":"sha512-UBIPrG/8hzSB27wiLWXlrWQS607Z/w7q+pBt4GCa1htkds9LHbQvZ5hUSQXaynUX/awmNzbISv9hsHc/WkEUaA==","time":1754018576242,"size":434661}