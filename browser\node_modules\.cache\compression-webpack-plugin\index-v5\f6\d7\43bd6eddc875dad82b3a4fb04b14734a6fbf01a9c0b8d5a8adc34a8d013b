
bddf6fbcbaa70ef277484cd243b35d9fb1caca6a	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.18.1754018536329.js\",\"contentHash\":\"896e2b7152c582133f8218942df24a7b\"}","integrity":"sha512-8DzOOCQ3IX3ed9aHp7wSiDF4BsvaCv2WALkoQSox6Ou2/ERfnp21TR9NEGWgkfja6k7f605RwRbu1vwbm8FCyg==","time":1754018575958,"size":90692}