package com.klaw.service.imp.caseServiceImpl;



import com.alibaba.fastjson.JSONObject;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.klaw.dao.caseDao.DisputeProjectMapper;
import com.klaw.entity.caseBean.DisputeProject;
import com.klaw.entity.caseBean.child.MiddleRelation;
import com.klaw.service.caseService.DisputeProjectService;
import com.klaw.service.caseService.childService.MiddleRelationService;
import com.klaw.utils.PageUtils;
import com.klaw.utils.Utils;
import com.klaw.vo.Json;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Map;


@Service
public class DisputeProjectServiceImpl extends ServiceImpl<DisputeProjectMapper, DisputeProject> implements DisputeProjectService {
    @Autowired
    private MiddleRelationService middleRelationService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveData(DisputeProject disputeProject)  {

        //插入或更新群诉案件数据
//        insertOrUpdateAllColumn(disputeProject);
        saveOrUpdate(disputeProject);

        //保存关联纠纷、关联案件
        middleRelationService.saveData(disputeProject.getId(),new String[]{"dispute","disputeProject"},disputeProject.getRelationDisputes());


    }

    @Override
    public DisputeProject queryDataById(String id) {
        DisputeProject disputeProject = getById(id);

        Map<String, List<MiddleRelation>> map = middleRelationService.queryData(id, "disputeProject",new String[]{"dispute"});
        disputeProject.setRelationDisputes(map.get("dispute"));
        return disputeProject;
    }

    @Override
    public Page<DisputeProject> queryPageData(JSONObject json)  {

        boolean isQuery = json.containsKey("isQuery") ? json.getBoolean("isQuery") : true;
        String fuzzyValue = json.containsKey("fuzzyValue") ? json.getString("fuzzyValue") : null;
        String projectName = json.containsKey("projectName") ? json.getString("projectName") : null;
        String projectCode = json.containsKey("projectCode") ? json.getString("projectCode") : null;
        String projectType = json.containsKey("projectType") ? json.getString("projectType") : null;
        String projectArea = json.containsKey("projectArea") ? json.getString("projectArea") : null;
        //立项时间(最小值)
        Date setTimeMin = json.containsKey("setTimeMin") ? json.getDate("setTimeMin") : null;
        //立项时间(最大值)
        Date setTimeMax = json.containsKey("setTimeMax") ? json.getDate("setTimeMax") : null;

        String orderCol = json.containsKey("orderCol") ? json.getString("orderCol") : null;
        boolean orderColValue = json.containsKey("orderColValue") ? json.getBoolean("orderColValue") : false;

        QueryWrapper<DisputeProject> wrapper = new QueryWrapper<DisputeProject>();


        if (StringUtils.isNotBlank(projectName)) {
            wrapper.and(i ->i.like("project_name", projectName));
        }
        if (StringUtils.isNotBlank(projectCode)) {
            wrapper.and(i ->i.like("project_code", projectCode));
        }
        if (StringUtils.isNotBlank(projectType)) {
            wrapper.and(i ->i.like("project_type_id", projectType));
        }
        if (StringUtils.isNotBlank(projectArea)) {
            wrapper.and(i ->i.like("project_area", projectArea));
        }
        if(setTimeMin != null){
            wrapper.and(i ->i.ge("set_time",setTimeMin));
        }
        if(setTimeMax != null){
            wrapper.and(i ->i.le("set_time",new Date(setTimeMax.getTime()+24 * 60 * 60 * 1000-1)));
        }
        if (StringUtils.isNotBlank(fuzzyValue)) {
            wrapper.and(i ->i.like("PROJECT_NAME", fuzzyValue).
                     or().like("PROJECT_CODE", fuzzyValue).
                     or().like("PROJECT_TYPE", fuzzyValue).
                     or().like("PROJECT_AREA", fuzzyValue).
                     or().like("to_char(SET_TIME,'yyyy-MM-dd')", fuzzyValue));

//            String[] cols = {"PROJECT_NAME", "PROJECT_CODE", "PROJECT_TYPE", "PROJECT_AREA", "CREATE_OGN_NAME",  "CREATE_PSN_NAME"};
//            wrapper.andNew();
//            int i = 0;
//            for (String col : cols) {
//                if (i == 0) {
//                    wrapper.like(col, fuzzyValue);
//                } else {
//                    wrapper.or().like(col, fuzzyValue);
//                }
//                i++;
//            }
        }


            //数据权限
//            Utils.dataPermSql(wrapper,null,isQuery);



        if(StringUtils.isNotBlank(orderCol)){
            wrapper.orderBy(false, orderColValue,Utils.humpToLine2(orderCol));
        }
        wrapper.orderBy(false, true,"create_time");

        return page(new PageUtils<DisputeProject>(json), wrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Json deleteDataById(String id) {
        String state = "fail";
        if (StringUtils.isNotBlank(id)) {

            middleRelationService.remove(new QueryWrapper<MiddleRelation>().eq("relation_id",id).or().eq("associated_id",id));
            boolean flag = removeById(id);
            state = "success";
        }
        return Json.succ("deleteDataById").data("state", state);
    }
}
