
5373b6318139d9a0289705826431a6df15bf8b80	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.208.1754018536329.js\",\"contentHash\":\"72ec5aa14e54fe9007792cacbd64ed77\"}","integrity":"sha512-hxkAPSPCT2EPn6Nm9scJaWR1nvZ8D5d9a1ucRJPF3oexS9a2wdyKmdK75kByruoCm/gmQyO8wcvwEIqlDfga2w==","time":1754018575989,"size":139620}