
81d2cac19dc89317356af5e7096b9ebc94c89edb	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.471.1754018536329.js\",\"contentHash\":\"8ac3868f3471d6ae5983ffe6f80b08a7\"}","integrity":"sha512-QtaFYY0/C+8Czk5sXh/gei4rFFBOzQ0sPNCDwPlOCIgfYAEdnoxjbfBKzdcNeU0QhU9jP9rXgDZzxCpq9O4tOg==","time":1754018575957,"size":47533}