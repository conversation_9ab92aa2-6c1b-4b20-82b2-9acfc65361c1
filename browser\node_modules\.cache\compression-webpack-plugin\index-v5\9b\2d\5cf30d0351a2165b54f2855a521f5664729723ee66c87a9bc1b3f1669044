
d6837ac4a02e7db7dc0ea9c6ec592a49edb93b61	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.438.1754018536329.js\",\"contentHash\":\"f76dc98480930e94760701df0015ed5c\"}","integrity":"sha512-r4dHv0EYFVKgST+nqesaqrUHhhnqEDvskvTK47wN//rjzMrK0u7G0cGfoGKSmrTzi9bqdyHgz60bEnFgFJCDFw==","time":1754018575976,"size":64786}