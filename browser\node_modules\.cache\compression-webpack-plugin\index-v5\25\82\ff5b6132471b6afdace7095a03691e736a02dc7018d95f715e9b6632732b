
d44896ef6be99f8ce1cc1a88f04727445ae9a3cb	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.117.1754018536329.js\",\"contentHash\":\"8c8d2542972a37ac4de05bcde6f904b7\"}","integrity":"sha512-AMdu8Zf/dTLCEl51VJZ28T9WFj5fKJ38tGVxscSYDRo86+1Z4heKdE4B1o3GGkObE6Lgkl/ye0kbClv99yJFOQ==","time":1754018576045,"size":178832}