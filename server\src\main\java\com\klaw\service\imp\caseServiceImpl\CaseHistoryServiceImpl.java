package com.klaw.service.imp.caseServiceImpl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.klaw.constant.CaseKind;
import com.klaw.dao.caseDao.CaseHistoryMapper;
import com.klaw.entity.caseBean.CaseRecord;
import com.klaw.entity.caseBean.CaseRecordHistory;
import com.klaw.entity.caseBean.child.*;
import com.klaw.service.caseService.CaseHistoryService;
import com.klaw.service.caseService.childService.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;


@Service
public class CaseHistoryServiceImpl extends ServiceImpl<CaseHistoryMapper, CaseRecordHistory> implements CaseHistoryService {


    @Autowired
    private AgentService agentService;
    @Autowired
    private PartiesService partiesService;
    @Autowired
    private IndictmentService indictmentService;
    @Autowired
    private ClaimService claimService;
    @Autowired
    private EvidenceService evidenceService;
    @Autowired
    private PreservationService preservationService;
    @Autowired
    private MiddleRelationService middleRelationService;
    @Autowired
    private JudgeContentService judgeContentService;
    @Autowired
    private JudgeFilesService judgeFilesService;
    @Autowired
    private CaseProcessService caseProcessService;
    @Autowired
    private AppraisalService appraisalService;
    @Autowired
    private CaseHistoryService caseHistoryService;
    @Autowired
    private FildocService fildocService;
    @Autowired
    private OtherDataService otherDataService;
    @Autowired
    private CaseEvidenceDataService caseEvidenceDataService;

//    @Autowired
//    private SysOrgService sysOrgService;

//    @Autowired
//    private SysNoticeService sysNoticeService;

    @Autowired
    private TagService tagService;

    @Autowired
    private CaseConfigService caseConfigService;

    @Autowired
    private AgentContractService agentContractService;

    @Autowired
    private CaseEasyModelService caseEasyModelService;
    @Autowired
    private CaseHistoryMapper caseHistoryMapper;

    @Override
    public CaseRecord queryDataById(String id) {
        CaseRecord caseBean = caseHistoryMapper.selectById(id);

        if (caseBean == null) {
            caseBean = caseHistoryService.getById(id);
        }

        //首环节的时候查询
        if (StringUtils.isBlank(caseBean.getParentId())) {

            caseBean.setAgentList(agentService.list(new QueryWrapper<Agent>().eq("parent_id", caseBean.getId()).orderBy(false,true,"seq")));
            caseBean.setAgentContractList(agentContractService.queryDataById(caseBean.getId()));

            Map<String, List<MiddleRelation>> map = middleRelationService.queryData(id, "case", new String[]{"caseProject", "dispute", "case"});
            List<MiddleRelation> relations = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(map.get("dispute"))) relations.addAll(map.get("dispute"));
            if (CollectionUtils.isNotEmpty(map.get("caseProject")))
                relations.addAll(map.get("caseProject"));
            if (CollectionUtils.isNotEmpty(map.get("case")))
                relations.addAll(map.get("case"));
            caseBean.setRelations(relations);
            caseBean.setCaseEasyModel(caseEasyModelService.queryDataByCaseId(caseBean.getId()));

            caseBean.setPreservationList(preservationService.list(new QueryWrapper<Preservation>().eq("parent_id", caseBean.getId()).orderBy(false,true,"seq")));
        }

        List<String> childs = getHasChilds(caseBean.getCaseProcessTypeCode());

        if (childs.isEmpty()) {
            return caseBean;
        }

        if (childs.contains("parties"))
            caseBean.setPartiesList(partiesService.list(new QueryWrapper<Parties>().eq("master_id", caseBean.getId()).orderBy(false,true,"seq")));
        if (childs.contains("claim"))
            caseBean.setClaimList(claimService.list(new QueryWrapper<Claim>().eq("parent_id", caseBean.getId()).orderBy(false,true,"seq")));
        if (childs.contains("caseProcess"))
            caseBean.setCaseProcessList(caseProcessService.list(new QueryWrapper<CaseProcess>().eq("parent_id", caseBean.getId()).orderBy(false,true,"seq")));
        if (childs.contains("appraisal"))
            caseBean.setAppraisalList(appraisalService.list(new QueryWrapper<Appraisal>().eq("parent_id", caseBean.getId()).orderBy(false,true,"seq")));
        if (childs.contains("judgeContent"))
            caseBean.setJudgeContentList(judgeContentService.list(new QueryWrapper<JudgeContent>().eq("parent_id", caseBean.getId()).orderBy(false,true,"seq")));
        if (childs.contains("otherData"))
            caseBean.setOtherDataList(otherDataService.list(new QueryWrapper<OtherData>().eq("parent_id", caseBean.getId()).orderBy(false,true,"seq")));
        if (childs.contains("caseEvidenceData"))
            caseBean.setCaseEvidenceDataList(caseEvidenceDataService.list(new QueryWrapper<CaseEvidenceData>().eq("parent_id", caseBean.getId()).orderBy(false,true,"create_Time")));

        return caseBean;
    }


    private List<String> getHasChilds(String code) {
        String o1 = "parties"; //当事人
        String o2 = "claim"; //诉讼请求
        String o3 = "caseProcess"; //过程记录
        String o7 = "appraisal"; //鉴定
        String o9 = "judgeContent"; //裁判内容
        String o10 = "otherData"; //其他信息
        String o11 = "cf"; //查封信息
        String o12 = "caseEvidenceData"; //查封信息
        if (StringUtils.isBlank(code)){
            return Arrays.asList(o1, o2, o3, o10, o12);
        }
        if (code.equals(CaseKind.LA.getKey())) {
            return Arrays.asList(o1, o2, o3, o10, o12);
        }
        if (code.equals(CaseKind.SQLT.getKey())) {
            return Arrays.asList(o3, o7, o9, o10);
        }
        if (code.equals(CaseKind.CXS.getKey())) {
            return Arrays.asList(o3, o10);
        }
        if (code.equals(CaseKind.CXES.getKey())) {
            return Arrays.asList(o3, o10);
        }
        if (code.equals(CaseKind.YS.getKey()) || code.equals(CaseKind.CSYS.getKey()) || code.equals(CaseKind.ZSYS.getKey())
                || code.equals(CaseKind.ZC.getKey())) {
            return Arrays.asList(o1, o2, o3, o7, o9, o10, o12);
        }
        if (code.equals(CaseKind.ES.getKey()) || code.equals(CaseKind.CSES.getKey()) || code.equals(CaseKind.ZSES.getKey())) {
            return Arrays.asList(o1, o2, o3, o9, o10);
        }
        if (code.equals(CaseKind.ZS.getKey())) {
            return Arrays.asList(o3, o9, o10);
        }
        if (code.equals(CaseKind.ZX.getKey())) {
            return Arrays.asList(o3, o7, o10, o11);
        }
        if (code.equals(CaseKind.JA.getKey())) {
            return new ArrayList<>();
        }
        if (code.equals(CaseKind.CXZC.getKey())) {
            return Arrays.asList(o3, o7, o10);
        }
        return new ArrayList<>();
    }

}
