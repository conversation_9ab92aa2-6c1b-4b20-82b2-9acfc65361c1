<template>
  <el-container class="container-manage-sg" direction="vertical">
    <el-header>
      <el-card v-if="activeName !== 'lawyer'">
        <el-input
            v-model="temp.fuzzyValue"
            class="filter_input"
            clearable
            placeholder="检索条件（律所名称、统一社会信用代码、负责人、传真、电子邮箱）"
            @clear="refreshData"
            @keyup.enter.native="refreshData"
        >
          <el-popover slot="prepend" placement="bottom-start" trigger="click" width="1000">
            <el-form ref="queryForm" label-width="100px" size="mini">
              <el-row>
                <el-col :span="12">
                  <el-form-item label="律所名称">
                    <el-input v-model="temp.lawyerFirm" clearable placeholder="请输入..." style="width:100%"/>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="负责人">
                    <el-input v-model="temp.functionary" clearable placeholder="请输入..." style="width:100%"/>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="12" class="mylabel">
                  <el-form-item label="统一社会信用代码">
                    <el-input v-model="temp.licenseCode" clearable placeholder="请输入..." style="width:100%"/>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="状态">
                    <el-select v-model="temp.dataState" clearable placeholder="请选择" style="width:100%"
                               @clear="refreshData" @keyup.enter.native="refreshData">
                      <el-option
                          v-for="item in utils.dataState_LAWYER_data"
                          :key="item.id"
                          :label="item.dicName"
                          :value="item.dicName"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="12">
                  <el-form-item label="律所类型">
                    <el-select v-model="temp.lawFirmType" clearable placeholder="请选择" style="width:100%"
                               @clear="refreshData" @keyup.enter.native="refreshData">
                      <el-option
                          v-for="item in lawFirmTypeData"
                          :key="item.id"
                          :label="item.name"
                          :value="item.name"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-button-group style="float: right">
                <el-button icon="el-icon-search" type="primary" @click="search_">搜索</el-button>
                <el-button icon="el-icon-refresh-left" type="primary" @click="empty_">重置</el-button>
              </el-button-group>
            </el-form>
            <el-button slot="reference" type="primary">高级检索</el-button>
          </el-popover>
          <el-button slot="append" icon="el-icon-search" @click="search_"/>
        </el-input>
      </el-card>
      <el-card v-else>
        <el-input
            v-model="temp.fuzzyValue"
            class="filter_input"
            clearable
            placeholder="检索条件（律所名称、统一社会信用代码、负责人、传真、电子邮箱）"
            @clear="refreshData"
            @keyup.enter.native="refreshData"
        >
          <el-popover slot="prepend" placement="bottom-start" trigger="click" width="1000">
            <el-form ref="queryForm" label-width="100px" size="mini">
              <el-row>
                <el-col :span="12">
                  <el-form-item label="律师名称">
                    <el-input v-model="temp.lawyerName" clearable placeholder="请输入..." style="width:100%"/>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="所属律所">
                    <el-input v-model="temp.lawyerFirm" clearable placeholder="请输入..." style="width:100%"/>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="12">
                  <el-form-item label="经办时间">
                    <el-row>
                      <el-col :span="11">
                        <el-date-picker
                            v-model="temp.createTimeMin"
                            format="yyyy-MM-dd"
                            placeholder="选择日期"
                            style="width: 100%"
                            type="date"
                            value-format="yyyy-MM-dd"
                        />
                      </el-col>
                      <el-col :span="2" style="text-align: center;">至</el-col>
                      <el-col :span="11">
                        <el-date-picker
                            v-model="temp.createTimeMax"
                            format="yyyy-MM-dd"
                            placeholder="选择日期"
                            style="width: 100%"
                            type="date"
                            value-format="yyyy-MM-dd"
                        />
                      </el-col>
                    </el-row>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="状态">
                    <el-select v-model="temp.dataState" clearable placeholder="请选择" style="width:100%"
                               @clear="refreshData" @keyup.enter.native="refreshData">
                      <el-option
                          v-for="item in utils.dataState_LAWYER_data"
                          :key="item.id"
                          :label="item.dicName"
                          :value="item.dicName"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="12">
                  <el-form-item label="主责律师">
                    <el-radio-group v-model="temp.whetherHostLawyer">
                      <el-radio border label="true">是</el-radio>
                      <el-radio border label="false">否</el-radio>
                    </el-radio-group>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-button-group style="float: right">
                <el-button icon="el-icon-search" type="primary" @click="search_">搜索</el-button>
                <el-button icon="el-icon-refresh-left" type="primary" @click="empty_">重置</el-button>
              </el-button-group>
            </el-form>
            <el-button slot="reference" type="primary">高级检索</el-button>
          </el-popover>
          <el-button slot="append" icon="el-icon-search" @click="search_"/>
        </el-input>
      </el-card>
    </el-header>

    <el-main>
      <el-card>
        <div style='position: relative;'>
          <el-tabs v-model="activeName" @tab-click="handleClick">
            <!--集团推荐库-->
            <el-tab-pane label="律所列表" name="lawFirm1">
              <div style="width: 100%">
                <el-table
                    ref="table"
                    :data="tableData_LawFirm"
                    :height="table_height"
                    :show-overflow-tooltip="true"
                    border
                    fit
                    highlight-current-row
                    stripe
                    style="width: 100%"
                    @sort-change="tableSort_PublicLawFirm"
                    @row-dblclick="rowDblclick_LawFirm"
                    @row-click="rowClick_LawFirm"
                >
                  <el-table-column align="center" label="序号" type="index" width="50"/>
                  <el-table-column v-if="ss.tableColumns.find(item => item.key === 'lawyerFirm').visible" align="center"
                                   label="律所名称" min-width="160" prop="lawyerFirm" show-overflow-tooltip
                                   sortable="custom">
                    <template slot-scope="scope">
                      <span v-if="topLawFirm(scope.row.licenseCode)" class="el-icon-s-help" style="color:red;"></span>
                      {{ scope.row.lawyerFirm }}
                    </template>
                  </el-table-column>
                  <el-table-column v-if="ss.tableColumns.find(item => item.key === 'licenseCode').visible"
                                   align="center" label="统一社会信用代码" min-width="120" prop="licenseCode"
                                   show-overflow-tooltip sortable="custom"/>
                  <el-table-column v-if="ss.tableColumns.find(item => item.key === 'lawFirmType').visible"
                                   align="center"
                                   label="律所类型" min-width="100" prop="lawFirmType" show-overflow-tooltip
                                   sortable="custom"/>
                  <el-table-column v-if="ss.tableColumns.find(item => item.key === 'functionary').visible"
                                   align="center"
                                   label="负责人" min-width="100" prop="functionary" show-overflow-tooltip
                                   sortable="custom"/>
                  <el-table-column v-if="ss.tableColumns.find(item => item.key === 'registerAddress').visible"
                                   align="center"
                                   label="地址" min-width="160" prop="registerAddress" show-overflow-tooltip
                                   sortable="custom"/>
                  <el-table-column v-if="ss.tableColumns.find(item => item.key === 'lawFirmLevel').visible"
                                   align="center"
                                   label="律所规模" min-width="90" prop="lawFirmLevel" show-overflow-tooltip
                                   sortable="custom"/>
                  <el-table-column v-if="ss.tableColumns.find(item => item.key === 'createOgnName').visible"
                                   align="center"
                                   label="经办单位" min-width="90" prop="createOgnName" show-overflow-tooltip
                                   sortable="custom"/>
                  <el-table-column v-if="ss.tableColumns.find(item => item.key === 'createTime').visible" align="center"
                                   label="经办时间" min-width="100" prop="createTime" show-overflow-tooltip
                                   sortable="custom">
                    <template slot-scope="scope">
                      <span>{{ scope.row.createTime | parseTime }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column v-if="ss.tableColumns.find(item => item.key === 'dataState').visible" align="center"
                                   label="状态" min-width="80" prop="dataState" show-overflow-tooltip
                                   sortable="custom"/>
                </el-table>
              </div>
            </el-tab-pane>
            <!--律师-->
            <el-tab-pane label="律师列表" name="lawyer">
              <div style="width: 100%">
                <el-table
                    ref="table2"
                    :data="tableData_Lawyer"
                    :height="table_height"
                    :show-overflow-tooltip="true"
                    border
                    fit
                    highlight-current-row
                    stripe
                    style="width: 100%"
                    @sort-change="tableSort_Lawyer"
                    @row-dblclick="rowDblclick_Lawyer"
                    @row-click="rowClick_Lawyer"
                >
                  <el-table-column align="center" label="序号" type="index" width="50"/>
                  <el-table-column v-if="ss2.tableColumns.find(item => item.key === 'lawyerName').visible"
                                   align="center"
                                   label="律师名称" min-width="200" prop="lawyerName" show-overflow-tooltip
                                   sortable="custom">
                    <template slot-scope="scope">
                      <span v-if="topLawFirm(scope.row.licenseCode)" class="el-icon-s-help" style="color:red;"></span>
                      {{ scope.row.lawyerName }}
                    </template>
                  </el-table-column>
                  <el-table-column v-if="ss2.tableColumns.find(item => item.key === 'whetherHostLawyer').visible"
                                   align="center" label="主责律师" min-width="100" prop="whetherHostLawyer"
                                   show-overflow-tooltip sortable="custom">
                    <template slot-scope="scope">
                      <span>{{ scope.row.whetherHostLawyer | whetherHostLawyerFilter }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column v-if="ss2.tableColumns.find(item => item.key === 'lawFirm').visible" align="center"
                                   label="所属律所" min-width="100" prop="lawFirm" show-overflow-tooltip
                                   sortable="custom"/>
                  <el-table-column v-if="ss2.tableColumns.find(item => item.key === 'beGoodAtDomain').visible"
                                   align="center" label="擅长领域" min-width="100" prop="beGoodAtDomain"
                                   show-overflow-tooltip
                                   sortable="custom"/>
                  <!--                <el-table-column-->
                  <!--                    v-if="ss2.tableColumns.find(item => item.key === 'typeName').visible"-->
                  <!--                    label="所属库"-->
                  <!--                    prop="typeName"-->
                  <!--                    min-width="80"-->
                  <!--                    align="center"-->
                  <!--                    show-overflow-tooltip sortable="custom"-->
                  <!--                />-->
                  <el-table-column v-if="ss2.tableColumns.find(item => item.key === 'createTime').visible"
                                   align="center"
                                   label="经办时间" min-width="120" prop="createTime" show-overflow-tooltip
                                   sortable="custom">
                    <template slot-scope="scope">
                      <span>{{ scope.row.createTime | parseTime }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column v-if="ss2.tableColumns.find(item => item.key === 'dataState').visible" align="center"
                                   label="状态" min-width="80" prop="dataState" show-overflow-tooltip
                                   sortable="custom"/>
                </el-table>
              </div>
            </el-tab-pane>
          </el-tabs>
          <div style="margin-bottom: 10px">
            <el-button size="small" style='position: absolute;right:10px;top:5px;' type="primary" @click="lawFirmAxis">
              时间轴
            </el-button>
          </div>
        </div>
      </el-card>
    </el-main>

    <el-footer>
      <pagination
          :limit.sync="temp.limit"
          :page.sync="temp.page"
          :total="temp.total"
          align="center"
          @pagination="refreshData"
      />
    </el-footer>
    <!--律所时间轴-->
    <el-drawer ref="drawer"
               :size="drawerWidth"
               :visible.sync="drawerDialog"
               :wrapper-closable="false"
               custom-class="case-drawer">
      <div class="slot">
        <div style="font-weight: bolder;font-size: 16px;padding-left: 20px;">
          <i class="el-icon-pie-chart"/>
          <span>{{ drawerTitle }}</span>
        </div>
      </div>
      <el-divider/>
      <div class="myCard" style="height: calc(100vh - 120px);">
        <el-scrollbar style="height:100%">
          <el-timeline style="margin-right: 10px;">
            <el-timeline-item v-for="item in timeAxis" :key="item" color="#0bbd87" placement="top">
              <el-card>
                <el-row>
                  <el-col :span="18">
                    <span class="title" style="font-weight: bolder">
                      {{ item.TIME | parseTime }}：{{ item.NAME }}{{ item.operating }}</span>
                  </el-col>
                  <el-col :span="6">
                    <el-button v-if="item.operating !== '黑名单'" @click="timeAxis_(item)">审批流程</el-button>
                  </el-col>
                </el-row>
              </el-card>
            </el-timeline-item>
          </el-timeline>
        </el-scrollbar>
      </div>
    </el-drawer>

    <!--律师时间轴-->
    <el-drawer ref="drawer" :size="drawerWidth" :visible.sync="drawerDialog_" :wrapper-closable="false"
               custom-class="case-drawer">
      <div class="slot">
        <div style="font-weight: bolder;font-size: 16px;padding-left: 20px;">
          <i class="el-icon-pie-chart"/>
          <span>{{ drawerTitle }}</span>
        </div>
      </div>
      <el-divider/>
      <div class="myCard" style="height: calc(100vh - 120px);">
        <el-scrollbar style="height:100%">
          <el-timeline style="margin-right: 10px;">
            <el-timeline-item v-for="item in timeAxis" :key="item" :color="changeColor(item.OPRETING)" placement="top">
              <el-card>
                <el-row>
                  <el-col :span="24">
                    <div style="margin: 5px">
                      <div style="margin-bottom: 5px">所在中介机构：{{ item.lawFirm }}</div>
                      <div style="margin-bottom: 5px">入库时间：{{ item.beginDate | parseTime }}</div>
                      <div style="margin-bottom: 5px">出库时间：{{ item.endDate | parseTime }}</div>
                    </div>
                  </el-col>
                </el-row>
              </el-card>
            </el-timeline-item>
          </el-timeline>
        </el-scrollbar>
      </div>
    </el-drawer>

  </el-container>
</template>

<script>
import lawyerApi from '@/api/LawyerManage/LawyerFirm/lawyer'
import lawFirmApi from '@/api/LawyerManage/LawyerFirm/lawyerFirm'
import pagination from "@/components/Pagination"
// // vuex状态值
import {mapGetters} from "vuex";
import taskApi from "@/api/_system/task";

export default {
  name: 'FirmLedgerIndex',
  inject: ['layout'],
  computed: {
    ...mapGetters(["orgContext", "currentFunctionId"])
  },
  components: {pagination},
  filters: {
    whetherHostLawyerFilter(val) {
      return val ? '是' : '否'
    }
  },
  data() {
    return {
      typeCode: null,
      tabLabel: "lawFirm1",
      typeName: null,
      activeName: 'lawFirm0',
      table_height: '100%', // 定义表格高度
      tableData_Lawyer: null, // 律师数据源
      tableData_LawFirm: null, // 律所数据源
      temp: {
        isQuery: false,
        lawyerFirm: null, // 律所名称
        functionary: null, // 负责人
        licenseCode: null, // 统一社会信用代码
        dataState: null, // 入库状态
        lawFirmType: null, // 律所类型
        lawyerName: null, // 律师名称
        createTimeMin: null, // 开始
        createTimeMax: null, // 结束
        whetherHostLawyer: null, // 是否主办律师
        fuzzyValue: null, // 模糊字段
        sortName: null, // 排序
        order: false,
        currentOgnId: null,
        typeCode: null,
        page: 1,
        limit: 10,
        total: 0,
      },
      ss: {
        data: this.tableData_LawFirm,
        tableColumns: [
          {key: 'lawyerFirm', label: '律所名称', visible: true},
          {key: 'licenseCode', label: '统一社会信用代码', visible: true},
          {key: 'functionary', label: '负责人', visible: true},
          {key: 'registerAddress', label: '地址', visible: true},
          {key: 'createPsnName', label: '经办人', visible: true},
          {key: 'createOgnName', label: '经办单位', visible: true},
          {key: 'createTime', label: '经办时间', visible: true, isTime: true},
          {key: 'dataState', label: '状态', visible: true},
          {key: 'lawFirmType', label: '律所类型', visible: true},
          {key: 'lawFirmLevel', label: '律所规模', visible: true},
        ]
      },
      ss2: {
        data: this.tableData_Lawyer,
        tableColumns: [
          {key: 'lawyerName', label: '律师名称', visible: true},
          {key: 'whetherHostLawyer', label: '主责律师', visible: true, isBoolean: true},
          {key: 'lawFirm', label: '所属律所', visible: true},
          {key: 'beGoodAtDomain', label: '擅长领域', visible: true},
          // {key: 'typeName', label: '所属库', visible: true},
          {key: 'createTime', label: '经办时间', visible: true, isTime: true},
          {key: 'dataState', label: '状态', visible: true}
        ]
      },
      drawerDialog: false,
      drawerWidth: '35%',
      drawerDialog_: false,
      drawerTitle: '律所时间轴',
      tableRow: null,
      timeAxis: [],
      lawFirmTypeData: [{id: '0', name: '正式律所'}, {id: '1', name: '临时律所'}],
    }
  },
  watch: {
    activeName(newVal) {
      this.temp = {
        isQuery: false,
        lawyerFirm: null, // 律所名称
        functionary: null, // 负责人
        licenseCode: null, // 统一社会信用代码
        dataState: null, // 入库状态
        lawFirmType: null, // 律所类型
        lawyerName: null, // 律师名称
        createTimeMin: null, // 开始
        createTimeMax: null, // 结束
        whetherHostLawyer: null, // 是否主办律师
        fuzzyValue: null, // 模糊字段
        sortName: null, // 排序
        order: false,
        orgId: null,
        currentOgnId: null,
        typeCode: null,
        page: 1,
        limit: 10,
        total: 0,
      }
    }
  },
  activated() {
    // 长连接页面第二次激活的时候,不会走created方法,会走此方法
    this.refreshData()
  },
  created() {
    this.isHeadquarter()
    this.refreshData()
  },
  mounted() {
    this.$nextTick(function () {
      this.table_height = window.innerHeight - this.$refs.table.$el.offsetTop - 84 - 175;

      // 监听窗口大小变化
      const self = this;
      window.onresize = function () {
        self.table_height = window.innerHeight - self.$refs.table.$el.offsetTop - 84 - 175;
      };
    });
  },
  methods: {
    refreshData() {
      this.temp.currentOgnId = this.orgContext.currentOgnId
      if (this.activeName === 'lawFirm0') {
        this.drawerTitle = '律所时间轴'
        this.temp.typeCode = '0'
        this.refreshData_PrivateLawFirm()
      } else if (this.activeName === 'lawFirm1') {
        this.drawerTitle = '律所时间轴'
        /**
         *         typeCode此时用来判断是否集团查看
         *         集团  1    下级单位 0
         */
        this.temp.typeCode = this.typeCode
        this.refreshData_PublicLawFirm()
      } else if (this.activeName === 'lawFirm2') {
        this.drawerTitle = '律所时间轴'
        /**
         *         typeCode此时用来判断是否集团查看
         *         集团  1    下级单位 0
         */
        this.temp.typeCode = this.typeCode
        this.refreshData_SubLawFirm()
      } else {
        this.drawerTitle = '律师时间轴'
        this.refreshData_Lawyer()
      }
    },
    refreshData_PrivateLawFirm() { //  查询方法
      // 私库查询用全路径的第三级查
      const me = this
      let arr = this.orgContext.currentPsnFullId.split('/')
      this.temp.currentOgnId = arr[3]
      lawFirmApi.queryPrivate(this.temp).then((res) => {
        me.tableData_LawFirm = res.data.page.records
        me.ss.data = this.tableData_LawFirm
        me.temp.total = res.data.page.total
      })
    },
    refreshData_PublicLawFirm() { //  查询方法
      const me = this
      let arr = this.orgContext.currentPsnFullId.split('/')
      this.temp.currentOgnId = arr[2]

      lawFirmApi.queryPublic(this.temp).then((res) => {
        me.tableData_LawFirm = res.data.page.records
        me.ss.data = this.tableData_LawFirm
        me.temp.total = res.data.page.total
      })
    },
    refreshData_SubLawFirm() { //  查询方法
      const me = this
      lawFirmApi.querySub(this.temp).then((res) => {
        me.tableData_LawFirm = res.data.page.records
        me.ss.data = this.tableData_LawFirm
        me.temp.total = res.data.page.total
      })
    },
    refreshData_Lawyer() { //  查询方法
      const me = this
      this.temp.typeCode = this.typeCode
      lawyerApi.queryLedger(this.temp).then((res) => {
        me.tableData_Lawyer = res.data.page.records
        me.ss2.data = this.tableData_Lawyer
        me.temp.total = res.data.page.total
      })
    },
    search_() { // 查询
      this.refreshData()
    },
    empty_() {
      this.temp = {
        isQuery: false,
        lawyerFirm: null, // 律所名称
        functionary: null, // 负责人
        licenseCode: null, // 统一社会信用代码
        dataState: null, // 入库状态
        lawFirmType: null, // 律所类型
        lawyerName: null, // 律师名称
        createTimeMin: null, // 开始
        createTimeMax: null, // 结束
        whetherHostLawyer: null, // 是否主办律师
        fuzzyValue: null, // 模糊字段
        sortName: null, // 排序
        order: false,
        currentOgnId: null,
        typeCode: null,
        page: 1,
        limit: 10,
        total: 0,
      }
      this.refreshData()
    },
    handleClick(tab, event) {
      this.tabLabel = tab;
      this.empty_()
      this.refreshData()
    },
    changeColor(val) {
      if (val === '入库') {
        return '#0bbd87'
      } else if (val === '变更') {
        return '#6633FF'
      } else if (val === '出库') {
        return '#FF0000'
      } else if (val === '黑名单') {
        return '#003300'
      } else {
        return '#C0C0C0'
      }
    },
    lawFirmAxis() {
      if (this.tabLabel === 'lawFirm1') {
        const row = this.tableRow
        const me = this
        lawFirmApi.queryLawFirmAxis({id: row.id}).then(res => {
          me.timeAxis = res.data.data
          me.drawerDialog = true
        })
      } else {
        const row = this.tableRow
        lawyerApi.queryLawyerAxis({id: row.id}).then(res => {
          this.timeAxis = res.data.data
          this.drawerDialog_ = true
        })
      }

    },
    lawyerAxis() {

    },
    tableSort_PrivateLawFirm(column, prop, order) {
      this.temp.sortName = column.prop
      this.temp.order = column.order === 'ascending'
      this.refreshData_PrivateLawFirm()
    },
    tableSort_PublicLawFirm(column, prop, order) {
      this.temp.sortName = column.prop
      this.temp.order = column.order === 'ascending'
      this.refreshData_PublicLawFirm()
    },
    tableSort_SubLawFirm(column, prop, order) {
      this.temp.sortName = column.prop
      this.temp.order = column.order === 'ascending'
      this.refreshData_SubLawFirm()
    },
    tableSort_Lawyer(column, prop, order) {
      this.temp.sortName = column.prop
      this.temp.order = column.order === 'ascending'
      this.refreshData_Lawyer()
    },
    rowDblclick_LawFirm(row, column, event) {
      this.layout.openNewTab(
          "律所信息",
          "firm_ledger_law_firm",
          "firm_ledger_law_firm",
          this.utils.createUUID(),
          {
            source: 'ledger', ...this.utils.routeState.VIEW(row.id)
          })
    },
    rowDblclick_Lawyer(row, column, event) {
      this.layout.openNewTab("律师信息", "firm_ledger_lawyer", "firm_ledger_lawyer", this.utils.createUUID(), {
        ...this.utils.routeState.VIEW(row.id)
      })
    },
    rowClick_LawFirm(row, column, event) {
      this.tableRow = row
    },
    rowClick_Lawyer(row, column, event) {
      this.tableRow = row
    },
    timeAxis_(item) {
      let functionCode

      switch (item.OPERATING) {
        case '入库':
          functionCode = 'firm_main_new'
          break
        case '推荐入库':
          functionCode = 'firm_main_change'
          break
        case '变更':
          functionCode = 'firm_main_change'
          break
        case '出库':
          functionCode = 'firm_out_main'
          break
        case '黑名单':
          functionCode = 'firm_black_main'
          break
      }

      taskApi.selectTaskId({businessKey: item.DATAID, isView: 'true'}).then(res => {
        const functionId = res.data.data[0].ID
        const tabId = this.utils.createUUID()
        this.layout.openNewTab(item.OPERATING + "信息",
            "design_page",
            "design_page",
            tabId,
            {
              processInstanceId: res.data.data[0].PID,//流程实例
              taskId: res.data.data[0].ID,//任务ID
              businessKey: item.DATAID, //业务数据ID
              functionId: functionId, //不传也可以，只有首环节需要传，实例启动就可以不传了
              entranceType: "FLOWABLE",
              type: "haveDealt",
              channel: 'business' //查看渠道，默认就传这个值即可，表示是从业务功能传过来的，首环节提交后过滤掉撤回按钮
            }
        )
      })

    },
    isHeadquarter() {
      const createOgnId = this.orgContext.currentOgnId

      // if (createOgnId === '15033708970596')
      // {
      this.typeCode = '1'
      this.typeName = '推荐库'
      this.activeName = 'lawFirm1'
      // }
      // else
      // {
      //   this.typeCode = '0'
      //   this.typeName = '资源库'
      //   this.activeName = 'lawFirm0'
      // }
    },
    topLawFirm(val) {
      const firms = ['31110000E00017891P', '31110000E000169525', '31310000425097733Y', '31110000E00016813E',
        '31110000E00016266T', '31110000400834282L', '31110000E00017525U', '31110000E00018675X']

      return firms.indexOf(val) > -1
    },
  }
}
</script>
<style scoped>
.label_ {
  margin-top: 10px;
  text-align: right;
  padding-right: 6px;
}

.el-dialog-div {
  height: 60vh;
  overflow: auto;
}

.el-tabs__header {
  width: calc(100% - 65px);
}
</style>
