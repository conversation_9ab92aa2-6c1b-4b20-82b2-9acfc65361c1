package com.klaw.service.imp.complianceRiskServiceImp;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.klaw.constant.NoticeType;
import com.klaw.dao.complianceRiskDao.RiskWarningMapper;
import com.klaw.dao.systemDao.SgSysUserMapper;
import com.klaw.entity.complianceRiskBean.RiskWarning;
import com.klaw.entity.complianceRiskBean.RiskWarningDataBase;
import com.klaw.entity.complianceRiskBean.RiskWarningReceiveInfo;
import com.klaw.entity.config.RiskMiitigationStatus;
import com.klaw.entity.config.RiskWarningQueryStatus;
import com.klaw.entity.systemBean.HrUserUnitm;
import com.klaw.entity.systemBean.SgHrOrgUnitB;
import com.klaw.entity.systemBean.SysUsers;
import com.klaw.service.complianceRiskService.RiskWarningDataBaseService;
import com.klaw.service.complianceRiskService.RiskWarningReciveInfoService;
import com.klaw.service.complianceRiskService.RiskWarningService;
import com.klaw.service.systemService.HrUserUnitmService;
import com.klaw.service.systemService.SgHrOrgUnitBService;
import com.klaw.service.systemService.SgSysNoticeService;
import com.klaw.service.systemService.SysUsersService;
import com.klaw.utils.DataAuthUtils;
import com.klaw.utils.PageUtils;
import com.sgai.mcp.core.impl.LoginHelper;
import liquibase.pro.packaged.S;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.Serializable;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class RiskWarningServiceImpl extends ServiceImpl<RiskWarningMapper, RiskWarning> implements RiskWarningService {

    @Resource
    private SgHrOrgUnitBService hrOrgUnitBService;

    @Resource
    private HrUserUnitmService hrUserUnitmService;

    @Resource
    private SgSysUserMapper sgSysUserMapper;

    @Resource
    private SysUsersService sysUsersService;

    @Resource
    private SgSysNoticeService sgSysNoticeService;

    @Autowired
    private RiskWarningReciveInfoService riskWarningReciveInfoService;

    @Autowired
    private RiskWarningDataBaseService riskWarningDataBaseService;

    @Override
    public boolean saveData(RiskWarning riskWarning) {
        riskWarning.setReportingOrganization(riskWarning.getCreatePsnFullName());
        riskWarning.setReportingPerson(riskWarning.getCreateOrgName());
        riskWarning.setRiskMitigationStatus(riskWarning.isSubmit() ? RiskMiitigationStatus.TO_BE_DISTRIBUTED : RiskMiitigationStatus.TEMPORARY_STOARGE);
        riskWarning.setReportingDate(new Date());
        List<String> receives = JSONArray.parseArray(riskWarning.getReceivingPersonId(), String.class);
        if (StringUtils.isNotBlank(riskWarning.getReceivingDepartmentCode()) && (receives == null || receives.size() <= 0)) {
            String dementCode = riskWarning.getReceivingDepartmentCode();
            SgHrOrgUnitB hrOrgUnitB = hrOrgUnitBService.getOne(new QueryWrapper<SgHrOrgUnitB>().eq("UNIT_ID", dementCode));
            if (hrOrgUnitB != null) {
                SgHrOrgUnitB hrOrgUnitBParent = hrOrgUnitBService.getOne(new QueryWrapper<SgHrOrgUnitB>().eq("UNIT_CODE", hrOrgUnitB.getParentId()));
                riskWarning.setReceivingDepartment(hrOrgUnitBParent.getName() + "/" + hrOrgUnitB.getName());
                if (StringUtils.isBlank(riskWarning.getReceivingPerson())) {
                    List<Map<String, Long>> leaderId = sgSysUserMapper.queryUserIds(dementCode, "HGFXJSR");
                    if (riskWarning.isSubmit()) {
                        if (leaderId.size() <= 0) {
                            throw new RuntimeException("接收单位未配置接收人!");
                        }
                    }
                    List<String> userIds = new ArrayList<>();
                    for (Map<String, Long> stringBigDecimalMap : leaderId) {
                        userIds.add(stringBigDecimalMap.get("userId").toString());
                    }
                    StringBuilder stringBuilderId = new StringBuilder();
                    StringBuilder stringBuilder = new StringBuilder();
                    for (int i = 0; i < userIds.size(); i++) {
                        SysUsers user = sysUsersService.getOne(new QueryWrapper<SysUsers>().eq("USER_ID", userIds.get(i)));
                        if (user == null) continue;
                        if (i == userIds.size() - 1) {
                            stringBuilderId.append(user.getUserName());
                            stringBuilder.append(user.getFullName());
                        } else {
                            stringBuilderId.append(user.getUserName()).append(",");
                            stringBuilder.append(user.getFullName()).append(",");
                        }
                    }
                    riskWarning.setDistributionPerson(stringBuilder.toString());
                    riskWarning.setDistributionPersonIds(stringBuilderId.toString());
                }
            }
        } else {
            if (receives == null) {
                receives = new ArrayList<>();
            }
            if (receives.size() >= 0) {
                List<String> names = new ArrayList<>();
                for (String receive : receives) {
                    SysUsers sysUsers = sysUsersService.getById(receive);
                    if (sysUsers != null) {
                        names.add(sysUsers.getFullName());
                    }
                }
                riskWarning.setReceivingPerson(names.toString());
            }
        }

        RiskWarningDataBase riskWarningDataBase = null;

        if (riskWarning.isSubmit()) {
            if (!Objects.equals(riskWarning.getWarningType(), "其他风险")) {
                if (receives.size() <= 0) {
                    JSONObject noticeJson = new JSONObject();
                    noticeJson.put("dataState", "view");
                    noticeJson.put("dataId", riskWarning.getId());
                    noticeJson.put("functionId", "Risk_Warning_Distribution," + riskWarning.getId());
                    noticeJson.put("isNotice", true);
                    List<String> receiveOrgIds = new ArrayList<>();
                    String distributionPersonIds = riskWarning.getDistributionPersonIds();
                    String[] disIds = distributionPersonIds.split(",");
                    for (String disId : disIds) {
                        List<HrUserUnitm> hrUserUnitms = hrUserUnitmService.list(new QueryWrapper<HrUserUnitm>().eq("USER_ID", disId));
                        for (HrUserUnitm hrUserUnitm : hrUserUnitms) {
                            List<SgHrOrgUnitB> list = hrOrgUnitBService.list(new QueryWrapper<SgHrOrgUnitB>().eq("UNIT_ID", hrUserUnitm.getUnitId()).eq("enabled_flag", "Y"));
                            List<String> collect = list.stream().map(i -> String.valueOf(i.getUnitId())).collect(Collectors.toList());
                            receiveOrgIds.addAll(collect);
                        }

                    }
                    riskWarning.setRiskMitigationStatus(RiskMiitigationStatus.TO_BE_DISTRIBUTED);
                    sgSysNoticeService.createNotices("风险预警：请及时分配风险应对人员！", "合规风险预警", NoticeType.HGFXYJ_NOTICE, riskWarning.getId(), "Risk_Warning_Distribution", noticeJson.toJSONString(), Long.parseLong(riskWarning.getCreateOrgId()), receiveOrgIds, false);
                }
                else {
                    JSONObject noticeJson = new JSONObject();
                    noticeJson.put("dataState", "view");
                    noticeJson.put("dataId", riskWarning.getId());
                    noticeJson.put("functionId", "Risk_Warning_Receive," + riskWarning.getId());
                    noticeJson.put("isNotice", true);
                    List<String> receiveOrgIds = new ArrayList<>();
                    List<String> javaList = JSONArray.parseArray(riskWarning.getReceivingPersonId()).toJavaList(String.class);
                    for (String java : javaList) {
                        List<HrUserUnitm> hrUserUnitms = hrUserUnitmService.list(new QueryWrapper<HrUserUnitm>().eq("USER_ID", java));
                        for (HrUserUnitm hrUserUnitm : hrUserUnitms) {
                            List<SgHrOrgUnitB> list = hrOrgUnitBService.list(new QueryWrapper<SgHrOrgUnitB>().eq("UNIT_ID", hrUserUnitm.getUnitId()).eq("enabled_flag", "Y"));
                            List<String> collect = list.stream().map(i -> String.valueOf(i.getUnitId())).collect(Collectors.toList());
                            receiveOrgIds.addAll(collect);
                        }
                    }
                    riskWarning.setRiskMitigationStatus(RiskMiitigationStatus.MENTIONED);
                    sgSysNoticeService.createNotices("风险预警：请及时应对潜在风险！", "合规风险预警", NoticeType.HGFXYJ_NOTICE, riskWarning.getId(), "Risk_Warning_Receive", noticeJson.toJSONString(), Long.parseLong(riskWarning.getCreateOrgId()), receiveOrgIds, false);
                }

                //创建各成员查看和待办状态
                if (RiskMiitigationStatus.MENTIONED == riskWarning.getRiskMitigationStatus() && StringUtils.isNotBlank(riskWarning.getReceivingPersonId())) {
                    String substring = riskWarning.getReceivingPerson().substring(1, riskWarning.getReceivingPerson().length() - 1);
                    List<String> names = Arrays.asList(substring.split(","));
                    String receivingPersonId = riskWarning.getReceivingPersonId();

                    List<String> ids = JSONArray.parseArray(receivingPersonId, String.class);

                    for (int i = 0; i < ids.size(); i++) {
                        RiskWarningReceiveInfo one = riskWarningReciveInfoService.getOne(new QueryWrapper<RiskWarningReceiveInfo>().eq("risk_id", riskWarning.getId()).eq("receiving_person_id", ids.get(i)));
                        if (one == null) {
                            RiskWarningReceiveInfo riskWarningReceiveInfo = new RiskWarningReceiveInfo();
                            riskWarningReceiveInfo.setId(UUID.randomUUID().toString());
                            riskWarningReceiveInfo.setRiskId(riskWarning.getId());
                            riskWarningReceiveInfo.setReceivingPerson(names.get(i));
                            riskWarningReceiveInfo.setReceivingPersonId(ids.get(i));
                            riskWarningReceiveInfo.setViewType(false);
                            riskWarningReceiveInfo.setHandleType(false);
                            riskWarningReceiveInfo.setReceivingDepartment(riskWarning.getReceivingDepartment());
                            riskWarningReceiveInfo.setReceivingDepartmentCode(riskWarning.getReceivingDepartmentCode());
                            riskWarningReciveInfoService.save(riskWarningReceiveInfo);
                        }
                    }
                }
                riskWarningDataBase = new RiskWarningDataBase();
                BeanUtils.copyProperties(riskWarning, riskWarningDataBase);
                riskWarningDataBaseService.saveOrUpdate(riskWarningDataBase);
            }
            if ("其他风险".equals(riskWarning.getWarningType())){
                riskWarning.setRiskMitigationStatus(RiskMiitigationStatus.MENTIONED);
            }
        }
        if ("其他风险".equals(riskWarning.getWarningType())) {

            if (riskWarningDataBase == null) {
                riskWarningDataBase = new RiskWarningDataBase();
                BeanUtils.copyProperties(riskWarning, riskWarningDataBase);
                riskWarningDataBaseService.saveOrUpdate(riskWarningDataBase);
            }
            return true;
        } else {
            return saveOrUpdate(riskWarning);
        }

    }

    @Override
    public QueryWrapper query(JSONObject jsonObject) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        QueryWrapper<RiskWarning> warpper = new QueryWrapper<>();
        if (innerMap.containsKey("receiveUser") && innerMap.get("receiveUser") != null) {
            warpper.eq("receiving_person", innerMap.get("receiveUser"));
        }
        if (innerMap.containsKey("orgDepartmentCode") && innerMap.get("orgDepartmentCode") != null) {
            warpper.eq("receiving_department", innerMap.get("orgDepartmentCode").toString());
        }
        if (innerMap.containsKey("predictRiskLevel") && innerMap.get("predictRiskLevel") != null) {
            warpper.eq("expected_risk_level", innerMap.get("predictRiskLevel"));
        }
        if (innerMap.containsKey("businessDomain") && innerMap.get("businessDomain") != null) {
            warpper.eq("business_domain", innerMap.get("businessDomain"));
        }
        if (innerMap.containsKey("dataStateCode") && innerMap.get("dataStateCode") != null) {
            warpper.eq("risk_mitigation_status", innerMap.get("dataStateCode"));
        }
        if (innerMap.containsKey("selectByMy") && innerMap.get("selectByMy") != null) {
            String selectByMy = (String) innerMap.get("selectByMy");
            if (RiskWarningQueryStatus.MY_INITIATE.getValue().equals(selectByMy)) {
                warpper.eq("create_psn_id", LoginHelper.getUserId());
            } else if (RiskWarningQueryStatus.MY_RECEIVE.getValue().equals(selectByMy)) {
                warpper.like("receiving_person_id", LoginHelper.getUserId());
            } else if (RiskWarningQueryStatus.MY_DISTRIBUTION.getValue().equals(selectByMy)) {
                warpper.like("distribution_person_id", LoginHelper.getUserName()).and(i -> i.eq("risk_mitigation_status", RiskMiitigationStatus.TO_BE_DISTRIBUTED)
                        .or().eq("risk_mitigation_status", RiskMiitigationStatus.MENTIONED));
            }
        }
        if (innerMap.containsKey("riskName") && innerMap.get("riskName") != null) {
            warpper.eq("risk_name", innerMap.get("riskName"));
        }
        if (innerMap.containsKey("fuzzyValue") && innerMap.get("fuzzyValue") != null) {
            String fuzzyValue = (String) innerMap.get("fuzzyValue");
            warpper.and(o -> {
                o.like("risk_name", fuzzyValue).or().like("business_domain", fuzzyValue)
                        .or().like("receiving_person", fuzzyValue).or().like("receiving_department", fuzzyValue)
                        .or().like("expected_risk_level", fuzzyValue).or().like("expected_risk_level", fuzzyValue);
            });
        }


        return warpper;
    }

    @Override
    public QueryWrapper query2(JSONObject jsonObject) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        QueryWrapper<RiskWarning> warpper = new QueryWrapper<>();
        if (innerMap.containsKey("receiveUser") && innerMap.get("receiveUser") != null) {
            warpper.eq("receiving_person", innerMap.get("receiveUser"));
        }
        if (innerMap.containsKey("orgDepartmentCode") && innerMap.get("orgDepartmentCode") != null) {
            warpper.eq("receiving_department", innerMap.get("orgDepartmentCode").toString());
        }
        if (innerMap.containsKey("predictRiskLevel") && innerMap.get("predictRiskLevel") != null) {
            warpper.eq("expected_risk_level", innerMap.get("predictRiskLevel"));
        }
        if (innerMap.containsKey("businessDomain") && innerMap.get("businessDomain") != null) {
            warpper.eq("business_domain", innerMap.get("businessDomain"));
        }
        if (innerMap.containsKey("dataStateCode") && innerMap.get("dataStateCode") != null) {
            warpper.eq("risk_mitigation_status", innerMap.get("dataStateCode"));
        }
        if (innerMap.containsKey("selectByMy") && innerMap.get("selectByMy") != null) {
            String selectByMy = (String) innerMap.get("selectByMy");
            if (RiskWarningQueryStatus.MY_INITIATE.getValue().equals(selectByMy)) {
                warpper.eq("create_psn_id", LoginHelper.getUserId());
            } else if (RiskWarningQueryStatus.MY_RECEIVE.getValue().equals(selectByMy)) {
                warpper.like("receiving_person_id", LoginHelper.getUserId());
            } else if (RiskWarningQueryStatus.MY_DISTRIBUTION.getValue().equals(selectByMy)) {
                warpper.like("distribution_person_id", LoginHelper.getUserName()).and(i -> i.eq("risk_mitigation_status", RiskMiitigationStatus.TO_BE_DISTRIBUTED)
                        .or().eq("risk_mitigation_status", RiskMiitigationStatus.MENTIONED));
            }
        }
        if (innerMap.containsKey("riskName") && innerMap.get("riskName") != null) {
            warpper.eq("risk_name", innerMap.get("riskName"));
        }
        if (innerMap.containsKey("fuzzyValue") && innerMap.get("fuzzyValue") != null) {
            String fuzzyValue = (String) innerMap.get("fuzzyValue");
            warpper.and(o -> {
                o.like("risk_name", fuzzyValue);
            });
        }


        return warpper;
    }

    @Override
    public Page page(QueryWrapper queryWrapper, JSONObject jsonObject) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        int page = (int) innerMap.get("page");
        int pageSize = (int) innerMap.get("limit");
        Page page1 = new Page(page, pageSize);
        Page result = this.page(page1, queryWrapper);
        return result;
    }


    @Override
    public RiskWarning getById(Serializable id) {

        return this.queryDataById((String) id);
    }

    @Override
    public Page<RiskWarning> queryPageData(JSONObject json) {
        QueryWrapper<RiskWarning> wrapper = new QueryWrapper<>();
        getFilter(json, wrapper);
        return page(new PageUtils<>(json), wrapper);
    }

    private void getFilter(JSONObject json, QueryWrapper<RiskWarning> wrapper) {
    }

    @Override
    public RiskWarning queryDataById(String id) {
        return baseMapper.selectById(id);
    }

    @Override
    public Object queryBaseById(String id) {
        RiskWarningDataBase warningDataBase = riskWarningDataBaseService.getById(id);
        if(warningDataBase!=null){
            return warningDataBase;
        }
        return baseMapper.selectById(id);
    }

    @Override
    public int deleteDataById(String id) {
        return baseMapper.deleteById(id);
    }

    @Override
    public Page<RiskWarningReceiveInfo> queryViewList(JSONObject jsonObject) {
        String id = jsonObject.getString("id");
        Integer page = jsonObject.getInteger("page");
        Integer limit = jsonObject.getInteger("limit");
        Page<RiskWarningReceiveInfo> pageObj = new Page<>();
        pageObj.setCurrent(page - 1);
        pageObj.setSize(limit);
        QueryWrapper<RiskWarningReceiveInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("risk_id", id);
        Page<RiskWarningReceiveInfo> result = riskWarningReciveInfoService.page(pageObj, queryWrapper);
        return result;
    }

    @Override
    public Page<RiskWarningReceiveInfo> queryHandleList(JSONObject jsonObject) {
        String id = jsonObject.getString("id");
        Integer page = jsonObject.getInteger("page");
        Integer limit = jsonObject.getInteger("limit");
        Page<RiskWarningReceiveInfo> pageObj = new Page<>();
        pageObj.setCurrent(page - 1);
        pageObj.setSize(limit);
        QueryWrapper<RiskWarningReceiveInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("risk_id", id);
        Page<RiskWarningReceiveInfo> result = riskWarningReciveInfoService.page(pageObj, queryWrapper);
        return result;
    }

    @Override
    public QueryWrapper<RiskWarningDataBase> queryDataBase(JSONObject request) {
        //是否台账功能（登记只查自己的，台账和弹框查自己和数据权限的）
        Map<String, Object> innerMap = request.getInnerMap();
        boolean isQuery = request.containsKey("isQuery") ? request.getBoolean("isQuery") : false;
        boolean isQuery1 = request.containsKey("isQuery1") ? request.getBoolean("isQuery1") : false;
        String orgId = request.containsKey("orgId") ? request.getString("orgId") : null;
        QueryWrapper<RiskWarningDataBase> queryWrapper = new QueryWrapper<>();
        if (innerMap.containsKey("receiveUser") && innerMap.get("receiveUser") != null) {
            queryWrapper.eq("receiving_person", innerMap.get("receiveUser"));
        }
        if (innerMap.containsKey("orgDepartmentCode") && innerMap.get("orgDepartmentCode") != null) {
            queryWrapper.eq("receiving_department", innerMap.get("orgDepartmentCode").toString());
        }
        if (innerMap.containsKey("predictRiskLevel") && innerMap.get("predictRiskLevel") != null) {
            queryWrapper.eq("expected_risk_level", innerMap.get("predictRiskLevel"));
        }
        if (innerMap.containsKey("businessDomain") && innerMap.get("businessDomain") != null) {
            queryWrapper.eq("business_domain", innerMap.get("businessDomain"));
        }
        if (innerMap.containsKey("dataStateCode") && innerMap.get("dataStateCode") != null) {
            queryWrapper.eq("risk_mitigation_status", innerMap.get("dataStateCode"));
        }
        if (innerMap.containsKey("riskName") && innerMap.get("riskName") != null) {
            queryWrapper.eq("risk_name", innerMap.get("riskName"));
        }
        if (innerMap.containsKey("fuzzyValue") && innerMap.get("fuzzyValue") != null) {
            String fuzzyValue = (String) innerMap.get("fuzzyValue");
            queryWrapper.and(o -> {
                o.like("risk_name", fuzzyValue).or().like("business_domain", fuzzyValue)
                        .or().like("receiving_person", fuzzyValue).or().like("receiving_department", fuzzyValue)
                        .or().like("expected_risk_level", fuzzyValue).or().like("expected_risk_level", fuzzyValue);
            });
        }
        //台账查询权限
        // 优先判断 isQuery1（如果业务允许）
        if (isQuery) {
            Long functionId = DataAuthUtils.getFunctionIdByCode("Risk_Warning_Standing_Book");
            DataAuthUtils.dataPermSql(queryWrapper, null, functionId, orgId);
        } else if (isQuery1) {
            Long functionId = DataAuthUtils.getFunctionIdByCode("riskdata_ledger_index");
            DataAuthUtils.dataPermSql(queryWrapper, null, functionId, orgId);
        } else {
            // 默认条件
            queryWrapper.eq("create_Org_Id", orgId);
        }

        return queryWrapper;
    }

}
