
bffdd1a83ede79bfbd11fd1ce95495f530daf6e5	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.108.1754018536329.js\",\"contentHash\":\"ba4c7b512cf1e366ab6db21108305560\"}","integrity":"sha512-rG8y4Q7sBCi2O7120jEwwd0xp+MBy0q87oFiA67MBgiV+FMSLbX2c1NEXwH/dFb6Ro+Ym3m2Xu/Bji2ZYyFpTg==","time":1754018575959,"size":115815}