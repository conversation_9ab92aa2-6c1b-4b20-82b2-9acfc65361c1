import {request} from '@/api/index';
export default{
    saveOrUpdate(data){
        return request({
            url:'/projectDemonstration/saveOrUpdate',
            method:"post",
            data
        })
    },
    queryById(data){
        return request({
            url:'/projectDemonstration/queryById/' + data,
            method:'get' 
        })
    },

    query(data){
        return request({
            url:'/projectDemonstration/query',
            method:'post',
            data
        })
    },
    delete(data){
        return request({
            url:'/projectDemonstration/delete/'+data,
            method:'get'
        })
    }
}