
16f7838d3eff238d5cb6a28b0d53768831bfc73b	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.377.1754018536329.js\",\"contentHash\":\"396b773717d69019720f28b43deb5b9f\"}","integrity":"sha512-4C2hwHufSRMkK2kZYrg7o9SKQ6MmfmBZj8Ye+PMYTsItY8yKGGBx61UzZn7I+UnqfsZFZE/POWCR7k03dExxhw==","time":1754018576023,"size":157833}