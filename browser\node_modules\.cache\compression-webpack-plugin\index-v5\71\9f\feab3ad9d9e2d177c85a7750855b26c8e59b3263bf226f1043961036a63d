
631821fa99ae68456d30f306ffb3c1314efd56ef	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.445.1754018536329.js\",\"contentHash\":\"b325f00e98cca0ed7bfab0bdc8f85369\"}","integrity":"sha512-76V0zgC6MNeKeA+7bOHmHHn5UhV8E6bdal6aCO5+YU7pULgPfbGLOmWHlQ9jI7zHG9ovdflyIl+GBRZLNso4yQ==","time":1754018576041,"size":161724}