
activiti.mailServerHost=smtp.126.com
activiti.mailServerPort=465
activiti.mailServerUsername=<EMAIL>
activiti.mailServerPassword=mcp_dev126
#126\u90AE\u7BB1\u7684\u5BA2\u6237\u7AEF\u6388\u6743\u5BC6\u7801
activiti.mailServerAuthorizationPassword=mcpdev126

server.port=8080
server.servlet.context-path=/mcp

spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.url=*******************************************************************************************************************
spring.datasource.username=root
spring.datasource.password=112Fw@2024


spring.redis.database=2
spring.redis.host=localhost
spring.redis.port=6379
spring.redis.password=bgfw@2025

spring.redis.lettuce.pool.max-active=8
spring.redis.lettuce.pool.max-wait=-1
spring.redis.lettuce.pool.max-idle=8
spring.redis.lettuce.pool.min-idle=0

license.licensePath=/home/<USER>/license/fwtest_btsteel_com.lic

swagger.url.patterns=/**

oa.sysCode=jtgk_fw
oa.path=http://**********:8080
oa.url=http://**********:8080/rest/ofs/ReceiveRequestInfoByJson
oa.client.client-id=1119
oa.client.client-secret=7364b2ce0e2b43488ae9f950e3c05410
oa.client.user-authorization-uri=https://amdev.shougang.com.cn/idp/oauth2/authorize
oa.client.access-token-uri=https://amdev.shougang.com.cn/idp/oauth2/getToken
oa.resource.user-info-uri=http://amdev.shougang.com.cn/idp/oauth2/getUserInfo
oa.delete.path=http://tcmp.btsteel.com/rest/ofs/deleteUserRequestInfoByJson

oa.resource.current-uri=http://fwtest.btsteel.com/ssoLogin
oa.resource.transfer-uri=http://fwtest.btsteel.com/transfer
oa.resource.uni-uri=http://fwtest.btsteel.com/fwh5/#/
oa.resource.contractDialogIndex-uri=http://fwtest.btsteel.com/contractDialogIndex
oa.out.glo-uri=http://fwtest.btsteel.com/logout

mybatis-plus.global-config.enable-sql-runner=true

oa.out.glo=https://amdev.shougang.com.cn/idp/profile/OAUTH2/Redirect/GLO

#oa.task.CXF_URL=http://10.1.249.199:50000/XISOAPAdapter/MessageServlet?senderParty=&senderService=BC_WHIR_JTGK&receiverParty=&receiverService=&interface=SI_uwl_field_out&interfaceNamespace=http%3A%2F%2Fshougang.com.cn%2FOA%2Fwhir%2FCommon
oa.task.username=pi_ser
oa.task.password=init1234
#oa.task.key=DFFD512F3C274EC11AF53753FC82B483
#oa.task.cmd=unifiedDealFile
#oa.task.systemkey=020

oa.task.CXF_URL=https://moat.sgai.com.cn:1443/sgai/api/process/
oa.task.key=DFFD512F3C274EC11AF53753FC82B483
oa.task.cmd=unifiedDealFile
oa.task.systemkey=020
oa.task.application=fb26fdf5-19d9-4baf-9ed8-15759efbd74f
oa.task.authorization=Basic YTNkMzJjZmItY2MyMC00NmQ1LWFkMDgtMmMzYmUzN2ZjNzM5OjVjODM0Y2ZkLTg3NjgtNGFjZS1hNWE5LWRjMGFhN2Q5ZmUzYQ==
oa.task.sourceId=75915689-a8a9-412e-9044-3033f2588220



minio.accesskey=minioadmin
minio.secrekey=minioadmin
minio.endpoint=http://10.168.108.22:23000
minio.bucket=bucket1

#\u6CA1\u7528\u5230\u4E0B\u65B9\u7684\u5730\u5740
process.file.url=http://10.168.108.22:80/mcp/sys_doc/download/

edoc.url=https://moat.sgai.com.cn:8099
edoc.username=fwadmin
edoc.password=edoc2
edoc.clienttype=4
edoc.token=003450e624f24d5d44c4a9c4049a680983b1
edoc.parentFolderId=45
edoc.parentArchiveId=356

standardtext.url=http://*************:8768
standardtext.urlAdLogin=http://*************:8769

knl.url=http://10.168.108.22:9094

#\u8C03\u7528\u6CD5\u52A1\u7CFB\u7EDF\u540E\u53F0\u7684\u57DF\u540D\u5730\u5740
WEB_URL=http://10.168.108.22:8080
#\u4E00\u8D77\u5199\uFF08\u6BD5\u5347\uFF09
EDITOR_URL=http://10.168.108.22:88

tyc.url=http://10.168.78.22:80/integrationServices/SharedDataInterface/PublicInerfaceTycPost

#ESB\u5730\u5740
asset.url=http://10.1.249.199:50000

#\u660E\u6E90\u5730\u5740
purchase.url=http://test-mingyuan.shougang.com.cn:8060/Cbgl/Interface/HTDL/ReceiveFwEffecState.ashx

#OA\u6280\u672F\u7814\u7A76\u9662
oa.jyy.url=http://oa-test.shougang.com.cn:7001/defaultroot/xfservices/GeneralWeb

#\u5305\u94A2\u901A\u5355\u70B9\u5730\u5740
sgt.url=http://moat.sgai.com.cn:8046
sgt.appid=UBbFGoahRf0rB4t0sa-cZc
sgt.secretKey=B7CaimKKZWt0j
sgt.accessTokenUrl=http://moat.sgai.com.cn:8046/interface/sgjt/i1/oauth/getaccesstoken?
sgt.userinfoUrl=http://moat.sgai.com.cn:8046/interface/sgjt/i1/oauth/userinfo?

#\uFFFD\uFFFD\u0434\u01E9\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\u05B7
fw.url=https://fw-test.shougang.com.cn
hs.clientId=dynamic_cru
hs.grantType=client_credentials
hs.clientSecret=YTFiNDBkZDEtOTUxZi00ZmQwLTg3NDQtNjY4N2Q5NWVkYTNk
#\uFFFD\uFFFD\uFFFD\uFFFD\u01E9\uFFFD\uFFFDixjob\uFFFD\uFFFD\u05B7
ixjob.dzht.url= http://10.68.24.64:8089/Soap?wsdl
#ixjob.dzht.url= https://fw-test.shougang.com.cn/Soap?wsdl
#ixjob.dzht.url= http://10.168.108.22/Soap?wsdl

#\uFFFD\u05F8\uFFFD\u0368\u05EAPDF
sgt.pdf=https://moat.sgai.com.cn:1443
sgt.secret=631EG8XRxswuRioRePerxTwJVhH9RalC

hwyun.obs.accessKey=B6TQQKBKPJPSLRPHDRBU
hwyun.obs.securityKey=96qvLRFQAfKmMYANBbmKTQRYiq1UlBvV6ENGplS3
#hwyun.obs.endPoint=obsv3.nm-bg-1.nmxlcloud.com
hwyun.obs.endPoint=http://10.202.19.229:8001
#hwyun.obs.endPoint=https://10.202.19.229:8002
hwyun.obs.bucketName=fwtest

wps.ak=ZXGWPOYDLNHQLUTV
wps.sk=SKkgcfxdpbawjdwd
wps.preview=http://************:8092/open/api/preview/v1/files/
wps.previewCallback=http://************:8092/open/api/preview/v1/files/
wps.edit=http://************:8092/open/api/edit/v1/files/
wps.convert=http://************:8092/open/api/cps/sync/v1/convert
wps.convertDown=http://************:8092/open/api/cps/v1/download
wps.contentOperate=http://************:8092/open/api/cps/sync/v1/content/operate
wps.type=test
sdk.fileDown=http://localhost:8088/sgaudit/sdkWpsFileDown

sftp.client.protocol=sftp
sftp.client.host=
sftp.client.port=
sftp.client.username=
sftp.client.password=
sftp.client.proxyUrl=
sftp.client.uploadUrl=doc/
sftp.client.privateKey=
sftp.client.passphrase=
sftp.client.sessionStrictHostKeyChecking=no
sftp.client.sessionConnectTimeout=15000
sftp.client.channelConnectedTimeout=15000
sftp.client.serviceUrl=

esb.url=http://************:8080


xxl.job.user-name=admin
xxl.job.password=123456
xxl.job.admin.addresses=http://*************:8088/xxl-job-admin
xxl.job.accessToken=eyJhbGciOiJIUzI1NiJ9
xxl.job.executor.appname=xxl-job-executor-sample
xxl.job.executor.address=
xxl.job.executor.ip=
xxl.job.executor.port=9999
xxl.job.executor.logpath=./logs/xxl-job/jobhandler
xxl.job.executor.logretentiondays=30