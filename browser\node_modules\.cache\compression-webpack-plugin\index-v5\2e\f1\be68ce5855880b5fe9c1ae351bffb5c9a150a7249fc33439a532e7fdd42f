
8fdf3dd02064485e480794d803d815f7158a7bbe	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.247.1754018536329.js\",\"contentHash\":\"e00d6a35ca3688cf007d8b56c36b4009\"}","integrity":"sha512-mGyr9kGqBT1vEGXYP1k6X+tzXDDsxM68ow+b98HgsftM8VCq8uhtr1R4T8zJBR2O++v3jWiXatzOkU65dzTY+w==","time":1754018576001,"size":131037}