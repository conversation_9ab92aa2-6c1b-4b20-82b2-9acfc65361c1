<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.klaw.dao.lawyerDao.LawFirmSelectionMapper">
    <resultMap id="BaseResultMap" type="com.klaw.entity.lawyerBean.LawFirmSelection">
        <id column="ID" jdbcType="VARCHAR" property="id"/>
        <result column="CREATE_OGN_ID" jdbcType="VARCHAR" property="createOgnId"/>
        <result column="CREATE_OGN_NAME" jdbcType="VARCHAR" property="createOgnName"/>
        <result column="CREATE_DEPT_ID" jdbcType="VARCHAR" property="createDeptId"/>
        <result column="CREATE_DEPT_NAME" jdbcType="VARCHAR" property="createDeptName"/>
        <result column="CREATE_GROUP_ID" jdbcType="VARCHAR" property="createGroupId"/>
        <result column="CREATE_GROUP_NAME" jdbcType="VARCHAR" property="createGroupName"/>
        <result column="CREATE_PSN_ID" jdbcType="VARCHAR" property="createPsnId"/>
        <result column="CREATE_PSN_NAME" jdbcType="VARCHAR" property="createPsnName"/>
        <result column="CREATE_ORG_ID" jdbcType="VARCHAR" property="createOrgId"/>
        <result column="CREATE_ORG_NAME" jdbcType="VARCHAR" property="createOrgName"/>
        <result column="CREATE_PSN_FULL_ID" jdbcType="VARCHAR" property="createPsnFullId"/>
        <result column="CREATE_PSN_FULL_NAME" jdbcType="VARCHAR" property="createPsnFullName"/>
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="DATA_STATE" jdbcType="VARCHAR" property="dataState"/>
        <result column="DATA_STATE_CODE" jdbcType="DECIMAL" property="dataStateCode"/>
        <result column="CREATE_OGN_IDOA" jdbcType="VARCHAR" property="createOgnIdoa"/>
        <result column="CREATE_OGN_NAMEOA" jdbcType="VARCHAR" property="createOgnNameoa"/>
        <result column="CREATE_DEPT_IDOA" jdbcType="VARCHAR" property="createDeptIdoa"/>
        <result column="CREATE_DEPT_NAMEOA" jdbcType="VARCHAR" property="createDeptNameoa"/>
        <result column="CREATE_GROUP_IDOA" jdbcType="VARCHAR" property="createGroupIdoa"/>
        <result column="CREATE_GROUP_NAMEOA" jdbcType="VARCHAR" property="createGroupNameoa"/>
        <result column="CREATE_PSN_IDOA" jdbcType="VARCHAR" property="createPsnIdoa"/>
        <result column="CREATE_PSN_NAMEOA" jdbcType="VARCHAR" property="createPsnNameoa"/>
        <result column="CREATE_ORG_IDOA" jdbcType="VARCHAR" property="createOrgIdoa"/>
        <result column="CREATE_ORG_NAMEOA" jdbcType="VARCHAR" property="createOrgNameoa"/>
        <result column="CREATE_PSN_FULL_IDOA" jdbcType="VARCHAR" property="createPsnFullIdoa"/>
        <result column="CREATE_PSN_FULL_NAMEOA" jdbcType="VARCHAR" property="createPsnFullNameoa"/>
        <result column="TITLE" jdbcType="VARCHAR" property="title"/>
        <result column="SELECTION_TYPE_NAME" jdbcType="VARCHAR" property="selectionTypeName"/>
        <result column="SELECTION_TYPE_ID" jdbcType="VARCHAR" property="selectionTypeId"/>
        <result column="BEGIN_DATE" jdbcType="TIMESTAMP" property="beginDate"/>
        <result column="END_DATE" jdbcType="TIMESTAMP" property="endDate"/>
        <result column="SELECTION_MODE_NAME" jdbcType="VARCHAR" property="selectionModeName"/>
        <result column="SELECTION_MODE_ID" jdbcType="VARCHAR" property="selectionModeId"/>
        <result column="APPLY_DEPT_NAME" jdbcType="VARCHAR" property="applyDeptName"/>
        <result column="APPLY_DEPT_ID" jdbcType="VARCHAR" property="applyDeptId"/>
        <result column="REMARK" jdbcType="CLOB" property="remark"/>
        <result column="INVITATION_ATTACHMENT" jdbcType="CLOB" property="invitationAttachment"/>
        <result column="TENDER_ATTACHMENT" jdbcType="CLOB" property="tenderAttachment"/>
        <result column="REVIEW_ATTACHMENT" jdbcType="CLOB" property="reviewAttachment"/>
        <result column="REPOPRT_ATTACHMENT" jdbcType="CLOB" property="repoprtAttachment"/>
        <result column="OTHER_ATTACHMENT" jdbcType="CLOB" property="otherAttachment"/>
    </resultMap>
    <sql id="Base_Column_List">
        ID, CREATE_OGN_ID, CREATE_OGN_NAME, CREATE_DEPT_ID, CREATE_DEPT_NAME, CREATE_GROUP_ID,
        CREATE_GROUP_NAME, CREATE_PSN_ID, CREATE_PSN_NAME, CREATE_ORG_ID, CREATE_ORG_NAME,
        CREATE_PSN_FULL_ID, CREATE_PSN_FULL_NAME, CREATE_TIME, UPDATE_TIME, DATA_STATE, DATA_STATE_CODE,
        CREATE_OGN_IDOA, CREATE_OGN_NAMEOA, CREATE_DEPT_IDOA, CREATE_DEPT_NAMEOA, CREATE_GROUP_IDOA,
        CREATE_GROUP_NAMEOA, CREATE_PSN_IDOA, CREATE_PSN_NAMEOA, CREATE_ORG_IDOA, CREATE_ORG_NAMEOA,
        CREATE_PSN_FULL_IDOA, CREATE_PSN_FULL_NAMEOA, TITLE, SELECTION_TYPE_NAME, SELECTION_TYPE_ID,
        BEGIN_DATE, END_DATE, SELECTION_MODE_NAME, SELECTION_MODE_ID, APPLY_DEPT_NAME, APPLY_DEPT_ID,
        REMARK, INVITATION_ATTACHMENT, TENDER_ATTACHMENT, REVIEW_ATTACHMENT, REPOPRT_ATTACHMENT,
        OTHER_ATTACHMENT
    </sql>
    <select id="queryCFDQ" parameterType="java.lang.String" resultType="java.util.Map">
        select m.id,m.TITLE,m.CREATE_PSN_FULL_ID,m.CREATE_PSN_FULL_NAME,m.CREATE_ORG_ID,m.CREATE_ORG_NAME,m.CREATE_PSN_ID,m.CREATE_PSN_NAME,
               m.CREATE_TIME,d.LAW_FIRM,d.LAW_FIRM_ID,d.LAWYER_NAME,d.lawyer_id
        from SG_LAW_FIRM_SELECTION m join SG_LAW_FIRM_SELECTION_DETAIL d on m.id=d.PARENT_ID
        where DATE_FORMAT(m.END_DATE,'yyyy-MM-dd') like #{date}
          and m.SELECTION_TYPE_NAME='聘请常年法律顾问'
          and WHETHER_WIN='是' and m.DATA_STATE_CODE=5
    </select>

    <resultMap id="PageMap" type="com.klaw.entity.lawyerBean.LawFirmSelection">
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="select_type" jdbcType="VARCHAR" property="selectType"/>
        <result column="select_type_id" jdbcType="VARCHAR" property="selectTypeId"/>
        <result column="CREATE_OGN_ID" jdbcType="VARCHAR" property="createOgnId"/>
        <result column="CREATE_OGN_NAME" jdbcType="VARCHAR" property="createOgnName"/>
        <result column="CREATE_DEPT_ID" jdbcType="VARCHAR" property="createDeptId"/>
        <result column="CREATE_DEPT_NAME" jdbcType="VARCHAR" property="createDeptName"/>
        <result column="CREATE_GROUP_ID" jdbcType="VARCHAR" property="createGroupId"/>
        <result column="CREATE_GROUP_NAME" jdbcType="VARCHAR" property="createGroupName"/>
        <result column="CREATE_PSN_ID" jdbcType="VARCHAR" property="createPsnId"/>
        <result column="CREATE_PSN_NAME" jdbcType="VARCHAR" property="createPsnName"/>
        <result column="CREATE_ORG_ID" jdbcType="VARCHAR" property="createOrgId"/>
        <result column="CREATE_ORG_NAME" jdbcType="VARCHAR" property="createOrgName"/>
        <result column="CREATE_PSN_FULL_ID" jdbcType="VARCHAR" property="createPsnFullId"/>
        <result column="CREATE_PSN_FULL_NAME" jdbcType="VARCHAR" property="createPsnFullName"/>
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="DATA_STATE" jdbcType="VARCHAR" property="dataState"/>
        <result column="DATA_STATE_CODE" jdbcType="DECIMAL" property="dataStateCode"/>
        <result column="TITLE" jdbcType="VARCHAR" property="title"/>
        <result column="SELECTION_TYPE_NAME" jdbcType="VARCHAR" property="selectionTypeName"/>
        <result column="SELECTION_TYPE_ID" jdbcType="VARCHAR" property="selectionTypeId"/>
        <result column="BEGIN_DATE" jdbcType="TIMESTAMP" property="beginDate"/>
        <result column="END_DATE" jdbcType="TIMESTAMP" property="endDate"/>
        <result column="SELECTION_MODE_NAME" jdbcType="VARCHAR" property="selectionModeName"/>
        <result column="SELECTION_MODE_ID" jdbcType="VARCHAR" property="selectionModeId"/>
        <result column="APPLY_DEPT_NAME" jdbcType="VARCHAR" property="applyDeptName"/>
        <result column="APPLY_DEPT_ID" jdbcType="VARCHAR" property="applyDeptId"/>
        <result column="REMARK" jdbcType="CLOB" property="remark"/>
        <result column="INVITATION_ATTACHMENT" jdbcType="CLOB" property="invitationAttachment"/>
        <result column="TENDER_ATTACHMENT" jdbcType="CLOB" property="tenderAttachment"/>
        <result column="REVIEW_ATTACHMENT" jdbcType="CLOB" property="reviewAttachment"/>
        <result column="REPOPRT_ATTACHMENT" jdbcType="CLOB" property="repoprtAttachment"/>
        <result column="OTHER_ATTACHMENT" jdbcType="CLOB" property="otherAttachment"/>
        <result column="RELATION_CASE_NAME" property="relationCaseName"/>
        <result column="RELATION_CASE_ID" property="relationCaseId"/>
        <result column="SERVICE_PROJECT" property="serviceProject" />
        <collection property="lawyerList" ofType="com.klaw.entity.lawyerBean.LawFirmSelectionDetail">
            <id column="ID_" property="id"/>
            <result column="PARENT_ID_" property="parentId"/>
            <result column="LAW_FIRM_" property="lawFirm"/>
            <result column="LAW_FIRM_ID_" property="lawFirmId"/>
            <result column="TYPE_CODE_" property="typeCode"/>
            <result column="TYPE_NAME_" property="typeName"/>
            <result column="LAWYER_NAME_" property="lawyerName"/>
            <result column="LAWYER_ID_" property="lawyerId"/>
            <result column="CHARTERED_NO_" property="charteredNo"/>
            <result column="REASON_" property="reason"/>
            <result column="WHETHER_REPLY_" property="whetherReply"/>
            <result column="WHETHER_FIXED_" property="whetherFixed"/>
            <result column="QUOTED_PRICE_STRING_" property="quotedPriceString"/>
            <result column="QUOTED_PRICE_" property="quotedPrice"/>
            <result column="FRACTION_" property="fraction"/>
            <result column="VOTERS_" property="voters"/>
            <result column="WHETHER_WIN_" property="whetherWin"/>
            <result column="WHETHER_OUT_LIB_" property="whetherOutLib"/>
        </collection>
    </resultMap>

    <select id="queryPageData" resultMap="PageMap">
        SELECT
            m.select_type_id,m.select_type,
            m.ID,m.TITLE,m.SELECTION_TYPE_NAME,m.SELECTION_TYPE_ID,m.BEGIN_DATE,m.END_DATE,m.SELECTION_MODE_NAME,m.SELECTION_MODE_ID,
            m.APPLY_DEPT_NAME,m.APPLY_DEPT_ID,m.REMARK,m.INVITATION_ATTACHMENT,m.TENDER_ATTACHMENT,m.REVIEW_ATTACHMENT,m.REPOPRT_ATTACHMENT,
            m.OTHER_ATTACHMENT,m.AGED_ATTACHMENT,m.QUOTED_PRICED_ATTACHMENT,m.ENTRUST_ATTACHMENT,m.SERVICE_PROJECT,m.SERVICE_PROJECT_ID,m.RELATION_CASE_NAME,
            m.RELATION_CASE_ID,m.AGENT_STAGE_NAME,m.AGENT_STAGE_ID,m.WIN_LAW_FIRM,m.WIN_LAW_FIRM_ID,m.SIGNED_REPORT_TYPE_NAME,
            m.SIGNED_REPORT_TYPE_ID,m.SIGNED_REPORT_NAME, m.SIGNED_REPORT_ID,m.SIGNED_REPORT_ATTACHMENT,m.RELATION_APPLY_ID,
            m.RELATION_APPLY_NAME,m.RELATION_SELECT_ID,m.RELATION_SELECT_NAME,m.SELECT_COUNT,m.CREATE_OGN_ID,m.CREATE_OGN_NAME,
            m.CREATE_DEPT_ID,m.CREATE_DEPT_NAME,m.CREATE_GROUP_ID,m.CREATE_GROUP_NAME,m.CREATE_PSN_ID,m.CREATE_PSN_NAME,m.CREATE_ORG_ID,
            m.CREATE_ORG_NAME,m.CREATE_PSN_FULL_ID,m.CREATE_PSN_FULL_NAME,m.CREATE_TIME,m.UPDATE_TIME,m.DATA_STATE,m.DATA_STATE_CODE
            ,d.ID  ID_, d.PARENT_ID PARENT_ID_, d.LAW_FIRM LAW_FIRM_, d.LAW_FIRM_ID LAW_FIRM_ID_, d.TYPE_CODE TYPE_CODE_,
            d.TYPE_NAME TYPE_NAME_, d.LAWYER_NAME LAWYER_NAME_, d.LAWYER_ID LAWYER_ID_, d.CHARTERED_NO CHARTERED_NO_,
            d.REASON REASON_, d.WHETHER_REPLY WHETHER_REPLY_, d.WHETHER_FIXED WHETHER_FIXED_, d.QUOTED_PRICE_STRING QUOTED_PRICE_STRING_,
            d.QUOTED_PRICE QUOTED_PRICE_, d.FRACTION FRACTION_, d.VOTERS VOTERS_, d.WHETHER_WIN WHETHER_WIN_,d.WHETHER_OUT_LIB WHETHER_OUT_LIB_
        FROM SG_LAW_FIRM_SELECTION m LEFT JOIN SG_LAW_FIRM_SELECTION_DETAIL d
        ON m.id = d.parent_id AND d.whether_win = '是'
        <where>
            ${ew.sqlSegment}
        </where>
    </select>

    <select id="queryUseList" resultMap="PageMap">
        SELECT
        m.ID,m.TITLE,m.SELECTION_TYPE_NAME,m.SELECTION_TYPE_ID,m.BEGIN_DATE,m.END_DATE,m.SELECTION_MODE_NAME,m.SELECTION_MODE_ID,
        m.APPLY_DEPT_NAME,m.APPLY_DEPT_ID,m.REMARK,m.INVITATION_ATTACHMENT,m.TENDER_ATTACHMENT,m.REVIEW_ATTACHMENT,m.REPOPRT_ATTACHMENT,
        m.OTHER_ATTACHMENT,m.AGED_ATTACHMENT,m.QUOTED_PRICED_ATTACHMENT,m.ENTRUST_ATTACHMENT,m.SERVICE_PROJECT,m.SERVICE_PROJECT_ID,m.RELATION_CASE_NAME,
        m.RELATION_CASE_ID,m.AGENT_STAGE_NAME,m.AGENT_STAGE_ID,m.WIN_LAW_FIRM,m.WIN_LAW_FIRM_ID,m.SIGNED_REPORT_TYPE_NAME,
        m.SIGNED_REPORT_TYPE_ID,m.SIGNED_REPORT_NAME, m.SIGNED_REPORT_ID,m.SIGNED_REPORT_ATTACHMENT,m.RELATION_APPLY_ID,
        m.RELATION_APPLY_NAME,m.RELATION_SELECT_ID,m.RELATION_SELECT_NAME,m.SELECT_COUNT,m.CREATE_OGN_ID,m.CREATE_OGN_NAME,
        m.CREATE_DEPT_ID,m.CREATE_DEPT_NAME,m.CREATE_GROUP_ID,m.CREATE_GROUP_NAME,m.CREATE_PSN_ID,m.CREATE_PSN_NAME,m.CREATE_ORG_ID,
        m.CREATE_ORG_NAME,m.CREATE_PSN_FULL_ID,m.CREATE_PSN_FULL_NAME,m.CREATE_TIME,m.UPDATE_TIME,m.DATA_STATE,m.DATA_STATE_CODE
        ,d.ID  ID_, d.PARENT_ID PARENT_ID_, d.LAW_FIRM LAW_FIRM_, d.LAW_FIRM_ID LAW_FIRM_ID_, d.TYPE_CODE TYPE_CODE_,
        d.TYPE_NAME TYPE_NAME_, d.LAWYER_NAME LAWYER_NAME_, d.LAWYER_ID LAWYER_ID_, d.CHARTERED_NO CHARTERED_NO_,
        d.REASON REASON_, d.WHETHER_REPLY WHETHER_REPLY_, d.WHETHER_FIXED WHETHER_FIXED_, d.QUOTED_PRICE_STRING QUOTED_PRICE_STRING_,
        d.QUOTED_PRICE QUOTED_PRICE_, d.FRACTION FRACTION_, d.VOTERS VOTERS_, d.WHETHER_WIN WHETHER_WIN_,d.WHETHER_OUT_LIB WHETHER_OUT_LIB_
        FROM SG_LAW_FIRM_SELECTION m LEFT JOIN SG_LAW_FIRM_SELECTION_DETAIL d
        ON m.id = d.parent_id AND d.whether_win = '是'
        LEFT JOIN sg_law_firm_select_team t ON m.id  = t.parent_id
        LEFT JOIN SG_LAWYER_USE l ON m.id = l.parent_id
        <where>
            ${ew.sqlSegment}
        </where>
    </select>
</mapper>
