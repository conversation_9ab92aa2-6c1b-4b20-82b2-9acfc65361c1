
62018e0c7c5a2e0b12c85776301a10212bc8d2bb	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.104.1754018536329.js\",\"contentHash\":\"b126f9932a4cad7d3fb589d6c8c24a0f\"}","integrity":"sha512-vC+1Bv5NUKOjbl/FwD0ORgdw9kRnl9/QKTPLo22fEdUoCrwH/e+4igCsZzL+U26FAH3x9+Qmp/AvWlkoFTfleg==","time":1754018576088,"size":259174}