<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.klaw.dao.test.WinBidInfoMapper">
    <resultMap id="BaseResultMap" type="com.klaw.entity.test.WinBidInfo">
        <id column="id" property="id" />
        <result column="project_source_system_name" property="projectSourceSystemName" />
        <result column="project_source_system_code" property="projectSourceSystemCode" />
        <result column="push_system_name" property="pushSystemName" />
        <result column="push_system_code" property="pushSystemCode" />
        <result column="ep_project_unique_code" property="epProjectUniqueCode" />
        <result column="creator_id" property="creatorId" />
        <result column="creator_code" property="creatorCode" />
        <result column="creator_name" property="creatorName" />
        <result column="creator_id_card" property="creatorIdCard" />
        <result column="creator_department_name" property="creatorDepartmentName" />
        <result column="creator_department_code" property="creatorDepartmentCode" />
        <result column="procurement_case_number" property="procurementCaseNumber" />
        <result column="project_name" property="projectName" />
        <result column="project_code" property="projectCode" />
        <result column="section_package_name" property="sectionPackageName" />
        <result column="section_package_code" property="sectionPackageCode" />
        <result column="procurement_unit_name" property="procurementUnitName" />
        <result column="procurement_unit_code" property="procurementUnitCode" />
        <result column="project_type" property="projectType" />
        <result column="procurement_bidding_method" property="procurementBiddingMethod" />
        <result column="public_scope" property="publicScope" />
        <result column="award_form" property="awardForm" />
        <result column="evaluation_completion_time" property="evaluationCompletionTime" />
        <result column="bid_evaluation_report_attachment" property="bidEvaluationReportAttachment" />
        <result column="successful_supplier_name" property="successfulSupplierName" />
        <result column="successful_supplier_code" property="successfulSupplierCode" />
        <result column="supplier_master_data_code" property="supplierMasterDataCode" />
        <result column="contract_price" property="contractPrice" />
        <result column="unit" property="unit" />
        <result column="is_tax_included" property="isTaxIncluded" />
        <result column="tax_rate" property="taxRate" />
        <result column="currency" property="currency" />
        <result column="contract_signing_type" property="contractSigningType" />
        <result column="list_details" property="listDetails" />
        <result column="remarks" property="remarks" />
        <result column="attachments" property="attachments" />
        <result column="template_type" property="templateType" />
        <result column="is_united" property="isUnited" />
    </resultMap>
    <sql id="selectWinBidInfoVo">
        SELECT
            id,
            project_source_system_name,
            project_source_system_code,
            push_system_name,
            push_system_code,
            ep_project_unique_code,
            creator_id,
            creator_code,
            creator_name,
            creator_id_card,
            creator_department_name,
            creator_department_code,
            procurement_case_number,
            project_name,
            project_code,
            section_package_name,
            section_package_code,
            procurement_unit_name,
            procurement_unit_code,
            project_type,
            procurement_bidding_method,
            public_scope,
            award_form,
            evaluation_completion_time,
            bid_evaluation_report_attachment,
            successful_supplier_name,
            successful_supplier_code,
            supplier_master_data_code,
            contract_price,
            unit,
            is_tax_included,
            tax_rate,
            currency,
            contract_signing_type,
            remarks,
            attachments,
            template_type,
            is_united
        FROM
            win_bid_info
    </sql>

    <!-- Select list of WinBidInfo with conditions -->
    <select id="selectWinBidInfoList"  resultMap="BaseResultMap" >
        <include refid="selectWinBidInfoVo"/>
        <where>
            <if test="id != null">
                AND id = #{id}
            </if>
            <if test="projectSourceSystemName != null and projectSourceSystemName != ''">
                AND project_source_system_name = #{projectSourceSystemName}
            </if>
            <if test="projectSourceSystemCode != null and projectSourceSystemCode != ''">
                AND project_source_system_code = #{projectSourceSystemCode}
            </if>
            <if test="pushSystemName != null and pushSystemName != ''">
                AND push_system_name = #{pushSystemName}
            </if>
            <if test="pushSystemCode != null and pushSystemCode != ''">
                AND push_system_code = #{pushSystemCode}
            </if>
            <if test="epProjectUniqueCode != null and epProjectUniqueCode != ''">
                AND ep_project_unique_code = #{epProjectUniqueCode}
            </if>
            <if test="creatorId != null and creatorId != ''">
                AND creator_id = #{creatorId}
            </if>
            <if test="creatorCode != null and creatorCode != ''">
                AND creator_code = #{creatorCode}
            </if>
            <if test="creatorName != null and creatorName != ''">
                AND creator_name = #{creatorName}
            </if>
            <if test="creatorIdCard != null and creatorIdCard != ''">
                AND creator_id_card = #{creatorIdCard}
            </if>
            <if test="creatorDepartmentName != null and creatorDepartmentName != ''">
                AND creator_department_name = #{creatorDepartmentName}
            </if>
            <if test="creatorDepartmentCode != null and creatorDepartmentCode != ''">
                AND creator_department_code = #{creatorDepartmentCode}
            </if>
            <if test="procurementCaseNumber != null and procurementCaseNumber != ''">
                AND procurement_case_number = #{procurementCaseNumber}
            </if>
            <if test="projectName != null and projectName != ''">
                AND project_name = #{projectName}
            </if>
            <if test="projectCode != null and projectCode != ''">
                AND project_code = #{projectCode}
            </if>
            <if test="sectionPackageName != null and sectionPackageName != ''">
                AND section_package_name = #{sectionPackageName}
            </if>
            <if test="sectionPackageCode != null and sectionPackageCode != ''">
                AND section_package_code = #{sectionPackageCode}
            </if>
            <if test="procurementUnitName != null and procurementUnitName != ''">
                AND procurement_unit_name = #{procurementUnitName}
            </if>
            <if test="procurementUnitCode != null and procurementUnitCode != ''">
                AND procurement_unit_code = #{procurementUnitCode}
            </if>
            <if test="projectType != null and projectType != ''">
                AND project_type = #{projectType}
            </if>
            <if test="procurementBiddingMethod != null and procurementBiddingMethod != ''">
                AND procurement_bidding_method = #{procurementBiddingMethod}
            </if>
            <if test="publicScope != null and publicScope != ''">
                AND public_scope = #{publicScope}
            </if>
            <if test="awardForm != null and awardForm != ''">
                AND award_form = #{awardForm}
            </if>
            <if test="evaluationCompletionTime != null">
                AND evaluation_completion_time = #{evaluationCompletionTime}
            </if>
            <if test="bidEvaluationReportAttachment != null and bidEvaluationReportAttachment != ''">
                AND bid_evaluation_report_attachment = #{bidEvaluationReportAttachment}
            </if>
            <if test="successfulSupplierName != null and successfulSupplierName != ''">
                AND successful_supplier_name = #{successfulSupplierName}
            </if>
            <if test="successfulSupplierCode != null and successfulSupplierCode != ''">
                AND successful_supplier_code = #{successfulSupplierCode}
            </if>
            <if test="supplierMasterDataCode != null and supplierMasterDataCode != ''">
                AND supplier_master_data_code = #{supplierMasterDataCode}
            </if>
            <if test="contractPrice != null and contractPrice != ''">
                AND contract_price = #{contractPrice}
            </if>
            <if test="unit != null and unit != ''">
                AND unit = #{unit}
            </if>
            <if test="isTaxIncluded != null and isTaxIncluded != ''">
                AND is_tax_included = #{isTaxIncluded}
            </if>
            <if test="taxRate != null">
                AND tax_rate = #{taxRate}
            </if>
            <if test="currency != null and currency != ''">
                AND currency = #{currency}
            </if>
            <if test="contractSigningType != null and contractSigningType != ''">
                AND contract_signing_type = #{contractSigningType}
            </if>
            <if test="remarks != null and remarks != ''">
                AND remarks = #{remarks}
            </if>
            <if test="attachments != null and attachments != ''">
                AND attachments = #{attachments}
            </if>
            <if test="templateType != null and templateType != ''">
                AND template_type = #{templateType}
            </if>
            <if test="isUnited != null and isUnited != ''">
                AND is_united = #{isUnited}
            </if>
        </where>
    </select>
</mapper>
