
f58e6d44bcd34b738bc90ab70ede5db97eb6eb00	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.193.1754018536329.js\",\"contentHash\":\"82142fb4b3be8b0bfcc792aaad3175f1\"}","integrity":"sha512-mZGacwxmbmXFNn8XnxSzcaFB5fwoOHbLDMrkE3e284agLQZgxN2PKUFKH5ydbhdN8xkiq1zhUcZbYQCtQkUoVg==","time":1754018576052,"size":179419}