package com.klaw.service.imp.ComplianceChackServiceImpl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.klaw.constant.NoticeType;
import com.klaw.constant.TaskType;
import com.klaw.dao.ComplianceChackDao.ComplianceChackMapper;
import com.klaw.dao.ComplianceChackDao.ComplianceChackRectificationPlanMapper;
import com.klaw.dao.systemDao.SgActRuTaskMapper;
import com.klaw.dao.systemDao.SgSysUserMapper;
import com.klaw.dao.systemDao.SysUsersMapper;
import com.klaw.entity.ComplianceChack.*;
import com.klaw.entity.config.RiskMiitigationStatus;
import com.klaw.entity.mainDataBean.MainMidStaffUnitOrg;
import com.klaw.entity.systemBean.HrUserUnitm;
import com.klaw.entity.systemBean.SgHrOrgUnitB;
import com.klaw.entity.systemBean.SgSysTask;
import com.klaw.entity.systemBean.SysUsers;
import com.klaw.service.ComplianceChackService.ComplianceChackRectificationPlanService;
import com.klaw.service.ComplianceChackService.ComplianceChackService;
import com.klaw.service.mainDataService.MainMidStaffUnitOrgService;
import com.klaw.service.systemService.HrUserUnitmService;
import com.klaw.service.systemService.SgHrOrgUnitBService;
import com.klaw.service.systemService.SgSysNoticeService;
import com.klaw.service.systemService.SgSysTaskService;
import com.klaw.utils.*;
import com.klaw.utils.wps.WPSUtils;
import com.sgai.mcp.core.impl.LoginHelper;
import com.sgai.mcp.flow.controller.FlowableMonitorController;
import com.sgai.mcp.flow.result.ResponseDataPage;
import com.sgai.mcp.sys.organization.dto.SysUserDTO;
import com.sgai.mcp.sys.organization.service.ISysUserService;
import org.apache.commons.lang3.StringUtils;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.runtime.ProcessInstance;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;

@Service
public class ComplianceChackServiceImpl extends ServiceImpl<ComplianceChackMapper,ComplianceChackTable> implements ComplianceChackService {

    @Resource
    private SgSysTaskService sgSysTaskService;
    @Resource
    private ComplianceChackMapper complianceChackMapper;

    @Resource
    private ComplianceChackRectificationPlanService chackRectificationPlanService;

    @Resource
    private SgActRuTaskMapper sgActRuTaskMapper;

    @Resource
    private SgSysUserMapper sgSysUserMapper;

    @Autowired
    private SysUsersMapper sysUsersMapper;

    @Resource
    private SgSysNoticeService sgSysNoticeService;

    @Resource
    private SgHrOrgUnitBService hrOrgUnitBService;

    @Resource
    private HrUserUnitmService hrUserUnitmService;

    @Resource
    private MainMidStaffUnitOrgService mainMidStaffUnitOrgService;

    @Autowired
    private RuntimeService runtimeService;

    @Value("${server.port}")
    private String port;

    @Override
    public Boolean addChackTask(ComplianceChackEntity complianceChackEntity) {
        ComplianceChackTable complianceChackTable = new ComplianceChackTable();
        //判读传入时是否传入id
        if (StringUtils.isBlank(complianceChackEntity.getId())){
            complianceChackTable.setId(IdUtil.getSnowflake().nextIdStr());
            complianceChackEntity.setId(complianceChackTable.getId());
        }
        BeanUtils.copyProperties(complianceChackEntity, complianceChackTable);
        //基础信息
        ComplianceChackEntity.ChackInfo chackInfo = complianceChackEntity.getChackInfo();
        if (chackInfo != null){
            //判断事项编码
            if (StringUtils.isBlank(chackInfo.getItemCode())){
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(new Date());
                int i = new Random().nextInt(90000)+10000;
                chackInfo.setItemCode("hgjc"+calendar.get(Calendar.YEAR)+i);
            }
            //基础信息编辑
            BeanUtils.copyProperties(chackInfo, complianceChackTable);
            List<Map<String, Long>> leaderId = sgSysUserMapper.queryUserIds(chackInfo.getInspectedUnitCode(), "HGFXJSR");
            if (leaderId == null || leaderId.size() == 0){
                throw new RuntimeException("被检查单位未配置接收人!");
            }
            //检查类型设置
            complianceChackTable.setCheckType(chackInfo.getCheckType().getKey());
        }

        //检查结果
        ComplianceChackEntity.ReviewResult reviewResult = complianceChackEntity.getReviewResult();
        if (reviewResult != null){
            BeanUtils.copyProperties(reviewResult, complianceChackTable);
        }

        //指定整改计划
        ComplianceChackEntity.RectificationPlan rectificationPlan = complianceChackEntity.getRectificationPlan();
        if (rectificationPlan != null){
            if ( rectificationPlan.getPlans() != null && rectificationPlan.getPlans().size() > 0){
                for (ComplianceChackRectificationPlan plan : rectificationPlan.getPlans()) {
                    //设置整改计划
                    if (StringUtils.isBlank(plan.getChackId())){
                        plan.setChackId(complianceChackEntity.getId());
                    }

                    if (StringUtils.isBlank(plan.getId())){
                        plan.setId(UUID.randomUUID().toString());
                    }
                    chackRectificationPlanService.saveOrUpdate(plan);
                }
            }
        }

        //整改计划结果
        ComplianceChackEntity.RectificationResult rectificationResult = complianceChackEntity.getRectificationResult();
        if (rectificationResult != null){
            BeanUtils.copyProperties(rectificationResult, complianceChackTable);
            //整改状态
            RectificationStatus rectificationStatus = rectificationResult.getRectificationStatus();
            if (rectificationStatus != null) {
                complianceChackTable.setRectificationStatus(rectificationStatus.getKey());
            }
        }

        //配置此条任务的状态
        if (complianceChackEntity.getIsSubmit()) {
            ComplianceChackTaskType taskType = getTaskType(complianceChackTable, complianceChackEntity.getCurrentTaskType(), complianceChackEntity.getCurrentProcessType());
            if (taskType != complianceChackEntity.getCurrentTaskType()){
                complianceChackTable.setCurrentProcessType(ProcessType.UN_START.getKey());
                complianceChackTable.setCurrentProcess(null);
                complianceChackTable.setCurrentProcessId(null);
            }
            complianceChackTable.setCurrentTaskType(taskType.getKey());
        } else if (complianceChackEntity.getCurrentTaskType() != null){
            complianceChackTable.setCurrentTaskType(complianceChackEntity.getCurrentTaskType().getKey());
        }

        boolean reslut = this.saveOrUpdate(complianceChackTable);
        return reslut;
    }

    @Override
    public void updateAndSendTask(String businessKey,String processInstanceId) {
        //查询检查任务
        ComplianceChackTable complianceChackTable = complianceChackMapper.selectById(businessKey);

        if (complianceChackTable != null){
            //更新任务状态
            String currentTaskType = complianceChackTable.getCurrentTaskType();
            ComplianceChackTaskType complianceChackTaskType = ComplianceChackTaskType.fromKey(currentTaskType);
            ComplianceChackTaskType taskType = getTaskType(complianceChackTable,complianceChackTaskType, ProcessType.COMPLETED);
            if (taskType != complianceChackTaskType){
                complianceChackTable.setCurrentTaskType(taskType.getKey());
                complianceChackTable.setCurrentProcess(" ");
                complianceChackTable.setCurrentProcessType(" ");
                complianceChackTable.setCurrentHandler(" ");
                complianceChackTable.setCurrentProcessId(" ");
                complianceChackTable.setDataStateCode(1);
                complianceChackTable.setDataState("已保存");
                complianceChackMapper.updateById(complianceChackTable);

                //发送消息
                JSONObject noticeJson = new JSONObject();
                noticeJson.put("dataState", "view");
                noticeJson.put("dataId", businessKey);
                noticeJson.put("functionId", "Risk_Warning_Receive," + businessKey);
                noticeJson.put("isNotice", true);
                List<String> receiveOrgIds = new ArrayList<>();
                //获取被检查单位负责人信息
                String unitCode = complianceChackTable.getInspectedUnitCode();
                List<Map<String, Long>> leaderId = sgSysUserMapper.queryUserIds(unitCode, "HGFXJSR");
                List<String> userIds = new ArrayList<>();
                for (Map<String, Long> stringBigDecimalMap : leaderId) {
                    userIds.add(stringBigDecimalMap.get("userId").toString());
                }
                for (String userId : userIds) {
                    //查询用户与组织信息
                    //List<HrUserUnitm> hrUserUnitms = hrUserUnitmService.list(new QueryWrapper<HrUserUnitm>().eq("USER_ID", userId));
                    QueryWrapper<MainMidStaffUnitOrg> staffUnitOrgQueryWrapper = new QueryWrapper<>();
                    staffUnitOrgQueryWrapper.eq("STAFF_CODE", userId);
                    staffUnitOrgQueryWrapper.eq("state","1");
                    staffUnitOrgQueryWrapper.eq("code_type","1");
                    List<MainMidStaffUnitOrg> staffCode = mainMidStaffUnitOrgService.list(staffUnitOrgQueryWrapper);
                    if (staffCode != null && staffCode.size() > 0){
                        SgHrOrgUnitB hrOrgUnitB = hrOrgUnitBService.getOne(new QueryWrapper<SgHrOrgUnitB>().eq("unit_code", staffCode.get(0).getCode()));
                        if (hrOrgUnitB != null){
                            receiveOrgIds.add(String.valueOf(hrOrgUnitB.getUnitId()));
                        }

                    }
                }
                sgSysNoticeService.createNotices("【"+complianceChackTable.getCreateOgnName()+"】发布合规检查任务，请查收！","合规检查", NoticeType.HGYXJC_NOTICE,businessKey,"Check_Result_Detail",noticeJson.toJSONString(),Long.parseLong(complianceChackTable.getCreateOrgId()),receiveOrgIds,false);
            }
        }

    }

    @Override
    public void updateChackProcess(String businessKey, String processInstanceId) {
        Thread thread = new Thread(() -> {
            try {
                Thread.sleep(3500);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
            ComplianceChackTable complianceChackTable = complianceChackMapper.selectById(businessKey);
            if (complianceChackTable != null) {
                String currentProcessInfo = ProcessUtilsPro.getCurrentProcessInfo(processInstanceId);
                String[] split = currentProcessInfo.split("/");
                String s = split[1];
                if (s != null) {
                    SysUsers sysUsers = sysUsersMapper.selectById(s);
                    if (sysUsers != null) {
                        split[1] = sysUsers.getFullName();
                    }
                }

                UpdateWrapper<ComplianceChackTable> complianceChackTableUpdateWrapper = new UpdateWrapper<>();
                complianceChackTableUpdateWrapper.eq("id", complianceChackTable.getId())
                        .set("current_process_type",split[0])
                        .set("current_handler",split[1]);
                this.update(complianceChackTableUpdateWrapper);
            }
        });
        thread.start();

    }

    @Override
    public void updateAndSendTaskResult(String businessKey, String processInstanceId) {
        //获取检查结果状态
        ComplianceChackTable complianceChackTable = complianceChackMapper.selectById(businessKey);
        String unitCode = complianceChackTable.getInspectedUnitCode();
        if (complianceChackTable.getNeedsRectification()) {
            //获取被检查单位

            List<String> userIds = new ArrayList<>();
            List<String> userName = new ArrayList<>();
            if (StringUtils.isNotBlank(unitCode)) {
                List<Map<String, Long>> leaderId = sgSysUserMapper.queryUserIds(unitCode, "HGFXJSR");
                for (Map<String, Long> stringBigDecimalMap : leaderId) {
                    String userid = stringBigDecimalMap.get("userId").toString();
                    userIds.add(userid);
                    SysUsers sysUsers = sysUsersMapper.selectById(userid);
                    if (sysUsers != null) {
                        userName.add(sysUsers.getFullName());
                    }
                }
            }
            complianceChackTable.setInspectedPsnCode(userIds.toString());
            complianceChackTable.setInspectedPsnName(userName.toString());
        }
        //获取被检查单位负责人信息
        List<Long> orgIds = new ArrayList<>();
        List<Map<String, Long>> leaderId = sgSysUserMapper.queryUserIds(unitCode, "HGFXJSR");
        List<String> userIds = new ArrayList<>();
        for (Map<String, Long> stringBigDecimalMap : leaderId) {
            userIds.add(stringBigDecimalMap.get("userId").toString());
        }
        for (String userId : userIds) {
            //查询用户与组织信息
            List<HrUserUnitm> hrUserUnitms = hrUserUnitmService.list(new QueryWrapper<HrUserUnitm>().eq("USER_ID", userId));
            if (hrUserUnitms.size() > 0) {
                HrUserUnitm hrUserUnitm = hrUserUnitms.get(0);
                orgIds.add(hrUserUnitm.getUnitId());
            }

        }

        if (complianceChackTable.getNeedsRectification()){
            JSONObject paramJson = new JSONObject();
            paramJson.put("handleMode", TaskType.ALL.getValue());
            paramJson.put("handleModeCode", TaskType.ALL.getKey());
            //发送待办
            sgSysTaskService.createTask(complianceChackTable.getItemName(),businessKey,"rectification_plan_detail",null,null, Long.parseLong(complianceChackTable.getCreateOrgId()),complianceChackTable.getLatestRectificationTime(),orgIds,paramJson.toJSONString(),"合规检查结果","数据确认");
        } else {
            JSONObject noticeJson = new JSONObject();
            noticeJson.put("dataState", "view");
            noticeJson.put("dataId", businessKey);
            noticeJson.put("functionId", "Risk_Warning_Receive," + businessKey);
            noticeJson.put("isNoti mce", true);

            List<String> list = orgIds.stream().map(o -> String.valueOf(o)).collect(Collectors.toList());
            //发送消息
            sgSysNoticeService.createNotices("【"+complianceChackTable.getCreateOgnName()+"】发布合规检查结果，请查收！","合规检查", NoticeType.HGYXJC_NOTICE,businessKey,"Check_Result_Detail",noticeJson.toJSONString(),Long.parseLong(complianceChackTable.getCreateOrgId()),list,false);
        }
        String currentTaskType = complianceChackTable.getCurrentTaskType();
        ComplianceChackTaskType complianceChackTaskType = ComplianceChackTaskType.fromKey(currentTaskType);
        ComplianceChackTaskType taskType = getTaskType(complianceChackTable,complianceChackTaskType, ProcessType.COMPLETED);
        complianceChackTable.setCurrentTaskType(taskType.getKey());
        complianceChackTable.setCurrentProcessType(" ");
        complianceChackTable.setCurrentProcessType(" ");
        complianceChackTable.setCurrentProcessId(" ");
        complianceChackTable.setCurrentHandler(" ");
        complianceChackTable.setDataStateCode(1);
        complianceChackTable.setDataState("已保存");
        complianceChackMapper.updateById(complianceChackTable);
    }

    @Override
    public ComplianceChackEntity queryById(String id,String cookie,String cosfToken) {
        ComplianceChackTable complianceChackTable = getById(id);
        if (complianceChackTable == null){
            throw new NullPointerException("传入id查询数据为空，请检查数据");
        }
        ComplianceChackEntity complianceChackEntity = convertEntity(complianceChackTable,cookie,cosfToken);
        return complianceChackEntity;
    }

    @Override
    public ComplianceChackEntity queryById(String id) {
        ComplianceChackTable complianceChackTable = getById(id);
        if (complianceChackTable == null){
            throw new NullPointerException("传入id查询数据为空，请检查数据");
        }
        ComplianceChackEntity complianceChackEntity = convertEntity(complianceChackTable,null,null);
        return complianceChackEntity;
    }

    @Override
    public Page query(JSONObject jsonObject,String cookie,String token) {
        Integer pageNum = jsonObject.getInteger("page");
        Integer pageSize = jsonObject.getInteger("limit");

        QueryWrapper<ComplianceChackTable> queryWrapper = new QueryWrapper<>();

        String chackType = jsonObject.getString("checkType");
        if (StringUtils.isNotBlank(chackType)){
            queryWrapper.eq("check_type", chackType);
        }

        String needsRectification = jsonObject.getString("needsRectification");
        if (StringUtils.isNotBlank(needsRectification)){
            queryWrapper.eq("needs_rectification", needsRectification);
        }

        String itemName = jsonObject.getString("itemName");
        if (StringUtils.isNotBlank(itemName)){
            queryWrapper.like("item_name", itemName);
        }


        String fuzzyValue = jsonObject.getString("fuzzyValue");
        if (StringUtils.isNotBlank(fuzzyValue)){
            queryWrapper.like("item_name", fuzzyValue);
        }

//        String currentTaskType = jsonObject.getString("currentTaskType");
//        if (StringUtils.isNotBlank(currentTaskType)){
//            queryWrapper.and(wrapper -> wrapper.eq("current_task_type", currentTaskType).or().isNull("current_task_type"));
//
//        }
        // 假设 jsonObject.getJSONArray("currentTaskType") 获取数组
        Object currentTaskTypeValue = jsonObject.get("currentTaskType");

        if (currentTaskTypeValue != null) {
            if (currentTaskTypeValue instanceof String) {
                // 情况 1：单值（字符串）
                String currentTaskType = (String) currentTaskTypeValue;
                if (StringUtils.isNotBlank(currentTaskType)) {
                    queryWrapper.and(wrapper ->
                            wrapper.eq("current_task_type", currentTaskType)
                                    .or()
                                    .isNull("current_task_type")
                    );
                }
            } else if (currentTaskTypeValue instanceof JSONArray) {
                // 情况 2：数组
                JSONArray currentTaskTypes = (JSONArray) currentTaskTypeValue;
                if (!currentTaskTypes.isEmpty()) {
                    List<String> taskTypeList = currentTaskTypes.toJavaList(String.class);
                    queryWrapper.and(wrapper ->
                            wrapper.in("current_task_type", taskTypeList)
                                    .or()
                                    .isNull("current_task_type")
                    );
                }
            }
            // 其他情况（如数字、布尔等）可以扩展
        }
        String orgId = jsonObject.getString("orgId");
        if (StringUtils.isNotBlank(orgId)){
            SgHrOrgUnitB hrOrgUnitB = hrOrgUnitBService.getOne(new QueryWrapper<SgHrOrgUnitB>().eq("UNIT_ID", orgId));
            if (hrOrgUnitB != null){
                String unitseq = hrOrgUnitB.getUnitseq();
                String[] split = unitseq.split("\\.");
                queryWrapper.eq("inspected_uint_code", split[split.length - 2]);
            }
        }
        if (jsonObject.containsKey("isInspected")){
            Boolean isInspected = jsonObject.getBoolean("isInspected");
            if (isInspected){
                queryWrapper.and(p -> p.like("inspected_psn_code",String.valueOf(LoginHelper.getUserId())));
            } else {
                queryWrapper.and(p -> p.eq("create_psn_id",String.valueOf(LoginHelper.getUserId())).or().like("inspected_psn_code",String.valueOf(LoginHelper.getUserId())));
            }
        } else {
            queryWrapper.and(p -> p.eq("create_psn_id",String.valueOf(LoginHelper.getUserId())).or().like("inspected_psn_code",String.valueOf(LoginHelper.getUserId())));
        }
        queryWrapper.orderByDesc("create_time");
        Page<ComplianceChackTable> tablePage = new Page<>();
        tablePage.setCurrent(pageNum);
        tablePage.setSize(pageSize);
        Page<ComplianceChackTable> page = complianceChackMapper.selectPage(tablePage, queryWrapper);
//        ArrayList<ComplianceChackTable> chackTables = new ArrayList<>();
//        page.getRecords().stream().forEach(complianceChackTable -> {
//            ComplianceChackTable updated = updateProcess(complianceChackTable.getId(), cookie, token);
//            if (updated != null){
//                chackTables.add(updated);
//            } else {
//                chackTables.add(complianceChackTable);
//            }
//        });
        return page;
    }

    @Override
    public Page queryBank(JSONObject jsonObject,String cookie,String token) {
        Integer pageNum = jsonObject.getInteger("page");
        Integer pageSize = jsonObject.getInteger("limit");

        QueryWrapper<ComplianceChackTable> queryWrapper = new QueryWrapper<>();
        String orgIdbank = StringUtil.emptyStr(jsonObject.getString("orgIdbank"));
        boolean isQuery = jsonObject.containsKey("isQuery") ? jsonObject.getBoolean("isQuery") : false;

        String chackType = jsonObject.getString("checkType");
        if (StringUtils.isNotBlank(chackType)){
            queryWrapper.eq("check_type", chackType);
        }

        String needsRectification = jsonObject.getString("needsRectification");
        if (StringUtils.isNotBlank(needsRectification)){
            queryWrapper.eq("needs_rectification", needsRectification);
        }

        String itemName = jsonObject.getString("itemName");
        if (StringUtils.isNotBlank(itemName)){
            queryWrapper.like("item_name", itemName);
        }


        String fuzzyValue = jsonObject.getString("fuzzyValue");
        if (StringUtils.isNotBlank(fuzzyValue)){
            queryWrapper.like("item_name", fuzzyValue);
        }

        String currentTaskType = jsonObject.getString("currentTaskType");
        if (StringUtils.isNotBlank(currentTaskType)){
            queryWrapper.and(wrapper -> wrapper.eq("current_task_type", currentTaskType).or().isNull("current_task_type"));

        }
        if (isQuery){
            Long functionId = DataAuthUtils.getFunctionIdByCode("audit_trail_index");
            DataAuthUtils.dataPermSql(queryWrapper, null, functionId, orgIdbank);
        } else {
            String orgId = jsonObject.getString("orgId");
            if (StringUtils.isNotBlank(orgId)){
                SgHrOrgUnitB hrOrgUnitB = hrOrgUnitBService.getOne(new QueryWrapper<SgHrOrgUnitB>().eq("UNIT_ID", orgId));
                if (hrOrgUnitB != null){
                    String unitseq = hrOrgUnitB.getUnitseq();
                    String[] split = unitseq.split("\\.");
                    queryWrapper.eq("inspected_uint_code", split[split.length - 2]);
                }
            }
            queryWrapper.or().eq("create_org_id", orgIdbank);

        }
        queryWrapper.orderByDesc("create_time");
        //queryWrapper.and(p -> p.eq("create_psn_id",String.valueOf(LoginHelper.getUserId())).or().like("inspected_psn_code",String.valueOf(LoginHelper.getUserId())));
        Page<ComplianceChackTable> tablePage = new Page<>();
        tablePage.setCurrent(pageNum);
        tablePage.setSize(pageSize);
        Page<ComplianceChackTable> page = complianceChackMapper.selectPage(tablePage, queryWrapper);
//        ArrayList<ComplianceChackTable> chackTables = new ArrayList<>();
//        page.getRecords().stream().forEach(complianceChackTable -> {
//            ComplianceChackTable updated = updateProcess(complianceChackTable.getId(), cookie, token);
//            if (updated != null){
//                chackTables.add(updated);
//            } else {
//                chackTables.add(complianceChackTable);
//            }
//        });
        return page;
    }

    @Override
    public boolean deleteById(String id) {
        boolean b = super.removeById(id);
        return b;
    }

    @Override
    public void sendMessageForCreater(ComplianceChackEntity complianceChackEntity,String pageName) {
        //发送消息
        JSONObject noticeJson = new JSONObject();
        noticeJson.put("dataState", "view");
        noticeJson.put("dataId", complianceChackEntity.getId());
        noticeJson.put("functionId", "Risk_Warning_Receive," + complianceChackEntity.getId());
        noticeJson.put("isNotice", true);
        List<String> receiveOrgIds = new ArrayList<>();
        //获取被检查单位负责人信息
        List<String> userIds = new ArrayList<>();
        userIds.add(complianceChackEntity.getCreatePsnId());
        for (String userId : userIds) {
            //查询用户与组织信息
            List<HrUserUnitm> hrUserUnitms = hrUserUnitmService.list(new QueryWrapper<HrUserUnitm>().eq("USER_ID", userId));
            if (hrUserUnitms.size() > 0) {
                HrUserUnitm hrUserUnitm = hrUserUnitms.get(0);
                receiveOrgIds.add(String.valueOf(hrUserUnitm.getUnitId()));
            }
        }
        sgSysNoticeService.createNotices(complianceChackEntity.getChackInfo().getItemName(),"合规检查", NoticeType.HGYXJC_NOTICE,complianceChackEntity.getId(),pageName,noticeJson.toJSONString(),Long.parseLong(complianceChackEntity.getCreateOrgId()),receiveOrgIds,false);
    }

    private ComplianceChackEntity convertEntity(ComplianceChackTable complianceChackTable,String cookie,String cosfToken){
        isViewModelByLoginRole(complianceChackTable);
//        if (StringUtils.isNotBlank(cookie) && StringUtils.isNotBlank(cosfToken)){
//            updateProcess(complianceChackTable.getId(),cookie,cosfToken);
//        }

        ComplianceChackEntity complianceChackEntity = new ComplianceChackEntity();
        BeanUtils.copyProperties(complianceChackTable,complianceChackEntity);
        complianceChackEntity.setCurrentProcess(JSONObject.parseObject(complianceChackTable.getCurrentProcess()));
        complianceChackEntity.setCurrentProcessType(ProcessType.getObjByKey(complianceChackTable.getCurrentProcessType()));
        complianceChackEntity.setCurrentTaskType(ComplianceChackTaskType.fromKey(complianceChackTable.getCurrentTaskType()));

        //基础信息
        ComplianceChackEntity.ChackInfo chackInfo = complianceChackEntity.new ChackInfo();
        BeanUtils.copyProperties(complianceChackTable,chackInfo);
        chackInfo.setCheckType(ComplianceChackType.getByKey(complianceChackTable.getCheckType()));
        complianceChackEntity.setChackInfo(chackInfo);

        //合规检查结果
        ComplianceChackEntity.ReviewResult reviewResult = complianceChackEntity.new ReviewResult();
        BeanUtils.copyProperties(complianceChackTable,reviewResult);
        complianceChackEntity.setReviewResult(reviewResult);

        //制定整改计划
        ComplianceChackEntity.RectificationPlan rectificationPlan = complianceChackEntity.new RectificationPlan();
        BeanUtils.copyProperties(complianceChackTable,rectificationPlan);
        //查询整改计划
        List<ComplianceChackRectificationPlan> plansList = chackRectificationPlanService.list(new QueryWrapper<ComplianceChackRectificationPlan>().eq("chack_id", complianceChackEntity.getId()));
        if (plansList == null){
            rectificationPlan.setPlans(new ArrayList<>());
        } else {
            rectificationPlan.setPlans(plansList);
        }
        complianceChackEntity.setRectificationPlan(rectificationPlan);

        //整改结果反馈
        ComplianceChackEntity.RectificationResult rectificationResult = complianceChackEntity.new RectificationResult();
        BeanUtils.copyProperties(complianceChackTable,rectificationResult);
        rectificationResult.setRectificationStatus(RectificationStatus.fromKey(complianceChackTable.getRectificationStatus()));
        complianceChackEntity.setRectificationResult(rectificationResult);
        return complianceChackEntity;
    }

    //判断任务流程
    private static ComplianceChackTaskType getTaskType(ComplianceChackTable complianceChackTable,ComplianceChackTaskType complianceChackTaskType, ProcessType processType) {
        if (processType == null){
            processType = ProcessType.UN_START;
        }
        if (complianceChackTaskType == null){
            return ComplianceChackTaskType.ISSUE_CHECK_TASK;
        } else if (complianceChackTaskType.getKey().equals(ComplianceChackTaskType.ISSUE_CHECK_TASK.getKey()) && processType.getKey().equals(ProcessType.COMPLETED.getKey())){
            return ComplianceChackTaskType.COMPLIANCE_INSPECTION_RESULTS;
        }else if (complianceChackTaskType.getKey().equals(ComplianceChackTaskType.COMPLIANCE_INSPECTION_RESULTS.getKey()) && processType.getKey().equals(ProcessType.COMPLETED.getKey())){
            if (complianceChackTable.getNeedsRectification() == null){
                return complianceChackTaskType;
            }
            //判断是否需要整改
            if (complianceChackTable.getNeedsRectification()) {
                return ComplianceChackTaskType.DESIGNATED_RECTIFICATION_PLAN;
            } else {
                return ComplianceChackTaskType.HAVE_BEEN_FILED;
            }
        }else if (complianceChackTaskType.getKey().equals(ComplianceChackTaskType.DESIGNATED_RECTIFICATION_PLAN.getKey()) && processType.getKey().equals(ProcessType.COMPLETED.getKey())){
            return ComplianceChackTaskType.RECTIFICATION_RESULT_FEEDBACK;
        }else if (complianceChackTaskType.getKey().equals(ComplianceChackTaskType.RECTIFICATION_RESULT_FEEDBACK.getKey()) && processType.getKey().equals(ProcessType.COMPLETED.getKey())){
            return ComplianceChackTaskType.HAVE_BEEN_FILED;
        }else {
            return complianceChackTaskType;
        }
    }

    //获取各个模块不同的登录角色是否要展示
    private void isViewModelByLoginRole(ComplianceChackTable complianceChackTable) {
        //获取当前登录人信息
        Long unitId = LoginHelper.getUnit_ID();
        SgHrOrgUnitB hrOrgUnitB = hrOrgUnitBService.getOne(new QueryWrapper<SgHrOrgUnitB>().eq("UNIT_ID", unitId).eq("UNIT_TYPE", "psn"));
        //获取当前任务状态
        ComplianceChackTaskType complianceChackTaskType = ComplianceChackTaskType.fromKey(complianceChackTable.getCurrentTaskType());
        //判断是发起人，还是被检查单位文书
        Long value = Long.valueOf(complianceChackTable.getCreateOrgId());
        if (complianceChackTaskType == null){
            complianceChackTable.setChackInfoIsView(true);
            complianceChackTable.setReviewResultIsView(false);
            complianceChackTable.setRectificationPlanIsView(false);
            complianceChackTable.setRectificationResultPlanIsView(false);
        } else if (hrOrgUnitB.getUnitId().longValue() == value.longValue()){
            //发起人
            switch (complianceChackTaskType){
                case ISSUE_CHECK_TASK:  //发布检查任务
                    complianceChackTable.setChackInfoIsView(true);
                    complianceChackTable.setReviewResultIsView(false);
                    complianceChackTable.setRectificationPlanIsView(false);
                    complianceChackTable.setRectificationResultPlanIsView(false);
                    break;
                case COMPLIANCE_INSPECTION_RESULTS: //合规检查结果
                case DESIGNATED_RECTIFICATION_PLAN:  //指定整改计划
                    complianceChackTable.setChackInfoIsView(true);
                    complianceChackTable.setReviewResultIsView(true);
                    complianceChackTable.setRectificationPlanIsView(false);
                    complianceChackTable.setRectificationResultPlanIsView(false);
                    break;
                case RECTIFICATION_RESULT_FEEDBACK:  //整改结果反馈
                    complianceChackTable.setChackInfoIsView(true);
                    complianceChackTable.setReviewResultIsView(true);
                    complianceChackTable.setRectificationPlanIsView(true);
                    complianceChackTable.setRectificationResultPlanIsView(false);
                    break;
                case HAVE_BEEN_FILED:  //已归档
                    complianceChackTable.setChackInfoIsView(true);
                    complianceChackTable.setReviewResultIsView(true);
                    complianceChackTable.setRectificationPlanIsView(true);
                    complianceChackTable.setRectificationResultPlanIsView(true);
                    break;
            }
        } else {
            //被检查文书
            switch (complianceChackTaskType){
                case ISSUE_CHECK_TASK:  //发布检查任务
                    complianceChackTable.setChackInfoIsView(false);
                    complianceChackTable.setReviewResultIsView(false);
                    complianceChackTable.setRectificationPlanIsView(false);
                    complianceChackTable.setRectificationResultPlanIsView(false);
                    break;
                case COMPLIANCE_INSPECTION_RESULTS: //合规检查结果
                    complianceChackTable.setChackInfoIsView(true);
                    complianceChackTable.setReviewResultIsView(false);
                    complianceChackTable.setRectificationPlanIsView(false);
                    complianceChackTable.setRectificationResultPlanIsView(false);
                    break;
                case DESIGNATED_RECTIFICATION_PLAN:  //指定整改计划
                    complianceChackTable.setChackInfoIsView(true);
                    complianceChackTable.setReviewResultIsView(true);
                    complianceChackTable.setRectificationPlanIsView(true);
                    complianceChackTable.setRectificationResultPlanIsView(false);
                    break;
                case RECTIFICATION_RESULT_FEEDBACK:  //整改结果反馈
                    complianceChackTable.setChackInfoIsView(true);
                    complianceChackTable.setReviewResultIsView(true);
                    complianceChackTable.setRectificationPlanIsView(true);
                    complianceChackTable.setRectificationResultPlanIsView(true);
                    break;
                case HAVE_BEEN_FILED:  //已归档
                    complianceChackTable.setChackInfoIsView(true);
                    complianceChackTable.setReviewResultIsView(true);
                    complianceChackTable.setRectificationPlanIsView(true);
                    complianceChackTable.setRectificationResultPlanIsView(true);
                    break;
            }
        }

    }
}

