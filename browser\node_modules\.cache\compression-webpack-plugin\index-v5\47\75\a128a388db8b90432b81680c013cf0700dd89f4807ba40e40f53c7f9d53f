
e0bcaa54b171bbdbad340b99ef04a22fa0f2772f	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.16.1754018536329.js\",\"contentHash\":\"72a7853bb86e0ecfc1e8b1ea683ecd5d\"}","integrity":"sha512-nAj38Mz+yVaaOqEKYaIHRmDCz8eSCsBoirVm51SvbuPjMBfsEWRgt46i+eqq95XCat0tlFSBQdSSbu9oZnnXJA==","time":1754018575958,"size":117670}