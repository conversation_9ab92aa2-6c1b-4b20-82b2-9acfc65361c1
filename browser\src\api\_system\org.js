import {request} from '@/api/index'

export default {

    query(data) {
        return request({
            url: '/sys_org/query',
            method: 'post',
            data
        })
    },
    queryUnit(data) {
        return request({
            url: '/sys_org/queryUnit',
            method: 'post',
            data
        })
    },
    querySeachData(data) {
        return request({
            url: '/sys_org/query_seach_data',
            method: 'post',
            data
        })
    },
    queryDefault<PERSON>eys(data) {
        return request({
            url: '/sys_org/query_default_keys',
            method: 'post',
            data
        })
    },
    queryCurrentInfo(data) {
        return request({
            url: '/sys_org/queryCurrentInfo',
            method: 'post',
            data
        })
    },
    queryCurrentAllOrg(data) {
        return request({
            url: '/sys_org/queryCurrentAllOrg',
            method: 'post',
            data
        })
    },
    queryFullName(data) {
        return request({
            url: '/sys_org/queryFullName',
            method: 'post',
            data
        })
    },
    queryPsnByParentId(data) {
        return request({
            url: '/sys_org/queryPsnByParentId',
            method: 'post',
            data
        })
    },
    queryLeaderList(data) {
        return request({
          url: '/sys_org/queryLeaderList',
          method: 'post',
          data
        })
    },
    roleCheck(data) {
        return request({
            url: '/sys_org/roleCheck',
            method: 'post',
            data
        })
    },
    queryMainDataOrgIds(data) {
        return request({
            url: '/sys_org/queryMainDataOrgIds',
            method: 'post',
            data
        })
    },
    queryOrgOther(data) {
        return request({
            url: '/sys_org/queryOrgOther',
            method: 'post',
            data
        })
    },
    knlLogin(data) {
        return request({
            url: '/sys_org/KnlLogin',
            method: 'post',
            data
        })
    },
    queryOrgUser(data) {
        return request({
            url: '/sys_org/queryOrgUser',
            method: 'post',
            data
        })
    },
    queryOrgUnit(data) {
        return request({
            url: '/sys_org/queryOrgUnit',
            method: 'post',
            data
        })
    },
    queryOrgDetail(data) {
      return request({
          url: '/sys_org/queryOrgDetail',
          method: 'post',
          data
      })
    },
}
