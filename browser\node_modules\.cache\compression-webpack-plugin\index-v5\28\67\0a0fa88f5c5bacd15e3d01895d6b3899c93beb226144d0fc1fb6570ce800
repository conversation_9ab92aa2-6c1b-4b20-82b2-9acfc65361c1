
b954b08674e5455dcd4e1e37a0fc31c91957a7e4	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.88.1754018536329.js\",\"contentHash\":\"10741635347680960630786e1fb864a1\"}","integrity":"sha512-n8NYyQFuRjXdNLxGGJT3t/szbF1pUUgYV1JL3mE24rKDJBogTusPJhZ59DVqMtg9y53UXdgp97pd148ISKXk8g==","time":1754018575956,"size":40563}