
8092624056ed1b82534b1378a7c8700b9ef5dce2	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.400.1754018536329.js\",\"contentHash\":\"4ed0f8aa43eeac57292bb4e057110f94\"}","integrity":"sha512-e7vkb5HI4ygDU5MZ/dpLkwZi2IjQtFPxCtbd+yk8gLXP3itQE9SNOkMy1t6xEmjr3ELp+6EvAzCsXbsCKQfzuA==","time":1754018576024,"size":133912}