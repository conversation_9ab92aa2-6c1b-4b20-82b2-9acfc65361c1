
c95742a6f14d78c74ee89a54af9ed443a311c11a	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.45.1754018536329.js\",\"contentHash\":\"b99923926add1ae7f6af1d9e26ad0528\"}","integrity":"sha512-y2W5cODeq+BmIWsX91oDJhHCvjllX32IOP6rk1VPEzoeTyiWFdJKgNFUjnmp/VRCLihgZcly1bdoPWeqDNdxYw==","time":1754018575955,"size":37488}