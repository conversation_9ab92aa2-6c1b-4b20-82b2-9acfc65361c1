
e16452af4f3146f8dfd91060c9189d176984e082	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.470.1754018536329.js\",\"contentHash\":\"fef3f6cb075e63b26b33456d77de4585\"}","integrity":"sha512-pyLWYFL3Zs7CkvK4l5b7Fxa2+TN7AMSaahxEGYJogumaug7xK/9nHffSM45ptVU7UTVQhSCX8VNuMZTDSUGwwg==","time":1754018575977,"size":108075}