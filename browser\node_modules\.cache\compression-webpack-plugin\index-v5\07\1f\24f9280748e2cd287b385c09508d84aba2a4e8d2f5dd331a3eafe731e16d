
9fbb37e2c2c17b6112b892d9e36492a5601419cd	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.95.1754018536329.js\",\"contentHash\":\"79c69fc2bf026e142a2392d19e35adcb\"}","integrity":"sha512-wk2eTMA9qwA5WZEYNBovibnRx7+he2U43Q+Fz01bBHzyomDGRoi1ig0gmiFCIibsJOv6LK82oqxXzr2WRgbGBA==","time":1754018576236,"size":417597}