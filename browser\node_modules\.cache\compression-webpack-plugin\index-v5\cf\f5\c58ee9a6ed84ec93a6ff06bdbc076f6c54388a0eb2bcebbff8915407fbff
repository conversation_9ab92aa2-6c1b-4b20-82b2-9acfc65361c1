
81db234a19dfc57cc24965968dfeae01fbe321e1	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.220.1754018536329.js\",\"contentHash\":\"7f9d93bdf77d51fd249af3988b2535c9\"}","integrity":"sha512-ZORM0t4NpVGmUznvCTyTk+RETf1uhl43cCxwh8GQClHIYwHy8bfKL9NdxVD7NyUkR2rkvrOdQ1u4VK+XWUuK2A==","time":1754018575992,"size":164947}