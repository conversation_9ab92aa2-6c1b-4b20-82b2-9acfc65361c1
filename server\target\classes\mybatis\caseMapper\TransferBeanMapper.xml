<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.klaw.dao.caseDao.TransferBeanMapper">
    <resultMap id="transferBeanMap" type="java.util.Map">
        <id property="id" column="id" />
        <result column="TRANSFERER_ID" property="transfererId" />
        <result column="TRANSFERER_NAME" property="transfererName" />
        <result column="TRANSFERER_DEPT_ID" property="transfererDeptId" />
        <result column="TRANSFERER_DEPT_NAME" property="transfererDeptName" />
        <result column="TRANSFERER_FULL_ID" property="transfererFullId" />
        <result column="BTRANSFERER_ID" property="btransfererId" />
        <result column="BTRANSFERER_NAME" property="btransfererName" />
        <result column="BTRANSFERER_DEPT_ID" property="btransfererDeptId" />
        <result column="BTRANSFERER_DEPT_NAME" property="btransfererDeptName" />
        <result column="BTRANSFERER_FULL_ID" property="btransfererFullId" />
        <result column="TRANSFERER_MODE" property="transfererMode" />
        <result column="TRANSFERER_MODE_NAME" property="transfererModeName" />
        <result column="TRANSFERER_RANGE" property="transfererRange" />
        <result column="DATA_STATE" property="dataState" />
        <result column="DATA_STATE_CODE" property="dataStateCode" />


        <result column="create_ogn_id" property="createOgnId"></result>
        <result column="create_ogn_name" property="createOgnName"></result>
        <result column="create_dept_id" property="createDeptId"></result>
        <result column="create_dept_name" property="createDeptName"></result>
        <result column="create_psn_id" property="createPsnId"></result>
        <result column="create_psn_name" property="createPsnName"></result>
        <result column="create_psn_full_id" property="createPsnFullId"></result>
        <result column="create_psn_full_name" property="createPsnFullName"></result>
        <result column="create_time" property="createTime"></result>
    </resultMap>
    <select id="queryTransferBean" parameterType="java.util.Map" resultMap="transferBeanMap">
        select distinct m.ID, m.TRANSFERER_ID, m.TRANSFERER_NAME, m.TRANSFERER_DEPT_ID, m.TRANSFERER_DEPT_NAME, m.TRANSFERER_FULL_ID, m.BTRANSFERER_ID, m.BTRANSFERER_NAME,
        m.BTRANSFERER_DEPT_ID, m.BTRANSFERER_DEPT_NAME, m.BTRANSFERER_FULL_ID, m.CREATE_OGN_ID, m.CREATE_OGN_NAME, m.CREATE_DEPT_ID, m.CREATE_DEPT_NAME,
        m.CREATE_PSN_ID, m.CREATE_PSN_NAME, m.CREATE_PSN_FULL_ID, m.CREATE_PSN_FULL_NAME, m.CREATE_TIME, d.TRANSFERER_MODE, d.TRANSFERER_MODE_NAME,
        d.TRANSFERER_RANGE,d.DATA_STATE, d.DATA_STATE_CODE
        from SG_TRANSFER_DETAIL m left join SG_TRANSFER_DETAIL d on m.id=d.master_id
        <where>
            ${ew.sqlSegment}
        </where>
    </select>

    <select id="getMode" parameterType="java.util.Map" resultType="com.klaw.entity.caseBean.TransferBean">
        select distinct  TRANSFERER_MODE, TRANSFERER_MODE_NAME
        from SG_TRANSFER_DETAIL
        <where>
            ${ew.sqlSegment}
        </where>
    </select>

    <select id="queryTransferId" resultType="com.klaw.entity.caseBean.TransferDetailBean">
        select b.transferer_Datas from SG_TRANSFER a inner join SG_TRANSFER_DETAIL b on a.id = b.master_Id
        where a.data_state = '已共享' and a.type = 'transfer' and a.TRANSFERER_RANGE ='part' and a.create_psn_full_id =#{createPsnFullId}
    </select>

    <select id="queryTransferAll" resultType="com.klaw.entity.caseBean.TransferBean">
         select * from SG_TRANSFER_DETAIL a where a.data_state = '已共享' and a.type = 'transfer'
         and a.create_psn_full_id = #{createPsnFullId}
    </select>
</mapper>
