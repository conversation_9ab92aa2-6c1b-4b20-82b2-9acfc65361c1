spring.profiles.active=dev
#dev
spring.mvc.dispatch-options-request=true
## item of list, split with comma ","
## cors configuration start
# default *;if credentials is true,the cors origins cannot be *,must be real origins
mcp.cors.origins=http://localhost:8082,http://localhost:8081,http://localhost:8080,http://************,http://fwtest.btsteel.com:8001
# default *
mcp.cors.headers=*
# default *
mcp.cors.methods=*
# default /**cd
mcp.cors.mapping.urls=/**
# default true
mcp.cors.allow.credentials=true
## cors configuration end

## csrf configuration start
# default true
mcp.csrf.check.allowed=true
# default null
mcp.csrf.exclude.urls=/login,/loginInfo,/getToken,/websocket/**,/i/api,/kaptcha,/sso/**,/i/api/v1/3rd/**,/riskWarning**,/contractAppSupple/importContractSuppleData,/complianceChack/**,/oaGet/**
# default POST,PUT,PATCH,DELETE
mcp.csrf.http.methods=POST,PUT,PATCH,DELETE,OPTIONS
## csrf configuration end

# customize password encoder, the value must be whole class name; default org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder
mcp.security.password.encoder=com.sgai.mcp.security.PasswordManager
# none security, can be requested by any one; default null
mcp.security.ignore.urls=/resources/**,/lib/**,/ws/**,/sys/config/query/title,/i/api,/loginInfo,/kaptcha,/sso/getAuthCode,/mcp/sso/getAuthCode,/sso/getAuthCode1,/mcp/sso/getAuthCode1
# customize the login success handler, the value must be whole class name; default null
mcp.login.handler.success=com.sgai.mcp.security.handler.CustomAuthenticationSuccessHandler
# customize the login failure handler, the value must be whole class name; default null
mcp.login.handler.failure=com.sgai.mcp.security.handler.LoginFailureHandler
# customize filters before logout, the value must be whole class name; default null
mcp.logout.handler.success=com.sgai.mcp.security.handler.CustomLogoutSuccessHandler

## captcha config start
# path of kaptcha config (captcha use kaptcha lib, used by login etc.); default null
mcp.kaptcha.properties.path=
# the urls will be valid captcha; default /login
mcp.captcha.valid.urls=/login
# the submit captcha code name to controller used by login etc.; default code
mcp.captcha.code.name=verifiCode
# captcha will be timeout within seconds; default 20
mcp.captcha.timeout.seconds=20
## captcha config end

mybatis-plus.config-location=classpath:mybatis/mybatis-configuration.xml
mybatis-plus.mapper-locations=classpath*:mybatis/**/*Mapper.xml
mybatis.mapper-locations=classpath*:mybatis/**/*Mapper.xml,META-INF/modeler-mybatis-mappings/**/*.xml
mybatis-plus.global-config.db-config.update-strategy=ignored
mybatis-plus.global-config.enable-sql-runner=true
mcp.mybatis.interceptors=com.github.pagehelper.PageInterceptor,encrypt.sgai.mcp.dynamic.interceptor.DynamicResultInterceptor,com.sgai.mcp.core.interceptor.RequestContextInterceptor,encrypt.sgai.mcp.dynamic.interceptor.DynamicMultiLanguageInterceptor,com.sgai.mcp.core.interceptor.MultiLanguageInterceptor,com.sgai.mcp.core.interceptor.SecurityTokenInterceptor,com.sgai.mcp.core.interceptor.OvnInterceptor,com.sgai.mcp.core.interceptor.AuditInterceptor,com.sgai.mcp.core.interceptor.CacheJoinInterceptor,com.sgai.mcp.core.interceptor.DataPermissionInterceptor,com.sgai.mcp.core.interceptor.JoinExampleQueryInterceptor,com.sgai.mcp.easytv.interceptor.TvFieldInterceptor,com.sgai.mcp.core.interceptor.SqlPrinterInterceptor

mcp.debounce.exclude.urls=/sys_dict/showSelect,/sys_dict/showAllSelect,/sys_dict/queryDataByArray,/sys_dict/querySolidByCode,/CaseLedger/queryFastQueryCase,/sys_doc/download

# ������������û���ҵ���������ת������Ա����
flowable.approver.default.id=10001

spring.thymeleaf.prefix=classpath:/static
spring.thymeleaf.suffix=.html

logging.level.com.sgai.mcp.account.mapper=debug
logging.file.path=./logs

# �������������Ϊ 16kb
server.max-http-header-size=4048576
#server.tomcat.max-http-form-post-size=********
spring.servlet.multipart.max-file-size=5GB
spring.servlet.multipart.max-request-size=5GB
#server.tomcat.max-http-form-post-size=-1
server.servlet.session.cookie.name=SESSIONID_MCP
#server.servlet.session.cookie.httpOnly=false

#encoding httpRequest httpResponse
spring.http.encoding.force=true
spring.http.encoding.charset=UTF-8
spring.http.encoding.enabled=true

server.tomcat.max-http-form-post-size=********
#������߳�����Ĭ��200��
server.tomcat.max-threads=1600
#���������Ĭ����10000
server.tomcat.max-connections=20000
#��С���������߳�����Ĭ��10��
server.tomcat.min-spare-threads=100
#�ȴ����г��ȣ�Ĭ��100��
server.tomcat.acceptCount=1500

### older config.properties
env.code=DEV

mapper.ORDER=BEFORE
#mapper.IDENTITY=Oracle
#mapper.ORDER=AFTER
mapper.IDENTITY=MySQL
#mapper.IDENTITY=DM

#default dateformat
spring.jackson.date-format=yyyy-MM-dd HH:mm:ss
spring.jackson.time-zone=GMT+8
spring.jackson.default-property-inclusion=non_null

spring.datasource.druid.validation-query=select 1 from dual
spring.datasource.druid.test-on-borrow=false
spring.datasource.druid.test-on-return=false
spring.datasource.druid.test-while-idle=true

#���Ʒ�����¼
wk.oauth.url=https://law.wkinfo.com.cn/oauth/thirdParty/login?appId=%s&cipher=%s
wk.appId=10216
wk.secret=SGs5OPTMtf8keNf9

spring.devtools.restart.enabled=true
spring.devtools.restart.additional-paths=src/main/java
spring.devtools.restart.exclude=WEB-INF/**
spring.freemarker.cache=false
spring.freemarker.charset=UTF-8
spring.freemarker.template-loader-path=classpath:/template/


jetcache.statIntervalMinutes=15
jetcache.areaInCacheName=false
jetcache.remote.default.type=redis
jetcache.remote.default.keyConvertor=fastjson
jetcache.remote.default.valueEncoder=java
jetcache.remote.default.valueDecoder=java
jetcache.remote.default.poolConfig.minIdle=5
jetcache.remote.default.poolConfig.maxIdle=20
jetcache.remote.default.poolConfig.maxTotal=50
jetcache.remote.default.host=127.0.0.1
jetcache.remote.default.port=6379


