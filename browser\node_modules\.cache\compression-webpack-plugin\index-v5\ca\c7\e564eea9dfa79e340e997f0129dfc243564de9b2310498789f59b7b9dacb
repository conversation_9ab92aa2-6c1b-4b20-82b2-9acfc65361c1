
8973507a54715d3bc2270111acdc7d6a581130b3	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.92.1754018536329.js\",\"contentHash\":\"7cc7880c6c1a9a44b6b4ac5feee480a7\"}","integrity":"sha512-UoY5YCECdPTiA81cEPh/50eJ8AFSKZBExmsqactMFcDKsuanPecbp+ApU+JzhShfD4YJ/xVnqJGHaXYpEsKFKA==","time":1754018576136,"size":323617}