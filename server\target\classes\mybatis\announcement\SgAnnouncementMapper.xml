<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.klaw.dao.announcement.SgAnnouncementMapper">

    <select id="queryListPage" resultType="com.klaw.entity.announcement.SgAnnouncement">
        select *
        from sg_announcement
        <where>
            <if test="data.title != null and data.title != ''">
                and title like concat('%', #{data.title}, '%')
            </if>
            <if test="data.isPublished != null and data.isPublished != ''">
                and is_published = #{data.isPublished}
            </if>
        </where>
    </select>

    <select id="listByEmp" resultType="com.klaw.entity.announcement.SgAnnouncement">
        select *
        from (
        SELECT SG.*,
        (select decode(count(1), 0, 'N', 'Y')
         from SG_ANNOUNCEMENT_RECEIVER SAR
        where SAR.ANNOUNCEMENT_SID = SG.ID
        <if test="data.createPsnFullId != null and data.createPsnFullId != ''">
            and SAR.RECEIVER_CODE = #{data.createPsnFullId}
        </if>
        ) as IS_READ
        FROM SG_ANNOUNCEMENT SG
        WHERE IS_PUBLISHED = 'Y'
        <if test="data.title != null and data.title != ''">
            and title like concat('%', #{data.title}, '%')
        </if>
        ) T
        order by t.IS_READ, T.CREATE_TIME desc
    </select>
</mapper>