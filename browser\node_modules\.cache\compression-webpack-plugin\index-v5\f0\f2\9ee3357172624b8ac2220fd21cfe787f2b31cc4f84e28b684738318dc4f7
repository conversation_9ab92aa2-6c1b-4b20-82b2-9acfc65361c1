
3435946b87b8e57205aee855a627877abf24fff7	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.389.1754018536329.js\",\"contentHash\":\"124b4b4355d99bf3202956e22b4bf100\"}","integrity":"sha512-W4MCXAmOnLB/h3uaPWGK3/MRoKAB4BaF5higZulhGDgWnPnhjMeF8hak4wmHPTn/xTC5dFzhVnTDYOR6fj6Ofw==","time":1754018576026,"size":170581}