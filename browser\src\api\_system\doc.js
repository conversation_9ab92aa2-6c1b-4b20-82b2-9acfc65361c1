import {request} from '@/api/index'

export default {

    download(docId) {
        return request({
            url: '/sys_doc/download/' + docId,
            method: 'get',
            responseType: 'blob'
        })
    },
    // download(docId) {
    //     return request({
    //         url: '/sys_doc/download/' + docId,
    //         method: 'get',
    //     })
    // },
    deleteFile(data) {
        return request({
            url: '/sys_doc/delete',
            method: 'post',
            data: { docId: data },
        })
    },
    upload(data, tt) {
        return request({
            url: '/sys_doc/upload',
            method: 'post',
            data: data,
            onUploadProgress: tt
        })
    },
    queryTree(data) {
        return request({
            url: '/sys_doc/queryTree',
            method: 'post',
            data: data
        })
    },
    search(data) {
        return request({
            url: '/sys_doc/search',
            method: 'post',
            data
        })
    },
    queryDataById(data) {
        return request({
            url: '/sys_doc/queryDataById',
            method: 'post',
            data
        })
    },
    // getFilePath(docId) {
    //     return request({
    //         url: '/sys_doc/getFilePath',
    //         method: 'post',
    //         data: { docId: docId }
    //     })
    // },
    getEditFilePath(docId,orgId) {
        return request({
            url: '/i/api/v1/3rd/edit/'+docId+'/'+orgId,
            method: 'get',
        })
    },
    getFilePath(docId,orgId) {
        return request({
            url: '/i/api/v1/3rd/preview/'+docId+'/'+orgId,
            method: 'get',
        })
    },
    convertToPDF(data) {
        return request({
            url: '/i/api/v1/3rd/reverse/' + data,
            method: 'get',
            responseType: 'blob',
        })
    },
    convertToWatermark(data) {
        return request({
            url: '/sys_doc/convertToWatermark/',
            method: 'post',
            responseType: 'blob',
            data
        })
    },
    uploadWithName(data, tt) {
        return request({
            url: '/sys_doc/uploadWithName',
            method: 'post',
            data: data,
            onUploadProgress: tt
        })
    },
    /**
     * 文件归集
     * @param data
     * @returns {*}
     */
    fileDown(data) {
        return request({
            url: '/sys_doc/fileDown',
            method: 'post',
            responseType: 'blob',
            data
        })
    },
    downloadURL(docId){
        return request({
            url: '/sys_doc/downloadURL/'+docId,
            method: 'get',
        })
    }
}
