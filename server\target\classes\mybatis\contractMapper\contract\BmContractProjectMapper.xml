<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.klaw.dao.contractDao.contract.BmContractProjectMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.klaw.entity.contractBean.contract.BmContractProject">
        <id column="id" property="id"/>
        <result column="parent_id" property="parentId"/>
        <result column="project_type" property="projectType"/>
        <result column="project_name" property="projectName"/>
        <result column="project_code" property="projectCode"/>
        <result column="child_project_code" property="childProjectCode"/>
        <result column="total_money" property="totalMoney"/>
        <result column="surplus_money" property="surplusMoney"/>
        <result column="assign_money" property="assignMoney"/>
        <result column="create_psn_full_id" property="createPsnFullId"/>
        <result column="create_psn_full_name" property="createPsnFullName"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        bp.id,
        bp.parent_id,
        bp.project_type,
        bp.project_name,
        bp.project_code,
        bp.whether,
        bp.child_project_code,
        bp.occupied_amount,
        bp.total_money,
        sp.surplus_money,
        bp.assign_money,
        bp.project_change_money_type,
        bp.project_change_money_type_code,
        bp.project_this_change_money,
        bp.change_assign_money,
        bp.new_assign_money,
        bp.project_no_perform_money,
        bp.pending_amount,
        bp.create_time,
        bp.create_psn_full_id,
        bp.create_psn_full_name,
        bp.project_type_code
    </sql>
    <select id="listInfo" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM bm_contract_project bp,
        sg_project_manage sp
        WHERE bp.project_code = sp.project_code
        and bp.parent_id = #{parentId}
        ORDER BY bp.create_time DESC
    </select>

</mapper>
