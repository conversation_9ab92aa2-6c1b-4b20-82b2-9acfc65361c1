<template>
  <FormWindow>
    <el-container v-loading="loading" style="height: calc(100vh - 150px);">
      <el-main v-if="view === 'old'">
        <el-scrollbar style="height: 100%" wrap-style="overflow-x: hidden;">
          <el-form ref="dataForm" style="padding-right: 10px;"
                   :model="mainData"
                   :rules="dataState !== 'view' ? rules : {}"
                   label-width="100px"
                   :style="dataState !== utils.formState.VIEW ? 'margin-right: 50px;' : ' margin-right: 0px;' "
          >
            <span style="text-align: left;font-size: 20px;margin-left: 43%;font-weight: 900;">律所律师信息变更详情单</span>
            <div style="margin-top: 10px;" v-if="dataState !== 'view'">
              <div style="margin-top: 10px;">
                <el-row>
                  <div style="margin: 10px">
                    <span style="font-weight: bold;font-size: 16px;color: #5A5A5F;">会知信息</span>
                  </div>
                  <el-divider></el-divider>
                </el-row>
                <el-row style="margin-top: 10px">
                  <el-col :span="24">
                    <el-form-item label="会知部门" prop="noticeDeptName">
                      <el-input v-if="dataState !== 'view'"
                                v-model="mainData.noticeDeptName"
                                clearable placeholder="请选择" class="input-with-select" disabled>
                        <el-button slot="append" icon="el-icon-search" />
                      </el-input>
                      <span v-else class="viewSpan">{{ mainData.noticeDeptName }}</span>
                    </el-form-item>
                  </el-col>
                </el-row>

                <el-row>
                  <div style="margin: 10px">
                    <span style="font-weight: bold;font-size: 16px;color: #5A5A5F;">变更信息</span>
                  </div>
                  <el-divider></el-divider>
                </el-row>
                <el-row>
                  <el-col :span="24">
                    <el-form-item label="变更类型" prop="changeTypeId"
                                  :rules="{ required: dataState !== 'view' && mainData.operatingType === 'change', message: '请选择变更类型', trigger: 'blur' }">
                      <el-select v-if="dataState !== 'view'" v-model="changeTypeIds" clearable placeholder="请选择"
                                 style="width: 100%" multiple @change="changeTypeChange">
                        <el-option
                            v-for="item in BGLXData"
                            :key="item.id"
                            :label="item.dicName"
                            :value="item.id"
                        />
                      </el-select>
                      <span v-else class="viewSpan">{{ mainData.changeTypeName }}</span>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="24">
                    <el-form-item label="变更原因" prop="changeReason"
                                  :rules="{ required: dataState !== 'view' && mainData.operatingType === 'change', message: '请填写变更原因', trigger: 'blur' }">
                      <el-input v-if="dataState !== 'view'"
                                v-model="mainData.changeReason"
                                type="textarea" placeholder="请输入..."
                                :autosize="{ minRows: 3, maxRows: 6}"
                                maxlength="1000" show-word-limit style="width: 100%" clearable/>
                      <text-span v-else class="viewSpan" :text=" mainData.changeReason"/>
                    </el-form-item>
                  </el-col>
                </el-row>

                <el-row>
                  <div style="margin: 10px">
                    <span style="font-weight: bold;font-size: 16px;color: #5A5A5F;">基本信息</span>
                  </div>
                  <el-divider></el-divider>
                </el-row>

                <el-row style="margin-top: 10px;">
                  <el-col :span="16">
                    <el-form-item label="事项名称" prop="itemName">
                      <el-input v-if="dataState !== 'view'" v-model.trim="mainData.itemName"
                                maxlength="100" show-word-limit style="width: 100%" placeholder="请输入..." clearable/>
                      <span v-else class="viewSpan">{{ mainData.itemName }}</span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="事项编号">
                      <el-input v-if="dataState !== 'view'" v-model.trim="mainData.sequenceCode" disabled
                                show-word-limit style="width: 100%"/>
                      <span v-else class="viewSpan">{{ mainData.sequenceCode }}</span>
                    </el-form-item>
                  </el-col>
                </el-row>

                <el-row>
                  <el-col :span="24">
                    <el-form-item label="律所名称" prop="lawyerFirm">
                      <el-input
                          v-if="dataState !== 'view'"
                          v-model.trim="mainData.lawyerFirm"
                          maxlength="50" show-word-limit style="width: 100%"
                          placeholder="请输入..." clearable/>
                      <span v-else class="viewSpan">{{ mainData.lawyerFirm }}</span>
                    </el-form-item>
                  </el-col>
                </el-row>

                <el-row>
                  <el-col :span="24">
                    <el-form-item label="律所地址">
                      <el-input v-if="dataState !== 'view'"
                                v-model="mainData.registerAddress"
                                maxlength="50" show-word-limit placeholder="请输入..." style="width: 100%" clearable/>
                      <span v-else class="viewSpan">{{ mainData.registerAddress }}</span>
                    </el-form-item>
                  </el-col>
                </el-row>

                <el-row>
                  <el-col :span="8">
                    <el-form-item label="律所类型" prop="lawFirmType">
                      <template>
                        <el-select v-if="dataState !== 'view'"
                                   v-model="mainData.lawFirmType"
                                   clearable placeholder="请选择" style="width: 100%"
                        >
                          <el-option
                              v-for="item in lawFirmTypeData"
                              :key="item.id"
                              :label="item.name"
                              :value="item.name"
                          />
                        </el-select>
                        <span v-else class="viewSpan">{{ mainData.lawFirmType }}</span>
                      </template>
                    </el-form-item>
                  </el-col>

                  <el-col :span="8">
                    <el-form-item label="律所电话" prop="lawFirmPhone">
                      <el-input v-if="dataState !== 'view'"
                                v-model="mainData.lawFirmPhone"
                                maxlength="20" show-word-limit style="width: 100%"
                                placeholder="请输入..." clearable/>
                      <span v-else class="viewSpan">{{ mainData.lawFirmPhone }}</span>
                    </el-form-item>
                  </el-col>

                  <el-col :span="8">
                    <el-form-item label="律所邮箱" prop="lawFirmEmail">
                      <el-input v-if="dataState !== 'view'"
                                v-model="mainData.lawFirmEmail"
                                maxlength="20" show-word-limit style="width: 100%"
                                placeholder="请输入..." clearable/>
                      <span v-else class="viewSpan">{{ mainData.lawFirmEmail }}</span>
                    </el-form-item>
                  </el-col>
                </el-row>

                <el-row>
                  <el-col :span="8">
                    <el-form-item label="负责人" prop="functionary">
                      <el-input v-if="dataState !== 'view'"
                                v-model="mainData.functionary"
                                maxlength="20" show-word-limit style="width: 100%"
                                placeholder="请输入..." clearable/>
                      <span v-else class="viewSpan">{{ mainData.functionary }}</span>
                    </el-form-item>
                  </el-col>

                  <el-col :span="8">
                    <el-form-item label="邮政编码">
                      <el-input v-if="dataState !== 'view'"
                                v-model="mainData.postalCode"
                                maxlength="20" show-word-limit style="width: 100%"
                                placeholder="请输入..." clearable/>
                      <span v-else class="viewSpan">{{ mainData.postalCode }}</span>
                    </el-form-item>
                  </el-col>

                  <el-col :span="8">
                    <el-form-item label="主管机关">
                      <el-input v-if="dataState !== 'view'"
                                v-model="mainData.issuingAuthority"
                                maxlength="30" show-word-limit style="width: 100%"
                                placeholder="请输入..." clearable/>
                      <span v-else class="viewSpan">{{ mainData.issuingAuthority }}</span>
                    </el-form-item>
                  </el-col>
                </el-row>

                <el-row>
                  <el-col :span="8" class="mylabel">
                    <el-form-item label="统一社会信用代码" prop="licenseCode" class="custom-word-break">
                      <span slot="label">统一社会<br>信用代码</span>
                      <el-input v-if="dataState !== 'view'"
                                v-model="mainData.licenseCode"
                                maxlength="20" show-word-limit style="width: 100%" placeholder="请输入..." clearable
                                :disabled="mainData.operatingType !== 'new'" @blur="licenseCodeBlur"/>
                      <span v-else class="viewSpan">{{ mainData.licenseCode }}</span>
                    </el-form-item>
                  </el-col>

                  <el-col :span="8">
                    <el-form-item label="批准文号">
                      <el-input v-if="dataState !== 'view'"
                                v-model="mainData.licenseNumber"
                                maxlength="40" show-word-limit style="width: 100%" placeholder="请输入..." clearable/>
                      <span v-else class="viewSpan">{{ mainData.licenseNumber }}</span>
                    </el-form-item>
                  </el-col>

                  <el-col :span="8">
                    <el-form-item label="设立资产">
                      <el-input v-if="dataState !== 'view'"
                                v-model="mainData.registeredCapital"
                                maxlength="20" show-word-limit style="width: 100%" placeholder="请输入..." clearable/>
                      <span v-else class="viewSpan">{{ mainData.registeredCapital }}</span>
                    </el-form-item>
                  </el-col>
                </el-row>

                <el-row>

                  <el-col :span="8">
                    <el-form-item label="成立时间">
                      <el-date-picker v-if="dataState !== 'view'"
                                      v-model="mainData.foundTime"
                                      value-format="yyyy-MM-dd" type="date" style="width: 100%" clearable/>
                      <span v-else class="viewSpan">{{ mainData.foundTime | parseTime }}</span>
                    </el-form-item>
                  </el-col>

                  <el-col :span="8">
                    <el-form-item label="执业人数">
                      <el-input-number v-if="dataState !== 'view'"
                                       v-model="mainData.workNumber"
                                       :controls="false"
                                       :precision="0"
                                       :max="99999999"
                                       show-word-limit placeholder="只能输入数字" style="width: 100%" clearable/>
                      <span v-else class="viewSpan">{{ mainData.workNumber }}</span>
                    </el-form-item>
                  </el-col>

                  <el-col :span="8">
                    <el-form-item label="申请部门" prop="applyDeptName" class="custom-word-break">
                      <span slot="label">申请部门/<br>所属公司</span>
                      <el-input v-if="dataState !== 'view'"
                                v-model="mainData.applyDeptName"
                                clearable placeholder="请选择" class="input-with-select" disabled>
                        <el-button slot="append" icon="el-icon-search" @click="chooseApprovalDeptClick"/>
                      </el-input>
                      <span v-else class="viewSpan">{{ mainData.applyDeptName }}</span>
                    </el-form-item>
                  </el-col>
                </el-row>

                <el-row>

                  <el-col :span="8">
                    <el-form-item label="年检情况">
                      <template>
                        <el-select v-if="dataState !== 'view'"
                                   v-model="mainData.annualInspectionId"
                                   clearable placeholder="请选择" style="width: 100%"
                                   @change="annualInspectionChange">
                          <el-option
                              v-for="item in NJQKData"
                              :key="item.id"
                              :label="item.dicName"
                              :value="item.id"
                          />
                        </el-select>
                        <span v-else class="viewSpan">{{ mainData.annualInspectionName }}</span>
                      </template>
                    </el-form-item>
                  </el-col>
                </el-row>

                <el-row v-if="mainData.annualInspectionName === '其他情况'">
                  <el-col :span="24">
                    <el-form-item label="年检情况说明" prop="lawyerFirm">
                      <el-input
                          v-if="dataState !== 'view'"
                          v-model.trim="mainData.annualInspectionDesc"
                          maxlength="50" show-word-limit style="width: 100%"
                          placeholder="请输入..." clearable/>
                      <span v-else class="viewSpan">{{ mainData.annualInspectionDesc }}</span>
                    </el-form-item>
                  </el-col>
                </el-row>

                <el-row>
                  <el-col :span="24">
                    <el-form-item label="擅长业务领域">
                      <el-select v-if="dataState !== 'view'"
                                 v-model="beGoodAtDomainIds_1"
                                 multiple placeholder="请选择" style="width: 100%"
                                 @change="beGoodAtDomainChange">
                        <el-option
                            v-for="item in SCLYData"
                            :key="item.id"
                            :label="item.dicName"
                            :value="item.id"
                        />
                      </el-select>
                      <span v-else class="viewSpan">{{ mainData.beGoodAtDomain }}</span>
                    </el-form-item>
                  </el-col>
                </el-row>

                <el-row>
                  <el-col :span="24">
                    <el-form-item label="律所简介">
                      <el-input v-if="dataState !== 'view'"
                                v-model="mainData.introduction"
                                type="textarea" placeholder="请输入..."
                                :autosize="{ minRows: 3, maxRows: 6}"
                                maxlength="1000" show-word-limit style="width: 100%" clearable/>
                      <text-span v-else class="viewSpan" :text=" mainData.introduction"/>
                    </el-form-item>
                  </el-col>
                </el-row>

                <el-row>
                  <el-col :span="24">
                    <el-form-item label="备注">
                      <el-input v-if="dataState !== 'view'"
                                v-model="mainData.remark"
                                type="textarea" placeholder="请输入..."
                                :autosize="{ minRows: 3, maxRows: 6}"
                                maxlength="1000" show-word-limit style="width: 100%" clearable/>
                      <text-span v-else class="viewSpan" :text=" mainData.remark"/>
                    </el-form-item>
                  </el-col>
                </el-row>

                <el-row>
                  <el-col :span="24" prop="businessLicense">
                    <el-form-item label="附件资料" prop="businessLicense">
                      <uploadDoc
                          v-model="mainData.businessLicense"
                          :files.sync="mainData.businessLicense"
                          :disabled="dataState === 'view'"
                          :tips="tip_"
                          :doc-path="docURL"
                      />
                    </el-form-item>
                  </el-col>
                </el-row>
              </div>
            </div>

            <SimpleBoardTitle v-if="dataState === 'view'" title="会知信息" style="margin-top: 5px;">
              <table class="table_content" style="margin-top: 10px;">
                <tbody>
                <tr>
                  <th colspan="2" class="th_label">会知部门</th>
                  <td colspan="22" class="td_value">{{ mainData.noticeDeptName }}</td>
                </tr>
                </tbody>
              </table>
            </SimpleBoardTitle>

            <SimpleBoardTitle v-if="dataState === 'view'" title="变更信息" style="margin-top: 5px;">
              <table class="table_content" style="margin-top: 10px;">
                <tbody>
                <tr>
                  <th colspan="2" class="th_label">变更类型</th>
                  <td colspan="22" class="td_value">{{ mainData.changeTypeName }}</td>
                </tr>
                </tbody>

                <tbody>
                <tr>
                  <th colspan="2" class="th_label">变更原因</th>
                  <td colspan="22" class="td_value">{{ mainData.changeReason }}</td>
                </tr>
                </tbody>
              </table>
            </SimpleBoardTitle>

            <SimpleBoardTitle v-if="dataState === 'view'" title="基本信息" style="margin-top: 5px;">
              <table class="table_content" style="margin-top: 10px;">
                <tbody>
                <tr>
                  <th colspan="2" class="th_label">律所名称</th>
                  <td colspan="14" class="td_value">{{ mainData.lawyerFirm }}</td>
                  <th colspan="2" class="th_label">事项编号</th>
                  <td colspan="6" class="td_value">{{ mainData.sequenceCode }}</td>
                </tr>
                </tbody>

                <tbody>
                <tr>
                  <th colspan="2" class="th_label">律所地址</th>
                  <td colspan="22" class="td_value">{{ mainData.registerAddress }}</td>
                </tr>
                </tbody>

                <tbody>
                <tr>
                  <th colspan="2" class="th_label">律所类型</th>
                  <td colspan="6" class="td_value">{{ mainData.lawFirmType }}</td>
                  <th colspan="2" class="th_label">律所电话</th>
                  <td colspan="6" class="td_value">{{ mainData.lawFirmPhone }}</td>
                  <th colspan="2" class="th_label">律所邮箱</th>
                  <td colspan="6" class="td_value">{{ mainData.lawFirmEmail }}</td>
                </tr>
                </tbody>

                <tbody>
                <tr>
                  <th colspan="2" class="th_label">负责人</th>
                  <td colspan="6" class="td_value">{{ mainData.functionary }}</td>
                  <th colspan="2" class="th_label">邮政编码</th>
                  <td colspan="6" class="td_value">{{ mainData.postalCode }}</td>
                  <th colspan="2" class="th_label">主管机关</th>
                  <td colspan="6" class="td_value">{{ mainData.issuingAuthority }}</td>
                </tr>
                </tbody>

                <tbody>
                <tr>
                  <th colspan="2" class="th_label">统一社会<br>信用代码</th>
                  <td colspan="6" class="td_value">{{ mainData.licenseCode }}</td>
                  <th colspan="2" class="th_label">批准文号</th>
                  <td colspan="6" class="td_value">{{ mainData.licenseNumber }}</td>
                  <th colspan="2" class="th_label">设立资产</th>
                  <td colspan="6" class="td_value">{{ mainData.registeredCapital }}</td>
                </tr>
                </tbody>

                <tbody>
                <tr>
                  <th colspan="2" class="th_label">成立时间</th>
                  <td colspan="6" class="td_value">{{ mainData.foundTime | parseTime }}</td>
                  <th colspan="2" class="th_label">执业人数</th>
                  <td colspan="6" class="td_value">{{ mainData.workNumber }}</td>
                  <th colspan="2" class="th_label">申请部门/<br>所属公司</th>
                  <td colspan="6" class="td_value">{{ mainData.applyDeptName }}</td>
                </tr>
                </tbody>

                <tbody>
                <tr>
                  <th colspan="2" class="th_label">年检情况</th>
                  <td colspan="22" class="td_value">{{ mainData.annualInspectionName }}</td>
                </tr>
                </tbody>

                <tbody v-if="mainData.annualInspectionName === '其他情况'">
                <tr>
                  <th colspan="2" class="th_label">年检情况说明</th>
                  <td colspan="22" class="td_value">{{ mainData.annualInspectionDesc }}</td>
                </tr>
                </tbody>

                <tbody>
                <tr>
                  <th colspan="2" class="th_label">擅长业务领域</th>
                  <td colspan="22" class="td_value">{{ mainData.beGoodAtDomain }}</td>
                </tr>
                </tbody>

                <tbody>
                <tr>
                  <th colspan="2" class="th_label_">律所简介</th>
                  <td colspan="22" class="td_value_">{{ mainData.introduction }}</td>
                </tr>
                </tbody>

                <tbody>
                <tr>
                  <th colspan="2" class="th_label_">备注</th>
                  <td colspan="22" class="td_value_">{{ mainData.remark }}</td>
                </tr>
                </tbody>

                <tbody>
                <tr>
                  <th colspan="2" class="th_label_">附件资料</th>
                  <td colspan="22" class="td_value_">
                    <uploadDoc
                        v-model="mainData.businessLicense"
                        :files.sync="mainData.businessLicense"
                        :disabled="dataState === 'view'"
                        :tips="tip_"
                        :doc-path="docURL"
                    />
                  </td>
                </tr>
                </tbody>
              </table>
            </SimpleBoardTitle>


            <simple-board title="主责律师/对接律师"
                          :has-add="hasAdd"
                          :data-state="dataState"
                          @addBtn="addLawyer"
                          style="margin-top: 30px;"
            >
              <el-table
                  style="width: 100%"
                  :data="mainData.lawyerList"
                  border
                  stripe
                  :show-overflow-tooltip="true"
                  fit
                  :height="200"
                  highlight-current-row
              >
                <el-table-column label="序号" type="index" align="center" width="60" show-overflow-tooltip/>
                <el-table-column label="姓名" min-width="100" align="center" prop="lawyerName" show-overflow-tooltip/>
                <el-table-column label="律师身份" min-width="100" align="center" prop="whetherHostLawyer"
                                 show-overflow-tooltip>
                  <template slot-scope="scope">
                    <span>{{ scope.row.whetherHostLawyer | whetherHostLawyerFilter }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="联系电话" min-width="100" align="center" prop="lawyerPhone" show-overflow-tooltip/>
                <el-table-column label="电子邮箱" min-width="100" align="center" prop="lawyerEmail" show-overflow-tooltip/>
                <el-table-column label="擅长领域" min-width="150" align="center" prop="beGoodAtDomain"
                                 show-overflow-tooltip/>
                <el-table-column label="操作" align="center" width="200px">
                  <template slot-scope="scope">
                    <el-button
                        v-if="dataState !== 'view'&& scope.row.whetherSystem && scope.row.currentStateCode === utils.dataState_LAWYER.YRK.code"
                        size="mini" type="primary"
                        @click.native.prevent="CKRow(scope.$index, scope.row)">出库
                    </el-button>
                    <el-button
                        v-if="dataState !== 'view' && scope.row.whetherSystem && scope.row.currentStateCode === utils.dataState_LAWYER.YCK.code"
                        size="mini" type="text" @click.native.prevent="QXCKRow(scope.$index, scope.row)">取消出库
                    </el-button>
                    <el-button
                        v-if="dataState !== 'view' && scope.row.currentStateCode !== utils.dataState_LAWYER.YCK.code"
                        size="mini" type="text" @click.native.prevent="editRow(scope.$index, scope.row)">编辑
                    </el-button>
                    <el-button v-if="dataState !== 'view' && !scope.row.whetherSystem" size="mini" type="text"
                               @click.native.prevent="deleteRow(scope.$index, scope.row)">删除
                    </el-button>
                    <el-button v-if="dataState === 'view'" size="mini" type="text"
                               @click.native.prevent="look(scope.$index,scope.row)">查看
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </simple-board>

            <div style="height: 100%;">
              <el-row style="margin-top: 20px;">
                <el-col :span="18">
                  <el-form-item label="经办组织">
                    <span class="viewSpan">{{ mainData.createPsnFullName }}</span>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="经办时间">
                    <span class="viewSpan">{{ mainData.createTime | parseTime }}</span>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>

            <!--选择部门或者人员-->
            <el-dialog :close-on-click-modal="false" title="选择部门" :visible.sync="orgVisible" width="50%">
              <div class="el-dialog-div">
                <orgTree :accordion="false" :is-checked-user="isCheckedUser" :show-user="showUser" :is-check="is_Check"
                         :checked-data.sync="zxcheckedData" :is-not-cascade="true" :is-filter="true"/>
              </div>
              <span slot="footer" class="dialog-footer">
                <el-button icon="" class="negative-btn" @click="cancel_">取消</el-button>
                <el-button type="primary" icon="" class="active-btn" @click="choiceDeptSure_">确定
                </el-button>
              </span>
            </el-dialog>

          </el-form>
        </el-scrollbar>
      </el-main>

      <el-main v-else>
        <el-card style="height: auto;margin-left: 1%;margin-right: 2%;margin-top: 1%;">
          <el-scrollbar style="height: 100%">
            <el-form ref="dataForm"
                     :model="mainData" :rules="rules" :class="className"
                     style="margin-left: 10px;margin-right: 10px;"
            >
              <el-row style="margin-top: 20px;">
                <span style="text-align: left;font-size: 20px;margin-left: 43%;font-weight: 900;">律所律师信息变更审批单</span>
              </el-row>

<!--              <SimpleBoardTitleApproval title="变更信息" style="margin-top: 5px;">-->
<!--                <table class="table_content">-->
<!--                  <tbody>-->
<!--                  <tr>-->
<!--                    <th colspan="3" class="th_label_approval">变更类型</th>-->
<!--                    <td colspan="21" class="td_value_approval">{{ mainData.changeTypeName }}</td>-->
<!--                  </tr>-->
<!--                  </tbody>-->

<!--                  <tbody>-->
<!--                  <tr>-->
<!--                    <th colspan="3" class="th_label_approval_">变更原因</th>-->
<!--                    <td colspan="21" class="td_value_approval_">{{ mainData.changeReason }}</td>-->
<!--                  </tr>-->
<!--                  </tbody>-->
<!--                </table>-->
<!--              </SimpleBoardTitleApproval>-->

              <SimpleBoardTitleApproval title="基本信息" style="margin-top: 10px;">
                <table class="table_content">
                  <tbody>
                    <tr>
                      <th colspan="3" class="th_label_approval">事项名称</th>
                      <td colspan="9" class="td_value_approval">{{ mainData.itemName }}</td>
                      <th colspan="3" class="th_label_approval">事项编号</th>
                      <td colspan="9" class="td_value_approval">{{ mainData.sequenceCode }}</td>
                    </tr>
                    <tr>
                      <th colspan="3" class="th_label_approval">经办组织</th>
                      <td colspan="9" class="td_value_approval">{{mainData.createPsnFullName}}</td>
                      <th colspan="3" class="th_label_approval">经办时间</th>
                      <td colspan="9" class="td_value_approval">{{ mainData.createTime | parseTime }}</td>
                    </tr>
                    <tr>
                      <th colspan="3" class="th_label_approval">会知部门</th>
                      <td colspan="21" class="td_value_approval">{{ mainData.noticeDeptName }}</td>
                    </tr>
                    <tr>
                      <th colspan="3" class="th_label_approval">律所名称</th>
                      <td colspan="21" class="td_value_approval">{{ mainData.lawyerFirm }}</td>
<!--                      <th colspan="3" class="th_label_approval">律所规模</th>-->
<!--                      <td colspan="9" class="td_value_approval">{{ mainData.lawFirmLevel }}</td>-->
                    </tr>
                    <tr>
<!--                      <th colspan="3" class="th_label_approval">成立时间</th>-->
<!--                      <td colspan="9" class="td_value_approval">{{ mainData.foundTime | parseTime }}</td>-->
                      <th colspan="3" class="th_label_approval">擅长业务领域</th>
                      <td colspan="21" class="td_value_approval">{{ mainData.beGoodAtDomain }}</td>
                    </tr>
                    <tr>
                      <th colspan="3" class="th_label_approval_">变更原因</th>
                      <td colspan="21" class="td_value_approval_">{{ mainData.remark }}</td>
                    </tr>
                  </tbody>

                  <tbody>
                  <tr>
                    <th colspan="3" class="th_label_approval">事项报告</th>
                    <td colspan="21" class="td_value_approval" style="height: 100%">
                      <div v-if="mainData.eventReport || isCreate">
                        <uploadDoc
                            v-model="mainData.eventReport"
                            :files.sync="mainData.eventReport"
                            :disabled="!isCreate"
                            :doc-path="docURL"
                            :tips="'根据相关管理要求，报告正文请线下加盖印章'"
                        />
                      </div>
                      <div v-else style="font-size: 15px">无</div>
                    </td>
                  </tr>
                  </tbody>

                  <tbody>
                  <tr>
                    <th colspan="3" class="th_label_approval">附件资料</th>
                    <td colspan="21" class="td_value_approval">
                      <div v-if="mainData.businessLicense">
                        <uploadDoc
                            v-model="mainData.businessLicense"
                            :files.sync="mainData.businessLicense"
                            :disabled="true"
                            :tips="tip_"
                            :doc-path="docURL"
                        />
                      </div>
                      <div v-else style="font-size: 15px">无</div>
                    </td>
                  </tr>
                  </tbody>
                </table>
              </SimpleBoardTitleApproval>

              <!--审批历史 -->
              <SimpleBoardTitleApproval style="margin-top: 10px;" title="审查意见" class="print-table-wrap">
                <ProcessOpinion style="border: solid 1px #606266;" :proc-inst-id="this.obj.processInstanceId" :task-id="this.obj.taskId" type-code="1"/>
                <div v-if="approvalIs && isParseElement">
                  <el-input
                      type="textarea"
                      :rows="2"
                      style="border-radius: 0 !important;"
                      placeholder="请输入审批意见"
                      v-model="approvalOpinion">
                  </el-input>
                </div>

              </SimpleBoardTitleApproval>

              <SimpleBoardTitleApproval style="margin-top: 10px;" title="领导意见" class="leadership-opinions-section-wrap">
                <div style="border: solid 1px #606266;overflow: hidden">
                  <ProcessOpinion :proc-inst-id="this.obj.processInstanceId" :task-id="this.obj.taskId" type-code="2"/>
                  <div v-if="approvalIs && isParseElementlg">
                    <el-input
                        type="textarea"
                        :rows="2"
                        style="border-radius: 0 !important;"
                        placeholder="请输入审批意见"
                        v-model="approvalOpinion">
                    </el-input>
                  </div>

                </div>
              </SimpleBoardTitleApproval>

            </el-form>
          </el-scrollbar>
        </el-card>
      </el-main>

      <Shortcut :approval-show="approvalShow" :noticeShow="noticeShow" :detail-show="detailShow" :print-show="printShow"
                @approvalClick="approvalClick" @detailClick="detailClick" @noticeClick="noticeClick" @printClick="printClick"/>

    </el-container>
  </FormWindow>
</template>

<script>
import lawyerFirmApi from "@/api/LawyerManage/LawyerFirm/lawFirmInApproval"
import lawyerApi from '@/api/LawyerManage/LawyerFirm/lawyerInApproval'
import noticeApi from "@/api/_system/notice"
import processApi from "@/api/_system/process"
import taskApi from "@/api/_system/task";
import orgApi from "@/api/_system/org";

import {mapGetters} from 'vuex'
import {recommended} from '@/view/utils/constants'

import FormWindow from '@/view/components/FormWindow/FormWindow'
import SimpleBoard from "@/view/components/SimpleBoard/SimpleBoardViewCase"
import UploadDoc from '@/view/components/UploadDoc/UploadDoc'
import textSpan from '@/view/components/TextSpan/TextSpan'
import orgTree from '@/view/components/OrgTree/OrgTree'
import Shortcut from "@/view/litigation/caseManage/caseExamine/Shortcut"
import lawyerFirm from "@/api/LawyerManage/LawyerFirm/lawyerFirm"
import SimpleBoardTitle from "../../../components/SimpleBoard/SimpleBoardTitle"
import ProcessOpinion from '@/view/components/ProcessOpinion/ProcessOpinion'
import SimpleBoardTitleApproval from "@/view/components/SimpleBoard/SimpleBoardTitleApproval";

export default {
  name: "FirmMainChange",
  inject: ['layout', 'mcpLayout', 'mcpDesignPage'],
  components: {
    SimpleBoardTitleApproval,
    FormWindow, UploadDoc, SimpleBoard, textSpan, orgTree, Shortcut, SimpleBoardTitle,
    ProcessOpinion
  },
  filters: {
    whetherHostLawyerFilter(val) {
      return val ? '主责律师' : '助理律师'
    }
  },
  data() {
    return {
      approvalOpinion:'',
      parseElement:null,
      approvalIs:false,
      className:'',
      create: null,
      type: null,
      dataState: null,//表单状态，新增、查看、编辑
      functionId: null,//终止的时候要用，需要手动关闭
      typeCode: null,
      typeName: null,
      loading: false,
      taskName: '测试标题001',//修改流程下一个待办标题（回退目前测试不行）
      changeId: null,
      tableData: [],
      tabId: null,
      view: 'new',
      rules: {
        lawyerFirm: [{required: true, message: '请输入律所名称', trigger: 'blur'}],
        lawFirmType: [{required: true, message: '请选择律所类型', trigger: 'blur'}],
        functionary: [{required: true, message: '请输入负责人', trigger: 'blur'}],
        licenseCode: [{required: true, message: '请输入统一社会信用代码', trigger: 'blur'}],
        applyDeptName: [{required: true, message: '请选择申请部门', trigger: 'blur'}],
        annualInspectionDesc: [{required: true, message: '请输入年检情况', trigger: 'blur'}],
      },
      // 律所数据
      mainData: {
        id: this.utils.createUUID(), // id
        itemName: null, // 事项名称
        noticeDeptName: null, //会知部门名称
        noticeDeptId: null, //会知部门id
        sequenceCode: null, //流水号
        lawyerFirm: null, // 律所名称
        registerAddress: null, // 注册地址/律所地址
        functionary: null, // 负责人
        lawFirmType: null, // 律所类型
        postalCode: null, // 邮政编码
        issuingAuthority: null, // 主管机关
        licenseCode: null, // 统一社会信用代码
        licenseNumber: null, // 批准文号
        registeredCapital: null, // 设立资产
        foundTime: null, // 成立时间
        workNumber: null, // 执业人数
        applyDeptName: null, // 申请部门/所属公司
        applyDeptId: null, // 申请部门/所属公司
        annualInspectionName: null, // 年检情况
        annualInspectionId: null, // 年检情况
        annualInspectionDesc: null, //年检情况描述
        noResponseTimes: null, // 未响应次数
        recommendedInstructionsName: null, // 推荐说明
        recommendedInstructionsId: null, // 推荐说明
        recommended: null, // 推荐
        introduction: null, // 律所简介
        remark: null, // 备注
        businessLicense: null, // 营业执照、附件资料
        updateTime: null, // 更新时间
        typeCode: null,
        typeName: null,
        dataState: this.utils.dataState_BPM.SAVE.name, //数据状态
        dataStateCode: this.utils.dataState_BPM.SAVE.code, //数据状态编码
        eventReport: null, //事项报告
        createOgnName: null, //  经办单位
        createDeptName: null, // 经办部门
        createPsnName: null, // 经办人员
        createTime: null, // 经办时间
        createOgnId: null,
        createDeptId: null,
        createPsnId: null,
        createPsnFullId: null,
        createPsnFullName: null,
        createGroupId: null,
        createGroupName: null,
        createOrgId: null,
        createOrgName: null,
        changeTimes: 0,
        parentId: null,
        dataSource: 'new',
        sourceId: null,
        whetherMy: 'no',
        lawyerList: [], // 弹框的表格数据源
        operatingType: 'new', // 弹框的表格数据源
        beGoodAtDomain: null, // 擅长领域
        beGoodAtDomainIds: null, // 擅长领域
        changeTypeName: null,
        changeTypeId: null,
        changeReason: null,
        lawFirmPhone: null, // 律所电话
        lawFirmEmail: null, // 律所邮箱
        lawFirmSelected: false, // 是否选聘
        lawFirmLevel: null, // 律所规模
        fileId: null, // 手写签批id
      },
      recommendedTitle: null,
      NJQKData: [],
      TJSMData: this.utils.recommended_data,
      SCLYData: [],
      BGLXData: [],
      tip_: '附送材料需要上传通过年检的《营业执照》/《执业资格许可证》（或副本）的复印件、负责人或主要联系人资格证书复印件。',
      docURL: '/firmIn',
      lawFirmTypeData: [{id: '0', name: '正式律所'}, {id: '1', name: '临时律所'}],
      zxcheckedData: [],
      showUser: false,
      is_Check: true,
      isCheckedUser: false,
      orgVisible: false,
      hasAdd: true,
      module: null,
      moduleName: null,

      activity: null,//记录当前待办处于流程实例的哪个环节
      obj: {// 流程处理逻辑需要的各种参数
        taskId: null,
        processInstanceId: null,
        businessKey: null,
        title: null,
        functionName: '律所审批',
        functionCode: 'firm_main_change',
        sid: null,
      },

      noticeParams: {},
      noticeData: {
        moduleName: '', // 模块名称
        dataId: '', // 数据ID
        url: '', // 地址
        title: '', // 地址
        params: {} // 其他参数
      },
    }
  },
  computed: {
    ...mapGetters(['orgContext']),
    TJSM() {
      const val = this.mainData.recommendedInstructionsId
      const me = this
      if (val === recommended.HYJGTJ.code) {
        me.recommendedTitle = '推荐机构全称'
      } else if (val === recommended.HZQYTJ.code) {
        me.recommendedTitle = '合作企业全称'
      } else if (val === recommended.QTYY.code) {
        me.recommendedTitle = '其他原因'
      } else {
        me.recommendedTitle = null
      }
      return val === recommended.HYJGTJ.code || val === recommended.HZQYTJ.code || val === recommended.QTYY.code
    },
    recommendedRules() {
      const val = this.mainData.recommendedInstructionsId
      const me = this
      let rule = {}
      if (val === recommended.HYJGTJ.code) {
        me.recommendedTitle = '推荐机构全称'
        rule = {required: me.dataState !== 'view', message: '请填写推荐机构全称', trigger: 'blur'}
      } else if (val === recommended.HZQYTJ.code) {
        me.recommendedTitle = '合作企业全称'
        rule = {required: me.dataState !== 'view', message: '请填写合作企业全称', trigger: 'blur'}
      } else if (val === recommended.QTYY.code) {
        me.recommendedTitle = '其他原因'
        rule = {required: me.dataState !== 'view', message: '请填写其他原因', trigger: 'blur'}
      }
      return rule
    },
    beGoodAtDomainIds_1: {
      set: function (data) {
        this.mainData.beGoodAtDomainIds = data.join(',')
      },
      get: function () {
        if (this.mainData.beGoodAtDomainIds) {
          return this.mainData.beGoodAtDomainIds.split(',')
        }
        return []
      }
    },
    changeTypeIds: {
      set: function (data) {
        this.mainData.changeTypeId = data.join(',')
      },
      get: function () {
        if (this.mainData.changeTypeId) {
          return this.mainData.changeTypeId.split(',')
        }
        return []
      }
    },
    isNotice() {
      const isNotice = this.$route.query.isNotice
      return this.mainData.dataStateCode !== this.utils.dataState_BPM.FINISH.code &&
          this.mainData.createPsnFullId === this.orgContext.currentPsnFullId && isNotice
    },
    noticeShow() {
      return this.mainData.dataStateCode === this.utils.dataState_BPM.FINISH.code
    },
    detailShow() {
      return this.view === 'new'
    },
    approvalShow() {
      return this.view === 'old'
    },
    printShow() {
      return this.view === 'new'
    },
    // 如果是从oa打开，则需需要加上这两个参数
    originOa() {
      let {origin,fullScreen} = this.$route.query
      return origin==='oa'&&fullScreen
    },
    isCreate: function ()
    {
      return this.create === 'create'
    },
    isParseElement(){
      return this.parseElement === '部门意见'
    },
    isParseElementlg(){
      return this.parseElement === '领导意见'
    }
  },
  created() {
    this.isHeadquarter()
    this.baseDataLoad()
    //因为是流程功能，知会、抄送，都需要按照流程抄送的配置打开，
    // 只是知会的需要更新消息表，抄送需要更新日志表
    //判断是知会还是抄送，可以根据param中的参数isNotice判断
    this.obj.functionName = '律所审批'
    const isNotice = this.$route.query.isNotice
    // isNotice在OA中打开会解析成Boolean，系统内会被转成字符"true"
    if (isNotice === true || isNotice === "true") {
      //知会
      if (this.$route.query.processInstanceId !== null && this.$route.query.processInstanceId !== undefined) {
        //保存的数据，获取不到实例ID，只有启动流程实例才能拿到
        this.obj.processInstanceId = this.$route.query.processInstanceId
        this.obj.taskId = this.$route.query.taskId
      }
      const read = this.$route.query.read
      const sid = this.$route.query.sid
      if (read === false || read === 'false') {
        noticeApi.read({sid: sid})
      }
    } else {
      //这里除了抄送会走，其他正常逻辑也会走，所以下面的参数判断了type === 'toRead'，即未读时，才会更新OA消息和日志记录
      this.obj.sid = this.$route.query.sid//消息表中的消息ID 日志表中的日志ID
      if (this.$route.query.processInstanceId !== null && this.$route.query.processInstanceId !== undefined) {
        //保存的数据，获取不到实例ID，只有启动流程实例才能拿到
        this.obj.processInstanceId = this.$route.query.processInstanceId
        this.obj.taskId = this.$route.query.taskId
      }
      const type = this.$route.query.type//获取消息状态类型，toRead-》未读，此时需要更新，haveRead-》已读，就不需要更新了
      if (type === 'toRead') {
        this.obj.pathname = window.location.pathname
        //返回url路径名（https://www.runoob.com/try/try.php?filename=tryjsref_loc_pathname，返回/try/try.php），判断是在法务系统打开还是在OA中打开
        //法务中打开会把相同流程实例的全部消息改为已读，所以是更新OA多条，OA中打开是只更新OA一条
        this.obj.title = "入库审批"
        //更新OA需要参数processInstanceId, title, functionName, oldTaskId,
        processApi.finishOATask(this.obj)
      }
    }
  },
  activated() {
    // 长连接页面第二次激活的时候,不会走created方法,会走此方法
    this.refresh_()
  },
  mounted() {
    //挂载完毕后，设置回调函数
    this.$emit('setCallBack', {
      beforeCallBack: this.beforeApproval,//点击办理或提交前的回调，效验必填控制
      afterCallBack: this.afterApproval,//点击办理弹框中再点击确定后的回调，审批完成后处理业务逻辑
      setTaskNodeInfo: this.setTaskNodeInfo,//挂载完毕后执行的回调，用于页面已进入需要处理的业务逻辑
      filterBtn: this.utils.filterBtn,
      beforeConfirmCallBack: this.beforeConfirmCallBack,
      beforeApprovalCb: this.beforeApprovalCb
    })
  },
  methods: {
    beforeConfirmCallBack(data,resolve,reject)
    {
      const customProperties = data.nodeInfo.customProperties.find(item => item.key === 'FlowCustomPropertiesSettingsServiceImpl')
      if(customProperties&&customProperties.value && JSON.parse(customProperties.value)['id']==="1"&&data.approvalFormData.comment=="")
      {
        // 消息按需求编辑 这只是个示例
        this.$confirm('未填写意见，请确认是否继续。', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          resolve()()
        }).catch(() => {
          reject()
        })
      }else {
        resolve()
      }
    },
    // 提交前
    beforeApproval(code, resolve) {
      // 校验必填项
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          // 保存
          this.save().then(() => {
            // 将部分参数传给OA
            this.mainData.title = "入库审批"
            this.mainData.code = code === null ? '' : code
            this.mainData.functionName = '律所审批'
            this.$emit('submit-success', this.mainData, 'id')
            resolve({success: true, formData: {...this.mainData},approvalOpinion:this.approvalOpinion})

          })
        } else {
          this.$message.success('请填写必填项!')
          resolve(false)
          return false
        }
      })
    },
    setTaskNodeInfo(event) {
      const customProperties = event.customProperties.find(item => item.key === 'FlowCustomPropertiesSettingsServiceImpl')
      if(customProperties !== null && customProperties !== undefined ){
        this.parseElement = JSON.parse(customProperties.value)['name']
      }
      console.log("部门意见："+this.parseElement)
      //（toDeal-待办处理，haveDealt-已办查看，toRead-未读，haveRead-已读）
      const type = this.$route.query.type
      // 业务ID
      const id = this.$route.query.businessKey
      //是否首环节（submitNode-首环节）
      this.activity = event.taskNodeType
      /*
      * 首环节
      *   1、businessKey-是null，说明是刚发起---执行init方法
      *   2、businessKey-有值
      *       1、回退处理--环节是submitNode   ---执行init
      *       2、已办查看--优先判断  haveDealt---执行load
      *       3、未读查看--优先判断  toRead   ---执行load
      *       4、已读查看--优先判断  haveRead ---执行load
      * */
      if (type === 'haveDealt' || type === 'toRead' || type === 'haveRead')
      {
        this.loadData(this.utils.formState.VIEW, id)
      }
      else
      { // 不是以上3种只能是待处理，只需要判断是否首环节即可
        if (event.taskNodeType === 'submitNode') {
          this.loadData(this.utils.formState.NEW, id)
        } else {
          this.loadData(this.utils.formState.VIEW, id)
        }
      }
    },
    //撤回转办
    beforeApprovalCb(code) {
      debugger
      if (code.type === "cancelTransfer") {
        this.obj.processInstanceId = this.$route.query.processInstanceId
        this.obj.taskId = this.$route.query.taskId
        this.obj.title = "入库审批"
        this.obj.code = code === null ? '' : code
        this.obj.functionName = '律所审批'
        let pathname = window.location.pathname
        this.obj.functionCode = 'firm_main_change'
        processApi.sendOATask(this.obj).then(res => {
          if (pathname === '/base/design_page') {
            this.mcpLayout.closeTab()
          } else {
            window.close()
          }
        })
      }

    },
    afterApproval(code, data) {
      // 回退到首环节，修改业务数据状态，修改任务标题
      console.log("code==" + code)
      console.log("data==" + data)
      this.obj.businessKey = this.mainData.id
      //获取参数，为后续操作准备，processInstanceId和taskId其实在created中赋值了，这里在赋值一次也行
      if (data != null && data.data != null) {
        this.obj.processInstanceId = data.data.id
      }
      if (this.obj.businessKey == null || this.obj.processInstanceId == null) {
        this.obj.processInstanceId = this.$route.query.processInstanceId
        this.obj.taskId = this.$route.query.taskId
      }

      //需要传参数到流程中，这里操作，不限于首环节传参数
      if (this.activity === 'submitNode') {
        lawyerFirmApi.setParam(this.obj).then(() => {
          console.log("传值成功")
        }).catch(() => {
        })
      }
      // 将部分参数传给OA
      this.obj.title = "入库审批"
      this.obj.code = code === null ? '' : code
      this.obj.functionName = '律所审批'
      //不是动态节点，给OA传待办
      if (code !== 'dynamic') {
        let loading = this.$loading({
          target: document.querySelector('.sg-page-wrap'),
          lock: false,
          text: '请稍后...',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        })
        processApi.sendOATask(this.obj).then(res => {
          loading.close()
          if (this.originOa) {
            window.close()
          }
          /*let pathname = window.location.pathname
          if (pathname.indexOf('/design_pages') !== -1) {
            window.close()
          }*/
        })
      }
    },
    baseDataLoad() {
      const codes = ["LS-NJQK", "LS-SCLY", "LS-BGLX"];
      this.utils.getDic(codes).then(response => {
        this.NJQKData = response.data.data[codes[0]];
        this.SCLYData = response.data.data[codes[1]];
        this.BGLXData = response.data.data[codes[2]];
      });
    },
    cancel_() {
      this.orgVisible = false
    },
    choiceDeptSure_() {
      let c = ''
      let cid = ''
      this.zxcheckedData.forEach((item) => {
        if (c.length === 0) {
          c = c + item.name
          cid = cid + item.unitId
        } else {
          c = c + ',' + item.name
          cid = cid + ',' + item.unitId
        }
      })
      this.mainData.applyDeptName = c
      this.mainData.applyDeptId = cid
      this.orgVisible = false
    },
    addLawyer() {
      return new Promise((resolve, reject) => {
        this.mainData.updateTime = new Date()
        lawyerFirmApi.save(this.mainData).then(response => {
          const tabId = this.utils.createUUID()
          this.layout.openNewTab(
              "律师信息",
              "main_lawyer",
              "main_lawyer",
              tabId,
              {
                mainData: this.mainData,
                ...this.utils.routeState.NEW(this.utils.createUUID()),
                tabId: tabId
              })

          resolve(response)
        }).catch(error => {
          reject(error)
        })
      })
    },
    editRow(index, row) {
      this.save().then(() => {
            const tabId = this.utils.createUUID()
            this.layout.openNewTab("律师信息", "main_lawyer", "main_lawyer",
                tabId,
                {
                  id: this.mainData.id,
                  lawyerFirm: this.mainData.lawyerFirm, ...this.utils.routeState.EDIT(row.id),
                  tabId: tabId
                })
          }
      )
    },
    deleteRow(index, row) {
      this.$confirm('您确定要删除主责律师吗？删除后主责律师以及下属助理律师都会被删掉！', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        new Promise((resolve) => {
          lawyerApi.delete(row).then((response) => {
            resolve(response)
          })
        }).then(() => {
          this.mainData.lawyerList.splice(index, 1)
          this.$message.success('删除成功!')
        })
      }).catch(() => {
        this.$message.info('已取消删除!')
      })
    },
    look(index, row) {
      const tabId = this.utils.createUUID()
      this.layout.openNewTab("律师信息", "main_lawyer", "main_lawyer", tabId, {
        id: this.mainData.id,
        lawyerFirm: this.mainData.lawyerFirm,
        ...this.utils.routeState.VIEW(row.id),
        tabId: tabId
      })
    },
    CKRow(index, row) {
      this.$confirm('您确定要出库主责律师吗？出库后主责律师以及下属助理律师都会被出库！', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        row.currentState = this.utils.dataState_LAWYER.YCK.name
        row.currentStateCode = this.utils.dataState_LAWYER.YCK.code
        lawyerApi.changeOut(row).then(() => {
        })
      }).catch(() => {
        this.$message.info('已取消删除!')
      })
    },
    QXCKRow(index, row) {
      this.$confirm('您确定要取消出库主责律师吗？取消出库后主责律师以及下属助理律师都会被取消出库！', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        row.currentState = this.utils.dataState_LAWYER.YRK.name
        row.currentStateCode = this.utils.dataState_LAWYER.YRK.code
        lawyerApi.changeOut(row).then(() => {
        })
      }).catch(() => {
        this.$message.info('已取消删除!')
      })
    },
    loadData(dataState, dataId) {
      const org = this.orgContext
      this.dataState = dataState
      this.functionId = this.$route.query.functionId

      if (this.$route.query.view !== undefined && this.$route.query.view !== '')
        this.view = this.$route.query.view
      if (this.$route.query.type !== undefined && this.$route.query.type !== '')
        this.type = this.$route.query.type
      if (this.$route.query.create !== undefined && this.$route.query.create !== '')
        this.create = this.$route.query.create
      if (this.$route.query.LawFirmChangeState === 'change') {
        this.mainData.operatingType = 'change'
        // 将原来的律所、律师查出来，复制到新的入库审批表中，数据来源都是=old
        lawyerFirmApi.changeLawFirm({id: dataId, type: 'change', org: org}).then(res => {
          this.mainData = res.data.data
          this.loading = false
          let year = new Date().getFullYear()
          this.utils.createKvsequence('LSBG' + year, 6).then(value => {
            this.mainData.sequenceCode = value.data.kvsequence
          }).then(() => {
            this.approvalOpinionIs()
          })
        })
      } else if (this.$route.query.LawFirmChangeState === 'in') {
        this.mainData.operatingType = 'in'
        // 将原来的律所信息查出来赋值到律所入库审批表中，数据来源是=old
        lawyerFirmApi.changeLawFirm({id: dataId, type: 'in', org: org}).then(res => {
          this.mainData = res.data.data
          this.loading = false
          let year = new Date().getFullYear()
          this.utils.createKvsequence('LSRK' + year, 6).then(value => {
            this.mainData.sequenceCode = value.data.kvsequence
          }).then(() => {
            this.approvalOpinionIs()
          })
        })
      } else {
        lawyerFirmApi.queryDataById({id: dataId}).then(res => {
          this.mainData = res.data.data
          this.loading = false
        }).then(() => {
          this.approvalOpinionIs()
        })
      }
    },
    refresh_() {
      lawyerFirmApi.queryDataById({id: this.mainData.id}).then(res => {
        if (res.data.data !== undefined) {
          this.mainData = res.data.data
          this.loading = false
        }
      })
    },
    updateLawyerFirm() {
      const array = this.mainData.lawyerList
      if (array.length > 0) {
        for (let i = 0; i < array.length; i++) {
          const arrayElement = array[i]
          arrayElement.lawFirm = this.mainData.lawyerFirm
        }
      }
    },
    save() {
      return new Promise((resolve, reject) => {
        this.mainData.updateTime = new Date()
        lawyerFirmApi.save(this.mainData).then(response => {
          resolve(response)
        }).catch(error => {
          reject(error)
        })
      })
    },
    submit() {
      this.save().then(() => {
        this.$emit('submit-success', this.mainData, 'id')
        this.$message.success('保存成功!')
      })
    },
    licenseCodeBlur() {
      lawyerFirm.isBlackList({id: this.mainData.id, code: this.mainData.licenseCode,}).then((res) => {
        const flag = res.data.data
        if (flag) {
          this.$message({
            showClose: true,
            message: '该律所已被拉黑！',
            type: 'warning'
          })
          this.mainData.licenseCode = null
        }
      })
      lawyerFirmApi.distinct({
        id: this.mainData.id,
        code: this.mainData.licenseCode,
        createOgnId: this.mainData.createOgnId
      }).then((res) => {
        const flag = res.data.data
        if (flag) {
          this.$message({
            showClose: true,
            message: '统一社会信用代码重复！',
            type: 'warning'
          })
          this.mainData.licenseCode = null
        }
      })
    },
    chooseApprovalDeptClick() {
      this.isCheckedUser = false
      this.showUser = false
      this.orgVisible = true
      this.is_Check = false
    },
    annualInspectionChange(newVal) {
      this.mainData.annualInspectionName = this.utils.getDicName(this.NJQKData, newVal)
    },
    recommendedInstructionsChange(newVal) {
      this.mainData.recommendedInstructionsName = this.utils.getDicName(this.TJSMData, newVal)
      this.mainData.recommended = null
    },
    beGoodAtDomainChange(newVal) {
      if (newVal && newVal.length > 0) {
        const arr = []
        for (let i = 0; i < newVal.length; i++) {
          const newValElement = newVal[i]
          const name = this.utils.getDicName(this.SCLYData, newValElement)
          arr.push(name)
        }
        this.mainData.beGoodAtDomain = arr.join('、')
      }
    },
    changeTypeChange(newVal) {
      if (newVal && newVal.length > 0) {
        const arr = []
        for (let i = 0; i < newVal.length; i++) {
          const newValElement = newVal[i]
          const name = this.utils.getDicName(this.BGLXData, newValElement)
          arr.push(name)
        }
        this.mainData.changeTypeName = arr.join('、')
      }
    },
    // 发起知会
    stopClick() {
      this.$confirm('您确定要终止当前流程吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.obj.processInstanceId = this.$route.query.processInstanceId
        this.obj.taskId = this.$route.query.taskId
        this.obj.title = '入库审批'
        this.obj.functionName = '律所审批'
        this.obj.code = 'stop'
        let pathname = window.location.pathname
        new Promise((resolve) => {
          let processInstanceId = this.$route.query.processInstanceId
          processApi.end(processInstanceId).then(res => {
            resolve(res)
          })
        }).then(val => {
          if (val.data.code === 200) {
            this.obj.functionCode = 'firm_main_change'
            processApi.sendOATask(this.obj).then(res => {
              if (pathname === '/base/design_page') {
                this.mcpLayout.closeTab()
              } else {
                window.close()
              }
            })
          }
        })
      }).catch(() => {
        this.$message.info('已取消删除!')
      })
    },
    finishClick() {
      this.$confirm('您确定要结束当前流程吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.obj.processInstanceId = this.$route.query.processInstanceId
        this.obj.taskId = this.$route.query.taskId
        this.obj.title = '入库审批'
        this.obj.functionName = '律所审批'
        this.obj.code = 'pass'
        let pathname = window.location.pathname
        new Promise((resolve) => {
          processApi.move({
            proInstId: this.$route.query.processInstanceId,
            taskId: this.$route.query.taskId
          }).then(res => {
            resolve(res)
          })
        }).then(val => {
          if (val.status === 200) {
            this.obj.functionCode = 'firm_main_change'
            processApi.sendOATask(this.obj).then(() => {
              if (pathname === '/base/design_page') {
                this.mcpLayout.closeTab()
              } else {
                window.close()
              }
            })
          }
        })
      }).catch(() => {
        this.$message.info('已取消删除!')
      })
    },
    noticeClick(cooperateFunc) {
      //必传参数
      this.noticeParams = {
        ...this.utils.routeState.VIEW(this.mainData.id),//主要作用是后台逻辑需要，其他作用各业务看情况使用
        processInstanceId: this.$route.query.processInstanceId,//流程实例
        taskId: this.$route.query.taskId,//任务ID
        businessKey: this.mainData.id, //业务数据ID
        entranceType: "FLOWABLE", //流程特定的标识
        type: "haveRead",//已处理
        whetherProcess: true,//系统内部打开的时候判断是否是流程，通过不同的方式打开查看
        isNotice: true//告知是知会消息打开的功能，有些按钮可以隐藏,只有知会消息，在created钩子函数中才执行相关，如果是流程抄送不需要传，可以根据上面created描述判断
      }
      this.noticeData.dataId = this.mainData.id
      this.noticeData.moduleName = '入库审批'
      this.noticeData.params = this.noticeParams
      this.noticeData.url = 'firm_main_change'//这个需要与功能维护中的值一样
      this.noticeData.title = '入库审批'
      cooperateFunc(this.noticeData)
    },
    isHeadquarter() {
      // const createOgnId = this.orgContext.currentOgnId
      //
      // if (createOgnId === '15033708970596') {
        this.typeCode = '1'
        this.typeName = '推荐库'
      // } else {
      //   this.typeCode = '0'
      //   this.typeName = '资源库'
      // }
    },
    detailClick() {
      const me = this
      if (this.mainData.dataStateCode !== this.utils.dataState_BPM.SAVE.code) {
        taskApi.selectTaskId({businessKey: this.mainData.id, isView: 'true'}).then(res => {
          const functionId = res.data.data[0].ID
          const tabId = this.utils.createUUID()

          let urlParam = {
            processInstanceId: res.data.data[0].PID,//流程实例
            taskId: res.data.data[0].ID,//任务ID
            businessKey: this.mainData.id, //业务数据ID
            functionId: functionId, //不传也可以，只有首环节需要传，实例启动就可以不传了
            entranceType: "FLOWABLE",
            type: "haveDealt",
            channel: 'business', //查看渠道，默认就传这个值即可，表示是从业务功能传过来的，首环节提交后过滤掉撤回按钮
            view: 'old',
          }

          if (me.$route.query.origin !== undefined && me.$route.query.origin === 'oa')
          {
            this.$set(urlParam, "origin", "oa")
            this.$set(urlParam, "fullScreen", "true")
          }

          this.layout.openNewTab("详细信息",
              "design_page",
              "design_page",
              tabId,
              urlParam
          )
        })
      } else {
        taskApi.selectFunctionId({functionCode: 'firm_main_change'}).then(res => {
          const functionId = res.data.data[0].ID
          const tabId = this.utils.createUUID()

          let urlParam = {
            processDefinitionKey: this.mcpDesignPage.processKey,
            functionId: functionId,
            entranceType: "FLOWABLE",
            ...this.utils.routeState.VIEW(this.mainData.id),
            channel: 'business',
            view: 'old',
          }

          if (me.$route.query.origin !== undefined && me.$route.query.origin === 'oa')
          {
            this.$set(urlParam, "origin", "oa")
            this.$set(urlParam, "fullScreen", "true")
          }

          this.layout.openNewTab("详细信息",
              "design_page",
              "design_page",
              tabId,
              urlParam
          )
        })
      }
    },
    approvalClick() {
      const me = this
      if (this.mainData.dataStateCode !== this.utils.dataState_BPM.SAVE.code) {
        taskApi.selectTaskId({businessKey: this.mainData.id, isView: 'true'}).then(res => {
          const functionId = res.data.data[0].ID
          const tabId = this.utils.createUUID()

          let urlParam  = {
            processInstanceId: res.data.data[0].PID,//流程实例
            taskId: res.data.data[0].ID,//任务ID
            businessKey: this.mainData.id, //业务数据ID
            functionId: functionId, //不传也可以，只有首环节需要传，实例启动就可以不传了
            entranceType: "FLOWABLE",
            type: "haveDealt",
            channel: 'business', //查看渠道，默认就传这个值即可，表示是从业务功能传过来的，首环节提交后过滤掉撤回按钮
            view: 'new',
          }

          if (me.$route.query.origin !== undefined && me.$route.query.origin === 'oa')
          {
            me.$set(urlParam, "origin", "oa")
            me.$set(urlParam, "fullScreen", "true")
          }

          this.layout.openNewTab("审批信息",
              "design_page",
              "design_page",
              tabId,
              urlParam
          )
        })
      } else {
        taskApi.selectFunctionId({functionCode: 'firm_main_change'}).then(res => {
          const functionId = res.data.data[0].ID
          const tabId = this.utils.createUUID()

          let urlParam = {
            processDefinitionKey: this.mcpDesignPage.processKey,
            functionId: functionId,
            entranceType: "FLOWABLE",
            ...this.utils.routeState.VIEW(this.mainData.id),
            channel: 'business',
            view: 'new',
          }

          if (me.$route.query.origin !== undefined && me.$route.query.origin === 'oa')
          {
            this.$set(urlParam, "origin", "oa")
            this.$set(urlParam, "fullScreen", "true")
          }

          this.layout.openNewTab("审批信息",
              "design_page",
              "design_page",
              tabId,
              urlParam
          )
        })
      }
    },
    printClick(){
      this.$print(this.$refs.dataForm)
    },
    approvalOpinionIs(){
      orgApi.roleCheck({ orgId: this.orgContext.currentOrgId, roleName: 'LDYJ' }).then(res => {
        console.log('approvalOpinionIs：',res)
        this.approvalIs = !(this.type !== 'toDeal' || res.data.data === false)
      })
    },
  }
}
</script>

<style scoped>
.el-dialog-div {
  height: 60vh;
  overflow: auto;
}

</style>
