package com.klaw.service.imp.complianceRiskServiceImp;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.klaw.dao.complianceRiskDao.RiskWarningDataBaseMapper;
import com.klaw.entity.complianceRiskBean.RiskWarningDataBase;
import com.klaw.service.complianceRiskService.RiskWarningDataBaseService;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service
public class RiskWarningDataBaseServiceImpl extends ServiceImpl<RiskWarningDataBaseMapper, RiskWarningDataBase> implements RiskWarningDataBaseService {
    @Override
    public Page page(QueryWrapper queryWrapper, JSONObject jsonObject) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        int page = (int) innerMap.get("page");
        int pageSize = (int) innerMap.get("limit");
        Page page1 = new Page(page, pageSize);
        Page result = this.page(page1, queryWrapper);
        return result;
    }
}
