import {request} from '@/api/index'

export default {
  query(data) {
    return request({
      url: '/project/query',
      method: 'post',
      data
    })
  },
  queryInternal(data) {
    return request({
      url: '/project/queryInternal',
      method: 'post',
      data
    })
  },
  save(data) {
    return request({
      url: '/project/save',
      method: 'post',
      data
    })
  },
  delete(data) {
    return request({
      url: '/project/delete',
      method: 'post',
      data
    })
  },
  queryById(data) {
    return request({
      url: '/project/queryById',
      method: 'post',
      data
    })
  },
  queryDialog(data) {
    return request({
      url: '/project/queryDialog',
      method: 'post',
      data
    })
  },
  checkOnly(data) {
    return request({
      url: '/project/checkOnly',
      method: 'post',
      data
    })
  },
  setParam(data) {
    return request({
      url: '/project/setParam',
      method: 'post',
      data
    })
  },
}