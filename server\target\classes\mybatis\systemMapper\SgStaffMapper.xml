<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.klaw.dao.mainDataDao.SgStaffMapper">


    <select id="queryPageData" resultType="java.util.Map">
        select
            s.code as "code",
            s.name as "name",
            u.user_account as "account",
            s.organization as "organization"
        from Sg_Staff s join SG_USER u on s.code = u.code
        <where>
            ${ew.sqlSegment}
        </where>
    </select>
</mapper>