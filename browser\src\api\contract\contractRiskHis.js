import {request} from '@/api/index'

export default {
    query(data) {
        return request({
            url: '/bm-contract-risk-his/query',
            method: 'post',
            data
        })
    },
   
    save(data) {
        return request({
            url: '/bm-contract-risk-his/save',
            method: 'post',
            data
        })
    },
    deleteById(data) {
        return request({
            url: '/bm-contract-risk-his/deleteById',
            method: 'post',
            data
        })
    },
    queryById(data) {
        return request({
            url: '/bm-contract-risk-his/getById',
            method: 'post',
            data
        })
    },

    updateById(data) {
        return request({
            url: '/bm-contract-risk-his/queryById',
            method: 'post',
            data
        })
    },
    loaddingRespUserData(data){
        return request({
            url: '/bm-contract-risk-his/loaddingRespUserData',
            method: 'post',
            data
        })
    }

}