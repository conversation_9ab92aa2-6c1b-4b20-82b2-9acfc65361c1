
1b2f95dcae87a562aa77e65aa61f274b8c285c03	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.370.1754018536329.js\",\"contentHash\":\"78ff76f1e5f63e30d63aad77f9b7b7ec\"}","integrity":"sha512-Tvowv2UwTqsf7pkTWUQrOcHPFRttq+kB1kgKjIC4QAEggUUmEGdHgOEX32E6YScDkV4YiZ8cvSY1KoMc2kH5Kg==","time":1754018575975,"size":69290}