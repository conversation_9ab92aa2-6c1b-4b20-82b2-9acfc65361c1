
3fb8c5d9ef88ea8550acd72a1d43387db1fc6a37	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.42.1754018536329.js\",\"contentHash\":\"7742e5326e160acab84a875e01bfb3f4\"}","integrity":"sha512-gYnafOqUzZgQbfuqHkm6Fsdo8jqSFEJfJjEuySg8PpiWrsQJdgCiRCCPwoVyRk8v/E6pKltTISSi1PVr5InSfQ==","time":1754018575955,"size":40843}