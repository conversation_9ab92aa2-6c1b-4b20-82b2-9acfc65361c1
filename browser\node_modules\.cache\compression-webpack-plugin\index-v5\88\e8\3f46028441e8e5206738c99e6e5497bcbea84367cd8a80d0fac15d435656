
9710ea82787748776cb2f9679dc5088d2eece772	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.26.1754018536329.js\",\"contentHash\":\"b7d075661344b2a75f15e69f63524138\"}","integrity":"sha512-zjBXSLDOnKz971C+964/OBOIK2DZFh3sye0jMXpio25NISil20eu7nHdvBUojtpUmGF+oHrskbxLPflNqf2Fsw==","time":1754018576084,"size":266373}