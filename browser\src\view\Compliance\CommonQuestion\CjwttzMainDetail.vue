<template>
	<div>
	<FormWindow ref="formWindow" @initData="initData" @loadData="loadData">
		<el-container v-loading="loading" style="height: calc(100vh - 84px)" :element-loading-text="loadingText">
			<el-main>
				<el-scrollbar style="height: 100%" wrap-style="overflow-x: hidden;">
					<!--数据表单块-->
					<el-form
						ref="dataForm"
						:model="mainData"
						:rules="!isView ? rules : {}"
						label-width="130px"
						:style="mainData.dataStateCode === utils.dataState_BPM.FINISH.code ? 'margin-right: 50px;' : ' margin-right: 10px;'">
						<el-row style="padding: 10px 0 10px 0; position: fixed; width: 100%; overflow-y: auto; background-color: white; z-index: 999">
<!--							<el-button v-if="!isView" type="primary" size="mini" icon="el-icon-folder-checked" @click="save_">保存</el-button>-->
							<el-button v-if="!isView" type="primary" size="mini" icon="el-icon-folder-checked" @click="submit_">提交</el-button>
						</el-row>
						<div style="padding-top: 50px"></div>
						<span style="text-align: left; font-size: 23px; margin-left: 43%; font-weight: 900">问题及解决措施明细</span>
						<!--基础信息块-->
						<qs-base-info :data.sync="mainData" :data-state="dataState" :authorization-data="authorizationData" :view="view" />
	

						<!--公共信息-->
						<OtherInfo :data.sync="mainData" :main-id="mainData.id" :data-state="dataState" style="margin-top: 20px" />

					</el-form>
				</el-scrollbar>
			</el-main>
			<!-- 选择模版 -->
			<!-- <Shortcut :templateShow="templateShow" @templateClick="templateClick" /> -->
		</el-container>
	</FormWindow>
	</div>
</template>

<script>
	// vuex缓存数据
	import { mapGetters } from 'vuex';

	// 接口api
	import CommonQuestionApi from '@/api/risk/CommonQuestion'
	import taskApi from '@/api/_system/task';
	// 组件
	import FormWindow from '@/view/components/FormWindow/FormWindow';
	import OtherInfo from '@/view/litigation/caseManage/case/caseChild/OtherInfo';
	import OrgSingleDialogSelect from '@/view/components/OrgSingleDialogSelect/index';
	import QsBaseInfo from './questionbaseInfo.vue';
	import CaseData from '@/view/litigation/caseManage/case/caseChild/CaseData';
	import CaseEvidenceData from '@/view/litigation/caseManage/case/caseChild/CaseEvidenceData';
	import Shortcut from '@/view/litigation/caseManage/caseExamine/Shortcut';
	import SimpleBoardTitleApproval from '@/view/components/SimpleBoard/SimpleBoardTitleApproval';

	export default {
		name: 'HgwhxcMainDetail',
		inject: ['layout', 'mcpLayout'],
		components: {
			SimpleBoardTitleApproval,
			CaseData,
			QsBaseInfo,
			OrgSingleDialogSelect,
			FormWindow,
			OtherInfo,
			CaseEvidenceData,
			Shortcut,
		},
		computed: {
			...mapGetters(['orgContext']),
			isView: function () {
				return this.dataState === this.utils.formState.VIEW;
			},
			templateShow() {
				return this.mainData.dataStateCode === this.utils.dataState_BPM.SAVE.code && this.dataState !== this.utils.formState.VIEW;
			},
		},
		data() {
			return {
				type: null,
				tabId: null,
				oarecordsDialog: false,
				loading: false,
				dataState: null,
				functionId: null, //终止的时候要用，需要手动关闭
				dataId: null,
				taskId: null,
				event:null,
				view: 'old',
				mainData: {
					evaluationYear: new Date().getFullYear().toString(),//报告年度
          category: null,//所属分类
          subCategory: null,//所属子类
          keyName: null, // 关键字
          questionDescription: null,//问题描述
          reasonOfOccurrence: null,//发生原因
          solution: null,//解决方案
					uploadAttachment: null,//相关附件
					releaseDate:null,//发布时间
					reviewStatus:null,//状态
					id: null, //主键
          documentNumber:null,//单据编号
					releasePerson:null,//发布人,
					releaseOrganization:null,//发布机构
					partiesList: [],
					claimList: [],
					otherDataList: [],
					relations: [],
					sealList: [],
					createOgnId: null, //当前机构ID
					createOgnName: null, //当前机构名称
					createDeptId: null, //当前部门ID
					createDeptName: null, //当前部门名称
					createGroupId: null, //当前部门ID
					createGroupName: null, //当前部门名称
					createPsnId: null, //当前人ID
					createPsnName: null, //当前人名称
					createOrgId: null, //当前组织ID
					createOrgName: null, //当前组织名称
					createPsnFullId: null, //当前人全路径ID
					createPsnFullName: null, //当前人全路径名称
					createPsnPhone: null, //经办人电话
					createTime: null, //创建时间
					// auditStatus:this.utils.dataState_BPM.SAVE.name,//状态
					// dataState: this.utils.dataState_BPM.SAVE.name, //数据状态
					// dataStateCode: this.utils.dataState_BPM.SAVE.code, //数据状态编码

				},
				rules: {
          category: [{ required: true, message: '请选择所属分类', trigger: 'blur' }],
          questionDescription: [{ required: true, message: '请输入问题描述', trigger: 'blur' }],
          reasonOfOccurrence: [{ required: true, message: '请输入发生原因', trigger: 'blur' }],
          solution: [{ required: true, message: '请输入解决方案', trigger: 'blur' }],
          keyName: [{ required: true, message: '请输入关键字', trigger: 'blur' }],

				},
				loadingText: '加载中...',
			};
		},
		methods: {
			initData(temp, dataState) {
				this.dataState = dataState;
				Object.assign(this.mainData, temp);

				let obj = this.utils.getManagementUnit(this.mainData.createPsnFullName, this.mainData.createPsnFullId);
				this.mainData.managementUnit = obj.name;
				this.mainData.managementUnitId = obj.id;
				this.mainData.unitType = obj.unitType;
				this.mainData.unitTypeId = obj.unitTypeId;

				this.view = 'old';

				let year = new Date().getFullYear();
				this.utils.createKvsequence('CJWTTZ' + year, 6).then((value) => {
					this.mainData.documentNumber = value.data.kvsequence;
				});

				this.loading = false;
			},
			loadData(dataState, dataId) {
				this.functionId = this.$route.query.functionId;
				if (this.$route.query.view !== undefined && this.$route.query.view !== '') this.view = this.$route.query.view;
				if (this.$route.query.type !== undefined && this.$route.query.type !== '') this.type = this.$route.query.type;
				this.dataState = dataState;
        CommonQuestionApi.queryById(dataId).then((response) => {
					this.mainData = response.data.data;
					this.authorizationData = response.data.data.authorization;
					if (response.data.data.authorization !== null) this.authorizationList = response.data.data.authorization.authorizationLitigationList;
					this.loading = false;
				});
			},
			// },
			save() {
				return new Promise((resolve, reject) => {
					//判断 releasePerson 和 releaseOrganization 是否有空值
					if (!this.mainData.releasePerson || !this.mainData.releaseOrganization) {
						this.mainData.releasePerson = this.mainData.createPsnName; // 评价人
						this.mainData.releaseOrganization = this.mainData.createDeptName; // 发布单位
					} else {
						this.mainData.releasePerson = this.mainData.releasePerson; // 评价人
						this.mainData.releaseOrganization = this.mainData.releaseOrganization; // 发布单位
					}
					this.mainData.reviewStatus = '已编辑';
          CommonQuestionApi
						.save(this.mainData)
						.then((response) => {
							resolve(response);
						})
						.catch((error) => {
							reject(error);
						});
				});
			},
			submit() {
				return new Promise((resolve, reject) => {
					//判断 releasePerson 和 releaseOrganization 是否有空值
					if (!this.mainData.releasePerson || !this.mainData.releaseOrganization) {
						this.mainData.releasePerson = this.mainData.createPsnName; // 评价人
						this.mainData.releaseOrganization = this.mainData.createDeptName; // 发布单位
					} else {
						this.mainData.releasePerson = this.mainData.releasePerson; // 评价人
						this.mainData.releaseOrganization = this.mainData.releaseOrganization; // 发布单位
					}
					this.mainData.reviewStatus = '已提交';
					this.mainData.releaseDate = new Date();
          CommonQuestionApi
						.save(this.mainData)
						.then((response) => {
							resolve(response);
						})
						.catch((error) => {
							reject(error);
						});
				});
			},
			// save_() {
			// 	this.save().then(() => {
			// 		this.mainData.reviewStatus = '已编辑';
			// 		this.$message.success('保存成功!');
			// 	});
			// },
			submit_() {
        this.$refs['dataForm'].validate((valid) => {
            if (valid) {
                this.submit().then(() => {
                    this.mainData.reviewStatus = '已提交';
                    this.$message.success('提交成功!');
					this.mcpLayout.closeTab();//关闭页面
					const uuid = this.utils.createUUID();//打开新页面
					this.layout.openNewTab('常见问题台账', 'cjwttz_index', 'cjwttz_index', uuid)
                });
            } else {
                this.$message.error('表单填写不完整，请检查后重新提交!');
            }
        });
    },
	saveCurrentTime() {
      const now = new Date();
      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, '0');
      const day = String(now.getDate()).padStart(2, '0');
      const hours = String(now.getHours()).padStart(2, '0');
      const minutes = String(now.getMinutes()).padStart(2, '0');
      const seconds = String(now.getSeconds()).padStart(2, '0');
    },
}}
</script>

<style scoped></style>
