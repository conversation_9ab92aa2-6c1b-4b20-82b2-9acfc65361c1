
978ea1962a6278dc7c531ced85d00fe17dbf5455	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.67.1754018536329.js\",\"contentHash\":\"c8b550e8c24287b830fe0f731de1c6d8\"}","integrity":"sha512-DMhPgFH9ZVuFXiszdwjHxhbK2m3lGxEIQvqbmloFy4WgCrVXZUEQfvqIOWU2zTrhshVOl12+aedRk0vvY23JhQ==","time":1754018575959,"size":59257}