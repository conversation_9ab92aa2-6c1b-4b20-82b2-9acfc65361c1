<template>
  <FormWindow ref="formWindow">
    <el-container v-loading="loading" :element-loading-text="loadingText" style="height: calc(100vh - 150px);">
      <el-main id="scrollbar">
        <el-card ref="printRef" style="height: auto;margin-left: 1%;margin-right: 2%;margin-top: 1%;">
          <el-scrollbar style="height: 100%;overflow-y: hidden">
            <el-form ref="dataForm"
                     :class="className"
                     :model="mainData" :rules="rules" style="margin-left: 10px;margin-right: 10px;">
              <el-row style="margin-top: 20px;">
                <span
                    style="text-align: left;font-size: 23px;margin-left: 36%;font-weight: 900;">{{
                    this.mainData.reviewCategoryName
                  }}审批流程</span>
              </el-row>
              <SimpleBoardTitleApproval title="基本信息">
                <table  class="table_content">
                  <tbody>
                  <tr>
                    <th class="th_label_approval" colspan="3">事项名称</th>
                    <td class="td_value_approval" colspan="9">{{ mainData.reviewSubject }}</td>
                    <th class="th_label_approval" colspan="3">事项编号</th>
                    <td class="td_value_approval" colspan="9">{{ mainData.reviewNumber }}</td>
                  </tr>
                  <tr>
                    <th class="th_label_approval" colspan="3">论证分类</th>
                    <td class="td_value_approval" colspan="9">{{ mainData.argumentClassificationName }}</td>
                    <th class="th_label_approval" colspan="3">拟开展业务模式</th>
                    <td class="td_value_approval" colspan="9">{{ mainData.proposedBusinessModel }}</td>
                  </tr>
                  <tr>
                    <th class="th_label_approval" colspan="3">业务领域</th>
                    <td v-if="mainData.businessArea !== '其他'" class="td_value_approval" colspan="21" style="white-space: normal">{{ mainData.businessArea }}</td>
                    <td v-else class="td_value_approval" colspan="21" style="white-space: normal">{{ mainData.businessArea }}</td>
                  </tr>
                  <tr v-if="mainData.argumentClassificationName == '遇到其他难以处理的合规风险'">
                    <th class="th_label_approval" colspan="3">风险描述</th>
                    <td class="td_value_approval" colspan="21">{{ mainData.otherArgumentClassification }}</td>
                  </tr>
                  <tr>
                    <th class="th_label_approval_" colspan="3">事项简介</th>
                    <td class="td_value_approval_" colspan="21">{{ mainData.matterDescription }}</td>
                  </tr>
                  </tbody>
                  <tbody>
                  <tr>
                    <th class="th_label_approval" colspan="3">相关资料</th>
                    <td class="td_value_approval" colspan="21">
                      <div v-if="mainData.reviewMaterials">
                        <UploadDoc :disabled="true" :files.sync="mainData.reviewMaterials" doc-path="/case"/>
                      </div>
                      <div v-else style="font-size: 15px">无</div>
                    </td>
                  </tr>
                  </tbody>
                </table>
              </SimpleBoardTitleApproval>
              <SimpleBoardTitleApproval v-if="this.demObj != null && this.demObj === '是'" title="合规论证">
                <table class="table_content">
                  <tbody>
                  <tr>
                    <th class="th_label_approval" colspan="3">论证报告</th>
                    <td class="td_value_approval" colspan="21">
                      <UploadDoc v-model="mainData.argumentationReport" :disabled="this.$route.query.type ==='haveDealt'" :files.sync="mainData.argumentationReport"
                                 doc-path="/case"/>
                    </td>
                  </tr>
                  <tr>
                    <th class="th_label_approval" colspan="3">论证材料</th>
                    <td class="td_value_approval" colspan="21">
                      <UploadDoc v-model="mainData.reportFiles" :disabled="this.$route.query.type ==='haveDealt'" :files.sync="mainData.reportFiles" doc-path="/case"/>
                    </td>
                  </tr>
                  </tbody>
                </table>
              </SimpleBoardTitleApproval>

              <!--审批历史 -->
              <SimpleBoardTitleApproval class="print-table-wrap" style="margin-top: 10px;" title="审查意见">
                <ProcessOpinion :proc-inst-id="this.obj.processInstanceId" :task-id="this.obj.taskId"
                                style="border: 1px solid #606266;" type-code="1"/>
                <div v-if="approvalIs && isParseElement">
                  <el-input
                      v-model="approvalOpinion"
                      :rows="2"
                      placeholder="请输入审批意见"
                      style="border-radius: 0 !important;"
                      type="textarea">
                  </el-input>
                </div>
              </SimpleBoardTitleApproval>

              <SimpleBoardTitleApproval class="leadership-opinions-section-wrap" style="margin-top: 10px;"
                                        title="领导意见">
                <div style="border: solid 1px #606266;overflow: hidden">
                  <ProcessOpinion :proc-inst-id="this.obj.processInstanceId" :show-header="false"
                                  :task-id="this.obj.taskId" type-code="2"/>
                  <div v-if="approvalIs && isParseElementlg">
                    <el-input
                        v-model="approvalOpinion"
                        :rows="2"
                        placeholder="请输入审批意见"
                        style="border-radius: 0 !important;"
                        type="textarea">
                    </el-input>
                  </div>
                </div>
              </SimpleBoardTitleApproval>

            </el-form>
          </el-scrollbar>
        </el-card>
      </el-main>
      <oa-records-dialog :dataid.sync="oaid" :visible.sync="oarecordsDialog"/>
      <Shortcut :detail-show="detailShow"
                :noticeShow="noticeShow" :print-show="printShow"
                @detailClick="detailClick"
                @noticeClick="noticeClick" @printClick="printClick"/>
    </el-container>
  </FormWindow>
</template>

<script>
// vuex缓存数据
import {mapGetters} from 'vuex'

// 接口api
import complianceReviewApi from '@/api/ComplianceReview/complianceReview.js';
import processApi from '@/api/_system/process'
import taskApi from "@/api/_system/task"
import noticeApi from "@/api/_system/notice";
import orgApi from '@/api/_system/org'
// 组件
import FormWindow from '@/view/components/FormWindow/FormWindow.vue'
import OtherInfo from '@/view/litigation/caseManage/case/caseChild/OtherInfo.vue'
import OrgSingleDialogSelect from '@/view/components/OrgSingleDialogSelect/index.vue'
import ProsecutionDialog from './Hgbg/ProsecutionDialog.vue'
import QsBaseInfo from './Hgbg/qisubaseInfo.vue'
import Claim from '@/view/litigation/caseManage/case/caseChild/Claim.vue'
import Litigant from '@/view/litigation/caseManage/case/caseChild/Litigant.vue'
import CaseData from '@/view/litigation/caseManage/case/caseChild/CaseData.vue'
import CaseEvidenceData from '@/view/litigation/caseManage/case/caseChild/CaseEvidenceData.vue'
import OaRecordsDialog from '@/view/litigation/caseManage/caseProsecution/OarecordsDialog.vue'
import Shortcut from '@/view/litigation/caseManage/caseExamine/Shortcut.vue'
import relations from '@/view/litigation/caseManage/caseProsecution/child/Relations.vue'
import SimpleBoardTitle from "@/view/components/SimpleBoard/SimpleBoardTitle.vue"
import Authorization from '@/view/litigation/authorizationManage/authorization/child/AuthorizationLitigation.vue'
import ProcessOpinion from "@/view/components/ProcessOpinion/ProcessOpinion.vue";
import SimpleBoardTitleApproval from "@/view/components/SimpleBoard/SimpleBoardTitleApproval.vue";
import seal from '@/view/litigation/contractManage/contractApproval/child/seal.vue'
import UploadDoc from "@/view/components/UploadDoc/UploadDoc.vue";
import caseFile from "@/view/litigation/disputeManage/disputeProcess/disputeChild/CaseFile.vue";

export default {
  name: 'HgscMainTsjy',
  inject: ['layout', 'mcpLayout', 'mcpDesignPage'],
  components: {
    caseFile,
    UploadDoc,
    SimpleBoardTitleApproval,
    CaseData, Litigant, Claim, QsBaseInfo, ProsecutionDialog, OrgSingleDialogSelect,
    FormWindow, OtherInfo, CaseEvidenceData, OaRecordsDialog, Shortcut, relations,
    SimpleBoardTitle, Authorization, ProcessOpinion, seal
  },
  computed: {
    ...mapGetters(['orgContext']),
    isView: function () {
      return this.dataState === this.utils.formState.VIEW
    },
    isNotice() {
      const isNotice = this.$route.query.isNotice
      return this.mainData.dataStateCode !== this.utils.dataState_BPM.FINISH.code &&
          this.mainData.createPsnFullId === this.orgContext.currentPsnFullId && isNotice
    },
    noticeShow() {
      return this.mainData.dataStateCode === this.utils.dataState_BPM.FINISH.code
    },
    detailShow() {
      return this.view === 'new'
    },
    approvalShow() {
      return this.view === 'old'
    },
    printShow() {
      return this.view === 'new'
    },
    // 如果是从oa打开，则需需要加上这两个参数
    originOa() {
      let {origin, fullScreen} = this.$route.query
      return origin === 'oa' && fullScreen
    },
    downloadShow() {
      if (this.view === undefined) {
        return true
      }
      return this.view === 'new' || this.view === 'detail'
    },
    isParseElement() {
      return this.parseElement === '部门意见'
    },
    isParseElementlg() {
      return this.parseElement === '领导意见'
    }
  },
  data() {
    return {
      reviewCategory: null,
      approvalOpinion: '',
      parseElement: null,
      demObj: null,
      approvalIs: false,
      className: '',
      qpShow: false,
      object1: null,
      type: null,
      timer: false,
      tabId: null,
      oarecordsDialog: false,
      loading: false,
      dataState: null,
      functionId: null,//终止的时候要用，需要手动关闭
      dataId: null,
      taskId: null,
      oaid: null,
      authorizationData: {},//授权信息
      authorizationList: [],//授权受托人信息
      view: 'new',
      create: '',
      mainData: {
        id: null, //主键
        reviewCategory: null,//审查类别
        reviewCategoryName: null,//审查类别
        reviewSubject: null,//事项题目
        reviewNumber: null,//事项编号
        isComplianceReviewed: null,//是否已合规审查
        hasProposalChanged: null,//议案是否有变化
        businessArea: null,//业务领域
        institutionalType: null, //制度类型
        otherBusinessArea: null, // 拟开展业务模式
        otherArgumentClassification: null, //风险描述
        argumentationReport: null, // 论证报告
        reportFiles: null, // 论证材料
        proposedBusinessModel: null, // 当业务领域选择“其他”时需填写此项
        matterDescription: null,//事项简介
        reviewMaterials: null,//相关附件
        basisForSystemFormulation: null,//制度制定依据
        relatedSignificantReview: null,//关联重大事项审查
        requiresTripleOneMeeting: null,//三重一大
        argumentClassification: null,//论证分类
        argumentClassificationName: null,//论证分类
        submittingEntity: null,//送审对象
        submittingEntityName: null,//送审对象
        requirementDocument: null,//需求文件
        requirementDocumentName: null,//需求文件
        createOgnId: null, //当前机构ID
        createOgnName: null, //当前机构名称
        createDeptId: null, //当前部门ID
        createDeptName: null, //当前部门名称
        createGroupId: null, //当前部门ID
        createGroupName: null, //当前部门名称
        createPsnId: null, //当前人ID
        createPsnName: null, //当前人名称
        createOrgId: null, //当前组织ID
        createOrgName: null, //当前组织名称
        createPsnFullId: null, //当前人全路径ID
        createPsnFullName: null, //当前人全路径名称
        createPsnPhone: null, //经办人电话
        checkList: [],// 合规审查
        examinationLists: [],// 合规审查
        createTime: null, //创建时间
        dataState: this.utils.dataState_BPM.SAVE.name, //数据状态
        dataStateCode: this.utils.dataState_BPM.SAVE.code, //数据状态编码
        originalComplianceReview: null, // 原事项题目
        decisionContent: null, // 议案正文
        requestText: null //请示正文
      },

      orgTreeDialog: false,
      orgDialogTitle: '组织信息',
      isAssign: false,

      prosecutionDialog: false,

      rules: {
        caseName: [
          {required: true, message: '请填写事项名称', trigger: 'blur'}
        ],
        belongPlate: [
          {required: true, message: '请选择所属单位', trigger: 'blur'}
        ],
        application: [
          {required: true, message: '请选择申请事项', trigger: 'blur'}
        ],
        projectName: [
          {required: true, message: '请填写项目名称', trigger: 'blur'}
        ]
      },

      activity: null,//记录当前待办处于流程实例的哪个环节
      obj: {// 流程处理逻辑需要的各种参数
        taskId: null,
        processInstanceId: null,
        businessKey: null,
        title: null,
        functionName: '合规审查',
        functionCode: null,
        sid: null,
      },

      noticeParams: {},
      noticeData: {
        moduleName: '', // 模块名称
        dataId: '', // 数据ID
        url: '', // 地址
        title: '', // 地址
        params: {} // 其他参数
      },

      loadingText: '加载中...'
    }
  },
  mounted() {
    window.vmh = this;
    //挂载完毕后，设置回调函数
    this.$emit('setCallBack', {
      beforeCallBack: this.beforeApproval,//点击办理或提交前的回调，效验必填控制
      afterCallBack: this.afterApproval,//点击办理弹框中再点击确定后的回调，审批完成后处理业务逻辑
      setTaskNodeInfo: this.setTaskNodeInfo,//挂载完毕后执行的回调，用于页面已进入需要处理的业务逻辑
      filterBtn: this.utils.filterBtn,
      beforeConfirmCallBack: this.beforeConfirmCallBack,
      beforeApprovalCb: this.beforeApprovalCb
    })
  },
  provide() {
    return {
      parentCase: this
    }
  },
  created() {
    //因为是流程功能，知会、抄送，都需要按照流程抄送的配置打开，
    // 只是知会的需要更新消息表，抄送需要更新日志表
    //判断是知会还是抄送，可以根据param中的参数isNotice判断

    this.obj.functionName = '合规审查';
    const isNotice = this.$route.query.isNotice
    // isNotice在OA中打开会解析成Boolean，系统内会被转成字符"true"
    if (isNotice === true || isNotice === "true") {
      //知会
      if (this.$route.query.processInstanceId !== null && this.$route.query.processInstanceId !== undefined) {
        //保存的数据，获取不到实例ID，只有启动流程实例才能拿到
        this.obj.processInstanceId = this.$route.query.processInstanceId
        this.obj.taskId = this.$route.query.taskId
      }
      const read = this.$route.query.read
      const sid = this.$route.query.sid
      if (read === false || read === 'false') {
        noticeApi.read({sid: sid})
      }
    } else {
      //这里除了抄送会走，其他正常逻辑也会走，所以下面的参数判断了type === 'toRead'，即未读时，才会更新OA消息和日志记录
      this.obj.sid = this.$route.query.sid//消息表中的消息ID 日志表中的日志ID
      if (this.$route.query.processInstanceId !== null && this.$route.query.processInstanceId !== undefined) {
        //保存的数据，获取不到实例ID，只有启动流程实例才能拿到
        this.obj.processInstanceId = this.$route.query.processInstanceId
        this.obj.taskId = this.$route.query.taskId
      }
      const type = this.$route.query.type//获取消息状态类型，toRead-》未读，此时需要更新，haveRead-》已读，就不需要更新了
      if (type === 'toRead') {
        this.obj.pathname = window.location.pathname
        //返回url路径名（https://www.runoob.com/try/try.php?filename=tryjsref_loc_pathname，返回/try/try.php），判断是在法务系统打开还是在OA中打开
        //法务中打开会把相同流程实例的全部消息改为已读，所以是更新OA多条，OA中打开是只更新OA一条
        this.obj.title = "案件风险审批"
        //更新OA需要参数processInstanceId, title, functionName, oldTaskId,
        processApi.finishOATask(this.obj)
      }

    }
  },
  methods: {
    setTaskNodeInfo(event) {
      const customProperties = event.customProperties.find(item => item.key === 'FlowCustomPropertiesSettingsServiceImpl')
      if (customProperties !== null && customProperties !== undefined) {
        this.parseElement = JSON.parse(customProperties.value)['name']
      }
      console.log("部门意见：" + this.parseElement)
      const demonstration = event.customProperties.find(item => item.key === 'FlowDemonstrationServiceImpl')
      if (demonstration !== null && demonstration !== undefined && demonstration.value) {
        this.demObj = JSON.parse(demonstration.value)['name']
      }
      console.log("部门意见：" + this.parseElement + "，论证节点：" + this.demObj)
      //（toDeal-待办处理，haveDealt-已办查看，toRead-未读，haveRead-已读）
      const type = this.$route.query.type
      // 业务ID
      const id = this.$route.query.businessKey
      //是否首环节（submitNode-首环节）
      this.activity = event.taskNodeType
      /*
      * 首环节
      *   1、businessKey-是null，说明是刚发起---执行init方法
      *   2、businessKey-有值
      *       1、回退处理--环节是submitNode   ---执行init
      *       2、已办查看--优先判断  haveDealt---执行load
      *       3、未读查看--优先判断  toRead   ---执行load
      *       4、已读查看--优先判断  haveRead ---执行load
      * */
      if (type === 'haveDealt' || type === 'toRead' || type === 'haveRead') {
        this.loadData(this.utils.formState.VIEW, id)
      } else { // 不是以上3种只能是待处理，只需要判断是否首环节即可
        if (event.taskNodeType === 'submitNode') {
          this.loadData(this.utils.formState.NEW, id)
        } else {
          this.loadData(this.utils.formState.VIEW, id)
        }
      }
    },
    loadData(dataState, dataId) {
      debugger
      this.functionId = this.$route.query.functionId
      if (this.$route.query.view !== undefined && this.$route.query.view !== '')
        this.view = this.$route.query.view
      if (this.$route.query.type !== undefined && this.$route.query.type !== '')
        this.type = this.$route.query.type
      if (this.$route.query.create !== undefined && this.$route.query.create !== '')
        this.create = this.$route.query.create
      this.reviewCategory = this.$route.query.type;
      this.dataState = dataState;
      complianceReviewApi.queryById(
          this.$route.query.businessKey
      ).then((res) => {
        console.log("----------------------------------");
        console.log(res.data);
        this.mainData = res.data.data;
        this.reviewCategory = res.data.data.reviewCategory
      })
      // ComplianceAccountabilityApi.queryById(dataId).then(response => {
      //   this.mainData = response.data.data
      //   this.authorizationData = response.data.data.authorization
      //   if (response.data.data.authorization !== null)
      //     this.authorizationList = response.data.data.authorization.authorizationLitigationList
      //   this.loading = false
      // }).then(() => {
      //   this.approvalOpinionIs()
      // })
    },
    //提交前 校验必填，resolve--校验成功或者失败需要将结果返回
    beforeApproval(code, resolve) {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.save().then(response => {
            this.mainData.title = this.mainData.reviewSubject
            this.mainData.code = code === null ? '' : code
            this.mainData.functionName = '合规审查'
            this.$emit('submit-success', this.mainData, 'id')
            this.$emit('setCallBack', {
              variables: {formData: {...this.mainData}}
            })
            resolve({success: true, formData: {...this.mainData}, approvalOpinion: this.approvalOpinion})
          })
        } else {
          resolve(false)
          this.$nextTick(function () {
            document.querySelector('.is-error').scrollIntoView(false)
          })
          return false
        }
      })
    },
    //撤回转办
    beforeApprovalCb(code) {
      debugger
      if (code.type === "cancelTransfer") {
        this.obj.processInstanceId = this.$route.query.processInstanceId
        this.obj.taskId = this.$route.query.taskId
        this.obj.title = this.mainData.reviewSubject
      this.obj.code = code === null ? '' : code
      this.obj.functionName = '合规审查'
        let pathname = window.location.pathname
        this.obj.functionCode = 'hgsc_main_tsjy'
        processApi.sendOATask(this.obj).then(res => {
          if (pathname === '/base/design_page') {
            this.mcpLayout.closeTab()
          } else {
            window.close()
          }
        })
      }

    },
    afterApproval(code, data) {
      // 回退到首环节，修改业务数据状态，修改任务标题
      console.log("code==" + code)
      console.log("data==" + data)
      this.obj.businessKey = this.mainData.id
      //获取参数，为后续操作准备，processInstanceId和taskId其实在created中赋值了，这里在赋值一次也行
      if (data != null && data.data != null) {
        this.obj.processInstanceId = data.data.id
      }
      if (this.obj.businessKey == null || this.obj.processInstanceId == null) {
        this.obj.processInstanceId = this.$route.query.processInstanceId
        this.obj.taskId = this.$route.query.taskId
      }

      //需要传参数到流程中，这里操作，不限于首环节传参数
      if (this.activity === 'submitNode') {
        complianceReviewApi.setParam(this.obj).then(response => {
          console.log("传值成功")
        })
      }
      // 将部分参数传给OA
      this.obj.title = this.mainData.reviewSubject
      this.obj.code = code === null ? '' : code
      this.obj.functionName = '合规审查'
      //不是动态节点，给OA传待办
      if (code !== 'dynamic') {
        let loading = this.$loading({
          target: document.querySelector('.sg-page-wrap'),
          lock: false,
          text: '请稍后...',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        })
        processApi.sendOATask(this.obj).then(res => {
          loading.close()
          if (this.originOa) {
            window.close()
          }
          /*let pathname = window.location.pathname
          if (pathname.indexOf('/design_pages') !== -1) {
            window.close()
          }*/
        })
      }
    },
    beforeConfirmCallBack(data, resolve, reject) {
      const customProperties = data.nodeInfo.customProperties.find(item => item.key === 'FlowCustomPropertiesSettingsServiceImpl')
      if (customProperties && customProperties.value && JSON.parse(customProperties.value)['id'] === "1" && data.approvalFormData.comment == "") {
        // 消息按需求编辑 这只是个示例
        this.$confirm('未填写意见，请确认是否继续。', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          resolve()()
        }).catch(() => {
          reject()
        })
      } else {
        resolve()
      }
    },
    handleConditionBranch(branch, resolve) {
      console.log('大写：' + JSON.stringify(branch))
      for (const b of branch) {
        let el = b.sequenceFlows[0].conditionExpression
      }
      let aa = branch[0]
      resolve([aa])
    },
    submit() {
      this.save().then(response => {
        this.$emit('submit-success', this.mainData, 'id')
        this.$message.success('保存成功!')
      })
    },
    save() {
      return new Promise((resolve, reject) => {
        complianceReviewApi.save(this.mainData).then(response => {
          resolve(response)
        }).catch(error => {
          reject(error)
        })
      })
    },

    stopClick() {
      this.$confirm('您确定要终止当前流程吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let processInstanceId = this.$route.query.processInstanceId
        this.obj.processInstanceId = this.$route.query.processInstanceId
        this.obj.taskId = this.$route.query.taskId
        this.obj.title = this.mainData.name
        this.obj.functionName = '合规审查'
        this.obj.code = 'stop'
        let pathname = window.location.pathname
        new Promise((resolve, reject) => {
          let processInstanceId = this.$route.query.processInstanceId
          processApi.end(processInstanceId).then(res => {
            resolve(res)
          })
        }).then(val => {
          if (val.data.code === 200) {
            this.obj.functionCode = 'case_risk_main'
            processApi.sendOATask(this.obj).then(res => {
              if (pathname === '/base/design_page') {
                this.mcpLayout.closeTab()
              } else {
                window.close()
              }
            })
          }
        })
      }).catch(() => {
        this.$message.info('已取消删除!')
      })
    },
    finishClick() {
      this.$confirm('您确定要结束当前流程吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let processInstanceId = this.$route.query.processInstanceId
        this.obj.processInstanceId = this.$route.query.processInstanceId
        this.obj.taskId = this.$route.query.taskId
        this.obj.title = this.mainData.name
        this.obj.functionName = '合规审查'
        this.obj.code = 'pass'
        let pathname = window.location.pathname
        new Promise((resolve, reject) => {
          processApi.move({
            proInstId: this.$route.query.processInstanceId,
            taskId: this.$route.query.taskId
          }).then(res => {
            resolve(res)
          })
        }).then(val => {
          if (val.status === 200) {
            this.obj.functionCode = 'case_risk_main'
            processApi.sendOATask(this.obj).then(res => {
              if (pathname === '/base/design_page') {
                this.mcpLayout.closeTab()
              } else {
                window.close()
              }
            })
          }
        })
      }).catch(() => {
        this.$message.info('已取消删除!')
      })
    },
    // 发起知会
    noticeClick(cooperateFunc) {
      //必传参数
      this.noticeParams = {
        ...this.utils.routeState.VIEW(this.mainData.id),//主要作用是后台逻辑需要，其他作用各业务看情况使用
        processInstanceId: this.$route.query.processInstanceId,//流程实例
        taskId: this.$route.query.taskId,//任务ID
        businessKey: this.mainData.id, //业务数据ID
        entranceType: "FLOWABLE", //流程特定的标识
        type: "haveRead",//已处理
        whetherProcess: true,//系统内部打开的时候判断是否是流程，通过不同的方式打开查看
        isNotice: true//告知是知会消息打开的功能，有些按钮可以隐藏,只有知会消息，在created钩子函数中才执行相关，如果是流程抄送不需要传，可以根据上面created描述判断
      }
      this.noticeData.dataId = this.mainData.id
      this.noticeData.moduleName = '风险告知审批'
      this.noticeData.params = this.noticeParams
      this.noticeData.url = 'case_risk_main'//这个需要与功能维护中的值一样
      this.noticeData.title = this.mainData.caseName
      cooperateFunc(this.noticeData)
    },
    //选择模板
    templateClick(val) {
      if (val) {
        this.prosecutionDialog = true
      }
    },
    prosecutionSelect(data) {
      this.mainData.caseName = data.caseName //事项名称
      this.mainData.causeOfInId = data.causeOfInId//案件类型ID
      this.mainData.causeOfIn = data.causeOfIn//案件类型
      this.mainData.causeOfInDes = data.causeOfInDes//案件类型说明
      this.mainData.caseMoney = data.caseMoney //标的额(元)
      this.mainData.projectName = data.projectName//项目名称
      this.mainData.whetherMajor = data.whetherMajor//是否重大案件
      this.mainData.venueIsOut = data.venueIsOut //是否集团内部诉讼
      this.mainData.venueAddress = data.venueAddress //详细地址
      this.mainData.venueProvince = data.venueProvince //省
      this.mainData.venueCity = data.venueCity //市
      this.mainData.venueRegion = data.venueRegion //区
      this.mainData.caseUndertaker = data.caseUndertaker//法务承办人
      this.mainData.caseUndertakerId = data.caseUndertakerId//法务承办人id
      this.mainData.ognPackage = data.ognPackage//单位包案领导
      this.mainData.ognPackageId = data.ognPackageId//单位包案领导id
      this.mainData.groupPackage = data.groupPackage//集团包案领导
      this.mainData.groupPackageId = data.groupPackageId//集团包案领导id
      this.mainData.involvedLevel = data.involvedLevel//涉案单位管理层级
      this.mainData.involvedLevelId = data.involvedLevelId//涉案单位管理层级
      this.mainData.unitType = data.unitType//单位类型
      this.mainData.unitTypeId = data.unitTypeId//单位类型id
      this.mainData.currentUnit = data.currentUnit//当事单位
      this.mainData.currentUnitId = data.currentUnitId//当事单位id
      this.mainData.des = data.des//情况说明
      this.mainData.greatReason = data.greatReason//重大案件案发原因
      this.mainData.greatPlan = data.greatPlan//重大案件下一步工作计划

      this.mainData.partiesList = data.partiesList
      this.mainData.partiesList.forEach(item => {
        item.id = this.utils.createUUID()
        item.masterId = this.mainData.id
      })

      this.mainData.claimList = data.claimList
      this.mainData.claimList.forEach(item => {
        item.id = this.utils.createUUID()
        item.parentId = this.mainData.id
      })

      this.mainData.otherDataList = data.otherDataList
      this.mainData.otherDataList.forEach(item => {
        item.id = this.utils.createUUID()
        item.parentId = this.mainData.id
        item.files = null
      })

      this.mainData.relations = data.relations
      this.mainData.relations.forEach(item => {
        item.id = this.utils.createUUID()
        item.relationId = this.mainData.id
      })
    },
    detailClick() {
      let row = this.mainData
      this.layout.openNewTab(row.reviewCategoryName, 'hgsc_main_detail_tsjy', 'hgsc_main_detail_tsjy', row.id, {
        functionId: 'hgsc_main_detail_tsjy,' + row.id,
        ...this.utils.routeState.VIEW(row.id),
        view: 'old',
        reviewCategory: row.reviewCategory, reviewCategoryName: row.reviewCategoryName
      });
    },
    approvalClick() {
      const me = this
      if (this.mainData.dataStateCode !== this.utils.dataState_BPM.SAVE.code) {
        taskApi.selectTaskId({businessKey: this.mainData.id, isView: 'true'}).then(res => {
          const functionId = res.data.data[0].ID
          const tabId = this.utils.createUUID()

          let urlParam = {
            processInstanceId: res.data.data[0].PID,//流程实例
            taskId: res.data.data[0].ID,//任务ID
            businessKey: this.mainData.id, //业务数据ID
            functionId: functionId, //不传也可以，只有首环节需要传，实例启动就可以不传了
            entranceType: "FLOWABLE",
            type: "haveDealt",
            channel: 'business', //查看渠道，默认就传这个值即可，表示是从业务功能传过来的，首环节提交后过滤掉撤回按钮
            view: 'new',
          }

          if (me.$route.query.origin !== undefined && me.$route.query.origin === 'oa') {
            me.$set(urlParam, "origin", "oa")
            me.$set(urlParam, "fullScreen", "true")
          }

          this.layout.openNewTab("审批信息",
              "design_page",
              "design_page",
              tabId,
              urlParam
          )
        })
      } else {
        taskApi.selectFunctionId({functionCode: 'case_risk_main'}).then(res => {
          const functionId = res.data.data[0].ID
          const tabId = this.utils.createUUID()

          let urlParam = {
            processDefinitionKey: this.mcpDesignPage.processKey,
            functionId: functionId,
            entranceType: "FLOWABLE",
            ...this.utils.routeState.VIEW(this.mainData.id),
            channel: 'business',
            view: 'new',
          }

          if (me.$route.query.origin !== undefined && me.$route.query.origin === 'oa') {
            this.$set(urlParam, "origin", "oa")
            this.$set(urlParam, "fullScreen", "true")
          }

          this.layout.openNewTab("审批信息",
              "design_page",
              "design_page",
              tabId,
              urlParam
          )
        })
      }
    },
    approvalOpinionIs() {
      orgApi.roleCheck({orgId: this.orgContext.currentOrgId, roleName: 'LDYJ'}).then(res => {
        console.log('approvalOpinionIs：', res)
        this.approvalIs = !(this.type !== 'toDeal' || res.data.data === false)
      })
    },
    queryHistoricalNode() {
      let taskId = this.$route.query.taskId
      taskApi.queryHistoricalNode({taskId: taskId}).then(res => {
        return res.data.data
      })
    },
    printClick() {
      this.$print(this.$refs.dataForm)
    },
  }
}
</script>

<style scoped>
.row_ {
  margin-top: 10px;
}

.label_ {
  margin-top: 10px;
  text-align: right;
  padding-right: 6px;
}

.title {
  font-size: 16px;
  font-weight: bold;
}

html, body, el-container, el-main, el-form, el-card {
  width: 100%;
}

/**
改变边框颜色
 */
.el-table {
  border-bottom: 1px solid black;
  border-right: 1px solid black;
  margin: 0 auto;
}

::v-deep.el-table th {
  //border: 1px solid black !important;
  font-size: 15px;
  color: gray;
  border: solid 1px #606266 !important;

  border-right: none !important;
  border-bottom: none !important;
  font-weight: 900;
}

::v-deep.el-table td {
  font-size: 15px;
  overflow-wrap: break-word;
  white-space: pre-wrap;
  word-wrap: break-word;
  text-align: left;
  color: black;

  border: 1px solid black;
  border-right: none !important;
  border-bottom: none !important;
}
</style>
