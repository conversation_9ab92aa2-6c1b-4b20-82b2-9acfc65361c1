
d40165c30d77f7d62952e66bc6af0853cba5ec81	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.82.1754018536329.js\",\"contentHash\":\"98dde805fc7d26f34005061adb62ae21\"}","integrity":"sha512-I/aL+bAXpYG1WRpq9oDI5WHwDBZMIKhoLDRFBqTNcwnDedsZxeaNagpUbCOL/uJpls3dlGVm0FZbUVLa/CxATw==","time":1754018575956,"size":47185}