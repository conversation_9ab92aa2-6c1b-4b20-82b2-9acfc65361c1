
8d2720167e6c29c39d1519f69d2110fd8c025c9c	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.316.1754018536329.js\",\"contentHash\":\"bccb1ea541f5d7ab1e8b37768756cfab\"}","integrity":"sha512-cm1OjP/Q9cg3OEH/fSTX2rY1zxYSnPVA1V1vFFC3+jCQkN1sp/OnHnh6qMeTS0+86KCz9xNL8ofe/EjePQoveA==","time":1754018576014,"size":118985}