
47865d899be6e1a206f68690ff1a209415d8e611	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.41.1754018536329.js\",\"contentHash\":\"30c3f6b2dc505f47cace8f664df7e569\"}","integrity":"sha512-RsAdwaADrzZE9qOnEn+xUOfBClj12R1tUUthDWRqxZJzrW5sEkfXqHeTvKmaIB9ADWehQ9eakLbOsLBnxBgUZA==","time":1754018575955,"size":45936}