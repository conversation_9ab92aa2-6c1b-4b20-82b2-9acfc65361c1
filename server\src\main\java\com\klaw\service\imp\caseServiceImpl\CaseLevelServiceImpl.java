package com.klaw.service.imp.caseServiceImpl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.klaw.entity.caseBean.CaseLevel;
import com.klaw.entity.caseBean.CaseSued;
import com.klaw.entity.caseBean.child.MiddleRelation;
import com.klaw.entity.caseBean.child.Parties;
import com.klaw.service.caseService.childService.MiddleRelationService;
import com.klaw.service.caseService.childService.PartiesService;
import com.klaw.utils.DataAuthUtils;
import com.klaw.utils.PageUtils;
import com.klaw.utils.Utils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.klaw.dao.caseDao.CaseLevelMapper;
import com.klaw.service.caseService.CaseLevelService;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Service
public class CaseLevelServiceImpl extends ServiceImpl<CaseLevelMapper, CaseLevel> implements CaseLevelService{

    @Resource
    private PartiesService partiesService;
    @Resource
    private MiddleRelationService middleRelationService;

    @Override
    public PageUtils<CaseLevel> queryPageData(JSONObject json) {
        QueryWrapper<CaseLevel> wrapper = new QueryWrapper<CaseLevel>();
        getFilter(json, wrapper);
        return page(new PageUtils<CaseLevel>(json), wrapper);
    }

    @Override
    public boolean saveData(CaseLevel caseLevel) {
        caseLevel.setUpdateTime(new Date());
        if(StringUtils.isNotBlank(caseLevel.getIds())){
            caseLevel.setOtherParties(this.queryRelationCaseParties(caseLevel.getIds()));
        }
        List<MiddleRelation> relations = caseLevel.getRelations();
        middleRelationService.saveData(caseLevel.getId(), new String[]{"caseLevel", "case"}, relations);
        boolean b = saveOrUpdate(caseLevel);
        return b;
    }

    @Override
    public void saveDataFinish(String businessKey) {

    }

    @Override
    public CaseLevel queryDataById(String id) {
        CaseLevel caseLevel = getById(id);
        Map<String, List<MiddleRelation>> map = middleRelationService.queryData(id, "caseLevel", new String[]{"case"});
        List<MiddleRelation> list = new ArrayList<MiddleRelation>();
        if(CollectionUtils.isNotEmpty(map.get("case"))){
            list.addAll(map.get("case"));
        }
        caseLevel.setRelations(list);
        return caseLevel;
    }

    @Override
    public boolean deleteDataById(String id) {
        middleRelationService.remove(new QueryWrapper<MiddleRelation>().eq("relation_id", "id").eq("relation_type", "caseLevel"));
        boolean b = removeById(id);
        return b;
    }
    //获取查询条件
    private void getFilter(JSONObject json, QueryWrapper<CaseLevel> wrapper) {
        //是否台账功能（登记只查自己的，台账和弹框查自己和数据权限的）
        boolean isQuery = json.containsKey("isQuery") ? json.getBoolean("isQuery") : false;
        String orgId = json.containsKey("orgId") ? json.getString("orgId") : "";
        //案件名称
        String caseName = json.containsKey("caseName") ? json.getString("caseName") : null;
        //风险等级
        String riskLevel = json.containsKey("riskLevel") ? json.getString("riskLevel") : null;
        //标的额
        BigDecimal moneyMin = json.containsKey("moneyMin") ? json.getBigDecimal("moneyMin") : BigDecimal.valueOf(0);
        BigDecimal moneyMax = json.containsKey("moneyMax") ? json.getBigDecimal("moneyMax") : BigDecimal.valueOf(0);
        //经办时间
        Date createTimeMin = json.containsKey("createTimeMin") ? json.getDate("createTimeMin") : null;
        Date createTimeMax = json.containsKey("createTimeMax") ? json.getDate("createTimeMax") : null;
        //收案时间
        Date caseStartTimeMin = json.containsKey("caseStartTimeMin") ? json.getDate("caseStartTimeMin") : null;
        Date caseStartTimeMax = json.containsKey("caseStartTimeMax") ? json.getDate("caseStartTimeMax") : null;
        //审批状态
        String dataState = json.containsKey("dataState") ? json.getString("dataState") : null;
        //经办部门
        String createDeptName = json.containsKey("createDeptName") ? json.getString("createDeptName") : null;
        //经办人
        String createPsnName = json.containsKey("createPsnName") ? json.getString("createPsnName") : null;

        //模糊搜索值
        String fuzzyValue = json.containsKey("fuzzyValue") ? json.getString("fuzzyValue") : null;
        //排序字段
        String sortName = json.containsKey("sortName") ? json.getString("sortName") : null;
        //排序字段是否升序
        boolean order = json.containsKey("order") ? json.getBoolean("order") : false;

        if (StringUtils.isNotBlank(caseName)) {
            wrapper.and(i -> i.like("case_name", caseName));
        }
        if (StringUtils.isNotBlank(riskLevel)) {
            wrapper.and(i -> i.like("risk_level", riskLevel));
        }
        /*标的额*/
        if (moneyMin != null && moneyMin.compareTo(BigDecimal.ZERO) != 0) {
            wrapper.and(i -> i.ge("CASE_MONEY", moneyMin));
        }
        if (moneyMax != null && moneyMax.compareTo(BigDecimal.ZERO) != 0) {
            wrapper.and(i -> i.le("CASE_MONEY", moneyMax));
        }
        /*经办时间*/
        if (createTimeMin != null) {
            wrapper.and(i -> i.ge("create_time", createTimeMin));
        }
        if (createTimeMax != null) {
            wrapper.and(i -> i.le("create_time", createTimeMax));
        }
        /*收立案时间*/
        if (caseStartTimeMin != null) {
            wrapper.and(i -> i.ge("case_time", caseStartTimeMin));
        }
        if (caseStartTimeMax != null) {
            wrapper.and(i -> i.le("case_time", caseStartTimeMax));
        }
        if (StringUtils.isNotBlank(createDeptName)) {
            wrapper.and(i -> i.like("create_dept_name", createDeptName));
        }
        if (StringUtils.isNotBlank(createPsnName)) {
            wrapper.and(i -> i.like("create_psn_name", createPsnName));
        }
        if (StringUtils.isNotBlank(dataState)) {
            wrapper.and(i -> i.eq("data_state", dataState));
        }

        //模糊搜索匹配字段
        String[] cols = {"case_name", "current_stage", "risk_level", "create_dept_name", "create_psn_name"};
        Utils.fuzzyValueQuery(wrapper, cols, fuzzyValue);
        Long functionId = DataAuthUtils.getFunctionIdByCode("case_level_ledger");
        if(isQuery){
            DataAuthUtils.dataPermSql(wrapper, null, functionId, orgId);
        }else{
            wrapper.like("create_psn_full_id", orgId);
        }

        if(org.apache.commons.lang.StringUtils.isNotBlank(sortName)){
            wrapper.orderBy(true, order, "to_char("+ Utils.humpToLine2(sortName)+")");
        }else{
            wrapper.orderBy(true, order,"create_time");
        }

    }

    private String queryRelationCaseParties(String ids)
    {
        String[] idArr = ids.split(",");
        String otherParties = "";

        for (String id : idArr)
        {
            List<Parties> parties = partiesService.list(new QueryWrapper<Parties>().eq("master_id", id));

            if (parties != null && parties.size() > 0)
            {
                for (Parties party : parties)
                {
                    if (!"原告".equals(party.getPartyType()))
                    {
                        otherParties +=  party.getParty() + ",";
                    }
                }
            }
        }

        if (org.apache.commons.lang.StringUtils.isNotBlank(otherParties))
        {
            otherParties = otherParties.substring(0, otherParties.length() - 1);
        }

        return otherParties;
    }
}
