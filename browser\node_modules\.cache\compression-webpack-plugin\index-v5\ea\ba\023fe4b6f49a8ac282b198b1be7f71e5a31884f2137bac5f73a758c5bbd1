
0bac9517840fa188d68bfe8cfa664419eaf5f9c8	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.252.1754018536329.js\",\"contentHash\":\"c1cdd71fa8c89b6bcfdd172c330864bf\"}","integrity":"sha512-MhOeep4h5bv0G5lPCBns4c6JeBJ7/cLn6ZcJ1ppTS2VjfJ1/KuT7Grh9wXhxqCecP9Qwyejge09Ja9q2nLH06g==","time":1754018576003,"size":131843}