<template>
  <el-container direction="vertical" class="container-manage-sg">
    <el-header>
      <el-card>
        <div>
          <el-input v-model="tableQuery.fuzzyValue" class="filter_input" placeholder="检索字段（报告主题、上报单位、报告类别、报告年度）"
            clearable @keyup.enter.native="refreshData" @clear="refreshData">
            <el-popover slot="prepend" placement="bottom-start" width="1000" trigger="click">
              <el-form ref="queryForm" label-width="100px" size="mini">
                <el-row>
                  <el-col :span="12">
                    <el-form-item label="报告主题">
                      <el-input v-model="tableQuery.reportSubject" clearable placeholder="请输入..." />
                    </el-form-item>
                  </el-col>
                  <!-- <el-col :span="8">
                                    <el-form-item label="风险发生部门" required>
                                      <el-input v-if="!isView" v-model="mainData.riskDepartment" placeholder="请选择" class="input-with-select"
                                                disabled>
                                        <el-button slot="append" icon="el-icon-search" @click="orgTreeDialog = true"/>
                                      </el-input>
                                      <span v-else class="viewSpan">{{ mainData.riskDepartment }}</span>
                                    </el-form-item>
                                  </el-col> -->
                  <el-col :span="12">
                    <el-form-item label="上报单位">
                      <el-input v-model="tableQuery.reportingUnit" placeholder="请选择" class="input-with-select">
                        <el-button slot="append" icon="el-icon-search" @click="showOrgTreeDialog" />
                      </el-input>
                      <!-- <span v-else class="viewSpan">{{ mainData.riskDepartment }}</span> -->
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-col>
                  <el-col :span="12">
                    <el-form-item label="报告类别">
                      <el-select v-model="tableQuery.reportCategory" clearable placeholder="请选择" style="width: 100%">
                        <el-option v-for="item in utils.compliance_report_type" :key="item.dicName"
                          :label="item.dicName" :value="item.dicName" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="上报日期">
                      <el-date-picker v-model="tableQuery.reportingDateMin" type="date" placeholder="选择日期"
                        style="width: 45%;float: left;" clearable />
                      <div style="width: 10%;float: left;text-align: center;" class="label_1"><span>至</span></div>
                      <el-date-picker v-model="tableQuery.reportingDateMax" type="date" placeholder="选择日期"
                        style="width: 45%;" clearable />

                    </el-form-item>
                  </el-col>
                </el-col>
                <el-button-group style="float: right">
                  <el-button type="primary" size="mini" icon="el-icon-search" @click="search_">搜索</el-button>
                  <el-button type="primary" size="mini" icon="el-icon-refresh-left" @click="empty_">重置</el-button>
                </el-button-group>
              </el-form>
              <!-- el-dialog 组件 -->
              <el-dialog :close-on-click-modal="false" title="选择部门" :visible.sync="dialogVisible" width="50%">
                <div class="el-dialog-div">
                  <orgTree :accordion="false" :is-checked-user="false" :show-user="false" :is-check="true"
                    :checked-data.sync="zxcheckedData" :is-not-cascade="true" :is-filter="true" />
                </div>
                <span slot="footer" class="dialog-footer">
                  <el-button icon="" class="negative-btn" @click="cancel">取消</el-button>
                  <el-button type="primary" icon="" class="active-btn" @click="choiceDeptSure">确定</el-button>
                </span>
              </el-dialog>
              <el-button slot="reference" size="small" type="primary">高级检索</el-button>
            </el-popover>
            <el-button slot="append" icon="el-icon-search" @click="search_" />
          </el-input>
        </div>

      </el-card>

    </el-header>

    <el-main>
      <SimpleBoardIndex :title="'法务风险合规报告'" :isButton="true" @addBtn="add_">
        <el-table ref="table" v-loading="tableLoading" :data="tableData" size="mini" border :height="table_height"
          stripe fit highlight-current-row :show-overflow-tooltip="true" row-key="id"
          style="table-layout: fixed;width: 100%;" @sort-change="tableSort" @row-dblclick="rowDblclick">
          <el-table-column type="index" width="50" label="序号" align="center" />
          <el-table-column v-if="ss.tableColumns.find(item => item.key === 'reportSubject').visible"
            prop="reportSubject" show-overflow-tooltip label="报告主题" min-width="250" />
          <el-table-column v-if="ss.tableColumns.find(item => item.key === 'reportYear').visible" prop="reportYear"
            show-overflow-tooltip label="报告年度" min-width="100" />
          <el-table-column v-if="ss.tableColumns.find(item => item.key === 'reportCategory').visible" label="报告类型"
            min-width="150" prop="reportCategory" sortable="custom">
            <template slot-scope="scope">
              {{ scope.row.reportCategory.join(', ') }}
            </template>
          </el-table-column>
          <el-table-column v-if="ss.tableColumns.find(item => item.key === 'reportKind').visible" prop="reportKind"
            show-overflow-tooltip label="报告种类" min-width="100" />
          <el-table-column v-if="ss.tableColumns.find(item => item.key === 'reporter').visible" show-overflow-tooltip
            prop="reporter" label="上报人" min-width="100" />
          <el-table-column v-if="ss.tableColumns.find(item => item.key === 'createTime').visible" prop="createTime"
            show-overflow-tooltip label="上报日期" min-width="100" sortable="custom">
            <template slot-scope="scope">
              <span>{{ scope.row.createTime | parseTime }}</span>
            </template>
          </el-table-column>
          <el-table-column v-if="ss.tableColumns.find(item => item.key === 'reportingUnit').visible"
            show-overflow-tooltip prop="reportingUnit" label="上报单位" min-width="250" />
          <el-table-column v-if="ss.tableColumns.find(item => item.key === 'dataState').visible" label="状态"
            min-width="100" prop="dataState" show-overflow-tooltip sortable="custom" />
<!--          <el-table-column v-if="ss.tableColumns.find(item => item.key === 'currentNode').visible" show-overflow-tooltip-->
<!--            prop="currentNode" label="当前节点" min-width="100" />-->
<!--          <el-table-column v-if="ss.tableColumns.find(item => item.key === 'nodeOperator').visible"-->
<!--            show-overflow-tooltip prop="nodeOperator" label="节点操作人" min-width="100" />-->

          <el-table-column label="操作" align="center" width="150" fixed="right">
            <template slot-scope="scope">
              <el-button type="text" v-if="isEdit(scope.row)" @click="edit_(scope.$index, scope.row)">编辑</el-button>
              <el-button type="text" @click="view_(scope.$index, scope.row)">查看</el-button>
              <el-button type="text" v-if="isDelete(scope.row)" @click="delete_(scope.$index, scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </SimpleBoardIndex>

    </el-main>
    <el-footer>
      <!--分页工具栏-->
      <pagination :total="tableQuery.total" :page.sync="tableQuery.page" :limit.sync="tableQuery.limit"
        @pagination="refreshData" />
    </el-footer>

  </el-container>
</template>

<script>
// 组件
import pagination from '@/view/components/Pagination/PaginationIndex'
import TableTools from '@/view/components/TableTools/index'
import SimpleBoardIndex from "@/view/components/SimpleBoard/SimpleBoardIndex";
import orgTree from '@/view/components/OrgTree/OrgTree'
// vuex状态值
import { mapGetters } from 'vuex'

// 接口api
import complianceReportApi from '@/api/risk/complianceReport.js'
import taskApi from '@/api/_system/task'

export default {
  name: 'HgbgIndex',
  inject: ["layout"],
  components: { pagination, TableTools, SimpleBoardIndex, orgTree },
  data() {
    return {
      tableQuery: {
        page: 1,
        limit: 10,
        total: 0,
        reportSubject: '', // 报告主题
        reportingUnit: '', // 上报单位
        reportCategory: '', // 报告类别
        reportYear: '', // 报告年度
        reportTime: '',//上报时间
        reportingDateMin: '',
        reportingDateMax: '',
        fuzzyValue: '', // 模糊搜索值
        orgId: '', // 上报单位id
      },
      table_height: '100%',
      tableData: [],
      dialogVisible: false,
      orgTreeDialog: false,
      zxcheckedData: [],
      orgVisible: false,
      tableLoading: false,
      ss: {
        data: this.tableData,
        tableColumns: [
          { key: 'reportSubject', label: '报告主题', visible: true },
          { key: 'reportYear', label: '报告年度', visible: true },
          { key: 'reportCategory', label: '报告类别', visible: true },
          { key: 'reportKind', label: '报告种类', visible: true },
          { key: 'createTime', label: '上报日期', visible: true },
          { key: 'reporter', label: '上报人', visible: true },
          { key: 'reportingUnit', label: '上报单位', visible: true },
          { key: 'currentNode', label: '当前节点', visible: true },
          { key: 'dataState', label: '状态', visible: true },
          { key: 'nodeOperator', label: '节点操作人', visible: true },
        ]
      },
      yearOptions: Array.from({ length: 10 }, (_, i) => new Date().getFullYear() - i)
    }
  },
  computed: {
    ...mapGetters([
      'orgContext', 'currentFunctionId'
    ])
  },
  activated() {
    // 长连接页面第二次激活的时候,不会走created方法,会走此方法
    this.refreshData()
  },
  created() {
    this.refreshData()
  },

  mounted() {
    this.$nextTick(function () {
      this.table_height = window.innerHeight - this.$refs.table.$el.offsetTop - 84 - 45

      // 监听窗口大小变化
      const self = this
      window.onresize = function () {
        self.table_height = window.innerHeight - self.$refs.table.$el.offsetTop - 84 - 45
      }
    })
  },
  methods: {
    isEdit(row) {
      return row.dataStateCode === this.utils.dataState_BPM.SAVE.code || row.dataStateCode === this.utils.dataState_BPM.BACK.code
    },
    isDelete(row) {
      return row.dataStateCode === this.utils.dataState_BPM.SAVE.code
    },
    // 刷新数据
    refreshData() {
      // 赋值当前人组织全路径
      this.tableQuery.functionCode = this.currentFunctionId.functionCode
      this.tableQuery.orgId = this.orgContext.currentOrgId
      this.tableQuery.currentPsnFullId = this.orgContext.currentPsnFullId
      // 主要用于删除,当前页只有一条数据的时候,删除需要回到上一页
      if (this.tableData.length === 0 && this.tableQuery.page > 1) {
        this.tableQuery.page--
      }
      complianceReportApi.query(this.tableQuery).then(response => {
        let rows = response.data.data.records
        rows.forEach(row => {
          if (typeof row.reportCategory === 'string') {
            try {
              row.reportCategory = JSON.parse(row.reportCategory);
            } catch (e) {
              console.error("Failed to parse reportCategory:", e);
              row.reportCategory = [];
            }
          }
        });
        this.tableData = rows
        this.ss.data = rows
        this.tableQuery.total = response.data.data.total
        this.tableLoading = false
      }).catch(error => {
        console.error("Error fetching data:", error);
        this.tableLoading = false;
      })
    },
    add_() {
      const tabId = this.utils.createUUID();
      this.layout.openNewTab(
        "法务风险合规报告上报详情单",
        "hgbg_main_detail",
        "hgbg_main_detail",
        tabId,
        {
          functionId: "hgbg_main_detail," + tabId,
          ...this.utils.routeState.NEW(tabId)
        }
      );
    },
    // 编辑
    edit_(index, row) {

      this.layout.openNewTab(
        "法务风险合规报告上报详情单",
        "hgbg_main_detail",
        "hgbg_main_detail",
        row.id,
        {
          functionId: "hgbg_main_detail," + row.id,
          ...this.utils.routeState.EDIT(row.id),
          view: 'old'
        }
      );
    },
    // 删除
    delete_(index, row) {
      this.$confirm('此操作将永久删除该数据及相关数据, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        new Promise((resolve, reject) => {
          complianceReportApi.deletebyid({
            id: row.id
          }).then((response) => {
            resolve(response)
          })
        }).then(value => {
          this.tableData.splice(index, 1)
          this.$message.success('删除成功!')
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },
    // 查看
    view_(index, row) {
      if (row.dataStateCode == this.utils.dataState_BPM.SAVE.code) {
        const tabId = this.utils.createUUID();
        this.layout.openNewTab(
          "法务风险合规报告详情",
          "hgbg_main_detail",
          "hgbg_main_detail",
          tabId,
          {
            functionId: "hgbg_main_detail," + tabId,
            ...this.utils.routeState.VIEW(row.id)
          }
        )
      }
      else {
        taskApi.selectTaskId({ businessKey: row.id, isView: 'true' }).then(res => {
          const functionId = res.data.data[0].ID
          const tabId = this.utils.createUUID()
          this.layout.openNewTab("法务风险合规报告审批信息",
            "design_page",
            "design_page",
            tabId,
            {
              processInstanceId: res.data.data[0].PID,//流程实例
              taskId: res.data.data[0].ID,//任务ID
              businessKey: row.id, //业务数据ID
              functionId: functionId, //不传也可以，只有首环节需要传，实例启动就可以不传了
              entranceType: "FLOWABLE",
              type: "haveDealt",
              channel: 'business', //查看渠道，默认就传这个值即可，表示是从业务功能传过来的，首环节提交后过滤掉撤回按钮
              view: 'new',
            }
          )
        })
      }
    },
    rowDblclick(row, column, event) {
      if (row.dataStateCode === this.utils.dataState_BPM.SAVE.code) {
        const tabId = this.utils.createUUID();
        this.layout.openNewTab(
          "法务风险合规报告审批信息",
          "hgbg_main_detail",
          "hgbg_main_detail",
          tabId,
          {
            functionId: "contract_approval_main_detail," + tabId,
            ...this.utils.routeState.VIEW(row.id)
          }
        )
      }
      else {
        taskApi.selectTaskId({ businessKey: row.id, isView: 'true' }).then(res => {
          const functionId = res.data.data[0].ID
          const tabId = this.utils.createUUID()
          this.layout.openNewTab("法务风险合规报告审批信息",
            "design_page",
            "design_page",
            tabId,
            {
              processInstanceId: res.data.data[0].PID,//流程实例
              taskId: res.data.data[0].ID,//任务ID
              businessKey: row.id, //业务数据ID
              functionId: functionId, //不传也可以，只有首环节需要传，实例启动就可以不传了
              entranceType: "FLOWABLE",
              type: "haveDealt",
              channel: 'business', //查看渠道，默认就传这个值即可，表示是从业务功能传过来的，首环节提交后过滤掉撤回按钮
              view: 'new',
            }
          )
        })
      }
    },
    tableSort(column, prop, order) {
      this.tableQuery.sortName = column.prop
      this.tableQuery.order = column.order === "ascending"
      this.refreshData()
    },
    // 点击搜索按钮事件,回到第一页,重新刷新数据
    search_: function () {
      this.tableQuery.page = 1
      this.refreshData()
    },
    // 点击清空按钮事件,清空查询条件属性,回到第一页,重新刷新数据
    empty_() {
      // 清空搜索条件
      this.tableQuery = {
        page: 1,
        limit: 10,
        total: 0,
        reportingDateMax: '',
        reportingDateMin: '',
        reportCategory: '',
        reportingUnit: '',
        reportSubject: '',
        fuzzyValue: '',
      };
      this.refreshData();
    },
    // 点击刷新按钮事件
    refresh_() {
      this.tableQuery.sortName = null
      this.tableQuery.order = null
      this.empty_()
    },
    showOrgTreeDialog() {
      this.dialogVisible = true;
    },
    cancel() {
      this.dialogVisible = false
    },
    choiceDeptSure() {
      let selectedUnits = this.zxcheckedData.map(item => item.name).join(', ');
      this.tableQuery.reportingUnit = selectedUnits;
      this.dialogVisible = false;
    },
  }
}
</script>

<style scoped>
.el-table__fixed-body-wrapper {
  top: 50px !important;
}
</style>
