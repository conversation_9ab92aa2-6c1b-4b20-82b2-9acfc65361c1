
9892d075393b546db0db42133868ccd8c25707d5	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.451.1754018536329.js\",\"contentHash\":\"eb2f0e695c0ddb6af2303a8d18ad6ed9\"}","integrity":"sha512-9Ke03JBIqYJB5FW3SZQ2+kxjfqcdWBVdWLh2v79VBT3fVDH/5/TCF4copt0wGSUqZOkPzImYHiKvoDJpYdH+kA==","time":1754018575977,"size":72574}