/** 
 * Kendo UI v2016.2.714 (http://www.telerik.com/kendo-ui)                                                                                                                                               
 * Copyright 2016 Telerik AD. All rights reserved.                                                                                                                                                      
 *                                                                                                                                                                                                      
 * Kendo UI commercial licenses may be obtained at                                                                                                                                                      
 * http://www.telerik.com/purchase/license-agreement/kendo-ui-complete                                                                                                                                  
 * If you do not own a commercial license, this file shall be governed by the trial license terms.                                                                                                      
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       

*/
/*!
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       

*/
/* Kendo skin */
/*!
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       

*/
/*!
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       

*/
.k-theme-test-class,
.ktb-theme-id-material {
  opacity: 0;
}
.ktb-var-accent {
  color: #3f51b5;
}
.ktb-var-base {
  color: #fff;
}
.ktb-var-background {
  color: #fff;
}
.ktb-var-border-radius {
  border-radius: 0px;
}
.ktb-var-normal-background {
  color: #fff;
}
.ktb-var-normal-gradient {
  background-image: none;
}
.ktb-var-normal-text-color {
  color: #444444;
}
.ktb-var-hover-background {
  color: #ebebeb;
}
.ktb-var-hover-gradient {
  background-image: none;
}
.ktb-var-hover-text-color {
  color: #444444;
}
.ktb-var-selected-background {
  color: #00b0ff;
}
.ktb-var-selected-gradient {
  background-image: none;
}
.ktb-var-selected-text-color {
  color: #3f51b5;
}
.ktb-var-error {
  color: #ffcdd2;
}
.ktb-var-warning {
  color: #fdefba;
}
.ktb-var-success {
  color: #c8e6c9;
}
.ktb-var-info {
  color: #bbdefb;
}
.ktb-var-series-a {
  color: #3f51b5;
}
.ktb-var-series-b {
  color: #03a9f4;
}
.ktb-var-series-c {
  color: #4caf50;
}
.ktb-var-series-d {
  color: #f9ce1d;
}
.ktb-var-series-e {
  color: #ff9800;
}
.ktb-var-series-f {
  color: #ff5722;
}
.k-grid-norecords-template {
  background-color: #fff;
  border: 1px solid #e6e6e6;
}
.k-in,
.k-item,
.k-window-action {
  border-color: transparent;
}
/* main colors */
.k-block,
.k-widget {
  background-color: #fff;
}
.k-block,
.k-widget,
.k-input,
.k-textbox,
.k-group,
.k-content,
.k-header,
.k-filter-row > th,
.k-editable-area,
.k-separator,
.k-colorpicker .k-i-arrow-s,
.k-textbox > input,
.k-autocomplete,
.k-dropdown-wrap,
.k-toolbar,
.k-group-footer td,
.k-grid-footer,
.k-footer-template td,
.k-state-default,
.k-state-default .k-select,
.k-state-disabled,
.k-grid-header,
.k-grid-header-wrap,
.k-grid-header-locked,
.k-grid-footer-locked,
.k-grid-content-locked,
.k-grid td,
.k-grid td.k-state-selected,
.k-grid-footer-wrap,
.k-pager-wrap,
.k-pager-wrap .k-link,
.k-pager-refresh,
.k-grouping-header,
.k-grouping-header .k-group-indicator,
.k-panelbar > .k-item > .k-link,
.k-panel > .k-item > .k-link,
.k-panelbar .k-panel,
.k-panelbar .k-content,
.k-treemap-tile,
.k-calendar th,
.k-slider-track,
.k-splitbar,
.k-dropzone-active,
.k-tiles,
.k-toolbar,
.k-tooltip,
.k-button-group .k-tool,
.k-upload-files {
  border-color: #e6e6e6;
}
.k-group,
.k-toolbar,
.k-grouping-header,
.k-pager-wrap,
.k-group-footer td,
.k-grid-footer,
.k-footer-template td,
.k-widget .k-status,
.k-calendar th,
.k-dropzone-hovered,
.k-widget.k-popup {
  background-color: #3f51b5;
}
.k-grouping-row td,
td.k-group-cell,
.k-resize-handle-inner {
  background-color: #3f51b5;
}
.k-list-container {
  border-color: rgba(0, 0, 0, 0.2);
  background-color: #ffffff;
}
.k-content,
.k-editable-area,
.k-panelbar > li.k-item,
.k-panel > li.k-item,
.k-tiles {
  background-color: #fff;
}
.k-alt,
.k-separator,
.k-resource.k-alt,
.k-pivot-layout > tbody > tr:first-child > td:first-child {
  background-color: #fafafa;
}
.k-pivot-rowheaders .k-alt .k-alt,
.k-header.k-alt {
  background-color: #e6e6e6;
}
.k-textbox,
.k-autocomplete.k-header,
.k-dropdown-wrap.k-state-active,
.k-picker-wrap.k-state-active,
.k-numeric-wrap.k-state-active {
  border-color: #e6e6e6;
  background-color: #fff;
}
.k-textbox > input,
.k-autocomplete .k-input,
.k-dropdown-wrap .k-input,
.k-autocomplete.k-state-focused .k-input,
.k-dropdown-wrap.k-state-focused .k-input,
.k-picker-wrap.k-state-focused .k-input,
.k-numeric-wrap.k-state-focused .k-input {
  border-color: #e6e6e6;
}
input.k-textbox,
textarea.k-textbox,
input.k-textbox:hover,
textarea.k-textbox:hover,
.k-textbox > input {
  background: none;
}
.k-input,
input.k-textbox,
textarea.k-textbox,
input.k-textbox:hover,
textarea.k-textbox:hover,
.k-textbox > input,
.k-multiselect-wrap {
  background-color: #fff;
  color: #444444;
}
.k-input[readonly] {
  background-color: #fff;
  color: #444444;
}
.k-block,
.k-widget,
.k-popup,
.k-content,
.k-toolbar,
.k-dropdown .k-input {
  color: #444444;
}
.k-inverse {
  color: #ffffff;
}
.k-block {
  color: #ffffff;
}
.k-link:link,
.k-link:visited,
.k-nav-current.k-state-hover .k-link {
  color: #428bca;
}
.k-tabstrip-items .k-link,
.k-panelbar > li > .k-link {
  color: #444444;
}
.k-header,
.k-treemap-title,
.k-grid-header .k-header > .k-link {
  color: #ffffff;
}
.k-header,
.k-grid-header,
.k-toolbar,
.k-dropdown-wrap,
.k-picker-wrap,
.k-numeric-wrap,
.k-grouping-header,
.k-pager-wrap,
.k-textbox,
.k-button,
.k-progressbar,
.k-draghandle,
.k-autocomplete,
.k-state-highlight,
.k-tabstrip-items .k-item,
.k-panelbar .k-tabstrip-items .k-item,
.km-pane-wrapper > .km-pane > .km-view > .km-content {
  background-image: none;
  background-position: 50% 50%;
  background-color: #3f51b5;
}
.k-widget.k-tooltip {
  background-image: none;
}
.k-block,
.k-header,
.k-grid-header,
.k-toolbar,
.k-grouping-header,
.k-pager-wrap,
.k-button,
.k-draghandle,
.k-treemap-tile,
html .km-pane-wrapper .k-header {
  background-color: #3f51b5;
}
/* icons */
.k-icon:hover,
.k-state-hover .k-icon,
.k-state-selected .k-icon,
.k-state-focused .k-icon,
.k-column-menu .k-state-hover .k-sprite,
.k-column-menu .k-state-active .k-sprite,
.k-pager-numbers .k-current-page .k-link:hover:after,
.k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view.k-state-hover > .k-link:after {
  opacity: 1;
}
.k-icon,
.k-state-disabled .k-icon,
.k-column-menu .k-sprite,
.k-pager-numbers .k-current-page .k-link:after,
.k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view > .k-link:after {
  opacity: 0.7;
}
.k-mobile-list .k-check:checked,
.k-mobile-list .k-edit-field [type=checkbox]:checked,
.k-mobile-list .k-edit-field [type=radio]:checked {
  opacity: 0.7;
}
.k-tool {
  border-color: transparent;
}
.k-icon,
.k-tool-icon,
.k-grouping-dropclue,
.k-drop-hint,
.k-column-menu .k-sprite,
.k-grid-mobile .k-resize-handle-inner:before,
.k-grid-mobile .k-resize-handle-inner:after,
.k-pager-numbers .k-current-page .k-link:after,
.k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view > .k-link:after,
.k-gantt-views > .k-current-view > .k-link:after {
  background-image: url('Material/sprite.png');
  border-color: transparent;
}
/* IE will ignore the above selectors if these are added too */
.k-mobile-list .k-check:checked,
.k-mobile-list .k-edit-field [type=checkbox]:checked,
.k-mobile-list .k-edit-field [type=radio]:checked {
  background-image: url('Material/sprite.png');
  border-color: transparent;
}
.k-loading,
.k-state-hover .k-loading {
  background-image: url('Material/loading.gif');
  background-position: 50% 50%;
}
.k-loading-image {
  background-image: url('Material/loading-image.gif');
}
.k-loading-color {
  background-color: #ffffff;
}
.k-button {
  color: #444444;
  border-color: #fafafa;
  background-color: #fafafa;
}
.k-draghandle {
  border-color: #3f51b5;
  background-color: #3f51b5;
  -webkit-box-shadow: none;
          box-shadow: none;
}
.k-draghandle:hover {
  border-color: #3f51b5;
  background-color: #3f51b5;
  -webkit-box-shadow: 0 0 0 8px rgba(63, 81, 181, 0.3);
          box-shadow: 0 0 0 8px rgba(63, 81, 181, 0.3);
}
/* Scheduler */
.k-scheduler {
  color: #ffffff;
  background-color: #fff;
}
.k-scheduler-layout {
  color: #444444;
}
.k-scheduler-datecolumn,
.k-scheduler-groupcolumn {
  background-color: #fff;
  color: #444444;
}
.k-scheduler-times tr,
.k-scheduler-times th,
.k-scheduler-table td,
.k-scheduler-header th,
.k-scheduler-header-wrap,
.k-scheduler-times {
  border-color: #e6e6e6;
}
.k-nonwork-hour {
  background-color: #fafafa;
}
.k-gantt .k-nonwork-hour {
  background-color: rgba(0, 0, 0, 0.02);
}
.k-gantt .k-header.k-nonwork-hour {
  background-color: rgba(0, 0, 0, 0.2);
}
.k-scheduler-table .k-today,
.k-today > .k-scheduler-datecolumn,
.k-today > .k-scheduler-groupcolumn {
  background-color: #e9e9e9;
}
.k-scheduler-now-arrow {
  border-left-color: #eed3d7;
}
.k-scheduler-now-line {
  background-color: #eed3d7;
}
.k-event,
.k-task-complete {
  border-color: #606fc7;
  background: #606fc7 0 -257px none repeat-x;
  color: #ffffff;
}
.k-event-inverse {
  color: #444444;
}
.k-event.k-state-selected {
  background-position: 0 0;
  -webkit-box-shadow: 0 0 0 2px #444444;
          box-shadow: 0 0 0 2px #444444;
}
.k-event .k-resize-handle:after,
.k-task-single .k-resize-handle:after {
  background-color: #ffffff;
}
.k-scheduler-marquee:before,
.k-scheduler-marquee:after {
  border-color: #fff;
}
.k-panelbar .k-content,
.k-panelbar .k-panel,
.k-panelbar .k-item {
  background-color: #fff;
  color: #444444;
  border-color: #cccccc;
}
.k-panelbar > li > .k-link {
  color: #444444;
}
.k-panelbar > .k-item > .k-link {
  border-color: #cccccc;
}
.k-panel > li.k-item {
  background-color: #fff;
}
/* states */
.k-state-active,
.k-state-active:hover,
.k-active-filter,
.k-tabstrip .k-state-active {
  background-color: #ffffff;
  border-color: #cccccc;
  color: #444444;
}
.k-fieldselector .k-list-container {
  background-color: #ffffff;
}
.k-button:focus,
.k-button.k-state-focused {
  border-color: #dbdbdb;
}
.k-button:hover,
.k-button.k-state-hover {
  color: #444444;
  border-color: #ebebeb;
  background-color: #ebebeb;
}
.k-button:active,
.k-button.k-state-active {
  color: #3f51b5;
  background-color: #dbdbdb;
  border-color: #dbdbdb;
}
.k-button:active:hover,
.k-button.k-state-active:hover {
  color: #ffffff;
  border-color: #5c6bc0;
  background-color: #5c6bc0;
}
.k-button:focus:not(.k-state-disabled):not([disabled]) {
  -webkit-box-shadow: 0 6px 17px 0 #c4c4c4;
          box-shadow: 0 6px 17px 0 #c4c4c4;
}
.k-button:focus:active:not(.k-state-disabled):not([disabled]) {
  -webkit-box-shadow: 0 6px 17px 0 rgba(235, 235, 235, 0.3);
          box-shadow: 0 6px 17px 0 rgba(235, 235, 235, 0.3);
}
.k-menu .k-state-hover > .k-state-active {
  background-color: transparent;
}
.k-state-highlight {
  background: #ffffff;
  color: #444444;
}
.k-state-focused,
.k-grouping-row .k-state-focused {
  border-color: #67afe9;
}
.k-calendar .k-link {
  color: #444444;
}
.k-calendar .k-footer {
  padding: 0;
}
.k-calendar .k-footer .k-nav-today {
  color: #444444;
  text-decoration: none;
  background-color: #fff;
}
.k-calendar .k-footer .k-nav-today:hover,
.k-calendar .k-footer .k-nav-today.k-state-hover {
  background-color: #fff;
  text-decoration: underline;
}
.k-calendar .k-footer .k-nav-today:active {
  background-color: #fff;
}
.k-calendar .k-link.k-nav-fast {
  color: #444444;
}
.k-calendar .k-nav-fast.k-state-hover {
  text-decoration: none;
  background-color: #ebebeb;
  color: #444444;
}
.k-calendar .k-link.k-state-hover {
  border-radius: 50%;
}
.k-calendar .k-footer .k-link {
  border-radius: 0;
}
.k-calendar th {
  background-color: #3f51b5;
}
.k-window-titlebar .k-link {
  border-radius: 50%;
}
.k-calendar-container.k-group {
  border-color: rgba(0, 0, 0, 0.2);
}
.k-state-selected,
.k-state-selected:link,
.k-state-selected:visited,
.k-list > .k-state-selected,
.k-list > .k-state-highlight,
.k-panel > .k-state-selected,
.k-ghost-splitbar-vertical,
.k-ghost-splitbar-horizontal,
.k-draghandle.k-state-selected:hover,
.k-scheduler .k-scheduler-toolbar .k-state-selected,
.k-scheduler .k-today.k-state-selected,
.k-marquee-color {
  color: #3f51b5;
  background-color: #fff;
  border-color: #ffffff;
}
.k-virtual-item.k-first,
.k-group-header + .k-list > .k-item.k-first,
.k-static-header + .k-list > .k-item.k-first {
  border-top-color: #ebebeb;
}
.k-group-header + div > .k-list > .k-item.k-first:before {
  border-top-color: #ebebeb;
}
.k-popup > .k-group-header,
.k-popup > .k-virtual-wrap > .k-group-header {
  background: #ebebeb;
  color: #3f51b5;
}
.k-popup .k-list .k-item > .k-group {
  background: #ebebeb;
  color: #3f51b5;
  border-bottom-left-radius: -1px;
}
.k-marquee-text {
  color: #3f51b5;
}
.k-state-focused,
.k-list > .k-state-focused,
.k-listview > .k-state-focused,
.k-listview > .k-state-focused.k-state-selected,
td.k-state-focused,
.k-button.k-state-focused {
  -webkit-box-shadow: inset 0 0 0 1px #808080;
          box-shadow: inset 0 0 0 1px #808080;
}
.k-state-focused.k-state-selected,
.k-list > .k-state-focused.k-state-selected,
td.k-state-focused.k-state-selected {
  -webkit-box-shadow: none;
          box-shadow: none;
}
.k-ie8 .k-panelbar span.k-state-focused,
.k-ie8 .k-menu li.k-state-focused,
.k-ie8 .k-listview > .k-state-focused,
.k-ie8 .k-grid-header th.k-state-focused,
.k-ie8 td.k-state-focused,
.k-ie8 .k-tool.k-state-hover,
.k-ie8 .k-button:focus,
.k-ie8 .k-button.k-state-focused,
.k-list > .k-state-selected.k-state-focused,
.k-list-optionlabel.k-state-selected.k-state-focused {
  -webkit-box-shadow: none;
          box-shadow: none;
}
.k-state-selected > .k-link,
.k-panelbar > li > .k-state-selected,
.k-panelbar > li.k-state-default > .k-link.k-state-selected {
  color: #3f51b5;
}
.k-state-hover,
.k-state-hover:hover,
.k-splitbar-horizontal-hover:hover,
.k-splitbar-vertical-hover:hover,
.k-list > .k-state-hover,
.k-scheduler .k-scheduler-toolbar ul li.k-state-hover,
.k-pager-wrap .k-link:hover,
.k-dropdown .k-state-focused,
.k-filebrowser-dropzone,
.k-mobile-list .k-item > .k-link:active,
.k-mobile-list .k-item > .k-label:active,
.k-mobile-list .k-edit-label.k-check:active,
.k-mobile-list .k-recur-view .k-check:active {
  color: #444444;
  background-color: #ebebeb;
  border-color: #ebebeb;
}
/* this selector should be used separately, otherwise old IEs ignore the whole rule */
.k-mobile-list .k-scheduler-timezones .k-edit-field:nth-child(2):active {
  color: #444444;
  background-color: #ebebeb;
  border-color: #ebebeb;
}
.k-ie8 .k-window-titlebar .k-state-hover {
  border-color: #ebebeb;
}
.k-state-hover > .k-select,
.k-state-focused > .k-select {
  border-color: #ebebeb;
}
.k-button:hover,
.k-button.k-state-hover,
.k-button:focus,
.k-button.k-state-focused,
.k-textbox:hover,
.k-state-hover,
.k-state-hover:hover,
.k-pager-wrap .k-link:hover,
.k-other-month.k-state-hover .k-link,
div.k-filebrowser-dropzone em,
.k-draghandle:hover {
  background-image: none;
}
.k-pager-wrap {
  background-color: #3f51b5;
  color: #ffffff;
}
.k-autocomplete.k-state-active,
.k-picker-wrap.k-state-active,
.k-numeric-wrap.k-state-active,
.k-dropdown-wrap.k-state-active,
.k-state-active,
.k-state-active:hover,
.k-state-active > .k-link,
.k-button:active,
.k-panelbar > .k-item > .k-state-focused {
  background-image: none;
}
.k-state-selected,
.k-button:active,
.k-button.k-state-active,
.k-draghandle.k-state-selected:hover {
  background-image: none;
}
.k-button:active,
.k-button.k-state-active,
.k-draghandle.k-state-selected:hover {
  background-position: 50% 50%;
}
.k-tool-icon {
  background-image: url('Material/sprite.png');
}
.k-state-hover > .k-link,
.k-other-month.k-state-hover .k-link,
div.k-filebrowser-dropzone em {
  color: #444444;
}
.k-autocomplete.k-state-hover,
.k-autocomplete.k-state-focused,
.k-picker-wrap.k-state-hover,
.k-picker-wrap.k-state-focused,
.k-numeric-wrap.k-state-hover,
.k-numeric-wrap.k-state-focused,
.k-dropdown-wrap.k-state-hover,
.k-dropdown-wrap.k-state-focused {
  background-color: #ffffff;
  background-image: none;
  background-position: 50% 50%;
  border-color: #ebebeb;
}
.km-pane-wrapper .k-mobile-list input:not([type="checkbox"]):not([type="radio"]),
.km-pane-wrapper .km-pane .k-mobile-list select:not([multiple]),
.km-pane-wrapper .k-mobile-list textarea,
.k-dropdown .k-state-focused .k-input {
  color: #444444;
}
.km-pane-wrapper .km-pane .k-mobile-list.k-filter-menu .k-space-right {
  background: #fff;
  border-color: #e6e6e6;
}
.km-pane-wrapper .km-pane .k-mobile-list.k-filter-menu .k-space-right > input {
  background-color: #fff;
  border-color: #f0f0f0;
}
.km-pane-wrapper .km-pane .k-mobile-list.k-filter-menu .k-space-right > input + .k-i-search:before {
  color: #444444;
}
.km-pane-wrapper .km-pane .k-mobile-list.k-filter-menu .k-space-right > input:focus {
  -webkit-box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2), 0 2px 3px rgba(0, 0, 0, 0.05);
          box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2), 0 2px 3px rgba(0, 0, 0, 0.05);
}
.k-dropdown .k-state-hover .k-input {
  color: #444444;
}
.k-state-error {
  border-color: #eed3d7;
  background-color: #f2dede;
  color: #b94a48;
}
.k-state-disabled {
  opacity: .7;
}
.k-ie8 .k-state-disabled {
  filter: alpha(opacity=70);
}
.k-tile-empty.k-state-selected,
.k-loading-mask.k-state-selected {
  border-width: 0;
  background-image: none;
  background-color: transparent;
}
.k-state-disabled,
.k-state-disabled .k-link,
.k-state-disabled .k-button,
.k-other-month,
.k-other-month .k-link,
.k-dropzone em,
.k-dropzone .k-upload-status,
.k-tile-empty strong,
.k-slider .k-draghandle {
  color: #999999;
}
/* Progressbar */
.k-progressbar-indeterminate {
  background: url('Material/indeterminate.gif');
}
.k-progressbar-indeterminate .k-progress-status-wrap,
.k-progressbar-indeterminate .k-state-selected {
  display: none;
}
/* Slider */
.k-slider-track {
  background-color: #e6e6e6;
}
.k-slider-selection {
  background-color: #fff;
}
.k-slider-horizontal .k-tick {
  background-image: url('Material/slider-h.gif');
}
.k-slider-vertical .k-tick {
  background-image: url('Material/slider-v.gif');
}
/* Tooltip */
.k-widget.k-tooltip {
  border-color: rgba(100, 100, 100, 0.9);
  background-color: rgba(100, 100, 100, 0.9);
  color: #ffffff;
}
.k-widget.k-tooltip-validation {
  border-color: #fdefba;
  background-color: #fdefba;
  color: #816704;
}
/* Bootstrap theme fix */
.input-prepend .k-tooltip-validation,
.input-append .k-tooltip-validation {
  font-size: 12px;
  position: relative;
  top: 3px;
}
.k-callout-n {
  border-bottom-color: rgba(100, 100, 100, 0.9);
}
.k-callout-w {
  border-right-color: rgba(100, 100, 100, 0.9);
}
.k-callout-s {
  border-top-color: rgba(100, 100, 100, 0.9);
}
.k-callout-e {
  border-left-color: rgba(100, 100, 100, 0.9);
}
.k-tooltip-validation .k-callout-n {
  border-bottom-color: #fdefba;
}
.k-tooltip-validation .k-callout-w {
  border-right-color: #fdefba;
}
.k-tooltip-validation .k-callout-s {
  border-top-color: #fdefba;
}
.k-tooltip-validation .k-callout-e {
  border-left-color: #fdefba;
}
/* Splitter */
.k-splitbar {
  background-color: #fafafa;
}
.k-restricted-size-vertical,
.k-restricted-size-horizontal {
  background-color: #b94a48;
}
/* Upload */
.k-file {
  background-color: #fff;
  border-color: #e6e6e6;
}
.k-file-progress {
  color: #0d7fdd;
}
.k-file-progress .k-progress {
  background-color: #bbdefb;
}
.k-file-success {
  color: #479b49;
}
.k-file-success .k-progress {
  background-color: #c8e6c9;
}
.k-file-error {
  color: #ff011a;
}
.k-file-error .k-progress {
  background-color: #ffcdd2;
}
/* ImageBrowser */
.k-tile {
  border-color: #fff;
}
.k-textbox:hover,
.k-tiles li.k-state-hover {
  border-color: #ebebeb;
}
.k-tiles li.k-state-selected {
  border-color: #ffffff;
}
.k-filebrowser .k-tile .k-folder,
.k-filebrowser .k-tile .k-file {
  background-image: url('Material/imagebrowser.png');
  -webkit-background-size: auto auto;
          background-size: auto auto;
}
/* TreeMap */
.k-leaf,
.k-leaf.k-state-hover:hover {
  color: #fff;
}
.k-leaf.k-inverse,
.k-leaf.k-inverse.k-state-hover:hover {
  color: #000;
}
/* Shadows */
.k-widget,
.k-button {
  -webkit-box-shadow: none;
          box-shadow: none;
}
.k-slider,
.k-treeview,
.k-upload {
  -webkit-box-shadow: none;
          box-shadow: none;
}
.k-state-hover {
  -webkit-box-shadow: 0 1px 0 rgba(255, 255, 255, 0.2);
          box-shadow: 0 1px 0 rgba(255, 255, 255, 0.2);
}
.k-textbox:focus,
.k-autocomplete.k-state-focused,
.k-dropdown-wrap.k-state-focused,
.k-picker-wrap.k-state-focused,
.k-numeric-wrap.k-state-focused {
  -webkit-box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2), 0 2px 3px rgba(0, 0, 0, 0.05);
          box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2), 0 2px 3px rgba(0, 0, 0, 0.05);
}
.k-state-selected {
  -webkit-box-shadow: 0 1px 1px rgba(0, 0, 0, 0.075) inset;
          box-shadow: 0 1px 1px rgba(0, 0, 0, 0.075) inset;
}
.k-state-active {
  -webkit-box-shadow: none;
          box-shadow: none;
}
.k-grid td.k-state-selected.k-state-focused {
  background-color: #ffffff;
}
.k-popup,
.k-menu .k-menu-group,
.k-grid .k-filter-options,
.k-time-popup,
.k-datepicker-calendar,
.k-autocomplete.k-state-border-down,
.k-autocomplete.k-state-border-up,
.k-dropdown-wrap.k-state-active,
.k-picker-wrap.k-state-active,
.k-multiselect.k-state-focused,
.k-filebrowser .k-image,
.k-tooltip {
  -webkit-box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.2);
          box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.2);
}
.k-calendar-container.k-popup {
  -webkit-box-shadow: 0 0px 6px 1px rgba(0, 0, 0, 0.2);
          box-shadow: 0 0px 6px 1px rgba(0, 0, 0, 0.2);
}
.k-treemap-tile.k-state-hover {
  -webkit-box-shadow: inset 0 0 0 3px #e6e6e6;
          box-shadow: inset 0 0 0 3px #e6e6e6;
}
/* Window */
.k-window {
  border-color: rgba(0, 0, 0, 0.2);
  -webkit-box-shadow: 1px 1px 7px 1px rgba(128, 128, 128, 0.2);
          box-shadow: 1px 1px 7px 1px rgba(128, 128, 128, 0.2);
  background-color: #fff;
}
.k-window.k-state-focused {
  border-color: rgba(0, 0, 0, 0.2);
  -webkit-box-shadow: 1px 1px 7px 1px rgba(0, 0, 0, 0.2);
          box-shadow: 1px 1px 7px 1px rgba(0, 0, 0, 0.2);
}
.k-window.k-window-maximized,
.k-window-maximized .k-window-titlebar,
.k-window-maximized .k-window-content {
  border-radius: 0;
}
.k-shadow {
  -webkit-box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.2);
          box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.2);
}
.k-inset {
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.2);
          box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.2);
}
/* Selection */
.k-editor-inline ::-moz-selection {
  background-color: #3f51b5;
  text-shadow: none;
  color: #fff;
}
.k-editor-inline ::selection {
  background-color: #3f51b5;
  text-shadow: none;
  color: #fff;
}
.k-editor-inline ::-moz-selection {
  background-color: #3f51b5;
  text-shadow: none;
  color: #fff;
}
/* Notification */
.k-widget.k-notification.k-notification-info {
  background-color: #bbdefb;
  color: #2b98f3;
  border-color: #bbdefb;
}
.k-widget.k-notification.k-notification-success {
  background-color: #c8e6c9;
  color: #5fb662;
  border-color: #c8e6c9;
}
.k-widget.k-notification.k-notification-warning {
  background-color: #fdefba;
  color: #f9cd25;
  border-color: #fdefba;
}
.k-widget.k-notification.k-notification-error {
  background-color: #ffcdd2;
  color: #ff3448;
  border-color: #ffcdd2;
}
/* Gantt */
.k-gantt .k-treelist {
  background: #fafafa;
}
.k-gantt .k-treelist .k-alt {
  background-color: #e0e0e0;
}
.k-gantt .k-treelist tr:hover {
  background-color: #ebebeb;
}
.k-gantt .k-treelist .k-state-selected,
.k-gantt .k-treelist .k-state-selected td,
.k-gantt .k-treelist .k-alt.k-state-selected,
.k-gantt .k-treelist .k-alt.k-state-selected > td {
  background-color: #fff;
}
.k-gantt .k-treelist .k-alt.k-state-selected:hover,
.k-gantt .k-treelist .k-alt.k-state-selected:hover td {
  background-color: #00a2eb;
}
.k-task-dot:after {
  background-color: #444444;
  border-color: #444444;
}
.k-task-dot:hover:after {
  background-color: #ffffff;
}
.k-task-summary {
  border-color: #98a2db;
  background: #98a2db;
}
.k-task-milestone,
.k-task-summary-complete {
  border-color: #444444;
  background: #444444;
}
.k-state-selected.k-task-summary {
  border-color: #98a2db;
  background: #98a2db;
}
.k-state-selected.k-task-milestone,
.k-state-selected .k-task-summary-complete {
  border-color: #fff;
  background: #fff;
}
.k-task-single {
  background-color: #7a87d1;
  border-color: #606fc7;
  color: #ffffff;
}
.k-state-selected.k-task-single {
  border-color: #ffffff;
}
.k-line {
  background-color: #444444;
  color: #444444;
}
.k-state-selected.k-line {
  background-color: #fff;
  color: #fff;
}
.k-resource {
  background-color: #fff;
}
/* PivotGrid */
.k-i-kpi-decrease,
.k-i-kpi-denied,
.k-i-kpi-equal,
.k-i-kpi-hold,
.k-i-kpi-increase,
.k-i-kpi-open {
  background-image: url('Material/sprite_kpi.png');
}
/* Border radius */
.k-block,
.k-button,
.k-textbox,
.k-drag-clue,
.k-touch-scrollbar,
.k-window,
.k-window-titleless .k-window-content,
.k-window-action,
.k-inline-block,
.k-grid .k-filter-options,
.k-grouping-header .k-group-indicator,
.k-autocomplete,
.k-multiselect,
.k-combobox,
.k-dropdown,
.k-dropdown-wrap,
.k-datepicker,
.k-timepicker,
.k-colorpicker,
.k-datetimepicker,
.k-notification,
.k-numerictextbox,
.k-picker-wrap,
.k-numeric-wrap,
.k-colorpicker,
.k-list-container,
.k-calendar-container,
.k-calendar td,
.k-calendar .k-link,
.k-treeview .k-in,
.k-editor-inline,
.k-tooltip,
.k-tile,
.k-slider-track,
.k-slider-selection,
.k-upload {
  border-radius: 0px;
}
.k-tool {
  text-align: center;
  vertical-align: middle;
}
.k-toolbar .k-split-button .k-button {
  border-radius: 0px 0 0 0px;
}
.k-rtl .k-tool.k-group-start,
.k-rtl .k-toolbar .k-split-button .k-button,
.k-rtl .k-toolbar .k-button-group .k-group-start {
  border-radius: 0 0px 0px 0;
}
.k-toolbar .k-split-button .k-split-button-arrow {
  border-radius: 0 0px 0px 0;
}
.k-rtl .k-tool.k-group-end,
.k-rtl .k-toolbar .k-button-group .k-group-end,
.k-rtl .k-toolbar .k-split-button .k-split-button-arrow {
  border-radius: 0px 0 0 0px;
}
.k-calendar-container.k-state-border-up,
.k-list-container.k-state-border-up,
.k-autocomplete.k-state-border-up,
.k-multiselect.k-state-border-up,
.k-dropdown-wrap.k-state-border-up,
.k-picker-wrap.k-state-border-up,
.k-numeric-wrap.k-state-border-up,
.k-window-content,
.k-filter-menu {
  border-radius: 0 0 0px 0px;
}
.k-autocomplete.k-state-border-up .k-input,
.k-dropdown-wrap.k-state-border-up .k-input,
.k-picker-wrap.k-state-border-up .k-input,
.k-picker-wrap.k-state-border-up .k-selected-color,
.k-numeric-wrap.k-state-border-up .k-input {
  border-radius: 0 0 0 0px;
}
.k-multiselect.k-state-border-up .k-multiselect-wrap {
  border-radius: 0 0 0px 0px;
}
.k-window-titlebar,
.k-block > .k-header,
.k-tabstrip-items .k-item,
.k-panelbar .k-tabstrip-items .k-item,
.k-tabstrip-items .k-link,
.k-calendar-container.k-state-border-down,
.k-list-container.k-state-border-down,
.k-autocomplete.k-state-border-down,
.k-multiselect.k-state-border-down,
.k-dropdown-wrap.k-state-border-down,
.k-picker-wrap.k-state-border-down,
.k-numeric-wrap.k-state-border-down {
  border-radius: 0px 0px 0 0;
}
.k-split-button.k-state-border-down > .k-button {
  border-radius: 0px 0 0 0;
}
.k-split-button.k-state-border-up > .k-button {
  border-radius: 0 0 0 0px;
}
.k-split-button.k-state-border-down > .k-split-button-arrow {
  border-radius: 0 0px 0 0;
}
.k-split-button.k-state-border-up > .k-split-button-arrow {
  border-radius: 0 0 0px 0;
}
.k-dropdown-wrap .k-input,
.k-picker-wrap .k-input,
.k-numeric-wrap .k-input {
  border-radius: -1px 0 0 -1px;
}
.k-rtl .k-dropdown-wrap .k-input,
.k-rtl .k-picker-wrap .k-input,
.k-rtl .k-numeric-wrap .k-input {
  border-radius: 0 -1px -1px 0;
}
.k-numeric-wrap .k-link {
  border-radius: 0 -1px 0 0;
}
.k-numeric-wrap .k-link + .k-link {
  border-radius: 0 0 -1px 0;
}
.k-colorpicker .k-selected-color {
  border-radius: -1px 0 0 -1px;
}
.k-rtl .k-colorpicker .k-selected-color {
  border-radius: 0 -1px -1px 0;
}
.k-autocomplete.k-state-border-down .k-input {
  border-radius: 0px 0px 0 0;
}
.k-dropdown-wrap.k-state-border-down .k-input,
.k-picker-wrap.k-state-border-down .k-input,
.k-picker-wrap.k-state-border-down .k-selected-color,
.k-numeric-wrap.k-state-border-down .k-input {
  border-radius: 0px 0 0 0;
}
.k-numeric-wrap .k-link.k-state-selected {
  background-color: #ebebeb;
}
.k-multiselect.k-state-border-down .k-multiselect-wrap {
  border-radius: -1px -1px 0 0;
}
.k-dropdown-wrap .k-select,
.k-picker-wrap .k-select,
.k-numeric-wrap .k-select,
.k-datetimepicker .k-select + .k-select,
.k-list-container.k-state-border-right {
  border-radius: 0 0px 0px 0;
}
.k-rtl .k-dropdown-wrap .k-select,
.k-rtl .k-picker-wrap .k-select,
.k-rtl .k-numeric-wrap .k-select,
.k-rtl .k-datetimepicker .k-select + .k-select,
.k-rtl .k-list-container.k-state-border-right {
  border-radius: 0px 0 0 0px;
}
.k-numeric-wrap.k-expand-padding .k-input {
  border-radius: 0px;
}
.k-textbox > input,
.k-autocomplete .k-input,
.k-multiselect-wrap {
  border-radius: 0;
}
.k-list .k-state-hover,
.k-list .k-state-focused,
.k-list .k-state-highlight,
.k-list .k-state-selected,
.k-fieldselector .k-list .k-item,
.k-list-optionlabel,
.k-dropzone {
  border-radius: 0;
}
.k-slider .k-button,
.k-grid .k-slider .k-button {
  border-radius: 13px;
}
.k-draghandle {
  border-radius: 13px;
}
.k-scheduler-toolbar > ul li:first-child,
.k-scheduler-toolbar > ul li:first-child .k-link,
.k-scheduler-toolbar > ul.k-scheduler-views li:first-child + li,
.k-scheduler-toolbar > ul.k-scheduler-views li:first-child + li .k-link {
  border-radius: 0px 0 0 0px;
}
.k-rtl .k-scheduler-toolbar > ul li:first-child,
.k-rtl .k-scheduler-toolbar > ul li:first-child .k-link,
.k-rtl .k-scheduler-toolbar > ul.k-scheduler-views li:first-child + li,
.k-rtl .k-scheduler-toolbar > ul.k-scheduler-views li:first-child + li .k-link,
.km-view.k-popup-edit-form .k-scheduler-toolbar > ul li:last-child,
.km-view.k-popup-edit-form .k-scheduler-toolbar > ul li:last-child .k-link {
  border-radius: 0 0px 0px 0;
}
.k-scheduler-phone .k-scheduler-toolbar > ul li.k-nav-today,
.k-scheduler-phone .k-scheduler-toolbar > ul li.k-nav-today .k-link,
.k-edit-field > .k-scheduler-navigation {
  border-radius: 0px;
}
.k-scheduler-toolbar .k-nav-next,
.k-scheduler-toolbar ul + ul li:last-child,
.k-scheduler-toolbar .k-nav-next .k-link,
.k-scheduler-toolbar ul + ul li:last-child .k-link {
  border-top-right-radius: 0px;
  border-bottom-right-radius: 0px;
}
.k-rtl .k-scheduler-toolbar .k-nav-next,
.k-rtl .k-scheduler-toolbar ul + ul li:last-child,
.k-rtl .k-scheduler-toolbar .k-nav-next .k-link,
.k-rtl .k-scheduler-toolbar ul + ul li:last-child .k-link {
  border-radius: 0px 0 0 0px;
}
.k-scheduler div.k-scheduler-footer ul li,
.k-scheduler div.k-scheduler-footer .k-link {
  border-radius: 0px;
}
.k-more-events,
.k-event,
.k-task-single,
.k-task-complete,
.k-event .k-link {
  border-radius: -1px;
}
.k-scheduler-mobile .k-event {
  border-radius: -2px;
}
/* Adaptive Grid */
.k-grid-mobile .k-column-active + th.k-header {
  border-left-color: #444444;
}
html .km-pane-wrapper .km-widget,
.k-ie .km-pane-wrapper .k-widget,
.k-ie .km-pane-wrapper .k-group,
.k-ie .km-pane-wrapper .k-content,
.k-ie .km-pane-wrapper .k-header,
.k-ie .km-pane-wrapper .k-popup-edit-form .k-edit-field .k-button,
.km-pane-wrapper .k-mobile-list .k-item,
.km-pane-wrapper .k-mobile-list .k-edit-label,
.km-pane-wrapper .k-mobile-list .k-edit-field {
  color: #444444;
}
@media screen and (-ms-high-contrast: active) and (-ms-high-contrast: none) {
  div.km-pane-wrapper a {
    color: #444444;
  }
}
.km-pane-wrapper .k-mobile-list .k-item,
.km-pane-wrapper .k-mobile-list .k-edit-field,
.km-pane-wrapper .k-mobile-list .k-recur-view > .k-edit-field .k-check {
  background-color: #fff;
  border-top: 1px solid #e7e7e7;
}
.km-pane-wrapper .k-mobile-list .k-edit-field textarea {
  outline-width: 0;
}
.km-pane-wrapper .k-mobile-list .k-item.k-state-selected {
  background-color: #fff;
  border-top-color: #ffffff;
}
.km-pane-wrapper .k-mobile-list .k-recur-view > .k-edit-field .k-check:first-child {
  border-top-color: transparent;
}
.km-pane-wrapper .k-mobile-list .k-item:last-child {
  -webkit-box-shadow: inset 0 -1px 0 #e7e7e7;
          box-shadow: inset 0 -1px 0 #e7e7e7;
}
.km-pane-wrapper .k-mobile-list > ul > li > .k-link,
.km-pane-wrapper .k-mobile-list .k-recur-view > .k-edit-label:nth-child(3),
.km-pane-wrapper #recurrence .km-scroll-container > .k-edit-label:first-child {
  color: #9b9b9b;
}
.km-pane-wrapper .k-mobile-list > ul > li > .k-link {
  border-bottom: 1px solid #e7e7e7;
}
.km-pane-wrapper .k-mobile-list .k-edit-field {
  -webkit-box-shadow: 0 1px 1px #e7e7e7;
          box-shadow: 0 1px 1px #e7e7e7;
}
.km-actionsheet .k-grid-delete,
.km-actionsheet .k-scheduler-delete,
.km-pane-wrapper .k-scheduler-delete,
.km-pane-wrapper .k-filter-menu .k-button[type=reset] {
  color: #fff;
  border-color: #eed3d7;
  background-color: red;
  background-image: -webkit-gradient(linear, left top, left bottom, from(rgba(255, 255, 255, 0.3)), to(rgba(255, 255, 255, 0.15)));
  background-image: -webkit-linear-gradient(top, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.15));
  background-image: linear-gradient(to bottom, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.15));
}
.km-actionsheet .k-grid-delete:active,
.km-actionsheet .k-scheduler-delete:active,
.km-pane-wrapper .k-scheduler-delete:active,
.km-pane-wrapper .k-filter-menu .k-button[type=reset]:active {
  background-color: #990000;
}
/* /Column Menu */
.k-autocomplete.k-state-default,
.k-picker-wrap.k-state-default,
.k-numeric-wrap.k-state-default,
.k-dropdown-wrap.k-state-default {
  background-image: none;
  background-position: 50% 50%;
  background-color: #fafafa;
  border-color: #f0f0f0;
}
.k-autocomplete.k-state-hover,
.k-picker-wrap.k-state-hover,
.k-numeric-wrap.k-state-hover,
.k-dropdown-wrap.k-state-hover {
  background-color: #ffffff;
  background-image: none;
  background-position: 50% 50%;
  border-color: #f5f5f5;
}
.k-multiselect.k-header {
  border-color: #f0f0f0;
}
.k-multiselect.k-header.k-state-hover {
  border-color: #f5f5f5;
}
.k-autocomplete.k-state-focused,
.k-picker-wrap.k-state-focused,
.k-numeric-wrap.k-state-focused,
.k-dropdown-wrap.k-state-focused,
.k-multiselect.k-header.k-state-focused {
  background-color: #ffffff;
  background-image: none;
  background-position: 50% 50%;
  border-color: #f5f5f5;
  -webkit-box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2), 0 2px 3px rgba(0, 0, 0, 0.05);
          box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2), 0 2px 3px rgba(0, 0, 0, 0.05);
}
.k-list-container {
  color: #444444;
}
.k-dropdown .k-input,
.k-dropdown .k-state-focused .k-input,
.k-menu .k-popup {
  color: #444444;
}
.k-state-default > .k-select {
  border-color: #f0f0f0;
}
.k-state-focused > .k-select {
  border-color: #f5f5f5;
}
.k-state-hover > .k-select {
  border-color: #f5f5f5;
}
.k-tabstrip:focus {
  -webkit-box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2), 0 2px 3px rgba(0, 0, 0, 0.05);
          box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2), 0 2px 3px rgba(0, 0, 0, 0.05);
}
.k-tabstrip-items .k-state-default .k-link,
.k-panelbar > li.k-state-default > .k-link {
  color: #ffffff;
}
.k-tabstrip-items .k-state-hover .k-link,
.k-panelbar > li.k-state-hover > .k-link,
.k-panelbar > li.k-state-default > .k-link.k-state-hover {
  color: #444444;
}
.k-panelbar > li > .k-state-focused.k-state-hover {
  background: #ebebeb;
  -webkit-box-shadow: none;
          box-shadow: none;
}
.k-tabstrip-items .k-state-default,
.k-panelbar .k-tabstrip-items .k-state-default {
  border-color: transparent;
}
.k-tabstrip-items .k-state-hover {
  border-color: #ebebeb;
}
.k-tabstrip .k-content.k-state-active {
  background-color: #fff;
  color: #444444;
}
.k-menu.k-header,
.k-menu .k-item {
  border-color: #e6e6e6;
}
.k-column-menu,
.k-column-menu .k-item,
.k-overflow-container .k-overflow-group {
  border-color: #cccccc;
}
.k-overflow-container .k-overflow-group {
  -webkit-box-shadow: inset 0 1px 0 #ffffff, 0 1px 0 #ffffff;
          box-shadow: inset 0 1px 0 #ffffff, 0 1px 0 #ffffff;
}
.k-toolbar-first-visible.k-overflow-group,
.k-overflow-container .k-overflow-group + .k-overflow-group {
  -webkit-box-shadow: 0 1px 0 #ffffff;
          box-shadow: 0 1px 0 #ffffff;
}
.k-toolbar-last-visible.k-overflow-group {
  -webkit-box-shadow: inset 0 1px 0 #ffffff;
          box-shadow: inset 0 1px 0 #ffffff;
}
.k-column-menu .k-separator {
  border-color: #cccccc;
  background-color: transparent;
}
.k-menu .k-group {
  border-color: rgba(0, 0, 0, 0.2);
}
.k-grid-filter.k-state-active {
  background-color: #ffffff;
}
.k-grouping-row td,
.k-group-footer td,
.k-grid-footer td {
  color: #ffffff;
  border-color: #cccccc;
  font-weight: bold;
}
.k-grouping-header {
  color: #ffffff;
}
.k-header,
.k-grid-header-wrap,
.k-grid .k-grouping-header,
.k-grid-header,
.k-pager-wrap,
.k-pager-wrap .k-textbox,
.k-pager-wrap .k-link,
.k-grouping-header .k-group-indicator,
.k-gantt-toolbar .k-state-default {
  border-color: #cccccc;
}
.k-primary,
.k-overflow-container .k-primary {
  color: #ffffff;
  border-color: #3f51b5;
  background-image: none;
  background-position: 50% 50%;
  background-color: #3f51b5;
  -webkit-box-shadow: none;
          box-shadow: none;
}
.k-primary:focus,
.k-primary.k-state-focused {
  color: #ffffff;
  border-color: #eff8ff;
  background-image: none;
  -webkit-box-shadow: 0 0 8px 0 #cfe6f8;
          box-shadow: 0 0 8px 0 #cfe6f8;
}
.k-primary:hover {
  color: #ffffff;
  border-color: #5c6bc0;
  background-image: none;
  background-color: #5c6bc0;
  -webkit-box-shadow: none;
          box-shadow: none;
}
.k-primary:focus:active:not(.k-state-disabled):not([disabled]),
.k-primary:focus:not(.k-state-disabled):not([disabled]) {
  -webkit-box-shadow: 0 0 8px 0 #cfe6f8;
          box-shadow: 0 0 8px 0 #cfe6f8;
}
.k-primary:active {
  color: #ffffff;
  border-color: #283593;
  background-image: none;
  background-color: #283593;
  -webkit-box-shadow: 0 6px 17px 0 rgba(0, 0, 0, 0.3);
          box-shadow: 0 6px 17px 0 rgba(0, 0, 0, 0.3);
}
.k-primary.k-state-disabled,
.k-state-disabled .k-primary,
.k-primary.k-state-disabled:hover,
.k-state-disabled .k-primary:hover,
.k-primary.k-state-disabled:hover,
.k-state-disabled .k-primary:active,
.k-primary.k-state-disabled:active {
  color: #a8a8a8;
  border-color: #eaeaea;
  background-color: #eaeaea;
  background-image: none;
  -webkit-box-shadow: none;
          box-shadow: none;
}
.k-pager-numbers .k-link,
.k-treeview .k-in {
  border-color: transparent;
}
.k-treeview .k-icon,
.k-scheduler-table .k-icon,
.k-grid .k-hierarchy-cell .k-icon {
  background-color: transparent;
  border-radius: 50%;
}
.k-scheduler-table .k-state-hover .k-icon {
  background-color: transparent;
}
.k-button:focus,
.k-split-button:focus {
  outline: none;
}
.k-split-button:focus {
  background-color: #dbdbdb;
}
.k-split-button:focus > .k-button {
  background: transparent;
  border-color: #dbdbdb;
}
.k-split-button:focus > .k-button.k-split-button-arrow {
  border-left-color: #fafafa;
}
.k-editor .k-tool:focus {
  outline: 0;
  border-color: #dbdbdb;
  -webkit-box-shadow: 0 6px 17px 0 #c4c4c4;
          box-shadow: 0 6px 17px 0 #c4c4c4;
}
/*!
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       

*/
.k-checkbox-label:before {
  border-color: #7f7f7f;
  background: #fff;
  border-radius: 1px;
}
.k-checkbox-label:hover:before,
.k-checkbox:checked + .k-checkbox-label:hover:before {
  border-color: #7f7f7f;
  -webkit-box-shadow: none;
          box-shadow: none;
}
.k-checkbox:checked + .k-checkbox-label:before {
  background-color: #3f51b5;
  border-color: #3f51b5;
  color: #ffffff;
}
.k-checkbox-label:active:before {
  -webkit-box-shadow: none;
          box-shadow: none;
  border-color: #7f7f7f;
}
.k-checkbox:checked + .k-checkbox-label:active:before {
  -webkit-box-shadow: none;
          box-shadow: none;
  border-color: #7f7f7f;
}
.k-checkbox:disabled + .k-checkbox-label {
  color: #999999;
}
.k-checkbox:disabled + .k-checkbox-label:hover:before {
  -webkit-box-shadow: none;
          box-shadow: none;
}
.k-checkbox:disabled + .k-checkbox-label:before,
.k-checkbox:checked:disabled + .k-checkbox-label:before,
.k-checkbox:checked:disabled + .k-checkbox-label:active:before,
.k-checkbox:checked:disabled + .k-checkbox-label:hover:before {
  color: #999999;
  background: #f5f5f5;
  border-color: #bfbfbf;
  border-radius: 1px;
}
.k-checkbox:focus + .k-checkbox-label:before {
  border-color: #7f7f7f;
  -webkit-box-shadow: none;
          box-shadow: none;
}
.k-checkbox:indeterminate + .k-checkbox-label:after {
  background-color: #3f51b5;
  background-image: none;
  border-color: #3f51b5;
  border-radius: 0px;
}
.k-checkbox:indeterminate:hover + .k-checkbox-label:after {
  border-color: #3f51b5;
  background-color: #3f51b5;
}
.k-checkbox + .k-checkbox-label:after {
  content: "";
  position: absolute;
  top: 1px;
  left: 1px;
  border-radius: 50%;
  width: 1em;
  height: 1em;
}
.k-checkbox:focus + .k-checkbox-label:after {
  -webkit-box-shadow: 0 0 0 12px rgba(235, 235, 235, 0.3);
          box-shadow: 0 0 0 12px rgba(235, 235, 235, 0.3);
}
.k-checkbox + .k-checkbox-label:active:after {
  -webkit-box-shadow: 0 0 0 12px rgba(235, 235, 235, 0.3);
          box-shadow: 0 0 0 12px rgba(235, 235, 235, 0.3);
}
.k-checkbox:checked + .k-checkbox-label:active:after {
  -webkit-box-shadow: 0 0 0 12px rgba(63, 81, 181, 0.3);
          box-shadow: 0 0 0 12px rgba(63, 81, 181, 0.3);
}
.k-checkbox:disabled + .k-checkbox-label:active:after {
  -webkit-box-shadow: none;
          box-shadow: none;
}
.k-checkbox:indeterminate + .k-checkbox-label:before {
  border-color: #3f51b5;
}
/*!
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       

*/
.k-radio-label:before {
  border-color: #7f7f7f;
  border-radius: 50%;
  background-color: #fff;
  border-width: 2px;
}
.k-radio-label:hover:before,
.k-radio:checked + .k-radio-label:hover:before {
  border-color: #7f7f7f;
  -webkit-box-shadow: none;
          box-shadow: none;
}
.k-radio:checked + .k-radio-label:after {
  background-color: #3f51b5;
  border-radius: 50%;
}
.k-radio-label:active:before {
  border-color: #6b7acb;
  -webkit-box-shadow: 0 0 2px 0 #6b7acb;
          box-shadow: 0 0 2px 0 #6b7acb;
}
.k-radio:checked + .k-radio-label:active:before {
  -webkit-box-shadow: 0 0 2px 0 #6b7acb;
          box-shadow: 0 0 2px 0 #6b7acb;
  border-color: #6b7acb;
}
.k-radio:disabled + .k-radio-label {
  color: #bfbfbf;
}
.k-radio:disabled + .k-radio-label:before,
.k-radio:disabled + .k-radio-label:active:before,
.k-radio:disabled + .k-radio-label:hover:after,
.k-radio:disabled + .k-radio-label:hover:before {
  background: #ffffff;
  border-color: #bfbfbf;
  -webkit-box-shadow: none;
          box-shadow: none;
}
.k-radio:disabled:checked + .k-radio-label:after {
  background-color: #3f51b5;
  opacity: .5;
}
.k-radio:focus + .k-radio-label:before {
  border-color: #6b7acb;
  -webkit-box-shadow: 0 0 2px 0 #6b7acb;
          box-shadow: 0 0 2px 0 #6b7acb;
}
.k-radio:checked + .k-radio-label:before,
.k-radio:checked + .k-radio-label:hover:before {
  border-color: #3f51b5;
}
.k-radio + .k-radio-label:active:before {
  border-color: #7f7f7f;
  -webkit-box-shadow: 0 0 0 12px rgba(235, 235, 235, 0.3);
          box-shadow: 0 0 0 12px rgba(235, 235, 235, 0.3);
}
.k-radio:checked + .k-radio-label:active:before {
  -webkit-box-shadow: 0 0 0 12px rgba(63, 81, 181, 0.3);
          box-shadow: 0 0 0 12px rgba(63, 81, 181, 0.3);
}
.k-radio:focus + .k-radio-label:before {
  border-color: #7f7f7f;
  -webkit-box-shadow: 0 0 0 12px rgba(235, 235, 235, 0.3);
          box-shadow: 0 0 0 12px rgba(235, 235, 235, 0.3);
}
.k-radio:disabled:checked + .k-radio-label:before,
.k-radio:disabled:checked + .k-radio-label:hover:before {
  border-color: #bfbfbf;
}
.k-radio:disabled:checked + .k-radio-label:active:before {
  -webkit-box-shadow: none;
          box-shadow: none;
}
@media only screen and (-webkit-min-device-pixel-ratio: 1.2), only screen and (min-device-pixel-ratio: 1.2) {
  .k-icon:not(.k-loading),
  .k-grouping-dropclue,
  .k-drop-hint,
  .k-callout,
  .k-tool-icon,
  .k-state-hover .k-tool-icon,
  .k-state-active .k-tool-icon,
  .k-state-active.k-state-hover .k-tool-icon,
  .k-state-selected .k-tool-icon,
  .k-state-selected.k-state-hover .k-tool-icon,
  .k-column-menu .k-sprite,
  .k-mobile-list .k-check:checked,
  .k-mobile-list .k-edit-field [type=checkbox]:checked,
  .k-mobile-list .k-edit-field [type=radio]:checked {
    background-image: url('Material/sprite_2x.png');
    -webkit-background-size: 340px 336px;
            background-size: 340px 336px;
  }
  .k-dropdown-wrap .k-input,
  .k-picker-wrap .k-input,
  .k-numeric-wrap .k-input {
    border-radius: -1px 0 0 -1px;
  }
  .k-i-kpi-decrease,
  .k-i-kpi-denied,
  .k-i-kpi-equal,
  .k-i-kpi-hold,
  .k-i-kpi-increase,
  .k-i-kpi-open {
    background-image: url('Material/sprite_kpi_2x.png');
    -webkit-background-size: 96px 16px;
            background-size: 96px 16px;
  }
}
@media screen and (-ms-high-contrast: active) {
  .k-editor-toolbar-wrap .k-dropdown-wrap.k-state-focused,
  .k-editor-toolbar-wrap .k-button-group .k-tool:focus {
    border-color: #fff;
  }
}
.k-button:hover .k-icon,
.k-tool-icon:hover,
.k-state-hover .k-tool-icon,
.k-state-selected .k-tool-icon,
.k-state-focused .k-tool-icon,
.k-button:hover .k-tool-icon,
.k-splitbar.k-splitbar-horizontal-hover .k-icon,
.k-splitbar.k-splitbar-vertical-hover .k-icon,
div.k-splitbar.k-state-focused .k-icon,
.k-textbox:hover > .k-icon,
.k-grouping-header .k-group-delete,
.k-grouping-header .k-button-icon:hover > .k-icon.k-group-delete,
.k-grouping-header .k-si-arrow-n,
.k-grouping-header .k-link:hover > .k-icon.k-si-arrow-n,
.k-grouping-header .k-si-arrow-s,
.k-grouping-header .k-link:hover > .k-icon.k-si-arrow-s,
.k-grid-toolbar .k-i-pdf,
.k-grid-toolbar .k-button:hover > .k-i-pdf,
.k-grid-toolbar .k-i-excel,
.k-grid-toolbar .k-button:hover > .k-i-excel,
.k-grid-toolbar .k-icon,
.k-scheduler-toolbar .k-icon,
.k-scheduler-footer .k-icon,
.k-scheduler-content .k-icon,
.k-gantt-toolbar .k-icon,
.k-field-actions .k-icon,
.k-notification .k-icon,
.k-pivot-configurator-settings .k-icon:hover,
.k-window-titlebar .k-icon {
  opacity: 1;
}
.k-tool-icon,
.k-splitbar .k-icon,
.k-pivot-configurator-settings .k-icon {
  opacity: 0.7;
}
.k-pager-wrap .k-link.k-state-disabled .k-icon {
  opacity: 0.25;
}
.k-button,
.k-button:hover,
.k-button.k-state-hover,
.k-button.k-state-focused,
.k-button:focus,
.k-button:focus:not(.k-state-disabled):not([disabled]) {
  -webkit-box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2), 0 2px 3px rgba(0, 0, 0, 0.05);
          box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2), 0 2px 3px rgba(0, 0, 0, 0.05);
}
.k-button,
.k-header {
  font-weight: 500;
}
.k-button:active,
.k-button.k-state-active,
.k-button:focus,
.k-button.k-state-focused {
  color: #444444;
  background-color: #dbdbdb;
  border-color: #dbdbdb;
}
.k-button:active:hover,
.k-button.k-state-active:hover {
  color: #444444;
  border-color: #dbdbdb;
  background-color: #dbdbdb;
  -webkit-box-shadow: 0 6px 17px 0 rgba(235, 235, 235, 0.3);
          box-shadow: 0 6px 17px 0 rgba(235, 235, 235, 0.3);
}
.k-button:hover,
.k-button.k-state-hover,
.k-button:active:hover,
.k-button.k-state-active:hover {
  color: #444444;
  border-color: #ebebeb;
  background-color: #ebebeb;
}
.k-primary:active,
.k-primary.k-state-active,
.k-primary:focus,
.k-primary.k-state-focused {
  color: #ffffff;
  border-color: #283593;
  background-image: none;
  background-color: #283593;
  -webkit-box-shadow: 0 6px 17px 0 rgba(0, 0, 0, 0.3);
          box-shadow: 0 6px 17px 0 rgba(0, 0, 0, 0.3);
}
.k-primary:hover,
.k-primary.k-state-hover,
.k-primary:active:hover,
.k-primary.k-state-active:hover {
  color: #ffffff;
  border-color: #5c6bc0;
  background-color: #5c6bc0;
}
.k-primary:focus:not(.k-state-disabled):not([disabled]),
.k-primary:focus:active:not(.k-state-disabled):not([disabled]) {
  -webkit-box-shadow: 0 6px 17px 0 rgba(0, 0, 0, 0.3);
          box-shadow: 0 6px 17px 0 rgba(0, 0, 0, 0.3);
}
.k-primary.k-state-disabled,
.k-state-disabled .k-primary,
.k-primary.k-state-disabled:hover,
.k-state-disabled .k-primary:hover,
.k-primary.k-state-disabled:hover,
.k-state-disabled .k-primary:active,
.k-primary.k-state-disabled:active {
  color: #a8a8a8;
  border-color: #eaeaea;
  background-color: #eaeaea;
  background-image: none;
  -webkit-box-shadow: none;
          box-shadow: none;
}
.k-widget .k-button:active,
.k-widget .k-button.k-state-active {
  color: #444444;
  background-color: #dbdbdb;
  border-color: #ebebeb;
}
.k-toolbar .k-overflow-anchor.k-state-active,
.k-toolbar .k-overflow-anchor.k-state-border-down {
  background-color: #ffffff;
}
.k-widget .k-button:active:hover,
.k-widget .k-button.k-state-active:hover {
  color: #444444;
  border-color: #ebebeb;
  background-color: #ebebeb;
}
.k-button[disabled],
.k-button.k-state-disabled,
.k-state-disabled .k-button,
.k-state-disabled .k-button:hover,
.k-button.k-state-disabled:hover,
.k-state-disabled .k-button:active,
.k-button.k-state-disabled:active,
.k-button.k-state-disabled:active:hover {
  color: #999999;
  border-color: #fafafa;
  background-color: #fafafa;
  background-image: none;
  -webkit-box-shadow: none;
          box-shadow: none;
}
.k-dropdown .k-state-default {
  border-color: #fafafa;
  background-image: none;
  background-position: 50% 50%;
  background-color: #fafafa;
}
.k-dropdown,
span.k-colorpicker {
  background-color: #fafafa;
}
.k-textbox {
  background-color: #fafafa;
  border-color: #f0f0f0;
}
.k-combobox,
.k-datepicker,
.k-timepicker,
.k-datetimepicker {
  background-color: #fafafa;
}
.k-picker-wrap.k-state-default > .k-select {
  border-color: #fafafa;
}
.k-datepicker .k-input,
.k-timepicker .k-input {
  background-color: #fafafa;
}
.k-autocomplete.k-state-active .k-input,
.k-picker-wrap.k-state-active .k-input,
.k-numeric-wrap.k-state-active .k-input {
  background-color: #fff;
}
.k-picker-wrap.k-state-hover > .k-select,
.k-picker-wrap.k-state-focused > .k-select {
  border-color: #ffffff;
}
.k-picker-wrap.k-state-hover .k-input,
.k-picker-wrap.k-state-focused .k-input {
  background-color: #ffffff;
}
.k-textbox:hover,
.k-overflow-anchor:hover,
.k-autocomplete.k-state-hover,
.k-picker-wrap.k-state-hover,
.k-numeric-wrap.k-state-hover,
.k-dropdown-wrap.k-state-hover {
  background-color: #fff;
  border-color: #f5f5f5;
  -webkit-box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2), 0 2px 3px rgba(0, 0, 0, 0.05);
          box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2), 0 2px 3px rgba(0, 0, 0, 0.05);
}
.k-textbox:focus,
.k-autocomplete.k-state-focused,
.k-picker-wrap.k-state-focused,
.k-numeric-wrap.k-state-focused,
.k-dropdown-wrap.k-state-focused,
.k-multiselect.k-header.k-state-focused {
  background-color: #ebebeb;
  background-image: none;
  background-position: 50% 50%;
  border-color: #ebebeb;
  -webkit-box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2), 0 2px 3px rgba(0, 0, 0, 0.05);
          box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2), 0 2px 3px rgba(0, 0, 0, 0.05);
}
.k-numeric-wrap.k-state-focused > .k-select {
  background-color: #ebebeb;
}
.k-textbox:focus,
.k-autocomplete.k-state-active,
.k-picker-wrap.k-state-active,
.k-numeric-wrap.k-state-active,
.k-dropdown-wrap.k-state-active,
.k-multiselect.k-header.k-state-active {
  background-color: #fff;
  background-image: none;
  background-position: 50% 50%;
  border-color: #f5f5f5;
  -webkit-box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2), 0 2px 3px rgba(0, 0, 0, 0.05);
          box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2), 0 2px 3px rgba(0, 0, 0, 0.05);
}
.k-autocomplete.k-state-disabled,
.k-picker-wrap.k-state-disabled,
.k-numeric-wrap.k-state-disabled,
.k-numeric-wrap.k-state-disabled .k-input,
.k-numeric-wrap.k-state-disabled .k-select,
.k-dropdown-wrap.k-state-disabled,
.k-multiselect.k-header.k-state-disabled {
  background-color: #fafafa;
}
.k-numeric-wrap.k-state-disabled .k-select {
  border-color: #fafafa;
}
.k-numerictextbox .k-select {
  background-color: #fff;
  border-color: #fff;
}
.k-list > .k-state-selected.k-state-focused {
  -webkit-box-shadow: none;
          box-shadow: none;
  color: #3f51b5;
}
.k-list > .k-state-selected {
  -webkit-box-shadow: none;
          box-shadow: none;
}
.k-list > .k-state-focused {
  border-color: transparent;
  -webkit-box-shadow: none;
          box-shadow: none;
}
.k-list > .k-state-hover,
.k-list > .k-state-selected.k-state-hover {
  background-color: #ebebeb;
  border-color: #ebebeb;
}
.k-list-container {
  border-color: #ebebeb;
}
.k-grid td.k-state-focused.k-state-selected {
  -webkit-box-shadow: inset 0 0 0 1px #808080;
          box-shadow: inset 0 0 0 1px #808080;
}
.k-calendar td.k-state-focused,
.k-calendar td.k-state-selected.k-state-focused {
  -webkit-box-shadow: inset 0 0 0 1px #808080;
          box-shadow: inset 0 0 0 1px #808080;
}
.k-calendar td.k-state-selected {
  background-color: #00b0ff;
  -webkit-box-shadow: none;
          box-shadow: none;
}
.k-calendar td.k-state-selected.k-state-hover {
  background-color: #00a2eb;
}
.k-calendar .k-state-selected > .k-link {
  color: #fff;
}
/* Calendar */
.k-calendar .k-header .k-link {
  color: #ffffff;
}
.k-calendar .k-footer {
  border-color: #e6e6e6;
}
.k-calendar td {
  border-radius: 50%;
}
.k-calendar .k-content th {
  background-color: #fff;
}
.k-calendar .k-header .k-state-hover {
  background-color: #32408f;
}
.k-calendar .k-footer .k-nav-today {
  color: #3f51b5;
}
.k-calendar .k-nav-fast.k-state-hover {
  border-radius: 0;
}
.k-calendar .k-today {
  background-color: #3f51b5;
}
.k-calendar .k-today .k-link {
  color: #fff;
}
.k-calendar .k-today.k-state-hover {
  background-color: #32408f;
}
.k-calendar .k-today:active {
  -webkit-box-shadow: inset 0 0 0 1px #2b387c;
          box-shadow: inset 0 0 0 1px #2b387c;
}
.k-calendar .k-link.k-state-hover,
.k-window-titlebar .k-link {
  -webkit-box-shadow: none;
          box-shadow: none;
}
.k-window-titlebar .k-state-hover {
  background-color: #5c6dc4;
  border-color: #5c6dc4;
}
/* TabStrip */
.k-tabstrip > .k-tabstrip-items > .k-item {
  border-radius: 0;
}
.k-tabstrip-items .k-state-active,
.k-panelbar .k-tabstrip-items .k-state-active {
  background-color: #3f51b5;
  background-image: none;
  border-color: #00b0ff;
}
.k-tabstrip .k-content.k-state-active {
  border-color: transparent;
}
.k-tabstrip-items .k-item.k-state-hover {
  background: #5c6dc4;
  border-color: #5c6dc4;
}
.k-tabstrip-items .k-state-hover .k-link {
  color: #ffffff;
}
/* Menu */
.k-group,
.k-flatcolorpicker.k-group,
.k-menu,
.k-menu .k-group,
.k-popup.k-widget.k-context-menu {
  color: #444444;
  background-color: #fff;
}
.k-menu .k-group,
.k-popup.k-context-menu.k-group {
  border-color: #e6e6e6;
}
.k-menu.k-header,
.k-menu .k-item,
.k-widget.k-menu-horizontal > .k-item {
  -webkit-box-shadow: none;
          box-shadow: none;
}
.k-menu .k-state-active,
.k-popup.k-context-menu.k-group .k-state-hover {
  background-color: #ebebeb;
  border-color: #ebebeb;
}
/* Toolbar */
.k-toolbar {
  background-color: #fafafa;
  border-color: #e6e6e6;
}
.k-toolbar .k-toggle-button:focus {
  background-color: transparent;
  border-color: #b3b3b3;
}
.k-toolbar .k-toggle-button:hover {
  background-color: #ebebeb;
}
.k-toolbar .k-toggle-button.k-state-active {
  color: #fff;
  background-color: #3f51b5;
  border-color: #3f51b5;
}
.k-toolbar .k-toggle-button.k-state-active:focus {
  background-color: #3f51b5;
  border-color: #2b387c;
}
.k-toolbar .k-toggle-button.k-state-active:hover {
  color: #fff;
  background-color: #32408f;
  border-color: #32408f;
}
.k-toolbar .k-button {
  -webkit-box-shadow: none;
          box-shadow: none;
}
.k-grid .k-header .k-button,
.k-scheduler .k-header .k-button,
.k-scheduler .k-header li,
.k-scheduler .k-header .k-link,
.k-gantt > .k-header li,
.k-gantt > .k-header .k-link,
.k-gantt-toolbar .k-button,
.km-pane-wrapper .k-header .k-button {
  color: #ffffff;
  background-color: #3f51b5;
  border-color: #3f51b5;
  -webkit-box-shadow: none;
          box-shadow: none;
}
.k-grid .k-header .k-button:hover,
.k-scheduler .k-header .k-button:hover,
.k-scheduler .k-scheduler-toolbar .k-scheduler-views li.k-state-hover,
.k-scheduler .k-scheduler-toolbar .k-scheduler-views li.k-state-hover .k-link,
.k-gantt .k-gantt-toolbar .k-gantt-views li.k-state-hover,
.k-gantt .k-gantt-toolbar .k-gantt-views li.k-state-hover .k-link,
.k-gantt .k-gantt-toolbar .k-button:hover,
.km-pane-wrapper .k-header .k-button:hover {
  background-color: #5c6dc4;
  border-color: #5c6dc4;
}
.km-pane-wrapper .k-header .k-button:active:hover {
  color: #ffffff;
}
.k-scheduler .k-scheduler-toolbar ul li.k-state-hover,
.k-scheduler .k-scheduler-toolbar .k-state-selected,
.k-gantt-toolbar .k-button {
  background-color: #3f51b5;
  border-color: #3f51b5;
}
.k-gantt .k-gantt-toolbar .k-button:active {
  background: #fff;
  -webkit-box-shadow: none;
          box-shadow: none;
}
.k-gantt-toolbar > .k-gantt-views > li.k-state-selected,
.k-gantt .k-gantt-toolbar .k-gantt-views li.k-state-selected.k-state-hover,
.k-scheduler .k-scheduler-toolbar .k-scheduler-views li.k-state-selected.k-state-hover,
.k-scheduler-toolbar > .k-scheduler-views > li.k-state-selected {
  border-bottom-color: #00b0ff;
}
.k-scheduler-mark {
  border-radius: 50%;
}
/* Grid */
.k-grid .k-alt {
  background-color: #fff;
}
.k-grouping-row td,
td.k-group-cell,
.k-resize-handle-inner {
  color: #444444;
  background-color: #fafafa;
}
.k-grouping-header .k-group-indicator,
.k-pivot-toolbar .k-button {
  color: #ffffff;
  background-color: #32408f;
  border-color: #32408f;
  -webkit-box-shadow: none;
          box-shadow: none;
}
.k-grid-header,
.k-grid-header .k-header,
.k-pager-wrap,
.k-pager-numbers .k-state-selected,
.k-grid-footer,
.k-grid-footer td,
.k-scheduler-header,
.km-pane-wrapper .k-grid-header .k-header {
  color: #444444;
  background-color: #fafafa;
}
.k-header.k-scheduler-footer .k-header,
.k-header.k-scheduler-footer ul.k-header li .k-link {
  color: #00b0ff;
  background-color: #fafafa;
}
.k-header.k-scheduler-footer ul.k-header li {
  background-color: #fafafa;
  border-color: #fafafa;
}
.k-header,
.k-grid-header-wrap,
.k-grid .k-grouping-header,
.k-grid-header,
.k-pager-wrap,
.k-pager-wrap .k-textbox,
.k-pager-wrap .k-link,
.k-gantt-toolbar .k-state-default,
.k-grouping-row td,
.k-group-footer td,
.k-grid-footer td {
  border-color: #e6e6e6;
}
.k-group-footer td,
.k-footer-template td,
.k-fieldselector .k-item.k-header {
  color: #444444;
  background-color: #fafafa;
}
.k-grid .k-grouping-header {
  color: rgba(255, 255, 255, 0.5);
}
.k-pager-wrap,
.k-editor-toolbar {
  color: #444444;
}
.k-grouping-header .k-link,
.k-grouping-header .k-link:link {
  color: #ffffff;
}
.k-scheduler-layout .k-state-selected,
.k-scheduler .k-today.k-state-selected,
.k-grid tr.k-state-selected,
.k-grid td.k-state-selected,
.k-grid td.k-state-selected.k-state-focused,
.k-marquee-color,
.k-gantt .k-treelist .k-state-selected,
.k-gantt .k-treelist .k-state-selected td,
.k-gantt .k-treelist .k-alt.k-state-selected,
.k-gantt .k-treelist .k-alt.k-state-selected > td,
.k-listview > .k-state-selected,
.k-state-selected.k-line {
  background-color: #00b0ff;
}
.k-state-selected.k-line {
  color: #00b0ff;
}
.k-grid tr.k-state-selected,
.k-grid td.k-state-selected,
.k-listview > .k-state-selected,
.k-state-selected .k-progress-status {
  color: #fff;
}
.k-grid tr:hover {
  background-color: #ebebeb;
}
.k-pivot-rowheaders .k-grid tr:hover {
  background: none;
}
.k-grid td.k-state-selected,
.k-grid tr.k-state-selected > td {
  border-color: #008dcc;
}
.k-grid td.k-state-selected:hover,
.k-grid tr.k-state-selected:hover td {
  background-color: #00a2eb;
}
.k-grid-header .k-header .k-link,
.k-grid-header .k-header,
.k-grid-header .k-link,
.k-grid-header .k-link:link,
.k-pager-info,
.k-scheduler-header,
.k-scheduler-agendaview .k-scheduler-datecolumn {
  color: #a8a8a8;
}
.k-gantt .k-task-draghandle {
  border-color: #00b0ff;
}
.k-grid-pager .k-link,
.k-grid-pager .k-link:link {
  color: #444444;
}
.k-pager-numbers .k-link,
.k-pager-wrap > .k-link {
  border-radius: 0;
}
.k-pager-numbers .k-state-selected {
  border-color: #3f51b5 transparent transparent;
  border-radius: 0;
  -webkit-box-shadow: none;
          box-shadow: none;
  color: #3f51b5;
}
.k-pager-wrap .k-link {
  border-color: #fafafa;
  cursor: pointer;
}
.k-pager-wrap .k-link:hover {
  background-color: transparent;
  border-color: transparent;
}
.k-scheduler-toolbar > ul li:first-child,
.k-scheduler-toolbar > ul li:first-child .k-link,
.k-scheduler-toolbar .k-nav-next,
.k-scheduler-toolbar ul + ul li:last-child,
.k-scheduler-toolbar .k-nav-next .k-link,
.k-scheduler-toolbar ul + ul li:last-child .k-link,
.k-gantt-toolbar li:first-child,
.k-gantt-toolbar li:first-child > .k-link,
.k-gantt-toolbar li:last-child,
.k-gantt-toolbar li:last-child > .k-link {
  border-radius: 0;
}
.k-grid,
.k-panelbar,
.k-notification,
.k-popup .k-textbox:focus,
.k-popup .k-autocomplete.k-state-focused,
.k-popup .k-picker-wrap.k-state-focused,
.k-popup .k-numeric-wrap.k-state-focused,
.k-popup .k-dropdown-wrap.k-state-focused,
.k-popup .k-multiselect.k-header.k-state-focused,
.k-popup .k-textbox:hover,
.k-popup .k-autocomplete.k-state-hover,
.k-popup .k-picker-wrap.k-state-hover,
.k-popup .k-numeric-wrap.k-state-hover,
.k-popup .k-dropdown-wrap.k-state-hover {
  -webkit-box-shadow: none;
          box-shadow: none;
}
/* PanelBar */
.k-panelbar,
.k-panelbar .k-header,
.k-panelbar .k-content,
.k-panel > li.k-item,
.k-panelbar .k-state-selected {
  background-color: #fafafa;
}
.k-panelbar .k-grid-toolbar {
  background-color: #3f51b5;
}
.k-panelbar > li.k-state-default > .k-link {
  color: #444444;
}
.k-panelbar > li > .k-state-hover {
  background-color: #ebebeb;
}
.k-panelbar > .k-item > .k-link,
.k-panelbar.k-header,
.k-panelbar .k-content,
.k-panelbar .k-panel,
.k-panelbar .k-item {
  border-color: #e6e6e6;
}
/* Splitter */
.k-splitbar {
  border-color: #fafafa;
}
.k-splitbar.k-state-focused {
  background-color: #3f51b5;
  border-color: #3f51b5;
  -webkit-box-shadow: none;
          box-shadow: none;
}
/* Upload */
.k-upload {
  color: #444444;
  background-color: #fff;
}
.k-upload-files .k-button,
.k-upload-files .k-button:focus,
.k-upload-files .k-button:focus:not(.k-state-disabled):not([disabled]) {
  -webkit-box-shadow: none;
          box-shadow: none;
}
/* Gantt */
.k-task-milestone,
.k-task-summary-complete,
.k-state-selected.k-task-milestone,
.k-state-selected .k-task-summary-complete {
  background-color: #3f51b5;
  border-color: #3f51b5;
}
.k-task-single {
  background-color: #66d0ff;
}
.k-task-complete {
  background: #00b0ff 0 -257px none repeat-x;
}
.k-treelist .k-state-selected,
.k-treelist .k-state-selected td,
.k-treelist .k-alt.k-state-selected,
.k-treelist .k-alt.k-state-selected > td {
  background-color: #00b0ff;
  border-color: #00b0ff;
}
.k-multiselect .k-button:focus:active:not(.k-state-disabled):not([disabled]),
.k-toolbar .k-button:focus:active:not(.k-state-disabled):not([disabled]),
.k-group-indicator .k-button,
.k-group-indicator .k-button:focus:active:not(.k-state-disabled):not([disabled]),
.k-group-indicator .k-button:focus:not(.k-state-disabled):not([disabled]),
.k-gantt-toolbar .k-button:focus:not(.k-state-disabled):not([disabled]),
.k-gantt-toolbar .k-button:focus:active:not(.k-state-disabled):not([disabled]),
.k-toolbar .k-button:focus:not(.k-state-disabled):not([disabled]),
.k-toolbar .k-button:focus:active:not(.k-state-disabled):not([disabled]),
.k-toolbar .k-button:active:hover,
.k-toolbar .k-button.k-state-active:hover {
  -webkit-box-shadow: none;
          box-shadow: none;
}
.k-multiselect .k-button:active:hover {
  color: #444444;
  background-color: #dbdbdb;
  border-color: #dbdbdb;
}
.k-multiselect-wrap > ul > .k-button {
  -webkit-box-shadow: none;
          box-shadow: none;
}
/* Editor */
table.k-editor {
  border-color: #e6e6e6;
}
.k-editor.k-header,
.editorToolbarWindow.k-header,
.k-filebrowser .k-header {
  background-color: #fafafa;
  -webkit-box-shadow: none;
          box-shadow: none;
}
.k-filebrowser .k-header {
  color: #444444;
}
.k-editor-toolbar .k-tool,
.k-group-start.k-group-end.k-tool {
  border-color: #fafafa;
}
.k-treeview .k-state-selected,
.k-treeview .k-state-focused,
.k-editor-toolbar .k-dropdown,
.k-panelbar > li > .k-state-focused {
  -webkit-box-shadow: none;
          box-shadow: none;
}
.k-treeview .k-state-focused,
.k-panelbar > li > .k-state-focused {
  background-color: #ebebeb;
}
.k-editor-toolbar .k-dropdown-wrap.k-state-default,
.k-toolbar .k-dropdown-wrap.k-state-default {
  border-color: #fafafa;
}
.k-editor-toolbar .k-tool.k-state-hover,
.k-editor-toolbar .k-dropdown-wrap.k-state-hover,
.k-toolbar .k-tool.k-state-hover,
.k-toolbar .k-dropdown-wrap.k-state-hover {
  color: #444444;
  border-color: #ebebeb;
  -webkit-box-shadow: none;
          box-shadow: none;
}
.k-editor-toolbar .k-tool.k-state-selected,
.k-toolbar .k-button-group .k-button.k-state-active {
  -webkit-box-shadow: none;
          box-shadow: none;
  background-color: #dbdbdb;
  border-color: #dbdbdb;
}
.k-editor-toolbar .k-tool.k-state-hover,
.k-toolbar .k-button-group .k-button:hover {
  background-color: #ebebeb;
  border-color: #ebebeb;
}
/* Progressbar */
.k-progressbar {
  background-color: #fafafa;
  border-color: #fafafa;
}
.k-progressbar .k-item,
.k-progressbar .k-item.k-state-selected {
  border-color: #fff;
}
.k-progressbar .k-state-selected {
  background-color: #3f51b5;
  border-color: #3f51b5;
}
.k-widget.k-tooltip-validation {
  -webkit-box-shadow: none;
          box-shadow: none;
}
/* Pivot Grid */
.k-grid.k-alt {
  background-color: #fafafa;
}
.k-gantt .k-treelist .k-alt,
.k-gantt .k-header.k-nonwork-hour {
  background-color: #fafafa;
}
.k-list > .k-state-hover,
.k-list > .k-state-focused {
  color: #444444;
  background-color: #ebebeb;
  border-color: #ebebeb;
}
/* Slider */
.k-slider-track {
  background-color: #cccccc;
  -webkit-box-shadow: none;
          box-shadow: none;
}
.k-slider-selection {
  background-color: #3f51b5;
  border-color: #3f51b5;
}
.k-slider .k-button,
.k-slider .k-button.k-state-hover,
.k-slider .k-button:active:hover,
.k-slider .k-button:focus,
.k-slider .k-button:active {
  background: none;
  border: none;
  -webkit-box-shadow: none;
          box-shadow: none;
}
.k-draghandle,
.k-flatcolorpicker .k-slider-horizontal .k-slider-track {
  -webkit-box-shadow: none;
          box-shadow: none;
}
.k-flatcolorpicker .k-hue-slider .k-draghandle,
.k-flatcolorpicker .k-transparency-slider .k-draghandle {
  border-color: #3f51b5;
  background-color: #3f51b5;
  -webkit-box-shadow: none;
          box-shadow: none;
}
.k-flatcolorpicker .k-hue-slider .k-draghandle:hover,
.k-flatcolorpicker .k-transparency-slider .k-draghandle:hover {
  border-color: #3f51b5;
  background-color: #3f51b5;
  -webkit-box-shadow: 0 0 0 8px rgba(63, 81, 181, 0.3);
          box-shadow: 0 0 0 8px rgba(63, 81, 181, 0.3);
}
.k-draghandle.k-state-selected,
.k-draghandle.k-state-selected:link,
.k-draghandle.k-state-selected:hover,
.k-flatcolorpicker .k-hue-slider .k-draghandle.k-state-selected,
.k-flatcolorpicker .k-transparency-slider .k-draghandle.k-state-selected {
  background-color: #cccccc;
  border-color: #cccccc;
}
.k-draghandle.k-state-focused,
.k-draghandle.k-state-focused:link,
.k-flatcolorpicker .k-hue-slider .k-draghandle.k-state-focused,
.k-flatcolorpicker .k-transparency-slider .k-draghandle.k-state-focused {
  -webkit-box-shadow: none;
          box-shadow: none;
  border-color: #3f51b5;
  background-color: #3f51b5;
}
.k-edit-form-container .k-edit-buttons {
  background-color: #fafafa;
}
.k-popup .k-button,
.k-popup .k-button:active:hover {
  -webkit-box-shadow: none;
          box-shadow: none;
}
.k-edit-form-container .k-button,
.k-popup .k-button,
.k-popup .k-primary:active,
.k-popup .k-primary:active:hover,
.k-edit-form-container .k-primary:active {
  color: #444444;
  background-color: #fafafa;
  border-color: #fafafa;
  -webkit-box-shadow: none;
          box-shadow: none;
}
.k-popup .k-primary,
.k-edit-form-container .k-primary {
  color: #00b0ff;
  background-color: #fafafa;
  border-color: #fafafa;
}
.k-split-wrapper .k-button,
.k-overflow-container .k-button,
.k-filter-menu .k-button {
  background: transparent;
  border-color: transparent;
}
.k-split-wrapper .k-button,
.k-overflow-container .k-button {
  text-transform: none;
}
.k-split-wrapper .k-button:hover,
.k-overflow-container .k-button:hover {
  background-color: #ebebeb;
  border-color: #ebebeb;
}
.k-split-wrapper .k-button:focus,
.k-overflow-container .k-button:focus,
.k-split-wrapper .k-button:focus:not(.k-state-disabled):not([disabled]),
.k-overflow-container .k-button:focus:not(.k-state-disabled):not([disabled]) {
  color: #3f51b5;
  -webkit-box-shadow: none;
          box-shadow: none;
}
.k-filter-menu .k-button {
  background: transparent;
  border-color: transparent;
}
.k-filter-menu .k-primary {
  border-left-color: #f0f0f0;
}
.k-filter-menu > div > div:last-child {
  border-color: #f0f0f0;
}
.k-popup .k-button:focus:active:not(.k-state-disabled):not([disabled]),
.k-edit-form-container .k-button:focus:active:not(.k-state-disabled):not([disabled]) {
  -webkit-box-shadow: none;
          box-shadow: none;
}
.k-edit-form-container .k-scheduler-delete {
  color: #00b0ff;
}
div.k-scheduler-marquee:before,
div.k-scheduler-marquee:after {
  border-color: #00b0ff;
}
.km-pane-wrapper > .km-pane > .km-view > .km-content {
  color: #3f51b5;
  background-color: #ffffff;
}
.km-pane-wrapper > .km-pane .km-content .k-mobile-list > ul > li > .k-link {
  color: #3f51b5;
}
.k-popup.k-context-menu {
  -webkit-box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.2);
          box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.2);
}
.k-drag-clue {
  color: #444444;
  background-color: #ebebeb;
  border-color: #ebebeb;
  -webkit-box-shadow: inset 0 0 0 1px #808080;
          box-shadow: inset 0 0 0 1px #808080;
}
.k-popup > .k-group-header,
.k-popup > .k-virtual-wrap > .k-group-header {
  color: #444444;
}
.k-popup .k-item > .k-group {
  color: #444444;
}
/* Responsive styles */
@media only screen and (max-width: 370px) {
  .k-webkit .k-pager-refresh,
  .k-ff .k-pager-refresh,
  .k-ie11 .k-pager-refresh,
  .k-safari .k-pager-refresh {
    display: none;
  }
}
@media only screen and (max-width: 590px) {
  .k-webkit .k-pager-refresh,
  .k-ff .k-pager-refresh,
  .k-ie11 .k-pager-refresh,
  .k-safari .k-pager-refresh {
    margin-right: 0;
  }
}
@media only screen and (max-width: 530px) {
  .k-webkit .k-pager-sizes,
  .k-ff .k-pager-sizes,
  .k-ie11 .k-pager-sizes,
  .k-safari .k-pager-sizes {
    display: none;
  }
}
@media only screen and (max-width: 687px) {
  .k-webkit .k-pager-info,
  .k-ff .k-pager-info,
  .k-ie11 .k-pager-info,
  .k-safari .k-pager-info {
    display: none;
  }
}
@media only screen and (max-width: 1024px) {
  .k-scheduler-toolbar > ul.k-scheduler-views {
    right: 13px;
    top: 0;
  }
  .k-webkit,
  .k-ff,
  .k-ie11,
  .k-safari {
    /* Responsive Scheduler */
    /* Responsive Pager */
  }
  .k-webkit .k-scheduler-toolbar > ul.k-scheduler-views,
  .k-ff .k-scheduler-toolbar > ul.k-scheduler-views,
  .k-ie11 .k-scheduler-toolbar > ul.k-scheduler-views,
  .k-safari .k-scheduler-toolbar > ul.k-scheduler-views {
    right: 13px;
    top: 0;
  }
  .k-webkit .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view,
  .k-ff .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view,
  .k-ie11 .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view,
  .k-safari .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view,
  .k-webkit .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view:hover,
  .k-ff .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view:hover,
  .k-ie11 .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view:hover,
  .k-safari .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view:hover {
    background-image: none;
    background-position: 50% 50%;
    background-color: transparent;
    border-color: transparent;
    border-radius: 0px;
    text-align: right;
  }
  .k-webkit .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded > li,
  .k-ff .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded > li,
  .k-ie11 .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded > li,
  .k-safari .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded > li {
    border-radius: 0;
  }
  .k-webkit .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded > li.k-current-view,
  .k-ff .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded > li.k-current-view,
  .k-ie11 .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded > li.k-current-view,
  .k-safari .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded > li.k-current-view {
    border-radius: -1px -1px 0 0;
  }
  .k-webkit .k-scheduler-toolbar > ul li:first-child,
  .k-ff .k-scheduler-toolbar > ul li:first-child,
  .k-ie11 .k-scheduler-toolbar > ul li:first-child,
  .k-safari .k-scheduler-toolbar > ul li:first-child,
  .k-webkit .k-scheduler-toolbar > ul li:first-child .k-link,
  .k-ff .k-scheduler-toolbar > ul li:first-child .k-link,
  .k-ie11 .k-scheduler-toolbar > ul li:first-child .k-link,
  .k-safari .k-scheduler-toolbar > ul li:first-child .k-link,
  .k-webkit .k-scheduler-toolbar > ul.k-scheduler-views li,
  .k-ff .k-scheduler-toolbar > ul.k-scheduler-views li,
  .k-ie11 .k-scheduler-toolbar > ul.k-scheduler-views li,
  .k-safari .k-scheduler-toolbar > ul.k-scheduler-views li,
  .k-webkit .k-scheduler-toolbar > ul.k-scheduler-views li .k-link,
  .k-ff .k-scheduler-toolbar > ul.k-scheduler-views li .k-link,
  .k-ie11 .k-scheduler-toolbar > ul.k-scheduler-views li .k-link,
  .k-safari .k-scheduler-toolbar > ul.k-scheduler-views li .k-link {
    border-radius: 0;
  }
  .k-webkit .k-scheduler-toolbar > ul.k-scheduler-views li:last-child,
  .k-ff .k-scheduler-toolbar > ul.k-scheduler-views li:last-child,
  .k-ie11 .k-scheduler-toolbar > ul.k-scheduler-views li:last-child,
  .k-safari .k-scheduler-toolbar > ul.k-scheduler-views li:last-child,
  .k-webkit .k-scheduler-toolbar > ul.k-scheduler-views li:last-child .k-link,
  .k-ff .k-scheduler-toolbar > ul.k-scheduler-views li:last-child .k-link,
  .k-ie11 .k-scheduler-toolbar > ul.k-scheduler-views li:last-child .k-link,
  .k-safari .k-scheduler-toolbar > ul.k-scheduler-views li:last-child .k-link {
    border-radius: 0 0 -1px -1px;
  }
  .k-webkit .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view:hover,
  .k-ff .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view:hover,
  .k-ie11 .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view:hover,
  .k-safari .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view:hover {
    border-color: transparent;
    background-image: none;
    background-color: transparent;
  }
  .k-webkit .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view > .k-link,
  .k-ff .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view > .k-link,
  .k-ie11 .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view > .k-link,
  .k-safari .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view > .k-link,
  .k-webkit .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view:hover > .k-link,
  .k-ff .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view:hover > .k-link,
  .k-ie11 .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view:hover > .k-link,
  .k-safari .k-scheduler-toolbar > ul.k-scheduler-views > li.k-current-view:hover > .k-link {
    color: #ffffff;
    min-width: 20px;
  }
  .k-webkit .k-scheduler-views > li.k-state-selected > .k-link:after,
  .k-ff .k-scheduler-views > li.k-state-selected > .k-link:after,
  .k-ie11 .k-scheduler-views > li.k-state-selected > .k-link:after,
  .k-safari .k-scheduler-views > li.k-state-selected > .k-link:after {
    display: block;
    content: "";
    position: absolute;
    top: 50%;
    margin-top: -0.5em;
    right: 0.333em;
    width: 1.333em;
    height: 1.333em;
  }
  .k-webkit .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded,
  .k-ff .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded,
  .k-ie11 .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded,
  .k-safari .k-scheduler-toolbar > ul.k-scheduler-views.k-state-expanded {
    border-width: 1px;
    border-style: solid;
    border-color: transparent;
    /*@secondary-border-color*/
    background-image: none;
    background-color: #3f51b5;
    border-radius: 0px;
    -webkit-box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.2);
            box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.2);
  }
  .k-webkit .k-pager-wrap,
  .k-ff .k-pager-wrap,
  .k-ie11 .k-pager-wrap,
  .k-safari .k-pager-wrap {
    min-height: 2.56em;
  }
  .k-webkit .k-pager-wrap .k-pager-nav,
  .k-ff .k-pager-wrap .k-pager-nav,
  .k-ie11 .k-pager-wrap .k-pager-nav,
  .k-safari .k-pager-wrap .k-pager-nav,
  .k-webkit .k-pager-input,
  .k-ff .k-pager-input,
  .k-ie11 .k-pager-input,
  .k-safari .k-pager-input {
    display: inline-block;
    vertical-align: top;
  }
  .k-webkit .k-pager-numbers,
  .k-ff .k-pager-numbers,
  .k-ie11 .k-pager-numbers,
  .k-safari .k-pager-numbers,
  .k-webkit .k-grid .k-pager-numbers,
  .k-ff .k-grid .k-pager-numbers,
  .k-ie11 .k-grid .k-pager-numbers,
  .k-safari .k-grid .k-pager-numbers {
    position: absolute;
    left: 5.6em;
    display: -webkit-inline-box;
    display: -webkit-inline-flex;
    display: -ms-inline-flexbox;
    display: inline-flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: reverse;
    -webkit-flex-direction: column-reverse;
        -ms-flex-direction: column-reverse;
            flex-direction: column-reverse;
    overflow: visible;
    height: auto;
  }
  .k-webkit .k-pager-numbers.k-state-expanded,
  .k-ff .k-pager-numbers.k-state-expanded,
  .k-ie11 .k-pager-numbers.k-state-expanded,
  .k-safari .k-pager-numbers.k-state-expanded,
  .k-webkit .k-grid .k-pager-numbers.k-state-expanded,
  .k-ff .k-grid .k-pager-numbers.k-state-expanded,
  .k-ie11 .k-grid .k-pager-numbers.k-state-expanded,
  .k-safari .k-grid .k-pager-numbers.k-state-expanded {
    -webkit-transform: translatey(-100%);
        -ms-transform: translatey(-100%);
            transform: translatey(-100%);
  }
  .k-webkit .km-pane-wrapper .k-pager-numbers,
  .k-ff .km-pane-wrapper .k-pager-numbers,
  .k-ie11 .km-pane-wrapper .k-pager-numbers,
  .k-safari .km-pane-wrapper .k-pager-numbers,
  .k-webkit .km-pane-wrapper .k-grid .k-pager-numbers,
  .k-ff .km-pane-wrapper .k-grid .k-pager-numbers,
  .k-ie11 .km-pane-wrapper .k-grid .k-pager-numbers,
  .k-safari .km-pane-wrapper .k-grid .k-pager-numbers {
    position: relative;
    left: 50%;
    -ms-transform: translate(-50%, 0%);
        transform: translate(-50%, 0%);
    -webkit-transform: translate(-50%, 0%);
  }
  .k-webkit .km-pane-wrapper .k-pager-numbers.k-state-expanded,
  .k-ff .km-pane-wrapper .k-pager-numbers.k-state-expanded,
  .k-ie11 .km-pane-wrapper .k-pager-numbers.k-state-expanded,
  .k-safari .km-pane-wrapper .k-pager-numbers.k-state-expanded,
  .k-webkit .km-pane-wrapper .k-grid .k-pager-numbers.k-state-expanded,
  .k-ff .km-pane-wrapper .k-grid .k-pager-numbers.k-state-expanded,
  .k-ie11 .km-pane-wrapper .k-grid .k-pager-numbers.k-state-expanded,
  .k-safari .km-pane-wrapper .k-grid .k-pager-numbers.k-state-expanded {
    -webkit-transform: translate(-50%, -100%);
    -ms-transform: translate(-50%, -100%);
        transform: translate(-50%, -100%);
  }
  .k-webkit .km-pane-wrapper .k-pager-numbers .k-link,
  .k-ff .km-pane-wrapper .k-pager-numbers .k-link,
  .k-ie11 .km-pane-wrapper .k-pager-numbers .k-link,
  .k-safari .km-pane-wrapper .k-pager-numbers .k-link,
  .k-webkit .km-pane-wrapper .k-pager-numbers .k-state-selected,
  .k-ff .km-pane-wrapper .k-pager-numbers .k-state-selected,
  .k-ie11 .km-pane-wrapper .k-pager-numbers .k-state-selected,
  .k-safari .km-pane-wrapper .k-pager-numbers .k-state-selected,
  .k-webkit .km-pane-wrapper .k-pager-wrap > .k-link,
  .k-ff .km-pane-wrapper .k-pager-wrap > .k-link,
  .k-ie11 .km-pane-wrapper .k-pager-wrap > .k-link,
  .k-safari .km-pane-wrapper .k-pager-wrap > .k-link,
  .k-webkit .km-pane-wrapper .k-pager-wrap > .k-pager-info,
  .k-ff .km-pane-wrapper .k-pager-wrap > .k-pager-info,
  .k-ie11 .km-pane-wrapper .k-pager-wrap > .k-pager-info,
  .k-safari .km-pane-wrapper .k-pager-wrap > .k-pager-info {
    padding-top: 0;
    padding-bottom: 0;
  }
  .k-webkit .k-rtl .k-pager-numbers,
  .k-ff .k-rtl .k-pager-numbers,
  .k-ie11 .k-rtl .k-pager-numbers,
  .k-safari .k-rtl .k-pager-numbers,
  .k-webkit .k-rtl .k-grid .k-pager-numbers,
  .k-ff .k-rtl .k-grid .k-pager-numbers,
  .k-ie11 .k-rtl .k-grid .k-pager-numbers,
  .k-safari .k-rtl .k-grid .k-pager-numbers {
    right: 5.6em;
    width: 5.15em;
  }
  .k-webkit .k-pager-numbers .k-current-page,
  .k-ff .k-pager-numbers .k-current-page,
  .k-ie11 .k-pager-numbers .k-current-page,
  .k-safari .k-pager-numbers .k-current-page,
  .k-webkit .k-grid .k-pager-numbers .k-current-page,
  .k-ff .k-grid .k-pager-numbers .k-current-page,
  .k-ie11 .k-grid .k-pager-numbers .k-current-page,
  .k-safari .k-grid .k-pager-numbers .k-current-page {
    display: block;
    border-left: 0;
  }
  .k-webkit .k-pager-numbers li:not(.k-current-page),
  .k-ff .k-pager-numbers li:not(.k-current-page),
  .k-ie11 .k-pager-numbers li:not(.k-current-page),
  .k-safari .k-pager-numbers li:not(.k-current-page) {
    display: none;
  }
  .k-webkit .k-pager-numbers .k-current-page .k-link,
  .k-ff .k-pager-numbers .k-current-page .k-link,
  .k-ie11 .k-pager-numbers .k-current-page .k-link,
  .k-safari .k-pager-numbers .k-current-page .k-link {
    width: 3.8em;
    line-height: 2.564em;
    padding: 0 .429em 0 0.8em;
    border-radius: 0px;
    background-image: none;
    background-position: 50% 50%;
    background-color: #fafafa;
    border: 1px solid transparent;
    border-top: 0;
    -webkit-box-shadow: 0 2px 2px 0 #fafafa;
            box-shadow: 0 2px 2px 0 #fafafa;
  }
  .k-webkit .k-pager-numbers .k-current-page:hover .k-link,
  .k-ff .k-pager-numbers .k-current-page:hover .k-link,
  .k-ie11 .k-pager-numbers .k-current-page:hover .k-link,
  .k-safari .k-pager-numbers .k-current-page:hover .k-link {
    border-radius: 0px;
    background-color: #fff;
    border: 1px solid #ebebeb;
    border-top: 0;
    -webkit-box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2), 0 2px 3px rgba(0, 0, 0, 0.05);
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2), 0 2px 3px rgba(0, 0, 0, 0.05);
  }
  .k-webkit .k-pager-numbers .k-current-page .k-link:after,
  .k-ff .k-pager-numbers .k-current-page .k-link:after,
  .k-ie11 .k-pager-numbers .k-current-page .k-link:after,
  .k-safari .k-pager-numbers .k-current-page .k-link:after {
    display: block;
    content: "";
    position: absolute;
    top: 50%;
    margin-top: -0.6em;
    right: 0.6em;
    width: 1.333em;
    height: 1.333em;
    background-position: 0 -30px;
  }
  .k-webkit .k-pager-numbers + .k-link,
  .k-ff .k-pager-numbers + .k-link,
  .k-ie11 .k-pager-numbers + .k-link,
  .k-safari .k-pager-numbers + .k-link {
    margin-left: 5.4em;
  }
  .k-webkit .k-rtl .k-pager-numbers + .k-link,
  .k-ff .k-rtl .k-pager-numbers + .k-link,
  .k-ie11 .k-rtl .k-pager-numbers + .k-link,
  .k-safari .k-rtl .k-pager-numbers + .k-link {
    margin-right: 5.4em;
    margin-left: 0;
  }
  .k-webkit .k-pager-wrap .k-pager-numbers .k-state-selected,
  .k-ff .k-pager-wrap .k-pager-numbers .k-state-selected,
  .k-ie11 .k-pager-wrap .k-pager-numbers .k-state-selected,
  .k-safari .k-pager-wrap .k-pager-numbers .k-state-selected,
  .k-webkit .k-pager-wrap .k-pager-numbers .k-link,
  .k-ff .k-pager-wrap .k-pager-numbers .k-link,
  .k-ie11 .k-pager-wrap .k-pager-numbers .k-link,
  .k-safari .k-pager-wrap .k-pager-numbers .k-link {
    display: block;
    margin-top: 0;
    margin-right: 0;
    padding: 1px 5px 1px .8em;
    text-align: left;
    border-top: 0;
    border-radius: 0;
  }
  .k-webkit .k-pager-wrap .k-pager-numbers li:not(.k-current-page) .k-link:hover,
  .k-ff .k-pager-wrap .k-pager-numbers li:not(.k-current-page) .k-link:hover,
  .k-ie11 .k-pager-wrap .k-pager-numbers li:not(.k-current-page) .k-link:hover,
  .k-safari .k-pager-wrap .k-pager-numbers li:not(.k-current-page) .k-link:hover {
    background-color: #ebebeb;
  }
  .k-webkit .k-pager-numbers.k-state-expanded,
  .k-ff .k-pager-numbers.k-state-expanded,
  .k-ie11 .k-pager-numbers.k-state-expanded,
  .k-safari .k-pager-numbers.k-state-expanded {
    -webkit-box-sizing: border-box;
            box-sizing: border-box;
    padding: 2px 0 0;
    border-width: 1px 1px 0 1px;
    border-style: solid;
    border-color: #ebebeb;
    /*@secondary-border-color*/
    background-color: #fff;
    border-radius: 0px 0px 0 0;
    -webkit-box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.2);
            box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.2);
  }
  .k-webkit .k-pager-numbers.k-state-expanded .k-current-page,
  .k-ff .k-pager-numbers.k-state-expanded .k-current-page,
  .k-ie11 .k-pager-numbers.k-state-expanded .k-current-page,
  .k-safari .k-pager-numbers.k-state-expanded .k-current-page {
    margin: -2.2em -1px 0;
    padding: 0;
  }
  .k-webkit .k-pager-numbers.k-state-expanded .k-current-page .k-link,
  .k-ff .k-pager-numbers.k-state-expanded .k-current-page .k-link,
  .k-ie11 .k-pager-numbers.k-state-expanded .k-current-page .k-link,
  .k-safari .k-pager-numbers.k-state-expanded .k-current-page .k-link {
    border-radius: 0 0 0px 0px;
    background-color: #fff;
    border: 1px solid #ebebeb;
    border-top: 0;
    -webkit-box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.2);
            box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.2);
  }
  .k-webkit .k-pager-numbers.k-state-expanded li,
  .k-ff .k-pager-numbers.k-state-expanded li,
  .k-ie11 .k-pager-numbers.k-state-expanded li,
  .k-safari .k-pager-numbers.k-state-expanded li {
    display: inline-block;
  }
  .k-webkit .k-gantt-toolbar > ul.k-gantt-views,
  .k-ff .k-gantt-toolbar > ul.k-gantt-views,
  .k-ie11 .k-gantt-toolbar > ul.k-gantt-views,
  .k-safari .k-gantt-toolbar > ul.k-gantt-views {
    top: 0;
  }
}
@media only screen and (max-width: 755px) {
  .k-webkit .k-pager-info,
  .k-ff .k-pager-info,
  .k-ie11 .k-pager-info,
  .k-safari .k-pager-info {
    display: none;
  }
}
@media only screen and (max-width: 572px) {
  .k-webkit .k-pager-sizes,
  .k-ff .k-pager-sizes,
  .k-ie11 .k-pager-sizes,
  .k-safari .k-pager-sizes {
    display: none;
  }
}
/* Default Theme */
.k-chart .k-mask {
  background-color: #fff;
  filter: alpha(opacity=68);
  opacity: 0.68;
}
.k-chart .k-selection {
  border-color: #e5e5e5;
}
.k-chart .k-handle {
  width: 15px;
  height: 15px;
  background-color: #3f51b5;
  border-radius: 10px;
}
.k-chart .k-leftHandle {
  left: -8px;
}
.k-chart .k-rightHandle {
  right: -8px;
}
.k-chart .k-handle:hover {
  background-color: #00b0ff;
  border-color: #00b0ff;
}
.k-chart .k-navigator-hint .k-tooltip {
  border: 3px solid #ffffff;
  -webkit-box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.2);
          box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.2);
  background: #ffffff;
  color: #242424;
}
.k-chart .k-navigator-hint .k-scroll {
  background: #3f51b5;
  height: 4px;
}
.k-chart-tooltip {
  background-image: none;
}
/* Map */
.k-map .k-marker {
  background-image: url("Material/markers.png");
}
@media only screen and (-webkit-min-device-pixel-ratio: 1.2), only screen and (min-device-pixel-ratio: 1.2) {
  .k-map .k-marker {
    background-image: url("Material/markers_2x.png");
  }
}
.k-map .k-attribution {
  color: #666666;
}
.k-map .k-shadow {
  background-color: #f9f9f9;
  border-color: #f9f9f9;
}
.k-map .k-zoom-control {
  border-color: #fff;
  -webkit-box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.2);
          box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.2);
  border-radius: 2px;
}
.k-map .k-map-controls .k-button {
  -webkit-box-shadow: none;
          box-shadow: none;
}
.k-map .k-map-controls .k-button:focus,
.k-map .k-map-controls .k-button:active,
.k-map .k-map-controls .k-button:focus:active {
  background-color: #d6d6d6;
  border-color: #d6d6d6;
  -webkit-box-shadow: none;
          box-shadow: none;
}
.k-buttons-horizontal .k-zoom-out {
  border-radius: 0 2px 2px 0;
}
.k-buttons-horizontal :first-child {
  border-radius: 2px 0 0 2px;
}
.k-rtl .k-buttons-horizontal .k-zoom-out {
  border-radius: 2px 0 0 2px;
}
.k-rtl .k-buttons-horizontal :first-child {
  border-radius: 0 2px 2px 0;
}
.k-button-wrap .k-button {
  font-size: 21px;
  padding: 7px 13px;
}
/*!
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       

*/
.k-spreadsheet-row-header,
.k-spreadsheet-column-header {
  background-color: #fff;
}
.k-spreadsheet-top-corner,
.k-spreadsheet-row-header,
.k-spreadsheet-column-header {
  background-color: #fff;
  background-image: none;
  color: #000000;
  border-color: #cccccc;
}
.k-spreadsheet-top-corner {
  border-color: #cccccc;
}
.k-spreadsheet-top-corner:after {
  border-color: transparent #cccccc #cccccc transparent;
}
.k-spreadsheet-pane {
  border-color: #cccccc;
}
.k-spreadsheet-pane .k-spreadsheet-vaxis,
.k-spreadsheet-pane .k-spreadsheet-haxis {
  border-color: #e6e6e6;
}
.k-spreadsheet-pane .k-spreadsheet-column-header,
.k-spreadsheet-pane .k-spreadsheet-row-header {
  border-color: #cccccc;
}
.k-spreadsheet-pane .k-spreadsheet-merged-cell {
  background-color: #fff;
}
.k-spreadsheet-pane .k-selection-partial,
.k-spreadsheet-pane .k-selection-full {
  border-color: rgba(0, 176, 255, 0.2);
  background-color: rgba(0, 176, 255, 0.2);
}
.k-spreadsheet-pane .k-filter-range {
  border-color: #00b0ff;
}
.k-spreadsheet-pane .k-spreadsheet-column-header .k-selection-partial,
.k-spreadsheet-pane .k-spreadsheet-column-header .k-selection-full {
  border-bottom-color: #00b0ff;
}
.k-spreadsheet-pane .k-spreadsheet-row-header .k-selection-partial,
.k-spreadsheet-pane .k-spreadsheet-row-header .k-selection-full {
  border-right-color: #00b0ff;
}
.k-auto-fill,
.k-spreadsheet-selection {
  border-color: #00b0ff;
  -webkit-box-shadow: inset 0 0 0 1px #fff, 0 0 0 1px #00b0ff;
          box-shadow: inset 0 0 0 1px #fff, 0 0 0 1px #00b0ff;
}
.k-spreadsheet-selection {
  background-color: rgba(0, 176, 255, 0.2);
}
.k-spreadsheet-active-cell {
  border-color: #00b0ff !important;
  background-color: #fff;
}
.k-spreadsheet-active-cell.k-single {
  color: #444444;
  background-color: #fff;
}
.k-spreadsheet .k-spreadsheet-action-bar {
  background-color: #fff;
  border-color: #e6e6e6;
}
.k-spreadsheet .k-spreadsheet-action-bar .k-spreadsheet-name-editor {
  border-color: #cccccc;
}
.k-spreadsheet .k-spreadsheet-action-bar .k-spreadsheet-formula-bar::before {
  border-color: #cccccc;
}
.k-spreadsheet .k-spreadsheet-formula-input {
  background-color: #fff;
  color: #444444;
}
.k-spreadsheet .k-resize-handle,
.k-spreadsheet .k-resize-hint-handle,
.k-spreadsheet .k-resize-hint-marker {
  background-color: #00b0ff;
}
.k-spreadsheet .k-resize-hint-vertical .k-resize-hint-handle,
.k-spreadsheet .k-resize-hint-vertical .k-resize-hint-marker {
  background-color: #00b0ff;
}
.k-spreadsheet .k-single-selection::after {
  background-color: #00b0ff;
  border-color: #fff;
}
.k-spreadsheet .k-auto-fill-punch {
  background-color: rgba(255, 255, 255, 0.5);
}
.k-spreadsheet .k-single-selection.k-dim-auto-fill-handle::after {
  background-color: rgba(0, 176, 255, 0.5);
}
.k-spreadsheet-format-cells .k-spreadsheet-preview {
  border-color: #e6e6e6;
}
.k-spreadsheet-filter {
  border-radius: 0px;
  background-color: #fff;
  -webkit-box-shadow: inset 0 0 0 1px #e6e6e6;
          box-shadow: inset 0 0 0 1px #e6e6e6;
}
.k-spreadsheet-filter.k-state-active {
  color: #3f51b5;
  background-color: #00b0ff;
}
.k-spreadsheet-filter:hover {
  color: #444444;
  background: #ebebeb;
  border-color: #d7d7d7;
}
.k-action-window .k-action-buttons {
  border-color: #e6e6e6;
}
.k-spreadsheet-sample {
  color: #919191;
}
.k-state-selected .k-spreadsheet-sample {
  color: inherit;
}
.k-spreadsheet-window .k-list-wrapper,
.k-spreadsheet-window .k-list {
  border-color: #e6e6e6;
  border-radius: 0px;
}
.k-spreadsheet-window .export-config,
.k-spreadsheet-window .k-edit-field > .k-orientation-label {
  border-color: #e6e6e6;
}
.k-spreadsheet-window .k-edit-field > input[type="radio"]:checked + .k-orientation-label {
  background-image: none;
  background-color: #3f51b5;
  color: #6776ca;
}
.k-spreadsheet-window .k-page-orientation {
  border-color: #e6e6e6;
  -webkit-box-shadow: 0 5px 5px 0 rgba(0, 0, 0, 0.1);
          box-shadow: 0 5px 5px 0 rgba(0, 0, 0, 0.1);
}
.k-spreadsheet-window .k-page-orientation:before {
  background: #fff;
  border-color: transparent;
  border-bottom-color: #e6e6e6;
  border-left-color: #e6e6e6;
}
.k-spreadsheet-window .k-margins-horizontal,
.k-spreadsheet-window .k-margins-vertical {
  background: transparent;
  border-color: #e6e6e6;
}
.k-spreadsheet-window .hint-wrapper:before {
  background: #e6e6e6;
}
.k-spreadsheet-toolbar.k-toolbar .k-button-group .k-button {
  border-radius: 0px;
}
.k-spreadsheet-toolbar > .k-widget,
.k-spreadsheet-toolbar > .k-button,
.k-spreadsheet-toolbar > .k-button-group {
  border-radius: 0px;
}
.k-spreadsheet-toolbar > .k-separator {
  border-color: #e6e6e6;
}
.k-spreadsheet-toolbar .k-overflow-anchor {
  border-radius: 0;
}
.k-spreadsheet-popup {
  border-radius: 0px;
}
.k-spreadsheet-popup .k-separator {
  background-color: #e6e6e6;
}
.k-spreadsheet-popup .k-button {
  background-color: transparent;
}
.k-spreadsheet-popup .k-button:hover {
  background-color: #ebebeb;
}
.k-spreadsheet-popup .k-state-active {
  background-color: #00b0ff;
  color: #ffffff;
}
.k-spreadsheet-popup .k-state-active:hover {
  background-color: #008dcc;
}
.k-spreadsheet-filter-menu .k-details {
  border-color: #e6e6e6;
}
.k-spreadsheet-filter-menu .k-details-content .k-space-right {
  background-color: #fff;
}
.k-spreadsheet-filter-menu .k-spreadsheet-value-treeview-wrapper {
  background-color: #fff;
  border-color: #e6e6e6;
  border-radius: 0px 0 0 0px;
}
.k-syntax-ref {
  color: #ff8822;
}
.k-syntax-num {
  color: #0099ff;
}
.k-syntax-func {
  font-weight: bold;
}
.k-syntax-str {
  color: #38b714;
}
.k-syntax-error {
  color: red;
}
.k-syntax-bool {
  color: #a9169c;
}
.k-syntax-startexp {
  font-weight: bold;
}
.k-syntax-paren-match {
  background-color: #caf200;
}
.k-series-a {
  border-color: #3f51b5;
  background-color: rgba(63, 81, 181, 0.15);
}
.k-series-b {
  border-color: #03a9f4;
  background-color: rgba(3, 169, 244, 0.15);
}
.k-series-c {
  border-color: #4caf50;
  background-color: rgba(76, 175, 80, 0.15);
}
.k-series-d {
  border-color: #f9ce1d;
  background-color: rgba(249, 206, 29, 0.15);
}
.k-series-e {
  border-color: #ff9800;
  background-color: rgba(255, 152, 0, 0.15);
}
.k-series-f {
  border-color: #ff5722;
  background-color: rgba(255, 87, 34, 0.15);
}
.k-spreadsheet-sheets-remove:hover .k-icon {
  color: #cc2222;
}
.k-spreadsheet-formula-list .k-state-focused {
  background-color: #00b0ff;
  color: #3f51b5;
}
@media only screen and (-webkit-min-device-pixel-ratio: 2) {
  .k-icon.k-font-icon {
    background-image: none;
  }
}
.k-spreadsheet .k-spreadsheet-quick-access-toolbar .k-button,
.k-spreadsheet .k-spreadsheet-sheets-bar .k-button {
  -webkit-box-shadow: none;
          box-shadow: none;
  color: #ffffff;
  border-radius: 0;
  line-height: 2.6em;
  width: 3em;
}
.k-spreadsheet .k-spreadsheet-quick-access-toolbar .k-button:hover,
.k-spreadsheet .k-spreadsheet-sheets-bar .k-button:hover {
  background-color: #324191;
  border-color: #324191;
}
.k-spreadsheet .k-spreadsheet-sheets-bar .k-button {
  left: 0;
  bottom: 0;
  padding-top: .5em;
  padding-bottom: .5em;
  line-height: 2.2em;
}
.k-spreadsheet .k-spreadsheet-sheets-remove {
  margin: 0 0 0 -1em;
}
.k-spreadsheet-sheets-items .k-state-default .k-link,
.k-spreadsheet-tabstrip .k-state-default .k-link {
  color: #9fa8da;
}
.k-spreadsheet-sheets-items .k-item.k-state-hover,
.k-spreadsheet-tabstrip .k-item.k-state-hover,
.k-spreadsheet-sheets-items .k-item.k-state-active,
.k-spreadsheet-tabstrip .k-item.k-state-active,
.k-spreadsheet-sheets-items .k-item.k-state-focused,
.k-spreadsheet-tabstrip .k-item.k-state-focused {
  background-color: transparent;
}
.k-spreadsheet-sheets-items .k-item.k-state-hover .k-link,
.k-spreadsheet-tabstrip .k-item.k-state-hover .k-link,
.k-spreadsheet-sheets-items .k-item.k-state-active .k-link,
.k-spreadsheet-tabstrip .k-item.k-state-active .k-link,
.k-spreadsheet-sheets-items .k-item.k-state-focused .k-link,
.k-spreadsheet-tabstrip .k-item.k-state-focused .k-link {
  color: #ffffff;
}
.k-spreadsheet-sheets-items .k-state-active .k-link,
.k-spreadsheet-tabstrip .k-state-active .k-link {
  color: #ffffff;
}
.k-spreadsheet-toolbar > .k-button:not(.k-overflow-anchor) {
  line-height: 2em;
}
.k-autocomplete > .k-i-close,
.k-lov > .k-dropdown-wrap > .k-i-close,
.k-tledit > .k-dropdown-wrap > .k-i-close,
.k-combobox > .k-dropdown-wrap > .k-i-close,
.k-datepicker > .k-picker-wrap > .k-i-close,
.k-datetimepicker > .k-picker-wrap > .k-i-close,
.k-multiselect > .k-multiselect-wrap > .k-i-close {
  display: none;
  position: absolute;
  right: 2.9em !important;
}
.k-autocomplete > .k-i-close {
  margin-right: 8px;
}
.k-datetimepicker > .k-picker-wrap > .k-i-close {
  right: 4.5em !important;
}
