
e94650621071fcc3142075242677767d7e40f89d	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.89.1754018536329.js\",\"contentHash\":\"0ef9f5cc5bd12cb58b3e420776c312f0\"}","integrity":"sha512-tlWCT7pFKm7YdGSdEAC6o3C7s9tfQLQaDBN3RExoLQz9iOmAa5uvcdCu5C1qFJjpBLM9xK50Y92L20+F5IjTDQ==","time":1754018575956,"size":51045}