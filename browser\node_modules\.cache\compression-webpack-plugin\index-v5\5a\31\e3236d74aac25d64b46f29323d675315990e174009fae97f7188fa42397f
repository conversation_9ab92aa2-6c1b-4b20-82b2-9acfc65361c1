
730519c95601c834934d17f68a8b9c445f5dd26c	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.385.1754018536329.js\",\"contentHash\":\"69446285c3331cc0e550c8b7448b13d9\"}","integrity":"sha512-NuPjE3NouiQosD7iT/N+gsDsEClkOXv1YvsGW0FskHlhFK0yinrGOvrUa5X7qgES86S4Kw/2iiMh6AlSg1nIeg==","time":1754018575976,"size":74978}