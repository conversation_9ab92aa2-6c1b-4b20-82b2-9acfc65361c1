<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.klaw.dao.systemDao.FlowExampleCarbonCopyMapper">
  <resultMap id="BaseResultMap" type="com.klaw.entity.systemBean.FlowExampleCarbonCopy">
    <id column="SID" jdbcType="DECIMAL" property="sid" />
    <result column="FLOW_EXAMPLE_ID" jdbcType="VARCHAR" property="flowExampleId" />
    <result column="FLOW_EXAMPLE_TASK_ID" jdbcType="VARCHAR" property="flowExampleTaskId" />
    <result column="FLOW_EXAMPLE_NODE_ID" jdbcType="VARCHAR" property="flowExampleNodeId" />
    <result column="CARBON_COPY_USER_ID" jdbcType="DECIMAL" property="carbonCopyUserId" />
    <result column="CARBON_COPY_TYPE" jdbcType="DECIMAL" property="carbonCopyType" />
    <result column="CREATED_BY" jdbcType="DECIMAL" property="createdBy" />
    <result column="CREATION_DATE" jdbcType="TIMESTAMP" property="creationDate" />
    <result column="LAST_UPDATED_BY" jdbcType="DECIMAL" property="lastUpdatedBy" />
    <result column="LAST_UPDATE_DATE" jdbcType="TIMESTAMP" property="lastUpdateDate" />
    <result column="OBJECT_VERSION_NUMBER" jdbcType="DECIMAL" property="objectVersionNumber" />
    <result column="ID_" jdbcType="VARCHAR" property="id_" />
  </resultMap>
  <sql id="Base_Column_List">
    T1.SID, T1.FLOW_EXAMPLE_ID, T1.FLOW_EXAMPLE_TASK_ID, T1.FLOW_EXAMPLE_NODE_ID, T1.CARBON_COPY_USER_ID,
    T1.CARBON_COPY_TYPE, T1.CREATED_BY, T1.CREATION_DATE, T1.LAST_UPDATED_BY, T1.LAST_UPDATE_DATE, T1.OBJECT_VERSION_NUMBER,
    (select T2.ID_ from ACT_HI_TASKINST T2 where T1.FLOW_EXAMPLE_ID = T2.PROC_INST_ID_ AND T1.FLOW_EXAMPLE_NODE_ID = T2.TASK_DEF_KEY_ order by start_time_ desc limit 1) as ID_
  </sql>

  <select id="queryFlowExampleCarbonCopyById" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/>
    from FLOW_EXAMPLE_CARBON_COPY T1
    WHERE
    T1.FLOW_EXAMPLE_ID = #{processInstanceId}
    and T1.CARBON_COPY_TYPE = 0
    <if test="sid != null">
      and SID = #{sid}
    </if>
  </select>
</mapper>