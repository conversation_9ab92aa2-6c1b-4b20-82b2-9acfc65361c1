<template>
	<div>
		<FormWindow ref="formWindow" @initData="initData" @loadData="loadData">
			<el-container v-loading="loading" style="height: calc(100vh - 84px)" :element-loading-text="loadingText">
				<el-main>
					<el-scrollbar style="height: 100%" wrap-style="overflow-x: hidden;">
						<!--数据表单块-->
						<el-form ref="dataForm" :model="mainData" :rules="!isView && isNew ? rules : {}"
              label-width="100px"
							:style="mainData.dataStateCode === utils.dataState_BPM.FINISH.code ? 'margin-right: 50px;' : ' margin-right: 10px;'"
							:validate-on-rule-change="false">
							<el-row
								style="padding: 10px 0 10px 0; position: fixed; width: 100%; overflow-y: auto; background-color: white; z-index: 999">
								<!-- <el-button v-if="!isView" type="success" size="mini" @click="approval_">生成审批单</el-button> -->
								<el-button v-if="!isView" type="primary" size="mini" @click="save_">
									保存
								</el-button>
								<el-button v-if="!isView" type="success" size="mini" @click="approval_">提交</el-button>
							</el-row>
							<div style="padding-top: 50px"></div>
							<span style="text-align: left; font-size: 23px; margin-left: 43%; font-weight: 900">{{
								this.title }}</span>
							<!--基础信息块-->
							<div v-if="dataState !== 'view'">
								<div
									style="padding-left: 10px; margin-bottom: 20px; font-size: 15px; color: red; font-weight: bolder">
								</div>
								<div style="margin: 10px">
									<span style="font-weight: bold; font-size: 16px; color: #5a5a5f">基本信息</span>
								</div>
								<el-divider />
								<el-row style="margin-top: 10px">
									<el-col :span="16">
										<el-form-item label="检查名称" prop="chackInfo.itemName">
											<el-input :disabled="!isNew" v-if="!isView"
												v-model="mainData.chackInfo.itemName" maxlength="100" show-word-limit
												placeholder="请输入..." clearable />
										</el-form-item>
									</el-col>
									<el-col :span="8">
										<el-form-item label="事项编码" prop="itemCode">
											<el-input disabled placeholder="后台自动生成" v-if="!isView"
												v-model="mainData.chackInfo.itemCode" maxlength="100" show-word-limit
												clearable />
										</el-form-item>
									</el-col>
								</el-row>
								<el-row>
									<el-col :span="8">
										<el-form-item label="计划开始检查时间" class="custom-word-break" prop="chackInfo.plannedStartTime">
                      <span slot="label">计划开始<br/>检查时间</span>
                      <el-date-picker :disabled="!isNew"
												v-model="mainData.chackInfo.plannedStartTime" value-format="yyyy-MM-dd"
												type="date" style="width: 100%" />
										</el-form-item>
									</el-col>
									<el-col :span="8">
										<el-form-item label="计划结束检查时间" class="custom-word-break" prop="chackInfo.plannedEndTime">
                      <span slot="label">计划结束<br/>检查时间</span>
                      <el-date-picker :disabled="!isNew"
												v-model="mainData.chackInfo.plannedEndTime" value-format="yyyy-MM-dd"
												type="date" style="width: 100%" />
										</el-form-item>
									</el-col>
									<el-col :span="8">
										<el-form-item label="被检查单位" prop="chackInfo.inspectedUnitName">
											<el-input v-model="mainData.chackInfo.inspectedUnitName" clearable
												placeholder="请选择..." maxlength="50" show-word-limit disabled>
												<el-button v-if="isNew" slot="append" icon="el-icon-search"
													@click="choiceOrg()" />
											</el-input>
										</el-form-item>
									</el-col>
								</el-row>
								<el-row>
									<el-col :span="16">
										<el-form-item label="参与部门" prop="chackInfo.involvedDepartmentList">
											<el-input v-if="dataState !== 'view'"
												v-model="mainData.chackInfo.involvedDepartmentList" clearable
												placeholder="请选择" class="input-with-select" disabled>
												<el-button v-if="isNew" slot="append" icon="el-icon-search"
													@click="choiceOrg1()" />
											</el-input>
										</el-form-item>
									</el-col>
								</el-row>
								<el-row>
									<el-col :span="24">
										<el-form-item label="检查通知" prop="chackInfo.inspectionNotice">
											<span slot="label">检查通知</span>
											<UploadDoc v-if="!isView" :disabled="!isNew"
												:files.sync="mainData.chackInfo.inspectionNotice" doc-path="/case" />
											<!-- <el-input v-if="!isView" :disabled="!isNew"
												v-model="mainData.chackInfo.inspectionNotice"
												:autosize="{ minRows: 3, maxRows: 20 }" type="textarea"
												placeholder="请输入检查要点" maxlength="500" show-word-limit />
											<text-span v-else class="viewSpan"
												:text="mainData.chackInfo.inspectionNotice" /> -->
										</el-form-item>
									</el-col>
								</el-row>
								<el-row>
									<el-col :span="24">
										<el-form-item label="相关附件">
											<UploadDoc :files.sync="mainData.chackInfo.relatedAttachmentsCreater"
												doc-path="/case" :disabled="!isNew" />
										</el-form-item>
									</el-col>
								</el-row>
								<!-- 第二部分 -->
								<div
									style="padding-left: 10px; margin-bottom: 20px; font-size: 15px; color: red; font-weight: bolder">
								</div>
								<div style="margin: 10px">
									<span style="font-weight: bold; font-size: 16px; color: #5a5a5f">检查结果</span>
								</div>
								<el-divider />
								<el-row>
									<el-col :span="24" style="margin-top: 10px;">
										<el-form-item label="检查结果" prop="reviewResult.inspectionResult">
											<span slot="label">检查结果</span>
											<el-input v-if="!isView" :disabled="!isNew"
												v-model="mainData.reviewResult.inspectionResult"
												:autosize="{ minRows: 3, maxRows: 20 }" type="textarea"
												placeholder="请输入检查要点" maxlength="500" show-word-limit />
											<text-span v-else class="viewSpan"
												:text="mainData.reviewResult.inspectionResult" />
										</el-form-item>
									</el-col>
								</el-row>
								<el-row>
									<el-col :span="8">
										<el-form-item label="是否整改" prop="reviewResult.needsRectification">
											<el-radio-group :disabled="!isNew"
												v-model="mainData.reviewResult.needsRectification">
												<el-radio :label="true">是</el-radio>
												<!-- <el-radio :label="false">否</el-radio> -->
											</el-radio-group>
										</el-form-item>
									</el-col>
									<el-col :span="8">
										<el-form-item label="整改完成时间" prop="reviewResult.latestRectificationTime">
											<el-date-picker :disabled="!isNew"
												v-model="mainData.reviewResult.latestRectificationTime" type="date" style="width: 100%" />
										</el-form-item>
									</el-col>
								</el-row>
								<el-row>
									<el-col :span="24">
										<el-form-item label="相关附件">
											<UploadDoc :files.sync="mainData.reviewResult.relatedAttachmentsResult"
												doc-path="/case" :disabled="!isNew" />
										</el-form-item>
									</el-col>
								</el-row>
								<!-- 第三部分 -->
<!--								<div-->
<!--									style="padding-left: 10px; margin-bottom: 20px; font-size: 15px; color: red; font-weight: bolder">-->
<!--								</div>-->
<!--								<div style="margin: 10px">-->
<!--									<span style="font-weight: bold; font-size: 16px; color: #5a5a5f">整改计划</span>-->
<!--								</div>-->
<!--								<el-divider />-->
<!--								<div style="display: inline;float: right;margin-top: 0px;margin-right: 30px">-->
<!--									<el-link style="margin-right: 12px;" type="primary" size="mini"-->
<!--										@click="addTableData">新增</el-link>-->
<!--									<el-link style="margin-right: 10px;" type="danger" size="mini" @click="delRow"-->
<!--										v-show="currentRow != null">删除</el-link>-->
<!--								</div>-->

                <!-- 第四部分 -->
                <simple-board :data-state="dataState" :has-value="true" :hasAdd="true" :hasDle="currentRow != null" :title="'整改计划'"
                              style="margin-top: 10px"
                              @addBtn="addTableData" @delBtn="delRow">
								<el-table ref="table" :data="mainData.rectificationPlan.plans" size="mini" border
                  fit highlight-current-row stripe style="width: 100%"  :show-overflow-tooltip="true" row-key="id"
									@current-change="handleCurrentChange">
									<el-table-column type="index" width="50" label="序号" align="center" />

									<el-table-column label="整改事项">
										<template slot-scope="scope">
											<el-form :model="mainData.rectificationPlan.plans[scope.$index]"
												:rules="tableRules"
												:ref="(el) => setRectificationMatters(el, scope.$index)">
												<el-form-item class="test" prop="rectificationMatters"
													style="padding:5px;"
													:style="{ marginBottom: marginBottomMatters[scope.$index] }">
													<el-input
														v-model="mainData.rectificationPlan.plans[scope.$index].rectificationMatters"
														placeholder="请输入"></el-input>
												</el-form-item>
											</el-form>
										</template>
									</el-table-column>
									<el-table-column label="预计完成时间">
										<template slot-scope="scope">
											<el-form :model="mainData.rectificationPlan.plans[scope.$index]"
												:rules="tableRules"
												:ref="(el) => setEstimatedRectificationTime(el, scope.$index)">
												<el-form-item class="test" prop="estimatedRectificationTime"
													style="padding:5px;"
													:style="{ marginBottom: marginBottomEstimated[scope.$index] }">
													<el-date-picker
														v-model="mainData.rectificationPlan.plans[scope.$index].estimatedRectificationTime"
														type="date" style="width: 100%" />
												</el-form-item>
											</el-form>
										</template>
									</el-table-column>
									<el-table-column label="整改措施">
										<template slot-scope="scope">
											<el-form :model="mainData.rectificationPlan.plans[scope.$index]"
												:rules="tableRules"
												:ref="(el) => setRectificationMeasures(el, scope.$index)">
												<el-form-item class="test" prop="rectificationMeasures"
													style="padding:5px;"
													:style="{ marginBottom: marginBottomMeasures[scope.$index] }">
													<el-input
														v-model="mainData.rectificationPlan.plans[scope.$index].rectificationMeasures"
														placeholder="请输入"></el-input>
												</el-form-item>
											</el-form>
										</template>
									</el-table-column>
									<el-table-column label="责任人">
										<template slot-scope="scope">
											<el-form :model="mainData.rectificationPlan.plans[scope.$index]"
												:rules="tableRules"
												:ref="(el) => setResponsiblePerson(el, scope.$index)">
												<el-form-item class="test" prop="responsiblePerson" style="padding:5px;"
													:style="{ marginBottom: marginBottomPerson[scope.$index] }">
													<el-input
														v-model="mainData.rectificationPlan.plans[scope.$index].responsiblePerson"
														clearable placeholder="请选择..." maxlength="50" show-word-limit
														disabled>
														<el-button slot="append" icon="el-icon-search"
															@click="showPerson(scope.$index)" />
													</el-input>
												</el-form-item>
											</el-form>
										</template>
									</el-table-column>
									<!-- <el-table-column fixed="right" label="操作">
										<template slot-scope="scope">
											<el-button size="mini" type="text" icon="el-icon-delete"
												@click="delete_(scope.$index, scope.row)">删除</el-button>
										</template>
									</el-table-column> -->
								</el-table>
                </simple-board>
							</div>

							<el-dialog :close-on-click-modal="false" title="选择责任人" :visible.sync="personVisible"
								width="50%">
								<div class="el-dialog-div">
									<orgTree :accordion="false" :is-checked-user="true" :show-user="true"
										:is-check="false" :checked-data.sync="personData" :is-not-cascade="true"
										:is-filter="true" />
								</div>
								<span slot="footer" class="dialog-footer">
									<el-button icon="" class="negative-btn" @click="cancelPerson">取消</el-button>
									<el-button type="primary" icon="" class="active-btn"
										@click="surePerson">确定</el-button>
								</span>
							</el-dialog>

							<el-dialog :close-on-click-modal="false" title="选择部门" :visible.sync="orgVisible1"
								width="50%">
								<div class="el-dialog-div">
									<orgTree :accordion="false" :is-checked-user="false" :show-user="false"
										:is-check="true" :checked-data.sync="zxcheckedData" :is-not-cascade="true"
										:is-filter="true" />
								</div>
								<span slot="footer" class="dialog-footer">
									<el-button icon="" class="negative-btn" @click="orgCancel1">取消</el-button>
									<el-button type="primary" icon="" class="active-btn"
										@click="orgSure1">确定</el-button>
								</span>
							</el-dialog>

							<el-dialog :close-on-click-modal="false" title="选择组织" :visible.sync="orgVisible"
								width="50%">
								<div class="el-dialog-div">
									<orgTree :accordion="false" :is-checked-user="false" :show-user="false"
										:is-check="false" :checked-data.sync="checkedData" :is-not-cascade="true"
										:is-filter="true" />
								</div>
								<span slot="footer" class="dialog-footer">
									<el-button icon="" class="negative-btn" @click="orgCancel">取消</el-button>
									<el-button type="primary" icon="" class="active-btn" @click="orgSure">确定</el-button>
								</span>
							</el-dialog>

							<!-- 查看时的判断 -->
							<div v-if="dataState == 'view'">
								<SimpleBoardTitle title="基本信息" style="margin-top: 5px">
									<table class="table_content" style="margin-top: 10px">
										<tbody>
											<tr>
												<th colspan="3" class="th_label">检查名称</th>
												<td colspan="13" class="td_value">{{ mainData.chackInfo.itemName }}</td>
												<th colspan="3" class="th_label">事项编码</th>
												<td colspan="5" class="td_value">{{ mainData.chackInfo.itemCode }}</td>
											</tr>
											<tr>
												<th colspan="3" class="th_label">计划开始检查时间</th>
												<td colspan="5" class="td_value">{{
													mainData.chackInfo.plannedStartTime }}</td>
												<th colspan="3" class="th_label">计划结束检查时间</th>
												<td colspan="5" class="td_value">{{ mainData.chackInfo.plannedEndTime }}
												</td>
												<th colspan="3" class="th_label">被检查单位</th>
												<td colspan="5" class="td_value">{{
													mainData.chackInfo.inspectedUnitName }}</td>
											</tr>
											<tr>
												<th colspan="3" class="th_label">参与部门</th>
												<td colspan="21" class="td_value">{{
													mainData.chackInfo.involvedDepartmentList }}</td>
											</tr>
											<tr>
												<th colspan="3" class="th_label">检查通知</th>
												<td colspan="21" class="td_value">
													<UploadDoc :files.sync="mainData.chackInfo.inspectionNotice"
														doc-path="/case" :disabled="isView" />
												</td>
											</tr>
											<tr>
												<th colspan="3" class="th_label">相关附件</th>
												<td colspan="21" class="td_value">
													<UploadDoc
														:files.sync="mainData.chackInfo.relatedAttachmentsCreater"
														doc-path="/case" :disabled="isView" />
												</td>
											</tr>
											<tr>
												<th colspan="3" class="th_label">检查结果</th>
												<td colspan="5" class="td_value">{{
													mainData.reviewResult.inspectionResult }}</td>
												<th colspan="3" class="th_label">是否整改</th>
												<td colspan="5" class="td_value">{{mainData.reviewResult.needsRectification != null ?mainData.reviewResult.needsRectification == true ? '是' : '否' : '' }}</td>
												<th colspan="3" class="th_label">整改完成时间</th>
												<td colspan="5" class="td_value">{{
													mainData.reviewResult.latestRectificationTime }}</td>
											</tr>
											<tr>
												<th colspan="3" class="th_label">相关附件</th>
												<td colspan="21" class="td_value">
													<UploadDoc
														:files.sync="mainData.reviewResult.relatedAttachmentsResult"
														doc-path="/case" :disabled="isView" />
												</td>
											</tr>
										</tbody>
									</table>
									<SimpleBoard style="margin-top: 5px" title="整改计划" :hasAdd="false">
										<el-table ref="table" :data="mainData.rectificationPlan.plans" size="mini"
											border stripe fit :show-overflow-tooltip="true" row-key="id"
											style="table-layout: fixed; width: 100%;">
											<el-table-column type="index" width="50" label="序号" align="center" />
											<el-table-column label="整改事项">
												<template slot-scope="{ row, $index }">
													<el-input disabled
														v-model="mainData.rectificationPlan.plans[$index].rectificationMatters"
														placeholder="请输入"></el-input>
												</template>
											</el-table-column>
											<el-table-column label="预计完成时间">
												<template slot-scope="scope">
													<el-date-picker disabled
														v-model="mainData.rectificationPlan.plans[scope.$index].estimatedRectificationTime"
														type="date" style="width: 100%" />
												</template>
											</el-table-column>
											<el-table-column label="整改措施">
												<template slot-scope="{ row, $index }">
													<el-input disabled
														v-model="mainData.rectificationPlan.plans[$index].rectificationMeasures"
														placeholder="请输入"></el-input>
												</template>
											</el-table-column>
											<el-table-column label="责任人">
												<template slot-scope="{ row, $index }">
													<!-- <el-input v-model="mainData.plans[$index].responsiblePersonCode" placeholder="请输入"></el-input> -->
													<el-input
														v-model="mainData.rectificationPlan.plans[$index].responsiblePerson"
														clearable placeholder="请选择..." maxlength="50" show-word-limit
														disabled>
													</el-input>
												</template>
											</el-table-column>
										</el-table>
									</SimpleBoard>
								</SimpleBoardTitle>

							</div>

							<!-- 选择重大事项 -->
							<SectionDataDialog :dialog-visible.sync="sectionVisible" @sectionSure="sectionSure" />
							<!--公共信息-->
							<OtherInfo :data.sync="mainData" :main-id="mainData.id" :data-state="dataState"
								style="width: 100%; margin-top: 50px" />
						</el-form>

						<!--案件审批表-->
						<prosecution-dialog :visible.sync="prosecutionDialog" :is-multiple="false"
							@onSure="prosecutionSelect" />
					</el-scrollbar>
				</el-main>
				<!-- 选择模版 -->
				<!-- <Shortcut :templateShow="templateShow" @templateClick="templateClick" /> -->
			</el-container>
		</FormWindow>
	</div>
</template>

<script>
// vuex缓存数据
import { mapGetters } from 'vuex';

// 接口api
// import complianceReviewApi from '@/api/ComplianceReview/complianceReview.js';
import HgjcApi from '@/api/Hgjc/Hgjc.js';
import taskApi from '@/api/_system/task';
import commonApi from '@/api/_system/common';
import SimpleBoardTitle from '@/view/components/SimpleBoard/SimpleBoardTitle'
import SimpleBoard from "../../../../components/SimpleBoard/SimpleBoardViewCase";
// 组件
import FormWindow from '@/view/components/FormWindow/FormWindow';
import OtherInfo from '@/view/litigation/caseManage/case/caseChild/OtherInfo';
import OrgSingleDialogSelect from '@/view/components/OrgSingleDialogSelect/index';
import ProsecutionDialog from './ProsecutionDialog';
import QsBaseInfo from './qisubaseInfo';
import CaseData from '@/view/litigation/caseManage/case/caseChild/CaseData';
import CaseEvidenceData from '@/view/litigation/caseManage/case/caseChild/CaseEvidenceData';
import Shortcut from '@/view/litigation/caseManage/caseExamine/Shortcut';
import SimpleBoardTitleApproval from '@/view/components/SimpleBoard/SimpleBoardTitleApproval';
import UploadDoc from '@/view/components/UploadDoc/UploadDoc.vue';
import SectionDataDialog from './dialog/SelectionDialog.vue';
import orgTree from '@/view/components/OrgTree/OrgTree';

export default {
	name: 'RectificationPlanDetail',
	inject: ['layout', 'mcpLayout'],
	components: {
		SimpleBoard,
		SimpleBoardTitle,
		SimpleBoardTitleApproval,
		CaseData,
		QsBaseInfo,
		ProsecutionDialog,
		OrgSingleDialogSelect,
		FormWindow,
		OtherInfo,
		CaseEvidenceData,
		Shortcut,
		UploadDoc,
		SectionDataDialog,
		orgTree,
	},
	computed: {
		...mapGetters(['orgContext']),
		isView: function () {
			return this.dataState === this.utils.formState.VIEW;
		},
		isNew: function () {
			return this.dataState === this.utils.formState.NEW || this.$route.query.isEdit;
		},
		templateShow() {
			return this.mainData.dataStateCode === this.utils.dataState_BPM.SAVE.code && this.dataState !== this.utils.formState.VIEW;
		},
	},
	data() {
		// 时间的校验
		const validateStartTime = (rule, value, callback) => {
			if (value === undefined) {
				callback(new Error('开始时间不能为空'))
			} else {
				if (new Date(this.mainData.chackInfo.plannedStartTime).getTime() <= new Date().getTime()) {
					callback()
				} else {
					callback()
				}
			}
		};
		const validateEndTime = (rule, value, callback) => {
			if (value === undefined) {
				callback(new Error('结束时间不能为空'))
			} else {
				if (
					new Date(this.mainData.chackInfo.plannedStartTime).getTime() >=
					new Date(this.mainData.chackInfo.plannedEndTime).getTime()
				) {
					callback(new Error('截止时间必须大于开始时间！'))
				} else {
					callback()
				}
			}
		};
		return {
			currentIndex: null,
			currentRow: null,
			marginBottomMatters: [],
			marginBottomEstimated: [],
			marginBottomMeasures: [],
			marginBottomPerson: [],
			formMatter: {},
			formEstimated: {},
			formMeasures: {},
			formPerson: {},
			sectionVisible: false,
			selectedIndex: null,
			orgVisible1: false,
			orgVisible: false,
			personData: [],
			personVisible: false,
			checkedData: [],
			zxcheckedData: [],
			businessAreaData: [],
			radio: true,
			radio1: true,
			title: '制定整改计划',
			type: null,
			tabId: null,
			oarecordsDialog: false,
			loading: false,
			dataState: null,
			functionId: null, //终止的时候要用，需要手动关闭
			dataId: null,
			taskId: null,

			view: 'old',
			mainData: {
				chackInfo: {
					checkType: "国资委要求",
					itemName: null,
					itemCode: null,
					plannedStartTime: new Date().toISOString().substring(0, 10),
					plannedEndTime: null,
					inspectedUnitName: null,
					inspectedUnitCode: null,
					involvedDepartmentList: null,
					involvedDepartmentListCode: null,
					inspectionNotice: null,
					relatedAttachmentsCreater: null,
					chackInfoIsView: true,
				},
				isSubmit: false,
				currentProcess: null,
				currentHandler: null,
				currentProcessType: null,
				currentProcessId: null,
				currentCheckNode: null,
				currentTaskType: "制定整改计划",
				reviewResult: {
					reviewResultIsView: true,
					inspectionResult: null,
					needsRectification: true,
					latestRectificationTime: null,
					relatedAttachmentsResult: null,
				},
				rectificationPlan: {
					rectificationPlanIsView: true,
					plans: [],
				},
				rectificationResult: {
					rectificationResultPlanIsView: true,
					rectificationSituation: null,
					rectificationStatus: null,
					rectificationReport: null,
				},
				id: null, //主键
				createOgnId: null, //当前机构ID
				createOgnName: null, //当前机构名称
				createDeptId: null, //当前部门ID
				createDeptName: null, //当前部门名称
				createGroupId: null, //当前部门ID
				createGroupName: null, //当前部门名称
				createPsnId: null, //当前人ID
				createPsnName: null, //当前人名称
				createOrgId: null, //当前组织ID
				createOrgName: null, //当前组织名称
				createPsnFullId: null, //当前人全路径ID
				createPsnFullName: null, //当前人全路径名称
				createPsnPhone: null, //经办人电话
				createTime: null, //创建时间
				// auditStatus: this.utils.dataState_BPM.SAVE.name, //状态
				dataState: this.utils.dataState_BPM.SAVE.name, //数据状态
				dataStateCode: this.utils.dataState_BPM.SAVE.code, //数据状态编码
			},
			orgTreeDialog: false,
			orgDialogTitle: '组织信息',
			isAssign: false,
			prosecutionDialog: false,
			rules: {
				'chackInfo.itemName': [{ required: true, message: '请输入检查名称' }],
				'chackInfo.plannedStartTime': [{ required: true, validator: validateStartTime, message: '请选择计划开始检查时间' }],
				'chackInfo.inspectedUnitName': [{ required: true, message: '请选择被检查单位' }],
				'chackInfo.inspectionNotice': [{ required: true, message: '请选择检查通知' }],
				'chackInfo.involvedDepartmentList': [{ required: true, message: '请选择参与部门' }],
				'chackInfo.plannedEndTime': [{ required: true, validator: validateEndTime, message: '请选择检查结束时间需大于开始时间' }],
				'reviewResult.needsRectification': [{ required: true, message: '请选择是否需要整改' }],
				'reviewResult.latestRectificationTime': [{ required: true, message: '请选择最完整改时间' }],
				'reviewResult.inspectionResult': [{ required: true, message: '请输入整改结果反馈' }],
			},
			tableRules: {
				rectificationMatters: [{ required: true, message: '请输入整改事项', trigger: 'change' }],
				estimatedRectificationTime: [{ required: true, message: '请选择预计完成时间', trigger: 'change' }],
				rectificationMeasures: [{ required: true, message: '请输入整改措施', trigger: 'change' }],
				responsiblePerson: [{ required: true, message: '请选择责任人', trigger: 'change' }],
			},
			activity: null, //记录当前待办处于流程实例的哪个环节
			obj: {
				// 流程处理逻辑需要的各种参数
				taskId: null,
				processInstanceId: null,
				businessKey: null,
				title: null,
				functionName: null,
				sid: null,
			},
			noticeParams: {},
			noticeData: {
				moduleName: '', // 模块名称
				dataId: '', // 数据ID
				url: '', // 地址
				title: '', // 地址
				params: {}, // 其他参数
			},
			loadingText: '加载中...',
			mark: 1
		};
	},
	provide() {
		return {
			parentCase: this,
		};
	},
	mounted() {
		this.marginBottomMatters = new Array(this.mainData.rectificationPlan.plans.length).fill('0px');
		this.marginBottomEstimated = new Array(this.mainData.rectificationPlan.plans.length).fill('0px');
		this.marginBottomMeasures = new Array(this.mainData.rectificationPlan.plans.length).fill('0px');
		this.marginBottomPerson = new Array(this.mainData.rectificationPlan.plans.length).fill('0px');
	},
	methods: {
		handleCurrentChange(currentRow) {
			this.currentIndex = this.mainData.rectificationPlan.plans.indexOf(currentRow);
			this.currentRow = currentRow;
		},
		delRow() {
			const index = this.currentIndex;
			this.mainData.rectificationPlan.plans.splice(index, 1);
			this.currentRow = null;
		},
		updateMargin() {
			this.validateData();
		},
		validateData() {
			let mValid = true;
			let contValid = true
			let sValid = true;
			let isValid = true;
			this.mainData.rectificationPlan.plans.forEach((row, index) => {

				const EstimatedRectificationTime = this.formEstimated[`f${index}`];
				const RectificationMeasures = this.formMeasures[`f${index}`];
				const RectificationMatters = this.formMatter[`f${index}`];
				const ResponsiblePerson = this.formPerson[`f${index}`];
				if (ResponsiblePerson) {
					ResponsiblePerson.validate((valid) => {
						if (!valid) {
							this.$nextTick(() => {
								this.$set(this.marginBottomPerson, index, '20px');
							});
							mValid = false;
						} else {
							this.$set(this.marginBottomPerson, index, '0px');
						}
					});
				} else {
					console.error(`表单 ${index} 的 ref 未找到`);
					mValid = false;
				}
				if (RectificationMatters) {
					RectificationMatters.validate((valid) => {
						if (!valid) {
							this.$set(this.marginBottomMatters, index, '20px');
							isValid = false;
						} else {
							this.$set(this.marginBottomMatters, index, '0px');
						}
					});
				} else {
					console.error(`表单 ${index} 的 ref 未找到`);
					isValid = false;
				}
				if (RectificationMeasures) {
					RectificationMeasures.validate((valid) => {
						if (!valid) {
							this.$set(this.marginBottomMeasures, index, '20px');
							sValid = false;
						} else {
							this.$set(this.marginBottomMeasures, index, '0px');
						}
					});
				} else {
					console.error(`表单 ${index} 的 ref 未找到`);
					sValid = false;
				}
				if (EstimatedRectificationTime) {
					EstimatedRectificationTime.validate((valid) => {
						if (!valid) {
							this.$set(this.marginBottomEstimated, index, '20px');
							contValid = false;
						} else {
							this.$set(this.marginBottomEstimated, index, '0px');
						}
					});
				} else {
					console.error(`表单 ${index} 的 ref 未找到`);
					contValid = false;
				}

			});
			return isValid && contValid && sValid && mValid
		},
		setRectificationMatters(el, index) {
			if (el) {
				if (this.formMatter[`f${index}`]) {
					return;
				}
				this.$set(this.formMatter, `f${index}`, el);
			}
		},
		setEstimatedRectificationTime(el, index) {
			if (el) {
				if (this.formEstimated[`f${index}`]) {
					return;
				}
				this.$set(this.formEstimated, `f${index}`, el);
			}
		},
		setRectificationMeasures(el, index) {
			if (el) {
				if (this.formMeasures[`f${index}`]) {
					return;
				}
				this.$set(this.formMeasures, `f${index}`, el);
			}
		},
		setResponsiblePerson(el, index) {
			if (el) {
				if (this.formPerson[`f${index}`]) {
					return;
				}
				this.$set(this.formPerson, `f${index}`, el);
			}
		},
		initData(temp, dataState) {
			this.dataState = dataState;
			Object.assign(this.mainData, temp);
			if(!this.isView){
				const year = new Date().getFullYear();
				this.utils.createKvsequence('ZGJH' + year, 6).then((value) => {
					this.mainData.chackInfo.itemCode = value.data.kvsequence;
				});
			}
		},
		loadData(dataState, dataId) {
			this.functionId = this.$route.query.functionId;
			if (this.$route.query.view !== undefined && this.$route.query.view !== '') this.view = this.$route.query.view;
			if (this.$route.query.type !== undefined && this.$route.query.type !== '') this.type = this.$route.query.type;
			this.dataState = dataState;
			this.title = this.$route.query.title
			HgjcApi.queryById(dataId).then((response) => {
				this.mainData = response.data.data;
				this.loading = false;
			});
		},
		// },
		sectionSure(val) {
			this.mainData.relatedSignificantReview = val.reviewNumber;
			this.sectionVisible = false;
		},
		save() {
			return new Promise((resolve, reject) => {
				HgjcApi
					.save(this.mainData)
					.then((response) => {
						resolve(response);
					})
					.catch((error) => {
						reject(error);
					});
			});
		},
		showPerson(index) {
			this.personVisible = true;
			this.selectedIndex = index;
		},
		cancelPerson() {
			this.personVisible = false;
		},
		surePerson() {
			let c = '';
			let cid = '';
			this.personData.forEach((item) => {
				if (c.length === 0) {
					c = c + item.name;
					cid = cid + item.unitId;
				} else {
					c = c + ',' + item.name;
					cid = cid + ',' + item.unitId;
				}
			});
			this.mainData.rectificationPlan.plans[this.selectedIndex].responsiblePerson = c;
			this.mainData.rectificationPlan.plans[this.selectedIndex].responsiblePersonCode = cid;
			this.personVisible = false;
		},
		delete_(index, row) {
			console.log(index, row);
			this.$message({
				type: 'success',
				message: '删除成功!',
			});
			this.mainData.rectificationPlan.plans.splice(index, 1);
		},
		choiceOrg() {
			this.orgVisible = true;
		},
		choiceOrg1() {
			this.orgVisible1 = true;
		},
		orgCancel1() {
			this.orgVisible1 = false;
		},
		orgCancel() {
			this.orgVisible = false;
		},
		orgSure1() {
			let c = '';
			let cid = '';
			this.zxcheckedData.forEach((item) => {
				if (c.length === 0) {
					c = c + item.name;
					cid = cid + item.unitId;
				} else {
					c = c + ',' + item.name;
					cid = cid + ',' + item.unitId;
				}
			});
			this.mainData.chackInfo.involvedDepartmentList = c;
			this.mainData.chackInfo.involvedDepartmentListCode = cid;
			this.orgVisible1 = false;
		},
		orgSure() {
			const res = this.checkedData[0];
			console.log(res, 'res');
			this.mainData.chackInfo.inspectedUnitName = res.name;
			this.mainData.chackInfo.inspectedUnitCode = res.unitId;
			this.orgVisible = false;
		},
		addTableData() {
			if (this.mainData.rectificationPlan.plans) {
				this.mainData.rectificationPlan.plans.push({
					rectificationMatters: null,
					estimatedRectificationTime: null,
					rectificationMeasures: null,
					responsiblePerson: null,
					responsiblePersonCode: null,
				});
			}
		},
		//选择模板
		templateClick(val) {
			if (val) {
				this.prosecutionDialog = true;
			}
		},
		prosecutionSelect(data) {
			this.mainData.reportSubject = data.reportSubject;
			this.mainData.reportYear = data.reportYear;
			this.mainData.reportCategory = data.reportCategory;
			this.mainData.internalExternalRisk = data.internalExternalRisk;
			this.mainData.positiveNegativeImpact = data.positiveNegativeImpact;
			this.mainData.involvedAmount = data.involvedAmount;
			this.mainData.riskDescription = data.riskDescription;
			this.mainData.riskReason = data.riskReason;
			this.mainData.potentialConsequences = data.potentialConsequences;
			this.mainData.reportFile = data.reportFile;
			this.mainData.unitTypeId = data.unitTypeId; //单位类型id
			this.mainData.riskDepartment = data.riskDepartment; //风险部门
			this.mainData.riskDepartmentId = data.riskDepartmentId; //风险部门Id
			this.mainData.currentUnit = data.currentUnit; //当事单位
			this.mainData.currentUnitId = data.currentUnitId; //当事单位id
			this.mainData.reporter = data.createPsnName; //经办人
			this.mainData.reportingUnit = data.createDeptName; //经办单位
			this.mainData.createPsnPhone = data.createPsnPhone; //经办人电话
			this.mainData.caseInterest = data.caseInterest; //利息
			this.mainData.createOgnName = data.createOgnName; //经办单位
			this.mainData.createPsnName = data.createPsnName; //经办人
			this.mainData.createDeptName = data.createDeptName; //经办部门
			// this.mainData.auditStatus = data.dataState;//状态
			this.mainData.partiesList = data.partiesList;
			this.mainData.partiesList.forEach((item) => {
				item.id = this.utils.createUUID();
				item.masterId = this.mainData.id;
			});

			this.mainData.claimList = data.claimList;
			this.mainData.claimList.forEach((item) => {
				item.id = this.utils.createUUID();
				item.parentId = this.mainData.id;
			});

			this.mainData.otherDataList = data.otherDataList;
			this.mainData.otherDataList.forEach((item) => {
				item.id = this.utils.createUUID();
				item.parentId = this.mainData.id;
				item.files = null;
			});

			this.mainData.relations = data.relations;
			this.mainData.relations.forEach((item) => {
				item.id = this.utils.createUUID();
				item.relationId = this.mainData.id;
			});
		},
		approval_() {

			if (this.mainData.rectificationPlan.plans.length <= 0) {
				this.$message.error("整改计划不能为空");
			} else {

				this.$refs['dataForm'].validate((valid) => {
					let isValid = this.validateData()
					if (valid && isValid) {
						this.mainData.currentTaskType = "整改结果反馈";
						this.mainData.isSubmit = true
						this.save()
							.then(() => {
								let pageName = "Rectification_Plan_Detail"
								HgjcApi.sendMessageForCreater(this.mainData, pageName);
							})
							.then(() => {
								this.mcpLayout.closeTab();
								this.layout.openNewTab('整改结果反馈', 'results_feedback_index', null, null)
							});
					}
				});
			}

		},
		save_() {
  this.$refs.dataForm.validate((valid) => {
    if (!valid) {
      this.$message.error('请完善基础表单信息');
      return;
    }

    const isTableValid = this.validateData();
    if (!isTableValid) {
      this.$message.error('请完善整改计划表格中的必填项');
      return;
    }

    this.mainData.isSubmit = false;
    HgjcApi.save(this.mainData)
      .then(() => {
        this.$message.success('保存成功!');
        // 重置表单验证状态
        this.$nextTick(() => {
          this.$refs.dataForm.clearValidate();
        });
      })
      .catch(error => {
        const errorMsg = error.response?.data?.message || '保存失败，请检查控制台';
        this.$message.error(errorMsg);
        console.error('保存错误详情:', error);
      });
  });
},
		saveCurrentTime() {
			const now = new Date();
			const year = now.getFullYear();
			const month = String(now.getMonth() + 1).padStart(2, '0');
			const day = String(now.getDate()).padStart(2, '0');
			const hours = String(now.getHours()).padStart(2, '0');
			const minutes = String(now.getMinutes()).padStart(2, '0');
			const seconds = String(now.getSeconds()).padStart(2, '0');
		},
	},
};
</script>

<style scoped>
.test {
	margin-bottom: 0px;
}
</style>
