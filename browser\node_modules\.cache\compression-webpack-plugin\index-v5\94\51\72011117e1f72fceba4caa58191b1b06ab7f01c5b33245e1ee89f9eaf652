
01403d513d29bdad675fe861902d0b41ae53f906	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.456.1754018536329.js\",\"contentHash\":\"fd084b32ddd3b7fbbe6696ebb5300485\"}","integrity":"sha512-N1clOJ6Mo7GBEID+S5k+6s7EVN23hZTU6u0KBx9ipx+DD17K9jwnJPhIl+nqyBJvbP55u6xbe2eo5oD1X+owQA==","time":1754018575977,"size":104266}