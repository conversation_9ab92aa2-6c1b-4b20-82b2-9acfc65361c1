
09797900a686195439a15ce8fafcea1740ae5709	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.279.1754018536329.js\",\"contentHash\":\"d357121df39fab328a0630eb6e9c6ac3\"}","integrity":"sha512-Y+VXlxY7bA9UeQkVkQYIUrZ0p5re4GaIpJCXv6qD0yCNs2TWUnBvf4r2pk+Gmyo9rpsoYXDzEZ3l1GHo681pbQ==","time":1754018575963,"size":115356}