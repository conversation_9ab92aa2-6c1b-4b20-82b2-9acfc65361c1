
c4402c882b3122ccd05e74509b2bfb08199d5945	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.23.1754018536329.js\",\"contentHash\":\"9ad5f458dd1f0df6e5000271a328af76\"}","integrity":"sha512-7Lx8YgMFe8NVsKeJ+4SfeY/QroKjJ/a3I/RT8LrA8VMRhft2avGuRC7LZgbQ8ebTqH6sVq9g8cLqbH0hKuwprQ==","time":1754018575978,"size":117677}