
555db319e63a10a66f9b581e91f3f287aeacb84a	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.373.1754018536329.js\",\"contentHash\":\"ada0a5c902f69aa52afd78b153031600\"}","integrity":"sha512-Ic77O8sgSAxf743MaTtB1LOZCRlZmK4I9JZpfL77o5kncKg3+zWV7vno4TWLcCSMFVwXzSjU/b06ab/FkVDmzQ==","time":1754018576021,"size":166390}