
59deaf34f39ff43f3d51fc9a3f479ee15d3e8869	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.474.1754018536329.js\",\"contentHash\":\"b71973f404b52267894006497e7d3d17\"}","integrity":"sha512-aBEjs1VhHzj1TyE7fCtNAghn/7v4FuPHETpEI8AcVrKam7DoLNsh1nkNgF5+h2oOabKG+HJaUr5wXyf8HtyG5A==","time":1754018575958,"size":22788}