-- 订单处理模块建表语句

-- 商品库存表
CREATE TABLE PRODUCT_INVENTORY (
    ID VARCHAR(50) PRIMARY KEY,
    PRODUCT_ID VARCHAR(50) NOT NULL UNIQUE,
    PRODUCT_NAME VARCHAR(200) NOT NULL,
    QUANTITY DECIMAL(18,2) DEFAULT 0,
    UNIT VARCHAR(50),
    WARNING_THRESHOLD DECIMAL(18,2) DEFAULT 0,
    CREATE_OGN_ID VARCHAR(50),
    CREATE_OGN_NAME VARCHAR(200),
    CREATE_DEPT_ID VARCHAR(50),
    CREATE_DEPT_NAME VARCHAR(200),
    CREATE_GROUP_ID VARCHAR(50),
    CREATE_GROUP_NAME VARCHAR(200),
    CREATE_PSN_ID VARCHAR(50),
    CREATE_PSN_NAME VARCHAR(100),
    CREATE_ORG_ID VARCHAR(50),
    CREATE_ORG_NAME VARCHAR(200),
    CREATE_PSN_FULL_ID VARCHAR(500),
    CREATE_PSN_FULL_NAME VARCHAR(500),
    CREATE_LEGAL_UNIT_ID VARCHAR(50),
    CREATE_LEGAL_UNIT_NAME VARCHAR(200),
    CREATE_TIME TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UPDATE_TIME TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    DATA_STATE VARCHAR(50) DEFAULT 'NORMAL',
    DATA_STATE_CODE INT DEFAULT 1
);

-- 订单表
CREATE TABLE PRODUCT_ORDER (
    ID VARCHAR(50) PRIMARY KEY,
    ORDER_ID VARCHAR(50) NOT NULL UNIQUE,
    PRODUCT_ID VARCHAR(50) NOT NULL,
    PRODUCT_NAME VARCHAR(200),
    QUANTITY DECIMAL(18,2) NOT NULL,
    UNIT VARCHAR(50),
    ORDER_STATUS VARCHAR(50) DEFAULT 'PENDING',
    CREATE_OGN_ID VARCHAR(50),
    CREATE_OGN_NAME VARCHAR(200),
    CREATE_DEPT_ID VARCHAR(50),
    CREATE_DEPT_NAME VARCHAR(200),
    CREATE_GROUP_ID VARCHAR(50),
    CREATE_GROUP_NAME VARCHAR(200),
    CREATE_PSN_ID VARCHAR(50),
    CREATE_PSN_NAME VARCHAR(100),
    CREATE_ORG_ID VARCHAR(50),
    CREATE_ORG_NAME VARCHAR(200),
    CREATE_PSN_FULL_ID VARCHAR(500),
    CREATE_PSN_FULL_NAME VARCHAR(500),
    CREATE_LEGAL_UNIT_ID VARCHAR(50),
    CREATE_LEGAL_UNIT_NAME VARCHAR(200),
    CREATE_TIME TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UPDATE_TIME TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    DATA_STATE VARCHAR(50) DEFAULT 'NORMAL',
    DATA_STATE_CODE INT DEFAULT 1,
    FOREIGN KEY (PRODUCT_ID) REFERENCES PRODUCT_INVENTORY(PRODUCT_ID)
);

-- 创建索引
CREATE INDEX IDX_PRODUCT_INVENTORY_PRODUCT_ID ON PRODUCT_INVENTORY(PRODUCT_ID);
CREATE INDEX IDX_PRODUCT_INVENTORY_NAME ON PRODUCT_INVENTORY(PRODUCT_NAME);
CREATE INDEX IDX_PRODUCT_ORDER_ORDER_ID ON PRODUCT_ORDER(ORDER_ID);
CREATE INDEX IDX_PRODUCT_ORDER_PRODUCT_ID ON PRODUCT_ORDER(PRODUCT_ID);
CREATE INDEX IDX_PRODUCT_ORDER_STATUS ON PRODUCT_ORDER(ORDER_STATUS);

-- 插入示例数据
INSERT INTO PRODUCT_INVENTORY (ID, PRODUCT_ID, PRODUCT_NAME, QUANTITY, UNIT, WARNING_THRESHOLD, CREATE_TIME) VALUES
('1', 'P001', '钢材A型', 1000.00, '吨', 100.00, NOW()),
('2', 'P002', '钢材B型', 500.00, '吨', 50.00, NOW()),
('3', 'P003', '钢材C型', 200.00, '吨', 20.00, NOW()),
('4', 'P004', '螺纹钢', 800.00, '吨', 80.00, NOW()),
('5', 'P005', '钢板', 300.00, '吨', 30.00, NOW()); 