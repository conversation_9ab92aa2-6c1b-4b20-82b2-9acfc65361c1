
6a059f5cc3c7a6483c047e9e173e57ff633ba729	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.222.1754018536329.js\",\"contentHash\":\"328660a5e3b4656dc9f7fcbcd7b620d3\"}","integrity":"sha512-UmAtiqKH3LevlYG/QtJ7zol2LBBDxGA5uZeeE5/xipbuAyhJeqWw4KpAju6JM/am0tz2D6svCB7aVOSzAEtD7g==","time":1754018576058,"size":210164}