
a5412eb1d58c045d034a19ffb409f703dcef652f	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.326.1754018536329.js\",\"contentHash\":\"88845414d0768231c81bd90d6f948b83\"}","integrity":"sha512-omfYGPhV9e3Sw9XaBri66czqjdvAoupl+jwJcv3Rf8VCdwFxjDheqFWKvnxDOxsVcNKi3YQLnoRA4h4IDaDk6w==","time":1754018575974,"size":103809}