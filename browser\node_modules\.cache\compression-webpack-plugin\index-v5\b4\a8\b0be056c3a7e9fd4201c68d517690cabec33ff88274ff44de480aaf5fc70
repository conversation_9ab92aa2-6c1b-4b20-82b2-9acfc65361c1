
fd7df6d3880331fe58b6468a8947437afb49dee5	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.255.1754018536329.js\",\"contentHash\":\"3b2e44e6c0ec2ec28137ec30efe3622c\"}","integrity":"sha512-Bk0JLbml8cun70kKTVNTgX3zHzioCMv7ZJSiIii1CmSIszGAJ4489Dq0SVplBtCcKNYSq/agUBySfMgyJRdhKQ==","time":1754018576005,"size":157940}