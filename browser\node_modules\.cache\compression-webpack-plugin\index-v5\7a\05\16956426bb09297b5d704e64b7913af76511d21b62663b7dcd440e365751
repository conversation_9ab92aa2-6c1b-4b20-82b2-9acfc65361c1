
0f4c243bd0fbd02c6a9c1f7298723f7ea924a674	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.378.1754018536329.js\",\"contentHash\":\"b47c2fd0ae606e05f976d5b499224a76\"}","integrity":"sha512-lKQhqUReMyiTx3d9KEaLqFwAbe8LmJFIxJD/J3lziIqURi3YS/R6l5u4knop0YV+EfGSVESAzuYLBlp5CXP8vw==","time":1754018575976,"size":61439}