package com.klaw.service.imp.complianceRiskServiceImp;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import com.klaw.dao.complianceRiskDao.CulturePropagandaMapper;

import com.klaw.entity.complianceRiskBean.CulturePropagandaEntity;
import com.klaw.service.complianceRiskService.CulturePropagandaService;
import com.klaw.utils.DataAuthUtils;
import com.klaw.utils.PageUtils;
import com.klaw.vo.Json;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class CulturePropagandaServiceImpl extends ServiceImpl<CulturePropagandaMapper,CulturePropagandaEntity> implements CulturePropagandaService {


    @Override
    public Page<CulturePropagandaEntity> queryPageData(JSONObject json) {
        QueryWrapper<CulturePropagandaEntity> wrapper = new QueryWrapper<>();
        getFilter(json, wrapper);
        return page(new PageUtils<>(json), wrapper);
    }



    public void getFilter(JSONObject json, QueryWrapper<CulturePropagandaEntity> wrapper) {
        //是否台账功能（登记只查自己的，台账和弹框查自己和数据权限的）
        boolean isQuery = json.containsKey("isQuery") ? json.getBoolean("isQuery") : false;
        String orgId = json.containsKey("orgId") ? json.getString("orgId") : "";
            // 标题
            String title = json.containsKey("title") ? json.getString("title") : null;

            // 编号
            String cultureId = json.containsKey("cultureId") ? json.getString("cultureId") : null;

            // 文化分类
            String cultureCategory = json.containsKey("cultureCategory") ? json.getString("cultureCategory") : null;


            // 模糊搜索值
            String fuzzyValue = json.containsKey("fuzzyValue") ? json.getString("fuzzyValue") : null;

            // 排序字段
            String sortName = json.containsKey("sortName") ? json.getString("sortName") : null;

            // 排序字段是否升序
            boolean order = json.containsKey("order") ? json.getBoolean("order") : false;

            // 应用查询条件
            if (StringUtils.isNotBlank(title)) {
                wrapper.and(i -> i.like("title", title));
            }

            if (StringUtils.isNotBlank(cultureId)) {
                wrapper.and(i -> i.like("culture_id", cultureId));
            }

            if (StringUtils.isNotBlank(cultureCategory)) {
                wrapper.and(i -> i.like("culture_category", cultureCategory));
            }


            if (StringUtils.isNotBlank(fuzzyValue)) {
                wrapper.and(i -> i.like("title", fuzzyValue)
                        .or().like("culture_id", fuzzyValue)
                        .or().like("culture_category", fuzzyValue));
            }

            if (StringUtils.isNotBlank(sortName) && order) {
                wrapper.orderByAsc(sortName);
            } else if (StringUtils.isNotBlank(sortName)) {
                wrapper.orderByDesc(sortName);
            }
        Long functionId = DataAuthUtils.getFunctionIdByCode("case_level_ledger");
        if(isQuery){
            DataAuthUtils.dataPermSql(wrapper, null, functionId, orgId);
        }else{
            wrapper.eq("create_org_id", orgId);
        }

            //按评价时间倒序显示
            wrapper.orderByDesc("create_time");
        }


        @Override
        public CulturePropagandaEntity queryDataById(String id) {
            CulturePropagandaEntity culturePropagandaEntity = getById(id);
            return culturePropagandaEntity;
        }

        @Override
        @Transactional(rollbackFor = Exception.class)
        public Json deleteDataById(String id) {
            boolean flag = removeById(id);
            if (!flag) {
                return Json.fail().msg("未找到需要删除的数据");
            }
            return Json.succ().msg("删除成功!");
        }
}


