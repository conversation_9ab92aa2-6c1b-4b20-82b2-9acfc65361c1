package com.klaw.service.imp.complianceOrganizationServiceImp;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.klaw.entity.complianceOrganizationBean.OrganizationMemberHistory;
import com.klaw.service.complianceOrganizationService.OrganizationMemberHistoryService;
import com.klaw.dao.complianceOrganizationDao.OrganizationMemberHistoryMapper;
import com.klaw.utils.DataAuthUtils;
import com.klaw.utils.StringUtil;
import com.sgai.mcp.core.impl.LoginHelper;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【organization_member_history(组织人员历史表)】的数据库操作Service实现
* @createDate 2024-12-06 17:57:56
*/
@Service
public class OrganizationMemberHistoryServiceImpl extends ServiceImpl<OrganizationMemberHistoryMapper, OrganizationMemberHistory>
    implements OrganizationMemberHistoryService{

    @Override
    public Page<OrganizationMemberHistory> queryHistory(JSONObject jsonObject) {
        boolean isQuery = jsonObject.containsKey("isQuery") ? jsonObject.getBoolean("isQuery") : false;
        String memberName = jsonObject.containsKey("memberName") ? jsonObject.getString("memberName") : null;
        String memberCode = jsonObject.containsKey("memberCode") ? jsonObject.getString("memberCode") : null;
        String dataState = jsonObject.containsKey("dataState") ? jsonObject.getString("dataState") : null;
        Integer page = jsonObject.containsKey("page") ? jsonObject.getInteger("page") : null;
        Integer limit = jsonObject.containsKey("limit") ? jsonObject.getInteger("limit") : null;
        String orgId = StringUtil.emptyStr(jsonObject.getString("orgId"));
        Long functionId = DataAuthUtils.getFunctionIdByCode("hgryk_index");
        Long[] roleIds = LoginHelper.getAllRoleId();
        List<Long> list = Arrays.asList(roleIds);
        List<OrganizationMemberHistory> results = this.baseMapper.queryHistory(isQuery,memberName,memberCode,dataState,list,functionId,orgId,page - 1,limit);
        int count = this.baseMapper.queryHistoryCount(isQuery,memberName,memberCode,dataState,orgId,list,functionId);
        Page<OrganizationMemberHistory> page1 = new Page<>();
        page1.setRecords(results);
        page1.setTotal(count);
        return page1;
    }
}




