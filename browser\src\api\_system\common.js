import {request} from '@/api/index'

export default {

  lnkUpdate(data) {
    return request({
      url: '/sys_common/update_lnk',
      method: 'post',
      data
    })
  },

  queryLnk() {
    return request({
      url: '/sys_common/query_lnk',
      method: 'get'
    })
  },

  getCheckCode() {
    return request({
      url: '/sys_common/getCheckCode',
      method: 'get'
    })
  },

  updateNote(data) {
    return request({
      url: '/sys_common/update_note',
      method: 'post',
      data
    })
  },

  queryNote() {
    return request({
      url: '/sys_common/query_note',
      method: 'get'
    })
  },

  createKvsequence(data) {
    return request({
      url: '/sys_common/createKvsequence',
      method: 'post',
      data
    })
  }
}
