# 订单处理模块 API 测试示例

## 1. 商品库存管理 API

### 1.1 添加商品库存
```bash
POST /api/inventory/add
Content-Type: application/json

{
    "productId": "P006",
    "productName": "测试钢材",
    "quantity": 500.00,
    "unit": "吨",
    "warningThreshold": 50.00
}
```

### 1.2 根据商品ID查询库存
```bash
GET /api/inventory/getByProductId/P001
```

### 1.3 根据商品名称模糊查询库存
```bash
GET /api/inventory/getByProductName?productName=钢材
```

### 1.4 更新库存（增加）
```bash
PUT /api/inventory/update?productId=P001&quantity=100&operation=ADD
```

### 1.5 更新库存（减少）
```bash
PUT /api/inventory/update?productId=P001&quantity=50&operation=REDUCE
```

### 1.6 查询预警库存
```bash
GET /api/inventory/warning
```

### 1.7 检查库存是否充足
```bash
GET /api/inventory/checkStock?productId=P001&requiredQuantity=100
```

## 2. 订单管理 API

### 2.1 提交订单
```bash
POST /api/order/submit
Content-Type: application/json

{
    "productId": "P001",
    "quantity": 50.00
}
```

### 2.2 根据订单ID查询订单
```bash
GET /api/order/getById/ORD1703123456789
```

### 2.3 根据商品ID查询订单列表
```bash
GET /api/order/getByProductId/P001
```

### 2.4 根据订单状态查询订单列表
```bash
GET /api/order/getByStatus/PENDING
```

### 2.5 更新订单状态
```bash
PUT /api/order/updateStatus?orderId=ORD1703123456789&orderStatus=COMPLETED
```

### 2.6 确认订单（自动扣减库存）
```bash
PUT /api/order/confirm/ORD1703123456789
```

## 3. 订单状态说明

- `PENDING`: 待处理
- `CONFIRMED`: 已确认
- `REJECTED`: 已拒绝
- `COMPLETED`: 已完成

## 4. 操作流程示例

1. **添加库存**: 先添加商品库存信息
2. **提交订单**: 提交订单，系统会检查库存是否充足
3. **确认订单**: 确认订单时，系统会自动扣减库存
4. **库存预警**: 定期查询预警库存，及时补货

## 5. 响应格式

```json
{
    "success": true,
    "message": "操作成功",
    "code": null,
    "data": {}
}
```

## 6. 错误处理

- 库存不足时，订单会被拒绝
- 库存低于预警阈值时，会返回警告信息
- 所有操作都有完整的错误处理和日志记录 