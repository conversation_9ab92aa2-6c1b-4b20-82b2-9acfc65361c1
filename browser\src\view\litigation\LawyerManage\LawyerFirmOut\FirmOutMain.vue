<template>
  <FormWindow>
    <el-container style="height: calc(100vh - 150px);">

      <el-main v-if="view === 'old'">
        <el-scrollbar style="height: 100%" wrap-style="overflow-x: hidden;">
          <el-form ref="dataForm"
                   :model="mainData"
                   :rules="dataState !== 'view' ? rules : {}"
                   :style="dataState !== utils.formState.VIEW ? 'margin-right: 50px;' : ' margin-right: 0px;'" label-width="120px"
                   style="padding-right: 10px;"
          >
            <span
                style="text-align: left;font-size: 20px;margin-left: 43%;font-weight: 900;">律所律师出库审批详情单</span>
            <div v-if="dataState !== 'view'">
              <div style="margin: 10px">
                <span style="font-weight: bold;font-size: 16px;color: #5A5A5F;">会知信息</span>
              </div>
              <el-divider></el-divider>
              <el-row style="margin-top: 10px">
                <el-col :span="24">
                  <el-form-item label="会知部门" prop="noticeDeptName">
                    <el-input v-if="dataState !== 'view'"
                              v-model="mainData.noticeDeptName"
                              class="input-with-select" clearable disabled placeholder="请选择">
                      <el-button slot="append" icon="el-icon-search" @click="chooseNoticeDeptClick"/>
                    </el-input>
                    <span v-else class="viewSpan">{{ mainData.noticeDeptName }}</span>
                  </el-form-item>
                </el-col>
              </el-row>

              <div style="margin: 10px">
                <span style="font-weight: bold;font-size: 16px;color: #5A5A5F;">出库信息</span>
              </div>
              <el-divider></el-divider>
              <el-row style="margin-top: 10px;">
                <el-col :span="8">
                  <el-form-item label="出库类型" prop="outTypeName">
                    <el-select v-if="dataState !== 'view'"
                               v-model="mainData.outTypeId" clearable disabled placeholder="请选择"
                               style="width:100%" @change="outTypeChange">
                      <el-option
                          v-for="item in utils.out_type_data"
                          :key="item.id"
                          :label="item.dicName"
                          :value="item.id"
                      />
                    </el-select>
                    <span v-else class="viewSpan">{{ mainData.outTypeName }}</span>
                  </el-form-item>
                </el-col>

                <el-col :span="8">
                  <el-form-item label="出库原因" prop="failId">
                    <el-select v-if="dataState !== 'view'" v-model="failIds"
                               clearable collapse-tags multiple placeholder="请选择" style="width:100%"
                               @change="failChange">
                      <el-option
                          v-for="item in failData"
                          :key="item.id"
                          :label="item.dicName"
                          :value="item.id"
                      />
                    </el-select>
                    <span v-else class="viewSpan">{{ mainData.failName }}</span>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="16">
                  <el-form-item label="事项名称" prop="itemName">
                    <el-input v-if="dataState !== 'view'" v-model.trim="mainData.itemName"
                              clearable maxlength="100" placeholder="请输入..." show-word-limit style="width: 100%"/>
                    <span v-else class="viewSpan">{{ mainData.itemName }}</span>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="事项编号">
                    <el-input v-if="dataState !== 'view'" v-model.trim="mainData.sequenceCode" disabled
                              show-word-limit style="width: 100%"/>
                    <span v-else class="viewSpan">{{ mainData.sequenceCode }}</span>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="24">
                  <el-form-item label="出库说明">
                    <el-input v-if="dataState !== 'view'" v-model="mainData.description"
                              :autosize="{ minRows: 3, maxRows: 6}" clearable
                              maxlength="1000"
                              placeholder="请输入..." show-word-limit style="width: 100%" type="textarea"/>
                    <text-span v-else :text=" mainData.description" class="viewSpan"/>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row>
                <el-col :span="24">
                  <el-form-item label="附件资料">
                    <uploadDoc
                        v-model="mainData.attachment"
                        :disabled="dataState==='view'"
                        :doc-path="docURL"
                        :files.sync="mainData.attachment"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>

            <div v-else>
              <SimpleBoardTitle style="margin-top: 5px;" title="会知信息">
                <table class="table_content" style="margin-top: 10px;">
                  <tbody>
                  <tr>
                    <th class="th_label" colspan="2">会知部门</th>
                    <td class="td_value" colspan="22">{{ mainData.noticeDeptName }}</td>
                  </tr>
                  </tbody>
                </table>
              </SimpleBoardTitle>

              <SimpleBoardTitle style="margin-top: 5px;" title="出库信息">
                <table class="table_content" style="margin-top: 10px;">
                  <tbody>
                  <tr>
                    <th class="th_label" colspan="2">事项名称</th>
                    <td class="td_value" colspan="6">{{ mainData.itemName }}</td>
                    <th class="th_label" colspan="2">事项编号</th>
                    <td class="td_value" colspan="14">{{ mainData.sequenceCode }}</td>
                  </tr>
                  <tr>
                    <th class="th_label" colspan="2">出库类型</th>
                    <td class="td_value" colspan="6">{{ mainData.outTypeName }}</td>
                    <th class="th_label" colspan="2">出库原因</th>
                    <td class="td_value" colspan="14">{{ mainData.failName }}</td>
                  </tr>
                  <tr>
                    <th class="th_label_" colspan="2">出库说明</th>
                    <td class="td_value_" colspan="22">{{ mainData.description }}</td>
                  </tr>
                  </tbody>

                  <tbody>
                  <tr>
                    <th class="th_label_" colspan="2">附件资料</th>
                    <td class="td_value_" colspan="22">
                      <uploadDoc
                          v-model="mainData.attachment"
                          :disabled="dataState==='view'"
                          :doc-path="docURL"
                          :files.sync="mainData.attachment"
                      />
                    </td>
                  </tr>
                  </tbody>
                </table>
              </SimpleBoardTitle>

            </div>


            <simple-board :data-state="dataState" :has-add="hasAdd" style="margin-top: 20px;" title="出库律所信息"
                          @addBtn="addLawyer">
              <el-table
                  v-draggable="mainData.lawFirmOutList"
                  :data="mainData.lawFirmOutList"
                  :height="200"
                  :show-overflow-tooltip="true"
                  border
                  fit
                  highlight-current-row
                  stripe
                  style="width: 100%"
              >
                <el-table-column align="center" label="序号" show-overflow-tooltip type="index" width="60"/>
                <el-table-column align="center" label="律所名称" min-width="100" prop="lawyerFirm"
                                 show-overflow-tooltip/>
                <el-table-column align="center" label="负责人" min-width="100" prop="functionary"
                                 show-overflow-tooltip/>
                <el-table-column align="center" label="擅长领域" min-width="200" prop="beGoodAtDomain"
                                 show-overflow-tooltip/>
                <el-table-column align="center" label="操作" width="200px">
                  <template slot-scope="scope">
                    <el-button v-if="dataState !== 'view'" size="mini" type="text"
                               @click.native.prevent="deleteRow(scope.$index, scope.row)">删除
                    </el-button>
                    <el-button v-if="dataState === 'view'" size="mini" type="text"
                               @click.native.prevent="look(scope.$index,scope.row)">查看
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </simple-board>

            <el-row style="margin-top: 20px;">
              <el-col :span="18">
                <el-form-item label="经办组织">
                  <span class="viewSpan">{{ mainData.createPsnFullName }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="经办时间">
                  <span class="viewSpan">{{ mainData.createTime | parseTime }}</span>
                </el-form-item>
              </el-col>
            </el-row>

            <!--选择律所-->
            <LawFirmDialog :dialog-visible.sync="LawFirmVisible" @lawFirmSure="lawFirmSure"/>
            <!--选择部门或者人员-->
            <el-dialog :close-on-click-modal="false" :visible.sync="orgVisible" title="选择部门" width="50%">
              <div class="el-dialog-div">
                <orgTree :accordion="false" :checked-data.sync="zxcheckedData" :is-check="is_Check" :is-checked-user="isCheckedUser"
                         :is-filter="true" :is-not-cascade="true" :show-user="showUser"/>
              </div>
              <span slot="footer" class="dialog-footer">
                <el-button class="negative-btn" icon="" @click="cancel_">取消</el-button>
                <el-button class="active-btn" icon="" type="primary" @click="choiceDeptSure_">确定
                </el-button>
              </span>
            </el-dialog>
          </el-form>
        </el-scrollbar>
      </el-main>

      <el-main v-else>
        <el-card style="height: auto;margin-left: 1%;margin-right: 2%;margin-top: 1%;">
          <el-scrollbar style="height: 100%;">
            <el-form ref="dataForm"
                     :class="className"
                     :model="mainData" :rules="rules" style="margin-left: 10px;margin-right: 10px;"
            >
              <el-row style="margin-top: 20px;">
                <span style="text-align: left;font-size: 23px;margin-left: 43%;font-weight: 900;">律所出库审批单</span>
              </el-row>
              <SimpleBoardTitleApproval style="margin-top: 5px;" title="出库信息">
                <table class="table_content">
                  <tbody>
                  <tr>
                    <th class="th_label_approval" colspan="3">事项名称</th>
                    <td class="td_value_approval" colspan="9">{{ mainData.itemName }}</td>
                    <th class="th_label_approval" colspan="3">事项编号</th>
                    <td class="td_value_approval" colspan="9">{{ mainData.sequenceCode }}</td>
                  </tr>
                  <tr>
                    <th class="th_label_approval" colspan="3">经办组织</th>
                    <td class="td_value_approval" colspan="9">{{ mainData.createPsnFullName }}</td>
                    <th class="th_label_approval" colspan="3">经办时间</th>
                    <td class="td_value_approval" colspan="9">{{ mainData.createTime | parseTime }}</td>
                  </tr>
                  <tr>
                    <th class="th_label_approval" colspan="3">律所名称</th>
                    <td class="td_value_approval" colspan="21">{{ lawyerFirms }}</td>
                  </tr>
                  <tr>
                    <th class="th_label_approval_" colspan="3">出库说明</th>
                    <td class="td_value_approval_" colspan="21">{{ mainData.description }}</td>
                  </tr>
                  </tbody>

                  <tbody>
                  <tr>
                    <th class="th_label_approval" colspan="3">附件资料</th>
                    <td class="td_value_approval" colspan="21">
                      <uploadDoc
                          v-model="mainData.attachment"
                          :disabled="true"
                          :doc-path="docURL"
                          :files.sync="mainData.attachment"
                      />
                    </td>
                  </tr>
                  </tbody>
                </table>
              </SimpleBoardTitleApproval>

              <!--审批历史 -->
              <SimpleBoardTitleApproval class="print-table-wrap" style="margin-top: 10px;" title="审查意见">
                <ProcessOpinion :proc-inst-id="this.obj.processInstanceId" :task-id="this.obj.taskId"
                                style="border: solid 1px #606266;" type-code="1"/>
                <div v-if="approvalIs && isParseElement">
                  <el-input
                      v-model="approvalOpinion"
                      :rows="2"
                      placeholder="请输入审批意见"
                      style="border-radius: 0 !important;"
                      type="textarea">
                  </el-input>
                </div>

              </SimpleBoardTitleApproval>

              <SimpleBoardTitleApproval class="leadership-opinions-section-wrap" style="margin-top: 10px;"
                                        title="领导意见">
                <div style="border: solid 1px #606266;overflow: hidden">
                  <ProcessOpinion :proc-inst-id="this.obj.processInstanceId" :task-id="this.obj.taskId" type-code="2"/>
                  <div v-if="approvalIs && isParseElementlg">
                    <el-input
                        v-model="approvalOpinion"
                        :rows="2"
                        placeholder="请输入审批意见"
                        style="border-radius: 0 !important;"
                        type="textarea">
                    </el-input>
                  </div>
                </div>
              </SimpleBoardTitleApproval>

            </el-form>
          </el-scrollbar>
        </el-card>
      </el-main>

      <Shortcut :detail-show="detailShow" :noticeShow="noticeShow" :print-show="printShow"
                @detailClick="detailClick" @noticeClick="noticeClick" @printClick="printClick"/>

    </el-container>

  </FormWindow>
</template>

<script>
import lawyerFirmApi from '@/api/LawyerManage/LawyerFirmOut/lawFirmOutApprovalMain'
import dictApi from '@/api/_system/dict'
import noticeApi from "@/api/_system/notice";
import processApi from "@/api/_system/process";
import orgApi from "@/api/_system/org";

import {mapGetters} from 'vuex'
import taskApi from "@/api/_system/task";

import FormWindow from '@/view/components/FormWindow/FormWindow'
import SimpleBoard from "@/view/components/SimpleBoard/SimpleBoardViewCase"
import uploadDoc from '@/view/components/UploadDoc/UploadDoc'
import LawFirmDialog from '@/view/litigation/LawyerManage/LawyerFirmOut/dialog/FirmOutDialog'
import textSpan from '@/view/components/TextSpan/TextSpan'
import orgTree from '@/view/components/OrgTree/OrgTree'
import Shortcut from "@/view/litigation/caseManage/caseExamine/Shortcut"
import SimpleBoardTitle from "../../../components/SimpleBoard/SimpleBoardTitle"
import ProcessOpinion from '@/view/components/ProcessOpinion/ProcessOpinion'
import SimpleBoardTitleApproval from "@/view/components/SimpleBoard/SimpleBoardTitleApproval"

export default {
  name: 'FirmOutMain',
  inject: ['layout', 'mcpLayout', 'mcpDesignPage'],
  components: {
    FormWindow, SimpleBoard, uploadDoc, LawFirmDialog, textSpan, orgTree, Shortcut,
    SimpleBoardTitle, ProcessOpinion, SimpleBoardTitleApproval
  },
  computed: {
    ...mapGetters(['orgContext']),
    editDisabled() {
      return this.mainData.dataStateCode === this.utils.dataState_BPM.SAVE.code || this.mainData.dataStateCode === this.utils.dataState_BPM.STARTING.code || this.mainData.dataStateCode === this.utils.dataState_BPM.BACK.code
    },
    isNotice() {
      const isNotice = this.$route.query.isNotice
      return this.mainData.dataStateCode !== this.utils.dataState_BPM.FINISH.code &&
          this.mainData.createPsnFullId === this.orgContext.currentPsnFullId && isNotice
    },
    noticeShow() {
      return this.mainData.dataStateCode === this.utils.dataState_BPM.FINISH.code
    },
    detailShow() {
      return this.view === 'new'
    },
    approvalShow() {
      return this.view === 'old'
    },
    printShow() {
      return this.view === 'new'
    },
    // 如果是从oa打开，则需需要加上这两个参数
    originOa() {
      let {origin, fullScreen} = this.$route.query
      return origin === 'oa' && fullScreen
    },
    isCreate: function () {
      return this.create === 'create'
    },
    isParseElement() {
      return this.parseElement === '部门意见'
    },
    isParseElementlg() {
      return this.parseElement === '领导意见'
    }
  },
  data() {
    return {
      lawyerFirms: '',
      approvalOpinion: '',
      parseElement: null,
      approvalIs: false,
      className: '',
      type: null,
      create: null,
      view: 'new',
      dataState: null,//表单状态，新增、查看、编辑
      functionId: null,//终止的时候要用，需要手动关闭
      typeCode: null,
      typeName: null,
      loading: true,
      tableData: [],
      tabId: null,
      rules: {
        outTypeName: [{required: true, message: '请选择出库类型', trigger: 'blur'}]
      },
      // 审批表数据
      mainData: {
        id: this.utils.createUUID(), // id
        sequenceCode: null, // 流水号
        outTypeName: null, // 出库类型name
        failId: null, // 淘汰原因ID
        description: null, // 出库说明
        attachment: null, // 依据资料
        updateTime: null, // 更新时间
        typeCode: null,
        typeName: null,
        dataState: this.utils.dataState_BPM.SAVE.name, // 状态 新增时默认是已保存
        dataStateCode: this.utils.dataState_BPM.SAVE.code, // 状态编码
        createOgnName: null, //  经办单位
        createDeptName: null, // 经办部门
        createPsnName: null, // 经办人员
        createTime: null, // 经办时间
        createOgnId: null,
        createDeptId: null,
        createPsnId: null,
        createPsnFullId: null,
        createPsnFullName: null,
        createGroupId: null,
        createGroupName: null,
        createOrgId: null,
        createOrgName: null,
        lawFirmOutList: [],
        fileId: null, // 手写签批id
      },
      failData: [],
      docURL: '/firmOut',
      zxcheckedData: [],
      showUser: false,
      is_Check: true,
      isCheckedUser: false,
      orgVisible: false,
      LawFirmVisible: false,
      LawFirmSelectState: this.utils.dataState_LAWYER.YRK.name,
      LawTypeSelectCode: "0",
      hasAdd: true,
      activity: null,//记录当前待办处于流程实例的哪个环节
      obj: {// 流程处理逻辑需要的各种参数
        taskId: null,
        processInstanceId: null,
        businessKey: null,
        title: null,
        functionName: '律所审批',
        functionCode: 'firm_out_main',
        sid: null,
      },

      noticeParams: {},
      noticeData: {
        moduleName: '', // 模块名称
        dataId: '', // 数据ID
        url: '', // 地址
        title: '', // 地址
        params: {} // 其他参数
      },
    }
  },
  created() {
    this.isHeadquarter()
    this.baseDataLoad()
    //因为是流程功能，知会、抄送，都需要按照流程抄送的配置打开，
    // 只是知会的需要更新消息表，抄送需要更新日志表
    //判断是知会还是抄送，可以根据param中的参数isNotice判断
    this.obj.functionName = '律所审批'
    const isNotice = this.$route.query.isNotice
    // isNotice在OA中打开会解析成Boolean，系统内会被转成字符"true"
    if (isNotice === true || isNotice === "true") {
      //知会
      if (this.$route.query.processInstanceId !== null && this.$route.query.processInstanceId !== undefined) {
        //保存的数据，获取不到实例ID，只有启动流程实例才能拿到
        this.obj.processInstanceId = this.$route.query.processInstanceId
        this.obj.taskId = this.$route.query.taskId
      }
      const read = this.$route.query.read
      const sid = this.$route.query.sid
      if (read === false || read === 'false') {
        noticeApi.read({sid: sid})
      }
    } else {
      //这里除了抄送会走，其他正常逻辑也会走，所以下面的参数判断了type === 'toRead'，即未读时，才会更新OA消息和日志记录
      this.obj.sid = this.$route.query.sid//消息表中的消息ID 日志表中的日志ID
      if (this.$route.query.processInstanceId !== null && this.$route.query.processInstanceId !== undefined) {
        //保存的数据，获取不到实例ID，只有启动流程实例才能拿到
        this.obj.processInstanceId = this.$route.query.processInstanceId
        this.obj.taskId = this.$route.query.taskId
      }
      const type = this.$route.query.type//获取消息状态类型，toRead-》未读，此时需要更新，haveRead-》已读，就不需要更新了
      if (type === 'toRead') {
        this.obj.pathname = window.location.pathname
        //返回url路径名（https://www.runoob.com/try/try.php?filename=tryjsref_loc_pathname，返回/try/try.php），判断是在法务系统打开还是在OA中打开
        //法务中打开会把相同流程实例的全部消息改为已读，所以是更新OA多条，OA中打开是只更新OA一条
        this.obj.title = "律所出库"
        //更新OA需要参数processInstanceId, title, functionName, oldTaskId,
        processApi.finishOATask(this.obj)
      }
    }
  },
  mounted() {
    //挂载完毕后，设置回调函数
    this.$emit('setCallBack', {
      beforeCallBack: this.beforeApproval,//点击办理或提交前的回调，效验必填控制
      afterCallBack: this.afterApproval,//点击办理弹框中再点击确定后的回调，审批完成后处理业务逻辑
      setTaskNodeInfo: this.setTaskNodeInfo,//挂载完毕后执行的回调，用于页面已进入需要处理的业务逻辑
      filterBtn: this.utils.filterBtn,
      beforeConfirmCallBack: this.beforeConfirmCallBack,
      beforeApprovalCb: this.beforeApprovalCb
    })
  },
  methods: {
    generateLawyerFirmsString() {
      // 使用 map 方法提取 lawyerFirm 属性，然后使用 join 方法拼接成字符串
      this.lawyerFirms = this.mainData.lawFirmOutList
          .map(item => item.lawyerFirm)
          .join('、');
    },
    beforeConfirmCallBack(data, resolve, reject) {
      const customProperties = data.nodeInfo.customProperties.find(item => item.key === 'FlowCustomPropertiesSettingsServiceImpl')
      if (customProperties && customProperties.value && JSON.parse(customProperties.value)['id'] === "1" && data.approvalFormData.comment == "") {
        // 消息按需求编辑 这只是个示例
        this.$confirm('未填写意见，请确认是否继续。', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          resolve()()
        }).catch(() => {
          reject()
        })
      } else {
        resolve()
      }
    },
    // 提交前
    beforeApproval(code, resolve) {
      // 校验必填项
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          // 保存
          this.save().then(() => {
            // 将部分参数传给OA
            this.mainData.title = "出库审批"
            this.mainData.code = code === null ? '' : code
            this.mainData.functionName = '律所审批'
            this.$emit('submit-success', this.mainData, 'id')
            // resolve(true)
            resolve({success: true, formData: {...this.mainData}, approvalOpinion: this.approvalOpinion})

          })
        } else {
          resolve(false)
          return false
        }
      })
    },
    setTaskNodeInfo(event) {
      const customProperties = event.customProperties.find(item => item.key === 'FlowCustomPropertiesSettingsServiceImpl')
      if (customProperties !== null && customProperties !== undefined) {
        this.parseElement = JSON.parse(customProperties.value)['name']
      }
      console.log("部门意见：" + this.parseElement)
      //（toDeal-待办处理，haveDealt-已办查看，toRead-未读，haveRead-已读）
      const type = this.$route.query.type
      // 业务ID
      const id = this.$route.query.businessKey
      //是否首环节（submitNode-首环节）
      this.activity = event.taskNodeType
      /*
      * 首环节
      *   1、businessKey-是null，说明是刚发起---执行init方法
      *   2、businessKey-有值
      *       1、回退处理--环节是submitNode   ---执行init
      *       2、已办查看--优先判断  haveDealt---执行load
      *       3、未读查看--优先判断  toRead   ---执行load
      *       4、已读查看--优先判断  haveRead ---执行load
      * */
      if (type === 'haveDealt' || type === 'toRead' || type === 'haveRead') {
        this.loadData(this.utils.formState.VIEW, id)
      } else { // 不是以上3种只能是待处理，只需要判断是否首环节即可
        if (event.taskNodeType === 'submitNode') {
          this.loadData(this.utils.formState.NEW, id)
        } else {
          this.loadData(this.utils.formState.VIEW, id)
        }
      }
    },
    //撤回转办
    beforeApprovalCb(code) {
      debugger
      if (code.type === "cancelTransfer") {
        this.obj.processInstanceId = this.$route.query.processInstanceId
        this.obj.taskId = this.$route.query.taskId
        this.obj.title = "出库审批"
        this.obj.code = code === null ? '' : code
        this.obj.functionName = '律所审批'
        let pathname = window.location.pathname
        this.obj.functionCode = 'firm_out_main'
        processApi.sendOATask(this.obj).then(res => {
          if (pathname === '/base/design_page') {
            this.mcpLayout.closeTab()
          } else {
            window.close()
          }
        })
      }

    },
    afterApproval(code, data) {
      // 回退到首环节，修改业务数据状态，修改任务标题
      console.log("code==" + code)
      console.log("data==" + data)
      this.obj.businessKey = this.mainData.id
      //获取参数，为后续操作准备，processInstanceId和taskId其实在created中赋值了，这里在赋值一次也行
      if (data != null && data.data != null) {
        this.obj.processInstanceId = data.data.id
      }
      if (this.obj.businessKey == null || this.obj.processInstanceId == null) {
        this.obj.processInstanceId = this.$route.query.processInstanceId
        this.obj.taskId = this.$route.query.taskId
      }

      //需要传参数到流程中，这里操作，不限于首环节传参数
      if (this.activity === 'submitNode') {
        lawyerFirmApi.setParam(this.obj).then(() => {
          console.log("传值成功")
        }).catch(() => {

        })
      }
      // 将部分参数传给OA
      this.obj.title = "出库审批"
      this.obj.code = code === null ? '' : code
      this.obj.functionName = '律所审批'
      //不是动态节点，给OA传待办
      if (code !== 'dynamic') {
        let loading = this.$loading({
          target: document.querySelector('.sg-page-wrap'),
          lock: false,
          text: '请稍后...',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        })
        processApi.sendOATask(this.obj).then(() => {
          loading.close()
          if (this.originOa) {
            window.close()
          }
          /*let pathname = window.location.pathname
          if (pathname.indexOf('/design_pages') !== -1) {
            window.close()
          }*/
        })
      }
    },
    // 根据数据ID加载数据
    loadData(dataState, dataId) {
      this.dataState = dataState
      this.functionId = this.$route.query.functionId
      if (this.$route.query.view !== undefined && this.$route.query.view !== '')
        this.view = this.$route.query.view
      if (this.$route.query.type !== undefined && this.$route.query.type !== '')
        this.type = this.$route.query.type
      if (this.$route.query.create !== undefined && this.$route.query.create !== '')
        this.create = this.$route.query.create
      lawyerFirmApi.queryDataById({id: dataId}).then(res => {
        this.mainData = res.data.data
        this.loading = false
      }).then(() => {
        this.approvalOpinionIs()
        this.generateLawyerFirmsString()
      })
    },
    approvalDisabled() {
      return this.mainData.dataStateCode !== this.utils.dataState_BPM.SAVE.code && this.mainData.dataStateCode !== this.utils.dataState_BPM.STARTING.code
    },
    save() {
      return new Promise((resolve, reject) => {
        this.mainData.updateTime = new Date()
        lawyerFirmApi.save(this.mainData).then(response => {
          resolve(response)
        }).catch(error => {
          reject(error)
        })
      })
    },
    submit() {
      this.$refs['dataForm'].validate(() => {
        this.save().then(() => {
          this.$emit('submit-success', this.mainData, 'id')
          this.$message.success('保存成功!')
        })
      })
    },
    outTypeChange(newVal) {
      this.mainData.outTypeName = this.utils.getDicName(this.utils.out_type_data, newVal)
    },
    failChange(newVal) {
      if (newVal && newVal.length > 0) {
        const arr = []
        for (let i = 0; i < newVal.length; i++) {
          const newValElement = newVal[i]
          const name = this.utils.getDicName(this.failData, newValElement)
          arr.push(name)
        }
        this.mainData.failName = arr.join('、')
      }
    },
    baseDataLoad() {
      dictApi.showSelect({
        dicCode: 'LS-CKYY'
      }).then(response => {
        this.failData = response.data.data
      })
    },
    addLawyer() {
      this.LawFirmVisible = true
    },
    look(index, row) {
      const tabId = this.utils.createUUID()
      this.layout.openNewTab("律所信息", "firm_ledger_law_firm", "firm_ledger_law_firm", tabId, {
        source: 'ledger', ...this.utils.routeState.VIEW(row.lawyerFirmId),
        tabId: tabId
      })
    },
    deleteRow(index) {
      this.mainData.lawFirmOutList.splice(index, 1)
    },
    lawFirmSure(val) {
      if (val && val.length > 0) {
        for (let i = 0; i < val.length; i++) {
          const valElement = val[i]
          const array = this.mainData.lawFirmOutList
          let bool = false
          for (let j = 0; j < array.length; j++) {
            const argument = array[j]
            if (argument.lawyerFirmId === valElement.id) {
              bool = true
              break
            }
          }
          if (!bool) {
            this.lawFirmCopy(valElement)
            this.mainData.lawFirmOutList.push(this.detailRow)
          }
        }
      }
      this.LawFirmVisible = false
    },
    lawFirmCopy(other) {
      const orgutil = this.orgContext
      this.detailRow = {
        id: null,
        lawyerFirm: null, // 律所名称
        lawyerFirmId: null, // 律所ID
        registerAddress: null, // 注册地址/律所地址
        functionary: null, // 负责人
        contactPerson: null, // 联系人
        phone: null, // 联系电话
        fas: null, // 传真
        email: null, // 邮箱
        postalCode: null, // 邮政编码
        issuingAuthority: null, // 主管机关
        licenseCode: null, // 统一社会信用代码
        licenseNumber: null, // 批准文号
        registeredCapital: null, // 设立资产
        foundTime: null, // 成立时间
        workNumber: null, // 执业人数
        applyDeptName: null, // 申请部门/所属公司
        applyDeptId: null, // 申请部门/所属公司
        annualInspectionName: null, // 年检情况
        annualInspectionId: null, // 年检情况
        noResponseTimes: null, // 未响应次数
        whetherMy: null, // 是否占住（yes-占住；no-未占住）
        parentId: null, // 父ID
        beGoodAtDomain: null, // 擅长领域
        beGoodAtDomainIds: null, // 擅长领域
        createOgnId: null,
        createOgnName: null,
        createDeptId: null,
        createDeptName: null,
        createPsnId: null,
        createPsnName: null,
        createPsnFullId: null,
        createPsnFullName: null,
        createGroupId: null,
        createGroupName: null,
        createOrgId: null,
        createOrgName: null,
        createTime: null,
        updateTime: null,
        typeCode: null,
        typeName: null
      }
      this.detailRow = {
        id: this.utils.createUUID(),
        lawyerFirm: other.lawyerFirm, // 律所名称
        lawyerFirmId: other.id, // 律所ID
        registerAddress: other.registerAddress, // 注册地址/律所地址
        functionary: other.functionary, // 负责人
        contactPerson: other.contactPerson, // 联系人
        phone: other.phone, // 联系电话
        fas: other.fas, // 传真
        email: other.email, // 邮箱
        postalCode: other.postalCode, // 邮政编码
        issuingAuthority: other.issuingAuthority, // 主管机关
        licenseCode: other.licenseCode, // 统一社会信用代码
        licenseNumber: other.licenseNumber, // 批准文号
        registeredCapital: other.registeredCapital, // 设立资产
        foundTime: other.foundTime, // 成立时间
        workNumber: other.workNumber, // 执业人数
        applyDeptName: other.applyDeptName, // 申请部门/所属公司
        applyDeptId: other.applyDeptId, // 申请部门/所属公司
        annualInspectionName: other.annualInspectionName, // 年检情况
        annualInspectionId: other.annualInspectionId, // 年检情况
        noResponseTimes: other.noResponseTimes, // 未响应次数
        whetherMy: 'no', // 是否占住（yes-占住；no-未占住）
        parentId: this.mainData.id, // 父ID
        beGoodAtDomain: other.beGoodAtDomain, // 擅长领域
        beGoodAtDomainIds: other.beGoodAtDomainIds, // 擅长领域
        createOgnId: orgutil.currentOgnId,
        createOgnName: orgutil.currentOgnName,
        createDeptId: orgutil.currentDeptId,
        createDeptName: orgutil.currentDeptName,
        createPsnId: orgutil.currentPsnId,
        createPsnName: orgutil.currentPsnName,
        createPsnFullId: orgutil.currentPsnFullId,
        createPsnFullName: orgutil.currentPsnFullName,
        createGroupId: orgutil.currentGroupId,
        createGroupName: orgutil.currentGroupName,
        createOrgId: orgutil.currentOrgId,
        createOrgName: orgutil.currentOrgName,
        createTime: new Date(),
        updateTime: new Date()
      }
    },
    cancel_() {
      this.orgVisible = false
    },
    choiceDeptSure_() {
      let c = '';
      let cid = '';
      this.zxcheckedData.forEach((item) => {
        if (c.length === 0) {
          c = c + item.orgName
          cid = cid + item.id
        } else {
          c = c + ',' + item.orgName
          cid = cid + ',' + item.id
        }
      })
      this.mainData.applyDeptName = c
      this.mainData.applyDeptId = cid
      this.orgVisible = false
    },
    isHeadquarter() {
      // const createOgnId = this.orgContext.currentOgnId
      //
      // if (createOgnId === '15033708970596') {
      this.typeCode = '1'
      this.typeName = '推荐库'
      // } else {
      //   this.typeCode = '0'
      //   this.typeName = '资源库'
      // }
    },
    stopClick() {
      this.$confirm('您确定要终止当前流程吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.obj.processInstanceId = this.$route.query.processInstanceId
        this.obj.taskId = this.$route.query.taskId
        this.obj.title = '出库审批'
        this.obj.functionName = '律所审批'
        this.obj.code = 'stop'
        let pathname = window.location.pathname
        new Promise((resolve) => {
          let processInstanceId = this.$route.query.processInstanceId
          processApi.end(processInstanceId).then(res => {
            resolve(res)
          })
        }).then(val => {
          if (val.data.code === 200) {
            this.obj.functionCode = 'firm_out_main'
            processApi.sendOATask(this.obj).then(() => {
              if (pathname === '/base/design_page') {
                this.mcpLayout.closeTab()
              } else {
                window.close()
              }
            })
          }
        })
      }).catch(() => {
        this.$message.info('已取消删除!')
      })
    },
    finishClick() {
      this.$confirm('您确定要结束当前流程吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.obj.processInstanceId = this.$route.query.processInstanceId
        this.obj.taskId = this.$route.query.taskId
        this.obj.title = '出库审批'
        this.obj.functionName = '律所审批'
        this.obj.code = 'pass'
        let pathname = window.location.pathname
        new Promise((resolve) => {
          processApi.move({
            proInstId: this.$route.query.processInstanceId,
            taskId: this.$route.query.taskId
          }).then(res => {
            resolve(res)
          })
        }).then(val => {
          if (val.status === 200) {
            this.obj.functionCode = 'firm_out_main'
            processApi.sendOATask(this.obj).then(() => {
              if (pathname === '/base/design_page') {
                this.mcpLayout.closeTab()
              } else {
                window.close()
              }
            })
          }
        })
      }).catch(() => {
        this.$message.info('已取消删除!')
      })
    },
    // 发起知会
    noticeClick(cooperateFunc) {
      //必传参数
      this.noticeParams = {
        ...this.utils.routeState.VIEW(this.mainData.id),//主要作用是后台逻辑需要，其他作用各业务看情况使用
        processInstanceId: this.$route.query.processInstanceId,//流程实例
        taskId: this.$route.query.taskId,//任务ID
        businessKey: this.mainData.id, //业务数据ID
        entranceType: "FLOWABLE", //流程特定的标识
        type: "haveRead",//已处理
        whetherProcess: true,//系统内部打开的时候判断是否是流程，通过不同的方式打开查看
        isNotice: true//告知是知会消息打开的功能，有些按钮可以隐藏,只有知会消息，在created钩子函数中才执行相关，如果是流程抄送不需要传，可以根据上面created描述判断
      }
      this.noticeData.dataId = this.mainData.id
      this.noticeData.moduleName = '出库审批'
      this.noticeData.params = this.noticeParams
      this.noticeData.url = 'firm_out_main'//这个需要与功能维护中的值一样
      this.noticeData.title = '出库审批'
      cooperateFunc(this.noticeData)
    },
    detailClick() {
      const tabId = this.utils.createUUID();
      this.layout.openNewTab(
          "出库信息",
          'firm_out_main_detail',
          'firm_out_main_detail',
          tabId,
          {
            functionId: 'firm_out_main_detail' + "," + tabId,
            ...this.utils.routeState.VIEW(this.mainData.id)
          }
      )
    },
    approvalClick() {
      const me = this
      if (this.mainData.dataStateCode !== this.utils.dataState_BPM.SAVE.code) {
        taskApi.selectTaskId({businessKey: this.mainData.id, isView: 'true'}).then(res => {
          const functionId = res.data.data[0].ID
          const tabId = this.utils.createUUID()

          let urlParam = {
            processInstanceId: res.data.data[0].PID,//流程实例
            taskId: res.data.data[0].ID,//任务ID
            businessKey: this.mainData.id, //业务数据ID
            functionId: functionId, //不传也可以，只有首环节需要传，实例启动就可以不传了
            entranceType: "FLOWABLE",
            type: "haveDealt",
            channel: 'business', //查看渠道，默认就传这个值即可，表示是从业务功能传过来的，首环节提交后过滤掉撤回按钮
            view: 'new',
          }

          if (me.$route.query.origin !== undefined && me.$route.query.origin === 'oa') {
            me.$set(urlParam, "origin", "oa")
            me.$set(urlParam, "fullScreen", "true")
          }

          this.layout.openNewTab("审批信息",
              "design_page",
              "design_page",
              tabId,
              urlParam
          )
        })
      } else {
        taskApi.selectFunctionId({functionCode: 'firm_out_main'}).then(res => {
          const functionId = res.data.data[0].ID
          const tabId = this.utils.createUUID()

          let urlParam = {
            processDefinitionKey: this.mcpDesignPage.processKey,
            functionId: functionId,
            entranceType: "FLOWABLE",
            ...this.utils.routeState.VIEW(this.mainData.id),
            channel: 'business',
            view: 'new',
          }

          if (me.$route.query.origin !== undefined && me.$route.query.origin === 'oa') {
            this.$set(urlParam, "origin", "oa")
            this.$set(urlParam, "fullScreen", "true")
          }

          this.layout.openNewTab("审批信息",
              "design_page",
              "design_page",
              tabId,
              urlParam
          )
        })
      }
    },
    printClick() {
      this.$print(this.$refs.dataForm)
    },
    approvalOpinionIs() {
      orgApi.roleCheck({orgId: this.orgContext.currentOrgId, roleName: 'LDYJ'}).then(res => {
        console.log('approvalOpinionIs：', res)
        this.approvalIs = !(this.type !== 'toDeal' || res.data.data === false)
      })
    },
  }
}
</script>

<style scoped>

.el-dialog-div {
  height: 60vh;
  overflow: auto;
}
</style>
