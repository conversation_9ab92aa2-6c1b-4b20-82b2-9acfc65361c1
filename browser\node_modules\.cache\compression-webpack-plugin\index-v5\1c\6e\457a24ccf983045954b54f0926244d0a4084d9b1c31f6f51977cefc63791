
3caa2c31e1d2717476d41462f8da52e839dd4301	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.284.1754018536329.js\",\"contentHash\":\"ba3d548f1f0c3cefece6a07bb82c751c\"}","integrity":"sha512-Mw175baYBUIBOUohOL0AOenqEA1PHRzIzCOiakh72ThINn8EkL/dZPqmMo0UgrLrOecMQopFvyxDzkNvKYl57g==","time":1754018575963,"size":97113}