
5b21c846d60dbdc632deb1e77b22299c702c5f3b	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.277.1754018536329.js\",\"contentHash\":\"728c9bc38cdd33f81fb2c46983cae936\"}","integrity":"sha512-NYQGnM4n6f98EegoLYRCEVwGy1YxIhUtuKPlT+zhWMm8cMoVp6Vjvgnj/Tgb12zcDDMiMM921eOrVVmrauu5Qg==","time":1754018575963,"size":106780}