<template>
  <el-container direction="vertical" class="container-manage-sg">
    <el-header>
      <el-card>
        <el-input
          v-model="temp.fuzzyValue"
          class="filter_input"
          placeholder="检索条件（订单号、商品名称、订单状态）"
          clearable
          @keyup.enter.native="refreshData"
          @clear="refreshData"
        >
          <el-popover slot="prepend" placement="bottom-start" width="800" trigger="click">
            <el-form ref="queryForm" label-width="100px" size="mini">
              <el-row>
                <el-col :span="12">
                  <el-form-item label="订单号">
                    <el-input v-model="temp.orderId" placeholder="请输入订单号..." style="width:100%" clearable/>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="商品ID">
                    <el-input v-model="temp.productId" placeholder="请输入商品ID..." style="width:100%" clearable/>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="12">
                  <el-form-item label="商品名称">
                    <el-input v-model="temp.productName" placeholder="请输入商品名称..." style="width:100%" clearable/>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="订单状态">
                    <el-select v-model="temp.orderStatus" placeholder="请选择订单状态..." style="width:100%" clearable>
                      <el-option label="待处理" value="PENDING"></el-option>
                      <el-option label="已确认" value="CONFIRMED"></el-option>
                      <el-option label="已拒绝" value="REJECTED"></el-option>
                      <el-option label="已完成" value="COMPLETED"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="12">
                  <el-form-item label="创建时间">
                    <el-date-picker
                      v-model="temp.createTimeRange"
                      type="daterange"
                      range-separator="至"
                      start-placeholder="开始日期"
                      end-placeholder="结束日期"
                      style="width:100%"
                      clearable>
                    </el-date-picker>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-button-group style="float: right">
                <el-button type="primary" size="mini" icon="el-icon-search" @click="search">搜索</el-button>
                <el-button type="primary" size="mini" icon="el-icon-refresh-left" @click="resetSearch">重置</el-button>
              </el-button-group>
            </el-form>
            <el-button slot="reference" size="mini" type="primary">高级检索</el-button>
          </el-popover>
          <el-button slot="append" icon="el-icon-search" @click="search"/>
        </el-input>
      </el-card>
    </el-header>
    
    <el-main>
      <div style="width:100%">
        <div class="table-header">
          <span class="table-title">订单管理列表</span>
          <div class="table-tools">
            <el-button type="primary" size="mini" icon="el-icon-plus" @click="showAddDialog">新增订单</el-button>
            <el-button type="success" size="mini" icon="el-icon-check" @click="batchConfirm" :disabled="selectedRows.length === 0">批量确认</el-button>
            <el-button size="mini" icon="el-icon-download" @click="exportData">导出</el-button>
            <el-button size="mini" icon="el-icon-refresh" @click="refreshData">刷新</el-button>
          </div>
        </div>
        
        <el-table
          ref="table"
          :data="tableData"
          border
          :show-overflow-tooltip="true"
          style="width: 100%; margin-top: 10px;"
          :height="tableHeight"
          stripe
          fit
          highlight-current-row
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" align="center"/>
          <el-table-column label="序号" type="index" align="center" width="60"/>
          <el-table-column label="订单号" prop="orderId" align="center" width="150" show-overflow-tooltip/>
          <el-table-column label="商品ID" prop="productId" align="center" width="120"/>
          <el-table-column label="商品名称" prop="productName" align="center" min-width="150" show-overflow-tooltip/>
          <el-table-column label="订单数量" prop="quantity" align="center" width="100">
            <template slot-scope="scope">
              {{ scope.row.quantity }} {{ scope.row.unit }}
            </template>
          </el-table-column>
          <el-table-column label="订单状态" prop="orderStatus" align="center" width="100">
            <template slot-scope="scope">
              <el-tag :type="getStatusType(scope.row.orderStatus)">
                {{ getStatusText(scope.row.orderStatus) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="创建时间" prop="createTime" align="center" width="150">
            <template slot-scope="scope">
              {{ formatDate(scope.row.createTime) }}
            </template>
          </el-table-column>
          <el-table-column label="创建人" prop="createPsnName" align="center" width="100"/>
          <el-table-column label="操作" align="center" width="200" fixed="right">
            <template slot-scope="scope">
              <el-button type="text" size="mini" @click="viewDetail(scope.row)" icon="el-icon-view">查看</el-button>
              <el-button 
                v-if="scope.row.orderStatus === 'PENDING'" 
                type="text" 
                size="mini" 
                @click="confirmOrder(scope.row)" 
                icon="el-icon-check">
                确认
              </el-button>
              <el-button 
                v-if="scope.row.orderStatus === 'PENDING'" 
                type="text" 
                size="mini" 
                @click="editOrder(scope.row)" 
                icon="el-icon-edit">
                编辑
              </el-button>
              <el-button 
                v-if="scope.row.orderStatus === 'PENDING'" 
                type="text" 
                size="mini" 
                @click="deleteOrder(scope.row)" 
                icon="el-icon-delete"
                style="color: #f56c6c;">
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        
        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="page.current"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="page.size"
            layout="total, sizes, prev, pager, next, jumper"
            :total="page.total">
          </el-pagination>
        </div>
    </div>
    </el-main>

    <!-- 新增/编辑订单对话框 -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="600px" @close="closeDialog">
      <el-form ref="orderForm" :model="orderForm" :rules="orderRules" label-width="100px">
        <el-form-item label="商品ID" prop="productId">
          <el-select
            v-model="orderForm.productId"
            placeholder="请选择商品"
            filterable
            style="width: 100%"
            @change="onProductChange">
            <el-option
              v-for="item in productList"
              :key="item.productId"
              :label="item.productId + ' - ' + item.productName"
              :value="item.productId">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="商品名称">
          <el-input v-model="orderForm.productName" readonly/>
        </el-form-item>
        <el-form-item label="当前库存">
          <span style="color: #909399;">{{ currentStock }} {{ stockUnit }}</span>
        </el-form-item>
        <el-form-item label="订单数量" prop="quantity">
          <el-input-number
            v-model="orderForm.quantity"
            :min="0.01"
            :step="0.01"
            :precision="2"
            style="width: 100%"
            placeholder="请输入订单数量">
          </el-input-number>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitOrder" :loading="submitting">确定</el-button>
      </div>
    </el-dialog>

    <!-- 订单详情对话框 -->
    <el-dialog title="订单详情" :visible.sync="detailVisible" width="600px">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="订单号">{{ orderDetail.orderId }}</el-descriptions-item>
        <el-descriptions-item label="商品ID">{{ orderDetail.productId }}</el-descriptions-item>
        <el-descriptions-item label="商品名称">{{ orderDetail.productName }}</el-descriptions-item>
        <el-descriptions-item label="订单数量">{{ orderDetail.quantity }} {{ orderDetail.unit }}</el-descriptions-item>
        <el-descriptions-item label="订单状态">
          <el-tag :type="getStatusType(orderDetail.orderStatus)">
            {{ getStatusText(orderDetail.orderStatus) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ formatDate(orderDetail.createTime) }}</el-descriptions-item>
        <el-descriptions-item label="创建人">{{ orderDetail.createPsnName }}</el-descriptions-item>
        <el-descriptions-item label="更新时间">{{ formatDate(orderDetail.updateTime) }}</el-descriptions-item>
      </el-descriptions>
    </el-dialog>
  </el-container>
</template>

<script>
import OrderAPI from '@/api/Order/Order'
import InventoryAPI from '@/api/Inventory/Inventory'

export default {
  name: 'OrderManagement',
  data() {
    return {
      loading: false,
      tableData: [],
      selectedRows: [],
      tableHeight: window.innerHeight - 300,
      
      // 搜索条件
      temp: {
        fuzzyValue: '',
        orderId: '',
        productId: '',
        productName: '',
        orderStatus: '',
        createTimeRange: []
      },
      
      // 分页
      page: {
        current: 1,
        size: 20,
        total: 0
      },
      
      // 对话框
      dialogVisible: false,
      detailVisible: false,
      dialogTitle: '新增订单',
      submitting: false,
      
      // 表单
      orderForm: {
        id: '',
        productId: '',
        productName: '',
        quantity: 1
      },
      orderRules: {
        productId: [
          { required: true, message: '请选择商品', trigger: 'change' }
        ],
        quantity: [
          { required: true, message: '请输入订单数量', trigger: 'blur' },
          { type: 'number', min: 0.01, message: '数量必须大于0', trigger: 'blur' }
        ]
      },
      
      // 其他数据
      orderDetail: {},
      productList: [],
      currentStock: 0,
      stockUnit: ''
    }
  },
  
  computed: {
    
  },
  
  created() {
    this.loadData()
    this.loadProductList()
  },
  
  mounted() {
    window.addEventListener('resize', this.handleResize)
  },
  
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize)
  },
  
  methods: {
    // 加载数据
    async loadData() {
      this.loading = true
      try {
        const params = {
          current: this.page.current,
          size: this.page.size,
          ...this.getSearchParams()
        }
        
        // 使用API接口调用
        const response = await OrderAPI.getOrdersByStatus('ALL', params)
        if (response.data.success) {
          this.tableData = response.data.data || []
          this.page.total = response.data.total || 0
        } else {
          this.$message.error(response.data.message || '加载数据失败')
        }
      } catch (error) {
        console.error('加载数据失败:', error)
        this.$message.error('加载数据失败')
      } finally {
        this.loading = false
      }
    },
    
    // 加载商品列表
    async loadProductList() {
      try {
        const response = await InventoryAPI.getAllInventory()
        if (response.data.success) {
          this.productList = response.data.data || []
        }
      } catch (error) {
        console.error('加载商品列表失败:', error)
      }
    },
    
    // 获取搜索参数
    getSearchParams() {
      const params = {}
      if (this.temp.fuzzyValue) params.fuzzyValue = this.temp.fuzzyValue
      if (this.temp.orderId) params.orderId = this.temp.orderId
      if (this.temp.productId) params.productId = this.temp.productId
      if (this.temp.productName) params.productName = this.temp.productName
      if (this.temp.orderStatus) params.orderStatus = this.temp.orderStatus
      if (this.temp.createTimeRange && this.temp.createTimeRange.length === 2) {
        params.startTime = this.temp.createTimeRange[0]
        params.endTime = this.temp.createTimeRange[1]
      }
      return params
    },
    
    // 搜索
    search() {
      this.page.current = 1
      this.loadData()
    },
    
    // 重置搜索
    resetSearch() {
      this.temp = {
        fuzzyValue: '',
        orderId: '',
        productId: '',
        productName: '',
        orderStatus: '',
        createTimeRange: []
      }
      this.search()
    },
    
    // 刷新数据
    refreshData() {
      this.loadData()
    },
    
    // 分页
    handleSizeChange(val) {
      this.page.size = val
      this.page.current = 1
      this.loadData()
    },
    
    handleCurrentChange(val) {
      this.page.current = val
      this.loadData()
    },
    
    // 表格选择
    handleSelectionChange(selection) {
      this.selectedRows = selection
    },
    
    // 显示新增对话框
    showAddDialog() {
      this.dialogTitle = '新增订单'
      this.orderForm = {
        id: '',
        productId: '',
        productName: '',
        quantity: 1
      }
      this.currentStock = 0
      this.stockUnit = ''
      this.dialogVisible = true
    },
    
    // 编辑订单
    editOrder(row) {
      this.dialogTitle = '编辑订单'
      this.orderForm = { ...row }
      this.onProductChange(row.productId)
      this.dialogVisible = true
    },
    
    // 商品选择变化
    async onProductChange(productId) {
      if (productId) {
        const product = this.productList.find(p => p.productId === productId)
        if (product) {
          this.orderForm.productName = product.productName
          this.currentStock = product.quantity
          this.stockUnit = product.unit
        }
      }
    },
    
    // 提交订单
    async submitOrder() {
      this.$refs.orderForm.validate(async (valid) => {
        if (valid) {
          this.submitting = true
          try {
            const response = await OrderAPI.submitOrder(this.orderForm)
            
            if (response.data.success) {
              this.$message.success(response.data.message || '操作成功')
              this.dialogVisible = false
              this.loadData()
            } else {
              this.$message.error(response.data.message || '操作失败')
            }
          } catch (error) {
            console.error('提交订单失败:', error)
            this.$message.error('提交订单失败')
          } finally {
            this.submitting = false
          }
        }
      })
    },
    
    // 确认订单
    async confirmOrder(row) {
      this.$confirm('确认要确认此订单吗？确认后将自动扣减库存。', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          const response = await OrderAPI.confirmOrder(row.orderId)
          if (response.data.success) {
            this.$message.success(response.data.message || '确认成功')
            this.loadData()
          } else {
            this.$message.error(response.data.message || '确认失败')
          }
        } catch (error) {
          console.error('确认订单失败:', error)
          this.$message.error('确认订单失败')
        }
      })
    },
    
    // 批量确认
    async batchConfirm() {
      const pendingOrders = this.selectedRows.filter(row => row.orderStatus === 'PENDING')
      if (pendingOrders.length === 0) {
        this.$message.warning('请选择待处理状态的订单')
        return
      }
      
      this.$confirm(`确认要批量确认选中的 ${pendingOrders.length} 个订单吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          const promises = pendingOrders.map(order => 
            OrderAPI.confirmOrder(order.orderId)
          )
          await Promise.all(promises)
          this.$message.success('批量确认成功')
          this.loadData()
        } catch (error) {
          console.error('批量确认失败:', error)
          this.$message.error('批量确认失败')
        }
      })
    },
    
    // 删除订单
    deleteOrder(row) {
      this.$confirm('确认要删除此订单吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          const response = await OrderAPI.deleteOrder(row.orderId)
          if (response.data.success) {
            this.$message.success('删除成功')
            this.loadData()
          } else {
            this.$message.error(response.data.message || '删除失败')
          }
        } catch (error) {
          console.error('删除订单失败:', error)
          this.$message.error('删除订单失败')
        }
      })
    },
    
    // 查看详情
    viewDetail(row) {
      this.orderDetail = { ...row }
      this.detailVisible = true
    },
    
    // 导出数据
    exportData() {
      this.$message.info('导出功能开发中...')
    },
    
    // 关闭对话框
    closeDialog() {
      this.$refs.orderForm.resetFields()
    },
    
    // 获取状态类型
    getStatusType(status) {
      const typeMap = {
        'PENDING': 'warning',
        'CONFIRMED': 'success',
        'REJECTED': 'danger',
        'COMPLETED': 'info'
      }
      return typeMap[status] || 'info'
    },
    
    // 获取状态文本
    getStatusText(status) {
      const textMap = {
        'PENDING': '待处理',
        'CONFIRMED': '已确认',
        'REJECTED': '已拒绝',
        'COMPLETED': '已完成'
      }
      return textMap[status] || status
    },
    
    // 格式化日期
    formatDate(date) {
      if (!date) return ''
      return new Date(date).toLocaleString('zh-CN')
    },
    
    // 窗口大小变化
    handleResize() {
      this.tableHeight = window.innerHeight - 300
    }
  }
}
</script>

<style scoped>
.container-manage-sg {
  height: 100vh;
  background: #f5f5f5;
}

.filter_input {
  width: 100%;
}

.table-header {
    display: flex;
  justify-content: space-between;
    align-items: center;
  margin-bottom: 10px;
}

.table-title {
    font-size: 16px;
  font-weight: bold;
  color: #303133;
}

.table-tools {
  display: flex;
  gap: 8px;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.el-header {
  padding: 20px;
  background: transparent;
}

.el-main {
  padding: 0 20px 20px;
}

.el-card {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.dialog-footer {
  text-align: right;
}

.el-table {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

/* 覆盖element-ui的一些样式 */
.el-button--text {
  color: #409EFF;
}

.el-button--text:hover {
  color: #66b1ff;
}

.el-tag {
  border: none;
}
</style>