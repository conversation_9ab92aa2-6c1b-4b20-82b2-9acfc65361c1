package com.klaw.service.imp.complianceRiskServiceImp;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.klaw.dao.complianceRiskDao.ProcessControlListMapper;
import com.klaw.entity.complianceRiskBean.ProcessControlList;
import com.klaw.service.complianceRiskService.ProcessControlListService;
import com.klaw.utils.DataAuthUtils;
import com.klaw.utils.PageUtils;
import com.klaw.vo.Json;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

@Service
public class ProcessControlListServiceImpl extends ServiceImpl<ProcessControlListMapper, ProcessControlList> implements ProcessControlListService {

    @Override
    public void saveData(ProcessControlList processControlList) {
        processControlList.setUpdateTime(new Date());
        saveOrUpdate(processControlList);
    }


    @Override
    public Page<ProcessControlList> queryPageData(JSONObject json) {
        QueryWrapper<ProcessControlList> wrapper = new QueryWrapper<>();
        getFilter(json, wrapper);
        return page(new PageUtils<>(json), wrapper);
    }


    public void getFilter(JSONObject json, QueryWrapper<ProcessControlList> wrapper){
        //是否台账功能（登记只查自己的，台账和弹框查自己和数据权限的）
        boolean isQuery = json.containsKey("isQuery") ? json.getBoolean("isQuery") : false;
        String orgId = json.containsKey("orgId") ? json.getString("orgId") : "";
        String responsibleDepartment = json.containsKey("responsibleDepartment") ? json.getString("responsibleDepartment") : null;
        String keyControlPoints = json.containsKey("keyControlPoints") ? json.getString("keyControlPoints") : null;
        String businessItems = json.containsKey("businessItems") ? json.getString("businessItems") : null;
        String fuzzyValue = json.containsKey("fuzzyValue") ? json.getString("fuzzyValue") : null;

        if (StringUtils.isNotBlank(responsibleDepartment)) {
            wrapper.like("responsible_department", responsibleDepartment);
        }
        if (StringUtils.isNotBlank(keyControlPoints)) {
            wrapper.like("key_control_points", keyControlPoints);
        }
        if (StringUtils.isNotBlank(businessItems)) {
            wrapper.like("business_items", businessItems);
        }
        if (StringUtils.isNotBlank(fuzzyValue)) {
            wrapper.and(i -> i.like("responsible_department", fuzzyValue)
                    .or().like("key_control_points", fuzzyValue)
                    .or().like("business_items", fuzzyValue))
            ;
        }
        if(isQuery){
            Long functionId = DataAuthUtils.getFunctionIdByCode("lcgkqd_ledger_index");
            DataAuthUtils.dataPermSql(wrapper, null, functionId, orgId);
            wrapper.eq("data_state", "已提交");
        }else{
            wrapper.eq("create_org_id", orgId);
        }
        wrapper.orderByDesc("create_time");
    }

    @Override
    public ProcessControlList queryDataById(String id) {
        ProcessControlList processControlList = getById(id);
        return processControlList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Json deleteDataById(String id) {
        boolean flag = removeById(id);
        if (!flag) {
            return Json.fail().msg("未找到需要删除的数据");
        }
        return Json.succ().msg("删除成功!");
    }
}
