
4cde94a08417fe7611a35ebfd2491e3be3e50bcc	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.221.1754018536329.js\",\"contentHash\":\"2a1501f1da6ec01877658da6d631c923\"}","integrity":"sha512-nEl/sNC5WG5ud1PcOU+RwMUNBn/QtS+r9NvFHS3uIWds0+qvl1Ob2drJIRrvSDmz8C8XB4OJzQo+duEsyrWLVA==","time":1754018576057,"size":182357}