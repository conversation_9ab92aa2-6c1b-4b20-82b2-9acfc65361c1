
ebf28217cc35414aea4326f1e6924a9c9c7a9d38	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.325.1754018536329.js\",\"contentHash\":\"12ed9bb4c177ad31428e2ee92846132c\"}","integrity":"sha512-2ohMXxdxTxPn7hJsjkGt3uhwzltvu16BuK/eDvn28+TYOoj9GUiaq56Kr13sMWBDYZfmxhGw7nB2I4+qGnnVgw==","time":1754018576017,"size":158905}