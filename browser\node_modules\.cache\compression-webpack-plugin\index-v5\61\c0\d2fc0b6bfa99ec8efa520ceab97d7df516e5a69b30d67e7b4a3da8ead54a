
d35a17006597efc7a008f0b1e4c4574a1560da96	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.295.1754018536329.js\",\"contentHash\":\"ac5b1427c467ea1d89247a5185edf9e1\"}","integrity":"sha512-FLbFl3qJb0J8Vu4qbQnr7hTFQ//LbAuJ4UWhpW89xk56YvGJGlVXJNh0gN62PgUN+1ypYJuDsyNn1dyfY4HhRA==","time":1754018575964,"size":111833}