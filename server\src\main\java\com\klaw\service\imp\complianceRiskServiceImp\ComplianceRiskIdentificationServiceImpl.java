package com.klaw.service.imp.complianceRiskServiceImp;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.klaw.dao.complianceRiskDao.ComplianceRiskIdentificationMapper;
import com.klaw.entity.complianceRiskBean.ComplianceRiskIdentification;
import com.klaw.service.complianceRiskService.ComplianceRiskIdentificationService;
import com.klaw.utils.DataAuthUtils;
import com.klaw.utils.PageUtils;
import com.klaw.vo.Json;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

@Service
public class ComplianceRiskIdentificationServiceImpl extends ServiceImpl<ComplianceRiskIdentificationMapper, ComplianceRiskIdentification> implements ComplianceRiskIdentificationService {
    @Autowired
    private ComplianceRiskIdentificationMapper complianceRiskIdentificationMapper;

    @Override
    public void saveData(ComplianceRiskIdentification complianceRiskIdentification) {
        complianceRiskIdentification.setUpdateTime(new Date());
        saveOrUpdate(complianceRiskIdentification);
    }


    @Override
    public Page<ComplianceRiskIdentification> queryPageData(JSONObject json) {
        QueryWrapper<ComplianceRiskIdentification> wrapper = new QueryWrapper<>();
        getFilter(json, wrapper);
        return page(new PageUtils<>(json), wrapper);
    }


    public void getFilter(JSONObject json, QueryWrapper<ComplianceRiskIdentification> wrapper){
        //是否台账功能（登记只查自己的，台账和弹框查自己和数据权限的）
        boolean isQuery = json.containsKey("isQuery") ? json.getBoolean("isQuery") : false;
        String orgId = json.containsKey("orgId") ? json.getString("orgId") : "";
        String legalLiabilityClause = json.containsKey("legalLiabilityClause") ? json.getString("legalLiabilityClause") : null;
        String regulationCategory = json.containsKey("regulationCategory") ? json.getString("regulationCategory") : null;
        String fuzzyValue = json.containsKey("fuzzyValue") ? json.getString("fuzzyValue") : null;

        if (StringUtils.isNotBlank(legalLiabilityClause)) {
            wrapper.like("legal_liability_clause", legalLiabilityClause);
        }
        if (StringUtils.isNotBlank(regulationCategory)) {
            wrapper.like("regulation_category", regulationCategory);
        }
        if (StringUtils.isNotBlank(fuzzyValue)) {
            wrapper.and(i -> i.like("legal_liability_clause", fuzzyValue)
                    .or().like("regulation_category", fuzzyValue))
            ;
        }

        if(isQuery){
        Long functionId = DataAuthUtils.getFunctionIdByCode("hgfxsb_ledger_index");
        DataAuthUtils.dataPermSql(wrapper, null, functionId, orgId);
        wrapper.eq("data_state", "已提交");
        }else{
            wrapper.eq("create_org_id", orgId);
        }
        wrapper.orderByDesc("create_time");
    }

    @Override
    public ComplianceRiskIdentification queryDataById(String id) {
        ComplianceRiskIdentification complianceRiskIdentification = getById(id);
        return complianceRiskIdentification;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Json deleteDataById(String id) {
        boolean flag = removeById(id);
        if (!flag) {
            return Json.fail().msg("未找到需要删除的数据");
        }
        return Json.succ().msg("删除成功!");
    }

}
