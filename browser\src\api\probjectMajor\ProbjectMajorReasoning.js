import {request} from '@/api/index'

export default {

    query(data) {
        return request({
            url: '/projectMajorReasoning/query',
            method: 'post',
            data
        })
    },

    save(data) {
        return request({
            url: '/projectMajorReasoning/save',
            method: 'post',
            data
        })
    },
    commit(data) {
        return request({
            url: '/projectMajorReasoning/commit',
            method: 'post',
            data
        })
    },
    getById(data) {
        return request({
            url: '/projectMajorReasoning/getById',
            method: 'post',
            data: {
                id: data
            }
        })
    },
    getByIdAndVersion(data) {
        return request({
            url: '/projectMajorReasoning/getById',
            method: 'post',
            data
        })
    },
    deleteById(data) {
        return request({
            url: '/projectMajorReasoning/deleteById',
            method: 'post',
            data

        })
    },
    getUserListByCurrentOrgId(data) {
        return request({
            url: '/projectMajorReasoning/getUserListByCurrentOrgId',
            method: 'post',
            data

        })
    },

    projectTransferUser(data){
        return request({
            url: '/projectMajorReasoning/projectTransferUser',
            method: 'post',
            data
        })
    }


}

