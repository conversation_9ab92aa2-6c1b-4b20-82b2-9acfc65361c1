<template>
	<div>
		<div v-if="!isView">
			<!--<div style="margin: 10px">
                <span style="font-weight: bold;font-size: 16px;color: #5A5A5F;">风险事件上报</span>
            </div>-->
			<span style="font-weight: bold; font-size: 16px; color: #5a5a5f">基本信息</span>
			<el-divider></el-divider>
			<el-row style="margin-top: 10px">
				<el-col :span="16">
					<el-form-item label="关联风险预警" prop="associatedRiskWarning">
						<el-input
							clearable
							disabled
							maxlength="50"
							placeholder="请输入..."
							show-word-limit
							style="width: 100%"
							v-model.trim="mainData.associatedRiskWarning"
						>
							<el-button @click="riskWaringVisible = true" icon="el-icon-search" slot="append"></el-button>
						</el-input>
					</el-form-item>
				</el-col>

				<el-col :span="8">
					<el-form-item label="风险编号" prop="riskNumber">
						<el-input disabled show-word-limit style="width: 100%" v-if="!isView" v-model.trim="mainData.riskNumber" />
					</el-form-item>
				</el-col>
			</el-row>

			<el-row>
				<el-col :span="16">
					<el-form-item label="风险名称" prop="mainData.riskName">
						<el-input
							clearable
							placeholder="请输入..."
							style="width: 100%"
							v-if="!isView"
							maxlength="100"
							show-word-limit
							v-model="mainData.riskName"
							:disabled="this.mainData.associatedRiskWarning != null"
						/>
						<span class="viewSpan" v-else>{{ mainData.riskName }}</span>
					</el-form-item>
				</el-col>

				<el-col :span="8">
					<el-form-item label="事件性质" prop="mainData.eventNature">
						<el-select disabled style="width: 100%" v-if="!isView" v-model.trim="mainData.eventNature">
							<el-option :key="item.id" :label="item.dicName" :value="item.dicCode" v-for="item in eventNatureData"></el-option>
						</el-select>
						<span class="viewSpan" v-else>{{ mainData.eventNature }}</span>
					</el-form-item>
				</el-col>
			</el-row>

			<el-row>
				<el-col :span="8">
					<el-form-item label="风险一级分类" prop="mainData.riskClassification">
						<el-select
							show-word-limit
							style="width: 100%"
							v-if="!isView"
							:disabled="this.mainData.associatedRiskWarning != null"
							v-model.trim="mainData.riskClassification"
							@change="changeRiskClassification"
						>
							<el-option :key="item.id" :label="item.dicName" :value="item.dicCode" v-for="item in riskClassificationData"></el-option>
						</el-select>
					</el-form-item>
				</el-col>
				<el-col :span="8">
					<el-form-item label="风险二级分类" prop="mainData.riskClassificationSec">
						<el-select
							show-word-limit
							style="width: 100%"
							v-if="!isView"
							:disabled="this.mainData.associatedRiskWarning != null"
							v-model.trim="mainData.riskClassificationSec"
							@change="changeRiskClassificationSec"
						>
							<el-option :key="item.id" :label="item.dicName" :value="item.dicCode" v-for="item in riskClassificationSecData"></el-option>
						</el-select>
					</el-form-item>
				</el-col>
				<el-col :span="8">
					<el-form-item label="风险等级" prop="mainData.expectedRiskLevel">
						<el-select show-word-limit style="width: 100%" v-if="!isView" v-model.trim="mainData.expectedRiskLevel">
							<el-option :key="item.id" :label="item.dicName" :value="item.dicCode" v-for="item in expectedRiskLevelData"></el-option>
						</el-select>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row>
				<el-col :span="8">
					<el-form-item label="业务领域" prop="mainData.businessDomain">
						<el-select style="width: 100%" v-if="!isView" v-model="mainData.businessDomain">
							<el-option :label="item.dicName" :value="item.dicCode" :key="item.dicCode" v-for="item in businessDomainData"></el-option>
						</el-select>
					</el-form-item>
				</el-col>
				<el-col :span="8" v-if="mainData.businessDomain == 'FX_QT'">
					<el-form-item label="业务领域" prop="mainData.businessDomain">
						<el-input v-model="mainData.otherBusinessArea" clearable placeholder="请输入业务领域..."></el-input>
					</el-form-item>
				</el-col>
				<el-col :span="8">
					<el-form-item label="涉及金额(元)" prop="mainData.involvedAmount">
						<Money :isDW="true" :isDX="true" :moneyParam.sync="mainData.involvedAmount" v-if="!isView"> </Money>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row>
				<el-col :span="24">
					<el-form-item label="风险描述" prop="mainData.riskDescription">
						<el-input
							placeholder="请输入..."
							style="width: 100%"
							:autosize="{ minRows: 3, maxRows: 6 }"
							maxlength="1000"
							show-word-limit
							type="textarea"
							v-if="!isView"
							v-model.trim="mainData.riskDescription"
						>
						</el-input>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row>
				<el-col :span="24">
					<el-form-item label="风险原因" prop="mainData.riskReason">
						<el-input
							placeholder="请输入..."
							style="width: 100%"
							:autosize="{ minRows: 3, maxRows: 6 }"
							maxlength="1000"
							show-word-limit
							type="textarea"
							v-model.trim="mainData.riskReason"
							v-if="!isView"
						>
						</el-input>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row>
				<el-col :span="24">
					<el-form-item label="监管部门" prop="mainData.receivingAgency">
						<el-input
							clearable
							placeholder="请输入..."
							:autosize="{ minRows: 3, maxRows: 5 }"
							show-word-limit
							maxlength="1000"
							type="textarea"
							style="width: 100%"
							v-if="!isView"
							v-model.trim="mainData.receivingAgency"
						>
						</el-input>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row>
				<el-col :span="24">
					<el-form-item label="相关文书" prop="mainData.uploadAttachment">
						<uploadDoc :disabled="isView" :doc-path="docURL" :files.sync="mainData.uploadAttachment" v-model="mainData.uploadAttachment" />
					</el-form-item>
				</el-col>
			</el-row>
			<simple-board
				:data-state="!isView"
				:has-value="true"
				:hasAdd="true"
				:hasDle="currentRow != null"
				:hasDown="currentRow != null"
				:hasUp="currentRow != null"
				:title="'涉及法律义务条款及相关法律责任条款'"
				style="margin-top: 10px"
				@addBtn="addRow"
				@delBtn="delRow"
				@downBtn="downRow"
				@upBtn="upRow"
			>
				<el-table
					ref="tableRef"
					:data="legalObligations"
					:show-overflow-tooltip="true"
					border
					fit
					highlight-current-row
					max-height="250"
					style="width: 100%"
					@current-change="selectRow"
				>
					<el-table-column align="center" label="序号" type="index" width="80">
						<template slot-scope="scope">
							{{ scope.$index + 1 }}
						</template>
					</el-table-column>

					<el-table-column align="center" label="法律法规名称" width="300">
						<template slot-scope="{ row, $index }">
							<el-form :ref="(el) => setFormRef(el, $index)" :model="row" :rules="rules">
								<el-form-item :style="{ marginBottom: marginBottom[$index] }" class="test" prop="lawsName" show-overflow-tooltip style="padding: 5px">
									<el-tooltip :content="`${row.lawsName?.length || 0}/200`" effect="dark" placement="top">
										<el-input
											v-model="row.lawsName"
											:autosize="{ minRows: 2 }"
											class="no-resize hide-word-limit"
											maxlength="200"
											placeholder="请输入..."
											show-word-limit
											type="textarea"
										/>
									</el-tooltip>
								</el-form-item>
							</el-form>
						</template>
					</el-table-column>

					<el-table-column align="center" label="条款内容">
						<template slot-scope="{ row, $index }">
							<el-form :ref="(el) => setFormRefContent(el, $index)" :model="row" :rules="rules">
								<el-form-item
									:style="{ marginBottom: marginBottomContent[$index] }"
									class="test"
									prop="clauseContent"
									show-overflow-tooltip
									style="padding: 5px"
								>
									<el-tooltip :content="`${row.clauseContent?.length || 0}/1000`" effect="dark" placement="top">
										<el-input
											v-model="row.clauseContent"
											:autosize="{ minRows: 2 }"
											class="no-resize hide-word-limit"
											maxlength="1000"
											placeholder="请输入..."
											show-word-limit
											type="textarea"
										/>
									</el-tooltip>
								</el-form-item>
							</el-form>
						</template>
					</el-table-column>
				</el-table>
			</simple-board>
		</div>

		<selectCurrentUnit
			:dialog-visible.sync="orgTreeCurrentDeptDialog"
			:is-checked-user="false"
			:isCheck="true"
			:selectDept="true"
			:show-group="false"
			:show-user="false"
			:title="'部门信息'"
			@sureDeptBtn="deptSelect"
		>
		</selectCurrentUnit>
		<div v-if="isView">
			<SimpleBoard style="margin-top: 10px" title="基本信息" :hasAdd="false">
				<table class="table_content" style="margin-top: 10px">
					<tbody>
						<tr>
							<th class="th_label" colspan="3">关联风险预警</th>
							<td class="td_value" colspan="9">{{ mainData.associatedRiskWarning }}</td>
							<th class="th_label" colspan="3">风险编号</th>
							<td class="td_value" colspan="9">{{ mainData.riskNumber }}</td>
						</tr>
						<tr>
							<th class="th_label" colspan="3">风险名称</th>
							<td class="td_value" colspan="9">{{ mainData.riskName }}</td>
							<th class="th_label" colspan="3">事件性质</th>
							<td class="td_value" colspan="9">{{ mainData.eventNature }}</td>
						</tr>
<tr>
  <th class="th_label" colspan="3">风险一级分类</th>
  <td class="td_value" colspan="9">{{ getDicName('riskClassification', mainData.riskClassification) }}</td>
  <th class="th_label" colspan="3">风险二级分类</th>
  <td class="td_value" colspan="9">{{ getDicName('riskClassificationSec', mainData.riskClassificationSec) }}</td>
</tr>
						<tr>
							<th class="th_label" colspan="3">风险等级</th>
							<td class="td_value" colspan="9">{{ mainData.expectedRiskLevelName }}</td>
							<th class="th_label" colspan="3">业务领域</th>
							<td class="td_value" colspan="9">{{ mainData.businessDomainName }}</td>
						</tr>
						<tr>
							<th class="th_label" colspan="3" v-if="mainData.businessDomain == 'FX_QT'">业务领域</th>
							<td class="td_value" colspan="9" v-if="mainData.businessDomain == 'FX_QT'">{{ mainData.otherBusinessArea }}</td>
							<th class="th_label" colspan="3" v-if="mainData.businessDomain == 'FX_QT'">风险等级</th>
							<td class="td_value" colspan="9" v-if="mainData.businessDomain == 'FX_QT'">{{ mainData.expectedRiskLevelName }}</td>
							<!-- <th class="th_label" colspan="3" v-if="mainData.businessDomain != 'FX_QT'">风险等级</th>
							<td class="td_value" colspan="21" v-if="mainData.businessDomain != 'FX_QT'">{{ mainData.expectedRiskLevelName }}</td> -->
						</tr>
						<tr>
							<th class="th_label" colspan="3">风险描述</th>
							<td class="td_value" colspan="21">{{ mainData.riskDescription }}</td>
						</tr>
						<tr>
							<th class="th_label" colspan="3">风险原因</th>
							<td class="td_value" colspan="21">{{ mainData.riskReason }}</td>
						</tr>
						<tr>
							<th class="th_label" colspan="3">监管部门</th>
							<td class="td_value" colspan="21">{{ mainData.receivingAgency }}</td>
						</tr>
						<tr>
							<th class="th_label" colspan="3">相关文书</th>
							<td class="td_value" colspan="21">
								<uploadDoc :disabled="isView" :doc-path="docURL" :files.sync="mainData.uploadAttachment" v-model="mainData.uploadAttachment" />
							</td>
						</tr>
					</tbody>
				</table>
				<!-- <div style="font-weight: bold;font-size: 16px;color: #5A5A5F;margin:10px 0px">涉及法律义务条款及相关法律责任条款</div>
                <el-divider></el-divider>
                <el-table :data="legalObligations"  :show-overflow-tooltip="true"
                     border fit  ref="table" stripe
                    style="table-layout: fixed;width: 100%;">
                    <el-table-column align="center" label="序号" type="index"></el-table-column>
                    <el-table-column align="center" label="法律法规名称" prop="lawsName" show-overflow-tooltip width="300">
                    </el-table-column>
                    <el-table-column align="center" label="条款内容" prop="clauseContent" show-overflow-tooltip>
                    </el-table-column>
                </el-table> -->
			</SimpleBoard>
			<SimpleBoard style="margin-top: 10px" title="涉及法律义务条款及相关法律责任条款" :hasAdd="false">
				<el-divider></el-divider>
				<el-table :data="legalObligations" :show-overflow-tooltip="true" border fit ref="table" stripe style="table-layout: fixed; width: 100%">
					<el-table-column align="center" label="序号" type="index"></el-table-column>
					<el-table-column align="center" label="法律法规名称" prop="lawsName" show-overflow-tooltip width="300"> </el-table-column>
					<el-table-column align="center" label="条款内容" prop="clauseContent" show-overflow-tooltip> </el-table-column>
				</el-table>
			</SimpleBoard>
		</div>

		<el-dialog :close-on-click-modal="false" :visible.sync="orgVisible" title="选择部门" width="50%">
			<div class="el-dialog-div">
				<orgTree
					:accordion="false"
					:checked-data.sync="zxcheckedData"
					:is-check="false"
					:is-filter="true"
					:is-not-cascade="true"
					:is-ogn="true"
					:otherHeight="280"
					:show-user="false"
				/>
			</div>
			<span class="dialog-footer" slot="footer">
				<el-button @click="orgVisible = false" class="negative-btn" icon="">取消</el-button>
				<el-button @click="orgSure_" class="active-btn" icon="" type="primary">确定</el-button>
			</span>
		</el-dialog>
		<RiskWaringDialog :riskWaringDialogVisible.sync="riskWaringVisible" @riskWaringSure_="riskWaringSure_"> </RiskWaringDialog>
	</div>
</template>

<script>
	import SimpleBoard from '@/view/components/SimpleBoard/SimpleBoardViewCase.vue';
	// import SimpleBoard from '@/view/components/SimpleBoard/SimpleBoardTitle'
	import Money from '@/view/components/Money/index';
	import RiskWaringDialog from './RiskWaringDialog';
	import selectCurrentUnit from '@/view/components/OrgSingleDialogSelect/selectCurrentUnit';
	import uploadDoc from '@/view/components/UploadDoc/UploadDoc';
	import orgTree from '@/view/components/OrgTree/OrgTree';
	import dictApi from '@/api/_system/dict';
	import orgApi from '@/api/_system/org';
	export default {
		name: 'FXSB',
		components: {
			SimpleBoard,
			Money,
			selectCurrentUnit,
			uploadDoc,
			orgTree,
			RiskWaringDialog,
		},
		props: {
			dataState: {
				type: String,
				default: '',
			},
			isView: {
				type: Boolean,
				default: false,
			},
			mainData: {
				type: Object,
				default: function () {
					return {};
				},
			},
			legalObligations: {
				type: Array,
				default: [],
			},
			rules: {
				type: Object,
				default: function () {
					return {};
				},
			},
		},
		watch: {
			'mainData.expectedRiskLevel'() {
				let obj = this.expectedRiskLevelData.find((item) => {
					return item.dicCode == this.mainData.expectedRiskLevel;
				});
				if (obj != null) {
					this.mainData.expectedRiskLevelName = obj.dicName;
				}
			},
			'mainData.businessDomain'() {
				let obj = this.businessDomainData.find((item) => {
					return item.dicCode == this.mainData.businessDomain;
				});
				if (obj != null) {
					this.mainData.businessDomainName = obj.dicName;
				}
			},
			'mainData.riskClassification'() {
				dictApi
					.showSelect({
						dicCode: this.mainData.riskClassification + '',
					})
					.then((response) => {
						this.riskClassificationSecData = response.data.data;
					});
			},
		},
		data() {
			return {
				inputHeight: 50, // 普通 input 的初始高度
				riskWaringVisible: false,
				currentRow: {},
				eventNatureData: [],
				expectedRiskLevelData: [],
				businessDomainData: [],
				zxcheckedData: [],
				docURL: '/complianceRiskWaring',
				rules: [],
				punishTypeData: [],
				rectificationData: [],
				endModeData: [],
				title: '',
				hasAdd: true,
				isHas: true,
				orgTreeCurrentUnitDialog: false,
				orgTreeCurrentDeptDialog: false,
				index: -1,
				orgVisible: false,
				riskClassificationData: [],
				riskClassificationSecData: [],
				marginBottom: [],
				marginBottomContent: [],
				formRefs: {},
				formRefsContent: {},
				currentRow: null,
				currentIndex: null,
			};
		},
		created() {
			this.baseDataLoad();
		},
		methods: {
			changeRiskClassification() {
				this.riskClassificationData.map((data) => {
					if (this.mainData.riskClassification == data.dicCode) {
						this.mainData.riskClassificationName = data.dicName;
						this.mainData.riskClassificationSec = null;
						this.mainData.riskClassificationSecName = null;
						dictApi
							.showSelect({
								dicCode: this.mainData.riskClassification + '',
							})
							.then((response) => {
								this.riskClassificationSecData = response.data.data;
							});
					}
				});
			},
			changeRiskClassificationSec() {
				this.riskClassificationSecData.map((data) => {
					if (this.mainData.riskClassificationSec == data.dicCode) {
						this.mainData.riskClassificationSecName = data.dicName;
					}
				});
			},
			orgSure_() {},
			baseDataLoad() {
				dictApi
					.showSelect({
						dicCode: 'businessDomainDic',
					})
					.then((response) => {
						this.businessDomainData = response.data.data;
					});
				dictApi
					.showSelect({
						dicCode: 'expectedRiskLevelDic',
					})
					.then((response) => {
						this.expectedRiskLevelData = response.data.data;
					});
				dictApi
					.showSelect({
						dicCode: 'eventNatureDic',
					})
					.then((response) => {
						this.eventNatureData = response.data.data;
					});
				dictApi
					.showSelect({
						dicCode: 'riskClassificationDic',
					})
					.then((response) => {
						this.riskClassificationData = response.data.data;
					});
			},
			orgCurrentUnitSelect(data) {
				const me = this;
				this.mainData.currentUnitId = data.unitId;
				this.mainData.currentUnit = data.name;
				orgApi.queryOrgOther({ orgId: data.unitId }).then((res) => {
					me.mainData.whetherMerge = res.data.merge;
					me.mainData.equityType = res.data.equityType;
				});
			},
			deptSelect(data) {
				let str = '';
				data.map((obj) => {
					str += obj.name + ',';
				});
				this.mainData.currentDept = str;
			},
			addRow() {
				let obj = { lawsName: '', clauseContent: '' };
				this.legalObligations.push(obj);
			},
			upRow() {
				if (this.currentRow) {
					const index = this.legalObligations.indexOf(this.currentRow);
					if (index > 0) {
						this.legalObligations.splice(index, 1);
						this.legalObligations.splice(index - 1, 0, this.currentRow);
					}
				}
			},
			downRow() {
				if (this.currentRow) {
					const index = this.legalObligations.indexOf(this.currentRow);
					if (index < this.legalObligations.length - 1) {
						this.legalObligations.splice(index, 1);
						this.legalObligations.splice(index + 1, 0, this.currentRow);
					}
				}
			},
			delRow() {
				const index = this.legalObligations.indexOf(this.currentRow);
				this.legalObligations.splice(index, 1);
				this.currentRow = {};
			},
			selectRow(row, column, event) {
				let index = this.legalObligations.indexOf(row);
				this.currentRow = row;
			},
			//选择我方签约主体
			contractUnitClick() {
				this.orgVisible = true;
			},
			getDicName(type, dicCode) {
				let obj = { dicName: '' };
				if (type == 'level') {
					obj = this.expectedRiskLevelData.find((item) => {
						return item.dicCode == dicCode;
					});
				} else if (type == 'businessMan') {
					obj = this.businessDomainData.find((item) => {
						return item.dicCode == dicCode;
					});
				} else if (type == 'riskClassification') {
					obj = this.riskClassificationData.find((item) => {
						return item.dicCode == dicCode;
					});
				} else if (type == 'riskClassificationSec') {
					obj = this.riskClassificationSecData.find((item) => {
						return item.dicCode == dicCode;
					});
				}else if (type == 'eventNature') {
					obj = this.eventNatureData.find((item) => {
						return item.dicCode == dicCode;
					});
				}
				if (obj != null) {
					return obj.dicName;
				}
			},
			riskWaringSure_(val) {
				this.mainData.associatedRiskWarning = val.riskName + '【' + val.riskNumber + '】';
				this.mainData.riskNumber = val.riskNumber;
				this.mainData.riskName = val.riskName;
				this.mainData.riskClassification = val.riskClassification;
				this.mainData.riskClassificationSec = val.riskClassificationSec;
				this.mainData.riskDescription = val.riskDescription;
				this.mainData.riskReason = val.riskReason;
				this.riskWaringVisible = false;
			},
			setFormRef(el, index) {
				if (el) this.formRefs[`form${index}`] = el;
			},
			setFormRefContent(el, index) {
				if (el) this.formRefsContent[`content${index}`] = el;
			},
			validateData() {
				let isValid = true;
				this.legalObligations.forEach((row, index) => {
					const formRef = this.formRefs[`form${index}`];
					const contentRef = this.formRefsContent[`content${index}`];

					if (formRef) {
						formRef.validate((valid) => !valid && (this.marginBottom[index] = '20px'));
					}
					if (contentRef) {
						contentRef.validate((valid) => !valid && (this.marginBottomContent[index] = '20px'));
					}
				});
				return isValid;
			},
		},
	};
</script>

<style scoped>
	.hide-word-limit >>> .el-input__count {
		display: none;
	}
	.no-resize /deep/ .el-textarea__inner {
		resize: none;
		overflow: hidden; /* 隐藏滚动条 */
	}

	.custom-height /deep/ .el-input__inner {
		height: 100%;
	}
	.custom-input >>> .el-input__inner {
		height: var(--input-height);
		transition: height 0.3s ease; /* 添加过渡效果 */
	}
	/* Element UI 错误状态保留默认边距 */
	::v-deep .el-table .el-form-item.is-error {
		margin-bottom: 20px !important; /* Element 默认值 */
	}
	/* 针对 el-table 内的 el-form-item */
	::v-deep .el-table .el-form-item {
		margin-left: 0 !important;
		margin-bottom: 0 !important;
	}
	/* 或者仅调整内容区域 */
	::v-deep .el-table .el-form-item__content {
		margin-left: 0 !important;
	}
</style>
