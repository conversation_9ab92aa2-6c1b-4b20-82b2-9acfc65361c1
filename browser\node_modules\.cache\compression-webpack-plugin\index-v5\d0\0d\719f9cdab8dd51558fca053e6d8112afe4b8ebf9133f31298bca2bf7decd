
9acdaff9a15d84bb5f804f6e2feb1d29303d6f80	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.289.1754018536329.js\",\"contentHash\":\"5506dcf377067e16883f189e6571bab4\"}","integrity":"sha512-fMtnxSxpq6KZXR89IZVwTmh/A/91TDbXDsOwyZDup2pyy6vrCDIvvDnOCzExmE0yVyvGxUjzoCZt4J9Z4DED1Q==","time":1754018575963,"size":106080}