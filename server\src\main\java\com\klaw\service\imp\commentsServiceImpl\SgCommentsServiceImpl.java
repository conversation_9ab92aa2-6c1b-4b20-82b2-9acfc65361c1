package com.klaw.service.imp.commentsServiceImpl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.klaw.dao.CommentsDao.SgCommentsMapper;
import com.klaw.entity.caseBean.CaseSued;
import com.klaw.entity.caseBean.Cooperation;
import com.klaw.entity.caseBean.SgCaseSupervise;
import com.klaw.entity.commentsBean.SgComments;
import com.klaw.entity.contractBean.SgContractApproval;
import com.klaw.service.commentsService.SgCommentsService;
import com.klaw.utils.DataAuthUtils;
import com.klaw.utils.PageUtils;
import com.klaw.utils.Utils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;

@Slf4j
@Service
public class SgCommentsServiceImpl extends ServiceImpl<SgCommentsMapper, SgComments> implements SgCommentsService
{
    @Resource
    SgCommentsService sgCommentsService;

    @Override
    public void saveData(SgComments sgComments)
    {
        sgCommentsService.saveOrUpdate(sgComments);
    }

    @Override
    public PageUtils<SgComments> queryPageData(JSONObject jsonObject) {
        QueryWrapper<SgComments> queryWrapper = new QueryWrapper<SgComments>();
        getFilter(jsonObject,queryWrapper);
        return page(new PageUtils<SgComments>(jsonObject), queryWrapper);
    }
    //获取查询条件
    private void getFilter(JSONObject json, QueryWrapper<SgComments> wrapper) {

        //案件名称
        String createPsnName = json.containsKey("createPsnName") ? json.getString("createPsnName") : null;
        //模糊搜索值
        String fuzzyValue = json.containsKey("fuzzyValue") ? json.getString("fuzzyValue") : null;
        //排序字段
        String sortName = json.containsKey("sortName") ? json.getString("sortName") : null;
        //排序字段是否升序
        boolean order = json.containsKey("order") ? json.getBoolean("order") : false;

        if (StringUtils.isNotBlank(createPsnName)) {
            wrapper.and(i -> i.like("create_Psn_Name", createPsnName));
        }
        //模糊搜索匹配字段
        String[] cols = {"create_Psn_Name"};
        Utils.fuzzyValueQuery(wrapper, cols, fuzzyValue);
        if(org.apache.commons.lang.StringUtils.isNotBlank(sortName)){
            wrapper.orderBy(true, order, "to_char("+ Utils.humpToLine2(sortName)+")");
        }else{
            wrapper.orderBy(true, order,"create_time");
        }

    }
}