
29cbe117d4ebc63b98ce601d75d148d40e2b4299	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.380.1754018536329.js\",\"contentHash\":\"cfc1f15a09ff1679f01f7d105bf0137f\"}","integrity":"sha512-toDvgNWJ8+F8Zsv0PsYYzrKBx1j1S0pE/96KfmAX1SdCjCmOxUhsacUZrJBx6sRwy+dtbb4TP33TM2VLQrhIJA==","time":1754018575976,"size":62103}