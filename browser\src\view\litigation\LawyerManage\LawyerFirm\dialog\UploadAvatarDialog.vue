<template>
  <el-dialog :close-on-click-modal="false" title="律师列表" :visible.sync="dialogVisible" width="70%">
    <div>
      <el-upload
          list-type="picture"
          action=''
          accept=".jpg, .png"
          :limit="1"
          :auto-upload="false"
          :file-list="fileList"
          :on-change="getFile"
          :on-preview="handlePictureCardPreview"
          :on-remove="handleUploadRemove"
      >
        <el-button size="small" type="primary">选择图片上传</el-button>
        <div slot="tip" class="el-upload__tip">只能上传一张jpg/png文件</div>
      </el-upload>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button icon="" class="negative-btn" @click="cancel_">取消</el-button>
      <el-button type="primary" icon="" class="active-btn" @click="sure_">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import lawyerApi from '@/api/LawyerManage/LawyerFirm/lawyer'
import pagination from "@/components/Pagination"
import textSpan from '@/view/components/TextSpan/TextSpan'
import {mapGetters} from "vuex";

export default {
  name: 'LawyerDialog',
  components: { pagination, textSpan },
  props: {
    dialogVisible: {// 是否显示
      type: Boolean,
      default: false
    },
  },
  computed: {
    ...mapGetters(['orgContext']),
  },
  created() {
  },
  data() {
    return {
      dialog_height: 'calc(100vh - 450px)', // table高度
      myDialogVisible: this.dialogVisible,
      fileList:[],
      file: null,
      proofImage:null,
    }
  },
  watch: {
    myDialogVisible(val) {
      this.$emit('update:dialogVisible', val)
    },
    dialogVisible(val) {
      this.myDialogVisible = val
    },
  },
  methods: {
    cancel_() {
      this.myDialogVisible = false;
    },
    sure_() {
      const isJPG = this.file.type === 'image/jpeg' || this.file.type === 'image/png';
      const isLt2M = this.file.size / 1024  < 200;

      if (!isJPG) {
        this.$message.error('上传头像图片只能是 JPG或PNG 格式!');
      }

      if (!isLt2M) {
        this.$message.error('上传头像图片大小不能超过 200K !');
      }

      if (isJPG && isLt2M)
      {
        this.myDialogVisible = false;
        this.$emit('avatarSure', this.proofImage)
      }
    },

    //通过getFile方法获取文件信息
    getFile(file, fileList) {
      console.log(file,'file')
      this.file = file.raw
      this.getBase64(file.raw).then(res => {
        const params = res
        this.proofImage = params
      })
    },
    // 图片转base64编码：

    getBase64(file) {
      return new Promise(function (resolve, reject) {
          const reader = new FileReader()
          let imgResult = ''
          reader.readAsDataURL(file)
          reader.onload = function () {
            console.log(reader.result,'reader.result')
            imgResult = reader.result
          }
          reader.onerror = function (error) {
            reject(error)
          }
          reader.onloadend = function () {
            resolve(imgResult)
          }
      })
    },
    // 预览和删除
    handleUploadRemove(file, fileList) {
      console.log(this.proofImage,'这个是base64的编码值')
      this.proofImage = ''

    },
    handlePictureCardPreview(file) {
      console.log(this.proofImage)
    },

  }
}
</script>

<style scoped>

</style>
