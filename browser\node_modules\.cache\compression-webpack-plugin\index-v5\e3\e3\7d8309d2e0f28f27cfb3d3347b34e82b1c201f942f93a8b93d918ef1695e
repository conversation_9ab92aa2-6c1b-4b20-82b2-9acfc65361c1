
8446d4f5aa5bcb083dc1fa4b7a454dbe29165ee6	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.416.1754018536329.js\",\"contentHash\":\"72149ed2bcf3e0daeb0e64f9046583bd\"}","integrity":"sha512-M6FXOEWx6ZgHMJXzOrW1KBkxp9wqPgYRpzak6fGJC5CGBHdrDc9i62moJ5R2Jd8gckzeCcnD6lLfAutXhFVKgQ==","time":1754018575976,"size":91063}