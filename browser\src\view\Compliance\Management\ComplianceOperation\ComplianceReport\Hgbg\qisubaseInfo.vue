<template xmlns:v-slot="http://www.w3.org/1999/XSL/Transform">

  <div style="margin-top: 10px" v-if="view === 'old'">
    <!--基础信息表单块-->
    <div v-if="dataState !== 'view'">
      <div style="padding-left: 10px;margin-bottom: 20px;font-size: 15px;color: red;font-weight: bolder">

      </div>
      <div style="margin: 10px">
        <span style="font-weight: bold;font-size: 16px;color: #5A5A5F;">基本信息</span>
      </div>
      <el-row style="margin-top: 10px">
        <el-col :span="16">
          <el-form-item label="报告主题" prop="reportSubject" >
            <el-input v-if="!isView" v-model="mainData.reportSubject" maxlength="100" show-word-limit
              placeholder="请输入..." clearable />
            <span v-else class="viewSpan">{{ mainData.reportSubject }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="报告编号">
            <el-input v-if="!isView" v-model.trim="mainData.processNumber" disabled show-word-limit
              style="width: 100%" />
            <span v-else class="viewSpan">{{ mainData.processNumber }}</span>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item label="报告种类" prop="reportKind" required>
            <el-select v-model="mainData.reportKind" clearable placeholder="请选择" style="width: 100%">
              <el-option label="专项工作报告" value="专项工作报告" />
              <el-option label="年度报告" value="年度报告" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="报告类型" prop="reportCategory">
            <el-select v-model="mainData.reportCategory" multiple clearable placeholder="请选择" style="width: 100%">
              <el-option label="法治建设" value="法治建设" />
              <el-option label="合规管理" value="合规管理" />
              <el-option label="风险管理" value="风险管理" />
              <el-option label="法律纠纷情况" value="法律纠纷情况" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="报告年度" prop="reportYear" required>
            <el-select v-model="mainData.reportYear" clearable placeholder="请选择" style="width: 100%">
              <el-option v-for="year in yearOptions" :key="year" :label="year" :value="year" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
      </el-row>
      <!-- <el-row>
        <el-col :span="24">
          <el-form-item label="报告说明" prop="reportExplanation" required>
            <span slot="label">报告说明</span>
            <el-input v-if="!isView" v-model="mainData.reportExplanation" :autosize="{ minRows: 5, maxRows: 15 }"
              type="textarea" placeholder="请输入报告说明" maxlength="2000" show-word-limit />
            <text-span v-else class="viewSpan" :text="mainData.reportExplanation" />
          </el-form-item>
        </el-col>
      </el-row> -->
      <el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="报告文本" prop="reportFile" required>
              <UploadDoc :files.sync="mainData.reportFile" doc-path="/case" :disabled="isView" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-row>

    </div>
    <!-- 查看 -->
    <div v-else>
      <SimpleBoardTitle title="基本信息">
        <table class="table_content">
          <tbody>
            <tr>
              <th colspan="2" class="th_label">报告主题</th>
              <td colspan="22" class="td_value">{{ mainData.reportSubject }}</td>
            </tr>
            <tr>
              <th colspan="2" class="th_label">项目编号</th>
              <td colspan="10" class="td_value">{{ mainData.processNumber }}</td>
              <th colspan="2" class="th_label">报告年度</th>
              <td colspan="10" class="td_value">{{ mainData.reportYear }}</td>
            </tr>
            <tr>
              <th colspan="2" class="th_label">报告种类</th>
              <td colspan="10" class="td_value">{{ mainData.reportKind }}</td>
              <th colspan="2" class="th_label">报告类型</th>
              <td colspan="10" class="td_value">{{ mainData.reportCategory.join(', ') }}</td>
            </tr>
            <!-- <tr>
              <th colspan="2" class="th_label">报告说明</th>
              <td colspan="22" class="td_value">{{ mainData.reportExplanation }}</td>
            </tr> -->
            <tr>
              <th colspan="2" class="th_label">报告文本</th>
              <td colspan="22" class="td_value">
                <UploadDoc :files.sync="mainData.reportFile" doc-path="/case" :disabled="isView" />
              </td>
            </tr>
          </tbody>

        </table>

      </SimpleBoardTitle>
    </div>

  </div>

  <div v-else>
    <SimpleBoardTitleApproval title="基本信息">
      <table class="table_content">
        <tbody>
          <tr>
            <th colspan="3" class="th_label_approval">报告主题</th>
            <td colspan="9" class="td_value_approval">{{ mainData.reportSubject }}</td>
            <th colspan="3" class="th_label_approval">报告类型</th>
            <td colspan="9" class="td_value_approval">{{ mainData.reportCategory }}</td>
          </tr>
          <tr>
            <th colspan="3" class="th_label_approval">经办人</th>
            <td colspan="9" class="td_value_approval">{{ mainData.reporter }}</td>
            <th colspan="3" class="th_label_approval">经办时间</th>
            <td colspan="9" class="td_value_approval">{{ mainData.createTime | parseTime }}</td>
          </tr>
          <tr>
            <th colspan="3" class="th_label_approval">经办人电话</th>
            <td colspan="9" class="td_value_approval">{{ mainData.createPsnPhone }}</td>
            <th colspan="3" class="th_label_approval">报告年度</th>
            <td colspan="9" class="td_value_approval">{{ mainData.reportYear }}</td>
          </tr>
          <!-- <tr>
            <th colspan="3" class="th_label_approval_">报告说明</th>
            <td colspan="21" class="td_value_approval_">{{ mainData.reportExplanation }}</td>
          </tr> -->
        </tbody>
        <tbody>
          <tr>
            <th colspan="3" class="th_label_approval">报告文本</th>
            <td colspan="21" class="td_value_approval">
              <div v-if="mainData.relatedAttachments">
                <UploadDoc :files.sync="mainData.relatedAttachments" doc-path="/case" :disabled="true" />
              </div>
              <div v-else style="font-size: 15px">无</div>
            </td>
          </tr>
        </tbody>
      </table>
    </SimpleBoardTitleApproval>
  </div>
</template>

<script>
import dictApi from '@/api/_system/dict'
import AreaSelect from '@/view/components/AreaSelect/AreaSelect'
import OrgSingleDialogSelect from '@/view/components/OrgSingleDialogSelect/index.vue'
import UploadDoc from '@/view/components/UploadDoc/UploadDoc.vue'
import orgTree from '@/view/components/OrgTree/OrgTree'
import SimpleBoardTitle from "@/view/components/SimpleBoard/SimpleBoardTitle"
import SimpleBoardTitleApproval from "@/view/components/SimpleBoard/SimpleBoardTitleApproval"
import money from "@/view/components/Money/index"
import OrgLeader from '@/view/components/OrgLeader/OrgLeader'


export default {
  name: 'QsBaseInfo',
  components: {
    OrgSingleDialogSelect, AreaSelect, UploadDoc, orgTree, SimpleBoardTitleApproval, money,
    SimpleBoardTitle, OrgLeader
  },
  props: {
    data: {
      type: Object,
      default: function () {
        return {}
      }
    },
    dataState: {
      type: String,
      default: ''
    },
    view: {
      type: String,
      default: 'new'
    },
    create: {
      type: String,
      default: ''
    },
    authorizationData: {
      type: Object,
      default: function () {
        return {}
      }
    },
  },
  data() {
    const currentYear = new Date().getFullYear(); // 获取当前年份
    return {
      orgDialogTitle: '组织信息',
      caseDialogVisible: false,
      mainData: this.data,
      dicTreeDialogVisible: false,
      dicTreeDialogVisible2: false,
      dicTreeDialogVisible3: false,
      orgTreeDialog: false,
      caseNatures: [],
      plateData: [],
      causeOfIns: [],
      applications: [],
      involvedLevelData: [],//涉案单位管理层级
      suedUnitTypeData: [], //被诉单位性质
      unitTypeData: [],//单位类型
      reportCategoryData: [],//报告类别
      zxcheckedData: [],
      orgVisible: false,
      tableQuery: {
        reportYear: '',
      },
      // mainData: {
      //   reportYear: currentYear, // 设置为当前年份
      // },
      yearOptions: Array.from({ length: 10 }, (_, i) => new Date().getFullYear() - i)
    }
  },
  computed: {
    isView: function () {
      return this.dataState === this.utils.formState.VIEW
    },
    causeOfInIds: {
      set: function (data) {
        this.mainData.causeOfInId = data.join(',')
      },
      get: function () {
        if (this.mainData.causeOfInId) {
          return this.mainData.causeOfInId.split(',')
        }
        return []
      }
    },
    isCreate: function () {
      return this.create === 'create'
    },
  },
  watch: {
    mainData: {
      handler(val, oldVal) {
        this.$emit('update:data', val)
      },
      deep: true
    },
    data(val) {
      this.mainData = Object.assign(this.mainData, val)
    },
    'mainData.involvedAmount': {
      handler(val, oldVal) {
        this.involvedAmountChange(val)
      },
      deep: true, immediate: true
    },
    'mainData.caseInterest': {
      handler(val, oldVal) {
        this.caseInterestChange(val)
      },
      deep: true, immediate: true
    }
  },
  created() {
    this.initDic()
    window.vm = this;
  },
  methods: {

    reportCategoryChange(val) {
      this.mainData.reportCategory = this.utils.getDicName(this.reportCategoryData, val)
    },
    venueIsOutOnChange(val) {
      this.mainData.venueAddress = null
      this.mainData.venueProvince = null
      this.mainData.venueCity = null
      this.mainData.venueRegion = null
    },
    sureBtn(data) {
      this.mainData.caseUndertakerId = data.unitId
      this.mainData.caseUndertaker = data.name
    },
    sureBtn2(data) {
      this.mainData.ognPackageId = data.unitId
      this.mainData.ognPackage = data.name
    },
    sureBtn3(data) {
      this.mainData.groupPackageId = data.unitId
      this.mainData.groupPackage = data.name
    },
    orgSelect(data) {
      this.mainData.currentUnitId = data.unitId
      this.mainData.currentUnit = data.name

      this.mainData.riskDepartmentId = data.unitId
      this.mainData.riskDepartment = data.name
    },
    moneyFocus(event) {
      event.currentTarget.select()
    },
    initDic() {
      dictApi.showAllSelect({
        dicCode: 'SSBK'
      }).then(response => {
        this.plateData = response.data.data
      })
      dictApi.showAllSelect({
        dicCode: 'AJ_AJLX'
      }).then(response => {
        this.causeOfIns = response.data.data
      })
      dictApi.showAllSelect({
        dicCode: 'AJ_SQSX'
      }).then(response => {
        this.applications = response.data.data
      })
      dictApi.showAllSelect({
        dicCode: 'AJ-SADWCJ'
      }).then(response => {
        this.involvedLevelData = response.data.data
      })
      dictApi.showAllSelect({
        dicCode: 'AJ-DWLX'
      }).then(response => {
        this.unitTypeData = response.data.data
      })
      dictApi.showAllSelect({
        dicCode: 'HG-BGLX'
      }).then(response => {
        this.reportCategoryData = response.data.data
      })
    },
    chooseApprovalDeptClick() {
      this.isCheckedUser = false
      this.showUser = false
      this.orgVisible = true
      this.is_Check = false
    },
    cancel_() {
      this.orgVisible = false
    },
    choiceDeptSure_() {
      let c = ''
      let cid = ''
      this.zxcheckedData.forEach((item) => {
        if (c.length === 0) {
          c = c + item.name
          cid = cid + item.unitId
        } else {
          c = c + ',' + item.name
          cid = cid + ',' + item.unitId
        }
      })
      this.mainData.noticeDeptName = c
      this.mainData.noticeDeptId = cid
      this.orgVisible = false
    },
    queryOtherPartys() {
      let partys = this.mainData.partiesList
      let otherPartys = ""

      if (partys.length > 0) {
        for (let i = 0; i < partys.length; i++) {
          if (partys[i].partyType !== '原告' && partys[i].party !== null && partys[i].party !== undefined)
            otherPartys += partys[i].party + ","
        }

        if (otherPartys !== "")
          otherPartys = otherPartys.slice(0, -1)
      }

      return otherPartys
    }
  }
}
</script>

<style lang="scss" scoped>
.hideContent {

  .el-input__inner,
  .el-radio.is-bordered,
  .el-textarea__inner,
  .el-input__count {
    background-color: #f9e8bb;
  }
}

.money-label-width .el-form-item__label {
  width: 150px;
  /* 指定宽度 */
}
</style>
