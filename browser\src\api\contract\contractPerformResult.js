import {request} from '@/api/index'

export default {
  saveStartTime(data) {
    return request({
      url: '/contractPerformResult/saveStartTime',
      method: 'post',
      data
    })
  },
  savePerform(data) {
    return request({
      url: '/contractPerformResult/savePerform',
      method: 'post',
      data
    })
  },
  addPerform(data) {
    return request({
      url: '/contractPerformResult/addPerform',
      method: 'post',
      data
    })
  },
  deletePerform(data) {
    return request({
      url: '/contractPerformResult/deletePerform',
      method: 'post',
      data
    })
  },
  queryContractApproval(data) {
    return request({
      url: '/contractPerformResult/queryContractApproval',
      method: 'post',
      data
    })
  },
  queryContractApprovalView(data) {
    return request({
      url: '/contractPerformResult/queryContractApprovalView',
      method: 'post',
      data
    })
  },
  queryPerform(data) {
    return request({
      url: '/contractPerformResult/queryPerform',
      method: 'post',
      data
    })
  },
  queryPerformResult(data) {
    return request({
      url: '/contractPerformResult/queryPerformResult',
      method: 'post',
      data
    })
  },
  updateStatus(data) {
    return request({
      url: '/contractPerformResult/updateStatus',
      method: 'post',
      data
    })
  },
  queryPageDataById(data) {
    return request({
      url: '/contractPerformResult/queryPageDataById',
      method: 'post',
      data
    })
  },
  updatePerformStatus(data) {
    return request({
      url: '/contractPerformResult/updatePerformStatus',
      method: 'post',
      data
    })
  }
}
