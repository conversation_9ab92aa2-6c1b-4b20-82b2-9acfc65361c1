
be52712e4a556edd368a695e4c8e825b88625a88	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.112.1754018536329.js\",\"contentHash\":\"fed8625b51c5116b610e3008be647c13\"}","integrity":"sha512-QsjbxTLDyyo895h/J33Dj6erVpxLpgMf3DROIY9gFmz/2M7qgsaFHmJw9FiFyIcpM1Bcov1dPYAVGXzY1/NITw==","time":1754018575979,"size":165037}