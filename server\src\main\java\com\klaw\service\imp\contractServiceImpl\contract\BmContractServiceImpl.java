package com.klaw.service.imp.contractServiceImpl.contract;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yitter.idgen.YitIdHelper;
import com.klaw.config.HweiOBSConfig;
import com.klaw.constant.DataStateBPM;
import com.klaw.constant.contractListEnum.*;
import com.klaw.dao.contractDao.contract.BmContractMapper;
import com.klaw.entity.authorizationBean.Authorization;
import com.klaw.entity.contractBean.SgContractType;
import com.klaw.entity.contractBean.SgSealApproval;
import com.klaw.entity.contractBean.SgSealApprovalDetail;
import com.klaw.entity.contractBean.SgSealManage;
import com.klaw.entity.contractBean.contract.*;
import com.klaw.entity.mainDataBean.MainMidStaff;
import com.klaw.entity.mainDataBean.MainMidStaffUnitOrg;
import com.klaw.entity.mainDataBean.MainOrganization;
import com.klaw.entity.mainDataBean.SgBusinessMan;
import com.klaw.entity.systemBean.BmContractInfo;
import com.klaw.entity.systemBean.SgHrOrgUnitB;
import com.klaw.entity.systemBean.SgSysDoc;
import com.klaw.entity.systemBean.SysDict;
import com.klaw.service.HweiYunOBSService.HweiYunOBSService;
import com.klaw.service.authorizationService.AuthorizationService;
import com.klaw.service.authorizationService.SgAuthorizationMatterService;
import com.klaw.service.contractService.*;
import com.klaw.service.contractService.contract.*;
import com.klaw.service.imp.contractServiceImpl.SgProjectManageServiceImpl;
import com.klaw.service.mainDataService.*;
import com.klaw.service.systemService.SgHrOrgUnitBService;
import com.klaw.service.systemService.SgKvsequenceService;
import com.klaw.service.systemService.SgSysDocService;
import com.klaw.service.systemService.SysDictService;
import com.klaw.utils.*;
import com.klaw.vo.ApiJson;
import com.klaw.vo.Json;
import com.klaw.vo.OrgContextVo;
import com.klaw.vo.contractVo.MyPage;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.xssf.usermodel.*;
import org.redisson.Redisson;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 服务实现类
 * 合同实现类
 *
 * <AUTHOR>
 * @since 2024-10-14
 */
@Slf4j
@Service
public class BmContractServiceImpl extends ServiceImpl<BmContractMapper, BmContract> implements BmContractService {

    //常规的审批完成的数据
    private static final String DIALOG_NORMAL = "normal";
    //合同合并审批
    private static final String DIALOG_MORE = "more";
    //合同用印审批
    private static final String DIALOG_SEAL = "seal";
    //合同列表
    private static final String CONTRACT_LIST = "contractList";
    //合同补录列表
    private static final String CONTRACT_SUPPLE = "contractSupple";
    //合同台账
    private static final String CONTRACT_LEDGER = "contractLedger";
    // 合同变更
    private static final String DIALOG_CHANGE = "change";
    // 合同补录变更
    private static final String DIALOG_SUPPLE_CHANGE = "suppleChange";
    // 合同关闭
    private static final String DIALOG_CLOSE = "close";
    // 合同终止
    private static final String DIALOG_STOP = "stop";
    //移交
    private static final String DIALOG_TURNOVER = "turnover";
    //履行风险中心
    private static final String DIALOG_RISKHIS = "riskHis";
    //归档
    private static final String DIALOG_PLACE = "place";
    //归档
    private static final String PERFORM = "perform";
    private static final String DIALOG_APPLE_APP = "appleApp";
    private static final String DIALOG_APPLE_SPPLE_APP = "appleSuppleApp";

    private static final Map<String, String> taxRateMap = new HashMap<>();

    static {
        // 替换键值对
        taxRateMap.put("16%", "0");
        taxRateMap.put("13%", "1");
        taxRateMap.put("11%", "2");
        taxRateMap.put("10%", "3");
        taxRateMap.put("9%", "4");
        taxRateMap.put("6%", "5");
        taxRateMap.put("5%", "6");
        taxRateMap.put("3%", "7");
        taxRateMap.put("1%", "8");
        taxRateMap.put("0%", "9");
    }

    @Autowired
    BmContractMapper bmContractMapper;
    @Autowired
    BmContractProjectService bmContractProjectService;
    @Autowired
    BmContractEffectService bmContractEffectService;
    @Autowired
    BmContractTextService bmContractTextService;
    @Autowired
    BmContractSealService bmContractSealService;
    @Autowired
    BmContractRelationService bmContractRelationService;
    @Autowired
    BmContractRiskService bmContractRiskService;
    @Autowired
    BmContractLeaseService bmContractLeaseService;
    @Autowired
    BmContractPlaceService bmContractPlaceService;
    @Autowired
    SgProjectManageServiceImpl sgProjectManageService;
    @Autowired
    SgContractTypeService contractTypeService;
    @Autowired
    SgBusinessManService sgBusinessManService;
    @Resource
    private SgKvsequenceService kvsequenceService;
    @Resource
    private SgHrOrgUnitBService sgHrOrgUnitBService;
    @Resource
    private SgContractTypeService sgContractTypeService;
    @Resource
    private AuthorizationService authorizationService;
    @Resource
    private BmContractInfoService bmContractInfoService;
    @Autowired
    private HweiOBSConfig hweiOBSConfig;
    @Autowired
    private SgSysDocService sgSysDocService;
    @Autowired
    private SgSealManageService sgSealManageService;
    @Resource
    private SgSealApprovalService sgSealApprovalService;
    @Resource
    private SgSealApprovalDetailService sgSealApprovalDetailService;
    @Autowired
    private BmContractRiskHisService bmContractRiskHisService;
    @Autowired
    private MainOrganizationService mainOrganizationService;
    @Autowired
    private MainMidStaffService mainMidStaffService;
    @Autowired
    private MainMidStaffUnitOrgService mainMidStaffUnitOrgService;
    @Autowired
    private Redisson redisson;
    @Value("${esb.url}")
    private String esbUrl;
    @Autowired
    private SysDictService sysDictService;
    @Autowired
    private HweiYunOBSService hweiYunOBSService;
    @Autowired
    private BmContractToMainService bmContractToMainService;
    @Autowired
    private SgStaffService sgStaffService;
    @Autowired
    private MainMidStaffUnitOrgService midStaffUnitOrgService;
    @Autowired
    private SgHrOrgUnitBService hrOrgUnitBService;
    @Autowired
    private SgAuthorizationMatterService sgAuthorizationMatterService;


    /**
     * 对json数据key进行替换
     */
    public static JSONObject changeJsonObj(JSONObject jsonObj, Map<String, String> keyMap) {
        JSONObject resJson = new JSONObject();
        Set<String> keySet = jsonObj.keySet();
        for (String key : keySet) {
            String resKey = keyMap.get(key) == null ? key : keyMap.get(key);
            try {
                JSONObject jsonobj1 = jsonObj.getJSONObject(key);
                resJson.put(resKey, changeJsonObj(jsonobj1, keyMap));
            } catch (Exception e) {
                try {
                    JSONArray jsonArr = jsonObj.getJSONArray(key);
                    resJson.put(resKey, changeJsonArr(jsonArr, keyMap));
                } catch (Exception x) {
                    resJson.put(resKey, jsonObj.get(key));
                }
            }
        }
        return resJson;
    }

    public static JSONArray changeJsonArr(JSONArray jsonArr, Map<String, String> keyMap) {
        JSONArray resJson = new JSONArray();
        for (int i = 0; i < jsonArr.size(); i++) {
            JSONObject jsonObj = jsonArr.getJSONObject(i);
            resJson.add(changeJsonObj(jsonObj, keyMap));
        }
        return resJson;
    }

    /**
     * 拆分文件名和文件类型
     *
     * @param fileName 完整的文件名
     * @return 包含文件名称和文件类型的数组
     */
    public static String[] splitFileName(String fileName) {
        // 检查文件名是否为空或不包含扩展名
        if (fileName == null || !fileName.contains(".")) {
            throw new IllegalArgumentException("无效的文件名");
        }

        // 使用最后一个点号分割文件名和扩展名
        int lastDotIndex = fileName.lastIndexOf('.');
        String name = fileName.substring(0, lastDotIndex);
        String extension = fileName.substring(lastDotIndex + 1);

        return new String[]{name, extension};
    }

    @Override
    public PageUtils<BmContract> queryPageData(JSONObject jsonObject) {
        StopWatch sw = new StopWatch();
        sw.start();
        PageUtils<BmContract> pageUtils = new PageUtils<>(jsonObject);
        QueryWrapper<BmContract> queryWrapper = new QueryWrapper<BmContract>();

        getFilter(queryWrapper, jsonObject);
        PageUtils<BmContract> page = bmContractMapper.queryPageList(pageUtils, queryWrapper);
        sw.stop();
        System.out.println("合同台账查询耗时：");
        System.out.println(sw.getTotalTimeMillis());
        return page;
    }

    @Override
    public MyPage<BmContract> queryPageData1(JSONObject jsonObject) {
        StopWatch sw = new StopWatch();
        sw.start();
        QueryWrapper<BmContract> queryWrapper = new QueryWrapper<BmContract>();
        getFilter(queryWrapper, jsonObject);
        int page = jsonObject.getIntValue("page");
        int limit = jsonObject.getIntValue("limit");
        int CurrentSize = page * limit - limit;

        // CurrentSize 自定义字段 例如: 现在分页处于第3页，每页条数是10条,那么第四页需要从
        // 第31条开始查询，抛弃前面30条数据,也就有了下面的这行代码
        Integer total = bmContractMapper.selectCount(queryWrapper); // 关键点(查询出符合条件的总条数)
        List<BmContract> resList = bmContractMapper.pageComments(queryWrapper, CurrentSize, limit);
        //以下是自定义Page拼装
        MyPage<BmContract> pageContract = new MyPage<>();
        int pages = (int) Math.ceil((double) total / limit);
        pageContract.setTotal(total);
        pageContract.setPages(pages);
        pageContract.setRecords(resList);
        pageContract.setLimit(limit);
        pageContract.setPage(CurrentSize);
        sw.stop();
        System.out.println("合同台账查询耗时：");
        System.out.println(sw.getTotalTimeMillis());
        return pageContract;
    }

    @Override
    public PageUtils<BmContract> queryRisk(JSONObject jsonObject) {
        StopWatch sw = new StopWatch();
        sw.start();
        PageUtils<BmContract> pageUtils = new PageUtils<>(jsonObject);
        QueryWrapper<BmContract> queryWrapper = new QueryWrapper<BmContract>();
        getRiskFilter(queryWrapper, jsonObject);
        PageUtils<BmContract> page = bmContractMapper.queryPageList(pageUtils, queryWrapper);
        sw.stop();
        System.out.println("合同风控清单台账查询耗时：");
        System.out.println(sw.getTotalTimeMillis());

        return page;
    }

    @Override
    public BmContract getBmContract(String id) {

        return bmContractMapper.selectById(id);
    }

    @Override
    public List<BmContract> getAllBmContract() {
        return bmContractMapper.selectList(null);

    }

    @Override
    public void add(BmContract bmContract) {
        bmContractMapper.insert(bmContract);
    }

    @Override
    public int modify(BmContract bmContract) {
        return bmContractMapper.updateById(bmContract);
    }

    @Override
    public void remove(String ids) {

        if (StringUtils.isNotEmpty(ids)) {
            String[] array = ids.split(",");
            if (!CollectionUtils.isEmpty(Arrays.asList(array))) {
                bmContractMapper.deleteBatchIds(Arrays.asList(array));
            }
        }

    }

    @Override
    @Transactional
    public JSONObject saveData(BmContract bmContract) {
        //保存前，需要校验项目的金额信息
        List<BmContractProject> projectList = bmContract.getProjectList();
        computeContractAmount(bmContract, projectList);
        JSONObject jsonObject = checkProject(projectList, bmContract.getId(), bmContract.getDataTypeCode(), bmContract.getParentId());
        if ("success".equals(jsonObject.getString("state"))) {
            List<BmContractText> textList = bmContract.getTextList();
            List<BmContractRelation> contractList = bmContract.getContractList();
            List<BmContractSeal> sealList = bmContract.getSealList();
            List<BmContractRisk> riskList = bmContract.getRiskList();
            List<BmContractEffect> effectList = bmContract.getEffectList();
            List<BmContractLease> leaseList = bmContract.getLeaseList();

            Utils.saveChilds(effectList, "parent_id", bmContract.getId(), bmContractEffectService);
            Utils.saveChilds(textList, "parent_id", bmContract.getId(), bmContractTextService);
            Utils.saveChilds(contractList, "parent_id", bmContract.getId(), bmContractRelationService);
            Utils.saveChilds(sealList, "parent_id", bmContract.getId(), bmContractSealService);
            Utils.saveChilds(riskList, "parent_id", bmContract.getId(), bmContractRiskService);
            Utils.saveChilds(leaseList, "parent_id", bmContract.getId(), bmContractLeaseService);
            Utils.saveChilds(projectList, "parent_id", bmContract.getId(), bmContractProjectService);
            integrationChild(bmContract, projectList, textList);
            Utils.saveChilds(projectList, "parent_id", bmContract.getId(), bmContractProjectService);

            //合同编号是否为空 合同编码预生成
            if (StringUtils.isBlank(bmContract.getContractCode())) {
                createContractCodeCommon(bmContract);
                jsonObject.put("contractCode", bmContract.getContractCode());
                jsonObject.put("version", bmContract.getVersion());
                jsonObject.put("contractCodeVersion", bmContract.getContractCodeVersion());
            }
            //自定义编码逻辑
            if (bmContract.getCustom() != null && !bmContract.getCustom()) {
                bmContract.setCustomCode(bmContract.getContractCode());
                jsonObject.put("customCode", bmContract.getCustomCode());
            }

            if (bmContract.getDataTypeCode() == 2 || bmContract.getDataTypeCode() == 5 || bmContract.getDataTypeCode() == 3) {
                // 合同变更
                BmContract oldContract = getById(bmContract.getId());
                if (oldContract != null && StringUtils.isNotBlank(oldContract.getOriginalContractId())) {
                    if (!oldContract.getOriginalContractId().equals(bmContract.getOriginalContractId())) {
                        BmContract relationContract = getById(oldContract.getOriginalContractId());
                        frozenContract(relationContract, false);

                        BmContract relationContract2 = getById(bmContract.getOriginalContractId());
                        frozenContract(relationContract2, true);
                    }
                } else {
                    if (StringUtils.isNotBlank(bmContract.getOriginalContractId())) {
                        BmContract relationContract = getById(bmContract.getOriginalContractId());
                        frozenContract(relationContract, true);
                    }
                }
            }
            saveOrUpdate(bmContract);
        }
        return jsonObject;
    }

    /**
     * 计算合同金额
     *
     * @param bmContract 合同实体类
     */
    private void computeContractAmount(BmContract bmContract, List<BmContractProject> projectList) {
        List<BmContractProject> oldProjectList = new ArrayList<>();
        //计算主表金额逻辑
        if (bmContract.getDataTypeCode() == 1 || bmContract.getDataTypeCode() == 4) {

            BigDecimal contractMoney = bmContract.getContractMoney();//合同金额
            BigDecimal exchangeRate = bmContract.getExchangeRate();//汇率
            BigDecimal valueAddedTaxAmount = bmContract.getValueAddedTaxAmount();//增值税额（元）

            if (contractMoney != null && exchangeRate != null) {
                bmContract.setAfterChangeMoney(contractMoney);
                bmContract.setAfterChangeValueAddedTaxAmount(valueAddedTaxAmount);

                //计算主合同 合同金额（人民币）
                //1、合同金额（人民币）= 合同金额（元）* 汇率
                BigDecimal contractMoneyRmb = contractMoney.multiply(exchangeRate);
                bmContract.setContractMoneyRmb(contractMoneyRmb);
                bmContract.setAfterChangeAmountRmb(contractMoneyRmb);

                //计算主合同 不含税金额：
                //1、是否含税为是：不含税金额（人民币元）=合同金额（人民币元）-增值税额
                //2、是否含税为否：不含税金额（人民币元）=合同金额（人民币元）

                if (bmContract.getIsTax()) {
                    if (valueAddedTaxAmount != null) {
                        bmContract.setExcludingTaxAmount(contractMoneyRmb.subtract(valueAddedTaxAmount));
                    }
                } else {
                    bmContract.setExcludingTaxAmount(contractMoneyRmb);
                }
            }
        }
        if (bmContract.getDataTypeCode() == 2 || bmContract.getDataTypeCode() == 5) {
            if (bmContract.getProjectDecisionCode() == 1 || bmContract.getProjectDecisionCode() == 2) {
                oldProjectList = bmContractProjectService.list(new QueryWrapper<BmContractProject>().eq("parent_id", bmContract.getOriginalContractId()));
            }

            BmContract oldContract = getById(bmContract.getOriginalContractId());
//            BigDecimal originalValueAddedTaxAmount = bmContract.getOriginalValueAddedTaxAmount();//原增值税额（元）
            BigDecimal originalValueAddedTaxAmount = oldContract.getAfterChangeValueAddedTaxAmount();//wch 2025 5月 23更改
            BigDecimal thisChangeValueAddedTaxAmount = bmContract.getThisChangeValueAddedTaxAmount();//本次变更增值税金额（元）
//            BigDecimal originalContractMoney = bmContract.getOriginalContractMoney();//原合同金额
            BigDecimal originalContractMoney = oldContract.getAfterChangeMoney();//原合同金额 wch 2025 5月 23更改
            BigDecimal thisChangeMoney = bmContract.getThisChangeMoney();//本次变更金额（元）
            BigDecimal exchangeRate = bmContract.getExchangeRate();//汇率

            if (thisChangeValueAddedTaxAmount != null) {
                //start
                //计算变更合同 变更后增值税金额（元）：
                //1、变更后增值税金额（元） = 原增值税额（元） +  本次变更增值税金额（元）
                bmContract.setAfterChangeValueAddedTaxAmount(originalValueAddedTaxAmount.add(thisChangeValueAddedTaxAmount));
                //end
            }

            if (thisChangeMoney != null) {
                // ----start----
                //计算变更合同 变更后合同金额（本币） 主合同专用：
                //1、变更后合同金额（本币） 主合同专用 = 原合同金额（最新的合同金额 本币） + 本次变更金额（本币）
                BigDecimal afterChangeMoney = originalContractMoney.add(thisChangeMoney);
                bmContract.setAfterChangeMoney(afterChangeMoney);
                //end

                // ----start----
                //计算变更合同  变更后合同金额（人民币） 主合同专用：
                //1、变更后合同金额（人民币） 主合同专用 = 变更后合同金额（本币） 主合同专用 * 汇率
                BigDecimal afterChangeAmountRmb = afterChangeMoney.multiply(exchangeRate);
                bmContract.setAfterChangeAmountRmb(afterChangeAmountRmb);
                //end

                // ----start----
                //计算变更合同 本次变更金额（人民币）：
                //1、本次变更金额（人民币） = 本次变更金额（本币）* 汇率
                bmContract.setChangeAmountRmb(exchangeRate.multiply(thisChangeMoney));
                //end

                if (bmContract.getAfterChangeValueAddedTaxAmount() != null) {
                    // ----start----
                    //计算变更合同 变更后不含税金额（人民币）：
                    //1、变更后不含税金额（人民币） = 变更后合同金额（人民币） 主合同专用 - 变更后增值税金额（人民币）
                    bmContract.setExcludingTaxAmount(afterChangeAmountRmb.subtract(bmContract.getAfterChangeValueAddedTaxAmount()));
                    // ----end----
                } else {
                    bmContract.setExcludingTaxAmount(afterChangeAmountRmb);
                }
            }
        }
        if (projectList != null) {
            //计算子表金额逻辑
            for (BmContractProject bmContractProject : projectList) {
                if (bmContract.getDataTypeCode() == 1 || bmContract.getDataTypeCode() == 4) {
                    BigDecimal assignMoney = bmContractProject.getAssignMoney();//分配合同额
                    //计算主合同项目子表 变更后项目分配额（元）
                    //1、变更后项目分配额 = 分配合同额
                    bmContractProject.setChangeAssignMoney(assignMoney);
                    //end

                    //计算主合同项目子表 最新项目分配额（元）主合同项目子表专用
                    //1、最新项目分配额（元）主合同项目子表专用 = 分配合同额
                    bmContractProject.setNewAssignMoney(assignMoney);
                    //end
                }
                if (bmContract.getDataTypeCode() == 2 || bmContract.getDataTypeCode() == 5) {
                    BigDecimal assignMoney = bmContractProject.getAssignMoney();//分配合同额
                    BigDecimal projectThisChangeMoney = bmContractProject.getProjectThisChangeMoney();//分配合同额

                    //计算变更合同项目子表 变更后项目分配额（元）
                    //1、变更后项目分配额 = 原分配合同额 + 项目本次变更金额
                    bmContractProject.setChangeAssignMoney(assignMoney.add(projectThisChangeMoney));
                    //end

                    //计算“主合同”项目子表 最新项目分配额（元）主合同项目子表专用
                    //1、最新项目分配额（元）主合同项目子表专用 = 变更后项目分配额（元）
                    //取对应交集的对象
                    //查询主合同子表中项目编码相同的数据 做更新
                    for (BmContractProject oldProject : oldProjectList) {
                        if (bmContractProject.getProjectCode().equals(oldProject.getProjectCode())) {
                            oldProject.setNewAssignMoney(bmContractProject.getChangeAssignMoney());
                        }
                    }
                    //end
                }
            }
        }
        if (oldProjectList != null) {
            Utils.saveChilds(oldProjectList, "parent_id", bmContract.getOriginalContractId(), bmContractProjectService);
        }
//        //去交集,既获取id相同的交集，需要更新
//        //1.先提取出id和结果，用map形式
//        List<String> oldIds = new ArrayList<>();
//        List<String> newIds = new ArrayList<>();
//        oldProjectList.forEach(it->oldIds.add(it.getProjectCode()));
//        projectList.forEach(it->newIds.add(it.getProjectCode()));
//
//
//        //1、首先修改 本次项目子表 “项目本次变更金额”，
//        // 原合同带来的项目 “变更后项目分配额” = 原分配合同额 + 项目本次变更金额
//        // 新增加的项目 “变更后项目分配额” = 项目本次变更金额
//
//        System.out.println("-----原合同带来的项目：变更后项目分配额 = 原合同分配额 + 项目本次变更金额 ---------------------");
//        List<BmContractProject> projectUpdate = projectList.stream().filter(it -> oldIds.contains(it.getProjectCode())).collect(Collectors.toList());
//        projectUpdate.forEach(p -> p.setChangeAssignMoney(p.getAssignMoney().add(p.getProjectThisChangeMoney())));
//
//
//        List<String> collectAdd = newIds.stream().filter(it -> !oldIds.contains(it)).collect(Collectors.toList());
//        System.out.println("-----新增加的项目：变更后项目分配额 = 原分配合同额--------------------");
//        List<BmContractProject> projectAdd = projectList.stream().filter(it -> collectAdd.contains(it.getProjectCode())).collect(Collectors.toList());
//        projectAdd.forEach(p -> p.setChangeAssignMoney(p.getAssignMoney()));
    }

    private void frozenContract(BmContract bmContract, boolean bool) {
        UpdateWrapper<BmContract> wrapper = new UpdateWrapper<>();
        if (bool) {
            wrapper.set("change_state", DataStateBPM.CHANGE_STATE_3.getValue());
            wrapper.set("change_state_code", DataStateBPM.CHANGE_STATE_3.getKey());
        } else {
            wrapper.set("change_state", DataStateBPM.CHANGE_STATE_1.getValue());
            wrapper.set("change_state_code", DataStateBPM.CHANGE_STATE_1.getKey());
        }
        wrapper.eq("id", bmContract.getId());
        update(wrapper);
    }

    @Override
    public JSONObject queryLeft() {
        QueryWrapper<BmContract> queryWrapper1 = new QueryWrapper<>();
        queryWrapper1.select("data_Source");
        queryWrapper1.isNotNull("data_Source");
        queryWrapper1.groupBy("data_Source");
        List<Object> list = bmContractMapper.selectObjs(queryWrapper1);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("dataList", list);
        return jsonObject;
    }

    @Override
    public BmContract queryDataById(String id) {
        BmContract bmContract = getById(id);
        if (bmContract.getProjectDecisionCode() != null) {
            if (bmContract.getProjectDecisionCode() == 1 || bmContract.getProjectDecisionCode() == 2) {
                List<BmContractProject> projectList = bmContractProjectService.list(new QueryWrapper<BmContractProject>().eq("parent_id", id).orderByAsc("create_time"));
                if (!CollectionUtils.isEmpty(projectList)) {
                    bmContract.setProjectList(projectList);
                }
            } else if (bmContract.getProjectDecisionCode() == 5) {
                List<BmContractLease> leaseList = bmContractLeaseService.list(new QueryWrapper<BmContractLease>().eq("parent_id", id).orderByAsc("create_time"));
                if (!CollectionUtils.isEmpty(leaseList)) {
                    bmContract.setLeaseList(leaseList);
                }
            }
        }
        if ("关联授权".equals(bmContract.getAuthorizedSource())) {
            if (StringUtils.isNotBlank(bmContract.getAuthorizedId())) {
                Authorization authorization = authorizationService.getById(bmContract.getAuthorizedId());
                bmContract.setAuthorization(authorization);
            }
        }
        List<BmContractText> contractTextList = bmContractTextService.list(new QueryWrapper<BmContractText>().eq("parent_id", id).orderByAsc("create_time"));
        if (!CollectionUtils.isEmpty(contractTextList)) {
            bmContract.setTextList(contractTextList);
        }

        List<BmContractRelation> contractRelationList = bmContractRelationService.list(new QueryWrapper<BmContractRelation>().eq("parent_id", id).orderByAsc("create_time"));
        if (!CollectionUtils.isEmpty(contractRelationList)) {
            bmContract.setContractList(contractRelationList);
        }

        List<BmContractSeal> sealList = bmContractSealService.list(new QueryWrapper<BmContractSeal>().eq("parent_id", id).orderByAsc("create_time"));
        if (!CollectionUtils.isEmpty(sealList)) {
            bmContract.setSealList(sealList);
        }

        List<BmContractRisk> riskList = bmContractRiskService.list(new QueryWrapper<BmContractRisk>().eq("parent_id", id).orderByAsc("risk_sort"));
        if (!CollectionUtils.isEmpty(riskList)) {
            bmContract.setRiskList(riskList);
        }
        return bmContract;
    }

    @Override
    public BmContract queryProjectById(String id) {
        BmContract bmContract = getById(id);
        if (bmContract.getProjectDecisionCode() == 1 || bmContract.getProjectDecisionCode() == 2) {
            List<BmContractProject> projectList = bmContractProjectService.listInfo(id);
            if (!CollectionUtils.isEmpty(projectList)) {
                bmContract.setProjectList(projectList);
            }
        } else if (bmContract.getProjectDecisionCode() == 5) {
            List<BmContractLease> leaseList = bmContractLeaseService.list(new QueryWrapper<BmContractLease>().eq("parent_id", id).orderByAsc("create_time"));
            if (!CollectionUtils.isEmpty(leaseList)) {
                bmContract.setLeaseList(leaseList);
            }
        }
        if ("关联授权".equals(bmContract.getAuthorizedSource())) {
            if (StringUtils.isNotBlank(bmContract.getAuthorizedId())) {
                Authorization authorization = authorizationService.getById(bmContract.getAuthorizedId());
                bmContract.setAuthorization(authorization);
            }
        }
        List<BmContractText> contractTextList = bmContractTextService.list(new QueryWrapper<BmContractText>().eq("parent_id", id).orderByAsc("create_time"));
        if (!CollectionUtils.isEmpty(contractTextList)) {
            bmContract.setTextList(contractTextList);
        }

        List<BmContractRelation> contractRelationList = bmContractRelationService.list(new QueryWrapper<BmContractRelation>().eq("parent_id", id).orderByAsc("create_time"));
        if (!CollectionUtils.isEmpty(contractRelationList)) {
            bmContract.setContractList(contractRelationList);
        }

        List<BmContractSeal> sealList = bmContractSealService.list(new QueryWrapper<BmContractSeal>().eq("parent_id", id).orderByAsc("create_time"));
        if (!CollectionUtils.isEmpty(sealList)) {
            bmContract.setSealList(sealList);
        }

        List<BmContractRisk> riskList = bmContractRiskService.list(new QueryWrapper<BmContractRisk>().eq("parent_id", id).orderByAsc("risk_sort"));
        if (!CollectionUtils.isEmpty(riskList)) {
            bmContract.setRiskList(riskList);
        }
        return bmContract;
    }

    @Override
    public BmContract queryTaskById(String id) {
        BmContract bmContract = getById(id);
        if (StringUtils.isNotBlank(bmContract.getAuthorizedId())) {
            Authorization authorization = authorizationService.getById(bmContract.getAuthorizedId());
            bmContract.setAuthorization(authorization);
        }
        return bmContract;
    }

    @Override
    public BmContract queryPlaceById(String id) {
        BmContract bmContract = getById(id);
        List<BmContractPlace> placeList = bmContractPlaceService.list(new QueryWrapper<BmContractPlace>().eq("parent_id", id).orderByAsc("create_time"));
        if (!CollectionUtils.isEmpty(placeList)) {
            bmContract.setPlaceList(placeList);
        } else {
            bmContract.setPlaceList(new ArrayList<>());
        }
        if (StringUtils.isNotBlank(bmContract.getAuthorizedId())) {
            Authorization authorization = authorizationService.getById(bmContract.getAuthorizedId());
            bmContract.setAuthorization(authorization);
        }
        return bmContract;
    }

    @Override
    public boolean deleteDataById(String id) {
        BmContract contract = getById(id);
        if (contract.getDataTypeCode() == 2 || contract.getDataTypeCode() == 5 || contract.getDataTypeCode() == 3) {
            BmContract relationContract = getById(contract.getOriginalContractId());
            if (relationContract != null) {
                relationContract.setChangeStateCode(DataStateBPM.CHANGE_STATE_1.getKey());
                relationContract.setChangeState(DataStateBPM.CHANGE_STATE_1.getValue());
                updateById(relationContract);
            }
        }
        List<BmContractProject> list = new ArrayList<>();
        //删除数据需要更改项目资金
        checkProject(list, id, contract.getDataTypeCode(), contract.getParentId());
        bmContractEffectService.remove(new QueryWrapper<BmContractEffect>().eq("parent_id", id));
        bmContractTextService.remove(new QueryWrapper<BmContractText>().eq("parent_id", id));
        bmContractProjectService.remove(new QueryWrapper<BmContractProject>().eq("parent_id", id));
        bmContractRelationService.remove(new QueryWrapper<BmContractRelation>().eq("parent_id", id));
        bmContractSealService.remove(new QueryWrapper<BmContractSeal>().eq("parent_id", id));
        bmContractRiskService.remove(new QueryWrapper<BmContractRisk>().eq("parent_id", id));
        return removeById(id);
    }

    @Override
    public PageUtils<BmContract> queryDialog(JSONObject json) {
        StopWatch sw = new StopWatch();
        sw.start();
        PageUtils<BmContract> pageUtils = new PageUtils<>(json);
        QueryWrapper<BmContract> queryWrapper = new QueryWrapper<BmContract>();

        String id = json.getString("id");
        String functionType = json.getString("functionType");
        String contractType = json.containsKey("contractType") ? json.getString("contractType") : null;
        String sealPurpose = json.containsKey("sealPurpose") ? json.getString("sealPurpose") : null;
        String contractTypeCode = json.containsKey("contractTypeCode") ? json.getString("contractTypeCode") : null;

        //判断是否位合同补录变更
        String typeName = json.containsKey("typeName") ? json.getString("typeName") : null;
        //是否台账功能（登记只查自己的，台账和弹框查自己和数据权限的）
        boolean isQuery = json.containsKey("isQuery") ? json.getBoolean("isQuery") : false;
        String orgId = json.containsKey("orgId") ? json.getString("orgId") : "";
        //顺序字段
        String sortName = json.containsKey("sortName") ? json.getString("sortName") : null;
        //顺序
        boolean order = json.containsKey("order") ? json.getBoolean("order") : false;
        //模糊搜索
        String fuzzyValue = json.getString("fuzzyValue");
        if (StringUtils.isNotBlank(id)) {
            queryWrapper.ne("id", id);
        }
        if (StringUtils.isNotBlank(contractType)) {
            queryWrapper.eq("contract_code", contractType);
        }
        /*// 如果是合同补充用印则查询已用印且未关闭合同数据
        if (StringUtils.isNotBlank(sealPurpose) && "合同补充用印".equals(sealPurpose)) {
            queryWrapper.eq("take_effect_code", 2).or().eq("take_effect_code", 3)
                    .and(i -> i.eq("close_state_code", 1));
            return page(new PageUtils<BmContract>(json), queryWrapper);
        }*/
        if (DIALOG_NORMAL.equals(functionType)) {
            System.out.printf("functionType：" + functionType);
            // 常规情况：已生效 且 主合同或补录主合同 且 不是变更中、已终止、已关闭
            queryWrapper.eq("take_effect_code", DataStateBPM.TAKE_EFFECT_3.getKey()).and(i -> i.eq("data_type_code", DataStateBPM.DATA_TYPE_1.getKey()).or().eq("data_type_code", DataStateBPM.DATA_TYPE_4.getKey())).and(j -> j.ne("change_state_code", DataStateBPM.CHANGE_STATE_6.getKey()).ne("change_state_code", DataStateBPM.CHANGE_STATE_3.getKey())).and(j -> j.and(a -> a.ne("close_State_Code", DataStateBPM.CLOSE_STATE_5.getKey()).ne("close_State_Code", DataStateBPM.CLOSE_STATE_3.getKey())).or().isNull("close_State_Code"));
        }
        if (DIALOG_MORE.equals(functionType)) {
            // 合并合同：选择保存的合同、主合同、不是已终止的合同
            queryWrapper.eq("data_state_code", DataStateBPM.SAVE.getKey()).eq("data_type_code", DataStateBPM.DATA_TYPE_1.getKey()).ne("change_state_code", DataStateBPM.CHANGE_STATE_6.getKey());
            queryWrapper.isNull("parent_id");
            isQuery = false;
            queryWrapper.eq("contract_type_code", contractTypeCode);
            queryWrapper.eq("is_Check_Success", 1);
        }
        if (DIALOG_SEAL.equals(functionType)) {
            //合同用印：
            queryWrapper.eq("take_effect_code", 1).ne("data_source_code", "100008").and(e -> e.eq("close_state_code", 1).or().isNull("close_state_code").ne("change_state_code", 6).ne("change_state_code", 3));
            queryWrapper.apply("id not in (select s.id from bm_contract s, SG_CONTRACT_SEAL g where s.id = g.parent_id )");
            isQuery = false;
        }
        if (DIALOG_CHANGE.equals(functionType)) {
            System.out.printf("functionType：" + functionType);
            // 变更：仅可选择本人经办的合同数据，合同性质为‘主合同’，合同变更状态为‘未变更’或‘已变更’，合同生效状态为‘已生效’，合同关闭状态为‘未关闭’
            queryWrapper
                    .eq("take_effect_code", DataStateBPM.TAKE_EFFECT_3.getKey())
                    .and(i -> i.eq("data_type_code", DataStateBPM.DATA_TYPE_1.getKey()).or().eq("data_type_code", DataStateBPM.DATA_TYPE_4.getKey())).and(j -> j.eq("change_state_code", DataStateBPM.CHANGE_STATE_1.getKey()).or().eq("change_state_code", DataStateBPM.CHANGE_STATE_5.getKey())).and(j -> j.and(a -> a.eq("close_State_Code", DataStateBPM.CLOSE_STATE_1.getKey()).or().isNull("close_State_Code")));
        }
        if (DIALOG_APPLE_APP.equals(functionType)) {
            System.out.printf("functionType：" + functionType);
            queryWrapper.eq("take_effect_code", DataStateBPM.TAKE_EFFECT_1.getKey())
                    .and(i -> i.eq("data_type_code", DataStateBPM.DATA_TYPE_1.getKey()).or().eq("data_type_code", DataStateBPM.DATA_TYPE_2.getKey()).or().eq("data_type_code", DataStateBPM.DATA_TYPE_3.getKey()))
                    .ne("data_source_code", "100008")
                    .and(j -> j.eq("close_State_Code", DataStateBPM.CLOSE_STATE_1.getKey()).or().isNull("close_State_Code"));
        }
        if (DIALOG_APPLE_SPPLE_APP.equals(functionType)) {
            System.out.printf("functionType：" + functionType);
            queryWrapper.in("take_effect_code", 2, 3).and(o -> o.ne("data_type_code", DataStateBPM.DATA_TYPE_4.getKey()).ne("data_type_code", DataStateBPM.DATA_TYPE_5.getKey())).and(j -> j.eq("close_State_Code", DataStateBPM.CLOSE_STATE_1.getKey()).or().isNull("close_State_Code"));
        }
        if (DIALOG_CLOSE.equals(functionType)) {
            System.out.printf("functionType：" + functionType);
            // 关闭：仅可选择本人经办的合同数据，合同性质为‘主合同’\补录，合同变更状态为‘未变更’或‘已变更’，流程状态为 ‘审批完成’，合同关闭状态为‘未关闭’
            queryWrapper.eq("data_state_code", DataStateBPM.FINISH.getKey()).and(i -> i.eq("data_type_code", DataStateBPM.DATA_TYPE_1.getKey()).or().eq("data_type_code", DataStateBPM.DATA_TYPE_4.getKey())).and(j -> j.eq("change_state_code", DataStateBPM.CHANGE_STATE_1.getKey()).or().eq("change_state_code", DataStateBPM.CHANGE_STATE_5.getKey())).and(j -> j.and(a -> a.eq("close_State_Code", DataStateBPM.CLOSE_STATE_1.getKey())));
        }
        if (DIALOG_STOP.equals(functionType)) {
            System.out.printf("functionType：" + functionType);
            boolean aBoolean = json.containsKey("isStop") ? json.getBoolean("isStop") : false;
            if (aBoolean) {
                queryWrapper.ne("data_source_code", 100008);
            } else {
                queryWrapper.eq("data_source_code", 100008);
            }
            // 终止：仅可选择本人经办的合同数据，合同性质为‘主合同’，合同变更状态为‘未变更’或‘已变更’，合同生效状态为‘已生效’，合同关闭状态为‘未关闭’
            queryWrapper.eq("take_effect_code", DataStateBPM.TAKE_EFFECT_3.getKey()).and(i -> i.eq("data_type_code", DataStateBPM.DATA_TYPE_1.getKey()).or().eq("data_type_code", DataStateBPM.DATA_TYPE_4.getKey())).and(j -> j.eq("change_state_code", DataStateBPM.CHANGE_STATE_1.getKey()).or().eq("change_state_code", DataStateBPM.CHANGE_STATE_5.getKey())).and(j -> j.eq("close_State_Code", DataStateBPM.CLOSE_STATE_1.getKey()));
        }
        if (DIALOG_TURNOVER.equals(functionType)) {
            queryWrapper.eq("take_Effect_Code", 3).and(e -> e.eq("close_state_code", 1)).ne("change_state_code", 3).ne("change_state_code", 6);
        }
        //风险履行中心查询合同明细，查询已生效的数据、未关闭数据
        if (DIALOG_RISKHIS.equals(functionType)) {
            queryWrapper.eq("take_Effect_Code", 3).and(e -> e.eq("close_state_code", 1)).ne("change_state_code", 3).ne("change_state_code", 6);
        }
        if (StringUtils.isNotBlank(typeName)) {
            if ("补录变更".equals(typeName)) {
                queryWrapper.eq("data_type_code", 4);
            }
        }
        if (StringUtils.isNotBlank(fuzzyValue)) {
            queryWrapper.and(i -> i.like("contract_name", fuzzyValue).or().like("contract_code", fuzzyValue).or().like("contract_type", fuzzyValue));
        }
        Long functionId = DataAuthUtils.getFunctionIdByCode("contract_approval_ledger");
        if (isQuery) {
            DataAuthUtils.dataPermSql(queryWrapper, null, functionId, orgId);
        } else {
            queryWrapper.eq("create_org_id", orgId);
        }

        if (StringUtils.isNotBlank(sortName)) {
            queryWrapper.orderBy(true, order, "to_char(" + Utils.humpToLine2(sortName) + ")");
        } else {
            queryWrapper.orderBy(true, order, "create_time");
        }

        PageUtils<BmContract> page = bmContractMapper.queryPageList(pageUtils, queryWrapper);
        sw.stop();
        System.out.println("合同弹窗查询耗时：");
        System.out.println(sw.getTotalTimeMillis());
        return page;
    }

    @Override
    public BmContract getByContractCode(String contractCode) {
        QueryWrapper<BmContract> queryWrapper = new QueryWrapper<BmContract>().eq("contract_code", contractCode);
        return baseMapper.selectOne(queryWrapper);
    }

    @Override
    public void changeContractPerformState(String id, Integer state) {
        BmContract bmContract = this.getBmContract(id);
        bmContract.setPerformStateCode(state);
        if (state == 3) {
            bmContract.setPerformState("履行中");
        } else if (state == 4) {
            bmContract.setPerformState("异常履行");
        } else if (state == 5) {
            bmContract.setPerformState("履行完成");
        }
        //bmContract.setPerformState(state !=5 ? "异常履行" : "履行完成");
        this.updateById(bmContract);
    }

    /**
     * 条件方法
     *
     * @param queryWrapper
     * @param json
     */
    public void getFilter(QueryWrapper<BmContract> queryWrapper, JSONObject json) {
        //是否台账功能（登记只查自己的，台账和弹框查自己和数据权限的）
        boolean isQuery = json.containsKey("isQuery") ? json.getBoolean("isQuery") : false;
        String orgId = json.containsKey("orgId") ? json.getString("orgId") : "";
        String functionType = json.getString("functionType");
        //顺序字段
        String sortName = json.containsKey("sortName") ? json.getString("sortName") : null;
        //顺序
        boolean order = json.containsKey("order") ? json.getBoolean("order") : false;

        // 合同名称
        String contractName = json.containsKey("contractName") ? json.getString("contractName") : null;
        // 合同金额
        BigDecimal contractMoneyMin = json.containsKey("contractMoneyMin") ? json.getBigDecimal("contractMoneyMin") : null;
        BigDecimal contractMoneyMax = json.containsKey("contractMoneyMax") ? json.getBigDecimal("contractMoneyMax") : null;
        // 合同性质--补充、终止、主合同
        String dataTypeName = json.containsKey("dataTypeName") ? json.getString("dataTypeName") : null;
        // 金额类型
        String moneyTypeCode = json.containsKey("moneyTypeCode") ? json.getString("moneyTypeCode") : null;
        // 收支方向
        String revenueExpenditureCode = json.containsKey("revenueExpenditureCode") ? json.getString("revenueExpenditureCode") : null;
        // 对方签约主体
        String otherPartyName = json.containsKey("otherPartyName") ? json.getString("otherPartyName") : null;
        // 我方签约主体
        String ourPartyName = json.containsKey("ourPartyName") ? json.getString("ourPartyName") : null;
        // 是否范本
        Boolean isAtemplate = json.containsKey("isAtemplate") ? json.getBoolean("isAtemplate") : null;
        // 项目名称
        String projectNames = json.containsKey("projectNames") ? json.getString("projectNames") : null;
        // 集团合同编号
        String contractCode = json.containsKey("contractCode") ? json.getString("contractCode") : null;
        // 自定义合同编码
        String customCode = json.containsKey("customCode") ? json.getString("customCode") : null;
        // 是否集团重大合同
        Boolean whetherGroupMajor = json.containsKey("whetherGroupMajor") ? json.getBoolean("whetherGroupMajor") : null;
        // 来源系统
        String dataSource = json.containsKey("dataSource") ? json.getString("dataSource") : null;
        // 经办人
        String createPsnName = json.containsKey("createPsnName") ? json.getString("createPsnName") : null;
        // 经办单位
        String createOgnName = json.containsKey("createOgnName") ? json.getString("createOgnName") : null;
        // 经办部门
        String createDeptName = json.containsKey("createDeptName") ? json.getString("createDeptName") : null;
        //经办时间
        Date startTimeMin = json.containsKey("startTimeMin") ? json.getDate("startTimeMin") : null;
        Date startTimeMax = json.containsKey("startTimeMax") ? json.getDate("startTimeMax") : null;
        // 流程状态
        String dataStateCode = json.containsKey("dataStateCode") ? json.getString("dataStateCode") : null;
        String dataState = json.containsKey("dataState") ? json.getString("dataState") : null;
        // 变更状态
        String changeStateCode = json.containsKey("changeStateCode") ? json.getString("changeStateCode") : null;
        String changeState = json.containsKey("changeState") ? json.getString("changeState") : null;
        // 生效状态
        String takeEffectCode = json.containsKey("takeEffectCode") ? json.getString("takeEffectCode") : null;
        String takeEffectName = json.containsKey("takeEffectName") ? json.getString("takeEffectName") : null;
        //关闭状态
        String closeStateCode = json.containsKey("closeStateCode") ? json.getString("closeStateCode") : null;
        String closeState = json.containsKey("closeState") ? json.getString("closeState") : null;


        String suppleFlag = json.containsKey("suppleFlag") ? json.getString("suppleFlag") : null;
        String performFlag = json.containsKey("performFlag") ? json.getString("performFlag") : null;

        // 合同类型
        String queryContractType = json.containsKey("queryContractType") ? json.getString("queryContractType") : null;
        // 合同类型
        String contractTypeCode = json.containsKey("contractTypeCode") ? json.getString("contractTypeCode") : null;
        //生效时间
        Date beginTime = json.containsKey("beginTime") ? json.getDate("beginTime") : null;
        Date endTime = json.containsKey("endTime") ? json.getDate("endTime") : null;
        // 审批单号
        String approvalCode = json.containsKey("approvalCode") ? json.getString("approvalCode") : null;
        //合同性质编码 -补录
        String dataTypeCode = json.containsKey("dataTypeCode") ? json.getString("dataTypeCode") : null;
        // 是否集团名义合同
        Boolean whetherUnitMajor = json.containsKey("whetherUnitMajor") ? json.getBoolean("whetherUnitMajor") : null;
        //归档状态
        String archivedStateCode = json.containsKey("archivedStateCode") ? json.getString("archivedStateCode") : null;
        //履行状态
        String performStateCode = json.containsKey("performStateCode") ? json.getString("performStateCode") : null;
        //模糊搜索
        String fuzzyValue = json.getString("fuzzyValue");

        if (StringUtils.isNotBlank(contractName)) {
            queryWrapper.like("contract_name", contractName);
        }
        if (StringUtils.isNotBlank(projectNames)) {
            queryWrapper.like("project_Names", projectNames);
        }

        if (StringUtils.isNotBlank(approvalCode)) {
            queryWrapper.like("approval_Code", approvalCode);
        }
        if (StringUtils.isNotBlank(contractCode)) {
            queryWrapper.like("contract_code", contractCode);
        }
        if (StringUtils.isNotBlank(customCode)) {
            queryWrapper.like("custom_code", customCode);
        }
        if (StringUtils.isNotBlank(contractTypeCode)) {
            queryWrapper.likeRight("contract_Type_Code", contractTypeCode);
        }
        if (StringUtils.isNotBlank(queryContractType)) {
            queryWrapper.eq("contract_Type", queryContractType);
        }
        if (StringUtils.isNotBlank(otherPartyName)) {
            queryWrapper.like("other_party_name", otherPartyName);
        }
        if (StringUtils.isNotBlank(ourPartyName)) {
            queryWrapper.like("our_party_name", ourPartyName);
        }
        if (!ObjectUtils.isEmpty(startTimeMin)) {
            queryWrapper.ge("create_Time", startTimeMin);
        }
        if (!ObjectUtils.isEmpty(startTimeMax)) {
            queryWrapper.le("create_Time", startTimeMax);
        }
        if (contractMoneyMin != null && contractMoneyMin.doubleValue() > 0) {
            queryWrapper.ge("contract_money", contractMoneyMin);
        }
        if (contractMoneyMax != null && contractMoneyMax.doubleValue() > 0) {
            queryWrapper.le("contract_money", contractMoneyMax);
        }
        if (!ObjectUtils.isEmpty(beginTime)) {
            queryWrapper.ge("contract_take_effect_date", beginTime);
        }
        if (!ObjectUtils.isEmpty(endTime)) {
            queryWrapper.le("contract_take_effect_date", endTime);
        }
        if (StringUtils.isNotBlank(moneyTypeCode)) {
            queryWrapper.eq("money_type_code", moneyTypeCode);
        }
        if (StringUtils.isNotBlank(revenueExpenditureCode)) {
            queryWrapper.eq("revenue_expenditure_code", revenueExpenditureCode);
        }
        if (!ObjectUtils.isEmpty(whetherUnitMajor)) {
            queryWrapper.eq("whether_unit_major", whetherUnitMajor ? 1 : 0);
        }
        if (!ObjectUtils.isEmpty(whetherGroupMajor)) {
            queryWrapper.eq("whether_group_major", whetherGroupMajor ? 1 : 0);
        }
        if (!ObjectUtils.isEmpty(isAtemplate)) {
            queryWrapper.eq("is_atemplate", isAtemplate ? 1 : 0);
        }
        if (StringUtils.isNotBlank(dataStateCode)) {
            queryWrapper.eq("data_state_code", dataStateCode);
        }
        if (StringUtils.isNotBlank(dataState)) {
            queryWrapper.eq("data_state", dataState);
        }
        if (StringUtils.isNotBlank(takeEffectCode)) {
            queryWrapper.eq("take_effect_code", takeEffectCode);
        }
        if (StringUtils.isNotBlank(takeEffectName)) {
            queryWrapper.eq("take_effect_name", takeEffectName);
        }
        if (StringUtils.isNotBlank(dataTypeName)) {
            queryWrapper.eq("data_type_name", dataTypeName);
        }
        if (StringUtils.isNotBlank(dataTypeCode)) {
            queryWrapper.eq("data_type_code", dataTypeCode);
        }
        if (StringUtils.isNotBlank(changeStateCode)) {
            queryWrapper.eq("change_state_code", changeStateCode);
        }
        if (StringUtils.isNotBlank(changeState)) {
            queryWrapper.eq("change_state", changeState);
        }
        if (StringUtils.isNotBlank(performStateCode)) {
            queryWrapper.eq("perform_state_code", performStateCode);
        }
        if (StringUtils.isNotBlank(archivedStateCode)) {
            queryWrapper.eq("archived_state_code", archivedStateCode);
        }
        if (StringUtils.isNotBlank(createPsnName)) {
            queryWrapper.eq("create_Psn_Name", createPsnName);
        }
        if (StringUtils.isNotBlank(createOgnName)) {
            queryWrapper.eq("create_Ogn_Name", createOgnName);
        }
        if (StringUtils.isNotBlank(createDeptName)) {
            queryWrapper.eq("create_Dept_Name", createDeptName);
        }
        if (StringUtils.isNotBlank(dataSource)) {
            queryWrapper.eq("data_Source", dataSource);
        }
        if (StringUtils.isNotBlank(closeStateCode)) {
            queryWrapper.eq("close_state_code", closeStateCode);
        }
        if (StringUtils.isNotBlank(closeState)) {
            queryWrapper.eq("close_state", closeState);
        }
        if (StringUtils.isNotBlank(fuzzyValue)) {
            queryWrapper.and(i ->
                    i.like("contract_name", fuzzyValue)
                            .or().like("contract_code_version", fuzzyValue)
                            .or().like("contract_type", fuzzyValue)
                            .or().like("approval_code", fuzzyValue)
                            .or().like("custom_code", fuzzyValue)
                            .or().like("other_party_name", fuzzyValue)
                            .or().like("our_party_name", fuzzyValue)
            );
        }
        if (isQuery) {
            Long functionId = DataAuthUtils.getFunctionIdByCode("contract_ledger_index");
            DataAuthUtils.dataPermSql(queryWrapper, null, functionId, orgId);
            queryWrapper.notIn("data_state_code", 1);
        } else {
            if (StringUtils.isNotBlank(suppleFlag)) {
                queryWrapper.in("data_type_code", Arrays.asList(4, 5));
            }
            if (PERFORM.equals(functionType)) {
                queryWrapper.in("data_type_code", Arrays.asList(1, 4));
            }
            if (DIALOG_PLACE.equals(functionType)) {
                queryWrapper.eq("data_type_code", 1);
                queryWrapper.eq("take_effect_code", 3);
                queryWrapper.eq("data_state_code", 5);
                queryWrapper.eq("close_state_code", 1);
                queryWrapper.eq("data_source_code", "100008");
            }
            if (CONTRACT_LIST.equals(functionType)) {
                queryWrapper.in("data_type_code", Arrays.asList(1, 2, 3));
                queryWrapper.in("data_state_code", Arrays.asList(1, 3, 4));
            }
            if (CONTRACT_SUPPLE.equals(functionType)) {
                queryWrapper.ne("take_effect_code", 3);
            }
            queryWrapper.eq("create_org_id", orgId);
        }
        if (StringUtils.isNotBlank(sortName)) {
            queryWrapper.orderBy(true, order, Utils.humpToLine2(sortName));
        } else {
//            queryWrapper.orderBy(true, order, "create_time");
            queryWrapper.orderByDesc("create_time desc,id");
        }
    }

    /**
     * 条件方法
     *
     * @param queryWrapper
     * @param json
     */
    public void getRiskFilter(QueryWrapper<BmContract> queryWrapper, JSONObject json) {
        //是否台账功能（登记只查自己的，台账和弹框查自己和数据权限的）
        boolean isQuery = json.containsKey("isQuery") ? json.getBoolean("isQuery") : false;
        String orgId = json.containsKey("orgId") ? json.getString("orgId") : "";
        String functionType = json.getString("functionType");
        //顺序字段
        String sortName = json.containsKey("sortName") ? json.getString("sortName") : null;
        //顺序
        boolean order = json.containsKey("order") ? json.getBoolean("order") : false;

        // 合同名称
        String contractName = json.containsKey("contractName") ? json.getString("contractName") : null;
        // 合同金额
        BigDecimal contractMoneyMin = json.containsKey("contractMoneyMin") ? json.getBigDecimal("contractMoneyMin") : null;
        BigDecimal contractMoneyMax = json.containsKey("contractMoneyMax") ? json.getBigDecimal("contractMoneyMax") : null;
        // 合同性质--补充、终止、主合同
        String dataTypeName = json.containsKey("dataTypeName") ? json.getString("dataTypeName") : null;
        // 金额类型
        String moneyTypeCode = json.containsKey("moneyTypeCode") ? json.getString("moneyTypeCode") : null;
        // 收支方向
        String revenueExpenditureCode = json.containsKey("revenueExpenditureCode") ? json.getString("revenueExpenditureCode") : null;
        // 对方签约主体
        String otherPartyName = json.containsKey("otherPartyName") ? json.getString("otherPartyName") : null;
        // 我方签约主体
        String ourPartyName = json.containsKey("ourPartyName") ? json.getString("ourPartyName") : null;
        // 是否范本
        Boolean isAtemplate = json.containsKey("isAtemplate") ? json.getBoolean("isAtemplate") : null;
        // 项目名称
        String projectNames = json.containsKey("projectNames") ? json.getString("projectNames") : null;
        // 集团合同编号
        String contractCode = json.containsKey("contractCode") ? json.getString("contractCode") : null;
        // 自定义合同编码
        String customCode = json.containsKey("customCode") ? json.getString("customCode") : null;
        // 是否集团重大合同
        Boolean whetherGroupMajor = json.containsKey("whetherGroupMajor") ? json.getBoolean("whetherGroupMajor") : null;
        // 经办人
        String createPsnName = json.containsKey("createPsnName") ? json.getString("createPsnName") : null;
        // 经办单位
        String createOgnName = json.containsKey("createOgnName") ? json.getString("createOgnName") : null;
        // 经办部门
        String createDeptName = json.containsKey("createDeptName") ? json.getString("createDeptName") : null;
        //经办时间
        Date startTimeMin = json.containsKey("startTimeMin") ? json.getDate("startTimeMin") : null;
        Date startTimeMax = json.containsKey("startTimeMax") ? json.getDate("startTimeMax") : null;
        // 生效状态
        String takeEffectCode = json.containsKey("takeEffectCode") ? json.getString("takeEffectCode") : null;
        String takeEffectName = json.containsKey("takeEffectName") ? json.getString("takeEffectName") : null;
        // 流程状态
        String dataStateCode = json.containsKey("dataStateCode") ? json.getString("dataStateCode") : null;
        String dataState = json.containsKey("dataState") ? json.getString("dataState") : null;
        String suppleFlag = json.containsKey("suppleFlag") ? json.getString("suppleFlag") : null;
        String performFlag = json.containsKey("performFlag") ? json.getString("performFlag") : null;

        // 合同类型
        String contractType = json.containsKey("contractType") ? json.getString("contractType") : null;
        //生效时间
        Date beginTime = json.containsKey("beginTime") ? json.getDate("beginTime") : null;
        Date endTime = json.containsKey("endTime") ? json.getDate("endTime") : null;
        //承办人
        String riskPsn = json.containsKey("riskPsn") ? json.getString("riskPsn") : null;
        String fuzzyValue = json.getString("fuzzyValue");

        if (StringUtils.isNotBlank(contractName)) {
            queryWrapper.like("contract_name", contractName);
        }
        if (StringUtils.isNotBlank(projectNames)) {
            queryWrapper.like("project_Names", projectNames);
        }

        if (StringUtils.isNotBlank(contractCode)) {
            queryWrapper.like("contract_code", contractCode);
        }
        if (StringUtils.isNotBlank(customCode)) {
            queryWrapper.like("custom_code", customCode);
        }
        if (StringUtils.isNotBlank(contractType)) {
            queryWrapper.eq("contract_Type", contractType);
        }
        if (StringUtils.isNotBlank(otherPartyName)) {
            queryWrapper.like("other_party_name", otherPartyName);
        }
        if (StringUtils.isNotBlank(ourPartyName)) {
            queryWrapper.like("our_party_name", ourPartyName);
        }
        if (!ObjectUtils.isEmpty(startTimeMin)) {
            queryWrapper.ge("create_Time", startTimeMin);
        }
        if (!ObjectUtils.isEmpty(startTimeMax)) {
            queryWrapper.le("create_Time", startTimeMax);
        }
        if (contractMoneyMin != null && contractMoneyMin.doubleValue() > 0) {
            queryWrapper.ge("contract_money", contractMoneyMin);
        }
        if (contractMoneyMax != null && contractMoneyMax.doubleValue() > 0) {
            queryWrapper.le("contract_money", contractMoneyMax);
        }
        if (!ObjectUtils.isEmpty(beginTime)) {
            queryWrapper.ge("contract_take_effect_date", beginTime);
        }
        if (!ObjectUtils.isEmpty(endTime)) {
            queryWrapper.le("contract_take_effect_date", endTime);
        }
        if (StringUtils.isNotBlank(moneyTypeCode)) {
            queryWrapper.eq("money_type_code", moneyTypeCode);
        }
        if (StringUtils.isNotBlank(revenueExpenditureCode)) {
            queryWrapper.eq("revenue_expenditure_code", revenueExpenditureCode);
        }
        if (!ObjectUtils.isEmpty(whetherGroupMajor)) {
            queryWrapper.eq("whether_group_major", whetherGroupMajor ? 1 : 0);
        }
        if (!ObjectUtils.isEmpty(isAtemplate)) {
            queryWrapper.eq("is_atemplate", isAtemplate ? 1 : 0);
        }
        if (StringUtils.isNotBlank(takeEffectCode)) {
            queryWrapper.eq("take_effect_code", takeEffectCode);
        }
        if (StringUtils.isNotBlank(takeEffectName)) {
            queryWrapper.eq("take_effect_name", takeEffectName);
        }
        if (StringUtils.isNotBlank(dataTypeName)) {
            queryWrapper.eq("data_type_name", dataTypeName);
        }
        if (StringUtils.isNotBlank(createPsnName)) {
            queryWrapper.eq("create_Psn_Name", createPsnName);
        }
        if (StringUtils.isNotBlank(createOgnName)) {
            queryWrapper.eq("create_Ogn_Name", createOgnName);
        }
        if (StringUtils.isNotBlank(createDeptName)) {
            queryWrapper.eq("create_Dept_Name", createDeptName);
        }
        if (StringUtils.isNotBlank(riskPsn)) {
            queryWrapper.eq("risk_psn", riskPsn);
        }
        if (StringUtils.isNotBlank(fuzzyValue)) {
            queryWrapper.and(i -> i.like("contract_name", fuzzyValue).or().like("contract_code", fuzzyValue).or().like("contract_type", fuzzyValue).or().like("approval_code", fuzzyValue));
        }
        if (StringUtils.isNotBlank(dataState)) {
            queryWrapper.like("data_state", dataState);
        }
        if (isQuery) {
            Long functionId = DataAuthUtils.getFunctionIdByCode("contract_ledger_index");
            DataAuthUtils.dataPermSql(queryWrapper, null, functionId, orgId);
            queryWrapper.notIn("data_state_code", Arrays.asList(1));
            queryWrapper.isNotNull("risk_psn_code");
            queryWrapper.in("data_type_code", Arrays.asList(1, 2, 3));

        }
        if (StringUtils.isNotBlank(sortName)) {
            queryWrapper.orderBy(true, order, Utils.humpToLine2(sortName));
        } else {
            queryWrapper.orderByDesc("create_time desc,id");
        }
    }

    private JSONObject checkProject(List<BmContractProject> projectList, String id, int dataTypeCode, String parentId) {
        if (dataTypeCode == 3) {
            JSONObject returnJson = new JSONObject();
            returnJson.put("state", "success");
            returnJson.put("message", "校验通过");
            return returnJson;
        }
        List<BmContractProject> addOrUpdateProjectList = new ArrayList<>();
        List<BmContractProject> deleteProjectList = new ArrayList<>();
        //数据库中查出的所有当前合同的项目集合
        List<BmContractProject> dataProjects = bmContractProjectService.list(new QueryWrapper<BmContractProject>().eq("parent_id", id));
        //数据库中的项目不是空
        if (!CollectionUtils.isEmpty(dataProjects)) {
            // 如果子表项目如果是空，就不需要判断了，直接跳过即可，所以这里只需要判断不是空的情况
            if (!CollectionUtils.isEmpty(projectList)) {
                //新增数据过滤
                rightManage(projectList, addOrUpdateProjectList, deleteProjectList);

                //查出前台数据中，对比上次，删除的数据
                List<BmContractProject> deleteList = notIncludeProjectList(dataProjects, projectList);
                rightManage2(deleteList, deleteProjectList);
                // 项目校验
                JSONObject jsonObject = sgProjectManageService.checkData(addOrUpdateProjectList, deleteProjectList, dataTypeCode, parentId);
                if ("success".equals(jsonObject.getString("state"))) {
                    updateProjectList(projectList);
                }
                return jsonObject;
            } else {
                //前台项目数据是空，说明前台将项目数据清空了
                rightManage2(dataProjects, deleteProjectList);
                // 项目不需要校验，直接还原即可
                sgProjectManageService.updateProject(addOrUpdateProjectList, deleteProjectList, null, dataTypeCode, parentId);
            }
        } else {
            if (!CollectionUtils.isEmpty(projectList)) {
                rightManage(projectList, addOrUpdateProjectList, deleteProjectList);
                JSONObject jsonObject = sgProjectManageService.checkData(addOrUpdateProjectList, deleteProjectList, dataTypeCode, parentId);
                if ("success".equals(jsonObject.getString("state"))) {
                    updateProjectList(projectList);
                }
                return jsonObject;
            }
        }
        JSONObject returnJson = new JSONObject();
        returnJson.put("state", "success");
        returnJson.put("message", "校验通过");
        return returnJson;
    }

    /**
     * Description  筛选“是否管理费”
     * Param [projectList]
     * Date 2022/6/21
     */
    private void rightManage(List<BmContractProject> projectList, List<BmContractProject> addOrUpdateProjectList, List<BmContractProject> deleteProjectList) {
        if (!CollectionUtils.isEmpty(projectList)) {
            for (BmContractProject project : projectList) {
                if ("科技项目".equals(project.getProjectType())) {
                    if (project.getWhether() != null) {
                        if (!project.getWhether()) {
                            addOrUpdateProjectList.add(project);
                        } else {
                            if (project.getOccupiedAmount() != null) {
                                deleteProjectList.add(project);
                            }
                        }
                    }
                } else {
                    addOrUpdateProjectList.add(project);
                }
            }
        }
    }

    /**
     * Description 判断删除的数据中，符合还原的项目
     * Param [projectList, addOrUpdateProjectList, deleteProjectList]
     * Return void
     * Author chenjie
     * Date 2022/6/21
     */
    private void rightManage2(List<BmContractProject> projectList, List<BmContractProject> deleteProjectList) {
        if (!CollectionUtils.isEmpty(projectList)) {
            for (BmContractProject BmContractProject : projectList) {
                if ("科技项目类".equals(BmContractProject.getProjectType())) {
                    if (BmContractProject.getWhether() != null && !BmContractProject.getWhether()) {
                        deleteProjectList.add(BmContractProject);
                    }
                } else {
                    deleteProjectList.add(BmContractProject);
                }
            }
        }
    }

    /**
     * Description 判断第一个list中的每一个元素，是否在第二个list中出现过
     * Param [projectList1, projectList2]
     * Date 2022/6/21
     */
    private List<BmContractProject> notIncludeProjectList(List<BmContractProject> projectList1, List<BmContractProject> projectList2) {
        List<BmContractProject> list = new ArrayList<>();
        for (BmContractProject contractProject1 : projectList1) {
            boolean bool = false;
            for (BmContractProject contractProject2 : projectList2) {
                if (contractProject1.getId().equals(contractProject2.getId())) {
                    bool = true;
                    break;
                }
            }
            if (!bool) {
                list.add(contractProject1);
            }
        }
        return list;
    }

    /**
     * @Description 项目到最后时，需要更新占用金额，为 分配金额
     * @Param [projectList]
     * @Date 2022/6/21
     */
    private void updateProjectList(List<BmContractProject> projectList) {
        for (BmContractProject bmContractProject : projectList) {
            if ("科技项目".equals(bmContractProject.getProjectType())) {
                if (bmContractProject.getWhether() != null && !bmContractProject.getWhether()) {
                    if (bmContractProject.getProjectChangeMoneyTypeCode() != null && bmContractProject.getProjectChangeMoneyTypeCode() != 2) {
                        bmContractProject.setOccupiedAmount(bmContractProject.getChangeAssignMoney());
                    } else {
                        bmContractProject.setOccupiedAmount(bmContractProject.getAssignMoney());
                    }
                } else {
                    bmContractProject.setOccupiedAmount(null);
                }
            } else {
                if (bmContractProject.getProjectChangeMoneyTypeCode() != null && bmContractProject.getProjectChangeMoneyTypeCode() != 2) {
                    bmContractProject.setOccupiedAmount(bmContractProject.getChangeAssignMoney());
                } else {
                    bmContractProject.setOccupiedAmount(bmContractProject.getAssignMoney());
                }
            }
        }
    }

    /**
     * 拼接子表字段
     *
     * @param bmContract
     * @param projectList
     * @param textList
     */
    private void integrationChild(BmContract bmContract, List<BmContractProject> projectList, List<BmContractText> textList) {

        String projectString = "";
        if (!CollectionUtils.isEmpty(projectList)) {
            for (BmContractProject bmContractProject : projectList) {
                if ("".equals(projectString)) {
                    projectString = bmContractProject.getProjectName();
                } else {
                    projectString += "、" + bmContractProject.getProjectName();
                }
            }
            bmContract.setProjectNames(projectString);
        } else {
            bmContract.setProjectNames(null);
        }
        String textString = "";
        if (!CollectionUtils.isEmpty(textList)) {
            for (BmContractText bmContractText : textList) {
                if ("".equals(textString)) {
                    textString = bmContractText.getFileFrom();
                } else {
                    textString += "、" + bmContractText.getFileFrom();
                }
            }
            bmContract.setFileTypes(textString);
        } else {
            bmContract.setFileTypes(null);
        }
    }

    /**
     * 生成合同编码
     *
     * @param bmContract
     * @return 合同编码
     */
    @Override
    public String createContractCode2(BmContract bmContract) {
        createContractCodeCommon(bmContract);
        return bmContract.getContractCode();
    }

    @Override
    public void takeEffectContract(JSONArray jsonArray) {
        List<BmContract> list = new ArrayList<>();
        BmContract bmContract = null;
        for (Object obj : jsonArray) {
            JSONObject json = (JSONObject) obj;
            bmContract = this.getById(json.getString("id"));
            if (StringUtils.isNotBlank(bmContract.getOriginalContractId())) {
                bmContract.setChangeState(DataStateBPM.CHANGE_STATE_5.getValue());
                bmContract.setChangeStateCode(DataStateBPM.CHANGE_STATE_5.getKey());
            }
            bmContract.setTakeEffectCode(3);
            bmContract.setTakeEffectName("已生效");
            list.add(bmContract);
        }
        this.updateBatchById(list);
        //补录合同生效推送数据
        for (BmContract contract : list) {
            bmContractToMainService.sendToMainContractDetail(contract.getId());
        }
    }

    @Override
    public PageUtils<BmContract> queryForSuppleDialog(JSONObject json) {
        StopWatch sw = new StopWatch();
        sw.start();
        PageUtils<BmContract> pageUtils = new PageUtils<>(json);
        QueryWrapper<BmContract> queryWrapper = new QueryWrapper<BmContract>();

        String id = json.getString("id");
        String functionType = json.getString("functionType");
        String contractType = json.containsKey("contractType") ? json.getString("contractType") : null;
        String sealPurposeId = json.containsKey("sealPurposeId") ? json.getString("sealPurposeId") : null;

        //判断是否位合同补录变更
        String typeName = json.containsKey("typeName") ? json.getString("typeName") : null;
        //是否台账功能（登记只查自己的，台账和弹框查自己和数据权限的）
        boolean isQuery = json.containsKey("isQuery") ? json.getBoolean("isQuery") : false;
        String orgId = json.containsKey("orgId") ? json.getString("orgId") : "";
        //顺序字段
        String sortName = json.containsKey("sortName") ? json.getString("sortName") : null;
        //顺序
        boolean order = json.containsKey("order") ? json.getBoolean("order") : false;
        //模糊搜索
        String fuzzyValue = json.getString("fuzzyValue");
        if (StringUtils.isNotBlank(id)) {
            queryWrapper.ne("id", id);
        }
        if (StringUtils.isNotBlank(contractType)) {
            queryWrapper.eq("contract_code", contractType);
        }
        // 如果是合同补充用印则查询已用印且未关闭合同数据
        if (StringUtils.isNotBlank(sealPurposeId)) {
            queryWrapper.eq("take_effect_code", 2).and(i -> i.eq("close_state_code", 1));
            return page(new PageUtils<BmContract>(json), queryWrapper);
        }

        if (DIALOG_SUPPLE_CHANGE.equals(functionType)) {
            System.out.printf("functionType：" + functionType);
            // 变更：仅可选择本人经办的补录主合同数据，合同性质为‘补录合同’，合同变更状态为‘未变更’或‘已变更’，合同生效状态为‘已生效’，合同关闭状态为‘未关闭’
            queryWrapper.and(i -> i.eq("data_type_code", DataStateBPM.DATA_TYPE_4.getKey())).and(j -> j.eq("change_state_code", DataStateBPM.CHANGE_STATE_1.getKey()).or().eq("change_state_code", DataStateBPM.CHANGE_STATE_5.getKey())).and(j -> j.and(a -> a.eq("close_State_Code", DataStateBPM.CLOSE_STATE_1.getKey()).or().isNull("close_State_Code")));
        }

        if (StringUtils.isNotBlank(fuzzyValue)) {
            queryWrapper.and(i -> i.like("contract_name", fuzzyValue).or().like("contract_code", fuzzyValue).or().like("contract_type", fuzzyValue));
        }
        Long functionId = DataAuthUtils.getFunctionIdByCode("contract_approval_ledger");
        if (isQuery) {
            DataAuthUtils.dataPermSql(queryWrapper, null, functionId, orgId);
        } else {
            queryWrapper.eq("create_org_id", orgId);
        }

        if (StringUtils.isNotBlank(sortName)) {
            queryWrapper.orderBy(true, order, "to_char(" + Utils.humpToLine2(sortName) + ")");
        } else {
            queryWrapper.orderBy(true, order, "create_time");
        }

        PageUtils<BmContract> page = bmContractMapper.queryPageList(pageUtils, queryWrapper);
        sw.stop();
        System.out.println(sw.getTotalTimeMillis());
        return page;
    }

    private void createContractCodeCommon(BmContract bmContract) {
        if (bmContract.getDataTypeCode() == 1) {
            String version = "V00";//第六段3位:版本号，例如:V00 表示1.0版本。
            String bmContractCode = getBmContractCode(bmContract);
            bmContract.setContractCode(bmContractCode);
            bmContract.setVersion(version);
            bmContract.setContractCodeVersion(bmContractCode + version);
        }
        if (bmContract.getDataTypeCode() == 2) {
            //修改变更合同编码为原合同编码
            bmContract.setContractCode(bmContract.getOriginalContractCode());
            String version = getVersion(bmContract);
            bmContract.setVersion(version);
            bmContract.setContractCodeVersion(bmContract.getOriginalContractCode() + bmContract.getVersion());
        }
        if (bmContract.getDataTypeCode() == 4) {
            String version = "V00";//第六段3位:版本号，例如:V00 表示1.0版本。

            String bmContractCode = getBmContractCode(bmContract);
            bmContract.setContractCode(bmContractCode);
            bmContract.setVersion(version);
            bmContract.setContractCodeVersion(bmContractCode + version);
        }
        if (bmContract.getDataTypeCode() == 5) {
            //修改变更合同编码为原合同编码
            bmContract.setContractCode(bmContract.getOriginalContractCode());
            String version = getVersion(bmContract);
            bmContract.setVersion(version);
            bmContract.setContractCodeVersion(bmContract.getOriginalContractCode() + bmContract.getVersion());
        }
        if (bmContract.getDataTypeCode() == 3) {
            bmContract.setVersion("V99");
            bmContract.setContractCode(bmContract.getOriginalContractCode());
            bmContract.setContractCodeVersion(bmContract.getContractCode() + "V99");
        }
    }

    private String getContractTypeCode(String code, Integer level) {
        if (level == 2) {
            return code;
        } else if (level == 3) {
            SgContractType typeCode = sgContractTypeService.getOne(new QueryWrapper<SgContractType>().eq("type_code", code));
            SgContractType contractType = sgContractTypeService.getById(typeCode.getParentId());
            return contractType.getTypeCode();
        }
        return code;
    }

    public String getVersion(BmContract bmContract) {
        String lockKey = "contractVersion";
        RLock redissonLock = redisson.getLock(lockKey);
        System.err.println("lockKey:" + lockKey);
        redissonLock.lock();
        System.err.println("contractVersion加锁");
        String versionReturn = "";
        try {
            QueryWrapper<BmContract> wrapper = new QueryWrapper<>();
            wrapper.eq("original_contract_id", bmContract.getOriginalContractId());
            wrapper.orderByDesc("create_time");
            wrapper.last("limit 1");
            BmContract parent = getOne(wrapper);
            String version = "";
            if (parent != null) {
                version = parent.getVersion();
            } else {
                version = "V00";
            }
            //计算变更合同编码
            int number = Integer.parseInt(version.substring(1));
            number++;
            versionReturn = "V" + String.format("%02d", number);

        } finally {
            redissonLock.unlock();
            System.err.println("contractVersion解锁");
        }
        return versionReturn;
    }

    public String getBmContractCode(BmContract bmContract) {
        String business = bmContract.getDataTypeCode() == 4 || bmContract.getDataTypeCode() == 5 ? "B" : "A";//第一段1位:表示业务类型，例如:A表示新增，B表示历史。默认A
        String unit = bmContract.getCreateOgnId();//第二段8位:单位编码，采用主数据8位单位流水编码。
        String typeCode = getContractTypeCode(bmContract.getContractTypeCode(), bmContract.getContractTypeLevel());//第三段4位:表示合同的-二分类编码，例如:CG02表示原燃料采购合同。
        SimpleDateFormat format = new SimpleDateFormat("yyMM");
        String dateTime = format.format(new Date());//第四段4位:日期，年度后两位+月度，例如:2408表示2024年8月。
        return kvsequenceService.createNextSequenceString(business + unit + typeCode + dateTime, 5, bmContract.getId());
    }

    @Override
    @Transactional
    public void saveDataFinish(String businessKey) {
        BmContract bmContract = queryDataById(businessKey);
        finishAfter(bmContract);
    }

    @Override
    @Transactional
    public void saveDataStop(String businessKey) {
        BmContract bmContract = queryDataById(businessKey);
        if (bmContract.getDataTypeCode() == 2 || bmContract.getDataTypeCode() == 3) {
            BmContract old = getById(bmContract.getOriginalContractId());
            old.setChangeState("未变更");
            old.setChangeStateCode(1);
            saveOrUpdate(old);
            log.info("流程终止按钮点击后需要改原合同状态修改为未变更 saveDataStop");
        }
    }

    @Transactional
    public void finishAfter(BmContract bmContract) {
        List<BmContract> updateList = new ArrayList<>();
        bmContract.setTakeEffectName("待用印");
        bmContract.setTakeEffectCode(1);
        //合同流程完成时保存结束时间
        bmContract.setApprovalStartEnd(new Date());

        //1.主合同 生成用印信息，更改状态
        if (bmContract.getDataTypeCode() == 1) {
            sealStamp(bmContract);
            bmContract.setPerformState("履行中");
            bmContract.setPerformStateCode(3);
            bmContract.setPerformanceState("未计划");
            bmContract.setPerformanceStateCode(1);
        }
        if (bmContract.getDataTypeCode() == 2) {
//            BmContract contractOld = getById(bmContract.getOriginalContractId());
//            contractOld.setChangeState("已变更");
//            contractOld.setChangeStateCode(5);
//            updateList.add(contractOld);
            sealStamp(bmContract);
            bmContract.setPerformState("履行中");
            bmContract.setPerformStateCode(3);
            bmContract.setPerformanceState("未计划");
            bmContract.setPerformanceStateCode(1);
        }
        if (bmContract.getDataTypeCode() == 3) {
            BmContract contractOld = getById(bmContract.getOriginalContractId());
            bmContract.setPerformState("履行中");
            bmContract.setPerformStateCode(3);
//            contractOld.setChangeState("变更中");
//            contractOld.setChangeStateCode(6);
            updateList.add(contractOld);
            sealStamp(bmContract);
        }
        updateList.add(bmContract);
        saveOrUpdateBatch(updateList);
    }

    public void sealStamp(BmContract bmContract) {
        String uuid = StringUtil.makeUUID();
        List<SgSealApprovalDetail> detailList = new ArrayList<>();
        String sealNames = "";
        StringBuilder sealIds = new StringBuilder();
        String sealTypes = "";
        StringBuilder sealTypeIds = new StringBuilder();
        String sealAdmins = "";
        StringBuilder sealAdminIds = new StringBuilder();

        List<BmContractSeal> list = bmContractSealService.list(new QueryWrapper<BmContractSeal>().eq("parent_id", bmContract.getId()));
        if (!CollectionUtils.isEmpty(list)) {
            for (BmContractSeal bmContractSeal : list) {
                if ("".equals(sealNames)) {
                    sealNames = bmContractSeal.getSealName();
                    sealIds = new StringBuilder(bmContractSeal.getSealId());
                } else {
                    sealNames += "、" + bmContractSeal.getSealName();
                    sealIds.append(",").append(bmContractSeal.getSealId());
                }
                if ("".equals(sealTypes)) {
                    sealTypes = bmContractSeal.getSealType();
                    sealTypeIds = new StringBuilder(bmContractSeal.getSealTypeId());
                } else {
                    sealTypes += "、" + bmContractSeal.getSealType();
                    sealTypeIds.append(",").append(bmContractSeal.getSealTypeId());
                }
                if ("".equals(sealAdmins)) {
                    sealAdmins = bmContractSeal.getSealAdmin();
                    sealAdminIds = new StringBuilder(bmContractSeal.getSealAdminId());
                } else {
                    sealAdmins += "、" + bmContractSeal.getSealAdmin();
                    sealAdminIds.append(",").append(bmContractSeal.getSealAdminId());
                }

                SgSealApprovalDetail sgSealApprovalDetail = new SgSealApprovalDetail();
                sgSealApprovalDetail.setId(StringUtil.makeUUID());
                sgSealApprovalDetail.setParentId(uuid);

                sgSealApprovalDetail.setSealName(bmContractSeal.getSealName());
                sgSealApprovalDetail.setSealId(bmContractSeal.getSealId());
                sgSealApprovalDetail.setSealType(bmContractSeal.getSealType());
                sgSealApprovalDetail.setSealTypeId(bmContractSeal.getSealTypeId());
                sgSealApprovalDetail.setSealNumberOld(String.valueOf(bmContractSeal.getSealNumber()));
                sgSealApprovalDetail.setPrintsNumberOld(String.valueOf(bmContractSeal.getPrintsNumber()));
                sgSealApprovalDetail.setSealAdminOld(bmContractSeal.getSealAdmin());
                sgSealApprovalDetail.setSealAdminIdOld(bmContractSeal.getSealAdminId());
                sgSealApprovalDetail.setDataState("待用印");
                sgSealApprovalDetail.setDataStateCode(1);
                //当选择印章类型为‘法人签字’时，流程审批完成后数据推送为待生效，实际用印数据与申请数据保持一致，用印时间取流程审批完成时间
                if ("法人签字".equals(bmContractSeal.getSealType())) {
                    sgSealApprovalDetail.setDataState("已用印");
                    sgSealApprovalDetail.setDataStateCode(4);
                    sgSealApprovalDetail.setSealNumber(String.valueOf(bmContractSeal.getSealNumber()));
                    sgSealApprovalDetail.setPrintsNumber(String.valueOf(bmContractSeal.getPrintsNumber()));
                    sgSealApprovalDetail.setOurSealTime(new Date());
                }
                sgSealApprovalDetail.setCreateTime(new Date());
                detailList.add(sgSealApprovalDetail);
            }
        }
        SgSealApproval sgSealApproval = new SgSealApproval();
        sgSealApproval.setId(uuid);
        sgSealApproval.setContractName(bmContract.getContractName());
        sgSealApproval.setContractId(bmContract.getId());
        sgSealApproval.setContractCode(bmContract.getContractCode());
        sgSealApproval.setSealPurpose("合同用印");
        sgSealApproval.setSealPurposeId("0");
        sgSealApproval.setTakeEffectName("待用印");
        sgSealApproval.setTakeEffectCode(1);
        if ("法人签字".equals(sealTypes)) {
            sgSealApproval.setTakeEffectName("待生效");
            sgSealApproval.setTakeEffectCode(2);
        }
        sgSealApproval.setSealNames(sealNames);
        sgSealApproval.setSealIds(sealIds.toString());
        sgSealApproval.setSealTypes(sealTypes);
        sgSealApproval.setSealTypeIds(sealTypeIds.toString());
        sgSealApproval.setSealAdmins(sealAdmins);
        sgSealApproval.setSealAdminIds(sealAdminIds.toString());
        sgSealApproval.setCreateOgnId(bmContract.getCreateOgnId());
        sgSealApproval.setCreateOgnName(bmContract.getCreateOgnName());
        sgSealApproval.setCreateDeptId(bmContract.getCreateDeptId());
        sgSealApproval.setCreateDeptName(bmContract.getCreateDeptName());
        sgSealApproval.setCreatePsnId(bmContract.getCreatePsnId());
        sgSealApproval.setCreatePsnName(bmContract.getCreatePsnName());
        sgSealApproval.setCreatePsnFullId(bmContract.getCreatePsnFullId());
        sgSealApproval.setCreatePsnFullName(bmContract.getCreatePsnFullName());
        sgSealApproval.setCreateOrgId(bmContract.getCreateOrgId());
        sgSealApproval.setCreateOrgName(bmContract.getCreateOrgName());
        sgSealApproval.setCreateGroupId(bmContract.getCreateGroupId());
        sgSealApproval.setCreateGroupName(bmContract.getCreateGroupName());
        sgSealApproval.setCreateTime(new Date());
        sgSealApproval.setDataState(DataStateBPM.FINISH.getValue());
        sgSealApproval.setDataStateCode(DataStateBPM.FINISH.getKey());
        sgSealApprovalService.saveOrUpdate(sgSealApproval);
        if (!CollectionUtils.isEmpty(detailList)) {
            sgSealApprovalDetailService.saveOrUpdateBatch(detailList);
        }
    }

    @Override
    @Transactional
    public ApiJson updateContractPerform(String params) {
        log.info("合同履行状态更新及风险上报接口开始：");
        log.info("{}, params: {}", "params", params);
        StopWatch sw = new StopWatch();
        sw.start();
        JSONObject json = JSONObject.parseObject(params);

        //合同名称
        String contractCode = json.containsKey("contractCode") ? json.getString("contractCode") : "";
        if (StringUtils.isBlank(contractCode)) {
            return ApiJson.fail(ApiJson.KEY_CODE, 601).msg("合同编码必须有值！");
        }

        //履行状态
        Integer performStateCode = json.containsKey("performStateCode") ? json.getInteger("performStateCode") : null;
        if (performStateCode == null) {
            return ApiJson.fail(ApiJson.KEY_CODE, 602).msg("履行状态必须有值！");
        }

        //来源系统编码
        String dataSource = json.containsKey("dataSource") ? json.getString("dataSource") : "";
        String dataSourceCode = json.containsKey("dataSourceCode") ? json.getString("dataSourceCode") : "";

        if (StringUtils.isBlank(dataSource) || StringUtils.isBlank(dataSourceCode)) {
            return ApiJson.fail(ApiJson.KEY_CODE, 603).msg("来源系统编码必须有值！");
        }

        BmContract bmContract = getOne(new QueryWrapper<BmContract>().eq("contract_code_version", contractCode));
        if (bmContract == null) {
            return ApiJson.fail(ApiJson.KEY_CODE, 605).msg("合同不存在！");
        }

        if (performStateCode == 3 || performStateCode == 5) {
            bmContract.setPerformStateCode(performStateCode);
            bmContract.setPerformState(PerformStateEnum.match(performStateCode).getMessage());
            saveOrUpdate(bmContract);
        }
        if (performStateCode == 4) {
            String riskType = json.containsKey("riskType") ? json.getString("riskType") : "";
            String riskDescription = json.containsKey("riskDescription") ? json.getString("riskDescription") : "";
            Date riskOccurrenceTime = json.containsKey("riskOccurrenceTime") ? json.getDate("riskOccurrenceTime") : null;
            String respUnit = json.containsKey("respUnit") ? json.getString("respUnit") : "";
            String respUnitCode = json.containsKey("respUnitCode") ? json.getString("respUnitCode") : "";
            String respDept = json.containsKey("respDept") ? json.getString("respDept") : "";
            String respDeptCode = json.containsKey("respDeptCode") ? json.getString("respDeptCode") : "";
            String respPsn = json.containsKey("respPsn") ? json.getString("respPsn") : "";
            String respPsnCode = json.containsKey("respPsnCode") ? json.getString("respPsnCode") : "";
            String reportPsn = json.containsKey("reportPsn") ? json.getString("reportPsn") : "";
            String reportPsnCode = json.containsKey("reportPsnCode") ? json.getString("reportPsnCode") : "";
            String reportPsnTel = json.containsKey("reportPsnTel") ? json.getString("reportPsnTel") : "";

            if (StringUtils.isBlank(riskType) || StringUtils.isBlank(riskDescription) || StringUtils.isBlank(respUnit) || StringUtils.isBlank(respUnitCode) || StringUtils.isBlank(respDept) || StringUtils.isBlank(respDeptCode) || StringUtils.isBlank(respPsn) || StringUtils.isBlank(respPsnCode) || StringUtils.isBlank(reportPsn) || StringUtils.isBlank(reportPsnCode) || StringUtils.isBlank(reportPsnTel)) {
                return ApiJson.fail(ApiJson.KEY_CODE, 604).msg("履行状态为‘异常履行’时必传！");
            }

            if (riskOccurrenceTime == null) {
                return ApiJson.fail(ApiJson.KEY_CODE, 604).msg("履行状态为‘异常履行’时必传！");
            }
            bmContract.setPerformStateCode(performStateCode);
            bmContract.setPerformState(PerformStateEnum.match(performStateCode).getMessage());

            bmContract.setRiskType(riskType);
            bmContract.setRiskDescription(riskDescription);
            bmContract.setRespUnit(respUnit);
            bmContract.setRespUnitCode(respUnitCode);
            bmContract.setRespDept(respDept);
            bmContract.setRespDeptCode(respDeptCode);
            bmContract.setRespPsn(respPsn);
            bmContract.setRespPsnCode(respPsnCode);
            bmContract.setReportPsn(reportPsn);
            bmContract.setReportPsnCode(reportPsnCode);
            bmContract.setReportPsnTel(reportPsnTel);
            bmContract.setRiskOccurrenceTime(riskOccurrenceTime);
            saveOrUpdate(bmContract);
            //插入历史表
            bmContractRiskHisService.saveRiskHisData(bmContract);
        }
        return ApiJson.succ(ApiJson.KEY_CODE).msg("更改成功！！");
    }

    @Override
    public Json queryAssetList(JSONObject obj) {

        JSONObject selectData = obj.getJSONObject("selectData");
        String documentNum = selectData.getString("documentNum");
        String owner = selectData.getString("owner");
        String documentType = selectData.getString("documentType");

        //固定参数
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("BusinessSystem", "100005");
        jsonObject.put("psw", "11");
        jsonObject.put("uuid", Utils.createUUID());
        jsonObject.put("user", "11");
        jsonObject.put("mdmId", "1000000208");

        JSONArray field = new JSONArray();
        JSONObject fieldChild = new JSONObject();
        fieldChild.put("DOCUMENT_TYPE", "");//单据编号
        fieldChild.put("DOCUMENT_NUM", "");//业务类型
        fieldChild.put("OWNER", owner);//资产所属单位
        field.add(fieldChild);
        jsonObject.put("Field", field);

        String string = OkHttpUtils.builder().url(esbUrl + "/bgjt/eam/tz/getLawQuery").addHeader("Content-Type", "application/json; charset=utf-8").addHeader("OnlineToken", "rPlSk46zpCBaH5vPK68hq5xN0XBhVODnEJzMmPBR3Wa2VR2xL1kAOpP7bW5LRIfaYenoRte2ye4=")
                // 如果是true的话，会类似于postman中post提交方式的raw，用json的方式提交，不是表单
                // 如果是false的话传统的表单提交
                .post(true, jsonObject.toJSONString()).sync();
        Map<String, String> keyMap = new HashMap<String, String>();
        keyMap.put("documentNum", "assetDocNo");
        keyMap.put("documentType", "businessType");
        keyMap.put("documentTypeCode", "businessTypeCode");
        keyMap.put("matterDepict", "assetDescription");
        keyMap.put("owner", "assetOwner");
        keyMap.put("contractCode", "assetContractNumber");
        keyMap.put("remarks", "assetRemarks");
        keyMap.put("list", "assetList");

        JSONObject jsonObj = changeJsonObj(JSONObject.parseObject(string), keyMap);

        return Json.succ("queryAssetList").data("data", jsonObj.toJSONString());
    }

    @Override
    public Boolean updateNotAllById(BigDecimal val1, BigDecimal val2, BigDecimal val3, String val4, long val5, String val6, long val7, String id) {
        return bmContractMapper.updateNotAllById(val1, val2, val3, val4, val5, val6, val7, id);
    }

    @Override
    public Boolean updateComById(String val1, String val2, String val3, Date val4, String id) {
        return bmContractMapper.updateComById(val1, val2, val3, val4, id);
    }


    @Override
    public List<Map<String, Object>> checkSuppleDataList(List<Map<String, Object>> dataList) {
        List<Map<String, Object>> errorMapList = new ArrayList<>();
        final int[] index = {3};
        String regex = "[^a-zA-Z0-9,]+";
        String mathPattern = "^-?\\d+(\\.\\d+)?$";
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        //获取合同类型list
        Wrapper<SgContractType> wrapper = new QueryWrapper<SgContractType>().eq("is_leaf", "Y");
        List<SgContractType> contractTypeList = contractTypeService.list(wrapper);
        List<SysDict> currencyList = sysDictService.queryDicSort("adeafd93ca8a438eac70c0705fb286e1");
        Pattern pattern = Pattern.compile(regex);
        dataList.forEach(data -> {
            Map<String, Object> errorMap = new HashMap<>();
            StringBuilder errorText = new StringBuilder();
            String contractName = StringUtil.emptyStr(data.get("*合同名称"));
            if (contractName.isEmpty()) {
                errorText.append("A" + index[0] + ":合同名称不能为空,");
            }
            String contractType = StringUtil.emptyStr(data.get("*合同类型"));
            if (contractType.isEmpty()) {
                errorText.append("B" + index[0] + ":合同类型不能为空,");
            }
            Optional<SgContractType> typeOptional = contractTypeList.stream().filter(type -> type.getTypeName().equals(contractType)).findFirst();
            if (!typeOptional.isPresent()) {
                errorText.append("B" + index[0] + ":合同类型不符合要求,");
            }
            String projeectDecision = StringUtil.emptyStr(data.get("*立项决策"));
            if (projeectDecision.isEmpty()) {
                errorText.append("C" + index[0] + ":立项决策不能为空,");
            }
            String relativeMethod = StringUtil.emptyStr(data.get("*相对方确认方式"));
            if (relativeMethod.isEmpty()) {
                //errorMap.put("relativeMethod", "相对方确认方式不能为空");
                errorText.append("D" + index[0] + ":相对方确认方式不能为空,");
            }
            RelativeMethodEnum relativeMethodEnum = RelativeMethodEnum.catchMessage(relativeMethod);
            if (relativeMethodEnum == null) {
                errorText.append("D" + index[0] + ":相对方确认方式不符合要求");
            }
            String custom = StringUtil.emptyStr(data.get("*自定义编码"));
            if (custom.isEmpty()) {
//                errorMap.put("custom", "自定义编码不能为空");
                errorText.append("E" + index[0] + ":自定义编码不能为空,");
            }
            String customCode = StringUtil.emptyStr(data.get("自定义编码值")).trim();//需修改字段名
            if ("是".equals(custom) && customCode.isEmpty()) {
                errorText.append("F" + index[0] + ":自定义编码值未填写,");
            }
            String ourPartyList = StringUtil.emptyStr(data.get("*我方签约主体"));
            if (ourPartyList.isEmpty()) {
                //errorMap.put("ourPartyList", "我方签约主体不能为空");
                errorText.append("G" + index[0] + ":我方签约主体不能为空,");
            }
            Matcher matcher = pattern.matcher(ourPartyList);
            if (matcher.find() && !"".equals(matcher.group())) {
                errorText.append("G" + index[0] + ":我方签约主体需填写主体编码，不得填写名称,");
            }
            String ourPosition = StringUtil.emptyStr(data.get("我方地位"));
            String otherPartyList = StringUtil.emptyStr(data.get("*对方签约主体"));
            if ("".equals(otherPartyList)) {
                errorText.append("I" + index[0] + ":对方签约主体不能为空,");
            }
            matcher = pattern.matcher(otherPartyList);
            if (matcher.find() && !"".equals(matcher.group())) {
                errorText.append("I" + index[0] + ":对方签约主体需填写主体编码，不得填写名称,");
            }
            List<MainOrganization> organizations = mainOrganizationService.listByIds(Arrays.asList(otherPartyList.split(",")));
            List<SgBusinessMan> businessManList = sgBusinessManService.listByIds(Arrays.asList(otherPartyList.split(",")));
            if ((organizations.size() + businessManList.size()) != otherPartyList.split(",").length) {
                errorText.append("I" + index[0] + ":对方签约主体未找到，请确认,");
            }
            String revenueExpenditure = StringUtil.emptyStr(data.get("*收支方向"));
            if (revenueExpenditure.isEmpty()) {
                errorText.append("J" + index[0] + ":收支方向不能为空,");
            }
            RevenueExpenditureEnum revenueExpenditureEnum = RevenueExpenditureEnum.catchMessage(revenueExpenditure);
            if (revenueExpenditureEnum == null) {
                errorText.append("J" + index[0] + ":收支方向不符合字典项,");
            }
            String moneyType = StringUtil.emptyStr(data.get("*金额类型"));
            if (moneyType.isEmpty()) {
                errorText.append("K" + index[0] + ":金额类型不能为空,");
            }
            String settlementMethod = StringUtil.emptyStr(data.get("结算方式"));
            if (!"不涉及".equals(revenueExpenditure)) {
                if (settlementMethod.isEmpty()) {
                    errorText.append("L" + index[0] + ":结算方式不得为空,");
                } else {
                    boolean flag = false;
                    for (String s : settlementMethod.split(",")) {
                        SettlementMethodEnum settlementMethodEnum = SettlementMethodEnum.catchMessage(s);
                        if (settlementMethodEnum == null && !flag) {
                            errorText.append("L" + index[0] + ":结算方式参数不符,");
                            flag = true;
                        }
                    }
                }
            }
            String originalContractMoney = StringUtil.emptyStr(data.containsKey("*合同金额（元）") ? data.get("*合同金额（元）") : data.get("合同金额（元）"));
            if (originalContractMoney.isEmpty()) {
                errorText.append("M" + index[0] + ":合同金额不得为空,");
            } else {

                if (!originalContractMoney.matches(mathPattern)) {
                    errorText.append("M" + index[0] + ":合同金额不得填写非数字类型及公式,");
                }

            }
            String currency = StringUtil.emptyStr(data.get("*币种"));
            if (currency.isEmpty()) {
                errorText.append("N" + index[0] + ":币种不得为空,");
            } else {
                Optional<SysDict> currFrist = currencyList.stream().filter(curr -> curr.getDicName().equals(currency)).findFirst();
                if (!currFrist.isPresent()) {
                    errorText.append("N" + index[0] + ":币种类型不规范");
                }
            }
            String isTax = StringUtil.emptyStr(data.get("*是否含税"));
            if ("".equals(isTax) || isTax == null) {
                errorText.append("O" + index[0] + ":是否含税不得为空,");
            }
            String valueAddedTaxRate = StringUtil.emptyStr(data.get("*增值税率"));
            if (valueAddedTaxRate.isEmpty()) {
                errorText.append("P" + index[0] + ":增值税率不得为空,");
            }
            boolean taxRateFlag = false;
            for (String taxRate : valueAddedTaxRate.split(",")) {
                if (!taxRateMap.containsKey(taxRate) && !taxRateFlag) {
                    errorText.append("P" + index[0] + ":增值税率不存在,");
                    //改为true，只推送一次错误日志
                    taxRateFlag = true;
                }
            }

            String valueAddedTaxAmount = StringUtil.emptyStr(data.get("增值税额（元）"));
            if ("是".equals(isTax) && valueAddedTaxAmount.isEmpty()) {
                errorText.append("Q" + index[0] + ":增值税额不能为空,");
            } else {
                if (!valueAddedTaxAmount.matches(mathPattern) && !valueAddedTaxAmount.isEmpty()) {
                    errorText.append("Q" + index[0] + ":增值税额字段不得填写非数字类型及公式,");
                }
            }
            String exchangeRate = StringUtil.emptyStr(data.get("*汇率"));

            if (exchangeRate.isEmpty()) {
                errorText.append("R" + index[0] + ":汇率不能为空,");
            }
            String contractStartDate = StringUtil.emptyStr(data.get("*合同生效日期"));
            if (contractStartDate.isEmpty()) {
                errorText.append("T" + index[0] + ":合同生效日期不能为空,");
            }
            try {
                sdf.parse(contractStartDate);
            } catch (Exception e) {
                errorText.append("T" + index[0] + ":合同生效日期格式不正确：修改为yyyy-MM-dd格式,");
            }
            String exchangeRateMethod = StringUtil.emptyStr(data.get("汇率方式"));
            String contractExecutedMoney = StringUtil.emptyStr(data.get("*合同已履行金额（元）"));
            if (contractExecutedMoney.isEmpty()) {
                errorText.append("U" + index[0] + ":合同已履行金额不能为空,");
            } else {
                if (!contractExecutedMoney.matches(mathPattern)) {
                    errorText.append("U" + index[0] + ":合同已履行金额字段不得填写非数字类型及公式,");
                }
            }
            String createPsnId = StringUtil.emptyStr(data.get("*经办人"));
            if (createPsnId.isEmpty()) {
                errorText.append("W" + index[0] + ":经办人id不能为空,");
            } else {
                MainMidStaff user = mainMidStaffService.getUserByCode(createPsnId);
                if (user == null) {
                    errorText.append("W" + index[0] + ":经办人数据不存在,");
                }
            }

            String deptCode = StringUtil.emptyStr(data.get("*经办人任职编码"));
            if (deptCode.isEmpty()) {
                errorText.append("X" + index[0] + ":经办人任职编码不能为空,");
            } else {
                MainMidStaffUnitOrg userMainData = mainMidStaffUnitOrgService.getByUserCodeAndDeptCode(createPsnId, deptCode);
                if (userMainData == null) {
                    errorText.append("X" + index[0] + ":经办人任职编码不存在,");
                }
                List<SgHrOrgUnitB> unitBList = hrOrgUnitBService.list(new QueryWrapper<SgHrOrgUnitB>().eq("unit_code", deptCode));
                if (unitBList.isEmpty()) {
                    errorText.append("X" + index[0] + ":该任职编码未开通,");
                }

            }

            if (!"".contentEquals(errorText)) {
                errorMap.put("index", index[0]);
                errorMap.put("errorText", errorText.toString());
                errorMapList.add(errorMap);
            }
            index[0]++;


        });
        return errorMapList;
    }

    @Override
    @Transactional
    public void importSuppleDataList(List<Map<String, Object>> dataList, String orgId) throws ParseException {
        OrgContextVo orgContext = null;
        //获取合同类型list
        Wrapper<SgContractType> wrapper = new QueryWrapper<SgContractType>().eq("is_leaf", "Y");
        List<SgContractType> contractTypeList = contractTypeService.list(wrapper);
        List<BmContract> suppleAppDataList = new ArrayList<>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        BmContract bmContract = null;
        List<SysDict> currencyList = sysDictService.queryDicSort("adeafd93ca8a438eac70c0705fb286e1");
        for (Map<String, Object> dataMap : dataList) {
            bmContract = new BmContract();
            bmContract.setDataTypeCode(4);
            bmContract.setVersion("V00");
            bmContract.setDataTypeName("补录主合同");
            bmContract.setDataSourceCode("100008");
            bmContract.setDataSource("法务系统");
            bmContract.setContractName(StringUtil.emptyStr(dataMap.get("*合同名称")));
            bmContract.setContractType(StringUtil.emptyStr(dataMap.get("*合同类型")));
            bmContract.setProjectDecision(StringUtil.emptyStr(dataMap.get("*立项决策")));
            bmContract.setProjectDecisionCode(ProjectDecisionEnum.catchMessage(bmContract.getProjectDecision()).getCode());
//            bmContract.setProjectDecision(StringUtil.emptyStr(dataMap.get("立项决策")));
            bmContract.setRelativeMethod(StringUtil.emptyStr(dataMap.get("*相对方确认方式")));
            bmContract.setRelativeMethodCode(RelativeMethodEnum.catchMessage(bmContract.getRelativeMethod()).getCode());
            bmContract.setCustom("是".equals(StringUtil.emptyStr(dataMap.get("*自定义编码"))));
            bmContract.setCustomCode(StringUtil.emptyStr(dataMap.get("自定义编码值")));
            bmContract.setOurPartyList(StringUtil.emptyStr(dataMap.get("*我方签约主体")));
            List<MainOrganization> organizations = mainOrganizationService.listByIds(Arrays.asList(bmContract.getOurPartyList().split(",")));
            bmContract.setOurPartyName(organizations.stream().map(MainOrganization::getCodeName).collect(Collectors.joining(",")));
            bmContract.setOurPosition(StringUtil.emptyStr(dataMap.get("我方地位")));
            bmContract.setOtherPartyList(StringUtil.emptyStr(dataMap.get("*对方签约主体")));
            organizations = mainOrganizationService.listByIds(Arrays.asList(bmContract.getOtherPartyList().split(",")));
            String otherNames = organizations.stream().map(MainOrganization::getCodeName).collect(Collectors.joining(","));
            List<SgBusinessMan> businessManList = sgBusinessManService.listByIds(Arrays.asList(bmContract.getOtherPartyList().split(",")));
            String busNames = businessManList.stream().map(SgBusinessMan::getBusinessName).collect(Collectors.joining(","));
            String otherPartyName = otherNames + "," + busNames;
            //处理对方签约主体头包含,情况
            otherNames = otherPartyName.startsWith(",") ? otherPartyName.substring(1) : otherPartyName;
            bmContract.setOtherPartyName(otherNames.endsWith(",") ? otherNames.substring(0, otherNames.length() - 1) : otherNames);
            bmContract.setRevenueExpenditure(StringUtil.emptyStr(dataMap.get("*收支方向")));
            bmContract.setRevenueExpenditureCode(RevenueExpenditureEnum.catchMessage(bmContract.getRevenueExpenditure()).getCode());
            bmContract.setMoneyType("其他类型".equals(StringUtil.emptyStr(dataMap.get("*金额类型"))) ? "其他" : StringUtil.emptyStr(dataMap.get("*金额类型")));
            bmContract.setMoneyTypeCode(MoneyTypeEnum.catchMessage(bmContract.getMoneyType()).getCode());
            bmContract.setSettlementMethod(StringUtil.emptyStr(dataMap.get("结算方式")));
            if (!"".equals(bmContract.getSettlementMethod())) {
                StringBuilder settlementCode = new StringBuilder();
                for (String settlementMethod : bmContract.getSettlementMethod().split(",")) {
                    System.out.println(bmContract.getSettlementMethod());
                    settlementCode.append(SettlementMethodEnum.catchMessage(settlementMethod).getCode()).append(",");
                }
                bmContract.setSettlementMethodCode(settlementCode.substring(0, settlementCode.length() - 1));
            }
            bmContract.setContractMoney(new BigDecimal(dataMap.get("*合同金额（元）").toString()));
            bmContract.setCurrency(StringUtil.emptyStr(dataMap.get("*币种")).trim());
            BmContract finalBmContract1 = bmContract;
            Optional<SysDict> currFrist = currencyList.stream().filter(curr -> curr.getDicName().equals(finalBmContract1.getCurrency())).findFirst();
            bmContract.setCurrencyCode(currFrist.get().getDicCode());
            bmContract.setIsTax("是".equals(StringUtil.emptyStr(dataMap.get("*是否含税"))));
            bmContract.setValueAddedTaxRate(StringUtil.emptyStr(dataMap.get("*增值税率")));
            StringBuilder taxRateStr = new StringBuilder();
            for (String taxRate : bmContract.getValueAddedTaxRate().split(",")) {
                taxRateStr.append(taxRateMap.get(taxRate)).append(",");
            }
            bmContract.setValueAddedTaxRateCode(taxRateStr.substring(0, taxRateStr.length() - 1));
            String o = StringUtil.emptyStr(dataMap.get("增值税额（元）"));
            if (!o.isEmpty()) {
                bmContract.setValueAddedTaxAmount(new BigDecimal(o));
            }
            bmContract.setExchangeRate(new BigDecimal(StringUtil.emptyStr(dataMap.get("*汇率"))));
            bmContract.setExchangeRateMethod(StringUtil.emptyStr(dataMap.get("汇率方式")));
            bmContract.setContractExecutedMoney(new BigDecimal(StringUtil.emptyStr(dataMap.get("*合同已履行金额（元）"))));
            bmContract.setId(YitIdHelper.nextId() + "");
            BmContract finalBmContract = bmContract;
            bmContract.setContractTypeCode(contractTypeList.stream().filter(type -> type.getTypeName().equals(finalBmContract.getContractType())).findFirst().get().getTypeCode());
            bmContract.setContractTypeLevel(contractTypeList.stream().filter(type -> type.getTypeName().equals(finalBmContract.getContractType())).findFirst().get().getNodeLevel());
            bmContract.setContractTakeEffectDate(sdf.parse(StringUtil.emptyStr(dataMap.get("*合同生效日期"))));
            //经办人工号/id
            String createPsnId = StringUtil.emptyStr(dataMap.get("*经办人"));
            //经办人任职编码
            String jobCode = StringUtil.emptyStr(dataMap.get("*经办人任职编码"));
            //依照excel中的经办人数据查询对应人的信息，将该经办人的数据动态设置到补录数据中
            //String createPsnOrgId = checkAndCreateSysData(createPsnId, jobCode);
            orgContext = OrgUtils.getOrgContextByCode(jobCode);
            bmContract.setCreateGroupId(orgContext.getCurrentGroupId());
            bmContract.setCreateGroupName(orgContext.getCurrentGroupName());
            bmContract.setCreatePsnId(orgContext.getCurrentPsnId());
            bmContract.setCreatePsnName(orgContext.getCurrentPsnName());
            bmContract.setCreatePsnFullId(orgContext.getCurrentPsnFullId());
            bmContract.setCreatePsnPhone(orgContext.getCurrentPsnPhone());
            bmContract.setCreateDeptId(orgContext.getCurrentDeptId());
            bmContract.setCreateDeptName(orgContext.getCurrentDeptName());
            bmContract.setCreateOgnId(orgContext.getCurrentOgnId());
            bmContract.setCreateOgnName(orgContext.getCurrentOgnName());
            bmContract.setCreatePsnFullName(orgContext.getCurrentPsnFullName());
            bmContract.setCreateOrgId(orgContext.getCurrentOrgId());
            bmContract.setCreateOrgName(orgContext.getCurrentOrgName());
            bmContract.setCreateLegalUnitId(orgContext.getCurrentLegalUnitId());
            bmContract.setCreateLegalUnitName(orgContext.getCurrentLegalUnitName());
            bmContract.setCreateTime(new Date());
            bmContract.setUpdateTime(new Date());
            //数据状态
            bmContract.setDataState("已保存");
            bmContract.setDataStateCode(1);
            //生效状态
            bmContract.setTakeEffectName("待生效");
            bmContract.setTakeEffectCode(2);
            //履行状态
            bmContract.setPerformState("未履行");
            bmContract.setPerformStateCode(1);
            //履行计划状态
            bmContract.setPerformanceState("未计划");
            bmContract.setPerformanceStateCode(1);
            //变更状态
            bmContract.setChangeState("未变更");
            bmContract.setChangeStateCode(1);
            //关闭状态
            bmContract.setCloseState("未关闭");
            bmContract.setCloseStateCode(1);
            //评价状态
            bmContract.setEvaluatedState("未评价");
            bmContract.setEvaluatedStateCode(1);
            //变更状态
            bmContract.setChangeState("未变更");
            bmContract.setChangeStateCode(1);
            //归档状态
            bmContract.setArchivedState("未归档");
            bmContract.setArchivedStateCode(1);
            //审批单号
            bmContract.setApprovalCode(kvsequenceService.createNextSequenceString("HTSP2024", 6));
            suppleAppDataList.add(bmContract);
        }
        this.saveBatch(suppleAppDataList);
    }

    private String checkAndCreateSysData(String createPsnId, String jobCode) {
        MainMidStaff mainMidStaff = mainMidStaffService.getUserByCode(createPsnId);
        MainMidStaffUnitOrg midStaffUnitOrg = mainMidStaffUnitOrgService.getByUserCodeAndDeptCode(createPsnId, jobCode);
        if (midStaffUnitOrg == null) {
            throw new IllegalArgumentException("此人员不存在并且无法开通!");
        }
        JSONObject jsonObject = new JSONObject();
        String[] codes = new String[]{createPsnId};
        jsonObject.put("codes", codes);
        JSONArray jsonArray = new JSONArray();
        JSONObject orgJson = new JSONObject();
        orgJson.put("account", createPsnId);
        orgJson.put("codeType", midStaffUnitOrg.getCodeType());
        orgJson.put("jobCode", jobCode);
        orgJson.put("name", mainMidStaff.getName());
        orgJson.put("orgCode", midStaffUnitOrg.getOrgCode());
        orgJson.put("organization", midStaffUnitOrg.getDeptName());
        orgJson.put("state", midStaffUnitOrg.getState());
        orgJson.put("code", createPsnId);
        jsonArray.add(orgJson);
        jsonObject.put("selectData", jsonArray);
        mainMidStaffService.addUser(jsonObject);
        QueryWrapper<SgHrOrgUnitB> queryWrapper = new QueryWrapper<SgHrOrgUnitB>().eq("unit_code", jobCode);
        SgHrOrgUnitB sgHrOrgUnitB = sgHrOrgUnitBService.getBaseMapper().selectOne(queryWrapper);
        return sgHrOrgUnitB.getUnitId() + "";
    }

    @Override
    public ApiJson addContractAsyn(String mainData) {
        log.info("合同接口接收请求值开始：");
        log.info("{}, mainData: {}", "mainData", mainData);
        StopWatch sw = new StopWatch();
        sw.start();
        if (StringUtils.isBlank(mainData)) {
            return ApiJson.fail(ApiJson.KEY_CODE, 500).msg("合同数据不能为空！");
        }
        //声明一些对象
        JSONObject json = JSONObject.parseObject(mainData);
        BmContractInfo bmContractInfo = new BmContractInfo();
        SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        bmContractInfo.setCreateTime(new Date());
        try {
            //主键
//        String replace = UUID.randomUUID().toString().replace("-", "");
            bmContractInfo.setId(String.valueOf(YitIdHelper.nextId()));
            //合同名称
            String contractName = json.containsKey("contractName") ? json.getString("contractName") : "";
            if (StringUtils.isBlank(contractName)) {
                return ApiJson.fail(ApiJson.KEY_CODE, 501).msg("合同名称必须有值！");
            }
            bmContractInfo.setContractName(contractName);
            //业务系统合同编码
            String thirdContractNo = json.containsKey("thirdContractNo") ? json.getString("thirdContractNo") : "";
            if (StringUtils.isBlank(thirdContractNo)) {
                return ApiJson.fail(ApiJson.KEY_CODE, 502).msg("业务系统合同编码必须有值！");
            }
            bmContractInfo.setThirdContractNo(thirdContractNo);
            //版本号
            String thirdVersion = json.containsKey("thirdVersion") ? json.getString("thirdVersion") : "";
            bmContractInfo.setThirdVersion(thirdVersion);

            // 系统来源
            String dataSource = json.containsKey("dataSource") ? json.getString("dataSource") : "";
            String dataSourceCode = json.containsKey("dataSourceCode") ? json.getString("dataSourceCode") : "";
            if (StringUtils.isBlank(dataSource) || StringUtils.isBlank(dataSourceCode)) {
                return ApiJson.fail(ApiJson.KEY_CODE, 503).msg("合同的系统来源必须有值！");
            }
            bmContractInfo.setDataSource(dataSource);
            bmContractInfo.setDataSourceCode(dataSourceCode);

            String extended_filed1 = json.containsKey("extended_filed1") ? json.getString("extended_filed1") : "";
            bmContractInfo.setExtendedFiled1(extended_filed1);

            //合同主键
            String contractId = json.containsKey("contractId") ? json.getString("contractId") : "";
            if (StringUtils.isNotBlank(contractId)) {
                bmContractInfo.setContractId(contractId);
            }

            // 数据类型（主合同-1，合同变更-2，合同终止-3）
            Integer dataTypeCode = json.containsKey("dataTypeCode") ? json.getInteger("dataTypeCode") : null;
            if (dataTypeCode == null) {
                return ApiJson.fail(ApiJson.KEY_CODE, 504).msg("合同的数据类型必须有值！");
            }
            if (dataTypeCode != 1 && dataTypeCode != 2 && dataTypeCode != 3) {
                return ApiJson.fail(ApiJson.KEY_CODE, 504).msg("合同的数据类型值不正确！");
            }
            bmContractInfo.setDataTypeCode(dataTypeCode);

            // 合同分类
            String contractTypeCode = json.containsKey("contractTypeCode") ? json.getString("contractTypeCode") : "";
            if (StringUtils.isBlank(contractTypeCode)) {
                return ApiJson.fail(ApiJson.KEY_CODE, 505).msg("合同类型必须有值！");
            }

            SgContractType typeCode = sgContractTypeService.getOne(new QueryWrapper<SgContractType>().eq("type_code", contractTypeCode));
            if (typeCode == null) {
                return ApiJson.fail(ApiJson.KEY_CODE, 506).msg("法务系统中没有此合同类型数据！");
            }
            //推送的一级合同直接打回
            if (typeCode.getNodeLevel() == 1) {
                return ApiJson.fail(ApiJson.KEY_CODE, 507).msg("合同分类不允许为一级！");
            }
            bmContractInfo.setContractTypeCode(typeCode.getTypeCode());
            bmContractInfo.setContractTypeName(typeCode.getTypeName());
            bmContractInfo.setNodeLevel(typeCode.getNodeLevel());

            String orgId = json.containsKey("orgId") ? json.getString("orgId") : "";
            if (StringUtils.isBlank(orgId)) {
                return ApiJson.fail(ApiJson.KEY_CODE, 508).msg("主数据员工任职代码必须有值！");
            }

            // 查询任职代码，如果没有查到 再查员工代码（取主任职代码）todo wch staff_code、state、code_type 加上索引
            if (sgHrOrgUnitBService.count(new QueryWrapper<SgHrOrgUnitB>().eq("unit_code", orgId)) < 1) {
                QueryWrapper<MainMidStaffUnitOrg> queryWrapper = new QueryWrapper<MainMidStaffUnitOrg>();
                queryWrapper.eq("staff_code", orgId);
                queryWrapper.eq("state", "1");
                queryWrapper.eq("code_type", "1");

                MainMidStaffUnitOrg serviceOne = midStaffUnitOrgService.getOne(queryWrapper);
                if (serviceOne != null) {
                    bmContractInfo.setOrgId(serviceOne.getCode());
                } else {
                    return ApiJson.fail(ApiJson.KEY_CODE, 508).msg("主数据员工任职代码必须正确！");
                }
            } else {
                bmContractInfo.setOrgId(orgId);
            }


            // 立项决策
            Integer projectDecisionId = json.containsKey("projectDecisionId") ? json.getInteger("projectDecisionId") : null;
            if (projectDecisionId == null) {
                return ApiJson.fail(ApiJson.KEY_CODE, 509).msg("立项决策编码必须有值！");
            }
            if (projectDecisionId != 0 && projectDecisionId != 1 && projectDecisionId != 2 && projectDecisionId != 3 && projectDecisionId != 4 && projectDecisionId != 5 && projectDecisionId != 6) {
                return ApiJson.fail(ApiJson.KEY_CODE, 509).msg("合同的立项决策编码值不正确！");
            }
            bmContractInfo.setProjectDecisionId(projectDecisionId);


            // 相对方确定方式
            Integer relativeMethodCode = json.containsKey("relativeMethodCode") ? json.getInteger("relativeMethodCode") : null;
            if (relativeMethodCode == null) {
                return ApiJson.fail(ApiJson.KEY_CODE, 510).msg("相对方确定方式编码必须有值！");
            }
            bmContractInfo.setRelativeMethodCode(relativeMethodCode);

            // 授权信息
            Integer authorizedSource = json.containsKey("authorizedSource") ? json.getInteger("authorizedSource") : null;
            if (authorizedSource == null) {
                return ApiJson.fail(ApiJson.KEY_CODE, 511).msg("授权类型编码必须有值！");
            } else {
                String authorizedName = json.containsKey("authorizedName") ? json.getString("authorizedName") : "";
                String authorizedId = json.containsKey("authorizedId") ? json.getString("authorizedId") : "";

                bmContractInfo.setAuthorizedSource(authorizedSource);
                if (authorizedSource == 1) {
                    if (StringUtils.isNotBlank(authorizedName) && StringUtils.isNotBlank(authorizedId)) {
                        bmContractInfo.setAuthorizedId(authorizedId);
                        bmContractInfo.setAuthorizedName(authorizedName);
                    }
                }
            }

            // 是否集团重大
            Integer whetherGroupMajor = json.containsKey("whetherGroupMajor") ? json.getInteger("whetherGroupMajor") : null;
            if (whetherGroupMajor == null) {
                return ApiJson.fail(ApiJson.KEY_CODE, 513).msg("是否集团重大合同编码必须有值！");
            }
            bmContractInfo.setWhetherGroupMajor(whetherGroupMajor);

            // 是否本单位重大合同
            Integer whetherUnitMajor = json.containsKey("whetherUnitMajor") ? json.getInteger("whetherUnitMajor") : null;
            if (whetherUnitMajor == null) {
                return ApiJson.fail(ApiJson.KEY_CODE, 515).msg("是否本单位重大合同编码必须有值！");
            }
            bmContractInfo.setWhetherUnitMajor(whetherUnitMajor);

            // 金额类型
            Integer moneyTypeCode = json.containsKey("moneyTypeCode") ? json.getInteger("moneyTypeCode") : null;
            if (moneyTypeCode == null) {
                return ApiJson.fail(ApiJson.KEY_CODE, 516).msg("金额类型编码必须有值！");
            }
            bmContractInfo.setMoneyTypeCode(moneyTypeCode);

            //是否含税
            Integer isTax = json.containsKey("isTax") ? json.getInteger("isTax") : null;
            if (isTax == null) {
                return ApiJson.fail(ApiJson.KEY_CODE, 517).msg("是否含税必须有值！");
            }
            bmContractInfo.setIsTax(isTax);

            // 结算方式
            String settlementMethodCode = json.containsKey("settlementMethodCode") ? json.getString("settlementMethodCode") : "";
            if (StringUtils.isBlank(settlementMethodCode)) {
                return ApiJson.fail(ApiJson.KEY_CODE, 518).msg("结算方式编码必须有值！");
            }
            bmContractInfo.setSettlementMethodCode(settlementMethodCode);

            // 收支方向编码
            Integer revenueExpenditureCode = json.containsKey("revenueExpenditureCode") ? json.getInteger("revenueExpenditureCode") : null;
            if (revenueExpenditureCode == null) {
                return ApiJson.fail(ApiJson.KEY_CODE, 519).msg("收支方向编码必须有值！");
            }
            bmContractInfo.setRevenueExpenditureCode(revenueExpenditureCode);

            try {
                //合同约定开始日期
                String agreedStartTime = json.containsKey("agreedStartTime") ? json.getString("agreedStartTime") : "";
                if (StringUtils.isNotBlank(agreedStartTime)) {
                    bmContractInfo.setAgreedStartTime(sf.parse(agreedStartTime));
                }
                //合同约定结束日期
                String agreedEndTime = json.containsKey("agreedEndTime") ? json.getString("agreedEndTime") : "";
                if (StringUtils.isNotBlank(agreedEndTime)) {
                    bmContractInfo.setAgreedEndTime(sf.parse(agreedEndTime));
                }
            } catch (ParseException e) {
                log.error("ParseException", e);
            }

            //我方地位
            String ourPosition = json.containsKey("ourPosition") ? json.getString("ourPosition") : "";
            if (StringUtils.isBlank(ourPosition)) {
                return ApiJson.fail(ApiJson.KEY_CODE, 520).msg("我方地位必须有值！");
            }
            bmContractInfo.setOurPosition(ourPosition);

            //我方签约主体
            String ourPartyName = json.containsKey("ourPartyName") ? json.getString("ourPartyName") : "";
            //我方签约主体编码
            String ourPartyList = json.containsKey("ourPartyList") ? json.getString("ourPartyList") : "";
            if (StringUtils.isBlank(ourPartyName) || StringUtils.isBlank(ourPartyList)) {
                return ApiJson.fail(ApiJson.KEY_CODE, 521).msg("我方签约主体必须有值！");
            }
            bmContractInfo.setOurPartyName(ourPartyName);
            bmContractInfo.setOurPartyList(ourPartyList);

            //对方签约主体
            String otherPartyName = json.containsKey("otherPartyName") ? json.getString("otherPartyName") : "";
            //对方签约主体编码
            String otherPartyList = json.containsKey("otherPartyList") ? json.getString("otherPartyList") : "";
            if (StringUtils.isBlank(otherPartyName) || StringUtils.isBlank(otherPartyList)) {
                return ApiJson.fail(ApiJson.KEY_CODE, 522).msg("对方签约主体必须有值！");
            }
            bmContractInfo.setOtherPartyName(otherPartyName);
            bmContractInfo.setOtherPartyList(otherPartyList);

            // 是否范本（0-否，1-是）
            Integer isItAtemplate = json.containsKey("isItAtemplate") ? json.getInteger("isItAtemplate") : null;
            if (isItAtemplate == null) {
                return ApiJson.fail(ApiJson.KEY_CODE, 523).msg("是否范本必须有值！");
            }
            bmContractInfo.setIsItAtemplate(isItAtemplate);

            //新增合同
            if (dataTypeCode == 1) {
                // 合同金额
                BigDecimal contractMoney = json.containsKey("contractMoney") ? json.getBigDecimal("contractMoney") : null;
                if (contractMoney == null) {
                    return ApiJson.fail(ApiJson.KEY_CODE, 524).msg("合同金额必须有值！");
                }
                bmContractInfo.setContractMoney(contractMoney);

                // 增值税率编码
                String valueAddedTaxRateCode = json.containsKey("valueAddedTaxRateCode") ? json.getString("valueAddedTaxRateCode") : "";
                String valueAddedTaxRate = json.containsKey("valueAddedTaxRate") ? json.getString("valueAddedTaxRate") : "";

                if (StringUtils.isBlank(valueAddedTaxRateCode) || StringUtils.isBlank(valueAddedTaxRate)) {
                    return ApiJson.fail(ApiJson.KEY_CODE, 525).msg("增值税率编码必须有值！");
                }
                bmContractInfo.setValueAddedTaxRate(valueAddedTaxRate);
                bmContractInfo.setValueAddedTaxRateCode(valueAddedTaxRateCode);

                // 增值税额（元）
                BigDecimal valueAddedTaxAmount = json.containsKey("valueAddedTaxAmount") ? json.getBigDecimal("valueAddedTaxAmount") : null;
                if (valueAddedTaxAmount != null) {
                    bmContractInfo.setValueAddedTaxAmount(valueAddedTaxAmount);
                }

                //币种名称
                String currency = json.containsKey("currency") ? json.getString("currency") : "";
                //币种代码
                String currencyCode = json.containsKey("currencyCode") ? json.getString("currencyCode") : "";
                if (StringUtils.isBlank(currency) || StringUtils.isBlank(currencyCode)) {
                    return ApiJson.fail(ApiJson.KEY_CODE, 526).msg("币种代码必须有值！");
                }
                bmContractInfo.setCurrency(currency);
                bmContractInfo.setCurrencyCode(currencyCode);

                // 汇率方式编码
                Integer exchangeRateMethodCode = json.containsKey("exchangeRateMethodCode") ? json.getInteger("exchangeRateMethodCode") : null;
                if (exchangeRateMethodCode == null) {
                    return ApiJson.fail(ApiJson.KEY_CODE, 526).msg("汇率方式编码必须有值！");
                }
                bmContractInfo.setExchangeRateMethodCode(exchangeRateMethodCode);

                // 汇率
                BigDecimal exchangeRate = json.containsKey("exchangeRate") ? json.getBigDecimal("exchangeRate") : null;
                if (exchangeRate == null) {
                    return ApiJson.fail(ApiJson.KEY_CODE, 527).msg("汇率必须有值！");
                }
                bmContractInfo.setExchangeRate(exchangeRate);

            }
            //变更合同
            if (dataTypeCode == 2) {

                //原合同编号
                String originalContractCode = json.containsKey("originalContractCode") ? json.getString("originalContractCode") : "";
                if (StringUtils.isBlank(originalContractCode)) {
                    return ApiJson.fail(ApiJson.KEY_CODE, 528).msg("原合同编号必须有值！");
                }
                bmContractInfo.setOriginalContractCode(originalContractCode);

                // 金额变更类型编码
                Integer changeMoneyTypeCode = json.containsKey("changeMoneyTypeCode") ? json.getInteger("changeMoneyTypeCode") : null;
                if (changeMoneyTypeCode == null) {
                    return ApiJson.fail(ApiJson.KEY_CODE, 529).msg("金额变更类型编码必须有值！");
                }
                bmContractInfo.setChangeMoneyTypeCode(changeMoneyTypeCode);

                if (changeMoneyTypeCode == 0 || changeMoneyTypeCode == 1) {
                    //本次变更合同金额
                    BigDecimal thisChangeMoney = json.containsKey("thisChangeMoney") ? json.getBigDecimal("thisChangeMoney") : null;
                    if (thisChangeMoney == null) {
                        return ApiJson.fail(ApiJson.KEY_CODE, 530).msg("本次变更合同金额必须有值！");
                    }
                    if (changeMoneyTypeCode == 1 && thisChangeMoney.signum() == 1) {
                        thisChangeMoney = thisChangeMoney.negate();
                    }
                    bmContractInfo.setThisChangeMoney(thisChangeMoney);

                    //本次变更增值税金额
                    BigDecimal thisChangeValueAddedTaxAmount = json.containsKey("thisChangeValueAddedTaxAmount") ? json.getBigDecimal("thisChangeValueAddedTaxAmount") : null;
                    if (thisChangeValueAddedTaxAmount == null) {
                        return ApiJson.fail(ApiJson.KEY_CODE, 531).msg("本次变更增值税金额必须有值！");
                    }
                    if (changeMoneyTypeCode == 1 && thisChangeValueAddedTaxAmount.signum() == 1) {
                        thisChangeValueAddedTaxAmount = thisChangeValueAddedTaxAmount.negate();
                    }
                    bmContractInfo.setThisChangeValueAddedTaxAmount(thisChangeValueAddedTaxAmount);
                }

            }
            //终止合同
            if (dataTypeCode == 3) {
                //原合同编号
                String originalContractCode = json.containsKey("originalContractCode") ? json.getString("originalContractCode") : "";
                if (StringUtils.isBlank(originalContractCode)) {
                    return ApiJson.fail(ApiJson.KEY_CODE, 528).msg("原合同编号必须有值！");
                }
                bmContractInfo.setOriginalContractCode(originalContractCode);

                //待履行金额
                BigDecimal contractExecutoryMoney = json.containsKey("contractExecutoryMoney") ? json.getBigDecimal("contractExecutoryMoney") : null;
                if (contractExecutoryMoney == null) {
                    return ApiJson.fail(ApiJson.KEY_CODE, 532).msg("待履行金额必须有值！");
                }
                bmContractInfo.setContractExecutoryMoney(contractExecutoryMoney);

                //合同终止说明
                String explain = json.containsKey("explain") ? json.getString("explain") : "";
                if (StringUtils.isBlank(explain)) {
                    return ApiJson.fail(ApiJson.KEY_CODE, 533).msg("合同终止说明必须有值！");
                }
                bmContractInfo.setExplain(explain);
            }

            // 生效状态编码
            Integer takeEffectCode = json.containsKey("takeEffectCode") ? json.getInteger("takeEffectCode") : null;
            if (takeEffectCode == null) {
                return ApiJson.fail(ApiJson.KEY_CODE, 534).msg("生效状态编码必须有值！");
            }
            bmContractInfo.setTakeEffectCode(takeEffectCode);

            //已生效
            if (takeEffectCode == 3) {
                //生效附件名称编码
                String alreadySealFile = json.containsKey("alreadySealFile") ? json.getString("alreadySealFile") : "";
                String alreadySealFileId = json.containsKey("alreadySealFileId") ? json.getString("alreadySealFileId") : "";
                if (StringUtils.isBlank(alreadySealFile) || StringUtils.isBlank(alreadySealFileId)) {
                    return ApiJson.fail(ApiJson.KEY_CODE, 535).msg("生效附件名称编码必须有值！");
                }
                String[] idsArray = alreadySealFileId.split(",");
                for (String string : idsArray) {
                    if (!hweiYunOBSService.doesObjectExist(string)) {
                        return ApiJson.fail(ApiJson.KEY_CODE, 535).msg("生效附件名称编码必须正确！");
                    }
                }
                bmContractInfo.setAlreadySealFile(alreadySealFile);
                bmContractInfo.setAlreadySealFileId(alreadySealFileId);
                //生效时间
                String contractTakeEffectDate = json.containsKey("contractTakeEffectDate") ? json.getString("contractTakeEffectDate") : "";
                //对方盖章时间
                String otherSealDate = json.containsKey("otherSealDate") ? json.getString("otherSealDate") : "";

                try {
                    if (StringUtils.isBlank(contractTakeEffectDate) || StringUtils.isBlank(otherSealDate)) {
                        return ApiJson.fail(ApiJson.KEY_CODE, 536).msg("合同生效时间必须有值！");
                    }
                    bmContractInfo.setOtherSealDate(sf.parse(otherSealDate));
                    bmContractInfo.setContractTakeEffectDate(sf.parse(contractTakeEffectDate));
                } catch (ParseException e) {
                    log.error("失败", e);
                    return ApiJson.fail(ApiJson.KEY_CODE, 536).msg("合同生效时间必须有值！");
                }

            }
            //待用印
            if (takeEffectCode == 1) {
                //待用印附件名称编码
                String staySealFile = json.containsKey("staySealFile") ? json.getString("staySealFile") : "";
                String staySealFileId = json.containsKey("staySealFileId") ? json.getString("staySealFileId") : "";
                if (StringUtils.isBlank(staySealFile) || StringUtils.isBlank(staySealFileId)) {
                    return ApiJson.fail(ApiJson.KEY_CODE, 537).msg("待用印附件名称编码必填！");
                }
                String[] idsArray = staySealFileId.split(",");
                for (String string : idsArray) {
                    if (!hweiYunOBSService.doesObjectExist(string)) {
                        return ApiJson.fail(ApiJson.KEY_CODE, 537).msg("待用印附件名称编码必须正确！");
                    }
                }
                bmContractInfo.setStaySealFile(staySealFile);
                bmContractInfo.setStaySealFileId(staySealFileId);
            }

//        // 是否有用印信息 todo wch 1.7删除
//        Integer whetherSeal = json.containsKey("whetherSeal") ? json.getInteger("whetherSeal") : null;
//        if (whetherSeal == null) {
//            return ApiJson.fail(ApiJson.KEY_CODE, 538).msg("是否有用印信息必须有值！");
//        }
//        bmContractInfo.setWhetherSeal(whetherSeal);

            // 合同履行状态编码
            Integer performStateCode = json.containsKey("performStateCode") ? json.getInteger("performStateCode") : null;
            if (performStateCode == null) {
                return ApiJson.fail(ApiJson.KEY_CODE, 539).msg("合同履行状态编码必须有值！");
            }
            bmContractInfo.setPerformStateCode(performStateCode);

            //用印子表
            String sealList = json.containsKey("sealList") ? json.getString("sealList") : "";
            if (takeEffectCode == 3) {
                if (StringUtils.isBlank(sealList)) {
                    return ApiJson.fail(ApiJson.KEY_CODE, 540).msg("已生效时，合同的用印信息必须有值！");
                }
                JSONArray jsonArray = JSONArray.parseArray(sealList);
                for (Object o : jsonArray) {
                    JSONObject jsonObject = JSONObject.parseObject(o.toString());
                    if (StringUtils.isBlank(jsonObject.getString("sealCode")) || StringUtils.isBlank(jsonObject.getString("sealNumber")) || StringUtils.isBlank(jsonObject.getString("printsNumber")) || StringUtils.isBlank(jsonObject.getString("sealNumberOld")) || StringUtils.isBlank(jsonObject.getString("printsNumberOld")) || StringUtils.isBlank(jsonObject.getString("ourSealDate"))) {
                        return ApiJson.fail(ApiJson.KEY_CODE, 541).msg("用印列表每个字段必须有值！");
                    }
                }
                bmContractInfo.setSealList(sealList);
            }
            if (takeEffectCode == 1) {
                if (StringUtils.isNotBlank(sealList)) {
                    bmContractInfo.setSealList(sealList);
                }
            }
            //项目列表
            String projectList = json.containsKey("projectList") ? json.getString("projectList") : "";
            if (projectDecisionId == 1 || projectDecisionId == 2) {
                if (StringUtils.isBlank(projectList)) {
                    return ApiJson.fail(ApiJson.KEY_CODE, 542).msg("立项决策为投资、科研项目必须有值！");
                }
                bmContractInfo.setProjectList(projectList);
            }
        } catch (NumberFormatException exception) {
            log.error("参数格式错误：{}", exception.getMessage());
            return ApiJson.fail(ApiJson.KEY_CODE, -1).msg("参数格式错误：" + exception);
        }
        //设置通过校验的数据
        bmContractInfo.setHandle(0);
        bmContractInfo.setStatusCode(0);//未成功
        boolean save = bmContractInfoService.save(bmContractInfo);
        sw.stop();
        System.out.println("合同接收接口耗时：");
        System.out.println(sw.getTotalTimeMillis());
        if (save) {
            log.info("推送成功！");
            return ApiJson.succ(ApiJson.KEY_CODE).msg("推送成功！");
        } else {
            log.info("推送失败！");
            return ApiJson.fail(ApiJson.KEY_CODE, -1).msg("推送失败！");
        }
    }


    //    @Scheduled(cron = "0 */30 * * * ?") //每30分钟执行一次
//    @Scheduled(cron = "0 */5 * * * ?") //每5分钟执行一次
//    @Scheduled(cron = "0 */2 * * * ?")
    public void syncScheduled() {
        RLock lock = redisson.getLock("syncScheduled");
        boolean flag = false;
        try {
            flag = lock.tryLock();
            if (flag) {
                log.info("加锁成功，开始执行业务");
                try {
                    log.info("处理合同同步业务逻辑");
                    StopWatch sw = new StopWatch();
                    sw.start();
                    synchronousContract();
                    sw.stop();
                    System.out.println("定时开始创建合同：");
                    System.out.println(sw.getTotalTimeMillis());
                } catch (Exception e) {
                    log.error("同步合同失败！", e);
                }
            } else {
                log.info("加锁失败，没有获取到锁");
            }
        } catch (Exception ex) {
            log.error("同步失败", ex);
        } finally {
            if (!flag) {
                return;
            }
            lock.unlock();
            log.info("Redisson分布式锁释放锁");
        }
    }

    @Transactional(propagation = Propagation.NESTED)
    public void synchronousContract() {
        log.info("开通同步合同：");
        SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd");

        //查出当天收到的业务数据通过非空校验的数据
        QueryWrapper<BmContractInfo> wrapper = new QueryWrapper<>();
        wrapper.eq("handle", 0);
        wrapper.eq("status_code", 0);
        wrapper.like("create_time", sf.format(new Date()));
        List<BmContractInfo> lists = bmContractInfoService.list(wrapper);
        for (BmContractInfo item : lists) {
            try {
                log.info("开通同步合同：");
                //创建合同
                BmContract bmContract = new BmContract();
                String mainId = "";
                String contractId = item.getContractId();
                //判断contractId是否为空，并且判断业务系统合同编码 + 系统来源 如果为空  表示新增
                if (StringUtils.isBlank(contractId)) {
                    if (StringUtils.isNotBlank(item.getThirdVersion())) {
                        QueryWrapper<BmContract> wrapper1 = new QueryWrapper<BmContract>();
                        wrapper1.eq("custom_code", item.getThirdContractNo());
                        wrapper1.eq("third_version", item.getThirdVersion());
                        wrapper1.eq("data_source_code", item.getDataSourceCode());
                        BmContract oldContract = getOne(wrapper1);
                        if (oldContract != null) {
                            if (!item.getDataTypeCode().equals(oldContract.getDataTypeCode())) {
                                sendFailData(item, "数据类型不能更新!");
                                continue;
                            }
                            mainId = oldContract.getId();
                            bmContract.setContractCode(oldContract.getContractCode());
                            bmContract.setVersion(oldContract.getVersion());
                            bmContract.setContractCodeVersion(oldContract.getContractCodeVersion());

                        } else {
                            mainId = UUID.randomUUID().toString().replaceAll("-", "");
                        }
                    } else {
                        mainId = UUID.randomUUID().toString().replaceAll("-", "");
                    }
                } else {
                    mainId = contractId;
                    BmContract oldContract = getById(mainId);
                    if (Objects.isNull(oldContract)) {
//                    setFail(item, "合同不存在!");
                        sendFailData(item, "合同不存在!");
                        continue;
                    } else if (!Objects.equals(oldContract.getDataTypeCode(), item.getDataTypeCode())) {
                        sendFailData(item, "数据类型不能更新!");
                        continue;
                    }
                    bmContract.setContractCode(oldContract.getContractCode());
                    bmContract.setVersion(oldContract.getVersion());
                    bmContract.setContractCodeVersion(oldContract.getContractCodeVersion());
                }

                //初始化合同字段
                bmContract.setId(mainId);
                bmContract.setContractName(item.getContractName());//合同名称
                bmContract.setCustomCode(item.getThirdContractNo());//业务系统编码
                bmContract.setCustom(true);
                bmContract.setThirdVersion(item.getThirdVersion());//版本号

                bmContract.setDataSource(item.getDataSource());//主数据系统名称
                bmContract.setDataSourceCode(item.getDataSourceCode());//主数据编码
                bmContract.setDataSourceCodeModel(item.getExtendedFiled1());
                bmContract.setDataTypeName(DataTypeEnum.match(item.getDataTypeCode()).getMessage());
                bmContract.setDataTypeCode(item.getDataTypeCode());//合同性质

                bmContract.setContractType(item.getContractTypeName());//合同分类
                bmContract.setContractTypeCode(item.getContractTypeCode());
                bmContract.setContractTypeLevel(item.getNodeLevel());

                bmContract.setProjectDecision(ProjectDecisionEnum.match(item.getProjectDecisionId()).getMessage());
                bmContract.setProjectDecisionCode(item.getProjectDecisionId());//立项决策
                bmContract.setIsEp(0);//是否EP 默认否

                bmContract.setRelativeMethod(RelativeMethodEnum.match(item.getRelativeMethodCode()).getMessage());
                bmContract.setRelativeMethodCode(item.getRelativeMethodCode());// 相对方确定方式

                bmContract.setAuthorizedSource(AuthorizedSourceEnum.match(item.getAuthorizedSource()).getMessage());// 授权信息
                if (item.getAuthorizedSource() == 1) {
                    Authorization authorization = authorizationService.getOne(new QueryWrapper<Authorization>().eq("authorized_Book_Number", item.getAuthorizedId()));
                    if (authorization != null) {
                        bmContract.setAuthorizedCode(authorization.getAuthorizedBookNumber());
                        bmContract.setAuthorizedId(authorization.getId());
                        bmContract.setAuthorizedName(authorization.getAuthorizedName());
                        bmContract.setAuthorizedPerson(authorization.getTrustee());
                        bmContract.setAuthorizedPersonId(authorization.getTrusteeId());
                        bmContract.setAuthorizedRange(authorization.getEntrustedRange());
                    }
                }

                bmContract.setWhetherGroupMajor(item.getWhetherGroupMajor() == 1);// 是否集团重大合同
                bmContract.setWhetherUnitMajor(item.getWhetherUnitMajor() == 1);// 是否本单位重大合同

                bmContract.setMoneyTypeCode(item.getMoneyTypeCode());// 金额类型
                bmContract.setMoneyType(MoneyTypeEnum.match(item.getMoneyTypeCode()).getMessage());

                bmContract.setIsTax(item.getIsTax() == 1);// 是否含税
                String[] split = item.getSettlementMethodCode().split(",");

                // 使用 StringBuilder 拼接新的字符串
                StringBuilder sb = new StringBuilder();
                for (int i = 0; i < split.length; i++) {
                    sb.append(SettlementMethodEnum.match(split[i]).getMessage());
                    if (i < split.length - 1) {
                        sb.append("、");
                    }
                }
                bmContract.setSettlementMethod(sb.toString());// 结算方式
                bmContract.setSettlementMethodCode(item.getSettlementMethodCode());

                bmContract.setRevenueExpenditure(RevenueExpenditureEnum.match(item.getRevenueExpenditureCode()).getMessage());
                bmContract.setRevenueExpenditureCode(item.getRevenueExpenditureCode());// 收支方向

                bmContract.setAgreedStartTime(item.getAgreedStartTime());// 合同约定开始日期
                bmContract.setAgreedEndTime(item.getAgreedEndTime());// 合同约定结束日期

                bmContract.setOurPosition(item.getOurPosition());// 我方单位

                String[] split1 = item.getOurPartyName().split(",");
                String[] split2 = item.getOurPartyList().split(",");
                if (split1.length != split2.length) {
                    sendFailData(item, "我方签约主体名称与编码数量不匹配!");
                    continue;
                }
                String[] split3 = item.getOtherPartyName().split(",");
                String[] split4 = item.getOtherPartyList().split(",");
                if (split3.length != split4.length) {
                    sendFailData(item, "对方签约主体名称与编码数量不匹配!");
                    continue;
                }
                ArrayList<String> arrayList = new ArrayList<>(split2.length);
                Collections.addAll(arrayList, split2);
                List<SgHrOrgUnitB> sgHrOrgUnitBS = hrOrgUnitBService.listByIds(arrayList);

                if (split2.length != sgHrOrgUnitBS.size()) {
                    sendFailData(item, "我方签约主体编码有误!");
                    continue;
                }

                boolean match = sgHrOrgUnitBS.stream().anyMatch(obj ->
                        !"ogn".contains(obj.getUnitType()));
                if (match) {
                    sendFailData(item, "我方签约主体编码有误!");
                    continue;
                }

                bmContract.setOurPartyName(item.getOurPartyName());// 我方签约主体
                bmContract.setOurPartyList(item.getOurPartyList());// 我方签约主体编码
                bmContract.setOtherPartyName(item.getOtherPartyName());// 对方签约主体
                bmContract.setOtherPartyList(item.getOtherPartyList());// 对方签约主体编码


                if (item.getDataTypeCode().equals(DataStateBPM.DATA_TYPE_1.getKey())) {
                    bmContract.setContractMoney(item.getContractMoney());//合同金额

                    bmContract.setValueAddedTaxRate(item.getValueAddedTaxRate());
                    bmContract.setValueAddedTaxRateCode(item.getValueAddedTaxRateCode());

                    bmContract.setValueAddedTaxAmount(item.getValueAddedTaxAmount());//增值税额（元）

                    bmContract.setCurrency(item.getCurrency());//币种名称
                    bmContract.setCurrencyCode(item.getCurrencyCode());

                    bmContract.setExchangeRateMethodCode(item.getExchangeRateMethodCode());//汇率方式
                    bmContract.setExchangeRateMethod(ExchangeRateMethodEnum.match(item.getExchangeRateMethodCode()).getMessage());//汇率方式
                    bmContract.setExchangeRate(item.getExchangeRate());//汇率

                }
                if (item.getDataTypeCode().equals(DataStateBPM.DATA_TYPE_2.getKey())) {
                    BmContract contractOld = getOne(new QueryWrapper<BmContract>().eq("contract_code_version", item.getOriginalContractCode()));
                    if (contractOld == null) {
//                    setFail(item, "无法查到合同变更关联的原合同信息!");
                        sendFailData(item, "无法查到合同变更关联的原合同信息!");
                        continue;
                    }
                    if (contractOld.getDataTypeCode() != 1) {
                        sendFailData(item, "无法查到合同变更关联的原合同信息!");
                        continue;
                    }
                    bmContract.setChangeMoneyTypeCode(item.getChangeMoneyTypeCode());//金额变更类型编码
                    bmContract.setChangeMoneyType(ChangeMoneyTypeEnum.match(item.getChangeMoneyTypeCode()).getMessage());//金额变更类型编码

                    bmContract.setOriginalContractCode(contractOld.getContractCode());//原合同编码
                    bmContract.setOriginalContractName(contractOld.getContractName());//原合同名称
                    bmContract.setOriginalContractId(contractOld.getId());//原合同id
                    bmContract.setOriginalContractMoney(contractOld.getContractMoney());//原合同金额
                    bmContract.setOriginalValueAddedTaxAmount(contractOld.getValueAddedTaxAmount());//原合同增值税额
                    bmContract.setCurrency(contractOld.getCurrency());//币种
                    bmContract.setCurrencyCode(contractOld.getCurrencyCode());
                    bmContract.setExchangeRateMethod(contractOld.getExchangeRateMethod());//汇率方式
                    bmContract.setExchangeRateMethodCode(contractOld.getExchangeRateMethodCode());
                    bmContract.setExchangeRate(contractOld.getExchangeRate());//汇率
                    bmContract.setValueAddedTaxRate(contractOld.getValueAddedTaxRate());//增值税税率
                    bmContract.setValueAddedTaxRateCode(contractOld.getValueAddedTaxRateCode());//增值税税率编码

                    if (item.getChangeMoneyTypeCode() != 2) {
                        bmContract.setThisChangeMoney(item.getThisChangeMoney());//本次变更合同金额
                        bmContract.setThisChangeValueAddedTaxAmount(item.getThisChangeValueAddedTaxAmount());//本次变更增值税金额
                    }
                }
                if (item.getDataTypeCode().equals(DataStateBPM.DATA_TYPE_3.getKey())) {
                    BmContract contractOld = getOne(new QueryWrapper<BmContract>().eq("contract_code_version", item.getOriginalContractCode()));
                    if (contractOld == null) {
                        sendFailData(item, "无法查到合同变更关联的原合同信息!");
                        continue;
                    }
                    bmContract.setContractExecutoryMoney(item.getContractExecutoryMoney());//待履行金额
                    bmContract.setExplain(item.getExplain());//合同终止说明

                    bmContract.setOriginalContractCode(contractOld.getContractCode());//原合同编码
                    bmContract.setOriginalContractName(contractOld.getContractName());//原合同名称
                    bmContract.setOriginalContractId(contractOld.getId());//原合同id
                    bmContract.setOriginalContractMoney(contractOld.getContractMoney());//原合同金额
                    bmContract.setOriginalValueAddedTaxAmount(contractOld.getValueAddedTaxAmount());//原合同增值税额
                    bmContract.setCurrency(contractOld.getCurrency());//币种
                    bmContract.setCurrencyCode(contractOld.getCurrencyCode());
                    bmContract.setExchangeRateMethod(contractOld.getExchangeRateMethod());//汇率方式
                    bmContract.setExchangeRateMethodCode(contractOld.getExchangeRateMethodCode());
                    bmContract.setExchangeRate(contractOld.getExchangeRate());
                    bmContract.setValueAddedTaxRate(contractOld.getValueAddedTaxRate());//增值税税率
                    bmContract.setValueAddedTaxRateCode(contractOld.getValueAddedTaxRateCode());//增值税税率编码
                }

                bmContract.setTakeEffectCode(item.getTakeEffectCode());//生效状态编码
                bmContract.setPerformStateCode(item.getPerformStateCode());//合同履行状态编码 todo wch 少履行状态名称
                bmContract.setPerformState(PerformStateEnum.match(item.getPerformStateCode()).getMessage());
                OrgContextVo orgContext = OrgUtils.getOrgContextByCode(item.getOrgId());

                List<BmContractSeal> sealList = new ArrayList<>();
                if (DataStateBPM.TAKE_EFFECT_3.getKey().equals(item.getTakeEffectCode())) {
                    bmContract.setContractTakeEffectDate(item.getContractTakeEffectDate());//生效时间
                    bmContract.setOtherSealDate(item.getOtherSealDate());//对方盖章时间
                }
                if (StringUtils.isNotBlank(item.getSealList())) {
                    JSONArray jsonArray = JSONArray.parseArray(item.getSealList());
                    for (Object o : jsonArray) {
                        JSONObject jsonObject = JSONObject.parseObject(o.toString());
                        BmContractSeal bgContractSeal = insertContractSeal(orgContext, mainId, jsonObject);
                        // 已生效时：添加合同子表bmContractSeal；如果有用印信息 生成一条待用印让用户生效，且生效时间和对方盖章时间必填
                        sealList.add(bgContractSeal);
                    }
                    bmContract.setSealList(sealList);
                }

                //项目信息
                List<BmContractProject> proList = new ArrayList<>();
                if (StringUtils.isNotBlank(item.getProjectList())) {
                    JSONArray jsonArray = JSONArray.parseArray(item.getProjectList());
                    for (Object o : jsonArray) {
                        BmContractProject bmContractProject = new BmContractProject();
                        bmContractProject.setId(StringUtil.makeUUID());
                        bmContractProject.setParentId(mainId);
                        JSONObject jsonObject = JSONObject.parseObject(o.toString());
                        // 立项类型
                        String projectType = jsonObject.containsKey("projectType") ? jsonObject.getString("projectType") : "";
                        if (StringUtils.isNotBlank(projectType)) {
                            bmContractProject.setProjectType(projectType);
                        }
                        //项目名称
                        String projectName = jsonObject.containsKey("projectName") ? jsonObject.getString("projectName") : "";
                        if (StringUtils.isNotBlank(projectName)) {
                            bmContractProject.setProjectName(projectName);
                        }
                        //项目编号
                        String projectCode = jsonObject.containsKey("projectCode") ? jsonObject.getString("projectCode") : "";
                        if (StringUtils.isNotBlank(projectCode)) {
                            bmContractProject.setProjectCode(projectCode);
                        }
                        //子项目号
                        String childProjectCode = jsonObject.containsKey("childProjectCode") ? jsonObject.getString("childProjectCode") : "";
                        if (StringUtils.isNotBlank(childProjectCode)) {
                            bmContractProject.setChildProjectCode(childProjectCode);
                        }
                        // 项目投资额
                        BigDecimal totalMoney = jsonObject.containsKey("totalMoney") ? jsonObject.getBigDecimal("totalMoney") : null;
                        if (totalMoney != null) {
                            bmContractProject.setTotalMoney(totalMoney);
                        }

                        if (item.getDataTypeCode() == 1) {
                            // 分配合同额
                            BigDecimal assignMoney = jsonObject.containsKey("assignMoney") ? jsonObject.getBigDecimal("assignMoney") : null;
                            if (assignMoney != null) {
                                bmContractProject.setAssignMoney(assignMoney);
                            }
                        }
                        if (item.getDataTypeCode() == 2) {
                            // 项目金额变更类型编码
                            Integer projectChangeMoneyTypeCode = jsonObject.containsKey("projectChangeMoneyTypeCode") ? jsonObject.getInteger("projectChangeMoneyTypeCode") : null;
                            if (projectChangeMoneyTypeCode != null) {
                                bmContractProject.setProjectChangeMoneyTypeCode(projectChangeMoneyTypeCode);
                                bmContractProject.setProjectChangeMoneyType(ChangeMoneyTypeEnum.match(projectChangeMoneyTypeCode).getMessage());
                            }
                            // 项目本次变更金额
                            BigDecimal projectThisChangeMoney = jsonObject.containsKey("projectThisChangeMoney") ? jsonObject.getBigDecimal("projectThisChangeMoney") : null;
                            if (projectThisChangeMoney != null) {
                                bmContractProject.setProjectThisChangeMoney(projectThisChangeMoney);
                            }
                        }
                        if (item.getDataTypeCode() == 3) {
                            // 项目未履行金额
                            BigDecimal projectNoPerformMoney = jsonObject.containsKey("projectNoPerformMoney") ? jsonObject.getBigDecimal("projectNoPerformMoney") : null;
                            if (projectNoPerformMoney != null) {
                                bmContractProject.setProjectNoPerformMoney(projectNoPerformMoney);
                            }
                        }
                        bmContractProject.setCreateTime(new Date());
                        bmContractProject.setCreatePsnFullId(orgContext.getCurrentPsnFullId());
                        bmContractProject.setCreatePsnFullName(orgContext.getCurrentPsnFullName());
                        proList.add(bmContractProject);
                    }
                    bmContract.setProjectDecision(ProjectDecisionEnum.match(item.getProjectDecisionId()).getMessage());//立项决策
                    bmContract.setProjectDecisionCode(item.getProjectDecisionId());
                    bmContract.setProjectList(proList);
                }
                try {
                    // 计算合同金额
                    computeContractAmount(bmContract, proList);
                } catch (Exception e) {
                    log.error("{}计算合同金额异常：{}", bmContract.getContractName(), e.getMessage());
                }

                bmContract.setCreateOgnId(orgContext.getCurrentOgnId());
                bmContract.setCreateOgnName(orgContext.getCurrentOgnName());
                bmContract.setCreateDeptId(orgContext.getCurrentDeptId());
                bmContract.setCreateDeptName(orgContext.getCurrentDeptName());
                bmContract.setCreatePsnId(orgContext.getCurrentPsnId());
                bmContract.setCreatePsnName(orgContext.getCurrentPsnName());
                bmContract.setCreatePsnFullId(orgContext.getCurrentPsnFullId());
                bmContract.setCreatePsnFullName(orgContext.getCurrentPsnFullName());
                bmContract.setCreateOrgId(orgContext.getCurrentOrgId());
                bmContract.setCreateOrgName(orgContext.getCurrentOrgName());
                bmContract.setCreateGroupId(orgContext.getCurrentGroupId());
                bmContract.setCreateGroupName(orgContext.getCurrentGroupName());
                bmContract.setCreateLegalUnitId(orgContext.getCurrentLegalUnitId());
                bmContract.setCreateLegalUnitName(orgContext.getCurrentLegalUnitName());
                bmContract.setCreateTime(new Date());
                bmContract.setTakeEffectName(TakeEffectCodeEnum.match(item.getTakeEffectCode()).getMessage());
                bmContract.setTakeEffectCode(item.getTakeEffectCode());
                bmContract.setDataSource(item.getDataSource());
                bmContract.setDataSourceCode(item.getDataSourceCode());
                bmContract.setDataSourceCodeModel(item.getExtendedFiled1());
                bmContract.setDataState(DataStateBPM.FINISH.getValue());
                bmContract.setDataStateCode(DataStateBPM.FINISH.getKey());
                bmContract.setChangeStateCode(DataStateBPM.CHANGE_STATE_1.getKey());
                bmContract.setChangeState(DataStateBPM.CHANGE_STATE_1.getValue());
                bmContract.setCloseStateCode(DataStateBPM.CLOSE_STATE_1.getKey());
                bmContract.setCloseState(DataStateBPM.CLOSE_STATE_1.getValue());
                bmContract.setArchivedStateCode(DataStateBPM.ARCHIVED_STATE_1.getKey());
                bmContract.setArchivedState(DataStateBPM.ARCHIVED_STATE_1.getValue());
                if (StringUtils.isBlank(bmContract.getContractCode())) {
                    createContractCode2(bmContract);
                }
                try {
                    boolean noLock = createNoLock(bmContract, item, orgContext);
                } catch (Exception e) {
                    sendFailData(item, e.getMessage());
                    log.error("保存合同失败 ：{}", e.getMessage());
                }
            } catch (Exception e) {
                item.setHandle(1);
                item.setHandleResult(e.getMessage());
                bmContractInfoService.saveOrUpdate(item);
                log.error("处理合同信息失败！item={}, 错误原因：{}", item, e.getMessage(), e);
                // 关键点：捕获异常后继续循环，不中断处理
            }
        }
    }

    @Transactional
    public boolean createNoLock(BmContract bmContract, BmContractInfo item, OrgContextVo orgContext) throws ParseException {
        // 生效状态为已生效 生成一条用印信息
        //用印区域------------------------
        String fileNames = item.getAlreadySealFile();//生效附件名称
        String objectKeys = item.getAlreadySealFileId();//fileId

        String fileNames1 = item.getStaySealFile();//待用印附件名称
        String objectKeys1 = item.getStaySealFileId();//fileId

        //判断合同是否为待用印
        if (item.getTakeEffectCode() == 1) {
            // 传的附件“是否范本”关联列表信息
            List<BmContractText> textList = new ArrayList<>();
            // 将字符串分割成数组
            String[] idsArray = objectKeys1.split(",");
            String[] namesArray = fileNames1.split(",");
            List<SgSysDoc> sgSysDocs = new ArrayList<>();
            for (int i = 0; i < idsArray.length; i++) {
                String[] parts = splitFileName(namesArray[i]);
                SgSysDoc sgSysDoc = new SgSysDoc();
                String sysDocId = Utils.createUUID();
                sgSysDoc.setId(sysDocId);
                sgSysDoc.setDocName(parts[0]);
                sgSysDoc.setDocType(parts[1]);
                sgSysDoc.setIsDoc(1);
                sgSysDoc.setFileId(idsArray[i]);
                sgSysDoc.setFilePlace(hweiOBSConfig.getBucketName());
                sgSysDoc.setCreateTime(new Date());
                sgSysDocs.add(sgSysDoc);

                JSONArray jsonArray = new JSONArray();
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("docId", sysDocId);
                jsonObject.put("name", namesArray[i]);
                jsonObject.put("status", namesArray[i]);
                jsonArray.add(jsonObject);
                // 这里做了计算，传过来的附件，以附件实体为准，如果关联关系没有找到，就按照非范本处理
                BmContractText bmContractText = new BmContractText();
                bmContractText.setId(StringUtil.makeUUID());
                bmContractText.setParentId(bmContract.getId());
                bmContractText.setFileTypeCode("0");
                bmContractText.setFileType("合同正文");
                bmContractText.setFileFromCode(0 == item.getIsItAtemplate() ? "2" : "1");
                bmContractText.setFileFrom(0 == item.getIsItAtemplate() ? "使用非范本" : "使用范本");
                bmContractText.setAttachment(jsonArray.toString());
                bmContractText.setCreateTime(new Date());
                bmContractText.setCreatePsnFullId(orgContext.getCurrentPsnFullId());
                bmContractText.setCreatePsnFullName(orgContext.getCurrentPsnFullName());
                textList.add(bmContractText);
                sgSysDocService.insertSelective(sgSysDoc);
            }
//            bmContract.setContractFinalDraftFile();
            if (!CollectionUtils.isEmpty(textList)) {
                Utils.saveChilds(textList, "parent_id", bmContract.getId(), bmContractTextService);
            }
        }
        //判断是否是已生效
//        if (item.getTakeEffectCode() == 3) {
//            //如果是 法务系统会生成一条用印的信息
//            sealStampOther(bmContract, fileNames, objectKeys, item, orgContext);
//        }
        if (!CollectionUtils.isEmpty(bmContract.getProjectList())) {
            Utils.saveChilds(bmContract.getProjectList(), "parent_id", bmContract.getId(), bmContractProjectService);
        }
        if (!CollectionUtils.isEmpty(bmContract.getSealList())) {
            sealStampOther(bmContract, fileNames, objectKeys, item, orgContext);
            Utils.saveChilds(bmContract.getSealList(), "parent_id", bmContract.getId(), bmContractSealService);
        }
        boolean saved = saveOrUpdate(bmContract);
        if (item.getDataTypeCode() == 2 || item.getDataTypeCode() == 3) {
            Integer code = item.getDataTypeCode() == 2 ? DataStateBPM.CHANGE_STATE_5.getKey() : DataStateBPM.CHANGE_STATE_6.getKey();
            String name = item.getDataTypeCode() == 2 ? DataStateBPM.CHANGE_STATE_5.getValue() : DataStateBPM.CHANGE_STATE_6.getValue();
            BmContract contractOld = getOne(new QueryWrapper<BmContract>().eq("contract_code_version", item.getOriginalContractCode()));
            contractOld.setChangeState(name);
            contractOld.setChangeStateCode(code);
            saveOrUpdate(contractOld);
        }
        sendContractCode(item, bmContract);
        bmContractInfoService.saveOrUpdate(item);
        return saved;
    }

    /**
     * 推送成功返回信息
     *
     * @param item       合同接口表
     * @param bmContract 合同
     */
    private void sendContractCode(BmContractInfo item, BmContract bmContract) {
        QueryWrapper<SysDict> wrapper = new QueryWrapper<>();
        if (StringUtils.isNotBlank(item.getExtendedFiled1())) {
            wrapper.eq("dic_code", item.getExtendedFiled1());
        } else {
            wrapper.eq("dic_code", bmContract.getDataSourceCode());
        }
        SysDict sysDict = sysDictService.getOne(wrapper);
        String returnObject = "";
        if (sysDict == null) {
            returnObject = "系统编码未配置";
            item.setHandleResult(returnObject);
            item.setHandle(1);
            item.setStatusCode(0);
            return;
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("thirdContractNo", bmContract.getCustomCode());
        jsonObject.put("thirdVersion", bmContract.getThirdVersion());
        jsonObject.put("contractId", bmContract.getId());
        jsonObject.put("contractCode", bmContract.getContractCodeVersion());
        JSONObject listOb = new JSONObject();
        listOb.put("code", 100000);
        listOb.put("data", jsonObject);
        listOb.put("message", "success");
        String string = listOb.toJSONString();
        log.info("推送结果：{}", string);
        returnObject = getString(sysDict, string);
        item.setEsbPush(1);
        item.setEsbResult(returnObject);
        item.setHandleResult(string);
        item.setHandle(1);
        item.setStatusCode(1);
    }

    /**
     * 推送失败返回信息
     *
     * @param item 合同接口表
     */
    private void sendFailData(BmContractInfo item, String msg) {

        QueryWrapper<SysDict> wrapper = new QueryWrapper<>();
        wrapper.eq("dic_code", item.getDataSourceCode());
        SysDict sysDict = sysDictService.getOne(wrapper);
        String returnObject = "";
        if (sysDict == null) {
            returnObject = "系统编码未配置";
            item.setHandleResult(returnObject);
            item.setHandle(1);
            item.setStatusCode(0);
            return;
        }
        JSONObject listOb = new JSONObject();
        listOb.put("code", -1);
        listOb.put("data", msg);
        listOb.put("thirdVersion", item.getThirdVersion());
        listOb.put("thirdContractNo", item.getThirdContractNo());
        listOb.put("message", "fail");
        String string = listOb.toJSONString();
        returnObject = getString(sysDict, string);
        item.setEsbPush(1);
        item.setEsbResult(returnObject);
        item.setHandleResult(string);
        item.setHandle(1);
        item.setStatusCode(0);
        bmContractInfoService.saveOrUpdate(item);
    }

    /**
     * 待用印合同推送生效时间 and 生效附件
     *
     * @param bmContract 合同
     */
    @Override
    public void sendSaveContractCode(BmContract bmContract) {
        try {
            QueryWrapper<SysDict> wrapper = new QueryWrapper<>();
            if (StringUtils.isNotBlank(bmContract.getDataSourceCodeModel())) {
                wrapper.eq("dic_code", bmContract.getDataSourceCodeModel());
            } else {
                wrapper.eq("dic_code", bmContract.getDataSourceCode());
            }
            SysDict sysDict = sysDictService.getOne(wrapper);
            if (sysDict == null) {
                log.error("合同生效后返回业务系统失败：系统编码未配置");
                return;
            }
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("thirdContractNo", bmContract.getCustomCode());
            jsonObject.put("thirdVersion", bmContract.getThirdVersion());
            jsonObject.put("contractId", bmContract.getId());
            jsonObject.put("contractCode", bmContract.getContractCodeVersion());
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
            jsonObject.put("contractTakeEffectDate", dateFormat.format(bmContract.getContractTakeEffectDate()));
            JSONArray jsonArray = JSONArray.parseArray(bmContract.getContractTakeEffectFile());
            StringBuilder buf = new StringBuilder();
            for (Object o : jsonArray) {
                JSONObject jsono = (JSONObject) o;
                String docId = jsono.getString("docId");
                SgSysDoc sysDoc = sgSysDocService.getById(docId);
                if ("fwtest".equals(sysDoc.getFilePlace()) || "fwprod".equals(sysDoc.getFilePlace())) {
                    buf.append(sysDoc.getFileId()).append(",");
                } else {
                    buf.append(sysDoc.getFilePlace()).append(sysDoc.getFileId()).append(",");
                }
            }
//            JSONObject object = jsonArray.getJSONObject(0);
//
//            String docId = object.getString("docId");
//            SgSysDoc sysDoc = sgSysDocService.getById(docId);
//            String objectKey = sysDoc.getFileId();
//            if(!"fwtest".equals(sysDoc.getFilePlace()) && !"fwprod".equals(sysDoc.getFilePlace())){
//                objectKey = sysDoc.getFilePlace() + objectKey;
//            }
            jsonObject.put("fileId", buf.substring(0, buf.length() - 1));
            JSONObject listOb = new JSONObject();
            listOb.put("code", 100000);
            listOb.put("data", jsonObject);
            listOb.put("message", "success");
            String string = listOb.toJSONString();
            log.info("合同生效后返回业务系统参数：{}", string);
            getString(sysDict, string);
        } catch (Exception e) {
            log.error("合同生效后返回业务系统失败：{}", e.getMessage());
        }
    }


    /**
     * 归档
     *
     * @param bmContract 合同实体类
     * @return 是否成功
     */
    @Override
    @Transactional
    public Boolean saveContractFileData(BmContract bmContract) {
        List<BmContractPlace> placeList = bmContract.getPlaceList();
        if (bmContract.getArchivedStateCode() == 5) {
            for (BmContractPlace bmContractPlace : placeList) {
                bmContractPlace.setPlaceStateName("已归档");
                bmContractPlace.setPlaceStateCode(5);
            }
            //合同归档接口，状态改为已归档。调用归档接口进行归档
            bmContract.setArchivedState("已归档");
            bmContract.setArchivedStateCode(5);
        }
        Utils.saveChilds(placeList, "parent_id", bmContract.getId(), bmContractPlaceService);
        return saveOrUpdate(bmContract);
    }


    private String getString(SysDict sysDict, String jsonObject) {
        String returnObject;
        returnObject = OkHttpUtils.builder().url(esbUrl + sysDict.getDicName())
                .addHeader("Content-Type", "application/json; charset=utf-8")
                .addHeader("OnlineToken", "rPlSk46zpCBaH5vPK68hq5xN0XBhVODnEJzMmPBR3Wa2VR2xL1kAOpP7bW5LRIfaYenoRte2ye4=")
                // 如果是true的话，会类似于postman中post提交方式的raw，用json的方式提交，不是表单
                // 如果是false的话传统的表单提交
                .post(true, jsonObject).sync();
        return returnObject;
    }


    private BmContractSeal insertContractSeal(OrgContextVo orgContext, String mainId, JSONObject jsonObject) {
        String sealCode = jsonObject.containsKey("sealCode") ? jsonObject.getString("sealCode") : "";
        Integer sealNumber = jsonObject.containsKey("sealNumber") ? jsonObject.getInteger("sealNumber") : null;
        Integer printsNumber = jsonObject.containsKey("printsNumber") ? jsonObject.getInteger("printsNumber") : null;

        Integer sealNumberOld = jsonObject.containsKey("sealNumberOld") ? jsonObject.getInteger("sealNumberOld") : null;
        Integer printsNumberOld = jsonObject.containsKey("printsNumberOld") ? jsonObject.getInteger("printsNumberOld") : null;
        Date ourSealDate = jsonObject.containsKey("ourSealDate") ? jsonObject.getDate("ourSealDate") : null;

        SgSealManage sgSealManage = sgSealManageService.getOne(new QueryWrapper<SgSealManage>().eq("seal_code", sealCode));
        if (sgSealManage != null) {
            BmContractSeal bmContractSeal = new BmContractSeal();
            bmContractSeal.setId(StringUtil.makeUUID());
            bmContractSeal.setParentId(mainId);
            bmContractSeal.setSealName(sgSealManage.getSealName());
            bmContractSeal.setSealCode(sealCode);
            bmContractSeal.setSealId(sgSealManage.getId());
            bmContractSeal.setSealType(sgSealManage.getSealType());
            bmContractSeal.setSealTypeId(sgSealManage.getSealTypeId());
            bmContractSeal.setSealTypeCode(sgSealManage.getSealTypeCode());
            bmContractSeal.setSealAdmin(sgSealManage.getSealAdmin());
            bmContractSeal.setSealAdminId(sgSealManage.getSealAdminId());

            bmContractSeal.setSealNumber(sealNumber);
            bmContractSeal.setPrintsNumber(printsNumber);

            bmContractSeal.setSealNumberOld(sealNumberOld);
            bmContractSeal.setPrintsNumberOld(printsNumberOld);

            bmContractSeal.setOurSealDate(ourSealDate);
            bmContractSeal.setCreateTime(new Date());
            bmContractSeal.setCreatePsnFullId(orgContext.getCurrentPsnFullId());
            bmContractSeal.setCreatePsnFullName(orgContext.getCurrentPsnFullName());
            return bmContractSeal;
        }

        return new BmContractSeal();
    }


    @Transactional
    public void sealStampOther(BmContract bmContract, String fileNames, String objectKeys, BmContractInfo item, OrgContextVo orgContext) throws ParseException {
        String uuid = StringUtil.makeUUID();
        List<SgSealApprovalDetail> detailList = new ArrayList<>();
        String sealNames = "";
        String sealIds = "";
        String sealTypes = "";
        String sealTypeIds = "";
        String sealAdmins = "";
        String sealAdminIds = "";

        List<BmContractSeal> list = bmContract.getSealList();
        if (!CollectionUtils.isEmpty(list)) {
            for (BmContractSeal bmContractSeal : list) {
                if ("".equals(sealNames)) {
                    sealNames = bmContractSeal.getSealName();
                    sealIds = bmContractSeal.getSealId();
                } else {
                    sealNames += "、" + bmContractSeal.getSealName();
                    sealIds += "," + bmContractSeal.getSealId();
                }
                if ("".equals(sealTypes)) {
                    sealTypes = bmContractSeal.getSealType();
                    sealTypeIds = bmContractSeal.getSealTypeId();
                } else {
                    sealTypes += "、" + bmContractSeal.getSealType();
                    sealTypeIds += "," + bmContractSeal.getSealTypeId();
                }
                if ("".equals(sealAdmins)) {
                    sealAdmins = bmContractSeal.getSealAdmin();
                    sealAdminIds = bmContractSeal.getSealAdminId();
                } else {
                    sealAdmins += "、" + bmContractSeal.getSealAdmin();
                    sealAdminIds += "," + bmContractSeal.getSealAdminId();
                }

                SgSealApprovalDetail sgSealApprovalDetail = new SgSealApprovalDetail();
                sgSealApprovalDetail.setId(StringUtil.makeUUID());
                sgSealApprovalDetail.setParentId(uuid);
                sgSealApprovalDetail.setSealName(bmContractSeal.getSealName());
                sgSealApprovalDetail.setSealId(bmContractSeal.getSealId());
                sgSealApprovalDetail.setSealType(bmContractSeal.getSealType());
                sgSealApprovalDetail.setSealTypeId(bmContractSeal.getSealTypeId());
                sgSealApprovalDetail.setSealAdminOld(bmContractSeal.getSealAdmin());
                sgSealApprovalDetail.setSealAdminIdOld(bmContractSeal.getSealAdminId());
                sgSealApprovalDetail.setSealNumberOld(bmContractSeal.getSealNumberOld().toString());
                sgSealApprovalDetail.setPrintsNumberOld(bmContractSeal.getPrintsNumberOld().toString());
                if (item.getTakeEffectCode() == 3) {
                    sgSealApprovalDetail.setOurSealTime(bmContractSeal.getOurSealDate());
                    sgSealApprovalDetail.setSealNumber(bmContractSeal.getSealNumber().toString());
                    sgSealApprovalDetail.setPrintsNumber(bmContractSeal.getPrintsNumber().toString());
                    sgSealApprovalDetail.setDataState("已生效");
                    sgSealApprovalDetail.setDataStateCode(3);
                }
                sgSealApprovalDetail.setCreateTime(new Date());
                detailList.add(sgSealApprovalDetail);
            }
        }
        SgSealApproval sgSealApproval = new SgSealApproval();
        sgSealApproval.setId(uuid);
        sgSealApproval.setContractName(bmContract.getContractName());
        sgSealApproval.setContractId(bmContract.getId());
        sgSealApproval.setContractCode(bmContract.getContractCode());
        sgSealApproval.setSealPurpose("合同用印");
        sgSealApproval.setSealPurposeId("0");
        sgSealApproval.setSealNames(sealNames);
        sgSealApproval.setSealIds(sealIds);
        sgSealApproval.setSealTypes(sealTypes);
        sgSealApproval.setSealTypeIds(sealTypeIds);
        sgSealApproval.setSealAdmins(sealAdmins);
        sgSealApproval.setSealAdminIds(sealAdminIds);
        sgSealApproval.setCreateOgnId(orgContext.getCurrentOgnId());
        sgSealApproval.setCreateOgnName(orgContext.getCurrentOgnName());
        sgSealApproval.setCreateDeptId(orgContext.getCurrentDeptId());
        sgSealApproval.setCreateDeptName(orgContext.getCurrentDeptName());
        sgSealApproval.setCreatePsnId(orgContext.getCurrentPsnId());
        sgSealApproval.setCreatePsnName(orgContext.getCurrentPsnName());
        sgSealApproval.setCreatePsnFullId(orgContext.getCurrentPsnFullId());
        sgSealApproval.setCreatePsnFullName(orgContext.getCurrentPsnFullName());
        sgSealApproval.setCreateOrgId(orgContext.getCurrentOrgId());
        sgSealApproval.setCreateOrgName(orgContext.getCurrentOrgName());
        sgSealApproval.setCreateGroupId(orgContext.getCurrentGroupId());
        sgSealApproval.setCreateGroupName(orgContext.getCurrentGroupName());
        sgSealApproval.setCreateTime(new Date());
        sgSealApproval.setDataState(DataStateBPM.FINISH.getValue());
        sgSealApproval.setDataStateCode(DataStateBPM.FINISH.getKey());

        if (item.getTakeEffectCode() == 3) {
            JSONArray jsonArray = new JSONArray();
            // 将字符串分割成数组
            String[] idsArray = objectKeys.split(",");
            String[] namesArray = fileNames.split(",");
            for (int i = 0; i < idsArray.length; i++) {
                String[] parts = splitFileName(namesArray[i]);
                SgSysDoc sgSysDoc = new SgSysDoc();
                String sysDocId = Utils.createUUID();
                sgSysDoc.setId(sysDocId);
                sgSysDoc.setDocName(parts[0]);
                sgSysDoc.setDocType(parts[1]);
                sgSysDoc.setIsDoc(1);
                sgSysDoc.setFileId(idsArray[i]);
                sgSysDoc.setFilePlace(hweiOBSConfig.getBucketName());
                sgSysDoc.setCreateTime(new Date());
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("docId", sysDocId);
                jsonObject.put("name", namesArray[i]);
                jsonObject.put("status", namesArray[i]);
                jsonArray.add(jsonObject);
                sgSysDocService.insertSelective(sgSysDoc);
            }
            sgSealApproval.setOriginalAttachment(jsonArray.toString());
            bmContract.setContractTakeEffectFile(jsonArray.toString());
            sgSealApproval.setContractCode(bmContract.getContractCode());
            sgSealApproval.setEffectiveTime(item.getContractTakeEffectDate());
            sgSealApproval.setTakeEffectName(DataStateBPM.TAKE_EFFECT_3.getValue());
            sgSealApproval.setTakeEffectCode(DataStateBPM.TAKE_EFFECT_3.getKey());
            if (item.getOtherSealDate() != null) {
                sgSealApproval.setOtherSealTime(item.getOtherSealDate());
            }
        }

        if (item.getTakeEffectCode() == 1) {
            sgSealApproval.setContractCode(bmContract.getContractCode());
            sgSealApproval.setTakeEffectName(DataStateBPM.TAKE_EFFECT_1.getValue());
            sgSealApproval.setTakeEffectCode(DataStateBPM.TAKE_EFFECT_1.getKey());
        }

        //更新数据时需要删除原有的用印
        sgSealApprovalService.remove(new QueryWrapper<SgSealApproval>().eq("contract_id", bmContract.getId()));
        sgSealApprovalService.saveOrUpdate(sgSealApproval);
        if (!CollectionUtils.isEmpty(detailList)) {
            sgSealApprovalDetailService.saveOrUpdateBatch(detailList);
        }
    }


    /**
     * 合同台账导出
     *
     * @param json     查询条件
     * @param response HTTP响应对象，用于返回Excel文件
     */
    @Override
    public void exportContractLedger(JSONObject json, HttpServletResponse response) {
        QueryWrapper<BmContract> queryWrapper = new QueryWrapper<>();
        // 应用查询条件
        getFilter(queryWrapper, json);
        List<BmContract> contractList = list(queryWrapper);

        ClassPathResource resource = new ClassPathResource("template/contract/contractLedger.xlsx");
        InputStream inputStream = null;
        ByteArrayOutputStream out = null;
        try {
            inputStream = resource.getInputStream();
            // 创建工作薄
            XSSFWorkbook workbook = new XSSFWorkbook(inputStream);
            // 获取第一个sheet页
            XSSFSheet sheet = workbook.getSheetAt(0);

            // 创建单元格样式
            XSSFCellStyle style = createCellStyle(workbook);
            XSSFCellStyle dateStyle = createCellStyle(workbook);
            short dateFormat = workbook.createDataFormat().getFormat("yyyy-MM-dd");
            dateStyle.setDataFormat(dateFormat);
            XSSFCellStyle moneyStyle = createCellStyle(workbook);
            short moneyFormat = workbook.createDataFormat().getFormat("0.00");
            moneyStyle.setDataFormat(moneyFormat);

            // 填充数据
            for (int i = 0; i < contractList.size(); i++) {
                BmContract contract = contractList.get(i);
                XSSFRow row = sheet.createRow(i + 1);

                // 设置行高
                row.setHeightInPoints(35);

                // 填充所有列的数据
                setCellsByContract(row, contract, i + 1, style, dateStyle, moneyStyle);
            }

            // 设置响应头
            String fileName = "合同台账.xlsx";
            response.setHeader("Access-Control-Expose-Headers", "Response-Type");
            response.setHeader("Response-Type", "doc");
            response.setContentType("application/octet-stream");
            response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(fileName, "UTF-8"));
            response.setCharacterEncoding("UTF-8");

            out = new ByteArrayOutputStream();
            OutputStream os = response.getOutputStream();
            workbook.write(os);
            os.flush();
            os.close();
        } catch (Exception e) {
            log.error("合同台账导出异常", e);
        } finally {
            if (out != null) {
                try {
                    out.close();
                } catch (IOException e) {
                    log.error("关闭输出流异常", e);
                }
            }
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    log.error("关闭输入流异常", e);
                }
            }
        }
    }

    /**
     * 创建单元格样式
     */
    private XSSFCellStyle createCellStyle(XSSFWorkbook workbook) {
        XSSFFont font = workbook.createFont();
        font.setFontName("宋体");
        font.setFontHeightInPoints((short) 10);
        XSSFCellStyle style = workbook.createCellStyle();
        style.setFont(font);
        style.setVerticalAlignment(VerticalAlignment.CENTER); // 垂直居中
        style.setAlignment(HorizontalAlignment.CENTER); // 水平居中
        style.setWrapText(true); // 自动换行
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        return style;
    }

    /**
     * 设置合同数据到Excel行
     */
    private void setCellsByContract(XSSFRow row, BmContract contract, int num, XSSFCellStyle style, XSSFCellStyle dateStyle, XSSFCellStyle moneyStyle) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");


        // 设置每个单元格的数据和样式
        for (int i = 0; i < 23; i++) {
            XSSFCell cell = row.createCell(i);

            // 根据列类型设置样式
            if (i == 15 || i == 16) { // 日期类型字段
                cell.setCellStyle(dateStyle);
            } else if (i == 8) { // 金额类型字段
                cell.setCellStyle(moneyStyle);
            } else {
                cell.setCellStyle(style);
            }

            // 根据列序号设置相应的值
            switch (i) {
                case 0: // 序号
                    cell.setCellValue(num);
                    break;
                case 1: // 合同编号
                    if (StringUtils.isNotBlank(contract.getContractCode())) {
                        cell.setCellValue(contract.getContractCode());
                    }
                    break;
                case 2: // 版本号
                    if (StringUtils.isNotBlank(contract.getVersion())) {
                        cell.setCellValue(contract.getVersion());
                    }
                    break;
                case 3: // 自定义编码
                    if (StringUtils.isNotBlank(contract.getCustomCode())) {
                        cell.setCellValue(contract.getCustomCode());
                    }
                    break;
                case 4: // 审批单号
                    if (StringUtils.isNotBlank(contract.getApprovalCode())) {
                        cell.setCellValue(contract.getApprovalCode());
                    }
                    break;
                case 5: // 合同名称
                    if (StringUtils.isNotBlank(contract.getContractName())) {
                        cell.setCellValue(contract.getContractName());
                    }
                    break;
                case 6: // 合同性质
                    if (StringUtils.isNotBlank(contract.getDataTypeName())) {
                        cell.setCellValue(contract.getDataTypeName());
                    }
                    break;
                case 7: // 合同类型
                    if (StringUtils.isNotBlank(contract.getContractType())) {
                        cell.setCellValue(contract.getContractType());
                    }
                    break;
                case 8: // 合同金额
                    if (contract.getDataTypeCode() != null) {
                        // 根据合同性质代码决定显示哪个金额
                        if (contract.getDataTypeCode() == 1 || contract.getDataTypeCode() == 4) {
                            if (contract.getContractMoney() != null) {
                                cell.setCellValue(contract.getContractMoney().doubleValue());
                            }
                        } else if (contract.getDataTypeCode() == 2 || contract.getDataTypeCode() == 5) {
                            if (contract.getThisChangeMoney() != null) {
                                cell.setCellValue(contract.getThisChangeMoney().doubleValue());
                            }
                        }
                    }
                    break;
                case 9: // 我方签约主体
                    if (StringUtils.isNotBlank(contract.getOurPartyName())) {
                        cell.setCellValue(contract.getOurPartyName());
                    }
                    break;
                case 10: // 对方签约主体
                    if (StringUtils.isNotBlank(contract.getOtherPartyName())) {
                        cell.setCellValue(contract.getOtherPartyName());
                    }
                    break;
                case 11: // 来源系统
                    if (StringUtils.isNotBlank(contract.getDataSource())) {
                        cell.setCellValue(contract.getDataSource());
                    }
                    break;
                case 12: // 经办人
                    if (StringUtils.isNotBlank(contract.getCreatePsnName())) {
                        cell.setCellValue(contract.getCreatePsnName());
                    }
                    break;
                case 13: // 经办部门
                    if (StringUtils.isNotBlank(contract.getCreateDeptName())) {
                        cell.setCellValue(contract.getCreateDeptName());
                    }
                    break;
                case 14: // 经办单位
                    if (StringUtils.isNotBlank(contract.getCreateDeptName())) {
                        cell.setCellValue(contract.getCreateDeptName());
                    }
                    break;
                case 15: // 经办时间
                    if (contract.getCreateTime() != null) {
                        sdf = new SimpleDateFormat("yyyy-MM-dd");
                        cell.setCellValue(sdf.format(contract.getCreateTime())); // 转为字符串
                    }
                    break;
                case 16: // 生效时间
                    if (contract.getContractTakeEffectDate() != null) {
                        sdf = new SimpleDateFormat("yyyy-MM-dd");
                        cell.setCellValue(sdf.format(contract.getContractTakeEffectDate())); // 转为字符串
                    }
                    break;
                case 17: // 生效状态
                    if (StringUtils.isNotBlank(contract.getTakeEffectName())) {
                        cell.setCellValue(contract.getTakeEffectName());
                    }
                    break;
                case 18: // 归档状态
                    if (StringUtils.isNotBlank(contract.getArchivedState())) {
                        cell.setCellValue(contract.getArchivedState());
                    }
                    break;
                case 19: // 变更状态
                    if (StringUtils.isNotBlank(contract.getChangeState())) {
                        cell.setCellValue(contract.getChangeState());
                    }
                    break;
                case 20: // 履行状态
                    if (StringUtils.isNotBlank(contract.getPerformState())) {
                        cell.setCellValue(contract.getPerformState());
                    }
                    break;
                case 21: // 关闭状态
                    if (StringUtils.isNotBlank(contract.getCloseState())) {
                        cell.setCellValue(contract.getCloseState());
                    }
                    break;
                case 22: // 流程状态
                    if (StringUtils.isNotBlank(contract.getDataState())) {
                        cell.setCellValue(contract.getDataState());
                    }
                    break;
                default:
                    break;
            }
        }
    }

    /**
     * 合同台账查询条件设置
     */
    private void getContractLedgerFilter(JSONObject json, QueryWrapper<BmContract> wrapper) {
        // 合同名称
        String contractName = json.containsKey("contractName") ? json.getString("contractName") : null;
        // 合同编码
        String contractCode = json.containsKey("contractCode") ? json.getString("contractCode") : null;
        // 合同类型
        String contractType = json.containsKey("contractType") ? json.getString("contractType") : null;
        // 我方签约主体
        String ourPartyName = json.containsKey("ourPartyName") ? json.getString("ourPartyName") : null;
        // 对方签约主体
        String otherPartyName = json.containsKey("otherPartyName") ? json.getString("otherPartyName") : null;
        // 收支方向
        String revenueExpenditure = json.containsKey("revenueExpenditure") ? json.getString("revenueExpenditure") : null;
        // 合同金额(最小值)
        BigDecimal contractMoneyMin = json.containsKey("contractMoneyMin") ? json.getBigDecimal("contractMoneyMin") : null;
        // 合同金额(最大值)
        BigDecimal contractMoneyMax = json.containsKey("contractMoneyMax") ? json.getBigDecimal("contractMoneyMax") : null;
        // 履行状态
        String performState = json.containsKey("performState") ? json.getString("performState") : null;
        // 变更状态
        String changeState = json.containsKey("changeState") ? json.getString("changeState") : null;
        // 开始日期(最小值)
        Date startDateMin = json.containsKey("startDateMin") ? json.getDate("startDateMin") : null;
        // 开始日期(最大值)
        Date startDateMax = json.containsKey("startDateMax") ? json.getDate("startDateMax") : null;
        // 结束日期(最小值)
        Date endDateMin = json.containsKey("endDateMin") ? json.getDate("endDateMin") : null;
        // 结束日期(最大值)
        Date endDateMax = json.containsKey("endDateMax") ? json.getDate("endDateMax") : null;
        // 模糊搜索值
        String fuzzyValue = json.containsKey("fuzzyValue") ? json.getString("fuzzyValue") : null;

        String orgId = json.containsKey("orgId") ? json.getString("orgId") : "";

        // 模糊搜索匹配字段
        String[] cols = {"contract_name", "contract_code", "contract_type", "our_party_name", "other_party_name",
                "revenue_expenditure", "contract_money", "perform_state", "change_state"};

        // 应用筛选条件
        if (StringUtils.isNotBlank(contractName)) {
            wrapper.and(i -> i.like("contract_name", contractName));
        }

        if (StringUtils.isNotBlank(contractCode)) {
            wrapper.and(i -> i.like("contract_code", contractCode));
        }

        if (StringUtils.isNotBlank(contractType)) {
            wrapper.and(i -> i.like("contract_type", contractType));
        }

        if (StringUtils.isNotBlank(ourPartyName)) {
            wrapper.and(i -> i.like("our_party_name", ourPartyName));
        }

        if (StringUtils.isNotBlank(otherPartyName)) {
            wrapper.and(i -> i.like("other_party_name", otherPartyName));
        }

        if (StringUtils.isNotBlank(revenueExpenditure)) {
            wrapper.and(i -> i.eq("revenue_expenditure", revenueExpenditure));
        }

        if (contractMoneyMin != null && contractMoneyMin.doubleValue() > 0) {
            wrapper.and(i -> i.ge("contract_money", contractMoneyMin));
        }

        if (contractMoneyMax != null && contractMoneyMax.doubleValue() > 0) {
            wrapper.and(i -> i.le("contract_money", contractMoneyMax));
        }

        if (StringUtils.isNotBlank(performState)) {
            wrapper.and(i -> i.eq("perform_state", performState));
        }

        if (StringUtils.isNotBlank(changeState)) {
            wrapper.and(i -> i.eq("change_state", changeState));
        }

        if (startDateMin != null) {
            wrapper.and(i -> i.ge("agreed_start_time", startDateMin));
        }

        if (startDateMax != null) {
            wrapper.and(i -> i.le("agreed_start_time", startDateMax));
        }

        if (endDateMin != null) {
            wrapper.and(i -> i.ge("agreed_end_time", endDateMin));
        }

        if (endDateMax != null) {
            wrapper.and(i -> i.le("agreed_end_time", endDateMax));
        }

        // 应用模糊搜索条件
        if (StringUtils.isNotBlank(fuzzyValue)) {
            wrapper.and(i -> i.like("contract_name", fuzzyValue)
                    .or().like("contract_code", fuzzyValue)
                    .or().like("contract_type", fuzzyValue)
                    .or().like("our_party_name", fuzzyValue)
                    .or().like("other_party_name", fuzzyValue)
                    .or().like("revenue_expenditure", fuzzyValue)
                    .or().like("contract_money", fuzzyValue)
                    .or().like("perform_state", fuzzyValue)
                    .or().like("change_state", fuzzyValue));
        }

        // 应用数据权限
        Long functionId = DataAuthUtils.getFunctionIdByCode("contract_ledger_index");
        DataAuthUtils.dataPermSql(wrapper, null, functionId, orgId, "", "contract");

        // 排序
        wrapper.orderByDesc("create_time");
    }

    /**
     * 导出风险清单台账
     *
     * @param json     查询条件
     * @param response HTTP响应对象
     */

    public void exportRiskLedger(JSONObject json, HttpServletResponse response) {
        QueryWrapper<BmContract> queryWrapper = new QueryWrapper<>();
        getRiskFilter(queryWrapper, json);
        List<BmContract> contractList = bmContractMapper.selectList(queryWrapper);

        ClassPathResource resource = new ClassPathResource("template/contract/contractRiskLedger.xlsx");
        InputStream inputStream = null;
        ByteArrayOutputStream out = null;
        try {
            inputStream = resource.getInputStream();
            // 创建工作薄
            XSSFWorkbook workbook = new XSSFWorkbook(inputStream);
            // 获取第一个sheet页
            XSSFSheet sheet = workbook.getSheetAt(0);

            // 创建单元格样式
            XSSFCellStyle style = createCellStyle(workbook);
            XSSFCellStyle dateStyle = createCellStyle(workbook);
            short dateFormat = workbook.createDataFormat().getFormat("yyyy-MM-dd");
            dateStyle.setDataFormat(dateFormat);
            XSSFCellStyle moneyStyle = createCellStyle(workbook);
            short moneyFormat = workbook.createDataFormat().getFormat("0.00");
            moneyStyle.setDataFormat(moneyFormat);

            // 填充数据
            for (int i = 0; i < contractList.size(); i++) {
                BmContract contract = contractList.get(i);
                XSSFRow row = sheet.createRow(i + 1);

                // 设置行高
                row.setHeightInPoints(35);

                // 填充所有列的数据
                setRiskCellsByContract(row, contract, i + 1, style, dateStyle, moneyStyle);
            }

            // 设置响应头
            String fileName = "风控清单台账.xlsx";
            response.setHeader("Access-Control-Expose-Headers", "Response-Type");
            response.setHeader("Response-Type", "doc");
            response.setContentType("application/octet-stream");
            response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(fileName, "UTF-8"));
            response.setCharacterEncoding("UTF-8");

            out = new ByteArrayOutputStream();
            OutputStream os = response.getOutputStream();
            workbook.write(os);
            os.flush();
            os.close();
        } catch (Exception e) {
            log.error("合同台账导出异常", e);
        } finally {
            if (out != null) {
                try {
                    out.close();
                } catch (IOException e) {
                    log.error("关闭输出流异常", e);
                }
            }
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    log.error("关闭输入流异常", e);
                }
            }
        }
    }

    /**
     * 设置风险清单台账单元格数据
     */
    private void setRiskCellsByContract(XSSFRow row, BmContract contract, int num, XSSFCellStyle style, XSSFCellStyle dateStyle, XSSFCellStyle moneyStyle) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

        for (int i = 0; i < 14; i++) {
            XSSFCell cell = row.createCell(i);

            // 金额和日期字段样式
            if (i == 6) { // 合同金额
                cell.setCellStyle(moneyStyle);
            } else if (i == 10 || i == 12) { // 经办时间、承办时间
                cell.setCellStyle(dateStyle);
            } else {
                cell.setCellStyle(style);
            }

            switch (i) {
                case 0: // 序号
                    cell.setCellValue(num);
                    break;
                case 1: // 合同编码
                    cell.setCellValue(contract.getContractCode() != null ? contract.getContractCode() : "");
                    break;
                case 2: // 版本号
                    cell.setCellValue(contract.getVersion() != null ? contract.getVersion() : "");
                    break;
                case 3: // 合同名称
                    cell.setCellValue(contract.getContractName() != null ? contract.getContractName() : "");
                    break;
                case 4: // 合同性质
                    cell.setCellValue(contract.getDataTypeName() != null ? contract.getDataTypeName() : "");
                    break;
                case 5: // 合同类型
                    cell.setCellValue(contract.getContractType() != null ? contract.getContractType() : "");
                    break;
                case 6: // 合同金额
                    if (contract.getContractMoney() != null) {
                        cell.setCellValue(contract.getContractMoney().doubleValue());
                    }
                    break;
                case 7: // 我方签约
                    cell.setCellValue(contract.getOurPartyName() != null ? contract.getOurPartyName() : "");
                    break;
                case 8: // 对方签约
                    cell.setCellValue(contract.getOtherPartyName() != null ? contract.getOtherPartyName() : "");
                    break;
                case 9: // 经办人
                    cell.setCellValue(contract.getCreatePsnName() != null ? contract.getCreatePsnName() : "");
                    break;
                case 10: // 经办时间
                    if (contract.getCreateTime() != null) {
                        cell.setCellValue(sdf.format(contract.getCreateTime()));
                    }
                    break;
                case 11: // 承办人
                    cell.setCellValue(contract.getRiskPsn() != null ? contract.getRiskPsn() : "");
                    break;
                case 12: // 承办时间
                    if (contract.getUndertakingTime() != null) {
                        cell.setCellValue(sdf.format(contract.getUndertakingTime()));
                    }
                    break;
                case 13: // 考核分数
                    if (contract.getAssessmentScore() != null) {
                        cell.setCellValue(contract.getAssessmentScore().doubleValue());
                    }
                    break;
                default:
                    break;
            }
        }
    }

    @Override
    public int countUnpublishedPerformPlans(String userId) {
        // 第一步：查询用户经办的所有已生效合同的唯一合同编码
        QueryWrapper<BmContract> codeQuery = new QueryWrapper<>();
        codeQuery.select("DISTINCT contract_code")
                .eq("create_psn_id", userId)
                .eq("take_effect_code", 3)
                .eq("take_effect_name", "已生效")
                .gt("contract_take_effect_date", LocalDateTime.of(2025, 7, 31, 23, 59, 59));

        List<Object> contractCodes = this.listObjs(codeQuery);

        if (contractCodes.isEmpty()) {
            return 0;
        }

        // 计数器
        int unpublishedCount = 0;

        // 对每个合同编码查询最新版本的履行计划状态
        for (Object codeObj : contractCodes) {
            String contractCode = codeObj.toString();

            // 查询该合同编码下最大版本的记录
            QueryWrapper<BmContract> versionQuery = new QueryWrapper<>();
            versionQuery.eq("contract_code", contractCode)
                    .orderByDesc("version")
                    .last("LIMIT 1");

            BmContract latestContract = this.getOne(versionQuery);

            // 如果最新版本的履行计划状态不是已计划(5)，则计数+1
            if (latestContract != null &&
                    (latestContract.getPerformanceStateCode() == null ||
                            latestContract.getPerformanceStateCode() != 5)) {
                unpublishedCount++;
            }
        }

        return unpublishedCount;
    }

    @Override
    @Transactional
    public void cancelSubmit(String id) {
        BmContract contract = getById(id);
        if (contract == null) {
            throw new RuntimeException("合同不存在");
        }

        // 检查当前状态是否是审批完成
        if (!DataStateBPM.FINISH.getKey().equals(contract.getDataStateCode())) {
            throw new RuntimeException("只有审批完成的合同可以取消提交");
        }

        // 更新状态为已保存
        contract.setDataStateCode(DataStateBPM.SAVE.getKey());
        contract.setDataState(DataStateBPM.SAVE.getValue());

        // 使用现有的modify方法更新
        modify(contract);

    }
}

