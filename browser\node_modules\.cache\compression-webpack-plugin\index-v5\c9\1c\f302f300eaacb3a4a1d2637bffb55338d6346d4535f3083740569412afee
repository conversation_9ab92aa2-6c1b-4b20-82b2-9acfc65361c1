
e9cf202d40969f94b1c527692378556536bed8b9	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.442.1754018536329.js\",\"contentHash\":\"d5204d479eb4c9b8d75cdd31f7c6c4d8\"}","integrity":"sha512-TMuXF2TTACfxE0HBoTqxsuyRGbfAnbWqP21Yj55CPYhfqvyyyuoCqT6Ru49dfzosTI0/xj9JubI8aVIGd+fIVw==","time":1754018575957,"size":45472}