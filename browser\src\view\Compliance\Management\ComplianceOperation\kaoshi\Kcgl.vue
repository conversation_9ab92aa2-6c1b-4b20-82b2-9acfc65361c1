<template>
  <el-container direction="vertical" class="container-manage-sg">
    <el-header>
      <el-card>
        <el-input
          v-model="temp.fuzzyValue"
          class="filter_input"
          placeholder="检索条件（商品ID、商品名称、库存状态）"
          clearable
          @keyup.enter.native="refreshData"
          @clear="refreshData"
        >
          <el-popover slot="prepend" placement="bottom-start" width="800" trigger="click">
            <el-form ref="queryForm" label-width="100px" size="mini">
              <el-row>
                <el-col :span="12">
                  <el-form-item label="商品ID">
                    <el-input v-model="temp.productId" placeholder="请输入商品ID..." style="width:100%" clearable/>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="商品名称">
                    <el-input v-model="temp.productName" placeholder="请输入商品名称..." style="width:100%" clearable/>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="12">
                  <el-form-item label="单位">
                    <el-input v-model="temp.unit" placeholder="请输入单位..." style="width:100%" clearable/>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="库存状态">
                    <el-select v-model="temp.stockStatus" placeholder="请选择库存状态..." style="width:100%" clearable>
                      <el-option label="正常" value="NORMAL"></el-option>
                      <el-option label="预警" value="WARNING"></el-option>
                      <el-option label="缺货" value="OUT_OF_STOCK"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="12">
                  <el-form-item label="数量范围">
                    <el-input-number
                      v-model="temp.minQuantity"
                      placeholder="最小数量"
                      style="width: 48%"
                      :min="0"
                      :precision="2"
                      clearable>
                    </el-input-number>
                    <span style="margin: 0 8px;">-</span>
                    <el-input-number
                      v-model="temp.maxQuantity"
                      placeholder="最大数量"
                      style="width: 48%"
                      :min="0"
                      :precision="2"
                      clearable>
                    </el-input-number>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-button-group style="float: right">
                <el-button type="primary" size="mini" icon="el-icon-search" @click="search">搜索</el-button>
                <el-button type="primary" size="mini" icon="el-icon-refresh-left" @click="resetSearch">重置</el-button>
              </el-button-group>
            </el-form>
            <el-button slot="reference" size="mini" type="primary">高级检索</el-button>
          </el-popover>
          <el-button slot="append" icon="el-icon-search" @click="search"/>
        </el-input>
      </el-card>
    </el-header>
    
    <el-main>
      <div style="width:100%">
        <div class="table-header">
          <span class="table-title">库存管理列表</span>
          <div class="table-tools">
            <el-button type="primary" size="mini" icon="el-icon-plus" @click="showAddDialog">新增库存</el-button>
            <el-button type="warning" size="mini" icon="el-icon-warning" @click="checkWarning">库存预警</el-button>
            <el-button type="info" size="mini" icon="el-icon-edit" @click="showBatchUpdateDialog" :disabled="selectedRows.length === 0">批量调整</el-button>
            <el-button size="mini" icon="el-icon-download" @click="exportData">导出</el-button>
            <el-button size="mini" icon="el-icon-refresh" @click="refreshData">刷新</el-button>
          </div>
        </div>
        
        <el-table
          ref="table"
          :data="tableData"
          border
          :show-overflow-tooltip="true"
          style="width: 100%; margin-top: 10px;"
          :height="tableHeight"
          stripe
          fit
          highlight-current-row
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" align="center"/>
          <el-table-column label="序号" type="index" align="center" width="60"/>
          <el-table-column label="商品ID" prop="productId" align="center" width="120" show-overflow-tooltip/>
          <el-table-column label="商品名称" prop="productName" align="center" min-width="150" show-overflow-tooltip/>
          <el-table-column label="当前库存" prop="quantity" align="center" width="120">
            <template slot-scope="scope">
              <span :class="getQuantityClass(scope.row)">
                {{ scope.row.quantity }} {{ scope.row.unit }}
              </span>
            </template>
          </el-table-column>
          <el-table-column label="预警阈值" prop="warningThreshold" align="center" width="120">
            <template slot-scope="scope">
              {{ scope.row.warningThreshold }} {{ scope.row.unit }}
            </template>
          </el-table-column>
          <el-table-column label="库存状态" align="center" width="100">
            <template slot-scope="scope">
              <el-tag :type="getStockStatusType(scope.row)">
                {{ getStockStatusText(scope.row) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="更新时间" prop="updateTime" align="center" width="150">
            <template slot-scope="scope">
              {{ formatDate(scope.row.updateTime) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="250" fixed="right">
            <template slot-scope="scope">
              <el-button type="text" size="mini" @click="viewDetail(scope.row)" icon="el-icon-view">查看</el-button>
              <el-button type="text" size="mini" @click="editInventory(scope.row)" icon="el-icon-edit">编辑</el-button>
              <el-dropdown @command="handleStockOperation" style="margin-left: 8px;">
                <el-button type="text" size="mini">
                  库存调整<i class="el-icon-arrow-down el-icon--right"></i>
                </el-button>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item :command="{action: 'add', row: scope.row}" icon="el-icon-plus">增加库存</el-dropdown-item>
                  <el-dropdown-item :command="{action: 'reduce', row: scope.row}" icon="el-icon-minus">减少库存</el-dropdown-item>
                  <el-dropdown-item :command="{action: 'adjust', row: scope.row}" icon="el-icon-edit-outline">库存调整</el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </template>
          </el-table-column>
        </el-table>
        
        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="page.current"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="page.size"
            layout="total, sizes, prev, pager, next, jumper"
            :total="page.total">
          </el-pagination>
        </div>
    </div>
    </el-main>

    <!-- 新增/编辑库存对话框 -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="600px" @close="closeDialog">
      <el-form ref="inventoryForm" :model="inventoryForm" :rules="inventoryRules" label-width="120px">
        <el-form-item label="商品ID" prop="productId">
          <el-input v-model="inventoryForm.productId" placeholder="请输入商品ID" :disabled="isEdit"/>
        </el-form-item>
        <el-form-item label="商品名称" prop="productName">
          <el-input v-model="inventoryForm.productName" placeholder="请输入商品名称"/>
        </el-form-item>
        <el-form-item label="当前库存" prop="quantity">
          <el-input-number
            v-model="inventoryForm.quantity"
            :min="0"
            :step="0.01"
            :precision="2"
            style="width: 100%"
            placeholder="请输入库存数量">
          </el-input-number>
        </el-form-item>
        <el-form-item label="单位" prop="unit">
          <el-select v-model="inventoryForm.unit" placeholder="请选择单位" style="width: 100%">
            <el-option label="吨" value="吨"></el-option>
            <el-option label="千克" value="千克"></el-option>
            <el-option label="个" value="个"></el-option>
            <el-option label="件" value="件"></el-option>
            <el-option label="米" value="米"></el-option>
            <el-option label="升" value="升"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="预警阈值" prop="warningThreshold">
          <el-input-number
            v-model="inventoryForm.warningThreshold"
            :min="0"
            :step="0.01"
            :precision="2"
            style="width: 100%"
            placeholder="请输入预警阈值">
          </el-input-number>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitInventory" :loading="submitting">确定</el-button>
      </div>
    </el-dialog>

    <!-- 库存调整对话框 -->
    <el-dialog :title="adjustTitle" :visible.sync="adjustVisible" width="500px">
      <el-form ref="adjustForm" :model="adjustForm" :rules="adjustRules" label-width="120px">
        <el-form-item label="商品信息">
          <span>{{ adjustForm.productName }} ({{ adjustForm.productId }})</span>
        </el-form-item>
        <el-form-item label="当前库存">
          <span style="color: #409EFF; font-weight: bold;">{{ adjustForm.currentQuantity }} {{ adjustForm.unit }}</span>
        </el-form-item>
        <el-form-item label="调整类型">
          <el-radio-group v-model="adjustForm.operation">
            <el-radio label="ADD">增加库存</el-radio>
            <el-radio label="REDUCE">减少库存</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="调整数量" prop="adjustQuantity">
          <el-input-number
            v-model="adjustForm.adjustQuantity"
            :min="0.01"
            :step="0.01"
            :precision="2"
            style="width: 100%"
            placeholder="请输入调整数量">
          </el-input-number>
        </el-form-item>
        <el-form-item label="调整后库存" v-if="adjustForm.adjustQuantity">
          <span :style="getAdjustResultStyle()">
            {{ getAdjustResult() }} {{ adjustForm.unit }}
          </span>
        </el-form-item>
        <el-form-item label="调整原因">
          <el-input
            v-model="adjustForm.reason"
            type="textarea"
            :rows="3"
            placeholder="请输入调整原因">
          </el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="adjustVisible = false">取消</el-button>
        <el-button type="primary" @click="submitAdjust" :loading="adjusting">确定</el-button>
      </div>
    </el-dialog>

    <!-- 批量调整对话框 -->
    <el-dialog title="批量库存调整" :visible.sync="batchAdjustVisible" width="600px">
      <el-form ref="batchAdjustForm" :model="batchAdjustForm" label-width="120px">
        <el-form-item label="选中商品">
          <span>已选择 {{ selectedRows.length }} 个商品</span>
        </el-form-item>
        <el-form-item label="调整类型">
          <el-radio-group v-model="batchAdjustForm.operation">
            <el-radio label="ADD">批量增加</el-radio>
            <el-radio label="REDUCE">批量减少</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="调整数量" prop="adjustQuantity">
          <el-input-number
            v-model="batchAdjustForm.adjustQuantity"
            :min="0.01"
            :step="0.01"
            :precision="2"
            style="width: 100%"
            placeholder="请输入调整数量">
          </el-input-number>
        </el-form-item>
        <el-form-item label="调整原因">
          <el-input
            v-model="batchAdjustForm.reason"
            type="textarea"
            :rows="3"
            placeholder="请输入批量调整原因">
          </el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="batchAdjustVisible = false">取消</el-button>
        <el-button type="primary" @click="submitBatchAdjust" :loading="batchAdjusting">确定</el-button>
      </div>
    </el-dialog>

    <!-- 库存详情对话框 -->
    <el-dialog title="库存详情" :visible.sync="detailVisible" width="600px">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="商品ID">{{ inventoryDetail.productId }}</el-descriptions-item>
        <el-descriptions-item label="商品名称">{{ inventoryDetail.productName }}</el-descriptions-item>
        <el-descriptions-item label="当前库存">
          <span :class="getQuantityClass(inventoryDetail)">
            {{ inventoryDetail.quantity }} {{ inventoryDetail.unit }}
          </span>
        </el-descriptions-item>
        <el-descriptions-item label="预警阈值">{{ inventoryDetail.warningThreshold }} {{ inventoryDetail.unit }}</el-descriptions-item>
        <el-descriptions-item label="库存状态">
          <el-tag :type="getStockStatusType(inventoryDetail)">
            {{ getStockStatusText(inventoryDetail) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ formatDate(inventoryDetail.createTime) }}</el-descriptions-item>
        <el-descriptions-item label="创建人">{{ inventoryDetail.createPsnName }}</el-descriptions-item>
        <el-descriptions-item label="更新时间">{{ formatDate(inventoryDetail.updateTime) }}</el-descriptions-item>
      </el-descriptions>
    </el-dialog>

    <!-- 预警库存对话框 -->
    <el-dialog title="库存预警" :visible.sync="warningVisible" width="800px">
      <el-alert
        title="以下商品库存低于预警阈值，请及时补货！"
        type="warning"
        :closable="false"
        style="margin-bottom: 20px;">
      </el-alert>
      <el-table :data="warningData" border stripe>
        <el-table-column label="序号" type="index" align="center" width="60"/>
        <el-table-column label="商品ID" prop="productId" align="center" width="120"/>
        <el-table-column label="商品名称" prop="productName" align="center" min-width="150"/>
        <el-table-column label="当前库存" prop="quantity" align="center" width="120">
          <template slot-scope="scope">
            <span style="color: #f56c6c; font-weight: bold;">
              {{ scope.row.quantity }} {{ scope.row.unit }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="预警阈值" prop="warningThreshold" align="center" width="120">
          <template slot-scope="scope">
            {{ scope.row.warningThreshold }} {{ scope.row.unit }}
          </template>
        </el-table-column>
        <el-table-column label="缺货量" align="center" width="120">
          <template slot-scope="scope">
            <span style="color: #f56c6c;">
              {{ (scope.row.warningThreshold - scope.row.quantity).toFixed(2) }} {{ scope.row.unit }}
            </span>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
  </el-container>
</template>

<script>
import InventoryAPI from '@/api/Inventory/Inventory'

export default {
  name: 'InventoryManagement',
  data() {
    return {
      loading: false,
      tableData: [],
      selectedRows: [],
      tableHeight: window.innerHeight - 300,
      
      // 搜索条件
      temp: {
        fuzzyValue: '',
        productId: '',
        productName: '',
        unit: '',
        stockStatus: '',
        minQuantity: null,
        maxQuantity: null
      },
      
      // 分页
      page: {
        current: 1,
        size: 20,
        total: 0
      },
      
      // 对话框
      dialogVisible: false,
      adjustVisible: false,
      batchAdjustVisible: false,
      detailVisible: false,
      warningVisible: false,
      dialogTitle: '新增库存',
      submitting: false,
      adjusting: false,
      batchAdjusting: false,
      isEdit: false,
      
      // 表单
      inventoryForm: {
        id: '',
        productId: '',
        productName: '',
        quantity: 0,
        unit: '',
        warningThreshold: 0
      },
      inventoryRules: {
        productId: [
          { required: true, message: '请输入商品ID', trigger: 'blur' }
        ],
        productName: [
          { required: true, message: '请输入商品名称', trigger: 'blur' }
        ],
        quantity: [
          { required: true, message: '请输入库存数量', trigger: 'blur' }
        ],
        unit: [
          { required: true, message: '请选择单位', trigger: 'change' }
        ],
        warningThreshold: [
          { required: true, message: '请输入预警阈值', trigger: 'blur' }
        ]
      },
      
      // 调整表单
      adjustForm: {
        productId: '',
        productName: '',
        currentQuantity: 0,
        unit: '',
        operation: 'ADD',
        adjustQuantity: 1,
        reason: ''
      },
      adjustTitle: '库存调整',
      adjustRules: {
        adjustQuantity: [
          { required: true, message: '请输入调整数量', trigger: 'blur' },
          { type: 'number', min: 0.01, message: '数量必须大于0', trigger: 'blur' }
        ]
      },
      
      // 批量调整表单
      batchAdjustForm: {
        operation: 'ADD',
        adjustQuantity: 1,
        reason: ''
      },
      
      // 其他数据
      inventoryDetail: {},
      warningData: []
    }
  },
  
  computed: {
    
  },
  
  created() {
    this.loadData()
  },
  
  mounted() {
    window.addEventListener('resize', this.handleResize)
  },
  
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize)
  },
  
  methods: {
    // 加载数据
    async loadData() {
      this.loading = true
      try {
        const params = {
          current: this.page.current,
          size: this.page.size,
          ...this.getSearchParams()
        }
        
        // 根据搜索条件调用不同的API
        let response
        if (this.temp.stockStatus === 'WARNING') {
          // 查询预警库存
          response = await InventoryAPI.checkWarningInventory()
        } else if (this.temp.productName && this.temp.productName.trim()) {
          // 有商品名称搜索条件时，使用模糊查询
          response = await InventoryAPI.getInventoryByProductName(this.temp.productName.trim())
        } else if (this.temp.fuzzyValue && this.temp.fuzzyValue.trim()) {
          // 有模糊搜索值时，按商品名称模糊查询
          response = await InventoryAPI.getInventoryByProductName(this.temp.fuzzyValue.trim())
        } else {
          // 没有搜索条件时，获取所有库存
          response = await InventoryAPI.getAllInventory()
        }
        
        if (response.data.success) {
          let data = response.data.data || []
          
          // 客户端过滤（实际项目中应该在后端实现）
          if (this.temp.productId) {
            data = data.filter(item => item.productId.toLowerCase().includes(this.temp.productId.toLowerCase()))
          }
          if (this.temp.unit) {
            data = data.filter(item => item.unit && item.unit.toLowerCase().includes(this.temp.unit.toLowerCase()))
          }
          if (this.temp.stockStatus === 'NORMAL') {
            data = data.filter(item => !this.isBelowWarning(item) && item.quantity > 0)
          } else if (this.temp.stockStatus === 'OUT_OF_STOCK') {
            data = data.filter(item => item.quantity <= 0)
          }
          if (this.temp.minQuantity !== null && this.temp.minQuantity > 0) {
            data = data.filter(item => item.quantity >= this.temp.minQuantity)
          }
          if (this.temp.maxQuantity !== null && this.temp.maxQuantity > 0) {
            data = data.filter(item => item.quantity <= this.temp.maxQuantity)
          }
          
          this.tableData = data
          this.page.total = data.length
        } else {
          this.$message.error(response.data.message || '加载数据失败')
        }
      } catch (error) {
        console.error('加载数据失败:', error)
        this.$message.error('加载数据失败')
      } finally {
        this.loading = false
      }
    },
    
    // 获取搜索参数
    getSearchParams() {
      const params = {}
      if (this.temp.fuzzyValue) params.fuzzyValue = this.temp.fuzzyValue
      return params
    },
    
    // 搜索
    search() {
      this.page.current = 1
      this.loadData()
    },
    
    // 重置搜索
    resetSearch() {
      this.temp = {
        fuzzyValue: '',
        productId: '',
        productName: '',
        unit: '',
        stockStatus: '',
        minQuantity: null,
        maxQuantity: null
      }
      this.search()
    },
    
    // 刷新数据
    refreshData() {
      this.loadData()
    },
    
    // 分页
    handleSizeChange(val) {
      this.page.size = val
      this.page.current = 1
      this.loadData()
    },
    
    handleCurrentChange(val) {
      this.page.current = val
      this.loadData()
    },
    
    // 表格选择
    handleSelectionChange(selection) {
      this.selectedRows = selection
    },
    
    // 显示新增对话框
    showAddDialog() {
      this.dialogTitle = '新增库存'
      this.isEdit = false
      this.inventoryForm = {
        id: '',
        productId: '',
        productName: '',
        quantity: 0,
        unit: '',
        warningThreshold: 0
      }
      this.dialogVisible = true
    },
    
    // 编辑库存
    editInventory(row) {
      this.dialogTitle = '编辑库存'
      this.isEdit = true
      // 只复制需要的字段，避免复制计算字段
      this.inventoryForm = {
        id: row.id,
        productId: row.productId,
        productName: row.productName,
        quantity: row.quantity,
        unit: row.unit,
        warningThreshold: row.warningThreshold
      }
      this.dialogVisible = true
    },
    
    // 提交库存
    async submitInventory() {
      this.$refs.inventoryForm.validate(async (valid) => {
        if (valid) {
          this.submitting = true
          try {
            let response
            if (this.isEdit) {
              // 编辑模式，调用更新接口
              response = await InventoryAPI.updateInventoryInfo(this.inventoryForm)
            } else {
              // 新增模式，调用添加接口
              response = await InventoryAPI.addInventory(this.inventoryForm)
            }
            
            if (response.data.success) {
              this.$message.success(response.data.message || '操作成功')
              this.dialogVisible = false
              this.loadData()
            } else {
              this.$message.error(response.data.message || '操作失败')
            }
          } catch (error) {
            console.error('操作失败:', error)
            this.$message.error('操作失败')
          } finally {
            this.submitting = false
          }
        }
      })
    },
    
    // 库存操作
    handleStockOperation(command) {
      const { action, row } = command
      this.adjustForm = {
        productId: row.productId,
        productName: row.productName,
        currentQuantity: row.quantity,
        unit: row.unit,
        operation: action === 'add' ? 'ADD' : 'REDUCE',
        adjustQuantity: 1,
        reason: ''
      }
      this.adjustTitle = action === 'add' ? '增加库存' : action === 'reduce' ? '减少库存' : '库存调整'
      this.adjustVisible = true
    },
    
    // 提交调整
    async submitAdjust() {
      this.$refs.adjustForm.validate(async (valid) => {
        if (valid) {
          this.adjusting = true
          try {
            const response = await InventoryAPI.updateInventory({
              productId: this.adjustForm.productId,
              quantity: this.adjustForm.adjustQuantity,
              operation: this.adjustForm.operation
            })
            
            if (response.data.success) {
              this.$message.success(response.data.message || '调整成功')
              this.adjustVisible = false
              this.loadData()
            } else {
              this.$message.error(response.data.message || '调整失败')
            }
          } catch (error) {
            console.error('调整失败:', error)
            this.$message.error('调整失败')
          } finally {
            this.adjusting = false
          }
        }
      })
    },
    
    // 显示批量调整对话框
    showBatchUpdateDialog() {
      if (this.selectedRows.length === 0) {
        this.$message.warning('请先选择要调整的商品')
        return
      }
      this.batchAdjustForm = {
        operation: 'ADD',
        adjustQuantity: 1,
        reason: ''
      }
      this.batchAdjustVisible = true
    },
    
    // 提交批量调整
    async submitBatchAdjust() {
      if (!this.batchAdjustForm.adjustQuantity || this.batchAdjustForm.adjustQuantity <= 0) {
        this.$message.error('请输入有效的调整数量')
        return
      }
      
      this.batchAdjusting = true
      try {
        const promises = this.selectedRows.map(row => 
          InventoryAPI.updateInventory({
            productId: row.productId,
            quantity: this.batchAdjustForm.adjustQuantity,
            operation: this.batchAdjustForm.operation
          })
        )
        
        await Promise.all(promises)
        this.$message.success('批量调整成功')
        this.batchAdjustVisible = false
        this.loadData()
      } catch (error) {
        console.error('批量调整失败:', error)
        this.$message.error('批量调整失败')
      } finally {
        this.batchAdjusting = false
      }
    },
    
    // 查看预警
    async checkWarning() {
      try {
        const response = await InventoryAPI.checkWarningInventory()
        if (response.data.success) {
          this.warningData = response.data.data || []
          if (this.warningData.length === 0) {
            this.$message.success('当前没有预警库存')
          } else {
            this.warningVisible = true
          }
        } else {
          this.$message.error(response.data.message || '查询预警库存失败')
        }
      } catch (error) {
        console.error('查询预警库存失败:', error)
        this.$message.error('查询预警库存失败')
      }
    },
    
    // 查看详情
    viewDetail(row) {
      this.inventoryDetail = { ...row }
      this.detailVisible = true
    },
    
    // 导出数据
    exportData() {
      this.$message.info('导出功能开发中...')
    },
    
    // 关闭对话框
    closeDialog() {
      this.$refs.inventoryForm.resetFields()
    },
    
    // 判断是否低于预警阈值
    isBelowWarning(row) {
      return row.quantity < row.warningThreshold
    },
    
    // 获取库存数量样式类
    getQuantityClass(row) {
      if (row.quantity <= 0) {
        return 'quantity-out-of-stock'
      } else if (this.isBelowWarning(row)) {
        return 'quantity-warning'
      }
      return 'quantity-normal'
    },
    
    // 获取库存状态类型
    getStockStatusType(row) {
      if (row.quantity <= 0) {
        return 'danger'
      } else if (this.isBelowWarning(row)) {
        return 'warning'
      }
      return 'success'
    },
    
    // 获取库存状态文本
    getStockStatusText(row) {
      if (row.quantity <= 0) {
        return '缺货'
      } else if (this.isBelowWarning(row)) {
        return '预警'
      }
      return '正常'
    },
    
    // 获取调整结果
    getAdjustResult() {
      if (!this.adjustForm.adjustQuantity) return this.adjustForm.currentQuantity
      
      if (this.adjustForm.operation === 'ADD') {
        return (this.adjustForm.currentQuantity + this.adjustForm.adjustQuantity).toFixed(2)
      } else {
        return (this.adjustForm.currentQuantity - this.adjustForm.adjustQuantity).toFixed(2)
      }
    },
    
    // 获取调整结果样式
    getAdjustResultStyle() {
      const result = this.getAdjustResult()
      if (result < 0) {
        return 'color: #f56c6c; font-weight: bold;'
      } else if (result < this.adjustForm.warningThreshold) {
        return 'color: #e6a23c; font-weight: bold;'
      }
      return 'color: #67c23a; font-weight: bold;'
    },
    
    // 格式化日期
    formatDate(date) {
      if (!date) return ''
      return new Date(date).toLocaleString('zh-CN')
    },
    
    // 窗口大小变化
    handleResize() {
      this.tableHeight = window.innerHeight - 300
    }
  }
}
</script>

<style scoped>
.container-manage-sg {
  height: 100vh;
  background: #f5f5f5;
}

.filter_input {
  width: 100%;
}

.table-header {
    display: flex;
  justify-content: space-between;
    align-items: center;
  margin-bottom: 10px;
}

.table-title {
    font-size: 16px;
  font-weight: bold;
  color: #303133;
}

.table-tools {
  display: flex;
  gap: 8px;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.el-header {
  padding: 20px;
  background: transparent;
}

.el-main {
  padding: 0 20px 20px;
}

.el-card {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.dialog-footer {
  text-align: right;
}

.el-table {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

/* 库存数量样式 */
.quantity-normal {
  color: #67c23a;
  font-weight: bold;
}

.quantity-warning {
  color: #e6a23c;
  font-weight: bold;
}

.quantity-out-of-stock {
  color: #f56c6c;
  font-weight: bold;
}

/* 覆盖element-ui的一些样式 */
.el-button--text {
  color: #409EFF;
}

.el-button--text:hover {
  color: #66b1ff;
}

.el-tag {
  border: none;
}

.el-dropdown {
  vertical-align: middle;
}
</style>