<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.klaw.dao.complianceTrainingDao.ComplianceTrainingReviewsMapper">

    <resultMap id="BaseResultMap" type="com.klaw.entity.complianceTrainingBean.ComplianceTrainingReviews">
            <id property="id" column="id" jdbcType="VARCHAR"/>
            <result property="complianceTrainingLearningTableId" column="compliance_training_learning_table_id" jdbcType="VARCHAR"/>
            <result property="createOgnId" column="create_ogn_id" jdbcType="VARCHAR"/>
            <result property="createOgnName" column="create_ogn_name" jdbcType="VARCHAR"/>
            <result property="createDeptId" column="create_dept_id" jdbcType="VARCHAR"/>
            <result property="createDeptName" column="create_dept_name" jdbcType="VARCHAR"/>
            <result property="createGroupId" column="create_group_id" jdbcType="VARCHAR"/>
            <result property="createGroupName" column="create_group_name" jdbcType="VARCHAR"/>
            <result property="createPsnId" column="create_psn_id" jdbcType="VARCHAR"/>
            <result property="createPsnName" column="create_psn_name" jdbcType="VARCHAR"/>
            <result property="createPsnAvatar" column="create_psn_avatar" jdbcType="VARCHAR"/>
            <result property="createOrgId" column="create_org_id" jdbcType="VARCHAR"/>
            <result property="createOrgName" column="create_org_name" jdbcType="VARCHAR"/>
            <result property="createPsnFullId" column="create_psn_full_id" jdbcType="VARCHAR"/>
            <result property="createPsnFullName" column="create_psn_full_name" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="dataState" column="data_state" jdbcType="VARCHAR"/>
            <result property="dataStateCode" column="data_state_code" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,compliance_training_learning_table_id,create_ogn_id,
        create_ogn_name,create_dept_id,create_dept_name,
        create_group_id,create_group_name,create_psn_id,
        create_psn_name,create_psn_avatar,create_org_id,
        create_org_name,create_psn_full_id,create_psn_full_name,
        create_time,update_time,data_state,
        data_state_code
    </sql>
</mapper>
