import {request} from '@/api/index'

export default {
    query(data) {
        return request({
            url: '/LawFirmBlackList/query',
            method: 'post',
            data
        })
    },
    save(data) {
        return request({
            url: '/LawFirmBlackList/save',
            method: 'post',
            data

        })
    },
    queryDataById(data) {
        return request({
            url: '/LawFirmBlackList/queryDataById',
            method: 'post',
            data
        })
    },
    delete(data) {
        return request({
            url: '/LawFirmBlackList/delete',
            method: 'post',
            data
        })
    },
    distinct(data) {
        return request({
            url: '/LawFirmBlackList/distinct',
            method: 'post',
            data
        })
    },
    setParam(data) {
        return request({
            url: '/LawFirmBlackList/setParam',
            method: 'post',
            data
        })
    }
}
