<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.klaw.dao.lawyerDao.LawFirmSelectCaseMapper">
    <resultMap id="BaseResultMap" type="com.klaw.entity.lawyerBean.LawFirmSelectCase">
        <id column="ID" jdbcType="VARCHAR" property="id"/>
        <result column="PARENT_ID" jdbcType="VARCHAR" property="parentId"/>
        <result column="RELATION_CASE_NAME" jdbcType="VARCHAR" property="relationCaseName"/>
        <result column="RELATION_CASE_ID" jdbcType="VARCHAR" property="relationCaseId"/>
        <result column="AGENT_STAGE_NAME" jdbcType="VARCHAR" property="agentStageName"/>
        <result column="AGENT_STAGE_ID" jdbcType="VARCHAR" property="agentStageId"/>
    </resultMap>
    <sql id="Base_Column_List">
        ID, PARENT_ID, RELATION_CASE_NAME, RELATION_CASE_ID, AGENT_STAGE_NAME, AGENT_STAGE_ID
    </sql>
    <select id="queryFinishData" resultMap="BaseResultMap">
        select
            d.ID, d.PARENT_ID, d.RELATION_CASE_NAME, d.RELATION_CASE_ID, d.AGENT_STAGE_NAME, d.AGENT_STAGE_ID
        from SG_LAW_FIRM_SELECTION m join SG_LAW_FIRM_SELECT_CASE d on m.id=d.PARENT_ID
        where m.DATA_STATE_CODE=5 and d.RELATION_CASE_ID=#{id}
    </select>
</mapper>