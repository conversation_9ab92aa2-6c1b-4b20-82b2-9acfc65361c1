<template>
    <div>
        <div v-if="!isView">
            <!--风险上报-->
            <div style="margin: 10px">
                <span style="font-weight: bold;font-size: 16px;color: #5A5A5F;">基本信息</span>
            </div>
            <Baseinfo :businessDomainData="businessDomainData" :eventNatureData="eventNatureData" :riskClassificationData="riskClassificationData"
                      :expectedRiskLevelData="expectedRiskLevelData" :mainData="mainData"></Baseinfo>

            <div style="margin: 10px">
                <span style="font-weight: bold;font-size: 16px;color: #5A5A5F;">应对过程</span>
            </div>
            <el-divider></el-divider>

            <div style="margin: 10px">
                <el-row>
                    <el-col :span="8">
                        <el-form-item label="应对过程状态" >
                            <el-select v-model="mainData.riskMitigationStatus" placeholder="请选择..." disabled>
                                <el-option label="未应对" value="2"></el-option>
                                <el-option label="应对中" value="3"> </el-option>
                                <el-option label="已完成" value="4"> </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="16">
                        <el-form-item label="应对过程相关附件" label-width="130px" >
                            <uploadDoc
                                    disabled
                                    :doc-path="docURL"
                                    :files.sync="mainData.respondProcessFiles"
                                    v-model="mainData.respondProcessFiles"
                            />
                        </el-form-item>
                    </el-col>

                </el-row>
                <el-divider></el-divider>
            </div>


            <el-table
                    ref="table"
                    :data="mitigationList"
                    border
                    style="table-layout: fixed;width: 100%;overflow:auto"
                    stripe
                    fit
                    highlight-current-row
                    :show-overflow-tooltip="true"
                    @row-click="selectRow"
            >


                <el-table-column type="index" label="序号" align="center"></el-table-column>
                <el-table-column prop="mitigationMeasures" label="应对措施" min-width="300"  show-overflow-tooltip >
                </el-table-column>
                <el-table-column prop="mitigationResults" label="应对结果" min-width="200" show-overflow-tooltip >
                </el-table-column>
                <el-table-column prop="executionTime" label="执行时间" min-width="150" show-overflow-tooltip ></el-table-column>
                <el-table-column prop="executionDepartment" label="执行部门" min-width="200" show-overflow-tooltip ></el-table-column>
                <el-table-column prop="executionPerson" label="执行人" min-width="150" show-overflow-tooltip ></el-table-column>
            </el-table>
            <div style="margin: 10px">
                <span style="font-weight: bold;font-size: 16px;color: #5A5A5F;">事件总结</span>
            </div>
            <el-divider></el-divider>
            <el-form-item label="事件总结" prop="mainData.summaryEvent">
                <uploadDoc
                        :disabled="isView"
                        :doc-path="docURL"
                        :files.sync="mainData.summaryEvent"
                        v-model="mainData.summaryEvent"
                />
            </el-form-item>
            <el-form-item label="改进措施" prop="mainData.improvementMeasures">
                <textarea cols="5" style="width:90%" v-model="mainData.improvementMeasures"></textarea>
            </el-form-item>
            <el-form-item label="结果凭证" prop="mainData.resultCredential">
                <uploadDoc
                        :disabled="isView"
                        :doc-path="docURL"
                        :files.sync="mainData.resultCredential"
                        v-model="mainData.resultCredential"
                />
            </el-form-item>
            <el-form-item label="关联资料" prop="mainData.summaryFile">
                <uploadDoc
                        :disabled="isView"
                        :doc-path="docURL"
                        :files.sync="mainData.summaryFile"
                        v-model="mainData.summaryFile"
                />
            </el-form-item>
        </div>
        <div v-if="isView">
            <SimpleBoard style="margin-top: 10px;" title="风险总结">
                <!--<el-form-item label="是否需要集团确认"  class="custom-word-break" prop="isGroupWhere" style="margin-top:5%">
                    <el-radio v-model="mainData.isGroupWhere" label="1" disabled>是</el-radio>
                    <el-radio v-model="mainData.isGroupWhere" label="2" disabled>否</el-radio>
                </el-form-item>
                <el-form-item label="事件总结" prop="resultCredential">
                    <textarea v-model="mainData.resultCredential" style="width:90%" cols="5" disabled readonly></textarea>
                </el-form-item>
                <el-form-item label="结果凭证" prop="resultCredential">
                    <uploadDoc
                            v-model="mainData.resultCredential"
                            :files.sync="mainData.resultCredential"
                            :disabled="isView"
                            :doc-path="docURL"
                    />
                </el-form-item>
                <el-form-item label="关联资料" >
                    <uploadDoc
                            v-model="mainData.summaryFile"
                            :files.sync="mainData.summaryFile"
                            :disabled="isView"
                            :doc-path="docURL"
                    />
                </el-form-item>-->
                <table class="table_content" style="margin-top: 10px;">
                    <tbody>
                    <!--<tr>
                        <th class="th_label" colspan="2">是否需要集团确认</th>
                        <td class="td_value" colspan="22">{{ mainData.isGroupWhere?是:否}}</td>
                    </tr>-->
                    <tr>
                        <th class="th_label" colspan="3">事件总结</th>
                        <td class="td_value" colspan="21">
                            <uploadDoc
                                    :disabled="isView"
                                    :doc-path="docURL"
                                    :files.sync="mainData.summaryEvent"
                                    v-model="mainData.summaryEvent"
                            />
                        </td>
                    </tr>
                    <tr>
                        <th class="th_label" colspan="3">改进措施</th>
                        <td class="td_value" colspan="21">{{ mainData.improvementMeasures}}</td>
                    </tr>
                    <tr>
                        <th class="th_label" colspan="3">结果凭证</th>
                        <td class="td_value" colspan="21">
                            <uploadDoc
                                    :disabled="isView"
                                    :doc-path="docURL"
                                    :files.sync="mainData.resultCredential"
                                    v-model="mainData.resultCredential"
                            />
                        </td>
                    </tr>
                    <tr>
                        <th class="th_label" colspan="3">关联资料</th>
                        <td class="td_value" colspan="21">
                            <uploadDoc
                                    :disabled="isView"
                                    :doc-path="docURL"
                                    :files.sync="mainData.summaryFile"
                                    v-model="mainData.summaryFile"
                            />
                        </td>
                    </tr>
                    </tbody>
                </table>
            </SimpleBoard>


        </div>

    </div>
</template>
<script>
    import SimpleBoard from '@/view/components/SimpleBoard/SimpleBoardTitle'
    import Baseinfo from './Baseinfo'
    import uploadDoc from '@/view/components/UploadDoc/UploadDoc'

    import orgApi from "@/api/_system/org";
    export default{
        name: "FXZJ",
        components: {
            SimpleBoard, uploadDoc,Baseinfo
        },
        activated() {
            // 长连接页面第二次激活的时候,不会走created方法,会走此方法
            this.initDic();
        },
        created() {
            // 长连接页面第二次激活的时候,不会走created方法,会走此方法
            this.initDic();
        },
        props: {
            dataState: {
                type: String,
                default: ""
            },
            isView: {
                type: Boolean,
                default: false
            },
            mainData: {
                type: Object,
                default:  function () {
                    return {}
                }
            },
            businessDomainData:{
                type:Array,
                default : function () {
                    return [];
                }
            },
            expectedRiskLevelData:{
                type:Array,
                default : function () {
                    return [];
                }
            },
            eventNatureData:{
                type:Array,
                default : function () {
                    return [];
                }
            },
            riskClassificationData:{
                type:Array,
                default : function () {
                    return [];
                }
            },
            mitigationList:{
                type:Array,
                default : function () {
                    return [];
                }
            },
        },
        data() {
            return {
                docURL: '/complianceRiskWaring',
                rules: [],
                title: '',
                hasAdd: true,
                isHas: true,
                tableData:[],

            }
        },
        methods:{
            initDic() {
                dictApi.showSelect({
                    dicCode: 'businessDomainDic'
                }).then(response => {
                    this.businessDomainData = response.data.data
                })
                dictApi.showSelect({
                    dicCode: 'expectedRiskLevelDic'
                }).then(response => {
                    this.expectedRiskLevelData = response.data.data
                })
                dictApi.showSelect({
                    dicCode: 'eventNatureDic'
                }).then(response => {
                    this.eventNatureData = response.data.data
                })
                dictApi.showSelect({
                    dicCode: 'riskClassificationDic'
                }).then(response => {
                    this.riskClassificationData = response.data.data
                })
            },
        }
    }
</script>