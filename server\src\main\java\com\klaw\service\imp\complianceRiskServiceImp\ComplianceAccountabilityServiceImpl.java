package com.klaw.service.imp.complianceRiskServiceImp;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.klaw.dao.complianceRiskDao.ComplianceAccountabilityMapper;
import com.klaw.dao.complianceRiskDao.InvestmentProjectRiskReviewMapper;
import com.klaw.entity.complianceRiskBean.ComplianceAccountability;
import com.klaw.entity.complianceRiskBean.InvestmentProjectRiskReview;
import com.klaw.service.complianceRiskService.ComplianceAccountabilityService;
import com.klaw.service.complianceRiskService.InvestmentProjectRiskReviewService;
import com.klaw.utils.PageUtils;
import com.klaw.vo.Json;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;

@Service
public class ComplianceAccountabilityServiceImpl extends ServiceImpl<ComplianceAccountabilityMapper, ComplianceAccountability> implements ComplianceAccountabilityService {

    @Autowired
    private ComplianceAccountabilityMapper complianceAccountabilityMapper;

    @Override
    public void saveData(ComplianceAccountability complianceAccountability) {
        complianceAccountability.setUpdateTime(new Date());
        saveOrUpdate(complianceAccountability);
    }


    @Override
    public Page<ComplianceAccountability> queryPageData(JSONObject json) {
        QueryWrapper<ComplianceAccountability> wrapper = new QueryWrapper<>();
        getFilter(json, wrapper);
        return page(new PageUtils<>(json), wrapper);
    }


    public void getFilter(JSONObject json, QueryWrapper<ComplianceAccountability> wrapper){
        // 从 JSON 对象中获取值，并设置查询条件
        String accountabilitySubject = json.containsKey("accountabilitySubject") ? json.getString("accountabilitySubject") : null;
        String responsibleParty = json.containsKey("responsibleParty") ? json.getString("responsibleParty") : null;
        Date accountabilityTime = json.containsKey("accountabilityTime") ? json.getDate("accountabilityTime") : null;
        String violationMatter = json.containsKey("violationMatter") ? json.getString("violationMatter") : null;
        String reportingOrganization = json.containsKey("reportingOrganization") ? json.getString("reportingOrganization") : null;
        String reporter = json.containsKey("reporter") ? json.getString("reporter") : null;
        String reportingDate = json.containsKey("reportingDate") ? json.getString("reportingDate") : null;
        String relatedMatter = json.containsKey("relatedMatter") ? json.getString("relatedMatter") : null;
        String uploadAttachment = json.containsKey("uploadAttachment") ? json.getString("uploadAttachment") : null;
        String createOgnId = json.containsKey("createOgnId") ? json.getString("createOgnId") : null;
        String createOgnName = json.containsKey("createOgnName") ? json.getString("createOgnName") : null;
        String createDeptId = json.containsKey("createDeptId") ? json.getString("createDeptId") : null;
        String createDeptName = json.containsKey("createDeptName") ? json.getString("createDeptName") : null;
        String createGroupId = json.containsKey("createGroupId") ? json.getString("createGroupId") : null;
        String createGroupName = json.containsKey("createGroupName") ? json.getString("createGroupName") : null;
        String createPsnId = json.containsKey("createPsnId") ? json.getString("createPsnId") : null;
        String createPsnName = json.containsKey("createPsnName") ? json.getString("createPsnName") : null;
        String createOrgId = json.containsKey("createOrgId") ? json.getString("createOrgId") : null;
        String createOrgName = json.containsKey("createOrgName") ? json.getString("createOrgName") : null;
        String createPsnFullId = json.containsKey("createPsnFullId") ? json.getString("createPsnFullId") : null;
        String createPsnFullName = json.containsKey("createPsnFullName") ? json.getString("createPsnFullName") : null;
        String dataState = json.containsKey("dataState") ? json.getString("dataState") : null;
        String dataStateCode = json.containsKey("dataStateCode") ? json.getString("dataStateCode") : null;
        String fuzzyValue = json.containsKey("fuzzyValue") ? json.getString("fuzzyValue") : null;
        String orgId = json.containsKey("orgId")? json.getString( "orgId"):"";

        if(StringUtils.isNotBlank(orgId)){
            wrapper.eq("create_org_id", orgId);
        }
        // 应用查询条件
        if (StringUtils.isNotBlank(accountabilitySubject)) {
            wrapper.like("accountability_subject", accountabilitySubject);
        }
        if (StringUtils.isNotBlank(responsibleParty)) {
            wrapper.like("responsible_party", responsibleParty);
        }
        if (accountabilityTime != null) {
            wrapper.eq("accountability_time", accountabilityTime);
        }
        if (StringUtils.isNotBlank(violationMatter)) {
            wrapper.like("violation_matter", violationMatter);
        }
        if (StringUtils.isNotBlank(reportingOrganization)) {
            wrapper.like("reporting_organization", reportingOrganization);
        }
        if (StringUtils.isNotBlank(reporter)) {
            wrapper.like("reporter", reporter);
        }
        if (StringUtils.isNotBlank(reportingDate)) {
            wrapper.like("reporting_date", reportingDate);
        }
        if (StringUtils.isNotBlank(relatedMatter)) {
            wrapper.like("related_matter", relatedMatter);
        }
        if (StringUtils.isNotBlank(uploadAttachment)) {
            wrapper.like("upload_attachment", uploadAttachment);
        }
        if (StringUtils.isNotBlank(createOgnId)) {
            wrapper.eq("create_ogn_id", createOgnId);
        }
        if (StringUtils.isNotBlank(createOgnName)) {
            wrapper.like("create_ogn_name", createOgnName);
        }
        if (StringUtils.isNotBlank(createDeptId)) {
            wrapper.eq("create_dept_id", createDeptId);
        }
        if (StringUtils.isNotBlank(createDeptName)) {
            wrapper.like("create_dept_name", createDeptName);
        }
        if (StringUtils.isNotBlank(createGroupId)) {
            wrapper.eq("create_group_id", createGroupId);
        }
        if (StringUtils.isNotBlank(createGroupName)) {
            wrapper.like("create_group_name", createGroupName);
        }
        if (StringUtils.isNotBlank(createPsnId)) {
            wrapper.eq("create_psn_id", createPsnId);
        }
        if (StringUtils.isNotBlank(createPsnName)) {
            wrapper.like("create_psn_name", createPsnName);
        }
        if (StringUtils.isNotBlank(createOrgId)) {
            wrapper.eq("create_org_id", createOrgId);
        }
        if (StringUtils.isNotBlank(createOrgName)) {
            wrapper.like("create_org_name", createOrgName);
        }
        if (StringUtils.isNotBlank(createPsnFullId)) {
            wrapper.like("create_psn_full_id", createPsnFullId);
        }
        if (StringUtils.isNotBlank(createPsnFullName)) {
            wrapper.like("create_psn_full_name", createPsnFullName);
        }
        if (StringUtils.isNotBlank(dataState)) {
            wrapper.like("data_state", dataState);
        }
        if (dataStateCode != null) {
            wrapper.eq("data_state_code", dataStateCode);
        }
        if (StringUtils.isNotBlank(fuzzyValue)) {
            wrapper.and(i -> i.like("accountability_subject", fuzzyValue)
                    .or().like("responsible_party", fuzzyValue))
            ;
        }
        wrapper.orderByDesc("create_time");
    }

    @Override
    public ComplianceAccountability queryDataById(String id) {
        ComplianceAccountability complianceAccountability = getById(id);
        return complianceAccountability;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Json deleteDataById(String id) {
        boolean flag = removeById(id);
        if (!flag) {
            return Json.fail().msg("未找到需要删除的数据");
        }
        return Json.succ().msg("删除成功!");
    }
}
