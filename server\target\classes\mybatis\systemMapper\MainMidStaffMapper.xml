<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.klaw.dao.mainDataDao.MainMidStaffMapper">


    <select id="queryPageData" resultType="java.util.Map">
        select
        s.code as "code",
        s.name as "name",
        s.old_code as "account",
        mid.dept_name as "organization",
        mid.dept_code as "orgCode",
        mid.code as "jobCode",
        mid.code_type as "codeType",
        mid.state as "state"
        from main_mid_staff s LEFT JOIN main_mid_staff_unit_org mid on s.code = mid.staff_code
        <where>
            ${ew.sqlSegment}
        </where>
    </select>

    <select id="queryProcessUserData" resultType="com.klaw.entity.mainDataBean.vo.ProcessStaffOrgVO">
        SELECT
        m.FULL_NAME fullName,
        m.USER_ID userId,
        m.USER_NAME userName,
        org.DEPT_NAME deptName,
        org.ORG_NAME orgName ,
        org.DEPT_CODE deptCode,
        org.ORG_CODE orgCode,
        org.code code
        FROM
        SYS_USER m
        LEFT JOIN SYS_USER_ROLE n ON m.USER_ID = n.USER_ID
        LEFT JOIN main_mid_staff_unit_org org ON m.USER_NAME = org.STAFF_CODE
        AND org.STATE = '1'
        AND org.CODE_TYPE = '1'
        <where>
            ${ew.sqlSegment}
        </where>
    </select>
</mapper>