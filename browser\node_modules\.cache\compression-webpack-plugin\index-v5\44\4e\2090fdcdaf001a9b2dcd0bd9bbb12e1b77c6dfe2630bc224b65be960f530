
9369d24783ad9930effb5add51bac310e0694f19	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.369.1754018536329.js\",\"contentHash\":\"542d7213c12016f98b63749ce506c365\"}","integrity":"sha512-YyHL1Z+juGkI2bJ6TeoUe8MvNmJIdGWWPfSA6QMilFlKQx54nF0BTKR/S9AeiuBCr+I6WNn9yYCKG4laqnsHIg==","time":1754018575975,"size":98308}