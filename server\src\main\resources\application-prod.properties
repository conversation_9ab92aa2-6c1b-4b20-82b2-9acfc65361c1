
activiti.mailServerHost=smtp.126.com
activiti.mailServerPort=465
activiti.mailServerUsername=<EMAIL>
activiti.mailServerPassword=mcp_dev126
#126\uFFFD\uFFFD\uFFFD\uFFFD\u013F\u037B\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\u0228\uFFFD\uFFFD\uFFFD\uFFFD
activiti.mailServerAuthorizationPassword=mcpdev126

server.port=8080
server.servlet.context-path=/mcp

spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.url=******************************************************************************************************************
spring.datasource.username=root
spring.datasource.password=bgjt#2024jtgk!

spring.redis.database=2
spring.redis.host=***********
spring.redis.port=6379
spring.redis.password=bgfw@2025

spring.redis.lettuce.pool.max-active=8
spring.redis.lettuce.pool.max-wait=-1
spring.redis.lettuce.pool.max-idle=8
spring.redis.lettuce.pool.min-idle=0
license.licensePath=/home/<USER>/license/mcplicense2025-06-20.lic

swagger.url.patterns=/**
oa.resource.transfer-uri=http://fw.btsteel.com/transfer
oa.resource.uni-uri=http://fw.btsteel.com/fwh5/#/

oa.task.CXF_URL=https://moat.sgai.com.cn:1443/sgai/api/process/
oa.task.key=DFFD512F3C274EC11AF53753FC82B483
oa.task.cmd=unifiedDealFile
oa.task.systemkey=020
oa.task.application=fb26fdf5-19d9-4baf-9ed8-15759efbd74f
oa.task.authorization=Basic YTNkMzJjZmItY2MyMC00NmQ1LWFkMDgtMmMzYmUzN2ZjNzM5OjVjODM0Y2ZkLTg3NjgtNGFjZS1hNWE5LWRjMGFhN2Q5ZmUzYQ==
oa.task.sourceId=75915689-a8a9-412e-9044-3033f2588220
oa.delete.path=http://cmp.btsteel.com/rest/ofs/deleteUserRequestInfoByJson

oa.sysCode=jtgk_fw
oa.path=http://cmp.btsteel.com
oa.url=http://cmp.btsteel.com/rest/ofs/ReceiveRequestInfoByJson

process.file.url=http://fw.btsteel.com:80/mcp/sys_doc/download/

standardtext.url=http://10.202.19.76:8768
standardtext.urlAdLogin=http://10.202.19.76:8769


#\uFFFD\uFFFD\uFFFD\u00F7\uFFFD\uFFFD\uFFFD\u03F5\u0373\uFFFD\uFFFD\u0328\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\u05B7
WEB_URL=http://fw.btsteel.com:8080
#\u04BB\uFFFD\uFFFD\u0434\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD
EDITOR_URL=http://fw.btsteel.com:88

hwyun.obs.accessKey=B6TQQKBKPJPSLRPHDRBU
hwyun.obs.securityKey=96qvLRFQAfKmMYANBbmKTQRYiq1UlBvV6ENGplS3
hwyun.obs.endPoint=http://10.202.19.76:8001
#hwyun.obs.endPoint=http://10.202.19.107:8001
#hwyun.obs.endPoint=http://10.202.19.229:8001
hwyun.obs.bucketName=fwprod

mybatis-plus.global-config.enable-sql-runner=true

wps.ak=ZXGWPOYDLNHQLUTV
wps.sk=SKkgcfxdpbawjdwd
wps.preview=http://************:8092/open/api/preview/v1/files/
wps.previewCallback=http://************:8092/open/api/preview/v1/files/
wps.edit=http://************:8092/open/api/edit/v1/files/
wps.convert=http://************:8092/open/api/cps/sync/v1/convert
wps.convertDown=http://************:8092/open/api/cps/v1/download
wps.contentOperate=http://************:8092/open/api/cps/sync/v1/content/operate
wps.type=pro
sdk.fileDown=http://localhost:8088/sgaudit/sdkWpsFileDown

sftp.client.protocol=sftp
sftp.client.host=
sftp.client.port=
sftp.client.username=
sftp.client.password=
sftp.client.proxyUrl=
sftp.client.uploadUrl=doc/
sftp.client.privateKey=
sftp.client.passphrase=
sftp.client.sessionStrictHostKeyChecking=no
sftp.client.sessionConnectTimeout=15000
sftp.client.channelConnectedTimeout=15000
sftp.client.serviceUrl=

#esb.url=https://************:9090
esb.url=http://************:8085

xxl.job.user-name=admin
xxl.job.password=123456
xxl.job.admin.addresses=http://*************:8088/xxl-job-admin
xxl.job.accessToken=eyJhbGciOiJIUzI1NiJ9
xxl.job.executor.appname=xxl-job-executor-sample
xxl.job.executor.address=
xxl.job.executor.ip=
xxl.job.executor.port=9999
xxl.job.executor.logpath=./logs/xxl-job/jobhandler
xxl.job.executor.logretentiondays=30