package com.klaw.service.imp.complianceRiskServiceImp;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.klaw.dao.complianceRiskDao.CommonQuestionMapper;
import com.klaw.entity.complianceRiskBean.CommonQuestionEntity;
import com.klaw.entity.config.RiskMiitigationStatus;
import com.klaw.entity.config.RiskWarningQueryStatus;
import com.klaw.service.complianceRiskService.CommonQuestionService;
import com.klaw.utils.PageUtils;
import com.klaw.vo.Json;
import com.sgai.mcp.core.impl.LoginHelper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class CommonQuestionServiceImpl extends ServiceImpl<CommonQuestionMapper, CommonQuestionEntity> implements CommonQuestionService {


    @Override
    public Page<CommonQuestionEntity> queryPageData(JSONObject json) {
        QueryWrapper<CommonQuestionEntity> wrapper = new QueryWrapper<>();
        getFilter(json, wrapper);
        return page(new PageUtils<>(json), wrapper);
    }



    public void getFilter(JSONObject json, QueryWrapper<CommonQuestionEntity> wrapper) {
            // 所属大类
            String category = json.containsKey("category") ? json.getString("category") : null;

            // 单据编号
            String documentNumber = json.containsKey("documentNumber") ? json.getString("documentNumber") : null;

            // 所属子类
            String subCategory = json.containsKey("subCategory") ? json.getString("subCategory") : null;
            // 关键字
            String keyName = json.containsKey("keyName") ? json.getString("keyName") : null;

            // 问题描述
            String questionDescription = json.containsKey("questionDescription") ? json.getString("questionDescription") : null;


            // 模糊搜索值
            String fuzzyValue = json.containsKey("fuzzyValue") ? json.getString("fuzzyValue") : null;

            // 排序字段
            String sortName = json.containsKey("sortName") ? json.getString("sortName") : null;

            // 选择类型
            String selectType = json.containsKey("selectType") ? json.getString("selectType") : null;

            // 排序字段是否升序
            boolean order = json.containsKey("order") ? json.getBoolean("order") : false;

            // 应用查询条件
            if (StringUtils.isNotBlank(category)) {
                wrapper.and(i -> i.like("category", category));
            }

            if (StringUtils.isNotBlank(documentNumber)) {
                wrapper.and(i -> i.like("document_number", documentNumber));
            }

            if (StringUtils.isNotBlank(subCategory)) {
                wrapper.and(i -> i.like("sub_category", subCategory));
            }

            if (StringUtils.isNotBlank(keyName)) {
                wrapper.and(i -> i.like("key_name", keyName));
            }


            if (StringUtils.isNotBlank(questionDescription)) {
                wrapper.and(i -> i.like("question_description", questionDescription));
            }

            if (StringUtils.isNotBlank(fuzzyValue)) {
                wrapper.and(i -> i.like("category", fuzzyValue)
                        .or().like("document_number", fuzzyValue)
                        .or().like("question_description", fuzzyValue)
                        .or().like("key_name", fuzzyValue)
                        .or().like("sub_category", fuzzyValue));

            }
            if (StringUtils.isNotBlank(selectType)) {
                if ("XZ".equals(selectType)) {
                    wrapper.eq("create_psn_id", LoginHelper.getUserId());
                }
            }
            if (StringUtils.isNotBlank(sortName) && order) {
                wrapper.orderByAsc(sortName);
            } else if (StringUtils.isNotBlank(sortName)) {
                wrapper.orderByDesc(sortName);
            }

            //按时间倒序显示
            wrapper.orderByDesc("release_date");
        }


        @Override
        public CommonQuestionEntity queryDataById(String id) {
            CommonQuestionEntity commonQuestionEntity = getById(id);
            return commonQuestionEntity;
        }

        @Override
        @Transactional(rollbackFor = Exception.class)
        public Json deleteDataById(String id) {
            boolean flag = removeById(id);
            if (!flag) {
                return Json.fail().msg("未找到需要删除的数据");
            }
            return Json.succ().msg("删除成功!");
        }
}


