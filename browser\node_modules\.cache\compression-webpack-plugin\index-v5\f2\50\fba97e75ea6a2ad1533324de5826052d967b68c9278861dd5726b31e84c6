
89a2b2bf1d8c158ded1ea90b0c6c6adbbf3f92e4	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.40.1754018536329.js\",\"contentHash\":\"1f81c1096efd396ffc5834205cd41892\"}","integrity":"sha512-quhAm4pYyaGeNaJi7tXk/yAkCY/nxD0i5qxMY5JMyxmWKvk/RunsKh9n5sn9BeoUDBeTMcz+XKrqgxXJ1azsuQ==","time":1754018575955,"size":40642}