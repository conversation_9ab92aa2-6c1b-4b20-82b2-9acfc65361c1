<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.klaw.dao.systemDao.SgSysMessageMapper">

    <resultMap id="BaseResultMap" type="com.klaw.entity.systemBean.SgSysMessage">
            <id property="messageId" column="MESSAGE_ID" jdbcType="BIGINT"/>
            <result property="messageType" column="MESSAGE_TYPE" jdbcType="VARCHAR"/>
            <result property="messageHost" column="MESSAGE_HOST" jdbcType="VARCHAR"/>
            <result property="messageFrom" column="MESSAGE_FROM" jdbcType="VARCHAR"/>
            <result property="subject" column="SUBJECT" jdbcType="VARCHAR"/>
            <result property="content" column="CONTENT" jdbcType="VARCHAR"/>
            <result property="priorityLevel" column="PRIORITY_LEVEL" jdbcType="VARCHAR"/>
            <result property="sendFlag" column="SEND_FLAG" jdbcType="VARCHAR"/>
            <result property="messageSource" column="MESSAGE_SOURCE" jdbcType="VARCHAR"/>
            <result property="sendType" column="SEND_TYPE" jdbcType="VARCHAR"/>
            <result property="messageInformType" column="MESSAGE_INFORM_TYPE" jdbcType="VARCHAR"/>
            <result property="messageLevel" column="MESSAGE_LEVEL" jdbcType="VARCHAR"/>
            <result property="messageOffline" column="MESSAGE_OFFLINE" jdbcType="BIGINT"/>
            <result property="resendNumber" column="RESEND_NUMBER" jdbcType="BIGINT"/>
            <result property="sendUserId" column="SEND_USER_ID" jdbcType="BIGINT"/>
            <result property="createdBy" column="CREATED_BY" jdbcType="BIGINT"/>
            <result property="creationDate" column="CREATION_DATE" jdbcType="TIMESTAMP"/>
            <result property="lastUpdatedBy" column="LAST_UPDATED_BY" jdbcType="BIGINT"/>
            <result property="lastUpdateDate" column="LAST_UPDATE_DATE" jdbcType="TIMESTAMP"/>
            <result property="objectVersionNumber" column="OBJECT_VERSION_NUMBER" jdbcType="INTEGER"/>
            <result property="deleted" column="DELETED" jdbcType="DECIMAL"/>
    </resultMap>

    <sql id="Base_Column_List">
        MESSAGE_ID,MESSAGE_TYPE,MESSAGE_HOST,
        MESSAGE_FROM,SUBJECT,CONTENT,
        PRIORITY_LEVEL,SEND_FLAG,MESSAGE_SOURCE,
        SEND_TYPE,MESSAGE_INFORM_TYPE,MESSAGE_LEVEL,
        MESSAGE_OFFLINE,RESEND_NUMBER,SEND_USER_ID,
        CREATED_BY,CREATION_DATE,LAST_UPDATED_BY,
        LAST_UPDATE_DATE,OBJECT_VERSION_NUMBER,DELETED
    </sql>

    <select id="selectReceiverMessage" resultType="java.util.Map">
        SELECT
            M.MESSAGE_ID,
            M.MESSAGE_TYPE,
            M.MESSAGE_HOST,
            M.MESSAGE_FROM,
            M.SUBJECT,
            M.CONTENT,
            M.PRIORITY_LEVEL,
            M.SEND_FLAG,
            M.MESSAGE_SOURCE,
            M.OBJECT_VERSION_NUMBER,
            M.CREATED_BY,
            M.CREATION_DATE,
            M.LAST_UPDATED_BY,
            M.LAST_UPDATE_DATE,
            M.SEND_TYPE,
            M.MESSAGE_INFORM_TYPE,
            M.MESSAGE_LEVEL,
            M.MESSAGE_OFFLINE,
            M.RESEND_NUMBER,
            M.DELETED,
            R.RECEIVER_USER_ID,
            R.MESSAGE_INFROM_DATE,
            R.MESSAGE_READ_TYPE,
            R.MESSAGE_ADDRESS_TYPE,
            U.FULL_NAME AS SEND_USER_NAME
        FROM
            SYS_MESSAGE M,
            SYS_MESSAGE_RECEIVER R,
            SYS_USER U
        WHERE
            M.MESSAGE_ID = R.MESSAGE_ID
          AND M.MESSAGE_FROM = U.USER_NAME
          AND R.MESSAGE_ADDRESS = #{messageAddress}
          AND R.MESSAGE_READ_TYPE = #{readType}
        ORDER BY
            R.MESSAGE_READ_TYPE,
            R.MESSAGE_INFROM_DATE DESC
    </select>
</mapper>
