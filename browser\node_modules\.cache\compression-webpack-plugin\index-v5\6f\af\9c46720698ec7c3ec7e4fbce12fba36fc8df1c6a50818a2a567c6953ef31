
11faf7326d1ed7206e9b3d3e687253f7f0722b31	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.53.1754018536329.js\",\"contentHash\":\"9a49605baff8714628e8321bdcd1b942\"}","integrity":"sha512-wmP9HAs5Z+q4KKqMdhM1w/chTVdlW/qxPr00eyz/vXBmedCIigWurpUrUXxpf9uqUlagPYkwtZRaAmnRbu8iJg==","time":1754018576296,"size":1214772}