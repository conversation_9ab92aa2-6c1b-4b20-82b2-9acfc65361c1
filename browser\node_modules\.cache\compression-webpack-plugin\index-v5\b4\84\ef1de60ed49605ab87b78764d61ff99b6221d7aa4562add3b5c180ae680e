
e24bbbed02af3fe4c538f04485b00c36528f4b00	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.433.1754018536329.js\",\"contentHash\":\"1386f55137b7fe965b9bc244e33d09e7\"}","integrity":"sha512-LDiYfuMhl6rEuXGeEAwG08EjvcUjTBle/RlPwzNthSK1Z0Ge5+haHRr018+NW4olJpxQ4Yb5Mt0a/7eaFOlKQA==","time":1754018575976,"size":61524}