import {request} from '@/api/index'

export default {
  save(data) {
    return request({
      url: '/bm-contract-batch/save',
      method: 'post',
      data
    })
  },
  delete(data) {
    return request({
      url: '/bm-contract-batch/delete',
      method: 'post',
      data
    })
  },
  queryById(data) {
    return request({
      url: '/bm-contract-batch/queryById',
      method: 'post',
      data
    })
  },
  setParam(data) {
    return request({
      url: '/bm-contract-batch/setParam',
      method: 'post',
      data
    })
  },
  saveComplianceFiles(data) {
    return request({
      url: '/bm-contract-batch/saveComplianceFiles',
      method: 'post',
      data
    })
  },
}