
bb388012ef3616499dfaf69184c97408b26c93c0	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.79.1754018536329.js\",\"contentHash\":\"e62c7fbbca3de122a5b100f19b61bfd5\"}","integrity":"sha512-dGsGQI0L2OY23nD5Yfg7skTkO9qfa8/e6L/AbzMgE6+Is/FCQ40vuM9QTezgo1v+g483w86xF9gIyEzHclfXcQ==","time":1754018575979,"size":176860}