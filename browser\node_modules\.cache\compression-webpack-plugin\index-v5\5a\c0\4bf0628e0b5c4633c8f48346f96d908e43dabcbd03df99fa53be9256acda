
505279636d4a5fb5cb2ddbcb007ae85b61ad1327	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.168.1754018536329.js\",\"contentHash\":\"a8b8009d1b3b5e222a6c7c8eeab839a2\"}","integrity":"sha512-UBPjzul4KQOT/SeWcnbdJXjTzDJWZDTOxkRYw0XjPWK8z2LxNMCoHDx8yC49cijH5m7V731JHyfjmUOVWTLtig==","time":1754018576346,"size":2181766}