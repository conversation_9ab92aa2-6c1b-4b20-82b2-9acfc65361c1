package com.klaw.service.imp.complianceTrainingServiceImp;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.klaw.dao.complianceTrainingDao.ComplianceTrainingReportsMapper;
import com.klaw.entity.complianceTrainingBean.ComplianceTrainingReports;
import com.klaw.entity.complianceTrainingBean.HouseholdComplianceTrainingLearningTable;
import com.klaw.service.complianceTrainingService.ComplianceTrainingReportsService;
import com.klaw.service.complianceTrainingService.HouseholdComplianceTrainingLearningTableService;
import com.klaw.utils.PageUtils;
import com.klaw.vo.complianceTrainingReports.ComplianceTrainingReportsVo;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @description 针对表【compliance_training_reports(合规培训报告表)】的数据库操作Service实现
 * @createDate 2024-12-02 11:53:53
 */
@Service
public class ComplianceTrainingReportsServiceImpl extends ServiceImpl<ComplianceTrainingReportsMapper, ComplianceTrainingReports>
        implements ComplianceTrainingReportsService {

    @Resource
    private HouseholdComplianceTrainingLearningTableService householdComplianceTrainingLearningTableService;
    @Resource
    private ComplianceTrainingReportsService complianceTrainingReportsService;
    @Override
    public ComplianceTrainingReportsVo queryPageData(JSONObject jsonObject) {
        String complianceTrainingLearningTableId = jsonObject.getString("complianceTrainingLearningTableId");
        int uploadedCount = 0;
        int notUploadedCount = 0;
        List<HouseholdComplianceTrainingLearningTable> userList = householdComplianceTrainingLearningTableService.list(new QueryWrapper<HouseholdComplianceTrainingLearningTable>()
                .eq("compliance_training_learning_table_id", complianceTrainingLearningTableId)
                .in("learning_progress", Arrays.asList("已完成", "学习中")));
        List<ComplianceTrainingReports> reportList = complianceTrainingReportsService.list(new QueryWrapper<ComplianceTrainingReports>()
                .eq("compliance_training_learning_table_id", complianceTrainingLearningTableId));
        QueryWrapper<ComplianceTrainingReports> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("compliance_training_learning_table_id", complianceTrainingLearningTableId);
        queryWrapper.orderByDesc("create_time");
        PageUtils<ComplianceTrainingReports> page = page(new PageUtils<>(jsonObject), queryWrapper);
        // 遍历userList拿到learningPsnId与reportList中的createPsnId作比较
        // 如果learningPsnId等于当前的createPsnId，则uploadedCount++
        Set<String> uploadedPsnIds = new HashSet<>();
        Set<String> notUploadedPsnIds = new HashSet<>();

        for (HouseholdComplianceTrainingLearningTable user : userList) {
            boolean isUploaded = false;
            for (ComplianceTrainingReports report : reportList) {
                if (user.getCreatePsnId().equals(report.getCreatePsnId())) {
                    isUploaded = true;
                    break;
                }
            }
            if (isUploaded) {
                uploadedPsnIds.add(user.getCreatePsnId());
            } else {
                notUploadedPsnIds.add(user.getCreatePsnId());
            }
        }

        uploadedCount = uploadedPsnIds.size();
        notUploadedCount = notUploadedPsnIds.size();
        ComplianceTrainingReportsVo reportsVo = new ComplianceTrainingReportsVo();
        reportsVo.setUploadedCount(uploadedCount);
        reportsVo.setNotUploadedCount(notUploadedCount);
        reportsVo.setComplianceTrainingReports(page);
        return reportsVo;
    }
}




