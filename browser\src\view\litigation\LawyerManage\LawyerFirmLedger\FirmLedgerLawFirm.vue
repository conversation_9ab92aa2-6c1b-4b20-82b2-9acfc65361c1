<template>
  <!--这个页面是作为律所库表数据查看用的，不涉及到操作，变更也只是一个入口跳转到律所入库审批界面-->
  <FormWindow ref="formWindow" @loadData="loadData">
    <el-container v-loading="loading" style="height: calc(100vh - 84px);">
      <el-header style="padding-top: 10px;padding-bottom: 10px;border-bottom: solid 1px #f7f7f7;background-color: #FCFCFC">
        <span style="text-align: left;font-size: 24px;margin: 0;font-weight: 1000;">律所信息</span>
        <div style="display: inline;float: right;margin-top: 0px;margin-right: 30px" v-if="source === 'ledger'">
          <el-button v-if="mainData.dataState === utils.dataState_LAWYER.YRK.name && contrastTypeCode" class="normal-btn" size="mini" icon="el-icon-folder-checked" @click="addBlackList()">移入黑名单</el-button>
          <el-button v-if="mainData.typeCode === '0' && mainData.dataState === utils.dataState_LAWYER.YRK.name && mainData.lawFirmType === '正式律所'" class="normal-btn" size="mini" icon="el-icon-folder-checked" @click="recommendLawFirm()">加入推荐库</el-button>
          <el-button v-if="mainData.dataState === utils.dataState_LAWYER.YCK.name  && contrastTypeCode" class="normal-btn" size="mini" icon="el-icon-folder-checked" @click="inLawFirm()">重新入库</el-button>
          <el-button v-if="mainData.dataState === utils.dataState_LAWYER.YRK.name && contrastTypeCode" class="normal-btn" size="mini" icon="el-icon-folder-checked" @click="changeLawFirm()">变更</el-button>
          <el-button v-if="mainData.dataState === utils.dataState_LAWYER.YRK.name" class="normal-btn" size="mini" icon="el-icon-folder-checked" @click="changeLawFirm()">变更</el-button>
        </div>
      </el-header>

      <el-main>
        <el-scrollbar style="height: 100%" wrap-style="overflow-x: hidden;">
          <el-form ref="dataForm" style="padding-right: 10px;" :model="mainData" label-width="100px">

            <SimpleBoardTitle title="基本信息" style="margin-top: 5px;">
              <table class="table_content" style="margin-top: 10px;">
                <tbody>
                <tr>
                  <th colspan="2" class="th_label">律所名称</th>
                  <td colspan="22" class="td_value">{{ mainData.lawyerFirm }}</td>
                </tr>
                </tbody>

                <tbody>
                <tr>
                  <th colspan="2" class="th_label">律所地址</th>
                  <td colspan="22" class="td_value">{{ mainData.registerAddress }}</td>
                </tr>
                </tbody>

                <tbody>
                <tr>
                  <th colspan="2" class="th_label">律所类型</th>
                  <td colspan="6" class="td_value">{{ mainData.lawFirmType }}</td>
                  <th colspan="2" class="th_label">律所电话</th>
                  <td colspan="6" class="td_value">{{ mainData.lawFirmPhone }}</td>
                  <th colspan="2" class="th_label">律所邮箱</th>
                  <td colspan="6" class="td_value">{{ mainData.lawFirmEmail }}</td>
                </tr>
                </tbody>

                <tbody>
                <tr>
                  <th colspan="2" class="th_label">负责人</th>
                  <td colspan="6" class="td_value">{{ mainData.functionary }}</td>
                  <th colspan="2" class="th_label">邮政编码</th>
                  <td colspan="6" class="td_value">{{ mainData.postalCode }}</td>
                  <th colspan="2" class="th_label">主管机关</th>
                  <td colspan="6" class="td_value">{{ mainData.issuingAuthority }}</td>
                </tr>
                </tbody>

                <tbody>
                <tr>
                  <th colspan="2" class="th_label">统一社会<br>信用代码</th>
                  <td colspan="6" class="td_value">{{ mainData.licenseCode }}</td>
                  <th colspan="2" class="th_label">批准文号</th>
                  <td colspan="6" class="td_value">{{ mainData.licenseNumber }}</td>
                  <th colspan="2" class="th_label">设立资产</th>
                  <td colspan="6" class="td_value">{{ mainData.registeredCapital }}</td>
                </tr>
                </tbody>

                <tbody>
                <tr>
                  <th colspan="2" class="th_label">成立时间</th>
                  <td colspan="6" class="td_value">{{ mainData.foundTime | parseTime }}</td>
                  <th colspan="2" class="th_label">执业人数</th>
                  <td colspan="6" class="td_value">{{ mainData.workNumber }}</td>
                  <th colspan="2" class="th_label">律所规模</th>
                  <td colspan="6" class="td_value">{{ mainData.lawFirmLevel }}</td>
                </tr>
                </tbody>

                <tbody>
                <tr>
                  <th colspan="2" class="th_label">申请部门</th>
                  <td colspan="6" class="td_value">{{ mainData.applyDeptName }}</td>
                  <th colspan="2" class="th_label">年检情况</th>
                  <td colspan="14" class="td_value">{{ mainData.annualInspectionName }}</td>
                </tr>
                </tbody>

                <tbody v-if="mainData.annualInspectionName === '其他'">
                <tr>
                  <th colspan="2" class="th_label">年检情况说明</th>
                  <td colspan="22" class="td_value">{{ mainData.annualInspectionDesc }}</td>
                </tr>
                </tbody>

                <tbody v-if="this.mainData.typeCode === '1'">
                <tr>
                  <th colspan="2" class="th_label">推荐说明</th>
                  <td colspan="22" class="td_value">{{ mainData.recommendedInstructionsName }}</td>
                </tr>
                </tbody>

                <tbody>
                <tr>
                  <th colspan="2" class="th_label">擅长业务领域</th>
                  <td colspan="22" class="td_value">{{ mainData.beGoodAtDomain }}</td>
                </tr>
                </tbody>

                <tbody>
                <tr>
                  <th colspan="2" class="th_label_">简介</th>
                  <td colspan="22" class="td_value_">{{ mainData.introduction }}</td>
                </tr>
                </tbody>

                <tbody>
                <tr>
                  <th colspan="2" class="th_label_">备注</th>
                  <td colspan="22" class="td_value_">{{ mainData.remark }}</td>
                </tr>
                </tbody>

                <tbody>
                <tr>
                  <th colspan="2" class="th_label_">附件资料</th>
                  <td colspan="22" class="td_value_">
                    <uploadDoc
                        v-model="mainData.businessLicense"
                        :files.sync="mainData.businessLicense"
                        :disabled="dataState==='view'"
                        :tips="tip_"
                        :doc-path="docURL"
                    />
                  </td>
                </tr>
                </tbody>
              </table>
            </SimpleBoardTitle>

            <simple-board title="主责律师/对接律师" :has-add="hasAdd" :data-state="dataState" style="margin-top: 20px;">
              <el-table
                  v-draggable="mainData.lawyerList"
                  style="width: 100%"
                  :data="mainData.lawyerList"
                  border
                  stripe
                  :show-overflow-tooltip="true"
                  fit
                  :height="200"
                  highlight-current-row
              >
                <el-table-column label="序号" type="index" align="center" width="60" show-overflow-tooltip />
                <el-table-column label="姓名" min-width="100" align="center" prop="lawyerName" show-overflow-tooltip />
                <el-table-column label="律师身份" min-width="100" align="center" prop="whetherHostLawyer" show-overflow-tooltip >
                  <template slot-scope="scope">
                    <span>{{ scope.row.whetherHostLawyer | whetherHostLawyerFilter }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="联系电话" min-width="100" align="center" prop="lawyerPhone" show-overflow-tooltip />
                <el-table-column label="电子邮箱" min-width="100" align="center" prop="lawyerEmail" show-overflow-tooltip />
                <el-table-column label="擅长领域" min-width="150" align="center" prop="beGoodAtDomain" show-overflow-tooltip />
                <el-table-column label="主要执业地域" min-width="150" align="center" prop="majorRegion" show-overflow-tooltip />
                <el-table-column label="操作" align="center" width="200px">
                  <template slot-scope="scope">
                    <el-button v-if="dataState === 'view'" size="mini" type="text" @click.native.prevent="look(scope.$index,scope.row)">查看</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </simple-board>

            <simple-board title="代理信息" :has-add="hasAdd" :data-state="dataState" style="margin-top: 20px;" >
              <el-table
                  style="width: 100%"
                  :data="mainData.agentList"
                  border
                  stripe
                  :show-overflow-tooltip="true"
                  fit
                  :height="200"
                  highlight-current-row
              >
                <el-table-column label="序号" type="index" align="center" width="60" show-overflow-tooltip />
                <el-table-column label="类型" align="center" prop="selectionTypeName" show-overflow-tooltip />
                <el-table-column label="名称" align="center" prop="name" show-overflow-tooltip />
                <el-table-column label="代理阶段" align="center" prop="agentStageName" show-overflow-tooltip />
                <el-table-column label="经办时间" align="center" prop="createTime" show-overflow-tooltip>
                  <template slot-scope="scope">
                    <span>{{ scope.row.createTime | parseTime }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="操作" align="center" width="200px">
                  <template slot-scope="scope">
                    <el-button
                        v-if="dataState === 'view'"
                        size="mini" type="text"
                        @click="lookAgent(scope.$index,scope.row)">查看</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </simple-board>

            <el-row style="margin-top: 20px" class="rowCol1">
              <el-col :span="16">
                <el-form-item label="经办单位">
                  <span class="viewSpan">{{ mainData.createOgnName }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="经办人">
                  <span class="viewSpan">{{ mainData.createPsnName }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="16">
                <el-form-item label="经办部门">
                  <span class="viewSpan">{{ mainData.createDeptName }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="经办时间">
                  <span class="viewSpan">{{ mainData.createTime | parseTime }}</span>
                </el-form-item>
              </el-col>
            </el-row>

          </el-form>
        </el-scrollbar>
      </el-main>

      <el-form ref="agentRow"
               :model="agentRow"
               >
        <!--新增代理信息-->
        <el-dialog :close-on-click-modal="false" :title="agentRowTitle" :visible.sync="agentDialogVisible" width="50%">
          <div class="el-dialog-div">
            <el-row>
              <el-col :span="24">
                <el-form-item label="类型" prop="selectionTypeId">
                  <el-select v-if="dataState !== 'view'" v-model="agentRow.selectionTypeId"
                             placeholder="请选择" style="width:100%" clearable
                             @change="selectionTypeChange" @clear="selectionTypeClear" >
                    <el-option
                        v-for="item in utils.selectionType_data"
                        :key="item.id"
                        :label="item.dicName"
                        :value="item.id"
                    />
                  </el-select>
                  <span v-else class="viewSpan">{{ agentRow.selectionTypeName }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="名称" prop="name">
                  <el-input v-if="dataState !== 'view'" v-model="agentRow.name"
                            maxlength="50" show-word-limit style="width: 100%"
                            placeholder="请输入..." class="input-with-select"
                            clearable  />
                  <span v-else class="viewSpan">{{ agentRow.relationCaseName }}</span>
                </el-form-item>
              </el-col>

              <el-col :span="12" v-if="agentRow.selectionTypeId === '2'">
                <el-form-item label="关联案件" prop="relationCaseName">
                  <el-input v-if="dataState !== 'view'" v-model="agentRow.relationCaseName" maxlength="20" show-word-limit style="width: 100%" placeholder="请输入..." disabled>
                    <el-button slot="append" icon="el-icon-search" @click="choiceCaseClick" />
                  </el-input>
                  <span v-else class="viewSpan">{{ agentRow.relationCaseName }}</span>
                </el-form-item>
              </el-col>

              <el-col :span="12" v-if="agentRow.selectionTypeId === '2'">
                <el-form-item label="代理阶段">
                  <el-select v-if="dataState !== 'view'" v-model="agentStageIds" multiple style="width: 100%;" @change="agentStageOnChange">
                    <el-option v-for="item in processData" :key="item.code" :label="item.name" :value="item.code" />
                  </el-select>
                  <span v-else class="viewSpan">{{ agentRow.agentStageName }}</span>
                </el-form-item>
              </el-col>

              <el-col :span="12" v-if="agentRow.selectionTypeId === '3'">
                <el-form-item label="开始时间" prop="beginTime">
                  <el-date-picker v-if="dataState !== 'view'"
                                  v-model="agentRow.beginTime"
                                  value-format="yyyy-MM-dd" type="date" style="width: 100%" clearable />
                  <span v-else class="viewSpan">{{ agentRow.beginTime | parseTime }}</span>
                </el-form-item>
              </el-col>

              <el-col :span="12" v-if="agentRow.selectionTypeId === '3'">
                <el-form-item label="结束时间" prop="endTime">
                  <el-date-picker v-if="dataState !== 'view'"
                                  v-model="agentRow.endTime"
                                  value-format="yyyy-MM-dd" type="date" style="width: 100%" clearable />
                  <span v-else class="viewSpan">{{ agentRow.endTime | parseTime }}</span>
                </el-form-item>
              </el-col>

              <el-col :span="24">
                <el-form-item label="详细描述" prop="description">
                  <el-input v-if="dataState !== 'view'" v-model="agentRow.description"
                            type="textarea" placeholder="请输入..."
                            :autosize="{ minRows: 3, maxRows: 6}" maxlength="1000"
                            show-word-limit style="width: 100%" clearable />
                  <text-span v-else class="viewSpan" :text=" agentRow.description" />
                </el-form-item>
              </el-col>

              <el-col :span="24">
                <el-form-item label="状态" prop="type">
                  <el-select v-if="dataState !== 'view'" v-model="agentRow.type"
                             placeholder="请选择" style="width:100%" clearable >
                    <el-option
                        v-for="item in this.typeData"
                        :key="item.id"
                        :label="item.name"
                        :value="item.name"
                    />
                  </el-select>
                  <span v-else class="viewSpan">{{ agentRow.type }}</span>
                </el-form-item>
              </el-col>

              <el-col :span="24" prop="attachment">
                <el-form-item label="附件"  prop="attachment">
                  <uploadDoc
                      v-model="agentRow.attachment"
                      :files.sync="agentRow.attachment"
                      :disabled="dataState === 'view'"
                      :doc-path="docURL"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
          <span slot="footer" class="dialog-footer">
            <el-button icon="" class="negative-btn" @click="cancelAgent">取消</el-button>
            <el-button v-if="dataState !== 'view'"
                       type="primary" icon="" class="active-btn"
                       @click="agentSure">确定</el-button>
          </span>
        </el-dialog>
      </el-form>
    </el-container>
  </FormWindow>
</template>

<script>
import lawyerFirmApi from "@/api/LawyerManage/LawyerFirm/lawyerFirm"
import FormWindow from '@/view/components/FormWindow/FormWindow'
import SimpleBoard from "@/view/components/SimpleBoard/SimpleBoardViewCase"
import uploadDoc from '@/view/components/UploadDoc/UploadDoc'
import textSpan from '@/view/components/TextSpan/TextSpan'
import {mapGetters} from 'vuex'
import taskApi from "@/api/_system/task"
import {isZC} from "@/view/utils/constants"
import ProcessOpinion from '@/view/components/ProcessOpinion/ProcessOpinion'
import SimpleBoardTitle from "../../../components/SimpleBoard/SimpleBoardTitle"

export default {
    name: 'FirmLedgerLawFirm',
    inject: ['layout'],
    components: { FormWindow, SimpleBoard, uploadDoc, textSpan, ProcessOpinion, SimpleBoardTitle },
    filters: {
      whetherHostLawyerFilter(val) {
        return val ? '是' : '否'
      }
    },
    data() {
      return {
        dataState: null,
        loading: true,
        typeName: null, // 库类型名称
        typeCode: null, // 库类型编码
        // 律所数据
        mainData: {
          id: this.utils.createUUID(), // id
          lawyerFirm: null, // 律所名称
          registerAddress: null, // 注册地址/律所地址
          functionary: null, // 负责人
          postalCode: null, // 邮政编码
          issuingAuthority: null, // 主管机关
          licenseCode: null, // 统一社会信用代码
          licenseNumber: null, // 批准文号
          registeredCapital: null, // 设立资产
          foundTime: null, // 成立时间
          workNumber: null, // 执业人数
          applyDeptName: null, // 申请部门/所属公司
          applyDeptId: null, // 申请部门/所属公司
          annualInspectionName: null, // 年检情况
          annualInspectionId: null, // 年检情况
          annualInspectionDesc:null,// 年检情况描述
          recommendedInstructionsName: null, // 推荐说明
          recommendedInstructionsId: null, // 推荐说明
          introduction: null, // 简介
          remark: null, // 备注
          businessLicense: null, // 营业执照、附件资料
          updateTime: null, // 更新时间
          typeName: null, // 库类型名称
          typeCode: null, // 库类型编码
          dataState: this.utils.dataState_BPM.SAVE.name, // 状态 新增时默认是已保存
          dataStateCode: this.utils.dataState_BPM.SAVE.code, // 状态编码
          createOgnName: null, //  经办单位
          createDeptName: null, // 经办部门
          createPsnName: null, // 经办人员
          createTime: null, // 经办时间
          createOgnId: null,
          createDeptId: null,
          createPsnId: null,
          createPsnFullId: null,
          createPsnFullName: null,
          createGroupId: null,
          createGroupName: null,
          createOrgId: null,
          createOrgName: null,
          changeTimes: 0,
          parentId: null,
          dataSource: 'new',
          sourceId: null,
          whetherMy: 'no',
          lawyerList: [], // 弹框的表格数据源
          agentList: [], // 代理信息表格数据源
          opretingType: 'new', // 弹框的表格数据源
          beGoodAtDomain: null, // 擅长领域
          beGoodAtDomainIds: null, // 擅长领域
          lawFirmPhone: null, // 律所电话
          lawFirmEmail: null, // 律所邮箱
          lawFirmType: null, // 律所类型
          lawFirmSelected: false, // 是否选聘
        },
        agentRow: {
          id: null,
          lawFirm: null, // 律所名称
          lawFirmId: null, // 律所ID
          lawyerName: null, // 律师姓名
          lawyerId: null, // 律师id
          charteredNo: null, // 执业证号
          parentId: null,
          selectionTypeId: null, // 类型id
          selectionTypeName: null, // 类型名称
          beginTime: null, // 开始时间
          endTime: null, // 结束时间
          name: null,    // 名称
          type: null, // 状态
          attachment: null, // 附件
          relationCaseName: null, // 关联案件
          relationCaseId: null, // 关联案件ID
          agentStageName: null, // 代理阶段
          agentStageId: null, // 代理阶段ID
          description: null, // 详细描述
          dataSource: 'new',
          createOgnName: null, //  经办单位
          createDeptName: null, // 经办部门
          createPsnName: null, // 经办人员
          createTime: null, // 经办时间
          updateTime: null, // 更新时间
          createOgnId: null,
          createDeptId: null,
          createPsnId: null,
          createPsnFullId: null,
          createPsnFullName: null,
          createGroupId: null,
          createGroupName: null,
          createOrgId: null,
          createOrgName: null,
        },
        NJQKData: [],
        TJSMData: [],
        SCLYData: [],
        lawFirmTypeData: [{id: '0', name: '正式律所'}, {id: '1', name: '临时律所'}],
        tip_: '附送材料需要上传通过年检的《营业执照》/《执业资格许可证》（或副本）的复印件、负责人或主要联系人资格证书复印件。',
        docURL: '/firmIn',
        showUser: false,
        is_Check: true,
        isCheckedUser: false,
        orgVisible: false,
        hasAdd: false,
        source: '',
        agentDialogVisible: false,
        typeData : [
          {id: 1, name: '未完成'},
          {id: 2, name: '已完成'},
          {id: 3, name: '不适用'},
        ],
      }
    },
    created() {
      this.isHeadquarter()
    },
    computed: {
      ...mapGetters(['orgContext']),
      contrastTypeCode: function() {
        return this.typeCode === this.mainData.typeCode
      },
      agentRowTitle() {
        return this.dataState !== 'view' ? '新增代理信息' : '查看代理信息'
      },
      agentStageIds: {
        set: function(data) {
          this.agentRow.agentStageId = data.join(',')
        },
        get: function() {
          if (this.agentRow.agentStageId) {
            return this.agentRow.agentStageId.split(',')
          }
          return []
        }
      },
      isZc: function() {
        return isZC(this.processName)
      },
      processData: function() {
        if (this.isZc) return [{ code: 'all', name: '全过程' }, ...this.utils.caseProcessData.ZC]
        return [{ code: 'all', name: '全过程' }, ...this.utils.caseProcessData.MSSS]
      },
    },
    methods:{
      // 根据数据ID加载数据
      loadData(dataState, dataId) {
        this.dataState = dataState
        if (this.$route.query.source) {
          this.source = this.$route.query.source
        }
        lawyerFirmApi.queryDataById({ id: dataId }).then(res => {
          this.mainData = res.data.data
          this.loading = false
        })
      },
      changeLawFirm() {
        this.$confirm('您确定要变更该数据吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          const row = this.mainData
          const uuid = this.utils.createUUID();
          this.layout.openNewTab(
              "入库信息",
              "firm_main_change_detail",
              "firm_main_change_detail",
              uuid,
              {
                LawFirmChangeState: 'change',
                functionId: "firm_main_new_detail,"+uuid,
                ...this.utils.routeState.EDIT(row.id)
              }
          );
          }).catch(() => {
            this.$message.info('已取消变更!')
        })
      },
      inLawFirm() {
        taskApi.selectFunctionId({functionCode: 'firm_main_change'}).then(res=> {
          const row = this.mainData
          const functionId = res.data.data[0].ID
          const tabId = this.utils.createUUID()
          this.layout.openNewTab("入库信息",
              "design_page",
              "design_page",
              tabId,
              {
                functionId: functionId,
                entranceType: "FLOWABLE",
                LawFirmChangeState: 'in',
                view: 'old',
                ...this.utils.routeState.EDIT(row.id)
              })
        })
      },
      recommendLawFirm() {
        lawyerFirmApi.isRecommend({id: this.mainData.id, code: this.mainData.licenseCode}).then(res=> {
          if (res.data.data)
          {
            lawyerFirmApi.getExistLawyerList({id: this.mainData.id, code: this.mainData.licenseCode}).then(res => {
              let message = ""
              let array = res.data.data
              if (array.length > 0) {
                message += "以下律师已被推荐: \n"
                for (let i = 0; i < array.length; i++) {
                  message += array[i].lawyerName + ','
                }
                message += '\n是否过滤后继续推荐'
              }
              this.$confirm('您确定要推荐该律所吗？' + message , '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
              }).then(() => {
                const uuid = this.utils.createUUID();
                this.layout.openNewTab(
                    "入库信息",
                    "firm_main_recommend_detail",
                    "firm_main_recommend_detail",
                    uuid,
                    {
                      functionId: "firm_main_recommend_detail,"+uuid,
                      LawFirmChangeState: 'recommend',
                      ...this.utils.routeState.EDIT(this.mainData.id)
                    }
                );
              }).catch(() => {
                this.$message.info('已取消推荐!')
              })
            })
          } else {
            this.$message.info('您推荐的律师已被其他人推荐!')
          }
        })
      },
      addBlackList() {
        this.$confirm('您确定要拉黑该律所？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          taskApi.selectFunctionId({functionCode: 'firm_black_main'}).then(res=>{
            const row = this.mainData
            const functionId = res.data.data[0].ID
            const tabId = this.utils.createUUID()
            this.layout.openNewTab("黑名单信息",
                "design_page",
                "design_page",
                tabId,
                {
                  functionId: functionId,
                  entranceType: "FLOWABLE",
                  LawFirmChangeState: 'addBlackList',
                  lawFirmId: row.id,
                  view: 'old',
                  ...this.utils.routeState.EDIT(tabId)
                })
          }).catch(() => {
            this.$message.info('已取消!')
          })
        })
      },
      look(index, row) {
        this.layout.openNewTab("律师信息","firm_ledger_lawyer","firm_ledger_lawyer", this.utils.createUUID(),{
          id: this.mainData.id,
          lawyerFirm: this.mainData.lawyerFirm,
          ...this.utils.routeState.VIEW(row.id)})
      },
      lookAgent(index, row) {
        this.agentRow = {}
        this.agentRow = Object.assign({}, row)
        this.agentDialogVisible = true
      },
      isHeadquarter() {
        // const createOgnId = this.orgContext.currentOgnId
        //
        // if (createOgnId === '15033708970596')
        // {
          this.typeCode = '1'
          this.typeName = '推荐库'
        // }
        // else
        // {
        //   this.typeCode = '0'
        //   this.typeName = '资源库'
        // }
      },
      cancelAgent() {
        this.agentDialogVisible = false
      },
      selectionTypeChange(newVal) {
        this.agentRow.selectionTypeName = this.utils.getDicName(this.utils.selectionType_data, newVal)
        this.agentRow.lawFirmSelectCaseList = []
      },
      selectionTypeClear() {
        this.agentRow.selectionTypeId = null
      },
      choiceCaseClick() {
        this.caseDialogVisible = true
      },
      agentStageOnChange(val) {
        if (val && val.length > 0) {
          const k = []
          this.processData.forEach(item => {
            if (val.includes(item.code)) {
              k.push(item.name)
            }
          })
          this.agentRow.agentStageName = k.join('、')
        } else {
          this.agentRow.agentStageName = null
        }
      },
      agentSure() {
        this.$refs['agentRow'].validate((valid) => {
          if (valid) {
            this.agentRow.updateTime = new Date()
            if (this.agentRow.state === 'edit') {
              Object.assign(this.detailData.agentList[this.index], JSON.parse(JSON.stringify(this.agentRow)))
            } else {
              this.detailData.agentList.push(JSON.parse(JSON.stringify(this.agentRow)))
            }
            this.agentDialogVisible = false
          } else {
            return false
          }
        })
      },
      topLawFirm(val){
        const  firms = ['31110000E00017891P', '31110000E000169525', '31310000425097733Y', '31110000E00016813E',
          '31110000E00016266T', '31110000400834282L', '31110000E00017525U', '31110000E00018675X']

        return firms.indexOf(val) > -1
      },
    }
  }
</script>

<style scoped>
.el-icon-s-help {
  color: red;
}
.rowCol1 .el-form-item {
  margin-bottom: 0 !important;
}
</style>