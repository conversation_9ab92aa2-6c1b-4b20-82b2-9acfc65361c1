<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.klaw.dao.systemDao.SgTaskLogMapper">
  <resultMap id="BaseResultMap" type="com.klaw.entity.systemBean.SgTaskLog">
    <!--@mbg.generated-->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="TASK_ID" jdbcType="VARCHAR" property="taskId" />
    <result column="PROC_INST_ID" jdbcType="VARCHAR" property="procInstId" />
    <result column="CREATE_PSN_ID" jdbcType="VARCHAR" property="createPsnId" />
    <result column="CREATE_PSN_NAME" jdbcType="VARCHAR" property="createPsnName" />
    <result column="RECEIVE_PSN_ID" jdbcType="VARCHAR" property="receivePsnId" />
    <result column="RECEIVE_PSN_NAME" jdbcType="VARCHAR" property="receivePsnName" />
    <result column="CREATE_DATE" jdbcType="TIMESTAMP" property="createDate" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, TASK_ID, PROC_INST_ID, CREATE_PSN_ID, CREATE_PSN_NAME, RECEIVE_PSN_ID, RECEIVE_PSN_NAME, 
    CREATE_DATE
  </sql>
</mapper>