
9cc8a20ab873290bc2078e17445cd4c25db15e89	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.469.1754018536329.js\",\"contentHash\":\"1151802b0f7db5c97c25c691c3b3cf81\"}","integrity":"sha512-nwWukdzgzZ33VO/Pdr3bS8mVr8er0ULB77V5cMvZDKQ2AXvrPr5EQ/w0VHW6cq236mXIP2jmajUeMKcHmpCZpg==","time":1754018575957,"size":48702}