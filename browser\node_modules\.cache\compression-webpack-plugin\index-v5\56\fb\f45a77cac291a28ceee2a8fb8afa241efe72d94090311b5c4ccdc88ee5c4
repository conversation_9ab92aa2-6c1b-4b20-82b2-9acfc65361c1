
8791f2aadd4d651217e91436d5d244c1bdefab2e	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.269.1754018536329.js\",\"contentHash\":\"d55c88a20d5aae7eb04dd56acb36c452\"}","integrity":"sha512-2CRH2mAJgjgMMwTGYEsn7HufORlUgmNtEE+nVQ7JO8pTilwIe4naFmL5YMt0nWcmi0Ltzlh6Fp3vekcEo+wKlw==","time":1754018576007,"size":138403}