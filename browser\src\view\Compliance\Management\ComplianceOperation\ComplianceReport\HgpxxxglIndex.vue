<template>
	<el-container direction="vertical" class="container-manage-sg">
		<el-header>
			<el-card>
				<div>
					<el-input v-model="tableQuery.fuzzyValue" class="filter_input" placeholder="检索字段（培训主题）" clearable
						@keyup.enter.native="refreshData" @clear="refreshData">
						<el-popover slot="prepend" placement="bottom-start" width="1000" trigger="click">
							<el-form ref="queryForm" label-width="100px" size="mini">
								<el-row>
									<el-col :span="12">
										<el-form-item label="培训主题">
											<el-input v-model="tableQuery.trainingTopic" clearable
												placeholder="请输入..." />
										</el-form-item>
									</el-col>
									<el-col :span="12">
										<el-form-item label="培训分类">
											<el-select v-model="tableQuery.trainingCategory" clearable placeholder="请选择"
												style="width: 100%">
												<el-option key="1" value="dwzxzzthgxx" label="党委中心组专题合规学习"></el-option>
												<el-option key="2" value="glcpxxx" label="管理层（董监高）培训学习"></el-option>
												<el-option key="3" value="hgglypxxx" label="合规管理员培训学习"></el-option>
												<el-option key="4" value="xygrzpxxx" label="新员工入职培训学习"></el-option>
												<el-option key="5" value="qypsxpxxg" label="全员普适性培训宣贯"></el-option>
												<el-option key="6" value="qt" label="其他"></el-option>
											</el-select>
										</el-form-item>
									</el-col>
								</el-row>
								<el-button-group style="float: right">
									<el-button type="primary" size="mini" icon="el-icon-search"
										@click="search_">搜索</el-button>
									<el-button type="primary" size="mini" icon="el-icon-refresh-left"
										@click="empty_">重置</el-button>
								</el-button-group>
							</el-form>
							<el-button slot="reference" size="small" type="primary">高级检索</el-button>
						</el-popover>
						<el-button slot="append" icon="el-icon-search" @click="search_" />
					</el-input>
				</div>
			</el-card>
		</el-header>

		<el-main>
			<!-- 选择送审单位 -->

			<SimpleBoardIndex :title="'合规培训学习'" :isButton="true" @addBtn="add_">
				<template slot="button">
					<el-button class="normal-btn" size="mini" icon="fa fa-file-excel-o"
						@click="exportExcel">导出</el-button>
				</template>
				<el-table ref="table" v-loading="tableLoading" :data="tableData" size="mini" border
					:height="table_height" stripe fit highlight-current-row :show-overflow-tooltip="true" row-key="id"
					style="table-layout: fixed; width: 100%" @sort-change="tableSort" id="jobResponsibilitiesList">
					<el-table-column type="index" width="50" label="序号" align="center" />
					<el-table-column prop="trainingTopic" show-overflow-tooltip label="培训主题" min-width="250">
						<template slot-scope="scope">
							<div @click="view_(scope.$index, scope.row)" style="color: #409eff;cursor: pointer;">
								{{ scope.row.trainingTopic }}</div>
						</template>
					</el-table-column>
					<el-table-column prop="trainingCategory" show-overflow-tooltip label="培训分类" min-width="200">
						<template slot-scope="scope">
							<div>
								{{
									scope.row.trainingCategory == 'dwzxzzthgxx'
										? '党委中心组专题合规学习'
										: scope.row.trainingCategory == 'glcpxxx'
											? '管理层（董监高）培训学习'
											: scope.row.trainingCategory == 'hgglypxxx'
												? '合规管理员培训学习'
												: scope.row.trainingCategory == 'xygrzpxxx'
													? '新员工入职培训学习'
													: scope.row.trainingCategory == 'qypsxpxxg'
														? '全员普适性培训宣贯'
														: scope.row.trainingCategory == 'qt'
															? '其他'
															: ''
								}}
							</div>
						</template>
					</el-table-column>
					<el-table-column show-overflow-tooltip prop="trainingStartTime" label="培训开始时间" min-width="150" />
					<el-table-column show-overflow-tooltip prop="trainingEndTime" label="培训结束时间" min-width="150" />
					<el-table-column prop="trainingStatus" show-overflow-tooltip label="培训状态" min-width="100">
						<template slot-scope="scope">
							<el-tag v-if="scope.row.trainingStatus == '培训未开始'" type="danger">培训未开始</el-tag>
							<el-tag v-if="scope.row.trainingStatus == '培训进行中'" type="success">培训进行中</el-tag>
							<el-tag v-if="scope.row.trainingStatus == '培训已结束'" type="info">培训已结束</el-tag>
						</template>
					</el-table-column>
					<el-table-column prop="learningProgress" show-overflow-tooltip label="学习进度" min-width="250">
						<template slot-scope="scope">
							<div style="display: flex;">
								<div style="color:#DC5443">未开始 </div>
								<div>{{ scope.row.notStartedCount + '人；' }}</div>
								<div style="color:#e6a23c">学习中 </div>
								<div>{{ scope.row.learningInProgressCount + '人；' }}</div>
								<div style="color:#7FB554">已完成 </div>
								<div>{{ scope.row.completedCount + '人；' }}</div>
							</div>
						</template>
					</el-table-column>
					<el-table-column prop="createOgnName" show-overflow-tooltip label="发布组织" min-width="300" />
					<el-table-column prop="createTime" show-overflow-tooltip label="发布日期" min-width="150" />
					<el-table-column prop="createPsnName" show-overflow-tooltip label="发布人" min-width="100" />
				</el-table>
			</SimpleBoardIndex>
		</el-main>
		<el-footer>
			<!--分页工具栏-->
			<pagination :total="tableQuery.total" :page.sync="tableQuery.page" :limit.sync="tableQuery.limit"
				@pagination="refreshData" />
		</el-footer>
	</el-container>
</template>

<script>
// 组件
import pagination from '@/view/components/Pagination/PaginationIndex';
import TableTools from '@/view/components/TableTools/index';
import SimpleBoardIndex from '@/view/components/SimpleBoard/SimpleBoardIndex';
import orgTree from '@/view/components/OrgTree/OrgTree';
// vuex审查状态值
import { mapGetters } from 'vuex';
import XLSX from 'xlsx';
import FileSaver from 'file-saver';

// 接口api
import ComplianceTrainingApi from '@/api/ComplianceTraining/ComplianceTraining.js';
import taskApi from '@/api/_system/task';

export default {
	name: 'HgpxxxglIndex',
	inject: ['layout'],
	components: { pagination, TableTools, SimpleBoardIndex, orgTree },
	data() {
		return {
			isCheckedUser: false,
			showUser: false,
			deptOrgVisible: false,
			entrustedUnitOrgVisible: false,
			// is_Check: false,
			tableQuery: {
				page: 1,
				limit: 10,
				total: 0,
				fuzzyValue: '', // 模糊搜索值
				trainingTopic: null,
				trainingCategory: null,
			},
			table_height: '100%',
			tableData: [],
			userDialogVisible: false,
			orgDialogVisible: false,
			orgTreeDialog: false,
			zxcheckedData: [],
			orgVisible: false,
			tableLoading: false,
			ss: {
				data: this.tableData,
			},
			// tableQuery: {
			//   reportYear: '',
			// },
			yearOptions: Array.from({ length: 10 }, (_, i) => new Date().getFullYear() - i),
		};
	},
	computed: {
		...mapGetters(['orgContext', 'currentFunctionId']),
	},
	activated() {
		// 长连接页面第二次激活的时候,不会走created方法,会走此方法
		this.refreshData();
	},
	created() {
		this.refreshData();
	},

	mounted() {
		this.$nextTick(function () {
			this.table_height = window.innerHeight - this.$refs.table.$el.offsetTop - 84 - 45;

			// 监听窗口大小变化
			const self = this;
			window.onresize = function () {
				self.table_height = window.innerHeight - self.$refs.table.$el.offsetTop - 84 - 45;
			};
		});
	},
	methods: {
		isEdit(row) {
			return row.dataStateCode === this.utils.dataState_BPM.SAVE.code || row.dataStateCode === this.utils.dataState_BPM.BACK.code;
		},
		isDelete(row) {
			return row.dataStateCode === this.utils.dataState_BPM.SAVE.code;
		},
		exportExcel() {
			//   // 获取原始表格元素
			//   const originalTable = document.querySelector('#jobResponsibilitiesList');
			//   const rows = originalTable.querySelectorAll('tr');
			//   const newTable = document.createElement('table');  // 创建临时表格并复制表头，同时移除最后一列
			//   const headerRow = rows[0].cloneNode(true);  // 假设最后一列是操作列，移除最后一列的表头单元格
			//   headerRow.deleteCell(headerRow.cells.length - 2);
			//   newTable.appendChild(headerRow);  // 复制数据行，同时移除操作列
			//   for (let i = 1; i < rows.length/2; i++) {
			//   const newRow = rows[i].cloneNode(true);    // 移除操作按钮列
			//   const actionCells = newRow.querySelectorAll('.el-button');
			//   actionCells.forEach(cell => cell.remove());    // 移除最后一列的数据单元格
			//   newRow.deleteCell(newRow.cells.length - 1);
			//   newTable.appendChild(newRow);
			//   }

			//   // 使用 SheetJS 从临时表格生成工作簿
			//   const wb = XLSX.utils.table_to_book(newTable, { raw: true });
			//   const wbout = XLSX.write(wb, {
			//     bookType: 'xlsx',
			//     bookSST: true,
			//     type: 'array',
			//   });
			//   try {
			//     FileSaver.saveAs(new Blob([wbout], {type: 'application/octet-stream'}), '合规培训学习（管理端）.xlsx');
			//   } catch (e) {
			//     if (typeof console !== 'undefined') {
			//       console.error(e);
			//     }
			//   }
			//   return wbout;
			var xlsxParam = { raw: true }; //转换成excel时，使用原始的格式
			var wb = XLSX.utils.table_to_book(document.querySelector('#jobResponsibilitiesList'), xlsxParam);
			var wbout = XLSX.write(wb, {
				bookType: 'xlsx',
				bookSST: true,
				type: 'array',
			});
			try {
				FileSaver.saveAs(new Blob([wbout], { type: 'application/octet-stream;charset=utf-8' }), '合规培训学习（管理端）.xlsx');
			} catch (e) {
				if (typeof console !== 'undefined') console.log(e, wbout);
			}
			this.tableQuery.limit = 10;
			this.refreshData();
			return wbout;
		},
		// 刷新数据
		refreshData() {
			// 赋值当前人组织全路径
			this.tableQuery.functionCode = this.currentFunctionId.functionCode;
			this.tableQuery.orgId = this.orgContext.currentOrgId;
			this.tableQuery.currentPsnFullId = this.orgContext.currentPsnFullId;

			// 主要用于删除,当前页只有一条数据的时候,删除需要回到上一页
			if (this.tableData.length === 0 && this.tableQuery.page > 1) {
				this.tableQuery.page--;
			}
			ComplianceTrainingApi
				.query(this.tableQuery)
				.then((response) => {
					let rows = response.data.data.records;
					this.tableData = rows;
					this.ss.data = rows;
					this.tableQuery.total = response.data.data.total;
					this.tableLoading = false;
				})
				.catch({});
		},
		// 点击打开查找机构弹窗
		chooseNoticeDeptClick() {
			this.deptOrgVisible = true;
		},
		// 关闭审查机构弹窗
		deptOrgCancel() {
			this.deptOrgVisible = false;
		},
		// 点击打开送审人弹窗
		choiceEntrustedUnitClick() {
			this.entrustedUnitOrgVisible = true;
		},
		// 点击关闭送审人弹窗
		entrustedUnitOrgCancel() {
			this.entrustedUnitOrgVisible = false;
		},
		entrustedUnitSure() {
			const res = this.zxcheckedData[0];
			console.log(res, 'res');
			this.tableQuery.submitter = res.name;
			this.entrustedUnitOrgVisible = false;
		},
		choiceNoticeDeptSure() {
			let c = '';
			let cid = '';
			this.zxcheckedData.forEach((item) => {
				if (c.length === 0) {
					c = c + item.name;
					cid = cid + item.unitId;
				} else {
					c = c + ',' + item.name;
					cid = cid + ',' + item.unitId;
				}
			});
			this.tableQuery.submittingUnit = c;
			this.deptOrgVisible = false;
		},
		add_(event) {
			const tabId = this.utils.createUUID();
			this.layout.openNewTab('合规培训', 'hgpxxxgl_main_detail', 'hgpxxxgl_main_detail', tabId, {
				functionId: 'hgpxxxgl_main_detail,' + tabId,
				...this.utils.routeState.NEW(tabId),
			});
		},
		// 编辑
		edit_(index, row) {
			console.log(row, 'row');
			const tabId = this.utils.createUUID();
			const name =
				row.reviewCategory == 'HG-SCLX-ZDSX'
					? '重大事项合规审查'
					: row.reviewCategory == 'HG-SCLX-HTHG'
						? '合同合规审查'
						: row.reviewCategory == 'HG-SCLX-QSSX'
							? '请示事项合规审查'
							: row.reviewCategory == 'HG-SCLX-ZDHG'
								? '制度合规审查'
								: row.reviewCategory == 'HG-SCLX-RCJY'
									? '日常经营类合规论证'
									: row.reviewCategory == 'HG-SCLX-HFHG'
										? '合法合规审查意见'
										: '';
			this.layout.openNewTab(name, 'hgsc_main_detail', 'hgsc_main_detail', row.id, {
				functionId: 'hgsc_main_detail,' + row.id,
				...this.utils.routeState.EDIT(row.id),
				view: 'old',
				reviewCategory: row.reviewCategory,
			});
		},
		// 删除
		delete_(index, row) {
			this.$confirm('此操作将永久删除该数据及相关数据, 是否继续?', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning',
			})
				.then(() => {
					new Promise((resolve, reject) => {
						complianceReviewApi
							.deletebyid({
								id: row.id,
							})
							.then((response) => {
								resolve(response);
							});
					}).then((value) => {
						this.tableData.splice(index, 1);
						this.$message.success('删除成功!');
					});
				})
				.catch(() => {
					this.$message({
						type: 'info',
						message: '已取消删除',
					});
				});
		},
		// 查看
		view_(index, row) {
			const tabId = this.utils.createUUID();
			this.layout.openNewTab('合规培训学习', 'hgpxxxgl_detail', 'hgpxxxgl_detail', tabId, {
				functionId: 'hgpxxxgl_detail,' + tabId,
				...this.utils.routeState.VIEW(row.id),
				trainingTopic: row.trainingTopic,
				trainingCategory: row.trainingCategory,
				trainingStartTime: row.trainingStartTime,
				trainingEndTime: row.trainingEndTime,
				trainingDirector: row.trainingDirector,
				notStartedCount: row.notStartedCount,
				learningInProgressCount: row.learningInProgressCount,
				completedCount: row.completedCount,
				likeCount: row.likeCount,
				dislikeCount: row.dislikeCount,
				id: row.id,
				courseDetailsList: row.courseDetailsList ? JSON.stringify(row.courseDetailsList) : [],
				reviewsList: row.reviewsList ? JSON.stringify(row.reviewsList) : [],
			});
		},
		rowDblclick(row, column, event) {
			if (row.dataStateCode === this.utils.dataState_BPM.SAVE.code) {
				const tabId = this.utils.createUUID();
				this.layout.openNewTab('合规报告审批信息', 'hgbg_main_detail', 'hgbg_main_detail', tabId, {
					functionId: 'contract_approval_main_detail,' + tabId,
					...this.utils.routeState.VIEW(row.id),
				});
			} else {
				taskApi.selectTaskId({ businessKey: row.id, isView: 'true' }).then((res) => {
					const functionId = res.data.data[0].ID;
					const tabId = this.utils.createUUID();
					this.layout.openNewTab('合规报告审批信息', 'design_page', 'design_page', tabId, {
						processInstanceId: res.data.data[0].PID, //流程实例
						taskId: res.data.data[0].ID, //任务ID
						businessKey: row.id, //业务数据ID
						functionId: functionId, //不传也可以，只有首环节需要传，实例启动就可以不传了
						entranceType: 'FLOWABLE',
						type: 'haveDealt',
						channel: 'business', //查看渠道，默认就传这个值即可，表示是从业务功能传过来的，首环节提交后过滤掉撤回按钮
						view: 'new',
					});
				});
			}
		},
		tableSort(column, prop, order) {
			this.tableQuery.sortName = column.prop;
			this.tableQuery.order = column.order === 'ascending';
			this.refreshData();
		},
		// 点击搜索按钮事件,回到第一页,重新刷新数据
		search_: function () {
			this.tableQuery.page = 1;
			this.refreshData();
		},
		// 点击清空按钮事件,清空查询条件属性,回到第一页,重新刷新数据
		empty_() {
			// 清空搜索条件
			this.tableQuery = {
				page: 1,
				limit: 10,
				total: 0,
				riskName: '',
				caseCode: '',
				expectedRiskLevel: '',
				fuzzyValue: '',
			};
			this.refreshData();
		},
		// 点击刷新按钮事件
		refresh_() {
			this.tableQuery.sortName = null;
			this.tableQuery.order = null;
			this.empty_();
		},
		showOrgTreeDialog() {
			this.orgDialogVisible = true;
		},
		showUserTreeDialog() {
			this.isCheckedUser = true;
			this.showUser = true;
			this.userDialogVisible = true;
		},
		cancel() {
			this.userDialogVisible = false;
		},
		choiceDeptSure() {
			let selectedUnits = this.zxcheckedData.map((item) => item.name).join(', ');
			this.tableQuery.reportingUnit = selectedUnits;
			this.userDialogVisible = false;
		},
	},
};
</script>

<style scoped>
.el-table__fixed-body-wrapper {
	top: 50px !important;
}
</style>
