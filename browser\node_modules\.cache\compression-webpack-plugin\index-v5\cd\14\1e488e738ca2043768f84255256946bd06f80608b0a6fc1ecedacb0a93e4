
3028f7cf6e5a542d4f96bbb49bee427de6c73062	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.334.1754018536329.js\",\"contentHash\":\"538ca92a1a4582a378b014b4c9fe00b2\"}","integrity":"sha512-Lhm/q0Uriju6GpbX0m4YJfeZnETY/jCvBpCHAWiO04+5xMS7fxgr1lx4OSfpWzwJ8dtg4Wwbeo4Czp4dQFnpuA==","time":1754018575974,"size":93111}