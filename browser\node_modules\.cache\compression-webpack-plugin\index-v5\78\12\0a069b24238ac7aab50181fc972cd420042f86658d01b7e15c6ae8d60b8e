
2e131c27aad6e92e667ac22e5953deee320e94e9	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.360.1754018536329.js\",\"contentHash\":\"9118a9914bea59dd1f39e97ff9db3f71\"}","integrity":"sha512-xO0m1DtRKWDyKOmcMmQpT65kxsqLyEtMADH0ww7rVeORjAVHzpLU4TBVnzoNog42OBrAOjUwtLidapAVxCf9/w==","time":1754018575975,"size":100671}