<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.klaw.dao.caseDao.SgCaseReportTaskMapper">
  <resultMap id="BaseResultMap" type="com.klaw.entity.caseBean.CaseReportTask">
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="YEAR" jdbcType="VARCHAR" property="year" />
    <result column="QUARTER" jdbcType="VARCHAR" property="quarter" />
    <result column="ATTACHMENT" jdbcType="CLOB" property="attachment" />
    <result column="CREATE_OGN_ID" jdbcType="VARCHAR" property="createOgnId" />
    <result column="CREATE_OGN_NAME" jdbcType="VARCHAR" property="createOgnName" />
    <result column="CREATE_DEPT_ID" jdbcType="VARCHAR" property="createDeptId" />
    <result column="CREATE_DEPT_NAME" jdbcType="VARCHAR" property="createDeptName" />
    <result column="CREATE_GROUP_ID" jdbcType="VARCHAR" property="createGroupId" />
    <result column="CREATE_GROUP_NAME" jdbcType="VARCHAR" property="createGroupName" />
    <result column="CREATE_PSN_ID" jdbcType="VARCHAR" property="createPsnId" />
    <result column="CREATE_PSN_NAME" jdbcType="VARCHAR" property="createPsnName" />
    <result column="CREATE_ORG_ID" jdbcType="VARCHAR" property="createOrgId" />
    <result column="CREATE_ORG_NAME" jdbcType="VARCHAR" property="createOrgName" />
    <result column="CREATE_PSN_FULL_ID" jdbcType="VARCHAR" property="createPsnFullId" />
    <result column="CREATE_PSN_FULL_NAME" jdbcType="VARCHAR" property="createPsnFullName" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="DATA_STATE" jdbcType="VARCHAR" property="dataState" />
    <result column="DATA_STATE_CODE" jdbcType="DECIMAL" property="dataStateCode" />
  </resultMap>
  <sql id="Base_Column_List">
    ID, "YEAR", QUARTER, ATTACHMENT, CREATE_OGN_ID, CREATE_OGN_NAME,
    CREATE_DEPT_ID, CREATE_DEPT_NAME, CREATE_GROUP_ID, CREATE_GROUP_NAME, CREATE_PSN_ID, 
    CREATE_PSN_NAME, CREATE_ORG_ID, CREATE_ORG_NAME, CREATE_PSN_FULL_ID, CREATE_PSN_FULL_NAME, 
    CREATE_TIME, UPDATE_TIME, DATA_STATE, DATA_STATE_CODE
  </sql>


  <resultMap id="BaseResultMap2" type="com.klaw.entity.caseBean.CaseRecord">
      <id column="ID" jdbcType="VARCHAR" property="id" />
      <result column="CASE_NUMBER" jdbcType="VARCHAR" property="caseNumber" />
      <result column="CASE_TIME" jdbcType="TIMESTAMP" property="caseTime" />
      <result column="CAUSE_NAME" jdbcType="VARCHAR" property="causeName" />
      <result column="CAUSE_ID" jdbcType="VARCHAR" property="causeId" />
      <result column="CASE_TYPE" jdbcType="VARCHAR" property="caseType" />
      <result column="CASE_TYPE_ID" jdbcType="VARCHAR" property="caseTypeId" />
      <result column="OUR_POSITION" jdbcType="VARCHAR" property="ourPosition" />
      <result column="VENUE_IS_OUT" jdbcType="DECIMAL" property="venueIsOut" />
      <result column="VENUE_PROVINCE" jdbcType="VARCHAR" property="venueProvince" />
      <result column="VENUE_CITY" jdbcType="VARCHAR" property="venueCity" />
      <result column="VENUE_REGION" jdbcType="VARCHAR" property="venueRegion" />
      <result column="VENUE_ADDRESS" jdbcType="VARCHAR" property="venueAddress" />
      <result column="CASE_MONEY" jdbcType="DECIMAL" property="caseMoney" />
      <result column="WHETHER_MAJOR" jdbcType="DECIMAL" property="whetherMajor" />
      <result column="COURT" jdbcType="VARCHAR" property="court" />
      <result column="COURT_ID" jdbcType="VARCHAR" property="courtId" />
      <result column="TRIAL" jdbcType="VARCHAR" property="trial" />
      <result column="TRIAL_ID" jdbcType="VARCHAR" property="trialId" />
      <result column="JUDGE_MAIN" jdbcType="VARCHAR" property="judgeMain" />
      <result column="JUDGE_MAIN_ID" jdbcType="VARCHAR" property="judgeMainId" />
      <result column="JUDGE_MAIN_PHONE" jdbcType="VARCHAR" property="judgeMainPhone" />
      <result column="JUDGE_VICE" jdbcType="VARCHAR" property="judgeVice" />
      <result column="PROOF_DATE" jdbcType="TIMESTAMP" property="proofDate" />
      <result column="REPLY_DATE" jdbcType="TIMESTAMP" property="replyDate" />
      <result column="DATA_STATE" jdbcType="VARCHAR" property="dataState" />
      <result column="DATA_STATE_CODE" jdbcType="DECIMAL" property="dataStateCode" />
      <result column="CASE_PROCESS" jdbcType="LONGVARCHAR" property="caseProcess" />
      <result column="CASE_PROCESS_IDS" jdbcType="LONGVARCHAR" property="caseProcessIds" />
      <result column="CREATE_OGN_ID" jdbcType="VARCHAR" property="createOgnId" />
      <result column="CREATE_OGN_NAME" jdbcType="VARCHAR" property="createOgnName" />
      <result column="CREATE_DEPT_ID" jdbcType="VARCHAR" property="createDeptId" />
      <result column="CREATE_DEPT_NAME" jdbcType="VARCHAR" property="createDeptName" />
      <result column="CREATE_GROUP_ID" jdbcType="VARCHAR" property="createGroupId" />
      <result column="CREATE_GROUP_NAME" jdbcType="VARCHAR" property="createGroupName" />
      <result column="CREATE_ORG_ID" jdbcType="VARCHAR" property="createOrgId" />
      <result column="CREATE_ORG_NAME" jdbcType="VARCHAR" property="createOrgName" />
      <result column="CREATE_PSN_ID" jdbcType="VARCHAR" property="createPsnId" />
      <result column="CREATE_PSN_NAME" jdbcType="VARCHAR" property="createPsnName" />
      <result column="CREATE_PSN_FULL_NAME" jdbcType="LONGVARCHAR" property="createPsnFullName" />
      <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
      <result column="CASE_DETAILS" jdbcType="CLOB" property="caseDetails" />
      <result column="FILES" jdbcType="CLOB" property="files" />
      <result column="JUDGE_VICE_PHONE" jdbcType="VARCHAR" property="judgeVicePhone" />
      <result column="CASE_ACCEPT_MONEY" jdbcType="DECIMAL" property="caseAcceptMoney" />
      <result column="CASE_ACCEPT_MONEY_OUR" jdbcType="DECIMAL" property="caseAcceptMoneyOur" />
      <result column="HANDLE_SITUATION" jdbcType="VARCHAR" property="handleSituation" />
      <result column="HANDLE_SITUATION_ID" jdbcType="VARCHAR" property="handleSituationId" />
      <result column="WHETHER_FORCE_EXECUTE" jdbcType="DECIMAL" property="whetherForceExecute" />
      <result column="FORCE_EXECUTE" jdbcType="LONGVARCHAR" property="forceExecute" />
      <result column="FORCE_EXECUTE_ID" jdbcType="LONGVARCHAR" property="forceExecuteId" />
      <result column="RECEIVE_MONEY" jdbcType="DECIMAL" property="receiveMoney" />
      <result column="SEND_MONEY" jdbcType="DECIMAL" property="sendMoney" />
      <result column="CASE_CURRENT_PROCESS" jdbcType="VARCHAR" property="caseCurrentProcess" />
      <result column="CASE_CURRENT_PROCESS_ID" jdbcType="VARCHAR" property="caseCurrentProcessId" />
      <result column="CASE_PROCESS_TYPE" jdbcType="VARCHAR" property="caseProcessType" />
      <result column="CASE_PROCESS_TYPE_CODE" jdbcType="VARCHAR" property="caseProcessTypeCode" />
      <result column="PARENT_ID" jdbcType="VARCHAR" property="parentId" />
      <result column="CASE_KIND" jdbcType="VARCHAR" property="caseKind" />
      <result column="CASE_KIND_CODE" jdbcType="VARCHAR" property="caseKindCode" />
      <result column="CASE_ALIAS" jdbcType="VARCHAR" property="caseAlias" />
      <result column="PREPOSITION_ID" jdbcType="VARCHAR" property="prepositionId" />
      <result column="INTERFILE" jdbcType="DECIMAL" property="interfile" />
      <result column="PREPOSITION_TYPE" jdbcType="VARCHAR" property="prepositionType" />
      <result column="BELONG_PLATE_ID" jdbcType="VARCHAR" property="belongPlateId" />
      <result column="BELONG_PLATE" jdbcType="VARCHAR" property="belongPlate" />
      <result column="WHETHER_EASY" jdbcType="DECIMAL" property="whetherEasy" />
      <result column="RESULT_ADDRESS" jdbcType="VARCHAR" property="resultAddress" />
      <result column="RESULT_ADDRESS_ID" jdbcType="VARCHAR" property="resultAddressId" />
      <result column="RESULT" jdbcType="VARCHAR" property="result" />
      <result column="RESULT_ID" jdbcType="VARCHAR" property="resultId" />
      <result column="ACTUAL_RECEIVE_MONEY" jdbcType="DECIMAL" property="actualReceiveMoney" />
      <result column="ACTUAL_SEND_MONEY" jdbcType="DECIMAL" property="actualSendMoney" />
      <result column="REMARKS" jdbcType="CLOB" property="remarks" />
      <result column="WHETHER_CONTINUE" jdbcType="DECIMAL" property="whetherContinue" />
      <result column="WHETHER_INCLUDE" jdbcType="DECIMAL" property="whetherInclude" />
      <result column="CONTINUE_END_DATE" jdbcType="TIMESTAMP" property="continueEndDate" />
      <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
      <result column="CASE_CODE" jdbcType="VARCHAR" property="caseCode" />
      <result column="WHETHER_BLANK" jdbcType="DECIMAL" property="whetherBlank" />
      <result column="WIN_ANALYSIS" jdbcType="LONGVARCHAR" property="winAnalysis" />
      <result column="EXP_SUMMARY" jdbcType="LONGVARCHAR" property="expSummary" />
      <result column="ACCOUNT_DES" jdbcType="LONGVARCHAR" property="accountDes" />
      <result column="HAS_CHILDREN" jdbcType="DECIMAL" property="hasChildren" />
      <result column="CAUSE_OF_IN_ID" jdbcType="VARCHAR" property="causeOfInId" />
      <result column="CAUSE_OF_IN" jdbcType="VARCHAR" property="causeOfIn" />
      <result column="JUDGE_PERSONS" jdbcType="CLOB" property="judgePersons" />
      <result column="BELONG_PROJECT" jdbcType="VARCHAR" property="belongProject" />
      <result column="BELONG_PLATE_FULL_ID" jdbcType="LONGVARCHAR" property="belongPlateFullId" />
      <result column="SORT" jdbcType="DECIMAL" property="sort" />
      <result column="CASE_NAME" jdbcType="VARCHAR" property="caseName" />
      <result column="CREATE_PSN_FULL_ID" jdbcType="VARCHAR" property="createPsnFullId" />
      <result column="CASE_TYPE_DES" jdbcType="VARCHAR" property="caseTypeDes" />
      <result column="PROJECT_NAME" jdbcType="VARCHAR" property="projectName" />
      <result column="OGN_PACKAGE" jdbcType="VARCHAR" property="ognPackage" />
      <result column="OGN_PACKAGE_ID" jdbcType="VARCHAR" property="ognPackageId" />
      <result column="GROUP_PACKAGE" jdbcType="VARCHAR" property="groupPackage" />
      <result column="GROUP_PACKAGE_ID" jdbcType="VARCHAR" property="groupPackageId" />
      <result column="INVOLVED_LEVEL" jdbcType="VARCHAR" property="involvedLevel" />
      <result column="INVOLVED_LEVEL_ID" jdbcType="VARCHAR" property="involvedLevelId" />
      <result column="UNIT_TYPE" jdbcType="VARCHAR" property="unitType" />
      <result column="UNIT_TYPE_ID" jdbcType="VARCHAR" property="unitTypeId" />
      <result column="INVOLVED_OGN_LEVEL" jdbcType="VARCHAR" property="involvedOgnLevel" />
      <result column="INVOLVED_OGN_LEVEL_ID" jdbcType="VARCHAR" property="involvedOgnLevelId" />
      <result column="WHETHER1" jdbcType="DECIMAL" property="whether1" />
      <result column="WHETHER2" jdbcType="DECIMAL" property="whether2" />
      <result column="WHETHER3" jdbcType="DECIMAL" property="whether3" />
      <result column="WHETHER4" jdbcType="DECIMAL" property="whether4" />
      <result column="WHETHER5" jdbcType="DECIMAL" property="whether5" />
      <result column="WHETHER6" jdbcType="DECIMAL" property="whether6" />
      <result column="WHETHER7" jdbcType="DECIMAL" property="whether7" />
      <result column="WHETHER8" jdbcType="DECIMAL" property="whether8" />
      <result column="WHETHER9" jdbcType="DECIMAL" property="whether9" />
      <result column="WHETHER10" jdbcType="DECIMAL" property="whether10" />
      <result column="WHETHER11" jdbcType="DECIMAL" property="whether11" />
      <result column="WHETHER12" jdbcType="DECIMAL" property="whether12" />
      <result column="WHETHER13" jdbcType="DECIMAL" property="whether13" />
      <result column="WHETHER14" jdbcType="DECIMAL" property="whether14" />
      <result column="WHETHER15" jdbcType="DECIMAL" property="whether15" />
      <result column="WHETHER16" jdbcType="DECIMAL" property="whether16" />
      <result column="WHETHER17" jdbcType="DECIMAL" property="whether17" />
      <result column="WHETHER18" jdbcType="DECIMAL" property="whether18" />
      <result column="WHETHER19" jdbcType="DECIMAL" property="whether19" />
      <result column="CASE_REASON" jdbcType="VARCHAR" property="caseReason" />
      <result column="CASE_REASON_ID" jdbcType="VARCHAR" property="caseReasonId" />
      <result column="RISK_LEVEL" jdbcType="VARCHAR" property="riskLevel" />
      <result column="RISK_LEVEL_ID" jdbcType="VARCHAR" property="riskLevelId" />
      <result column="WHETHER_GROUP" jdbcType="DECIMAL" property="whetherGroup" />
      <result column="GROUP_NAME" jdbcType="VARCHAR" property="groupName" />
      <result column="GROUP_ID" jdbcType="VARCHAR" property="groupId" />
      <result column="SUSPECTED_CHARGES" jdbcType="VARCHAR" property="suspectedCharges" />
      <result column="GREAT_REASON" jdbcType="CLOB" property="greatReason" />
      <result column="GREAT_PLAN" jdbcType="CLOB" property="greatPlan" />
      <result column="END_REPORT" jdbcType="CLOB" property="endReport" />
      <result column="CAUSE_ECONOMIC_LOSSES" jdbcType="DECIMAL" property="causeEconomicLosses" />
      <result column="AVOID_ECONOMIC_LOSSES" jdbcType="DECIMAL" property="avoidEconomicLosses" />
      <result column="ACTUAL_SEND_OTHER" jdbcType="DECIMAL" property="actualSendOther" />
      <result column="ACTUAL_RECEIVE_OTHER" jdbcType="DECIMAL" property="actualReceiveOther" />
      <result column="WHETHER_SUBMIT_IDEA" jdbcType="DECIMAL" property="whetherSubmitIdea" />
      <result column="WHETHER_REVIEW" jdbcType="DECIMAL" property="whetherReview" />
      <result column="START_TIME" jdbcType="TIMESTAMP" property="startTime" />
      <result column="END_TIME" jdbcType="TIMESTAMP" property="endTime" />
      <result column="SUPERVISE_NUMBER" jdbcType="DECIMAL" property="superviseNumber" />
      <result column="CAIPAN" jdbcType="DECIMAL" property="caipan" />
      <result column="LAJG" jdbcType="VARCHAR" property="lajg" />
      <result column="APPLY_PSN_NAME" jdbcType="VARCHAR" property="applyPsnName" />
      <result column="CASE_END" jdbcType="TIMESTAMP" property="caseEnd" />
  </resultMap>

  <select id="queryCaseList" resultMap="BaseResultMap2">
      select
      m.ID,  m.CASE_NUMBER,  m.CASE_TIME,  m.CAUSE_NAME,  m.CAUSE_ID,  m.CASE_TYPE,  m.CASE_TYPE_ID,  m.OUR_POSITION,
      m.VENUE_IS_OUT,  m.VENUE_PROVINCE,  m.VENUE_CITY,  m.VENUE_REGION,  m.VENUE_ADDRESS,  m.CASE_MONEY,
      m.WHETHER_MAJOR,  m.COURT,  m.COURT_ID,  m.TRIAL,  m.TRIAL_ID,  m.JUDGE_MAIN,  m.JUDGE_MAIN_ID,  m.JUDGE_MAIN_PHONE,
      m.JUDGE_VICE,  m.PROOF_DATE,  m.REPLY_DATE,  m.DATA_STATE,  m.DATA_STATE_CODE,  m.CASE_PROCESS,  m.CASE_PROCESS_IDS,
      m.CREATE_OGN_ID,  m.CREATE_OGN_NAME,  m.CREATE_DEPT_ID,  m.CREATE_DEPT_NAME,  m.CREATE_GROUP_ID,
      m.CREATE_GROUP_NAME,  m.CREATE_ORG_ID,  m.CREATE_ORG_NAME,  m.CREATE_PSN_ID,  m.CREATE_PSN_NAME,
      m.CREATE_PSN_FULL_NAME,  m.CREATE_TIME,  m.CASE_DETAILS,  m.FILES,  m.JUDGE_VICE_PHONE,  m.CASE_ACCEPT_MONEY,
      m.CASE_ACCEPT_MONEY_OUR,  m.HANDLE_SITUATION,  m.HANDLE_SITUATION_ID,  m.WHETHER_FORCE_EXECUTE,
      m.FORCE_EXECUTE,  m.FORCE_EXECUTE_ID,  m.RECEIVE_MONEY,  m.SEND_MONEY,  m.CASE_CURRENT_PROCESS,
      m.CASE_CURRENT_PROCESS_ID,  m.CASE_PROCESS_TYPE,  m.CASE_PROCESS_TYPE_CODE,  m.PARENT_ID,  m.CASE_KIND,
      m.CASE_KIND_CODE,  m.CASE_ALIAS,  m.PREPOSITION_ID,  m.INTERFILE,  m.PREPOSITION_TYPE,  m.BELONG_PLATE_ID,
      m.BELONG_PLATE,  m.WHETHER_EASY,  m. RESULT_ADDRESS,  m.RESULT_ADDRESS_ID,
      m.RESULT,  m.RESULT_ID,  m.ACTUAL_RECEIVE_MONEY,  m.ACTUAL_SEND_MONEY,  m.REMARKS,  m.WHETHER_CONTINUE,
      m.WHETHER_INCLUDE,  m.CONTINUE_END_DATE,  m.UPDATE_TIME,  m.CASE_CODE,  m.WHETHER_BLANK,
      m.WIN_ANALYSIS,  m.EXP_SUMMARY,  m.ACCOUNT_DES,  m.HAS_CHILDREN,  m.CAUSE_OF_IN_ID,  m.CAUSE_OF_IN,
      m.JUDGE_PERSONS,  m.BELONG_PROJECT,  m.BELONG_PLATE_FULL_ID,  m.SORT,  m.CASE_NAME,
      m.CREATE_PSN_FULL_ID,  m.CASE_TYPE_DES,  m.PROJECT_NAME,  m.OGN_PACKAGE,  m.OGN_PACKAGE_ID,  m.GROUP_PACKAGE,
      m.GROUP_PACKAGE_ID,  m.INVOLVED_LEVEL,  m.INVOLVED_LEVEL_ID,  m.UNIT_TYPE,  m.UNIT_TYPE_ID,  m.INVOLVED_OGN_LEVEL,
      m.INVOLVED_OGN_LEVEL_ID,  m.WHETHER1,  m.WHETHER2,  m.WHETHER3,  m.WHETHER4,  m.WHETHER5,  m.WHETHER6,
      m.WHETHER7,  m.WHETHER8,  m.WHETHER9,  m.WHETHER10,  m.WHETHER11,  m.WHETHER12,  m.WHETHER13,  m.WHETHER14,
      m.WHETHER15,  m.WHETHER16,  m.WHETHER17,  m.WHETHER18,  m.WHETHER19,  m.CASE_REASON,  m.CASE_REASON_ID,
      m.RISK_LEVEL,  m.RISK_LEVEL_ID,  m.WHETHER_GROUP,  m.GROUP_NAME,  m.GROUP_ID,  m.SUSPECTED_CHARGES,
      m.GREAT_REASON,  m.GREAT_PLAN,  m.END_REPORT,  m.CAUSE_ECONOMIC_LOSSES,  m.AVOID_ECONOMIC_LOSSES,
      m.ACTUAL_SEND_OTHER,  m.ACTUAL_RECEIVE_OTHER,  m.WHETHER_SUBMIT_IDEA,  m.WHETHER_REVIEW,  m.START_TIME,
      m.END_TIME,  m.SUPERVISE_NUMBER,  m.CAIPAN,  m.LAJG,  m.APPLY_PSN_NAME,  m.CASE_END
      from
        sg_case_records m
      <where>
          m.id not in (
              select id from (
                  select m.id, m.CASE_NAME,
                  case when d.name is null then '无' else d.name end as tagName
                  from sg_case_records m left join sg_tag d on m.id = d.data_id
                  <where>
                      ${ew.sqlSegment}
                  </where>
              )  A
              where tagName ='一审开庭前撤诉' or tagName = '并案处理' or tagName = '重复数据'
          )
          and
          ${ew.sqlSegment}
      </where>
  </select>


    <select id="queryCaseList2" resultMap="BaseResultMap2">
        select
        m.ID,  m.CASE_NUMBER,  m.CASE_TIME,  m.CAUSE_NAME,  m.CAUSE_ID,  m.CASE_TYPE,  m.CASE_TYPE_ID,  m.OUR_POSITION,
        m.VENUE_IS_OUT,  m.VENUE_PROVINCE,  m.VENUE_CITY,  m.VENUE_REGION,  m.VENUE_ADDRESS,  m.CASE_MONEY,
        m.WHETHER_MAJOR,  m.COURT,  m.COURT_ID,  m.TRIAL,  m.TRIAL_ID,  m.JUDGE_MAIN,  m.JUDGE_MAIN_ID,  m.JUDGE_MAIN_PHONE,
        m.JUDGE_VICE,  m.PROOF_DATE,  m.REPLY_DATE,  m.DATA_STATE,  m.DATA_STATE_CODE,  m.CASE_PROCESS,  m.CASE_PROCESS_IDS,
        m.CREATE_OGN_ID,  m.CREATE_OGN_NAME,  m.CREATE_DEPT_ID,  m.CREATE_DEPT_NAME,  m.CREATE_GROUP_ID,
        m.CREATE_GROUP_NAME,  m.CREATE_ORG_ID,  m.CREATE_ORG_NAME,  m.CREATE_PSN_ID,  m.CREATE_PSN_NAME,
        m.CREATE_PSN_FULL_NAME,  m.CREATE_TIME,  m.CASE_DETAILS,  m.FILES,  m.JUDGE_VICE_PHONE,  m.CASE_ACCEPT_MONEY,
        m.CASE_ACCEPT_MONEY_OUR,  m.HANDLE_SITUATION,  m.HANDLE_SITUATION_ID,  m.WHETHER_FORCE_EXECUTE,
        m.FORCE_EXECUTE,  m.FORCE_EXECUTE_ID,  m.RECEIVE_MONEY,  m.SEND_MONEY,  m.CASE_CURRENT_PROCESS,
        m.CASE_CURRENT_PROCESS_ID,  m.CASE_PROCESS_TYPE,  m.CASE_PROCESS_TYPE_CODE,  m.PARENT_ID,  m.CASE_KIND,
        m.CASE_KIND_CODE,  m.CASE_ALIAS,  m.PREPOSITION_ID,  m.INTERFILE,  m.PREPOSITION_TYPE,  m.BELONG_PLATE_ID,
        m.BELONG_PLATE,  m.WHETHER_EASY,  m. RESULT_ADDRESS,  m.RESULT_ADDRESS_ID,
        m.RESULT,  m.RESULT_ID,  m.ACTUAL_RECEIVE_MONEY,  m.ACTUAL_SEND_MONEY,  m.REMARKS,  m.WHETHER_CONTINUE,
        m.WHETHER_INCLUDE,  m.CONTINUE_END_DATE,  m.UPDATE_TIME,  m.CASE_CODE,  m.WHETHER_BLANK,
        m.WIN_ANALYSIS,  m.EXP_SUMMARY,  m.ACCOUNT_DES,  m.HAS_CHILDREN,  m.CAUSE_OF_IN_ID,  m.CAUSE_OF_IN,
        m.JUDGE_PERSONS,  m.BELONG_PROJECT,  m.BELONG_PLATE_FULL_ID,  m.SORT,  m.CASE_NAME,
        m.CREATE_PSN_FULL_ID,  m.CASE_TYPE_DES,  m.PROJECT_NAME,  m.OGN_PACKAGE,  m.OGN_PACKAGE_ID,  m.GROUP_PACKAGE,
        m.GROUP_PACKAGE_ID,  m.INVOLVED_LEVEL,  m.INVOLVED_LEVEL_ID,  m.UNIT_TYPE,  m.UNIT_TYPE_ID,  m.INVOLVED_OGN_LEVEL,
        m.INVOLVED_OGN_LEVEL_ID,  m.WHETHER1,  m.WHETHER2,  m.WHETHER3,  m.WHETHER4,  m.WHETHER5,  m.WHETHER6,
        m.WHETHER7,  m.WHETHER8,  m.WHETHER9,  m.WHETHER10,  m.WHETHER11,  m.WHETHER12,  m.WHETHER13,  m.WHETHER14,
        m.WHETHER15,  m.WHETHER16,  m.WHETHER17,  m.WHETHER18,  m.WHETHER19,  m.CASE_REASON,  m.CASE_REASON_ID,
        m.RISK_LEVEL,  m.RISK_LEVEL_ID,  m.WHETHER_GROUP,  m.GROUP_NAME,  m.GROUP_ID,  m.SUSPECTED_CHARGES,
        m.GREAT_REASON,  m.GREAT_PLAN,  m.END_REPORT,  m.CAUSE_ECONOMIC_LOSSES,  m.AVOID_ECONOMIC_LOSSES,
        m.ACTUAL_SEND_OTHER,  m.ACTUAL_RECEIVE_OTHER,  m.WHETHER_SUBMIT_IDEA,  m.WHETHER_REVIEW,  m.START_TIME,
        m.END_TIME,  m.SUPERVISE_NUMBER,  m.CAIPAN,  m.LAJG,  m.APPLY_PSN_NAME,  m.CASE_END
        from
        sg_case_records m
        <where>
            m.id not in (
            select id from (
            select m.id, m.CASE_NAME,
            case when d.name is null then '无' else d.name end as tagName
            from sg_case_records m left join sg_tag d on m.id = d.data_id
            <where>
                ${ew.sqlSegment}
            </where>
            )  A
            where tagName ='一审开庭前撤诉' or tagName = '并案处理' or tagName = '重复数据'
            )
            and m.case_kind is not null
            and
            ${ew.sqlSegment}
        </where>
    </select>


    <select id="getUnReportCaseList" resultMap="BaseResultMap2">
        select
                *
        from
                (
                        select
                                m.id,
                                count(d.id) as num
                        from
                                sg_case_records m left join (
                                    select * from sg_case_report where report_year = #{reportYear} and report_quarter = #{reportQuarter}
                                ) d on m.id = d.parent_id
                        <where>
                            ${ew.sqlSegment}
                        </where>
                        group by
                                m.id
                )   A
        where
                num=0

    </select>

    <resultMap id="BaseResultMap3" type="com.klaw.entity.caseBean.child.MiddleRelation">
        <association property="relationData" javaType="com.klaw.entity.caseBean.CaseReportTask">
            <result column="TIME2" jdbcType="TIMESTAMP" property="createTime" />
        </association>
        <association property="associatedData" javaType="com.klaw.entity.caseBean.CaseRecord">
            <id column="ID" jdbcType="VARCHAR" property="id" />
            <result column="CASE_NUMBER" jdbcType="VARCHAR" property="caseNumber" />
            <result column="CASE_TIME" jdbcType="TIMESTAMP" property="caseTime" />
            <result column="CAUSE_NAME" jdbcType="VARCHAR" property="causeName" />
            <result column="CAUSE_ID" jdbcType="VARCHAR" property="causeId" />
            <result column="CASE_TYPE" jdbcType="VARCHAR" property="caseType" />
            <result column="CASE_TYPE_ID" jdbcType="VARCHAR" property="caseTypeId" />
            <result column="OUR_POSITION" jdbcType="VARCHAR" property="ourPosition" />
            <result column="VENUE_IS_OUT" jdbcType="DECIMAL" property="venueIsOut" />
            <result column="VENUE_PROVINCE" jdbcType="VARCHAR" property="venueProvince" />
            <result column="VENUE_CITY" jdbcType="VARCHAR" property="venueCity" />
            <result column="VENUE_REGION" jdbcType="VARCHAR" property="venueRegion" />
            <result column="VENUE_ADDRESS" jdbcType="VARCHAR" property="venueAddress" />
            <result column="CASE_MONEY" jdbcType="DECIMAL" property="caseMoney" />
            <result column="WHETHER_MAJOR" jdbcType="DECIMAL" property="whetherMajor" />
            <result column="COURT" jdbcType="VARCHAR" property="court" />
            <result column="COURT_ID" jdbcType="VARCHAR" property="courtId" />
            <result column="TRIAL" jdbcType="VARCHAR" property="trial" />
            <result column="TRIAL_ID" jdbcType="VARCHAR" property="trialId" />
            <result column="JUDGE_MAIN" jdbcType="VARCHAR" property="judgeMain" />
            <result column="JUDGE_MAIN_ID" jdbcType="VARCHAR" property="judgeMainId" />
            <result column="JUDGE_MAIN_PHONE" jdbcType="VARCHAR" property="judgeMainPhone" />
            <result column="JUDGE_VICE" jdbcType="VARCHAR" property="judgeVice" />
            <result column="PROOF_DATE" jdbcType="TIMESTAMP" property="proofDate" />
            <result column="REPLY_DATE" jdbcType="TIMESTAMP" property="replyDate" />
            <result column="DATA_STATE" jdbcType="VARCHAR" property="dataState" />
            <result column="DATA_STATE_CODE" jdbcType="DECIMAL" property="dataStateCode" />
            <result column="CASE_PROCESS" jdbcType="LONGVARCHAR" property="caseProcess" />
            <result column="CASE_PROCESS_IDS" jdbcType="LONGVARCHAR" property="caseProcessIds" />
            <result column="CREATE_OGN_ID" jdbcType="VARCHAR" property="createOgnId" />
            <result column="CREATE_OGN_NAME" jdbcType="VARCHAR" property="createOgnName" />
            <result column="CREATE_DEPT_ID" jdbcType="VARCHAR" property="createDeptId" />
            <result column="CREATE_DEPT_NAME" jdbcType="VARCHAR" property="createDeptName" />
            <result column="CREATE_GROUP_ID" jdbcType="VARCHAR" property="createGroupId" />
            <result column="CREATE_GROUP_NAME" jdbcType="VARCHAR" property="createGroupName" />
            <result column="CREATE_ORG_ID" jdbcType="VARCHAR" property="createOrgId" />
            <result column="CREATE_ORG_NAME" jdbcType="VARCHAR" property="createOrgName" />
            <result column="CREATE_PSN_ID" jdbcType="VARCHAR" property="createPsnId" />
            <result column="CREATE_PSN_NAME" jdbcType="VARCHAR" property="createPsnName" />
            <result column="CREATE_PSN_FULL_NAME" jdbcType="LONGVARCHAR" property="createPsnFullName" />
            <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
            <result column="CASE_DETAILS" jdbcType="CLOB" property="caseDetails" />
            <result column="FILES" jdbcType="CLOB" property="files" />
            <result column="JUDGE_VICE_PHONE" jdbcType="VARCHAR" property="judgeVicePhone" />
            <result column="CASE_ACCEPT_MONEY" jdbcType="DECIMAL" property="caseAcceptMoney" />
            <result column="CASE_ACCEPT_MONEY_OUR" jdbcType="DECIMAL" property="caseAcceptMoneyOur" />
            <result column="HANDLE_SITUATION" jdbcType="VARCHAR" property="handleSituation" />
            <result column="HANDLE_SITUATION_ID" jdbcType="VARCHAR" property="handleSituationId" />
            <result column="WHETHER_FORCE_EXECUTE" jdbcType="DECIMAL" property="whetherForceExecute" />
            <result column="FORCE_EXECUTE" jdbcType="LONGVARCHAR" property="forceExecute" />
            <result column="FORCE_EXECUTE_ID" jdbcType="LONGVARCHAR" property="forceExecuteId" />
            <result column="RECEIVE_MONEY" jdbcType="DECIMAL" property="receiveMoney" />
            <result column="SEND_MONEY" jdbcType="DECIMAL" property="sendMoney" />
            <result column="CASE_CURRENT_PROCESS" jdbcType="VARCHAR" property="caseCurrentProcess" />
            <result column="CASE_CURRENT_PROCESS_ID" jdbcType="VARCHAR" property="caseCurrentProcessId" />
            <result column="CASE_PROCESS_TYPE" jdbcType="VARCHAR" property="caseProcessType" />
            <result column="CASE_PROCESS_TYPE_CODE" jdbcType="VARCHAR" property="caseProcessTypeCode" />
            <result column="PARENT_ID" jdbcType="VARCHAR" property="parentId" />
            <result column="CASE_KIND" jdbcType="VARCHAR" property="caseKind" />
            <result column="CASE_KIND_CODE" jdbcType="VARCHAR" property="caseKindCode" />
            <result column="CASE_ALIAS" jdbcType="VARCHAR" property="caseAlias" />
            <result column="PREPOSITION_ID" jdbcType="VARCHAR" property="prepositionId" />
            <result column="INTERFILE" jdbcType="DECIMAL" property="interfile" />
            <result column="PREPOSITION_TYPE" jdbcType="VARCHAR" property="prepositionType" />
            <result column="BELONG_PLATE_ID" jdbcType="VARCHAR" property="belongPlateId" />
            <result column="BELONG_PLATE" jdbcType="VARCHAR" property="belongPlate" />
            <result column="WHETHER_EASY" jdbcType="DECIMAL" property="whetherEasy" />
            <result column="RESULT_ADDRESS" jdbcType="VARCHAR" property="resultAddress" />
            <result column="RESULT_ADDRESS_ID" jdbcType="VARCHAR" property="resultAddressId" />
            <result column="RESULT" jdbcType="VARCHAR" property="result" />
            <result column="RESULT_ID" jdbcType="VARCHAR" property="resultId" />
            <result column="ACTUAL_RECEIVE_MONEY" jdbcType="DECIMAL" property="actualReceiveMoney" />
            <result column="ACTUAL_SEND_MONEY" jdbcType="DECIMAL" property="actualSendMoney" />
            <result column="REMARKS" jdbcType="CLOB" property="remarks" />
            <result column="WHETHER_CONTINUE" jdbcType="DECIMAL" property="whetherContinue" />
            <result column="WHETHER_INCLUDE" jdbcType="DECIMAL" property="whetherInclude" />
            <result column="CONTINUE_END_DATE" jdbcType="TIMESTAMP" property="continueEndDate" />
            <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
            <result column="CASE_CODE" jdbcType="VARCHAR" property="caseCode" />
            <result column="WHETHER_BLANK" jdbcType="DECIMAL" property="whetherBlank" />
            <result column="WIN_ANALYSIS" jdbcType="LONGVARCHAR" property="winAnalysis" />
            <result column="EXP_SUMMARY" jdbcType="LONGVARCHAR" property="expSummary" />
            <result column="ACCOUNT_DES" jdbcType="LONGVARCHAR" property="accountDes" />
            <result column="HAS_CHILDREN" jdbcType="DECIMAL" property="hasChildren" />
            <result column="CAUSE_OF_IN_ID" jdbcType="VARCHAR" property="causeOfInId" />
            <result column="CAUSE_OF_IN" jdbcType="VARCHAR" property="causeOfIn" />
            <result column="JUDGE_PERSONS" jdbcType="CLOB" property="judgePersons" />
            <result column="BELONG_PROJECT" jdbcType="VARCHAR" property="belongProject" />
            <result column="BELONG_PLATE_FULL_ID" jdbcType="LONGVARCHAR" property="belongPlateFullId" />
            <result column="SORT" jdbcType="DECIMAL" property="sort" />
            <result column="CASE_NAME" jdbcType="VARCHAR" property="caseName" />
            <result column="CREATE_PSN_FULL_ID" jdbcType="VARCHAR" property="createPsnFullId" />
            <result column="CASE_TYPE_DES" jdbcType="VARCHAR" property="caseTypeDes" />
            <result column="PROJECT_NAME" jdbcType="VARCHAR" property="projectName" />
            <result column="OGN_PACKAGE" jdbcType="VARCHAR" property="ognPackage" />
            <result column="OGN_PACKAGE_ID" jdbcType="VARCHAR" property="ognPackageId" />
            <result column="GROUP_PACKAGE" jdbcType="VARCHAR" property="groupPackage" />
            <result column="GROUP_PACKAGE_ID" jdbcType="VARCHAR" property="groupPackageId" />
            <result column="INVOLVED_LEVEL" jdbcType="VARCHAR" property="involvedLevel" />
            <result column="INVOLVED_LEVEL_ID" jdbcType="VARCHAR" property="involvedLevelId" />
            <result column="UNIT_TYPE" jdbcType="VARCHAR" property="unitType" />
            <result column="UNIT_TYPE_ID" jdbcType="VARCHAR" property="unitTypeId" />
            <result column="INVOLVED_OGN_LEVEL" jdbcType="VARCHAR" property="involvedOgnLevel" />
            <result column="INVOLVED_OGN_LEVEL_ID" jdbcType="VARCHAR" property="involvedOgnLevelId" />
            <result column="WHETHER1" jdbcType="DECIMAL" property="whether1" />
            <result column="WHETHER2" jdbcType="DECIMAL" property="whether2" />
            <result column="WHETHER3" jdbcType="DECIMAL" property="whether3" />
            <result column="WHETHER4" jdbcType="DECIMAL" property="whether4" />
            <result column="WHETHER5" jdbcType="DECIMAL" property="whether5" />
            <result column="WHETHER6" jdbcType="DECIMAL" property="whether6" />
            <result column="WHETHER7" jdbcType="DECIMAL" property="whether7" />
            <result column="WHETHER8" jdbcType="DECIMAL" property="whether8" />
            <result column="WHETHER9" jdbcType="DECIMAL" property="whether9" />
            <result column="WHETHER10" jdbcType="DECIMAL" property="whether10" />
            <result column="WHETHER11" jdbcType="DECIMAL" property="whether11" />
            <result column="WHETHER12" jdbcType="DECIMAL" property="whether12" />
            <result column="WHETHER13" jdbcType="DECIMAL" property="whether13" />
            <result column="WHETHER14" jdbcType="DECIMAL" property="whether14" />
            <result column="WHETHER15" jdbcType="DECIMAL" property="whether15" />
            <result column="WHETHER16" jdbcType="DECIMAL" property="whether16" />
            <result column="WHETHER17" jdbcType="DECIMAL" property="whether17" />
            <result column="WHETHER18" jdbcType="DECIMAL" property="whether18" />
            <result column="WHETHER19" jdbcType="DECIMAL" property="whether19" />
            <result column="CASE_REASON" jdbcType="VARCHAR" property="caseReason" />
            <result column="CASE_REASON_ID" jdbcType="VARCHAR" property="caseReasonId" />
            <result column="RISK_LEVEL" jdbcType="VARCHAR" property="riskLevel" />
            <result column="RISK_LEVEL_ID" jdbcType="VARCHAR" property="riskLevelId" />
            <result column="WHETHER_GROUP" jdbcType="DECIMAL" property="whetherGroup" />
            <result column="GROUP_NAME" jdbcType="VARCHAR" property="groupName" />
            <result column="GROUP_ID" jdbcType="VARCHAR" property="groupId" />
            <result column="SUSPECTED_CHARGES" jdbcType="VARCHAR" property="suspectedCharges" />
            <result column="GREAT_REASON" jdbcType="CLOB" property="greatReason" />
            <result column="GREAT_PLAN" jdbcType="CLOB" property="greatPlan" />
            <result column="END_REPORT" jdbcType="CLOB" property="endReport" />
            <result column="CAUSE_ECONOMIC_LOSSES" jdbcType="DECIMAL" property="causeEconomicLosses" />
            <result column="AVOID_ECONOMIC_LOSSES" jdbcType="DECIMAL" property="avoidEconomicLosses" />
            <result column="ACTUAL_SEND_OTHER" jdbcType="DECIMAL" property="actualSendOther" />
            <result column="ACTUAL_RECEIVE_OTHER" jdbcType="DECIMAL" property="actualReceiveOther" />
            <result column="WHETHER_SUBMIT_IDEA" jdbcType="DECIMAL" property="whetherSubmitIdea" />
            <result column="WHETHER_REVIEW" jdbcType="DECIMAL" property="whetherReview" />
            <result column="START_TIME" jdbcType="TIMESTAMP" property="startTime" />
            <result column="END_TIME" jdbcType="TIMESTAMP" property="endTime" />
            <result column="SUPERVISE_NUMBER" jdbcType="DECIMAL" property="superviseNumber" />
            <result column="CAIPAN" jdbcType="DECIMAL" property="caipan" />
            <result column="LAJG" jdbcType="VARCHAR" property="lajg" />
            <result column="APPLY_PSN_NAME" jdbcType="VARCHAR" property="applyPsnName" />
            <result column="CASE_END" jdbcType="TIMESTAMP" property="caseEnd" />
        </association>
    </resultMap>
    <sql id="Base_Column_List3">
     c.ID,  c.CASE_NUMBER,  c.CASE_TIME,  c.CAUSE_NAME,  c.CAUSE_ID,  c.CASE_TYPE,  c.CASE_TYPE_ID,  c.OUR_POSITION,
    c.VENUE_IS_OUT,  c.VENUE_PROVINCE,  c.VENUE_CITY,  c.VENUE_REGION,  c.VENUE_ADDRESS,  c.CASE_MONEY,
    c.WHETHER_MAJOR,  c.COURT,  c.COURT_ID,  c.TRIAL,  c.TRIAL_ID,  c.JUDGE_MAIN,  c.JUDGE_MAIN_ID,  c.JUDGE_MAIN_PHONE,
    c.JUDGE_VICE,  c.PROOF_DATE,  c.REPLY_DATE,  c.DATA_STATE,  c.DATA_STATE_CODE,  c.CASE_PROCESS,  c.CASE_PROCESS_IDS,
    c.CREATE_OGN_ID,  c.CREATE_OGN_NAME,  c.CREATE_DEPT_ID,  c.CREATE_DEPT_NAME,  c.CREATE_GROUP_ID,
    c.CREATE_GROUP_NAME,  c.CREATE_ORG_ID,  c.CREATE_ORG_NAME,  c.CREATE_PSN_ID,  c.CREATE_PSN_NAME,
    c.CREATE_PSN_FULL_NAME,  c.CREATE_TIME,  c.CASE_DETAILS,  c.FILES,  c.JUDGE_VICE_PHONE,  c.CASE_ACCEPT_MONEY,
    c.CASE_ACCEPT_MONEY_OUR,  c.HANDLE_SITUATION,  c.HANDLE_SITUATION_ID,  c.WHETHER_FORCE_EXECUTE,
    c.FORCE_EXECUTE,  c.FORCE_EXECUTE_ID,  c.RECEIVE_MONEY,  c.SEND_MONEY,  c.CASE_CURRENT_PROCESS,
    c.CASE_CURRENT_PROCESS_ID,  c.CASE_PROCESS_TYPE,  c.CASE_PROCESS_TYPE_CODE,  c.PARENT_ID,  c.CASE_KIND,
    c.CASE_KIND_CODE,  c.CASE_ALIAS,  c.PREPOSITION_ID,  c.INTERFILE,  c.PREPOSITION_TYPE,  c.BELONG_PLATE_ID,
    c.BELONG_PLATE,  c.WHETHER_EASY,  c. RESULT_ADDRESS,  c.RESULT_ADDRESS_ID,
    c.RESULT,  c.RESULT_ID,  c.ACTUAL_RECEIVE_MONEY,  c.ACTUAL_SEND_MONEY,  c.REMARKS,  c.WHETHER_CONTINUE,
    c.WHETHER_INCLUDE,  c.CONTINUE_END_DATE,  c.UPDATE_TIME,  c.CASE_CODE,  c.WHETHER_BLANK,
    c.WIN_ANALYSIS,  c.EXP_SUMMARY,  c.ACCOUNT_DES,  c.HAS_CHILDREN,  c.CAUSE_OF_IN_ID,  c.CAUSE_OF_IN,
    c.JUDGE_PERSONS,  c.BELONG_PROJECT,  c.BELONG_PLATE_FULL_ID,  c.SORT,  c.CASE_NAME,
    c.CREATE_PSN_FULL_ID,  c.CASE_TYPE_DES,  c.PROJECT_NAME,  c.OGN_PACKAGE,  c.OGN_PACKAGE_ID,  c.GROUP_PACKAGE,
    c.GROUP_PACKAGE_ID,  c.INVOLVED_LEVEL,  c.INVOLVED_LEVEL_ID,  c.UNIT_TYPE,  c.UNIT_TYPE_ID,  c.INVOLVED_OGN_LEVEL,
    c.INVOLVED_OGN_LEVEL_ID,  c.WHETHER1,  c.WHETHER2,  c.WHETHER3,  c.WHETHER4,  c.WHETHER5,  c.WHETHER6,
    c.WHETHER7,  c.WHETHER8,  c.WHETHER9,  c.WHETHER10,  c.WHETHER11,  c.WHETHER12,  c.WHETHER13,  c.WHETHER14,
    c.WHETHER15,  c.WHETHER16,  c.WHETHER17,  c.WHETHER18,  c.WHETHER19,  c.CASE_REASON,  c.CASE_REASON_ID,
    c.RISK_LEVEL,  c.RISK_LEVEL_ID,  c.WHETHER_GROUP,  c.GROUP_NAME,  c.GROUP_ID,  c.SUSPECTED_CHARGES,
    c.GREAT_REASON,  c.GREAT_PLAN,  c.END_REPORT,  c.CAUSE_ECONOMIC_LOSSES,  c.AVOID_ECONOMIC_LOSSES,
    c.ACTUAL_SEND_OTHER,  c.ACTUAL_RECEIVE_OTHER,  c.WHETHER_SUBMIT_IDEA,  c.WHETHER_REVIEW,  c.START_TIME,
    c.END_TIME,  c.SUPERVISE_NUMBER,  c.CAIPAN,  c.LAJG,  c.APPLY_PSN_NAME,  c.CASE_END, t.CREATE_TIME as TIME2
  </sql>

    <select id="queryCaseByTask" resultMap="BaseResultMap3">
        select <include refid="Base_Column_List3"/>
        from SG_CASE_REPORT_TASK t join SG_MIDDLE_RELATION m
        on t.ID = m.RELATION_ID join SG_CASE_RECORDS c on m.ASSOCIATED_ID = c.ID
        <where>
            ${ew.sqlSegment}
        </where>
    </select>

    <resultMap id="BaseResultMap4" type="com.klaw.entity.caseBean.child.MiddleRelation">
        <association property="relationData" javaType="com.klaw.entity.caseBean.CaseReportTask">
            <result column="TIME2" jdbcType="TIMESTAMP" property="createTime" />
        </association>
        <association property="associatedData" javaType="com.klaw.entity.disputeBean.SgDispute">
            <id column="ID" jdbcType="VARCHAR" property="id" />
            <result column="NAME" jdbcType="VARCHAR" property="name" />
            <result column="CODE" jdbcType="VARCHAR" property="code" />
            <result column="OCCUR_TIME" jdbcType="TIMESTAMP" property="occurTime" />
            <result column="DISPUTE_TYPE_CODE" jdbcType="VARCHAR" property="disputeTypeCode" />
            <result column="DISPUTE_TYPE_NAME" jdbcType="VARCHAR" property="disputeTypeName" />
            <result column="PUNISH_CODE" jdbcType="VARCHAR" property="punishCode" />
            <result column="OUR_POSITION_CODE" jdbcType="VARCHAR" property="ourPositionCode" />
            <result column="OUR_POSITION_NAME" jdbcType="VARCHAR" property="ourPositionName" />
            <result column="UNIT_TYPE_CODE" jdbcType="VARCHAR" property="unitTypeCode" />
            <result column="UNIT_TYPE_NAME" jdbcType="VARCHAR" property="unitTypeName" />
            <result column="PUNISH_SUBJECT" jdbcType="VARCHAR" property="punishSubject" />
            <result column="WHETHER_HAS_CONTACTS" jdbcType="DECIMAL" property="whetherHasContacts" />
            <result column="PUNISH_TYPE_CODE" jdbcType="VARCHAR" property="punishTypeCode" />
            <result column="PUNISH_TYPE_NAME" jdbcType="VARCHAR" property="punishTypeName" />
            <result column="BE_PUNISH_OJECT" jdbcType="VARCHAR" property="bePunishOject" />
            <result column="PUNISH_TIME" jdbcType="TIMESTAMP" property="punishTime" />
            <result column="PUNISH_RESULT" jdbcType="VARCHAR" property="punishResult" />
            <result column="RECTIFICATION_CODE" jdbcType="VARCHAR" property="rectificationCode" />
            <result column="RECTIFICATION_NAME" jdbcType="VARCHAR" property="rectificationName" />
            <result column="RECTIFICATION_DESC" jdbcType="VARCHAR" property="rectificationDesc" />
            <result column="WHETHER_LITIGATION" jdbcType="DECIMAL" property="whetherLitigation" />
            <result column="WHETHER_OVER" jdbcType="DECIMAL" property="whetherOver" />
            <result column="PUNISH_REASONS" jdbcType="CLOB" property="punishReasons" />
            <result column="HELP_EXECUTE_TYPE_CODE" jdbcType="VARCHAR" property="helpExecuteTypeCode" />
            <result column="HELP_EXECUTE_TYPE_NAME" jdbcType="VARCHAR" property="helpExecuteTypeName" />
            <result column="HELP_EXECUTE_NO" jdbcType="VARCHAR" property="helpExecuteNo" />
            <result column="HELP_EXECUTE_START" jdbcType="TIMESTAMP" property="helpExecuteStart" />
            <result column="HELP_EXECUTE_END" jdbcType="TIMESTAMP" property="helpExecuteEnd" />
            <result column="CASE_STAGE_CODE" jdbcType="VARCHAR" property="caseStageCode" />
            <result column="CASE_STAGE_NAME" jdbcType="VARCHAR" property="caseStageName" />
            <result column="WHETHER_RELATION_CASE" jdbcType="DECIMAL" property="whetherRelationCase" />
            <result column="INVOLVED_PERSON" jdbcType="VARCHAR" property="involvedPerson" />
            <result column="WHETHER_NUBMER_MONEY" jdbcType="DECIMAL" property="whetherNubmerMoney" />
            <result column="RECEPTIONIST" jdbcType="VARCHAR" property="receptionist" />
            <result column="RECEPTIONIST_TIME" jdbcType="VARCHAR" property="receptionistTime" />
            <result column="HELP_EXECUTE_ASK" jdbcType="CLOB" property="helpExecuteAsk" />
            <result column="RISK_LEVEL_CODE" jdbcType="VARCHAR" property="riskLevelCode" />
            <result column="RISK_LEVEL_NAME" jdbcType="VARCHAR" property="riskLevelName" />
            <result column="DISPUTE_MONEY" jdbcType="DECIMAL" property="disputeMoney" />
            <result column="PROJECT_NAME" jdbcType="VARCHAR" property="projectName" />
            <result column="DISPUTE_DESCRIPTION" jdbcType="CLOB" property="disputeDescription" />
            <result column="REMARKS" jdbcType="CLOB" property="remarks" />
            <result column="CREATE_OGN_ID" jdbcType="VARCHAR" property="createOgnId" />
            <result column="CREATE_OGN_NAME" jdbcType="VARCHAR" property="createOgnName" />
            <result column="CREATE_DEPT_ID" jdbcType="VARCHAR" property="createDeptId" />
            <result column="CREATE_DEPT_NAME" jdbcType="VARCHAR" property="createDeptName" />
            <result column="CREATE_GROUP_ID" jdbcType="VARCHAR" property="createGroupId" />
            <result column="CREATE_GROUP_NAME" jdbcType="VARCHAR" property="createGroupName" />
            <result column="CREATE_PSN_ID" jdbcType="VARCHAR" property="createPsnId" />
            <result column="CREATE_PSN_NAME" jdbcType="VARCHAR" property="createPsnName" />
            <result column="CREATE_ORG_ID" jdbcType="VARCHAR" property="createOrgId" />
            <result column="CREATE_ORG_NAME" jdbcType="VARCHAR" property="createOrgName" />
            <result column="CREATE_PSN_FULL_ID" jdbcType="VARCHAR" property="createPsnFullId" />
            <result column="CREATE_PSN_FULL_NAME" jdbcType="VARCHAR" property="createPsnFullName" />
            <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
            <result column="DATA_STATE" jdbcType="VARCHAR" property="dataState" />
            <result column="DATA_STATE_CODE" jdbcType="DECIMAL" property="dataStateCode" />
            <result column="DISPUTE_MONEY_STR" jdbcType="VARCHAR" property="disputeMoneyStr" />
            <result column="EXECUTE_COURT" jdbcType="VARCHAR" property="executeCourt" />
            <result column="JUDGE_ONE" jdbcType="VARCHAR" property="judgeOne" />
            <result column="JUDGE_TWO" jdbcType="VARCHAR" property="judgeTwo" />
            <result column="CASE_NAME" jdbcType="VARCHAR" property="caseName" />
            <result column="CASE_ID" jdbcType="VARCHAR" property="caseId" />
            <result column="END_TIME" jdbcType="TIMESTAMP" property="endTime" />
            <result column="END_MODE_CODE" jdbcType="VARCHAR" property="endModeCode" />
            <result column="END_MODE_NAME" jdbcType="VARCHAR" property="endModeName" />
            <result column="RECEIVE_MONEY" jdbcType="DECIMAL" property="receiveMoney" />
            <result column="END_DESCRIPTION" jdbcType="CLOB" property="endDescription" />
            <result column="ATTACHMENT" jdbcType="CLOB" property="attachment" />
            <result column="CONTACTS_INFO" jdbcType="VARCHAR" property="contactsInfo" />
            <result column="OTHER_TYPE_DESC" jdbcType="VARCHAR" property="otherTypeDesc" />
            <result column="PARENT_ID" jdbcType="VARCHAR" property="parentId" />
            <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
            <result column="POSSIBLE_LEGAL" jdbcType="VARCHAR" property="possibleLegal" />
            <result column="POSSIBLE_OGN" jdbcType="VARCHAR" property="possibleOgn" />
            <result column="WHETHER_OGN" jdbcType="DECIMAL" property="whetherOgn" />
            <result column="INVOLVE_REASON" jdbcType="VARCHAR" property="involveReason" />
            <result column="INVOLVE_PROBLEM" jdbcType="CLOB" property="involveProblem" />
            <result column="PREVENTION" jdbcType="CLOB" property="prevention" />
            <result column="OTHER_PARTY" jdbcType="VARCHAR" property="otherParty" />
        </association>
    </resultMap>
    <sql id="Base_Column_List4">
    c.ID,  c.NAME,  c.CODE,  c.OCCUR_TIME,  c.DISPUTE_TYPE_CODE,  c.DISPUTE_TYPE_NAME,  c.PUNISH_CODE,  c.OUR_POSITION_CODE,
    c.OUR_POSITION_NAME,  c.UNIT_TYPE_CODE,  c.UNIT_TYPE_NAME,  c.PUNISH_SUBJECT,  c.WHETHER_HAS_CONTACTS,  c.PUNISH_TYPE_CODE,
    c.PUNISH_TYPE_NAME,  c.BE_PUNISH_OJECT,  c.PUNISH_TIME,  c.PUNISH_RESULT,  c.RECTIFICATION_CODE,  c.RECTIFICATION_NAME,
    c.RECTIFICATION_DESC,  c.WHETHER_LITIGATION,  c.WHETHER_OVER,  c.PUNISH_REASONS,  c.HELP_EXECUTE_TYPE_CODE,
    c.HELP_EXECUTE_TYPE_NAME,  c.HELP_EXECUTE_NO,  c.HELP_EXECUTE_START,  c.HELP_EXECUTE_END,  c.CASE_STAGE_CODE,
    c.CASE_STAGE_NAME,  c.WHETHER_RELATION_CASE,  c.INVOLVED_PERSON,  c.WHETHER_NUBMER_MONEY,  c.RECEPTIONIST,
    c.RECEPTIONIST_TIME,  c.HELP_EXECUTE_ASK,  c.RISK_LEVEL_CODE,  c.RISK_LEVEL_NAME,  c.DISPUTE_MONEY,  c.PROJECT_NAME,
    c.DISPUTE_DESCRIPTION,  c.REMARKS,  c.CREATE_OGN_ID,  c.CREATE_OGN_NAME,  c.CREATE_DEPT_ID,  c.CREATE_DEPT_NAME,
    c.CREATE_GROUP_ID,  c.CREATE_GROUP_NAME,  c.CREATE_PSN_ID,  c.CREATE_PSN_NAME,  c.CREATE_ORG_ID,  c.CREATE_ORG_NAME,
    c.CREATE_PSN_FULL_ID,  c.CREATE_PSN_FULL_NAME,  c.CREATE_TIME,  c.DATA_STATE,  c.DATA_STATE_CODE,  c.DISPUTE_MONEY_STR,
    c.EXECUTE_COURT,  c.JUDGE_ONE,  c.JUDGE_TWO,  c.CASE_NAME,  c.CASE_ID,  c.END_TIME,  c.END_MODE_CODE,  c.END_MODE_NAME,
    c.RECEIVE_MONEY,  c.END_DESCRIPTION,  c.ATTACHMENT,  c.CONTACTS_INFO,  c.OTHER_TYPE_DESC,  c.PARENT_ID,  c.UPDATE_TIME,
    c.POSSIBLE_LEGAL,  c.POSSIBLE_OGN,  c.WHETHER_OGN,  c.INVOLVE_REASON,  c.INVOLVE_PROBLEM,  c.PREVENTION,  c.OTHER_PARTY,
      t.CREATE_TIME as TIME2
  </sql>

    <select id="queryDisputeByTask" resultMap="BaseResultMap4">
        select <include refid="Base_Column_List4"/>
        from SG_CASE_REPORT_TASK t join SG_MIDDLE_RELATION m
        on t.ID = m.RELATION_ID join SG_DISPUTE c on m.ASSOCIATED_ID = c.ID
        <where>
            ${ew.sqlSegment}
        </where>
    </select>
</mapper>