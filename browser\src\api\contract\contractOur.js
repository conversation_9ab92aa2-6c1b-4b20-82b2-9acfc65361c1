import {request} from '@/api/index'

export default {
  query(data) {
    return request({
      url: '/contractOur/query',
      method: 'post',
      data
    })
  },
  save(data) {
    return request({
      url: '/contractOur/save',
      method: 'post',
      data
    })
  },
  delete(data) {
    return request({
      url: '/contractOur/delete',
      method: 'post',
      data
    })
  },
  queryById(data) {
    return request({
      url: '/contractOur/queryById',
      method: 'post',
      data
    })
  },

}