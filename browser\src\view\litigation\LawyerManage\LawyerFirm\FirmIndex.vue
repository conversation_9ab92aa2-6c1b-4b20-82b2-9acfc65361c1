<template>
  <el-container class="container-manage-sg" direction="vertical">
    <el-header>
      <el-card>
        <el-input
            v-model="temp.fuzzyValue"
            class="filter_input"
            clearable
            placeholder="检索条件（律所名称、统一社会信用代码、负责人）"
            @clear="refreshData"
            @keyup.enter.native="refreshData">
          <el-popover slot="prepend" placement="bottom-start" trigger="click" width="1000">
            <el-form ref="queryForm" label-width="100px" size="mini">
              <el-row>
                <el-col :span="12">
                  <el-form-item label="律所名称">
                    <el-input v-model="temp.lawyerFirm" clearable placeholder="请输入..."
                              style="width:100%"/>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="负责人">
                    <el-input v-model="temp.functionary" clearable placeholder="请输入..." style="width:100%"/>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="12" class="myLabel">
                  <el-form-item class="custom-word-break" label="统一社会信用代码">
                    <span slot="label">统一社会<br>信用代码</span>
                    <el-input v-model="temp.licenseCode" clearable placeholder="请输入..." style="width:100%"/>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="状态">
                    <el-select v-model="temp.dataState" clearable placeholder="请选择" style="width:100%"
                               @clear="refreshData" @keyup.enter.native="refreshData">
                      <el-option
                          v-for="item in this.utils.dataState_BPM_data"
                          :key="item.code"
                          :label="item.name"
                          :value="item.name"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="12">
                  <el-form-item label="经办日期">
                    <el-row>
                      <el-col :span="11">
                        <el-date-picker
                            v-model="temp.beginTime"
                            format="yyyy-MM-dd"
                            placeholder="选择日期"
                            style="width: 100%"
                            type="date"
                            value-format="yyyy-MM-dd"
                        />
                      </el-col>
                      <el-col :span="2" style="text-align: center;">至</el-col>
                      <el-col :span="11">
                        <el-date-picker
                            v-model="temp.endTime"
                            format="yyyy-MM-dd"
                            placeholder="选择日期"
                            style="width: 100%"
                            type="date"
                            value-format="yyyy-MM-dd"
                        />
                      </el-col>
                    </el-row>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-button-group style="float: right">
                <el-button icon="el-icon-search" size="mini" type="primary" @click="search_">搜索</el-button>
                <el-button icon="el-icon-refresh-left" size="mini" type="primary" @click="empty_">重置</el-button>
              </el-button-group>
            </el-form>
            <el-button slot="reference" size="mini" type="primary">高级检索</el-button>
          </el-popover>
          <el-button slot="append" icon="el-icon-search" @click="search_"/>
        </el-input>
      </el-card>
    </el-header>

    <el-main>
      <SimpleBoard2 :isFirmIndex="true" :title="'律所入库信息'" @addFormalFirm="addFormalFirm"
                    @addTemporaryFirm="addTemporaryFirm">
        <el-table
            ref="table"
            :data="tableData"
            :height="table_height"
            :show-overflow-tooltip="true"
            border
            fit
            highlight-current-row
            stripe
            style="width: 100%"
            @sort-change="tableSort"
            @row-dblclick="rowDblclick">
          <el-table-column align="center" label="序号" type="index" width="50"/>
          <el-table-column
              v-if="ss.tableColumns.find(item => item.key === 'lawFirmType').visible"
              align="center"
              label="律所类型"
              min-width="160"
              prop="lawFirmType"
              show-overflow-tooltip sortable="custom"
          />
          <el-table-column
              v-if="ss.tableColumns.find(item => item.key === 'lawyerFirm').visible"
              align="center"
              label="律所名称"
              min-width="160"
              prop="lawyerFirm"
              show-overflow-tooltip sortable="custom">
            <template slot-scope="scope">
              <span v-if="topLawFirm(scope.row.licenseCode)" class="el-icon-s-help" style="color:red;"></span>
              {{ scope.row.lawyerFirm }}
            </template>
          </el-table-column>
          <el-table-column
              v-if="ss.tableColumns.find(item => item.key === 'licenseCode').visible"
              align="center" label="统一社会信用代码"
              min-width="145"
              prop="licenseCode"
              show-overflow-tooltip sortable="custom"
          />
          <el-table-column
              v-if="ss.tableColumns.find(item => item.key === 'functionary').visible"
              align="center"
              label="负责人"
              min-width="80"
              prop="functionary"
              show-overflow-tooltip sortable="custom"
          />
          <el-table-column
              v-if="ss.tableColumns.find(item => item.key === 'dataState').visible"
              align="center"
              label="审批状态"
              min-width="80"
              prop="dataState"
              show-overflow-tooltip sortable="custom"
          />
          <el-table-column
              v-if="ss.tableColumns.find(item => item.key === 'createTime').visible"
              align="center"
              label="经办时间"
              min-width="90"
              prop="createTime"
              show-overflow-tooltip sortable="custom">
            <template slot-scope="scope">
              <span>{{ scope.row.createTime | parseTime }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" fixed="right" label="操作" min-width="120">
            <template slot-scope="scope">
              <el-button v-if="isEdit(scope.row)" type="text" @click="edit_(scope.$index,scope.row)">编辑</el-button>
              <el-button type="text" @click="view_(scope.$index,scope.row)">查看</el-button>
              <el-button v-if="isDelete(scope.row)" type="text" @click="delete_(scope.$index,scope.row)">删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </SimpleBoard2>
    </el-main>

    <el-footer>
      <pagination
          :limit.sync="temp.limit"
          :page.sync="temp.page"
          :total="temp.total"
          align="center"
          @pagination="refreshData"
      />
    </el-footer>

  </el-container>
</template>

<script>

import pagination from '../../../components/Pagination/PaginationIndex'
import lawyerFirmApprovalApi from '@/api/LawyerManage/LawyerFirm/lawFirmInApproval'
import lawyerFirmApi from '@/api/LawyerManage/LawyerFirm/lawyerFirm'
import taskApi from '@/api/_system/task'
import SimpleBoard2 from "@/view/components/SimpleBoard/SimpleBoardIndex"
import {mapGetters} from "vuex"
import simpleUtils from '@/view/utils/simpleUtils'
import orgApi from '@/api/_system/org'

export default {
  name: 'FirmIndex',
  inject: ['layout'],
  computed: {
    ...mapGetters(["orgContext", "currentFunctionId"])
  },
  components: {pagination, SimpleBoard2, simpleUtils},
  filters: {
    operatingTypeFilter(val) {
      let type = ''
      if (val === 'change') {
        type = '律所变更'
      } else if (val === 'in') {
        type = '重新入库'
      } else if (val === 'new') {
        type = '新增律所'
      } else if (val === 'recommend') {
        type = '推荐入库'
      }
      return type
    }
  },
  data() {
    return {
      isLegal: false,
      table_height: '100%', // 定义表格高度
      tableData: null, // 定义表格数据源
      typeName: null,   // 库类别名称
      typeCode: null,  // 库类别代码
      temp: {
        isQuery: false,  // 是否登记
        lawyerFirm: null, // 律所名称
        functionary: null, // 负责人
        licenseCode: null, // 统一社会信用代码
        dataState: null, // 状态
        beginTime: null, //开始时间
        endTime: null,  //结束时间
        fuzzyValue: null, // 模糊字段
        sortName: null, // 排序字段
        order: false, // 排序顺序
        orgId: null,
        page: 1,
        limit: 10,
        total: 0,
      },
      ss: {
        data: this.tableData,
        tableColumns: [
          {key: 'lawyerFirm', label: '律所名称', visible: true},
          {key: 'licenseCode', label: '统一社会信用代码', visible: true},
          {key: 'functionary', label: '负责人', visible: true},
          {key: 'operatingType', label: '操作类型', visible: true},
          {key: 'lawFirmType', label: '律所类型', visible: true},
          {key: 'dataState', label: '审批状态', visible: true},
          {key: 'createTime', label: '经办时间', visible: true},
        ]
      },
      taskId: null,
      processInstanceId: null,
      selectData: [{code: '0', name: '资源库'}, {code: '1', name: '推荐库'},],
    }
  },
  created() {
    this.refreshData()
    this.isHeadquarter()
  },
  activated() {
    // 长连接页面第二次激活的时候,不会走created方法,会走此方法
    this.refreshData()
  },
  mounted() {
    this.$nextTick(function () {
      this.table_height = window.innerHeight - this.$refs.table.$el.offsetTop - 84 - 65
      // 监听窗口大小变化
      const self = this
      window.onresize = function () {
        self.table_height = window.innerHeight - self.$refs.table.$el.offsetTop - 84 - 65
      }
    })
  },
  methods: {
    isHeadquarter() {
      const createOgnId = this.orgContext.currentOgnId

      // if (createOgnId === '15033708970596') {
      this.typeCode = '1'
      this.typeName = '推荐库'
      // } else {
      //   this.typeCode = '0'
      //   this.typeName = '资源库'
      // }
    },
    refreshData() {
      this.temp.orgId = this.orgContext.currentOrgId
      lawyerFirmApi.query(this.temp).then((res) => {
        this.tableData = res.data.page.records
        this.ss.data = this.tableData
        this.temp.total = res.data.page.total
      })
      this.isLegal_()
    },
    isLegal_() {
      orgApi.roleCheck({orgId: this.orgContext.currentOrgId, roleName: 'FLGWFWBQX'}).then(res => {
        console.log('isLegal：', res)
        this.isLegal = res.data.data
      })
    },
    search_() {
      this.refreshData()
    },
    empty_() {
      this.temp = {
        isQuery: false,  // 是否台账
        lawyerFirm: null, // 律所名称
        functionary: null, // 负责人
        licenseCode: null, // 统一社会信用代码
        dataState: null, // 状态
        beginTime: null, //开始时间
        endTime: null,  //结束时间
        fuzzyValue: null, // 模糊字段
        sortName: null, // 排序字段
        order: false, // 排序顺序
        orgId: null,
        page: 1,
        limit: 10,
        total: 0,
      }
      this.refreshData()
    },
    refresh_() {
      this.temp.sortName = null
      this.empty_()
    },
    addFormalFirm() {
      const uuid = this.utils.createUUID();
      this.layout.openNewTab(
          "入库信息",
          "firm_main_new_detail",
          "firm_main_new_detail",
          uuid,
          {
            functionId: "firm_main_new_detail," + uuid,
            ...this.utils.routeState.NEW(uuid),
            lawFirmType: "正式律所"
          }
      );
    },
    addTemporaryFirm() {
      const uuid = this.utils.createUUID();
      this.layout.openNewTab(
          "入库信息",
          "firm_main_new_detail",
          "firm_main_new_detail",
          uuid,
          {
            functionId: "firm_main_new_detail," + uuid,
            ...this.utils.routeState.NEW(uuid),
            lawFirmType: "临时律所"
          }
      );
    },
    tableSort(column) {
      this.temp.sortName = column.prop
      this.temp.order = column.order === 'ascending'
      this.refreshData()
    },
    rowDblclick(row) {
      taskApi.selectTaskId({businessKey: row.id, isView: 'true'}).then(res => {
        if (res.data.data.length > 0) {
          const functionId = res.data.data[0].ID
          const tabId = this.utils.createUUID()
          this.layout.openNewTab("律所信息",
              "design_page",
              "design_page",
              tabId,
              {
                processInstanceId: res.data.data[0].PID,//流程实例
                taskId: res.data.data[0].ID,//任务ID
                businessKey: row.id, //业务数据ID
                functionId: functionId, //不传也可以，只有首环节需要传，实例启动就可以不传了
                entranceType: "FLOWABLE",
                type: "haveDealt",
                channel: 'business', //查看渠道，默认就传这个值即可，表示是从业务功能传过来的，首环节提交后过滤掉撤回按钮
                view: 'new',
              }
          )
        } else {
          let functionCode
          if (row.operatingType === 'new') {
            functionCode = 'firm_main_new_detail'
          } else if (row.operatingType === 'change' || row.operatingType === 'in') {
            functionCode = 'firm_main_change_detail'
          } else if (row.operatingType === 'recommend') {
            functionCode = 'firm_main_recommend_detail'
          }

          const tabId = this.utils.createUUID();
          this.layout.openNewTab(
              "律所信息",
              functionCode,
              functionCode,
              tabId,
              {
                functionId: functionCode + "," + tabId,
                ...this.utils.routeState.VIEW(row.id)
              }
          )
        }
      })
    },
    isEdit(row) {
      return row.dataStateCode === 1
    },
    isDelete(row) {
      return row.dataStateCode === this.utils.dataState_BPM.SAVE.code
    },
    view_(index, row) {
      taskApi.selectTaskId({businessKey: row.id, isView: 'true'}).then(res => {
        if (res.data.data.length > 0) {
          const functionId = res.data.data[0].ID
          const tabId = this.utils.createUUID()
          this.layout.openNewTab("入库信息",
              "design_page",
              "design_page",
              tabId,
              {
                processInstanceId: res.data.data[0].PID,//流程实例
                taskId: res.data.data[0].ID,//任务ID
                businessKey: row.id, //业务数据ID
                functionId: functionId, //不传也可以，只有首环节需要传，实例启动就可以不传了
                entranceType: "FLOWABLE",
                type: "haveDealt",
                channel: 'business', //查看渠道，默认就传这个值即可，表示是从业务功能传过来的，首环节提交后过滤掉撤回按钮
                view: 'new',
              }
          )
        } else {
          let functionCode = 'firm_main_new_detail';
          console.log(row.operatingType);
          if (row.operatingType === 'new') {
            functionCode = 'firm_main_new_detail'
          } else if (row.operatingType === 'change' || row.operatingType === 'in') {
            functionCode = 'firm_main_change_detail'
          } else if (row.operatingType === 'recommend') {
            functionCode = 'firm_main_recommend_detail'
          }
          // console.log(functionCode);
          const tabId = this.utils.createUUID();
          this.layout.openNewTab(
              "入库信息",
              functionCode,
              functionCode,
              tabId,
              {
                functionId: functionCode + "," + tabId,
                ...this.utils.routeState.VIEW(row.id)
              }
          )
        }
      })
    },
    edit_(index, row) {

      const tabId = this.utils.createUUID();
      this.layout.openNewTab(
          "入库信息",
          'firm_main_new_detail',
          'firm_main_new_detail',
          tabId,
          {
            // functionId: functionCode + "," + tabId,
            ...this.utils.routeState.EDIT(row.id),
          }
      );

      // let functionCode = 'firm_main_new_detail'
      //
      // switch (row.operatingType) {
      //   case "new":
      //     functionCode = 'firm_main_new_detail';
      //     break;
      //   case "change":
      //   case "in":
      //     functionCode = 'firm_main_change_detail';
      //     break;
      //   case "recommend":
      //     functionCode = 'firm_main_recommend_detail';
      //     break;
      // }
      //
      // const tabId = this.utils.createUUID();
      // this.layout.openNewTab(
      //     "入库信息",
      //     functionCode,
      //     functionCode,
      //     tabId,
      //     {
      //       functionId: functionCode + "," + tabId,
      //       ...this.utils.routeState.EDIT(row.id),
      //     }
      // );
    },
    delete_(index, row) {
      this.$confirm('您确定要删除吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        new Promise((resolve) => {
          lawyerFirmApprovalApi.delete({id: row.id}).then((response) => {
            resolve(response)
          })
        }).then(() => {
          this.tableData.splice(index, 1)
          this.$message.success('删除成功!')
        })
      }).catch(() => {
        this.$message.info('已取消删除!')
      })
    },
    topLawFirm(val) {
      const firms = ['31110000E00017891P', '31110000E000169525', '31310000425097733Y', '31110000E00016813E',
        '31110000E00016266T', '31110000400834282L', '31110000E00017525U', '31110000E00018675X']

      return firms.indexOf(val) > -1
    },
  }
}

</script>

<style scoped>
</style>