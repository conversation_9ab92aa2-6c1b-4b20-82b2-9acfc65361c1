
3b3a9b1ade48414c1d4eb190705ff2da5580fd77	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.309.1754018536329.js\",\"contentHash\":\"7a2e7f30921a617428e51dc0f7ddab87\"}","integrity":"sha512-C9rBYukhlC3z1u1T1brbeUNtgHWTXGlou9/ULN0bavAVp1Hkx5AoAsBJvGfw7Tv00JIkN26AGiQBMgMJm4e9fw==","time":1754018575974,"size":114481}