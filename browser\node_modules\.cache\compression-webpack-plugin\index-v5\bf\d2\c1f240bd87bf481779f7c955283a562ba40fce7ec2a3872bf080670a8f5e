
1119a2519530ac57ac71675402dca0855fe2fbf0	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.446.1754018536329.js\",\"contentHash\":\"b33afd29964e6fd542632db751e33d85\"}","integrity":"sha512-wDHGE4uhddABMiDDv5jMaz9e2/uh/ooILMmB8hB3/ByO5vEzS0lo158WrclP1sor1IARIr3tzzZ5HxRnPIbhCQ==","time":1754018575957,"size":23816}