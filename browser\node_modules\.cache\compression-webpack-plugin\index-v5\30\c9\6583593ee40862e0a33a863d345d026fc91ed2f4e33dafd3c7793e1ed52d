
241fd961f29822f0c71dd583dc750b7e53f4462a	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.29.1754018536329.js\",\"contentHash\":\"5f17b5b6442b126a7fd300026b0e7309\"}","integrity":"sha512-OHT5LLQxq3ytZgHUHcs32dPuRmVK4vxOWabiSpmhCv/NGMGLTlEXqs63kI6PrcADxfNGoryD1Gd4DvBA1eqn4w==","time":1754018575958,"size":60657}