package com.klaw.service.imp.caseServiceImpl;


import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.klaw.dao.caseDao.DisputeMapper;
import com.klaw.entity.caseBean.Dispute;
import com.klaw.service.caseService.DisputeService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

@Service("disputeService")
@Transactional
public class DisputeServiceImpl extends ServiceImpl<DisputeMapper, Dispute> implements DisputeService {
    @Resource
    DisputeMapper disputeMapper;



    @Override
    public Page<Dispute> queryDispute(Page<Dispute> page, Wrapper<Dispute> wrapper) {
//        wrapper = (Wrapper<Dispute>) SqlHelper.fillWrapper(page, wrapper);
        return page.setRecords(disputeMapper.queryDispute(page, wrapper));
    }
}
