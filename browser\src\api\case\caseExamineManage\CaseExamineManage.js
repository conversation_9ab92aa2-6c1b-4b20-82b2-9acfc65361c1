import {request} from '@/api/index'

export default {
  query(data) {
    return request({
      url: '/caseExamineManage/queryAll',
      method: 'post',
      data
    })
  },
  queryId(data) {
    return request({
      url: '/caseExamineManage/queryId',
      method: 'post',
      data
    })
  },
  save(data) {
    return request({
      url: '/caseExamineManage/save',
      method: 'post',
      data
    })
  },
  delete(data) {
    return request({
      url: '/caseExamineManage',
      method: 'delete',
      data
    })
  },
  queryIdAndName(data) {
    return request({
      url: '/caseExamineManage/queryIdAndName',
      method: 'post',
      data
    })
  },
  queryView(data) {
    return request({
      url: '/caseExamineManage/queryView',
      method: 'post',
      data
    })
  },
  startOAFlow(data) {
    return request({
      url: '/caseExamineManage/startOAFlow',
      method: 'post',
      data
    })
  },
    setParam(data) {
        return request({
            url: '/caseExamineManage/setParam',
            method: 'post',
            data
        })
    }
}
