<template>
  <simple-board title="关联信息" :is-second="true" :is-has="true" :has-value.sync="isHasContent" :dropdowns="relationTypes" :data-state="dataState" :is-dropdown="true" @addBtn="add_" @deleteData="deleteAll">
    <el-table ref="table" v-draggable="tableData" v-loading="tableLoading" :data="tableData" max-height="200" stripe fit highlight-current-row :row-style="{height:'20px'}">
      <el-table-column type="index" width="50" label="序号" />
      <el-table-column prop="name" label="关联类型" width="200" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ relationValue(scope.row,'type') }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="money" label="名称" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ relationValue(scope.row,'name') }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="number" label="编号" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ relationValue(scope.row,'number') }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="time" label="发生时间" width="200" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ relationValue(scope.row, 'time') | parseTime }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="160" align="center">
        <template slot-scope="scope">
          <el-button v-if=" !isView " type="text" size="mini" @click="handleDelete(scope.$index, scope.row)">删除</el-button>
          <el-button type="text" size="mini" @click="handleView(scope.$index, scope.row)">查看</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!--案件选择框-->
    <CaseProjectDialog :visible.sync="caseProjectDialogVisible" @onSure="dialogSure" />
    <!--案件选择框-->
    <relationDisputeDialog :dispute-dialog-visible.sync="disputeDialogVisible" @disputeSure_="dialogSure" />
    <!--案件选择框-->
    <CaseDialog :visible.sync="caseDialogVisible" @onSure="dialogSure" />
    <!--合同选择框-->
    <contractDialog :visible.sync="contractDialogVisible" @onSure="dialogSure"  />


  </simple-board>
</template>

<script>
// 组件
import CaseProjectDialog from "../../../dialog/CaseProjectDialog";
import SimpleBoard from "../../../../components/SimpleBoard/SimpleBoardViewCase";
import RelationDisputeDialog from "../../../disputeManage/disputeProcess/RelationDisputeDialog";
import CaseDialog from "../../../dialog/CaseDialog";
import caseApi from "@/api/case/case";
import contractDialog from "@/view/litigation/contractManage/contractApproval/relationContract"
import taskApi from "@/api/_system/task";

export default {
  name: "Relations",
  inject: ['layout'],
  components: {
    CaseDialog,
    RelationDisputeDialog,
    SimpleBoard,
    CaseProjectDialog,
    contractDialog
  },
  props: {
    dataList: {
      type: Array,
      default: function() {
        return [];
      }
    },
    mainId: {
      type: String,
      default: ""
    },
    dataState: {
      type: String,
      default: ""
    },
    initRelationId: {
      type: String,
      default: ""
    },
    relationType: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      tableLoading: false,
      tableData: this.dataList,

      relationTypes: [
        { code: "case", name: "关联案件" },
        { code: "dispute", name: "关联纠纷" },
        { code: "caseProject", name: "关联群诉案件" },
        { code: "contract", name: "关联合同" },
      ],

      associatedType: null,
      caseDialogVisible: false,
      caseProjectDialogVisible: false,
      disputeDialogVisible: false,
      contractDialogVisible: false,
    };
  },
  computed: {
    isView: function() {
      return this.dataState === this.utils.formState.VIEW;
    },
    isHasContent: function() {
      return this.tableData.length > 0;
    }
  },
  watch: {
    tableData: {
      handler(val, oldVal) {
        this.$emit("update:dataList", val);
      },
      deep: true
    },
    dataList(val) {
      this.tableData = val === null ? [] : val;
    },
    initRelationId: {
      handler(val, oldVal) {
        if (val !== null && val !== "") {
          caseApi.queryById(val).then(res => {
            this.associatedType = "case";
            const arr = [];
            arr.push(res.data.data);
            this.dialogSure(arr);
          });
        }
      }
    }
  },

  methods: {
    handleDelete(index, row) {
      this.tableData.splice(index, 1);
    },
    add_(event) {
      if (event) {
        this.associatedType = event.code;
        this.caseDialogVisible = event.code === "case";
        this.caseProjectDialogVisible = event.code === "caseProject";
        this.disputeDialogVisible = event.code === "dispute";
        this.contractDialogVisible = event.code === "contract";
      }
    },
    handleView(index, row) {
      let projectId;
      let type;
      if (this.isRelation(row.relationId)) {
        projectId = row.associatedId;
        type = row.associatedType;
      } else {
        projectId = row.relationId;
        type = row.relationType;
      }
      const tabId = this.utils.createUUID()
      if (type === "case") {
        this.layout.openNewTab(
            "案件进程信息",
            "case_ledger_view",
            "case_ledger_view",
            tabId, {
              ...this.utils.routeState.VIEW(projectId), tabId: tabId
            }
        )
      } else if (type === "caseProject") {
        this.layout.openNewTab(
            "群诉案件信息",
            "case_project_main",
            "case_project_main",
            tabId, {
              ...this.utils.routeState.VIEW(projectId), tabId: tabId
            }
        )
      } else if (type === "dispute") {
        // this.$router.push({
        //   name: "RegisterMain",
        //   params: { ...this.utils.routeState.VIEW(projectId) }
        // });
      } else if (type === "contract") {
       this.view_(null,row.associatedData)
      }
    },
    dialogSure(data) {
      for (let i = 0; i < data.length; i++) {
        if (
            !this.inArrayObj(this.tableData, data[i].id) &&
            this.mainId !== data[i].id
        ) {
          this.tableData.push({
            id: this.utils.createUUID(),
            relationId: this.mainId,
            relationType: this.relationType,
            associatedId: data[i].id,
            associatedType: this.associatedType,
            associatedData: data[i],
            relationData: {}
          });
        }
      }
    },

    inArrayObj(arry, key) {
      for (let i = 0; i < arry.length; i++) {
        if (arry[i].associatedId === key || arry[i].relationId === key) {
          return true;
        }
      }
      return false;
    },

    isRelation(val) {
      return val === this.mainId;
    },

    deleteAll() {
      this.tableData.splice(0, this.tableData.length);
    },
    relationValue(row, bs) {
      const value = {};
      let type = row.associatedType;
      let data = row.associatedData;
      if (!this.isRelation(row.relationId)) {
        type = row.relationType;
        data = row.relationData;
      }
      if (type === "case") {
        value.type = "关联案件";
        value.name = data.caseName;
        value.number = data.caseNumber;
        value.time = data.caseTime;
      } else if (type === "caseProject") {
        value.type = "关联群诉案件";
        value.name = data.projectName;
        value.number = data.projectCode;
        value.time = data.setTime;
      } else if (type === "dispute") {
        value.type = "关联纠纷";
        value.name = data.name;
        value.number = data.code;
        value.time = data.occurTime;
      } else if (type === "contract") {
        value.type = "关联合同";
        value.name = data.contractName
        value.number = data.contractCode
        value.time = data.createTime
      } else {
        value.type = null;
        value.name = null;
        value.time = null;
      }
      return value[bs];
    },
    view_(index, row) {
      if (row.dataSourceCode === 1) {
        if (row.dataTypeCode === this.utils.contractNature.main.id) {
          this.otherView(row, 'contract_approval_main_detail')
          return
        }
        /*合同变更*/
        if (row.dataTypeCode === this.utils.contractNature.change.id) {
          this.otherView(row, 'contract_change_main_detail')
          return
        }
      }
      /*合并审批*/
      if (row.mergeStateCode === 3 || row.mergeStateCode === 5) {
        this.moreView(row)
        return
      }
      /*合同*/
      if (row.dataTypeCode === this.utils.contractNature.main.id) {
        this.contractView(row)
        return
      }
      /*合同变更*/
      if (row.dataTypeCode === this.utils.contractNature.change.id) {
        this.changeView(row)
        return
      }
      /*终止协议*/
      if (row.dataTypeCode === this.utils.contractNature.stop.id) {
        this.stopView(row)
        return
      }
      /*补录主合同*/
      if (row.dataTypeCode === this.utils.contractNature.supMain.id) {
        this.supMainView(row)
        return
      }
      /*补录主合同*/
      if (row.dataTypeCode === this.utils.contractNature.supChange.id) {
        this.supChangeView(row)
        return
      }
    },
    otherView(row, address) {
      const uuid = this.utils.createUUID()
      this.layout.openNewTab('合同信息',
          address,
          address,
          uuid,
          {
            functionId: address + "," + uuid,
            ...this.utils.routeState.VIEW(row.id),
            view: 'old',
            dataSourceCode: 1
          }
      )
    },
    moreView(row) {
      if (row.dataStateCode === this.utils.dataState_BPM.SAVE.code)
      {
        const tabId = this.utils.createUUID();
        this.layout.openNewTab(
            "合同合并审批",
            "contract_more_main_detail",
            "contract_more_main_detail",
            tabId,
            {
              functionId: "contract_more_main_detail," + tabId,
              ...this.utils.routeState.VIEW(row.parentId)
            }
        )
      }
      else
      {
        taskApi.selectTaskId({businessKey: row.parentId,isView: 'true'}).then(res=>{
          const functionId = res.data.data[0].ID
          const tabId = this.utils.createUUID()
          this.layout.openNewTab("合同合并审批",
              "design_page",
              "design_page",
              tabId,
              {
                processInstanceId: res.data.data[0].PID,//流程实例
                taskId: res.data.data[0].ID,//任务ID
                businessKey: row.parentId, //业务数据ID
                functionId:functionId, //不传也可以，只有首环节需要传，实例启动就可以不传了
                entranceType: "FLOWABLE",
                type: "haveDealt",
                channel: 'business', //查看渠道，默认就传这个值即可，表示是从业务功能传过来的，首环节提交后过滤掉撤回按钮
                view: 'new',
              }
          )
        })
      }
    },
    supMainView(row){
      const uuid = this.utils.createUUID()
      this.layout.openNewTab("合同补录信息",
          "contract_supplement_main",
          "contract_supplement_main",
          uuid,
          {
            functionId: "contract_supplement_main," + uuid,//不涉及手动关闭，可以不传
            ...this.utils.routeState.VIEW(row.id),
            view: 'old'
          }
      )
    },
    supChangeView(row){
      const uuid = this.utils.createUUID()
      this.layout.openNewTab("合同补录信息",
          "contract_supplement_change",
          "contract_supplement_change",
          uuid,
          {
            functionId: "contract_supplement_change," + uuid,//不涉及手动关闭，可以不传
            ...this.utils.routeState.VIEW(row.id),
            view: 'old'
          }
      )
    },
    contractView(row) {
      if (row.dataStateCode === this.utils.dataState_BPM.SAVE.code)
      {
        const tabId = this.utils.createUUID();
        this.layout.openNewTab(
            "合同审批信息",
            "contract_approval_main_detail",
            "contract_approval_main_detail",
            tabId,
            {
              functionId: "contract_approval_main_detail," + tabId,
              ...this.utils.routeState.VIEW(row.id)
            }
        )
      }
      else
      {
        taskApi.selectTaskId({businessKey: row.id,isView: 'true'}).then(res=>{
          const functionId = res.data.data[0].ID
          const tabId = this.utils.createUUID()
          this.layout.openNewTab("合同审批信息",
              "design_page",
              "design_page",
              tabId,
              {
                processInstanceId: res.data.data[0].PID,//流程实例
                taskId: res.data.data[0].ID,//任务ID
                businessKey: row.id, //业务数据ID
                functionId:functionId, //不传也可以，只有首环节需要传，实例启动就可以不传了
                entranceType: "FLOWABLE",
                type: "haveDealt",
                channel: 'business', //查看渠道，默认就传这个值即可，表示是从业务功能传过来的，首环节提交后过滤掉撤回按钮
                view: 'new',
              }
          )
        })
      }
    },
    changeView(row) {
      if (row.dataStateCode === this.utils.dataState_BPM.SAVE.code)
      {
        const tabId = this.utils.createUUID();
        this.layout.openNewTab(
            "合同变更",
            "contract_change_main_detail",
            "contract_change_main_detail",
            tabId,
            {
              functionId: "contract_change_main_detail," + tabId,
              ...this.utils.routeState.VIEW(row.id)
            }
        )
      }
      else
      {
        taskApi.selectTaskId({businessKey: row.id,isView: 'true'}).then(res=>{
          const functionId = res.data.data[0].ID
          const tabId = this.utils.createUUID()
          this.layout.openNewTab("合同审批信息",
              "design_page",
              "design_page",
              tabId,
              {
                processInstanceId: res.data.data[0].PID,//流程实例
                taskId: res.data.data[0].ID,//任务ID
                businessKey: row.id, //业务数据ID
                functionId:functionId, //不传也可以，只有首环节需要传，实例启动就可以不传了
                entranceType: "FLOWABLE",
                type: "haveDealt",
                channel: 'business', //查看渠道，默认就传这个值即可，表示是从业务功能传过来的，首环节提交后过滤掉撤回按钮
                view: 'new',
              }
          )
        })
      }
    },
    stopView(row) {
      if (row.dataStateCode === this.utils.dataState_BPM.SAVE.code)
      {
        const tabId = this.utils.createUUID();
        this.layout.openNewTab(
            "合同终止协议",
            "contract_stop_main_detail",
            "contract_stop_main_detail",
            tabId,
            {
              functionId: "contract_stop_main_detail," + tabId,
              ...this.utils.routeState.VIEW(row.id)
            }
        )
      }
      else
      {
        taskApi.selectTaskId({businessKey: row.id,isView: 'true'}).then(res=>{
          const functionId = res.data.data[0].ID
          const tabId = this.utils.createUUID()
          this.layout.openNewTab("合同审批信息",
              "design_page",
              "design_page",
              tabId,
              {
                processInstanceId: res.data.data[0].PID,//流程实例
                taskId: res.data.data[0].ID,//任务ID
                businessKey: row.id, //业务数据ID
                functionId:functionId, //不传也可以，只有首环节需要传，实例启动就可以不传了
                entranceType: "FLOWABLE",
                type: "haveDealt",
                channel: 'business', //查看渠道，默认就传这个值即可，表示是从业务功能传过来的，首环节提交后过滤掉撤回按钮
                view: 'new',
              }
          )
        })
      }
    },
  }
};
</script>

<style scoped>
</style>