
5b7d634a488af718db147c788144ed6445c4cf5a	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.421.1754018536329.js\",\"contentHash\":\"aa8235e4225fd38c43250197b1f5d4a5\"}","integrity":"sha512-JmIC3dYwgREhNBx2jWKB1ZeKibeSR81nQtMTPJVzN+8KRJ/TpJfde5jHAsI0h1Rvj/bcowzCq2TH3ivpKxij3g==","time":1754018575957,"size":32175}