package com.klaw.service.imp.complianceTrainingServiceImp;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.klaw.dao.complianceTrainingDao.ComplianceTrainingLearningTableMapper;
import com.klaw.entity.complianceTrainingBean.*;
import com.klaw.service.complianceTrainingService.*;
import com.klaw.utils.OrgUtils;
import com.klaw.utils.PageUtils;
import com.klaw.utils.Utils;
import com.klaw.vo.OrgContextVo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【compliance_training_learning_table(合规培训学习表)】的数据库操作Service实现
 * @createDate 2024-12-02 08:53:30
 */
@Service
public class ComplianceTrainingLearningTableServiceImpl extends ServiceImpl<ComplianceTrainingLearningTableMapper, ComplianceTrainingLearningTable>
        implements ComplianceTrainingLearningTableService {

    @Resource
    private HouseholdComplianceTrainingLearningTableService householdComplianceTrainingLearningTableService;
    @Resource
    private TrainingCourseDetailsService trainingCourseDetailsService;
    @Resource
    private ComplianceTrainingReviewsService complianceTrainingReviewsService;

    @Override
    public Page<ComplianceTrainingLearningTable> queryPageData(JSONObject jsonObject) {
        QueryWrapper<ComplianceTrainingLearningTable> queryWrapper = new QueryWrapper<>();
        getFilter(jsonObject, queryWrapper);
        PageUtils<ComplianceTrainingLearningTable> page = page(new PageUtils<>(jsonObject), queryWrapper);
        List<ComplianceTrainingLearningTable> records = page.getRecords();
        for (ComplianceTrainingLearningTable record : records) {
            // 学习人员字段统计
            setLearningProgress(record);
            setTrainingStatus(record);
            setCourseDetails(record);
            setReviews(record);
        }
        page.setRecords(records);
        return page;
    }

    private void setReviews(ComplianceTrainingLearningTable record) {
        List<ComplianceTrainingReviews> reviewsList = complianceTrainingReviewsService.list(new QueryWrapper<ComplianceTrainingReviews>().eq("compliance_training_learning_table_id", record.getId()));
        record.setReviewsList(reviewsList);
    }

    private void setCourseDetails(ComplianceTrainingLearningTable record) {
        List<TrainingCourseDetails> courseDetailsList = trainingCourseDetailsService.list(new QueryWrapper<TrainingCourseDetails>().eq("compliance_training_learning_table_id", record.getId()));
        record.setTrainingCourseDetailsList(courseDetailsList);
    }

    private void setTrainingStatus(ComplianceTrainingLearningTable record) {
        // 当天日期＜培训开始时间，则培训状态=培训未开始
        Date currentDate = new Date();
        if (currentDate.before(record.getTrainingStartTime())) {
            record.setTrainingStatus("培训未开始");
        }
        // 培训开始时间≤当天日期≤培训结束日期，则培训状态=培训进行中
        if (currentDate.after(record.getTrainingStartTime()) && currentDate.before(record.getTrainingEndTime())) {
            record.setTrainingStatus("培训进行中");
        }
        // 当天日期＞培训结束时间，则培训状态=培训已结束
        if (currentDate.after(record.getTrainingEndTime())) {
            record.setTrainingStatus("培训已结束");
        }
    }

    @Override
    @Transactional
    public void saveData(ComplianceTrainingLearningTable complianceTrainingLearningTable) {
        saveOrUpdate(complianceTrainingLearningTable);
        // 保存课程信息
        List<TrainingCourseDetails> detailsList = complianceTrainingLearningTable.getTrainingCourseDetailsList();
        if (!CollectionUtils.isEmpty(detailsList)) {
//            Utils.saveChilds(detailsList, "compliance_training_learning_table_id", complianceTrainingLearningTable.getId(), trainingCourseDetailsService, true);
            for (TrainingCourseDetails details : detailsList) {
                details.setComplianceTrainingLearningTableId(complianceTrainingLearningTable.getId());
                if (details.getTrainingMaterials()!=null && details.getTrainingMaterials().contains(".mp4")){
                    details.setIsVideo("Y");
                }else{
                    details.setIsVideo("N");
                }
                trainingCourseDetailsService.saveOrUpdate(details);
            }
        }
        // 保存人员信息
        saveLearningPersonnelInfo(complianceTrainingLearningTable);

    }

    @Override
    public ComplianceTrainingLearningTable queryDataById(String id) {
        ComplianceTrainingLearningTable complianceTrainingLearningTable = getById(id);
        // 课程信息
        List<TrainingCourseDetails> trainingCourseDetails = trainingCourseDetailsService.list(new QueryWrapper<TrainingCourseDetails>().eq("compliance_training_learning_table_id", id));
        complianceTrainingLearningTable.setTrainingCourseDetailsList(trainingCourseDetails);

        int learningInProgressCount = 0;
        int learningNotStartedCount = 0;
        int learningCompletedCount = 0;
        // 学习进度统计信息
        List<HouseholdComplianceTrainingLearningTable> householdComplianceTrainingLearningTableList = householdComplianceTrainingLearningTableService.list(new QueryWrapper<HouseholdComplianceTrainingLearningTable>()
                .eq("compliance_training_learning_table_id", id));
        for (HouseholdComplianceTrainingLearningTable householdComplianceTrainingLearningTable : householdComplianceTrainingLearningTableList) {
            if (householdComplianceTrainingLearningTable.getLearningProgress() != null) {
                switch (householdComplianceTrainingLearningTable.getLearningProgress()) {
                    case "未开始":
                        learningNotStartedCount++;
                        break;
                    case "学习中":
                        learningInProgressCount++;
                        break;
                    case "已完成":
                        learningCompletedCount++;
                        break;
                }
            }
        }
        complianceTrainingLearningTable.setCompletedCount(learningCompletedCount);
        complianceTrainingLearningTable.setLearningInProgressCount(learningInProgressCount);
        complianceTrainingLearningTable.setNotStartedCount(learningNotStartedCount);
//        complianceTrainingLearningTable.setHouseholdComplianceTrainingLearningTableList(householdComplianceTrainingLearningTableList);
        // 评论信息
        List<ComplianceTrainingReviews> reviewsList = complianceTrainingReviewsService.list(new QueryWrapper<ComplianceTrainingReviews>()
                .eq("compliance_training_learning_table_id", id)
                .orderByDesc("create_time"));
        complianceTrainingLearningTable.setReviewsList(reviewsList);
        return complianceTrainingLearningTable;
    }

    @Override
    public void likeOrDislike(JSONObject jsonObject) {
        String id = jsonObject.containsKey("id") ? jsonObject.getString("id") : null;
        Boolean like = jsonObject.containsKey("like") ? jsonObject.getBoolean("like") : null;
        Boolean dislike = jsonObject.containsKey("dislike") ? jsonObject.getBoolean("dislike") : null;
        UpdateWrapper<ComplianceTrainingLearningTable> updateWrapper = new UpdateWrapper<>();
        if (like != null) {
            if (like) {
                updateWrapper.eq("id", id)
                        .setSql("like_count = like_count + 1");
            } else {
                updateWrapper.eq("id", id)
                        .setSql("like_count = like_count - 1");
            }
        }
        if (dislike != null) {
            if (dislike) {
                updateWrapper.eq("id", id)
                        .setSql("dislike_count = dislike_count + 1");
            } else {
                updateWrapper.eq("id", id)
                        .setSql("dislike_count = dislike_count - 1");
            }
        }
        update(updateWrapper);
    }


    private void saveLearningPersonnelInfo(ComplianceTrainingLearningTable complianceTrainingLearningTable) {
        // 主修信息
        String[] mandatoryObjectIdArray = complianceTrainingLearningTable.getMandatoryObjectId().split(",");
        String[] mandatoryObjectNameArray = complianceTrainingLearningTable.getMandatoryObjectName().split(",");
        // 选修信息
//        String[] electiveObjectIdArray = complianceTrainingLearningTable.getElectiveObjectId().split(",");
//        String[] electiveObjectNameArray = complianceTrainingLearningTable.getElectiveObjectName().split(",");
        List<HouseholdComplianceTrainingLearningTable> householdComplianceTrainingLearningTableList = new ArrayList<>();
        for (int i = 0; i < mandatoryObjectIdArray.length; i++) {
            HouseholdComplianceTrainingLearningTable householdComplianceTrainingLearningTable = new HouseholdComplianceTrainingLearningTable();
            BeanUtils.copyProperties(complianceTrainingLearningTable, householdComplianceTrainingLearningTable);
            householdComplianceTrainingLearningTable.setId(Utils.createUUID());
            householdComplianceTrainingLearningTable.setComplianceTrainingLearningTableId(complianceTrainingLearningTable.getId());
            OrgContextVo orgContextVo = OrgUtils.getOrgContextByCode(mandatoryObjectIdArray[i]);
            householdComplianceTrainingLearningTable.setCreatePsnId(orgContextVo.getCurrentPsnId());
            householdComplianceTrainingLearningTable.setCreatePsnName(mandatoryObjectNameArray[i]);
            householdComplianceTrainingLearningTableList.add(householdComplianceTrainingLearningTable);
        }
        householdComplianceTrainingLearningTableService.saveOrUpdateBatch(householdComplianceTrainingLearningTableList);
        // 保存选修人员信息

//        householdComplianceTrainingLearningTableList.clear();
//        for (int i = 0; i < electiveObjectIdArray.length; i++) {
//            householdComplianceTrainingLearningTable.setCreatePsnId(electiveObjectIdArray[i]);
//            householdComplianceTrainingLearningTable.setCreatePsnName(electiveObjectNameArray[i]);
//            householdComplianceTrainingLearningTable.setIsMandatory(0);
//            BeanUtils.copyProperties(complianceTrainingLearningTable, householdComplianceTrainingLearningTable);
//            householdComplianceTrainingLearningTableList.add(householdComplianceTrainingLearningTable);
//        }
//        householdComplianceTrainingLearningTableService.saveOrUpdateBatch(householdComplianceTrainingLearningTableList);
    }

    private void setLearningProgress(ComplianceTrainingLearningTable record) {
        QueryWrapper<HouseholdComplianceTrainingLearningTable> queryWrapper = new QueryWrapper<>();
        QueryWrapper<HouseholdComplianceTrainingLearningTable> queryWrapper1 = new QueryWrapper<>();
        QueryWrapper<HouseholdComplianceTrainingLearningTable> queryWrapper2 = new QueryWrapper<>();
        String id = record.getId();
        queryWrapper
                .eq("compliance_training_learning_table_id", id)
                .eq("learning_progress", "未开始");
        int notStartedCount = householdComplianceTrainingLearningTableService.count(queryWrapper);
        record.setNotStartedCount(notStartedCount);
        queryWrapper1
                .eq("compliance_training_learning_table_id", id)
                .eq("learning_progress", "学习中");
        int learningInProgressCount = householdComplianceTrainingLearningTableService.count(queryWrapper1);
        record.setLearningInProgressCount(learningInProgressCount);
        queryWrapper2
                .eq("compliance_training_learning_table_id", id)
                .eq("learning_progress", "已完成");
        int completedCount = householdComplianceTrainingLearningTableService.count(queryWrapper2);
        record.setCompletedCount(completedCount);
    }

    private void getFilter(JSONObject jsonObject, QueryWrapper<ComplianceTrainingLearningTable> queryWrapper) {
        String trainingTopic = jsonObject.containsKey("trainingTopic") ? jsonObject.getString("trainingTopic") : null;
        String trainingCategory = jsonObject.containsKey("trainingCategory") ? jsonObject.getString("trainingCategory") : null;
        String fuzzyValue = jsonObject.containsKey("fuzzyValue") ? jsonObject.getString("fuzzyValue") : null;
        //顺序字段
        String sortName = jsonObject.containsKey("sortName") ? jsonObject.getString("sortName") : null;
        //顺序
        boolean order = jsonObject.containsKey("order") ? jsonObject.getBoolean("order") : false;
        if (StringUtils.isNotBlank(trainingTopic)) {
            queryWrapper.like("training_topic", trainingTopic);
        }
        if (StringUtils.isNotBlank(trainingCategory)) {
            queryWrapper.like("training_category", trainingCategory);
        }
        //按送审时间倒序展示
        queryWrapper.orderByDesc("training_start_time");
        // 模糊搜索匹配字段
        String[] cols = {"training_topic", "training_category"};
        Utils.fuzzyValueQuery(queryWrapper, cols, fuzzyValue);

        if (StringUtils.isNotBlank(sortName)) {
            queryWrapper.orderBy(true, order, Utils.humpToLine2(sortName));
        } else {
            queryWrapper.orderBy(true, order, "training_start_time");
        }
    }
}
