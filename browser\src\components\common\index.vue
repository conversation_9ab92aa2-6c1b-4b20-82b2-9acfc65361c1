<template>
  <grid-layout ref="layoutEngine" :auto-size="autoSize" :col-num="defaultColNum"
               :is-draggable="isDraggable" :is-mirrored="isMirrored" :is-resizable="isResizable"
               :layout.sync="layouts" :margin="[0, 0]" :responsive="responsive" :row-height="rowHeight"
               :use-css-transforms="useCssTransform" :vertical-compact="verticalCompact"
               class="w100 h100" style="overflow-x: hidden;background: #F7FAFF">
    <grid-item v-for="(item) in layouts" :key="item.i" :h="item.h" :i="item.i" :static="true" :w="item.w"
               :x="item.x" :y="item.y" class="sg-grid-layout-wrap" style="padding: 5px;">
      <el-card v-if="$route.params.component==='index'" class="box-card" shadow="hover" style="border-radius: 5px;">

        <component :is="item.view" v-if="['Echarts'].includes(item.view.name)&&$route.name==='index'"></component>
        <keep-alive v-else>
          <component :is="item.view"></component>
        </keep-alive>
      </el-card>
    </grid-item>
  </grid-layout>
</template>

<script>
import {sysDashboardQuery} from "../../api";
import * as VueGridLayout from "vue-grid-layout";
import {mapState} from "vuex";

export default {
  name: 'index',
  components: {
    GridLayout: VueGridLayout.GridLayout,
    GridItem: VueGridLayout.GridItem,
  },
  data() {
    return {
      layouts: [],
      defaultColNum: 12,
      isDraggable: false,
      isResizable: false,
      autoSize: true,
      responsive: false,
      isMirrored: false,
      verticalCompact: false,
      useCssTransform: true,
      rowHeight: 1,
      yMultiple: 4,
    }
  },
  computed: {
    ...mapState(['dashboardLayouts']),
  },
  mounted() {
    const that = this;
    window.onresize = () => {
      that.reLayout()
    };
  },
  methods: {
    reLayout() {
      if (this.$route.name === 'index') {
        this.layouts.forEach(item => {
          if (item.autoHeight) {
            this.layoutAutoHeight(item);
          }
        })
        this.layoutUpdate();
      }
    },
    queryDashboardLayout() {
      sysDashboardQuery().then(res => {
        if (res.data.success) {
          if (res.data.rows.length <= 0) {
            //无默认情况下寻找其他可用
            this.queryUsable()
            return false
          }
          this.getDataSuccess(res)
        } else {
          this.$message.error(res.data.message)
        }
      }).catch(e => {
        console.log(e)
      });
    },
    //无默认情况下寻找其他可用
    queryUsable() {
      // queryMyDashBoardScope().then(res => {
      //     if(res.data.success){
      //         if(res.data.rows.length <= 0){
      //             return
      //         }
      //         this.getDataSuccess(res)
      //     } else {
      //         this.$message.error(res.data.message)
      //     }
      // })
    },
    getDataSuccess(res) {
      let layout = res.data.rows[0]
      localStorage.setItem('dashboardCode', layout.dashboardCode)
      let rows = layout.items;
      rows.forEach(item => {
        item.view = this.getView(item.componentPath);
        this.extHandler(item, true)
        if (item.autoHeight) {
          this.layoutAutoHeight(item);
        }
        this.layouts.push(item);
      })
      this.layoutUpdate();
    },
    getView(name) {
      let component = this.dashboardLayouts.find(item => item.path === name)
      return component ? component['component']['default'] : false
    },
    extHandler(item, isBoolean) {
      item.isClickType = isBoolean ? item.isClickType > 0 : Number(item.isClickType)
      item.isDraggable = isBoolean ? item.isDraggable > 0 : Number(item.isDraggable)
      item.isResizable = isBoolean ? item.isResizable > 0 : Number(item.isResizable)
      item.static = isBoolean ? item.static > 0 : Number(item.static)
      item.autoHeight = isBoolean ? item.autoHeight > 0 : Number(item.autoHeight)
    },
    //高度自适应
    layoutAutoHeight(gridItem) {
      /*计算高度*/
      // gridItem.h = this.handleHeight(this.$refs.layoutEngine.$el.clientHeight);
      if (gridItem.autoHeight) {
        gridItem.h = this.handleHeight(this.$refs.layoutEngine.$el.clientHeight * gridItem.heightScale / 100);
      } else {
        gridItem.h = this.handleHeight(this.$refs.layoutEngine.$el.clientHeight);
      }
      /*高度自适应，重新Y轴计算位置*/
      if (gridItem.y > 0) {
        gridItem.y = this.handleY(gridItem.y);
      }
    },
    //高度等比例缩小
    handleHeight(h) {
      return Math.floor(h / this.rowHeight);
      //return h / this.heightMultiple;
    },
    // 重新计算布局
    layoutUpdate() {
      this.$refs.layoutEngine.layoutUpdate();
    },
    //Y轴等比例缩小
    handleY(y) {
      return y / this.yMultiple;
    },
  },
  created() {
    this.queryDashboardLayout();
  },
  beforeRouteEnter(to, from, next) {
    next(vm => {
      vm.$nextTick(() => {
        vm.reLayout()
      })
    })
  }
}

</script>

<style lang="scss" scoped>
.box-card {
  width: 100%;
  height: 100%;

  /deep/ .el-tabs__header {
    .is-active, .el-tabs__item:hover {
      background-color: white !important;
      border-color: white !important;
    }
  }
}

/deep/ .el-card__body {
  padding: 0px;

}
</style>