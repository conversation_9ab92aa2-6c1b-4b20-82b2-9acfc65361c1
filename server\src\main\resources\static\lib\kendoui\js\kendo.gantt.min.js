/** 
 * Kendo UI v2016.3.1118 (http://www.telerik.com/kendo-ui)                                                                                                                                              
 * Copyright 2016 Telerik AD. All rights reserved.                                                                                                                                                      
 *                                                                                                                                                                                                      
 * Kendo UI commercial licenses may be obtained at                                                                                                                                                      
 * http://www.telerik.com/purchase/license-agreement/kendo-ui-complete                                                                                                                                  
 * If you do not own a commercial license, this file shall be governed by the trial license terms.                                                                                                      
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       

*/
!function(e,define){define("util/main.min",["kendo.core.min"],e)}(function(){return function(){function e(e){return typeof e!==P}function t(e,t){var n=i(t);return R.round(e*n)/n}function i(e){return e?R.pow(10,e):1}function n(e,t,i){return R.max(R.min(e,i),t)}function r(e){return e*W}function s(e){return e/W}function o(e){return"number"==typeof e&&!isNaN(e)}function a(t,i){return e(t)?t:i}function d(e){return e*e}function l(e){var t,i=[];for(t in e)i.push(t+e[t]);return i.sort().join("")}function c(e){var t,i=2166136261;for(t=0;t<e.length;++t)i+=(i<<1)+(i<<4)+(i<<7)+(i<<8)+(i<<24),i^=e.charCodeAt(t);return i>>>0}function h(e){return c(l(e))}function u(e){var t,i=e.length,n=N,r=L;for(t=0;t<i;t++)r=R.max(r,e[t]),n=R.min(n,e[t]);return{min:n,max:r}}function f(e){return u(e).min}function p(e){return u(e).max}function g(e){return v(e).min}function m(e){return v(e).max}function v(e){var t,i,n,r=N,s=L;for(t=0,i=e.length;t<i;t++)n=e[t],null!==n&&isFinite(n)&&(r=R.min(r,n),s=R.max(s,n));return{min:r===N?void 0:r,max:s===L?void 0:s}}function _(e){if(e)return e[e.length-1]}function y(e,t){return e.push.apply(e,t),e}function k(e){return A.template(e,{useWithBlock:!1,paramName:"d"})}function w(t,i){return e(i)&&null!==i?" "+t+"='"+i+"' ":""}function b(e){var t,i="";for(t=0;t<e.length;t++)i+=w(e[t][0],e[t][1]);return i}function S(t){var i,n,r="";for(i=0;i<t.length;i++)n=t[i][1],e(n)&&(r+=t[i][0]+":"+n+";");if(""!==r)return r}function x(e){return"string"!=typeof e&&(e+="px"),e}function T(e){var t,i,n=[];if(e)for(t=A.toHyphens(e).split("-"),i=0;i<t.length;i++)n.push("k-pos-"+t[i]);return n.join(" ")}function D(t){return""===t||null===t||"none"===t||"transparent"===t||!e(t)}function C(e){for(var t={1:"i",10:"x",100:"c",2:"ii",20:"xx",200:"cc",3:"iii",30:"xxx",300:"ccc",4:"iv",40:"xl",400:"cd",5:"v",50:"l",500:"d",6:"vi",60:"lx",600:"dc",7:"vii",70:"lxx",700:"dcc",8:"viii",80:"lxxx",800:"dccc",9:"ix",90:"xc",900:"cm",1e3:"m"},i=[1e3,900,800,700,600,500,400,300,200,100,90,80,70,60,50,40,30,20,10,9,8,7,6,5,4,3,2,1],n="";e>0;)e<i[0]?i.shift():(n+=t[i[0]],e-=i[0]);return n}function H(e){var t,i,n,r,s;for(e=e.toLowerCase(),t={i:1,v:5,x:10,l:50,c:100,d:500,m:1e3},i=0,n=0,r=0;r<e.length;++r){if(s=t[e.charAt(r)],!s)return null;i+=s,s>n&&(i-=2*n),n=s}return i}function F(e){var t=Object.create(null);return function(){var i,n="";for(i=arguments.length;--i>=0;)n+=":"+arguments[i];return n in t?t[n]:t[n]=e.apply(this,arguments)}}function M(e){for(var t,i,n=[],r=0,s=e.length;r<s;)t=e.charCodeAt(r++),t>=55296&&t<=56319&&r<s?(i=e.charCodeAt(r++),56320==(64512&i)?n.push(((1023&t)<<10)+(1023&i)+65536):(n.push(t),r--)):n.push(t);return n}function I(e){return e.map(function(e){var t="";return e>65535&&(e-=65536,t+=String.fromCharCode(e>>>10&1023|55296),e=56320|1023&e),t+=String.fromCharCode(e)}).join("")}function z(e,t){function i(e,i){for(var n=[],r=0,s=0,o=0;r<e.length&&s<i.length;)t(e[r],i[s])<=0?n[o++]=e[r++]:n[o++]=i[s++];return r<e.length&&n.push.apply(n,e.slice(r)),s<i.length&&n.push.apply(n,i.slice(s)),n}return e.length<2?e.slice():function n(e){var t,r,s;return e.length<=1?e:(t=Math.floor(e.length/2),r=e.slice(0,t),s=e.slice(t),r=n(r),s=n(s),i(r,s))}(e)}var R=Math,A=window.kendo,E=A.deepExtend,W=R.PI/180,N=Number.MAX_VALUE,L=-Number.MAX_VALUE,P="undefined",B=Date.now;B||(B=function(){return(new Date).getTime()}),E(A,{util:{MAX_NUM:N,MIN_NUM:L,append:y,arrayLimits:u,arrayMin:f,arrayMax:p,defined:e,deg:s,hashKey:c,hashObject:h,isNumber:o,isTransparent:D,last:_,limitValue:n,now:B,objectKey:l,round:t,rad:r,renderAttr:w,renderAllAttr:b,renderPos:T,renderSize:x,renderStyle:S,renderTemplate:k,sparseArrayLimits:v,sparseArrayMin:g,sparseArrayMax:m,sqr:d,valueOrDefault:a,romanToArabic:H,arabicToRoman:C,memoize:F,ucs2encode:I,ucs2decode:M,mergeSort:z}}),A.drawing.util=A.util,A.dataviz.util=A.util}(),window.kendo},"function"==typeof define&&define.amd?define:function(e,t,i){(i||t)()}),function(e,define){define("pdf/core.min",["kendo.core.min","util/main.min"],e)}(function(){!function(e,t,i){"use strict";function n(){function e(){var t,r,s;for(t=0;t<arguments.length;++t){if(r=arguments[t],r===i)throw Error("Cannot output undefined to PDF");if(r instanceof w)r.beforeRender(e),r.render(e);else if(O(r))g(r,e);else if(p(r))m(r,e);else if("number"==typeof r){if(isNaN(r))throw Error("Cannot output NaN to PDF");s=r.toFixed(7),s.indexOf(".")>=0&&(s=s.replace(/\.?0+$/,"")),"-0"==s&&(s="0"),n.writeString(s)}else/string|boolean/.test(typeof r)?n.writeString(r+""):"function"==typeof r.get?n.write(r.get()):"object"==typeof r&&(r?e(new V(r)):n.writeString("null"))}}var t=0,n=R();return e.writeData=function(e){n.write(e)},e.withIndent=function(i){++t,i(e),--t},e.indent=function(){e(ne,h("",2*t,"  ")),e.apply(null,arguments)},e.offset=function(){return n.offset()},e.toString=function(){throw Error("FIX CALLER")},e.get=function(){return n.get()},e.stream=function(){return n},e}function r(e,t){var i=e.beforeRender,n=e.render;e.beforeRender=function(){},e.render=function(e){e(t," 0 R")},e.renderFull=function(r){e._offset=r.offset(),r(t," 0 obj "),i.call(e,r),n.call(e,r),r(" endobj")}}function s(e){var t,i,n;if("function"!=typeof e&&(t=e,e=function(e,i){return e in t?t[e]:i}),i=e("paperSize",oe.a4),!i)return{};if("string"==typeof i&&(i=oe[i.toLowerCase()],null==i))throw Error("Unknown paper size");return i[0]=k(i[0]),i[1]=k(i[1]),e("landscape",!1)&&(i=[Math.max(i[0],i[1]),Math.min(i[0],i[1])]),n=e("margin"),n&&("string"==typeof n||"number"==typeof n?(n=k(n,0),n={left:n,top:n,right:n,bottom:n}):n={left:k(n.left,0),top:k(n.top,0),right:k(n.right,0),bottom:k(n.bottom,0)},e("addMargin")&&(i[0]+=n.left+n.right,i[1]+=n.top+n.bottom)),{paperSize:i,margin:n}}function o(e){function t(t,i){return e&&null!=e[t]?e[t]:i}var i,o,a=this,d=n(),l=0,c=[];a.getOption=t,a.attach=function(e){return c.indexOf(e)<0&&(r(e,++l),c.push(e)),e},a.pages=[],a.FONTS={},a.IMAGES={},a.GRAD_COL_FUNCTIONS={},a.GRAD_OPC_FUNCTIONS={},a.GRAD_COL={},a.GRAD_OPC={},i=a.attach(new K),o=a.attach(new Q),i.setPages(o),a.addPage=function(e){var t,i,r,d=s(function(t,i){return e&&null!=e[t]?e[t]:i}),l=d.paperSize,c=d.margin,h=l[0],u=l[1];return c&&(h-=c.left+c.right,u-=c.top+c.bottom),t=new X(n(),null,(!0)),i={Contents:a.attach(t),Parent:o,MediaBox:[0,0,l[0],l[1]]},r=new $(a,i),r._content=t,o.addPage(a.attach(r)),r.transform(1,0,0,-1,0,l[1]),c&&(r.translate(c.left,c.top),r.rect(0,0,h,u),r.clip()),a.pages.push(r),r},a.render=function(){var e,n;for(d("%PDF-1.4",ne,"%ÂÁÚÏÎ",ne,ne),e=0;e<c.length;++e)c[e].renderFull(d),d(ne,ne);for(n=d.offset(),d("xref",ne,0," ",c.length+1,ne),d("0000000000 65535 f ",ne),e=0;e<c.length;++e)d(u(c[e]._offset,10)," 00000 n ",ne);return d(ne),d("trailer",ne),d(new V({Size:c.length+1,Root:i,Info:new V({Producer:new G(t("producer","Kendo UI PDF Generator v."+te.version)),Title:new G(t("title","")),Author:new G(t("author","")),Subject:new G(t("subject","")),Keywords:new G(t("keywords","")),Creator:new G(t("creator","Kendo UI PDF Generator v."+te.version)),CreationDate:t("date",new Date)})}),ne,ne),d("startxref",ne,n,ne),d("%%EOF",ne),d.stream().offset(0)}}function a(t,i){function n(){e.console&&(e.console.error?e.console.error("Cannot load URL: %s",t):e.console.log("Cannot load URL: %s",t)),i(null)}var r=new XMLHttpRequest;r.open("GET",t,!0),ie&&(r.responseType="arraybuffer"),r.onload=function(){200==r.status||304==r.status?i(ie?new Uint8Array(r.response):new VBArray(r.responseBody).toArray()):n()},r.onerror=n,r.send(null)}function d(e,t){var i=ae[e];i?t(i):a(e,function(i){if(null==i)throw Error("Cannot load font from "+e);var n=new te.pdf.TTFFont(i);ae[e]=n,t(n)})}function l(e,t){function n(e){l.src=e,l.complete&&!te.support.browser.msie?s():(l.onload=s,l.onerror=r)}function r(){t(de[e]="TAINTED")}function s(){var n,s,d,c,h,u,f,p,g,m,v,_;if(a&&/^image\/jpe?g$/i.test(a.type))return n=new FileReader,n.onload=function(){l=new x(l.width,l.height,R(new Uint8Array(this.result))),URL.revokeObjectURL(o),t(de[e]=l)},n.readAsArrayBuffer(a),i;s=document.createElement("canvas"),s.width=l.width,s.height=l.height,d=s.getContext("2d"),d.drawImage(l,0,0);try{c=d.getImageData(0,0,l.width,l.height)}catch(y){return r()}finally{o&&URL.revokeObjectURL(o)}for(h=!1,u=R(),f=R(),p=c.data,g=0;g<p.length;)u.writeByte(p[g++]),u.writeByte(p[g++]),u.writeByte(p[g++]),m=p[g++],m<255&&(h=!0),f.writeByte(m);h?l=new T(l.width,l.height,u,f):(v=s.toDataURL("image/jpeg"),v=v.substr(v.indexOf(";base64,")+8),_=R(),_.writeBase64(v),_.offset(0),l=new x(l.width,l.height,_)),t(de[e]=l)}var o,a,d,l=de[e];l?t(l):(l=new Image,/^data:/i.test(e)||(l.crossOrigin="Anonymous"),ie&&!/^data:/i.test(e)?(d=new XMLHttpRequest,d.onload=function(){a=d.response,o=URL.createObjectURL(a),n(o)},d.onerror=r,d.open("GET",e,!0),d.responseType="blob",d.send()):n(e))}function c(e){return function(t,i){var n=t.length,r=n;if(0===n)return i();for(;r-- >0;)e(t[r],function(){0===--n&&i()})}}function h(e,t,i){for(;e.length<t;)e=i+e;return e}function u(e,t){return h(e+"",t,"0")}function f(e,t){return Object.prototype.hasOwnProperty.call(e,t)}function p(e){return e instanceof Date}function g(e,t){t("["),e.length>0&&t.withIndent(function(){for(var i=0;i<e.length;++i)i>0&&i%8===0?t.indent(e[i]):t(" ",e[i])}),t(" ]")}function m(e,t){t("(D:",u(e.getUTCFullYear(),4),u(e.getUTCMonth()+1,2),u(e.getUTCDate(),2),u(e.getUTCHours(),2),u(e.getUTCMinutes(),2),u(e.getUTCSeconds(),2),"Z)")}function v(e){return e*(72/25.4)}function _(e){return v(10*e)}function y(e){return 72*e}function k(e,i){var n,r;if("number"==typeof e)return e;if("string"==typeof e&&(n=/^\s*([0-9.]+)\s*(mm|cm|in|pt)\s*$/.exec(e),n&&(r=t(n[1]),!isNaN(r))))return"pt"==n[2]?r:{mm:v,cm:_,"in":y}[n[2]](r);if(null!=i)return i;throw Error("Can't parse unit: "+e)}function w(){}function b(e,t,i){i||(i=w),e.prototype=new i;for(var n in t)f(t,n)&&(e.prototype[n]=t[n]);return e}function S(e){return f(q,e)?q[e]:q[e]=new j(e)}function x(e,t,i){this.asStream=function(){var n=new X(i,{Type:S("XObject"),Subtype:S("Image"),Width:e,Height:t,BitsPerComponent:8,ColorSpace:S("DeviceRGB"),Filter:S("DCTDecode")});return n._resourceName=S("I"+ ++re),n}}function T(e,t,i,n){this.asStream=function(r){var s=new X(n,{Type:S("XObject"),Subtype:S("Image"),Width:e,Height:t,BitsPerComponent:8,ColorSpace:S("DeviceGray")},(!0)),o=new X(i,{Type:S("XObject"),Subtype:S("Image"),Width:e,Height:t,BitsPerComponent:8,ColorSpace:S("DeviceRGB"),SMask:r.attach(s)},(!0));return o._resourceName=S("I"+ ++re),o}}function D(e){return e.map(function(e){return O(e)?D(e):"number"==typeof e?(Math.round(1e3*e)/1e3).toFixed(3):e}).join(" ")}function C(e,t,i,n,r,s,o){var a=D([t,i,n,r,s,o]),d=e.GRAD_COL_FUNCTIONS[a];return d||(d=e.GRAD_COL_FUNCTIONS[a]=e.attach(new V({FunctionType:2,Domain:[0,1],Range:[0,1,0,1,0,1],N:1,C0:[t,i,n],C1:[r,s,o]}))),d}function H(e,t,i){var n=D([t,i]),r=e.GRAD_OPC_FUNCTIONS[n];return r||(r=e.GRAD_OPC_FUNCTIONS[n]=e.attach(new V({FunctionType:2,Domain:[0,1],Range:[0,1],N:1,C0:[t],C1:[i]}))),r}function F(e,t){function i(e){return 1==e.length?e[0]:{FunctionType:3,Functions:e,Domain:[0,1],Bounds:h,Encode:u}}var n,r,s,o,a,d=!1,l=[],c=[],h=[],u=[];for(n=1;n<t.length;++n)r=t[n-1],s=t[n],o=r.color,a=s.color,c.push(C(e,o.r,o.g,o.b,a.r,a.g,a.b)),(o.a<1||a.a<1)&&(d=!0),h.push(s.offset),u.push(0,1);if(d)for(n=1;n<t.length;++n)r=t[n-1],s=t[n],o=r.color,a=s.color,l.push(H(e,o.a,a.a));return h.pop(),{hasAlpha:d,colors:i(c),opacities:d?i(l):null}}function M(e,t,i,n,r,s){var o,a,d;return s||(d=[t].concat(n),i.forEach(function(e){d.push(e.offset,e.color.r,e.color.g,e.color.b)}),a=D(d),o=e.GRAD_COL[a]),o||(o=new V({Type:S("Shading"),ShadingType:t?3:2,ColorSpace:S("DeviceRGB"),Coords:n,Domain:[0,1],Function:r,Extend:[!0,!0]}),e.attach(o),o._resourceName="S"+ ++re,a&&(e.GRAD_COL[a]=o)),o}function I(e,t,i,n,r,s){var o,a,d;return s||(d=[t].concat(n),i.forEach(function(e){d.push(e.offset,e.color.a)}),a=D(d),o=e.GRAD_OPC[a]),o||(o=new V({Type:S("ExtGState"),AIS:!1,CA:1,ca:1,SMask:{Type:S("Mask"),S:S("Luminosity"),G:e.attach(new X("/a0 gs /s0 sh",{Type:S("XObject"),Subtype:S("Form"),FormType:1,BBox:s?[s.left,s.top+s.height,s.left+s.width,s.top]:[0,1,1,0],Group:{Type:S("Group"),S:S("Transparency"),CS:S("DeviceGray"),I:!0},Resources:{ExtGState:{a0:{CA:1,ca:1}},Shading:{s0:{ColorSpace:S("DeviceGray"),Coords:n,Domain:[0,1],ShadingType:t?3:2,Function:r,Extend:[!0,!0]}}}}))}}),e.attach(o),o._resourceName="O"+ ++re,a&&(e.GRAD_OPC[a]=o)),o}function z(e,t,i){var n="radial"==t.type,r=F(e,t.stops),s=n?[t.start.x,t.start.y,t.start.r,t.end.x,t.end.y,t.end.r]:[t.start.x,t.start.y,t.end.x,t.end.y],o=M(e,n,t.stops,s,r.colors,t.userSpace&&i),a=r.hasAlpha?I(e,n,t.stops,s,r.opacities,t.userSpace&&i):null;return{hasAlpha:r.hasAlpha,shading:o,opacity:a}}function R(t){function i(){return D>=C}function n(){return D<C?t[D++]:0}function r(e){w(D),t[D++]=255&e,D>C&&(C=D)}function s(){return n()<<8|n()}function o(e){r(e>>8),r(e)}function a(){var e=s();return e>=32768?e-65536:e}function d(e){o(e<0?e+65536:e)}function l(){return 65536*s()+s()}function c(e){o(e>>>16&65535),o(65535&e)}function h(){var e=l();return e>=2147483648?e-4294967296:e}function u(e){c(e<0?e+4294967296:e)}function f(){return l()/65536}function p(e){c(Math.round(65536*e))}function g(){return h()/65536}function m(e){u(Math.round(65536*e))}function v(e){return k(e,n)}function _(e){return String.fromCharCode.apply(String,v(e))}function y(e){for(var t=0;t<e.length;++t)r(e.charCodeAt(t))}function k(e,t){for(var i=Array(e),n=0;n<e;++n)i[n]=t();return i}var w,b,S,x,T,D=0,C=0;return null==t?t=ie?new Uint8Array(256):[]:C=t.length,w=ie?function(e){if(e>=t.length){var i=new Uint8Array(Math.max(e+256,2*t.length));i.set(t,0),t=i}}:function(){},b=ie?function(){return new Uint8Array(t.buffer,0,C)}:function(){return t},S=ie?function(e){if("string"==typeof e)return y(e);var i=e.length;w(D+i),t.set(e,D),D+=i,D>C&&(C=D)}:function(e){if("string"==typeof e)return y(e);for(var t=0;t<e.length;++t)r(e[t])},x=ie?function(e,i){if(t.buffer.slice)return new Uint8Array(t.buffer.slice(e,e+i));var n=new Uint8Array(i);return n.set(new Uint8Array(t.buffer,e,i)),n}:function(e,i){return t.slice(e,e+i)},T={eof:i,readByte:n,writeByte:r,readShort:s,writeShort:o,readLong:l,writeLong:c,readFixed:f,writeFixed:p,readShort_:a,writeShort_:d,readLong_:h,writeLong_:u,readFixed_:g,writeFixed_:m,read:v,write:S,readString:_,writeString:y,times:k,get:b,slice:x,offset:function(e){return null!=e?(D=e,T):D},skip:function(e){D+=e},toString:function(){throw Error("FIX CALLER.  BinaryStream is no longer convertible to string!")},length:function(){return C},saveExcursion:function(e){var t=D;try{return e()}finally{D=t}},writeBase64:function(t){e.atob?y(e.atob(t)):S(se.decode(t))},base64:function(){return se.encode(b())}}}function A(e){return e.replace(/^\s*(['"])(.*)\1\s*$/,"$2")}function E(e){var t,i=/^\s*((normal|italic)\s+)?((normal|small-caps)\s+)?((normal|bold|\d+)\s+)?(([0-9.]+)(px|pt))(\/(([0-9.]+)(px|pt)|normal))?\s+(.*?)\s*$/i,n=i.exec(e);return n?(t=n[8]?parseInt(n[8],10):12,{italic:n[2]&&"italic"==n[2].toLowerCase(),variant:n[4],bold:n[6]&&/bold|700/i.test(n[6]),fontSize:t,lineHeight:n[12]?"normal"==n[12]?t:parseInt(n[12],10):null,fontFamily:n[14].split(/\s*,\s*/g).map(A)}):{fontSize:12,fontFamily:"sans-serif"}}function W(e){function t(t){return e.bold&&(t+="|bold"),e.italic&&(t+="|italic"),t.toLowerCase()}var i,n,r,s=e.fontFamily;if(s instanceof Array)for(r=0;r<s.length&&(i=t(s[r]),!(n=ee[i]));++r);else n=ee[s.toLowerCase()];for(;"function"==typeof n;)n=n();return n||(n="Times-Roman"),n}function N(e,t){e=e.toLowerCase(),ee[e]=function(){return ee[t]},ee[e+"|bold"]=function(){return ee[t+"|bold"]},ee[e+"|italic"]=function(){return ee[t+"|italic"]},ee[e+"|bold|italic"]=function(){return ee[t+"|bold|italic"]}}function L(e,t){if(1==arguments.length)for(var i in e)f(e,i)&&L(i,e[i]);else switch(e=e.toLowerCase(),ee[e]=t,e){case"dejavu sans":ee["sans-serif"]=t;break;case"dejavu sans|bold":ee["sans-serif|bold"]=t;break;case"dejavu sans|italic":ee["sans-serif|italic"]=t;break;case"dejavu sans|bold|italic":ee["sans-serif|bold|italic"]=t;break;case"dejavu serif":ee.serif=t;break;case"dejavu serif|bold":ee["serif|bold"]=t;break;case"dejavu serif|italic":ee["serif|italic"]=t;break;case"dejavu serif|bold|italic":ee["serif|bold|italic"]=t;break;case"dejavu mono":ee.monospace=t;break;case"dejavu mono|bold":ee["monospace|bold"]=t;break;case"dejavu mono|italic":ee["monospace|italic"]=t;break;case"dejavu mono|bold|italic":ee["monospace|bold|italic"]=t}}function P(e,t){var i=e[0],n=e[1],r=e[2],s=e[3],o=e[4],a=e[5],d=t[0],l=t[1],c=t[2],h=t[3],u=t[4],f=t[5];return[i*d+n*c,i*l+n*h,r*d+s*c,r*l+s*h,o*d+a*c+u,o*l+a*h+f]}function B(e){return 1===e[0]&&0===e[1]&&0===e[2]&&1===e[3]&&0===e[4]&&0===e[5]}var O,G,U,j,q,V,X,K,Q,Y,Z,J,$,ee,te=e.kendo,ie=!!e.Uint8Array,ne="\n",re=0,se=function(){var e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";return{decode:function(t){for(var i,n,r,s,o,a,d,l=t.replace(/[^A-Za-z0-9\+\/\=]/g,""),c=0,h=l.length,u=[];c<h;)i=e.indexOf(l.charAt(c++)),n=e.indexOf(l.charAt(c++)),r=e.indexOf(l.charAt(c++)),s=e.indexOf(l.charAt(c++)),o=i<<2|n>>>4,a=(15&n)<<4|r>>>2,d=(3&r)<<6|s,u.push(o),64!=r&&u.push(a),64!=s&&u.push(d);return u},encode:function(t){for(var i,n,r,s,o,a,d,l=0,c=t.length,h="";l<c;)i=t[l++],n=t[l++],r=t[l++],s=i>>>2,o=(3&i)<<4|n>>>4,a=(15&n)<<2|r>>>6,d=63&r,l-c==2?a=d=64:l-c==1&&(d=64),h+=e.charAt(s)+e.charAt(o)+e.charAt(a)+e.charAt(d);return h}}}(),oe={a0:[2383.94,3370.39],a1:[1683.78,2383.94],a2:[1190.55,1683.78],a3:[841.89,1190.55],a4:[595.28,841.89],a5:[419.53,595.28],a6:[297.64,419.53],a7:[209.76,297.64],a8:[147.4,209.76],a9:[104.88,147.4],a10:[73.7,104.88],b0:[2834.65,4008.19],b1:[2004.09,2834.65],b2:[1417.32,2004.09],b3:[1000.63,1417.32],b4:[708.66,1000.63],b5:[498.9,708.66],b6:[354.33,498.9],b7:[249.45,354.33],b8:[175.75,249.45],b9:[124.72,175.75],b10:[87.87,124.72],c0:[2599.37,3676.54],c1:[1836.85,2599.37],c2:[1298.27,1836.85],c3:[918.43,1298.27],c4:[649.13,918.43],c5:[459.21,649.13],c6:[323.15,459.21],c7:[229.61,323.15],c8:[161.57,229.61],c9:[113.39,161.57],c10:[79.37,113.39],executive:[521.86,756],folio:[612,936],legal:[612,1008],letter:[612,792],tabloid:[792,1224]},ae={"Times-Roman":!0,"Times-Bold":!0,"Times-Italic":!0,"Times-BoldItalic":!0,Helvetica:!0,"Helvetica-Bold":!0,"Helvetica-Oblique":!0,"Helvetica-BoldOblique":!0,Courier:!0,"Courier-Bold":!0,"Courier-Oblique":!0,"Courier-BoldOblique":!0,Symbol:!0,ZapfDingbats:!0},de={},le=c(d),ce=c(l);o.prototype={loadFonts:le,loadImages:ce,getFont:function(e){var t=this.FONTS[e];if(!t){if(t=ae[e],!t)throw Error("Font "+e+" has not been loaded");t=this.attach(t===!0?new Y(e):new Z(this,t)),this.FONTS[e]=t}return t},getImage:function(e){var t=this.IMAGES[e];if(!t){if(t=de[e],!t)throw Error("Image "+e+" has not been loaded");if("TAINTED"===t)return null;t=this.IMAGES[e]=this.attach(t.asStream(this))}return t},getOpacityGS:function(e,i){var n,r,s,o=t(e).toFixed(3);return e=t(o),o+=i?"S":"F",n=this._opacityGSCache||(this._opacityGSCache={}),r=n[o],r||(s={Type:S("ExtGState")},i?s.CA=e:s.ca=e,r=this.attach(new V(s)),r._resourceName=S("GS"+ ++re),n[o]=r),r},dict:function(e){return new V(e)},name:function(e){return S(e)},stream:function(e,t){return new X(t,e)}},O=Array.isArray||function(e){return e instanceof Array},w.prototype.beforeRender=function(){},G=b(function(e){this.value=e},{render:function(e){var t,i="",n=this.escape();for(t=0;t<n.length;++t)i+=String.fromCharCode(255&n.charCodeAt(t));e("(",i,")")},escape:function(){return this.value.replace(/([\(\)\\])/g,"\\$1")},toString:function(){return this.value}}),U=b(function(e){this.value=e},{render:function(e){e("<");for(var t=0;t<this.value.length;++t)e(u(this.value.charCodeAt(t).toString(16),4));e(">")}},G),j=b(function(e){this.name=e},{render:function(e){e("/"+this.escape())},escape:function(){return this.name.replace(/[^\x21-\x7E]/g,function(e){return"#"+u(e.charCodeAt(0).toString(16),2)})},toString:function(){return this.name}}),q={},j.get=S,V=b(function(e){this.props=e},{render:function(e){var t=this.props,i=!0;e("<<"),e.withIndent(function(){for(var n in t)f(t,n)&&!/^_/.test(n)&&(i=!1,e.indent(S(n)," ",t[n]))}),i||e.indent(),e(">>")}}),X=b(function(e,t,i){if("string"==typeof e){var n=R();n.write(e),e=n}this.data=e,this.props=t||{},this.compress=i},{render:function(t){var i=this.data.get(),n=this.props;this.compress&&e.pako&&"function"==typeof e.pako.deflate&&(n.Filter?n.Filter instanceof Array||(n.Filter=[n.Filter]):n.Filter=[],n.Filter.unshift(S("FlateDecode")),i=e.pako.deflate(i)),n.Length=i.length,t(new V(n)," stream",ne),t.writeData(i),t(ne,"endstream")}}),K=b(function(e){e=this.props=e||{},e.Type=S("Catalog")},{setPages:function(e){this.props.Pages=e}},V),Q=b(function(){this.props={Type:S("Pages"),Kids:[],Count:0}},{addPage:function(e){this.props.Kids.push(e),this.props.Count++}},V),Y=b(function(e){this.props={Type:S("Font"),Subtype:S("Type1"),BaseFont:S(e)},this._resourceName=S("F"+ ++re)},{encodeText:function(e){return new G(e+"")}},V),Z=b(function(e,t,i){var n,r;i=this.props=i||{},i.Type=S("Font"),i.Subtype=S("Type0"),i.Encoding=S("Identity-H"),this._pdf=e,this._font=t,this._sub=t.makeSubset(),this._resourceName=S("F"+ ++re),n=t.head,this.name=t.psName,r=this.scale=t.scale,this.bbox=[n.xMin*r,n.yMin*r,n.xMax*r,n.yMax*r],this.italicAngle=t.post.italicAngle,this.ascent=t.ascent*r,this.descent=t.descent*r,this.lineGap=t.lineGap*r,this.capHeight=t.os2.capHeight||this.ascent,this.xHeight=t.os2.xHeight||0,this.stemV=0,this.familyClass=(t.os2.familyClass||0)>>8,this.isSerif=this.familyClass>=1&&this.familyClass<=7,this.isScript=10==this.familyClass,this.flags=(t.post.isFixedPitch?1:0)|(this.isSerif?2:0)|(this.isScript?8:0)|(0!==this.italicAngle?64:0)|32},{encodeText:function(e){return new U(this._sub.encodeText(e+""))},getTextWidth:function(e,t){var i,n,r=0,s=this._font.cmap.codeMap;for(i=0;i<t.length;++i)n=s[t.charCodeAt(i)],r+=this._font.widthOfGlyph(n||0);return r*e/1e3},beforeRender:function(){var e,t,i,r,s=this,o=s._sub,a=o.render(),d=new X(R(a),{Length1:a.length},(!0)),l=s._pdf.attach(new V({Type:S("FontDescriptor"),FontName:S(s._sub.psName),FontBBox:s.bbox,Flags:s.flags,StemV:s.stemV,ItalicAngle:s.italicAngle,Ascent:s.ascent,Descent:s.descent,CapHeight:s.capHeight,XHeight:s.xHeight,FontFile2:s._pdf.attach(d)})),c=o.ncid2ogid,h=o.firstChar,u=o.lastChar,f=[];!function p(e,t){if(e<=u){var i=c[e];null==i?p(e+1):(t||f.push(e,t=[]),t.push(s._font.widthOfGlyph(i)),p(e+1,t))}}(h),e=new V({Type:S("Font"),Subtype:S("CIDFontType2"),BaseFont:S(s._sub.psName),CIDSystemInfo:new V({Registry:new G("Adobe"),Ordering:new G("Identity"),Supplement:0}),FontDescriptor:l,FirstChar:h,LastChar:u,DW:Math.round(s._font.widthOfGlyph(0)),W:f,CIDToGIDMap:s._pdf.attach(s._makeCidToGidMap())}),t=s.props,t.BaseFont=S(s._sub.psName),t.DescendantFonts=[s._pdf.attach(e)],i=new J(h,u,o.subset),r=new X(n(),null,(!0)),r.data(i),t.ToUnicode=s._pdf.attach(r)},_makeCidToGidMap:function(){return new X(R(this._sub.cidToGidMap()),null,(!0))}},V),J=b(function(e,t,i){this.firstChar=e,this.lastChar=t,this.map=i},{render:function(e){e.indent("/CIDInit /ProcSet findresource begin"),e.indent("12 dict begin"),e.indent("begincmap"),e.indent("/CIDSystemInfo <<"),e.indent("  /Registry (Adobe)"),e.indent("  /Ordering (UCS)"),e.indent("  /Supplement 0"),e.indent(">> def"),e.indent("/CMapName /Adobe-Identity-UCS def"),e.indent("/CMapType 2 def"),e.indent("1 begincodespacerange"),e.indent("  <0000><ffff>"),e.indent("endcodespacerange");var t=this;e.indent(t.lastChar-t.firstChar+1," beginbfchar"),e.withIndent(function(){var i,n,r,s;for(i=t.firstChar;i<=t.lastChar;++i){for(n=t.map[i],r=te.util.ucs2encode([n]),e.indent("<",u(i.toString(16),4),">","<"),s=0;s<r.length;++s)e(u(r.charCodeAt(s).toString(16),4));e(">")}}),e.indent("endbfchar"),e.indent("endcmap"),e.indent("CMapName currentdict /CMap defineresource pop"),e.indent("end"),e.indent("end")}}),$=b(function(e,t){this._pdf=e,this._rcount=0,this._textMode=!1,this._fontResources={},this._gsResources={},this._xResources={},this._patResources={},this._shResources={},this._opacity=1,this._matrix=[1,0,0,1,0,0],this._annotations=[],this._font=null,this._fontSize=null,this._contextStack=[],t=this.props=t||{},t.Type=S("Page"),t.ProcSet=[S("PDF"),S("Text"),S("ImageB"),S("ImageC"),S("ImageI")],t.Resources=new V({Font:new V(this._fontResources),ExtGState:new V(this._gsResources),XObject:new V(this._xResources),Pattern:new V(this._patResources),Shading:new V(this._shResources)}),t.Annots=this._annotations},{_out:function(){this._content.data.apply(null,arguments)},transform:function(e,t,i,n,r,s){B(arguments)||(this._matrix=P(arguments,this._matrix),this._out(e," ",t," ",i," ",n," ",r," ",s," cm"),this._out(ne))},translate:function(e,t){this.transform(1,0,0,1,e,t)},scale:function(e,t){this.transform(e,0,0,t,0,0)},rotate:function(e){var t=Math.cos(e),i=Math.sin(e);this.transform(t,i,-i,t,0,0)},beginText:function(){this._textMode=!0,this._out("BT",ne)},endText:function(){this._textMode=!1,this._out("ET",ne)},_requireTextMode:function(){if(!this._textMode)throw Error("Text mode required; call page.beginText() first")},_requireFont:function(){if(!this._font)throw Error("No font selected; call page.setFont() first")},setFont:function(e,t){this._requireTextMode(),null==e?e=this._font:e instanceof Z||(e=this._pdf.getFont(e)),null==t&&(t=this._fontSize),this._fontResources[e._resourceName]=e,this._font=e,this._fontSize=t,this._out(e._resourceName," ",t," Tf",ne)},setTextLeading:function(e){this._requireTextMode(),this._out(e," TL",ne)},setTextRenderingMode:function(e){this._requireTextMode(),this._out(e," Tr",ne)},showText:function(e,t){var i,n;this._requireFont(),e.length>1&&t&&this._font instanceof Z&&(i=this._font.getTextWidth(this._fontSize,e),n=t/i*100,this._out(n," Tz ")),this._out(this._font.encodeText(e)," Tj",ne)},showTextNL:function(e){this._requireFont(),this._out(this._font.encodeText(e)," '",ne)},addLink:function(e,t){var i=this._toPage({x:t.left,y:t.bottom}),n=this._toPage({x:t.right,y:t.top});this._annotations.push(new V({Type:S("Annot"),Subtype:S("Link"),Rect:[i.x,i.y,n.x,n.y],Border:[0,0,0],A:new V({Type:S("Action"),S:S("URI"),URI:new G(e)})}))},setStrokeColor:function(e,t,i){this._out(e," ",t," ",i," RG",ne)},setOpacity:function(e){this.setFillOpacity(e),this.setStrokeOpacity(e),this._opacity*=e},setStrokeOpacity:function(e){if(e<1){var t=this._pdf.getOpacityGS(this._opacity*e,!0);this._gsResources[t._resourceName]=t,this._out(t._resourceName," gs",ne)}},setFillColor:function(e,t,i){this._out(e," ",t," ",i," rg",ne)},setFillOpacity:function(e){if(e<1){var t=this._pdf.getOpacityGS(this._opacity*e,!1);this._gsResources[t._resourceName]=t,this._out(t._resourceName," gs",ne)}},gradient:function(e,t){var i,n,r;this.save(),this.rect(t.left,t.top,t.width,t.height),this.clip(),e.userSpace||this.transform(t.width,0,0,t.height,t.left,t.top),i=z(this._pdf,e,t),n=i.shading._resourceName,this._shResources[n]=i.shading,i.hasAlpha&&(r=i.opacity._resourceName,this._gsResources[r]=i.opacity,this._out("/"+r+" gs ")),this._out("/"+n+" sh",ne),this.restore()},setDashPattern:function(e,t){this._out(e," ",t," d",ne)},setLineWidth:function(e){this._out(e," w",ne)},setLineCap:function(e){this._out(e," J",ne)},setLineJoin:function(e){this._out(e," j",ne)},setMitterLimit:function(e){this._out(e," M",ne)},save:function(){this._contextStack.push(this._context()),this._out("q",ne)},restore:function(){this._out("Q",ne),this._context(this._contextStack.pop())},moveTo:function(e,t){this._out(e," ",t," m",ne)},lineTo:function(e,t){this._out(e," ",t," l",ne)},bezier:function(e,t,i,n,r,s){this._out(e," ",t," ",i," ",n," ",r," ",s," c",ne)},bezier1:function(e,t,i,n){this._out(e," ",t," ",i," ",n," y",ne)},bezier2:function(e,t,i,n){this._out(e," ",t," ",i," ",n," v",ne)},close:function(){this._out("h",ne)},rect:function(e,t,i,n){this._out(e," ",t," ",i," ",n," re",ne)},ellipse:function(e,t,i,n){function r(t){return e+t}function s(e){return t+e}var o=.5522847498307936;this.moveTo(r(0),s(n)),this.bezier(r(i*o),s(n),r(i),s(n*o),r(i),s(0)),this.bezier(r(i),s(-n*o),r(i*o),s(-n),r(0),s(-n)),this.bezier(r(-i*o),s(-n),r(-i),s(-n*o),r(-i),s(0)),this.bezier(r(-i),s(n*o),r(-i*o),s(n),r(0),s(n))},circle:function(e,t,i){this.ellipse(e,t,i,i)},stroke:function(){this._out("S",ne)},nop:function(){this._out("n",ne)},clip:function(){this._out("W n",ne)},clipStroke:function(){this._out("W S",ne)},closeStroke:function(){this._out("s",ne)},fill:function(){this._out("f",ne)},fillStroke:function(){this._out("B",ne)},drawImage:function(e){var t=this._pdf.getImage(e);t&&(this._xResources[t._resourceName]=t,this._out(t._resourceName," Do",ne))},comment:function(e){var t=this;e.split(/\r?\n/g).forEach(function(e){t._out("% ",e,ne)})},_context:function(e){return null==e?{opacity:this._opacity,matrix:this._matrix}:(this._opacity=e.opacity,this._matrix=e.matrix,i)},_toPage:function(e){var t=this._matrix,i=t[0],n=t[1],r=t[2],s=t[3],o=t[4],a=t[5];return{x:i*e.x+r*e.y+o,y:n*e.x+s*e.y+a}}},V),ee={serif:"Times-Roman","serif|bold":"Times-Bold","serif|italic":"Times-Italic","serif|bold|italic":"Times-BoldItalic","sans-serif":"Helvetica","sans-serif|bold":"Helvetica-Bold","sans-serif|italic":"Helvetica-Oblique","sans-serif|bold|italic":"Helvetica-BoldOblique",monospace:"Courier","monospace|bold":"Courier-Bold","monospace|italic":"Courier-Oblique","monospace|bold|italic":"Courier-BoldOblique",zapfdingbats:"ZapfDingbats","zapfdingbats|bold":"ZapfDingbats","zapfdingbats|italic":"ZapfDingbats","zapfdingbats|bold|italic":"ZapfDingbats"},N("Times New Roman","serif"),N("Courier New","monospace"),N("Arial","sans-serif"),N("Helvetica","sans-serif"),N("Verdana","sans-serif"),N("Tahoma","sans-serif"),N("Georgia","sans-serif"),N("Monaco","monospace"),N("Andale Mono","monospace"),te.pdf={Document:o,BinaryStream:R,defineFont:L,parseFontDef:E,getFontURL:W,loadFonts:le,loadImages:ce,getPaperOptions:s,TEXT_RENDERING_MODE:{fill:0,stroke:1,fillAndStroke:2,invisible:3,fillAndClip:4,strokeAndClip:5,fillStrokeClip:6,clip:7}}}(window,parseFloat)},"function"==typeof define&&define.amd?define:function(e,t,i){(i||t)()}),function(e,define){define("pdf/ttf.min",["pdf/core.min","util/main.min"],e)}(function(){!function(e){"use strict";function t(e,t){return Object.prototype.hasOwnProperty.call(e,t)}function i(e){return Object.keys(e).sort(function(e,t){return e-t}).map(parseFloat)}function n(e){var t,i,n;for(this.raw=e,this.scalerType=e.readLong(),this.tableCount=e.readShort(),this.searchRange=e.readShort(),this.entrySelector=e.readShort(),this.rangeShift=e.readShort(),t=this.tables={},i=0;i<this.tableCount;++i)n={tag:e.readString(4),checksum:e.readLong(),offset:e.readLong(),length:e.readLong()},t[n.tag]=n}function r(e){function i(e,t){this.definition=t,this.length=t.length,this.offset=t.offset,this.file=e,this.rawData=e.raw,this.parse(e.raw)}i.prototype.raw=function(){return this.rawData.slice(this.offset,this.length)};for(var n in e)t(e,n)&&(i[n]=i.prototype[n]=e[n]);return i}function s(){var e,t="",i=_+"";for(e=0;e<i.length;++e)t+=String.fromCharCode(i.charCodeAt(e)-48+65);return++_,t}function o(e){this.font=e,this.subset={},this.unicodes={},this.ogid2ngid={0:0},this.ngid2ogid={0:0},this.ncid2ogid={},this.next=this.firstChar=1,this.nextGid=1,this.psName=s()+"+"+this.font.psName}function a(e,t){var i,n,r,s=this,o=s.contents=k(e);if("ttcf"==o.readString(4)){if(!t)throw Error("Must specify a name for TTC files");for(o.readLong(),i=o.readLong(),n=0;n<i;++n)if(r=o.readLong(),o.saveExcursion(function(){o.offset(r),s.parse()}),s.psName==t)return;throw Error("Font "+t+" not found in collection")}o.offset(0),s.parse()}var d,l,c,h,u,f,p,g,m,v,_,y=e.kendo.pdf,k=y.BinaryStream;n.prototype={readTable:function(e,t){var i=this.tables[e];if(!i)throw Error("Table "+e+" not found in directory");return this[e]=i.table=new t(this,i)},render:function(e){var i,n,r,s,o,a,d,l,c=Object.keys(e).length,h=Math.pow(2,Math.floor(Math.log(c)/Math.LN2)),u=16*h,f=Math.floor(Math.log(h)/Math.LN2),p=16*c-u,g=k();
g.writeLong(this.scalerType),g.writeShort(c),g.writeShort(u),g.writeShort(f),g.writeShort(p),i=16*c,n=g.offset()+i,r=null,s=k();for(o in e)if(t(e,o))for(a=e[o],g.writeString(o),g.writeLong(this.checksum(a)),g.writeLong(n),g.writeLong(a.length),s.write(a),"head"==o&&(r=n),n+=a.length;n%4;)s.writeByte(0),n++;return g.write(s.get()),d=this.checksum(g.get()),l=2981146554-d,g.offset(r+8),g.writeLong(l),g.get()},checksum:function(e){e=k(e);for(var t=0;!e.eof();)t+=e.readLong();return 4294967295&t}},d=r({parse:function(e){e.offset(this.offset),this.version=e.readLong(),this.revision=e.readLong(),this.checkSumAdjustment=e.readLong(),this.magicNumber=e.readLong(),this.flags=e.readShort(),this.unitsPerEm=e.readShort(),this.created=e.read(8),this.modified=e.read(8),this.xMin=e.readShort_(),this.yMin=e.readShort_(),this.xMax=e.readShort_(),this.yMax=e.readShort_(),this.macStyle=e.readShort(),this.lowestRecPPEM=e.readShort(),this.fontDirectionHint=e.readShort_(),this.indexToLocFormat=e.readShort_(),this.glyphDataFormat=e.readShort_()},render:function(e){var t=k();return t.writeLong(this.version),t.writeLong(this.revision),t.writeLong(0),t.writeLong(this.magicNumber),t.writeShort(this.flags),t.writeShort(this.unitsPerEm),t.write(this.created),t.write(this.modified),t.writeShort_(this.xMin),t.writeShort_(this.yMin),t.writeShort_(this.xMax),t.writeShort_(this.yMax),t.writeShort(this.macStyle),t.writeShort(this.lowestRecPPEM),t.writeShort_(this.fontDirectionHint),t.writeShort_(e),t.writeShort_(this.glyphDataFormat),t.get()}}),l=r({parse:function(e){e.offset(this.offset);var t=this.file.head.indexToLocFormat;this.offsets=0===t?e.times(this.length/2,function(){return 2*e.readShort()}):e.times(this.length/4,e.readLong)},offsetOf:function(e){return this.offsets[e]},lengthOf:function(e){return this.offsets[e+1]-this.offsets[e]},render:function(e){var t,i=k(),n=e[e.length-1]>65535;for(t=0;t<e.length;++t)n?i.writeLong(e[t]):i.writeShort(e[t]/2);return{format:n?1:0,table:i.get()}}}),c=r({parse:function(e){e.offset(this.offset),this.version=e.readLong(),this.ascent=e.readShort_(),this.descent=e.readShort_(),this.lineGap=e.readShort_(),this.advanceWidthMax=e.readShort(),this.minLeftSideBearing=e.readShort_(),this.minRightSideBearing=e.readShort_(),this.xMaxExtent=e.readShort_(),this.caretSlopeRise=e.readShort_(),this.caretSlopeRun=e.readShort_(),this.caretOffset=e.readShort_(),e.skip(8),this.metricDataFormat=e.readShort_(),this.numOfLongHorMetrics=e.readShort()},render:function(e){var t=k();return t.writeLong(this.version),t.writeShort_(this.ascent),t.writeShort_(this.descent),t.writeShort_(this.lineGap),t.writeShort(this.advanceWidthMax),t.writeShort_(this.minLeftSideBearing),t.writeShort_(this.minRightSideBearing),t.writeShort_(this.xMaxExtent),t.writeShort_(this.caretSlopeRise),t.writeShort_(this.caretSlopeRun),t.writeShort_(this.caretOffset),t.write([0,0,0,0,0,0,0,0]),t.writeShort_(this.metricDataFormat),t.writeShort(e.length),t.get()}}),h=r({parse:function(e){e.offset(this.offset),this.version=e.readLong(),this.numGlyphs=e.readShort(),this.maxPoints=e.readShort(),this.maxContours=e.readShort(),this.maxComponentPoints=e.readShort(),this.maxComponentContours=e.readShort(),this.maxZones=e.readShort(),this.maxTwilightPoints=e.readShort(),this.maxStorage=e.readShort(),this.maxFunctionDefs=e.readShort(),this.maxInstructionDefs=e.readShort(),this.maxStackElements=e.readShort(),this.maxSizeOfInstructions=e.readShort(),this.maxComponentElements=e.readShort(),this.maxComponentDepth=e.readShort()},render:function(e){var t=k();return t.writeLong(this.version),t.writeShort(e.length),t.writeShort(this.maxPoints),t.writeShort(this.maxContours),t.writeShort(this.maxComponentPoints),t.writeShort(this.maxComponentContours),t.writeShort(this.maxZones),t.writeShort(this.maxTwilightPoints),t.writeShort(this.maxStorage),t.writeShort(this.maxFunctionDefs),t.writeShort(this.maxInstructionDefs),t.writeShort(this.maxStackElements),t.writeShort(this.maxSizeOfInstructions),t.writeShort(this.maxComponentElements),t.writeShort(this.maxComponentDepth),t.get()}}),u=r({parse:function(e){var t,i,n;e.offset(this.offset),t=this.file,i=t.hhea,this.metrics=e.times(i.numOfLongHorMetrics,function(){return{advance:e.readShort(),lsb:e.readShort_()}}),n=t.maxp.numGlyphs-t.hhea.numOfLongHorMetrics,this.leftSideBearings=e.times(n,e.readShort_)},forGlyph:function(e){var t=this.metrics,i=t.length;return e<i?t[e]:{advance:t[i-1].advance,lsb:this.leftSideBearings[e-i]}},render:function(e){var t,i,n=k();for(t=0;t<e.length;++t)i=this.forGlyph(e[t]),n.writeShort(i.advance),n.writeShort_(i.lsb);return n.get()}}),f=function(){function e(e){this.raw=e}function i(e){var t,i,r;for(this.raw=e,t=this.glyphIds=[],i=this.idOffsets=[];;){if(r=e.readShort(),i.push(e.offset()),t.push(e.readShort()),!(r&o))break;e.skip(r&n?4:2),r&d?e.skip(8):r&a?e.skip(4):r&s&&e.skip(2)}}var n,s,o,a,d;return e.prototype={compound:!1,render:function(){return this.raw.get()}},n=1,s=8,o=32,a=64,d=128,i.prototype={compound:!0,render:function(e){var t,i,n=k(this.raw.get());for(t=0;t<this.glyphIds.length;++t)i=this.glyphIds[t],n.offset(this.idOffsets[t]),n.writeShort(e[i]);return n.get()}},r({parse:function(){this.cache={}},glyphFor:function(n){var r,s,o,a,d,l,c,h,u,f,p,g=this.cache;return t(g,n)?g[n]:(r=this.file.loca,s=r.lengthOf(n),0===s?g[n]=null:(o=this.rawData,a=this.offset+r.offsetOf(n),d=k(o.slice(a,s)),l=d.readShort_(),c=d.readShort_(),h=d.readShort_(),u=d.readShort_(),f=d.readShort_(),p=g[n]=l==-1?new i(d):new e(d),p.numberOfContours=l,p.xMin=c,p.yMin=h,p.xMax=u,p.yMax=f,p))},render:function(e,t,i){var n,r,s,o=k(),a=[];for(n=0;n<t.length;++n)r=t[n],s=e[r],a.push(o.offset()),s&&o.write(s.render(i));return a.push(o.offset()),{table:o.get(),offsets:a}}})}(),p=function(){function e(e,t){this.text=e,this.length=e.length,this.platformID=t.platformID,this.platformSpecificID=t.platformSpecificID,this.languageID=t.languageID,this.nameID=t.nameID}return r({parse:function(t){var i,n,r,s,o,a,d;for(t.offset(this.offset),t.readShort(),i=t.readShort(),n=this.offset+t.readShort(),r=t.times(i,function(){return{platformID:t.readShort(),platformSpecificID:t.readShort(),languageID:t.readShort(),nameID:t.readShort(),length:t.readShort(),offset:t.readShort()+n}}),s=this.strings={},o=0;o<r.length;++o)a=r[o],t.offset(a.offset),d=t.readString(a.length),s[a.nameID]||(s[a.nameID]=[]),s[a.nameID].push(new e(d,a));this.postscriptEntry=s[6][0],this.postscriptName=this.postscriptEntry.text.replace(/[^\x20-\x7F]/g,"")},render:function(i){var n,r,s,o,a,d,l=this.strings,c=0;for(n in l)t(l,n)&&(c+=l[n].length);r=k(),s=k(),r.writeShort(0),r.writeShort(c),r.writeShort(6+12*c);for(n in l)if(t(l,n))for(o=6==n?[new e(i,this.postscriptEntry)]:l[n],a=0;a<o.length;++a)d=o[a],r.writeShort(d.platformID),r.writeShort(d.platformSpecificID),r.writeShort(d.languageID),r.writeShort(d.nameID),r.writeShort(d.length),r.writeShort(s.offset()),s.writeString(d.text);return r.write(s.get()),r.get()}})}(),g=function(){var e=".notdef .null nonmarkingreturn space exclam quotedbl numbersign dollar percent ampersand quotesingle parenleft parenright asterisk plus comma hyphen period slash zero one two three four five six seven eight nine colon semicolon less equal greater question at A B C D E F G H I J K L M N O P Q R S T U V W X Y Z bracketleft backslash bracketright asciicircum underscore grave a b c d e f g h i j k l m n o p q r s t u v w x y z braceleft bar braceright asciitilde Adieresis Aring Ccedilla Eacute Ntilde Odieresis Udieresis aacute agrave acircumflex adieresis atilde aring ccedilla eacute egrave ecircumflex edieresis iacute igrave icircumflex idieresis ntilde oacute ograve ocircumflex odieresis otilde uacute ugrave ucircumflex udieresis dagger degree cent sterling section bullet paragraph germandbls registered copyright trademark acute dieresis notequal AE Oslash infinity plusminus lessequal greaterequal yen mu partialdiff summation product pi integral ordfeminine ordmasculine Omega ae oslash questiondown exclamdown logicalnot radical florin approxequal Delta guillemotleft guillemotright ellipsis nonbreakingspace Agrave Atilde Otilde OE oe endash emdash quotedblleft quotedblright quoteleft quoteright divide lozenge ydieresis Ydieresis fraction currency guilsinglleft guilsinglright fi fl daggerdbl periodcentered quotesinglbase quotedblbase perthousand Acircumflex Ecircumflex Aacute Edieresis Egrave Iacute Icircumflex Idieresis Igrave Oacute Ocircumflex apple Ograve Uacute Ucircumflex Ugrave dotlessi circumflex tilde macron breve dotaccent ring cedilla hungarumlaut ogonek caron Lslash lslash Scaron scaron Zcaron zcaron brokenbar Eth eth Yacute yacute Thorn thorn minus multiply onesuperior twosuperior threesuperior onehalf onequarter threequarters franc Gbreve gbreve Idotaccent Scedilla scedilla Cacute cacute Ccaron ccaron dcroat".split(/\s+/g);return r({parse:function(e){var t,i;switch(e.offset(this.offset),this.format=e.readLong(),this.italicAngle=e.readFixed_(),this.underlinePosition=e.readShort_(),this.underlineThickness=e.readShort_(),this.isFixedPitch=e.readLong(),this.minMemType42=e.readLong(),this.maxMemType42=e.readLong(),this.minMemType1=e.readLong(),this.maxMemType1=e.readLong(),this.format){case 65536:case 196608:break;case 131072:for(t=e.readShort(),this.glyphNameIndex=e.times(t,e.readShort),this.names=[],i=this.offset+this.length;e.offset()<i;)this.names.push(e.readString(e.readByte()));break;case 151552:t=e.readShort(),this.offsets=e.read(t);break;case 262144:this.map=e.times(this.file.maxp.numGlyphs,e.readShort)}},glyphFor:function(t){switch(this.format){case 65536:return e[t]||".notdef";case 131072:var i=this.glyphNameIndex[t];return i<e.length?e[i]:this.names[i-e.length]||".notdef";case 151552:case 196608:return".notdef";case 262144:return this.map[t]||65535}},render:function(t){var i,n,r,s,o,a,d;if(196608==this.format)return this.raw();for(i=k(this.rawData.slice(this.offset,32)),i.writeLong(131072),i.offset(32),n=[],r=[],s=0;s<t.length;++s)o=t[s],a=this.glyphFor(o),d=e.indexOf(a),d>=0?n.push(d):(n.push(e.length+r.length),r.push(a));for(i.writeShort(t.length),s=0;s<n.length;++s)i.writeShort(n[s]);for(s=0;s<r.length;++s)i.writeByte(r[s].length),i.writeString(r[s]);return i.get()}})}(),m=function(){function t(t,i,n){var r=this;r.platformID=t.readShort(),r.platformSpecificID=t.readShort(),r.offset=i+t.readLong(),t.saveExcursion(function(){var i,s,o,a,d,l,c,h,u,f,p,g,m,v,_,y,k;switch(t.offset(r.offset),r.format=t.readShort()){case 0:for(r.length=t.readShort(),r.language=t.readShort(),s=0;s<256;++s)n[s]=t.readByte();break;case 4:for(r.length=t.readShort(),r.language=t.readShort(),o=t.readShort()/2,t.skip(6),a=t.times(o,t.readShort),t.skip(2),d=t.times(o,t.readShort),l=t.times(o,t.readShort_),c=t.times(o,t.readShort),h=(r.length+r.offset-t.offset())/2,u=t.times(h,t.readShort),s=0;s<o;++s)for(f=d[s],p=a[s],i=f;i<=p;++i)0===c[s]?g=i+l[s]:(m=c[s]/2-(o-s)+(i-f),g=u[m]||0,0!==g&&(g+=l[s])),n[i]=65535&g;break;case 6:for(r.length=t.readShort(),r.language=t.readShort(),i=t.readShort(),v=t.readShort();v-- >0;)n[i++]=t.readShort();break;case 12:for(t.readShort(),r.length=t.readLong(),r.language=t.readLong(),_=t.readLong();_-- >0;)for(i=t.readLong(),y=t.readLong(),k=t.readLong();i<=y;)n[i++]=k++;break;default:e.console&&e.console.error("Unhandled CMAP format: "+r.format)}})}function n(e,t){function n(i){return t[e[i]]}var r,s,o,a,d,l,c,h,u,f,p,g,m,v,_,y,w,b=i(e),S=[],x=[],T=null,D=null;for(r=0;r<b.length;++r)s=b[r],o=n(s),a=o-s,null!=T&&a===D||(T&&x.push(T),S.push(s),D=a),T=s;for(T&&x.push(T),x.push(65535),S.push(65535),d=S.length,l=2*d,c=2*Math.pow(2,Math.floor(Math.log(d)/Math.LN2)),h=Math.log(c/2)/Math.LN2,u=l-c,f=[],p=[],g=[],r=0;r<d;++r){if(m=S[r],v=x[r],65535==m){f.push(0),p.push(0);break}if(_=n(m),m-_>=32768)for(f.push(0),p.push(2*(g.length+d-r)),y=m;y<=v;++y)g.push(n(y));else f.push(_-m),p.push(0)}return w=k(),w.writeShort(3),w.writeShort(1),w.writeLong(12),w.writeShort(4),w.writeShort(16+8*d+2*g.length),w.writeShort(0),w.writeShort(l),w.writeShort(c),w.writeShort(h),w.writeShort(u),x.forEach(w.writeShort),w.writeShort(0),S.forEach(w.writeShort),f.forEach(w.writeShort_),p.forEach(w.writeShort),g.forEach(w.writeShort),w.get()}return r({parse:function(e){var i,n=this,r=n.offset;e.offset(r),n.codeMap={},n.version=e.readShort(),i=e.readShort(),n.tables=e.times(i,function(){return new t(e,r,n.codeMap)})},render:function(e,t){var i=k();return i.writeShort(0),i.writeShort(1),i.write(n(e,t)),i.get()}})}(),v=r({parse:function(e){e.offset(this.offset),this.version=e.readShort(),this.averageCharWidth=e.readShort_(),this.weightClass=e.readShort(),this.widthClass=e.readShort(),this.type=e.readShort(),this.ySubscriptXSize=e.readShort_(),this.ySubscriptYSize=e.readShort_(),this.ySubscriptXOffset=e.readShort_(),this.ySubscriptYOffset=e.readShort_(),this.ySuperscriptXSize=e.readShort_(),this.ySuperscriptYSize=e.readShort_(),this.ySuperscriptXOffset=e.readShort_(),this.ySuperscriptYOffset=e.readShort_(),this.yStrikeoutSize=e.readShort_(),this.yStrikeoutPosition=e.readShort_(),this.familyClass=e.readShort_(),this.panose=e.times(10,e.readByte),this.charRange=e.times(4,e.readLong),this.vendorID=e.readString(4),this.selection=e.readShort(),this.firstCharIndex=e.readShort(),this.lastCharIndex=e.readShort(),this.version>0&&(this.ascent=e.readShort_(),this.descent=e.readShort_(),this.lineGap=e.readShort_(),this.winAscent=e.readShort(),this.winDescent=e.readShort(),this.codePageRange=e.times(2,e.readLong),this.version>1&&(this.xHeight=e.readShort(),this.capHeight=e.readShort(),this.defaultChar=e.readShort(),this.breakChar=e.readShort(),this.maxContext=e.readShort()))},render:function(){return this.raw()}}),_=1e5,o.prototype={use:function(e){var t,i,n,r=this;return"string"==typeof e?kendo.util.ucs2decode(e).reduce(function(e,t){return e+String.fromCharCode(r.use(t))},""):(t=r.unicodes[e],t||(t=r.next++,r.subset[t]=e,r.unicodes[e]=t,i=r.font.cmap.codeMap[e],i&&(r.ncid2ogid[t]=i,null==r.ogid2ngid[i]&&(n=r.nextGid++,r.ogid2ngid[i]=n,r.ngid2ogid[n]=i))),t)},encodeText:function(e){return this.use(e)},glyphIds:function(){return i(this.ogid2ngid)},glyphsFor:function(e,t){var i,n,r;for(t||(t={}),i=0;i<e.length;++i)n=e[i],t[n]||(r=t[n]=this.font.glyf.glyphFor(n),r&&r.compound&&this.glyphsFor(r.glyphIds,t));return t},render:function(){var e,n,r,s,o,a,d,l,c=this.glyphsFor(this.glyphIds());for(e in c)t(c,e)&&(e=parseInt(e,10),null==this.ogid2ngid[e]&&(n=this.nextGid++,this.ogid2ngid[e]=n,this.ngid2ogid[n]=e));return r=i(this.ngid2ogid),s=r.map(function(e){return this.ngid2ogid[e]},this),o=this.font,a=o.glyf.render(c,s,this.ogid2ngid),d=o.loca.render(a.offsets),this.lastChar=this.next-1,l={cmap:m.render(this.ncid2ogid,this.ogid2ngid),glyf:a.table,loca:d.table,hmtx:o.hmtx.render(s),hhea:o.hhea.render(s),maxp:o.maxp.render(s),post:o.post.render(s),name:o.name.render(this.psName),head:o.head.render(d.format),"OS/2":o.os2.render()},this.font.directory.render(l)},cidToGidMap:function(){var e,t,i,n=k(),r=0;for(e=this.firstChar;e<this.next;++e){for(;r<e;)n.writeShort(0),r++;t=this.ncid2ogid[e],t?(i=this.ogid2ngid[t],n.writeShort(i)):n.writeShort(0),r++}return n.get()}},a.prototype={parse:function(){var e=this.directory=new n(this.contents);this.head=e.readTable("head",d),this.loca=e.readTable("loca",l),this.hhea=e.readTable("hhea",c),this.maxp=e.readTable("maxp",h),this.hmtx=e.readTable("hmtx",u),this.glyf=e.readTable("glyf",f),this.name=e.readTable("name",p),this.post=e.readTable("post",g),this.cmap=e.readTable("cmap",m),this.os2=e.readTable("OS/2",v),this.psName=this.name.postscriptName,this.ascent=this.os2.ascent||this.hhea.ascent,this.descent=this.os2.descent||this.hhea.descent,this.lineGap=this.os2.lineGap||this.hhea.lineGap,this.scale=1e3/this.head.unitsPerEm},widthOfGlyph:function(e){return this.hmtx.forGlyph(e).advance*this.scale},makeSubset:function(){return new o(this)}},y.TTFFont=a}(window)},"function"==typeof define&&define.amd?define:function(e,t,i){(i||t)()}),function(e,define){define("util/text-metrics.min",["kendo.core.min","util/main.min"],e)}(function(){!function(e){function t(){return{width:0,height:0,baseline:0}}function i(e,t,i){return h.current.measure(e,t,i)}function n(e,t){var i=[];if(e.length>0&&document.fonts){try{i=e.map(function(e){return document.fonts.load(e)})}catch(n){s.logToConsole(n)}Promise.all(i).then(t,t)}else t()}var r=document,s=window.kendo,o=s.Class,a=s.util,d=a.defined,l=o.extend({init:function(e){this._size=e,this._length=0,this._map={}},put:function(e,t){var i=this,n=i._map,r={key:e,value:t};n[e]=r,i._head?(i._tail.newer=r,r.older=i._tail,i._tail=r):i._head=i._tail=r,i._length>=i._size?(n[i._head.key]=null,i._head=i._head.newer,i._head.older=null):i._length++},get:function(e){var t=this,i=t._map[e];if(i)return i===t._head&&i!==t._tail&&(t._head=i.newer,t._head.older=null),i!==t._tail&&(i.older&&(i.older.newer=i.newer,i.newer.older=i.older),i.older=t._tail,i.newer=null,t._tail.newer=i,t._tail=i),i.value}}),c=e("<div style='position: absolute !important; top: -4000px !important; width: auto !important; height: auto !important;padding: 0 !important; margin: 0 !important; border: 0 !important;line-height: normal !important; visibility: hidden !important; white-space: nowrap!important;' />")[0],h=o.extend({init:function(e){this._cache=new l(1e3),this._initOptions(e)},options:{baselineMarkerSize:1},measure:function(i,n,s){var o,l,h,u,f,p,g,m;if(!i)return t();if(o=a.objectKey(n),l=a.hashKey(i+o),h=this._cache.get(l),h)return h;u=t(),f=s?s:c,p=this._baselineMarker().cloneNode(!1);for(g in n)m=n[g],d(m)&&(f.style[g]=m);return e(f).text(i),f.appendChild(p),r.body.appendChild(f),(i+"").length&&(u.width=f.offsetWidth-this.options.baselineMarkerSize,u.height=f.offsetHeight,u.baseline=p.offsetTop+this.options.baselineMarkerSize),u.width>0&&u.height>0&&this._cache.put(l,u),f.parentNode.removeChild(f),u},_baselineMarker:function(){return e("<div class='k-baseline-marker' style='display: inline-block; vertical-align: baseline;width: "+this.options.baselineMarkerSize+"px; height: "+this.options.baselineMarkerSize+"px;overflow: hidden;' />")[0]}});h.current=new h,s.util.TextMetrics=h,s.util.LRUCache=l,s.util.loadFonts=n,s.util.measureText=i}(window.kendo.jQuery)},"function"==typeof define&&define.amd?define:function(e,t,i){(i||t)()}),function(e,define){define("util/base64.min",["util/main.min"],e)}(function(){return function(){function e(e){var i,n,r,o,a,d,l,c="",h=0;for(e=t(e);h<e.length;)i=e.charCodeAt(h++),n=e.charCodeAt(h++),r=e.charCodeAt(h++),o=i>>2,a=(3&i)<<4|n>>4,d=(15&n)<<2|r>>6,l=63&r,isNaN(n)?d=l=64:isNaN(r)&&(l=64),c=c+s.charAt(o)+s.charAt(a)+s.charAt(d)+s.charAt(l);return c}function t(e){var t,i,n="";for(t=0;t<e.length;t++)i=e.charCodeAt(t),i<128?n+=r(i):i<2048?(n+=r(192|i>>>6),n+=r(128|63&i)):i<65536&&(n+=r(224|i>>>12),n+=r(128|i>>>6&63),n+=r(128|63&i));return n}var i=window.kendo,n=i.deepExtend,r=String.fromCharCode,s="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";n(i.util,{encodeBase64:e,encodeUTF8:t})}(),window.kendo},"function"==typeof define&&define.amd?define:function(e,t,i){(i||t)()}),function(e,define){define("mixins/observers.min",["kendo.core.min"],e)}(function(){return function(e){var t=Math,i=window.kendo,n=i.deepExtend,r=e.inArray,s={observers:function(){return this._observers=this._observers||[]},addObserver:function(e){return this._observers?this._observers.push(e):this._observers=[e],this},removeObserver:function(e){var t=this.observers(),i=r(e,t);return i!=-1&&t.splice(i,1),this},trigger:function(e,t){var i,n,r=this._observers;if(r&&!this._suspended)for(n=0;n<r.length;n++)i=r[n],i[e]&&i[e](t);return this},optionsChange:function(e){e=e||{},e.element=this,this.trigger("optionsChange",e)},geometryChange:function(){this.trigger("geometryChange",{element:this})},suspend:function(){return this._suspended=(this._suspended||0)+1,this},resume:function(){return this._suspended=t.max((this._suspended||0)-1,0),this},_observerField:function(e,t){this[e]&&this[e].removeObserver(this),this[e]=t,t.addObserver(this)}};n(i,{mixins:{ObserversMixin:s}})}(window.kendo.jQuery),window.kendo},"function"==typeof define&&define.amd?define:function(e,t,i){(i||t)()}),function(e,define){define("pdf/drawing.min",["kendo.core.min","kendo.color.min","kendo.drawing.min","pdf/core.min"],e)}(function(){!function(e,t){"use strict";function i(t,i){function n(e,t,i){return i||(i=c),i.pdf&&null!=i.pdf[e]?i.pdf[e]:t}function r(){function r(e){var t,i,r,s,d,l=e.options,c=D(e),h=c.bbox;e=c.root,t=n("paperSize",n("paperSize","auto"),l),i=!1,"auto"==t&&(h?(r=h.getSize(),t=[r.width,r.height],i=!0,s=h.getOrigin(),c=new C.Group,c.transform(new H.Matrix(1,0,0,1,(-s.x),(-s.y))),c.append(e),e=c):t="A4"),d=o.addPage({paperSize:t,margin:n("margin",n("margin"),l),addMargin:i,landscape:n("landscape",n("landscape",!1),l)}),a(e,d,o)}if(!(--s>0)){var o=new e.pdf.Document({producer:n("producer"),title:n("title"),author:n("author"),subject:n("subject"),keywords:n("keywords"),creator:n("creator"),date:n("date")});h?t.children.forEach(r):r(t),i(o.render(),o)}}var s,d=[],l=[],c=t.options,h=n("multiPage");t.traverse(function(t){o({Image:function(e){l.indexOf(e.src())<0&&l.push(e.src())},Text:function(t){var i=e.pdf.parseFontDef(t.options.font),n=e.pdf.getFontURL(i);d.indexOf(n)<0&&d.push(n)}},t)}),s=2,e.pdf.loadFonts(d,r),e.pdf.loadImages(l,r)}function n(e,t){i(e,function(e){t("data:application/pdf;base64,"+e.base64())})}function r(e,t){i(e,function(e){t(new Blob([e.get()],{type:"application/pdf"}))})}function s(t,i,s,o){window.Blob&&!e.support.browser.safari?r(t,function(t){e.saveAs({dataURI:t,fileName:i}),o&&o(t)}):n(t,function(t){e.saveAs({dataURI:t,fileName:i,proxyURL:s}),o&&o(t)})}function o(e,t){var i=e[t.nodeType];return i?i.call.apply(i,arguments):t}function a(e,t,i){var n,r,s;e.options._pdfDebug&&t.comment("BEGIN: "+e.options._pdfDebug),n=e.transform(),r=e.opacity(),t.save(),null!=r&&r<1&&t.setOpacity(r),d(e,t,i),l(e,t,i),n&&(s=n.matrix(),t.transform(s.a,s.b,s.c,s.d,s.e,s.f)),c(e,t,i),o({Path:m,MultiPath:v,Circle:_,Arc:y,Text:k,Image:b,Group:w,Rect:S},e,t,i),t.restore(),e.options._pdfDebug&&t.comment("END: "+e.options._pdfDebug)}function d(e,t){var i,n,r,s,o,a,d=e.stroke&&e.stroke();if(d){if(i=d.color){if(i=T(i),null==i)return;t.setStrokeColor(i.r,i.g,i.b),1!=i.a&&t.setStrokeOpacity(i.a)}if(n=d.width,null!=n){if(0===n)return;t.setLineWidth(n)}r=d.dashType,r&&t.setDashPattern(M[r],0),s=d.lineCap,s&&t.setLineCap(I[s]),o=d.lineJoin,o&&t.setLineJoin(z[o]),a=d.opacity,null!=a&&t.setStrokeOpacity(a)}}function l(e,t){var i,n,r=e.fill&&e.fill();if(r&&!(r instanceof C.Gradient)){if(i=r.color){if(i=T(i),null==i)return;t.setFillColor(i.r,i.g,i.b),1!=i.a&&t.setFillOpacity(i.a)}n=r.opacity,null!=n&&t.setFillOpacity(n)}}function c(e,t,i){var n=e.clip();n&&(g(n,t,i),t.clip())}function h(e){return e&&(e instanceof C.Gradient||e.color&&!/^(none|transparent)$/i.test(e.color)&&(null==e.width||e.width>0)&&(null==e.opacity||e.opacity>0))}function u(e,t,i,n){var r,s,o,a,d,l,c,h,u=e.fill();if(u instanceof C.Gradient)return n?t.clipStroke():t.clip(),r=u instanceof C.RadialGradient,r?(s={x:u.center().x,y:u.center().y,r:0},o={x:u.center().x,y:u.center().y,r:u.radius()}):(s={x:u.start().x,y:u.start().y},o={x:u.end().x,y:u.end().y}),a=u.stops.elements().map(function(e){var t,i=e.offset();return i=/%$/.test(i)?parseFloat(i)/100:parseFloat(i),t=T(e.color()),t.a*=e.opacity(),{offset:i,color:t}}),a.unshift(a[0]),a.push(a[a.length-1]),d={userSpace:u.userSpace(),type:r?"radial":"linear",start:s,end:o,stops:a},l=e.rawBBox(),c=l.topLeft(),h=l.getSize(),l={left:c.x,top:c.y,width:h.width,height:h.height},t.gradient(d,l),!0}function f(e,t,i){h(e.fill())&&h(e.stroke())?u(e,t,i,!0)||t.fillStroke():h(e.fill())?u(e,t,i,!1)||t.fill():h(e.stroke())?t.stroke():t.nop()}function p(e,t){var i,n,r,s=e.segments;if(4==s.length&&e.options.closed){for(i=[],n=0;n<s.length;++n){if(s[n].controlIn())return!1;i[n]=s[n].anchor()}if(r=i[0].y==i[1].y&&i[1].x==i[2].x&&i[2].y==i[3].y&&i[3].x==i[0].x||i[0].x==i[1].x&&i[1].y==i[2].y&&i[2].x==i[3].x&&i[3].y==i[0].y)return t.rect(i[0].x,i[0].y,i[2].x-i[0].x,i[2].y-i[0].y),!0}}function g(e,t,i){var n,r,s,o,a,d,l=e.segments;if(0!==l.length&&!p(e,t,i)){for(r=0;r<l.length;++r)s=l[r],o=s.anchor(),n?(a=n.controlOut(),d=s.controlIn(),a&&d?t.bezier(a.x,a.y,d.x,d.y,o.x,o.y):t.lineTo(o.x,o.y)):t.moveTo(o.x,o.y),n=s;e.options.closed&&t.close()}}function m(e,t,i){g(e,t,i),f(e,t,i)}function v(e,t,i){var n,r=e.paths;for(n=0;n<r.length;++n)g(r[n],t,i);f(e,t,i)}function _(e,t,i){var n=e.geometry();t.circle(n.center.x,n.center.y,n.radius),f(e,t,i)}function y(e,t,i){var n,r=e.geometry().curvePoints();for(t.moveTo(r[0].x,r[0].y),n=1;n<r.length;)t.bezier(r[n].x,r[n++].y,r[n].x,r[n++].y,r[n].x,r[n++].y);f(e,t,i)}function k(t,i){var n,r=e.pdf.parseFontDef(t.options.font),s=t._position;t.fill()&&t.stroke()?n=F.fillAndStroke:t.fill()?n=F.fill:t.stroke()&&(n=F.stroke),i.transform(1,0,0,-1,s.x,s.y+r.fontSize),i.beginText(),i.setFont(e.pdf.getFontURL(r),r.fontSize),i.setTextRenderingMode(n),i.showText(t.content(),t._pdfRect?t._pdfRect.width():null),i.endText()}function w(e,t,i){var n,r;for(e._pdfLink&&t.addLink(e._pdfLink.url,e._pdfLink),n=e.children,r=0;r<n.length;++r)a(n[r],t,i)}function b(e,t){var i,n,r,s=e.src();s&&(i=e.rect(),n=i.getOrigin(),r=i.getSize(),t.transform(r.width,0,0,-r.height,n.x,n.y+r.height),t.drawImage(s))}function S(e,t,i){var n=e.geometry();t.rect(n.origin.x,n.origin.y,n.size.width,n.size.height),f(e,t,i)}function x(e,i){var n,r=t.Deferred();for(n in i)"margin"==n&&e.options.pdf&&e.options.pdf._ignoreMargin||e.options.set("pdf."+n,i[n]);return C.pdf.toDataURL(e,r.resolve),r.promise()}function T(t){var i=e.parseColor(t,!0);return i?i.toRGB():null}function D(e){function t(e){return d=!0,e}function i(e){return e.visible()&&e.opacity()>0&&(h(e.fill())||h(e.stroke()))}function n(e){var t,i,n=[];for(t=0;t<e.length;++t)i=a(e[t]),null!=i&&n.push(i);return n}function r(e,t){var i,n=l,r=c;e.transform()&&(c=c.multiplyCopy(e.transform().matrix())),i=e.clip(),i&&(i=i.bbox(),i&&(i=i.bbox(c),l=l?H.Rect.intersect(l,i):i));try{return t()}finally{l=n,c=r}}function s(e){if(null==l)return!1;var t=e.rawBBox().bbox(c);return l&&t&&(t=H.Rect.intersect(t,l)),t}function a(a){return r(a,function(){if(!(a instanceof C.Group||a instanceof C.MultiPath)){var r=s(a);if(!r)return t(null);u=u?H.Rect.union(u,r):r}return o({Path:function(e){return 0!==e.segments.length&&i(e)?e:t(null)},MultiPath:function(e){if(!i(e))return t(null);var r=new C.MultiPath(e.options);return r.paths=n(e.paths),0===r.paths.length?t(null):r},Circle:function(e){return i(e)?e:t(null)},Arc:function(e){return i(e)?e:t(null)},Text:function(e){return/\S/.test(e.content())&&i(e)?e:t(null)},Image:function(e){return e.visible()&&e.opacity()>0?e:t(null)},Group:function(i){var r=new C.Group(i.options);return r.children=n(i.children),r._pdfLink=i._pdfLink,i===e||0!==r.children.length||i._pdfLink?r:t(null)},Rect:function(e){return i(e)?e:t(null)}},a)})}var d,l=!1,c=H.Matrix.unit(),u=null;do d=!1,e=a(e);while(e&&d);return{root:e,bbox:u}}var C=e.drawing,H=e.geometry,F=e.pdf.TEXT_RENDERING_MODE,M={dash:[4],dashDot:[4,2,1,2],dot:[1,2],longDash:[8,2],longDashDot:[8,2,1,2],longDashDotDot:[8,2,1,2,1,2],solid:[]},I={butt:0,round:1,square:2},z={miter:0,round:1,bevel:2};e.deepExtend(C,{exportPDF:x,pdf:{toDataURL:n,toBlob:r,saveAs:s,toStream:i}})}(window.kendo,window.kendo.jQuery)},"function"==typeof define&&define.amd?define:function(e,t,i){(i||t)()}),function(e,define){define("kendo.pdf.min",["kendo.core.min","pdf/core.min","pdf/ttf.min","pdf/drawing.min","kendo.drawing.min"],e)}(function(){return function(e,t){e.PDFMixin={extend:function(e){e.events.push("pdfExport"),e.options.pdf=this.options,e.saveAsPDF=this.saveAsPDF,e._drawPDF=this._drawPDF,e._drawPDFShadow=this._drawPDFShadow},options:{fileName:"Export.pdf",proxyURL:"",paperSize:"auto",allPages:!1,landscape:!1,margin:null,title:null,author:null,subject:null,keywords:null,creator:"Kendo UI PDF Generator v."+e.version,date:null},saveAsPDF:function(){var i,n=new t.Deferred,r=n.promise(),s={promise:r};if(!this.trigger("pdfExport",s))return i=this.options.pdf,i.multiPage=i.multiPage||i.allPages,this._drawPDF(n).then(function(t){return e.drawing.exportPDF(t,i)}).done(function(t){e.saveAs({dataURI:t,fileName:i.fileName,proxyURL:i.proxyURL,forceProxy:i.forceProxy,proxyTarget:i.proxyTarget}),n.resolve()}).fail(function(e){n.reject(e)}),r},_drawPDF:function(i){var n=new t.Deferred;return e.drawing.drawDOM(this.wrapper).done(function(e){var t={page:e,pageNumber:1,progress:1,totalPages:1};i.notify(t),n.resolve(t.page)}).fail(function(e){n.reject(e)}),n},_drawPDFShadow:function(i,n){var r,s,o;return i=i||{},r=this.wrapper,s=t("<div class='k-pdf-export-shadow'>"),i.width&&s.css({width:i.width,overflow:"visible"}),r.before(s),s.append(i.content||r.clone(!0,!0)),o=t.Deferred(),setTimeout(function(){var t=e.drawing.drawDOM(s,n);t.always(function(){s.remove()}).then(function(){o.resolve.apply(o,arguments)}).fail(function(){o.reject.apply(o,arguments)}).progress(function(){o.progress.apply(o,arguments)})},15),o.promise()}}}(kendo,window.kendo.jQuery),kendo},"function"==typeof define&&define.amd?define:function(e,t,i){(i||t)()}),/** 
 * Kendo UI v2016.2.714 (http://www.telerik.com/kendo-ui)                                                                                                                                               
 * Copyright 2016 Telerik AD. All rights reserved.                                                                                                                                                      
 *                                                                                                                                                                                                      
 * Kendo UI commercial licenses may be obtained at                                                                                                                                                      
 * http://www.telerik.com/purchase/license-agreement/kendo-ui-complete                                                                                                                                  
 * If you do not own a commercial license, this file shall be governed by the trial license terms.                                                                                                      
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       

*/
function(e,define){define("kendo.gantt.list.min",["kendo.dom.min","kendo.touch.min","kendo.draganddrop.min","kendo.columnsorter.min","kendo.datetimepicker.min","kendo.editable.min"],e)}(function(){return function(e){function t(e){var t,i,n=[],r=e.className;for(t=0,i=e.level;t<i;t++)n.push(s("span",{className:r}));return n}function i(){var t=n._activeElement();t&&"body"!==t.nodeName.toLowerCase()&&e(t).blur()}var n=window.kendo,r=n.dom,s=r.element,o=r.text,a=n.support.browser,d=n.support.mobileOS,l=n.ui,c=l.Widget,h=e.extend,u=e.map,f=e.isFunction,p=a.msie&&a.version<9,g=n.keys,m={title:"Title",start:"Start Time",end:"End Time",percentComplete:"% Done",parentId:"Predecessor ID",id:"ID",orderId:"Order ID"},v="string",_=".kendoGanttList",y="click",k=".",w="<table style='visibility: hidden;'><tbody><tr style='height:{0}'><td>&nbsp;</td></tr></tbody></table>",b={wrapper:"k-treelist k-grid k-widget",header:"k-header",alt:"k-alt",rtl:"k-rtl",editCell:"k-edit-cell",group:"k-treelist-group",gridHeader:"k-grid-header",gridHeaderWrap:"k-grid-header-wrap",gridContent:"k-grid-content",gridContentWrap:"k-grid-content",selected:"k-state-selected",icon:"k-icon",iconCollapse:"k-i-collapse",iconExpand:"k-i-expand",iconHidden:"k-i-none",iconPlaceHolder:"k-icon k-i-none",input:"k-input",link:"k-link",resizeHandle:"k-resize-handle",resizeHandleInner:"k-resize-handle-inner",dropPositions:"k-insert-top k-insert-bottom k-add k-insert-middle",dropTop:"k-insert-top",dropBottom:"k-insert-bottom",dropAdd:"k-add",dropMiddle:"k-insert-middle",dropDenied:"k-denied",dragStatus:"k-drag-status",dragClue:"k-drag-clue",dragClueText:"k-clue-text"},S=l.GanttList=c.extend({init:function(t,i){c.fn.init.call(this,t,i),0===this.options.columns.length&&this.options.columns.push("title"),this.dataSource=this.options.dataSource,this._columns(),this._layout(),this._domTrees(),this._header(),this._sortable(),this._editable(),this._selectable(),this._draggable(),this._resizable(),this._attachEvents(),this._adjustHeight(),this.bind("render",function(){var t,i;this.options.resizable&&(t=this.header.find("col"),i=this.content.find("col"),this.header.find("th").not(":last").each(function(n){var r=e(this).outerWidth();t.eq(n).width(r),i.eq(n).width(r)}),t.last().css("width","auto"),i.last().css("width","auto"))},!0)},_adjustHeight:function(){this.content.height(this.element.height()-this.header.parent().outerHeight())},destroy:function(){c.fn.destroy.call(this),this._reorderDraggable&&this._reorderDraggable.destroy(),this._tableDropArea&&this._tableDropArea.destroy(),this._contentDropArea&&this._contentDropArea.destroy(),this._columnResizable&&this._columnResizable.destroy(),this.touch&&this.touch.destroy(),this.timer&&clearTimeout(this.timer),this.content.off(_),this.header.find("thead").off(_),this.header.find(k+S.link).off(_),this.header=null,this.content=null,this.levels=null,n.destroy(this.element)},options:{name:"GanttList",selectable:!0,editable:!0,resizable:!1},_attachEvents:function(){var t=this,i=S.styles;t.content.on(y+_,"td > span."+i.icon+":not(."+i.iconHidden+")",function(i){var n=e(this),r=t._modelFromElement(n);r.set("expanded",!r.get("expanded")),i.stopPropagation()})},_domTrees:function(){this.headerTree=new r.Tree(this.header[0]),this.contentTree=new r.Tree(this.content[0])},_columns:function(){var e=this.options.columns,t=function(){this.field="",this.title="",this.editable=!1,this.sortable=!1};this.columns=u(e,function(e){return e=typeof e===v?{field:e,title:m[e]}:e,h(new t,e)})},_layout:function(){var t=this,i=this.options,r=this.element,s=S.styles,o=function(){var r,s=typeof i.rowHeight===v?i.rowHeight:i.rowHeight+"px",o=e(n.format(w,s));return t.content.append(o),r=o.find("tr").outerHeight(),o.remove(),r};r.addClass(s.wrapper).append("<div class='"+s.gridHeader+"'><div class='"+s.gridHeaderWrap+"'></div></div>").append("<div class='"+s.gridContentWrap+"'></div>"),this.header=r.find(k+s.gridHeaderWrap),this.content=r.find(k+s.gridContent),i.rowHeight&&(this._rowHeight=o())},_header:function(){var e=this.headerTree,t=s("colgroup",null,this._cols()),i=s("thead",{role:"rowgroup"},[s("tr",{role:"row"},this._ths())]),n=s("table",{style:{minWidth:this.options.listWidth+"px"},role:"grid"},[t,i]);e.render([n])},_render:function(e){var t,i,n,r={style:{minWidth:this.options.listWidth+"px"},tabIndex:0,role:"treegrid"};this._rowHeight&&(r.style.height=e.length*this._rowHeight+"px"),this.levels=[{field:null,value:0}],t=s("colgroup",null,this._cols()),i=s("tbody",{role:"rowgroup"},this._trs(e)),n=s("table",r,[t,i]),this.contentTree.render([n]),this.trigger("render")},_ths:function(){var e,t,i,n,r=this.columns,a=[];for(i=0,n=r.length;i<n;i++)e=r[i],t={"data-field":e.field,"data-title":e.title,className:S.styles.header,role:"columnheader"},a.push(s("th",t,[o(e.title)]));return this.options.resizable&&a.push(s("th",{className:S.styles.header,role:"columnheader"})),a},_cols:function(){var e,t,i,n,r,o=this.columns,a=[];for(n=0,r=o.length;n<r;n++)e=o[n],i=e.width,t=i&&0!==parseInt(i,10)?{style:{width:typeof i===v?i:i+"px"}}:null,a.push(s("col",t,[]));return this.options.resizable&&a.push(s("col",{style:{width:"1px"}})),a},_trs:function(e){var t,i,n,r,s,o=[],a=[],d=S.styles;for(r=0,s=e.length;r<s;r++)t=e[r],n=this._levels({idx:t.parentId,id:t.id,summary:t.summary}),i={"data-uid":t.uid,"data-level":n,role:"row"},t.summary&&(i["aria-expanded"]=t.expanded),r%2!==0&&a.push(d.alt),t.summary&&a.push(d.group),a.length&&(i.className=a.join(" ")),o.push(this._tds({task:t,attr:i,level:n})),a=[];return o},_tds:function(e){var t,i,n,r=[],o=this.columns;for(i=0,n=o.length;i<n;i++)t=o[i],r.push(this._td({task:e.task,column:t,level:e.level}));return this.options.resizable&&r.push(s("td",{role:"gridcell"})),s("tr",e.attr,r)},_td:function(e){var i,r,a,d=[],l=this.options.resourcesField,c=S.styles,h=e.task,u=e.column,f=h.get(u.field);if(u.field==l){for(f=f||[],i=[],a=0;a<f.length;a++)i.push(n.format("{0} [{1}]",f[a].get("name"),f[a].get("formatedValue")));i=i.join(", ")}else i=u.format?n.format(u.format,f):f;return"title"===u.field&&(d=t({level:e.level,className:c.iconPlaceHolder}),d.push(s("span",{className:c.icon+" "+(h.summary?h.expanded?c.iconCollapse:c.iconExpand:c.iconHidden)})),r=n.format("{0}, {1:P0}",i,h.percentComplete)),d.push(s("span",{"aria-label":r},[o(i)])),s("td",{role:"gridcell"},d)},_levels:function(e){var t,i,n,r=this.levels,s=e.summary,o=e.idx,a=e.id;for(i=0,n=r.length;i<n;i++)if(t=r[i],t.field==o)return s&&r.push({field:a,value:t.value+1}),t.value},_sortable:function(){var e,t,i,r,s,o=this,a=this.options.resourcesField,d=this.columns,l=this.header.find("th["+n.attr("field")+"]"),c=function(e){(0===o.dataSource.total()||o.editable&&o.editable.trigger("validate"))&&(e.preventDefault(),e.stopImmediatePropagation())};for(r=0,s=l.length;r<s;r++)e=d[r],e.sortable&&e.field!==a&&(i=l.eq(r),t=i.data("kendoColumnSorter"),t&&t.destroy(),i.attr("data-"+n.ns+"field",e.field).kendoColumnSorter({dataSource:this.dataSource}).find(k+S.styles.link).on("click"+_,c));l=null},_selectable:function(){var t=this,i=this.options.selectable;i&&this.content.on(y+_,"tr",function(i){var n=e(this);t.editable&&t.editable.trigger("validate"),i.ctrlKey?t.clearSelection():t.select(n)})},select:function(e){var t=this.content.find(e),i=S.styles.selected;return t.length?(t.siblings(k+i).removeClass(i).attr("aria-selected",!1).end().addClass(i).attr("aria-selected",!0),void this.trigger("change")):this.content.find(k+i)},clearSelection:function(){var e=this.select();e.length&&(e.removeClass(S.styles.selected),this.trigger("change"))},_setDataSource:function(e){this.dataSource=e},_editable:function(){var t=this,n=S.styles,r="span."+n.icon+":not("+n.iconHidden+")",s=function(){var e=t.editable;e&&(e.end()?t._closeCell():e.trigger("validate"))},o=function(t){var r=e(t.currentTarget);r.hasClass(n.editCell)||i()};this.options.editable&&(this._startEditHandler=function(i){var n=i.currentTarget?e(i.currentTarget):i,r=t._columnFromElement(n);t.editable||r&&r.editable&&t._editCell({cell:n,column:r})},t.content.on("focusin"+_,function(){clearTimeout(t.timer),t.timer=null}).on("focusout"+_,function(){t.timer=setTimeout(s,1)}).on("keydown"+_,function(e){e.keyCode===g.ENTER&&e.preventDefault()}).on("keyup"+_,function(e){var n,r,o=e.keyCode;switch(o){case g.ENTER:i(),s();break;case g.ESC:t.editable&&(n=t._editableContainer,r=t._modelFromElement(n),t.trigger("cancel",{model:r,cell:n})||t._closeCell(!0))}}),d?t.touch=t.content.kendoTouch({filter:"td",touchstart:function(e){o(e.touch)},doubletap:function(i){e(i.touch.initialTouch).is(r)||t._startEditHandler(i.touch)}}).data("kendoTouch"):t.content.on("mousedown"+_,"td",function(e){o(e)}).on("dblclick"+_,"td",function(i){e(i.target).is(r)||t._startEditHandler(i)}))},_editCell:function(t){var i,r=this.options.resourcesField,s=S.styles,o=t.cell,a=t.column,d=this._modelFromElement(o),l=this.dataSource._createNewModel(d.toJSON()),c=l.fields[a.field]||l[a.field],h=c.validation,u=n.attr("type"),g=n.attr("bind"),m=n.attr("format"),v={name:a.field,required:!!c.validation&&c.validation.required===!0};return a.field===r?void a.editor(o,l):(this._editableContent=o.children().detach(),this._editableContainer=o,o.data("modelCopy",l),"date"!==c.type&&"date"!==e.type(c)||a.format&&!/H|m|s|F|g|u/.test(a.format)||(v[g]="value:"+a.field,v[u]="date",a.format&&(v[m]=n._extractFormat(a.format)),i=function(t,i){e('<input type="text"/>').attr(v).appendTo(t).kendoDateTimePicker({format:i.format})}),this.editable=o.addClass(s.editCell).kendoEditable({fields:{field:a.field,format:a.format,editor:a.editor||i},model:l,clearContainer:!1}).data("kendoEditable"),h&&h.dateCompare&&f(h.dateCompare)&&h.message&&(e("<span "+n.attr("for")+'="'+a.field+'" class="k-invalid-msg"/>').hide().appendTo(o),o.find("[name="+a.field+"]").attr(n.attr("dateCompare-msg"),h.message)),this.editable.bind("validate",function(e){var t=this.element.find(":kendoFocusable:first").focus();p&&t.focus(),e.preventDefault()}),void(this.trigger("edit",{model:d,cell:o})&&this._closeCell(!0)))},_closeCell:function(e){var t=S.styles,i=this._editableContainer,n=this._modelFromElement(i),r=this._columnFromElement(i),s=r.field,o=i.data("modelCopy"),a={};a[s]=o.get(s),i.empty().removeData("modelCopy").removeClass(t.editCell).append(this._editableContent),this.editable.unbind(),this.editable.destroy(),this.editable=null,this._editableContainer=null,this._editableContent=null,e||("start"===s&&(a.end=new Date(a.start.getTime()+n.duration())),this.trigger("update",{task:n,updateInfo:a}))},_draggable:function(){var t,i=this,r=null,s=!0,o=S.styles,a=n.support.isRtl(this.element),l="tr["+n.attr("level")+" = 0]:last",c={},u=function(){r=null,t=null,s=!0,c={}},f=function(e){for(var t=e;t;){if(r.get("id")===t.get("id")){s=!1;break}t=i.dataSource.taskParent(t)}},p=function(){var i=e(t).height(),r=n.getOffset(t).top;h(t,{beforeLimit:r+.25*i,afterLimit:r+.75*i})},g=function(e){var i,r,s,a,d;t&&(i=e.location,r=o.dropAdd,s="add",a=parseInt(t.attr(n.attr("level")),10),i<=t.beforeLimit?(d=t.prev(),r=o.dropTop,s="insert-before"):i>=t.afterLimit&&(d=t.next(),r=o.dropBottom,s="insert-after"),d&&parseInt(d.attr(n.attr("level")),10)===a&&(r=o.dropMiddle),c.className=r,c.command=s)},m=function(){return i._reorderDraggable.hint.children(k+o.dragStatus).removeClass(o.dropPositions)};this.options.editable&&(this._reorderDraggable=this.content.kendoDraggable({distance:10,holdToDrag:d,group:"listGroup",filter:"tr[data-uid]",ignore:k+o.input,hint:function(t){return e('<div class="'+o.header+" "+o.dragClue+'"/>').css({width:300,paddingLeft:t.css("paddingLeft"),paddingRight:t.css("paddingRight"),lineHeight:t.height()+"px",paddingTop:t.css("paddingTop"),paddingBottom:t.css("paddingBottom")}).append('<span class="'+o.icon+" "+o.dragStatus+'" /><span class="'+o.dragClueText+'"/>')},cursorOffset:{top:-20,left:0},container:this.content,dragstart:function(e){return i.editable&&i.editable.trigger("validate")?void e.preventDefault():(r=i._modelFromElement(e.currentTarget),this.hint.children(k+o.dragClueText).text(r.get("title")),void(a&&this.hint.addClass(o.rtl)))},drag:function(e){s&&(g(e.y),m().addClass(c.className))},dragend:function(){u()},dragcancel:function(){u()}}).data("kendoDraggable"),this._tableDropArea=this.content.kendoDropTargetArea({distance:0,group:"listGroup",filter:"tr[data-uid]",dragenter:function(e){t=e.dropTarget,f(i._modelFromElement(t)),p(),m().toggleClass(o.dropDenied,!s)},dragleave:function(){s=!0,m()},drop:function(){var e=i._modelFromElement(t),n=e.orderId,o={parentId:e.parentId};if(s){switch(c.command){case"add":o.parentId=e.id;break;case"insert-before":o.orderId=e.parentId===r.parentId&&e.orderId>r.orderId?n-1:n;break;case"insert-after":o.orderId=e.parentId===r.parentId&&e.orderId>r.orderId?n:n+1}i.trigger("update",{task:r,updateInfo:o})}}}).data("kendoDropTargetArea"),this._contentDropArea=this.element.kendoDropTargetArea({distance:0,group:"listGroup",filter:k+o.gridContent,drop:function(){var e=i._modelFromElement(i.content.find(l)),t=e.orderId,n={parentId:null,orderId:null!==r.parentId?t+1:t};i.trigger("update",{task:r,updateInfo:n})}}).data("kendoDropTargetArea"))},_resizable:function(){var t=this,i=S.styles,n=function(n){var r,s,o=e(n.currentTarget),a=t.resizeHandle,d=o.position(),l=d.left,c=o.outerWidth(),h=o.closest("div"),u=n.clientX+e(window).scrollLeft(),f=t.options.columnResizeHandleWidth;return l+=h.scrollLeft(),a||(a=t.resizeHandle=e('<div class="'+i.resizeHandle+'"><div class="'+i.resizeHandleInner+'" /></div>')),r=o.offset().left+c,(s=u>r-f&&u<r+f)?(h.append(a),void a.show().css({top:d.top,left:l+c-f-1,height:o.outerHeight(),width:3*f}).data("th",o)):void a.hide()};this.options.resizable&&(this._columnResizable&&this._columnResizable.destroy(),this.header.find("thead").on("mousemove"+_,"th",n),this._columnResizable=this.header.kendoResizable({handle:k+i.resizeHandle,start:function(i){var n=e(i.currentTarget).data("th"),r="col:eq("+n.index()+")",s=t.header.find("table"),o=t.content.find("table");t.element.addClass("k-grid-column-resizing"),this.col=o.children("colgroup").find(r).add(s.find(r)),this.th=n,this.startLocation=i.x.location,this.columnWidth=n.outerWidth(),this.table=s.add(o),this.totalWidth=this.table.width()-s.find("th:last").outerWidth()},resize:function(e){var t=11,i=e.x.location-this.startLocation;this.columnWidth+i<t&&(i=t-this.columnWidth),this.table.css({minWidth:this.totalWidth+i}),this.col.width(this.columnWidth+i)},resizeend:function(){var e,i,n;t.element.removeClass("k-grid-column-resizing"),e=Math.floor(this.columnWidth),i=Math.floor(this.th.outerWidth()),n=t.columns[this.th.index()],t.trigger("columnResize",{column:n,oldWidth:e,newWidth:i}),this.table=this.col=this.th=null}}).data("kendoResizable"))},_modelFromElement:function(e){var t=e.closest("tr"),i=this.dataSource.getByUid(t.attr(n.attr("uid")));return i},_columnFromElement:function(e){var t=e.closest("td"),i=t.parent(),n=i.children().index(t);return this.columns[n]}});h(!0,l.GanttList,{styles:b})}(window.kendo.jQuery),window.kendo},"function"==typeof define&&define.amd?define:function(e,t,i){(i||t)()}),/** 
 * Kendo UI v2016.2.714 (http://www.telerik.com/kendo-ui)                                                                                                                                               
 * Copyright 2016 Telerik AD. All rights reserved.                                                                                                                                                      
 *                                                                                                                                                                                                      
 * Kendo UI commercial licenses may be obtained at                                                                                                                                                      
 * http://www.telerik.com/purchase/license-agreement/kendo-ui-complete                                                                                                                                  
 * If you do not own a commercial license, this file shall be governed by the trial license terms.                                                                                                      
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       

*/
function(e,define){define("kendo.gantt.timeline.min",["kendo.dom.min","kendo.touch.min","kendo.draganddrop.min"],e)}(function(){return function(e){function t(e){return delete e.name,delete e.prefix,delete e.views,e}function i(e){var t=[],i=e.workWeekStart;for(t.push(i);e.workWeekEnd!=i;)i>6?i-=7:i++,t.push(i);return t}function n(){var t=kendo._activeElement();t&&"body"!==t.nodeName.toLowerCase()&&e(t).blur()}var r,s,o=kendo.ui.Widget,a=kendo.dom.element,d=kendo.dom.text,l=kendo.dom.html,c=e.isPlainObject,h=e.extend,u=e.proxy,f=kendo.support.browser,p=!1,g=kendo.keys,m=kendo.data.Query,v="string",_=".kendoGanttTimeline",y="click",k="dblclick",w="mousemove",b="mouseenter",S="mouseleave",x="keydown",T=".",D=kendo.template("#=kendo.toString(start, 't')#"),C=kendo.template("#=kendo.toString(start, 'ddd M/dd')#"),H=kendo.template("#=kendo.toString(start, 'ddd M/dd')# - #=kendo.toString(kendo.date.addDays(end, -1), 'ddd M/dd')#"),F=kendo.template("#=kendo.toString(start, 'MMM')#"),M=kendo.template("#=kendo.toString(start, 'yyyy')#"),I=kendo.template('<div class="#=styles.marquee#"><div class="#=styles.marqueeColor#"></div></div>'),z=kendo.template('<div style="z-index: 100002;" class="#=styles.tooltipWrapper#"><div class="#=styles.tooltipContent#"><div>#=messages.start#: #=kendo.toString(start, format)#</div><div>#=messages.end#: #=kendo.toString(end, format)#</div></div></div>'),R=kendo.template('<div style="z-index: 100002;" class="#=styles.tooltipWrapper#" ><div class="#=styles.tooltipContent#">#=text#%</div><div class="#=styles.tooltipCallout#" style="left:13px;"></div></div>'),A=kendo.template('<div class="#=styles.taskDetails#"><strong>#=task.title#</strong><div class="#=styles.taskDetailsPercent#">#=kendo.toString(task.percentComplete, "p0")#</div><ul class="#=styles.reset#"><li>#=messages.start#: #=kendo.toString(task.start, "h:mm tt ddd, MMM d")#</li><li>#=messages.end#: #=kendo.toString(task.end, "h:mm tt ddd, MMM d")#</li></ul></div>'),E="<table style='visibility: hidden;'><tbody><tr style='height:{0}'><td>&nbsp;</td></tr></tbody></table>",W={day:{type:"kendo.ui.GanttDayView"},week:{type:"kendo.ui.GanttWeekView"},month:{type:"kendo.ui.GanttMonthView"},year:{type:"kendo.ui.GanttYearView"}},N={alt:"k-alt",reset:"k-reset",nonWorking:"k-nonwork-hour",header:"k-header",gridHeader:"k-grid-header",gridHeaderWrap:"k-grid-header-wrap",gridContent:"k-grid-content",tasksWrapper:"k-gantt-tables",rowsTable:"k-gantt-rows",columnsTable:"k-gantt-columns",tasksTable:"k-gantt-tasks",dependenciesWrapper:"k-gantt-dependencies",resource:"k-resource",resourceAlt:"k-resource k-alt",task:"k-task",taskSingle:"k-task-single",taskMilestone:"k-task-milestone",taskSummary:"k-task-summary",taskWrap:"k-task-wrap",taskMilestoneWrap:"k-milestone-wrap",resourcesWrap:"k-resources-wrap",taskDot:"k-task-dot",taskDotStart:"k-task-start",taskDotEnd:"k-task-end",taskDragHandle:"k-task-draghandle",taskContent:"k-task-content",taskTemplate:"k-task-template",taskActions:"k-task-actions",taskDelete:"k-task-delete",taskComplete:"k-task-complete",taskDetails:"k-task-details",taskDetailsPercent:"k-task-pct",link:"k-link",icon:"k-icon",iconDelete:"k-si-close",taskResizeHandle:"k-resize-handle",taskResizeHandleWest:"k-resize-w",taskResizeHandleEast:"k-resize-e",taskSummaryProgress:"k-task-summary-progress",taskSummaryComplete:"k-task-summary-complete",line:"k-line",lineHorizontal:"k-line-h",lineVertical:"k-line-v",arrowWest:"k-arrow-w",arrowEast:"k-arrow-e",dragHint:"k-drag-hint",dependencyHint:"k-dependency-hint",tooltipWrapper:"k-widget k-tooltip k-popup k-group k-reset",tooltipContent:"k-tooltip-content",tooltipCallout:"k-callout k-callout-s",callout:"k-callout",marquee:"k-marquee k-gantt-marquee",marqueeColor:"k-marquee-color"},L=kendo.ui.GanttView=o.extend({init:function(e,t){o.fn.init.call(this,e,t),this.title=this.options.title||this.options.name,this.header=this.element.find(T+L.styles.gridHeader),this.content=this.element.find(T+L.styles.gridContent),this.contentWidth=this.content.width(),this._workDays=i(this.options),this._headerTree=t.headerTree,this._taskTree=t.taskTree,this._taskTemplate=t.taskTemplate?kendo.template(t.taskTemplate,h({},kendo.Template,t.templateSettings)):null,this._dependencyTree=t.dependencyTree,this._taskCoordinates={},this._currentTime()},destroy:function(){o.fn.destroy.call(this),clearTimeout(this._tooltipTimeout),this.headerRow=null,this.header=null,this.content=null,this._dragHint=null,this._resizeHint=null,this._resizeTooltip=null,this._taskTooltip=null,this._percentCompleteResizeTooltip=null,this._headerTree=null,this._taskTree=null,this._dependencyTree=null},options:{showWorkHours:!1,showWorkDays:!1,workDayStart:new Date(1980,1,1,8,0,0),workDayEnd:new Date(1980,1,1,17,0,0),workWeekStart:1,workWeekEnd:5,hourSpan:1,slotSize:100,currentTimeMarker:{updateInterval:1e4}},renderLayout:function(){this._slots=this._createSlots(),this._tableWidth=this._calculateTableWidth(),this.createLayout(this._layout()),this._slotDimensions(),this._adjustHeight(),this.content.find(T+L.styles.dependenciesWrapper).width(this._tableWidth)},_adjustHeight:function(){this.content.height(this.element.height()-this.header.outerHeight())},createLayout:function(e){var t=this._headers(e),i=this._colgroup(),n=this._headerTree,r=a("thead",null,t),s=a("table",{style:{width:this._tableWidth+"px"}},[i,r]);n.render([s]),this.headerRow=this.header.find("table:first tr").last()},_slotDimensions:function(){var e,t,i,n,r=this.headerRow[0].children,s=this._timeSlots();for(i=0,n=r.length;i<n;i++)t=r[i],e=s[i],e.offsetLeft=t.offsetLeft,e.offsetWidth=t.offsetWidth},render:function(e){var t,i,n=e.length,r=L.styles,s=this._rowsTable(n),o=this._columnsTable(n),a=this._tasksTable(e),d=this.options.currentTimeMarker,l=this.options.calculatedSize;this._taskTree.render([s,o,a]),t=this.content.find(T+r.rowsTable),l&&(i=l.row*e.length,this.content.find(T+r.tasksTable).height(i),t.height(i)),this._contentHeight=t.height(),this._rowHeight=l?l.row:this._contentHeight/t.find("tr").length,this.content.find(T+r.columnsTable).height(this._contentHeight),d!==!1&&void 0!==d.updateInterval&&this._renderCurrentTime()},_rowsTable:function(e){var t,i,n=[],r=L.styles,s=[null,{className:r.alt}];for(i=0;i<e;i++)t=a("tr",s[i%2],[a("td",null,[d(" ")])]),n.push(t);return this._createTable(1,n,{className:r.rowsTable})},_columnsTable:function(){var e,t,i,n,r,s=[],o=L.styles,l=this._timeSlots(),c=l.length,h=0;for(r=0;r<c;r++)t=l[r],n={},i=t.span,h+=i,1!==i&&(n.colspan=i),t.isNonWorking&&(n.className=o.nonWorking),s.push(a("td",n,[d(" ")]));return e=a("tr",null,s),this._createTable(h,[e],{className:o.columnsTable})},_tasksTable:function(e){var t,i,n,r,s,o,d,l,c=[],h=L.styles,u=this._taskCoordinates={},f=this._calculateMilestoneWidth(),g=Math.round(f.width),m=this.options.resourcesField,v=[h.resource,h.resourceAlt],_=this.options.calculatedSize,y=this._calculateResourcesMargin(),k=this._calculateTaskBorderWidth(),w=function(e){var t=n.left,i=t+n.width;r.isMilestone()&&(t-=g/2,i=t+g),u[r.id]={start:t,end:i,rowIndex:e}};for(d=0,l=e.length;d<l;d++)r=e[d],n=this._taskPosition(r),n.borderWidth=k,t=a("tr",null),i=a("td",null,[this._renderTask(e[d],n)]),r[m]&&r[m].length&&(s=p?this._tableWidth-n.left:Math.max(n.width||f.clientWidth,0)+n.left,o={width:this._tableWidth-(s+y)+"px"},o[p?"right":"left"]=s+"px",_&&(o.height=_.cell+"px"),i.children.push(a("div",{className:h.resourcesWrap,style:o},this._renderResources(r[m],v[d%2])))),t.children.push(i),c.push(t),w(d);return this._createTable(1,c,{className:L.styles.tasksTable})},_createTable:function(e,t,i){var n,r,s,o=[];for(s=0;s<e;s++)o.push(a("col"));return n=a("colgroup",null,o),r=a("tbody",null,t),i.style||(i.style={}),i.style.width=this._tableWidth+"px",a("table",i,[n,r])},_calculateTableWidth:function(){var e,t,i,n,r=this._timeSlots(),s=0,o=0;for(i=0,n=r.length;i<n;i++)e=r[i].span,o+=e,e>s&&(s=e);return t=Math.round(o*this.options.slotSize/s)},_calculateMilestoneWidth:function(){var t,i,n=L.styles.task+" "+L.styles.taskMilestone,r=e("<div class='"+n+"' style='visibility: hidden; position: absolute'>");return this.content.append(r),i=r[0].getBoundingClientRect(),t={width:i.right-i.left,clientWidth:r[0].clientWidth},r.remove(),t},_calculateResourcesMargin:function(){var t,i=e("<div class='"+L.styles.resourcesWrap+"' style='visibility: hidden; position: absolute'>");return this.content.append(i),t=parseInt(i.css(p?"margin-right":"margin-left"),10),i.remove(),t},_calculateTaskBorderWidth:function(){var t,i,n=L.styles.task+" "+L.styles.taskSingle,r=e("<div class='"+n+"' style='visibility: hidden; position: absolute'>");return this.content.append(r),i=kendo.getComputedStyles(r[0],["border-left-width"]),t=parseFloat(i["border-left-width"],10),r.remove(),t},_renderTask:function(e,t){var i,n,r,s=this.options.editable,o=t.left,d=L.styles,l=d.taskWrap,c=this.options.calculatedSize,h={},u={className:l,style:{left:o+"px"}};return c&&(u.style.height=c.cell+"px"),e.summary?n=this._renderSummary(e,t):e.isMilestone()?(n=this._renderMilestone(e,t),u.className+=" "+d.taskMilestoneWrap):n=this._renderSingleTask(e,t),i=a("div",u,[n]),s&&(i.children.push(a("div",{className:d.taskDot+" "+d.taskDotStart})),i.children.push(a("div",{className:d.taskDot+" "+d.taskDotEnd}))),e.summary||e.isMilestone()||!s||null!==this._taskTemplate||(r=Math.round(t.width*e.percentComplete),h[p?"right":"left"]=r+"px",i.children.push(a("div",{className:d.taskDragHandle,style:h}))),i},_renderSingleTask:function(e,t){var i,n,r,s=L.styles,o=Math.round(t.width*e.percentComplete),c=[];return null!==this._taskTemplate?i=l(this._taskTemplate(e)):(i=d(e.title),c.push(a("div",{className:s.taskComplete,style:{width:o+"px"}}))),n=a("div",{className:s.taskContent},[a("div",{className:s.taskTemplate},[i])]),c.push(n),this.options.editable&&(n.children.push(a("span",{className:s.taskActions},[a("a",{className:s.link+" "+s.taskDelete,href:"#"},[a("span",{className:s.icon+" "+s.iconDelete})])])),n.children.push(a("span",{className:s.taskResizeHandle+" "+s.taskResizeHandleWest})),n.children.push(a("span",{className:s.taskResizeHandle+" "+s.taskResizeHandleEast}))),r=a("div",{className:s.task+" "+s.taskSingle,"data-uid":e.uid,style:{width:Math.max(t.width-2*t.borderWidth,0)+"px"}},c)},_renderMilestone:function(e){var t=L.styles,i=a("div",{className:t.task+" "+t.taskMilestone,"data-uid":e.uid});return i},_renderSummary:function(e,t){var i=L.styles,n=Math.round(t.width*e.percentComplete),r=a("div",{className:i.task+" "+i.taskSummary,"data-uid":e.uid,style:{width:t.width+"px"}},[a("div",{className:i.taskSummaryProgress,style:{width:n+"px"}},[a("div",{className:i.taskSummaryComplete,style:{width:t.width+"px"}})])]);return r},_renderResources:function(e,t){var i,n,r,s=[];for(n=0,r=e.length;n<r;n++)i=e[n],s.push(a("span",{className:t,style:{color:i.get("color")}},[d(i.get("name"))]));return p&&s.reverse(),s},_taskPosition:function(e){var t=Math.round,i=t(this._offset(p?e.end:e.start)),n=t(this._offset(p?e.start:e.end));return{left:i,width:n-i}},_offset:function(e){var t,i,n,r,s=this._timeSlots(),o=0;return s.length?(r=this._slotIndex("start",e),t=s[r],t.end<e?o=t.offsetWidth:t.start<=e&&(i=e-t.start,n=t.end-t.start,o=i/n*t.offsetWidth),p&&(o=t.offsetWidth+1-o),t.offsetLeft+o):0},_slotIndex:function(e,t,i){var n,r=this._timeSlots(),s=0,o=r.length-1;i&&(r=[].slice.call(r).reverse());do n=Math.ceil((o+s)/2),r[n][e]<t?s=n:(n===o&&n--,o=n);while(s!==o);return i&&(s=r.length-1-s),s},_timeByPosition:function(t,i,n){var r,s,o,a=this._slotByPosition(t);return i?n?a.end:a.start:(r=t-e(T+L.styles.tasksTable).offset().left,s=a.end-a.start,o=r-a.offsetLeft,p&&(o=a.offsetWidth-o),new Date(a.start.getTime()+s*(o/a.offsetWidth)))},_slotByPosition:function(t){var i=t-e(T+L.styles.tasksTable).offset().left,n=this._slotIndex("offsetLeft",i,p);return this._timeSlots()[n]},_renderDependencies:function(e){var t,i,n=[],r=this._dependencyTree;for(t=0,i=e.length;t<i;t++)n.push.apply(n,this._renderDependency(e[t]));r.render(n)},_renderDependency:function(e){var t,i,n,r,s=this._taskCoordinates[e.predecessorId],o=this._taskCoordinates[e.successorId];if(!s||!o)return[];for(i="_render"+["FF","FS","SF","SS"][p?3-e.type:e.type],t=this[i](s,o),n=0,r=t.length;n<r;n++)t[n].attr["data-uid"]=e.uid;return t},_renderFF:function(e,t){var i=this._dependencyFF(e,t,!1);return i[i.length-1].children[0]=this._arrow(!0),i},_renderSS:function(e,t){var i=this._dependencyFF(t,e,!0);return i[0].children[0]=this._arrow(!1),i.reverse()},_renderFS:function(e,t){var i=this._dependencyFS(e,t,!1);return i[i.length-1].children[0]=this._arrow(!1),i},_renderSF:function(e,t){var i=this._dependencyFS(t,e,!0);return i[0].children[0]=this._arrow(!0),i.reverse()},_dependencyFF:function(e,t,i){var n,r=this,s=[],o=0,a=0,d=0,l=0,c=i?"start":"end",h=2,u=1,f=this._rowHeight,p=10,g=e.rowIndex*f+Math.floor(f/2)-1,m=t.rowIndex*f+Math.floor(f/2)-1,v=L.styles,_=function(){s.push(r._line(v.line+" "+v.lineHorizontal,{left:o+"px",top:a+"px",width:d+"px"}))},y=function(){s.push(r._line(v.line+" "+v.lineVertical,{left:o+"px",top:a+"px",height:l+"px"}))};return o=e[c],a=g,d=p,n=t[c]-e[c],n>0!==i&&(d=Math.abs(n)+p),i?(o-=d,d-=u,_()):(_(),o+=d-h),m<a?(l=a-m,l+=h,a=m,y()):(l=m-a,l+=h,y(),a+=l-h),d=Math.abs(o-t[c]),i||(d-=u,o-=d),_(),s},_dependencyFS:function(e,t,i){var n=this,r=[],s=0,o=0,a=0,d=0,l=this._rowHeight,c=Math.floor(l/2),h=10,u=2*h,f=t.start-e.end,p=2,g=1,m=e.rowIndex*l+Math.floor(l/2)-1,v=t.rowIndex*l+Math.floor(l/2)-1,_=L.styles,y=function(){r.push(n._line(_.line+" "+_.lineHorizontal,{left:s+"px",top:o+"px",width:a+"px"}))},k=function(){r.push(n._line(_.line+" "+_.lineVertical,{left:s+"px",top:o+"px",height:d+"px"}))};return s=e.end,o=m,a=h,i&&(s+=g,f>u&&(a=f-(h-p)),a-=g),y(),s+=a-p,f<=u&&(d=i?Math.abs(v-m)-c:c,v<m?(o-=d,d+=p,k()):(k(),o+=d),a=e.end-t.start+u,a<h&&(a=h),s-=a-p,y()),v<m?(d=o-v,o=v,d+=p,k()):(d=v-o,k(),o+=d),a=t.start-s,i||(a-=g),y(),r},_line:function(e,t){return a("div",{className:e,style:t})},_arrow:function(e){return a("span",{className:e?L.styles.arrowWest:L.styles.arrowEast})},_colgroup:function(){var e,t,i,n=this._timeSlots(),r=n.length,s=[];for(e=0;e<r;e++)for(t=0,i=n[e].span;t<i;t++)s.push(a("col"));return a("colgroup",null,s)},_createDragHint:function(e){this._dragHint=e.clone().addClass(L.styles.dragHint).css("cursor","move"),e.parent().append(this._dragHint)},_updateDragHint:function(e){var t=this._offset(e);this._dragHint.css({left:t})},_removeDragHint:function(){this._dragHint.remove(),this._dragHint=null},_createResizeHint:function(t){var i,n,r=L.styles,s=this._taskCoordinates[t.id].rowIndex*this._rowHeight,o=this.options,a=o.messages;this._resizeHint=e(I({styles:r})).css({top:0,height:this._contentHeight}),this.content.append(this._resizeHint),this._resizeTooltip=e(z({styles:r,start:t.start,end:t.end,messages:a.views,format:o.resizeTooltipFormat})).css({top:0,left:0}),this.content.append(this._resizeTooltip),this._resizeTooltipWidth=this._resizeTooltip.outerWidth(),i=this._resizeTooltip.outerHeight(),n=s-i,n<0&&(n=s+this._rowHeight),this._resizeTooltipTop=n},_updateResizeHint:function(t,i,n){var r=this._offset(p?i:t),s=this._offset(p?t:i),o=s-r,a=n!==p?r:s,d=this._tableWidth-kendo.support.scrollbar(),l=this._resizeTooltipWidth,c=this.options,h=c.messages,u=e(T+L.styles.tasksTable).offset().left-e(T+L.styles.tasksWrapper).offset().left;p&&(r+=u),this._resizeHint.css({left:r,width:o}),this._resizeTooltip&&this._resizeTooltip.remove(),a-=Math.round(l/2),a<0?a=0:a+l>d&&(a=d-l),p&&(a+=u),this._resizeTooltip=e(z({styles:L.styles,start:t,end:i,messages:h.views,format:c.resizeTooltipFormat})).css({top:this._resizeTooltipTop,left:a,"min-width":l}).appendTo(this.content)},_removeResizeHint:function(){this._resizeHint.remove(),this._resizeHint=null,this._resizeTooltip.remove(),this._resizeTooltip=null},_updatePercentCompleteTooltip:function(t,i,n){var r,s,o,a;this._removePercentCompleteTooltip(),r=this._percentCompleteResizeTooltip=e(R({styles:L.styles,text:n})).appendTo(this.element),s=Math.round(r.outerWidth()/2),o=r.find(T+L.styles.callout),a=Math.round(o.outerWidth()/2),r.css({top:t-(r.outerHeight()+a),left:i-s}),o.css("left",s-a)},_removePercentCompleteTooltip:function(){this._percentCompleteResizeTooltip&&this._percentCompleteResizeTooltip.remove(),this._percentCompleteResizeTooltip=null},_updateDependencyDragHint:function(e,t,i){this._removeDependencyDragHint(),i?this._creteVmlDependencyDragHint(e,t):this._creteDependencyDragHint(e,t)},_creteDependencyDragHint:function(t,i){var n=L.styles,r=i.x-t.x,s=i.y-t.y,o=Math.sqrt(r*r+s*s),a=Math.atan(s/r);r<0&&(a+=Math.PI),e("<div class='"+n.line+" "+n.lineHorizontal+" "+n.dependencyHint+"'></div>").css({top:t.y,left:t.x,width:o,"transform-origin":"0% 0","-ms-transform-origin":"0% 0","-webkit-transform-origin":"0% 0",transform:"rotate("+a+"rad)","-ms-transform":"rotate("+a+"rad)","-webkit-transform":"rotate("+a+"rad)"}).appendTo(this.content)},_creteVmlDependencyDragHint:function(t,i){var n=e("<kvml:line class='"+L.styles.dependencyHint+"' style='position:absolute; top: 0px; left: 0px;' strokecolor='black' strokeweight='2px' from='"+t.x+"px,"+t.y+"px' to='"+i.x+"px,"+i.y+"px'></kvml:line>").appendTo(this.content);n[0].outerHTML=n[0].outerHTML},_removeDependencyDragHint:function(){this.content.find(T+L.styles.dependencyHint).remove()},_createTaskTooltip:function(t,i,n){var r,s=L.styles,o=this.options,a=this.content,d=a.offset(),l=a.width(),c=kendo.scrollLeft(a),h=e(i).parents("tr").first(),u=h.offset(),f=o.tooltip&&o.tooltip.template?kendo.template(o.tooltip.template):A,g=p?n-(d.left+c+kendo.support.scrollbar()):n-(d.left-c),m=u.top+h.outerHeight()-d.top+a.scrollTop(),v=this._taskTooltip=e('<div style="z-index: 100002;" class="'+s.tooltipWrapper+'" ><div class="'+s.taskContent+'"></div></div>');v.css({left:g,top:m}).appendTo(a).find(T+s.taskContent).append(f({styles:s,task:t,messages:o.messages.views})),v.outerHeight()<u.top-d.top&&v.css("top",u.top-d.top-v.outerHeight()+a.scrollTop()),r=v.outerWidth(),r+g-c>l&&(g-=r,g<c&&(g=c+l-(r+17)),v.css("left",g))},_removeTaskTooltip:function(){this._taskTooltip&&this._taskTooltip.remove(),this._taskTooltip=null},_scrollTo:function(e){var t=e.offset().left,i=e.width(),n=t+i,r=e.closest("tr"),s=r.offset().top,o=r.height(),a=s+o,d=this.content,l=d.offset(),c=l.top,h=d.height(),u=c+h,f=l.left,p=d.width(),g=f+p,m=kendo.support.scrollbar();s<c?d.scrollTop(d.scrollTop()+(s-c)):a>u&&d.scrollTop(d.scrollTop()+(a+m-u)),t<f&&i>p&&n<g||n>g&&i<p?d.scrollLeft(d.scrollLeft()+(n+m-g)):(n>g&&i>p&&t>f||t<f&&i<p)&&d.scrollLeft(d.scrollLeft()+(t-f))},_timeSlots:function(){return this._slots&&this._slots.length?this._slots[this._slots.length-1]:[]},_headers:function(e){var t,i,n,r,s,o,l,c,h=[],u=L.styles;for(s=0,o=e.length;s<o;s++){for(t=e[s],i=[],l=0,c=t.length;l<c;l++)n=t[l],r=d(n.text),i.push(a("th",{colspan:n.span,className:u.header+(n.isNonWorking?" "+u.nonWorking:"")},[r]));h.push(a("tr",null,i))}return h},_hours:function(e,t){var i,n,r,s=[],o=this.options,a=o.workDayStart.getHours(),d=o.workDayEnd.getHours(),l=o.hourSpan;for(e=new Date(e),t=new Date(t);e<t;)i=new Date(e),r=i.getHours(),n=r>=a&&r<d,i.setHours(i.getHours()+l),r==i.getHours()&&i.setHours(i.getHours()+2*l),o.showWorkHours&&!n||s.push({start:e,end:i,isNonWorking:!n,span:1}),e=i;return s},_days:function(e,t){var i,n,r=[];for(e=new Date(e),t=new Date(t);e<t;)i=kendo.date.nextDay(e),n=this._isWorkDay(e),this.options.showWorkDays&&!n||r.push({start:e,end:i,isNonWorking:!n,span:1}),e=i;return r},_weeks:function(e,t){var i,n,r,s=[],o=this.calendarInfo().firstDay;for(e=new Date(e),t=new Date(t);e<t;)i=kendo.date.dayOfWeek(kendo.date.addDays(e,1),o,1),i>t&&(i=t),n=this._days(e,i),r=n.length,r>0&&s.push({start:n[0].start,end:n[r-1].end,span:r}),e=i;return s},_months:function(e,t){var i,n,r,s=[];for(e=new Date(e),t=new Date(t);e<t;)i=new Date(e),i.setMonth(i.getMonth()+1),n=this._days(e,i),r=n.length,r>0&&s.push({start:n[0].start,end:n[r-1].end,span:r}),e=i;return s},_years:function(e,t){var i,n=[];for(e=new Date(e),t=new Date(t);e<t;)i=new Date(e),i.setFullYear(i.getFullYear()+1),n.push({start:e,end:i,span:12}),e=i;return n},_slotHeaders:function(e,t){var i,n,r,s=[];for(n=0,r=e.length;n<r;n++)i=e[n],s.push({text:t(i),isNonWorking:!!i.isNonWorking,span:i.span});return s},_isWorkDay:function(e){var t,i,n=e.getDay(),r=this._workDays;for(t=0,i=r.length;t<i;t++)if(r[t]===n)return!0;return!1},calendarInfo:function(){return kendo.getCulture().calendars.standard},_renderCurrentTime:function(){var t,i=this._getCurrentTime(),n=this._offset(i),r=e("<div class='k-current-time'></div>"),s=L.styles,o=e(T+s.tasksWrapper),a=e(T+s.tasksTable);this.content&&this._timeSlots().length&&(this.content.find(".k-current-time").remove(),t=this._timeSlots()[this._slotIndex("start",i)],i<t.start||i>t.end||(o.length&&a.length&&(n+=a.offset().left-o.offset().left),r.css({left:n+"px",top:"0px",width:"1px",height:this._contentHeight+"px"}).appendTo(this.content)))},_getCurrentTime:function(){return new Date},_currentTime:function(){var e=this.options.currentTimeMarker;e!==!1&&void 0!==e.updateInterval&&(this._renderCurrentTime(),this._currentTimeUpdateTimer=setInterval(u(this._renderCurrentTime,this),e.updateInterval))}});h(!0,L,{styles:N}),kendo.ui.GanttDayView=L.extend({name:"day",options:{timeHeaderTemplate:D,dayHeaderTemplate:C,resizeTooltipFormat:"h:mm tt ddd, MMM d"},range:function(e){this.start=kendo.date.getDate(e.start),this.end=kendo.date.getDate(e.end),(kendo.date.getMilliseconds(e.end)>0||this.end.getTime()===this.start.getTime())&&(this.end=kendo.date.addDays(this.end,1))},_createSlots:function(){var e,t,i,n,r=[],s=this._days(this.start,this.end),o=[];for(i=0,n=s.length;i<n;i++)e=s[i],t=this._hours(e.start,e.end),e.span=t.length,o.push.apply(o,t);return r.push(s),r.push(o),r},_layout:function(){var e=[],t=this.options;return e.push(this._slotHeaders(this._slots[0],kendo.template(t.dayHeaderTemplate))),e.push(this._slotHeaders(this._slots[1],kendo.template(t.timeHeaderTemplate))),e}}),kendo.ui.GanttWeekView=L.extend({name:"week",options:{dayHeaderTemplate:C,weekHeaderTemplate:H,resizeTooltipFormat:"h:mm tt ddd, MMM d"},range:function(e){var t=this.calendarInfo(),i=t.firstDay,n=e.end;i===n.getDay()&&n.setDate(n.getDate()+7),this.start=kendo.date.getDate(kendo.date.dayOfWeek(e.start,i,-1)),this.end=kendo.date.getDate(kendo.date.dayOfWeek(n,i,1))},_createSlots:function(){var e=[];return e.push(this._weeks(this.start,this.end)),e.push(this._days(this.start,this.end)),e},_layout:function(){var e=[],t=this.options;return e.push(this._slotHeaders(this._slots[0],kendo.template(t.weekHeaderTemplate))),e.push(this._slotHeaders(this._slots[1],kendo.template(t.dayHeaderTemplate))),e}}),kendo.ui.GanttMonthView=L.extend({name:"month",options:{weekHeaderTemplate:H,monthHeaderTemplate:F,resizeTooltipFormat:"dddd, MMM d, yyyy"},range:function(e){this.start=kendo.date.firstDayOfMonth(e.start),this.end=kendo.date.addDays(kendo.date.getDate(kendo.date.lastDayOfMonth(e.end)),1)},_createSlots:function(){var e=[];return e.push(this._months(this.start,this.end)),e.push(this._weeks(this.start,this.end)),e},_layout:function(){var e=[],t=this.options;return e.push(this._slotHeaders(this._slots[0],kendo.template(t.monthHeaderTemplate))),e.push(this._slotHeaders(this._slots[1],kendo.template(t.weekHeaderTemplate))),e}}),kendo.ui.GanttYearView=L.extend({name:"year",options:{yearHeaderTemplate:M,monthHeaderTemplate:F,resizeTooltipFormat:"dddd, MMM d, yyyy"},range:function(e){this.start=kendo.date.firstDayOfMonth(new Date(e.start.setMonth(0))),this.end=kendo.date.firstDayOfMonth(new Date(e.end.setMonth(12)))},_createSlots:function(){var t=[],i=this._months(this.start,this.end);return e(i).each(function(e,t){t.span=1}),t.push(this._years(this.start,this.end)),t.push(i),t},_layout:function(){var e=[],t=this.options;return e.push(this._slotHeaders(this._slots[0],kendo.template(t.yearHeaderTemplate))),e.push(this._slotHeaders(this._slots[1],kendo.template(t.monthHeaderTemplate))),e}}),r={wrapper:"k-timeline k-grid k-widget",gridHeader:"k-grid-header",gridHeaderWrap:"k-grid-header-wrap",gridContent:"k-grid-content",gridContentWrap:"k-grid-content",tasksWrapper:"k-gantt-tables",dependenciesWrapper:"k-gantt-dependencies",task:"k-task",line:"k-line",taskResizeHandle:"k-resize-handle",taskResizeHandleWest:"k-resize-w",taskDragHandle:"k-task-draghandle",taskComplete:"k-task-complete",taskDelete:"k-task-delete",taskWrapActive:"k-task-wrap-active",taskWrap:"k-task-wrap",taskDot:"k-task-dot",taskDotStart:"k-task-start",taskDotEnd:"k-task-end",hovered:"k-state-hover",selected:"k-state-selected",origin:"k-origin"},s=kendo.ui.GanttTimeline=o.extend({init:function(e,t){o.fn.init.call(this,e,t),this.options.views&&this.options.views.length||(this.options.views=["day","week","month"]),p=kendo.support.isRtl(e),this._wrapper(),this._domTrees(),this._views(),this._selectable(),this._draggable(),this._resizable(),this._percentResizeDraggable(),this._createDependencyDraggable(),this._attachEvents(),this._tooltip()},options:{name:"GanttTimeline",messages:{views:{day:"Day",week:"Week",month:"Month",year:"Year",start:"Start",end:"End"}},snap:!0,selectable:!0,editable:!0},destroy:function(){o.fn.destroy.call(this),clearTimeout(this._tooltipTimeout),this._currentTimeUpdateTimer&&clearInterval(this._currentTimeUpdateTimer),this._unbindView(this._selectedView),this._moveDraggable&&this._moveDraggable.destroy(),this._resizeDraggable&&this._resizeDraggable.destroy(),this._percentDraggable&&this._percentDraggable.destroy(),this._dependencyDraggable&&this._dependencyDraggable.destroy(),this.touch&&this.touch.destroy(),this._headerTree=null,this._taskTree=null,this._dependencyTree=null,this.wrapper.off(_),kendo.destroy(this.wrapper)},_wrapper:function(){var t=s.styles,i=this,n=this.options,r=function(){var r,s,o=typeof n.rowHeight===v?n.rowHeight:n.rowHeight+"px",a=e(kendo.format(E,o)),d=i.wrapper.find(T+t.tasksWrapper);return d.append(a),r=a.find("tr").outerHeight(),s=a.find("td").height(),a.remove(),{row:r,cell:s}};this.wrapper=this.element.addClass(t.wrapper).append("<div class='"+t.gridHeader+"'><div class='"+t.gridHeaderWrap+"'></div></div>").append("<div class='"+t.gridContentWrap+"'><div class='"+t.tasksWrapper+"'></div><div class='"+t.dependenciesWrapper+"'></div></div>"),n.rowHeight&&(this._calculatedSize=r())},_domTrees:function(){var e=s.styles,t=kendo.dom.Tree,i=this.wrapper;this._headerTree=new t(i.find(T+e.gridHeaderWrap)[0]),this._taskTree=new t(i.find(T+e.tasksWrapper)[0]),this._dependencyTree=new t(i.find(T+e.dependenciesWrapper)[0])},_views:function(){var e,t,i,n,r,s,o,a=this.options.views;for(this.views={},s=0,o=a.length;s<o;s++)e=a[s],t=c(e),t&&e.selectable===!1||(i=t?"string"!=typeof e.type?e.title:e.type:e,n=W[i],n&&(t&&(e.type=n.type),n.title=this.options.messages.views[i]),e=h({title:i},n,t?e:{}),i&&(this.views[i]=e,r&&!e.selected||(r=i)));r&&(this._selectedViewName=r)},view:function(e){return e&&(this._selectView(e),this.trigger("navigate",{view:e,action:"changeView"})),this._selectedView},_selectView:function(e){e&&this.views[e]&&(this._selectedView&&this._unbindView(this._selectedView),this._selectedView=this._initializeView(e),this._selectedViewName=e)},_viewByIndex:function(e){var t,i=this.views;for(t in i){if(!e)return t;e--}},_initializeView:function(e){var i,n=this.views[e];if(n){if(i=n.type,"string"==typeof i&&(i=kendo.getter(n.type)(window)),!i)throw Error("There is no such view");n=new i(this.wrapper,t(h(!0,{headerTree:this._headerTree,taskTree:this._taskTree,dependencyTree:this._dependencyTree,calculatedSize:this._calculatedSize},n,this.options)))}return n},_unbindView:function(e){e&&e.destroy()},_range:function(e){var t,i,n={field:"start",dir:"asc"},r={field:"end",dir:"desc"};return e&&e.length?(t=new m(e).sort(n).toArray()[0].start||new Date,i=new m(e).sort(r).toArray()[0].end||new Date,{start:new Date(t),end:new Date(i)}):{start:new Date,end:new Date}},_render:function(e){var t=this.view(),i=this._range(e);this._tasks=e,t.range(i),t.renderLayout(),t.render(e)},_renderDependencies:function(e){this.view()._renderDependencies(e)},_taskByUid:function(e){var t,i,n=this._tasks,r=n.length;for(i=0;i<r;i++)if(t=n[i],t.uid===e)return t},_draggable:function(){var e,t,i,r,o=this,a=this.options.snap,d=s.styles,l=function(){o.view()._removeDragHint(),e&&e.css("opacity",1),e=null,t=null,o.dragInProgress=!1};this.options.editable&&(this._moveDraggable=new kendo.ui.Draggable(this.wrapper,{distance:0,filter:T+d.task,holdToDrag:kendo.support.mobileOS,ignore:T+d.taskResizeHandle}),this._moveDraggable.bind("dragstart",function(n){var s=o.view();return e=n.currentTarget.parent(),t=o._taskByUid(n.currentTarget.attr("data-uid")),o.trigger("moveStart",{task:t})?void n.preventDefault():(i=t.start,r=s._timeByPosition(n.x.location,a)-i,s._createDragHint(e),e.css("opacity",.5),clearTimeout(o._tooltipTimeout),void(o.dragInProgress=!0))}).bind("drag",kendo.throttle(function(e){var n,s,d;o.dragInProgress&&(n=o.view(),s=new Date(n._timeByPosition(e.x.location,a)-r),d=s,o.trigger("move",{task:t,start:s})||(i=s,p&&(d=new Date(i.getTime()+t.duration())),n._updateDragHint(d)))},15)).bind("dragend",function(){o.trigger("moveEnd",{task:t,start:i}),l()}).bind("dragcancel",function(){l()}).userEvents.bind("select",function(){n()}))},_resizable:function(){var e,t,i,r,o,a=this,d=this.options.snap,l=s.styles,c=function(){a.view()._removeResizeHint(),e=null,t=null,a.dragInProgress=!1};this.options.editable&&(this._resizeDraggable=new kendo.ui.Draggable(this.wrapper,{distance:0,filter:T+l.taskResizeHandle,holdToDrag:!1}),this._resizeDraggable.bind("dragstart",function(n){return o=n.currentTarget.hasClass(l.taskResizeHandleWest),p&&(o=!o),e=n.currentTarget.closest(T+l.task),t=a._taskByUid(e.attr("data-uid")),a.trigger("resizeStart",{task:t})?void n.preventDefault():(i=t.start,r=t.end,a.view()._createResizeHint(t),clearTimeout(a._tooltipTimeout),void(a.dragInProgress=!0))}).bind("drag",kendo.throttle(function(e){var n,s;a.dragInProgress&&(n=a.view(),s=n._timeByPosition(e.x.location,d,!o),o?i=s<r?s:r:r=s>i?s:i,a.trigger("resize",{task:t,start:i,end:r})||n._updateResizeHint(i,r,o))},15)).bind("dragend",function(){a.trigger("resizeEnd",{task:t,resizeStart:o,start:i,end:r}),c()}).bind("dragcancel",function(){c()}).userEvents.bind("select",function(){n()}))},_percentResizeDraggable:function(){var e,t,i,r,o,a,d,l,c,h,u=this,f=s.styles,g=function(){u.view()._removePercentCompleteTooltip(),t=null,e=null,u.dragInProgress=!1},m=function(e){t.find(T+f.taskComplete).width(e).end().siblings(T+f.taskDragHandle).css(p?"right":"left",e)};this.options.editable&&(this._percentDraggable=new kendo.ui.Draggable(this.wrapper,{distance:0,filter:T+f.taskDragHandle,holdToDrag:!1}),this._percentDraggable.bind("dragstart",function(n){return u.trigger("percentResizeStart")?void n.preventDefault():(t=n.currentTarget.siblings(T+f.task),e=u._taskByUid(t.attr("data-uid")),d=e.percentComplete,i=t.offset(),r=this.element.offset(),o=t.find(T+f.taskComplete).width(),a=t.outerWidth(),clearTimeout(u._tooltipTimeout),void(u.dragInProgress=!0))}).bind("drag",kendo.throttle(function(e){if(u.dragInProgress){h=p?-e.x.initialDelta:e.x.initialDelta;var t=Math.max(0,Math.min(a,o+h));d=Math.round(t/a*100),m(t),l=i.top-r.top,c=i.left+t-r.left,p&&(c+=a-2*t),u.view()._updatePercentCompleteTooltip(l,c,d)}},15)).bind("dragend",function(){u.trigger("percentResizeEnd",{task:e,percentComplete:d/100}),g()}).bind("dragcancel",function(){m(o),g()}).userEvents.bind("select",function(){n()}))},_createDependencyDraggable:function(){var t,i,r,o=this,a=e(),d=e(),l=f.msie&&f.version<9,c=s.styles,h=function(){t.css("display","").removeClass(c.hovered),t.parent().removeClass(c.origin),t=null,u(!1),d=e(),a=e(),o.view()._removeDependencyDragHint(),o.dragInProgress=!1},u=function(e){d.hasClass(c.origin)||(d.find(T+c.taskDot).css("display",e?"block":""),a.toggleClass(c.hovered,e))};this.options.editable&&(l&&document.namespaces&&document.namespaces.add("kvml","urn:schemas-microsoft-com:vml","#default#VML"),
this._dependencyDraggable=new kendo.ui.Draggable(this.wrapper,{distance:0,filter:T+c.taskDot,holdToDrag:!1}),this._dependencyDraggable.bind("dragstart",function(e){var n,s;return o.trigger("dependencyDragStart")?void e.preventDefault():(t=e.currentTarget.css("display","block").addClass(c.hovered),t.parent().addClass(c.origin),n=t.offset(),s=o.wrapper.find(T+c.tasksWrapper).offset(),i=Math.round(n.left-s.left+t.outerHeight()/2),r=Math.round(n.top-s.top+t.outerWidth()/2),clearTimeout(o._tooltipTimeout),void(o.dragInProgress=!0))}).bind("drag",kendo.throttle(function(t){var n,s,h,f;o.dragInProgress&&(o.view()._removeDependencyDragHint(),n=e(kendo.elementUnderCursor(t)),s=o.wrapper.find(T+c.tasksWrapper).offset(),h=t.x.location-s.left,f=t.y.location-s.top,o.view()._updateDependencyDragHint({x:i,y:r},{x:h,y:f},l),u(!1),a=n.hasClass(c.taskDot)?n:e(),d=n.closest(T+c.taskWrap),u(!0))},15)).bind("dragend",function(){var e,i,n,r,s;a.length&&(e=t.hasClass(c.taskDotStart),i=a.hasClass(c.taskDotStart),n=e?i?3:2:i?1:0,r=o._taskByUid(t.siblings(T+c.task).attr("data-uid")),s=o._taskByUid(a.siblings(T+c.task).attr("data-uid")),r!==s&&o.trigger("dependencyDragEnd",{type:n,predecessor:r,successor:s})),h()}).bind("dragcancel",function(){h()}).userEvents.bind("select",function(){n()}))},_selectable:function(){var t=this,i=s.styles;this.options.selectable&&this.wrapper.on(y+_,T+i.task,function(i){i.stopPropagation(),i.ctrlKey?t.trigger("clear"):t.trigger("select",{uid:e(this).attr("data-uid")})}).on(y+_,T+i.taskWrap,function(t){t.stopPropagation(),e(this).css("z-index","0");var n=e(document.elementFromPoint(t.clientX,t.clientY));n.hasClass(i.line)&&n.click(),e(this).css("z-index","")}).on(y+_,T+i.tasksWrapper,function(){t.selectDependency().length>0?t.clearSelection():t.trigger("clear")}).on(y+_,T+i.line,function(e){e.stopPropagation(),t.selectDependency(this)})},select:function(e){var t=this.wrapper.find(e),i=s.styles;return t.length?(this.clearSelection(),t.addClass(i.selected),void(kendo.support.mobileOS&&t.parent().addClass(i.taskWrapActive))):this.wrapper.find(T+i.task+T+i.selected)},selectDependency:function(t){var i,n=this.wrapper.find(t),r=s.styles;return n.length?(this.clearSelection(),this.trigger("clear"),i=e(n).attr("data-uid"),void this.wrapper.find(T+r.line+"[data-uid='"+i+"']").addClass(r.selected)):this.wrapper.find(T+r.line+T+r.selected)},clearSelection:function(){var e=s.styles;this.wrapper.find(T+e.selected).removeClass(e.selected),kendo.support.mobileOS&&this.wrapper.find(T+e.taskWrapActive).removeClass(e.taskWrapActive)},_attachEvents:function(){var t=this,i=s.styles;this.options.editable&&(this._tabindex(),this.wrapper.on(y+_,T+i.taskDelete,function(n){t.trigger("removeTask",{uid:e(this).closest(T+i.task).attr("data-uid")}),n.stopPropagation(),n.preventDefault()}).on(x+_,function(e){var i;e.keyCode===g.DELETE&&(i=t.selectDependency(),i.length&&(t.trigger("removeDependency",{uid:i.attr("data-uid")}),t.clearSelection()))}),kendo.support.mobileOS?this.touch=this.wrapper.kendoTouch({filter:T+i.task,doubletap:function(i){t.trigger("editTask",{uid:e(i.touch.currentTarget).attr("data-uid")})}}).data("kendoTouch"):this.wrapper.on(k+_,T+i.task,function(i){t.trigger("editTask",{uid:e(this).attr("data-uid")}),i.stopPropagation(),i.preventDefault()}))},_tooltip:function(){var t,i=this,n=this.options.tooltip,r=s.styles,o=function(e){t=e.clientX};n&&n.visible===!1||(kendo.support.mobileOS?(this.wrapper.on(y+_,T+r.taskDelete,function(e){e.stopPropagation(),i.view()._removeTaskTooltip()}).on(S+_,T+r.task,function(t){var n=e(t.relatedTarget).parents(T+r.taskWrap,T+r.task);0===n.length&&i.view()._removeTaskTooltip()}),this.touch&&this.touch.bind("tap",function(t){var n=t.touch.target,r=i._taskByUid(e(n).attr("data-uid")),s=t.touch.x.client;i.view()._taskTooltip&&i.view()._removeTaskTooltip(),i.view()._createTaskTooltip(r,n,s)}).bind("doubletap",function(){i.view()._removeTaskTooltip()})):this.wrapper.on(b+_,T+r.task,function(){var n=this,r=i._taskByUid(e(this).attr("data-uid"));i.dragInProgress||(i._tooltipTimeout=setTimeout(function(){i.view()._createTaskTooltip(r,n,t)},800),e(this).on(w,o))}).on(S+_,T+r.task,function(){clearTimeout(i._tooltipTimeout),i.view()._removeTaskTooltip(),e(this).off(w,o)}))}}),h(!0,s,{styles:r})}(window.kendo.jQuery),window.kendo},"function"==typeof define&&define.amd?define:function(e,t,i){(i||t)()}),/** 
 * Kendo UI v2016.2.714 (http://www.telerik.com/kendo-ui)                                                                                                                                               
 * Copyright 2016 Telerik AD. All rights reserved.                                                                                                                                                      
 *                                                                                                                                                                                                      
 * Kendo UI commercial licenses may be obtained at                                                                                                                                                      
 * http://www.telerik.com/purchase/license-agreement/kendo-ui-complete                                                                                                                                  
 * If you do not own a commercial license, this file shall be governed by the trial license terms.                                                                                                      
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       
                                                                                                                                                                                                       

*/
function(e,define){define("util/main.min",["kendo.core.min"],e)}(function(){return function(){function e(e){return typeof e!==L}function t(e,t){var n=i(t);return z.round(e*n)/n}function i(e){return e?z.pow(10,e):1}function n(e,t,i){return z.max(z.min(e,i),t)}function r(e){return e*E}function s(e){return e/E}function o(e){return"number"==typeof e&&!isNaN(e)}function a(t,i){return e(t)?t:i}function d(e){return e*e}function l(e){var t,i=[];for(t in e)i.push(t+e[t]);return i.sort().join("")}function c(e){var t,i=2166136261;for(t=0;t<e.length;++t)i+=(i<<1)+(i<<4)+(i<<7)+(i<<8)+(i<<24),i^=e.charCodeAt(t);return i>>>0}function h(e){return c(l(e))}function u(e){var t,i=e.length,n=W,r=N;for(t=0;t<i;t++)r=z.max(r,e[t]),n=z.min(n,e[t]);return{min:n,max:r}}function f(e){return u(e).min}function p(e){return u(e).max}function g(e){return v(e).min}function m(e){return v(e).max}function v(e){var t,i,n,r=W,s=N;for(t=0,i=e.length;t<i;t++)n=e[t],null!==n&&isFinite(n)&&(r=z.min(r,n),s=z.max(s,n));return{min:r===W?void 0:r,max:s===N?void 0:s}}function _(e){if(e)return e[e.length-1]}function y(e,t){return e.push.apply(e,t),e}function k(e){return R.template(e,{useWithBlock:!1,paramName:"d"})}function w(t,i){return e(i)&&null!==i?" "+t+"='"+i+"' ":""}function b(e){var t,i="";for(t=0;t<e.length;t++)i+=w(e[t][0],e[t][1]);return i}function S(t){var i,n,r="";for(i=0;i<t.length;i++)n=t[i][1],e(n)&&(r+=t[i][0]+":"+n+";");if(""!==r)return r}function x(e){return"string"!=typeof e&&(e+="px"),e}function T(e){var t,i,n=[];if(e)for(t=R.toHyphens(e).split("-"),i=0;i<t.length;i++)n.push("k-pos-"+t[i]);return n.join(" ")}function D(t){return""===t||null===t||"none"===t||"transparent"===t||!e(t)}function C(e){for(var t={1:"i",10:"x",100:"c",2:"ii",20:"xx",200:"cc",3:"iii",30:"xxx",300:"ccc",4:"iv",40:"xl",400:"cd",5:"v",50:"l",500:"d",6:"vi",60:"lx",600:"dc",7:"vii",70:"lxx",700:"dcc",8:"viii",80:"lxxx",800:"dccc",9:"ix",90:"xc",900:"cm",1e3:"m"},i=[1e3,900,800,700,600,500,400,300,200,100,90,80,70,60,50,40,30,20,10,9,8,7,6,5,4,3,2,1],n="";e>0;)e<i[0]?i.shift():(n+=t[i[0]],e-=i[0]);return n}function H(e){var t,i,n,r,s;for(e=e.toLowerCase(),t={i:1,v:5,x:10,l:50,c:100,d:500,m:1e3},i=0,n=0,r=0;r<e.length;++r){if(s=t[e.charAt(r)],!s)return null;i+=s,s>n&&(i-=2*n),n=s}return i}function F(e){var t=Object.create(null);return function(){var i,n="";for(i=arguments.length;--i>=0;)n+=":"+arguments[i];return n in t?t[n]:e.apply(this,arguments)}}function M(e){for(var t,i,n=[],r=0,s=e.length;r<s;)t=e.charCodeAt(r++),t>=55296&&t<=56319&&r<s?(i=e.charCodeAt(r++),56320==(64512&i)?n.push(((1023&t)<<10)+(1023&i)+65536):(n.push(t),r--)):n.push(t);return n}function I(e){return e.map(function(e){var t="";return e>65535&&(e-=65536,t+=String.fromCharCode(e>>>10&1023|55296),e=56320|1023&e),t+=String.fromCharCode(e)}).join("")}var z=Math,R=window.kendo,A=R.deepExtend,E=z.PI/180,W=Number.MAX_VALUE,N=-Number.MAX_VALUE,L="undefined",P=Date.now;P||(P=function(){return(new Date).getTime()}),A(R,{util:{MAX_NUM:W,MIN_NUM:N,append:y,arrayLimits:u,arrayMin:f,arrayMax:p,defined:e,deg:s,hashKey:c,hashObject:h,isNumber:o,isTransparent:D,last:_,limitValue:n,now:P,objectKey:l,round:t,rad:r,renderAttr:w,renderAllAttr:b,renderPos:T,renderSize:x,renderStyle:S,renderTemplate:k,sparseArrayLimits:v,sparseArrayMin:g,sparseArrayMax:m,sqr:d,valueOrDefault:a,romanToArabic:H,arabicToRoman:C,memoize:F,ucs2encode:I,ucs2decode:M}}),R.drawing.util=R.util,R.dataviz.util=R.util}(),window.kendo},"function"==typeof define&&define.amd?define:function(e,t,i){(i||t)()}),function(e,define){define("util/text-metrics.min",["kendo.core.min","util/main.min"],e)}(function(){!function(e){function t(){return{width:0,height:0,baseline:0}}function i(e,t,i){return h.current.measure(e,t,i)}function n(e,t){var i=[];if(e.length>0&&document.fonts){try{i=e.map(function(e){return document.fonts.load(e)})}catch(n){s.logToConsole(n)}Promise.all(i).then(t,t)}else t()}var r=document,s=window.kendo,o=s.Class,a=s.util,d=a.defined,l=o.extend({init:function(e){this._size=e,this._length=0,this._map={}},put:function(e,t){var i=this,n=i._map,r={key:e,value:t};n[e]=r,i._head?(i._tail.newer=r,r.older=i._tail,i._tail=r):i._head=i._tail=r,i._length>=i._size?(n[i._head.key]=null,i._head=i._head.newer,i._head.older=null):i._length++},get:function(e){var t=this,i=t._map[e];if(i)return i===t._head&&i!==t._tail&&(t._head=i.newer,t._head.older=null),i!==t._tail&&(i.older&&(i.older.newer=i.newer,i.newer.older=i.older),i.older=t._tail,i.newer=null,t._tail.newer=i,t._tail=i),i.value}}),c=e("<div style='position: absolute !important; top: -4000px !important; width: auto !important; height: auto !important;padding: 0 !important; margin: 0 !important; border: 0 !important;line-height: normal !important; visibility: hidden !important; white-space: nowrap!important;' />")[0],h=o.extend({init:function(e){this._cache=new l(1e3),this._initOptions(e)},options:{baselineMarkerSize:1},measure:function(i,n,s){var o,l,h,u,f,p,g,m;if(!i)return t();if(o=a.objectKey(n),l=a.hashKey(i+o),h=this._cache.get(l),h)return h;u=t(),f=s?s:c,p=this._baselineMarker().cloneNode(!1);for(g in n)m=n[g],d(m)&&(f.style[g]=m);return e(f).text(i),f.appendChild(p),r.body.appendChild(f),(i+"").length&&(u.width=f.offsetWidth-this.options.baselineMarkerSize,u.height=f.offsetHeight,u.baseline=p.offsetTop+this.options.baselineMarkerSize),u.width>0&&u.height>0&&this._cache.put(l,u),f.parentNode.removeChild(f),u},_baselineMarker:function(){return e("<div class='k-baseline-marker' style='display: inline-block; vertical-align: baseline;width: "+this.options.baselineMarkerSize+"px; height: "+this.options.baselineMarkerSize+"px;overflow: hidden;' />")[0]}});h.current=new h,s.util.TextMetrics=h,s.util.LRUCache=l,s.util.loadFonts=n,s.util.measureText=i}(window.kendo.jQuery)},"function"==typeof define&&define.amd?define:function(e,t,i){(i||t)()}),function(e,define){define("util/base64.min",["util/main.min"],e)}(function(){return function(){function e(e){var i,n,r,o,a,d,l,c="",h=0;for(e=t(e);h<e.length;)i=e.charCodeAt(h++),n=e.charCodeAt(h++),r=e.charCodeAt(h++),o=i>>2,a=(3&i)<<4|n>>4,d=(15&n)<<2|r>>6,l=63&r,isNaN(n)?d=l=64:isNaN(r)&&(l=64),c=c+s.charAt(o)+s.charAt(a)+s.charAt(d)+s.charAt(l);return c}function t(e){var t,i,n="";for(t=0;t<e.length;t++)i=e.charCodeAt(t),i<128?n+=r(i):i<2048?(n+=r(192|i>>>6),n+=r(128|63&i)):i<65536&&(n+=r(224|i>>>12),n+=r(128|i>>>6&63),n+=r(128|63&i));return n}var i=window.kendo,n=i.deepExtend,r=String.fromCharCode,s="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";n(i.util,{encodeBase64:e,encodeUTF8:t})}(),window.kendo},"function"==typeof define&&define.amd?define:function(e,t,i){(i||t)()}),function(e,define){define("mixins/observers.min",["kendo.core.min"],e)}(function(){return function(e){var t=Math,i=window.kendo,n=i.deepExtend,r=e.inArray,s={observers:function(){return this._observers=this._observers||[]},addObserver:function(e){return this._observers?this._observers.push(e):this._observers=[e],this},removeObserver:function(e){var t=this.observers(),i=r(e,t);return i!=-1&&t.splice(i,1),this},trigger:function(e,t){var i,n,r=this._observers;if(r&&!this._suspended)for(n=0;n<r.length;n++)i=r[n],i[e]&&i[e](t);return this},optionsChange:function(e){e=e||{},e.element=this,this.trigger("optionsChange",e)},geometryChange:function(){this.trigger("geometryChange",{element:this})},suspend:function(){return this._suspended=(this._suspended||0)+1,this},resume:function(){return this._suspended=t.max((this._suspended||0)-1,0),this},_observerField:function(e,t){this[e]&&this[e].removeObserver(this),this[e]=t,t.addObserver(this)}};n(i,{mixins:{ObserversMixin:s}})}(window.kendo.jQuery),window.kendo},"function"==typeof define&&define.amd?define:function(e,t,i){(i||t)()}),function(e,define){define("kendo.gantt.min",["kendo.data.min","kendo.popup.min","kendo.window.min","kendo.resizable.min","kendo.gantt.list.min","kendo.gantt.timeline.min","kendo.grid.min","kendo.pdf.min"],e)}(function(){return function(e,t){function i(e){return"["+g.attr("uid")+(e?"='"+e+"']":"]")}function n(e){return delete e.name,delete e.prefix,delete e.remove,delete e.edit,delete e.add,delete e.navigate,e}function r(e){var t,i,n,r,s,o;if(e.filter("[name=end], [name=start]").length){for(t=e.attr("name"),i=g.widgetInstance(e,g.ui),n={},r=e;r!==window&&!s;)r=r.parent(),s=r.data("kendoEditable");return!(o=s?s.options.model:null)||(n.start=o.start,n.end=o.end,n[t]=i?i.value():g.parseDate(e.value()),n.start<=n.end)}return!0}function s(t,i){var n=t.parents("["+g.attr("role")+'="gantt"]'),r=[],s=o(n);t.attr(W,0),i&&s.each(function(t,i){r[t]=e(i).scrollTop()});try{t[0].setActive()}catch(a){t[0].focus()}i&&s.each(function(t,i){e(i).scrollTop(r[t])})}function o(t){return e(t).parentsUntil("body").filter(function(e,t){var i=g.getComputedStyles(t,["overflow"]);return"visible"!=i.overflow}).add(window)}var a,d,l,c,h,u,f,p,g=window.kendo,m="matchMedia"in window,v=g.support.browser,_=g.support.mobileOS,y=g.Observable,k=g.ui.Widget,w=g.data.DataSource,b=g.data.ObservableObject,S=g.data.ObservableArray,x=g.data.Query,T=e.isArray,D=e.inArray,C=g.isFunction,H=e.proxy,F=e.extend,M=e.isPlainObject,I=e.map,z=g.keys,R=3,A=".kendoGantt",E="p0",W="tabIndex",N="click",L="width",P="string",B={down:{origin:"bottom left",position:"top left"},up:{origin:"top left",position:"bottom left"}},O="aria-activedescendant",G="gantt_active_cell",U="action-option-focused",j=".",q="Are you sure you want to delete this task?",V="Are you sure you want to delete this dependency?",X=g.template('<button class="#=styles.buttonToggle#"><span class="#=styles.iconToggle#">&nbps;</span></button>'),K='<button class="#=styles.button# #=className#" #if (action) {#data-action="#=action#"#}#><span class="#=iconClass#"></span><span>#=text#</span></button>',Q='<a class="#=className#" #=attr# href="\\#">#=text#</a>',Y=g.template('<li class="#=styles.currentView# #=styles.viewButtonDefault#"><a href="\\#" class="#=styles.link#">&nbps;</a></li>'),Z=g.template('<ul class="#=styles.viewsWrapper#">#for(var view in views){#<li class="#=styles.viewButtonDefault# #=styles.viewButton#-#= view.toLowerCase() #" data-#=ns#name="#=view#"><a href="\\#" class="#=styles.link#">#=views[view].title#</a></li>#}#</ul>'),J=g.template('<div class="#=styles.popupWrapper#"><ul class="#=styles.popupList#" role="listbox">#for(var i = 0, l = actions.length; i < l; i++){#<li class="#=styles.item#" data-action="#=actions[i].data#" role="option">#=actions[i].text#</span>#}#</ul></div>'),$=function(t,i){var n={name:i.field},r=i.model.fields[i.field].validation;r&&M(r)&&r.message&&(n[g.attr("dateCompare-msg")]=r.message),e('<input type="text" required '+g.attr("type")+'="date" '+g.attr("role")+'="datetimepicker" '+g.attr("bind")+'="value:'+i.field+'" '+g.attr("validate")+"='true' />").attr(n).appendTo(t),e("<span "+g.attr("for")+'="'+i.field+'" class="k-invalid-msg"/>').hide().appendTo(t)},ee=function(t,i){e('<a href="#" class="'+i.styles.button+'">'+i.messages.assignButton+"</a>").click(i.click).appendTo(t)},te={wrapper:"k-widget k-gantt",rowHeight:"k-gantt-rowheight",listWrapper:"k-gantt-layout k-gantt-treelist",list:"k-gantt-treelist",timelineWrapper:"k-gantt-layout k-gantt-timeline",timeline:"k-gantt-timeline",splitBarWrapper:"k-splitbar k-state-default k-splitbar-horizontal k-splitbar-draggable-horizontal k-gantt-layout",splitBar:"k-splitbar",splitBarHover:"k-splitbar-horizontal-hover",popupWrapper:"k-list-container",popupList:"k-list k-reset",resizeHandle:"k-resize-handle",icon:"k-icon",item:"k-item",line:"k-line",buttonDelete:"k-gantt-delete",buttonCancel:"k-gantt-cancel",buttonSave:"k-gantt-update",buttonToggle:"k-gantt-toggle",primary:"k-primary",hovered:"k-state-hover",selected:"k-state-selected",focused:"k-state-focused",gridHeader:"k-grid-header",gridHeaderWrap:"k-grid-header-wrap",gridContent:"k-grid-content",popup:{form:"k-popup-edit-form",editForm:"k-gantt-edit-form",formContainer:"k-edit-form-container",resourcesFormContainer:"k-resources-form-container",message:"k-popup-message",buttonsContainer:"k-edit-buttons k-state-default",button:"k-button",editField:"k-edit-field",editLabel:"k-edit-label",resourcesField:"k-gantt-resources"},toolbar:{headerWrapper:"k-floatwrap k-header k-gantt-toolbar",footerWrapper:"k-floatwrap k-header k-gantt-toolbar",toolbar:"k-gantt-toolbar",expanded:"k-state-expanded",views:"k-gantt-views",viewsWrapper:"k-reset k-header k-gantt-views",actions:"k-gantt-actions",button:"k-button k-button-icontext",buttonToggle:"k-button k-button-icon k-gantt-toggle",iconPlus:"k-icon k-i-plus",iconPdf:"k-icon k-i-pdf",iconToggle:"k-icon k-i-gantt-toggle",viewButtonDefault:"k-state-default",viewButton:"k-view",currentView:"k-current-view",link:"k-link",pdfButton:"k-gantt-pdf",appendButton:"k-gantt-create"}},ie=y.extend({init:function(e,t){y.fn.init.call(this),this.element=e,this.options=F(!0,{},this.options,t),this._popup()},options:{direction:"down",navigatable:!1},_current:function(e){var t=p.styles,i=this.list.find(j+t.focused),n=i[e]();n.length&&(i.removeClass(t.focused).removeAttr("id"),n.addClass(t.focused).attr("id",U),this.list.find("ul").removeAttr(O).attr(O,U))},_popup:function(){var t=this,i=p.styles,n="li"+j+i.item,r=j+i.toolbar.appendButton,s=this.options.messages.actions,o=this.options.navigatable;this.list=e(J({styles:i,actions:[{data:"add",text:s.addChild},{data:"insert-before",text:s.insertBefore},{data:"insert-after",text:s.insertAfter}]})),this.element.append(this.list),this.popup=new g.ui.Popup(this.list,F({anchor:this.element.find(r),open:function(){t._adjustListWidth()},animation:this.options.animation},B[this.options.direction])),this.element.on(N+A,r,function(n){var r=e(this),s=r.attr(g.attr("action"));n.preventDefault(),s?t.trigger("command",{type:s}):(t.popup.open(),o&&t.list.find("li:first").addClass(i.focused).attr("id",U).end().find("ul").attr({TABINDEX:0,"aria-activedescendant":U}).focus())}),this.list.find(n).hover(function(){e(this).addClass(i.hovered)},function(){e(this).removeClass(i.hovered)}).end().on(N+A,n,function(){t.trigger("command",{type:e(this).attr(g.attr("action"))}),t.popup.close()}),o&&(this.popup.bind("close",function(){t.list.find(n).removeClass(i.focused).end().find("ul").attr(W,0),t.element.parents("["+g.attr("role")+'="gantt"]').find(j+i.gridContent+" > table:first").focus()}),this.list.find("ul").on("keydown"+A,function(e){var n=e.keyCode;switch(n){case z.UP:e.preventDefault(),t._current("prev");break;case z.DOWN:e.preventDefault(),t._current("next");break;case z.ENTER:t.list.find(j+i.focused).click();break;case z.ESC:e.preventDefault(),t.popup.close()}}))},_adjustListWidth:function(){var e,t,i=this.list,n=p.styles,r=i[0].style.width,s=this.element.find(j+n.toolbar.appendButton),o=i.outerWidth();!i.data(L)&&r||(e=window.getComputedStyle?window.getComputedStyle(s[0],null):0,t=e?parseFloat(e.width):s.outerWidth(),e&&(v.mozilla||v.msie)&&(t+=parseFloat(e.paddingLeft)+parseFloat(e.paddingRight)+parseFloat(e.borderLeftWidth)+parseFloat(e.borderRightWidth)),r="border-box"!==i.css("box-sizing")?t-(i.outerWidth()-i.width()):t,o>r&&(r=o),i.css({fontFamily:s.css("font-family"),width:r}).data(L,r))},destroy:function(){clearTimeout(this._focusTimeout),this.popup.destroy(),this.element.off(A),this.list.off(A),this.unbind()}}),ne=function(e,t){return function(i){var n,r;if(i=T(i)?{data:i}:i,n=i||{},r=n.data,n.data=r,!(n instanceof e)&&n instanceof w)throw Error("Incorrect DataSource type. Only "+t+" instances are supported");return n instanceof e?n:new e(n)}},re=g.data.Model.define({id:"id",fields:{id:{type:"number"},predecessorId:{type:"number"},successorId:{type:"number"},type:{type:"number"}}}),se=w.extend({init:function(e){w.fn.init.call(this,F(!0,{},{schema:{modelBase:re,model:re}},e))},successors:function(e){return this._dependencies("predecessorId",e)},predecessors:function(e){return this._dependencies("successorId",e)},dependencies:function(e){var t=this.predecessors(e),i=this.successors(e);return t.push.apply(t,i),t},_dependencies:function(e,t){var i=this.view(),n={field:e,operator:"eq",value:t};return i=new x(i).filter(n).toArray()}});se.create=ne(se,"GanttDependencyDataSource"),d=g.data.Model.define({duration:function(){var e=this.end,t=this.start;return e-t},isMilestone:function(){return 0===this.duration()},_offset:function(e){var t,i,n=["start","end"];for(i=0;i<n.length;i++)t=new Date(this.get(n[i]).getTime()+e),this.set(n[i],t)},id:"id",fields:{id:{type:"number"},parentId:{type:"number",defaultValue:null,validation:{required:!0}},orderId:{type:"number",validation:{required:!0}},title:{type:"string",defaultValue:"New task"},start:{type:"date",validation:{required:!0}},end:{type:"date",validation:{required:!0,dateCompare:r,message:"End date should be after or equal to the start date"}},percentComplete:{type:"number",validation:{required:!0,min:0,max:1,step:.01}},summary:{type:"boolean"},expanded:{type:"boolean",defaultValue:!0}}}),l=w.extend({init:function(e){w.fn.init.call(this,F(!0,{},{schema:{modelBase:d,model:d}},e))},remove:function(e){var t=e.get("parentId"),i=this.taskAllChildren(e);return this._removeItems(i),e=w.fn.remove.call(this,e),this._childRemoved(t,e.get("orderId")),e},add:function(e){if(e)return e=this._toGanttTask(e),this.insert(this.taskSiblings(e).length,e)},insert:function(e,t){if(t)return t=this._toGanttTask(t),t.set("orderId",e),t=w.fn.insert.call(this,e,t),this._reorderSiblings(t,this.taskSiblings(t).length-1),this._resolveSummaryFields(this.taskParent(t)),t},taskChildren:function(e){var i,n=this.view(),r={field:"parentId",operator:"eq",value:null},s=this._sort&&this._sort.length?this._sort:{field:"orderId",dir:"asc"};if(e){if(i=e.get("id"),i===t||null===i||""===i)return[];r.value=i}return n=new x(n).filter(r).sort(s).toArray()},taskAllChildren:function(e){var t=[],i=this,n=function(e){var r=i.taskChildren(e);t.push.apply(t,r),I(r,n)};return e?n(e):t=this.view(),t},taskSiblings:function(e){if(!e)return null;var t=this.taskParent(e);return this.taskChildren(t)},taskParent:function(e){return e&&null!==e.get("parentId")?this.get(e.parentId):null},taskLevel:function(e){for(var t=0,i=this.taskParent(e);null!==i;)t+=1,i=this.taskParent(i);return t},taskTree:function(e){var t,i,n,r,s=[],o=this.taskChildren(e);for(i=0,n=o.length;i<n;i++)t=o[i],s.push(t),t.get("expanded")&&(r=this.taskTree(t),s.push.apply(s,r));return s},update:function(e,i){var n,r,s=this,o=function(e,t){var i,n,r=s.taskAllChildren(e);for(i=0,n=r.length;i<n;i++)r[i]._offset(t)},a=function(e){var t=e.field,i=e.sender;switch(t){case"start":s._resolveSummaryStart(s.taskParent(i)),o(i,i.get(t).getTime()-n.getTime());break;case"end":s._resolveSummaryEnd(s.taskParent(i));break;case"percentComplete":s._resolveSummaryPercentComplete(s.taskParent(i));break;case"orderId":s._reorderSiblings(i,n)}};i.parentId!==t&&(n=e.get("parentId"),n!==i.parentId&&(e.set("parentId",i.parentId),s._childRemoved(n,e.get("orderId")),e.set("orderId",s.taskSiblings(e).length-1),s._resolveSummaryFields(s.taskParent(e))),delete i.parentId),e.bind("change",a);for(r in i)n=e.get(r),e.set(r,i[r]);e.unbind("change",a)},_resolveSummaryFields:function(e){e&&(this._updateSummary(e),this.taskChildren(e).length&&(this._resolveSummaryStart(e),this._resolveSummaryEnd(e),this._resolveSummaryPercentComplete(e)))},_resolveSummaryStart:function(e){var t=this,i=function(e){var i,n,r,s=t.taskChildren(e),o=s[0].start.getTime();for(n=1,r=s.length;n<r;n++)i=s[n].start.getTime(),i<o&&(o=i);return new Date(o)};this._updateSummaryRecursive(e,"start",i)},_resolveSummaryEnd:function(e){var t=this,i=function(e){var i,n,r,s=t.taskChildren(e),o=s[0].end.getTime();for(n=1,r=s.length;n<r;n++)i=s[n].end.getTime(),i>o&&(o=i);return new Date(o)};this._updateSummaryRecursive(e,"end",i)},_resolveSummaryPercentComplete:function(e){var t=this,i=function(e){var i=t.taskChildren(e),n=new x(i).aggregate([{field:"percentComplete",aggregate:"average"}]);return n.percentComplete.average};this._updateSummaryRecursive(e,"percentComplete",i)},_updateSummaryRecursive:function(e,t,i){var n,r;e&&(n=i(e),e.set(t,n),r=this.taskParent(e),r&&this._updateSummaryRecursive(r,t,i))},_childRemoved:function(e,t){var i,n,r=null===e?null:this.get(e),s=this.taskChildren(r);for(i=t,n=s.length;i<n;i++)s[i].set("orderId",i);this._resolveSummaryFields(r)},_reorderSiblings:function(e,t){var i,n=e.get("orderId"),r=n>t,s=r?t:n,o=r?n:t,a=r?s:s+1,d=this.taskSiblings(e);for(o=Math.min(o,d.length-1),i=s;i<=o;i++)d[i]!==e&&(d[i].set("orderId",a),a+=1)},_updateSummary:function(e){if(null!==e){var t=this.taskChildren(e).length;e.set("summary",t>0)}},_toGanttTask:function(e){if(!(e instanceof d)){var t=e;e=this._createNewModel(),e.accept(t)}return e}}),l.create=ne(l,"GanttDataSource"),F(!0,g.data,{GanttDataSource:l,GanttTask:d,GanttDependencyDataSource:se,GanttDependency:re}),c={desktop:{dateRange:$,resources:ee}},h=g.Observable.extend({init:function(e,t){g.Observable.fn.init.call(this),this.element=e,this.options=F(!0,{},this.options,t),this.createButton=this.options.createButton},fields:function(t,i){var n,r=this,s=this.options,o=s.messages.editor,a=s.resources,d=function(e){e.preventDefault(),a.editor(r.container.find(j+p.styles.popup.resourcesField),i)};return s.editable.template?n=e.map(i.fields,function(e,t){return{field:t}}):(n=[{field:"title",title:o.title},{field:"start",title:o.start,editor:t.dateRange},{field:"end",title:o.end,editor:t.dateRange},{field:"percentComplete",title:o.percentComplete,format:E}],i.get(a.field)&&n.push({field:a.field,title:o.resources,messages:o,editor:t.resources,click:d,styles:p.styles.popup})),n},_buildEditTemplate:function(e,t,i){var n,r,s,o,a=this.options.resources,d=this.options.editable.template,l=F({},g.Template,this.options.templateSettings),c=l.paramName,h=p.styles.popup,u="";if(d)typeof d===P&&(d=window.unescape(d)),u+=g.template(d,l)(e);else for(n=0,r=t.length;n<r;n++)s=t[n],u+='<div class="'+h.editLabel+'"><label for="'+s.field+'">'+(s.title||s.field||"")+"</label></div>",s.field===a.field&&(u+='<div class="'+h.resourcesField+'" style="display:none"></div>'),!e.editable||e.editable(s.field)?(i.push(s),u+="<div "+g.attr("container-for")+'="'+s.field+'" class="'+h.editField+'"></div>'):(o="#:",s.field?(s=g.expr(s.field,c),o+=s+"==null?'':"+s):o+="''",o+="#",o=g.template(o,l),u+='<div class="'+h.editField+'">'+o(e)+"</div>");return u}}),u=h.extend({destroy:function(){this.close(),this.unbind()},editTask:function(e){this.editable=this._createPopupEditor(e)},close:function(){var e=this,t=function(){e.editable&&(e.editable.destroy(),e.editable=null,e.container=null),e.popup&&(e.popup.destroy(),e.popup=null)};this.editable&&this.container.is(":visible")?(e.trigger("close",{window:e.container}),this.container.data("kendoWindow").bind("deactivate",t).close()):t()},showDialog:function(t){var i,n,r,s,o=t.buttons,a=p.styles.popup,d=g.format('<div class="{0}"><div class="{1}"><p class="{2}">{3}</p><div class="{4}">',a.form,a.formContainer,a.message,t.text,a.buttonsContainer);for(i=0,n=o.length;i<n;i++)d+=this.createButton(o[i]);d+="</div></div></div>",r=this.element,this.popup&&this.popup.destroy(),s=this.popup=e(d).appendTo(r).eq(0).on("click",j+a.button,function(t){t.preventDefault(),s.close();var i=e(t.currentTarget).index();o[i].click()}).kendoWindow({modal:!0,resizable:!1,draggable:!1,title:t.title,visible:!1,deactivate:function(){this.destroy(),r.focus()}}).getKendoWindow(),s.center().open()},_createPopupEditor:function(t){var i,n,r=this,s={},o=this.options.messages,a=p.styles,d=a.popup,l=g.format('<div {0}="{1}" class="{2} {3}"><div class="{4}">',g.attr("uid"),t.uid,d.form,d.editForm,d.formContainer),h=this.fields(c.desktop,t),u=[];return l+=this._buildEditTemplate(t,h,u),l+='<div class="'+d.buttonsContainer+'">',l+=this.createButton({name:"update",text:o.save,className:p.styles.primary}),l+=this.createButton({name:"cancel",text:o.cancel}),l+=this.createButton({name:"delete",text:o.destroy}),l+="</div></div></div>",i=this.container=e(l).appendTo(this.element).eq(0).kendoWindow(F({modal:!0,resizable:!1,draggable:!0,title:o.editor.editorTitle,visible:!1,close:function(e){e.userTriggered&&r.trigger("cancel",{container:i,model:t})&&e.preventDefault()}},s)),n=i.kendoEditable({fields:u,model:t,clearContainer:!1,validateOnBlur:!0,target:r.options.target}).data("kendoEditable"),g.cycleForm(i),this.trigger("edit",{container:i,model:t})?r.trigger("cancel",{container:i,model:t}):(i.data("kendoWindow").center().open(),i.on(N+A,j+a.buttonCancel,function(e){e.preventDefault(),e.stopPropagation(),r.trigger("cancel",{container:i,model:t})}),i.on(N+A,j+a.buttonSave,function(e){var n,s,o,a,d;for(e.preventDefault(),e.stopPropagation(),n=r.fields(c.desktop,t),s={},a=0,d=n.length;a<d;a++)o=n[a].field,s[o]=t.get(o);r.trigger("save",{container:i,model:t,updateInfo:s})}),i.on(N+A,j+a.buttonDelete,function(e){e.preventDefault(),e.stopPropagation(),r.trigger("remove",{container:i,model:t})})),n}}),f=k.extend({init:function(e,t){k.fn.init.call(this,e,t),this.wrapper=this.element,this.model=this.options.model,this.resourcesField=this.options.resourcesField,this.createButton=this.options.createButton,this._initContainer(),this._attachHandlers()},events:["save"],open:function(){this.window.center().open(),this.grid.resize(!0)},close:function(){this.window.bind("deactivate",H(this.destroy,this)).close()},destroy:function(){this._dettachHandlers(),this.grid.destroy(),this.grid=null,this.window.destroy(),this.window=null,k.fn.destroy.call(this),g.destroy(this.wrapper),this.element=this.wrapper=null},_attachHandlers:function(){var t=p.styles,i=this.grid,n=this._cancelProxy=H(this._cancel,this);this.container.on(N+A,j+t.buttonCancel,this._cancelProxy),this._saveProxy=H(this._save,this),this.container.on(N+A,j+t.buttonSave,this._saveProxy),this.window.bind("close",function(e){e.userTriggered&&n(e)}),i.wrapper.on(N+A,"input[type='checkbox']",function(){var t=e(this),n=e(t).closest("tr"),r=i.dataSource.getByUid(n.attr(g.attr("uid"))),s=e(t).is(":checked")?1:"";r.set("value",s)})},_dettachHandlers:function(){this._cancelProxy=null,this._saveProxy=null,this.container.off(A),this.grid.wrapper.off()},_cancel:function(e){e.preventDefault(),this.close()},_save:function(e){e.preventDefault(),this._updateModel(),this.wrapper.is(j+p.styles.popup.resourcesField)||this.trigger("save",{container:this.wrapper,model:this.model}),this.close()},_initContainer:function(){var t=p.styles.popup,i=g.format('<div class="{0} {1}"><div class="{2} {3}"/></div>"',t.form,t.editForm,t.formContainer,t.resourcesFormContainer);i=e(i),this.container=i.find(j+t.resourcesFormContainer),this.window=i.kendoWindow({modal:!0,resizable:!1,draggable:!0,visible:!1,title:this.options.messages.resourcesEditorTitle}).data("kendoWindow"),this._resourceGrid(),this._createButtons()},_resourceGrid:function(){var t=this,i=this.options.messages,n=e('<div id="resources-grid"/>').appendTo(this.container);this.grid=new g.ui.Grid(n,{columns:[{field:"name",title:i.resourcesHeader,template:"<label><input type='checkbox' value='#=name#'# if (value > 0 && value !== null) {#checked='checked'# } #/>#=name#</labe>"},{field:"value",title:i.unitsHeader,template:function(e){var t=e.format,i=null!==e.value?e.value:"";return t?g.toString(i,t):i}}],height:280,sortable:!0,editable:!0,filterable:!0,dataSource:{data:t.options.data,schema:{model:{id:"id",fields:{id:{from:"id"},name:{from:"name",type:"string",editable:!1},value:{from:"value",type:"number",defaultValue:""},format:{from:"format",type:"string"}}}}},save:function(e){var t=!!e.values.value;e.container.parent().find("input[type='checkbox']").prop("checked",t)}})},_createButtons:function(){var e,t,i=this.options.buttons,n='<div class="'+p.styles.popup.buttonsContainer+'">';for(e=0,t=i.length;e<t;e++)n+=this.createButton(i[e]);n+="</div>",this.container.append(n)},_updateModel:function(){var e,t,i,n=[],r=this.grid.dataSource.data();for(t=0,i=r.length;t<i;t++)e=r[t].get("value"),null!==e&&e>0&&n.push(r[t]);this.model[this.resourcesField]=n}}),p=k.extend({init:function(e,t,i){T(t)&&(t={dataSource:t}),a={append:{text:"Add Task",action:"add",className:p.styles.toolbar.appendButton,iconClass:p.styles.toolbar.iconPlus},pdf:{text:"Export to PDF",className:p.styles.toolbar.pdfButton,iconClass:p.styles.toolbar.iconPdf}},k.fn.init.call(this,e,t),i&&(this._events=i),this._wrapper(),this._resources(),this.options.views&&this.options.views.length||(this.options.views=["day","week","month"]),this._timeline(),this._toolbar(),this._footer(),this._adjustDimensions(),this._preventRefresh=!0,this.view(this.timeline._selectedViewName),this._preventRefresh=!1,this._dataSource(),this._assignments(),this._dropDowns(),this._list(),this._dependencies(),this._resizable(),this._scrollable(),this._dataBind(),this._attachEvents(),this._createEditor(),g.notify(this)},events:["dataBinding","dataBound","add","edit","remove","cancel","save","change","navigate","moveStart","move","moveEnd","resizeStart","resize","resizeEnd","columnResize"],options:{name:"Gantt",autoBind:!0,navigatable:!1,selectable:!0,editable:!0,resizable:!1,columnResizeHandleWidth:R,columns:[],views:[],dataSource:{},dependencies:{},resources:{},assignments:{},taskTemplate:null,messages:{save:"Save",cancel:"Cancel",destroy:"Delete",deleteTaskConfirmation:q,deleteDependencyConfirmation:V,deleteTaskWindowTitle:"Delete task",deleteDependencyWindowTitle:"Delete dependency",views:{day:"Day",week:"Week",month:"Month",year:"Year",start:"Start",end:"End"},actions:{append:"Add Task",addChild:"Add Child",insertBefore:"Add Above",insertAfter:"Add Below",pdf:"Export to PDF"},editor:{editorTitle:"Task",resourcesEditorTitle:"Resources",title:"Title",start:"Start",end:"End",percentComplete:"Complete",resources:"Resources",assignButton:"Assign",resourcesHeader:"Resources",unitsHeader:"Units"}},showWorkHours:!0,showWorkDays:!0,toolbar:null,workDayStart:new Date(1980,1,1,8,0,0),workDayEnd:new Date(1980,1,1,17,0,0),workWeekStart:1,workWeekEnd:5,hourSpan:1,snap:!0,height:600,listWidth:"30%",rowHeight:null},select:function(e){var i=this.list;return e?(i.select(e),t):i.select()},clearSelection:function(){this.list.clearSelection()},destroy:function(){k.fn.destroy.call(this),this.dataSource&&(this.dataSource.unbind("change",this._refreshHandler),this.dataSource.unbind("progress",this._progressHandler),this.dataSource.unbind("error",this._errorHandler)),this.dependencies&&(this.dependencies.unbind("change",this._dependencyRefreshHandler),this.dependencies.unbind("error",this._dependencyErrorHandler)),this.timeline&&(this.timeline.unbind(),this.timeline.destroy()),this.list&&(this.list.unbind(),this.list.destroy()),this.footerDropDown&&this.footerDropDown.destroy(),this.headerDropDown&&this.headerDropDown.destroy(),this._editor&&this._editor.destroy(),this._resizeDraggable&&this._resizeDraggable.destroy(),this.toolbar.off(A),m&&(this._mediaQuery.removeListener(this._mediaQueryHandler),this._mediaQuery=null),e(window).off("resize"+A,this._resizeHandler),e(this.wrapper).off(A),this.toolbar=null,this.footer=null},setOptions:function(t){var i,n=g.deepExtend({},this.options,t),r=this._events;t.views||(i=this.view().name,n.views=e.map(this.options.views,function(e){var t=M(e),n=t?"string"!=typeof e.type?e.title:e.type:e;return i===n?t?e.selected=!0:e={type:n,selected:!0}:t&&(e.selected=!1),e})),t.dataSource||(n.dataSource=this.dataSource),t.dependencies||(n.dependencies=this.dependencies),t.resources||(n.resources=this.resources),t.assignments||(n.assignments=this.assignments),this.destroy(),this.element.empty(),this.options=null,this.init(this.element,n,r),k.fn._setEvents.call(this,n)},_attachEvents:function(){this._resizeHandler=H(this.resize,this,!1),e(window).on("resize"+A,this._resizeHandler)},_wrapper:function(){var e=p.styles,t=[e.icon,e.resizeHandle].join(" "),i=this.options,n=i.height,r=i.width;this.wrapper=this.element.addClass(e.wrapper).append("<div class='"+e.listWrapper+"'><div></div></div>").append("<div class='"+e.splitBarWrapper+"'><div class='"+t+"'></div></div>").append("<div class='"+e.timelineWrapper+"'><div></div></div>"),
this.wrapper.find(j+e.list).width(i.listWidth),n&&this.wrapper.height(n),r&&this.wrapper.width(r),i.rowHeight&&this.wrapper.addClass(e.rowHeight)},_toolbar:function(){var t,i,n,r=this,s=p.styles,o=j+s.toolbar.views+" > li",a=j+s.toolbar.pdfButton,d=j+s.buttonToggle,l=j+s.gridContent,c=e(j+s.list),h=e(j+s.timeline),u=s.hovered,f=this.options.toolbar,v=e("<div class='"+s.toolbar.actions+"'>"),_=function(e){e.matches?c.css({display:"none","max-width":0}):(c.css({display:"inline-block",width:"30%","max-width":"none"}),h.css("display","inline-block"),r.refresh(),h.find(l).scrollTop(r.scrollTop)),r._resize()};C(f)||(f=typeof f===P?f:this._actions(f),f=H(g.template(f),this)),n=e(X({styles:s.toolbar})),i=e(Z({ns:g.ns,views:this.timeline.views,styles:s.toolbar})),v.append(f({})),t=e("<div class='"+s.toolbar.headerWrapper+"'>").append(n).append(i).append(v),i.find("li").length>1&&i.prepend(Y({styles:s.toolbar})),this.wrapper.prepend(t),this.toolbar=t,m&&(this._mediaQueryHandler=H(_,this),this._mediaQuery=window.matchMedia("(max-width: 480px)"),this._mediaQuery.addListener(this._mediaQueryHandler)),t.on(N+A,o,function(t){var n,o,a;t.preventDefault(),n=r.list,o=e(this).attr(g.attr("name")),a=i.find(j+s.toolbar.currentView),a.is(":visible")&&a.parent().toggleClass(s.toolbar.expanded),n.editable&&n.editable.trigger("validate")||r.trigger("navigate",{view:o})||r.view(o)}).on(N+A,a,function(e){e.preventDefault(),r.saveAsPDF()}).on(N+A,d,function(e){e.preventDefault(),c.is(":visible")?(c.css({display:"none",width:"0"}),h.css({display:"inline-block",width:"100%"}),r.refresh(),h.find(l).scrollTop(r.scrollTop)):(h.css({display:"none",width:0}),c.css({display:"inline-block",width:"100%","max-width":"none"}).find(l).scrollTop(r.scrollTop)),r._resize()}),this.wrapper.find(j+s.toolbar.toolbar+" li").hover(function(){e(this).addClass(u)},function(){e(this).removeClass(u)})},_actions:function(){var e,t,i=this.options,n=i.toolbar,r="";if(!T(n)){if(!i.editable)return r;n=["append"]}for(e=0,t=n.length;e<t;e++)r+=this._createButton(n[e]);return r},_footer:function(){var t,i,n,r,s;this.options.editable&&(t=p.styles.toolbar,i=this.options.messages.actions,n=e(g.template(K)(F(!0,{styles:t},a.append,{text:i.append}))),r=e("<div class='"+t.actions+"'>").append(n),s=e("<div class='"+t.footerWrapper+"'>").append(r),this.wrapper.append(s),this.footer=s)},_createButton:function(e){var t=e.template||K,i=this.options.messages.actions,n=typeof e===P?e:e.name||e.text,r=a[n]?a[n].className:"k-gantt-"+(n||"").replace(/\s/g,""),s={iconClass:"",action:"",text:n,className:r,styles:p.styles.toolbar};if(!(n||M(e)&&e.template))throw Error("Custom commands should have name specified");return s=F(!0,s,a[n],{text:i[n]}),M(e)&&(e.className&&D(s.className,e.className.split(" "))<0&&(e.className+=" "+s.className),s=F(!0,s,e)),g.template(t)(s)},_adjustDimensions:function(){var e=this.element,t=p.styles,i=j+t.list,n=j+t.timeline,r=j+t.splitBar,s=this.toolbar.outerHeight(),o=this.footer?this.footer.outerHeight():0,a=e.height(),d=e.width(),l=e.find(r).outerWidth(),c=e.find(i).outerWidth();e.children([i,n,r].join(",")).height(a-(s+o)).end().children(n).width(d-(l+c)),d<c+l&&e.find(i).width(d-l)},_scrollTo:function(e){var t,n,r=this.timeline.view(),s=this.list,o=g.attr("uid"),a="string"==typeof e?e:e.closest("tr"+i()).attr(o),d=function(){0!==n.length&&t()};r.content.is(":visible")?(n=r.content.find(i(a)),t=function(){r._scrollTo(n)}):(n=s.content.find(i(a)),t=function(){n.get(0).scrollIntoView()}),d()},_dropDowns:function(){var e=this,t=j+p.styles.toolbar.actions,i=this.options.messages.actions,n=this.timeline,r=function(t){var i,r=t.type,s=e.dataSource,o=s._createNewModel(),a=e.dataItem(e.select()),d=s.taskParent(a),l=n.view()._timeSlots()[0],c="add"===r?a:d,h=e.list.editable;h&&h.trigger("validate")||(o.set("title","New task"),c?(o.set("parentId",c.get("id")),o.set("start",c.get("start")),o.set("end",c.get("end"))):(o.set("start",l.start),o.set("end",l.end)),"add"!==r&&(i=a.get("orderId"),i="insert-before"===r?i:i+1),e._createTask(o,i))};this.options.editable&&(this.footerDropDown=new ie(this.footer.children(t).eq(0),{messages:{actions:i},direction:"up",animation:{open:{effects:"slideIn:up"}},navigatable:e.options.navigatable}),this.headerDropDown=new ie(this.toolbar.children(t).eq(0),{messages:{actions:i},navigatable:e.options.navigatable}),this.footerDropDown.bind("command",r),this.headerDropDown.bind("command",r))},_list:function(){var e,t,i=this,n=i.options.navigatable,r=p.styles,o=this.wrapper.find(j+r.list),a=o.find("> div"),d=this.wrapper.find(j+r.toolbar.actions+" > button"),l={columns:this.options.columns||[],dataSource:this.dataSource,selectable:this.options.selectable,editable:this.options.editable,resizable:this.options.resizable,columnResizeHandleWidth:this.options.columnResizeHandleWidth,listWidth:o.outerWidth(),resourcesField:this.resources.field,rowHeight:this.options.rowHeight},c=l.columns,h=function(){n&&(i._current(i._cachedCurrent),s(i.list.content.find("table"),!0)),delete i._cachedCurrent};for(t=0;t<c.length;t++)e=c[t],e.field===this.resources.field&&"function"!=typeof e.editor&&(e.editor=H(this._createResourceEditor,this));this.list=new g.ui.GanttList(a,l),this.list.bind("render",function(){i._navigatable()},!0).bind("edit",function(e){i._cachedCurrent=e.cell,i.trigger("edit",{task:e.model,container:e.cell})&&e.preventDefault()}).bind("cancel",function(e){i.trigger("cancel",{task:e.model,container:e.cell})&&e.preventDefault(),h()}).bind("update",function(e){i._updateTask(e.task,e.updateInfo),h()}).bind("change",function(){i.trigger("change");var e=i.list.select();e.length?(d.removeAttr("data-action","add"),i.timeline.select("[data-uid='"+e.attr("data-uid")+"']")):(d.attr("data-action","add"),i.timeline.clearSelection())}).bind("columnResize",function(e){i.trigger("columnResize",{column:e.column,oldWidth:e.oldWidth,newWidth:e.newWidth})})},_timeline:function(){var e=this,i=p.styles,r=n(F(!0,{resourcesField:this.resources.field},this.options)),s=this.wrapper.find(j+i.timeline+" > div"),o=j+i.toolbar.currentView+" > "+j+i.toolbar.link;this.timeline=new g.ui.GanttTimeline(s,r),this.timeline.bind("navigate",function(t){var n=t.view.replace(/\./g,"\\.").toLowerCase(),r=e.toolbar.find(j+i.toolbar.views+" > li").removeClass(i.selected).end().find(j+i.toolbar.viewButton+"-"+n).addClass(i.selected).find(j+i.toolbar.link).text();e.toolbar.find(o).text(r),e.refresh()}).bind("moveStart",function(i){var n=e.list.editable;return n&&n.trigger("validate")?(i.preventDefault(),t):(e.trigger("moveStart",{task:i.task})&&i.preventDefault(),t)}).bind("move",function(t){var i=t.task,n=t.start,r=new Date(n.getTime()+i.duration());e.trigger("move",{task:i,start:n,end:r})&&t.preventDefault()}).bind("moveEnd",function(t){var i=t.task,n=t.start,r=new Date(n.getTime()+i.duration());e.trigger("moveEnd",{task:i,start:n,end:r})||e._updateTask(e.dataSource.getByUid(i.uid),{start:n,end:r})}).bind("resizeStart",function(i){var n=e.list.editable;return n&&n.trigger("validate")?(i.preventDefault(),t):(e.trigger("resizeStart",{task:i.task})&&i.preventDefault(),t)}).bind("resize",function(t){e.trigger("resize",{task:t.task,start:t.start,end:t.end})&&t.preventDefault()}).bind("resizeEnd",function(t){var i=t.task,n={};t.resizeStart?n.start=t.start:n.end=t.end,e.trigger("resizeEnd",{task:i,start:t.start,end:t.end})||e._updateTask(e.dataSource.getByUid(i.uid),n)}).bind("percentResizeStart",function(t){var i=e.list.editable;i&&i.trigger("validate")&&t.preventDefault()}).bind("percentResizeEnd",function(t){e._updateTask(e.dataSource.getByUid(t.task.uid),{percentComplete:t.percentComplete})}).bind("dependencyDragStart",function(t){var i=e.list.editable;i&&i.trigger("validate")&&t.preventDefault()}).bind("dependencyDragEnd",function(t){var i=e.dependencies._createNewModel({type:t.type,predecessorId:t.predecessor.id,successorId:t.successor.id});e._createDependency(i)}).bind("select",function(t){var i=e.list.editable;i&&i.trigger("validate"),e.select("[data-uid='"+t.uid+"']")}).bind("editTask",function(t){var i=e.list.editable;i&&i.trigger("validate")||e.editTask(t.uid)}).bind("clear",function(){e.clearSelection()}).bind("removeTask",function(t){var i=e.list.editable;i&&i.trigger("validate")||e.removeTask(e.dataSource.getByUid(t.uid))}).bind("removeDependency",function(t){var i=e.list.editable;i&&i.trigger("validate")||e.removeDependency(e.dependencies.getByUid(t.uid))})},_dataSource:function(){var e=this.options,t=e.dataSource;t=T(t)?{data:t}:t,this.dataSource&&this._refreshHandler?this.dataSource.unbind("change",this._refreshHandler).unbind("progress",this._progressHandler).unbind("error",this._errorHandler):(this._refreshHandler=H(this.refresh,this),this._progressHandler=H(this._requestStart,this),this._errorHandler=H(this._error,this)),this.dataSource=g.data.GanttDataSource.create(t).bind("change",this._refreshHandler).bind("progress",this._progressHandler).bind("error",this._errorHandler)},_dependencies:function(){var e=this.options.dependencies||{},t=T(e)?{data:e}:e;this.dependencies&&this._dependencyRefreshHandler?this.dependencies.unbind("change",this._dependencyRefreshHandler).unbind("error",this._dependencyErrorHandler):(this._dependencyRefreshHandler=H(this.refreshDependencies,this),this._dependencyErrorHandler=H(this._error,this)),this.dependencies=g.data.GanttDependencyDataSource.create(t).bind("change",this._dependencyRefreshHandler).bind("error",this._dependencyErrorHandler)},_resources:function(){var e=this.options.resources,t=e.dataSource||{};this.resources={field:"resources",dataTextField:"name",dataColorField:"color",dataFormatField:"format"},F(this.resources,e),this.resources.dataSource=g.data.DataSource.create(t)},_assignments:function(){var e=this.options.assignments,t=e.dataSource||{};this.assignments?this.assignments.dataSource.unbind("change",this._assignmentsRefreshHandler):this._assignmentsRefreshHandler=H(this.refresh,this),this.assignments={dataTaskIdField:"taskId",dataResourceIdField:"resourceId",dataValueField:"value"},F(this.assignments,e),this.assignments.dataSource=g.data.DataSource.create(t),this.assignments.dataSource.bind("change",this._assignmentsRefreshHandler)},_createEditor:function(){var e=this,i=this._editor=new u(this.wrapper,F({},this.options,{target:this,resources:{field:this.resources.field,editor:H(this._createResourceEditor,this)},createButton:H(this._createPopupButton,this)}));i.bind("cancel",function(i){var n=e.dataSource.getByUid(i.model.uid);return e.trigger("cancel",{container:i.container,task:n})?(i.preventDefault(),t):(e.cancelTask(),t)}).bind("edit",function(t){var i=e.dataSource.getByUid(t.model.uid);e.trigger("edit",{container:t.container,task:i})&&t.preventDefault()}).bind("save",function(t){var i=e.dataSource.getByUid(t.model.uid);e.saveTask(i,t.updateInfo)}).bind("remove",function(t){e.removeTask(t.model.uid)}).bind("close",e._onDialogClose)},_onDialogClose:function(){},_createResourceEditor:function(e,t){var i=this,n=t instanceof b?t:t.model,r=n.get("id"),s=this.options.messages,o=i.resources.field,a=this._resourceEditor=new f(e,{resourcesField:o,data:this._wrapResourceData(r),model:n,messages:F({},s.editor),buttons:[{name:"update",text:s.save,className:p.styles.primary},{name:"cancel",text:s.cancel}],createButton:H(this._createPopupButton,this),save:function(e){i._updateAssignments(e.model.get("id"),e.model.get(o))}});a.open()},_createPopupButton:function(e){var t=e.name||e.text,i={className:p.styles.popup.button+" k-gantt-"+(t||"").replace(/\s/g,""),text:t,attr:""};if(!(t||M(e)&&e.template))throw Error("Custom commands should have name specified");return M(e)&&(e.className&&(e.className+=" "+i.className),i=F(!0,i,e)),g.template(Q)(i)},view:function(e){return this.timeline.view(e)},dataItem:function(e){var t,i;return e?(t=this.list,i=t.content.find(e),t._modelFromElement(i)):null},setDataSource:function(e){this.options.dataSource=e,this._dataSource(),this.list._setDataSource(this.dataSource),this.options.autoBind&&e.fetch()},setDependenciesDataSource:function(e){this.options.dependencies=e,this._dependencies(),this.options.autoBind&&e.fetch()},items:function(){return this.wrapper.children(".k-task")},_updateAssignments:function(e,t){for(var i,n,r,s,o,a,d,l=this.assignments.dataSource,c=this.assignments.dataTaskIdField,h=this.assignments.dataResourceIdField,u=!1,f=new x(l.view()).filter({field:c,operator:"eq",value:e}).toArray();f.length;){for(i=f[0],s=0,o=t.length;s<o;s++)if(n=t[s],i.get(h)===n.get("id")){r=t[s].get("value"),this._updateAssignment(i,r),t.splice(s,1),u=!0;break}u||this._removeAssignment(i),u=!1,f.shift()}for(a=0,d=t.length;a<d;a++)n=t[a],this._createAssignment(n,e);l.sync()},cancelTask:function(){var e=this._editor,t=e.container;t&&e.close()},editTask:function(e){var t,i="string"==typeof e?this.dataSource.getByUid(e):e;i&&(t=this.dataSource._createNewModel(i.toJSON()),t.uid=i.uid,this.cancelTask(),this._editTask(t))},_editTask:function(e){this._editor.editTask(e)},saveTask:function(e,t){var i=this._editor,n=i.container,r=i.editable;n&&r&&r.end()&&this._updateTask(e,t)},_updateTask:function(e,t){var i=this.resources.field;this.trigger("save",{task:e,values:t})||(this._preventRefresh=!0,this.dataSource.update(e,t),t[i]&&this._updateAssignments(e.get("id"),t[i]),this._syncDataSource())},_updateAssignment:function(e,t){var i=this.assignments.dataValueField;e.set(i,t)},removeTask:function(e){var t=this,i="string"==typeof e?this.dataSource.getByUid(e):e;i&&this._taskConfirm(function(e){e||t._removeTask(i)},i)},_createTask:function(e,i){if(!this.trigger("add",{task:e,dependency:null})){var n=this.dataSource;this._preventRefresh=!0,i===t?n.add(e):n.insert(i,e),this._scrollToUid=e.uid,this._syncDataSource()}},_createDependency:function(e){this.trigger("add",{task:null,dependency:e})||(this._preventDependencyRefresh=!0,this.dependencies.add(e),this._preventDependencyRefresh=!1,this.dependencies.sync())},_createAssignment:function(e,t){var i=this.assignments,n=i.dataSource,r=i.dataTaskIdField,s=i.dataResourceIdField,o=i.dataValueField,a=n._createNewModel();a[r]=t,a[s]=e.get("id"),a[o]=e.get("value"),n.add(a)},removeDependency:function(e){var t=this,i="string"==typeof e?this.dependencies.getByUid(e):e;i&&this._dependencyConfirm(function(e){e||t._removeDependency(i)},i)},_removeTaskDependencies:function(e,t){this._preventDependencyRefresh=!0;for(var i=0,n=t.length;i<n;i++)this.dependencies.remove(t[i]);this._preventDependencyRefresh=!1,this.dependencies.sync()},_removeTaskAssignments:function(e){var t,i,n=this.assignments.dataSource,r=n.view(),s={field:this.assignments.dataTaskIdField,operator:"eq",value:e.get("id")};for(r=new x(r).filter(s).toArray(),this._preventRefresh=!0,t=0,i=r.length;t<i;t++)n.remove(r[t]);this._preventRefresh=!1,n.sync()},_removeTask:function(e){var t=this.dependencies.dependencies(e.id);this.trigger("remove",{task:e,dependencies:t})||(this._removeTaskDependencies(e,t),this._removeTaskAssignments(e),this._preventRefresh=!0,this.dataSource.remove(e)&&this._syncDataSource(),this._preventRefresh=!1)},_removeDependency:function(e){this.trigger("remove",{task:null,dependencies:[e]})||this.dependencies.remove(e)&&this.dependencies.sync()},_removeAssignment:function(e){this.assignments.dataSource.remove(e)},_taskConfirm:function(e,t){var i=this.options.messages;this._confirm(e,{model:t,text:i.deleteTaskConfirmation,title:i.deleteTaskWindowTitle})},_dependencyConfirm:function(e,t){var i=this.options.messages;this._confirm(e,{model:t,text:i.deleteDependencyConfirmation,title:i.deleteDependencyWindowTitle})},_confirm:function(e,t){var i,n,r=this.options.editable;r===!0||r.confirmation!==!1?(i=this.options.messages,n=[{name:"delete",text:i.destroy,className:p.styles.primary,click:function(){e()}},{name:"cancel",text:i.cancel,click:function(){e(!0)}}],this.showDialog(F(!0,{},t,{buttons:n}))):e()},showDialog:function(e){this._editor.showDialog(e)},refresh:function(){var e,t,n,r,s,o;this._preventRefresh||this.list.editable||(this._progress(!1),e=this.dataSource,t=e.taskTree(),n=this._scrollToUid,o=-1,this.current&&(s=this.current.closest("tr").attr(g.attr("uid")),o=this.current.index()),this.trigger("dataBinding")||(0!==this.resources.dataSource.data().length&&this._assignResources(t),this._editor&&this._editor.close(),this.clearSelection(),this.list._render(t),this.timeline._render(t),this.timeline._renderDependencies(this.dependencies.view()),n&&(this._scrollTo(n),this.select(i(n))),(n||s)&&o>=0&&(r=this.list.content.find("tr"+i(n||s)+" > td:eq("+o+")"),this._current(r)),this._scrollToUid=null,this.trigger("dataBound")))},refreshDependencies:function(){this._preventDependencyRefresh||this.trigger("dataBinding")||(this.timeline._renderDependencies(this.dependencies.view()),this.trigger("dataBound"))},_assignResources:function(e){var t,i,n=this.resources,r=this.assignments,s=function(){var e=r.dataSource.view(),t={field:r.dataTaskIdField};return e=new x(e).group(t).toArray()},o=s(),a=function(e,t){var i,r,s=e.get("id");for(g.setter(n.field)(e,new S([])),i=0,r=o.length;i<r;i++)o[i].value===s&&t(e,o[i].items)},d=function(e,t){var i,s,o,a,d,l,c,h;for(i=0,s=t.length;i<s;i++)o=t[i],a=n.dataSource.get(o.get(r.dataResourceIdField)),d=o.get(r.dataValueField),l=o.get(r.dataResourceIdField),c=a.get(n.dataFormatField)||E,h=g.toString(d,c),e[n.field].push(new b({id:l,name:a.get(n.dataTextField),color:a.get(n.dataColorField),value:d,formatedValue:h}))};for(t=0,i=e.length;t<i;t++)a(e[t],d)},_wrapResourceData:function(e){var t,i,n,r=this,s=[],o=this.resources.dataSource.view(),a=this.assignments.dataSource.view(),d=new x(a).filter({field:r.assignments.dataTaskIdField,operator:"eq",value:e}).toArray(),l=function(e){var t=null;return new x(d).filter({field:r.assignments.dataResourceIdField,operator:"eq",value:e}).select(function(e){t+=e.get(r.assignments.dataValueField)}),t};for(i=0,n=o.length;i<n;i++)t=o[i],s.push({id:t.get("id"),name:t.get(r.resources.dataTextField),format:t.get(r.resources.dataFormatField)||E,value:l(t.id)});return s},_syncDataSource:function(){this._preventRefresh=!1,this._requestStart(),this.dataSource.sync()},_requestStart:function(){this._progress(!0)},_error:function(){this._progress(!1)},_progress:function(e){g.ui.progress(this.element,e)},_resizable:function(){var t,i,n,r=this,s=this.wrapper,o=p.styles,a=j+o.gridContent,d=s.find(j+o.list),l=s.find(j+o.timeline);this._resizeDraggable=s.find(j+o.splitBar).height(d.height()).hover(function(){e(this).addClass(o.splitBarHover)},function(){e(this).removeClass(o.splitBarHover)}).end().kendoResizable({orientation:"horizontal",handle:j+o.splitBar,start:function(){t=d.width(),i=l.width(),n=l.find(a).scrollLeft()},resize:function(e){var o=e.x.initialDelta;g.support.isRtl(s)&&(o*=-1),t+o<0||i-o<0||(d.width(t+o),l.width(i-o),l.find(a).scrollLeft(n+o),r.timeline.view()._renderCurrentTime())}}).data("kendoResizable")},_scrollable:function(){var t=this,i=p.styles,n=j+i.gridContent,r=j+i.gridHeaderWrap,s=this.timeline.element.find(r),o=this.timeline.element.find(n),a=this.list.element.find(r),d=this.list.element.find(n);_&&d.css("overflow-y","auto"),o.on("scroll",function(){t.scrollTop=this.scrollTop,s.scrollLeft(this.scrollLeft),d.scrollTop(this.scrollTop)}),d.on("scroll",function(){a.scrollLeft(this.scrollLeft)}).on("DOMMouseScroll"+A+" mousewheel"+A,function(t){var i=o.scrollTop(),n=g.wheelDeltaY(t);n&&(t.preventDefault(),e(t.currentTarget).one("wheel"+A,!1),o.scrollTop(i+-n))})},_navigatable:function(){var n,r=this,o=this.options.navigatable,a=this.options.editable,d=this.list.header.find("table"),l=this.list.content.find("table"),c=p.styles,h=g.support.isRtl(this.wrapper),u=this.timeline.element.find(j+c.gridContent),f=d.add(l),m=i(),v={collapse:!1,expand:!0},_=function(e){var t=r.timeline.view()._timeSlots()[0].offsetWidth;u.scrollLeft(u.scrollLeft()+(e?-t:t))},y=function(e){var t=r.current.parent("tr"+i()),n=r.current.index(),o=t[e]();0!==r.select().length&&r.clearSelection(),0!==o.length?(r._current(o.children("td:eq("+n+")")),r._scrollTo(r.current)):r.current.is("td")&&"prev"==e?s(d):r.current.is("th")&&"next"==e&&s(l)},k=function(e){var t=r.current[e]();0!==t.length&&(r._current(t),n=r.current.index())},w=function(e){var t=r.dataItem(r.current);t.summary&&t.expanded!==e&&t.set("expanded",e)},b=function(){var e,t;r.options.editable&&!r.list.editable&&(e=r.select(),t=g.attr("uid"),e.length&&r.removeTask(e.attr(t)))};return e(this.wrapper).on("mousedown"+A,"tr"+m+", div"+m+":not("+j+c.line+")",function(t){var n,d=e(t.currentTarget),l=e(t.target).is(":button,a,:input,a>.k-icon,textarea,span.k-icon,span.k-link,.k-input,.k-multiselect-wrap");t.ctrlKey||(o&&(n=d.is("tr")?e(t.target).closest("td"):r.list.content.find("tr"+i(d.attr(g.attr("uid")))+" > td:first"),r._current(n)),!o&&!a||l||(r._focusTimeout=setTimeout(function(){s(r.list.content.find("table"),!0)},2)))}),o!==!0?(l.on("keydown"+A,function(e){e.keyCode==z.DELETE&&b()}),t):(f.on("focus"+A,function(){var t=this===l.get(0)?"td":"th",i=r.select(),s=r.current||e(i.length?i:this).find(t+":eq("+(n||0)+")");r._current(s)}).on("blur"+A,function(){r._current(),this==d&&e(this).attr(W,-1)}).on("keydown"+A,function(t){var i,n=t.keyCode;if(r.current)switch(i=r.current.is("td"),n){case z.RIGHT:t.preventDefault(),t.altKey?_():t.ctrlKey?w(h?v.collapse:v.expand):k(h?"prev":"next");break;case z.LEFT:t.preventDefault(),t.altKey?_(!0):t.ctrlKey?w(h?v.expand:v.collapse):k(h?"next":"prev");break;case z.UP:t.preventDefault(),y("prev");break;case z.DOWN:t.preventDefault(),y("next");break;case z.SPACEBAR:t.preventDefault(),i&&r.select(r.current.closest("tr"));break;case z.ENTER:t.preventDefault(),i?r.options.editable&&(r._cachedCurrent=r.current,r.list._startEditHandler(r.current),e(this).one("keyup",function(e){e.stopPropagation()})):r.current.children("a.k-link").click();break;case z.ESC:t.stopPropagation();break;case z.DELETE:i&&b();break;default:n>=49&&n<=57&&r.view(r.timeline._viewByIndex(n-49))}}),t)},_current:function(t){var i,n=p.styles;this.current&&this.current.length&&this.current.removeClass(n.focused).removeAttr("id"),t&&t.length?(this.current=t.addClass(n.focused).attr("id",G),i=e(g._activeElement()),i.is("table")&&this.wrapper.find(i).length>0&&i.removeAttr(O).attr(O,G)):this.current=null},_dataBind:function(){var t,i=this;i.options.autoBind&&(this._preventRefresh=!0,this._preventDependencyRefresh=!0,t=e.map([this.dataSource,this.dependencies,this.resources.dataSource,this.assignments.dataSource],function(e){return e.fetch()}),e.when.apply(null,t).done(function(){i._preventRefresh=!1,i._preventDependencyRefresh=!1,i.refresh()}))},_resize:function(){this._adjustDimensions(),this.timeline.view()._adjustHeight(),this.timeline.view()._renderCurrentTime(),this.list._adjustHeight()}}),g.PDFMixin&&(g.PDFMixin.extend(p.fn),p.fn._drawPDF=function(){var e=p.styles,t="."+e.list,i=this.wrapper.find(t).width(),n=this.wrapper.clone();return n.find(t).css("width",i),this._drawPDFShadow({content:n},{avoidLinks:this.options.pdf.avoidLinks})}),g.ui.plugin(p),F(!0,p,{styles:te})}(window.kendo.jQuery),window.kendo},"function"==typeof define&&define.amd?define:function(e,t,i){(i||t)()});
//# sourceMappingURL=kendo.gantt.min.js.map
