<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.klaw.dao.contractDao.contract.BmContractCloseMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.klaw.entity.contractBean.contract.BmContractClose">
        <id column="id" property="id"/>
        <result column="approval_code" property="approvalCode"/>
        <result column="contract_name" property="contractName"/>
        <result column="contract_code" property="contractCode"/>
        <result column="contract_id" property="contractId"/>
        <result column="contract_type" property="contractType"/>
        <result column="contract_type_code" property="contractTypeCode"/>
        <result column="closing_reason" property="closingReason"/>
        <result column="closing_reason_id" property="closingReasonId"/>
        <result column="closing_description" property="closingDescription"/>
        <result column="other_attachment" property="otherAttachment"/>
        <result column="change_contract_num" property="changeContractNum"/>
        <result column="take_effect_name" property="takeEffectName"/>
        <result column="take_effect_code" property="takeEffectCode"/>
        <result column="close_type" property="closeType"/>
        <result column="close_type_code" property="closeTypeCode"/>
        <result column="close_state" property="closeState"/>
        <result column="close_state_code" property="closeStateCode"/>
        <result column="perform_situation" property="performSituation"/>
        <result column="nature" property="nature"/>
        <result column="nature_code" property="natureCode"/>
        <result column="is_project" property="isProject"/>
        <result column="create_ogn_id" property="createOgnId"/>
        <result column="create_ogn_name" property="createOgnName"/>
        <result column="create_dept_id" property="createDeptId"/>
        <result column="create_dept_name" property="createDeptName"/>
        <result column="create_group_id" property="createGroupId"/>
        <result column="create_group_name" property="createGroupName"/>
        <result column="create_psn_id" property="createPsnId"/>
        <result column="create_psn_name" property="createPsnName"/>
        <result column="create_org_id" property="createOrgId"/>
        <result column="create_org_name" property="createOrgName"/>
        <result column="create_psn_full_id" property="createPsnFullId"/>
        <result column="create_psn_full_name" property="createPsnFullName"/>
        <result column="create_legal_unit_id" property="createLegalUnitId"/>
        <result column="create_legal_unit_name" property="createLegalUnitName"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_psn_phone" property="createPsnPhone"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, approval_code, contract_name, contract_code, contract_id, contract_type, contract_type_code, closing_reason, closing_reason_id, closing_description, other_attachment, change_contract_num, take_effect_name, take_effect_code, close_type, close_type_code, close_state, close_state_code, perform_situation, nature, nature_code, is_project, create_ogn_id, create_ogn_name, create_dept_id, create_dept_name, create_group_id, create_group_name, create_psn_id, create_psn_name, create_org_id, create_org_name, create_psn_full_id, create_psn_full_name, create_legal_unit_id, create_legal_unit_name, create_transfer_time, create_transfer_name, create_time, update_time, create_psn_phone
    </sql>
    <select id="selectHotels" resultType="java.util.Map">
        select a.id                     as "id",
               a.data_Type_Name         as "dataTypeName",
               a.data_Type_Code         as "dataTypeCode",
               a.contract_code          as "contractCode",
               a.contract_code_version  as "contractCodeVersion",
               a.contract_money         as "contractMoney",
               a.this_change_money      as "thisChangeMoney",
               a.change_money_type      as "changeMoneyType",
               a.change_money_type_code as "changeMoneyTypeCode",
               a.money_type_code        as "moneyTypeCode",
               a.money_Type             as "moneyType",
               p.id                     as "id",
               p.project_Name           as "projectName",
               p.project_Code           as "projectCode",
               p.project_Type           as "projectType",
               p.assign_Money           as "assignMoney"

        from bm_contract a
                 join bm_contract_project p
                      on a.id = p.parent_id
        where (contract_code = #{contractCode} or a.original_contract_code = #{contractCode})
        order by a.data_Type_Name

    </select>

</mapper>
