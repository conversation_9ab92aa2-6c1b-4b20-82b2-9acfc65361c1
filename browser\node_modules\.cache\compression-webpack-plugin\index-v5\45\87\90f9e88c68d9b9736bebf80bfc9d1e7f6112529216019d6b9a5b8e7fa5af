
6f7f1235f1e3f2693b04080c74e385cb683f8c78	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.435.1754018536329.js\",\"contentHash\":\"354f5206e456badff5a348ef8eb5e4c1\"}","integrity":"sha512-GETakqWjQl49Okt4/VNqmOCuhYaHndZPXXOGFSm5svznvA7cR4q7vr22D9MGUiAfvGK/bJrSos0u6Sv0Sc5fPg==","time":1754018575977,"size":96014}