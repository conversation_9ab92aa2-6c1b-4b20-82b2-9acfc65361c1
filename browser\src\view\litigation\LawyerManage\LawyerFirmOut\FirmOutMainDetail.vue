<template>
  <FormWindow ref="formWindow" @initData="initData" @loadData="loadData">
    <el-container style="height: calc(100vh - 84px);">
      <el-main>
        <el-scrollbar style="height: 100%" wrap-style="overflow-x: hidden;">
          <el-form ref="dataForm"
                   :model="mainData"
                   :rules="dataState !== 'view' ? rules : {}"
                   :style="dataState !== utils.formState.VIEW ? 'margin-right: 50px;' : ' margin-right: 0px;'"
                   label-width="120px"
                   style="padding-right: 10px;">
            <el-row
                style="padding: 10px 0 10px 0; position:fixed;  width: 100%;overflow-y:auto;background-color: white;z-index: 999">
              <el-button v-if="!isView" size="mini" type="success" @click="approval_">生成审批单</el-button>
              <el-button v-if="!isView" class="normal-btn" icon="el-icon-folder-checked" size="mini" @click="save_">
                保存
              </el-button>
            </el-row>
            <div style="padding-top: 50px;"></div>
            <span style="text-align: left;font-size: 20px;margin-left: 43%;font-weight: 900;">律所出库详情单</span>

            <div v-if="dataState !== 'view'">
              <div style="margin: 10px">
                <span style="font-weight: bold;font-size: 16px;color: #5A5A5F;">出库信息</span>
              </div>
              <el-divider></el-divider>
              <el-row style="margin-top: 10px;">
                <el-col :span="16">
                  <el-form-item label="事项名称" prop="itemName">
                    <el-input v-if="dataState !== 'view'" v-model.trim="mainData.itemName"
                              clearable maxlength="100" placeholder="请输入..." show-word-limit style="width: 100%"/>
                    <span v-else class="viewSpan">{{ mainData.itemName }}</span>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="事项编号">
                    <el-input v-if="dataState !== 'view'" v-model.trim="mainData.sequenceCode" disabled
                              show-word-limit style="width: 100%"/>
                    <span v-else class="viewSpan">{{ mainData.sequenceCode }}</span>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row style="margin-top: 10px;">
                <el-col :span="8">
                  <el-form-item label="出库类型" prop="outTypeName">
                    <el-select v-if="dataState !== 'view'"
                               v-model="mainData.outTypeId" clearable disabled placeholder="请选择"
                               style="width:100%" @change="outTypeChange">
                      <el-option
                          v-for="item in utils.out_type_data"
                          :key="item.id"
                          :label="item.dicName"
                          :value="item.id"
                      />
                    </el-select>
                    <span v-else class="viewSpan">{{ mainData.outTypeName }}</span>
                  </el-form-item>
                </el-col>

                <el-col :span="8">
                  <el-form-item label="出库原因">
                    <el-select v-if="dataState !== 'view'" v-model="failIds"
                               clearable collapse-tags multiple placeholder="请选择" style="width:100%"
                               @change="failChange">
                      <el-option
                          v-for="item in failData"
                          :key="item.id"
                          :label="item.dicName"
                          :value="item.id"
                      />
                    </el-select>
                    <span v-else class="viewSpan">{{ mainData.failName }}</span>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row>
                <el-col :span="24">
                  <el-form-item label="出库事由">
                    <el-input v-if="dataState !== 'view'" v-model="mainData.description"
                              :autosize="{ minRows: 3, maxRows: 6}" clearable
                              maxlength="1000"
                              placeholder="请输入..." show-word-limit style="width: 100%" type="textarea"/>
                    <text-span v-else :text=" mainData.description" class="viewSpan"/>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row>
                <el-col :span="24">
                  <el-form-item label="附件资料">
                    <uploadDoc
                        v-model="mainData.attachment"
                        :disabled="dataState==='view'"
                        :doc-path="docURL"
                        :files.sync="mainData.attachment"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>

            <div v-else>

              <SimpleBoardTitle style="margin-top: 5px;" title="出库信息">
                <table class="table_content" style="margin-top: 10px;">
                  <tbody>
                  <tr>
                    <th class="th_label" colspan="2">事项名称</th>
                    <td class="td_value" colspan="6">{{ mainData.itemName }}</td>
                    <th class="th_label" colspan="2">事项编号</th>
                    <td class="td_value" colspan="14">{{ mainData.sequenceCode }}</td>
                  </tr>
                  <tr>
                    <th class="th_label" colspan="2">出库类型</th>
                    <td class="td_value" colspan="6">{{ mainData.outTypeName }}</td>
                    <th class="th_label" colspan="2">出库原因</th>
                    <td class="td_value" colspan="14">{{ mainData.failName }}</td>
                  </tr>
                  <tr>
                    <th class="th_label_" colspan="2">出库说明</th>
                    <td class="td_value_" colspan="22">{{ mainData.description }}</td>
                  </tr>
                  </tbody>

                  <tbody>
                  <tr>
                    <th class="th_label_" colspan="2">附件资料</th>
                    <td class="td_value_" colspan="22">
                      <uploadDoc
                          v-model="mainData.attachment"
                          :disabled="dataState==='view'"
                          :doc-path="docURL"
                          :files.sync="mainData.attachment"
                      />
                    </td>
                  </tr>
                  </tbody>
                </table>
              </SimpleBoardTitle>

            </div>
            <simple-board :data-state="dataState" :has-add="hasAdd" style="margin-top: 20px;" title="出库律所信息"
                          @addBtn="addLawyer">
              <el-table
                  v-draggable="mainData.lawFirmOutList"
                  :data="mainData.lawFirmOutList"
                  :height="200"
                  :show-overflow-tooltip="true"
                  border
                  fit
                  highlight-current-row
                  stripe
                  style="width: 100%"
              >
                <el-table-column align="center" label="序号" show-overflow-tooltip type="index" width="60"/>
                <el-table-column align="center" label="律所名称" min-width="100" prop="lawyerFirm"
                                 show-overflow-tooltip>
                  <template slot="header">
                    <span style="color: #F56C6C;margin-right: 4px;">*</span>律所名称
                  </template>
                </el-table-column>
                <el-table-column align="center" label="负责人" min-width="100" prop="functionary"
                                 show-overflow-tooltip/>
                <el-table-column align="center" label="擅长领域" min-width="200" prop="beGoodAtDomain"
                                 show-overflow-tooltip/>
                <el-table-column align="center" label="操作" width="200px">
                  <template slot-scope="scope">
                    <el-button v-if="dataState !== 'view'" size="mini" type="text"
                               @click.native.prevent="deleteRow(scope.$index, scope.row)">删除
                    </el-button>
                    <el-button v-if="dataState === 'view'" size="mini" type="text"
                               @click.native.prevent="look(scope.$index,scope.row)">查看
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </simple-board>

            <el-row class="rowCol1" style="margin-top: 20px">
              <el-col :span="16">
                <el-form-item label="经办单位">
                  <span class="viewSpan">{{ mainData.createOgnName }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="经办人">
                  <span class="viewSpan">{{ mainData.createPsnName }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="16">
                <el-form-item label="经办部门">
                  <span class="viewSpan">{{ mainData.createDeptName }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="经办时间">
                  <span class="viewSpan">{{ mainData.createTime | parseTime }}</span>
                </el-form-item>
              </el-col>
            </el-row>

            <!--选择会知部门-->
            <el-dialog :close-on-click-modal="false" :visible.sync="deptOrgVisible" title="选择部门" width="50%">
              <div class="el-dialog-div">
                <orgTree :accordion="false" :checked-data.sync="zxcheckedData" :is-check="true" :is-checked-user="false"
                         :is-filter="true" :is-not-cascade="true" :show-user="false"/>
              </div>
              <span slot="footer" class="dialog-footer">
                <el-button class="negative-btn" icon="" @click="deptOrgCancel">取消</el-button>
                <el-button class="active-btn" icon="" type="primary" @click="choiceNoticeDeptSure">确定</el-button>
              </span>
            </el-dialog>

            <!--选择律所-->
            <LawFirmDialog :dialog-visible.sync="LawFirmVisible" @lawFirmSure="lawFirmSure"/>
            <!--选择部门或者人员-->
            <el-dialog :close-on-click-modal="false" :visible.sync="orgVisible" title="选择部门" width="50%">
              <div class="el-dialog-div">
                <orgTree :accordion="false" :checked-data.sync="zxcheckedData" :is-check="is_Check"
                         :is-checked-user="isCheckedUser"
                         :is-filter="true" :is-not-cascade="true" :show-user="showUser"/>
              </div>
              <span slot="footer" class="dialog-footer">
                <el-button class="negative-btn" icon="" @click="cancel_">取消</el-button>
                <el-button class="active-btn" icon="" type="primary" @click="choiceDeptSure_">确定
                </el-button>
              </span>
            </el-dialog>
          </el-form>
        </el-scrollbar>
      </el-main>
    </el-container>

  </FormWindow>
</template>

<script>
import lawyerFirmApi from '@/api/LawyerManage/LawyerFirmOut/lawFirmOutApprovalMain'
import dictApi from '@/api/_system/dict'

import {mapGetters} from 'vuex'
import taskApi from "@/api/_system/task";

import FormWindow from '@/view/components/FormWindow/FormWindow'
import SimpleBoard from "@/view/components/SimpleBoard/SimpleBoardViewCase"
import uploadDoc from '@/view/components/UploadDoc/UploadDoc'
import LawFirmDialog from '@/view/litigation/LawyerManage/LawyerFirmOut/dialog/FirmOutDialog'
import textSpan from '@/view/components/TextSpan/TextSpan'
import orgTree from '@/view/components/OrgTree/OrgTree'
import SimpleBoardTitle from "../../../components/SimpleBoard/SimpleBoardTitle"
import ProcessOpinion from '@/view/components/ProcessOpinion/ProcessOpinion'
import SimpleBoardTitleApproval from "@/view/components/SimpleBoard/SimpleBoardTitleApproval"

export default {
  name: 'FirmOutMainDetail',
  inject: ['layout', 'mcpLayout'],
  components: {
    FormWindow,
    SimpleBoard,
    uploadDoc,
    LawFirmDialog,
    textSpan,
    orgTree,
    SimpleBoardTitle,
    ProcessOpinion,
    SimpleBoardTitleApproval
  },
  computed: {
    ...mapGetters(['orgContext']),
    editDisabled() {
      return this.mainData.dataStateCode === this.utils.dataState_BPM.SAVE.code || this.mainData.dataStateCode === this.utils.dataState_BPM.STARTING.code || this.mainData.dataStateCode === this.utils.dataState_BPM.BACK.code
    },
    isView: function () {
      return this.dataState === this.utils.formState.VIEW
    },
    failIds: {
      set: function (data) {
        this.mainData.failId = data.join(',')
      },
      get: function () {
        if (this.mainData.failId) {
          return this.mainData.failId.split(',')
        }
        return []
      }
    },
  },
  data() {
    return {
      type: null,
      dataState: null,//表单状态，新增、查看、编辑
      functionId: null,//终止的时候要用，需要手动关闭
      typeCode: null,
      typeName: null,
      tableData: [],
      deptOrgVisible: false,
      rules: {
        outTypeName: [{required: true, message: '请选择出库类型', trigger: 'blur'}]
      },
      // 审批表数据
      mainData: {
        id: this.utils.createUUID(), // id
        itemName: null, // 事项名称
        noticeDeptName: null, //会知部门名称
        noticeDeptId: null, //会知部门id
        sequenceCode: null, // 流水号
        outTypeName: null, // 出库类型name
        failId: null, // 淘汰原因ID
        description: null, // 出库说明
        attachment: null, // 依据资料
        updateTime: null, // 更新时间
        typeCode: null,
        typeName: null,
        dataState: this.utils.dataState_BPM.SAVE.name, // 状态 新增时默认是已保存
        dataStateCode: this.utils.dataState_BPM.SAVE.code, // 状态编码
        createOgnName: null, //  经办单位
        createDeptName: null, // 经办部门
        createPsnName: null, // 经办人员
        createTime: null, // 经办时间
        createOgnId: null,
        createDeptId: null,
        createPsnId: null,
        createPsnFullId: null,
        createPsnFullName: null,
        createGroupId: null,
        createGroupName: null,
        createOrgId: null,
        createOrgName: null,
        lawFirmOutList: [],
        fileId: null, // 手写签批id
      },
      failData: [],
      docURL: '/lawyerOut',
      zxcheckedData: [],
      showUser: false,
      is_Check: true,
      isCheckedUser: false,
      orgVisible: false,
      LawFirmVisible: false,
      LawFirmSelectState: this.utils.dataState_LAWYER.YRK.name,
      LawTypeSelectCode: "0",
      hasAdd: true,
    }
  },
  created() {
    this.isHeadquarter()
    this.baseDataLoad()
  },
  methods: {
    initData(temp, dataState) {
      this.isHeadquarter()
      temp.typeName = this.typeName
      temp.typeCode = this.typeCode

      this.dataState = dataState

      Object.assign(this.mainData, temp)
      this.view = 'old'

      this.mainData.outTypeId = '2'
      this.mainData.outTypeName = '淘汰出库'
      this.dataState = dataState
      let year = new Date().getFullYear()
      this.utils.createKvsequence('LSCK' + year, 6).then(value => {
        this.mainData.sequenceCode = value.data.kvsequence
      })
    },
    // 根据数据ID加载数据
    loadData(dataState, dataId) {
      this.dataState = dataState
      this.functionId = this.$route.query.functionId
      if (this.$route.query.view !== undefined && this.$route.query.view !== '')
        this.view = this.$route.query.view
      if (this.$route.query.type !== undefined && this.$route.query.type !== '')
        this.type = this.$route.query.type
      lawyerFirmApi.queryDataById({id: dataId}).then(res => {
        this.mainData = res.data.data
        this.loading = false
      })
    },
    approvalDisabled() {
      return this.mainData.dataStateCode !== this.utils.dataState_BPM.SAVE.code && this.mainData.dataStateCode !== this.utils.dataState_BPM.STARTING.code
    },
    save() {
      if (this.mainData.lawFirmOutList.length > 0) {
        return new Promise((resolve, reject) => {
          this.mainData.updateTime = new Date()
          lawyerFirmApi.save(this.mainData).then(response => {
            resolve(response)
          }).catch(error => {
            reject(error)
          })
        })
      } else {
        this.$message.error('请添加出库律所！')
      }
    },
    submit() {
      this.$refs['dataForm'].validate(() => {
        this.save().then(() => {
          this.$emit('submit-success', this.mainData, 'id')
          this.$message.success('保存成功!')
        })
      })
    },
    outTypeChange(newVal) {
      this.mainData.outTypeName = this.utils.getDicName(this.utils.out_type_data, newVal)
    },
    failChange(newVal) {
      if (newVal && newVal.length > 0) {
        const arr = []
        for (let i = 0; i < newVal.length; i++) {
          const newValElement = newVal[i]
          const name = this.utils.getDicName(this.failData, newValElement)
          arr.push(name)
        }
        this.mainData.failName = arr.join('、')
      }
    },
    baseDataLoad() {
      dictApi.showSelect({
        dicCode: 'LS-CKYY'
      }).then(response => {
        this.failData = response.data.data
      })
    },
    addLawyer() {
      this.LawFirmVisible = true
    },
    look(index, row) {
      const tabId = this.utils.createUUID()
      this.layout.openNewTab("律所信息", "firm_ledger_law_firm", "firm_ledger_law_firm", tabId, {
        source: 'ledger', ...this.utils.routeState.VIEW(row.lawyerFirmId),
        tabId: tabId
      })
    },
    deleteRow(index) {
      this.mainData.lawFirmOutList.splice(index, 1)
    },
    lawFirmSure(val) {
      if (val && val.length > 0) {
        for (let i = 0; i < val.length; i++) {
          const valElement = val[i]
          const array = this.mainData.lawFirmOutList
          let bool = false
          for (let j = 0; j < array.length; j++) {
            const argument = array[j]
            if (argument.lawyerFirmId === valElement.id) {
              bool = true
              break
            }
          }
          if (!bool) {
            this.lawFirmCopy(valElement)
            this.mainData.lawFirmOutList.push(this.detailRow)
          }
        }
      }
      this.LawFirmVisible = false
    },
    lawFirmCopy(other) {
      const orgutil = this.orgContext
      this.detailRow = {
        id: null,
        lawyerFirm: null, // 律所名称
        lawyerFirmId: null, // 律所ID
        registerAddress: null, // 注册地址/律所地址
        functionary: null, // 负责人
        contactPerson: null, // 联系人
        phone: null, // 联系电话
        fas: null, // 传真
        email: null, // 邮箱
        postalCode: null, // 邮政编码
        issuingAuthority: null, // 主管机关
        licenseCode: null, // 统一社会信用代码
        licenseNumber: null, // 批准文号
        registeredCapital: null, // 设立资产
        foundTime: null, // 成立时间
        workNumber: null, // 执业人数
        applyDeptName: null, // 申请部门/所属公司
        applyDeptId: null, // 申请部门/所属公司
        annualInspectionName: null, // 年检情况
        annualInspectionId: null, // 年检情况
        noResponseTimes: null, // 未响应次数
        whetherMy: null, // 是否占住（yes-占住；no-未占住）
        parentId: null, // 父ID
        beGoodAtDomain: null, // 擅长领域
        beGoodAtDomainIds: null, // 擅长领域
        createOgnId: null,
        createOgnName: null,
        createDeptId: null,
        createDeptName: null,
        createPsnId: null,
        createPsnName: null,
        createPsnFullId: null,
        createPsnFullName: null,
        createGroupId: null,
        createGroupName: null,
        createOrgId: null,
        createOrgName: null,
        createTime: null,
        updateTime: null,
        typeCode: null,
        typeName: null
      }
      this.detailRow = {
        id: this.utils.createUUID(),
        lawyerFirm: other.lawyerFirm, // 律所名称
        lawyerFirmId: other.id, // 律所ID
        registerAddress: other.registerAddress, // 注册地址/律所地址
        functionary: other.functionary, // 负责人
        contactPerson: other.contactPerson, // 联系人
        phone: other.phone, // 联系电话
        fas: other.fas, // 传真
        email: other.email, // 邮箱
        postalCode: other.postalCode, // 邮政编码
        issuingAuthority: other.issuingAuthority, // 主管机关
        licenseCode: other.licenseCode, // 统一社会信用代码
        licenseNumber: other.licenseNumber, // 批准文号
        registeredCapital: other.registeredCapital, // 设立资产
        foundTime: other.foundTime, // 成立时间
        workNumber: other.workNumber, // 执业人数
        applyDeptName: other.applyDeptName, // 申请部门/所属公司
        applyDeptId: other.applyDeptId, // 申请部门/所属公司
        annualInspectionName: other.annualInspectionName, // 年检情况
        annualInspectionId: other.annualInspectionId, // 年检情况
        noResponseTimes: other.noResponseTimes, // 未响应次数
        whetherMy: 'no', // 是否占住（yes-占住；no-未占住）
        parentId: this.mainData.id, // 父ID
        beGoodAtDomain: other.beGoodAtDomain, // 擅长领域
        beGoodAtDomainIds: other.beGoodAtDomainIds, // 擅长领域
        createOgnId: orgutil.currentOgnId,
        createOgnName: orgutil.currentOgnName,
        createDeptId: orgutil.currentDeptId,
        createDeptName: orgutil.currentDeptName,
        createPsnId: orgutil.currentPsnId,
        createPsnName: orgutil.currentPsnName,
        createPsnFullId: orgutil.currentPsnFullId,
        createPsnFullName: orgutil.currentPsnFullName,
        createGroupId: orgutil.currentGroupId,
        createGroupName: orgutil.currentGroupName,
        createOrgId: orgutil.currentOrgId,
        createOrgName: orgutil.currentOrgName,
        createTime: new Date(),
        updateTime: new Date()
      }
    },
    cancel_() {
      this.orgVisible = false
    },
    choiceDeptSure_() {
      let c = '';
      let cid = '';
      this.zxcheckedData.forEach((item) => {
        if (c.length === 0) {
          c = c + item.orgName
          cid = cid + item.id
        } else {
          c = c + ',' + item.orgName
          cid = cid + ',' + item.id
        }
      })
      this.mainData.applyDeptName = c
      this.mainData.applyDeptId = cid
      this.orgVisible = false
    },
    isHeadquarter() {
      const createOgnId = this.orgContext.currentOgnId

      // if (createOgnId === '15033708970596') {
      this.typeCode = '1'
      this.typeName = '推荐库'
      // } else {
      //   this.typeCode = '0'
      //   this.typeName = '资源库'
      // }
    },
    approval_() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.save().then(() => {
            const tabId = this.mainData.id
            if (this.mainData.dataStateCode === this.utils.dataState_BPM.SAVE.code) {
              taskApi.selectFunctionId({functionCode: 'firm_out_main'}).then(res => {
                const functionId = res.data.data[0].ID
                this.layout.openNewTab("入库信息",
                    "design_page",
                    functionId,
                    tabId,
                    {
                      ...this.utils.routeState.NEW(tabId),
                      functionId: functionId,
                      businessKey: tabId,
                      entranceType: "FLOWABLE",
                      create: 'create',
                      view: 'new'
                    }
                )
              })
            } else {
              const tabId = this.mainData.id
              taskApi.selectTaskId({businessKey: tabId, isView: ''}).then(res => {
                const functionId = res.data.data[0].ID
                const uuid = this.utils.createUUID()
                this.layout.openNewTab("出库信息",
                    "design_page",
                    "design_page",
                    uuid,
                    {
                      processInstanceId: res.data.data[0].PID,//流程实例
                      taskId: res.data.data[0].ID,//任务ID
                      businessKey: tabId, //业务数据ID
                      functionId: functionId, //不传也可以，只有首环节需要传，实例启动就可以不传了
                      entranceType: "FLOWABLE",
                      type: "toDeal",
                      view: 'new',
                      create: 'create',
                    }
                )
              })
            }
          }).then(() => {
            this.mcpLayout.closeTab()
          })
        }
      })
    },
    save_() {
      this.save().then(() => {
        this.$message.success("保存成功!");
      });
    },
    chooseNoticeDeptClick() {
      this.deptOrgVisible = true
    },
    deptOrgCancel() {
      this.deptOrgVisible = false
    },
    choiceNoticeDeptSure() {
      let c = ''
      let cid = ''
      this.zxcheckedData.forEach((item) => {
        if (c.length === 0) {
          c = c + item.name
          cid = cid + item.unitId
        } else {
          c = c + ',' + item.name
          cid = cid + ',' + item.unitId
        }
      })
      this.mainData.noticeDeptName = c
      this.mainData.noticeDeptId = cid
      this.deptOrgVisible = false
    },
  }
}
</script>

<style scoped>

.el-dialog-div {
  height: 60vh;
  overflow: auto;
}

.rowCol1 .el-form-item {
  margin-bottom: 0 !important;
}
</style>
