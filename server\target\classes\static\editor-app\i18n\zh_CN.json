{"HEADER.BRAND": "Activiti 编辑器", "HEADER.BRAND_TAGLINE": "powered by Alfresco", "PAGE.HEADER": "业务流程细节", "ACTION.OK": "Ok", "ACTION.SAVE": "保存", "ACTION.SAVE-AND-CLOSE": "保存并关闭", "ACTION.SEND": "发送", "ACTION.CANCEL": "取消", "ACTION.SELECT": "选择", "ACTION.ADD": "和", "ACTION.REMOVE": "删除", "ACTION.MOVE.UP": "Move entry up", "ACTION.MOVE.DOWN": "Move entry down", "MAIN_NAVIGATION_ORCHESTRATIONS": "业务流程", "MAIN_NAVIGATION_DISPATCH_RULES": "分配规则", "MAIN_NAVIGATION_ASSET_GROUPS": "维护组", "MAIN_NAVIGATION_SOLUTIONS": "解决方案", "TOOLBAR.ACTION.CLOSE": "关闭编辑器，然后返回到首页", "TOOLBAR.ACTION.SAVE": "保存模型", "TOOLBAR.ACTION.VALIDATE": "验证模型", "TOOLBAR.ACTION.CUT": "剪切（选择一个或多个元素）", "TOOLBAR.ACTION.COPY": "拷贝（选择一个或多个元素）", "TOOLBAR.ACTION.PASTE": "粘贴", "TOOLBAR.ACTION.DELETE": "删除选定的元素", "TOOLBAR.ACTION.UNDO": "撤消", "TOOLBAR.ACTION.REDO": "重做", "TOOLBAR.ACTION.ZOOMIN": "放大", "TOOLBAR.ACTION.ZOOMOUT": "缩小", "TOOLBAR.ACTION.ZOOMACTUAL": "缩放到实际大小", "TOOLBAR.ACTION.ZOOMFIT": "缩放到适合大小", "TOOLBAR.ACTION.MOVE": "移动", "TOOLBAR.ACTION.IMPORT": "导入", "TOOLBAR.ACTION.EXPORT": "导出", "TOOLBAR.ACTION.BENDPOINT.ADD": "添加节点", "TOOLBAR.ACTION.BENDPOINT.REMOVE": "移除节点", "TOOLBAR.ACTION.SWITCH": "切换到旧版", "TOOLBAR.ACTION.ALIGNHORIZONTAL": "水平对齐", "TOOLBAR.ACTION.ALIGNVERTICAL": "垂直对齐", "TOOLBAR.ACTION.SAMESIZE": "相同尺寸", "TOOLBAR.ACTION.HELP": "开始导游", "TOOLBAR.ACTION.FEEDBACK": "提供反馈", "KICKSTART.PROCESS_TOOLBAR.ACTION.SAVE": "保存当前模型", "KICKSTART.PROCESS_TOOLBAR.ACTION.VALIDATE": "验证当前模型", "KICKSTART.PROCESS_TOOLBAR.ACTION.HELP": "开始导游", "KICKSTART.PROCESS_TOOLBAR.ACTION.FEEDBACK": "提供反馈", "FORM_TOOLBAR.ACTION.SAVE": "保存当前模型", "FORM_TOOLBAR.ACTION.VALIDATE": "验证当前模型", "FORM_TOOLBAR.ACTION.HELP": "开始导游", "FORM_TOOLBAR.ACTION.FEEDBACK": "提供反馈", "APP_DEFINITION_TOOLBAR.ACTION.SAVE": "保存定义的应用程序", "APP_DEFINITION_TOOLBAR.ACTION.VALIDATE": "验证定义的应用程序", "APP_DEFINITION_TOOLBAR.ACTION.HELP": "开始导游", "APP_DEFINITION_TOOLBAR.ACTION.FEEDBACK": "提供反馈", "BUTTON.ACTION.DELETE.TOOLTIP": "从模型中删除元素", "BUTTON.ACTION.MORPH.TOOLTIP": "改变元素类型", "ELEMENT.AUTHOR": "作者", "ELEMENT.DATE_CREATED": "创建日期", "ELEMENT.SELECTED_EMPTY_TITLE": "(No name)", "PROPERTY.REMOVED": "移除的", "PROPERTY.EMPTY": "没有值", "PROPERTY.PROPERTY.EDIT.TITLE": "更改 \"{{title}}\"", "PROPERTY.FEEDBACK.TITLE": "请填写你的反馈", "PROPERTY.ASSIGNMENT.TITLE": "分配", "PROPERTY.ASSIGNMENT.TYPE": "类型", "PROPERTY.ASSIGNMENT.TYPE.IDENTITYSTORE": "Identity store", "PROPERTY.ASSIGNMENT.TYPE.STATIC": "静态值", "PROPERTY.ASSIGNMENT.ASSIGNEE": "指派对象", "PROPERTY.ASSIGNMENT.MATCHING": "使用 &uparrow; 和 &downarrow; 选择并按回车键确认或使用鼠标", "PROPERTY.ASSIGNMENT.ASSIGNEE_PLACEHOLDER": "输入一个指派对象", "PROPERTY.ASSIGNMENT.EMPTY": "没有选中指派对象", "PROPERTY.ASSIGNMENT.ASSIGNEE_DISPLAY": "指派 {{assignee}}", "PROPERTY.ASSIGNMENT.CANDIDATE_USERS_DISPLAY": "{{length}} 候选用户", "PROPERTY.ASSIGNMENT.CANDIDATE_USERS": "候选用户", "PROPERTY.ASSIGNMENT.CANDIDATE_GROUPS_DISPLAY": "{{length}} 候选组", "PROPERTY.ASSIGNMENT.CANDIDATE_GROUPS": "候选组", "PROPERTY.ASSIGNMENT.USER_IDM_DISPLAY": "用户 {{firstName}} {{lastName}}", "PROPERTY.ASSIGNMENT.USER_IDM_EMAIL_DISPLAY": "用户 {{email}}", "PROPERTY.ASSIGNMENT.IDM_EMPTY": "流程发起人", "PROPERTY.ASSIGNMENT.IDM.TYPE": "分配", "PROPERTY.ASSIGNMENT.IDM.NO_CANDIDATE_USERS": "没有选择候选用户...", "PROPERTY.ASSIGNMENT.IDM.NO_CANDIDATE_GROUPS": "没有选择候选组...", "PROPERTY.ASSIGNMENT.IDM.DROPDOWN.INITIATOR": "分配给进程发起人", "PROPERTY.ASSIGNMENT.IDM.DROPDOWN.USER": "分配给单个用户", "PROPERTY.ASSIGNMENT.IDM.DROPDOWN.USERS": "候选用户", "PROPERTY.ASSIGNMENT.IDM.DROPDOWN.GROUPS": "候选组", "PROPERTY.ASSIGNMENT.EMAIL.HELP": "输入电子邮件地址并按回车键继续", "PROPERTY.EXECUTIONLISTENERS.DISPLAY": "{{length}} 执行监听器", "PROPERTY.EXECUTIONLISTENERS.EMPTY": "没有配置执行监听器", "PROPERTY.EXECUTIONLISTENERS.EVENT": "事件", "PROPERTY.EXECUTIONLISTENERS.CLASS": "类", "PROPERTY.EXECUTIONLISTENERS.CLASS.PLACEHOLDER": "输入类名", "PROPERTY.EXECUTIONLISTENERS.EXPRESSION": "表达式", "PROPERTY.EXECUTIONLISTENERS.EXPRESSION.PLACEHOLDER": "输入一个表达式", "PROPERTY.EXECUTIONLISTENERS.DELEGATEEXPRESSION": "代理表达式", "PROPERTY.EXECUTIONLISTENERS.DELEGATEEXPRESSION.PLACEHOLDER": "输入一个代理表达式", "PROPERTY.EXECUTIONLISTENERS.UNSELECTED": "没有选择执行监听器", "PROPERTY.EXECUTIONLISTENERS.FIELDS.NAME": "字段名", "PROPERTY.EXECUTIONLISTENERS.FIELDS.NAME.PLACEHOLDER": "输入一个字段名", "PROPERTY.EXECUTIONLISTENERS.FIELDS.EXPRESSION": "表达式", "PROPERTY.EXECUTIONLISTENERS.FIELDS.EXPRESSION.PLACEHOLDER": "输入一个表达式", "PROPERTY.EXECUTIONLISTENERS.FIELDS.STRINGVALUE": "字符串的值", "PROPERTY.EXECUTIONLISTENERS.FIELDS.STRINGVALUE.PLACEHOLDER": "输入一个字符串的值", "PROPERTY.EXECUTIONLISTENERS.FIELDS.STRING": "字符串", "PROPERTY.EXECUTIONLISTENERS.FIELDS.STRING.PLACEHOLDER": "输入一个字符串", "PROPERTY.EXECUTIONLISTENERS.FIELDS.IMPLEMENTATION": "值", "PROPERTY.EXECUTIONLISTENERS.FIELDS.EMPTY": "没有选中字段", "PROPERTY.FIELDS": "{{length}} 字段", "PROPERTY.FIELDS.EMPTY": "没有选择字段", "PROPERTY.FIELDS.NAME": "字段名", "PROPERTY.FIELDS.NAME.PLACEHOLDER": "输入一个字段名", "PROPERTY.FIELDS.EXPRESSION": "表达式", "PROPERTY.FIELDS.EXPRESSION.PLACEHOLDER": "输入一个表达式", "PROPERTY.FIELDS.STRINGVALUE": "字符串的值", "PROPERTY.FIELDS.STRINGVALUE.PLACEHOLDER": "输入一个字符串的值", "PROPERTY.FIELDS.STRING": "字符串", "PROPERTY.FIELDS.STRING.PLACEHOLDER": "输入一个字符串", "PROPERTY.FIELDS.IMPLEMENTATION": "值", "PROPERTY.FIELDS.UNSELECTED": "没有选中字段", "PROPERTY.FORMPROPERTIES.VALUE": "{{length}} 表单属性", "PROPERTY.FORMPROPERTIES.EMPTY": "没有选中表单属性", "PROPERTY.FORMPROPERTIES.ID": "Id", "PROPERTY.FORMPROPERTIES.ID.PLACEHOLDER": "输入一个表单 id", "PROPERTY.FORMPROPERTIES.NAME": "表单名", "PROPERTY.FORMPROPERTIES.NAME.PLACEHOLDER": "输入一个表单名", "PROPERTY.FORMPROPERTIES.TYPE": "类型", "PROPERTY.FORMPROPERTIES.DATEPATTERN": "日期模式", "PROPERTY.FORMPROPERTIES.DATEPATTERN.PLACEHOLDER": "输入一个日期模式", "PROPERTY.FORMPROPERTIES.VALUES": "值", "PROPERTY.FORMPROPERTIES.ENUMVALUES.EMPTY": "没有选中的枚举值", "PROPERTY.FORMPROPERTIES.VALUES.ID": "Id", "PROPERTY.FORMPROPERTIES.VALUES.NAME": "名称", "PROPERTY.FORMPROPERTIES.VALUES.ID.PLACEHOLDER": "输入一个 id", "PROPERTY.FORMPROPERTIES.VALUES.NAME.PLACEHOLDER": "输入一个名称", "PROPERTY.FORMPROPERTIES.EXPRESSION": "表达式", "PROPERTY.FORMPROPERTIES.EXPRESSION.PLACEHOLDER": "输入一个表达式", "PROPERTY.FORMPROPERTIES.VARIABLE": "变量", "PROPERTY.FORMPROPERTIES.VARIABLE.PLACEHOLDER": "输入一个变量", "PROPERTY.FORMPROPERTIES.REQUIRED": "必需", "PROPERTY.FORMPROPERTIES.READABLE": "可读", "PROPERTY.FORMPROPERTIES.WRITABLE": "可写", "PROPERTY.INPARAMETERS.VALUE": "{{length}} 输入参数", "PROPERTY.INPARAMETERS.EMPTY": "没有配置输入参数", "PROPERTY.OUTPARAMETERS.VALUE": "{{length}} out-parameters", "PROPERTY.OUTPARAMETERS.EMPTY": "没有配置输出参数", "PROPERTY.PARAMETER.SOURCE": "源码", "PROPERTY.PARAMETER.SOURCE.PLACEHOLDER": "出入一段源码", "PROPERTY.PARAMETER.SOURCEEXPRESSION": "源码表达式", "PROPERTY.PARAMETER.SOURCEEXPRESSION.PLACEHOLDER": "输入源码表达式", "PROPERTY.PARAMETER.TARGET": "目标", "PROPERTY.PARAMETER.TARGET.PLACEHOLDER": "输入一个目标", "PROPERTY.PARAMETER.EMPTY": "没有选中参数", "PROPERTY.SUBPROCESSREFERENCE.EMPTY": "没有选中子过程引用", "PROPERTY.SUBPROCESSREFERENCE.TITLE": "Collapsed subprocess reference", "PROPERTY.SUBPROCESSREFERENCE.ERROR.SUBPROCESS": "加载子过程时出现错误. 请稍后再试", "PROPERTY.SUBPROCESSREFERENCE.FOLDER.ROOT": "文件夹", "PROPERTY.SUBPROCESSREFERENCE.FOLDER.LOADING": "加载文件夹中...", "PROPERTY.SUBPROCESSREFERENCE.FOLDER.EMPTY": "该文件夹没有子文件夹", "PROPERTY.SUBPROCESSREFERENCE.SUBPROCESS.LOADING": "加载子过程中...", "PROPERTY.SUBPROCESSREFERENCE.SUBPROCESS.EMPTY": "该文件夹下没有子过程", "PROPERTY.FORMREFERENCE.EMPTY": "没有选中表单引用", "PROPERTY.FORMREFERENCE.TITLE": "表单序列", "PROPERTY.FORMREFERENCE.ERROR.FORM": "加载表单时出现错误. 请稍后再试", "PROPERTY.FORMREFERENCE.FOLDER.ROOT": "文件夹", "PROPERTY.FORMREFERENCE.FOLDER.LOADING": "加载文件夹中...", "PROPERTY.FORMREFERENCE.FOLDER.EMPTY": "该文件夹没有子文件夹", "PROPERTY.FORMREFERENCE.FORM.LOADING": "加载表单中...", "PROPERTY.FORMREFERENCE.FORM.EMPTY": "该文件夹没有表单", "PROPERTY.TASKLISTENERS.VALUE": "{{length}} 任务监听器", "PROPERTY.TASKLISTENERS.EMPTY": "没有配置任务监听器", "PROPERTY.TASKLISTENERS.EVENT": "事件", "PROPERTY.TASKLISTENERS.CLASS": "类", "PROPERTY.TASKLISTENERS.CLASS.PLACEHOLDER": "输入一个类名", "PROPERTY.TASKLISTENERS.EXPRESSION": "表达式", "PROPERTY.TASKLISTENERS.EXPRESSION.PLACEHOLDER": "输入一个表达式", "PROPERTY.TASKLISTENERS.DELEGATEEXPRESSION": "Delegate expression", "PROPERTY.TASKLISTENERS.DELEGATEEXPRESSION.PLACEHOLDER": "Enter a delegate expression", "PROPERTY.TASKLISTENERS.UNSELECTED": "没有选中任务监听器", "PROPERTY.TASKLISTENERS.FIELDS.NAME": "名称", "PROPERTY.TASKLISTENERS.FIELDS.NAME.PLACEHOLDER": "输入一个名称", "PROPERTY.TASKLISTENERS.FIELDS.EXPRESSION": "表达式", "PROPERTY.TASKLISTENERS.FIELDS.EXPRESSION.PLACEHOLDER": "输入一个表达式", "PROPERTY.TASKLISTENERS.FIELDS.STRINGVALUE": "字符串值", "PROPERTY.TASKLISTENERS.FIELDS.STRINGVALUE.PLACEHOLDER": "输入一个字符串值", "PROPERTY.TASKLISTENERS.FIELDS.STRING": "字符串", "PROPERTY.TASKLISTENERS.FIELDS.STRING.PLACEHOLDER": "输入一个字符串", "PROPERTY.TASKLISTENERS.FIELDS.IMPLEMENTATION": "值", "PROPERTY.TASKLISTENERS.FIELDS.EMPTY": "没有选中字段", "PROPERTY.EVENTLISTENERS.DISPLAY": "{{length}} 事件监听器", "PROPERTY.EVENTLISTENERS.EMPTY": "没有配置事件监听器", "PROPERTY.EVENTLISTENERS.EVENTS": "事件", "PROPERTY.EVENTLISTENERS.RETHROW": "再次触发事件?", "PROPERTY.EVENTLISTENERS.CLASS": "类", "PROPERTY.EVENTLISTENERS.CLASS.PLACEHOLDER": "输入一个类名", "PROPERTY.EVENTLISTENERS.DELEGATEEXPRESSION": "Delegate 表达式", "PROPERTY.EVENTLISTENERS.DELEGATEEXPRESSION.PLACEHOLDER": "输入一个 delegate 表达式", "PROPERTY.EVENTLISTENERS.ENTITYTYPE": "实体类型", "PROPERTY.EVENTLISTENERS.ENTITYTYPE.PLACEHOLDER": "输入一个实体类型", "PROPERTY.EVENTLISTENERS.RETHROWTYPE": "触发的事件类型", "PROPERTY.EVENTLISTENERS.ERRORCODE": "错误码", "PROPERTY.EVENTLISTENERS.ERRORCODE.PLACEHOLDER": "输入一个错误码", "PROPERTY.EVENTLISTENERS.MESSAGENAME": "消息名", "PROPERTY.EVENTLISTENERS.MESSAGENAME.PLACEHOLDER": "输入一个消息名", "PROPERTY.EVENTLISTENERS.SIGNALNAME": "信号名", "PROPERTY.EVENTLISTENERS.SIGNALNAME.PLACEHOLDER": "输入一个信号名", "PROPERTY.EVENTLISTENERS.UNSELECTED": "没有选中事件监听器", "PROPERTY.SIGNALDEFINITIONS.DISPLAY": "{{length}} 信号定义", "PROPERTY.SIGNALDEFINITIONS.EMPTY": "没有配置信号定义", "PROPERTY.SIGNALDEFINITIONS.SCOPE-GLOBAL": "全局", "PROPERTY.SIGNALDEFINITIONS.SCOPE-PROCESSINSTANCE": "过程实例", "PROPERTY.SIGNALDEFINITIONS.ID": "Id", "PROPERTY.SIGNALDEFINITIONS.NAME": "名称", "PROPERTY.SIGNALDEFINITIONS.SCOPE": "作用域", "PROPERTY.MESSAGEDEFINITIONS.DISPLAY": "{{length}} 消息定义", "PROPERTY.MESSAGEDEFINITIONS.EMPTY": "没有配置消息定义", "PROPERTY.MESSAGEDEFINITIONS.ID": "消息 Id", "PROPERTY.MESSAGEDEFINITIONS.NAME": "消息名", "PROPERTY.SEQUENCEFLOW.ORDER.EMPTY": "No sequence flow order determined", "PROPERTY.SEQUENCEFLOW.ORDER.NOT.EMPTY": "Sequence flow order set", "PROPERTY.SEQUENCEFLOW.ORDER.NO.OUTGOING.SEQUENCEFLOW.FOUND": "No outgoing sequence flow found.", "PROPERTY.SEQUENCEFLOW.ORDER.DESCRIPTION": "Set the order in which the sequence flow need to be evaluated:", "PROPERTY.SEQUENCEFLOW.ORDER.SEQUENCEFLOW.VALUE": "Sequence flow to {{targetType}} {{targetTitle}}", "PROPERTY.SEQUENCEFLOW.CONDITION.TITLE": "工作流条件", "PROPERTY.SEQUENCEFLOW.CONDITION.TYPE.TITLE": "条件类型", "PROPERTY.SEQUENCEFLOW.CONDITION.TYPE.VARIABLE": "选择变量", "PROPERTY.SEQUENCEFLOW.CONDITION.TYPE.STATIC": "静态值", "PROPERTY.SEQUENCEFLOW.EASY.CONDITION.STATIC": "简单表达式", "PROPERTY.SEQUENCEFLOW.CONDITION.STATIC": "条件表达式", "PROPERTY.SEQUENCEFLOW.CONDITION.STATIC_PLACEHOLDER": "表达式填充", "PROPERTY.SEQUENCEFLOW.CONDITION.VARIABLE.TYPE": "变量类型", "PROPERTY.SEQUENCEFLOW.CONDITION.VARIABLE.NO-CONDITION": "没有条件", "PROPERTY.SEQUENCEFLOW.CONDITION.VARIABLE.FORM-FIELD": "表单字段", "PROPERTY.SEQUENCEFLOW.CONDITION.VARIABLE.FORM-OUTCOME": "表单输出", "PROPERTY.SEQUENCEFLOW.CONDITION.VARIABLE.SELECT-FIELD": "选择字段", "PROPERTY.SEQUENCEFLOW.CONDITION.VARIABLE.NO-FIELDS-AVAILABLE": "没有找到字段", "PROPERTY.SEQUENCEFLOW.CONDITION.VARIABLE.SELECT-FORM": "选择表单", "PROPERTY.SEQUENCEFLOW.CONDITION.VARIABLE.NO-FORMS-AVAILABLE": "没有找到表单", "PROPERTY.SEQUENCEFLOW.CONDITION.VARIABLE.SELECT-OPERATOR": "选择操作", "PROPERTY.SEQUENCEFLOW.CONDITION.VARIABLE.EQUALS": "相等", "PROPERTY.SEQUENCEFLOW.CONDITION.VARIABLE.NOTEQUALS": "不相等", "PROPERTY.SEQUENCEFLOW.CONDITION.VARIABLE.LESSTHAN": "小于", "PROPERTY.SEQUENCEFLOW.CONDITION.VARIABLE.GREATERTHAN": "大于", "PROPERTY.SEQUENCEFLOW.CONDITION.VARIABLE.SELECT-OUTCOME": "选择输出结果", "PROPERTY.SEQUENCEFLOW.CONDITION.VARIABLE.NO-OUTCOMES-AVAILABLE": "没有找到输出结果", "PROPERTY.SEQUENCEFLOW.CONDITION.NO-CONDITION-DISPLAY": "没有条件", "PROPERTY.BUSINESS.VARIABLES.LIST.EMPTY": "没有配置业务变量", "PROPERTY.BUSINESS.VARIABLES.LIST.DISPLAY": "{{length}} 业务变量", "MODEL.SAVE.TITLE": "保存模型", "MODEL.NAME": "名称", "MODEL.DESCRIPTION": "描述", "MODEL.SAVE.NEWVERSION": "保存这个作为一个新版本？这意味着你可以总是回到以前的版本", "MODEL.SAVE.COMMENT": "评论", "MODEL.SAVE.SAVING": "保存模型", "MODEL.LASTMODIFIEDDATE": "上次保存的", "MODEL.SAVE.ERROR": "未知错误发生: 无法保存模型", "EVENT_TYPE.ACTIVITY.COMPENSATE.TOOLTIP": "An activity is about to be executed as a compensation for another activity. The event targets the activity that is about to be executed for compensation", "EVENT_TYPE.ACTIVITY.COMPLETED.TOOLTIP": "An activity has been completed successfully", "EVENT_TYPE.ACTIVITY.ERROR.RECEIVED.TOOLTIP": "An activity has received an error event. Dispatched before the actual error has been received by the activity", "EVENT_TYPE.MEMBERSHIP.CREATED.TOOLTIP": "A new membership has been created", "EVENT_TYPE.MEMBERSHIP.DELETED.TOOLTIP": "A single membership has been deleted", "EVENT_TYPE.MEMBERSHIPS.DELETED.TOOLTIP": "All memberships in the related group have been deleted. No individual events will be dispatched due to possible performance reasons", "EVENT_TYPE.TASK.ASSIGNED.TOOLTIP": "A task as been assigned. This is thrown alongside with an ENTITY_UPDATED event", "EVENT_TYPE.TASK.COMPLETED.TOOLTIP": "A task has been completed. Dispatched before the task entity is deleted", "EVENT_TYPE.UNCAUGHT.BPMNERROR.TOOLTIP": "When a BPMN Error was thrown, but was not caught within in the process", "EVENT_TYPE.VARIABLE.CREATED.TOOLTIP": "创建了一个新的变量", "EVENT_TYPE.VARIABLE.DELETED.TOOLTIP": "已删除现有变量", "EVENT_TYPE.VARIABLE.UPDATED.TOOLTIP": "已更新现有变量", "TOOLBAR.ACTION.PALETTE.COLLAPSED": "折叠面板", "TOOLBAR.ACTION.PROPERTY.COLLAPSED": "折叠属性"}