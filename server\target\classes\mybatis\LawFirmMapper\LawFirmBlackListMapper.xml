<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.klaw.dao.lawyerDao.LawFirmBlackListMapper">
    <resultMap id="BaseResultMap" type="com.klaw.entity.lawyerBean.LawFirmBlackList">
        <id column="ID" jdbcType="VARCHAR" property="id" />
        <result column="CREATE_OGN_ID" jdbcType="VARCHAR" property="createOgnId" />
        <result column="CREATE_OGN_NAME" jdbcType="VARCHAR" property="createOgnName" />
        <result column="CREATE_DEPT_ID" jdbcType="VARCHAR" property="createDeptId" />
        <result column="CREATE_DEPT_NAME" jdbcType="VARCHAR" property="createDeptName" />
        <result column="CREATE_GROUP_ID" jdbcType="VARCHAR" property="createGroupId" />
        <result column="CREATE_GROUP_NAME" jdbcType="VARCHAR" property="createGroupName" />
        <result column="CREATE_PSN_ID" jdbcType="VARCHAR" property="createPsnId" />
        <result column="CREATE_PSN_NAME" jdbcType="VARCHAR" property="createPsnName" />
        <result column="CREATE_ORG_ID" jdbcType="VARCHAR" property="createOrgId" />
        <result column="CREATE_ORG_NAME" jdbcType="VARCHAR" property="createOrgName" />
        <result column="CREATE_PSN_FULL_ID" jdbcType="VARCHAR" property="createPsnFullId" />
        <result column="CREATE_PSN_FULL_NAME" jdbcType="VARCHAR" property="createPsnFullName" />
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
        <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="DATA_STATE" jdbcType="VARCHAR" property="dataState" />
        <result column="DATA_STATE_CODE" jdbcType="DECIMAL" property="dataStateCode" />
        <result column="LAWYER_FIRM" jdbcType="VARCHAR" property="lawyerFirm" />
        <result column="REGISTER_ADDRESS" jdbcType="VARCHAR" property="registerAddress" />
        <result column="BLACKLIST_REASON" jdbcType="CLOB" property="blacklistReason" />
        <result column="FUNCTIONARY" jdbcType="VARCHAR" property="functionary" />
        <result column="CONTACT_PERSON" jdbcType="VARCHAR" property="contactPerson" />
        <result column="PHONE" jdbcType="VARCHAR" property="phone" />
        <result column="FAS" jdbcType="VARCHAR" property="fas" />
        <result column="EMAIL" jdbcType="VARCHAR" property="email" />
        <result column="POSTAL_CODE" jdbcType="VARCHAR" property="postalCode" />
        <result column="ISSUING_AUTHORITY" jdbcType="VARCHAR" property="issuingAuthority" />
        <result column="LICENSE_CODE" jdbcType="VARCHAR" property="licenseCode" />
        <result column="LICENSE_NUMBER" jdbcType="VARCHAR" property="licenseNumber" />
        <result column="REGISTERED_CAPITAL" jdbcType="VARCHAR" property="registeredCapital" />
        <result column="FOUND_TIME" jdbcType="TIMESTAMP" property="foundTime" />
        <result column="WORK_NUMBER" jdbcType="DECIMAL" property="workNumber" />
        <result column="APPLY_DEPT_NAME" jdbcType="VARCHAR" property="applyDeptName" />
        <result column="APPLY_DEPT_ID" jdbcType="VARCHAR" property="applyDeptId" />
        <result column="ANNUAL_INSPECTION_NAME" jdbcType="VARCHAR" property="annualInspectionName" />
        <result column="ANNUAL_INSPECTION_ID" jdbcType="VARCHAR" property="annualInspectionId" />
        <result column="INTRODUCTION" jdbcType="CLOB" property="introduction" />
        <result column="REMARK" jdbcType="CLOB" property="remark" />
        <result column="BUSINESS_LICENSE" jdbcType="CLOB" property="businessLicense" />
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        ID, CREATE_OGN_ID, CREATE_OGN_NAME, CREATE_DEPT_ID, CREATE_DEPT_NAME, CREATE_GROUP_ID,
        CREATE_GROUP_NAME, CREATE_PSN_ID, CREATE_PSN_NAME, CREATE_ORG_ID, CREATE_ORG_NAME,
        CREATE_PSN_FULL_ID, CREATE_PSN_FULL_NAME, CREATE_TIME, UPDATE_TIME, DATA_STATE, DATA_STATE_CODE,
        LAWYER_FIRM, REGISTER_ADDRESS, BLACKLIST_REASON, FUNCTIONARY, CONTACT_PERSON, PHONE,
        FAS, EMAIL, POSTAL_CODE, ISSUING_AUTHORITY, LICENSE_CODE, LICENSE_NUMBER, REGISTERED_CAPITAL,
        FOUND_TIME, WORK_NUMBER, APPLY_DEPT_NAME, APPLY_DEPT_ID, ANNUAL_INSPECTION_NAME,
        ANNUAL_INSPECTION_ID, INTRODUCTION, REMARK, BUSINESS_LICENSE
    </sql>

    <select id="queryDataByFuzzy" parameterType="java.util.Map" resultMap="BaseResultMap">
        select
            *
        from SG_LAW_FIRM_BLACKLIST
    </select>
</mapper>
