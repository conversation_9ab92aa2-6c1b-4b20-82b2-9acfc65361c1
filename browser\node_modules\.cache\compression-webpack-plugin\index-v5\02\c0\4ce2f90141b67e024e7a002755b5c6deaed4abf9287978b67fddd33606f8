
d007ce22180491d1a2fc2a0be89876984d85d26d	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.136.1754018536329.js\",\"contentHash\":\"fbb9255f4b3faa02aec4458bed8254a0\"}","integrity":"sha512-FVSrz+q8vKU8H6lT2NtvdMuQ3azeNAI61tgQQMPaB2sO3vlKCqSPFieebfP8LmI/CvZV2hi6oqWT3xhKZJFchw==","time":1754018575956,"size":37508}