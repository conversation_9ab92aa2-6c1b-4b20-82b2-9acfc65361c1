
0766c968dfb5416310224aa140011c0a5e716747	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.364.1754018536329.js\",\"contentHash\":\"e0133935a153a3b473ef956cac890003\"}","integrity":"sha512-PeAduT7R0WHCFLZzjjIbmbyOE3pv226+qyRp3WcqGlxfUl0fRjuEEUE9PY2Ck39r0uY03XLcsJITuowACCN9iA==","time":1754018575975,"size":95946}