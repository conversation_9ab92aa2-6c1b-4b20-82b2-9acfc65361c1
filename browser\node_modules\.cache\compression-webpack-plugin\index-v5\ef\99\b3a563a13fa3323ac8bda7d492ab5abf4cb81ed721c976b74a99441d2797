
88d39479597c8d7135192b9bbdced32ccb73ec58	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.118.1754018536329.js\",\"contentHash\":\"9c7ec7f16dbe23a048d69bf831a01da7\"}","integrity":"sha512-0rCVsrpt4YrfyOYMzq4w9bS5+MPn16lLbLNLEU0ccIDiCutIgMXnSQPKhWLCoKH5biAk7/qdrAa4jR/zIZ6UbA==","time":1754018575979,"size":170815}