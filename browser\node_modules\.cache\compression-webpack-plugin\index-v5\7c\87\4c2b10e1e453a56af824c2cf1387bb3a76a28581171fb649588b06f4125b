
d24eb43a284707921990a8e25373efe5ca191387	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.241.1754018536329.js\",\"contentHash\":\"e1209fc460224bcac4e5e3b5df890eef\"}","integrity":"sha512-UOmXARMHYR8UqktpARVFGOyH7iJDOQfGeTKEcne1a/I9G14G9shdl2h5TTCtqJmNt2n0xnEqNupRN1ktKVX5NQ==","time":1754018575996,"size":145654}