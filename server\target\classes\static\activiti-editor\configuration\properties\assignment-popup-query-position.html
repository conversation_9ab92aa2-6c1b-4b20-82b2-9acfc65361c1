<#--
* description: 岗位查询
* version: 3.0
* author:<EMAIL>
*
-->
<#include "../../../include/header.html">
<body>
<script>
    var viewModel = kendo.observable({
        model: {},
        queryResource: function (e) {
            $('#grid').data('kendoGrid').dataSource.page(1);
        },
        //将选择的员工信息传递到指派对象页面
        savePosition:function(){
            var checked = grid.selectedDataItems();
            if (checked.length==1){
                window.parent.fun(checked[0].positionCode)
            }else{
                kendo.ui.showInfoDialog({
                    message: $l('hap.tip.selectrow')
                })
            }
        }
    });

</script>

<div id="page-content"  >
    <div class="pull-left" id="toolbar-btn" style="padding-bottom:10px;">
        <span data-bind="click:savePosition" type="button" class="btn btn-primary k-grid-add"  style="float:left;margin-right:5px;" >确定</span>
    </div>
    <div class="pull-right" id="query-form" style="padding-bottom:10px;">
        <input data-role="maskedtextbox" placeholder='<@spring.message "position.positioncode"/>' type="text" style="width: 150px;float:left;margin-right:5px;" data-bind="value:model.positionCode" class="k-textbox">
        <input data-role="maskedtextbox" placeholder='<@spring.message "position.name"/>' type="text" style="width: 150px;float:left;margin-right:5px;" data-bind="value:model.name"
               class="k-textbox">
<span class="btn btn-primary" style="float:left;width:70px" data-bind="click:queryResource"
      type="submit"><i class="fa fa-search" style="margin-right:3px;"></i><@spring.message "hap.query"/></span>
        <div style="clear:both"></div>
    </div>
    <script>kendo.bind($('#page-content'), viewModel);</script>
    <div style="clear:both">
        <div id="grid"></div>
    </div>

</div>
<script>
    var crudServiceBaseUrl = '${base.contextPath}/hr/position',
            dataSource = new kendo.data.DataSource({
                transport: {
                    read: {
                        url: crudServiceBaseUrl + "/query",
                        type    : "POST",
                        dataType: "json"
                    },
                    parameterMap: function (options, type) {
                        if (type === "read") {
                            return Hap.prepareQueryParameter(viewModel.model.toJSON(), options);
                        }
                    }
                },
                batch: true,
                serverPaging: true,
                pageSize: 5,
                schema: {
                    data: 'rows',
                    total: 'total',
                    model: {
                        id: "positionId",
                        fields: {
                            positionCode: {type: "string"},
                            name: {type: "string"}
                        }
                    }
                }
            });

    grid = $("#grid").kendoGrid({
        dataSource: dataSource,
        navigatable: false,
        height: '280px',
        resizable: true,
        scrollable: true,
        selectable:'radio,rowbox',
        pageable: {
            pageSizes: [5, 10, 20, 50],
            refresh: true,
            buttonCount: 5
        },
        columns: [
            {
                field: "positionCode",
                title: '<@spring.message "position.positioncode"/>',
                width: 80
            },
            {
                field: "name",
                title: '<@spring.message "position.name"/>',
                width: 80
            }, {
                field: "description",
                title: '<@spring.message "position.description"/>',
                width: 120
            }],
        editable: false
    }).data("kendoGrid");
</script>
</body>
</html>