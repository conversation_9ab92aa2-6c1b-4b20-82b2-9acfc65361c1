import {request} from '@/api/index'

export default {

    query(data) {
        return request({
            url: '/projectMajorSummary/query',
            method: 'post',
            data
        })
    },

    save(data) {
        return request({
            url: '/projectMajorSummary/save',
            method: 'post',
            data
        })
    },
    getById(data) {
        return request({
            url: '/projectMajorSummary/getById',
            method: 'post',
            data: {
                id: data
            }
        })
    },
    getByReasoningId(data) {
        return request({
            url: '/projectMajorSummary/getByReasoningId',
            method: 'post',
            data: {
                id: data
            }
        })
    },
    deleteById(data) {
        return request({
            url: '/projectMajorSummary/deleteById',
            method: 'post',
            data
        })
    },


}

