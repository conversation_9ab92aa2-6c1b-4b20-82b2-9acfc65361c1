
607d9f747b8c6ccc1bf203d301af24d9f3809428	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.204.1754018536329.js\",\"contentHash\":\"55ff28170c4cdde9d8d19d58720ad714\"}","integrity":"sha512-TMDX/4rGfWQeeNPL4iZx/WV2IJiajdNyY/23NAezVYtiSgRkA0Cd/u8FNKZs14teUEIPvGLZ0P5BskWXG7iFDg==","time":1754018575960,"size":96950}