
959bbc811c01e6288fec8285f8e9c3d72699e0a1	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.137.1754018536329.js\",\"contentHash\":\"f2b28986a4c680b77cca3e49b7a05e49\"}","integrity":"sha512-tEZImuXdvD062wibhcCktMnW1/o+4+0P9X1reMxiedVXQQcFe3H4zOAFTw1GnVGjdKYt7poWO9SxaUB5mohpxQ==","time":1754018575956,"size":37706}