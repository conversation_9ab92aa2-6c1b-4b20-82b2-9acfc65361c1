
f3aac1120d1cf6b799e6d11c6db5ddd2521ac409	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.418.1754018536329.js\",\"contentHash\":\"e059054e70cc78612b04713ba3647be5\"}","integrity":"sha512-Aj0pWph/j1o9X8JyPyZlJDzQC/ROZI2iwA53TI63gkCq9vVtqRAjgFBw3mI+AKMkYpVXJkRFNtRPbByyuXswqg==","time":1754018575956,"size":32193}