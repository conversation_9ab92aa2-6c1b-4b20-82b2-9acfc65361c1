import { request } from '@/api/index';

export default {
	query(data) {
		return request({
			url: '/lawFirmSelection/query',
			method: 'post',
			data,
		});
	},
	queryUseList(data) {
		return request({
			url: '/lawFirmSelection/queryUseList',
			method: 'post',
			data,
		});
	},
	save(data) {
		return request({
			url: '/lawFirmSelection/save',
			method: 'post',
			data,
		});
	},
	saveDataFinish(data) {
		return request({
			url: '/lawFirmSelection/saveDataFinish',
			method: 'post',
			data,
		});
	},
	queryDataById(data) {
		return request({
			url: '/lawFirmSelection/queryDataById',
			method: 'post',
			data,
		});
	},
	delete(data) {
		return request({
			url: '/lawFirmSelection/delete',
			method: 'post',
			data,
		});
	},
	queryOldYearEvaluateById(data) {
		return request({
			url: '/lawFirmSelection/queryOldYearEvaluateById',
			method: 'post',
			data,
		});
	},
	setParam(data) {
		return request({
			url: '/lawFirmSelection/setParam',
			method: 'post',
			data,
		});
	},
	lawFirmSelectDialog(data) {
		return request({
			url: '/lawFirmSelection/lawFirmSelectDialog',
			method: 'post',
			data,
		});
	},
};
