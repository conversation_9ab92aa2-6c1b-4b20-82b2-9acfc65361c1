
87173b8e50a285b987a6450f81d7f3045f0f5c81	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.244.1754018536329.js\",\"contentHash\":\"a2ca46a4938d7865d22a28e8a0b6b895\"}","integrity":"sha512-s1QepD4NlfFfHEXBKxbQovG8k+0lrC3IVQgQohTIWLmgbaTPl1xTR4hEs6jUmVVreDeJRQA6fUWH9WrzwhZ+5g==","time":1754018575997,"size":142807}