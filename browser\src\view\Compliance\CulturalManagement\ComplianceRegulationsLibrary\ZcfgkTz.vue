<template>
  <el-container direction="vertical" class="container-manage-sg">
    <el-header>
      <el-card>
        <div>
          <el-input v-model="tableQuery.fuzzyValue" class="filter_input" placeholder="检索字段（政策法规名称）" clearable
            @keyup.enter.native="refreshData" @clear="refreshData">
            <el-popover slot="prepend" placement="bottom-start" width="1000" trigger="click">
              <el-form ref="queryForm" label-width="100px" size="mini">
                <el-row>
                  <el-col :span="12">
                    <el-form-item label="政策法规名称">
                      <el-input v-model="tableQuery.regulationName" clearable placeholder="请输入..." />
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-button-group style="float: right">
                  <el-button type="primary" size="mini" icon="el-icon-search" @click="search_">搜索</el-button>
                  <el-button type="primary" size="mini" icon="el-icon-refresh-left" @click="empty_">重置</el-button>
                </el-button-group>
              </el-form>
              <el-button slot="reference" size="small" type="primary">高级检索</el-button>
            </el-popover>
            <el-button slot="append" icon="el-icon-search" @click="search_" />
          </el-input>
        </div>

      </el-card>

    </el-header>

    <el-main>
      <SimpleBoardIndex :title="'政策法规库台账'">
        <template slot="button">
          <el-button type="primary" class="normal-btn" size="mini" @click="exportExcel">导出Excel</el-button>
        </template>
        <el-table ref="table" v-loading="tableLoading" :data="tableData" size="mini" border :height="table_height"
          stripe fit highlight-current-row :show-overflow-tooltip="true" row-key="id"
          style="table-layout: fixed;width: 100%;" @sort-change="tableSort" @row-dblclick="rowDblclick"
          id="ZcfgkTableList">
          <el-table-column type="index" width="50" label="序号" align="center" />
          <el-table-column v-if="ss.tableColumns.find(item => item.key === 'complianceArea').visible"
            prop="complianceArea" show-overflow-tooltip label="合规领域" min-width="100" />
          <el-table-column v-if="ss.tableColumns.find(item => item.key === 'regulationName').visible"
            prop="regulationName" show-overflow-tooltip label="政策法规名称" min-width="250" />
          <el-table-column v-if="ss.tableColumns.find(item => item.key === 'fileNumber').visible" prop="fileNumber"
            show-overflow-tooltip label="文件编号" min-width="200" />
          <el-table-column v-if="ss.tableColumns.find(item => item.key === 'fileNature').visible" prop="fileNature"
            show-overflow-tooltip label="文件性质" min-width="100" />
          <el-table-column v-if="ss.tableColumns.find(item => item.key === 'issuingUnit').visible" prop="issuingUnit"
            show-overflow-tooltip label="颁布单位" min-width="250" />
          <el-table-column v-if="ss.tableColumns.find(item => item.key === 'effectiveDate').visible"
            prop="effectiveDate" show-overflow-tooltip label="实施日期" min-width="150" sortable="custom">
            <template slot-scope="scope">
              <span>{{ scope.row.effectiveDate | parseTime }}</span>
            </template>
          </el-table-column>
          <el-table-column v-if="ss.tableColumns.find(item => item.key === 'reviewStatus').visible"
                           prop="reviewStatus" show-overflow-tooltip label="状态" min-width="100" sortable="custom" />
          <el-table-column v-if="ss.tableColumns.find(item => item.key === 'applicableUnit').visible"
            prop="applicableUnit" show-overflow-tooltip label="适用单位" min-width="100" sortable="custom" />

          <el-table-column label="操作" align="center" width="150" fixed="right">
            <template slot-scope="scope">
              <!-- <el-button type="text" v-if="scope.row.reviewStatus !== '已提交'"
                @click="edit_(scope.$index, scope.row)">编辑</el-button> -->
              <el-button type="text" @click="view_(scope.$index, scope.row)">查看</el-button>
              <!-- <el-button type="text" v-if="scope.row.reviewStatus !== '已提交'"
                @click="delete_(scope.$index, scope.row)">删除</el-button> -->
            </template>
          </el-table-column>
        </el-table>
      </SimpleBoardIndex>

    </el-main>
    <el-footer>
      <!--分页工具栏-->
      <pagination :total="tableQuery.total" :page.sync="tableQuery.page" :limit.sync="tableQuery.limit"
        @pagination="refreshData" />
    </el-footer>

  </el-container>
</template>

<script>
// 组件
import pagination from '@/view/components/Pagination/PaginationIndex'
import TableTools from '@/view/components/TableTools/index'
import SimpleBoardIndex from "@/view/components/SimpleBoard/SimpleBoardIndex";
import SimpleBoard2 from "@/view/components/SimpleBoard/SimpleBoardIndex";
import orgTree from '@/view/components/OrgTree/OrgTree'
// vuex状态值
import { mapGetters } from 'vuex'
import XLSX from 'xlsx';
import FileSaver from 'file-saver';
// 接口api
import ComplianceRegulationsLibraryApi from '@/api/risk/ComplianceRegulationsLibrary'

export default {
  name: 'ZcfgkIndex',
  inject: ["layout"],
  components: { pagination, TableTools, SimpleBoardIndex, orgTree, SimpleBoard2 },
  data() {
    return {
      tableQuery: {
        page: 1,
        limit: 10,
        total: 0,
        regulationName: null, // 政策法规名称
        reviewStatus:"已提交", // 数据状态
        isQuery:true,
        orgId: '', // 上报单位id
      },
      yearOptions: Array.from({ length: 10 }, (_, i) => new Date().getFullYear() - i),
      table_height: '100%',
      tableData: [],
      dialogVisible: false,
      orgTreeDialog: false,
      zxcheckedData: [],
      orgVisible: false,
      tableLoading: false,
      ss: {
        data: this.tableData,
        tableColumns: [
          { key: 'complianceArea', label: '合规领域', visible: true },
          { key: 'regulationName', label: '政策法规名称', visible: true },
          { key: 'fileNature', label: '文件性质', visible: true },
          { key: 'caseSource', label: '案件来源', visible: true },
          { key: 'fileNumber', label: '文件编号', visible: true },
          { key: 'issuingUnit', label: '颁布单位', visible: true },
          { key: 'applicableUnit', label: '适用单位', visible: true },
          { key: 'reviewStatus', label: '状态', visible: true },
          { key: 'effectiveDate', label: '实施日期', visible: true }
        ]
      },
    }
  },
  computed: {
    ...mapGetters([
      'orgContext', 'currentFunctionId'
    ])
  },
  activated() {
    // 长连接页面第二次激活的时候,不会走created方法,会走此方法
    this.refreshData()
  },
  created() {
    this.refreshData()
  },

  mounted() {
    this.$nextTick(function () {
      this.table_height = window.innerHeight - this.$refs.table.$el.offsetTop - 84 - 45

      // 监听窗口大小变化
      const self = this
      window.onresize = function () {
        self.table_height = window.innerHeight - self.$refs.table.$el.offsetTop - 84 - 45
      }
    })
  },
  methods: {
    isEdit(row) {
      return row.dataStateCode === this.utils.dataState_BPM.SAVE.code || row.dataStateCode === this.utils.dataState_BPM.BACK.code
    },
    isDelete(row) {
      return row.dataStateCode === this.utils.dataState_BPM.SAVE.code
    },
    // 刷新数据
    refreshData() {
      this.tableQuery.orgId = this.orgContext.currentOrgId
      // 主要用于删除,当前页只有一条数据的时候,删除需要回到上一页
      if (this.tableData.length === 0 && this.tableQuery.page > 1) {
        this.tableQuery.page--
      }
      ComplianceRegulationsLibraryApi.query(this.tableQuery).then(response => {
        let rows = response.data.data.records
        this.tableData = rows
        this.ss.data = rows
        this.tableQuery.total = response.data.data.total
        this.tableLoading = false
      }).catch({})
    },
    add_(event) {

      const tabId = this.utils.createUUID();
      this.layout.openNewTab(
        "政策法规详情单",
        "zcfgk_main_detail",
        "zcfgk_main_detail",
        tabId,
        {
          functionId: "zcfgk_main_detail," + tabId,
          ...this.utils.routeState.NEW(tabId),
        }
      );
    },
    // 编辑
    edit_(index, row) {

      this.layout.openNewTab(
        "政策法规详情单",
        "zcfgk_main_detail",
        "zcfgk_main_detail",
        row.id,
        {
          functionId: "zcfgk_main_detail," + row.id,
          ...this.utils.routeState.EDIT(row.id),
          view: 'old'
        }
      );
    },
    // 删除
    delete_(index, row) {
      this.$confirm('此操作将永久删除该数据及相关数据, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        new Promise((resolve, reject) => {
          ComplianceRegulationsLibraryApi.deletebyid({
            id: row.id
          }).then((response) => {
            resolve(response)
          })
        }).then(value => {
          this.tableData.splice(index, 1)
          this.$message.success('删除成功!')
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },
    // 查看
    view_(index, row) {
      const tabId = this.utils.createUUID();
      this.layout.openNewTab(
        "政策法规详情单",
        "zcfgk_main_detail",
        "zcfgk_main_detail",
        tabId,
        {
          functionId: "zcfgk_main_detail," + tabId,
          ...this.utils.routeState.VIEW(row.id)
        }
      )
    },
    tableSort(column, prop, order) {
      this.tableQuery.sortName = column.prop
      this.tableQuery.order = column.order === "ascending"
      this.refreshData()
    },
    // 点击搜索按钮事件,回到第一页,重新刷新数据
    search_: function () {
      this.tableQuery.page = 1
      this.refreshData()
    },
    // 点击清空按钮事件,清空查询条件属性,回到第一页,重新刷新数据
    empty_() {
      // 清空搜索条件
      this.tableQuery = {
        page: 1,
        limit: 10,
        total: 0,
        evaluationObjectType: '',
        evaluationObject: '',
        evaluationYear: '',
      };
      this.refreshData();
    },
    // 点击刷新按钮事件
    refresh_() {
      this.tableQuery.sortName = null
      this.tableQuery.order = null
      this.empty_()
    },
    showOrgTreeDialog() {
      this.dialogVisible = true;
    },
    cancel() {
      this.dialogVisible = false
    },
    choiceDeptSure() {
      let selectedUnits = this.zxcheckedData.map(item => item.name).join(', ');
      this.tableQuery.reportingUnit = selectedUnits;
      this.dialogVisible = false;
    },
    exportExcel() {
      // 获取原始表格元素
      const originalTable = document.querySelector('#ZcfgkTableList');
      const rows = originalTable.querySelectorAll('tr');
      const newTable = document.createElement('table');

      // 创建标题行
      const titleRow = document.createElement('tr');
      const titleCell = document.createElement('td');
      titleCell.colSpan = rows[0].cells.length - 2; // 假设最后一列是操作列，减去两列（操作列和可能的其他列）
      titleCell.style.textAlign = 'center';
      titleCell.style.fontSize = '16px';
      titleCell.style.fontWeight = 'bold';
      titleCell.textContent = '政策法规库列表';
      titleRow.appendChild(titleCell);
      newTable.appendChild(titleRow);

      // 创建临时表格并复制表头，同时移除最后一列
      const headerRow = rows[0].cloneNode(true);
      // 假设最后一列是操作列，移除最后一列的表头单元格
      headerRow.deleteCell(headerRow.cells.length - 2);
      newTable.appendChild(headerRow);

      // 复制数据行，同时移除操作列
      for (let i = 1; i < rows.length / 2; i++) {
        const newRow = rows[i].cloneNode(true);
        // 移除操作按钮列
        const actionCells = newRow.querySelectorAll('.el-button');
        actionCells.forEach(cell => cell.remove());
        // 移除最后一列的数据单元格
        newRow.deleteCell(newRow.cells.length - 1);
        newTable.appendChild(newRow);
      }

      // 使用 SheetJS 从临时表格生成工作簿
      const wb = XLSX.utils.table_to_book(newTable, { raw: true });
      const wbout = XLSX.write(wb, {
        bookType: 'xlsx',
        bookSST: true,
        type: 'array',
      });

      // 使用 FileSaver 保存 Excel 文件
      try {
        const blob = new Blob([wbout], { type: 'application/octet-stream' });
        FileSaver.saveAs(blob, '政策法规库列表.xlsx');
      } catch (e) {
        if (typeof console !== 'undefined') {
          console.error(e);
        }
      }
    }
  }
}
</script>

<style scoped>
.el-table__fixed-body-wrapper {
  top: 50px !important;
}
</style>
