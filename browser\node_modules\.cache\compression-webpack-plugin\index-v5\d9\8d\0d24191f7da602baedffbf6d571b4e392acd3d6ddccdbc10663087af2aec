
b4501ff92cffee064f2253b89dba087d5cd73334	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.81.1754018536329.js\",\"contentHash\":\"71436f9332eefe99537585c24772693c\"}","integrity":"sha512-yr5icMMmgPmPxg/VruadAYaVZm5ni/nl29290a2NyUt9tVXJ0+3NRZmcLxReRem1bGi/sJMKCvtiyAW09UVMag==","time":1754018575956,"size":37095}