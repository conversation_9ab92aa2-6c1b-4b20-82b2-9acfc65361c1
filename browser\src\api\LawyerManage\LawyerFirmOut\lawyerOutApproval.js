import {request} from '@/api/index'

export default {
    query(data) {
        return request({
            url: '/lawyerOutApproval/getLawyerOutList',
            method: 'post',
            data
        })
    },
    save(data) {
        return request({
            url: '/lawyerOutApproval/save',
            method: 'post',
            data
        })
    },
    queryDataById(data) {
        return request({
            url: '/lawyerOutApproval/getDataById',
            method: 'post',
            data
        })
    },
    delete(data) {
        return request({
            url: '/lawyerOutApproval/delete',
            method: 'post',
            data
        })
    },
    setParam(data) {
        return request({
            url: '/lawyerOutApproval/setParam',
            method: 'post',
            data
        })
    }
}
