
60a4c92f4c9441a77e8e0b926c30e23871ee8dca	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.106.1754018536329.js\",\"contentHash\":\"4f093c2a94cc9978de5399fd196fe047\"}","integrity":"sha512-WdATZpuAmh02lVMdYUAL17IH47w2ki4OJmZStbHik3KsZDM0SZZbG+URNo8SAVwfT0y+VF4D631IHL7+HGX2rw==","time":1754018575979,"size":149556}