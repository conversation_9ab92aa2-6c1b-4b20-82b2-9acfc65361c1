package com.klaw.service.imp.complianceRiskServiceImp;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.klaw.dao.complianceRiskDao.ComplianceCaseLibraryMapper;
import com.klaw.entity.complianceRiskBean.ComplianceCaseLibraryEntity;
import com.klaw.service.complianceRiskService.ComplianceCaseLibraryService;

import com.klaw.utils.DataAuthUtils;
import com.klaw.utils.PageUtils;
import com.klaw.vo.Json;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class ComplianceCaseLibraryServiceImpl extends ServiceImpl<ComplianceCaseLibraryMapper, ComplianceCaseLibraryEntity> implements ComplianceCaseLibraryService {


    @Override
    public Page<ComplianceCaseLibraryEntity> queryPageData(JSONObject json) {
        QueryWrapper<ComplianceCaseLibraryEntity> wrapper = new QueryWrapper<>();
        getFilter(json, wrapper);
        return page(new PageUtils<>(json), wrapper);
    }



    public void getFilter(JSONObject json, QueryWrapper<ComplianceCaseLibraryEntity> wrapper) {
        //是否台账功能（登记只查自己的，台账和弹框查自己和数据权限的）
        boolean isQuery = json.containsKey("isQuery") ? json.getBoolean("isQuery") : false;
        String orgId = json.containsKey("orgId") ? json.getString("orgId") : null;
        // 案例名称
        String caseName = json.containsKey("caseName") ? json.getString("caseName") : null;

        String reviewStatus = json.containsKey("reviewStatus") ? json.getString("reviewStatus") : null;

        // 模糊搜索值
        String fuzzyValue = json.containsKey("fuzzyValue") ? json.getString("fuzzyValue") : null;
        // 排序字段
        String sortName = json.containsKey("sortName") ? json.getString("sortName") : null;

        // 排序字段是否升序
        boolean order = json.containsKey("order") ? json.getBoolean("order") : false;

        if (StringUtils.isNotBlank(fuzzyValue)) {
            wrapper.and(i -> i.like("case_name", fuzzyValue));
        }

        // 应用查询条件
        if (StringUtils.isNotBlank(caseName)) {
            wrapper.and(i -> i.like("case_name", caseName));
        }
        if (StringUtils.isNotBlank(reviewStatus)) {
            wrapper.and(i -> i.eq("review_status", reviewStatus));
        }

        if (StringUtils.isNotBlank(sortName) && order) {
            wrapper.orderByAsc(sortName);
        } else if (StringUtils.isNotBlank(sortName)) {
            wrapper.orderByDesc(sortName);
        }

        //台账权限
        if (isQuery){
            Long functionId = DataAuthUtils.getFunctionIdByCode("hgalk_tz");
            DataAuthUtils.dataPermSql(wrapper, null, functionId, orgId);
        }else {
            //经办人权限隔离
            wrapper.eq("create_org_id", orgId);
        }
        //按创建时间倒序显示
        wrapper.orderByDesc("case_occurrence_date");
    }


    @Override
    public ComplianceCaseLibraryEntity queryDataById(String id) {
        ComplianceCaseLibraryEntity complianceCaseLibraryEntity = getById(id);
        return complianceCaseLibraryEntity;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Json deleteDataById(String id) {
        boolean flag = removeById(id);
        if (!flag) {
            return Json.fail().msg("未找到需要删除的数据");
        }
        return Json.succ().msg("删除成功!");
    }
}



