<template>
  <FormWindow ref="formWindow">
    <el-container v-loading="loading" style="height: calc(100vh - 180px);">
      <el-main v-if="view === 'old'">
        <el-scrollbar style="height: 100%" wrap-style="overflow-x: hidden;">

          <el-form ref="dataForm" style="padding-right: 10px;" :model="mainData"
                   :rules="dataState !== 'view' ? rules : {}" label-width="100px"
                   :style="dataState !== utils.formState.VIEW ? 'margin-right: 50px;' : ' margin-right: 0px;' ">
            <span
                style="text-align: left;font-size: 20px;margin-left: 43%;font-weight: 900;">律师黑名单审批详情单</span>
            <div v-if="dataState !== 'view'">
              <div style="margin: 10px">
                <span style="font-weight: bold;font-size: 16px;color: #5A5A5F;">基本信息</span>
              </div>
              <el-divider></el-divider>
              <el-row style="margin: 10px">
                <el-col :span="24" class="mylabel">
                  <el-form-item label="加入黑名单原因" class="custom-word-break" prop="blackReason">
                    <span slot="label">列入原因</span>
                    <el-input v-if="dataState !== 'view'" v-model="mainData.blackReason"
                              type="textarea" placeholder="请输入..."
                              :autosize="{ minRows: 3, maxRows: 6}"
                              maxlength="1000" show-word-limit style="width: 100%" clearable/>
                    <span v-else class="viewSpan">{{ mainData.blackReason }}</span>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row>
                <el-row>
                  <el-col :span="24" prop="attachment">
                    <el-form-item label="附件资料" prop="attachment">
                      <uploadDoc
                          v-model="mainData.attachment"
                          :files.sync="mainData.attachment"
                          :disabled="dataState === 'view'"
                          :doc-path="docURL"
                      />
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-row>

              <div style="margin: 10px">
                <span style="font-weight: bold;font-size: 16px;color: #5A5A5F;">律师信息</span>
              </div>
              <el-divider></el-divider>

              <div>
                <el-row style="margin-top: 10px">
                  <el-col :span="24">
                    <el-form-item label="" prop="avatar">
                      <el-avatar :size="100" :src="mainData.avatar"></el-avatar>
                      <el-button v-if="dataState !== 'view'" type="primary" style="margin-left: 10px;"
                                 @click="changeAvatar">修改头像
                      </el-button>
                    </el-form-item>
                  </el-col>
                </el-row>

                <el-row>
                  <el-col :span="8">
                    <el-form-item label="律师名称" prop="lawyerName">
                      <el-input v-if="dataState !== 'view'" v-model.trim="mainData.lawyerName" maxlength="20"
                                show-word-limit style="width: 100%" placeholder="请输入..." clearable
                                @clear="lawyerNameClear('main')">
                        <el-button slot="append" icon="el-icon-search" @click="chooseLawyerClick()"/>
                      </el-input>
                      <span v-else class="viewSpan">{{ mainData.lawyerName }}</span>
                    </el-form-item>
                  </el-col>
                  <lawyerDialog :dialogVisible.sync="lawyerDialogVisible" @lawyerSure="lawyerSure"/>
                  <el-col :span="16">
                    <el-form-item label="所属律所" prop="lawFirm">
                      <!--                    <el-input v-if="dataState !== 'view'"-->
                      <!--                              v-model="mainData.lawFirm" style="width: 100%"-->
                      <!--                              disabled :title="mainData.lawFirm"/>-->
                      <!--                    <span v-else class="viewSpan">{{ mainData.lawFirm }}</span>-->
                      <el-input v-if="dataState !== 'view'"
                                v-model="mainData.lawFirm"
                                clearable placeholder="请选择" class="input-with-select" disabled>
                        <el-button slot="append" icon="el-icon-search" @click="chooseNoticeDeptClick"/>
                      </el-input>
                      <span v-else class="viewSpan">{{ mainData.lawFirm }}</span>
                    </el-form-item>
                  </el-col>
                  <LawFirmDialog :dialog-visible.sync="dialogVisible" :is-multiple="false"
                                 :law-firm-state="LawFirmSelectState" @lawFirmSure="lawFirmSure"/>
                </el-row>

                <el-row>
                  <el-col :span="8">
                    <el-form-item label="性别" prop="sex">
                      <el-radio-group v-if="dataState !== 'view'" v-model="mainData.sex" disabled>
                        <el-radio label="男" border>男</el-radio>
                        <el-radio label="女" border>女</el-radio>
                      </el-radio-group>
                      <span v-else class="viewSpan">{{ mainData.sex }}</span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="联系电话" prop="lawyerPhone">
                      <el-input v-if="dataState !== 'view'"
                                v-model="mainData.lawyerPhone"
                                maxlength="20" show-word-limit style="width: 100%"
                                placeholder="请输入..." clearable disabled/>
                      <span v-else class="viewSpan">{{ mainData.lawyerPhone }}</span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="电子邮箱" prop="lawyerEmail">
                      <el-input v-if="dataState !== 'view'"
                                v-model="mainData.lawyerEmail"
                                maxlength="30" show-word-limit style="width: 100%"
                                placeholder="请输入..." clearable disabled/>
                      <span v-else class="viewSpan">{{ mainData.lawyerEmail }}</span>
                    </el-form-item>
                  </el-col>
                </el-row>

                <el-row>
                  <el-col :span="8" class="mylabel">
                    <el-form-item label="从事律师执业时间" class="custom-word-break" prop="lawyerTime">
                      <span slot="label">从事律师<br>执业时间</span>
                      <el-date-picker v-if="dataState !== 'view'"
                                      v-model="mainData.lawyerTime"
                                      value-format="yyyy-MM-dd" type="date"
                                      style="width: 100%" clearable disabled/>
                      <span v-else class="viewSpan">{{ mainData.lawyerTime | parseTime }}</span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="执业证号" prop="charteredNo">
                      <el-input v-if="dataState !== 'view'"
                                v-model="mainData.charteredNo"
                                maxlength="20" show-word-limit style="width: 100%" placeholder="请输入..."
                                clearable @blur="charteredNoBlur" disabled/>
                      <span v-else class="viewSpan">{{ mainData.charteredNo }}</span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="主要执业地域" prop="majorRegion">
                      <el-input v-if="dataState !== 'view'"
                                v-model="mainData.majorRegion"
                                maxlength="200" show-word-limit style="width: 100%" placeholder="请输入..."
                                clearable disabled/>
                      <span v-else class="viewSpan">{{ mainData.majorRegion }}</span>
                    </el-form-item>
                  </el-col>
                </el-row>

                <el-row>
                  <el-col :span="24">
                    <el-form-item label="擅长业务领域" prop="beGoodAtDomainIds">
                      <el-select v-if="dataState !== 'view'"
                                 v-model="mainData.beGoodAtDomain"
                                 multiple placeholder="请选择" style="width: 100%"
                                 @change="beGoodAtDomainChange" disabled>
                        <el-option
                            v-for="item in SCLYData"
                            :key="item.id"
                            :label="item.dicName"
                            :value="item.id"
                        />
                      </el-select>
                      <span v-else class="viewSpan">{{ mainData.beGoodAtDomain }}</span>
                    </el-form-item>
                  </el-col>
                </el-row>

                <el-row>
                  <el-col :span="24">
                    <el-form-item label="备注" prop="remarks">
                      <el-input v-if="dataState !== 'view'"
                                v-model="mainData.remarks"
                                type="textarea" placeholder="请输入..."
                                :autosize="{ minRows: 3, maxRows: 6}"
                                maxlength="1000" show-word-limit style="width: 100%" clearable disabled/>
                      <text-span v-else class="viewSpan" :text="mainData.remarks"/>
                    </el-form-item>
                  </el-col>
                </el-row>
              </div>
            </div>


            <div v-else>
              <SimpleBoardTitle title="基本信息" style="margin-top: 5px;">
                <table class="table_content" style="margin-top: 10px;">
                  <tbody>
                  <tr>
                    <th colspan="2" class="th_label_">加入黑名单原因</th>
                    <td colspan="22" class="td_value_">{{ mainData.blacklistReason }}</td>
                  </tr>
                  </tbody>

                  <tbody>
                  <tr>
                    <th colspan="2" class="th_label_">相关附件</th>
                    <td colspan="22" class="td_value_">
                      <uploadDoc
                          v-model="mainData.attachment"
                          :files.sync="mainData.attachment"
                          :disabled="dataState === 'view'"
                          :doc-path="docURL"
                      />
                    </td>
                  </tr>
                  </tbody>
                </table>
              </SimpleBoardTitle>

              <SimpleBoardTitle title="律所信息" style="margin-top: 10px;">
                <table class="table_content" style="margin-top: 10px;">
                  <tbody>
                  <tr>
                    <th colspan="2" class="th_label">事项名称</th>
                    <td colspan="14" class="td_value">{{ mainData.itemName }}</td>
                    <th colspan="2" class="th_label">事项编号</th>
                    <td colspan="6" class="td_value">{{ mainData.sequenceCode }}</td>
                  </tr>
                  <tr>
                    <th colspan="2" class="th_label">律所名称</th>
                    <td colspan="22" class="td_value">{{ mainData.lawyerFirm }}</td>
                  </tr>
                  </tbody>

                  <tbody>
                  <tr>
                    <th colspan="2" class="th_label">律所地址</th>
                    <td colspan="22" class="td_value">{{ mainData.registerAddress }}</td>
                  </tr>
                  </tbody>

                  <tbody>
                  <tr>
                    <th colspan="2" class="th_label">律所电话</th>
                    <td colspan="6" class="td_value">{{ mainData.lawFirmPhone }}</td>
                    <th colspan="2" class="th_label">律所传真</th>
                    <td colspan="6" class="td_value">{{ mainData.lawFirmFax }}</td>
                    <th colspan="2" class="th_label">负责人</th>
                    <td colspan="6" class="td_value">{{ mainData.functionary }}</td>
                  </tr>
                  </tbody>

                  <tbody>
                  <tr>
                    <th colspan="2" class="th_label">邮政编码</th>
                    <td colspan="6" class="td_value">{{ mainData.postalCode }}</td>
                    <th colspan="2" class="th_label">主管机关</th>
                    <td colspan="6" class="td_value">{{ mainData.issuingAuthority }}</td>
                    <th colspan="2" class="th_label">统一社会<br>信用代码</th>
                    <td colspan="6" class="td_value">{{ mainData.licenseCode }}</td>
                  </tr>
                  </tbody>

                  <tbody>
                  <tr>
                    <th colspan="2" class="th_label">批准文号</th>
                    <td colspan="6" class="td_value">{{ mainData.licenseNumber }}</td>
                    <th colspan="2" class="th_label">设立资产</th>
                    <td colspan="6" class="td_value">{{ mainData.registeredCapital }}</td>
                    <th colspan="2" class="th_label">成立时间</th>
                    <td colspan="6" class="td_value">{{ mainData.foundTime | parseTime }}</td>
                  </tr>
                  </tbody>

                  <tbody>
                  <tr>
                    <th colspan="2" class="th_label">执业人数</th>
                    <td colspan="6" class="td_value">{{ mainData.workNumber }}</td>
                    <th colspan="2" class="th_label">申请部门</th>
                    <td colspan="6" class="td_value">{{ mainData.applyDeptName }}</td>
                    <th colspan="2" class="th_label">年检情况</th>
                    <td colspan="6" class="td_value">{{ annualInspectionName }}</td>
                  </tr>
                  </tbody>

                  <tbody>
                  <tr>
                    <th colspan="2" class="th_label_">简介</th>
                    <td colspan="22" class="td_value_">{{ mainData.introduction }}</td>
                  </tr>
                  </tbody>

                  <tbody>
                  <tr>
                    <th colspan="2" class="th_label_">备注</th>
                    <td colspan="22" class="td_value_">{{ mainData.remark }}</td>
                  </tr>
                  </tbody>

                  <tbody>
                  <tr>
                    <th colspan="2" class="th_label_">附件资料</th>
                    <td colspan="22" class="td_value_">
                      <uploadDoc
                          v-model="mainData.businessLicense"
                          :files.sync="mainData.businessLicense"
                          :disabled="dataState==='view'"
                          :doc-path="docURL"
                      />
                    </td>
                  </tr>
                  </tbody>

                </table>
              </SimpleBoardTitle>
            </div>

            <el-row style="margin-top: 20px;">
              <el-col :span="18">
                <el-form-item label="经办组织">
                  <span class="viewSpan">{{ mainData.createPsnFullName }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="经办时间">
                  <span class="viewSpan">{{ mainData.createTime | parseTime }}</span>
                </el-form-item>
              </el-col>
            </el-row>


            <!--选择部门或者人员-->
            <el-dialog :close-on-click-modal="false" title="选择部门" :visible.sync="orgVisible" width="50%">
              <div class="el-dialog-div">
                <orgTree :accordion="false" :is-checked-user="isCheckedUser" :show-user="showUser" :is-check="is_Check"
                         :checked-data.sync="zxcheckedData" :is-not-cascade="true" :is-filter="true"/>
              </div>
              <span slot="footer" class="dialog-footer">
                <el-button icon="" class="negative-btn" @click="cancel_">取消</el-button>
                <el-button type="primary" icon="" class="active-btn" @click="choiceDeptSure_">确定
                </el-button>
              </span>
            </el-dialog>

            <LawFirmDialog :dialog-visible.sync="lawFirmVisible"
                           :is-multiple="lawFirmisMultiple"
                           :law-firm-state="lawFirmState" @lawFirmSure="lawFirmSure"/>

          </el-form>
        </el-scrollbar>
      </el-main>

      <el-main v-else>
        <el-card style="height: auto;margin-left: 1%;margin-right: 2%;margin-top: 1%;">
          <el-scrollbar style="height: 100%;">
            <el-form ref="dataForm"
                     style="margin-left: 10px;margin-right: 10px;"
                     :model="mainData" :rules="rules" :class="className"
            >
              <el-row style="margin-top: 20px;">
                <span
                    style="text-align: left;font-size: 23px;margin-left: 43%;font-weight: 900;">律师黑名单审批单</span>
              </el-row>
              <SimpleBoardTitleApproval title="基本信息" style="margin-top: 5px;">
                <table class="table_content">
                  <tbody>
                  <tr>
                    <th colspan="3" class="th_label_approval">事项名称</th>
                    <td colspan="9" class="td_value_approval">{{ mainData.itemName }}</td>
                    <th colspan="3" class="th_label_approval">事项编号</th>
                    <td colspan="9" class="td_value_approval">{{ mainData.sequenceCode }}</td>
                  </tr>
                  <tr>
                    <th colspan="3" class="th_label_approval">经办组织</th>
                    <td colspan="9" class="td_value_approval">{{ mainData.createPsnFullName }}</td>
                    <th colspan="3" class="th_label_approval">经办时间</th>
                    <td colspan="9" class="td_value_approval">{{ mainData.createTime | parseTime }}</td>
                  </tr>
                  <tr>
                    <th colspan="3" class="th_label_approval">律师名称</th>
                    <td colspan="9" class="td_value_approval">{{ mainData.lawyerName }}</td>
                    <th colspan="3" class="th_label_approval">所属律所</th>
                    <td colspan="9" class="td_value_approval">{{ mainData.lawFirm }}</td>
                  </tr>
                  <tr>
                    <th colspan="3" class="th_label_approval_">加入黑名单说明</th>
                    <td colspan="21" class="td_value_approval_">{{ mainData.blackReason }}</td>
                  </tr>
                  </tbody>

                  <tbody>
                  <tr>
                    <th colspan="3" class="th_label_approval">附件资料</th>
                    <td colspan="21" class="td_value_approval">
                      <uploadDoc
                          v-model="mainData.businessLicense"
                          :files.sync="mainData.businessLicense"
                          :disabled="true"
                          :doc-path="docURL"
                      />
                    </td>
                  </tr>
                  </tbody>
                </table>
              </SimpleBoardTitleApproval>

              <!--审批历史 -->
              <SimpleBoardTitleApproval style="margin-top: 10px;" title="审查意见" class="print-table-wrap">
                <ProcessOpinion style="border: solid 1px #606266;" :proc-inst-id="this.obj.processInstanceId"
                                :task-id="this.obj.taskId" type-code="1"/>
                <div v-if="approvalIs && isParseElement">
                  <el-input
                      type="textarea"
                      :rows="2"
                      style="border-radius: 0 !important;"
                      placeholder="请输入审批意见"
                      v-model="approvalOpinion">
                  </el-input>
                </div>

              </SimpleBoardTitleApproval>

              <SimpleBoardTitleApproval style="margin-top: 10px;" title="领导意见"
                                        class="leadership-opinions-section-wrap">
                <div style="border: solid 1px #606266;overflow: hidden">
                  <ProcessOpinion :proc-inst-id="this.obj.processInstanceId" :task-id="this.obj.taskId" type-code="2"/>
                  <div v-if="approvalIs && isParseElementlg">
                    <el-input
                        type="textarea"
                        :rows="2"
                        style="border-radius: 0 !important;"
                        placeholder="请输入审批意见"
                        v-model="approvalOpinion">
                    </el-input>
                  </div>

                </div>
              </SimpleBoardTitleApproval>

            </el-form>
          </el-scrollbar>
        </el-card>
      </el-main>

      <Shortcut :approval-show="approvalShow" :noticeShow="noticeShow" :detail-show="detailShow" :print-show="printShow"
                @approvalClick="approvalClick" @detailClick="detailClick" @noticeClick="noticeClick"
                @printClick="printClick"/>

    </el-container>
  </FormWindow>
</template>

<script>
import lawFirmBlacklistApi from '@/api/LawyerManage/LawFirmBlackList/lawFirmBlackList'
import lawyerBlackListApi from '@/api/LawyerManage/LawyerBlackList/lawyerBlackList'
import lawyerInFirmApi from '@/api/LawyerManage/LawyerFirm/lawFirmInApproval'
import dictApi from '@/api/_system/dict'
import noticeApi from "@/api/_system/notice";
import processApi from "@/api/_system/process"
import taskApi from "@/api/_system/task";
import orgApi from "@/api/_system/org";

import {mapGetters} from 'vuex'

import FormWindow from '@/view/components/FormWindow/FormWindow'
import uploadDoc from '@/view/components/UploadDoc/UploadDoc'
import LawFirmDialog from '@/view/litigation/LawyerManage/LawFirmBlackList/dialog/FirmBlackDialog'
import textSpan from '@/view/components/TextSpan/TextSpan'
import orgTree from '@/view/components/OrgTree/OrgTree'
import Shortcut from "@/view/litigation/caseManage/caseExamine/Shortcut"
import SimpleBoardTitle from "../../../components/SimpleBoard/SimpleBoardTitle"
import ProcessOpinion from '@/view/components/ProcessOpinion/ProcessOpinion'
import SimpleBoardTitleApproval from "@/view/components/SimpleBoard/SimpleBoardTitleApproval"
import lawyerFirm from "@/api/LawyerManage/LawyerFirm/lawyerFirm"

export default {
  name: 'LawyerBlackListMain',
  inject: ['layout', 'mcpLayout', 'mcpDesignPage'],
  components: {
    FormWindow,
    uploadDoc,
    LawFirmDialog,
    textSpan,
    orgTree,
    Shortcut,
    SimpleBoardTitle,
    ProcessOpinion,
    SimpleBoardTitleApproval
  },
  data() {
    return {
      approvalOpinion: '',
      parseElement: null,
      approvalIs: false,
      className: '',
      create: null,
      type: null,
      dataState: null,//表单状态，新增、查看、编辑
      functionId: null,//终止的时候要用，需要手动关闭
      typeCode: null,
      typeName: null,
      loading: false,
      view: 'new',
      rules: {
        lawyerFirm: [{required: true, message: '请输入律所名称', trigger: 'blur'}],
        functionary: [{required: true, message: '请输入负责人', trigger: 'blur'}],
        contactPerson: [{required: true, message: '请输入联系人', trigger: 'blur'}],
        licenseCode: [{required: true, message: '请输入统一社会信用代码', trigger: 'blur'}],
        phone: [{required: true, message: '请输入联系电话', trigger: 'blur'}],
        applyDeptName: [{required: true, message: '请选择申请部门', trigger: 'blur'}],
        blacklistReason: [{required: true, message: '请填写加入黑名单原因', trigger: 'blur'}]
      },
      // 律所数据
      mainData: {
        id: this.utils.createUUID(), // id
        sequenceCode: null, //流水号
        lawFirm: null, // 律所名称
        blackReason: null, // 黑名单原因
        remarks: null, // 备注
        attachment: null, // 附件资料
        updateTime: null, // 更新时间
        typeCode: null,
        typeName: null,
        dataState: this.utils.dataState_BPM.SAVE.name, // 状态 新增时默认是已保存
        dataStateCode: this.utils.dataState_BPM.SAVE.code, // 状态编码
        createOgnName: null, //  经办单位
        createDeptName: null, // 经办部门
        createPsnName: null, // 经办人员
        createTime: null, // 经办时间
        createOgnId: null,
        createDeptId: null,
        createPsnId: null,
        createPsnFullId: null,
        createPsnFullName: null,
        createGroupId: null,
        createGroupName: null,
        createOrgId: null,
        createOrgName: null,
        dataSource: 'new',
        sourceId: null,
      },
      NJQKData: [],
      docURL: '/lawyerBlack',
      zxcheckedData: [],
      showUser: false,
      is_Check: true,
      isCheckedUser: false,
      orgVisible: false,
      lawFirmVisible: false,
      lawFirmisMultiple: false,
      lawFirmState: this.utils.dataState_LAWYER.YRK.name + ',' + this.utils.dataState_LAWYER.YCK.name,

      activity: null,//记录当前待办处于流程实例的哪个环节
      obj: {// 流程处理逻辑需要的各种参数
        taskId: null,
        processInstanceId: null,
        businessKey: null,
        title: null,
        functionName: '律师审批',
        functionCode: 'lawyer_black_list_main',
        sid: null,
      },

      noticeParams: {},
      noticeData: {
        moduleName: '', // 模块名称
        dataId: '', // 数据ID
        url: '', // 地址
        title: '', // 地址
        params: {} // 其他参数
      },
    }
  },
  computed: {
    ...mapGetters(['orgContext']),
    isNotice() {
      const isNotice = this.$route.query.isNotice
      return this.mainData.dataStateCode !== this.utils.dataState_BPM.FINISH.code &&
          this.mainData.createPsnFullId === this.orgContext.currentPsnFullId && isNotice
    },
    noticeShow() {
      return this.mainData.dataStateCode === this.utils.dataState_BPM.FINISH.code
    },
    detailShow() {
      return this.view === 'new'
    },
    approvalShow() {
      return this.view === 'old'
    },
    printShow() {
      return this.view === 'new'
    },
    // 如果是从oa打开，则需需要加上这两个参数
    originOa() {
      let {origin, fullScreen} = this.$route.query
      return origin === 'oa' && fullScreen
    },
    isCreate: function () {
      return this.create === 'create'
    },
    isParseElement() {
      return this.parseElement === '部门意见'
    },
    isParseElementlg() {
      return this.parseElement === '领导意见'
    }
  },
  created() {
    this.isHeadquarter()
    this.baseDataLoad()
    //因为是流程功能，知会、抄送，都需要按照流程抄送的配置打开，
    // 只是知会的需要更新消息表，抄送需要更新日志表
    //判断是知会还是抄送，可以根据param中的参数isNotice判断
    this.obj.functionName = '律所审批'
    const isNotice = this.$route.query.isNotice
    // isNotice在OA中打开会解析成Boolean，系统内会被转成字符"true"
    if (isNotice === true || isNotice === "true") {
      //知会
      if (this.$route.query.processInstanceId !== null && this.$route.query.processInstanceId !== undefined) {
        //保存的数据，获取不到实例ID，只有启动流程实例才能拿到
        this.obj.processInstanceId = this.$route.query.processInstanceId
        this.obj.taskId = this.$route.query.taskId
      }
      const read = this.$route.query.read
      const sid = this.$route.query.sid
      if (read === false || read === 'false') {
        noticeApi.read({sid: sid})
      }
    } else {
      //这里除了抄送会走，其他正常逻辑也会走，所以下面的参数判断了type === 'toRead'，即未读时，才会更新OA消息和日志记录
      this.obj.sid = this.$route.query.sid//消息表中的消息ID 日志表中的日志ID
      if (this.$route.query.processInstanceId !== null && this.$route.query.processInstanceId !== undefined) {
        //保存的数据，获取不到实例ID，只有启动流程实例才能拿到
        this.obj.processInstanceId = this.$route.query.processInstanceId
        this.obj.taskId = this.$route.query.taskId
      }
      const type = this.$route.query.type//获取消息状态类型，toRead-》未读，此时需要更新，haveRead-》已读，就不需要更新了
      if (type === 'toRead') {
        this.obj.pathname = window.location.pathname
        //返回url路径名（https://www.runoob.com/try/try.php?filename=tryjsref_loc_pathname，返回/try/try.php），判断是在法务系统打开还是在OA中打开
        //法务中打开会把相同流程实例的全部消息改为已读，所以是更新OA多条，OA中打开是只更新OA一条
        this.obj.title = "黑名单审批"
        //更新OA需要参数processInstanceId, title, functionName, oldTaskId,
        processApi.finishOATask(this.obj)
      }
    }
  },
  mounted() {
    //挂载完毕后，设置回调函数
    this.$emit('setCallBack', {
      beforeCallBack: this.beforeApproval,//点击办理或提交前的回调，效验必填控制
      afterCallBack: this.afterApproval,//点击办理弹框中再点击确定后的回调，审批完成后处理业务逻辑
      setTaskNodeInfo: this.setTaskNodeInfo,//挂载完毕后执行的回调，用于页面已进入需要处理的业务逻辑
      filterBtn: this.utils.filterBtn,
      beforeConfirmCallBack: this.beforeConfirmCallBack,
      beforeApprovalCb: this.beforeApprovalCb
    })
  },
  methods: {
    beforeConfirmCallBack(data, resolve, reject) {
      const customProperties = data.nodeInfo.customProperties.find(item => item.key === 'FlowCustomPropertiesSettingsServiceImpl')
      if (customProperties && customProperties.value && JSON.parse(customProperties.value)['id'] === "1" && data.approvalFormData.comment == "") {
        // 消息按需求编辑 这只是个示例
        this.$confirm('未填写意见，请确认是否继续。', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          resolve()()
        }).catch(() => {
          reject()
        })
      } else {
        resolve()
      }
    },
    beforeApproval(code, resolve) {
      const me = this
      // 校验必填项
      me.$refs['dataForm'].validate((valid) => {
        if (valid) {
          // 保存
          me.save().then(() => {
            this.mainData.title = "黑名单审批"
            this.mainData.code = code === null ? '' : code
            this.mainData.functionName = '律所审批'
            me.$emit('submit-success', me.mainData, 'id')
            // resolve(true)
            resolve({success: true, formData: {...this.mainData}, approvalOpinion: this.approvalOpinion})

          })
        } else {
          me.$message.success('请填写必填项!')
          resolve(false)
          return false
        }
      })
    },
    setTaskNodeInfo(event) {
      const customProperties = event.customProperties.find(item => item.key === 'FlowCustomPropertiesSettingsServiceImpl')
      if (customProperties !== null && customProperties !== undefined) {
        this.parseElement = JSON.parse(customProperties.value)['name']
      }
      console.log("部门意见：" + this.parseElement)
      //（toDeal-待办处理，haveDealt-已办查看，toRead-未读，haveRead-已读）
      const type = this.$route.query.type
      // 业务ID
      const id = this.$route.query.businessKey
      //是否首环节（submitNode-首环节）
      this.activity = event.taskNodeType
      /*
      * 首环节
      *   1、businessKey-是null，说明是刚发起---执行init方法
      *   2、businessKey-有值
      *       1、回退处理--环节是submitNode   ---执行init
      *       2、已办查看--优先判断  haveDealt---执行load
      *       3、未读查看--优先判断  toRead   ---执行load
      *       4、已读查看--优先判断  haveRead ---执行load
      * */
      if (type === 'haveDealt' || type === 'toRead' || type === 'haveRead') {
        this.loadData(this.utils.formState.VIEW, id)
      } else { // 不是以上3种只能是待处理，只需要判断是否首环节即可
        if (event.taskNodeType === 'submitNode') {
          this.loadData(this.utils.formState.NEW, id)
        } else {
          this.loadData(this.utils.formState.VIEW, id)
        }
      }
    },
    //撤回转办
    beforeApprovalCb(code) {
      debugger
      if (code.type === "cancelTransfer") {
        this.obj.processInstanceId = this.$route.query.processInstanceId
        this.obj.taskId = this.$route.query.taskId
        this.obj.title = "黑名单审批"
      this.obj.code = code === null ? '' : code
      this.obj.functionName = '律所审批'
        let pathname = window.location.pathname
        this.obj.functionCode = 'lawyer_black_list_main'
        processApi.sendOATask(this.obj).then(res => {
          if (pathname === '/base/design_page') {
            this.mcpLayout.closeTab()
          } else {
            window.close()
          }
        })
      }

    },
    afterApproval(code, data) {
      // 回退到首环节，修改业务数据状态，修改任务标题
      console.log("code==" + code)
      console.log("data==" + data)
      this.obj.businessKey = this.mainData.id
      //获取参数，为后续操作准备，processInstanceId和taskId其实在created中赋值了，这里在赋值一次也行
      if (data != null && data.data != null) {
        this.obj.processInstanceId = data.data.id
      }
      if (this.obj.businessKey == null || this.obj.processInstanceId == null) {
        this.obj.processInstanceId = this.$route.query.processInstanceId
        this.obj.taskId = this.$route.query.taskId
      }

      //需要传参数到流程中，这里操作，不限于首环节传参数
      if (this.activity === 'submitNode') {
        lawyerBlackListApi.setParam(this.obj).then(() => {
          console.log("传值成功")
        })
      }
      // 将部分参数传给OA
      this.obj.title = "黑名单审批"
      this.obj.code = code === null ? '' : code
      this.obj.functionName = '律所审批'
      //不是动态节点，给OA传待办
      if (code !== 'dynamic') {
        let loading = this.$loading({
          target: document.querySelector('.sg-page-wrap'),
          lock: false,
          text: '请稍后...',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        })
        processApi.sendOATask(this.obj).then(() => {
          loading.close()
          if (this.originOa) {
            window.close()
          }
        })
      }
    },
    baseDataLoad() {
      dictApi.showSelect({
        dicCode: 'LS-NJQK'
      }).then(response => {
        this.NJQKData = response.data.data
      })
    },
    // 根据数据ID加载数据
    loadData(dataState, dataId) {
      this.dataState = dataState
      this.functionId = this.$route.query.functionId
      const lawFirmId = this.$route.query.lawFirmId
      if (this.$route.query.view !== undefined && this.$route.query.view !== '')
        this.view = this.$route.query.view
      if (this.$route.query.type !== undefined && this.$route.query.type !== '')
        this.type = this.$route.query.type
      if (this.$route.query.create !== undefined && this.$route.query.create !== '')
        this.create = this.$route.query.create
      const me = this
      if (this.$route.query.LawFirmChangeState === 'addBlackList') {
        lawyerFirm.queryDataById({id: lawFirmId}).then(res => {
          const temp = {}
          temp.id = this.utils.createUUID()
          temp.createOgnId = this.orgContext.currentOgnId
          temp.createOgnName = this.orgContext.currentOgnName
          temp.createDeptId = this.orgContext.currentDeptId
          temp.createDeptName = this.orgContext.currentDeptName
          temp.createPsnId = this.orgContext.currentPsnId
          temp.createPsnName = this.orgContext.currentPsnName
          temp.createPsnFullId = this.orgContext.currentPsnFullId
          temp.createPsnFullName = this.orgContext.currentPsnFullName
          temp.createGroupId = this.orgContext.currentGroupId
          temp.createGroupName = this.orgContext.currentGroupName
          temp.createOrgId = this.orgContext.currentOrgId
          temp.createOrgName = this.orgContext.currentOrgName
          temp.createTime = new Date()
          Object.assign(me.mainData, temp)
          let year = new Date().getFullYear()
          this.utils.createKvsequence('LSHMD' + year, 6).then(value => {
            this.mainData.sequenceCode = value.data.kvsequence
          })

          me.mainData.lawyerFirm = res.data.data.lawyerFirm
          me.mainData.sourceId = res.data.data.id
          me.mainData.dataSource = 'old'

          me.mainData.registerAddress = res.data.data.registerAddress
          me.mainData.functionary = res.data.data.functionary
          me.mainData.lawFirmPhone = res.data.data.lawFirmPhone
          me.mainData.lawFirmFax = res.data.data.lawFirmFax
          me.mainData.postalCode = res.data.data.postalCode
          me.mainData.issuingAuthority = res.data.data.issuingAuthority // 主管机关
          me.mainData.licenseCode = res.data.data.licenseCode
          me.mainData.licenseNumber = res.data.data.licenseNumber
          me.mainData.registeredCapital = res.data.data.registeredCapital // 设立资产
          me.mainData.foundTime = res.data.data.foundTime // 成立时间
          me.mainData.workNumber = res.data.data.workNumber // 执业人数
          me.mainData.applyDeptName = res.data.data.applyDeptName // 申请部门/所属公司
          me.mainData.applyDeptId = res.data.data.applyDeptId // 申请部门/所属公司
          me.mainData.annualInspectionName = res.data.data.annualInspectionName // 年检情况
          me.mainData.annualInspectionId = res.data.data.annualInspectionId // 年检情况
          me.mainData.introduction = res.data.data.introduction // 简介
          me.mainData.remark = res.data.data.remark // 备注
          me.mainData.typeCode = res.data.data.typeCode // 库类型编码
          me.mainData.typeName = res.data.data.typeName // 库类型名称
        }).then(() => {
          this.approvalOpinionIs()

        })
      } else {
        lawyerBlackListApi.queryDataById({id: dataId}).then(res => {
          this.mainData = res.data.data
          this.loading = false
        }).then(() => {
          this.approvalOpinionIs()

        })
      }
    },
    submit() {
      if (this.mainData.dataSource === 'new') {
        new Promise((resolve) => {
          lawyerInFirmApi.distinct({id: this.mainData.sourceId, code: this.mainData.licenseCode}).then((res) => {
            const flag = res.data.data
            if (flag) {
              this.$message({
                showClose: true,
                message: '与律所库中的统一社会信用代码重复！',
                type: 'warning'
              })
              this.mainData.licenseCode = null
            }
            resolve(flag)
          })
        }).then(value => {
          if (!value) {
            lawyerBlackListApi.distinct({id: this.mainData.id, code: this.mainData.licenseCode}).then((res) => {
              const flag = res.data.data
              if (flag) {
                this.$message({
                  showClose: true,
                  message: '与黑名单中的律所统一社会信用代码重复！',
                  type: 'warning'
                })
                this.mainData.licenseCode = null
              } else {
                this.save().then(() => {
                  this.$message.success('保存成功！')
                })
              }
            })
          }
        })
      } else {
        this.save().then(() => {
          this.$message.success('保存成功！')
        })
      }
    },
    save() {
      return new Promise((resolve, reject) => {
        this.mainData.updateTime = new Date()
        lawyerBlackListApi.save(this.mainData).then(response => {
          resolve(response)
        }).catch(error => {
          reject(error)
        })
      })
    },
    lawyerFirmClear() {
      this.mainData.lawyerFirm = null
      this.mainData.sourceId = null
      this.mainData.dataSource = 'new'
    },
    chooseLawFirmClick() {
      this.lawFirmVisible = true
    },
    cancel_() {
      this.orgVisible = false
    },
    choiceDeptSure_() {
      let c = ''
      let cid = ''
      this.zxcheckedData.forEach((item) => {
        if (c.length === 0) {
          c = c + item.name
          cid = cid + item.unitId
        } else {
          c = c + ',' + item.name
          cid = cid + ',' + item.unitId
        }
      })
      this.mainData.applyDeptName = c
      this.mainData.applyDeptId = cid
      this.orgVisible = false
    },
    lawFirmSure(val) {
      this.lawFirmVisible = false
      const val_ = JSON.parse(JSON.stringify(val))
      this.mainData.lawyerFirm = val_.lawyerFirm
      this.mainData.sourceId = val_.id
      this.mainData.dataSource = 'old'

      this.mainData.registerAddress = val_.registerAddress // 注册地址/律所地址
      this.mainData.lawFirmPhone = val_.lawFirmPhone
      this.mainData.postalCode = val_.postalCode // 邮政编码
      this.mainData.issuingAuthority = val_.issuingAuthority // 主管机关
      this.mainData.licenseCode = val_.licenseCode // 统一社会信用代码
      this.mainData.licenseNumber = val_.licenseNumber // 批准文号
      this.mainData.registeredCapital = val_.registeredCapital // 设立资产
      this.mainData.foundTime = val_.foundTime // 成立时间
      this.mainData.workNumber = val_.workNumber // 执业人数
      this.mainData.applyDeptName = val_.applyDeptName // 申请部门/所属公司
      this.mainData.applyDeptId = val_.applyDeptId // 申请部门/所属公司
      this.mainData.annualInspectionName = val_.annualInspectionName // 年检情况
      this.mainData.annualInspectionId = val_.annualInspectionId // 年检情况
      this.mainData.introduction = val_.introduction // 简介
      this.mainData.remark = val_.remark // 备注
      this.mainData.typeCode = val_.typeCode // 库类型编码
      this.mainData.typeName = val_.typeName // 库类型名称
    },
    licenseCodeBlur() {
      if (this.mainData.dataSource === 'new') {
        new Promise((resolve) => {
          const id_ = this.mainData.id
          lawyerInFirmApi.distinct({id: id_, code: this.mainData.licenseCode}).then((res) => {
            const flag = res.data.data
            if (flag) {
              this.$message({
                showClose: true,
                message: '与律所库中的统一社会信用代码重复！',
                type: 'warning'
              })
              this.mainData.licenseCode = null
            }
            resolve(flag)
          })
        }).then(value => {
          if (!value) {
            lawFirmBlacklistApi.distinct({id: this.mainData.id, code: this.mainData.licenseCode}).then((res) => {
              const flag = res.data.data
              if (flag) {
                this.$message({
                  showClose: true,
                  message: '与黑名单中的律所统一社会信用代码重复！',
                  type: 'warning'
                })
                this.mainData.licenseCode = null
              }
            })
          }
        })
      }
    },
    chooseApprovalDeptClick() {
      this.isCheckedUser = false
      this.showUser = false
      this.is_Check = false
      this.orgVisible = true
    },
    annualInspectionChange(newVal) {
      this.mainData.annualInspectionName = this.utils.getDicName(this.NJQKData, newVal)
    },
    stopClick() {
      this.$confirm('您确定要终止当前流程吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.obj.processInstanceId = this.$route.query.processInstanceId
        this.obj.taskId = this.$route.query.taskId
        this.obj.title = '黑名单审批'
        this.obj.functionName = '律所审批'
        this.obj.code = 'stop'
        let pathname = window.location.pathname
        new Promise((resolve) => {
          let processInstanceId = this.$route.query.processInstanceId
          processApi.end(processInstanceId).then(res => {
            resolve(res)
          })
        }).then(val => {
          if (val.data.code === 200) {
            this.obj.functionCode = 'firm_black_main'
            processApi.sendOATask(this.obj).then(() => {
              if (pathname === '/base/design_page') {
                this.mcpLayout.closeTab()
              } else {
                window.close()
              }
            })
          }
        })
      }).catch(() => {
        this.$message.info('已取消删除!')
      })
    },
    finishClick() {
      this.$confirm('您确定要结束当前流程吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.obj.processInstanceId = this.$route.query.processInstanceId
        this.obj.taskId = this.$route.query.taskId
        this.obj.title = '黑名单审批'
        this.obj.functionName = '律所审批'
        this.obj.code = 'pass'
        let pathname = window.location.pathname
        new Promise((resolve) => {
          processApi.move({
            proInstId: this.$route.query.processInstanceId,
            taskId: this.$route.query.taskId
          }).then(res => {
            resolve(res)
          })
        }).then(val => {
          if (val.status === 200) {
            this.obj.functionCode = 'firm_black_main'
            processApi.sendOATask(this.obj).then(() => {
              if (pathname === '/base/design_page') {
                this.mcpLayout.closeTab()
              } else {
                window.close()
              }
            })
          }
        })
      }).catch(() => {
        this.$message.info('已取消删除!')
      })
    },
    // 发起知会
    noticeClick(cooperateFunc) {
      //必传参数
      this.noticeParams = {
        ...this.utils.routeState.VIEW(this.mainData.id),//主要作用是后台逻辑需要，其他作用各业务看情况使用
        processInstanceId: this.$route.query.processInstanceId,//流程实例
        taskId: this.$route.query.taskId,//任务ID
        businessKey: this.mainData.id, //业务数据ID
        entranceType: "FLOWABLE", //流程特定的标识
        type: "haveRead",//已处理
        whetherProcess: true,//系统内部打开的时候判断是否是流程，通过不同的方式打开查看
        isNotice: true//告知是知会消息打开的功能，有些按钮可以隐藏,只有知会消息，在created钩子函数中才执行相关，如果是流程抄送不需要传，可以根据上面created描述判断
      }
      this.noticeData.dataId = this.mainData.id
      this.noticeData.moduleName = '黑名单审批'
      this.noticeData.params = this.noticeParams
      this.noticeData.url = 'firm_black_main'//这个需要与功能维护中的值一样
      this.noticeData.title = "黑名单审批"
      cooperateFunc(this.noticeData)
    },
    isHeadquarter() {
      // const createOgnId = this.orgContext.currentOgnId
      //
      // if (createOgnId === '15033708970596') {
        this.typeCode = '1'
        this.typeName = '推荐库'
      // } else {
      //   this.typeCode = '0'
      //   this.typeName = '资源库'
      // }
    },
    detailClick() {
      const me = this
      if (this.mainData.dataStateCode !== this.utils.dataState_BPM.SAVE.code) {
        taskApi.selectTaskId({businessKey: this.mainData.id, isView: 'true'}).then(res => {
          const functionId = res.data.data[0].ID
          const tabId = this.utils.createUUID()

          let urlParam = {
            processInstanceId: res.data.data[0].PID,//流程实例
            taskId: res.data.data[0].ID,//任务ID
            businessKey: this.mainData.id, //业务数据ID
            functionId: functionId, //不传也可以，只有首环节需要传，实例启动就可以不传了
            entranceType: "FLOWABLE",
            type: "haveDealt",
            channel: 'business', //查看渠道，默认就传这个值即可，表示是从业务功能传过来的，首环节提交后过滤掉撤回按钮
            view: 'old',
          }

          if (me.$route.query.origin !== undefined && me.$route.query.origin === 'oa') {
            this.$set(urlParam, "origin", "oa")
            this.$set(urlParam, "fullScreen", "true")
          }

          this.layout.openNewTab("详细信息",
              "design_page",
              "design_page",
              tabId,
              urlParam
          )
        })
      } else {
        taskApi.selectFunctionId({functionCode: 'firm_black_main'}).then(res => {
          const functionId = res.data.data[0].ID
          const tabId = this.utils.createUUID()

          let urlParam = {
            processDefinitionKey: this.mcpDesignPage.processKey,
            functionId: functionId,
            entranceType: "FLOWABLE",
            ...this.utils.routeState.VIEW(this.mainData.id),
            channel: 'business',
            view: 'old',
          }

          if (me.$route.query.origin !== undefined && me.$route.query.origin === 'oa') {
            this.$set(urlParam, "origin", "oa")
            this.$set(urlParam, "fullScreen", "true")
          }

          this.layout.openNewTab("详细信息",
              "design_page",
              "design_page",
              tabId,
              urlParam
          )
        })
      }
    },
    approvalClick() {
      const me = this
      if (this.mainData.dataStateCode !== this.utils.dataState_BPM.SAVE.code) {
        taskApi.selectTaskId({businessKey: this.mainData.id, isView: 'true'}).then(res => {
          const functionId = res.data.data[0].ID
          const tabId = this.utils.createUUID()

          let urlParam = {
            processInstanceId: res.data.data[0].PID,//流程实例
            taskId: res.data.data[0].ID,//任务ID
            businessKey: this.mainData.id, //业务数据ID
            functionId: functionId, //不传也可以，只有首环节需要传，实例启动就可以不传了
            entranceType: "FLOWABLE",
            type: "haveDealt",
            channel: 'business', //查看渠道，默认就传这个值即可，表示是从业务功能传过来的，首环节提交后过滤掉撤回按钮
            view: 'new',
          }

          if (me.$route.query.origin !== undefined && me.$route.query.origin === 'oa') {
            me.$set(urlParam, "origin", "oa")
            me.$set(urlParam, "fullScreen", "true")
          }

          this.layout.openNewTab("审批信息",
              "design_page",
              "design_page",
              tabId,
              urlParam
          )
        })
      } else {
        taskApi.selectFunctionId({functionCode: 'firm_black_main'}).then(res => {
          const functionId = res.data.data[0].ID
          const tabId = this.utils.createUUID()

          let urlParam = {
            processDefinitionKey: this.mcpDesignPage.processKey,
            functionId: functionId,
            entranceType: "FLOWABLE",
            ...this.utils.routeState.VIEW(this.mainData.id),
            channel: 'business',
            view: 'new',
          }

          if (me.$route.query.origin !== undefined && me.$route.query.origin === 'oa') {
            this.$set(urlParam, "origin", "oa")
            this.$set(urlParam, "fullScreen", "true")
          }

          this.layout.openNewTab("审批信息",
              "design_page",
              "design_page",
              tabId,
              urlParam
          )
        })
      }
    },
    printClick() {
      this.$print(this.$refs.dataForm)
    },
    approvalOpinionIs() {
      orgApi.roleCheck({orgId: this.orgContext.currentOrgId, roleName: 'LDYJ'}).then(res => {
        console.log('approvalOpinionIs：', res)
        this.approvalIs = !(this.type !== 'toDeal' || res.data.data === false)
      })
    },
  }
}
</script>

<style>
.el-dialog-div {
  height: 60vh;
  overflow: auto;
}
</style>
