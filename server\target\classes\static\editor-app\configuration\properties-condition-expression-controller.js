/*
 * Activiti Modeler component part of the Activiti project
 * Copyright 2005-2014 Alfresco Software, Ltd. All rights reserved.
 * 
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.

 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA  02110-1301  USA
 */

/*
 * Condition expression
 */

var KisBpmConditionExpressionCtrl = [ '$scope', '$modal', function($scope, $modal) {

    // Config for the modal window
    var opts = {
        template:  'activiti-editor/configuration/properties/condition-expression-popup.html?version=' + Date.now(),
        scope: $scope
    };

    // Open the dialog
    $modal(opts);
}];

var KisBpmConditionExpressionPopupCtrl = [ '$scope', '$translate', '$http', function($scope, $translate, $http) {
    $scope.businessVariables = [];
    $scope.conditions = ["==",">","<",">=","<="];
    $scope.selectedVariable = null;
    $scope.inputValue = null;
    $scope.conditionExpression = {value: ''};
    $scope.gridData = [[],[],[],[],[]];
    $scope.grids = [0];
    $scope.selectGrids = 0;
    $scope.columnDefs = [
        { field: 'gx', displayName: "逻辑关系" },
        { field: 'name', displayName: "业务变量" },
        { field: 'conditions', displayName: "条件"},
        { field: 'inputValue', displayName: "值"}
    ];
    $scope.selectedListeners = [];

	// Put json representing condition on scope
   if ($scope.property.value !== undefined && $scope.property.value !== null) {

        $scope.conditionExpression = {value: $scope.property.value};
        
    } else {
        $scope.conditionExpression = {value: ''};
    }

    $scope.gridOptions = [{
        data: "gridData[0]",
        enableRowReordering: true,
        headerRowHeight: 28,
        multiSelect: false,
        keepLastSelected : false,
        selectedItems: $scope.selectedListeners,
        columnDefs: $scope.columnDefs,
        index:0

    }];
    $http.post('/mcp/wfl/business/variables/query', {modelId : EDITOR.UTIL.getParameterByName('modelId')})
        .success(function(response) {
            $scope.businessVariables = response.rows
        });
   /* $http.post('/mcp/wfl/business/variables/query', {modelId : 10001})
        .success(function(response) {
            $scope.businessVariables = response.rows
            if ($scope.property.value !== undefined && $scope.property.value !== null) {
                var values = $scope.property.value.substring(2, $scope.property.value.length-1).split(" ");
                debugger;
                if($scope.conditions.indexOf(values[1]) > -1){
                    $scope.selectedVariable = values[0];
                    $scope.selectedcondition = values[1];
                    $scope.inputValue = values[2];
                }else{
                    $scope.conditionExpression = {value: $scope.property.value};
                }

            }

        });*/

    // Click handler for add button
    $scope.addNewListener = function() {
        $scope.gridData[$scope.selectGrids].push({
            gx:"AND",
            name : '',
            conditions : '',
            inputValue: '',
            conditionExpression : null,
            index:$scope.selectGrids
        });
    };

    $scope.removeListener = function() {
        if ($scope.selectedListeners.length > 0) {
            var index = $scope.gridData[$scope.selectGrids].indexOf($scope.selectedListeners[0]);
            $scope.gridData[$scope.selectGrids].splice(index, 1);
            $scope.listenerDetailsChanged();
        }
    };
    $scope.save = function() {
       /* if($scope.selectedVariable != null && $scope.selectedcondition != null &&  $scope.inputValue != null){
            $scope.conditionExpression.value = "${" +  $scope.selectedVariable + " " + $scope.selectedcondition + " " + $scope.inputValue +"}";
        }
        /!*var conditionEasyExpression = "${" +  $scope.selectedVariable + $scope.selectedcondition + $scope.inputValue +"}";
        console.log(conditionEasyExpression);*!/*/
        $scope.property.value = $scope.conditionExpression.value;
        $scope.updatePropertyInModel($scope.property);
        $scope.close();
    };

    // Close button handler
    $scope.close = function() {
    	$scope.property.mode = 'read';
    	$scope.$hide();
    };
    
    $scope.listenerDetailsChanged = function () {
        debugger;
        if ($scope.selectedListeners.length > 0) {
            var index = $scope.gridData[$scope.selectGrids].indexOf($scope.selectedListeners[0]);
            if(index > -1){
                $scope.gridData[$scope.selectGrids][index].conditionExpression =  $scope.selectedListeners[0].name + $scope.selectedListeners[0].conditions + $scope.selectedListeners[0].inputValue;
            }
        }
        var conditionExpression = "${"
        for(var i=0,len=$scope.gridData.length;i<len;i++){
            var data = $scope.gridData[i];
            //console.log(data);
            if(data.length > 0){
                if(i>0) {
                    conditionExpression += "||";
                }
                conditionExpression += "(" + data[0].conditionExpression;
                for(var j=1,len1=data.length;j<len1;j++){
                    conditionExpression += "and" + data[j].conditionExpression;
                }
                conditionExpression +=")";
            }
        }
        conditionExpression +="}";
        $scope.conditionExpression = {value: conditionExpression};
    }

    $scope.addGroupListener = function (){
        var option = {
            data: "gridData["+ $scope.grids.length +"]",
                enableRowReordering: true,
            headerRowHeight: 28,
            multiSelect: false,
            keepLastSelected : false,
            selectedItems: $scope.selectedListeners,
            columnDefs: $scope.columnDefs,
            index:0

        };
        $scope.gridOptions.push(option);
        $scope.grids.push($scope.grids.length);
    }

    $scope.removeGroupListener = function (){
        var index = $scope.grids.indexOf($scope.selectGrids);
        $scope.grids.splice(index, 1);
        $scope.gridData[index] = [];
        $scope.listenerDetailsChanged();
        if($scope.selectGrids > -1){
            $scope.selectGrids--;
        }
        //$scope.selectedListeners = [];querySelectorAll
    }

    $scope.gridsClick = function (t){
        debugger
        //console.log(t.this.$parent.item);
        /*if ($scope.selectedFields.length > 0) {
            var index = $scope.selectedListeners[0].fields.indexOf($scope.selectedFields[0]);
            $scope.gridFieldOptions.selectItem(index, false);
            $scope.selectedListeners[0].fields.splice(index, 1);

            $scope.selectedFields.length = 0;
            if (index < $scope.selectedListeners[0].fields.length) {
                $scope.gridFieldOptions.selectItem(index + 1, true);
            } else if ($scope.selectedListeners[0].fields.length > 0) {
                $scope.gridFieldOptions.selectItem(index - 1, true);
            }
        }*/
        /*for (var i=0,len=$scope.selectedListeners.length;i<len;i++){
            if($scope.selectedListeners[i].index != t.this.$parent.item){
                $scope.gridOptions[$scope.selectGrids].selectItem(i,false);
            }
        }*/

        $scope.selectGrids = t.this.$parent.item;
    }

    
}];