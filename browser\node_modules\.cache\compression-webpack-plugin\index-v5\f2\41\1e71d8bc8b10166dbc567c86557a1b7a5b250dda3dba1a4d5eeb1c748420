
774e56<PERSON><PERSON>cff2c7d96d31f8dee9857d1c91ef2a	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.350.1754018536329.js\",\"contentHash\":\"136a40fdb71f36ec47b7d19e95202b61\"}","integrity":"sha512-CDPChmOeYwA9VsXPP/Cs32RhYlwN3gpCJKkRHHfY2IMfyeZpV/brg/eltgtrneAc9c1azoeA49k6gAO+VDzGzg==","time":1754018576018,"size":123453}