<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.klaw.dao.complianceOrganizationDao.OrganizationMemberMapper">

    <resultMap id="BaseResultMap" type="com.klaw.entity.complianceOrganizationBean.OrganizationMember">
            <id property="id" column="id" jdbcType="VARCHAR"/>
            <result property="memberCode" column="member_code" jdbcType="VARCHAR"/>
            <result property="memberName" column="member_name" jdbcType="VARCHAR"/>
            <result property="organization" column="organization" jdbcType="VARCHAR"/>
            <result property="position" column="position" jdbcType="VARCHAR"/>
            <result property="graduationSchool" column="graduation_school" jdbcType="VARCHAR"/>
            <result property="professionalQualification" column="professional_qualification" jdbcType="VARCHAR"/>
            <result property="jobStatus" column="job_status" jdbcType="VARCHAR"/>
            <result property="createOgnId" column="create_ogn_id" jdbcType="VARCHAR"/>
            <result property="createOgnName" column="create_ogn_name" jdbcType="VARCHAR"/>
            <result property="createDeptId" column="create_dept_id" jdbcType="VARCHAR"/>
            <result property="createDeptName" column="create_dept_name" jdbcType="VARCHAR"/>
            <result property="createGroupId" column="create_group_id" jdbcType="VARCHAR"/>
            <result property="createGroupName" column="create_group_name" jdbcType="VARCHAR"/>
            <result property="createPsnId" column="create_psn_id" jdbcType="VARCHAR"/>
            <result property="createPsnName" column="create_psn_name" jdbcType="VARCHAR"/>
            <result property="createOrgId" column="create_org_id" jdbcType="VARCHAR"/>
            <result property="createOrgName" column="create_org_name" jdbcType="VARCHAR"/>
            <result property="createPsnFullId" column="create_psn_full_id" jdbcType="VARCHAR"/>
            <result property="createPsnFullName" column="create_psn_full_name" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="dataState" column="data_state" jdbcType="VARCHAR"/>
            <result property="dataStateCode" column="data_state_code" jdbcType="INTEGER"/>
            <result property="roleId" column="role_id" jdbcType="VARCHAR"/>
            <result property="enableTime" column="enable_time" jdbcType="VARCHAR"/>
            <result property="disableTime" column="disable_time" jdbcType="VARCHAR"/>
            <result property="description" column="description" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,member_code,member_name,
        organization,position,graduation_school,
        professional_qualification,job_status,create_ogn_id,
        create_ogn_name,create_dept_id,create_dept_name,
        create_group_id,create_group_name,create_psn_id,
        create_psn_name,create_org_id,create_org_name,
        create_psn_full_id,create_psn_full_name,create_time,
        update_time,data_state,data_state_code,
        role_id,enable_time,disable_time,
        description
    </sql>




    <!-- 查询合规人员数量 -->
    <select id="countCompliancePersonnel" resultType="int">
        SELECT COUNT(*) FROM compliance_personnel
    </select>

</mapper>
