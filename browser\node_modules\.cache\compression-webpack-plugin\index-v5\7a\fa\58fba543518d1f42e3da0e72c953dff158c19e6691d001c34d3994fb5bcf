
7f643cd43550a3d2543c6a4845c3181d4ac7df71	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.288.1754018536329.js\",\"contentHash\":\"da8941ccbb38e85655461a36ef4c2f35\"}","integrity":"sha512-ldf8SVnPOiW0k3CoZBG6uWqkQDBo9pwu7ULInghtbhI7ya3k0mzgWCjxwREYwEXlwZ3n6r3BBqCsi4EzrVm7GQ==","time":1754018575964,"size":116967}