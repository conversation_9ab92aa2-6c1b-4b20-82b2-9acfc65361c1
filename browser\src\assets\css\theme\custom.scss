@import "../../../style/mixins";
$colors-list:#9C28B1 #409EFF #FF0000;
$colors-value-list:'9C28B1' '409EFF' 'FF0000';

//自定义主题
@for $i from 1 to length($colors-list)+1{
  .custom-#{nth($colors-value-list,$i)} {

    %bg-color{
      background-color: nth($colors-list,$i);
    }
    //header 样式
    .el-header.navbar.app-header,
    .main-tabs-wrap .el-tabs__header .is-active:before{
      @extend %bg-color;
    }

    // main区域上方tab文字颜色
    .main-tabs-wrap .el-tabs__item span.el-icon-close,
    .rightMenuProp>li.items:hover{
      color:nth($colors-list,$i);
    }

    .sg-dialog-wrap{
      //重写dialog时间输入框的样式
      .el-date-editor.el-input,
      .el-input__inner{
        width: 180px !important;
      }

      .sg-txt-rate .el-input__inner{
        width: 130px !important;
      }

      .sg-small-input {
        .el-input__inner{
          width:80px !important;
        }
        div.el-input{
          width:80px !important;
        }
      }
      .sg-dialog-pagination input[type=number].el-input__inner{
        width: 30px !important;
      }

      //重写dialog下拉框的样式
      div.el-select{
        width: 190px !important;
      }
      div.el-select--small{
        width: 180px !important;
      }
      
      .el-dialog__body{
        padding-top: 0px !important;
      }

      //重写弹出层td的padding
      .el-table td{
        padding:0px 0;
      }
      //错误信息提示样式
      .el-form-item__error{
        padding-top: 0px;
      }
    }

    .sg-dialog-wrap-table{
      .el-form{
        width: 100%;
      }
      .el-dialog__body{
        padding-left: 0px;
      }
      .el-dialog__header{
        margin-bottom: 0px;
      }
    }

    %height{
      height: 45px;
      line-height: 45px;
    }

    //重写窗口左侧菜单高度
    .left-menu-wrap{
      overflow: hidden !important;
      .el-submenu__title,.el-submenu .el-menu-item{
        @extend %height;
      }
      .el-menu-item-group__title{
        padding: 0;
      }
    }

    //重写左侧菜单缩小模式下子菜单的高度
    .left-sub-menu{
      .el-menu-item{
        @extend %height;
      }
      .el-menu-item-group__title{
        padding: 0;
      }
    }

    //重写弹出框的最小宽度
    .el-popover{
      min-width: 10px;
    }
    .sg-theme-popover{
      border-width: 0px;
      @include box-shadow(0 0px 3px 0 rgba(0,0,0,.3));
    }

    .sg-left-menu{
      border-right-width: 0px;
    }

    //重写弹出层下拉框右侧箭头位置
    .el-dialog__body .el-select .el-input__suffix{
      right: 15px !important;
    }

    //用户有关操作菜单颜色
    .popoverList {
      width: 100%;
      li:hover {
        @extend %bg-color;
        color: #ffffff;
      }
    }

    //重写按钮大小
    .el-button--medium{
      padding: 10px 16px;
    }

    //主区域 tr 高度调整
    .main-tabs-wrap .el-table td
    {
      padding: 0;
    }
    .el-table th{
      padding: 8px 0;
    }
    .main-tabs-wrap .elx-editable .elx-editable-column,
    .main-tabs-wrap .el-table__row
    {
      height: 40px;
    }

    //列表复选框右侧边框
    .el-table--border th.sg-selection-column,
    .el-table--border td.sg-selection-column,
    .el-table--border th.sg-radio-column,
    .el-table--border td.sg-radio-column{
      border-right: 0;

      .el-checkbox:last-of-type{
        margin-right: 0 !important;
      }
    }


    //table复选框列样式修改
    .el-table .sg-selection-column .cell{
      padding-right: 0;
    }

    //主题选择
    .sg-theme-button {
      @extend %bg-color;
    }
    .colorIcon {
      @extend %bg-color;
    }
    //自定义主题色
    .sg-active-color{
      color:nth($colors-list,$i);
    }

    .sg-tv-wrap {
      .table-header{
        background-color: nth($colors-list,$i);
      }
      .table-wrap{
        border: nth($colors-list,$i) 2px solid;
      }
    }
  }
}

