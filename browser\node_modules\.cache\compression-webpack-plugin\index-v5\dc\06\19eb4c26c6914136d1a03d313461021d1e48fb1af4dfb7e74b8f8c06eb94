
2c318e10f182987679c772b159910e48ccc1e1d9	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.236.1754018536329.js\",\"contentHash\":\"3953e78e3edc89f8ef87c6b5fe39a430\"}","integrity":"sha512-FtrVNXgzliCz6AuG+D9r5NZA6VgrRr97Dc3lznXMvZ2iZFRg61ljk7zfVwna6N2pn6VonZVMPB5lZrsQBGxvAw==","time":1754018576060,"size":218539}