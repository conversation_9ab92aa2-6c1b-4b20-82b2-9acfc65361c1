package com.klaw.service.imp.caseServiceImpl;



import com.alibaba.fastjson.JSONObject;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.klaw.dao.caseDao.CaseProjectMapper;
import com.klaw.entity.caseBean.CaseProject;
import com.klaw.entity.caseBean.child.MiddleRelation;
import com.klaw.service.caseService.CaseProjectService;
import com.klaw.service.caseService.childService.MiddleRelationService;
import com.klaw.utils.DataAuthUtils;
import com.klaw.utils.PageUtils;
import com.klaw.utils.Utils;
import com.klaw.vo.Json;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@Service
public class CaseProjectServiceImpl extends ServiceImpl<CaseProjectMapper, CaseProject> implements CaseProjectService {
    @Autowired
    private MiddleRelationService middleRelationService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveData(CaseProject caseProject)  {

        //插入或更新群诉案件数据
//        insertOrUpdateAllColumn(caseProject);
        saveOrUpdate(caseProject);

        //保存关联纠纷、关联案件
        middleRelationService.saveData(caseProject.getId(),new String[]{"case","caseProject"},caseProject.getRelationCases());


    }

    @Override
    public CaseProject queryDataById(String id) {
        CaseProject caseProject = getById(id);
        Map<String, List<MiddleRelation>> map = middleRelationService.queryData(id, "caseProject",new String[]{"case"});
        if(!map.isEmpty()){
            map.replaceAll((key, valueList) -> valueList.stream()
                    .filter(mr -> !(mr.getRelationData() == null && mr.getAssociatedData() == null))
                    .collect(Collectors.toList()));
        }
        caseProject.setRelationCases(map.get("case"));
        return caseProject;
    }

    @Override
    public Page<CaseProject> queryPageData(JSONObject json)  {

        boolean isQuery = json.containsKey("isQuery") ? json.getBoolean("isQuery") : true;
        String fuzzyValue = json.containsKey("fuzzyValue") ? json.getString("fuzzyValue") : null;
        String projectName = json.containsKey("projectName") ? json.getString("projectName") : null;
        String projectType = json.containsKey("projectType") ? json.getString("projectType") : null;
        String belongPlate = json.containsKey("belongPlate") ? json.getString("belongPlate") : null;;
        Long functionId = json.containsKey("functionId") ? json.getLong("functionId") : null;
        String orgId = json.containsKey("orgId") ? json.getString("orgId") : "";

        //立项时间(最小值)
        Date setTimeMin = json.containsKey("setTimeMin") ? json.getDate("setTimeMin") : null;
        //立项时间(最大值)
        Date setTimeMax = json.containsKey("setTimeMax") ? json.getDate("setTimeMax") : null;

        String orderCol = json.containsKey("orderCol") ? json.getString("orderCol") : null;
        boolean orderColValue = json.containsKey("orderColValue") ? json.getBoolean("orderColValue") : false;

        QueryWrapper<CaseProject> wrapper = new QueryWrapper<CaseProject>();


        if (StringUtils.isNotBlank(projectName)) {
            wrapper.and(i ->i.like("project_name", projectName));
        }
        if (StringUtils.isNotBlank(projectType)) {
            wrapper.and(i ->i.like("project_type", projectType));
        }
        if (StringUtils.isNotBlank(belongPlate)){
            wrapper.and(i ->i.like("belong_plate", belongPlate));
        }
        if(setTimeMin != null){
            wrapper.and(i ->i.ge("set_time",setTimeMin));
        }
        if(setTimeMax != null){
            wrapper.and(i ->i.le("set_time",setTimeMax));
        }
        if (StringUtils.isNotBlank(fuzzyValue)) {
            String[] cols = {"PROJECT_NAME", "BELONG_PLATE", "PROJECT_TYPE", "CREATE_OGN_NAME",  "CREATE_PSN_NAME"};
//            wrapper.andNew();
            int i = 0;
            for (String col : cols) {
                if (i == 0) {
                    wrapper.like(col, fuzzyValue);
                } else {
                    wrapper.or().like(col, fuzzyValue);
                }
                i++;
            }
        }


            //数据权限
//            Utils.dataPermSql(wrapper,isQuery ? "belong_Plate_Full_Id" : null,isQuery);
        //陈杰加的移交判断逻辑
        Long functionId2 = DataAuthUtils.getFunctionIdByCode("case_project_view");
        if(isQuery){
            DataAuthUtils.dataPermSql(wrapper, null, functionId2, orgId);
        }else{
            wrapper.like("create_psn_full_id", orgId);
        }



        if(StringUtils.isNotBlank(orderCol)){
            wrapper.orderBy(true, orderColValue,Utils.humpToLine2(orderCol));
        }
        wrapper.orderBy(true, false,"create_time");

        return page(new PageUtils<CaseProject>(json), wrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Json deleteDataById(String id) {
        boolean flag = removeById(id);
        QueryWrapper<MiddleRelation> wrapper =  new QueryWrapper<MiddleRelation>().eq("relation_id",id).or().eq("associated_id",id);
        List<MiddleRelation> middleRelations = middleRelationService.list(wrapper);
        List<String> strList = new ArrayList<>();
        for (int i = 0; i <middleRelations.size() ; i++) {
            strList.add(middleRelations.get(i).getId());
        }
        if (strList!=null&&!strList.isEmpty()&&strList.size()>0){
            middleRelationService.removeByIds(strList);
        }
        if(!flag){
            return Json.fail().msg("未找到需要删除的数据");
        }
        return Json.succ().msg("删除成功!");
    }
}
