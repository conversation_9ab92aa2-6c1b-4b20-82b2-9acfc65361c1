<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.klaw.dao.basicDao.LineChartMapper">

    <select id="queryMoney" resultType="java.util.Map">
        SELECT
            COALESCE( bm_contract_sum.after_change_amount_rmb_all, 0 ) AS 'money'
        FROM
            (
                SELECT
                    '2024-01' AS month_y UNION
                SELECT
                    '2024-02' UNION
                SELECT
                    '2024-03' UNION
                SELECT
                    '2024-04' UNION
                SELECT
                    '2024-05' UNION
                SELECT
                    '2024-06' UNION
                SELECT
                    '2024-07' UNION
                SELECT
                    '2024-08' UNION
                SELECT
                    '2024-09' UNION
                SELECT
                    '2024-10' UNION
                SELECT
                    '2024-11' UNION
                SELECT
                    '2024-12'
            ) months
                LEFT JOIN (
                SELECT
                    DATE_FORMAT( create_time, '%Y-%m' ) AS month_y,
                    SUM( after_change_amount_rmb ) AS after_change_amount_rmb_all
                FROM
                    bm_contract
                WHERE
                    revenue_expenditure = #{revenueExpenditure}
                        AND YEAR( create_time ) = YEAR( CURDATE( ) )
        GROUP BY
            DATE_FORMAT( create_time, '%Y-%m' )
            ) bm_contract_sum ON months.month_y = bm_contract_sum.month_y
        ORDER BY
            months.month_y
    </select>
</mapper>
