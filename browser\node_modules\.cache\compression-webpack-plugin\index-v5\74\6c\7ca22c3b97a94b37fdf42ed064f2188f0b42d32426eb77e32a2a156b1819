
2fd7c0d93ed5a151f53217c5caf7d75229c23b36	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.164.1754018536329.js\",\"contentHash\":\"e8ec4680ea4fa62c3c6ffece356c50e5\"}","integrity":"sha512-jmTKFrSeyOyaYsNVLo5CqE2PmIv8hERi8EYVyxY54jqA+KO5H5gp8peCAmb9l4rwmETYUBAU2soN1Gq9GdL6GA==","time":1754018575960,"size":72092}