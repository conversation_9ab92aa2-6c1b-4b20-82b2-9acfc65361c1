import {request} from '@/api/index'

export default {
  query(data) {
    return request({
      url: '/lawyer/query',
      method: 'post',
      data
    })
  },
  save(data) {
    return request({
      url: '/lawyer/save',
      method: 'post',
      data
    })
  },
  delete(data) {
    return request({
      url: '/lawyer',
      method: 'delete',
      data
    })
  },
  queryDataById(data) {
    return request({
      url: '/lawyer/queryDataById',
      method: 'post',
      data
    })
  },
  updateState(data) {
    return request({
      url: '/lawyer/updateState',
      method: 'post',
      data
    })
  },
  deleteById(data) {
    return request({
      url: '/lawyer',
      method: 'delete',
      data
    })
  },
  /**
   * 初始数据 条件isMind
   * @param {*} data 
   * @returns 
   */
  queryMind(data) {
    return request({
      url: '/lawyer/queryMind',
      method: 'post',
      data
    })
  }
}

