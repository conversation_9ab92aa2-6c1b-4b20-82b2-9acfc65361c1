server.port=8081
server.servlet.context-path=/mcp

spring.redis.database=1
spring.redis.host=************
spring.redis.port=6379
spring.redis.password=
spring.redis.lettuce.pool.max-active=8
spring.redis.lettuce.pool.max-wait=-1
spring.redis.lettuce.pool.max-idle=8
spring.redis.lettuce.pool.min-idle=0
spring.datasource.url=*******************************************
spring.datasource.username=mcp2301
spring.datasource.password=mcp2301
spring.datasource.driver-class-name=oracle.jdbc.OracleDriver
license.licensePath=/license/mcplicense.lic
activiti.mailServerHost=smtp.126.com
activiti.mailServerPort=465
activiti.mailServerUsername=<EMAIL>
activiti.mailServerPassword=mcp_dev126
activiti.mailServerAuthorizationPassword=mcpdev126

swagger.url.patterns=/dynamic/**,/dynamicmd/**,/pagedesigner/**