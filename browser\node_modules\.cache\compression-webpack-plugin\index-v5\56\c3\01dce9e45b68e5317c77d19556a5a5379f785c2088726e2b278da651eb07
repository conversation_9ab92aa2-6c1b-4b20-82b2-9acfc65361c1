
95d35e05024337da5c79edba118fe9b69e5e2453	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.33.1754018536329.js\",\"contentHash\":\"468088c1755bda56c6509dee28aa4b6d\"}","integrity":"sha512-4bOF3A7RZvX2a5B6sa+LoamPcw7IyAbI4V7f/TJXx60NG9GyHQ8fh7w7vkVpldEoJcFIC309cohSfynhYcnzqg==","time":1754018575958,"size":60163}