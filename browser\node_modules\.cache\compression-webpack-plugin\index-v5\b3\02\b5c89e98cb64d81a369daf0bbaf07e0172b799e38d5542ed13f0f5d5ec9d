
2e744026e4e5ac51db39f1660013674b1a12439f	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.356.1754018536329.js\",\"contentHash\":\"fbc33d68c0eb9cc5d92b74f7bde7959b\"}","integrity":"sha512-jN6A38KW54KHzC4chrZZ6hsJlT84EFMWNTKRYgXg+7Sp5lRecfj6WZY5CTWhtPP2VJ3NttxjbTQGqbUxw/1TpA==","time":1754018575975,"size":69785}