import {request} from '@/api/index'

export default {
    query(data) {
        return request({
            url: '/lawFirmInApproval/query',
            method: 'post',
            data
        })
    },
    querys(data) {
        return request({
            url: '/lawFirmInApproval/querys',
            method: 'post',
            data
        })
    },
    save(data) {
        return request({
            url: '/lawFirmInApproval/save',
            method: 'post',
            data
        })
    },
    queryDataById(data) {
        return request({
            url: '/lawFirmInApproval/queryDataById',
            method: 'post',
            data
        })
    },
    delete(data) {
        return request({
            url: '/lawFirmInApproval/delete',
            method: 'post',
            data
        })
    },
    distinct(data) {
        return request({
            url: '/lawFirmInApproval/distinct',
            method: 'post',
            data
        })
    },
    changeLawFirm(data) {
        return request({
            url: '/lawFirmInApproval/changeLawFirm',
            method: 'post',
            data
        })
    }
}
