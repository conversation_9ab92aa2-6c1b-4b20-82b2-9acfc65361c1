import {request} from '@/api/index'

export default {

  query(data) {
    return request({
      url: '/sys_notice/query',
      method: 'post',
      data
    })
  },
  read(data) {
    return request({
      url: '/sys_notice/read',
      method: 'post',
      data
    })
  },
  save(data) {
    return request({
      url: '/sys_notice/save',
      method: 'post',
      data
    })
  },
  queryById(data) {
      return request({
          url: '/sys_notice/queryById',
          method: 'post',
          data
      })
  }
}
