<template>
  <el-dialog :close-on-click-modal="false" title="律所信息" :visible.sync="dialogVisible" width="70%">
    <div style="margin-bottom: 5px">
      <el-row>
        <el-col :span="4">
          <el-input v-model="tableQuery.fuzzyValue" clearable placeholder="模糊搜索" @clear="empty_"
                    @keyup.enter.native="select_"/>
        </el-col>
        <el-col :span="2">
          <el-button type="primary" @click="select_">查询</el-button>
        </el-col>
      </el-row>
    </div>

    <div>
      <el-table
          v-loading="tableLoading"
          :data="tableData"
          border
          style="table-layout: fixed;width: 99%;"
          :height="dialog_height"
          :fit="true"
          stripe
          highlight-current-row
          @current-change="lawFirmCurrentChange"
          @selection-change="lawFirmSelectionChange"
      >
        <el-table-column v-if="isMultiple" type="selection" width="55"/>
        <el-table-column type="index" label="序号"/>
        <el-table-column prop="lawyerFirm" label="律所名称" min-width="100" show-overflow-tooltip/>
        <el-table-column prop="licenseCode" label="统一社会信用代码" min-width="100" show-overflow-tooltip/>
        <el-table-column prop="functionary" label="负责人" min-width="100" show-overflow-tooltip/>
<!--        <el-table-column prop="typeName" label="所属库" min-width="100" show-overflow-tooltip/>-->
        <el-table-column prop="beGoodAtDomain" label="擅长领域" min-width="200" show-overflow-tooltip/>
      </el-table>
    </div>

    <div style="text-align: center">
      <el-footer>
        <pagination
            v-show="tableQuery.total>0"
            :total="tableQuery.total"
            :page.sync="tableQuery.page"
            :limit.sync="tableQuery.limit"
            style="float: left;padding:20px 16px;"
            @pagination="refreshData"
        />
      </el-footer>
    </div>
    <span slot="footer" class="dialog-footer">
          <el-button icon="" class="negative-btn" @click="cancel_">取消</el-button>
          <el-button type="primary" icon="" class="active-btn" @click="sure_">确定</el-button>
        </span>
  </el-dialog>
</template>

<script>
import lawFirmApi from '@/api/LawyerManage/LawyerFirm/lawyerFirm'
import pagination from "@/components/Pagination"
import textSpan from '@/view/components/TextSpan/TextSpan'
import {mapGetters} from 'vuex'

export default {
  name: 'LawFirmDialog',
  components: {pagination, textSpan},
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    isMultiple: {
      type: Boolean,
      default: true
    },
    lawFirmState: {
      type: String,
      default: null
    },
    lawTypeCode: {
      type: String,
      default: "0"
    },
    lawFirmType: {
      type: String,
      default: null
    },
  },
  computed: {
    ...mapGetters(['orgContext']),
  },
  data() {
    return {
      mydialogVisible: this.dialogVisible,
      tableLoading: true,
      tableQuery: {// 分页
        lawFirmType: null,
        page: 1,
        limit: 10,
        total: 0,
        name: null,
        val: null,
        tag: null,
        showAll: true,
        isQuery: false,
        fuzzyValue: '',
        dataState: ''
      },
      tableData: [], // 列表数据源
      dialog_height: 'calc(100vh - 450px)', // table高度
      tableTempData: null,
      myLawFirmState: null,
      myLawTypeCode: null,
      myLawFirmType: null,
    }
  },
  watch: {
    dialogVisible(val) {
      this.mydialogVisible = val
      if (val) {
        this.refreshData()
      }
    },
    mydialogVisible(val) {
      this.$emit('update:dialogVisible', val)
    },
    lawFirmState: {
      handler(newVal) {
        this.myLawFirmState = newVal
      },
      immediate: true
    },
    lawTypeCode: {
      handler(newVal) {

        this.myLawTypeCode = newVal
      },
      immediate: true
    },
    lawFirmType: {
      handler(newVal) {
        this.myLawFirmType = newVal
      },
      immediate: true
    },
  },
  methods: {
    select_() {
      this.refreshData()
    },
    empty_() {
      this.tableQuery = {
        lawFirmType: null,
        page: 1,
        limit: 10,
        total: 0,
        name: null,
        val: null,
        tag: null,
        showAll: true,
        isQuery: false,
        fuzzyValue: '',
        dataState: this.state
      }
      this.refreshData()
    },
    lawFirmCurrentChange(currentRow) {
      this.tableTempData = currentRow
    },
    lawFirmSelectionChange(selection) {
      this.tableTempData = selection
    },
    refreshData() {
      this.tableQuery.dataStateArray = this.myLawFirmState
      this.tableQuery.currentOgnId = this.orgContext.currentOgnId
      this.tableQuery.typeCode = this.myLawTypeCode
      this.tableQuery.lawFirmType = this.myLawFirmType
      console.log(this.tableQuery.lawFirmType);
      lawFirmApi.queryForSelect(this.tableQuery).then(res => {
        this.tableData = res.data.page.records
        this.tableQuery.total = res.data.page.total
        this.tableLoading = false
      })
    },
    cancel_() {
      this.mydialogVisible = false
    },
    sure_() {
      this.$emit('lawFirmSure', this.tableTempData)
    }
  }
}
</script>

<style scoped>

</style>
