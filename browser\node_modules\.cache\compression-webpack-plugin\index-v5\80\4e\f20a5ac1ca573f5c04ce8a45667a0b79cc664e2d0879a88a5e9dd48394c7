
c76523c32c54bdcfd7a34e9550200c29c289f169	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.77.1754018536329.js\",\"contentHash\":\"d72900afea96814466b02cddd0cb87b3\"}","integrity":"sha512-hkCiIkDajX4MfT0KSRVIwQAmkmOtMhoam0GHJwqaDGmVQIeUV9q2DT47WoWS5UH5OW6M5S2sTnJVJPIjVqnHUg==","time":1754018575959,"size":68028}