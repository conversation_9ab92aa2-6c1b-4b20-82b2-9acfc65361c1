<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.klaw.dao.lawyerDao.LawFirmEvaluateMainMapper">
    <resultMap id="BaseResultMap" type="com.klaw.entity.lawyerBean.LawFirmEvaluateDetail">
        <id column="ID" jdbcType="VARCHAR" property="id"/>
        <result column="PARENT_ID" jdbcType="VARCHAR" property="parentId"/>
        <result column="LAW_FIRM_NAME" jdbcType="VARCHAR" property="lawFirmName"/>
        <result column="LAW_FIRM_ID" jdbcType="VARCHAR" property="lawFirmId"/>
        <result column="LAWYER_NAME" jdbcType="VARCHAR" property="lawyerName"/>
        <result column="LAWYER_ID" jdbcType="VARCHAR" property="lawyerId"/>
        <result column="SELECTION_TYPE_NAME" jdbcType="VARCHAR" property="selectionTypeName"/>
        <result column="SELECTION_TYPE_ID" jdbcType="DECIMAL" property="selectionTypeId"/>
        <result column="PROJECT_NAME" jdbcType="VARCHAR" property="projectName"/>
        <result column="PROJECT_ID" jdbcType="VARCHAR" property="projectId"/>
        <result column="APPRAISERS" jdbcType="VARCHAR" property="appraisers"/>
        <result column="SCORE" jdbcType="DECIMAL" property="score"/>

        <association property="lawFirmEvaluateMain" javaType="com.klaw.entity.lawyerBean.LawFirmEvaluateMain">
            <id column="ID_" jdbcType="VARCHAR" property="id"/>
            <result column="CREATE_OGN_ID_" jdbcType="VARCHAR" property="createOgnId"/>
            <result column="CREATE_OGN_NAME_" jdbcType="VARCHAR" property="createOgnName"/>
            <result column="CREATE_DEPT_ID_" jdbcType="VARCHAR" property="createDeptId"/>
            <result column="CREATE_DEPT_NAME_" jdbcType="VARCHAR" property="createDeptName"/>
            <result column="CREATE_GROUP_ID_" jdbcType="VARCHAR" property="createGroupId"/>
            <result column="CREATE_GROUP_NAME_" jdbcType="VARCHAR" property="createGroupName"/>
            <result column="CREATE_PSN_ID_" jdbcType="VARCHAR" property="createPsnId"/>
            <result column="CREATE_PSN_NAME_" jdbcType="VARCHAR" property="createPsnName"/>
            <result column="CREATE_ORG_ID_" jdbcType="VARCHAR" property="createOrgId"/>
            <result column="CREATE_ORG_NAME_" jdbcType="VARCHAR" property="createOrgName"/>
            <result column="CREATE_PSN_FULL_ID_" jdbcType="VARCHAR" property="createPsnFullId"/>
            <result column="CREATE_PSN_FULL_NAME_" jdbcType="VARCHAR" property="createPsnFullName"/>
            <result column="CREATE_TIME_" jdbcType="TIMESTAMP" property="createTime"/>
            <result column="DATA_STATE_" jdbcType="VARCHAR" property="dataState"/>
            <result column="DATA_STATE_CODE_" jdbcType="DECIMAL" property="dataStateCode"/>
            <result column="CREATE_OGN_IDOA_" jdbcType="VARCHAR" property="createOgnIdoa"/>
            <result column="CREATE_OGN_NAMEOA_" jdbcType="VARCHAR" property="createOgnNameoa"/>
            <result column="CREATE_DEPT_IDOA_" jdbcType="VARCHAR" property="createDeptIdoa"/>
            <result column="CREATE_DEPT_NAMEOA_" jdbcType="VARCHAR" property="createDeptNameoa"/>
            <result column="CREATE_GROUP_IDOA_" jdbcType="VARCHAR" property="createGroupIdoa"/>
            <result column="CREATE_GROUP_NAMEOA_" jdbcType="VARCHAR" property="createGroupNameoa"/>
            <result column="CREATE_PSN_IDOA_" jdbcType="VARCHAR" property="createPsnIdoa"/>
            <result column="CREATE_PSN_NAMEOA_" jdbcType="VARCHAR" property="createPsnNameoa"/>
            <result column="CREATE_ORG_IDOA_" jdbcType="VARCHAR" property="createOrgIdoa"/>
            <result column="CREATE_ORG_NAMEOA_" jdbcType="VARCHAR" property="createOrgNameoa"/>
            <result column="CREATE_PSN_FULL_IDOA_" jdbcType="VARCHAR" property="createPsnFullIdoa"/>
            <result column="CREATE_PSN_FULL_NAMEOA_" jdbcType="VARCHAR" property="createPsnFullNameoa"/>
            <result column="TASK_NAME_" jdbcType="VARCHAR" property="taskName"/>
            <result column="ENTRUST_NAME_" jdbcType="VARCHAR" property="entrustName"/>
            <result column="ENTRUST_ID_" jdbcType="VARCHAR" property="entrustId"/>
            <result column="DESCRIPTION_" jdbcType="CLOB" property="description"/>
        </association>
    </resultMap>
    <sql id="Base_Column_List">
        ID, CREATE_OGN_ID, CREATE_OGN_NAME, CREATE_DEPT_ID, CREATE_DEPT_NAME, CREATE_GROUP_ID,
        CREATE_GROUP_NAME, CREATE_PSN_ID, CREATE_PSN_NAME, CREATE_ORG_ID, CREATE_ORG_NAME,
        CREATE_PSN_FULL_ID, CREATE_PSN_FULL_NAME, CREATE_TIME, UPDATE_TIME, DATA_STATE, DATA_STATE_CODE,
        CREATE_OGN_IDOA, CREATE_OGN_NAMEOA, CREATE_DEPT_IDOA, CREATE_DEPT_NAMEOA, CREATE_GROUP_IDOA,
        CREATE_GROUP_NAMEOA, CREATE_PSN_IDOA, CREATE_PSN_NAMEOA, CREATE_ORG_IDOA, CREATE_ORG_NAMEOA,
        CREATE_PSN_FULL_IDOA, CREATE_PSN_FULL_NAMEOA, TASK_NAME, ENTRUST_NAME, ENTRUST_ID,
        DESCRIPTION
    </sql>
    <select id="queryPageData" resultMap="BaseResultMap">
        select
        d.ID, d.PARENT_ID, d.LAW_FIRM_NAME, d.LAW_FIRM_ID, d.LAWYER_NAME, d.LAWYER_ID, d.SELECTION_TYPE_NAME, d.
        SELECTION_TYPE_ID, d.PROJECT_NAME, d.PROJECT_ID, d.SCORE,d.APPRAISERS,
        m.ID ID_,m.CREATE_OGN_ID CREATE_OGN_ID_,m.CREATE_OGN_NAME CREATE_OGN_NAME_,m.CREATE_DEPT_ID
        CREATE_DEPT_ID_,m.CREATE_DEPT_NAME CREATE_DEPT_NAME_,m.CREATE_GROUP_ID CREATE_GROUP_ID_,m.CREATE_GROUP_NAME
        CREATE_GROUP_NAME_,m.CREATE_PSN_ID CREATE_PSN_ID_,m.CREATE_PSN_NAME CREATE_PSN_NAME_,m.CREATE_ORG_ID
        CREATE_ORG_ID_,m.CREATE_ORG_NAME CREATE_ORG_NAME_,m.CREATE_PSN_FULL_ID
        CREATE_PSN_FULL_ID_,m.CREATE_PSN_FULL_NAME CREATE_PSN_FULL_NAME_,m.CREATE_TIME CREATE_TIME_,m.UPDATE_TIME
        UPDATE_TIME_,m.DATA_STATE DATA_STATE_,m.DATA_STATE_CODE DATA_STATE_CODE_,m.CREATE_OGN_IDOA
        CREATE_OGN_IDOA_,m.CREATE_OGN_NAMEOA CREATE_OGN_NAMEOA_,m.CREATE_DEPT_IDOA
        CREATE_DEPT_IDOA_,m.CREATE_DEPT_NAMEOA CREATE_DEPT_NAMEOA_,m.CREATE_GROUP_IDOA
        CREATE_GROUP_IDOA_,m.CREATE_GROUP_NAMEOA CREATE_GROUP_NAMEOA_,m.CREATE_PSN_IDOA
        CREATE_PSN_IDOA_,m.CREATE_PSN_NAMEOA CREATE_PSN_NAMEOA_,m.CREATE_ORG_IDOA CREATE_ORG_IDOA_,m.CREATE_ORG_NAMEOA
        CREATE_ORG_NAMEOA_,m.CREATE_PSN_FULL_IDOA CREATE_PSN_FULL_IDOA_,m.CREATE_PSN_FULL_NAMEOA
        CREATE_PSN_FULL_NAMEOA_,m.TASK_NAME TASK_NAME_,m.ENTRUST_NAME ENTRUST_NAME_,m.ENTRUST_ID
        ENTRUST_ID_,m.DESCRIPTION DESCRIPTION_

        from SG_LAW_FIRM_EVALUATE_MAIN m left join SG_LAW_FIRM_EVALUATE_DETAIL d on m.id = d.PARENT_ID
        <where>
            ${ew.sqlSegment}
        </where>
    </select>
</mapper>