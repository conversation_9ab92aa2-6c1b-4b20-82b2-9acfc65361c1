import {request} from '@/api/index'

export default {
  saveStartTime(data) {
    return request({
      url: '/contractPerform/saveStartTime',
      method: 'post',
      data
    })
  },
  savePerform(data) {
    return request({
      url: '/contractPerform/savePerform',
      method: 'post',
      data
    })
  },
  addPerform(data) {
    return request({
      url: '/contractPerform/addPerform',
      method: 'post',
      data
    })
  },
  queryPerformById(data) {
    return request({
      url: '/contractPerform/queryPerformById',
      method: 'post',
      data
    })
  },
  deletePerform(data) {
    return request({
      url: '/contractPerform/deletePerform',
      method: 'post',
      data
    })
  },
  queryContractApproval(data) {
    return request({
      url: '/contractPerform/queryContractApproval',
      method: 'post',
      data
    })
  },
  queryContractApprovalView(data) {
    return request({
      url: '/contractPerform/queryContractApprovalView',
      method: 'post',
      data
    })
  },
  queryPerform(data) {
    return request({
      url: '/contractPerform/queryPerform',
      method: 'post',
      data
    })
  },
  updateStatus(data) {
    return request({
      url: '/contractPerform/updateStatus',
      method: 'post',
      data
    })
  },
  queryPageDataById(data) {
    return request({
      url: '/contractPerform/queryPageDataById',
      method: 'post',
      data
    })
  },
  updatePerformStatus(data) {
    return request({
      url: '/contractPerform/updatePerformStatus',
      method: 'post',
      data
    })
  },
  queryTimeAxis(data) {
    return request({
      url: '/contractPerform/queryTimeAxis',
      method: 'post',
      data
    })
  },
  createScheduler(data) {
    return request({
      url: '/contractPerform/createScheduler',
      method: 'post',
      data
    })
  },
  // 获取用户未发布履行计划的合同数量
   getUnpublishedContractCount() {
    return request({
      url: '/bm-contract/checkPerformPlans',
      method: 'get'
    });
  }
}
