<template>
	<FormWindow ref="formWindow" @initData="initData" @loadData="loadData">
		<el-container v-loading="loading" style="height: calc(100vh - 84px)" :element-loading-text="loadingText">
			<el-main>
				<el-scrollbar style="height: 100%" wrap-style="overflow-x: hidden;">
					<!--数据表单块-->
					<el-form ref="dataForm" :model="mainData" :rules="!isView ? rules : {}" label-width="80px"
						:style="mainData.dataStateCode === utils.dataState_BPM.FINISH.code ? 'margin-right: 10px;' : ' margin-right: 10px;'">
						<el-row
							style="padding: 10px 0 10px 0; position: fixed; width: 100%; overflow-y: auto; background-color: white; z-index: 999">
							<el-button v-if="!isView" type="primary" size="mini" @click="save_">保存</el-button>
							<el-button v-if="!isView" type="success" size="mini" @click="approval_">生成审批单</el-button>

						</el-row>
						<div style="padding-top: 50px"></div>
						<span style="text-align: left; font-size: 23px; margin-left: 43%; font-weight: 900"
							v-if="mainData.type == '1'">合规问责</span>
						<span style="text-align: left; font-size: 23px; margin-left: 43%; font-weight: 900"
							v-if="mainData.type == '2'">协同监督</span>
						<!--基础信息块-->
						<qs-base-info :data.sync="mainData" :data-state="dataState"
							:authorization-data="authorizationData" :view="view" />
						<!--公共信息-->
						<OtherInfo :data.sync="mainData" :main-id="mainData.id" :data-state="dataState"
							style="margin-top: 20px" />
					</el-form>

					<!--案件审批表-->
					<prosecution-dialog :visible.sync="prosecutionDialog" :is-multiple="false"
						@onSure="prosecutionSelect" />
				</el-scrollbar>
			</el-main>

			<!--			<Shortcut :templateShow="templateShow" @templateClick="templateClick" />-->
		</el-container>
	</FormWindow>
</template>

<script>
// vuex缓存数据
import { mapGetters } from 'vuex';

// 接口api
import ComplianceAccountabilityApi from '@/api/risk/ComplianceAccountability'
import taskApi from '@/api/_system/task';
// 组件
import FormWindow from '@/view/components/FormWindow/FormWindow';
import OtherInfo from '@/view/litigation/caseManage/case/caseChild/OtherInfo';
import OrgSingleDialogSelect from '@/view/components/OrgSingleDialogSelect/index';
import ProsecutionDialog from './ProsecutionDialog';
import QsBaseInfo from './qisubaseInfo';
import Claim from '@/view/litigation/caseManage/case/caseChild/Claim.vue';
import Litigant from '@/view/litigation/caseManage/case/caseChild/Litigant';
import CaseData from '@/view/litigation/caseManage/case/caseChild/CaseData';
import CaseEvidenceData from '@/view/litigation/caseManage/case/caseChild/CaseEvidenceData';
import Shortcut from '@/view/litigation/caseManage/caseExamine/Shortcut';
import relations from '@/view/litigation/caseManage/caseProsecution/child/Relations';
import Authorization from '@/view/litigation/authorizationManage/authorization/child/AuthorizationLitigation';
import SimpleBoardTitleApproval from '@/view/components/SimpleBoard/SimpleBoardTitleApproval';
import seal from '@/view/litigation/contractManage/contractApproval/child/seal';

export default {
	name: 'HgwzMainDetail',
	inject: ['layout', 'mcpLayout'],
	components: {
		SimpleBoardTitleApproval,
		CaseData,
		Litigant,
		Claim,
		QsBaseInfo,
		ProsecutionDialog,
		OrgSingleDialogSelect,
		FormWindow,
		OtherInfo,
		CaseEvidenceData,
		Shortcut,
		relations,
		Authorization,
		seal,
	},
	computed: {
		...mapGetters(['orgContext']),
		isView: function () {
			return this.dataState === this.utils.formState.VIEW;
		},
		templateShow() {
			return this.mainData.dataStateCode === this.utils.dataState_BPM.SAVE.code && this.dataState !== this.utils.formState.VIEW;
		},
	},

	created() {
		// 从路由参数中获取传递的数据
		const mainData = this.$route.query.mainData;
		if (mainData) {
			this.mainData = JSON.parse(mainData); // 将字符串解析为对象
		}
	},
	data() {
		return {
			showSea: false,
			type: null,
			tabId: null,
			oarecordsDialog: false,
			loading: false,
			dataState: null,
			functionId: null, //终止的时候要用，需要手动关闭
			dataId: null,
			taskId: null,
			authorizationData: {
				authorizationPeriod: '选择日期',
				entrustedMatter: '代表委托单位办理诉讼业务', //委托事项
				authorizationName: '普通授权',
				entrustedRange: null, //授权范围
				endTime: null,
				startTime: null,
				endTimeStr: null,
				startTimeStr: null,
			}, //授权信息
			authorizationList: [], //授权受托人信息
			view: 'old',
			mainData: {
				code: null,//编号
				id: null, //主键
				oaDept: null,
				accountabilitySubject: null, // 项目名称
				responsibleParty: null,//责任对象
				accountabilityTime: null,//问责时间
				relatedMatter: null,//关联事项
				violationMatter: null,//违规事项
				reporter: null, // 上报人
				reportingUnit: null, // 上报单位
				reportingDateMin: null, // 上报日期最小值
				reportingDateMax: null, // 上报日期最大值
				currentNode: null, // 当前节点
				reviewStatus: null, // 审核状态
				projectOverview: null, // 项目概述
				riskAssessmentReport: null, // 风险评估报告
				relevantMaterials: null, // 相关资料
				reportingOrganization: null, // 上报组织
				participants: null, // 参会人员
				reviewOpinion: null, // 评审意见
				relevantAttachments: null, // 相关附件
				participatingDepartment: null, // 参与部门
				participatingPersonnel: null, // 参与人员
				attachments: null, // 附件
				reviewTimeMin: null, // 评审时间最小值
				reviewTimeMax: null, // 评审时间最大值
				createOgnId: null, //当前机构ID
				createOgnName: null, //当前机构名称
				createDeptId: null, //当前部门ID
				createDeptName: null, //当前部门名称
				createGroupId: null, //当前部门ID
				createGroupName: null, //当前部门名称
				createPsnId: null, //当前人ID
				createPsnName: null, //当前人名称
				createOrgId: null, //当前组织ID
				createOrgName: null, //当前组织名称
				createPsnFullId: null, //当前人全路径ID
				createPsnFullName: null, //当前人全路径名称
				createPsnPhone: null, //当前人电话
				createTime: null, //创建时间
				dataState: this.utils.dataState_BPM.SAVE.name, //数据状态
				dataStateCode: this.utils.dataState_BPM.SAVE.code, //数据状态编码
				eventReport: null, //事项报告
				fuzzyValue: null, // 模糊搜索值
				type: null,
			},
			orgTreeDialog: false,
			orgDialogTitle: '组织信息',
			isAssign: false,
			prosecutionDialog: false,
			rules: {
				accountabilitySubject: [{ required: true, message: '请填写主题', trigger: 'blur' }],
				accountabilityTime: [{ required: true, message: '请填写时间', trigger: 'blur' }],
				responsibleParty: [{ required: true, message: '请选择对象', trigger: 'blur' }],
				oaDept: [{ required: true, message: '请选择协同单位', trigger: 'blur' }],
				relatedMatter: [{ required: true, message: '请填写关联事件', trigger: 'blur' }],
				violationMatter: [{ required: true, message: '请填写违规事件', trigger: 'blur' }],
			},
			activity: null, //记录当前待办处于流程实例的哪个环节
			obj: {
				// 流程处理逻辑需要的各种参数
				taskId: null,
				processInstanceId: null,
				businessKey: null,
				title: null,
				functionName: null,
				sid: null,
			},
			noticeParams: {},
			noticeData: {
				moduleName: '', // 模块名称
				dataId: '', // 数据ID
				url: '', // 地址
				title: '', // 地址
				params: {}, // 其他参数
			},
			loadingText: '加载中...',
		};
	},
	provide() {
		return {
			parentCase: this,
		};
	},
	created() {
		// 从路由参数中获取传递的数据
		const mainData = this.$route.query.mainData;
		if (mainData) {
			this.mainData = JSON.parse(mainData); // 将字符串解析为对象
		}
	},
	methods: {
		initData(temp, dataState) {
			this.dataState = dataState;
			Object.assign(this.mainData, temp);
			this.mainData.type = this.$route.query.type;//获取评价对象类型
			console.log("this.mainData.type", this.mainData.type)
			let obj = this.utils.getManagementUnit(this.mainData.createPsnFullName, this.mainData.createPsnFullId);
			console.log("let obj", obj);
			this.mainData.managementUnit = obj.name;
			this.mainData.managementUnitId = obj.id;
			this.mainData.unitType = obj.unitType;
			this.mainData.unitTypeId = obj.unitTypeId;

			this.view = 'old';

			// let year = new Date().getFullYear();
			// this.utils.createKvsequence('HGWZ' + year, 6).then((value) => {
			// 	this.mainData.code = value.data.kvsequence;
			// });
			let year = new Date().getFullYear();
			if (!this.mainData.code) {
				if (this.mainData.type == '1') {
					this.utils.createKvsequence('HGWZ' + year, 6).then((value) => {
						this.mainData.code = value.data.kvsequence;
					});
				} else {
					this.utils.createKvsequence('HGJD' + year, 6).then((value) => {
						this.mainData.code = value.data.kvsequence;
					});
				}
			}
			this.mainData.currentUnit = this.mainData.createOgnName;
			this.mainData.currentUnitId = this.mainData.createOgnId;

			this.mainData.partiesList = [];
			this.mainData.partiesList.push({
				id: this.utils.createUUID(),
				partyType: '原告',
				party: null,
				contacts: null,
				contactsInfo: null,
				contactsAddress: null,
				createTime: new Date(),
				masterId: this.mainData.id,
				isSystemCreate: 'system',
			});
			this.mainData.partiesList.push({
				id: this.utils.createUUID(),
				partyType: '被告',
				party: null,
				contacts: null,
				contactsInfo: null,
				contactsAddress: null,
				createTime: new Date(new Date().getTime() + 1),
				masterId: this.mainData.id,
				isSystemCreate: 'system',
			});
			const interCode = 'AJ_GC_CLMC_QS';
			const codes = [interCode];
			this.utils.getDic(codes).then((response) => {
				const datas = response.data.data[codes[0]].filter((item) => item.whetherSolidified === true);
				if (datas && datas.length > 0) {
					datas.forEach((item, index) => {
						const data = this.childData(index);
						data.name = item.dicName;
						data.whetherSys = true;
						this.mainData.otherDataList.push(data);
					});
				}
			});

			this.loading = false;
		},
		loadData(dataState, dataId) {
			this.functionId = this.$route.query.functionId;
			if (this.$route.query.view !== undefined && this.$route.query.view !== '') this.view = this.$route.query.view;
			if (this.$route.query.type !== undefined && this.$route.query.type !== '') this.type = this.$route.query.type;
			this.dataState = dataState;
			ComplianceAccountabilityApi.queryById(dataId).then((response) => {
				this.mainData = response.data.data;
				this.authorizationData = response.data.data.authorization;
				if (response.data.data.authorization !== null) this.authorizationList = response.data.data.authorization.authorizationLitigationList;
				this.loading = false;
			});
		},
		save() {
			return new Promise((resolve, reject) => {
				ComplianceAccountabilityApi.save(this.mainData)
					.then((response) => {
						resolve(response);
					})
					.catch((error) => {
						reject(error);
					});
			});
		},
		//选择模板
		templateClick(val) {
			if (val) {
				this.prosecutionDialog = true;
			}
		},
		prosecutionSelect(data) {
			this.mainData.caseName = data.caseName; //事项名称
			this.mainData.causeOfInId = data.causeOfInId; //案件类型ID
			this.mainData.partiesList = data.partiesList;
			this.mainData.partiesList.forEach((item) => {
				item.id = this.utils.createUUID();
				item.masterId = this.mainData.id;
			});

			this.mainData.claimList = data.claimList;
			this.mainData.claimList.forEach((item) => {
				item.id = this.utils.createUUID();
				item.parentId = this.mainData.id;
			});

			this.mainData.otherDataList = data.otherDataList;
			this.mainData.otherDataList.forEach((item) => {
				item.id = this.utils.createUUID();
				item.parentId = this.mainData.id;
				item.files = null;
			});

			this.mainData.relations = data.relations;
			this.mainData.relations.forEach((item) => {
				item.id = this.utils.createUUID();
				item.relationId = this.mainData.id;
			});
		},
		approval_() {
			this.$refs['dataForm'].validate((valid) => {
				if (valid) {
					this.save()
						.then(() => {
							const tabId = this.mainData.id;
							if (this.mainData.type == '1') {
								if (this.mainData.dataStateCode == this.utils.dataState_BPM.SAVE.code) {
									taskApi.selectFunctionId({ functionCode: 'hgwz_main' }).then((res) => {
										const functionId = res.data.data[0].ID;
										this.layout.openNewTab('合规问责审批', 'design_page', functionId, tabId, {
											...this.utils.routeState.NEW(tabId),
											functionId: functionId,
											businessKey: tabId,
											entranceType: 'FLOWABLE',
											create: 'create',
											view: 'new',
										});
									});
								} else {
									const tabId = this.mainData.id;
									taskApi.selectTaskId({ businessKey: tabId, isView: '' }).then((res) => {
										const functionId = res.data.data[0].ID;
										const uuid = this.utils.createUUID();
										this.layout.openNewTab('合规问责审批', 'design_page', 'design_page', uuid, {
											processInstanceId: res.data.data[0].PID, //流程实例
											taskId: res.data.data[0].ID, //任务ID
											businessKey: tabId, //业务数据ID
											functionId: functionId, //不传也可以，只有首环节需要传，实例启动就可以不传了
											entranceType: 'FLOWABLE',
											type: 'toDeal',
											view: 'new',
											create: 'create',
										});
									});
								}
							} else if (this.mainData.type == '2') {
								if (this.mainData.dataStateCode == this.utils.dataState_BPM.SAVE.code) {
									taskApi.selectFunctionId({ functionCode: 'xtjd_main' }).then((res) => {
										const functionId = res.data.data[0].ID;
										this.layout.openNewTab('协同监督审批', 'design_page', functionId, tabId, {
											...this.utils.routeState.NEW(tabId),
											functionId: functionId,
											businessKey: tabId,
											entranceType: 'FLOWABLE',
											create: 'create',
											view: 'new',
										});
									});
								} else {
									const tabId = this.mainData.id;
									taskApi.selectTaskId({ businessKey: tabId, isView: '' }).then((res) => {
										const functionId = res.data.data[0].ID;
										const uuid = this.utils.createUUID();
										this.layout.openNewTab('协同监督审批', 'design_page', 'design_page', uuid, {
											processInstanceId: res.data.data[0].PID, //流程实例
											taskId: res.data.data[0].ID, //任务ID
											businessKey: tabId, //业务数据ID
											functionId: functionId, //不传也可以，只有首环节需要传，实例启动就可以不传了
											entranceType: 'FLOWABLE',
											type: 'toDeal',
											view: 'new',
											create: 'create',
										});
									});
								}
							}


						})
						.then(() => {
							this.mcpLayout.closeTab();
						});
				}
			});
		},
		save_() {
			this.save().then(() => {
				this.$message.success('保存成功!');
				// this.mcpLayout.closeTab();//关闭页面
			});
		},
		changeSwitch(val) {
			this.showSea = val;
		}
	},
};
</script>

<style scoped></style>
