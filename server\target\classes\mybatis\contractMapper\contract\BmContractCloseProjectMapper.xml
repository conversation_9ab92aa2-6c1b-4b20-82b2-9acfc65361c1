<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.klaw.dao.contractDao.contract.BmContractCloseProjectMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.klaw.entity.contractBean.contract.BmContractCloseProject">
        <id column="id" property="id"/>
        <result column="parent_id" property="parentId"/>
        <result column="project_type" property="projectType"/>
        <result column="project_name" property="projectName"/>
        <result column="project_code" property="projectCode"/>
        <result column="occupied_amount" property="occupiedAmount"/>
        <result column="assign_money" property="assignMoney"/>
        <result column="fulfillment_amount" property="fulfillmentAmount"/>
        <result column="rollback_amount" property="rollbackAmount"/>
        <result column="create_time" property="createTime"/>
        <result column="create_psn_full_id" property="createPsnFullId"/>
        <result column="create_psn_full_name" property="createPsnFullName"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, parent_id, project_type, project_name, project_code, occupied_amount, assign_money, fulfillment_amount, rollback_amount, create_time, create_psn_full_id, create_psn_full_name
    </sql>

</mapper>
