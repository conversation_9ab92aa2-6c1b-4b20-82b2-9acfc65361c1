<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.klaw.dao.systemDao.SgActReModelMapper">
    <resultMap id="BaseResultMap" type="com.klaw.entity.systemBean.SgActReModel">
        <id column="ID_" jdbcType="VARCHAR" property="id"/>
        <result column="REV_" jdbcType="DECIMAL" property="rev"/>
        <result column="NAME_" jdbcType="VARCHAR" property="name"/>
        <result column="KEY_" jdbcType="VARCHAR" property="key"/>
        <result column="CATEGORY_" jdbcType="VARCHAR" property="category"/>
        <result column="CREATE_TIME_" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="LAST_UPDATE_TIME_" jdbcType="TIMESTAMP" property="lastUpdateTime"/>
        <result column="VERSION_" jdbcType="DECIMAL" property="version"/>
        <result column="META_INFO_" jdbcType="VARCHAR" property="metaInfo"/>
        <result column="DEPLOYMENT_ID_" jdbcType="VARCHAR" property="deploymentId"/>
        <result column="EDITOR_SOURCE_VALUE_ID_" jdbcType="VARCHAR" property="editorSourceValueId"/>
        <result column="EDITOR_SOURCE_EXTRA_VALUE_ID_" jdbcType="VARCHAR" property="editorSourceExtraValueId"/>
        <result column="TENANT_ID_" jdbcType="VARCHAR" property="tenantId"/>
    </resultMap>
    <sql id="Base_Column_List">
        ID_
        , REV_, NAME_, KEY_, CATEGORY_, CREATE_TIME_, LAST_UPDATE_TIME_, VERSION_, META_INFO_,
    DEPLOYMENT_ID_, EDITOR_SOURCE_VALUE_ID_, EDITOR_SOURCE_EXTRA_VALUE_ID_, TENANT_ID_
    </sql>
    <select id="queryCodeByDeploymentId" resultType="java.util.Map">
        SELECT
        f.FUNCTION_CODE
        FROM
        ACT_RE_MODEL M
        LEFT JOIN SYS_FUNCTION_PD P ON M.KEY_ = P.PROCESSDEFINITION_KEY
        LEFT JOIN sys_function_b f on f.FUNCTION_ID = p.FUNCTION_ID
        WHERE f.FUNCTION_CODE is not null
        <if test="deploymentId !=  null  and deploymentId != '' ">
            and M.DEPLOYMENT_ID_=#{deploymentId}
        </if>
    </select>
    <select id="queryCodeByKey_" resultType="java.util.Map">
        select
        f.FUNCTION_CODE
        from
        ACT_RE_MODEL M
        LEFT JOIN SYS_FUNCTION_PD P
        ON
        M.KEY_ = P.PROCESSDEFINITION_KEY

        LEFT JOIN sys_function_b f
        on
        f.FUNCTION_ID = p.FUNCTION_ID
        WHERE f.FUNCTION_CODE is not null
        <if test="key_ !=  null  and key_ != '' ">
            and M.KEY_==#{key_}
        </if>
    </select>
    <select id="queryCodeByKey" resultType="java.util.Map">
        SELECT
        f.FUNCTION_CODE
        FROM
        ACT_RE_MODEL M
        LEFT JOIN SYS_FUNCTION_PD P ON M.KEY_ = P.PROCESSDEFINITION_KEY
        LEFT JOIN sys_function_b f on f.FUNCTION_ID = p.FUNCTION_ID
        WHERE f.FUNCTION_CODE is not null
        <if test="processDefinitionKey !=  null  and processDefinitionKey != '' ">
            and M.KEY_=#{processDefinitionKey}
        </if>
    </select>

</mapper>