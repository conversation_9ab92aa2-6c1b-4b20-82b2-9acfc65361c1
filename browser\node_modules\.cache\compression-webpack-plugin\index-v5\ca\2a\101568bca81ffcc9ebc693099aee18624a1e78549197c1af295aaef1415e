
4f4c0f4f000d433c292d7d5f2d92955b34feb060	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.202.1754018536329.js\",\"contentHash\":\"8ef036f570db983820f20b8a191e6d9c\"}","integrity":"sha512-3oK/GjZKwEqPcgPYF0xh3ifFff3mkymm148diROaEBP7kBT5ZipMDZ/wBU9CQH/PS0OlN8DKIcT4/gzOnnujEg==","time":1754018576150,"size":319326}