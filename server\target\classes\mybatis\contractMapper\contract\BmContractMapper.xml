<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.klaw.dao.contractDao.contract.BmContractMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.klaw.entity.contractBean.contract.BmContract">
        <id column="id" property="id"/>
        <result column="parent_id" property="parentId"/>
        <result column="data_state" property="dataState"/>
        <result column="data_state_code" property="dataStateCode"/>
        <result column="take_effect_name" property="takeEffectName"/>
        <result column="take_effect_code" property="takeEffectCode"/>
        <result column="perform_state" property="performState"/>
        <result column="perform_state_code" property="performStateCode"/>
        <result column="performance_state" property="performanceState"/>
        <result column="performance_state_code" property="performanceStateCode"/>
        <result column="change_state" property="changeState"/>
        <result column="change_state_code" property="changeStateCode"/>
        <result column="close_state" property="closeState"/>
        <result column="close_state_code" property="closeStateCode"/>
        <result column="evaluated_state" property="evaluatedState"/>
        <result column="evaluated_state_code" property="evaluatedStateCode"/>
        <result column="archived_state" property="archivedState"/>
        <result column="archived_state_code" property="archivedStateCode"/>
        <result column="data_type_name" property="dataTypeName"/>
        <result column="data_type_code" property="dataTypeCode"/>
        <result column="merge_state" property="mergeState"/>
        <result column="merge_state_code" property="mergeStateCode"/>
        <result column="data_source" property="dataSource"/>
        <result column="data_source_code" property="dataSourceCode"/>
        <result column="approval_code" property="approvalCode"/>
        <result column="contract_name" property="contractName"/>
        <result column="contract_code" property="contractCode"/>
        <result column="version" property="version"/>
        <result column="contract_type" property="contractType"/>
        <result column="contract_type_code" property="contractTypeCode"/>
        <result column="contract_type_level" property="contractTypeLevel"/>
        <result column="custom" property="custom"/>
        <result column="custom_code" property="customCode"/>
        <result column="third_version" property="thirdVersion"/>
        <result column="our_party_name" property="ourPartyName"/>
        <result column="our_party_list" property="ourPartyList"/>
        <result column="our_position" property="ourPosition"/>
        <result column="other_party_name" property="otherPartyName"/>
        <result column="other_party_list" property="otherPartyList"/>
        <result column="money_type" property="moneyType"/>
        <result column="money_type_code" property="moneyTypeCode"/>
        <result column="settlement_method" property="settlementMethod"/>
        <result column="settlement_method_code" property="settlementMethodCode"/>
        <result column="revenue_expenditure" property="revenueExpenditure"/>
        <result column="revenue_expenditure_code" property="revenueExpenditureCode"/>
        <result column="excluding_Tax_Amount" property="excludingTaxAmount"/>
        <result column="contract_money" property="contractMoney"/>
        <result column="contract_money_rmb" property="contractMoneyRmb"/>
        <result column="is_tax" property="isTax"/>
        <result column="value_added_tax_rate" property="valueAddedTaxRate"/>
        <result column="value_added_tax_rate_code" property="valueAddedTaxRateCode"/>
        <result column="value_added_tax_amount" property="valueAddedTaxAmount"/>
        <result column="currency" property="currency"/>
        <result column="currency_code" property="currencyCode"/>
        <result column="exchange_rate_method" property="exchangeRateMethod"/>
        <result column="exchange_rate_method_code" property="exchangeRateMethodCode"/>
        <result column="exchange_rate" property="exchangeRate"/>
        <result column="agreed_start_time" property="agreedStartTime"/>
        <result column="agreed_end_time" property="agreedEndTime"/>
        <result column="whether_group_major" property="whetherGroupMajor"/>
        <result column="whether_unit_major" property="whetherUnitMajor"/>
        <result column="authorized_source" property="authorizedSource"/>
        <result column="authorized_code" property="authorizedCode"/>
        <result column="authorized_id" property="authorizedId"/>
        <result column="authorized_name" property="authorizedName"/>
        <result column="authorized_person" property="authorizedPerson"/>
        <result column="authorized_range" property="authorizedRange"/>
        <result column="contract_subject_matter" property="contractSubjectMatter"/>
        <result column="summary_note" property="summaryNote"/>
        <result column="project_decision" property="projectDecision"/>
        <result column="project_decision_code" property="projectDecisionCode"/>
        <result column="relative_method" property="relativeMethod"/>
        <result column="relative_method_code" property="relativeMethodCode"/>
        <result column="contract_code_version" property="contractCodeVersion"/>
        <result column="authorized_person_id" property="authorizedPersonId"/>
        <result column="standard_attachment_version" property="standardAttachmentVersion"/>
        <result column="is_atemplate" property="isAtemplate"/>
        <result column="original_contract_name" property="originalContractName"/>
        <result column="original_contract_code" property="originalContractCode"/>
        <result column="original_contract_id" property="originalContractId"/>
        <result column="original_contract_money" property="originalContractMoney"/>
        <result column="original_value_added_tax_amount" property="originalValueAddedTaxAmount"/>
        <result column="change_money_type" property="changeMoneyType"/>
        <result column="change_money_type_code" property="changeMoneyTypeCode"/>
        <result column="this_change_money" property="thisChangeMoney"/>
        <result column="this_change_value_added_tax_amount" property="thisChangeValueAddedTaxAmount"/>
        <result column="after_change_money" property="afterChangeMoney"/>
        <result column="after_change_value_added_tax_amount" property="afterChangeValueAddedTaxAmount"/>
        <result column="contract_executory_money" property="contractExecutoryMoney"/>
        <result column="contract_executed_money" property="contractExecutedMoney"/>
        <result column="change_amount_rmb" property="changeAmountRmb"/>
        <result column="after_change_amount_rmb" property="afterChangeAmountRmb"/>
        <result column="create_ogn_id" property="createOgnId"/>
        <result column="create_ogn_name" property="createOgnName"/>
        <result column="create_dept_id" property="createDeptId"/>
        <result column="create_dept_name" property="createDeptName"/>
        <result column="create_group_id" property="createGroupId"/>
        <result column="create_group_name" property="createGroupName"/>
        <result column="create_psn_id" property="createPsnId"/>
        <result column="create_psn_name" property="createPsnName"/>
        <result column="create_org_id" property="createOrgId"/>
        <result column="create_org_name" property="createOrgName"/>
        <result column="create_psn_full_id" property="createPsnFullId"/>
        <result column="create_psn_full_name" property="createPsnFullName"/>
        <result column="create_legal_unit_id" property="createLegalUnitId"/>
        <result column="create_legal_unit_name" property="createLegalUnitName"/>
        <result column="create_transfer_time" property="createTransferTime"/>
        <result column="create_transfer_name" property="createTransferName"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_psn_phone" property="createPsnPhone"/>
        <result column="contract_take_effect_date" property="contractTakeEffectDate"/>
        <result column="other_seal_date" property="otherSealDate"/>
        <result column="close_id" property="closeId"/>
        <result column="explain" property="explain"/>
        <result column="project_names" property="projectNames"/>
        <result column="file_types" property="fileTypes"/>
        <result column="contract_files" property="contractFiles"/>
        <result column="approval_start_time" property="approvalStartTime"/>
        <result column="approval_start_end" property="approvalStartEnd"/>
        <result column="is_check_success" property="isCheckSuccess"/>
        <result column="business_type" property="businessType"/>
        <result column="business_type_code" property="businessTypeCode"/>
        <result column="asset_doc_no" property="assetDocNo"/>
        <result column="asset_contract_number" property="assetContractNumber"/>
        <result column="asset_description" property="assetDescription"/>
        <result column="asset_owner" property="assetOwner"/>
        <result column="asset_remarks" property="assetRemarks"/>
        <result column="risk_type" property="riskType"/>
        <result column="risk_description" property="riskDescription"/>
        <result column="risk_occurrence_time" property="riskOccurrenceTime"/>
        <result column="resp_unit" property="respUnit"/>
        <result column="resp_unit_code" property="respUnitCode"/>
        <result column="resp_dept" property="respDept"/>
        <result column="resp_dept_code" property="respDeptCode"/>
        <result column="resp_psn" property="respPsn"/>
        <result column="resp_psn_code" property="respPsnCode"/>
        <result column="report_psn" property="reportPsn"/>
        <result column="report_psn_code" property="reportPsnCode"/>
        <result column="report_psn_tel" property="reportPsnTel"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, data_state, data_state_code, take_effect_name, take_effect_code, perform_state, perform_state_code, performance_state, performance_state_code, change_state, change_state_code, close_state, close_state_code, evaluated_state, evaluated_state_code, archived_state, archived_state_code, data_type_name, data_type_code, data_source, data_source_code, approval_code, contract_name, contract_code, version, contract_type, contract_type_code, custom, custom_code, our_party_name, our_party_list, our_position, other_party_name, other_party_list, money_type, money_type_code, settlement_method, settlement_method_code, revenue_expenditure, revenue_expenditure_code, contract_money, is_tax, value_added_tax_rate, value_added_tax_rate_code, value_added_tax_amount, currency, currency_code, exchange_rate_method, exchange_rate_method_code, exchange_rate, agreed_start_time, agreed_end_time, whether_group_major, whether_unit_major, authorized_source, authorized_code, authorized_id, authorized_name, authorized_person, authorized_range, contract_subject_matter, summary_note, project_decision, project_decision_code, relative_determination_method, relative_determination_method_code, contract_code_version, authorized_person_id, is_atemplate, original_contract_name, original_contract_code, original_contract_id, original_contract_money, original_value_added_tax_rate, change_money_type, change_money_type_code, this_change_money, this_change_value_added_tax_amount, after_change_money, after_change_value_added_tax_amount, contract_executory_money, contract_executed_money, create_ogn_id, create_ogn_name, create_dept_id, create_dept_name, create_group_id, create_group_name, create_psn_id, create_psn_name, create_org_id, create_org_name, create_psn_full_id, create_psn_full_name, create_legal_unit_id, create_legal_unit_name, create_time, update_time, contract_take_effect_date, close_id
    </sql>
    <sql id="query_Page_List">
        id,
            data_state,
            data_state_code,
            parent_id,
            take_effect_code,
            take_effect_name,
            perform_state,
            perform_state_code,
            performance_state,
            performance_state_code,
            change_state,
            change_state_code,
            close_state,
            close_state_code,
            evaluated_state,
            evaluated_state_code,
            archived_state,
            archived_state_code,
            data_type_name,
            data_type_code,
            data_source,
            data_source_code,
            approval_code,
            contract_name,
            contract_code,
            custom_code,
            version,
            contract_type,
            contract_type_code,
            our_party_name,
            other_party_name,
            money_type,
            contract_money,
            contract_take_effect_date,
            close_id,
            evaluated_state,
            revenue_expenditure,
            after_change_money,
            contract_executed_money,
            contract_executory_money,
            settlement_method,
            currency,
            currency_code,
            exchange_rate_method,
            exchange_rate,
            this_change_money,
            create_psn_name,
            create_dept_name,
            create_ogn_name,
            create_time,
            merge_state,
            merge_state_code,
            assessment_score,
            risk_psn,
            risk_psn_code,
            undertaking_time
    </sql>
    <sql id="query_Page_List1">
        a.id,
        data_state,
        data_state_code,
        parent_id,
        take_effect_code,
        take_effect_name,
        perform_state,
        perform_state_code,
        performance_state,
        performance_state_code,
        change_state,
        change_state_code,
        close_state,
        close_state_code,
        evaluated_state,
        evaluated_state_code,
        archived_state,
        archived_state_code,
        data_type_name,
        data_type_code,
        data_source,
        data_source_code,
        approval_code,
        contract_name,
        contract_code,
        custom_code,
        version,
        contract_type,
        contract_type_code,
        our_party_name,
        other_party_name,
        money_type,
        contract_money,
        contract_take_effect_date,
        close_id,
        evaluated_state,
        revenue_expenditure,
        after_change_money,
        contract_executed_money,
        contract_executory_money,
        settlement_method,
        currency,
        currency_code,
        exchange_rate_method,
        exchange_rate,
        this_change_money,
        create_psn_name,
        create_dept_name,
        create_ogn_name,
        create_time,
        merge_state,
        merge_state_code,
        assessment_score,
        risk_psn,
        risk_psn_code,
        undertaking_time,
        contract_take_effect_file
    </sql>

    <update id="updateNotAllById">
        UPDATE bm_contract
        SET after_change_money                  = #{newVal1},
            after_change_amount_rmb             = #{newVal2},
            after_change_value_added_tax_amount = #{newVal3},
            change_state                        = #{newVal4},
            change_state_code                   = #{newVal5},
            performance_state                   = #{newVal6},
            performance_state_code              = #{newVal7}
        WHERE id = #{id}
    </update>

    <update id="updateComById">
        UPDATE bm_contract
        SET
        compliance_files = #{newVal1},
        risk_psn = #{newVal2},
        risk_psn_code = #{newVal3},
        undertaking_time = #{newVal4}
        WHERE id = #{id}
    </update>

    <select id="queryPageList" resultMap="BaseResultMap">
        select
        <include refid="query_Page_List"/>
        from bm_contract
        <where>
            ${ew.sqlSegment}
        </where>
    </select>

    <select id="pageComments" resultMap="BaseResultMap">
        SELECT
        <include refid="query_Page_List1"/>
        FROM bm_contract a
        INNER JOIN
        (
        SELECT
        id
        FROM
        bm_contract
        <where>
            ${ew.sqlSegment}
        </where>
        LIMIT #{currentSize},#{size}
        ) b on a.id = b.id
    </select>

    <select id="queryChangeCount" resultType="java.util.Map">
        select count(*) as num
        from bm_contract
        where contract_code is not null
        start with id = #{id}
        connect by prior id = original_contract_id
    </select>
</mapper>
