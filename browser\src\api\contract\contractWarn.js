import {request} from '@/api/index'

export default {
  queryAll(data) {
    return request({
      url: '/contractWarn/queryAll',
      method: 'post',
      data
    })
  },
  queryView(data) {
    return request({
      url: '/contractWarn/queryView',
      method: 'post',
      data
    })
  },
  save(data) {
    return request({
      url: '/contractWarn/save',
      method: 'post',
      data
    })
  },
  queryById(data) {
    return request({
      url: '/contractWarn/queryById',
      method: 'post',
      data
    })
  },
  searchContractId(data) {
    return request({
      url: '/contractWarn/searchContractId',
      method: 'post',
      data
    })
  },
  querySealIdIsNull(data) {
    return request({
      url: '/contractWarn/querySealIdIsNull',
      method: 'post',
      data: data
    })
  },
  updateStatus(data) {
    return request({
      url: 'contractWarn/updateStatus',
      method: 'post',
      data
    })
  }
}
