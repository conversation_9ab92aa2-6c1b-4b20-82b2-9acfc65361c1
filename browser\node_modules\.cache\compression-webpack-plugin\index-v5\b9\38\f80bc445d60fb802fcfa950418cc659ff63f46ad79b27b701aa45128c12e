
a8dd96fc62d970203b6dd6fb8613023dacf86b59	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.320.1754018536329.js\",\"contentHash\":\"68a4c87864dcf3f3ac36524076fe5375\"}","integrity":"sha512-EtfxBDyWOY8fCgLnq5XM9OeyX6zXHYmLIb4G+7lKLg5xXjIyXXA/I8fyvq4Qg78RKB8xOJRtHn4wpjOk5VBX8g==","time":1754018575974,"size":109699}