
4609a284dda2ef4685cf46e177bca2d3871ef86c	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.50.1754018536329.js\",\"contentHash\":\"c94c30e99b5ba40273bd6f705b3ca01e\"}","integrity":"sha512-LdHGTYa8oBR/fkHU8mPMLttj6u1SGBQvm7U8wq8kflX/dgDaV2BqMpun7fRkh6yCGfJXpSKVFmUSlE2Ikp62bQ==","time":1754018575956,"size":56602}