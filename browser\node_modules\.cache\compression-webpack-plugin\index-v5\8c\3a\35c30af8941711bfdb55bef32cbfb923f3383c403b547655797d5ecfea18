
45aaced658528a7bc32bab053431c9876dc3e174	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.229.1754018536329.js\",\"contentHash\":\"fff3976fca787fd91c202df18a2be62b\"}","integrity":"sha512-WuwqFEdrs0WG6sghKj+Ei+9YlnARuZtmSWB6SsPQzC29NLVYomWgAeQADIk4D9TudMficQIxfnii7NaFXd6q9A==","time":1754018575995,"size":168430}