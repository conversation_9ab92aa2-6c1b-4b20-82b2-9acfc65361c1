
2130cc26ca94fa6710aea63d671c40a7570dc0cb	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.62.1754018536329.js\",\"contentHash\":\"830ce13b2ff458431b3460a112ddbb94\"}","integrity":"sha512-dTP+t1YVowMly1jEL89WeH5cNoas0gdbk1M8YFeXwetq0GMZQtcO5j9gwcv5pFjivdFiiXui5ZGb87eTuup4Tw==","time":1754018575959,"size":98765}