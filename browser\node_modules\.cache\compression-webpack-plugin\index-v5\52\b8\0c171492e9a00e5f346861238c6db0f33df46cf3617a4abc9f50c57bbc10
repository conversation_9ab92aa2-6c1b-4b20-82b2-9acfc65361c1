
b01fbc805e44920392f31f5feef07193f9bf937a	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.322.1754018536329.js\",\"contentHash\":\"7035615b9a39bfa989e8034d56d91d17\"}","integrity":"sha512-kO5VPo5sycxpZ4J2k3EMbtILteASpUM/0LYwHpVHGh+KzWjyhin2lWPLCTAfRuHgQ9xaaR30KEHvO/pOaWThUQ==","time":1754018575974,"size":115335}