
63bc5ccd47a91805a81a2b153e595975a3280d60	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.28.1754018536329.js\",\"contentHash\":\"5635f0a27fc40a1d33724e2d69ed76a1\"}","integrity":"sha512-xGTuipWCIcwVBMh6C1t31gqR5O+M50oThTh2FaSCrMAO5YgoItLzpcn5FOrXP0OJcFT3jAdR9GvmSHVO2t5dxg==","time":1754018575955,"size":52855}