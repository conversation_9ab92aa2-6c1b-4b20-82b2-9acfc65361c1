# jetCache配置
jetcache:
  remote:
    default: # 需要和注解上面的area保持一致 表示这个area的远程缓存配置
      type: redis # 远程缓存了类型
      keyConvertor: fastjson # key序列化方式
      valueEncoder: bean:cacheJackson2
      valueDecoder: bean:cacheJackson2 # value 反序列化 目前支持 java和kryo
      poolConfig:
        minIdle: 5 #最小空闲连接数
        maxIdle: 20 # 最大空闲连接数
        maxTotal: 50 # 最大连接数
      host: localhost   # 指定自己的redis地址
      port: 6379   # 指定自己的port
      database: 0   # redis库
