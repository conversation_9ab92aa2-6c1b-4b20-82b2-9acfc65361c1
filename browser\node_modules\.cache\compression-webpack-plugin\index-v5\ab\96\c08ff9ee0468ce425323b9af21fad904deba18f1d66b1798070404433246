
adb8167ca2c38e429a17acb203b6a425993bdd4a	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.264.1754018536329.js\",\"contentHash\":\"7f8464013646d5a6e75c80f5e0a51b10\"}","integrity":"sha512-wGpwVXqGTM5EWqfMzzUIn3F9AMeHIHcckY+E4UtElNAe0o2lVoauU6qBrkPPMSD21hDXMQhcQHrVh9iCkPDIgw==","time":1754018576004,"size":123061}