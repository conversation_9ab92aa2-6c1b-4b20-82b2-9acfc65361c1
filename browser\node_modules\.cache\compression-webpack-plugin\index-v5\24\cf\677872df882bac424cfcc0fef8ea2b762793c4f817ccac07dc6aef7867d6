
434115ce6eb189db7f111ca72912f4a9a8905a6a	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.272.1754018536329.js\",\"contentHash\":\"f2a098eac57c7933221f51bbd0d11e84\"}","integrity":"sha512-lTIJDCb+HiP4dMltCZmXtCMsFGmJhia0rNnHN7vXjmZXYQJnXPtLYafY6dTDiwvLahnX23PvUREs7T5FHRd+/A==","time":1754018575962,"size":104828}