import {request} from '@/api/index'

export default {

    /**
     * 查询所有案件
     * @param {caseList} data
     * @returns
     */
    queryCase(data) {
        return request({
            url: '/case/query',
            method: 'post',
            data
        })
    },
    /**
     * 查询所有案件
     * @param {caseList} data
     * @returns
     */
    queryDialog(data) {
        return request({
            url: '/case/queryDialog',
            method: 'post',
            data
        })
    },
    /**
     * 根据案件ID进行分页查询
     * @param {caseList} data
     * @returns
     */
    queryCaseAndTags(data) {
        return request({
            url: '/case/queryCaseAndTags',
            method: 'post',
            data
        })
    },

    /**
     * 加载子数据
     * @param {id} id
     * @returns
     */
    queryDataByParentId(id) {
        return request({
            url: '/case/query_by_parent',
            method: 'post',
            data: {
                parentId: id
            }
        })
    },

    /**
     * 测试树形菜单
     * @returns
     */
    findOrgUserTree() {
        return request({
            url: '/sys_org/findOrgUserTree',
            method: 'post'
        })
    },
    /**
     * 根据ID查询案件资料
     * @param {*} data
     * @returns
     */
    loadOtherData(data) {
        return request({
            url: '/case/loadOtherData',
            method: 'post',
            data
        })
    },
    /**
     * 根据id查询案件信息
     * @param {*} data
     * @returns
     */
    queryById(data) {
        return request({
            url: '/case/query_by_id',
            method: 'post',
            data: {
                id: data
            }
        })
    },
    /**
     * 根据id查询案件主信息
     * @param {*} data
     * @returns
     */
    queryMainById(data) {
        return request({
            url: '/case/queryMainById',
            method: 'post',
            data: {
                id: data
            }
        })
    },
    queryExecuteById(data) {
        return request({
            url: '/case/queryExecuteById',
            method: 'post',
            data
        })
    },

    queryPageDataById(data) {
        return request({
            url: '/case/queryPageDataById',
            method: 'post',
            data
        })
    },

    /**
     * 根据ID查询案件进程
     * @param {} id
     * @returns
     */
    queryProcessById(id) {
        return request({
            url: '/case/query_current_process',
            method: 'post',
            data: {
                id: id
            }
        })
    },
    /**
     * 保存案件信息
     * @param {案件内容} data
     * @returns
     */
    save(data) {
        return request({
            url: '/case/save',
            method: 'post',
            data
        })
    },

    queryCaseAndTags2(data) {
        return request({
            url: '/case/queryCaseAndTags2',
            method: 'post',
            data
        })
    },
    queryCaseAndTags3(data) {
        return request({
            url: '/case/queryCaseAndTags3',
            method: 'post',
            data
        })
    },
    /**
     * 删除案件信息
     * @param {案件内容} data
     * @returns
     */
    deleteCase(data) {
        return request({
            url: '/case',
            method: 'delete',
            data
        })
    },

    /**
     * 复制案件信息
     * @param {案件内容} data
     * @returns
     */
    copyCase(data) {
        return request({
            url: '/case/copyCase',
            method: 'post',
            data
        })
    },

    /**
     * 取消起诉
     * @param {案件内容} data
     * @returns
     */
    cancelCase(data) {
        return request({
            url: '/case/cancel',
            method: 'post',
            data
        })
    },

    /**
     * 导出word
     * @param {案件id} id
     * @returns
     */
    createWord(data) {
        return request({
            url: '/case/createWord?id=' + data.id,
            method: 'get',
            responseType: 'blob'
        })
    },

    /**
     * 查看更新历史
     * @param {案件内容} data
     * @returns
     */
    queryHistory(data) {
        return request({
            url: '/case/query_history',
            method: 'post',
            data
        })
    },

    saveProcess(data) {
        return request({
            url: '/case/save_process',
            method: 'post',
            data
        })
    },

    queryProcessByIdAndStage(id, stage) {
        return request({
            url: '/case/queryProcessByIdAndStage',
            method: 'post',
            data: {
                id: id,
                stage: stage
            }
        })
    },

    /**
     * 查找板块案件统计表
     * @param data
     * @returns {*}
     */
    querySelect(data) {
        return request({
            url: '/case/querySelect',
            method: 'post',
            data
        })
    },
    /**
     * 查找板块案件统计表2
     * @param data
     * @returns {*}
     */
    querySelectTWO(data) {
        return request({
            url: '/case/querySelectTWO',
            method: 'post',
            data
        })
    },

    reportExcelw(data) {
        return request({
            url: '/exportexcel/exportcasenew',
            method: 'post',
            responseType: 'blob',
            data
        })
    },
    reportExcelw2(data) {
        return request({
            url: '/exportexcel/exportcasenew2',
            method: 'post',
            responseType: 'blob',
            data
        })
    },
    reportExcelw3(data) {
        return request({
            url: '/exportexcel/exportcasenew3',
            method: 'post',
            responseType: 'blob',
            data
        })
    },
    reportExcelw4(data) {
        return request({
            url: '/exportexcel/exportcasenew4',
            method: 'post',
            responseType: 'blob',
            data
        })
    },
    /**
     * 文件归集
     * @param data
     * @returns {*}
     */
    fileDown(data) {
        return request({
            url: '/case/fileDown',
            method: 'post',
            responseType: 'blob',
            data
        })
    },
    //更新排序
    updateSort(data) {
        return request({
            url: '/case/updateSort',
            method: 'post',
            data
        })
    },
    //删除进程
    deleteProcess(data) {
        return request({
            url: '/case/deleteProcess',
            method: 'post',
            data
        })
    },
    getOneByIdAndCode(data) {
        return request({
            url: '/case/getOneByIdAndCode',
            method: 'post',
            data
        })
    },
    queryPreservation(data) {
        return request({
            url: '/case/queryPreservation',
            method: 'post',
            data
        })
    },

    queryPreservationByParent(id) {
        return request({
            url: '/case/queryPreservationByParent',
            method: 'post',
            data: {
                parentId: id
            }
        })
    },

    /**
     * 更新发送提醒
     * @param data
     * @returns {*}
     */
    updateRemind(data) {
        return request({
            url: '/case/updateRemind',
            method: 'post',
            data
        })
    },

  /**
   * 更新发送提醒
   * @param data
   * @returns {*}
   */
  restartById(data) {
    return request({
      url: '/case/restartById',
      method: 'post',
      data
    })
  },
  exportGreatCase(data) {
    return request({
      url: '/case/exportGreatCase',
      method: 'post',
      responseType: 'blob',
      data
    })
  },
    /**
     * 首页查询案件数量
     * @param data
     * @returns {*}
     */
    queryCaseByIndex(data) {
        return request({
            url: '/case/queryCaseByIndex',
            method: 'post',
            data
        })
    },

    /**
     * 统计案件数量，以案件性质为维度（民事诉讼、商事仲裁、劳动仲裁、行政诉讼、刑事案件、境外案件）
     */
    queryCaseKind(data) {
        return request({
            url: '/case/queryCaseKind',
            method: 'post',
            data
        })
    },
    /**
     * 年度统计案件上报
     */
    queryAnnualCaseReport(data) {
        return request({
            url: '/case/queryAnnualCaseReport',
            method: 'post',
            data
        })
    },
    /**
     * 季度统计案件上报
     */
    queryQuarterCaseReport(data) {
        return request({
            url: '/case/queryQuarterCaseReport',
            method: 'post',
            data
        })
    },
    getToken(data) {
        return request({
            url: '/case/getToken',
            method: 'post',
            data
        })
    },
    getDisce(data) {
        return request({
            url: '/case/getDisce',
            method: 'post',
            data
        })
    },
}
