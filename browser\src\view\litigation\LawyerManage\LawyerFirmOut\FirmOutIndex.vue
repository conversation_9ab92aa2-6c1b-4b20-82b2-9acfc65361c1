<template>
  <el-container class="container-manage-sg" direction="vertical">
    <el-header>
      <el-card>
        <el-input
            v-model="temp.fuzzyValue"
            class="filter_input"
            clearable
            placeholder="检索条件（律所名称、负责人、统一社会信用代码、传真、电子邮箱）"
            @clear="refreshData"
            @keyup.enter.native="refreshData"
        >
          <el-popover slot="prepend" placement="bottom-start" trigger="click" width="1000">
            <el-form ref="queryForm" label-width="100px" size="mini">
              <el-row>
                <el-col :span="12">
                  <el-form-item label="出库律所名称">
                    <el-input v-model="temp.lawyerFirm" clearable placeholder="请输入..." style="width:100%"/>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="出库原因">
                    <el-select v-model="temp.failName" clearable placeholder="请选择" style="width:100%"
                               @clear="refreshData" @keyup.enter.native="refreshData">
                      <el-option
                          v-for="item in failData"
                          :key="item.dicName"
                          :label="item.dicName"
                          :value="item.dicName"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="12">
                  <el-form-item label="出库类型">
                    <el-select v-model="temp.outTypeName" clearable placeholder="请选择" style="width:100%">
                      <el-option
                          v-for="item in utils.out_type_data"
                          :key="item.dicName"
                          :label="item.dicName"
                          :value="item.dicName"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="状态">
                    <el-select v-model="temp.dataState" clearable placeholder="请选择" style="width:100%"
                               @clear="refreshData" @keyup.enter.native="refreshData">
                      <el-option
                          v-for="item in utils.dataState_BPM_data"
                          :key="item.code"
                          :label="item.name"
                          :value="item.name"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-button-group style="float: right">
                <el-button icon="el-icon-search" type="primary" @click="search_">搜索</el-button>
                <el-button icon="el-icon-refresh-left" type="primary" @click="empty_">重置</el-button>
              </el-button-group>
            </el-form>
            <el-button slot="reference" type="primary">高级检索</el-button>
          </el-popover>
          <el-button slot="append" icon="el-icon-search" @click="search_"/>
        </el-input>
      </el-card>
    </el-header>

    <el-main>
      <SimpleBoard2 :isButton="true" :title="'律所出库信息'" @addBtn="add_">
        <el-table
            ref="table"
            :data="tableData"
            :height="table_height"
            :show-overflow-tooltip="true"
            border
            fit
            highlight-current-row
            stripe
            style="width: 100%"
            @sort-change="tableSort"
            @row-dblclick="rowDblclick"
        >
          <el-table-column align="center" label="序号" type="index" width="50"/>
          <el-table-column v-if="ss.tableColumns.find(item => item.key === 'lawyerFirm').visible" align="center"
                           label="出库律所名称" min-width="200" prop="lawyerFirm" show-overflow-tooltip sortable="custom"/>
          <el-table-column v-if="ss.tableColumns.find(item => item.key === 'outTypeName').visible" align="center"
                           label="出库类型" min-width="100" prop="lawFirmOutApprovalMain.outTypeName"
                           show-overflow-tooltip sortable="custom"/>
          <el-table-column v-if="ss.tableColumns.find(item => item.key === 'failName').visible" align="center"
                           label="出库原因" min-width="100" prop="lawFirmOutApprovalMain.failName" show-overflow-tooltip
                           sortable="custom"/>
          <el-table-column v-if="ss.tableColumns.find(item => item.key === 'createTime').visible" align="center"
                           label="经办时间" min-width="100" prop="lawFirmOutApprovalMain.createTime" show-overflow-tooltip
                           sortable="custom">
            <template slot-scope="scope">
              <span>{{ scope.row.lawFirmOutApprovalMain.createTime | parseTime }}</span>
            </template>
          </el-table-column>
          <el-table-column v-if="ss.tableColumns.find(item => item.key === 'dataState').visible" align="center"
                           label="审批状态" prop="lawFirmOutApprovalMain.dataState" show-overflow-tooltip sortable="custom"
                           width="100"/>
          <el-table-column align="center" label="操作">
            <template slot-scope="scope">
              <el-button v-if="isEdit(scope.row)" type="text" @click="edit_(scope.$index,scope.row)">编辑</el-button>
              <el-button type="text" @click="view_(scope.$index,scope.row)">查看</el-button>
              <el-button v-if="isDelete(scope.row)" type="text" @click="delete_(scope.$index,scope.row)">删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </SimpleBoard2>
    </el-main>

    <el-footer>
      <pagination
          :limit.sync="temp.limit"
          :page.sync="temp.page"
          :total="temp.total"
          align="center"
          @pagination="refreshData"
      />
    </el-footer>

  </el-container>
</template>

<script>
import lawyerFirmApi from '@/api/LawyerManage/LawyerFirmOut/lawFirmOutApprovalMain'
import dictApi from '@/api/_system/dict'
import pagination from "@/components/Pagination"
import SimpleBoard2 from "@/view/components/SimpleBoard/SimpleBoardIndex"
// // vuex状态值
import {mapGetters} from "vuex";
import taskApi from "@/api/_system/task";

export default {
  name: 'FirmOutIndex',
  components: {pagination, SimpleBoard2},
  inject: ['layout'],
  computed: {
    ...mapGetters(["orgContext", "currentFunctionId"])
  },
  data() {
    return {
      table_height: '100%', // 定义表格高度
      tableData: null, // 定义表格数据源
      temp: {
        lawyerFirm: null, // 律所名称
        failName: null, // 出库原因
        outTypeName: null, // 出库类型
        dataState: null, // 状态
        fuzzyValue: null, // 模糊字段
        sortName: null, // 排序
        order: false,
        page: 1,
        limit: 10,
        total: 0,
        isQuery: false,
        functionId: null,
        orgId: null,
      },
      ss: {
        data: this.tableData,
        tableColumns: [
          {key: 'lawyerFirm', label: '出库律所名称', visible: true},
          {key: 'outTypeName', label: '出库类型', visible: true},
          {key: 'failName', label: '出库原因', visible: true},
          {key: 'createTime', label: '经办时间', visible: true},
          {key: 'dataState', label: '审批状态', visible: true}
        ]
      },
      failData: []
    }
  },
  activated() {
    // 长连接页面第二次激活的时候,不会走created方法,会走此方法
    this.refreshData()
  },
  created() {
    this.baseDataLoad()
    this.refreshData()
  },
  mounted() {
    this.$nextTick(function () {
      this.table_height = window.innerHeight - this.$refs.table.$el.offsetTop - 84 - 65
      // 监听窗口大小变化
      const self = this
      window.onresize = function () {
        self.table_height = window.innerHeight - self.$refs.table.$el.offsetTop - 84 - 65
      }
    })
  },
  methods: {
    isEdit(row) {
      return row.lawFirmOutApprovalMain.dataStateCode === this.utils.dataState_BPM.SAVE.code || row.lawFirmOutApprovalMain.dataStateCode === this.utils.dataState_BPM.BACK.code
    },
    isDelete(row) {
      return row.lawFirmOutApprovalMain.dataStateCode === this.utils.dataState_BPM.SAVE.code
    },
    refreshData() { //  查询方法
      this.temp.orgId = this.orgContext.currentOrgId
      lawyerFirmApi.query(this.temp).then((res) => {
        this.tableData = res.data.page.records
        this.ss.data = this.tableData
        this.temp.total = res.data.page.total
      })
    },
    search_() { // 查询
      this.refreshData()
    },
    empty_() {
      this.temp = {
        lawyerFirm: null, // 律所名称
        functionary: null, // 负责人
        licenseCode: null, // 统一社会信用代码
        fas: null, // 传真
        email: null, // 邮件
        dataState: null, // 状态
        fuzzyValue: null, // 模糊字段
        sortName: null, // 排序
        order: false,
        page: 1,
        limit: 10,
        total: 0,
        isQuery: false
      }
      this.refreshData()
    },
    refresh_() {
      this.temp.sortName = null
      this.empty_()
    },
    add_() {
      const uuid = this.utils.createUUID();
      this.layout.openNewTab(
          "律所出库信息",
          "firm_out_main_detail",
          "firm_out_main_detail",
          uuid,
          {
            functionId: "firm_out_main_detail," + uuid,
            ...this.utils.routeState.NEW(uuid)
          }
      );
    },
    baseDataLoad() {
      dictApi.showSelect({
        dicCode: 'LS-CKYY'
      }).then(response => {
        this.failData = response.data.data
      })
    },
    tableSort(column) {
      if (column.prop.search("lawFirmOutApprovalMain.") !== -1) {
        this.temp.sortName = column.prop.replace("lawFirmOutApprovalMain.", "m.")
      } else {
        this.temp.sortName = 'd.' + column.prop
      }
      this.temp.order = column.order === 'ascending'
      this.refreshData()
    },
    rowDblclick(row) {
      if (row.lawFirmOutApprovalMain.dataStateCode !== this.utils.dataState_BPM.SAVE.code) {
        taskApi.selectTaskId({businessKey: row.lawFirmOutApprovalMain.id, isView: 'true'}).then(res => {
          const functionId = res.data.data[0].ID
          const tabId = this.utils.createUUID()
          this.layout.openNewTab("出库信息",
              "design_page",
              "design_page",
              tabId,
              {
                processInstanceId: res.data.data[0].PID,//流程实例
                taskId: res.data.data[0].ID,//任务ID
                businessKey: row.lawFirmOutApprovalMain.id, //业务数据ID
                functionId: functionId, //不传也可以，只有首环节需要传，实例启动就可以不传了
                entranceType: "FLOWABLE",
                type: "haveDealt",
                view: 'new'
              }
          )
        })
      } else {
        taskApi.selectFunctionId({functionCode: 'firm_out_main'}).then(res => {
          const functionId = res.data.data[0].ID
          const tabId = this.utils.createUUID()
          this.layout.openNewTab("出库信息",
              "design_page",
              "design_page",
              tabId,
              {
                functionId: functionId,
                entranceType: "FLOWABLE",
                ...this.utils.routeState.VIEW(row.lawFirmOutApprovalMain.id),
                channel: 'business',
                view: 'old'
              }
          )
        })
      }
    },
    edit_(index, row) {
      const tabId = this.utils.createUUID();
      this.layout.openNewTab(
          "出库信息",
          'firm_out_main_detail',
          'firm_out_main_detail',
          tabId,
          {
            functionId: 'firm_out_main_detail' + "," + tabId,
            ...this.utils.routeState.EDIT(row.lawFirmOutApprovalMain.id),
          }
      );
    },
    view_(index, row) {
      taskApi.selectTaskId({businessKey: row.lawFirmOutApprovalMain.id, isView: 'true'}).then(res => {
        if (res.data.data.length > 0) {
          const functionId = res.data.data[0].ID
          const tabId = this.utils.createUUID()
          this.layout.openNewTab("出库信息",
              "design_page",
              "design_page",
              tabId,
              {
                processInstanceId: res.data.data[0].PID,//流程实例
                taskId: res.data.data[0].ID,//任务ID
                businessKey: row.lawFirmOutApprovalMain.id, //业务数据ID
                functionId: functionId, //不传也可以，只有首环节需要传，实例启动就可以不传了
                entranceType: "FLOWABLE",
                type: "haveDealt",
                channel: 'business', //查看渠道，默认就传这个值即可，表示是从业务功能传过来的，首环节提交后过滤掉撤回按钮
                view: 'new',
              }
          )
        } else {
          const tabId = this.utils.createUUID();
          this.layout.openNewTab(
              "出库信息",
              'firm_out_main_detail',
              'firm_out_main_detail',
              tabId,
              {
                functionId: 'firm_out_main_detail' + "," + tabId,
                ...this.utils.routeState.VIEW(row.lawFirmOutApprovalMain.id)
              }
          )
        }
      })
    },
    delete_(index, row) {
      this.$confirm('您确定要删除吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        new Promise((resolve) => {
          lawyerFirmApi.delete({id: row.lawFirmOutApprovalMain.id}).then((response) => {
            resolve(response)
          })
          this.refreshData()
        }).then(() => {
          this.tableData.splice(index, 1)
          this.$message.success('删除成功!')
        })
      }).catch(() => {
        this.$message.info('已取消删除!')
      })
    },
  }
}

</script>

<style scoped>

</style>