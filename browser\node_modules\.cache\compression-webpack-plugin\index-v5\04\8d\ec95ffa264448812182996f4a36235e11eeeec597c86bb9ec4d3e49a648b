
a768eef576686d8ed192dddd7c4e34dbf3feb294	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.87.1754018536329.js\",\"contentHash\":\"1627325f61823f964822c8104fb9b483\"}","integrity":"sha512-m73dIxUCzRjGMbBalA9HvWIsZo3L9AILbXneXyIcJrt35M/s9Y8l2gfvd4lyRnu2nBe6Xp3RXpBABFt1uVGmgQ==","time":1754018575979,"size":141582}