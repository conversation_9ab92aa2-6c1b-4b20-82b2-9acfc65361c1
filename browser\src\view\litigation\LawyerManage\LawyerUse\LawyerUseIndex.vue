<template>
	<el-container direction="vertical" class="container-manage-sg">
		<el-header>
			<el-card>
				<div>
					<el-input
						v-model="temp.fuzzyValue"
						class="filter_input"
						placeholder="检索条件（聘用律师、关联案件、服务项目）"
						clearable
						@keyup.enter.native="refreshData"
						@clear="refreshData">
						<el-popover slot="prepend" placement="bottom-start" width="1000" trigger="click">
							<el-form ref="queryForm" label-width="100px" size="mini">
								<el-row>
									<el-col :span="12">
										<el-form-item label="申请公司">
											<el-input v-model="temp.applyDeptName" placeholder="请输入..." style="width: 100%" clearable />
										</el-form-item>
									</el-col>
									<el-col :span="12">
										<el-form-item label="服务项目">
											<el-input v-model="temp.serviceProject" placeholder="请输入..." style="width: 100%" clearable />
										</el-form-item>
									</el-col>
								</el-row>
								<el-row>
									<el-col :span="12">
										<el-form-item label="关联案件">
											<el-input v-model="temp.relationCaseName" placeholder="请输入..." style="width: 100%" clearable />
										</el-form-item>
									</el-col>
									<el-col :span="12">
										<el-form-item label="外聘类型">
											<el-select v-model="temp.selectionTypeName" placeholder="请选择" style="width: 100%" clearable @clear="refreshData">
												<el-option v-for="item in utils.selectionType_data" :key="item.dicName" :label="item.dicName" :value="item.dicName" />
											</el-select>
										</el-form-item>
									</el-col>
								</el-row>
								<el-row>
									<el-col :span="12">
										<el-form-item label="选聘模式">
											<el-select v-model="temp.selectionModeName" placeholder="请选择" style="width: 100%" clearable @clear="refreshData">
												<el-option v-for="item in utils.selectionMode_data" :key="item.dicName" :label="item.dicName" :value="item.dicName" />
											</el-select>
										</el-form-item>
									</el-col>
<!--									<el-col :span="12">-->
<!--										<el-form-item label="所属库">-->
<!--											<el-select-->
<!--												v-model="temp.typeCode"-->
<!--												placeholder="请选择"-->
<!--												style="width: 100%"-->
<!--												clearable-->
<!--												@keyup.enter.native="refreshData"-->
<!--												@clear="refreshData">-->
<!--												<el-option v-for="item in this.selectData" :key="item.code" :label="item.name" :value="item.code" />-->
<!--											</el-select>-->
<!--										</el-form-item>-->
<!--									</el-col>-->
								</el-row>
								<el-row>
									<el-col :span="12">
										<el-form-item label="律师名称">
											<el-input v-model="temp.lawyerName" placeholder="请输入..." style="width: 100%" clearable />
										</el-form-item>
									</el-col>
								</el-row>
								<el-button-group style="float: right">
									<el-button type="primary" icon="el-icon-search" @click="search_">搜索</el-button>
									<el-button type="primary" icon="el-icon-refresh-left" @click="empty_">重置</el-button>
								</el-button-group>
							</el-form>
							<el-button slot="reference" type="primary">高级检索</el-button>
						</el-popover>
						<el-button slot="append" icon="el-icon-search" @click="search_" />
					</el-input>
				</div>
			</el-card>
		</el-header>

		<el-main>
			<SimpleBoardIndex :title="'律师选聘登记'" :hasAdd="false">
				<el-table
					ref="table"
					:data="tableData"
					border
					:show-overflow-tooltip="true"
					style="width: 100%"
					:height="table_height"
					stripe
					fit
					highlight-current-row
					@sort-change="tableSort"
					@row-dblclick="rowDblclick">
					<el-table-column label="序号" type="index" align="center" width="50" />
					<el-table-column
						v-if="ss.tableColumns.find((item) => item.key === 'selectionModeName').visible"
						label="选聘模式"
						prop="selectionModeName"
						align="center"
						min-width="100"
						show-overflow-tooltip
						sortable="custom" />
					<el-table-column
						v-if="ss.tableColumns.find((item) => item.key === 'selectionTypeName').visible"
						label="外聘类型"
						prop="selectionTypeName"
						min-width="100"
						align="center"
						show-overflow-tooltip
						sortable="custom" />

					<el-table-column
						v-if="ss.tableColumns.find((item) => item.key === 'serviceProject').visible"
						label="服务项目/关联案件"
						prop="serviceProject"
						align="center"
						min-width="160"
						show-overflow-tooltip
						sortable="custom">
						<template slot-scope="scope">
							<span>{{ scope.row | serviceProjectFilter }}</span>
						</template>
					</el-table-column>

					<el-table-column
						v-if="ss.tableColumns.find((item) => item.key === 'beginTime').visible"
						label="开始时间"
						prop="createTime"
						min-width="100"
						align="center"
						show-overflow-tooltip
						sortable="custom">
						<template slot-scope="scope">
							<span>{{ scope.row.createTime | parseTime() }}</span>
						</template>
					</el-table-column>
					<el-table-column
						v-if="ss.tableColumns.find((item) => item.key === 'endTime').visible"
						label="结束时间"
						prop="createTime"
						min-width="100"
						align="center"
						show-overflow-tooltip
						sortable="custom">
						<template slot-scope="scope">
							<span>{{ scope.row.createTime | parseTime() }}</span>
						</template>
					</el-table-column>
					<el-table-column
						v-if="ss.tableColumns.find((item) => item.key === 'winLawFirm').visible"
						label="聘用律所"
						prop="winLawFirm"
						min-width="100"
						align="center"
						show-overflow-tooltip
						sortable="custom" />
<!--					<el-table-column-->
<!--						v-if="ss.tableColumns.find((item) => item.key === 'typeName').visible"-->
<!--						label="所属库"-->
<!--						prop="typeName"-->
<!--						min-width="100"-->
<!--						align="center"-->
<!--						show-overflow-tooltip-->
<!--						sortable="custom" />-->
					<el-table-column
						v-if="ss.tableColumns.find((item) => item.key === 'createTime').visible"
						label="经办时间"
						prop="createTime"
						min-width="100"
						align="center"
						show-overflow-tooltip
						sortable="custom">
						<template slot-scope="scope">
							<span>{{ scope.row.createTime | parseTime() }}</span>
						</template>
					</el-table-column>
					<el-table-column
						v-if="ss.tableColumns.find((item) => item.key === 'dataState').visible"
						label="状态"
						prop="dataState"
						min-width="80"
						align="center"
						show-overflow-tooltip
						sortable="custom" />
					<el-table-column label="操作" align="center" min-width="100" fixed="right">
						<template slot-scope="scope">
							<el-button v-if="scope.row.dataState !== utils.State.START.name" type="text" @click.native.prevent="editRow(scope.$index, scope.row)"
								>登记
							</el-button>
							<el-button type="text" @click.native.prevent="look(scope.$index, scope.row)">查看</el-button>
						</template>
					</el-table-column>
				</el-table>
			</SimpleBoardIndex>
		</el-main>

		<el-footer>
			<pagination align="center" :total="temp.total" :page.sync="temp.page" :limit.sync="temp.limit" @pagination="refreshData" />
		</el-footer>
	</el-container>
</template>

<script>
	import lawyerFirmApi from '@/api/LawyerManage/LawFirmSelection/lawFirmSelection';
	import pagination from '@/components/Pagination';
	import SimpleBoardIndex from '@/view/components/SimpleBoard/SimpleBoardIndex';
	import { mapGetters } from 'vuex';
	import taskApi from '@/api/_system/task';

	export default {
		name: 'LawyerUseIndex',
		inject: ['layout'],
		components: { pagination, SimpleBoardIndex },
		computed: {
			...mapGetters(['orgContext', 'currentFunctionId']),
		},
		filters: {
			serviceProjectFilter(row) {
				let name = '';
				if (row.serviceProject && row.serviceProject !== null) {
					name = row.serviceProject;
				} else {
					name = row.relationCaseName;
				}
				return name;
			},
		},
		data() {
			return {
				table_height: '100%', // 定义表格高度
				tableData: null, // 定义表格数据源
				temp: {
					applyDeptName: null, // 申请公司
					serviceProject: null, // 服务项目
					relationCaseName: null, // 关联案件
					selectionTypeName: null, // 外聘类型
					selectionModeName: null, // 选聘模式
					lawyerName: null,
					lawFirm: null,
					typeCode: null, // 库类型编码
					dataState: null, // 状态
					fuzzyValue: null, // 模糊字段
					sortName: null, // 排序
					functionType: 'use', // 排序
					order: false,
					page: 1,
					limit: 10,
					total: 0,
					isQuery: false,
					functionId: null,
					orgId: null,
				},
				ss: {
					data: this.tableData,
					tableColumns: [
						{ key: 'selectionModeName', label: '选聘模式', visible: true },
						{ key: 'selectionTypeName', label: '外聘类型', visible: true },
						{ key: 'beginTime', label: '开始时间', visible: true },
						{ key: 'endTime', label: '结束时间', visible: true },
						{ key: 'serviceProject', label: '服务项目/关联案件', visible: true },
						{ key: 'winLawFirm', label: '聘用律所', visible: true },
						// { key: 'typeName', label: '所属库', visible: true },
						{ key: 'createTime', label: '经办时间', visible: true },
						{ key: 'dataState', label: '状态', visible: true },
					],
				},
				// selectData: [
				// 	{ code: '0', name: '资源库' },
				// 	{ code: '1', name: '推荐库' },
				// ],
			};
		},
		activated() {
			// 长连接页面第二次激活的时候,不会走created方法,会走此方法
			this.refreshData();
		},
		created() {
			this.refreshData();
		},
		mounted() {
			this.$nextTick(function () {
				this.table_height = window.innerHeight - this.$refs.table.$el.offsetTop - 84 - 65;
				// 监听窗口大小变化
				const self = this;
				window.onresize = function () {
					self.table_height = window.innerHeight - self.$refs.table.$el.offsetTop - 84 - 65;
				};
			});
		},
		methods: {
			refreshData() {
				// 查询方法
				this.temp.functionId = this.currentFunctionId.id;
				this.temp.orgId = this.orgContext.currentOrgId;
				lawyerFirmApi.queryUseList(this.temp).then((res) => {
					this.tableData = res.data.page.records;
					this.ss.data = this.tableData;
					this.temp.total = res.data.page.total;

					var table = this.tableData;

					for (var i = 0; i < table.length; i++) {
						var lawyerList = table[i].lawyerList;

						for (var j = 0; j < lawyerList.length; j++) {
							if (lawyerList[j].whetherWin == '是') {
								table[i].winLawFirm = lawyerList[j].lawFirm;
								table[i].typeName = lawyerList[j].typeName;
							}
						}
					}
				});
			},
			search_() {
				// 查询
				this.refreshData();
			},
			empty_() {
				this.temp = {
					applyDeptName: null, // 申请公司
					serviceProject: null, // 服务项目
					relationCaseName: null, // 关联案件
					selectionTypeName: null, // 外聘类型
					selectionModeName: null, // 选聘模式
					typeCode: null, // 库类型编码
					dataState: null, // 状态
					fuzzyValue: null, // 模糊字段
					sortName: null, // 排序
					order: false,
					page: 1,
					limit: 10,
					total: 0,
					isQuery: false,
					functionId: null,
					orgId: null,
				};
				this.refreshData();
			},
			direct() {
				taskApi.selectFunctionId({ functionCode: 'firm_select_main_direct' }).then((res) => {
					const functionId = res.data.data[0].ID;
					const tabId = this.utils.createUUID();
					this.layout.openNewTab('选聘审批信息', 'design_page', 'design_page', tabId, {
						functionId: functionId,
						entranceType: 'FLOWABLE',
						...this.utils.routeState.NEW(tabId),
					});
				});
			},
			compare() {
				taskApi.selectFunctionId({ functionCode: 'firm_select_main_compare' }).then((res) => {
					const functionId = res.data.data[0].ID;
					const tabId = this.utils.createUUID();
					this.layout.openNewTab('选聘审批信息', 'design_page', 'design_page', tabId, {
						functionId: functionId,
						entranceType: 'FLOWABLE',
						...this.utils.routeState.NEW(tabId),
					});
				});
			},
			tableSort(column, prop, order) {
				this.temp.sortName = column.prop;
				this.temp.order = column.order === 'ascending';
				this.refreshData();
			},
			rowDblclick(row, column, event) {
				if (row.dataStateCode !== this.utils.dataState_BPM.SAVE.code) {
					taskApi.selectTaskId({ businessKey: row.id, isView: 'true' }).then((res) => {
						const functionId = res.data.data[0].ID;
						const tabId = this.utils.createUUID();
						this.layout.openNewTab('入库信息', 'design_page', 'design_page', tabId, {
							processInstanceId: res.data.data[0].PID, //流程实例
							taskId: res.data.data[0].ID, //任务ID
							businessKey: row.id, //业务数据ID
							functionId: functionId, //不传也可以，只有首环节需要传，实例启动就可以不传了
							entranceType: 'FLOWABLE',
							type: 'haveDealt',
							channel: 'business', //查看渠道，默认就传这个值即可，表示是从业务功能传过来的，首环节提交后过滤掉撤回按钮
						});
					});
				} else {
					let functionCode = null;

					if (row.selectionModeId === 3) {
						functionCode = 'firm_select_main_compare';
					} else {
						functionCode = 'firm_select_main_direct';
					}

					taskApi.selectFunctionId({ functionCode: functionCode }).then((res) => {
						const functionId = res.data.data[0].ID;
						const tabId = this.utils.createUUID();
						this.layout.openNewTab('审批信息', 'design_page', 'design_page', tabId, {
							functionId: functionId,
							entranceType: 'FLOWABLE',
							...this.utils.routeState.VIEW(row.id),
							channel: 'business',
						});
					});
				}
			},
			look(index, row) {
				const functionId = this.utils.createUUID();
				this.layout.openNewTab('律师对接登记', 'Lawyer_use_main_detail', 'Lawyer_use_main_detail', functionId, {
					functionId: 'condition_main,' + functionId,
					...this.utils.routeState.VIEW(row.id),
				});
			},
			editRow(index, row) {
				const functionId = this.utils.createUUID();
				this.layout.openNewTab('律师对接登记', 'Lawyer_use_main_detail', 'Lawyer_use_main_detail', functionId, {
					functionId: 'condition_main,' + functionId,
					...this.utils.routeState.EDIT(row.id),
				});
			},
		},
	};
</script>

<style scoped>
	.label_ {
		margin-top: 10px;
		text-align: right;
		padding-right: 6px;
	}
</style>
