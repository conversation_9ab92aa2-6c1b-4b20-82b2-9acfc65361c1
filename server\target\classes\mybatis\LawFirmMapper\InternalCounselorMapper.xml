<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.klaw.dao.lawyerDao.InternalCounselorMapper">
    <select id="queryStaffInfoById" resultType="java.util.Map">
        SELECT
            t.code AS "code",
            t.name AS "name",
            t.gender AS "gender",
            t.office_telephone AS "phone",
            t.user_status AS "status",
            t.organization AS "dept"
        from
            SG_ORGANIZATION o, SG_STAFF t
        where
            mcp_unit_id = #{id} and o.user_id = t.code
    </select>
</mapper>