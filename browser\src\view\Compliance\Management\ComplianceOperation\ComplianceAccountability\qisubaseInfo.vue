<template xmlns:v-slot="http://www.w3.org/1999/XSL/Transform">

  <div style="margin-top: 10px" v-if="view === 'old'">
    <!--基础信息表单块-->
    <div v-if="dataState !== 'view'">
      <div style="padding-left: 10px;margin-bottom: 20px;font-size: 15px;color: red;font-weight: bolder">

      </div>
      <div style="margin: 10px">
        <span style="font-weight: bold;font-size: 16px;color: #5A5A5F;">基本信息</span>
        <el-divider></el-divider>
      </div>
      <el-row style="margin-top: 10px">
        <el-col :span="8">
          <el-form-item label="审批单号" prop="code">
            <el-input v-if="!isView" v-model.trim="mainData.code" disabled show-word-limit style="width: 100%" />
            <span v-else class="viewSpan">{{ mainData.code }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="16">
          <el-form-item label="主题" prop="accountabilitySubject">
            <el-input v-if="!isView" v-model="mainData.accountabilitySubject" placeholder="请输入..." clearable />
            <span v-else class="viewSpan">{{ mainData.accountabilitySubject }}</span>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="8">
          <el-form-item label="时间" prop="accountabilityTime">
            <el-date-picker v-if="!isView" v-model="mainData.accountabilityTime" format="yyyy-MM-dd"
              value-format="yyyy-MM-dd" type="date" style="width: 100%;" />
            <span v-else class="viewSpan">{{ mainData.accountabilityTime | date }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="16">
          <el-form-item label="对象" prop="responsibleParty">
            <el-input v-if="!isView" v-model="mainData.responsibleParty" placeholder="请选择" class="input-with-select"
              disabled>
              <el-button slot="append" icon="el-icon-search" @click="orgVisible = true" />
            </el-input>
            <span v-else class="viewSpan">{{ mainData.responsibleParty }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="协同单位" prop="oaDept" v-if="mainData.type == '2'">
            <el-input v-if="!isView" v-model="mainData.oaDept" placeholder="请选择" class="input-with-select" disabled>
              <el-button slot="append" icon="el-icon-search" @click="orgVisibleOa = true" />
            </el-input>
            <span v-else class="viewSpan">{{ mainData.oaDept }}</span>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="关联事件" prop="relatedMatter">
            <span slot="label">关联事件</span>
            <el-input v-if="!isView" v-model="mainData.relatedMatter" :autosize="{ minRows: 3, maxRows: 20 }"
              type="textarea" placeholder="请输入关联事件" maxlength="500" show-word-limit />
            <text-span v-else class="viewSpan" :text="mainData.relatedMatter" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="违规事件" prop="violationMatter">
              <span slot="label">违规事件</span>
              <el-input v-if="!isView" v-model="mainData.violationMatter" :autosize="{ minRows: 3, maxRows: 20 }"
                type="textarea" placeholder="请输入违规事件" maxlength="500" show-word-limit />
              <text-span v-else class="viewSpan" :text="mainData.violationMatter" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="上传附件">
              <UploadDoc :files.sync="mainData.uploadAttachment" doc-path="/case" :disabled="isView" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-row>
      <!-- 集团包案领导 -->
      <OrgLeader :dialog-visible.sync="dicTreeDialogVisible3" @leaderSure="sureBtn3"></OrgLeader>
      <org-single-dialog-select :dialog-visible.sync="orgTreeDialog" :show-user="true" :show-group="true"
        :title="orgDialogTitle" :is-checked-user="false" @sureBtn="orgSelect" />

      <!--选择部门或者人员-->
      <el-dialog :close-on-click-modal="false" title="选择部门" :visible.sync="orgVisible" width="50%">
        <div class="el-dialog-div">
          <orgTree :accordion="false" :is-checked-user="false" :show-user="true" :is-check="false"
            :checked-data.sync="zxcheckedData" :is-not-cascade="true" :is-filter="true" />
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button icon="" class="negative-btn" @click="cancel_">取消</el-button>
          <el-button type="primary" icon="" class="active-btn" @click="choiceDeptSure_">确定
          </el-button>
        </span>
      </el-dialog>

      <!--选择协同部门-->
      <el-dialog :close-on-click-modal="false" title="选择部门" :visible.sync="orgVisibleOa" width="50%">
        <div class="el-dialog-div">
          <orgTree :accordion="false" :is-checked-user="false" :show-user="false" :is-check="true"
            :checked-data.sync="zxcheckedData" :is-not-cascade="true" :is-filter="true" />
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button icon="" class="negative-btn" @click="cancelOa_">取消</el-button>
          <el-button type="primary" icon="" class="active-btn" @click="choiceDeptSureOa_">确定
          </el-button>
        </span>
      </el-dialog>
    </div>
    <!-- 查看 -->
    <div v-else>
      <SimpleBoardTitle title="基本信息">
        <table class="table_content">
          <tbody>
            <tr>
              <th colspan="2" class="th_label_">主题</th>
              <td colspan="10" class="td_value_">{{ mainData.accountabilitySubject }}</td>
              <th colspan="2" class="th_label_">时间</th>
              <td colspan="10" class="td_value_">{{ mainData.accountabilityTime | parseTime }}</td>
            </tr>
            <tr>
              <th colspan="2" class="th_label">对象</th>
              <td colspan="22" class="td_value">{{ mainData.responsibleParty }}</td>
            </tr>
            <tr v-if="mainData.type == '2'">
              <th colspan="2" class="th_label">协同单位</th>
              <td colspan="22" class="td_value">{{ mainData.oaDept }}</td>
            </tr>
          </tbody>
          <tbody>
            <tr>
              <th colspan="2" class="th_label_">关联事项</th>
              <td colspan="22" class="td_value_">{{ mainData.relatedMatter }}</td>
            </tr>
            <tr>
              <th colspan="2" class="th_label_">违规事项</th>
              <td colspan="22" class="td_value_">{{ mainData.violationMatter }}</td>
            </tr>
            <tr>
              <th colspan="2" class="th_label">上传附件</th>
              <td colspan="22" class="td_value">
                <UploadDoc :files.sync="mainData.uploadAttachment" doc-path="/case" :disabled="isView" />
              </td>
            </tr>
          </tbody>
        </table>
      </SimpleBoardTitle>
    </div>

  </div>

  <div v-else>
    <SimpleBoardTitleApproval title="基本信息">
      <table class="table_content">
        <tbody>
          <tr>
            <th colspan="3" class="th_label_approval">问责主题</th>
            <td colspan="9" class="td_value_approval">{{ mainData.projectName }}</td>
            <th colspan="3" class="th_label_approval">项目编号</th>
            <td colspan="9" class="td_value_approval">{{ mainData.projectNumber }}</td>
          </tr>
          <tr>
            <th colspan="3" class="th_label_approval">经办人</th>
            <td colspan="9" class="td_value_approval">{{ mainData.createPsnFullName }}</td>
            <th colspan="3" class="th_label_approval">经办时间</th>
            <td colspan="9" class="td_value_approval">{{ mainData.createTime | parseTime }}</td>
          </tr>
          <tr>
            <th colspan="3" class="th_label_approval">经办人电话</th>
            <td colspan="9" class="td_value_approval">{{ mainData.createPsnPhone }}</td>
            <th colspan="3" class="th_label_approval">风险发生部门</th>
            <td colspan="9" class="td_value_approval">{{ queryOtherPartys() }}</td>

          </tr>
          <tr>
            <th colspan="3" class="th_label_approval">项目金额(万元)</th>
            <td colspan="9" class="td_value_approval">{{ mainData.projectAmount | toThousandFilter }}</td>
          </tr>

          <tr>
            <th colspan="3" class="th_label_approval_">项目概述</th>
            <td colspan="21" class="td_value_approval_">{{ mainData.projectOverview }}</td>
          </tr>
        </tbody>

        <tbody>
          <tr>
            <th colspan="3" class="th_label_approval">事项报告</th>
            <td colspan="21" class="td_value_approval" style="height: 100%">
              <div v-if="mainData.eventReport || isCreate">
                <uploadDoc v-model="mainData.eventReport" :files.sync="mainData.eventReport" :disabled="!isCreate"
                  doc-path="/case" />
              </div>
            </td>
          </tr>
        </tbody>

        <tbody>
          <tr>
            <th colspan="3" class="th_label_approval">附件材料</th>
            <td colspan="21" class="td_value_approval">
              <div v-if="mainData.relevantAttachments">
                <UploadDoc :files.sync="mainData.relevantAttachments" doc-path="/case" :disabled="true" />
              </div>
              <div v-else style="font-size: 15px">无</div>
            </td>
          </tr>
        </tbody>
      </table>
    </SimpleBoardTitleApproval>
  </div>
</template>

<script>
import dictApi from '@/api/_system/dict'
import AreaSelect from '@/view/components/AreaSelect/AreaSelect'
import OrgSingleDialogSelect from '@/view/components/OrgSingleDialogSelect/index.vue'
import UploadDoc from '@/view/components/UploadDoc/UploadDoc.vue'
import orgTree from '@/view/components/OrgTree/OrgTree'
import SimpleBoardTitle from "@/view/components/SimpleBoard/SimpleBoardTitle"
import SimpleBoardTitleApproval from "@/view/components/SimpleBoard/SimpleBoardTitleApproval"
import money from "@/view/components/Money/index"
import OrgLeader from '@/view/components/OrgLeader/OrgLeader'


export default {
  name: 'QsBaseInfo',
  components: {
    OrgSingleDialogSelect, AreaSelect, UploadDoc, orgTree, SimpleBoardTitleApproval, money,
    SimpleBoardTitle, OrgLeader
  },
  props: {
    data: {
      type: Object,
      default: function () {
        return {}
      }
    },
    dataState: {
      type: String,
      default: ''
    },
    view: {
      type: String,
      default: 'new'
    },
    create: {
      type: String,
      default: ''
    },
    authorizationData: {
      type: Object,
      default: function () {
        return {}
      }
    },
  },
  data() {
    return {
      dicTreeDialogVisible4: false,
      orgDialogTitle: '组织信息',
      caseDialogVisible: false,
      mainData: this.data,
      dicTreeDialogVisible: false,
      dicTreeDialogVisible2: false,
      dicTreeDialogVisible3: false,
      orgTreeDialog: false,
      caseNatures: [],
      plateData: [],
      causeOfIns: [],
      applications: [],
      involvedLevelData: [],//涉案单位管理层级
      suedUnitTypeData: [], //被诉单位性质
      unitTypeData: [],//单位类型
      expectedRiskLevelData: [],//案件级别
      eventNatureData: [],//事件性质
      businessFieldData: [],//业务领域
      zxcheckedData: [],
      orgVisible: false,
      orgVisibleOa: false,
    }
  },
  computed: {
    isView: function () {
      return this.dataState === this.utils.formState.VIEW
    },
    causeOfInIds: {
      set: function (data) {
        this.mainData.causeOfInId = data.join(',')
      },
      get: function () {
        if (this.mainData.causeOfInId) {
          return this.mainData.causeOfInId.split(',')
        }
        return []
      }
    },
    isCreate: function () {
      return this.create === 'create'
    },
  },
  watch: {
    mainData: {
      handler(val, oldVal) {
        this.$emit('update:data', val)
      },
      deep: true
    },
    data(val) {
      this.mainData = Object.assign(this.mainData, val)
    },

  },
  created() {
    // let year = new Date().getFullYear();
    // if (this.mainData.type == '1') {
    //   this.utils.createKvsequence('HGWZ' + year, 6).then((value) => {
    //     this.mainData.code = value.data.kvsequence;
    //   });
    // } else {
    //   this.utils.createKvsequence('HGJD' + year, 6).then((value) => {
    //     this.mainData.code = value.data.kvsequence;
    //   });
    // }
    this.initDic()
    // window.vm = this;
  },
  methods: {
    involvedAmountChange(val) {
      // 你的逻辑代码
    },
    expectedRiskLevelChange(val) {
      this.mainData.expectedRiskLevel = this.utils.getDicName(this.expectedRiskLevelData, val)
    },
    eventNatureChange(val) {
      this.mainData.eventNature = this.utils.getDicName(this.eventNatureData, val)
    },
    businessFieldChange(val) {
      this.mainData.businessField = this.utils.getDicName(this.businessFieldData, val)
    },
    venueIsOutOnChange(val) {
      this.mainData.venueAddress = null
      this.mainData.venueProvince = null
      this.mainData.venueCity = null
      this.mainData.venueRegion = null
    },
    sureBtn(data) {
      this.mainData.caseUndertakerId = data.unitId
      this.mainData.caseUndertaker = data.name
    },
    sureBtn2(data) {
      this.mainData.ognPackageId = data.unitId
      this.mainData.ognPackage = data.name
    },
    sureBtn3(data) {
      this.mainData.groupPackageId = data.unitId
      this.mainData.groupPackage = data.name
    },
    orgSelect(data) {
      this.mainData.currentUnitId = data.unitId
      this.mainData.currentUnit = data.name

      // this.mainData.participatingReviewDepartmentId = data.unitId
      this.mainData.responsibleParty = data.name
    },
    moneyFocus(event) {
      event.currentTarget.select()
    },
    initDic() {
      dictApi.showAllSelect({
        dicCode: 'SSBK'
      }).then(response => {
        this.plateData = response.data.data
      })
      dictApi.showAllSelect({
        dicCode: 'AJ_AJLX'
      }).then(response => {
        this.causeOfIns = response.data.data
      })
      dictApi.showAllSelect({
        dicCode: 'AJ_SQSX'
      }).then(response => {
        this.applications = response.data.data
      })
      dictApi.showAllSelect({
        dicCode: 'AJ-SADWCJ'
      }).then(response => {
        this.involvedLevelData = response.data.data
      })
      dictApi.showAllSelect({
        dicCode: 'AJ-DWLX'
      }).then(response => {
        this.unitTypeData = response.data.data
      })
      dictApi.showAllSelect({
        dicCode: 'AJ-BSDWXZ'
      }).then(response => {
        this.suedUnitTypeData = response.data.data
      })
      dictApi.showAllSelect({
        dicCode: 'AJ-FXJB'
      }).then(response => {
        this.expectedRiskLevelData = response.data.data
      })
      dictApi.showAllSelect({
        dicCode: 'AJ_SJXZ'
      }).then(response => {
        this.eventNatureData = response.data.data
      })
      dictApi.showAllSelect({
        dicCode: 'AJ_YWLY'
      }).then(response => {
        this.businessFieldData = response.data.data
      })
    },
    chooseApprovalDeptClick() {
      this.isCheckedUser = false
      this.showUser = false
      this.orgVisible = true
      this.is_Check = false
    },
    cancel_() {
      this.orgVisible = false
    },
    choiceDeptSure_() {
      let c = ''
      let cid = ''
      this.zxcheckedData.forEach((item) => {
        if (c.length === 0) {
          c = c + item.name
          cid = cid + item.unitId
        } else {
          c = c + ',' + item.name
          cid = cid + ',' + item.unitId
        }
      })
      // this.mainData.noticeDeptName = c
      // this.mainData.noticeDeptId = cid
      this.mainData.responsibleParty = c
      this.mainData.responsiblePartyCode = cid
      this.orgVisible = false
    },
    cancelOa_() {
      this.orgVisibleOa = false
    },
    choiceDeptSureOa_() {
      let c = ''
      let cid = ''
      this.zxcheckedData.forEach((item) => {
        if (c.length === 0) {
          c = c + item.name
          cid = cid + item.unitId
        } else {
          c = c + ',' + item.name
          cid = cid + ',' + item.unitId
        }
      })
      // this.mainData.noticeDeptName = c
      // this.mainData.noticeDeptId = cid
      this.mainData.oaDept = c
      console.log("this.mainData.oaDept ", this.mainData.oaDept)
      // this.mainData.oaDeptCode = cid
      this.orgVisibleOa = false
    },
    queryOtherPartys() {
      let partys = this.mainData.partiesList
      let otherPartys = ""

      if (partys.length > 0) {
        for (let i = 0; i < partys.length; i++) {
          if (partys[i].partyType !== '原告' && partys[i].party !== null && partys[i].party !== undefined)
            otherPartys += partys[i].party + ","
        }

        if (otherPartys !== "")
          otherPartys = otherPartys.slice(0, -1)
      }

      return otherPartys
    }
  }
}
</script>

<style lang="scss" scoped>
.hideContent {

  .el-input__inner,
  .el-radio.is-bordered,
  .el-textarea__inner,
  .el-input__count {
    background-color: #f9e8bb;
  }
}

.money-label-width .el-form-item__label {
  width: 150px;
  /* 指定宽度 */
}
</style>
