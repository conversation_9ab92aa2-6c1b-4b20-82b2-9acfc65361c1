<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.klaw.dao.contractDao.SgProjectManageMapper">
  <resultMap id="BaseResultMap" type="com.klaw.entity.mainDataBean.SgProjectManage">
      <id column="CODE"  property="code" />
      <result column="PROJECT_NAME"  property="projectName" />
      <result column="PROJECT_CODE"  property="projectCode" />
      <result column="PROJECT_TYPE_CODE"  property="projectTypeCode" />
      <result column="PROJECT_TYPE"  property="projectType" />
      <result column="PROJECT_CHILD_CODE"  property="projectChildCode" />
      <result column="PROJECT_CHILD"  property="projectChild" />
      <result column="PROJECT_STATE_CODE"  property="projectStateCode" />
      <result column="PROJECT_STATE"  property="projectState" />
      <result column="INVESTMENT_UNIT_CODE"  property="investmentUnitCode" />
      <result column="INVESTMENT_UNIT"  property="investmentUnit" />
      <result column="PROJECT_DEPT_CODE"  property="projectDeptCode" />
      <result column="PROJECT_DEPT"  property="projectDept" />
      <result column="PROJECT_PERSON_CODE"  property="projectPersonCode" />
      <result column="PROJECT_PERSON"  property="projectPerson" />
      <result column="ESCROW_UNIT_CODE"  property="escrowUnitCode" />
      <result column="ESCROW_UNIT"  property="escrowUnit" />
      <result column="DOMESTIC_AND_FOREIGN_CODE"  property="domesticAndForeignCode" />
      <result column="DOMESTIC_AND_FOREIGN"  property="domesticAndForeign" />
      <result column="PROJECT_INDUSTRY_CODE"  property="projectIndustryCode" />
      <result column="PROJECT_INDUSTRY"  property="projectIndustry" />
      <result column="ENTERPRISE_NAME"  property="enterpriseName" />
      <result column="PROJECT_INVESTMENT"  property="projectInvestment" />
      <result column="CATEGORY_CODE"  property="categoryCode" />
      <result column="CATEGORY"  property="category" />
      <result column="STATE"  property="state" />
      <result column="STATE_DESC"  property="stateDesc" />
      <result column="TYPE"  property="type" />
      <result column="PROJECT_SOURCE"  property="projectSource" />
      <result column="PROJECT_UNIT"  property="projectUnit" />
      <result column="PROJECT_UNIT_CODE"  property="projectUnitCode" />
      <result column="PARTICIPANT_UNIT"  property="participantUnit" />
      <result column="PARTICIPANT_UNIT_CODE"  property="participantUnitCode" />
      <result column="PARTNER_UNIT"  property="partnerUnit" />
      <result column="PARTNER_UNIT_CODE"  property="partnerUnitCode" />
      <result column="BUSINESS_CODE"  property="businessCode" />
      <result column="REMARKS"  property="remarks" />
      <result column="ATTACHMENT"  property="attachment" />
      <result column="HOLD_MONEY"  property="holdMoney" />
      <result column="SURPLUS_MONEY"  property="surplusMoney" />
  </resultMap>
  <sql id="Base_Column_List">
        CODE,
        PROJECT_NAME,
        PROJECT_CODE,
        PROJECT_TYPE_CODE,
        PROJECT_TYPE,
        PROJECT_CHILD_CODE,
        PROJECT_CHILD,
        PROJECT_STATE_CODE,
        PROJECT_STATE,
        INVESTMENT_UNIT_CODE,
        INVESTMENT_UNIT,
        PROJECT_DEPT_CODE,
        PROJECT_DEPT,
        PROJECT_PERSON_CODE,
        PROJECT_PERSON,
        ESCROW_UNIT_CODE,
        ESCROW_UNIT,
        DOMESTIC_AND_FOREIGN_CODE,
        DOMESTIC_AND_FOREIGN,
        PROJECT_INDUSTRY_CODE,
        PROJECT_INDUSTRY,
        ENTERPRISE_NAME,
        PROJECT_INVESTMENT,
        CATEGORY_CODE,
        CATEGORY,
        STATE,
        STATE_DESC,
        TYPE,
        PROJECT_SOURCE,
        PROJECT_UNIT,
        PROJECT_UNIT_CODE,
        PARTICIPANT_UNIT,
        PARTICIPANT_UNIT_CODE,
        PARTNER_UNIT,
        PARTNER_UNIT_CODE,
        BUSINESS_CODE,
        REMARKS,
        ATTACHMENT,
        HOLD_MONEY,
        SURPLUS_MONEY
  </sql>
<!--  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">-->
<!--    &lt;!&ndash;@mbg.generated&ndash;&gt;-->
<!--    select -->
<!--    <include refid="Base_Column_List" />-->
<!--    from SG_PROJECT_MANAGE-->
<!--    where ID = #{id,jdbcType=VARCHAR}-->
<!--  </select>-->
<!--  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">-->
<!--    &lt;!&ndash;@mbg.generated&ndash;&gt;-->
<!--    delete from SG_PROJECT_MANAGE-->
<!--    where ID = #{id,jdbcType=VARCHAR}-->
<!--  </delete>-->
<!--  <insert id="insert" parameterType="com.klaw.entity.mainDataBean.SgProjectManage">-->
<!--    &lt;!&ndash;@mbg.generated&ndash;&gt;-->
<!--    insert into SG_PROJECT_MANAGE (ID, PROJECT_NAME, PROJECT_CODE, -->
<!--      REMARK, CREATE_OGN_ID, CREATE_OGN_NAME, -->
<!--      CREATE_DEPT_ID, CREATE_DEPT_NAME, CREATE_GROUP_ID, -->
<!--      CREATE_GROUP_NAME, CREATE_PSN_ID, CREATE_PSN_NAME, -->
<!--      CREATE_ORG_ID, CREATE_ORG_NAME, CREATE_PSN_FULL_ID, -->
<!--      CREATE_PSN_FULL_NAME, CREATE_TIME, UPDATE_TIME, -->
<!--      DATA_STATE, DATA_STATE_CODE)-->
<!--    values (#{id,jdbcType=VARCHAR}, #{projectName,jdbcType=VARCHAR}, #{projectCode,jdbcType=VARCHAR}, -->
<!--      #{remark,jdbcType=CLOB}, #{createOgnId,jdbcType=VARCHAR}, #{createOgnName,jdbcType=VARCHAR}, -->
<!--      #{createDeptId,jdbcType=VARCHAR}, #{createDeptName,jdbcType=VARCHAR}, #{createGroupId,jdbcType=VARCHAR}, -->
<!--      #{createGroupName,jdbcType=VARCHAR}, #{createPsnId,jdbcType=VARCHAR}, #{createPsnName,jdbcType=VARCHAR}, -->
<!--      #{createOrgId,jdbcType=VARCHAR}, #{createOrgName,jdbcType=VARCHAR}, #{createPsnFullId,jdbcType=VARCHAR}, -->
<!--      #{createPsnFullName,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, -->
<!--      #{dataState,jdbcType=VARCHAR}, #{dataStateCode,jdbcType=DECIMAL})-->
<!--  </insert>-->
<!--  <insert id="insertSelective" parameterType="com.klaw.entity.mainDataBean.SgProjectManage">-->
<!--    &lt;!&ndash;@mbg.generated&ndash;&gt;-->
<!--    insert into SG_PROJECT_MANAGE-->
<!--    <trim prefix="(" suffix=")" suffixOverrides=",">-->
<!--      <if test="id != null">-->
<!--        ID,-->
<!--      </if>-->
<!--      <if test="projectName != null">-->
<!--        PROJECT_NAME,-->
<!--      </if>-->
<!--      <if test="projectCode != null">-->
<!--        PROJECT_CODE,-->
<!--      </if>-->
<!--      <if test="remark != null">-->
<!--        REMARK,-->
<!--      </if>-->
<!--      <if test="createOgnId != null">-->
<!--        CREATE_OGN_ID,-->
<!--      </if>-->
<!--      <if test="createOgnName != null">-->
<!--        CREATE_OGN_NAME,-->
<!--      </if>-->
<!--      <if test="createDeptId != null">-->
<!--        CREATE_DEPT_ID,-->
<!--      </if>-->
<!--      <if test="createDeptName != null">-->
<!--        CREATE_DEPT_NAME,-->
<!--      </if>-->
<!--      <if test="createGroupId != null">-->
<!--        CREATE_GROUP_ID,-->
<!--      </if>-->
<!--      <if test="createGroupName != null">-->
<!--        CREATE_GROUP_NAME,-->
<!--      </if>-->
<!--      <if test="createPsnId != null">-->
<!--        CREATE_PSN_ID,-->
<!--      </if>-->
<!--      <if test="createPsnName != null">-->
<!--        CREATE_PSN_NAME,-->
<!--      </if>-->
<!--      <if test="createOrgId != null">-->
<!--        CREATE_ORG_ID,-->
<!--      </if>-->
<!--      <if test="createOrgName != null">-->
<!--        CREATE_ORG_NAME,-->
<!--      </if>-->
<!--      <if test="createPsnFullId != null">-->
<!--        CREATE_PSN_FULL_ID,-->
<!--      </if>-->
<!--      <if test="createPsnFullName != null">-->
<!--        CREATE_PSN_FULL_NAME,-->
<!--      </if>-->
<!--      <if test="createTime != null">-->
<!--        CREATE_TIME,-->
<!--      </if>-->
<!--      <if test="updateTime != null">-->
<!--        UPDATE_TIME,-->
<!--      </if>-->
<!--      <if test="dataState != null">-->
<!--        DATA_STATE,-->
<!--      </if>-->
<!--      <if test="dataStateCode != null">-->
<!--        DATA_STATE_CODE,-->
<!--      </if>-->
<!--    </trim>-->
<!--    <trim prefix="values (" suffix=")" suffixOverrides=",">-->
<!--      <if test="id != null">-->
<!--        #{id,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="projectName != null">-->
<!--        #{projectName,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="projectCode != null">-->
<!--        #{projectCode,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="remark != null">-->
<!--        #{remark,jdbcType=CLOB},-->
<!--      </if>-->
<!--      <if test="createOgnId != null">-->
<!--        #{createOgnId,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="createOgnName != null">-->
<!--        #{createOgnName,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="createDeptId != null">-->
<!--        #{createDeptId,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="createDeptName != null">-->
<!--        #{createDeptName,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="createGroupId != null">-->
<!--        #{createGroupId,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="createGroupName != null">-->
<!--        #{createGroupName,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="createPsnId != null">-->
<!--        #{createPsnId,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="createPsnName != null">-->
<!--        #{createPsnName,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="createOrgId != null">-->
<!--        #{createOrgId,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="createOrgName != null">-->
<!--        #{createOrgName,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="createPsnFullId != null">-->
<!--        #{createPsnFullId,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="createPsnFullName != null">-->
<!--        #{createPsnFullName,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="createTime != null">-->
<!--        #{createTime,jdbcType=TIMESTAMP},-->
<!--      </if>-->
<!--      <if test="updateTime != null">-->
<!--        #{updateTime,jdbcType=TIMESTAMP},-->
<!--      </if>-->
<!--      <if test="dataState != null">-->
<!--        #{dataState,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="dataStateCode != null">-->
<!--        #{dataStateCode,jdbcType=DECIMAL},-->
<!--      </if>-->
<!--    </trim>-->
<!--  </insert>-->
<!--  <update id="updateByPrimaryKeySelective" parameterType="com.klaw.entity.mainDataBean.SgProjectManage">-->
<!--    &lt;!&ndash;@mbg.generated&ndash;&gt;-->
<!--    update SG_PROJECT_MANAGE-->
<!--    <set>-->
<!--      <if test="projectName != null">-->
<!--        PROJECT_NAME = #{projectName,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="projectCode != null">-->
<!--        PROJECT_CODE = #{projectCode,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="remark != null">-->
<!--        REMARK = #{remark,jdbcType=CLOB},-->
<!--      </if>-->
<!--      <if test="createOgnId != null">-->
<!--        CREATE_OGN_ID = #{createOgnId,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="createOgnName != null">-->
<!--        CREATE_OGN_NAME = #{createOgnName,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="createDeptId != null">-->
<!--        CREATE_DEPT_ID = #{createDeptId,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="createDeptName != null">-->
<!--        CREATE_DEPT_NAME = #{createDeptName,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="createGroupId != null">-->
<!--        CREATE_GROUP_ID = #{createGroupId,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="createGroupName != null">-->
<!--        CREATE_GROUP_NAME = #{createGroupName,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="createPsnId != null">-->
<!--        CREATE_PSN_ID = #{createPsnId,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="createPsnName != null">-->
<!--        CREATE_PSN_NAME = #{createPsnName,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="createOrgId != null">-->
<!--        CREATE_ORG_ID = #{createOrgId,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="createOrgName != null">-->
<!--        CREATE_ORG_NAME = #{createOrgName,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="createPsnFullId != null">-->
<!--        CREATE_PSN_FULL_ID = #{createPsnFullId,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="createPsnFullName != null">-->
<!--        CREATE_PSN_FULL_NAME = #{createPsnFullName,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="createTime != null">-->
<!--        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},-->
<!--      </if>-->
<!--      <if test="updateTime != null">-->
<!--        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},-->
<!--      </if>-->
<!--      <if test="dataState != null">-->
<!--        DATA_STATE = #{dataState,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="dataStateCode != null">-->
<!--        DATA_STATE_CODE = #{dataStateCode,jdbcType=DECIMAL},-->
<!--      </if>-->
<!--    </set>-->
<!--    where ID = #{id,jdbcType=VARCHAR}-->
<!--  </update>-->
<!--  <update id="updateByPrimaryKey" parameterType="com.klaw.entity.mainDataBean.SgProjectManage">-->
<!--    &lt;!&ndash;@mbg.generated&ndash;&gt;-->
<!--    update SG_PROJECT_MANAGE-->
<!--    set PROJECT_NAME = #{projectName,jdbcType=VARCHAR},-->
<!--      PROJECT_CODE = #{projectCode,jdbcType=VARCHAR},-->
<!--      REMARK = #{remark,jdbcType=CLOB},-->
<!--      CREATE_OGN_ID = #{createOgnId,jdbcType=VARCHAR},-->
<!--      CREATE_OGN_NAME = #{createOgnName,jdbcType=VARCHAR},-->
<!--      CREATE_DEPT_ID = #{createDeptId,jdbcType=VARCHAR},-->
<!--      CREATE_DEPT_NAME = #{createDeptName,jdbcType=VARCHAR},-->
<!--      CREATE_GROUP_ID = #{createGroupId,jdbcType=VARCHAR},-->
<!--      CREATE_GROUP_NAME = #{createGroupName,jdbcType=VARCHAR},-->
<!--      CREATE_PSN_ID = #{createPsnId,jdbcType=VARCHAR},-->
<!--      CREATE_PSN_NAME = #{createPsnName,jdbcType=VARCHAR},-->
<!--      CREATE_ORG_ID = #{createOrgId,jdbcType=VARCHAR},-->
<!--      CREATE_ORG_NAME = #{createOrgName,jdbcType=VARCHAR},-->
<!--      CREATE_PSN_FULL_ID = #{createPsnFullId,jdbcType=VARCHAR},-->
<!--      CREATE_PSN_FULL_NAME = #{createPsnFullName,jdbcType=VARCHAR},-->
<!--      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},-->
<!--      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},-->
<!--      DATA_STATE = #{dataState,jdbcType=VARCHAR},-->
<!--      DATA_STATE_CODE = #{dataStateCode,jdbcType=DECIMAL}-->
<!--    where ID = #{id,jdbcType=VARCHAR}-->
<!--  </update>-->
    <select id="queryPageList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from SG_PROJECT_MANAGE
        <where>
            ${ew.sqlSegment}
        </where>

    </select>
</mapper>