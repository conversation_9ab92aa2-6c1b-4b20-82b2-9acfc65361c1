import axios from '../js_public/axios';
import qs from 'qs'
axios.defaults.headers.common['X-Requested-With'] = 'XMLHttpRequest';
let headersConfig = {
    headers: {
        'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
    }
};
let headers= {
    'Content-Type': 'application/json;charset=UTF-8'
};
/*-----------------------------界面菜单获取-----------------------------*/
//菜单获取
/*export const menus = data => {
    const url = '/sys/function/menus';
    return axios.get(url, data);
};*/
export const menus = data => {
    let timestamp = new Date().getTime();
    const url = `/sys/function/menus?t=${timestamp}`;
    return axios.get(url, data);
};

/*-----------------------------用户有关功能-----------------------------*/
//用户登录
export const userLogin = data => {
    const url = '/login';
    return axios.post(url, data, headersConfig);
};
//用户登录
export const userLogin1 = data => {
    const url = '/login';
    return axios.post(url, data, headers);
};

export const loginInfo = data => {
    const url = '/loginInfo';
    return axios.post(url, data);
};
//退出登录
export const userLogout = data => {
    const url = '/logout';
    return axios.post(url, data, headersConfig);
};
//获取用户信息(用户名精确查询)
export const getUserInfoByName = data => {
    const url = `/sys/user/queryByUserName`;
    return axios.post(url, data, headersConfig);
};
// 业务变量 新增、修改
export const saveBusinessVariables = (data) =>{
    const url = '/wfl/business/variables/save';
    return axios.post(url, data);
}

export const queryBusinessVariables = (data) =>{
    const url = '/wfl/business/variables/query';
    return axios.post(url, data);
}

export const deleteBusinessVariables = (data) =>{
    const url = '/wfl/business/variables/delete';
    return axios.post(url, data);
}

/*-----------------------------资源管理-----------------------------*/
// 列表查询接口
export const resourceQuery = (data) =>{
    const url = 'sys/resource/query';
    return axios.post(url, data, headersConfig);
}
// 资源新增、编辑接口
export const resourceSubmit = (data) =>{
    const url = 'sys/resource/submit';
    return axios.post(url, data);
}
// 资源删除接口
export const resourceDelete = (data) =>{
    const url = 'sys/resource/remove';
    return axios.post(url, data);
}
// 个性化查询接口
export const characterQuery = (data,resourceId)=>{
    const url = 'sys/resourceCustomization/query?resourceId='+resourceId;
    return axios.post(url, data, headersConfig);
}
// 个性化删除接口
export const characterDelete = (data)=>{
    const url = 'sys/resourceCustomization/remove';
    return axios.post(url, data);
}
// 个性化删除接口
export const characterSubmit = (data)=>{
    const url = 'sys/resourceCustomization/submit';
    return axios.post(url, data);
}
// 权限组件查询接口
export const powerComponentQuery = (data,resourceId)=>{
    const url = 'sys/resourceItem/query?resourceId='+resourceId;
    return axios.post(url, data, headersConfig);
}
// 权限组件新增、编辑接口
export const powerComponentSubmit = (data)=>{
    const url = 'sys/resourceItem/submit';
    return axios.post(url, data);
}
// 权限组件删除接口
export const powerComponentDelete = (data)=>{
    const url = 'sys/resourceItem/remove';
    return axios.post(url, data);
}
// 权限组件详情查询接口
export const powerComponentDetailQuery = (data,resourceItemId)=>{
    const url = 'sys/resourceItemElement/query?resourceItemId='+resourceItemId;
    return axios.post(url, data, headersConfig);
}
// 权限组件详情提交接口
export const powerComponentDetailSubmit = (data)=>{
    const url = 'sys/resourceItemElement/submit';
    return axios.post(url, data);
}
// 权限组件详情删除接口
export const powerComponentDetailDelete = (data)=>{
    const url = 'sys/resourceItemElement/remove';
    return axios.post(url, data);
}

//获取token
export const get_CSRF = data => {
    const url = '/getToken';
    return axios.post(url, data);
};
//获取用户首选项
export const getPreferences = data => {
    const url = '/sys/preferences/queryPreferences'
    return axios.post(url,data)
}
/*-----------------------------员工关系-----------------------------*/
export const hrEmpUnitQuery = data => {
    const url = `/hr/empUnit/query`;
    return axios.post(url,data);
};

/*-----------------------------字典项查询-----------------------------*/
export const dictionaryCode = data => {
    const url = `/common/codes?code=${data.code}`;
    return axios.post(url, data);
};
/*-----------------------------管理成本设置-----------------------------*/
//岗位信息查询
export const unitQuery = data => {
    const url = `common/lov/LOV_UNIT`;
    return axios.post(url, data, headersConfig);
};
/**---------------------------节假日---------------------------------------- */
//节假日添加或修改
export const queryHolidays = data => {
    const url = `/base/business/info/queryHolidays?ifHoliday=${data.years}`;
    return axios.post(url,qs.stringify(data),headersConfig);
};
export const checkHolidays = data => {
    const url = `/base/business/info/checkNow`;
    return axios.get(url, data);
};
/*-----------------------------组织管理-------------------------------------*/
export const hrUnitQueryAll = data => {
    const url = `/hr/unit/queryall`;
    return axios.post(url,data);
};
/*-----------------------------导出 导入-------------------------------------*/

export const chargeExport = (url,data) => {
    // const url = `/item/travel/charge/export`;
    return axios.post(url,data,{
        responseType: 'blob',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
        }
    });
};

export const templateDown = (data) => {
    const url = `/sys/attach/file/downloadFiled`;
    return axios.post(url,data,{
        responseType: 'blob'
    });
};
/*-----------------------------真实成本-------------------------------------*/
export const userRoleQuery = data => {
    const url = `/sys/role/query`;
    return axios.post(url,data,headersConfig);
};
/*-----------------------------获取权限-------------------------------------*/

export const getPermissions = data => {
    const url = `/sys/function/accessNoCheckData?functionCode=${data.functionCode}`;
    return axios.get(url,data,headersConfig);
};
/**  首页下载 */
export const homeDownloadFiled = data => {
    const url = `/sys/attach/file/download`;
    return axios.post(url,data,{
        responseType: 'blob',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
        }
    });
};
/** 获取下载地址 */
export const homeAttachmentQuery = data => {
    const url = `/sys/attachment/query`;
    return axios.post(url,data,headersConfig);
};
//获取下拉选数据  通过出入地址
export const getOption = (data={},url) => {
	return axios.post(url,qs.stringify(data),headersConfig);
};
//新建邮件模板
export const addTemplates = data =>{
    const url = `/sys/messageTemplate/add`;
    return axios.post(url,data,headers);
}
//查询附件
export const getFile = data =>{
    const url = `/sys/attach/file/queryFiles`;
    return axios.post(url,data,headersConfig);
}
/**-------------------接口管理-------------------------- */
//删除接口配置信息
export const delInterfaceConfig = data =>{
    const url = `/sys/interface/deleteHeader`;
    return axios.post(url,data,headers);
}
/**-------------------工作流-------------------------- */
//url 参数转成对象
export const parseUrl = function (url) {
    var obj = {};
    var keyvalue = [];
    var paraString = url.substring(url.indexOf("?") + 1, url.length).split("&");
    for (var i in paraString) {
        keyvalue = paraString[i].split("=");
        obj[keyvalue[0]] = keyvalue[1];
    }
    return obj;
}

/**
 * 业务模板查询接口
 */

export const businessConfigQuery = data =>{
    const url = `/dynamic/businessConfig/querycpx`;
    return axios.post(url,data);
}

export const updatebusinessTemplate = data =>{
    const url = `/dynamic/businessConfig/updatecpx`;
    return axios.post(url,data);
}
//查询消息中心详情
export const getDetailedInformation = data =>{
	const url = `/sys/message/receiver/detail`;
	return axios.post(url,data,headersConfig);
}
// 流程监控获取流程图
export const getFlowChartData = procInstId =>{
    const url = `/wfl/monitor/${procInstId}/diagram`;
    return axios.get(url);
}

//
export const getTaskById = data =>{
    const url = `/wfl/query/queryTaskDetailed`;
    return axios.post(url,data);
}

export const queryCurrentInfo= data =>{
    const url = `/sys_org/queryCurrentInfo`;
    return axios.post(url,data);
}

export const queryCurrentAllOrg= data =>{
    const url = `/sys_org/queryCurrentAllOrg`;
    return axios.post(url,data);
}

//获取当前登录用户信息
export const getCurrentUserInfo = data => {
    const url = 'sys/organization/user/queryUserDetail';
    return axios.post(url);
};

export const changeOrgUnit = data => {
    const url = '/changeOrgUnit';
    return axios.post(url, data,headersConfig);
};

//个人仪表查询
export const sysDashboardQuery = data =>{
    const url = `/dashboard/query`;
    return axios.post(url, data,headersConfig);
};

export const request = axios
