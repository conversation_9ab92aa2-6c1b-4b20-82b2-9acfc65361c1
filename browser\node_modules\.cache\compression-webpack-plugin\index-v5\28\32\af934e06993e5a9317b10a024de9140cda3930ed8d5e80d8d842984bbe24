
26b192c6d7559cfd012d83363f27cad745f37f1d	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.84.1754018536329.js\",\"contentHash\":\"4213f252bd6de460f50955de026e7fc4\"}","integrity":"sha512-Yb5vGfDOsIEctXKK1ITYMMVtmWSNVj7WWBpwI3/7fe02kfhvmvaTwmW3Jvf1n6Vype2sbB8Mdeuo8v04vkth4g==","time":1754018575956,"size":45954}