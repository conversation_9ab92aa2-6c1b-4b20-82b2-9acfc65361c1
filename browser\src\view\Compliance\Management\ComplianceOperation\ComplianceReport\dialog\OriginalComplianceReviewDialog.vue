<template>
  <el-dialog :close-on-click-modal="false" title="重大决策事项合规审查列表" :visible.sync="dialogVisible" width="70%">
    <div style="margin-bottom: 5px">
      <!--      <el-row>-->
      <!--        <el-col :span="4">-->
      <!--          <el-input v-model="tableQuery.fuzzyValue" clearable placeholder="模糊搜索" @clear="empty_"-->
      <!--                    @keyup.enter.native="select_"/>-->
      <!--        </el-col>-->
      <!--        <el-col :span="2">-->
      <!--          <el-button type="primary" @click="select_">查询</el-button>-->
      <!--        </el-col>-->
      <!--        <el-col :span="2" :offset="0">-->
      <!--          <el-button type="primary" @click="refresh">刷新</el-button>-->
      <!--        </el-col>-->
      <!--      </el-row>-->
      <span>
          <el-input style="width: 20%" v-model="tableQuery.fuzzyValue" clearable placeholder="模糊搜索" @clear="empty_"
                    @keyup.enter.native="select_"/>
          <el-button type="primary" @click="select_">查询</el-button>
<!--          <el-button type="primary" @click="refresh">刷新</el-button>-->
      </span>
    </div>
    <div>
      <el-table
          v-loading="tableLoading"
          :data="tableData"
          border
          style="table-layout: fixed;width: 99%;"
          :height="dialog_height"
          :fit="true"
          stripe
          highlight-current-row
          @current-change="contractCurrentChange"
      >
        <el-table-column type="index" label="序号"/>
        <el-table-column prop="reviewNumber" label="审查编号" show-overflow-tooltip/>
        <el-table-column prop="reviewSubject" label="审查主题" show-overflow-tooltip/>
        <el-table-column prop="businessArea" label="业务领域" show-overflow-tooltip/>
        <el-table-column prop="createTime" label="经办时间" :formatter="formatDate" show-overflow-tooltip/>
<!--        <el-table-column prop="dataState" label="审批状态" show-overflow-tooltip/>-->
      </el-table>
    </div>
    <div style="text-align: center">
      <el-footer style="height: 80px;">
        <pagination
            v-show="tableQuery.total>0"
            :total="tableQuery.total"
            :page.sync="tableQuery.page"
            :limit.sync="tableQuery.limit"
            style="padding:20px 16px;"
            @pagination="refreshData"
        />
      </el-footer>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button icon="" class="negative-btn" @click="cancel_">取消</el-button>
      <el-button type="primary" class="active-btn" @click="sure_">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import complianceReviewApi from '@/api/ComplianceReview/complianceReview.js'
import pagination from "@/components/Pagination"
import textSpan from '@/view/components/TextSpan/TextSpan'
import {mapGetters} from "vuex";


export default {
  name: 'AutEvaluationDialog',
  components: {pagination, textSpan},
  filters: {
    whetherHostLawyerForm(val) {
      return val === true ? '是' : '否'
    }
  },
  props: {
    dialogVisible: {// 是否显示
      type: Boolean,
      default: false
    },
    // lawyerState: {// 律师状态
    //   type: String,
    //   default: null
    // },
    // lawFirmId: {// 所属律所
    //   type: String,
    //   default: null
    // }
  },
  computed: {
    ...mapGetters(['orgContext']),
  },
  data() {
    return {
      mydialogVisible: this.dialogVisible,
      tableLoading: true,
      tableQuery: {// 分页
        page: 1,
        limit: 10,
        total: 0,
        name: null,
        val: null,
        tag: null,
        showAll: true,
        isQuery: false,
        fuzzyValue: '',
        orgId: null,
      },
      tableData: [], // 列表数据源
      allTableData: [], // 列表全部数据源
      fuzzyTableData: [], // 列表模糊搜索数据源

      dialog_height: 'calc(100vh - 450px)', // table高度
      tableTempData: null
    }
  },
  watch: {
    dialogVisible(val) {
      this.mydialogVisible = val
      if (val) {
        this.refreshData()
      }
    },
    mydialogVisible(val) {
      this.$emit('update:dialogVisible', val)
    },
    // lawyerState: {
    //   handler(newVal) {
    //     if (newVal) {
    //       this.tableQuery.dataState = newVal
    //     }
    //   },
    //   immediate: true
    // },
    // lawFirmId: {
    //   handler(newVal) {
    //     if (newVal) {
    //       this.tableQuery.lawFirmId = newVal
    //     }
    //   },
    //   immediate: true
    // }
  },
  methods: {
    formatDate(row, column, cellValue) {
      debugger
      if (!cellValue) return "";
      return cellValue.split(" ")[0]; // 截取日期部分
    },
    // refresh() {
    //   this.loading = true;
    //   this.$message.info('数据正在刷新，请稍等！');
    //   AutEvaApi.refresh().then((res) => {
    //     this.refreshData();
    //     this.loading = false;
    //   }).catch((error) => {
    //     console.error('Error:', error);
    //     this.loading = false;
    //   });
    // },
    select_() {
      this.refreshData()
    },
    empty_() {
      this.tableQuery = {
        page: 1,
        limit: 10,
        total: 0,
        name: null,
        val: null,
        tag: null,
        showAll: true,
        isQuery: false,
        fuzzyValue: '',
      }
      this.refreshData()
    },
    contractCurrentChange(currentRow) {
      this.tableTempData = currentRow
    },
    refreshData() {
      // AutEvaluationApi.queryDialog(this.tableQuery).then(res => {
      //   console.log(this.tableLoading);
      //   this.tableData = res.data.data.records
      //   this.tableQuery.total = res.data.data.total
      //   this.tableLoading = false
      // })
      this.tableQuery.orgId = this.orgContext.currentOrgId;
      complianceReviewApi.getOriginalComplianceReview(this.tableQuery).then(res => {
        this.tableData = res.data.data.records
        this.tableQuery.total = res.data.data.total
        this.tableLoading = false
      })
    },
    cancel_() {
      this.mydialogVisible = false
    },
    sure_() {
      this.$emit('originalComplianceReviewSure', this.tableTempData)
    }

  }
}
</script>

<style scoped>

</style>
