<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.klaw.dao.lawyerDao.LawyerInApprovalMapper">
    <resultMap id="BaseResultMap" type="com.klaw.entity.lawyerBean.LawyerInApproval">
        <id column="ID" jdbcType="VARCHAR" property="id" />
        <result column="CREATE_OGN_ID" jdbcType="VARCHAR" property="createOgnId" />
        <result column="CREATE_OGN_NAME" jdbcType="VARCHAR" property="createOgnName" />
        <result column="CREATE_DEPT_ID" jdbcType="VARCHAR" property="createDeptId" />
        <result column="CREATE_DEPT_NAME" jdbcType="VARCHAR" property="createDeptName" />
        <result column="CREATE_GROUP_ID" jdbcType="VARCHAR" property="createGroupId" />
        <result column="CREATE_GROUP_NAME" jdbcType="VARCHAR" property="createGroupName" />
        <result column="CREATE_PSN_ID" jdbcType="VARCHAR" property="createPsnId" />
        <result column="CREATE_PSN_NAME" jdbcType="VARCHAR" property="createPsnName" />
        <result column="CREATE_ORG_ID" jdbcType="VARCHAR" property="createOrgId" />
        <result column="CREATE_ORG_NAME" jdbcType="VARCHAR" property="createOrgName" />
        <result column="CREATE_PSN_FULL_ID" jdbcType="VARCHAR" property="createPsnFullId" />
        <result column="CREATE_PSN_FULL_NAME" jdbcType="VARCHAR" property="createPsnFullName" />
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
        <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="DATA_STATE" jdbcType="VARCHAR" property="dataState" />
        <result column="DATA_STATE_CODE" jdbcType="DECIMAL" property="dataStateCode" />
        <result column="LAWYER_NAME" jdbcType="VARCHAR" property="lawyerName" />
        <result column="LAW_FIRM" jdbcType="VARCHAR" property="lawFirm" />
        <result column="LAW_FIRM_ID" jdbcType="VARCHAR" property="lawFirmId" />
        <result column="BEGIN_DATE" jdbcType="TIMESTAMP" property="beginDate" />
        <result column="END_DATE" jdbcType="TIMESTAMP" property="endDate" />
        <result column="LAWYER_PHONE" jdbcType="VARCHAR" property="lawyerPhone" />
        <result column="LAWYER_EMAIL" jdbcType="VARCHAR" property="lawyerEmail" />
        <result column="LAWYER_TIME" jdbcType="TIMESTAMP" property="lawyerTime" />
        <result column="CHARTERED_NO" jdbcType="VARCHAR" property="charteredNo" />
        <result column="BE_GOOD_AT_DOMAIN" jdbcType="VARCHAR" property="beGoodAtDomain" />
        <result column="BE_GOOD_AT_DOMAIN_IDS" jdbcType="VARCHAR" property="beGoodAtDomainIds" />
        <result column="REMARKS" jdbcType="CLOB" property="remarks" />
        <result column="IS_COUNSEL" jdbcType="VARCHAR" property="isCounsel" />
        <result column="SEQ" jdbcType="DECIMAL" property="seq" />
        <result column="WHETHER_HOST_LAWYER" jdbcType="DECIMAL" property="whetherHostLawyer" />
        <result column="PARENT_ID" jdbcType="VARCHAR" property="parentId" />
    </resultMap>
    <sql id="Base_Column_List">
        ID, CREATE_OGN_ID, CREATE_OGN_NAME, CREATE_DEPT_ID, CREATE_DEPT_NAME, CREATE_GROUP_ID,
        CREATE_GROUP_NAME, CREATE_PSN_ID, CREATE_PSN_NAME, CREATE_ORG_ID, CREATE_ORG_NAME,
        CREATE_PSN_FULL_ID, CREATE_PSN_FULL_NAME, CREATE_TIME, UPDATE_TIME, DATA_STATE, DATA_STATE_CODE,
        LAWYER_NAME, LAW_FIRM, LAW_FIRM_ID, BEGIN_DATE, LAWYER_PHONE, LAWYER_EMAIL, LAWYER_TIME,
        CHARTERED_NO, BE_GOOD_AT_DOMAIN, BE_GOOD_AT_DOMAIN_IDS, CASES_EXPERIENCE,
        INVOLVE_LAW_COURT, REMARKS, IS_COUNSEL, SEQ, WHETHER_HOST_LAWYER, PARENT_ID
    </sql>

    <select id="queryLawyerByDate" resultMap="BaseResultMap">
        select d.*
        from SG_LAW_FIRM_IN_APPROVAL m join SG_LAWYER_IN_APPROVAL d on m.id=d.LAW_FIRM_ID
        <where>
            d.SOURCE_ID = #{id}
            and m.operating_type <![CDATA[ <> ]]> 'change'
        </where>
        <if test="param !=  null  and param != '' ">
            order by m.create_time ${param}
        </if>
    </select>
</mapper>