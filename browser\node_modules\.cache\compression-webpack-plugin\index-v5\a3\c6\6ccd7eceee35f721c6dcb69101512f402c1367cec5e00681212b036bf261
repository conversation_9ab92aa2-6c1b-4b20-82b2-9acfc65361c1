
62e3f0714fe6b8a697114c057292ba8e468f1709	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.367.1754018536329.js\",\"contentHash\":\"80970ede04f7d4dbc3fc6d86901bb552\"}","integrity":"sha512-KgkRZMD0kCq5ftUNEJQc12e38bhM7Efxp06CHJCDf7MlYtXn2zSiYPz+/lB/J84J6TabvZNq9YMOraJ2KDV1tA==","time":1754018575976,"size":109759}