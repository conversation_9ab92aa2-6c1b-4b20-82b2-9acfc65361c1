
40ce1fbf46cbc1c5dd46b297f72cf81ffc32b054	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.71.1754018536329.js\",\"contentHash\":\"d1a8f3ff7cbe19ce68322776463a9be7\"}","integrity":"sha512-zMFqBvuAWiveq8dy2hE/JBv/ZwgxnWcYyDN8e+n2ele6FSHoHP1gU4kTkEK08SjRJ2OGFER04dwqijTrTEbrZQ==","time":1754018576042,"size":189244}