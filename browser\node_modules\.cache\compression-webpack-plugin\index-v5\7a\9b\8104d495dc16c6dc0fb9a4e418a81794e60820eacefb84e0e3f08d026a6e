
1fcd623803d95311b0a39b32155d2be1d5883126	{"key":"{\"nodeVersion\":\"v12.22.12\",\"compression-webpack-plugin\":\"6.1.1\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"fw\\u002Fstatic\\u002Fjs\\u002Fchunk.52.1754018536329.js\",\"contentHash\":\"2772d7571c8fdd4ee295d6e91c81dff8\"}","integrity":"sha512-AXB1WHcatw6uRVUQkxV04xv3v6wKLe20AbBsSdnjwV9WwIm7infdp/M85VtQHPngmDhLEcCV47KkIZPpKaxTWw==","time":1754018576289,"size":965119}