<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.klaw.dao.contractDao.SgOppositeManageMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.klaw.entity.contractBean.SgOppositeManage">
        <id column="CODE" property="code" />
        <result column="BUSINESS_NAME" property="businessName" />
        <result column="SHORTER_FORM_NAME" property="shorterFormName" />
        <result column="ENGLISH_NAME" property="englishName" />
        <result column="IS_CUSTOMER_CODE" property="isCustomerCode" />
        <result column="IS_CUSTOMER_DESC" property="isCustomerDesc" />
        <result column="IS_SUPPLIER_CODE" property="isSupplierCode" />
        <result column="IS_SUPPLIER_DESC" property="isSupplierDesc" />
        <result column="BUSINESS_CLASSIFY_CODE" property="businessClassifyCode" />
        <result column="BUSINESS_CLASSIFY_DESC" property="businessClassifyDesc" />
        <result column="NATION_CODE" property="nationCode" />
        <result column="NATION_NAME" property="nationName" />
        <result column="ID_CARD_CODE" property="idCardCode" />
        <result column="ID_CARD_DESC" property="idCardDesc" />
        <result column="ID_CARD_NUMBER" property="idCardNumber" />
        <result column="LICENSE_CODE" property="licenseCode" />
        <result column="REGISTER_NUMBER" property="registerNumber" />
        <result column="TAX_NUMBER" property="taxNumber" />
        <result column="ORG_NUMBER" property="orgNumber" />
        <result column="REGISTER_ADDRESS" property="registerAddress" />
        <result column="PHONE" property="phone" />
        <result column="TAXPAYER_CLASSIFY_CODE" property="taxpayerClassifyCode" />
        <result column="TAXPAYER_CLASSIFY_DESC" property="taxpayerClassifyDesc" />
        <result column="STATE" property="state" />
        <result column="STATE_DESC" property="stateDesc" />
        <result column="BUSINESS_EVALUATION" property="businessEvaluation" />
        <result column="DATA_SOURCE" property="dataSource" />
        <result column="QUALIFICATION_DOCUMENTS" property="qualificationDocuments" />
        <result column="BUSINESS_LICENSE" property="businessLicense" />
        <result column="LEGAL_REPRESENTATIVE_NAME" property="legalRepresentativeName" />
        <result column="LEGAL_REPRESENTATIVE_PHONE" property="legalRepresentativePhone" />
        <result column="BUSINESS_LEADER_NAME" property="businessLeaderName" />
        <result column="BUSINESS_LEADER_PHONE" property="businessLeaderPhone" />
        <result column="BANK_NAME" property="bankName" />
        <result column="BANK_ACCOUNT" property="bankAccount" />
        <result column="EXPLAINS" property="explains" />
        <result column="REMARKS" property="remarks" />
        <result column="CREATE_OGN_ID" property="createOgnId" />
        <result column="CREATE_OGN_NAME" property="createOgnName" />
        <result column="CREATE_DEPT_ID" property="createDeptId" />
        <result column="CREATE_DEPT_NAME" property="createDeptName" />
        <result column="CREATE_GROUP_ID" property="createGroupId" />
        <result column="CREATE_GROUP_NAME" property="createGroupName" />
        <result column="CREATE_PSN_ID" property="createPsnId" />
        <result column="CREATE_PSN_NAME" property="createPsnName" />
        <result column="CREATE_ORG_ID" property="createOrgId" />
        <result column="CREATE_ORG_NAME" property="createOrgName" />
        <result column="CREATE_PSN_FULL_ID" property="createPsnFullId" />
        <result column="CREATE_PSN_FULL_NAME" property="createPsnFullName" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="UPDATE_ORG_ID" property="updateOrgId" />
        <result column="UPDATE_ORG_NAME" property="updateOrgName" />
        <result column="UPDATE_TIME" property="updateTime" />
        <result column="CASE_NUM" property="caseNum" />
        <result column="PERFORMANCE_RATE" property="performanceRate" />
        <result column="WHETHER_BLACK" property="whetherBlack" />
        <result column="PSN_NAMES" property="psnNames" />
        <result column="PSN_IDS" property="psnIds" />
        <result column="DATA_STATE" property="dataState" />
        <result column="DATA_STATE_CODE" property="dataStateCode" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        CODE,
        BUSINESS_NAME,
        is_customer_code,
        is_customer_desc,
        is_supplier_code,
        is_supplier_desc,
        business_classify_code,
        business_classify_desc,
        license_code,
        case_num,
        performance_rate,
        data_source,
        state_desc,
        legal_representative_name,
        id_card_number

    </sql>
    <select id="queryPageList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from SG_BUSINESS_MAN
        <where>
            ${ew.sqlSegment}
        </where>
    </select>

</mapper>
